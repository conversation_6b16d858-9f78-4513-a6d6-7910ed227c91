# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: warehouse/stocktake.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='warehouse/stocktake.proto',
  package='warehouse',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x19warehouse/stocktake.proto\x12\twarehouse\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"p\n GetProductByStocktakeTypeRequest\x12\x16\n\x0estocktake_type\x18\x01 \x01(\t\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x14\n\x0cstorage_type\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"b\n\x14StocktakeProductType\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12\x14\n\x0cstorage_type\x18\x05 \x01(\t\"R\n!GetProductByStocktakeTypeResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.warehouse.StocktakeProductType\"9\n\x1aGetStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x83\n\n\tStocktake\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x13\n\x0bschedule_id\x18\x05 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x07 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\t \x01(\x08\x12\x18\n\x10\x66orecasting_time\x18\n \x01(\t\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x13\n\x0bresult_type\x18\x0c \x01(\t\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x14\n\x0cst_diff_flag\x18\x0e \x01(\x05\x12+\n\x06status\x18\x0f \x01(\x0e\x32\x1b.warehouse.Stocktake.STATUS\x12\x33\n\x0eprocess_status\x18\x10 \x01(\x0e\x32\x1b.warehouse.Stocktake.STATUS\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\x12\x11\n\treview_by\x18\x15 \x01(\x04\x12/\n\x0btarget_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x14\n\x0cupdated_name\x18\x1b \x01(\t\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x18\n\x10\x64iff_err_message\x18\x1d \x01(\t\x12\x19\n\x11month_err_message\x18\x1f \x01(\t\x12\x15\n\roriginal_code\x18# \x01(\t\x12\x17\n\x0foriginal_doc_id\x18$ \x01(\x04\x12\x13\n\x0bis_recreate\x18% \x01(\x08\x12\x15\n\rrecreate_code\x18& \x01(\t\x12\x17\n\x0frecreate_doc_id\x18\' \x01(\x04\x12\x13\n\x0bsubmit_name\x18( \x01(\t\x12\x14\n\x0c\x61pprove_name\x18) \x01(\t\x12\x16\n\x0estocktake_type\x18* \x01(\t\x12\x12\n\nrequest_id\x18+ \x01(\x04\x12\x14\n\x0ctotal_amount\x18\x32 \x01(\x01\x12\x19\n\x11total_diff_amount\x18\x33 \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\x34 \x01(\x01\x12\x1f\n\x17total_diff_sales_amount\x18\x35 \x01(\x01\x12/\n\x0b\x61ttachments\x18\x36 \x03(\x0b\x32\x1a.warehouse.AttachmentsWare\"\x87\x01\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tCANCELLED\x10\x02\x12\r\n\tSUBMITTED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\x0c\n\x08\x41PPROVED\x10\x05\x12\r\n\tCONFIRMED\x10\x06\x12\x0c\n\x08SUBMIT_0\x10\x07\x12\r\n\tAPPROVE_0\x10\x08\",\n\x0f\x41ttachmentsWare\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"\xc2\x06\n\x13GetStocktakeRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x12\n\nbranch_ids\x18\x03 \x03(\x04\x12=\n\x0cstore_status\x18\x04 \x03(\x0e\x32\'.warehouse.GetStocktakeRequest.S_STATUS\x12\x34\n\x05_type\x18\x05 \x03(\x0e\x32%.warehouse.GetStocktakeRequest.S_TYPE\x12\x35\n\x06status\x18\x06 \x03(\x0e\x32%.warehouse.GetStocktakeRequest.STATUS\x12\x15\n\rschedule_code\x18\x07 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x08 \x03(\x04\x12\x0e\n\x06offset\x18\t \x01(\r\x12\r\n\x05limit\x18\n \x01(\r\x12.\n\nstart_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0btarget_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x0e \x01(\t\x12\x0b\n\x03ids\x18\x15 \x03(\x04\x12\x11\n\tis_create\x18\x0f \x01(\x08\x12\x16\n\x0estocktake_type\x18\x10 \x01(\t\x12\r\n\x05order\x18\x11 \x01(\t\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x13 \x01(\t\x12\x0b\n\x03lan\x18\x14 \x01(\t\x12\x15\n\rschedule_name\x18\x16 \x01(\t\"\"\n\x08S_STATUS\x12\n\n\x06OPENED\x10\x00\x12\n\n\x06\x43LOSED\x10\x01\"\x1d\n\x06S_TYPE\x12\x05\n\x01W\x10\x00\x12\x05\n\x01\x44\x10\x01\x12\x05\n\x01M\x10\x02\"\x87\x01\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tCANCELLED\x10\x02\x12\r\n\tSUBMITTED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\x0c\n\x08\x41PPROVED\x10\x05\x12\r\n\tCONFIRMED\x10\x06\x12\x0c\n\x08SUBMIT_0\x10\x07\x12\r\n\tAPPROVE_0\x10\x08\"I\n\x14GetStocktakeResponse\x12\"\n\x04rows\x18\x01 \x03(\x0b\x32\x14.warehouse.Stocktake\x12\r\n\x05total\x18\x02 \x01(\r\"\xdf\x01\n\x1aGetStocktakeProductRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x14\n\x0cinclude_unit\x18\x02 \x01(\x08\x12\r\n\x05limit\x18\x03 \x01(\r\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\x15\n\rinclude_total\x18\x05 \x01(\x08\x12\x13\n\x0b\x63\x61tegory_id\x18\x06 \x01(\x04\x12\x14\n\x0cstorage_type\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x17\n\x0finclude_barcode\x18\n \x01(\x08\"\xf7\x04\n\x17StocktakeProductTagName\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x64oc_id\x18\x02 \x01(\x04\x12\x0e\n\x06stp_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x0e\n\x06tag_id\x18\x05 \x01(\x04\x12\x10\n\x08tag_name\x18\x06 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x07 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x08 \x01(\x01\x12\x0f\n\x07unit_id\x18\t \x01(\x04\x12\x11\n\tunit_spec\x18\n \x01(\t\x12\x11\n\tunit_name\x18\x0b \x01(\t\x12\x11\n\tunit_rate\x18\x0c \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\r \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0f \x01(\t\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x0f\n\x07user_id\x18\x11 \x01(\x04\x12\x12\n\ncreated_by\x18\x12 \x01(\x04\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12.\n\ncreated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x16 \x01(\t\x12\x14\n\x0cupdated_name\x18\x17 \x01(\t\x12\x10\n\x08tax_rate\x18\x18 \x01(\x01\x12\x11\n\ttax_price\x18\x19 \x01(\x01\x12\x12\n\ncost_price\x18\x1a \x01(\x01\x12\x13\n\x0bsales_price\x18\x1b \x01(\x01\"\xd3\x01\n\x15StocktakeProductUnits\x12\x0f\n\x07unit_id\x18\x01 \x01(\x04\x12\x11\n\tunit_name\x18\x02 \x01(\t\x12\x11\n\tunit_spec\x18\x03 \x01(\t\x12\x11\n\tunit_rate\x18\x04 \x01(\x01\x12\x10\n\x08tax_rate\x18\x05 \x01(\x01\x12\x11\n\ttax_price\x18\x06 \x01(\x01\x12\x12\n\ncost_price\x18\x07 \x01(\x01\x12\x13\n\x0bsales_price\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\x11\n\tstocktake\x18\n \x01(\x08\"\x9a\x0b\n\x10StocktakeProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0e\n\x06\x64oc_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x06 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x07 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x08 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\t \x01(\t\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\x0f\n\x07\x64\x65leted\x18\x0b \x01(\x08\x12\x15\n\rdiff_quantity\x18\x0c \x01(\x01\x12\x15\n\rdisplay_order\x18\r \x01(\t\x12\x0f\n\x07\x65xtends\x18\x0e \x01(\t\x12\x0f\n\x07ignored\x18\x0f \x01(\x08\x12\x1a\n\x12inventory_quantity\x18\x10 \x01(\x01\x12\x11\n\tis_system\x18\x11 \x01(\x08\x12\x13\n\x0bitem_number\x18\x12 \x01(\x04\x12\x17\n\x0fmaterial_number\x18\x13 \x01(\t\x12\x14\n\x0cproduct_code\x18\x14 \x01(\t\x12\x14\n\x0cproduct_name\x18\x15 \x01(\t\x12\x10\n\x08quantity\x18\x16 \x01(\x01\x12\x0f\n\x07st_type\x18\x17 \x01(\t\x12\x14\n\x0cstorage_type\x18\x18 \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x19 \x01(\x01\x12\x0f\n\x07unit_id\x18\x1a \x01(\x04\x12\x11\n\tunit_name\x18\x1b \x01(\t\x12\x11\n\tunit_rate\x18\x1c \x01(\x01\x12\x11\n\tunit_spec\x18\x1d \x01(\t\x12/\n\x05units\x18\x1e \x03(\x0b\x32 .warehouse.StocktakeProductUnits\x12\x12\n\ncreated_by\x18\x1f \x01(\x04\x12\x12\n\nupdated_by\x18  \x01(\x04\x12/\n\x0btarget_date\x18! \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18$ \x01(\x04\x12\x0e\n\x06status\x18% \x01(\t\x12\x0e\n\x06is_pda\x18& \x01(\x08\x12\x38\n\x0cproduct_tags\x18\' \x03(\x0b\x32\".warehouse.StocktakeProductTagName\x12\x10\n\x08is_empty\x18( \x01(\x08\x12\x14\n\x0c\x63reated_name\x18) \x01(\t\x12\x14\n\x0cupdated_name\x18* \x01(\t\x12\x0f\n\x07is_null\x18+ \x01(\x08\x12\x14\n\x0ctag_quantity\x18, \x01(\x01\x12#\n\x1b\x63onvert_accounting_quantity\x18- \x01(\x01\x12\x0e\n\x06is_bom\x18. \x01(\x08\x12\x1c\n\x14\x61llow_stocktake_edit\x18/ \x01(\x08\x12\x0c\n\x04spec\x18\x30 \x01(\t\x12\x12\n\ndiff_price\x18\x31 \x01(\x01\x12\x0f\n\x07\x62\x61rcode\x18\x32 \x03(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x36 \x01(\x04\x12\x13\n\x0bposition_id\x18\x37 \x01(\x04\x12\x15\n\rcategory_name\x18\x38 \x01(\t\x12\x10\n\x08tax_rate\x18\x39 \x01(\x01\x12\x11\n\ttax_price\x18: \x01(\x01\x12\x0e\n\x06\x61mount\x18; \x01(\x01\x12\x13\n\x0b\x64iff_amount\x18< \x01(\x01\x12\x12\n\ncost_price\x18= \x01(\x01\x12\x14\n\x0csales_amount\x18> \x01(\x01\x12\x13\n\x0bsales_price\x18? \x01(\x01\x12\x19\n\x11\x64iff_sales_amount\x18@ \x01(\x01\"\x94\x01\n\x1bGetStocktakeProductResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.warehouse.StocktakeProduct\x12\r\n\x05total\x18\x02 \x01(\x04\x12;\n\rposition_rows\x18\x03 \x03(\x0b\x32$.warehouse.StocktakePositionProducts\"\x90\x01\n\x1aUserCreateStocktakeRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\r\n\x05_type\x18\x03 \x01(\t\x12/\n\x0btarget_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x05 \x01(\t\"\x9b\x06\n\x1bUserCreateStocktakeResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x13\n\x0bschedule_id\x18\x05 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x07 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\t \x01(\x08\x12\x18\n\x10\x66orecasting_time\x18\n \x01(\t\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x13\n\x0bresult_type\x18\x0c \x01(\t\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x14\n\x0cst_diff_flag\x18\x0e \x01(\x05\x12=\n\x06status\x18\x0f \x01(\x0e\x32-.warehouse.UserCreateStocktakeResponse.STATUS\x12\x45\n\x0eprocess_status\x18\x10 \x01(\x0e\x32-.warehouse.UserCreateStocktakeResponse.STATUS\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\x12\x11\n\treview_by\x18\x15 \x01(\x04\x12/\n\x0btarget_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x14\n\x0cupdated_name\x18\x1b \x01(\t\"!\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\"b\n\x0bTagQuantity\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06tag_id\x18\x02 \x01(\x04\x12\x10\n\x08tag_name\x18\x03 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x04 \x01(\x01\x12\x0f\n\x07unit_id\x18\x05 \x01(\x04\"\xbb\x01\n\x14PutStocktakeProducts\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x0f\n\x07unit_id\x18\x03 \x01(\x04\x12,\n\x0ctag_products\x18\x04 \x03(\x0b\x32\x16.warehouse.TagQuantity\x12\x0e\n\x06is_pda\x18\x05 \x01(\x08\x12\x10\n\x08is_empty\x18\x06 \x01(\x08\x12\x0f\n\x07is_null\x18\x07 \x01(\x08\x12\x13\n\x0b\x64\x65l_tag_ids\x18\x08 \x03(\x04\"~\n\x1aPutStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x31\n\x08products\x18\x02 \x03(\x0b\x32\x1f.warehouse.PutStocktakeProducts\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x04 \x01(\x08\"L\n\x1dRejectStocktakeProductRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0e\n\x06reason\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"0\n\x1eRejectStocktakeProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"-\n\x1bPutStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"_\n\x1c\x43heckStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\r\n\x05\x63heck\x18\x02 \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"G\n\x1b\x43heckStocktakeByDocIDDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\"\xce\x05\n\x1d\x43heckStocktakeByDocIDResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x36\n\x06\x61\x64just\x18\x02 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12>\n\x0ereceiving_diff\x18\x03 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x38\n\x08transfer\x18\x04 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x39\n\treceiving\x18\x05 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x36\n\x06return\x18\x06 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12@\n\x10\x64irect_receiving\x18\x07 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x45\n\x15\x64irect_receiving_diff\x18\x08 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12=\n\rdirect_return\x18\t \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x39\n\tstocktake\x18\n \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x36\n\x06\x64\x65mand\x18\x0b \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12<\n\x0cself_picking\x18\x0c \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\"L\n\x1e\x43onfirmStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\r\n\x05\x63heck\x18\x02 \x01(\x08\x12\x0b\n\x03lan\x18\x03 \x01(\t\"0\n\x1eSubmitStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"c\n\x1dSubmitStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x13\n\x0bsubmit_name\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x04 \x01(\x08\"s\n\x1e\x41pproveStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x14\n\x0c\x61pprove_name\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x05 \x01(\x08\"\xed\x04\n\x1f\x41pproveStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0f\n\x07handler\x18\x02 \x01(\x08\x12\x36\n\x06\x61\x64just\x18\x03 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12>\n\x0ereceiving_diff\x18\x04 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x38\n\x08transfer\x18\x05 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x39\n\treceiving\x18\x06 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x36\n\x06return\x18\x07 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12@\n\x10\x64irect_receiving\x18\x08 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x45\n\x15\x64irect_receiving_diff\x18\t \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12=\n\rdirect_return\x18\n \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12<\n\x0cself_picking\x18\x0c \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\"1\n\x1f\x43onfirmStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"<\n\x1d\x43\x61ncelStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"0\n\x1e\x43\x61ncelStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"p\n\x1b\x43reateStocktakeBatchRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x03 \x01(\t\"x\n\x1c\x43reateStocktakeBatchResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x16\n\x0eschedule_count\x18\x02 \x01(\r\x12\x30\n\x0crequest_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"k\n\x16\x43reateStocktakeRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x03 \x01(\t\"[\n\x17\x43reateStocktakeResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x30\n\x0crequest_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"E\n\x1e\x43heckedStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\"1\n\x1f\x43heckedStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"_\n\x17GetStocktakeTagsRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\x12\x10\n\x08tag_name\x18\x03 \x01(\t\x12\x12\n\nbranch_ids\x18\x04 \x03(\x04\"\xaa\x02\n\rStocktakeTags\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x0f\n\x07user_id\x18\x04 \x01(\x04\x12\x12\n\ncreated_by\x18\x05 \x01(\x04\x12\x12\n\nupdated_by\x18\x06 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\t \x01(\t\x12\x14\n\x0cupdated_name\x18\n \x01(\t\x12\x11\n\tbranch_id\x18\x0b \x01(\x04\x12\x13\n\x0b\x62ranch_name\x18\x0c \x01(\t\"B\n\x18GetStocktakeTagsResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.warehouse.StocktakeTags\"\xd3\x02\n\x1a\x41\x63tionStocktakeTagsRequest\x12\x0e\n\x06tag_id\x18\x01 \x01(\x04\x12<\n\x06\x61\x63tion\x18\x02 \x01(\x0e\x32,.warehouse.ActionStocktakeTagsRequest.Action\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x0b\n\x03lan\x18\x05 \x01(\t\x12\x12\n\nbranch_ids\x18\x06 \x03(\x04\x12\x12\n\nregion_ids\x18\x07 \x03(\x04\x12\x15\n\radd_dimension\x18\x08 \x01(\t\x12\x0f\n\x07tag_ids\x18\t \x03(\x04\x12\x13\n\x0borigin_name\x18\n \x01(\t\x12\x13\n\x0b\x63opy_branch\x18\x0b \x01(\x04\"?\n\x06\x41\x63tion\x12\x07\n\x03get\x10\x00\x12\n\n\x06\x63reate\x10\x01\x12\n\n\x06\x64\x65lete\x10\x02\x12\n\n\x06update\x10\x03\x12\x08\n\x04\x63opy\x10\x04\"\xb3\x02\n\x1b\x41\x63tionStocktakeTagsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x12\n\npartner_id\x18\x04 \x01(\x04\x12\x0f\n\x07user_id\x18\x05 \x01(\x04\x12\x12\n\ncreated_by\x18\x06 \x01(\x04\x12\x12\n\nupdated_by\x18\x07 \x01(\x04\x12.\n\ncreated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\n \x01(\t\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12\x11\n\tbranch_id\x18\x0c \x01(\x04\"-\n\x1bGetStocktakeTagsByIdRequest\x12\x0e\n\x06tag_id\x18\x01 \x01(\x04\"/\n!DeleteStocktakeProductTagsRequest\x12\n\n\x02id\x18\x01 \x03(\x04\"4\n\"DeleteStocktakeProductTagsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xf9\x02\n\x1aGetStocktakeBalanceRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x0e\n\x06offset\x18\x05 \x01(\r\x12/\n\x0btarget_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x07 \x03(\t\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x15\n\rschedule_code\x18\t \x01(\t\x12\x16\n\x0estocktake_type\x18\n \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0b \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x0c \x01(\t\x12\x0b\n\x03lan\x18\r \x01(\t\x12\x0c\n\x04type\x18\x35 \x01(\t\"\x93\t\n\x10StocktakeBalance\x12\x17\n\x0f\x62ranch_batch_id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x03 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x04 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\x06 \x01(\x08\x12\x34\n\x10\x66orecasting_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\n\n\x02id\x18\x08 \x01(\x04\x12\x16\n\x0eprocess_status\x18\t \x01(\t\x12\x0e\n\x06remark\x18\n \x01(\t\x12\x13\n\x0bresult_type\x18\x0b \x01(\t\x12\x11\n\treview_by\x18\x0c \x01(\x04\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x13\n\x0bschedule_id\x18\x0e \x01(\x04\x12\x14\n\x0cst_diff_flag\x18\x0f \x01(\x05\x12\x0e\n\x06status\x18\x10 \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12/\n\x0btarget_date\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\x13 \x01(\t\x12\x12\n\npartner_id\x18\x14 \x01(\x04\x12\x0f\n\x07user_id\x18\x15 \x01(\x04\x12\x12\n\ncreated_by\x18\x16 \x01(\x04\x12\x12\n\nupdated_by\x18\x17 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x18 \x01(\t\x12\x14\n\x0cupdated_name\x18\x19 \x01(\t\x12.\n\ncreated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x18\n\x10\x64iff_err_message\x18\x1d \x01(\t\x12\x19\n\x11month_err_message\x18\x1f \x01(\t\x12\x13\n\x0b\x62ranch_name\x18# \x01(\t\x12\x13\n\x0b\x62ranch_code\x18$ \x01(\t\x12\x15\n\roriginal_code\x18% \x01(\t\x12\x17\n\x0foriginal_doc_id\x18& \x01(\x04\x12\x13\n\x0bis_recreate\x18\' \x01(\x08\x12\x15\n\rrecreate_code\x18( \x01(\t\x12\x17\n\x0frecreate_doc_id\x18) \x01(\x04\x12\x13\n\x0bsubmit_name\x18* \x01(\t\x12\x14\n\x0c\x61pprove_name\x18+ \x01(\t\x12\x16\n\x0estocktake_type\x18, \x01(\t\x12\x12\n\nrequest_id\x18- \x01(\x04\x12\x14\n\x0ctotal_amount\x18. \x01(\x01\x12\x19\n\x11total_diff_amount\x18/ \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\x30 \x01(\x01\x12\x1f\n\x17total_diff_sales_amount\x18\x31 \x01(\x01\x12\x36\n\x0b\x61ttachments\x18\x32 \x03(\x0b\x32!.warehouse.AttachmentsWareBalance\"3\n\x16\x41ttachmentsWareBalance\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"W\n\x1bGetStocktakeBalanceResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.warehouse.StocktakeBalance\x12\r\n\x05total\x18\x02 \x01(\x04\"E\n&GetStocktakeBalanceProductGroupRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"d\n\x0cTagProductBi\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x02 \x01(\x01\x12\x15\n\rtag_unit_name\x18\x03 \x01(\t\x12\x15\n\rtag_uint_rate\x18\x04 \x01(\x01\"\x9e\x05\n\x1cStocktakeBalanceProductGroup\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x04 \x01(\t\x12\x15\n\rdiff_quantity\x18\x05 \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\x06 \x01(\x01\x12\x11\n\tis_system\x18\x07 \x01(\x08\x12\x17\n\x0fmaterial_number\x18\x08 \x01(\t\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x12\n\nproduct_id\x18\n \x01(\x04\x12\x14\n\x0cproduct_name\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x14\n\x0cstorage_type\x18\r \x01(\t\x12\x10\n\x08tag_code\x18\x0e \x01(\t\x12\x10\n\x08tag_name\x18\x0f \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x10 \x01(\x01\x12\x0f\n\x07unit_id\x18\x11 \x01(\x04\x12\x11\n\tunit_name\x18\x12 \x01(\t\x12\x11\n\tunit_rate\x18\x13 \x01(\x01\x12\x11\n\tunit_spec\x18\x14 \x01(\t\x12,\n\x0btag_details\x18\x15 \x03(\x0b\x32\x17.warehouse.TagProductBi\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x16 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x17 \x01(\x01\x12\x13\n\x0bposition_id\x18\x19 \x01(\x04\x12\x15\n\rposition_code\x18\x1a \x01(\t\x12\x15\n\rposition_name\x18\x1b \x01(\t\"`\n\'GetStocktakeBalanceProductGroupResponse\x12\x35\n\x04rows\x18\x01 \x03(\x0b\x32\'.warehouse.StocktakeBalanceProductGroup\"\xf8\x02\n\x1aStocktakeBiDetailedRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x11\n\tstore_ids\x18\n \x03(\x04\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x16\n\x0estocktake_type\x18\x12 \x01(\t\"\x91\x0c\n\x13StocktakeBiDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x04 \x01(\t\x12\x15\n\rbranch_1_name\x18\x05 \x01(\t\x12\x15\n\rbranch_2_name\x18\x06 \x01(\t\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\x12\x15\n\rcategory_code\x18\x08 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\t \x01(\x04\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65leted\x18\x0c \x01(\x08\x12\x15\n\rdiff_quantity\x18\r \x01(\x01\x12\x15\n\rdisplay_order\x18\x0e \x01(\t\x12\x0e\n\x06\x64oc_id\x18\x0f \x01(\x04\x12\x0f\n\x07\x65xtends\x18\x10 \x01(\t\x12\n\n\x02id\x18\x11 \x01(\x04\x12\x0f\n\x07ignored\x18\x12 \x01(\x08\x12\x1a\n\x12inventory_quantity\x18\x13 \x01(\x01\x12\x11\n\tis_system\x18\x14 \x01(\x08\x12\x13\n\x0bitem_number\x18\x15 \x01(\x04\x12\x17\n\x0fmaterial_number\x18\x16 \x01(\t\x12\x14\n\x0cproduct_code\x18\x17 \x01(\t\x12\x12\n\nproduct_id\x18\x18 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x19 \x01(\t\x12\x10\n\x08quantity\x18\x1a \x01(\x01\x12\x0c\n\x04type\x18\x1b \x01(\t\x12\x14\n\x0cstorage_type\x18\x1c \x01(\t\x12\x12\n\nstore_code\x18\x1d \x01(\t\x12\x12\n\nstore_name\x18\x1e \x01(\t\x12/\n\x0btarget_date\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1a\n\x12unit_diff_quantity\x18  \x01(\x01\x12\x0f\n\x07unit_id\x18! \x01(\x04\x12\x11\n\tunit_name\x18\" \x01(\t\x12\x11\n\tunit_rate\x18# \x01(\x01\x12\x11\n\tunit_spec\x18$ \x01(\t\x12\x12\n\npartner_id\x18% \x01(\x04\x12\x12\n\ncreated_by\x18& \x01(\x04\x12\x12\n\nupdated_by\x18\' \x01(\x04\x12\x14\n\x0c\x63reated_name\x18( \x01(\t\x12\x14\n\x0cupdated_name\x18) \x01(\t\x12.\n\ncreated_at\x18* \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18+ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08is_empty\x18, \x01(\x08\x12\x0e\n\x06is_pda\x18- \x01(\x08\x12\x0e\n\x06status\x18. \x01(\t\x12\r\n\x05units\x18/ \x01(\t\x12\x0f\n\x07user_id\x18\x30 \x01(\x04\x12\x0f\n\x07is_null\x18\x31 \x01(\x08\x12\x14\n\x0ctag_quantity\x18\x32 \x01(\x01\x12-\n\x0cproduct_tags\x18\x33 \x03(\x0b\x32\x17.warehouse.ProductTagBi\x12\x11\n\tis_enable\x18\x34 \x01(\x08\x12\x0c\n\x04spec\x18\x36 \x01(\t\x12\x17\n\x0f\x64iff_percentage\x18\x37 \x01(\x01\x12\x30\n\x0c\x63reated_time\x18\x38 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11\x63reated_user_name\x18\x39 \x01(\t\x12\x32\n\x0esubmitted_time\x18: \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1b\n\x13submitted_user_name\x18; \x01(\t\x12\x13\n\x0b\x62ranch_type\x18< \x01(\t\x12\x13\n\x0bposition_id\x18> \x01(\x04\x12\x15\n\rposition_code\x18? \x01(\t\x12\x15\n\rposition_name\x18@ \x01(\t\x12\x16\n\x0estocktake_type\x18\x35 \x01(\t\x12\x15\n\rschedule_code\x18\x41 \x01(\t\x12\x15\n\rschedule_name\x18\x42 \x01(\t\"\xc5\x01\n\x0cProductTagBi\x12\x13\n\x0btag_unit_id\x18\x01 \x01(\x04\x12\x15\n\rtag_unit_name\x18\x02 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x03 \x01(\x01\x12\x10\n\x08tag_name\x18\x04 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x05 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x06 \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x07 \x01(\x01\x12\n\n\x02id\x18\x08 \x01(\x04\"P\n\x08ST_total\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\"o\n\x1bStocktakeBiDetailedResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.warehouse.StocktakeBiDetailed\x12\"\n\x05total\x18\x02 \x01(\x0b\x32\x13.warehouse.ST_total\"\x99\x02\n\x1dStocktakeBalanceRegionRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\r\n\x05limit\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12)\n\x05start\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tregion_id\x18\x06 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x07 \x03(\x04\x12\x0c\n\x04type\x18\x08 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\t \x01(\t\x12\x0b\n\x03lan\x18\n \x01(\t\x12\x16\n\x0estocktake_type\x18\x0b \x01(\t\"\xc2\x03\n\x1dStocktakeBalanceRegionDetails\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x01 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x04 \x01(\t\x12\x15\n\rdiff_quantity\x18\x05 \x01(\x01\x12\x10\n\x08\x64oc_type\x18\x06 \x01(\t\x12\x1a\n\x12inventory_quantity\x18\x07 \x01(\x01\x12\x17\n\x0fmaterial_number\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x10\n\x08store_id\x18\x0c \x01(\x04\x12\x12\n\nstore_name\x18\r \x01(\t\x12\r\n\x05tag_1\x18\x0e \x01(\x04\x12\x19\n\x11tag_1_name_pinyin\x18\x0f \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x10 \x01(\x01\x12\x11\n\tunit_name\x18\x11 \x01(\t\x12\x11\n\tunit_rate\x18\x12 \x01(\x01\"\x9e\x04\n\x16StocktakeBalanceRegion\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x01 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x39\n\x07\x64\x65tails\x18\x04 \x03(\x0b\x32(.warehouse.StocktakeBalanceRegionDetails\x12\x11\n\tunit_name\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x10\n\x08quantity\x18\x08 \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\t \x01(\x01\x12\x1e\n\x16\x61\x63\x63oinventory_quantity\x18\n \x01(\x01\x12\x15\n\rdiff_quantity\x18\x0b \x01(\x01\x12\x1a\n\x12unit_diff_quantity\x18\x0c \x01(\x01\x12\x11\n\tunit_rate\x18\r \x01(\x01\x12\x0c\n\x04type\x18\x0e \x01(\t\x12\x12\n\nproduct_id\x18\x0f \x01(\x04\x12\x10\n\x08store_id\x18\x10 \x01(\x04\x12\x12\n\nstore_name\x18\x11 \x01(\t\x12\x12\n\nstore_code\x18\x12 \x01(\t\x12\x14\n\x0cproduct_code\x18\x13 \x01(\t\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x14 \x01(\x01\x12\x16\n\x0estocktake_type\x18\x15 \x01(\t\"`\n\x1eStocktakeBalanceRegionResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.warehouse.StocktakeBalanceRegion\x12\r\n\x05total\x18\x02 \x01(\x04\"\xb9\x01\n\x15StoreDataScopeRequest\x12\x0e\n\x06search\x18\x01 \x01(\t\x12\x15\n\rsearch_fields\x18\x02 \x01(\t\x12\x0b\n\x03ids\x18\x03 \x03(\x04\x12\x15\n\rreturn_fields\x18\x04 \x01(\t\x12\x0f\n\x07\x66ilters\x18\x05 \x01(\t\x12\x18\n\x10relation_filters\x18\x06 \x01(\t\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\"\x9f\x03\n\x0bScopeStores\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x13\n\x0bsecond_code\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x06 \x01(\t\x12\x0b\n\x03tel\x18\x07 \x01(\t\x12\x0f\n\x07\x63ontact\x18\x08 \x01(\t\x12\x0e\n\x06status\x18\t \x01(\t\x12\x0f\n\x07name_en\x18\n \x01(\t\x12\x11\n\topen_date\x18\x0b \x01(\t\x12\x12\n\nclose_date\x18\x0c \x01(\t\x12\r\n\x05\x65mail\x18\r \x01(\t\x12\x12\n\ngeo_region\x18\x0e \x03(\t\x12\x15\n\rbranch_region\x18\x0f \x03(\t\x12\x14\n\x0corder_region\x18\x10 \x03(\t\x12\x1b\n\x13\x64istribution_region\x18\x11 \x03(\t\x12\x17\n\x0fpurchase_region\x18\x12 \x03(\t\x12\x15\n\rmarket_region\x18\x13 \x03(\t\x12\x17\n\x0ftransfer_region\x18\x14 \x03(\t\x12\x18\n\x10\x61ttribute_region\x18\x15 \x03(\t\"E\n\x0eStoreDataScope\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.warehouse.ScopeStores\x12\r\n\x05total\x18\x02 \x01(\x04\":\n\x1b\x41\x64vanceStocktakeDiffRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x95\x01\n\x1c\x41\x64vanceStocktakeDiffResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.warehouse.StocktakeProduct\x12\r\n\x05total\x18\x02 \x01(\x04\x12;\n\rposition_rows\x18\x03 \x03(\x0b\x32$.warehouse.StocktakePositionProducts\"\xa9\x03\n\x1aStocktakeDiffReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12/\n\x0btarget_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x16\n\x0estocktake_type\x18\t \x01(\t\x12\x15\n\rschedule_code\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x13\n\x0bupper_limit\x18\x0f \x01(\t\x12\x13\n\x0blower_limit\x18\x10 \x01(\t\x12\x14\n\x0cis_wms_store\x18\x11 \x01(\x08\x12\x0b\n\x03lan\x18\x12 \x01(\t\"\xb4\x05\n\x16StocktakeDiffReportRow\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x14\n\x0c\x63ompany_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x12\n\nproduct_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x0c\n\x04type\x18\t \x01(\t\x12\x15\n\rdiff_quantity\x18\n \x01(\x01\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x0c \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\r \x01(\x01\x12 \n\x18\x64iff_quantity_percentage\x18\x0e \x01(\x01\x12/\n\x0btarget_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07unit_id\x18\x10 \x01(\x04\x12\x11\n\tunit_name\x18\x11 \x01(\t\x12\x11\n\tunit_rate\x18\x12 \x01(\x01\x12\x11\n\tunit_code\x18\x13 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x14 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x15 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x16 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x18 \x01(\t\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x19 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x1a \x01(\x01\x12\x11\n\tunit_spec\x18\x1b \x01(\t\x12\x0e\n\x06status\x18\x1c \x01(\t\x12\x15\n\rschedule_code\x18\x1d \x01(\t\"]\n\x1bStocktakeDiffReportResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.warehouse.StocktakeDiffReportRow\x12\r\n\x05total\x18\x02 \x01(\x04\"\x96\x01\n\x17GetUncompleteDocRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x0b\n\x03lan\x18\x04 \x01(\t\"L\n\x11\x43heckDemandDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x0f\n\x07is_plan\x18\x04 \x01(\x08\"\xb9\x05\n\x18GetUncompleteDocResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x36\n\x06\x61\x64just\x18\x02 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12>\n\x0ereceiving_diff\x18\x03 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x38\n\x08transfer\x18\x04 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x39\n\treceiving\x18\x05 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x36\n\x06return\x18\x06 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12@\n\x10\x64irect_receiving\x18\x07 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x45\n\x15\x64irect_receiving_diff\x18\x08 \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12=\n\rdirect_return\x18\t \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12\x39\n\tstocktake\x18\n \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\x12,\n\x06\x64\x65mand\x18\x0b \x03(\x0b\x32\x1c.warehouse.CheckDemandDetail\x12\x36\n\x06\x61ssets\x18\x0c \x03(\x0b\x32&.warehouse.CheckStocktakeByDocIDDetail\"\xab\x01\n\x1bRecreateStocktakeDocRequest\x12\x0f\n\x07\x64oc_ids\x18\x01 \x03(\x04\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x02 \x01(\x08\x12\x15\n\rschedule_name\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x15\n\rschedule_code\x18\x05 \x01(\t\x12\x13\n\x0bschedule_id\x18\x06 \x01(\x04\x12\x0b\n\x03lan\x18\x07 \x01(\t\"s\n\x1cRecreateStocktakeDocResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x1b\n\x13restocktake_doc_ids\x18\x02 \x03(\x04\x12&\n\x1ehas_recreate_doc_id_no_confirm\x18\x03 \x03(\x04\"\xce\x02\n\x1dStocktakeDocStatisticsRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x0fperiod_group_by\x18\x04 \x01(\x0e\x32\x1c.warehouse.PeriodGroupMethod\x12\x16\n\x0estocktake_type\x18\x05 \x03(\t\x12\x0e\n\x06status\x18\x06 \x03(\t\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x03\x12\r\n\x05order\x18\t \x01(\t\x12\x0c\n\x04sort\x18\n \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0b \x01(\x08\x12\x0b\n\x03lan\x18\x0c \x01(\t\"\x97\x01\n\x16StocktakeDocStatistics\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\t\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x16\n\x0estocktake_type\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\r\n\x05\x63ount\x18\x06 \x01(\x01\x12\x10\n\x08store_id\x18\x07 \x01(\x03\"`\n\x1eStocktakeDocStatisticsResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.warehouse.StocktakeDocStatistics\x12\r\n\x05total\x18\x02 \x01(\x03\"\xc7\x03\n!StocktakeDiffCollectReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12/\n\x0btarget_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x16\n\x0estocktake_type\x18\t \x01(\t\x12\x15\n\rschedule_code\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x15\n\rperiod_symbol\x18\x0f \x01(\t\x12\x13\n\x0bupper_limit\x18\x10 \x01(\t\x12\x13\n\x0blower_limit\x18\x11 \x01(\t\x12\x14\n\x0cis_wms_store\x18\x12 \x01(\x08\x12\x0b\n\x03lan\x18\x13 \x01(\t\"\xde\x04\n\x1dStocktakeDiffCollectReportRow\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x14\n\x0c\x63ompany_code\x18\x04 \x01(\t\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x15\n\rdiff_quantity\x18\x08 \x01(\x01\x12\x10\n\x08quantity\x18\t \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\n \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\x0b \x01(\x01\x12 \n\x18\x64iff_quantity_percentage\x18\x0c \x01(\x01\x12\x0f\n\x07unit_id\x18\r \x01(\x04\x12\x11\n\tunit_name\x18\x0e \x01(\t\x12\x11\n\tunit_rate\x18\x0f \x01(\x01\x12\x11\n\tunit_code\x18\x10 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x11 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x12 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x13 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x14 \x01(\t\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x15 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x16 \x01(\x01\x12\x11\n\tunit_spec\x18\x17 \x01(\t\x12\x15\n\rperiod_symbol\x18\x18 \x01(\t\"k\n\"StocktakeDiffCollectReportResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.warehouse.StocktakeDiffCollectReportRow\x12\r\n\x05total\x18\x02 \x01(\x04\"\xbd\x01\n\x1aUncompleteDocReportRequest\x12\x10\n\x08\x62us_date\x18\x01 \x01(\t\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\r\n\x05model\x18\x07 \x03(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x0b\n\x03lan\x18\x10 \x01(\t\"\xcb\x02\n\x1bUncompleteDocReportResponse\x12H\n\x04rows\x18\x01 \x03(\x0b\x32:.warehouse.UncompleteDocReportResponse.UncompleteDocReport\x12\r\n\x05total\x18\x02 \x01(\x04\x1a\xd2\x01\n\x13UncompleteDocReport\x12\r\n\x05model\x18\x01 \x01(\t\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x17\n\x0f\x64oc_update_name\x18\x07 \x01(\t\x12\x17\n\x0f\x64oc_update_time\x18\x08 \x01(\t\x12\x10\n\x08\x64oc_date\x18\t \x01(\t\x12\x10\n\x08\x62us_date\x18\n \x01(\t\x12\x10\n\x08store_id\x18\x0b \x01(\x04\"U\n\x1dStocktakeProductImportRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12\x11\n\tfile_data\x18\x03 \x01(\t\"\xfa\x02\n\x19ProductImportResponseRows\x12\x0f\n\x07row_num\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x14\n\x0cstorage_type\x18\x05 \x01(\t\x12\x0c\n\x04spec\x18\x06 \x01(\t\x12\x0c\n\x04unit\x18\x07 \x01(\t\x12\x11\n\terror_msg\x18\x08 \x01(\t\x12\x13\n\x0bposition_id\x18\t \x01(\t\x12\x15\n\rposition_code\x18\n \x01(\t\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x11\n\tquantity1\x18\x0c \x01(\x01\x12\r\n\x05unit1\x18\r \x01(\t\x12\x11\n\tquantity2\x18\x0e \x01(\x01\x12\r\n\x05unit2\x18\x0f \x01(\t\x12\x11\n\tquantity3\x18\x10 \x01(\x01\x12\r\n\x05unit3\x18\x11 \x01(\t\x12\x11\n\tquantity4\x18\x12 \x01(\x01\x12\r\n\x05unit4\x18\x13 \x01(\t\"\x9b\x01\n\x1eStocktakeProductImportResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x32\n\x04rows\x18\x02 \x03(\x0b\x32$.warehouse.ProductImportResponseRows\x12\x11\n\tfile_name\x18\x03 \x01(\t\x12\x10\n\x08rows_num\x18\x04 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x05 \x01(\x04\"E\n!UpdateStocktakeImportBatchRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"D\n\"UpdateStocktakeImportBatchResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0e\n\x06\x64oc_id\x18\x02 \x01(\x04\"\x9c\x01\n\x19StocktakePositionProducts\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12-\n\x08products\x18\x04 \x03(\x0b\x32\x1b.warehouse.StocktakeProduct\x12\r\n\x05total\x18\x05 \x01(\x04*:\n\x11PeriodGroupMethod\x12\n\n\x06\x42Y_DAY\x10\x00\x12\x0c\n\x08\x42Y_MONTH\x10\x01\x12\x0b\n\x07\x42Y_YEAR\x10\x02\x32\xe4#\n\x12warehouseStockTake\x12\xad\x01\n\x15\x43heckStocktakeByDocID\x12\'.warehouse.CheckStocktakeByDocIDRequest\x1a(.warehouse.CheckStocktakeByDocIDResponse\"A\x82\xd3\xe4\x93\x02;\x1a\x39/api/v2/supply/warehouse/stocktake/{doc_id}/confirm/check\x12\xad\x01\n\x17\x43onfirmStocktakeByDocID\x12).warehouse.ConfirmStocktakeByDocIDRequest\x1a*.warehouse.ConfirmStocktakeByDocIDResponse\";\x82\xd3\xe4\x93\x02\x35\x1a\x33/api/v2/supply/warehouse/stocktake/{doc_id}/confirm\x12\xb0\x01\n\x17\x41pproveStocktakeByDocID\x12).warehouse.ApproveStocktakeByDocIDRequest\x1a*.warehouse.ApproveStocktakeByDocIDResponse\">\x82\xd3\xe4\x93\x02\x38\x1a\x33/api/v2/supply/warehouse/stocktake/{doc_id}/approve:\x01*\x12\xac\x01\n\x16RejectStocktakeProduct\x12(.warehouse.RejectStocktakeProductRequest\x1a).warehouse.RejectStocktakeProductResponse\"=\x82\xd3\xe4\x93\x02\x37\x1a\x32/api/v2/supply/warehouse/stocktake/{doc_id}/reject:\x01*\x12\xa9\x01\n\x16\x43\x61ncelStocktakeByDocID\x12(.warehouse.CancelStocktakeByDocIDRequest\x1a).warehouse.CancelStocktakeByDocIDResponse\":\x82\xd3\xe4\x93\x02\x34\x1a\x32/api/v2/supply/warehouse/stocktake/{doc_id}/cancel\x12\x87\x01\n\x13GetStocktakeByDocID\x12%.warehouse.GetStocktakeByDocIDRequest\x1a\x14.warehouse.Stocktake\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/supply/warehouse/stocktake/{doc_id}\x12{\n\x0cGetStocktake\x12\x1e.warehouse.GetStocktakeRequest\x1a\x1f.warehouse.GetStocktakeResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/supply/warehouse/stocktake\x12\xa1\x01\n\x13GetStocktakeProduct\x12%.warehouse.GetStocktakeProductRequest\x1a&.warehouse.GetStocktakeProductResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/warehouse/stocktake/{doc_id}/product\x12\x9c\x01\n\x13PutStocktakeByDocID\x12%.warehouse.PutStocktakeByDocIDRequest\x1a&.warehouse.PutStocktakeByDocIDResponse\"6\x82\xd3\xe4\x93\x02\x30\x1a+/api/v2/supply/warehouse/stocktake/{doc_id}:\x01*\x12\xb3\x01\n\x17\x43heckedStocktakeByDocID\x12).warehouse.CheckedStocktakeByDocIDRequest\x1a*.warehouse.CheckedStocktakeByDocIDResponse\"A\x82\xd3\xe4\x93\x02;\x1a\x36/api/v2/supply/warehouse/stocktake/{doc_id}/init/check:\x01*\x12\x94\x01\n\x10GetStocktakeTags\x12\".warehouse.GetStocktakeTagsRequest\x1a#.warehouse.GetStocktakeTagsResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/warehouse/stocktake/product/tags\x12\xa7\x01\n\x13\x41\x63tionStocktakeTags\x12%.warehouse.ActionStocktakeTagsRequest\x1a&.warehouse.ActionStocktakeTagsResponse\"A\x82\xd3\xe4\x93\x02;\x1a\x36/api/v2/supply/warehouse/stocktake/product/tags/action:\x01*\x12\xbb\x01\n\x1a\x44\x65leteStocktakeProductTags\x12,.warehouse.DeleteStocktakeProductTagsRequest\x1a-.warehouse.DeleteStocktakeProductTagsResponse\"@\x82\xd3\xe4\x93\x02:\x1a\x35/api/v2/supply/warehouse/stocktake/product/tags/clean:\x01*\x12\x9b\x01\n\x13GetStocktakeBalance\x12%.warehouse.GetStocktakeBalanceRequest\x1a&.warehouse.GetStocktakeBalanceResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/warehouse/stocktake/bi/balance\x12\xac\x01\n\x16SubmitStocktakeByDocID\x12(.warehouse.SubmitStocktakeByDocIDRequest\x1a).warehouse.SubmitStocktakeByDocIDResponse\"=\x82\xd3\xe4\x93\x02\x37\x1a\x32/api/v2/supply/warehouse/stocktake/{doc_id}/submit:\x01*\x12\xd3\x01\n\x1fGetStocktakeBalanceProductGroup\x12\x31.warehouse.GetStocktakeBalanceProductGroupRequest\x1a\x32.warehouse.GetStocktakeBalanceProductGroupResponse\"I\x82\xd3\xe4\x93\x02\x43\x12\x41/api/v2/supply/warehouse/stocktake/balance/{doc_id}/product/group\x12\x9a\x01\n\x13StocktakeBiDetailed\x12%.warehouse.StocktakeBiDetailedRequest\x1a&.warehouse.StocktakeBiDetailedResponse\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/warehouse/stocktake/bi/detail\x12\xaf\x01\n\x16StocktakeBalanceRegion\x12(.warehouse.StocktakeBalanceRegionRequest\x1a).warehouse.StocktakeBalanceRegionResponse\"@\x82\xd3\xe4\x93\x02:\x12\x38/api/v2/supply/warehouse/stocktake/balance/product/group\x12\xa4\x01\n\x14\x41\x64vanceStocktakeDiff\x12&.warehouse.AdvanceStocktakeDiffRequest\x1a\'.warehouse.AdvanceStocktakeDiffResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/warehouse/stocktake/{doc_id}/advance\x12\x9c\x01\n\x13StocktakeDiffReport\x12%.warehouse.StocktakeDiffReportRequest\x1a&.warehouse.StocktakeDiffReportResponse\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/supply/warehouse/stocktake/diff/report\x12\x8c\x01\n\x10GetUncompleteDoc\x12\".warehouse.GetUncompleteDocRequest\x1a#.warehouse.GetUncompleteDocResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/warehouse/uncomplete_doc\x12\xa3\x01\n\x14RecreateStocktakeDoc\x12&.warehouse.RecreateStocktakeDocRequest\x1a\'.warehouse.RecreateStocktakeDocResponse\":\x82\xd3\xe4\x93\x02\x34\"//api/v2/supply/warehouse/recreate_stocktake_doc:\x01*\x12\xab\x01\n\x16StocktakeDocStatistics\x12(.warehouse.StocktakeDocStatisticsRequest\x1a).warehouse.StocktakeDocStatisticsResponse\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/supply/warehouse/stocktake_doc_statistics:\x01*\x12\xb9\x01\n\x1aStocktakeDiffCollectReport\x12,.warehouse.StocktakeDiffCollectReportRequest\x1a-.warehouse.StocktakeDiffCollectReportResponse\">\x82\xd3\xe4\x93\x02\x38\x12\x36/api/v2/supply/warehouse/stocktake/diff_collect/report\x12\x9c\x01\n\x13UncompleteDocReport\x12%.warehouse.UncompleteDocReportRequest\x1a&.warehouse.UncompleteDocReportResponse\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/supply/warehouse/uncomplete_doc_report\x12\xab\x01\n\x16StocktakeProductImport\x12(.warehouse.StocktakeProductImportRequest\x1a).warehouse.StocktakeProductImportResponse\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/supply/warehouse/stocktake/product/import:\x01*\x12\xbc\x01\n\x1aUpdateStocktakeImportBatch\x12,.warehouse.UpdateStocktakeImportBatchRequest\x1a-.warehouse.UpdateStocktakeImportBatchResponse\"A\x82\xd3\xe4\x93\x02;\x1a\x36/api/v2/supply/warehouse/update/stocktake/import/batch:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_PERIODGROUPMETHOD = _descriptor.EnumDescriptor(
  name='PeriodGroupMethod',
  full_name='warehouse.PeriodGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BY_DAY', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_MONTH', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_YEAR', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=23652,
  serialized_end=23710,
)
_sym_db.RegisterEnumDescriptor(_PERIODGROUPMETHOD)

PeriodGroupMethod = enum_type_wrapper.EnumTypeWrapper(_PERIODGROUPMETHOD)
BY_DAY = 0
BY_MONTH = 1
BY_YEAR = 2


_STOCKTAKE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='warehouse.Stocktake.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMIT_0', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVE_0', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1609,
  serialized_end=1744,
)
_sym_db.RegisterEnumDescriptor(_STOCKTAKE_STATUS)

_GETSTOCKTAKEREQUEST_S_STATUS = _descriptor.EnumDescriptor(
  name='S_STATUS',
  full_name='warehouse.GetStocktakeRequest.S_STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OPENED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CLOSED', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2424,
  serialized_end=2458,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_S_STATUS)

_GETSTOCKTAKEREQUEST_S_TYPE = _descriptor.EnumDescriptor(
  name='S_TYPE',
  full_name='warehouse.GetStocktakeRequest.S_TYPE',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='W', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='D', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='M', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2460,
  serialized_end=2489,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_S_TYPE)

_GETSTOCKTAKEREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='warehouse.GetStocktakeRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMIT_0', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVE_0', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1609,
  serialized_end=1744,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_STATUS)

_USERCREATESTOCKTAKERESPONSE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='warehouse.UserCreateStocktakeResponse.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1609,
  serialized_end=1642,
)
_sym_db.RegisterEnumDescriptor(_USERCREATESTOCKTAKERESPONSE_STATUS)

_ACTIONSTOCKTAKETAGSREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='warehouse.ActionStocktakeTagsRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='get', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='create', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='delete', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='update', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='copy', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=10231,
  serialized_end=10294,
)
_sym_db.RegisterEnumDescriptor(_ACTIONSTOCKTAKETAGSREQUEST_ACTION)


_GETPRODUCTBYSTOCKTAKETYPEREQUEST = _descriptor.Descriptor(
  name='GetProductByStocktakeTypeRequest',
  full_name='warehouse.GetProductByStocktakeTypeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.GetProductByStocktakeTypeRequest.stocktake_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.GetProductByStocktakeTypeRequest.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse.GetProductByStocktakeTypeRequest.storage_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetProductByStocktakeTypeRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=103,
  serialized_end=215,
)


_STOCKTAKEPRODUCTTYPE = _descriptor.Descriptor(
  name='StocktakeProductType',
  full_name='warehouse.StocktakeProductType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.StocktakeProductType.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.StocktakeProductType.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.StocktakeProductType.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='warehouse.StocktakeProductType.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse.StocktakeProductType.storage_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=217,
  serialized_end=315,
)


_GETPRODUCTBYSTOCKTAKETYPERESPONSE = _descriptor.Descriptor(
  name='GetProductByStocktakeTypeResponse',
  full_name='warehouse.GetProductByStocktakeTypeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetProductByStocktakeTypeResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=317,
  serialized_end=399,
)


_GETSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='GetStocktakeByDocIDRequest',
  full_name='warehouse.GetStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.GetStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetStocktakeByDocIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=401,
  serialized_end=458,
)


_STOCKTAKE = _descriptor.Descriptor(
  name='Stocktake',
  full_name='warehouse.Stocktake',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.Stocktake.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.Stocktake.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='warehouse.Stocktake.branch_batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.Stocktake.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='warehouse.Stocktake.schedule_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.Stocktake.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='warehouse.Stocktake.calculate_inventory', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.Stocktake.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='warehouse.Stocktake.forecasting', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='warehouse.Stocktake.forecasting_time', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.Stocktake.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='warehouse.Stocktake.result_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.Stocktake.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='warehouse.Stocktake.st_diff_flag', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.Stocktake.status', index=14,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='warehouse.Stocktake.process_status', index=15,
      number=16, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='warehouse.Stocktake.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.Stocktake.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.Stocktake.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.Stocktake.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='warehouse.Stocktake.review_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.Stocktake.target_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.Stocktake.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.Stocktake.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.Stocktake.user_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.Stocktake.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.Stocktake.updated_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='warehouse.Stocktake.schedule_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_err_message', full_name='warehouse.Stocktake.diff_err_message', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_err_message', full_name='warehouse.Stocktake.month_err_message', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_code', full_name='warehouse.Stocktake.original_code', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_doc_id', full_name='warehouse.Stocktake.original_doc_id', index=31,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='warehouse.Stocktake.is_recreate', index=32,
      number=37, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_code', full_name='warehouse.Stocktake.recreate_code', index=33,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_doc_id', full_name='warehouse.Stocktake.recreate_doc_id', index=34,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='warehouse.Stocktake.submit_name', index=35,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='warehouse.Stocktake.approve_name', index=36,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.Stocktake.stocktake_type', index=37,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.Stocktake.request_id', index=38,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='warehouse.Stocktake.total_amount', index=39,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_amount', full_name='warehouse.Stocktake.total_diff_amount', index=40,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='warehouse.Stocktake.total_sales_amount', index=41,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_sales_amount', full_name='warehouse.Stocktake.total_diff_sales_amount', index=42,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='warehouse.Stocktake.attachments', index=43,
      number=54, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _STOCKTAKE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=461,
  serialized_end=1744,
)


_ATTACHMENTSWARE = _descriptor.Descriptor(
  name='AttachmentsWare',
  full_name='warehouse.AttachmentsWare',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.AttachmentsWare.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='warehouse.AttachmentsWare.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1746,
  serialized_end=1790,
)


_GETSTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='GetStocktakeRequest',
  full_name='warehouse.GetStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetStocktakeRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.GetStocktakeRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='warehouse.GetStocktakeRequest.branch_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_status', full_name='warehouse.GetStocktakeRequest.store_status', index=3,
      number=4, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='_type', full_name='warehouse.GetStocktakeRequest._type', index=4,
      number=5, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.GetStocktakeRequest.status', index=5,
      number=6, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.GetStocktakeRequest.schedule_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='warehouse.GetStocktakeRequest.product_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetStocktakeRequest.offset', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetStocktakeRequest.limit', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.GetStocktakeRequest.start_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.GetStocktakeRequest.end_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.GetStocktakeRequest.target_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.GetStocktakeRequest.code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='warehouse.GetStocktakeRequest.ids', index=14,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_create', full_name='warehouse.GetStocktakeRequest.is_create', index=15,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.GetStocktakeRequest.stocktake_type', index=16,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.GetStocktakeRequest.order', index=17,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.GetStocktakeRequest.sort', index=18,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.GetStocktakeRequest.branch_type', index=19,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetStocktakeRequest.lan', index=20,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='warehouse.GetStocktakeRequest.schedule_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETSTOCKTAKEREQUEST_S_STATUS,
    _GETSTOCKTAKEREQUEST_S_TYPE,
    _GETSTOCKTAKEREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1793,
  serialized_end=2627,
)


_GETSTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='GetStocktakeResponse',
  full_name='warehouse.GetStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetStocktakeResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetStocktakeResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2629,
  serialized_end=2702,
)


_GETSTOCKTAKEPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetStocktakeProductRequest',
  full_name='warehouse.GetStocktakeProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.GetStocktakeProductRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_unit', full_name='warehouse.GetStocktakeProductRequest.include_unit', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetStocktakeProductRequest.limit', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetStocktakeProductRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetStocktakeProductRequest.include_total', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='warehouse.GetStocktakeProductRequest.category_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse.GetStocktakeProductRequest.storage_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.GetStocktakeProductRequest.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetStocktakeProductRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_barcode', full_name='warehouse.GetStocktakeProductRequest.include_barcode', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2705,
  serialized_end=2928,
)


_STOCKTAKEPRODUCTTAGNAME = _descriptor.Descriptor(
  name='StocktakeProductTagName',
  full_name='warehouse.StocktakeProductTagName',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.StocktakeProductTagName.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.StocktakeProductTagName.doc_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stp_id', full_name='warehouse.StocktakeProductTagName.stp_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.StocktakeProductTagName.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='warehouse.StocktakeProductTagName.tag_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='warehouse.StocktakeProductTagName.tag_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='warehouse.StocktakeProductTagName.tag_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.StocktakeProductTagName.accounting_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.StocktakeProductTagName.unit_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.StocktakeProductTagName.unit_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeProductTagName.unit_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeProductTagName.unit_rate', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.StocktakeProductTagName.accounting_unit_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.StocktakeProductTagName.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='warehouse.StocktakeProductTagName.accounting_unit_spec', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.StocktakeProductTagName.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.StocktakeProductTagName.user_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.StocktakeProductTagName.created_by', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.StocktakeProductTagName.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.StocktakeProductTagName.created_at', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.StocktakeProductTagName.updated_at', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.StocktakeProductTagName.created_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.StocktakeProductTagName.updated_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='warehouse.StocktakeProductTagName.tax_rate', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='warehouse.StocktakeProductTagName.tax_price', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='warehouse.StocktakeProductTagName.cost_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='warehouse.StocktakeProductTagName.sales_price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2931,
  serialized_end=3562,
)


_STOCKTAKEPRODUCTUNITS = _descriptor.Descriptor(
  name='StocktakeProductUnits',
  full_name='warehouse.StocktakeProductUnits',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.StocktakeProductUnits.unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeProductUnits.unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.StocktakeProductUnits.unit_spec', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeProductUnits.unit_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='warehouse.StocktakeProductUnits.tax_rate', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='warehouse.StocktakeProductUnits.tax_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='warehouse.StocktakeProductUnits.cost_price', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='warehouse.StocktakeProductUnits.sales_price', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='warehouse.StocktakeProductUnits.default', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='warehouse.StocktakeProductUnits.stocktake', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3565,
  serialized_end=3776,
)


_STOCKTAKEPRODUCT = _descriptor.Descriptor(
  name='StocktakeProduct',
  full_name='warehouse.StocktakeProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.StocktakeProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.StocktakeProduct.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.StocktakeProduct.doc_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.StocktakeProduct.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.StocktakeProduct.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.StocktakeProduct.accounting_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.StocktakeProduct.accounting_unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.StocktakeProduct.accounting_unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='warehouse.StocktakeProduct.accounting_unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.StocktakeProduct.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='warehouse.StocktakeProduct.deleted', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='warehouse.StocktakeProduct.diff_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='display_order', full_name='warehouse.StocktakeProduct.display_order', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='warehouse.StocktakeProduct.extends', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignored', full_name='warehouse.StocktakeProduct.ignored', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='warehouse.StocktakeProduct.inventory_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='warehouse.StocktakeProduct.is_system', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='warehouse.StocktakeProduct.item_number', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='warehouse.StocktakeProduct.material_number', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.StocktakeProduct.product_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.StocktakeProduct.product_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.StocktakeProduct.quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_type', full_name='warehouse.StocktakeProduct.st_type', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse.StocktakeProduct.storage_type', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='warehouse.StocktakeProduct.unit_diff_quantity', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.StocktakeProduct.unit_id', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeProduct.unit_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeProduct.unit_rate', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.StocktakeProduct.unit_spec', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='warehouse.StocktakeProduct.units', index=29,
      number=30, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.StocktakeProduct.created_by', index=30,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.StocktakeProduct.updated_by', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.StocktakeProduct.target_date', index=32,
      number=33, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.StocktakeProduct.created_at', index=33,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.StocktakeProduct.updated_at', index=34,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.StocktakeProduct.user_id', index=35,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.StocktakeProduct.status', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='warehouse.StocktakeProduct.is_pda', index=37,
      number=38, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_tags', full_name='warehouse.StocktakeProduct.product_tags', index=38,
      number=39, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='warehouse.StocktakeProduct.is_empty', index=39,
      number=40, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.StocktakeProduct.created_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.StocktakeProduct.updated_name', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='warehouse.StocktakeProduct.is_null', index=42,
      number=43, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='warehouse.StocktakeProduct.tag_quantity', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_accounting_quantity', full_name='warehouse.StocktakeProduct.convert_accounting_quantity', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_bom', full_name='warehouse.StocktakeProduct.is_bom', index=45,
      number=46, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_stocktake_edit', full_name='warehouse.StocktakeProduct.allow_stocktake_edit', index=46,
      number=47, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='warehouse.StocktakeProduct.spec', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_price', full_name='warehouse.StocktakeProduct.diff_price', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='warehouse.StocktakeProduct.barcode', index=49,
      number=50, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='warehouse.StocktakeProduct.category_id', index=50,
      number=54, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.StocktakeProduct.position_id', index=51,
      number=55, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='warehouse.StocktakeProduct.category_name', index=52,
      number=56, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='warehouse.StocktakeProduct.tax_rate', index=53,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='warehouse.StocktakeProduct.tax_price', index=54,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='warehouse.StocktakeProduct.amount', index=55,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_amount', full_name='warehouse.StocktakeProduct.diff_amount', index=56,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='warehouse.StocktakeProduct.cost_price', index=57,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='warehouse.StocktakeProduct.sales_amount', index=58,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='warehouse.StocktakeProduct.sales_price', index=59,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_sales_amount', full_name='warehouse.StocktakeProduct.diff_sales_amount', index=60,
      number=64, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3779,
  serialized_end=5213,
)


_GETSTOCKTAKEPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeProductResponse',
  full_name='warehouse.GetStocktakeProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetStocktakeProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetStocktakeProductResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='warehouse.GetStocktakeProductResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5216,
  serialized_end=5364,
)


_USERCREATESTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='UserCreateStocktakeRequest',
  full_name='warehouse.UserCreateStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.UserCreateStocktakeRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.UserCreateStocktakeRequest.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='_type', full_name='warehouse.UserCreateStocktakeRequest._type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.UserCreateStocktakeRequest.target_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.UserCreateStocktakeRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5367,
  serialized_end=5511,
)


_USERCREATESTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='UserCreateStocktakeResponse',
  full_name='warehouse.UserCreateStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.UserCreateStocktakeResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.UserCreateStocktakeResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='warehouse.UserCreateStocktakeResponse.branch_batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.UserCreateStocktakeResponse.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='warehouse.UserCreateStocktakeResponse.schedule_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.UserCreateStocktakeResponse.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='warehouse.UserCreateStocktakeResponse.calculate_inventory', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.UserCreateStocktakeResponse.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='warehouse.UserCreateStocktakeResponse.forecasting', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='warehouse.UserCreateStocktakeResponse.forecasting_time', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.UserCreateStocktakeResponse.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='warehouse.UserCreateStocktakeResponse.result_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.UserCreateStocktakeResponse.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='warehouse.UserCreateStocktakeResponse.st_diff_flag', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.UserCreateStocktakeResponse.status', index=14,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='warehouse.UserCreateStocktakeResponse.process_status', index=15,
      number=16, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='warehouse.UserCreateStocktakeResponse.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.UserCreateStocktakeResponse.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.UserCreateStocktakeResponse.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.UserCreateStocktakeResponse.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='warehouse.UserCreateStocktakeResponse.review_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.UserCreateStocktakeResponse.target_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.UserCreateStocktakeResponse.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.UserCreateStocktakeResponse.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.UserCreateStocktakeResponse.user_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.UserCreateStocktakeResponse.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.UserCreateStocktakeResponse.updated_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _USERCREATESTOCKTAKERESPONSE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5514,
  serialized_end=6309,
)


_TAGQUANTITY = _descriptor.Descriptor(
  name='TagQuantity',
  full_name='warehouse.TagQuantity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.TagQuantity.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='warehouse.TagQuantity.tag_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='warehouse.TagQuantity.tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='warehouse.TagQuantity.tag_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.TagQuantity.unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6311,
  serialized_end=6409,
)


_PUTSTOCKTAKEPRODUCTS = _descriptor.Descriptor(
  name='PutStocktakeProducts',
  full_name='warehouse.PutStocktakeProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.PutStocktakeProducts.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.PutStocktakeProducts.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.PutStocktakeProducts.unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_products', full_name='warehouse.PutStocktakeProducts.tag_products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='warehouse.PutStocktakeProducts.is_pda', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='warehouse.PutStocktakeProducts.is_empty', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='warehouse.PutStocktakeProducts.is_null', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='del_tag_ids', full_name='warehouse.PutStocktakeProducts.del_tag_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6412,
  serialized_end=6599,
)


_PUTSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='PutStocktakeByDocIDRequest',
  full_name='warehouse.PutStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.PutStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='warehouse.PutStocktakeByDocIDRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.PutStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='warehouse.PutStocktakeByDocIDRequest.all_zero', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6601,
  serialized_end=6727,
)


_REJECTSTOCKTAKEPRODUCTREQUEST = _descriptor.Descriptor(
  name='RejectStocktakeProductRequest',
  full_name='warehouse.RejectStocktakeProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.RejectStocktakeProductRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='warehouse.RejectStocktakeProductRequest.reason', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.RejectStocktakeProductRequest.lan', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6729,
  serialized_end=6805,
)


_REJECTSTOCKTAKEPRODUCTRESPONSE = _descriptor.Descriptor(
  name='RejectStocktakeProductResponse',
  full_name='warehouse.RejectStocktakeProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.RejectStocktakeProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6807,
  serialized_end=6855,
)


_PUTSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='PutStocktakeByDocIDResponse',
  full_name='warehouse.PutStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.PutStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6857,
  serialized_end=6902,
)


_CHECKSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDRequest',
  full_name='warehouse.CheckStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.CheckStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check', full_name='warehouse.CheckStocktakeByDocIDRequest.check', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.CheckStocktakeByDocIDRequest.branch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.CheckStocktakeByDocIDRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6904,
  serialized_end=6999,
)


_CHECKSTOCKTAKEBYDOCIDDETAIL = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDDetail',
  full_name='warehouse.CheckStocktakeByDocIDDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.CheckStocktakeByDocIDDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.CheckStocktakeByDocIDDetail.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.CheckStocktakeByDocIDDetail.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7001,
  serialized_end=7072,
)


_CHECKSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDResponse',
  full_name='warehouse.CheckStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='warehouse.CheckStocktakeByDocIDResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='warehouse.CheckStocktakeByDocIDResponse.adjust', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='warehouse.CheckStocktakeByDocIDResponse.receiving_diff', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='warehouse.CheckStocktakeByDocIDResponse.transfer', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='warehouse.CheckStocktakeByDocIDResponse.receiving', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='warehouse.CheckStocktakeByDocIDResponse.return', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='warehouse.CheckStocktakeByDocIDResponse.direct_receiving', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='warehouse.CheckStocktakeByDocIDResponse.direct_receiving_diff', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='warehouse.CheckStocktakeByDocIDResponse.direct_return', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='warehouse.CheckStocktakeByDocIDResponse.stocktake', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand', full_name='warehouse.CheckStocktakeByDocIDResponse.demand', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_picking', full_name='warehouse.CheckStocktakeByDocIDResponse.self_picking', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7075,
  serialized_end=7793,
)


_CONFIRMSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='ConfirmStocktakeByDocIDRequest',
  full_name='warehouse.ConfirmStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.ConfirmStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check', full_name='warehouse.ConfirmStocktakeByDocIDRequest.check', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.ConfirmStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7795,
  serialized_end=7871,
)


_SUBMITSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='SubmitStocktakeByDocIDResponse',
  full_name='warehouse.SubmitStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.SubmitStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7873,
  serialized_end=7921,
)


_SUBMITSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='SubmitStocktakeByDocIDRequest',
  full_name='warehouse.SubmitStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.SubmitStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='warehouse.SubmitStocktakeByDocIDRequest.submit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.SubmitStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='warehouse.SubmitStocktakeByDocIDRequest.all_zero', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7923,
  serialized_end=8022,
)


_APPROVESTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='ApproveStocktakeByDocIDRequest',
  full_name='warehouse.ApproveStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.ApproveStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.ApproveStocktakeByDocIDRequest.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='warehouse.ApproveStocktakeByDocIDRequest.approve_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.ApproveStocktakeByDocIDRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='warehouse.ApproveStocktakeByDocIDRequest.all_zero', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8024,
  serialized_end=8139,
)


_APPROVESTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='ApproveStocktakeByDocIDResponse',
  full_name='warehouse.ApproveStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.ApproveStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='handler', full_name='warehouse.ApproveStocktakeByDocIDResponse.handler', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='warehouse.ApproveStocktakeByDocIDResponse.adjust', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='warehouse.ApproveStocktakeByDocIDResponse.receiving_diff', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='warehouse.ApproveStocktakeByDocIDResponse.transfer', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='warehouse.ApproveStocktakeByDocIDResponse.receiving', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='warehouse.ApproveStocktakeByDocIDResponse.return', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='warehouse.ApproveStocktakeByDocIDResponse.direct_receiving', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='warehouse.ApproveStocktakeByDocIDResponse.direct_receiving_diff', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='warehouse.ApproveStocktakeByDocIDResponse.direct_return', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_picking', full_name='warehouse.ApproveStocktakeByDocIDResponse.self_picking', index=10,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8142,
  serialized_end=8763,
)


_CONFIRMSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='ConfirmStocktakeByDocIDResponse',
  full_name='warehouse.ConfirmStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.ConfirmStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8765,
  serialized_end=8814,
)


_CANCELSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CancelStocktakeByDocIDRequest',
  full_name='warehouse.CancelStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.CancelStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.CancelStocktakeByDocIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8816,
  serialized_end=8876,
)


_CANCELSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CancelStocktakeByDocIDResponse',
  full_name='warehouse.CancelStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.CancelStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8878,
  serialized_end=8926,
)


_CREATESTOCKTAKEBATCHREQUEST = _descriptor.Descriptor(
  name='CreateStocktakeBatchRequest',
  full_name='warehouse.CreateStocktakeBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.CreateStocktakeBatchRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='warehouse.CreateStocktakeBatchRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.CreateStocktakeBatchRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8928,
  serialized_end=9040,
)


_CREATESTOCKTAKEBATCHRESPONSE = _descriptor.Descriptor(
  name='CreateStocktakeBatchResponse',
  full_name='warehouse.CreateStocktakeBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.CreateStocktakeBatchResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_count', full_name='warehouse.CreateStocktakeBatchResponse.schedule_count', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='warehouse.CreateStocktakeBatchResponse.request_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9042,
  serialized_end=9162,
)


_CREATESTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='CreateStocktakeRequest',
  full_name='warehouse.CreateStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.CreateStocktakeRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='warehouse.CreateStocktakeRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.CreateStocktakeRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9164,
  serialized_end=9271,
)


_CREATESTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='CreateStocktakeResponse',
  full_name='warehouse.CreateStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.CreateStocktakeResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='warehouse.CreateStocktakeResponse.request_date', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9273,
  serialized_end=9364,
)


_CHECKEDSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CheckedStocktakeByDocIDRequest',
  full_name='warehouse.CheckedStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.CheckedStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.CheckedStocktakeByDocIDRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9366,
  serialized_end=9435,
)


_CHECKEDSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CheckedStocktakeByDocIDResponse',
  full_name='warehouse.CheckedStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.CheckedStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9437,
  serialized_end=9486,
)


_GETSTOCKTAKETAGSREQUEST = _descriptor.Descriptor(
  name='GetStocktakeTagsRequest',
  full_name='warehouse.GetStocktakeTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.GetStocktakeTagsRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetStocktakeTagsRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='warehouse.GetStocktakeTagsRequest.tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='warehouse.GetStocktakeTagsRequest.branch_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9488,
  serialized_end=9583,
)


_STOCKTAKETAGS = _descriptor.Descriptor(
  name='StocktakeTags',
  full_name='warehouse.StocktakeTags',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.StocktakeTags.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.StocktakeTags.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.StocktakeTags.partner_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.StocktakeTags.user_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.StocktakeTags.created_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.StocktakeTags.updated_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.StocktakeTags.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.StocktakeTags.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.StocktakeTags.created_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.StocktakeTags.updated_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.StocktakeTags.branch_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='warehouse.StocktakeTags.branch_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9586,
  serialized_end=9884,
)


_GETSTOCKTAKETAGSRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeTagsResponse',
  full_name='warehouse.GetStocktakeTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetStocktakeTagsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9886,
  serialized_end=9952,
)


_ACTIONSTOCKTAKETAGSREQUEST = _descriptor.Descriptor(
  name='ActionStocktakeTagsRequest',
  full_name='warehouse.ActionStocktakeTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='warehouse.ActionStocktakeTagsRequest.tag_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='warehouse.ActionStocktakeTagsRequest.action', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.ActionStocktakeTagsRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.ActionStocktakeTagsRequest.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.ActionStocktakeTagsRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='warehouse.ActionStocktakeTagsRequest.branch_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_ids', full_name='warehouse.ActionStocktakeTagsRequest.region_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='add_dimension', full_name='warehouse.ActionStocktakeTagsRequest.add_dimension', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_ids', full_name='warehouse.ActionStocktakeTagsRequest.tag_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_name', full_name='warehouse.ActionStocktakeTagsRequest.origin_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='copy_branch', full_name='warehouse.ActionStocktakeTagsRequest.copy_branch', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONSTOCKTAKETAGSREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9955,
  serialized_end=10294,
)


_ACTIONSTOCKTAKETAGSRESPONSE = _descriptor.Descriptor(
  name='ActionStocktakeTagsResponse',
  full_name='warehouse.ActionStocktakeTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.ActionStocktakeTagsResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.ActionStocktakeTagsResponse.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.ActionStocktakeTagsResponse.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.ActionStocktakeTagsResponse.partner_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.ActionStocktakeTagsResponse.user_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.ActionStocktakeTagsResponse.created_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.ActionStocktakeTagsResponse.updated_by', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.ActionStocktakeTagsResponse.created_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.ActionStocktakeTagsResponse.updated_at', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.ActionStocktakeTagsResponse.created_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.ActionStocktakeTagsResponse.updated_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.ActionStocktakeTagsResponse.branch_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10297,
  serialized_end=10604,
)


_GETSTOCKTAKETAGSBYIDREQUEST = _descriptor.Descriptor(
  name='GetStocktakeTagsByIdRequest',
  full_name='warehouse.GetStocktakeTagsByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='warehouse.GetStocktakeTagsByIdRequest.tag_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10606,
  serialized_end=10651,
)


_DELETESTOCKTAKEPRODUCTTAGSREQUEST = _descriptor.Descriptor(
  name='DeleteStocktakeProductTagsRequest',
  full_name='warehouse.DeleteStocktakeProductTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.DeleteStocktakeProductTagsRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10653,
  serialized_end=10700,
)


_DELETESTOCKTAKEPRODUCTTAGSRESPONSE = _descriptor.Descriptor(
  name='DeleteStocktakeProductTagsResponse',
  full_name='warehouse.DeleteStocktakeProductTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.DeleteStocktakeProductTagsResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10702,
  serialized_end=10754,
)


_GETSTOCKTAKEBALANCEREQUEST = _descriptor.Descriptor(
  name='GetStocktakeBalanceRequest',
  full_name='warehouse.GetStocktakeBalanceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.GetStocktakeBalanceRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.GetStocktakeBalanceRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.GetStocktakeBalanceRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetStocktakeBalanceRequest.limit', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetStocktakeBalanceRequest.offset', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.GetStocktakeBalanceRequest.target_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.GetStocktakeBalanceRequest.status', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetStocktakeBalanceRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.GetStocktakeBalanceRequest.schedule_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.GetStocktakeBalanceRequest.stocktake_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.GetStocktakeBalanceRequest.is_wms_store', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.GetStocktakeBalanceRequest.branch_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetStocktakeBalanceRequest.lan', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.GetStocktakeBalanceRequest.type', index=13,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10757,
  serialized_end=11134,
)


_STOCKTAKEBALANCE = _descriptor.Descriptor(
  name='StocktakeBalance',
  full_name='warehouse.StocktakeBalance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='warehouse.StocktakeBalance.branch_batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.StocktakeBalance.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.StocktakeBalance.branch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='warehouse.StocktakeBalance.calculate_inventory', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.StocktakeBalance.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='warehouse.StocktakeBalance.forecasting', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='warehouse.StocktakeBalance.forecasting_time', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.StocktakeBalance.id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='warehouse.StocktakeBalance.process_status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.StocktakeBalance.remark', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='warehouse.StocktakeBalance.result_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='warehouse.StocktakeBalance.review_by', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.StocktakeBalance.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='warehouse.StocktakeBalance.schedule_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='warehouse.StocktakeBalance.st_diff_flag', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.StocktakeBalance.status', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='warehouse.StocktakeBalance.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.StocktakeBalance.target_date', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.StocktakeBalance.type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.StocktakeBalance.partner_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.StocktakeBalance.user_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.StocktakeBalance.created_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.StocktakeBalance.updated_by', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.StocktakeBalance.created_name', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.StocktakeBalance.updated_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.StocktakeBalance.created_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.StocktakeBalance.updated_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='warehouse.StocktakeBalance.schedule_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_err_message', full_name='warehouse.StocktakeBalance.diff_err_message', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_err_message', full_name='warehouse.StocktakeBalance.month_err_message', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='warehouse.StocktakeBalance.branch_name', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='warehouse.StocktakeBalance.branch_code', index=31,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_code', full_name='warehouse.StocktakeBalance.original_code', index=32,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_doc_id', full_name='warehouse.StocktakeBalance.original_doc_id', index=33,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='warehouse.StocktakeBalance.is_recreate', index=34,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_code', full_name='warehouse.StocktakeBalance.recreate_code', index=35,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_doc_id', full_name='warehouse.StocktakeBalance.recreate_doc_id', index=36,
      number=41, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='warehouse.StocktakeBalance.submit_name', index=37,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='warehouse.StocktakeBalance.approve_name', index=38,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeBalance.stocktake_type', index=39,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.StocktakeBalance.request_id', index=40,
      number=45, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='warehouse.StocktakeBalance.total_amount', index=41,
      number=46, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_amount', full_name='warehouse.StocktakeBalance.total_diff_amount', index=42,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='warehouse.StocktakeBalance.total_sales_amount', index=43,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_sales_amount', full_name='warehouse.StocktakeBalance.total_diff_sales_amount', index=44,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='warehouse.StocktakeBalance.attachments', index=45,
      number=50, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11137,
  serialized_end=12308,
)


_ATTACHMENTSWAREBALANCE = _descriptor.Descriptor(
  name='AttachmentsWareBalance',
  full_name='warehouse.AttachmentsWareBalance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.AttachmentsWareBalance.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='warehouse.AttachmentsWareBalance.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12310,
  serialized_end=12361,
)


_GETSTOCKTAKEBALANCERESPONSE = _descriptor.Descriptor(
  name='GetStocktakeBalanceResponse',
  full_name='warehouse.GetStocktakeBalanceResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetStocktakeBalanceResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetStocktakeBalanceResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12363,
  serialized_end=12450,
)


_GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST = _descriptor.Descriptor(
  name='GetStocktakeBalanceProductGroupRequest',
  full_name='warehouse.GetStocktakeBalanceProductGroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.GetStocktakeBalanceProductGroupRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetStocktakeBalanceProductGroupRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12452,
  serialized_end=12521,
)


_TAGPRODUCTBI = _descriptor.Descriptor(
  name='TagProductBi',
  full_name='warehouse.TagProductBi',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='warehouse.TagProductBi.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='warehouse.TagProductBi.tag_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_unit_name', full_name='warehouse.TagProductBi.tag_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_uint_rate', full_name='warehouse.TagProductBi.tag_uint_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12523,
  serialized_end=12623,
)


_STOCKTAKEBALANCEPRODUCTGROUP = _descriptor.Descriptor(
  name='StocktakeBalanceProductGroup',
  full_name='warehouse.StocktakeBalanceProductGroup',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.StocktakeBalanceProductGroup.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.StocktakeBalanceProductGroup.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.StocktakeBalanceProductGroup.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='warehouse.StocktakeBalanceProductGroup.accounting_unit_spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='warehouse.StocktakeBalanceProductGroup.diff_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='warehouse.StocktakeBalanceProductGroup.inventory_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='warehouse.StocktakeBalanceProductGroup.is_system', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='warehouse.StocktakeBalanceProductGroup.material_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.StocktakeBalanceProductGroup.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.StocktakeBalanceProductGroup.product_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.StocktakeBalanceProductGroup.product_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.StocktakeBalanceProductGroup.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse.StocktakeBalanceProductGroup.storage_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_code', full_name='warehouse.StocktakeBalanceProductGroup.tag_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='warehouse.StocktakeBalanceProductGroup.tag_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='warehouse.StocktakeBalanceProductGroup.unit_diff_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.StocktakeBalanceProductGroup.unit_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeBalanceProductGroup.unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeBalanceProductGroup.unit_rate', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.StocktakeBalanceProductGroup.unit_spec', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_details', full_name='warehouse.StocktakeBalanceProductGroup.tag_details', index=20,
      number=21, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='warehouse.StocktakeBalanceProductGroup.accounting_inventory_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='warehouse.StocktakeBalanceProductGroup.accounting_diff_quantity', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.StocktakeBalanceProductGroup.position_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.StocktakeBalanceProductGroup.position_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.StocktakeBalanceProductGroup.position_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12626,
  serialized_end=13296,
)


_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeBalanceProductGroupResponse',
  full_name='warehouse.GetStocktakeBalanceProductGroupResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetStocktakeBalanceProductGroupResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13298,
  serialized_end=13394,
)


_STOCKTAKEBIDETAILEDREQUEST = _descriptor.Descriptor(
  name='StocktakeBiDetailedRequest',
  full_name='warehouse.StocktakeBiDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.StocktakeBiDetailedRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.StocktakeBiDetailedRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='warehouse.StocktakeBiDetailedRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.StocktakeBiDetailedRequest.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.StocktakeBiDetailedRequest.offset', index=4,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.StocktakeBiDetailedRequest.limit', index=5,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.StocktakeBiDetailedRequest.include_total', index=6,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.StocktakeBiDetailedRequest.product_name', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.StocktakeBiDetailedRequest.store_ids', index=8,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.StocktakeBiDetailedRequest.order', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.StocktakeBiDetailedRequest.sort', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.StocktakeBiDetailedRequest.code', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.StocktakeBiDetailedRequest.is_wms_store', index=12,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.StocktakeBiDetailedRequest.branch_type', index=13,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.StocktakeBiDetailedRequest.lan', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeBiDetailedRequest.stocktake_type', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13397,
  serialized_end=13773,
)


_STOCKTAKEBIDETAILED = _descriptor.Descriptor(
  name='StocktakeBiDetailed',
  full_name='warehouse.StocktakeBiDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.StocktakeBiDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.StocktakeBiDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.StocktakeBiDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='warehouse.StocktakeBiDetailed.accounting_unit_spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_1_name', full_name='warehouse.StocktakeBiDetailed.branch_1_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_2_name', full_name='warehouse.StocktakeBiDetailed.branch_2_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.StocktakeBiDetailed.branch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='warehouse.StocktakeBiDetailed.category_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='warehouse.StocktakeBiDetailed.category_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='warehouse.StocktakeBiDetailed.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.StocktakeBiDetailed.code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='warehouse.StocktakeBiDetailed.deleted', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='warehouse.StocktakeBiDetailed.diff_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='display_order', full_name='warehouse.StocktakeBiDetailed.display_order', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.StocktakeBiDetailed.doc_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='warehouse.StocktakeBiDetailed.extends', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.StocktakeBiDetailed.id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignored', full_name='warehouse.StocktakeBiDetailed.ignored', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='warehouse.StocktakeBiDetailed.inventory_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='warehouse.StocktakeBiDetailed.is_system', index=19,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='warehouse.StocktakeBiDetailed.item_number', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='warehouse.StocktakeBiDetailed.material_number', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.StocktakeBiDetailed.product_code', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.StocktakeBiDetailed.product_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.StocktakeBiDetailed.product_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.StocktakeBiDetailed.quantity', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.StocktakeBiDetailed.type', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse.StocktakeBiDetailed.storage_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.StocktakeBiDetailed.store_code', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.StocktakeBiDetailed.store_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.StocktakeBiDetailed.target_date', index=30,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='warehouse.StocktakeBiDetailed.unit_diff_quantity', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.StocktakeBiDetailed.unit_id', index=32,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeBiDetailed.unit_name', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeBiDetailed.unit_rate', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.StocktakeBiDetailed.unit_spec', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.StocktakeBiDetailed.partner_id', index=36,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.StocktakeBiDetailed.created_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.StocktakeBiDetailed.updated_by', index=38,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.StocktakeBiDetailed.created_name', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.StocktakeBiDetailed.updated_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.StocktakeBiDetailed.created_at', index=41,
      number=42, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.StocktakeBiDetailed.updated_at', index=42,
      number=43, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='warehouse.StocktakeBiDetailed.is_empty', index=43,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='warehouse.StocktakeBiDetailed.is_pda', index=44,
      number=45, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.StocktakeBiDetailed.status', index=45,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='warehouse.StocktakeBiDetailed.units', index=46,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.StocktakeBiDetailed.user_id', index=47,
      number=48, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='warehouse.StocktakeBiDetailed.is_null', index=48,
      number=49, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='warehouse.StocktakeBiDetailed.tag_quantity', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_tags', full_name='warehouse.StocktakeBiDetailed.product_tags', index=50,
      number=51, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_enable', full_name='warehouse.StocktakeBiDetailed.is_enable', index=51,
      number=52, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='warehouse.StocktakeBiDetailed.spec', index=52,
      number=54, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_percentage', full_name='warehouse.StocktakeBiDetailed.diff_percentage', index=53,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_time', full_name='warehouse.StocktakeBiDetailed.created_time', index=54,
      number=56, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_user_name', full_name='warehouse.StocktakeBiDetailed.created_user_name', index=55,
      number=57, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submitted_time', full_name='warehouse.StocktakeBiDetailed.submitted_time', index=56,
      number=58, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submitted_user_name', full_name='warehouse.StocktakeBiDetailed.submitted_user_name', index=57,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.StocktakeBiDetailed.branch_type', index=58,
      number=60, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.StocktakeBiDetailed.position_id', index=59,
      number=62, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.StocktakeBiDetailed.position_code', index=60,
      number=63, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.StocktakeBiDetailed.position_name', index=61,
      number=64, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeBiDetailed.stocktake_type', index=62,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.StocktakeBiDetailed.schedule_code', index=63,
      number=65, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='warehouse.StocktakeBiDetailed.schedule_name', index=64,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13776,
  serialized_end=15329,
)


_PRODUCTTAGBI = _descriptor.Descriptor(
  name='ProductTagBi',
  full_name='warehouse.ProductTagBi',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_unit_id', full_name='warehouse.ProductTagBi.tag_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_unit_name', full_name='warehouse.ProductTagBi.tag_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='warehouse.ProductTagBi.tag_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='warehouse.ProductTagBi.tag_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.ProductTagBi.accounting_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.ProductTagBi.accounting_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.ProductTagBi.accounting_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.ProductTagBi.id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15332,
  serialized_end=15529,
)


_ST_TOTAL = _descriptor.Descriptor(
  name='ST_total',
  full_name='warehouse.ST_total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='warehouse.ST_total.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='warehouse.ST_total.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='warehouse.ST_total.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15531,
  serialized_end=15611,
)


_STOCKTAKEBIDETAILEDRESPONSE = _descriptor.Descriptor(
  name='StocktakeBiDetailedResponse',
  full_name='warehouse.StocktakeBiDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.StocktakeBiDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.StocktakeBiDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15613,
  serialized_end=15724,
)


_STOCKTAKEBALANCEREGIONREQUEST = _descriptor.Descriptor(
  name='StocktakeBalanceRegionRequest',
  full_name='warehouse.StocktakeBalanceRegionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.StocktakeBalanceRegionRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.StocktakeBalanceRegionRequest.limit', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.StocktakeBalanceRegionRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='warehouse.StocktakeBalanceRegionRequest.start', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='warehouse.StocktakeBalanceRegionRequest.end', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_id', full_name='warehouse.StocktakeBalanceRegionRequest.region_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='warehouse.StocktakeBalanceRegionRequest.product_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.StocktakeBalanceRegionRequest.type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.StocktakeBalanceRegionRequest.branch_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.StocktakeBalanceRegionRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeBalanceRegionRequest.stocktake_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15727,
  serialized_end=16008,
)


_STOCKTAKEBALANCEREGIONDETAILS = _descriptor.Descriptor(
  name='StocktakeBalanceRegionDetails',
  full_name='warehouse.StocktakeBalanceRegionDetails',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='warehouse.StocktakeBalanceRegionDetails.accounting_inventory_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.StocktakeBalanceRegionDetails.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.StocktakeBalanceRegionDetails.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='warehouse.StocktakeBalanceRegionDetails.branch_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='warehouse.StocktakeBalanceRegionDetails.diff_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_type', full_name='warehouse.StocktakeBalanceRegionDetails.doc_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='warehouse.StocktakeBalanceRegionDetails.inventory_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='warehouse.StocktakeBalanceRegionDetails.material_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.StocktakeBalanceRegionDetails.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.StocktakeBalanceRegionDetails.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.StocktakeBalanceRegionDetails.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.StocktakeBalanceRegionDetails.store_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.StocktakeBalanceRegionDetails.store_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_1', full_name='warehouse.StocktakeBalanceRegionDetails.tag_1', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_1_name_pinyin', full_name='warehouse.StocktakeBalanceRegionDetails.tag_1_name_pinyin', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='warehouse.StocktakeBalanceRegionDetails.unit_diff_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeBalanceRegionDetails.unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeBalanceRegionDetails.unit_rate', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16011,
  serialized_end=16461,
)


_STOCKTAKEBALANCEREGION = _descriptor.Descriptor(
  name='StocktakeBalanceRegion',
  full_name='warehouse.StocktakeBalanceRegion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='warehouse.StocktakeBalanceRegion.accounting_inventory_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.StocktakeBalanceRegion.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.StocktakeBalanceRegion.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='warehouse.StocktakeBalanceRegion.details', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeBalanceRegion.unit_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.StocktakeBalanceRegion.product_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.StocktakeBalanceRegion.quantity', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='warehouse.StocktakeBalanceRegion.inventory_quantity', index=7,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accoinventory_quantity', full_name='warehouse.StocktakeBalanceRegion.accoinventory_quantity', index=8,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='warehouse.StocktakeBalanceRegion.diff_quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='warehouse.StocktakeBalanceRegion.unit_diff_quantity', index=10,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeBalanceRegion.unit_rate', index=11,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.StocktakeBalanceRegion.type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.StocktakeBalanceRegion.product_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.StocktakeBalanceRegion.store_id', index=14,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.StocktakeBalanceRegion.store_name', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.StocktakeBalanceRegion.store_code', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.StocktakeBalanceRegion.product_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='warehouse.StocktakeBalanceRegion.accounting_diff_quantity', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeBalanceRegion.stocktake_type', index=19,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16464,
  serialized_end=17006,
)


_STOCKTAKEBALANCEREGIONRESPONSE = _descriptor.Descriptor(
  name='StocktakeBalanceRegionResponse',
  full_name='warehouse.StocktakeBalanceRegionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.StocktakeBalanceRegionResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.StocktakeBalanceRegionResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17008,
  serialized_end=17104,
)


_STOREDATASCOPEREQUEST = _descriptor.Descriptor(
  name='StoreDataScopeRequest',
  full_name='warehouse.StoreDataScopeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='search', full_name='warehouse.StoreDataScopeRequest.search', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='warehouse.StoreDataScopeRequest.search_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='warehouse.StoreDataScopeRequest.ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='warehouse.StoreDataScopeRequest.return_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='warehouse.StoreDataScopeRequest.filters', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_filters', full_name='warehouse.StoreDataScopeRequest.relation_filters', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.StoreDataScopeRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.StoreDataScopeRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.StoreDataScopeRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17107,
  serialized_end=17292,
)


_SCOPESTORES = _descriptor.Descriptor(
  name='ScopeStores',
  full_name='warehouse.ScopeStores',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.ScopeStores.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.ScopeStores.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.ScopeStores.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='second_code', full_name='warehouse.ScopeStores.second_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.ScopeStores.type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='warehouse.ScopeStores.address', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='warehouse.ScopeStores.tel', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='warehouse.ScopeStores.contact', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.ScopeStores.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name_en', full_name='warehouse.ScopeStores.name_en', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='open_date', full_name='warehouse.ScopeStores.open_date', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_date', full_name='warehouse.ScopeStores.close_date', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='email', full_name='warehouse.ScopeStores.email', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_region', full_name='warehouse.ScopeStores.geo_region', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region', full_name='warehouse.ScopeStores.branch_region', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_region', full_name='warehouse.ScopeStores.order_region', index=15,
      number=16, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_region', full_name='warehouse.ScopeStores.distribution_region', index=16,
      number=17, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_region', full_name='warehouse.ScopeStores.purchase_region', index=17,
      number=18, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='market_region', full_name='warehouse.ScopeStores.market_region', index=18,
      number=19, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_region', full_name='warehouse.ScopeStores.transfer_region', index=19,
      number=20, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attribute_region', full_name='warehouse.ScopeStores.attribute_region', index=20,
      number=21, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17295,
  serialized_end=17710,
)


_STOREDATASCOPE = _descriptor.Descriptor(
  name='StoreDataScope',
  full_name='warehouse.StoreDataScope',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.StoreDataScope.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.StoreDataScope.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17712,
  serialized_end=17781,
)


_ADVANCESTOCKTAKEDIFFREQUEST = _descriptor.Descriptor(
  name='AdvanceStocktakeDiffRequest',
  full_name='warehouse.AdvanceStocktakeDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.AdvanceStocktakeDiffRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.AdvanceStocktakeDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17783,
  serialized_end=17841,
)


_ADVANCESTOCKTAKEDIFFRESPONSE = _descriptor.Descriptor(
  name='AdvanceStocktakeDiffResponse',
  full_name='warehouse.AdvanceStocktakeDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.AdvanceStocktakeDiffResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.AdvanceStocktakeDiffResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='warehouse.AdvanceStocktakeDiffResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17844,
  serialized_end=17993,
)


_STOCKTAKEDIFFREPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeDiffReportRequest',
  full_name='warehouse.StocktakeDiffReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.StocktakeDiffReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.StocktakeDiffReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.StocktakeDiffReportRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='warehouse.StocktakeDiffReportRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.StocktakeDiffReportRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.StocktakeDiffReportRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.StocktakeDiffReportRequest.target_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.StocktakeDiffReportRequest.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeDiffReportRequest.stocktake_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.StocktakeDiffReportRequest.schedule_code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.StocktakeDiffReportRequest.status', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.StocktakeDiffReportRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.StocktakeDiffReportRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upper_limit', full_name='warehouse.StocktakeDiffReportRequest.upper_limit', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lower_limit', full_name='warehouse.StocktakeDiffReportRequest.lower_limit', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.StocktakeDiffReportRequest.is_wms_store', index=15,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.StocktakeDiffReportRequest.lan', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17996,
  serialized_end=18421,
)


_STOCKTAKEDIFFREPORTROW = _descriptor.Descriptor(
  name='StocktakeDiffReportRow',
  full_name='warehouse.StocktakeDiffReportRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.StocktakeDiffReportRow.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.StocktakeDiffReportRow.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.StocktakeDiffReportRow.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.StocktakeDiffReportRow.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='warehouse.StocktakeDiffReportRow.company_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.StocktakeDiffReportRow.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.StocktakeDiffReportRow.product_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.StocktakeDiffReportRow.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.StocktakeDiffReportRow.type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='warehouse.StocktakeDiffReportRow.diff_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.StocktakeDiffReportRow.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.StocktakeDiffReportRow.accounting_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='warehouse.StocktakeDiffReportRow.inventory_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity_percentage', full_name='warehouse.StocktakeDiffReportRow.diff_quantity_percentage', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.StocktakeDiffReportRow.target_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.StocktakeDiffReportRow.unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeDiffReportRow.unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeDiffReportRow.unit_rate', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='warehouse.StocktakeDiffReportRow.unit_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.StocktakeDiffReportRow.accounting_unit_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.StocktakeDiffReportRow.accounting_unit_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='warehouse.StocktakeDiffReportRow.accounting_unit_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='warehouse.StocktakeDiffReportRow.company_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='warehouse.StocktakeDiffReportRow.accounting_inventory_quantity', index=23,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='warehouse.StocktakeDiffReportRow.accounting_diff_quantity', index=24,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.StocktakeDiffReportRow.unit_spec', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.StocktakeDiffReportRow.status', index=26,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.StocktakeDiffReportRow.schedule_code', index=27,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18424,
  serialized_end=19116,
)


_STOCKTAKEDIFFREPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeDiffReportResponse',
  full_name='warehouse.StocktakeDiffReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.StocktakeDiffReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.StocktakeDiffReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19118,
  serialized_end=19211,
)


_GETUNCOMPLETEDOCREQUEST = _descriptor.Descriptor(
  name='GetUncompleteDocRequest',
  full_name='warehouse.GetUncompleteDocRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.GetUncompleteDocRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.GetUncompleteDocRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.GetUncompleteDocRequest.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetUncompleteDocRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19214,
  serialized_end=19364,
)


_CHECKDEMANDDETAIL = _descriptor.Descriptor(
  name='CheckDemandDetail',
  full_name='warehouse.CheckDemandDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.CheckDemandDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.CheckDemandDetail.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.CheckDemandDetail.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_plan', full_name='warehouse.CheckDemandDetail.is_plan', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19366,
  serialized_end=19442,
)


_GETUNCOMPLETEDOCRESPONSE = _descriptor.Descriptor(
  name='GetUncompleteDocResponse',
  full_name='warehouse.GetUncompleteDocResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='warehouse.GetUncompleteDocResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='warehouse.GetUncompleteDocResponse.adjust', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='warehouse.GetUncompleteDocResponse.receiving_diff', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='warehouse.GetUncompleteDocResponse.transfer', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='warehouse.GetUncompleteDocResponse.receiving', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='warehouse.GetUncompleteDocResponse.return', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='warehouse.GetUncompleteDocResponse.direct_receiving', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='warehouse.GetUncompleteDocResponse.direct_receiving_diff', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='warehouse.GetUncompleteDocResponse.direct_return', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='warehouse.GetUncompleteDocResponse.stocktake', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand', full_name='warehouse.GetUncompleteDocResponse.demand', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='assets', full_name='warehouse.GetUncompleteDocResponse.assets', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19445,
  serialized_end=20142,
)


_RECREATESTOCKTAKEDOCREQUEST = _descriptor.Descriptor(
  name='RecreateStocktakeDocRequest',
  full_name='warehouse.RecreateStocktakeDocRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_ids', full_name='warehouse.RecreateStocktakeDocRequest.doc_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='warehouse.RecreateStocktakeDocRequest.calculate_inventory', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='warehouse.RecreateStocktakeDocRequest.schedule_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.RecreateStocktakeDocRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.RecreateStocktakeDocRequest.schedule_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='warehouse.RecreateStocktakeDocRequest.schedule_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.RecreateStocktakeDocRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20145,
  serialized_end=20316,
)


_RECREATESTOCKTAKEDOCRESPONSE = _descriptor.Descriptor(
  name='RecreateStocktakeDocResponse',
  full_name='warehouse.RecreateStocktakeDocResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.RecreateStocktakeDocResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='restocktake_doc_ids', full_name='warehouse.RecreateStocktakeDocResponse.restocktake_doc_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_recreate_doc_id_no_confirm', full_name='warehouse.RecreateStocktakeDocResponse.has_recreate_doc_id_no_confirm', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20318,
  serialized_end=20433,
)


_STOCKTAKEDOCSTATISTICSREQUEST = _descriptor.Descriptor(
  name='StocktakeDocStatisticsRequest',
  full_name='warehouse.StocktakeDocStatisticsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.StocktakeDocStatisticsRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.StocktakeDocStatisticsRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.StocktakeDocStatisticsRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='warehouse.StocktakeDocStatisticsRequest.period_group_by', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeDocStatisticsRequest.stocktake_type', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.StocktakeDocStatisticsRequest.status', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.StocktakeDocStatisticsRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.StocktakeDocStatisticsRequest.offset', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.StocktakeDocStatisticsRequest.order', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.StocktakeDocStatisticsRequest.sort', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.StocktakeDocStatisticsRequest.is_wms_store', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.StocktakeDocStatisticsRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20436,
  serialized_end=20770,
)


_STOCKTAKEDOCSTATISTICS = _descriptor.Descriptor(
  name='StocktakeDocStatistics',
  full_name='warehouse.StocktakeDocStatistics',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='date', full_name='warehouse.StocktakeDocStatistics.date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.StocktakeDocStatistics.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.StocktakeDocStatistics.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeDocStatistics.stocktake_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.StocktakeDocStatistics.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='warehouse.StocktakeDocStatistics.count', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.StocktakeDocStatistics.store_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20773,
  serialized_end=20924,
)


_STOCKTAKEDOCSTATISTICSRESPONSE = _descriptor.Descriptor(
  name='StocktakeDocStatisticsResponse',
  full_name='warehouse.StocktakeDocStatisticsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.StocktakeDocStatisticsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.StocktakeDocStatisticsResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20926,
  serialized_end=21022,
)


_STOCKTAKEDIFFCOLLECTREPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportRequest',
  full_name='warehouse.StocktakeDiffCollectReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.StocktakeDiffCollectReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.StocktakeDiffCollectReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.StocktakeDiffCollectReportRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='warehouse.StocktakeDiffCollectReportRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.StocktakeDiffCollectReportRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.StocktakeDiffCollectReportRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='warehouse.StocktakeDiffCollectReportRequest.target_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.StocktakeDiffCollectReportRequest.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='warehouse.StocktakeDiffCollectReportRequest.stocktake_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.StocktakeDiffCollectReportRequest.schedule_code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.StocktakeDiffCollectReportRequest.status', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.StocktakeDiffCollectReportRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.StocktakeDiffCollectReportRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='warehouse.StocktakeDiffCollectReportRequest.period_symbol', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upper_limit', full_name='warehouse.StocktakeDiffCollectReportRequest.upper_limit', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lower_limit', full_name='warehouse.StocktakeDiffCollectReportRequest.lower_limit', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.StocktakeDiffCollectReportRequest.is_wms_store', index=16,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.StocktakeDiffCollectReportRequest.lan', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21025,
  serialized_end=21480,
)


_STOCKTAKEDIFFCOLLECTREPORTROW = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportRow',
  full_name='warehouse.StocktakeDiffCollectReportRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.StocktakeDiffCollectReportRow.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.StocktakeDiffCollectReportRow.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.StocktakeDiffCollectReportRow.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='warehouse.StocktakeDiffCollectReportRow.company_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.StocktakeDiffCollectReportRow.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.StocktakeDiffCollectReportRow.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.StocktakeDiffCollectReportRow.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='warehouse.StocktakeDiffCollectReportRow.diff_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.StocktakeDiffCollectReportRow.quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.StocktakeDiffCollectReportRow.accounting_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='warehouse.StocktakeDiffCollectReportRow.inventory_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity_percentage', full_name='warehouse.StocktakeDiffCollectReportRow.diff_quantity_percentage', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.StocktakeDiffCollectReportRow.unit_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.StocktakeDiffCollectReportRow.unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.StocktakeDiffCollectReportRow.unit_rate', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='warehouse.StocktakeDiffCollectReportRow.unit_code', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.StocktakeDiffCollectReportRow.accounting_unit_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.StocktakeDiffCollectReportRow.accounting_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='warehouse.StocktakeDiffCollectReportRow.accounting_unit_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='warehouse.StocktakeDiffCollectReportRow.company_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='warehouse.StocktakeDiffCollectReportRow.accounting_inventory_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='warehouse.StocktakeDiffCollectReportRow.accounting_diff_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.StocktakeDiffCollectReportRow.unit_spec', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='warehouse.StocktakeDiffCollectReportRow.period_symbol', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21483,
  serialized_end=22089,
)


_STOCKTAKEDIFFCOLLECTREPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportResponse',
  full_name='warehouse.StocktakeDiffCollectReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.StocktakeDiffCollectReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.StocktakeDiffCollectReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22091,
  serialized_end=22198,
)


_UNCOMPLETEDOCREPORTREQUEST = _descriptor.Descriptor(
  name='UncompleteDocReportRequest',
  full_name='warehouse.UncompleteDocReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bus_date', full_name='warehouse.UncompleteDocReportRequest.bus_date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.UncompleteDocReportRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.UncompleteDocReportRequest.code', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.UncompleteDocReportRequest.limit', index=3,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.UncompleteDocReportRequest.offset', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model', full_name='warehouse.UncompleteDocReportRequest.model', index=5,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.UncompleteDocReportRequest.order', index=6,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.UncompleteDocReportRequest.sort', index=7,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.UncompleteDocReportRequest.is_wms_store', index=8,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.UncompleteDocReportRequest.lan', index=9,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22201,
  serialized_end=22390,
)


_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT = _descriptor.Descriptor(
  name='UncompleteDocReport',
  full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='model', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.model', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.code', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_update_name', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.doc_update_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_update_time', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.doc_update_time', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_date', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.doc_date', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_date', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.bus_date', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.UncompleteDocReportResponse.UncompleteDocReport.store_id', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22514,
  serialized_end=22724,
)

_UNCOMPLETEDOCREPORTRESPONSE = _descriptor.Descriptor(
  name='UncompleteDocReportResponse',
  full_name='warehouse.UncompleteDocReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.UncompleteDocReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.UncompleteDocReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22393,
  serialized_end=22724,
)


_STOCKTAKEPRODUCTIMPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeProductImportRequest',
  full_name='warehouse.StocktakeProductImportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.StocktakeProductImportRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='warehouse.StocktakeProductImportRequest.file_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_data', full_name='warehouse.StocktakeProductImportRequest.file_data', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22726,
  serialized_end=22811,
)


_PRODUCTIMPORTRESPONSEROWS = _descriptor.Descriptor(
  name='ProductImportResponseRows',
  full_name='warehouse.ProductImportResponseRows',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_num', full_name='warehouse.ProductImportResponseRows.row_num', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.ProductImportResponseRows.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.ProductImportResponseRows.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.ProductImportResponseRows.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse.ProductImportResponseRows.storage_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='warehouse.ProductImportResponseRows.spec', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='warehouse.ProductImportResponseRows.unit', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_msg', full_name='warehouse.ProductImportResponseRows.error_msg', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.ProductImportResponseRows.position_id', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.ProductImportResponseRows.position_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.ProductImportResponseRows.position_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity1', full_name='warehouse.ProductImportResponseRows.quantity1', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit1', full_name='warehouse.ProductImportResponseRows.unit1', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity2', full_name='warehouse.ProductImportResponseRows.quantity2', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit2', full_name='warehouse.ProductImportResponseRows.unit2', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity3', full_name='warehouse.ProductImportResponseRows.quantity3', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit3', full_name='warehouse.ProductImportResponseRows.unit3', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity4', full_name='warehouse.ProductImportResponseRows.quantity4', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit4', full_name='warehouse.ProductImportResponseRows.unit4', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22814,
  serialized_end=23192,
)


_STOCKTAKEPRODUCTIMPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeProductImportResponse',
  full_name='warehouse.StocktakeProductImportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.StocktakeProductImportResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.StocktakeProductImportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='warehouse.StocktakeProductImportResponse.file_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows_num', full_name='warehouse.StocktakeProductImportResponse.rows_num', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='warehouse.StocktakeProductImportResponse.batch_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23195,
  serialized_end=23350,
)


_UPDATESTOCKTAKEIMPORTBATCHREQUEST = _descriptor.Descriptor(
  name='UpdateStocktakeImportBatchRequest',
  full_name='warehouse.UpdateStocktakeImportBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='warehouse.UpdateStocktakeImportBatchRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.UpdateStocktakeImportBatchRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23352,
  serialized_end=23421,
)


_UPDATESTOCKTAKEIMPORTBATCHRESPONSE = _descriptor.Descriptor(
  name='UpdateStocktakeImportBatchResponse',
  full_name='warehouse.UpdateStocktakeImportBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.UpdateStocktakeImportBatchResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='warehouse.UpdateStocktakeImportBatchResponse.doc_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23423,
  serialized_end=23491,
)


_STOCKTAKEPOSITIONPRODUCTS = _descriptor.Descriptor(
  name='StocktakePositionProducts',
  full_name='warehouse.StocktakePositionProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.StocktakePositionProducts.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.StocktakePositionProducts.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.StocktakePositionProducts.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='warehouse.StocktakePositionProducts.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.StocktakePositionProducts.total', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23494,
  serialized_end=23650,
)

_GETPRODUCTBYSTOCKTAKETYPERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCTTYPE
_STOCKTAKE.fields_by_name['status'].enum_type = _STOCKTAKE_STATUS
_STOCKTAKE.fields_by_name['process_status'].enum_type = _STOCKTAKE_STATUS
_STOCKTAKE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['attachments'].message_type = _ATTACHMENTSWARE
_STOCKTAKE_STATUS.containing_type = _STOCKTAKE
_GETSTOCKTAKEREQUEST.fields_by_name['store_status'].enum_type = _GETSTOCKTAKEREQUEST_S_STATUS
_GETSTOCKTAKEREQUEST.fields_by_name['_type'].enum_type = _GETSTOCKTAKEREQUEST_S_TYPE
_GETSTOCKTAKEREQUEST.fields_by_name['status'].enum_type = _GETSTOCKTAKEREQUEST_STATUS
_GETSTOCKTAKEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST_S_STATUS.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKEREQUEST_S_TYPE.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKEREQUEST_STATUS.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKE
_STOCKTAKEPRODUCTTAGNAME.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCTTAGNAME.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['units'].message_type = _STOCKTAKEPRODUCTUNITS
_STOCKTAKEPRODUCT.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['product_tags'].message_type = _STOCKTAKEPRODUCTTAGNAME
_GETSTOCKTAKEPRODUCTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCT
_GETSTOCKTAKEPRODUCTRESPONSE.fields_by_name['position_rows'].message_type = _STOCKTAKEPOSITIONPRODUCTS
_USERCREATESTOCKTAKEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['status'].enum_type = _USERCREATESTOCKTAKERESPONSE_STATUS
_USERCREATESTOCKTAKERESPONSE.fields_by_name['process_status'].enum_type = _USERCREATESTOCKTAKERESPONSE_STATUS
_USERCREATESTOCKTAKERESPONSE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE_STATUS.containing_type = _USERCREATESTOCKTAKERESPONSE
_PUTSTOCKTAKEPRODUCTS.fields_by_name['tag_products'].message_type = _TAGQUANTITY
_PUTSTOCKTAKEBYDOCIDREQUEST.fields_by_name['products'].message_type = _PUTSTOCKTAKEPRODUCTS
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['stocktake'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['demand'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['self_picking'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['self_picking'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CREATESTOCKTAKEBATCHREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKEBATCHRESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKEREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKERESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKETAGS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKETAGS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKETAGSRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKETAGS
_ACTIONSTOCKTAKETAGSREQUEST.fields_by_name['action'].enum_type = _ACTIONSTOCKTAKETAGSREQUEST_ACTION
_ACTIONSTOCKTAKETAGSREQUEST_ACTION.containing_type = _ACTIONSTOCKTAKETAGSREQUEST
_ACTIONSTOCKTAKETAGSRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ACTIONSTOCKTAKETAGSRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['forecasting_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['attachments'].message_type = _ATTACHMENTSWAREBALANCE
_GETSTOCKTAKEBALANCERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCE
_STOCKTAKEBALANCEPRODUCTGROUP.fields_by_name['tag_details'].message_type = _TAGPRODUCTBI
_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCEPRODUCTGROUP
_STOCKTAKEBIDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['product_tags'].message_type = _PRODUCTTAGBI
_STOCKTAKEBIDETAILED.fields_by_name['created_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['submitted_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILEDRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBIDETAILED
_STOCKTAKEBIDETAILEDRESPONSE.fields_by_name['total'].message_type = _ST_TOTAL
_STOCKTAKEBALANCEREGIONREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCEREGIONREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCEREGION.fields_by_name['details'].message_type = _STOCKTAKEBALANCEREGIONDETAILS
_STOCKTAKEBALANCEREGIONRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCEREGION
_STOREDATASCOPE.fields_by_name['rows'].message_type = _SCOPESTORES
_ADVANCESTOCKTAKEDIFFRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCT
_ADVANCESTOCKTAKEDIFFRESPONSE.fields_by_name['position_rows'].message_type = _STOCKTAKEPOSITIONPRODUCTS
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTROW.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDIFFREPORTROW
_GETUNCOMPLETEDOCREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNCOMPLETEDOCREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['stocktake'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['demand'].message_type = _CHECKDEMANDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['assets'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_STOCKTAKEDOCSTATISTICSRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDOCSTATISTICS
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDIFFCOLLECTREPORTROW
_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT.containing_type = _UNCOMPLETEDOCREPORTRESPONSE
_UNCOMPLETEDOCREPORTRESPONSE.fields_by_name['rows'].message_type = _UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT
_STOCKTAKEPRODUCTIMPORTRESPONSE.fields_by_name['rows'].message_type = _PRODUCTIMPORTRESPONSEROWS
_STOCKTAKEPOSITIONPRODUCTS.fields_by_name['products'].message_type = _STOCKTAKEPRODUCT
DESCRIPTOR.message_types_by_name['GetProductByStocktakeTypeRequest'] = _GETPRODUCTBYSTOCKTAKETYPEREQUEST
DESCRIPTOR.message_types_by_name['StocktakeProductType'] = _STOCKTAKEPRODUCTTYPE
DESCRIPTOR.message_types_by_name['GetProductByStocktakeTypeResponse'] = _GETPRODUCTBYSTOCKTAKETYPERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeByDocIDRequest'] = _GETSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['Stocktake'] = _STOCKTAKE
DESCRIPTOR.message_types_by_name['AttachmentsWare'] = _ATTACHMENTSWARE
DESCRIPTOR.message_types_by_name['GetStocktakeRequest'] = _GETSTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['GetStocktakeResponse'] = _GETSTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeProductRequest'] = _GETSTOCKTAKEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeProductTagName'] = _STOCKTAKEPRODUCTTAGNAME
DESCRIPTOR.message_types_by_name['StocktakeProductUnits'] = _STOCKTAKEPRODUCTUNITS
DESCRIPTOR.message_types_by_name['StocktakeProduct'] = _STOCKTAKEPRODUCT
DESCRIPTOR.message_types_by_name['GetStocktakeProductResponse'] = _GETSTOCKTAKEPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['UserCreateStocktakeRequest'] = _USERCREATESTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['UserCreateStocktakeResponse'] = _USERCREATESTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['TagQuantity'] = _TAGQUANTITY
DESCRIPTOR.message_types_by_name['PutStocktakeProducts'] = _PUTSTOCKTAKEPRODUCTS
DESCRIPTOR.message_types_by_name['PutStocktakeByDocIDRequest'] = _PUTSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['RejectStocktakeProductRequest'] = _REJECTSTOCKTAKEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['RejectStocktakeProductResponse'] = _REJECTSTOCKTAKEPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['PutStocktakeByDocIDResponse'] = _PUTSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDRequest'] = _CHECKSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDDetail'] = _CHECKSTOCKTAKEBYDOCIDDETAIL
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDResponse'] = _CHECKSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmStocktakeByDocIDRequest'] = _CONFIRMSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['SubmitStocktakeByDocIDResponse'] = _SUBMITSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitStocktakeByDocIDRequest'] = _SUBMITSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['ApproveStocktakeByDocIDRequest'] = _APPROVESTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['ApproveStocktakeByDocIDResponse'] = _APPROVESTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmStocktakeByDocIDResponse'] = _CONFIRMSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CancelStocktakeByDocIDRequest'] = _CANCELSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CancelStocktakeByDocIDResponse'] = _CANCELSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CreateStocktakeBatchRequest'] = _CREATESTOCKTAKEBATCHREQUEST
DESCRIPTOR.message_types_by_name['CreateStocktakeBatchResponse'] = _CREATESTOCKTAKEBATCHRESPONSE
DESCRIPTOR.message_types_by_name['CreateStocktakeRequest'] = _CREATESTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['CreateStocktakeResponse'] = _CREATESTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['CheckedStocktakeByDocIDRequest'] = _CHECKEDSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CheckedStocktakeByDocIDResponse'] = _CHECKEDSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeTagsRequest'] = _GETSTOCKTAKETAGSREQUEST
DESCRIPTOR.message_types_by_name['StocktakeTags'] = _STOCKTAKETAGS
DESCRIPTOR.message_types_by_name['GetStocktakeTagsResponse'] = _GETSTOCKTAKETAGSRESPONSE
DESCRIPTOR.message_types_by_name['ActionStocktakeTagsRequest'] = _ACTIONSTOCKTAKETAGSREQUEST
DESCRIPTOR.message_types_by_name['ActionStocktakeTagsResponse'] = _ACTIONSTOCKTAKETAGSRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeTagsByIdRequest'] = _GETSTOCKTAKETAGSBYIDREQUEST
DESCRIPTOR.message_types_by_name['DeleteStocktakeProductTagsRequest'] = _DELETESTOCKTAKEPRODUCTTAGSREQUEST
DESCRIPTOR.message_types_by_name['DeleteStocktakeProductTagsResponse'] = _DELETESTOCKTAKEPRODUCTTAGSRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceRequest'] = _GETSTOCKTAKEBALANCEREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBalance'] = _STOCKTAKEBALANCE
DESCRIPTOR.message_types_by_name['AttachmentsWareBalance'] = _ATTACHMENTSWAREBALANCE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceResponse'] = _GETSTOCKTAKEBALANCERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceProductGroupRequest'] = _GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST
DESCRIPTOR.message_types_by_name['TagProductBi'] = _TAGPRODUCTBI
DESCRIPTOR.message_types_by_name['StocktakeBalanceProductGroup'] = _STOCKTAKEBALANCEPRODUCTGROUP
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceProductGroupResponse'] = _GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeBiDetailedRequest'] = _STOCKTAKEBIDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBiDetailed'] = _STOCKTAKEBIDETAILED
DESCRIPTOR.message_types_by_name['ProductTagBi'] = _PRODUCTTAGBI
DESCRIPTOR.message_types_by_name['ST_total'] = _ST_TOTAL
DESCRIPTOR.message_types_by_name['StocktakeBiDetailedResponse'] = _STOCKTAKEBIDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionRequest'] = _STOCKTAKEBALANCEREGIONREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionDetails'] = _STOCKTAKEBALANCEREGIONDETAILS
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegion'] = _STOCKTAKEBALANCEREGION
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionResponse'] = _STOCKTAKEBALANCEREGIONRESPONSE
DESCRIPTOR.message_types_by_name['StoreDataScopeRequest'] = _STOREDATASCOPEREQUEST
DESCRIPTOR.message_types_by_name['ScopeStores'] = _SCOPESTORES
DESCRIPTOR.message_types_by_name['StoreDataScope'] = _STOREDATASCOPE
DESCRIPTOR.message_types_by_name['AdvanceStocktakeDiffRequest'] = _ADVANCESTOCKTAKEDIFFREQUEST
DESCRIPTOR.message_types_by_name['AdvanceStocktakeDiffResponse'] = _ADVANCESTOCKTAKEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDiffReportRequest'] = _STOCKTAKEDIFFREPORTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDiffReportRow'] = _STOCKTAKEDIFFREPORTROW
DESCRIPTOR.message_types_by_name['StocktakeDiffReportResponse'] = _STOCKTAKEDIFFREPORTRESPONSE
DESCRIPTOR.message_types_by_name['GetUncompleteDocRequest'] = _GETUNCOMPLETEDOCREQUEST
DESCRIPTOR.message_types_by_name['CheckDemandDetail'] = _CHECKDEMANDDETAIL
DESCRIPTOR.message_types_by_name['GetUncompleteDocResponse'] = _GETUNCOMPLETEDOCRESPONSE
DESCRIPTOR.message_types_by_name['RecreateStocktakeDocRequest'] = _RECREATESTOCKTAKEDOCREQUEST
DESCRIPTOR.message_types_by_name['RecreateStocktakeDocResponse'] = _RECREATESTOCKTAKEDOCRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDocStatisticsRequest'] = _STOCKTAKEDOCSTATISTICSREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDocStatistics'] = _STOCKTAKEDOCSTATISTICS
DESCRIPTOR.message_types_by_name['StocktakeDocStatisticsResponse'] = _STOCKTAKEDOCSTATISTICSRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportRequest'] = _STOCKTAKEDIFFCOLLECTREPORTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportRow'] = _STOCKTAKEDIFFCOLLECTREPORTROW
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportResponse'] = _STOCKTAKEDIFFCOLLECTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['UncompleteDocReportRequest'] = _UNCOMPLETEDOCREPORTREQUEST
DESCRIPTOR.message_types_by_name['UncompleteDocReportResponse'] = _UNCOMPLETEDOCREPORTRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeProductImportRequest'] = _STOCKTAKEPRODUCTIMPORTREQUEST
DESCRIPTOR.message_types_by_name['ProductImportResponseRows'] = _PRODUCTIMPORTRESPONSEROWS
DESCRIPTOR.message_types_by_name['StocktakeProductImportResponse'] = _STOCKTAKEPRODUCTIMPORTRESPONSE
DESCRIPTOR.message_types_by_name['UpdateStocktakeImportBatchRequest'] = _UPDATESTOCKTAKEIMPORTBATCHREQUEST
DESCRIPTOR.message_types_by_name['UpdateStocktakeImportBatchResponse'] = _UPDATESTOCKTAKEIMPORTBATCHRESPONSE
DESCRIPTOR.message_types_by_name['StocktakePositionProducts'] = _STOCKTAKEPOSITIONPRODUCTS
DESCRIPTOR.enum_types_by_name['PeriodGroupMethod'] = _PERIODGROUPMETHOD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetProductByStocktakeTypeRequest = _reflection.GeneratedProtocolMessageType('GetProductByStocktakeTypeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTBYSTOCKTAKETYPEREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetProductByStocktakeTypeRequest)
  ))
_sym_db.RegisterMessage(GetProductByStocktakeTypeRequest)

StocktakeProductType = _reflection.GeneratedProtocolMessageType('StocktakeProductType', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTTYPE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeProductType)
  ))
_sym_db.RegisterMessage(StocktakeProductType)

GetProductByStocktakeTypeResponse = _reflection.GeneratedProtocolMessageType('GetProductByStocktakeTypeResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTBYSTOCKTAKETYPERESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetProductByStocktakeTypeResponse)
  ))
_sym_db.RegisterMessage(GetProductByStocktakeTypeResponse)

GetStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeByDocIDRequest)

Stocktake = _reflection.GeneratedProtocolMessageType('Stocktake', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.Stocktake)
  ))
_sym_db.RegisterMessage(Stocktake)

AttachmentsWare = _reflection.GeneratedProtocolMessageType('AttachmentsWare', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTSWARE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AttachmentsWare)
  ))
_sym_db.RegisterMessage(AttachmentsWare)

GetStocktakeRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeRequest)

GetStocktakeResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKERESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeResponse)

GetStocktakeProductRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEPRODUCTREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeProductRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeProductRequest)

StocktakeProductTagName = _reflection.GeneratedProtocolMessageType('StocktakeProductTagName', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTTAGNAME,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeProductTagName)
  ))
_sym_db.RegisterMessage(StocktakeProductTagName)

StocktakeProductUnits = _reflection.GeneratedProtocolMessageType('StocktakeProductUnits', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTUNITS,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeProductUnits)
  ))
_sym_db.RegisterMessage(StocktakeProductUnits)

StocktakeProduct = _reflection.GeneratedProtocolMessageType('StocktakeProduct', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCT,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeProduct)
  ))
_sym_db.RegisterMessage(StocktakeProduct)

GetStocktakeProductResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEPRODUCTRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeProductResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeProductResponse)

UserCreateStocktakeRequest = _reflection.GeneratedProtocolMessageType('UserCreateStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _USERCREATESTOCKTAKEREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UserCreateStocktakeRequest)
  ))
_sym_db.RegisterMessage(UserCreateStocktakeRequest)

UserCreateStocktakeResponse = _reflection.GeneratedProtocolMessageType('UserCreateStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _USERCREATESTOCKTAKERESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UserCreateStocktakeResponse)
  ))
_sym_db.RegisterMessage(UserCreateStocktakeResponse)

TagQuantity = _reflection.GeneratedProtocolMessageType('TagQuantity', (_message.Message,), dict(
  DESCRIPTOR = _TAGQUANTITY,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.TagQuantity)
  ))
_sym_db.RegisterMessage(TagQuantity)

PutStocktakeProducts = _reflection.GeneratedProtocolMessageType('PutStocktakeProducts', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEPRODUCTS,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.PutStocktakeProducts)
  ))
_sym_db.RegisterMessage(PutStocktakeProducts)

PutStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('PutStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.PutStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(PutStocktakeByDocIDRequest)

RejectStocktakeProductRequest = _reflection.GeneratedProtocolMessageType('RejectStocktakeProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTSTOCKTAKEPRODUCTREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.RejectStocktakeProductRequest)
  ))
_sym_db.RegisterMessage(RejectStocktakeProductRequest)

RejectStocktakeProductResponse = _reflection.GeneratedProtocolMessageType('RejectStocktakeProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTSTOCKTAKEPRODUCTRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.RejectStocktakeProductResponse)
  ))
_sym_db.RegisterMessage(RejectStocktakeProductResponse)

PutStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('PutStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.PutStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(PutStocktakeByDocIDResponse)

CheckStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CheckStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDRequest)

CheckStocktakeByDocIDDetail = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDDetail', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDDETAIL,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CheckStocktakeByDocIDDetail)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDDetail)

CheckStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CheckStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDResponse)

ConfirmStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('ConfirmStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ConfirmStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(ConfirmStocktakeByDocIDRequest)

SubmitStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('SubmitStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.SubmitStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(SubmitStocktakeByDocIDResponse)

SubmitStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('SubmitStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.SubmitStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(SubmitStocktakeByDocIDRequest)

ApproveStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('ApproveStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVESTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ApproveStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(ApproveStocktakeByDocIDRequest)

ApproveStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('ApproveStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVESTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ApproveStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(ApproveStocktakeByDocIDResponse)

ConfirmStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('ConfirmStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ConfirmStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(ConfirmStocktakeByDocIDResponse)

CancelStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CancelStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CancelStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CancelStocktakeByDocIDRequest)

CancelStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CancelStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CancelStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CancelStocktakeByDocIDResponse)

CreateStocktakeBatchRequest = _reflection.GeneratedProtocolMessageType('CreateStocktakeBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEBATCHREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateStocktakeBatchRequest)
  ))
_sym_db.RegisterMessage(CreateStocktakeBatchRequest)

CreateStocktakeBatchResponse = _reflection.GeneratedProtocolMessageType('CreateStocktakeBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEBATCHRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateStocktakeBatchResponse)
  ))
_sym_db.RegisterMessage(CreateStocktakeBatchResponse)

CreateStocktakeRequest = _reflection.GeneratedProtocolMessageType('CreateStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateStocktakeRequest)
  ))
_sym_db.RegisterMessage(CreateStocktakeRequest)

CreateStocktakeResponse = _reflection.GeneratedProtocolMessageType('CreateStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKERESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateStocktakeResponse)
  ))
_sym_db.RegisterMessage(CreateStocktakeResponse)

CheckedStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CheckedStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKEDSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CheckedStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CheckedStocktakeByDocIDRequest)

CheckedStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CheckedStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKEDSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CheckedStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CheckedStocktakeByDocIDResponse)

GetStocktakeTagsRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeTagsRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsRequest)

StocktakeTags = _reflection.GeneratedProtocolMessageType('StocktakeTags', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKETAGS,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeTags)
  ))
_sym_db.RegisterMessage(StocktakeTags)

GetStocktakeTagsResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeTagsResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsResponse)

ActionStocktakeTagsRequest = _reflection.GeneratedProtocolMessageType('ActionStocktakeTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONSTOCKTAKETAGSREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ActionStocktakeTagsRequest)
  ))
_sym_db.RegisterMessage(ActionStocktakeTagsRequest)

ActionStocktakeTagsResponse = _reflection.GeneratedProtocolMessageType('ActionStocktakeTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONSTOCKTAKETAGSRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ActionStocktakeTagsResponse)
  ))
_sym_db.RegisterMessage(ActionStocktakeTagsResponse)

GetStocktakeTagsByIdRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSBYIDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeTagsByIdRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsByIdRequest)

DeleteStocktakeProductTagsRequest = _reflection.GeneratedProtocolMessageType('DeleteStocktakeProductTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETESTOCKTAKEPRODUCTTAGSREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.DeleteStocktakeProductTagsRequest)
  ))
_sym_db.RegisterMessage(DeleteStocktakeProductTagsRequest)

DeleteStocktakeProductTagsResponse = _reflection.GeneratedProtocolMessageType('DeleteStocktakeProductTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETESTOCKTAKEPRODUCTTAGSRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.DeleteStocktakeProductTagsResponse)
  ))
_sym_db.RegisterMessage(DeleteStocktakeProductTagsResponse)

GetStocktakeBalanceRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeBalanceRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceRequest)

StocktakeBalance = _reflection.GeneratedProtocolMessageType('StocktakeBalance', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBalance)
  ))
_sym_db.RegisterMessage(StocktakeBalance)

AttachmentsWareBalance = _reflection.GeneratedProtocolMessageType('AttachmentsWareBalance', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTSWAREBALANCE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AttachmentsWareBalance)
  ))
_sym_db.RegisterMessage(AttachmentsWareBalance)

GetStocktakeBalanceResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCERESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeBalanceResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceResponse)

GetStocktakeBalanceProductGroupRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceProductGroupRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeBalanceProductGroupRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceProductGroupRequest)

TagProductBi = _reflection.GeneratedProtocolMessageType('TagProductBi', (_message.Message,), dict(
  DESCRIPTOR = _TAGPRODUCTBI,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.TagProductBi)
  ))
_sym_db.RegisterMessage(TagProductBi)

StocktakeBalanceProductGroup = _reflection.GeneratedProtocolMessageType('StocktakeBalanceProductGroup', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEPRODUCTGROUP,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBalanceProductGroup)
  ))
_sym_db.RegisterMessage(StocktakeBalanceProductGroup)

GetStocktakeBalanceProductGroupResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceProductGroupResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetStocktakeBalanceProductGroupResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceProductGroupResponse)

StocktakeBiDetailedRequest = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILEDREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBiDetailedRequest)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailedRequest)

StocktakeBiDetailed = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailed', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILED,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBiDetailed)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailed)

ProductTagBi = _reflection.GeneratedProtocolMessageType('ProductTagBi', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTTAGBI,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ProductTagBi)
  ))
_sym_db.RegisterMessage(ProductTagBi)

ST_total = _reflection.GeneratedProtocolMessageType('ST_total', (_message.Message,), dict(
  DESCRIPTOR = _ST_TOTAL,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ST_total)
  ))
_sym_db.RegisterMessage(ST_total)

StocktakeBiDetailedResponse = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILEDRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBiDetailedResponse)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailedResponse)

StocktakeBalanceRegionRequest = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBalanceRegionRequest)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionRequest)

StocktakeBalanceRegionDetails = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionDetails', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONDETAILS,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBalanceRegionDetails)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionDetails)

StocktakeBalanceRegion = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegion', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGION,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBalanceRegion)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegion)

StocktakeBalanceRegionResponse = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeBalanceRegionResponse)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionResponse)

StoreDataScopeRequest = _reflection.GeneratedProtocolMessageType('StoreDataScopeRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOREDATASCOPEREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StoreDataScopeRequest)
  ))
_sym_db.RegisterMessage(StoreDataScopeRequest)

ScopeStores = _reflection.GeneratedProtocolMessageType('ScopeStores', (_message.Message,), dict(
  DESCRIPTOR = _SCOPESTORES,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ScopeStores)
  ))
_sym_db.RegisterMessage(ScopeStores)

StoreDataScope = _reflection.GeneratedProtocolMessageType('StoreDataScope', (_message.Message,), dict(
  DESCRIPTOR = _STOREDATASCOPE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StoreDataScope)
  ))
_sym_db.RegisterMessage(StoreDataScope)

AdvanceStocktakeDiffRequest = _reflection.GeneratedProtocolMessageType('AdvanceStocktakeDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADVANCESTOCKTAKEDIFFREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AdvanceStocktakeDiffRequest)
  ))
_sym_db.RegisterMessage(AdvanceStocktakeDiffRequest)

AdvanceStocktakeDiffResponse = _reflection.GeneratedProtocolMessageType('AdvanceStocktakeDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADVANCESTOCKTAKEDIFFRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AdvanceStocktakeDiffResponse)
  ))
_sym_db.RegisterMessage(AdvanceStocktakeDiffResponse)

StocktakeDiffReportRequest = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDiffReportRequest)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportRequest)

StocktakeDiffReportRow = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportRow', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTROW,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDiffReportRow)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportRow)

StocktakeDiffReportResponse = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDiffReportResponse)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportResponse)

GetUncompleteDocRequest = _reflection.GeneratedProtocolMessageType('GetUncompleteDocRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETUNCOMPLETEDOCREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetUncompleteDocRequest)
  ))
_sym_db.RegisterMessage(GetUncompleteDocRequest)

CheckDemandDetail = _reflection.GeneratedProtocolMessageType('CheckDemandDetail', (_message.Message,), dict(
  DESCRIPTOR = _CHECKDEMANDDETAIL,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CheckDemandDetail)
  ))
_sym_db.RegisterMessage(CheckDemandDetail)

GetUncompleteDocResponse = _reflection.GeneratedProtocolMessageType('GetUncompleteDocResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETUNCOMPLETEDOCRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetUncompleteDocResponse)
  ))
_sym_db.RegisterMessage(GetUncompleteDocResponse)

RecreateStocktakeDocRequest = _reflection.GeneratedProtocolMessageType('RecreateStocktakeDocRequest', (_message.Message,), dict(
  DESCRIPTOR = _RECREATESTOCKTAKEDOCREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.RecreateStocktakeDocRequest)
  ))
_sym_db.RegisterMessage(RecreateStocktakeDocRequest)

RecreateStocktakeDocResponse = _reflection.GeneratedProtocolMessageType('RecreateStocktakeDocResponse', (_message.Message,), dict(
  DESCRIPTOR = _RECREATESTOCKTAKEDOCRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.RecreateStocktakeDocResponse)
  ))
_sym_db.RegisterMessage(RecreateStocktakeDocResponse)

StocktakeDocStatisticsRequest = _reflection.GeneratedProtocolMessageType('StocktakeDocStatisticsRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICSREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDocStatisticsRequest)
  ))
_sym_db.RegisterMessage(StocktakeDocStatisticsRequest)

StocktakeDocStatistics = _reflection.GeneratedProtocolMessageType('StocktakeDocStatistics', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICS,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDocStatistics)
  ))
_sym_db.RegisterMessage(StocktakeDocStatistics)

StocktakeDocStatisticsResponse = _reflection.GeneratedProtocolMessageType('StocktakeDocStatisticsResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICSRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDocStatisticsResponse)
  ))
_sym_db.RegisterMessage(StocktakeDocStatisticsResponse)

StocktakeDiffCollectReportRequest = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDiffCollectReportRequest)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportRequest)

StocktakeDiffCollectReportRow = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportRow', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTROW,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDiffCollectReportRow)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportRow)

StocktakeDiffCollectReportResponse = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeDiffCollectReportResponse)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportResponse)

UncompleteDocReportRequest = _reflection.GeneratedProtocolMessageType('UncompleteDocReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _UNCOMPLETEDOCREPORTREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UncompleteDocReportRequest)
  ))
_sym_db.RegisterMessage(UncompleteDocReportRequest)

UncompleteDocReportResponse = _reflection.GeneratedProtocolMessageType('UncompleteDocReportResponse', (_message.Message,), dict(

  UncompleteDocReport = _reflection.GeneratedProtocolMessageType('UncompleteDocReport', (_message.Message,), dict(
    DESCRIPTOR = _UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT,
    __module__ = 'warehouse.stocktake_pb2'
    # @@protoc_insertion_point(class_scope:warehouse.UncompleteDocReportResponse.UncompleteDocReport)
    ))
  ,
  DESCRIPTOR = _UNCOMPLETEDOCREPORTRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UncompleteDocReportResponse)
  ))
_sym_db.RegisterMessage(UncompleteDocReportResponse)
_sym_db.RegisterMessage(UncompleteDocReportResponse.UncompleteDocReport)

StocktakeProductImportRequest = _reflection.GeneratedProtocolMessageType('StocktakeProductImportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTIMPORTREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeProductImportRequest)
  ))
_sym_db.RegisterMessage(StocktakeProductImportRequest)

ProductImportResponseRows = _reflection.GeneratedProtocolMessageType('ProductImportResponseRows', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTIMPORTRESPONSEROWS,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ProductImportResponseRows)
  ))
_sym_db.RegisterMessage(ProductImportResponseRows)

StocktakeProductImportResponse = _reflection.GeneratedProtocolMessageType('StocktakeProductImportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTIMPORTRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakeProductImportResponse)
  ))
_sym_db.RegisterMessage(StocktakeProductImportResponse)

UpdateStocktakeImportBatchRequest = _reflection.GeneratedProtocolMessageType('UpdateStocktakeImportBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESTOCKTAKEIMPORTBATCHREQUEST,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UpdateStocktakeImportBatchRequest)
  ))
_sym_db.RegisterMessage(UpdateStocktakeImportBatchRequest)

UpdateStocktakeImportBatchResponse = _reflection.GeneratedProtocolMessageType('UpdateStocktakeImportBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESTOCKTAKEIMPORTBATCHRESPONSE,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UpdateStocktakeImportBatchResponse)
  ))
_sym_db.RegisterMessage(UpdateStocktakeImportBatchResponse)

StocktakePositionProducts = _reflection.GeneratedProtocolMessageType('StocktakePositionProducts', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPOSITIONPRODUCTS,
  __module__ = 'warehouse.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.StocktakePositionProducts)
  ))
_sym_db.RegisterMessage(StocktakePositionProducts)



_WAREHOUSESTOCKTAKE = _descriptor.ServiceDescriptor(
  name='warehouseStockTake',
  full_name='warehouse.warehouseStockTake',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=23713,
  serialized_end=28293,
  methods=[
  _descriptor.MethodDescriptor(
    name='CheckStocktakeByDocID',
    full_name='warehouse.warehouseStockTake.CheckStocktakeByDocID',
    index=0,
    containing_service=None,
    input_type=_CHECKSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CHECKSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002;\0329/api/v2/supply/warehouse/stocktake/{doc_id}/confirm/check'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmStocktakeByDocID',
    full_name='warehouse.warehouseStockTake.ConfirmStocktakeByDocID',
    index=1,
    containing_service=None,
    input_type=_CONFIRMSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CONFIRMSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0323/api/v2/supply/warehouse/stocktake/{doc_id}/confirm'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveStocktakeByDocID',
    full_name='warehouse.warehouseStockTake.ApproveStocktakeByDocID',
    index=2,
    containing_service=None,
    input_type=_APPROVESTOCKTAKEBYDOCIDREQUEST,
    output_type=_APPROVESTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0323/api/v2/supply/warehouse/stocktake/{doc_id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectStocktakeProduct',
    full_name='warehouse.warehouseStockTake.RejectStocktakeProduct',
    index=3,
    containing_service=None,
    input_type=_REJECTSTOCKTAKEPRODUCTREQUEST,
    output_type=_REJECTSTOCKTAKEPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0322/api/v2/supply/warehouse/stocktake/{doc_id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelStocktakeByDocID',
    full_name='warehouse.warehouseStockTake.CancelStocktakeByDocID',
    index=4,
    containing_service=None,
    input_type=_CANCELSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CANCELSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0322/api/v2/supply/warehouse/stocktake/{doc_id}/cancel'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeByDocID',
    full_name='warehouse.warehouseStockTake.GetStocktakeByDocID',
    index=5,
    containing_service=None,
    input_type=_GETSTOCKTAKEBYDOCIDREQUEST,
    output_type=_STOCKTAKE,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/supply/warehouse/stocktake/{doc_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktake',
    full_name='warehouse.warehouseStockTake.GetStocktake',
    index=6,
    containing_service=None,
    input_type=_GETSTOCKTAKEREQUEST,
    output_type=_GETSTOCKTAKERESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/supply/warehouse/stocktake'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeProduct',
    full_name='warehouse.warehouseStockTake.GetStocktakeProduct',
    index=7,
    containing_service=None,
    input_type=_GETSTOCKTAKEPRODUCTREQUEST,
    output_type=_GETSTOCKTAKEPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/warehouse/stocktake/{doc_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='PutStocktakeByDocID',
    full_name='warehouse.warehouseStockTake.PutStocktakeByDocID',
    index=8,
    containing_service=None,
    input_type=_PUTSTOCKTAKEBYDOCIDREQUEST,
    output_type=_PUTSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\032+/api/v2/supply/warehouse/stocktake/{doc_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckedStocktakeByDocID',
    full_name='warehouse.warehouseStockTake.CheckedStocktakeByDocID',
    index=9,
    containing_service=None,
    input_type=_CHECKEDSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CHECKEDSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002;\0326/api/v2/supply/warehouse/stocktake/{doc_id}/init/check:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeTags',
    full_name='warehouse.warehouseStockTake.GetStocktakeTags',
    index=10,
    containing_service=None,
    input_type=_GETSTOCKTAKETAGSREQUEST,
    output_type=_GETSTOCKTAKETAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/warehouse/stocktake/product/tags'),
  ),
  _descriptor.MethodDescriptor(
    name='ActionStocktakeTags',
    full_name='warehouse.warehouseStockTake.ActionStocktakeTags',
    index=11,
    containing_service=None,
    input_type=_ACTIONSTOCKTAKETAGSREQUEST,
    output_type=_ACTIONSTOCKTAKETAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\002;\0326/api/v2/supply/warehouse/stocktake/product/tags/action:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteStocktakeProductTags',
    full_name='warehouse.warehouseStockTake.DeleteStocktakeProductTags',
    index=12,
    containing_service=None,
    input_type=_DELETESTOCKTAKEPRODUCTTAGSREQUEST,
    output_type=_DELETESTOCKTAKEPRODUCTTAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0325/api/v2/supply/warehouse/stocktake/product/tags/clean:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeBalance',
    full_name='warehouse.warehouseStockTake.GetStocktakeBalance',
    index=13,
    containing_service=None,
    input_type=_GETSTOCKTAKEBALANCEREQUEST,
    output_type=_GETSTOCKTAKEBALANCERESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/warehouse/stocktake/bi/balance'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitStocktakeByDocID',
    full_name='warehouse.warehouseStockTake.SubmitStocktakeByDocID',
    index=14,
    containing_service=None,
    input_type=_SUBMITSTOCKTAKEBYDOCIDREQUEST,
    output_type=_SUBMITSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0322/api/v2/supply/warehouse/stocktake/{doc_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeBalanceProductGroup',
    full_name='warehouse.warehouseStockTake.GetStocktakeBalanceProductGroup',
    index=15,
    containing_service=None,
    input_type=_GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST,
    output_type=_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE,
    serialized_options=_b('\202\323\344\223\002C\022A/api/v2/supply/warehouse/stocktake/balance/{doc_id}/product/group'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeBiDetailed',
    full_name='warehouse.warehouseStockTake.StocktakeBiDetailed',
    index=16,
    containing_service=None,
    input_type=_STOCKTAKEBIDETAILEDREQUEST,
    output_type=_STOCKTAKEBIDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/warehouse/stocktake/bi/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeBalanceRegion',
    full_name='warehouse.warehouseStockTake.StocktakeBalanceRegion',
    index=17,
    containing_service=None,
    input_type=_STOCKTAKEBALANCEREGIONREQUEST,
    output_type=_STOCKTAKEBALANCEREGIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0228/api/v2/supply/warehouse/stocktake/balance/product/group'),
  ),
  _descriptor.MethodDescriptor(
    name='AdvanceStocktakeDiff',
    full_name='warehouse.warehouseStockTake.AdvanceStocktakeDiff',
    index=18,
    containing_service=None,
    input_type=_ADVANCESTOCKTAKEDIFFREQUEST,
    output_type=_ADVANCESTOCKTAKEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/warehouse/stocktake/{doc_id}/advance'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDiffReport',
    full_name='warehouse.warehouseStockTake.StocktakeDiffReport',
    index=19,
    containing_service=None,
    input_type=_STOCKTAKEDIFFREPORTREQUEST,
    output_type=_STOCKTAKEDIFFREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/supply/warehouse/stocktake/diff/report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetUncompleteDoc',
    full_name='warehouse.warehouseStockTake.GetUncompleteDoc',
    index=20,
    containing_service=None,
    input_type=_GETUNCOMPLETEDOCREQUEST,
    output_type=_GETUNCOMPLETEDOCRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/warehouse/uncomplete_doc'),
  ),
  _descriptor.MethodDescriptor(
    name='RecreateStocktakeDoc',
    full_name='warehouse.warehouseStockTake.RecreateStocktakeDoc',
    index=21,
    containing_service=None,
    input_type=_RECREATESTOCKTAKEDOCREQUEST,
    output_type=_RECREATESTOCKTAKEDOCRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\"//api/v2/supply/warehouse/recreate_stocktake_doc:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDocStatistics',
    full_name='warehouse.warehouseStockTake.StocktakeDocStatistics',
    index=22,
    containing_service=None,
    input_type=_STOCKTAKEDOCSTATISTICSREQUEST,
    output_type=_STOCKTAKEDOCSTATISTICSRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/supply/warehouse/stocktake_doc_statistics:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDiffCollectReport',
    full_name='warehouse.warehouseStockTake.StocktakeDiffCollectReport',
    index=23,
    containing_service=None,
    input_type=_STOCKTAKEDIFFCOLLECTREPORTREQUEST,
    output_type=_STOCKTAKEDIFFCOLLECTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0226/api/v2/supply/warehouse/stocktake/diff_collect/report'),
  ),
  _descriptor.MethodDescriptor(
    name='UncompleteDocReport',
    full_name='warehouse.warehouseStockTake.UncompleteDocReport',
    index=24,
    containing_service=None,
    input_type=_UNCOMPLETEDOCREPORTREQUEST,
    output_type=_UNCOMPLETEDOCREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/supply/warehouse/uncomplete_doc_report'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeProductImport',
    full_name='warehouse.warehouseStockTake.StocktakeProductImport',
    index=25,
    containing_service=None,
    input_type=_STOCKTAKEPRODUCTIMPORTREQUEST,
    output_type=_STOCKTAKEPRODUCTIMPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/supply/warehouse/stocktake/product/import:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateStocktakeImportBatch',
    full_name='warehouse.warehouseStockTake.UpdateStocktakeImportBatch',
    index=26,
    containing_service=None,
    input_type=_UPDATESTOCKTAKEIMPORTBATCHREQUEST,
    output_type=_UPDATESTOCKTAKEIMPORTBATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002;\0326/api/v2/supply/warehouse/update/stocktake/import/batch:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_WAREHOUSESTOCKTAKE)

DESCRIPTOR.services_by_name['warehouseStockTake'] = _WAREHOUSESTOCKTAKE

# @@protoc_insertion_point(module_scope)
