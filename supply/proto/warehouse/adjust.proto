syntax = "proto3";

package warehouse;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

//Adjust 每日损耗服务
service WarehouseAdjust {
    //Ping 健康检查
    rpc Ping (google.protobuf.Empty) returns (Pong) {
        option (google.api.http) = { get: "/api/v2/supply/warehouse/ping" };
    }
    //GetAdjust查询每日损耗表9
    rpc GetAdjust (GetAdjustRequest) returns (GetAdjustResponse) {
        option (google.api.http) = {get:"/api/v2/supply/warehouse/adjust"};
    }
    //GetAdjustProduct每日损耗表商品查询10
    rpc GetAdjustProduct (GetAdjustProductRequest) returns (GetAdjustProductResponse) {
        option (google.api.http) = {get:"/api/v2/supply/warehouse/adjust/{adjust_id}/product"};
    }
    //GetAdjustByID查询一个每日损耗表11
    rpc GetAdjustByID (GetAdjustByIDRequest) returns (Adjust) {
        option (google.api.http) = {get:"/api/v2/supply/warehouse/adjust/{adjust_id}"};
    }
    //ConfirmAdjust确认一个每日损耗表12
    rpc ConfirmAdjust (ConfirmAdjustRequest) returns (ConfirmAdjustResponse) {
        option (google.api.http) = {put:"/api/v2/supply/warehouse/adjust/{adjust_id}/confirm" body: "*"};
    }
    // 提交一个报废单
    rpc SubmitAdjust (SubmitAdjustRequest) returns (SubmitAdjustResponse) {
        option (google.api.http) = {put:"/api/v2/supply/warehouse/adjust/{adjust_id}/submit" body: "*"};
    }
    // 审核一个报废单
    rpc ApproveAdjust (ApproveAdjustRequest) returns (ApproveAdjustResponse) {
        option (google.api.http) = {put:"/api/v2/supply/warehouse/adjust/{adjust_id}/approve" body: "*"};
    }
    // 驳回一个报废单
    rpc RejectAdjust (RejectAdjustRequest) returns (RejectAdjustResponse) {
        option (google.api.http) = {put:"/api/v2/supply/warehouse/adjust/{adjust_id}/reject" body: "*"};
    }
    // GetAdjustProductByStoreID查询门店可损耗商品13
    rpc GetAdjustProductByStoreID (GetAdjustProductByStoreIDRequest) returns (GetAdjustProductByStoreIDResponse) {
        option (google.api.http) = {get:"/api/v2/supply/warehouse/adjust/store/products"};
    }
    //CreatedAdjust手动创建一个每日损耗表14
    rpc CreatedAdjust (CreatedAdjustRequest) returns (Adjust) {
        option (google.api.http) = {post:"/api/v2/supply/warehouse/adjust/create" body: "*"};
    }
    //UpdateAdjust更新一个每日损耗表15
    rpc UpdateAdjust (UpdateAdjustRequest) returns (Adjust) {
        option (google.api.http) = {put:"/api/v2/supply/warehouse/adjust/{adjust_id}/update" body: "*"};
    }
    //DeleteAdjust删除一个每日损耗表16
    rpc DeleteAdjust (DeleteAdjustRequest) returns (DeleteAdjustResponse) {
        option (google.api.http) = {put:"/api/v2/supply/warehouse/adjust/{adjust_id}/delete" body: "*"};
    }
    //DeleteAdjustProduct删除每日损耗表的商品17
    rpc DeleteAdjustProduct (DeleteAdjustProductRequest) returns (DeleteAdjustProductResponse) {
        option (google.api.http) = {put:"/api/v2/supply/warehouse/adjust/product/{adjust_id}/delete" body: "*"};
    }
    //GetAdjustBiCollect每日损耗表汇总报表18
    rpc GetAdjustBiCollect (GetAdjustBiCollectRequest) returns (GetAdjustBiCollectResponse) {
        option (google.api.http) = {get:"/api/v2/supply/warehouse/adjust/bi/collect" };
    }
    //CancelAdjust 取消每日损耗表20
    rpc CancelAdjust (CancelAdjustRequest) returns (CancelAdjustResponse) {
        option (google.api.http) = { put: "/api/v2/supply/warehouse/adjust/auto_create/cancel" body: "*"};
    }
    //GetAdjustCollectDetailed损耗表明细汇总报表21
    rpc GetAdjustCollectDetailed (GetAdjustCollectDetailedRequest) returns (GetAdjustCollectDetailedResponse) {
        option (google.api.http) = { get: "/api/v2/supply/warehouse/adjust/bi/detailed"};
    }
    //AutoCloseCreatedAdjust自动关闭未确认的损耗表,不传参数默认前一天，传date操作date日期的单子22
    rpc AutoCloseCreatedAdjust (AutoCloseCreatedAdjustRequest) returns (AutoCloseCreatedAdjustResponse) {
        option (google.api.http) = { get: "/api/v2/supply/warehouse/adjust/close/auto"};
    }
    // 物料报废量成本报表
    rpc GetMaterialAdjustData (GetMaterialAdjustDataRequest) returns (GetMaterialAdjustDataResponse){
        option (google.api.http) = { get: "/api/v2/supply/warehouse/adjust/material/cost"};
    }

    // 第三方根据code创建报废
    rpc CreatedAdjustByCode (CreatedAdjustByCodeRequest) returns (Adjust) {
        option (google.api.http) = {post:"/api/v2/supply/warehouse/adjust/pos" body: "*"};
    }
}
message Pong {
    string msg = 1;
}

message CreateAdjustRequest {
    //请求ID
    uint64 request_id = 1;
    //请求时间
    google.protobuf.Timestamp request_date = 2;
}
message CreateAdjustResponse {
    bool result = 1;
    //盘点商品数量
    //请求时间
    google.protobuf.Timestamp request_date = 2;
}
message GetAdjustRequest {
    bool include_total = 1;
    google.protobuf.Timestamp start_date = 2;
    google.protobuf.Timestamp end_date = 3;
    string reason_type = 4;
    repeated uint64 branches = 5;
    // 商品ids
    repeated uint64 product_ids = 6;
    uint32 offset = 7;
    uint32 limit = 8;
    enum STATUS {
        NONE = 0;
        // 新建
        INITED = 1;
        // 提交
        SUBMITTED = 2;
        // 审核
        APPROVED = 3;
        // 驳回
        REJECTED = 4;
        // 作废
        CANCELLED = 5;
    }
    //每日损耗计划状态
    repeated STATUS status = 9;
    string code = 10;
    // 排序(默认asc)
    string order = 12;
    string sort = 13;
    string branch_type = 30; // STORE, WAREHOUSE
    repeated string sources = 31;
}
message Adjust {
    uint64 id = 1;
    uint64 adjust_order_number = 2;
    uint64 adjust_store = 3;
    string adjust_store_secondary_id = 4;
    string code = 5;
    uint64 partner_id = 6;
    string process_status = 7;
    string reason_type = 8;
    string remark = 9;
    string status = 10;
    google.protobuf.Timestamp adjust_date = 11;
    google.protobuf.Timestamp created_at = 12;
    google.protobuf.Timestamp updated_at = 13;
    uint64 created_by = 14;
    uint64 updated_by = 15;
    uint64 user_id = 16;
    uint64 branch_batch_id = 17;
    string schedule_code = 18;
    uint64 schedule_id = 19;
    uint64 request_id = 20;
    string created_name = 21;
    string updated_name = 22;
    uint64 receive_id = 25;
    string receive_code = 26;
    string schedule_name = 28;
    string branch_type = 30; // STORE, WAREHOUSE
    // 附件
    repeated Attachments attachments = 31;    // 单据来源
    // "POS_ADJUST"      # POS端报废
    // "MANUAL_CREATED"  # 手动新建
    // "PLAN_CREATED"    # 报废计划创建
    string source = 32;
    string reason_name = 33;
    // 驳回原因
    string reject_reason = 36;
    double total_amount = 38; // 含税金额总数
    double total_sales_amount = 39; // 含税零售金额总数
    string currency = 40;
}
message GetAdjustResponse {
    repeated Adjust rows = 1;
    uint32 total = 2;
}
message GetAdjustProductRequest {
    uint64 adjust_id = 1;
    bool include_total = 2;
    string order = 3;
    uint32 offset = 4;
    uint32 limit = 5;
    string lan = 6;
}
message AdjustProduct {
    uint64 id = 1;
    double accounting_quantity = 2;
    uint64 accounting_unit_id = 3;
    string accounting_unit_name = 4;
    string accounting_unit_spec = 5;
    uint64 adjust_id = 6;
    uint64 adjust_store = 7;
    double confirmed_quantity = 8;
    uint64 created_by = 9;
    bool is_confirmed = 10;
    uint32 item_number = 11;
    string material_number = 12;
    uint64 partner_id = 13;
    string product_code = 14;
    uint64 product_id = 15;
    string product_name = 16;
    double quantity = 17;
    string spec = 18;
    double stocktake_quantity = 19;
    uint64 stocktake_unit_id = 20;
    uint64 unit_id = 21;
    string unit_name = 22;
    string unit_spec = 23;
    uint64 updated_by = 24;
    google.protobuf.Timestamp adjust_date = 25;
    google.protobuf.Timestamp updated_at = 26;
    google.protobuf.Timestamp created_at = 27;
    uint64 user_id = 28;
    string created_name = 29;
    string updated_name = 30;
    string reason_type = 31;
    double convert_accounting_quantity = 32;
    bool is_bom = 33;
    uint64 position_id =34;
    string sku_remark = 35;
    repeated CreateAdjustProductUint units = 36;
    string model_name = 37;
    double tax_rate = 38;
    double tax_price = 39;
    double amount = 40;
    double tax_amount = 41;
    double cost_price = 42;
    double sales_amount = 43;
    double sales_price = 44;
    string price = 45;
    string currency = 46;
}
message GetAdjustProductResponse {
    repeated AdjustProduct rows = 1;
    uint32 total = 2;
    repeated AdjustPositionProducts position_rows = 3;
}
message GetAdjustByIDRequest {
    uint64 adjust_id = 1;
    string branch_type = 2;
}
message ConfirmAdjustRequest {
    uint64 adjust_id = 1;
    string branch_type= 2;
}
message ConfirmAdjustResponse {
    bool result = 1;
}

message SubmitAdjustRequest {
    uint64 adjust_id = 1;
}

message SubmitAdjustResponse {
    bool result = 1;
}

message ApproveAdjustRequest {
    uint64 adjust_id = 1;
}

message ApproveAdjustResponse {
    bool result = 1;
}

message RejectAdjustRequest {
    uint64 adjust_id = 1;
    // 驳回原因
    string reject_reason = 2;
}

message RejectAdjustResponse {
    bool result = 1;
}

message GetAdjustProductByStoreIDRequest {
    uint64 store_id = 1;
    uint32 limit = 2;
    uint32 offset = 3;
    bool include_total = 4;
    google.protobuf.Timestamp adjust_date = 5;
    string search = 6;
    string search_fields = 7;
    repeated uint64 category_ids = 8;
    string lan= 9;
     // 排序条件
    string order_by=10;
    // asc or desc， 暂时未使用
    string sort = 11;
}
message CreateAdjustProductUint {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string updated = 7;
    double rate = 8;
    bool default = 9;
    bool order = 10;
    bool purchase = 11;
    bool sales = 12;
    bool stocktake = 13;
    bool bom = 14;
    bool default_stocktake = 15;
    bool transfer = 16;
    double tax_rate = 17;
    double tax_price = 18;
    double cost_price = 19;
}
message CreateAdjustProduct {
    uint64 product_id = 1;
    string loss_report_order = 2;
    string product_code = 3;
    string product_name = 4;
    string model_name = 5;
    string storage_type = 6;
    uint64 product_category_id = 7;
    repeated CreateAdjustProductUint units = 8;
    repeated string barcode = 9;
    // 实时库存数量
    double real_inventory_qty = 10;
    string price = 11;
    string currency = 12;
}
message GetAdjustProductByStoreIDResponse {
    repeated CreateAdjustProduct rows = 1;
    uint32 total = 2;
    // 库存未异动的商品
    repeated CreateAdjustProduct inventory_unchanged_rows = 3;
}
message CreatedAdjustProduct {
    uint64 id = 1;
    uint64 product_id = 2;
    uint64 unit_id = 3;
    double quantity = 4;
    string reason_type = 5;
    uint64 position_id = 6;
    repeated SkuRemark skuRemark = 7;
    double tax_rate = 8;
    double tax_price = 9;
    double amount = 10;
    double tax_amount = 11;
    double cost_price = 12;
    double sales_price = 13;
    double sales_amount = 14;
}
message CreatedAdjustRequest {
    uint64 adjust_store = 1;
    repeated CreatedAdjustProduct products = 2;
    string reason_type = 3;
    string remark = 4;
    uint64 request_id = 5;
    google.protobuf.Timestamp adjust_date = 6;
    // 区分门店还是仓库: STORE or WAREHOUSE
    string branch_type = 7;
    uint64 position_id = 8;
    // 单据来源
    // "POS_ADJUST"      # POS端报废
    // "MANUAL_CREATED"  # 手动新建
    // "PLAN_CREATED"    # 报废计划创建
    string source = 9;
    // 附件
    repeated Attachments attachments = 11;
}

message Attachments {
    string type = 1;
    string url = 2;
}
message UpdateAdjustRequest {
    uint64 adjust_id = 1;
    repeated CreatedAdjustProduct products = 2;
    string remark = 3;
    string reason_type = 4;
    google.protobuf.Timestamp adjust_date = 5;
    // 区分门店还是仓库: STORE or WAREHOUSE
    string branch_type = 6;
    // 附件
    repeated Attachments attachments = 7;
    uint64 position_id = 8;
}
message DeleteAdjustRequest {
    uint64 adjust_id = 1;
}
message DeleteAdjustResponse {
    bool result = 1;
}
message DeleteAdjustProductRequest {
    uint64 adjust_id = 1;
    repeated uint64 ids = 2;    // 删除商品对应的记录ids
}
message DeleteAdjustProductResponse {
    bool result = 1;
}
message GetAdjustBiCollectRequest {
    repeated uint64 category_ids = 1;
    string product_name = 2;
    google.protobuf.Timestamp start_date = 3;
    google.protobuf.Timestamp end_date = 4;
    uint32 limit = 5;
    uint32 offset = 6;
    bool include_total = 7;
    repeated uint64 store_ids = 8;
    repeated uint64 bom_product_id =9;
    string period_symbol = 10;
    // 排序(默认asc)
    string order = 11;
    string sort = 12;
    string code = 13;
    // 报废原因
    string reason_type = 14;
    bool is_wms_store = 15;
    // 区分门店还是仓库
    string branch_type = 16;
    string lan = 17;
    uint32 hour_offset = 18;
    // 暂不使用
    repeated uint64 position_ids = 19;
}
message AdjustBiCollectResponse {
    double accounting_quantity = 1;
    uint64 accounting_unit_id = 2;
    string accounting_unit_name = 3;
    string category_code = 4;
    uint64 category_id = 5;
    string category_name = 6;
    //"category_parent"
    string product_code = 7;
    uint64 product_id = 8;
    string product_name = 9;
    double quantity = 11;
    string store_code = 12;
    uint64 store_id = 13;
    string store_name = 14;
    uint64 unit_id = 15;
    string unit_name = 16;
    string reason_type = 17;
    string reason_name = 21;
    // 拆解原料
    uint64 bom_product_id = 18;
    string bom_product_code = 19;
    string bom_product_name = 20;
    //bom配方单位
    uint64 bom_unit_id = 22;
    string bom_unit_code = 23;
    string bom_unit_name = 24;
    //bom核算单位
    uint64 bom_accounting_unit_id = 25;
    string bom_accounting_unit_code = 26;
    string bom_accounting_unit_name = 27;
    double qty = 28;
    double price = 29;
    double accounting_qty = 30;
    double cost = 31;
    string period_symbol = 32;
    uint64 position_id = 33;
    string position_code = 34;
    string position_name = 35;
    google.protobuf.Timestamp adjust_date = 36;
}
message AD_total {
    uint64 count = 1;
    double sum_quantity = 2;
    double sum_accounting_quantity = 3;
    double sum_qty = 4;
    double sum_accounting_qty = 5;
}
message GetAdjustBiCollectResponse {
    repeated AdjustBiCollectResponse rows = 1;
    AD_total total = 2;
}

message CancelAdjustRequest {
    uint64 adjust_id = 1;
}
message CancelAdjustResponse {
    bool result = 1;
}
message GetAdjustCollectDetailedRequest {
    repeated uint64 category_ids = 1;
    string product_name = 2;
    google.protobuf.Timestamp start_date = 3;
    google.protobuf.Timestamp end_date = 4;
    uint32 limit = 5;
    uint32 offset = 6;
    bool include_total = 7;
    repeated uint64 store_ids = 8;
    repeated uint64 bom_product_id = 9;
    // 排序(默认asc)
    string order = 10;
    string sort = 11;
    string code = 12;
    bool is_wms_store = 14;
    // 区分门店还是仓库
    string branch_type = 16;
    string lan = 17;
    repeated uint64 position_ids = 18;
    // 报废原因
    string reason_type = 19;
}
message AdjustCollectDetailed {
    double accounting_quantity = 1;
    uint64 accounting_unit_id = 2;
    string accounting_unit_name = 3;
    string adjust_code = 4;
    google.protobuf.Timestamp adjust_date = 5;
    uint64 adjust_id = 6;
    string category_code = 7;
    uint64 category_id = 8;
    string category_name = 9;
    uint64 id = 10;
    string product_code = 11;
    uint64 product_id = 12;
    string product_name = 13;
    double quantity = 14;
    string reason_type = 15;
    string reason_name = 33;
    string store_code = 16;
    uint64 store_id = 17;
    string store_name = 18;
    uint64 unit_id = 19;
    string unit_name = 20;
    // 拆解原料
    uint64 bom_product_id = 21;
    string bom_product_code = 22;
    string bom_product_name = 23;
    //bom配方单位
    uint64 bom_unit_id = 24;
    string bom_unit_code = 25;
    string bom_unit_name = 26;
    //bom核算单位
    uint64 bom_accounting_unit_id = 27;
    string bom_accounting_unit_code = 28;
    string bom_accounting_unit_name = 29;
    double qty = 30;
    double price = 31;
    double accounting_qty = 32;
    double cost = 34;
    string branch_type = 35;
    string remark = 36;
    uint64 position_id = 37;
    string position_code = 38;
    string position_name = 39;
}
message GetAdjustCollectDetailedResponse {
    repeated AdjustCollectDetailed rows = 1;
    AD_total total = 2;
}
message AutoCloseCreatedAdjustResponse {
    bool result = 1;
}
message AutoCloseCreatedAdjustRequest {
    string adjust_date = 1;
    uint64 partner_id = 2;
    uint64 user_id = 3;
}

message GetMaterialAdjustDataRequest{
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    repeated uint64 store_ids = 3;
    repeated uint64 material_ids = 4;
    uint64 limit = 5;
    uint64 offset = 6;
    bool is_wms_store = 7;
    string lan = 10;
    bool include_total = 11;
}

message GetMaterialAdjustDataResponse {
    repeated AdjustResponse rows = 1;
    AD_total total = 2;
}

message AdjustResponse{
    // 核算数量
    double accounting_quantity = 1;
    uint64 accounting_unit_id = 2;
    string accounting_unit_name = 3;
    string category_code = 4;
    uint64 category_id = 5;
    string category_name = 6;
    //"category_parent"
    string product_code = 7;
    uint64 product_id = 8;
    string product_name = 9;
    // 报废数量
    double quantity = 11;
    // 门店编号，id,名称
    string store_code = 12;
    uint64 store_id = 13;
    string store_name = 14;
    // 单位
    uint64 unit_id = 15;
    string unit_name = 16;
    string reason_type = 17;
    // 拆解原料
    uint64 bom_product_id = 18;
    string bom_product_code = 19;
    string bom_product_name = 20;
    double qty = 21;
    //bom配方单位
    uint64 bom_unit_id = 22;
    string bom_unit_code = 23;
    string bom_unit_name = 24;
    //bom核算单位
    uint64 bom_accounting_unit_id = 25;
    string bom_accounting_unit_code = 26;
    string bom_accounting_unit_name = 27;
    double accounting_qty = 28;
    // 单位成本
    double price = 29;
    // 物料成本
    double cost = 30;
    // 报废日期
    string adjust_date = 31;
}

message CreatedAdjustProductByCode {
    uint64 id = 1;
    string product_code = 2;
    string unit_code = 3;
    double quantity = 4;
    string reason_type = 5;
    repeated SkuRemark skuRemark = 6;
}
message SkuRemark {
    message Tag {
        uint64 id = 1;
        string code = 2;
        string name = 3;
    }
    Tag name = 1;
    Tag values = 2;
}
message CreatedAdjustByCodeRequest {
    string adjust_store = 1;
    repeated CreatedAdjustProductByCode products = 2;
    string reason_type = 3;
    string remark = 4;
    string request_id = 5;
    string adjust_date = 6;
    string lan = 7;
}

message AdjustPositionProducts{
    uint64 position_id = 1;
    string position_code = 2;
    string position_name = 3;
    repeated AdjustProduct products = 4;
    uint64 total = 5;
}