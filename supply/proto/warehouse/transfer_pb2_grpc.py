# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from warehouse import transfer_pb2 as warehouse_dot_transfer__pb2


class WarehouseTransferStub(object):
  """Transfer 调拨服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetTransfer = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetTransfer',
        request_serializer=warehouse_dot_transfer__pb2.GetTransferRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetTransferResponse.FromString,
        )
    self.GetTransferByID = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetTransferByID',
        request_serializer=warehouse_dot_transfer__pb2.GetTransferByIDRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetTransferByIDResponse.FromString,
        )
    self.GetTransferProductByBranchID = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetTransferProductByBranchID',
        request_serializer=warehouse_dot_transfer__pb2.GetTransferProductByBranchIDRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetTransferProductByBranchIDResponse.FromString,
        )
    self.GetTransferRegionByBranchID = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetTransferRegionByBranchID',
        request_serializer=warehouse_dot_transfer__pb2.GetTransferRegionByBranchIDRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetTransferRegionByBranchIDResponse.FromString,
        )
    self.GetTransferProductByTransferID = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetTransferProductByTransferID',
        request_serializer=warehouse_dot_transfer__pb2.GetTransferProductByTransferIDRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetTransferProductByTransferIDResponse.FromString,
        )
    self.CreateTransfer = channel.unary_unary(
        '/warehouse.WarehouseTransfer/CreateTransfer',
        request_serializer=warehouse_dot_transfer__pb2.CreateTransferRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.Transfer.FromString,
        )
    self.UpdateTransfer = channel.unary_unary(
        '/warehouse.WarehouseTransfer/UpdateTransfer',
        request_serializer=warehouse_dot_transfer__pb2.UpdateTransferRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.UpdateTransferResponse.FromString,
        )
    self.ConfirmTransfer = channel.unary_unary(
        '/warehouse.WarehouseTransfer/ConfirmTransfer',
        request_serializer=warehouse_dot_transfer__pb2.ConfirmTransferRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.Transfer.FromString,
        )
    self.DeleteTransferProduct = channel.unary_unary(
        '/warehouse.WarehouseTransfer/DeleteTransferProduct',
        request_serializer=warehouse_dot_transfer__pb2.DeleteTransferProductRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.DeleteTransferProductResponse.FromString,
        )
    self.DeleteTransfer = channel.unary_unary(
        '/warehouse.WarehouseTransfer/DeleteTransfer',
        request_serializer=warehouse_dot_transfer__pb2.DeleteTransferRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.DeleteTransferResponse.FromString,
        )
    self.SubmitTransfer = channel.unary_unary(
        '/warehouse.WarehouseTransfer/SubmitTransfer',
        request_serializer=warehouse_dot_transfer__pb2.SubmitTransferRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.Transfer.FromString,
        )
    self.CancelTransfer = channel.unary_unary(
        '/warehouse.WarehouseTransfer/CancelTransfer',
        request_serializer=warehouse_dot_transfer__pb2.CancelTransferRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.Transfer.FromString,
        )
    self.GetTransferCollect = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetTransferCollect',
        request_serializer=warehouse_dot_transfer__pb2.GetTransferCollectRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetTransferCollectResponse.FromString,
        )
    self.GetTransferCollectDetailed = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetTransferCollectDetailed',
        request_serializer=warehouse_dot_transfer__pb2.GetTransferCollectDetailedRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetTransferCollectDetailedResponse.FromString,
        )
    self.CreateInnerTransfer = channel.unary_unary(
        '/warehouse.WarehouseTransfer/CreateInnerTransfer',
        request_serializer=warehouse_dot_transfer__pb2.CreateInnerTransferRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.CreateInnerTransferResponse.FromString,
        )
    self.GetTransferList = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetTransferList',
        request_serializer=warehouse_dot_transfer__pb2.GetTransferListRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetTransferListResponse.FromString,
        )
    self.GetProductList = channel.unary_unary(
        '/warehouse.WarehouseTransfer/GetProductList',
        request_serializer=warehouse_dot_transfer__pb2.GetProductListRequest.SerializeToString,
        response_deserializer=warehouse_dot_transfer__pb2.GetProductListResponse.FromString,
        )


class WarehouseTransferServicer(object):
  """Transfer 调拨服务
  """

  def GetTransfer(self, request, context):
    """GetTransfer 查询调拨单1
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferByID(self, request, context):
    """GetTransferByID 查询一个调拨单2
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferProductByBranchID(self, request, context):
    """GetTransferProductByBranchID 取得门店可调拨商品3
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferRegionByBranchID(self, request, context):
    """GetTransferRegionByID 查询相同属性区域门店4
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferProductByTransferID(self, request, context):
    """GetTransferProductByTransferID 获取一个调拨单商品5
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateTransfer(self, request, context):
    """CreateTransfer 创建调拨单6
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateTransfer(self, request, context):
    """UpdateTransfer 修改调拨单7
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmTransfer(self, request, context):
    """ConfirmTransfer 确认调拨单8
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteTransferProduct(self, request, context):
    """DeleteTransferProduct 删除调拨单商品10
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteTransfer(self, request, context):
    """DeleteTransfer 删除调拨单11
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitTransfer(self, request, context):
    """SubmitTransfer 提交调拨单12
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelTransfer(self, request, context):
    """CancelTransfer 取消调拨单13
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferCollect(self, request, context):
    """SubmitTransferReceiving 提交调拨单收货数量13
    rpc SubmitTransferReceiving (SubmitTransferReceivingRequest) returns (Transfer) {
    option (google.api.http) = { put: "/api/v2/supply/warehouse/transfer/main/{transfer_id}/submit/receiving" body: "*"};
    }
    FinalizedTransfer 核算完成调拨单13
    rpc FinalizedTransfer (FinalizedTransferRequest) returns (FinalizedTransferResponse) {
    option (google.api.http) = { put: "/api/v2/supply/warehouse/transfer/main/{transfer_id}/finalize"};
    }
    GetTransferCollect调拨单汇总报表14
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferCollectDetailed(self, request, context):
    """GetTransferCollectDetailed调拨单明细汇总报表15
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateInnerTransfer(self, request, context):
    """CreateTransfer 自动创建内部调拨单接口(暂时仅内部rpc调用)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferList(self, request, context):
    """GetTransferList 查询调拨单列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductList(self, request, context):
    """GetProductList 查询商品列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_WarehouseTransferServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransfer,
          request_deserializer=warehouse_dot_transfer__pb2.GetTransferRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetTransferResponse.SerializeToString,
      ),
      'GetTransferByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferByID,
          request_deserializer=warehouse_dot_transfer__pb2.GetTransferByIDRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetTransferByIDResponse.SerializeToString,
      ),
      'GetTransferProductByBranchID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferProductByBranchID,
          request_deserializer=warehouse_dot_transfer__pb2.GetTransferProductByBranchIDRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetTransferProductByBranchIDResponse.SerializeToString,
      ),
      'GetTransferRegionByBranchID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferRegionByBranchID,
          request_deserializer=warehouse_dot_transfer__pb2.GetTransferRegionByBranchIDRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetTransferRegionByBranchIDResponse.SerializeToString,
      ),
      'GetTransferProductByTransferID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferProductByTransferID,
          request_deserializer=warehouse_dot_transfer__pb2.GetTransferProductByTransferIDRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetTransferProductByTransferIDResponse.SerializeToString,
      ),
      'CreateTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.CreateTransfer,
          request_deserializer=warehouse_dot_transfer__pb2.CreateTransferRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.Transfer.SerializeToString,
      ),
      'UpdateTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateTransfer,
          request_deserializer=warehouse_dot_transfer__pb2.UpdateTransferRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.UpdateTransferResponse.SerializeToString,
      ),
      'ConfirmTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmTransfer,
          request_deserializer=warehouse_dot_transfer__pb2.ConfirmTransferRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.Transfer.SerializeToString,
      ),
      'DeleteTransferProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteTransferProduct,
          request_deserializer=warehouse_dot_transfer__pb2.DeleteTransferProductRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.DeleteTransferProductResponse.SerializeToString,
      ),
      'DeleteTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteTransfer,
          request_deserializer=warehouse_dot_transfer__pb2.DeleteTransferRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.DeleteTransferResponse.SerializeToString,
      ),
      'SubmitTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitTransfer,
          request_deserializer=warehouse_dot_transfer__pb2.SubmitTransferRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.Transfer.SerializeToString,
      ),
      'CancelTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.CancelTransfer,
          request_deserializer=warehouse_dot_transfer__pb2.CancelTransferRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.Transfer.SerializeToString,
      ),
      'GetTransferCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferCollect,
          request_deserializer=warehouse_dot_transfer__pb2.GetTransferCollectRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetTransferCollectResponse.SerializeToString,
      ),
      'GetTransferCollectDetailed': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferCollectDetailed,
          request_deserializer=warehouse_dot_transfer__pb2.GetTransferCollectDetailedRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetTransferCollectDetailedResponse.SerializeToString,
      ),
      'CreateInnerTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.CreateInnerTransfer,
          request_deserializer=warehouse_dot_transfer__pb2.CreateInnerTransferRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.CreateInnerTransferResponse.SerializeToString,
      ),
      'GetTransferList': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferList,
          request_deserializer=warehouse_dot_transfer__pb2.GetTransferListRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetTransferListResponse.SerializeToString,
      ),
      'GetProductList': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductList,
          request_deserializer=warehouse_dot_transfer__pb2.GetProductListRequest.FromString,
          response_serializer=warehouse_dot_transfer__pb2.GetProductListResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'warehouse.WarehouseTransfer', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
