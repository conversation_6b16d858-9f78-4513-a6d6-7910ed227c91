# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: pos_stocktake.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='pos_stocktake.proto',
  package='pos_stocktake',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x13pos_stocktake.proto\x12\rpos_stocktake\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"d\n\x19UploadPosStocktakeRequest\x12\x12\n\nstore_code\x18\x01 \x01(\t\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x11\n\tsync_type\x18\x04 \x01(\t\",\n\x1aUploadPosStocktakeResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"1\n\x1bGetStocktakeQuantityRequest\x12\x12\n\nstore_code\x18\x01 \x01(\t\"?\n\x1bGetStocktakeQuantityReponse\x12\x10\n\x08quantity\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t2\xd6\x02\n\x0cPosStocktake\x12\x97\x01\n\x12UploadPosStocktake\x12(.pos_stocktake.UploadPosStocktakeRequest\x1a).pos_stocktake.UploadPosStocktakeResponse\",\x82\xd3\xe4\x93\x02&\x1a!/api/v2/supply/stocktake/pos/sync:\x01*\x12\xab\x01\n\x14GetStocktakeQuantity\x12*.pos_stocktake.GetStocktakeQuantityRequest\x1a*.pos_stocktake.GetStocktakeQuantityReponse\";\x82\xd3\xe4\x93\x02\x35\x12\x30/api/v2/supply/stocktake/weight/qty/{store_code}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_UPLOADPOSSTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='UploadPosStocktakeRequest',
  full_name='pos_stocktake.UploadPosStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='pos_stocktake.UploadPosStocktakeRequest.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='pos_stocktake.UploadPosStocktakeRequest.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='pos_stocktake.UploadPosStocktakeRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sync_type', full_name='pos_stocktake.UploadPosStocktakeRequest.sync_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=101,
  serialized_end=201,
)


_UPLOADPOSSTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='UploadPosStocktakeResponse',
  full_name='pos_stocktake.UploadPosStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='pos_stocktake.UploadPosStocktakeResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=203,
  serialized_end=247,
)


_GETSTOCKTAKEQUANTITYREQUEST = _descriptor.Descriptor(
  name='GetStocktakeQuantityRequest',
  full_name='pos_stocktake.GetStocktakeQuantityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='pos_stocktake.GetStocktakeQuantityRequest.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=249,
  serialized_end=298,
)


_GETSTOCKTAKEQUANTITYREPONSE = _descriptor.Descriptor(
  name='GetStocktakeQuantityReponse',
  full_name='pos_stocktake.GetStocktakeQuantityReponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='quantity', full_name='pos_stocktake.GetStocktakeQuantityReponse.quantity', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='pos_stocktake.GetStocktakeQuantityReponse.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=300,
  serialized_end=363,
)

DESCRIPTOR.message_types_by_name['UploadPosStocktakeRequest'] = _UPLOADPOSSTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['UploadPosStocktakeResponse'] = _UPLOADPOSSTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeQuantityRequest'] = _GETSTOCKTAKEQUANTITYREQUEST
DESCRIPTOR.message_types_by_name['GetStocktakeQuantityReponse'] = _GETSTOCKTAKEQUANTITYREPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

UploadPosStocktakeRequest = _reflection.GeneratedProtocolMessageType('UploadPosStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADPOSSTOCKTAKEREQUEST,
  __module__ = 'pos_stocktake_pb2'
  # @@protoc_insertion_point(class_scope:pos_stocktake.UploadPosStocktakeRequest)
  ))
_sym_db.RegisterMessage(UploadPosStocktakeRequest)

UploadPosStocktakeResponse = _reflection.GeneratedProtocolMessageType('UploadPosStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADPOSSTOCKTAKERESPONSE,
  __module__ = 'pos_stocktake_pb2'
  # @@protoc_insertion_point(class_scope:pos_stocktake.UploadPosStocktakeResponse)
  ))
_sym_db.RegisterMessage(UploadPosStocktakeResponse)

GetStocktakeQuantityRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeQuantityRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEQUANTITYREQUEST,
  __module__ = 'pos_stocktake_pb2'
  # @@protoc_insertion_point(class_scope:pos_stocktake.GetStocktakeQuantityRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeQuantityRequest)

GetStocktakeQuantityReponse = _reflection.GeneratedProtocolMessageType('GetStocktakeQuantityReponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEQUANTITYREPONSE,
  __module__ = 'pos_stocktake_pb2'
  # @@protoc_insertion_point(class_scope:pos_stocktake.GetStocktakeQuantityReponse)
  ))
_sym_db.RegisterMessage(GetStocktakeQuantityReponse)



_POSSTOCKTAKE = _descriptor.ServiceDescriptor(
  name='PosStocktake',
  full_name='pos_stocktake.PosStocktake',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=366,
  serialized_end=708,
  methods=[
  _descriptor.MethodDescriptor(
    name='UploadPosStocktake',
    full_name='pos_stocktake.PosStocktake.UploadPosStocktake',
    index=0,
    containing_service=None,
    input_type=_UPLOADPOSSTOCKTAKEREQUEST,
    output_type=_UPLOADPOSSTOCKTAKERESPONSE,
    serialized_options=_b('\202\323\344\223\002&\032!/api/v2/supply/stocktake/pos/sync:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeQuantity',
    full_name='pos_stocktake.PosStocktake.GetStocktakeQuantity',
    index=1,
    containing_service=None,
    input_type=_GETSTOCKTAKEQUANTITYREQUEST,
    output_type=_GETSTOCKTAKEQUANTITYREPONSE,
    serialized_options=_b('\202\323\344\223\0025\0220/api/v2/supply/stocktake/weight/qty/{store_code}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_POSSTOCKTAKE)

DESCRIPTOR.services_by_name['PosStocktake'] = _POSSTOCKTAKE

# @@protoc_insertion_point(module_scope)
