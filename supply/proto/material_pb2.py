# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: material.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='material.proto',
  package='material',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x0ematerial.proto\x12\x08material\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe8\x01\n\x1cGetMaterialDifferenceRequest\x12.\n\nsales_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x63lose_store\x18\x02 \x01(\x08\x12\x15\n\rclose_product\x18\x03 \x01(\x08\x12\x13\n\x0b\x63lose_start\x18\x04 \x01(\x08\x12\x11\n\tclose_end\x18\x05 \x01(\x08\x12\x0e\n\x06offset\x18\x06 \x01(\x03\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x10\n\x08store_id\x18\x08 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\t \x03(\x04\"\xf8\x04\n\x12MaterialDifference\x12\x12\n\nstore_code\x18\x01 \x01(\t\x12\x12\n\nstore_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63lose_store\x18\x03 \x01(\x08\x12\x15\n\rbranch_region\x18\x04 \x01(\t\x12\x12\n\ngeo_region\x18\x05 \x01(\t\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x15\n\rclose_product\x18\x08 \x01(\x08\x12\x18\n\x10product_category\x18\t \x01(\t\x12\x14\n\x0cproduct_type\x18\n \x01(\t\x12\x10\n\x08\x62om_type\x18\x0b \x01(\t\x12\x11\n\tsale_type\x18\x0c \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\r \x01(\t\x12\x15\n\rproduct_price\x18\x0e \x01(\x01\x12\x16\n\x0estart_quantity\x18\x0f \x01(\x01\x12\x13\n\x0bstart_price\x18\x10 \x01(\x01\x12\x14\n\x0c\x65nd_quantity\x18\x11 \x01(\x01\x12\x11\n\tend_price\x18\x12 \x01(\x01\x12\x1a\n\x12receiving_quantity\x18\x13 \x01(\x01\x12\x17\n\x0freceiving_price\x18\x14 \x01(\x01\x12\x19\n\x11transfer_quantity\x18\x15 \x01(\x01\x12\x16\n\x0etransfer_price\x18\x16 \x01(\x01\x12\x16\n\x0esales_quantity\x18\x17 \x01(\x01\x12\x13\n\x0bsales_price\x18\x18 \x01(\x01\x12\x15\n\rdiff_quantity\x18\x19 \x01(\x01\x12\x12\n\ndiff_price\x18\x1a \x01(\x01\x12\x17\n\x0f\x64iff_percentage\x18\x1b \x01(\x01\"Z\n\x1dGetMaterialDifferenceResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.material.MaterialDifference\x12\r\n\x05total\x18\x02 \x01(\x04\x32\xa1\x01\n\x08material\x12\x94\x01\n\x15GetMaterialDifference\x12&.material.GetMaterialDifferenceRequest\x1a\'.material.GetMaterialDifferenceResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/supply/material_differenceb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETMATERIALDIFFERENCEREQUEST = _descriptor.Descriptor(
  name='GetMaterialDifferenceRequest',
  full_name='material.GetMaterialDifferenceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sales_date', full_name='material.GetMaterialDifferenceRequest.sales_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_store', full_name='material.GetMaterialDifferenceRequest.close_store', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_product', full_name='material.GetMaterialDifferenceRequest.close_product', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_start', full_name='material.GetMaterialDifferenceRequest.close_start', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_end', full_name='material.GetMaterialDifferenceRequest.close_end', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='material.GetMaterialDifferenceRequest.offset', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='material.GetMaterialDifferenceRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='material.GetMaterialDifferenceRequest.store_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='material.GetMaterialDifferenceRequest.product_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=92,
  serialized_end=324,
)


_MATERIALDIFFERENCE = _descriptor.Descriptor(
  name='MaterialDifference',
  full_name='material.MaterialDifference',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='material.MaterialDifference.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='material.MaterialDifference.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_store', full_name='material.MaterialDifference.close_store', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region', full_name='material.MaterialDifference.branch_region', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_region', full_name='material.MaterialDifference.geo_region', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='material.MaterialDifference.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='material.MaterialDifference.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_product', full_name='material.MaterialDifference.close_product', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category', full_name='material.MaterialDifference.product_category', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='material.MaterialDifference.product_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_type', full_name='material.MaterialDifference.bom_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='material.MaterialDifference.sale_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='material.MaterialDifference.accounting_unit_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_price', full_name='material.MaterialDifference.product_price', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_quantity', full_name='material.MaterialDifference.start_quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_price', full_name='material.MaterialDifference.start_price', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_quantity', full_name='material.MaterialDifference.end_quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_price', full_name='material.MaterialDifference.end_price', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_quantity', full_name='material.MaterialDifference.receiving_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_price', full_name='material.MaterialDifference.receiving_price', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_quantity', full_name='material.MaterialDifference.transfer_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_price', full_name='material.MaterialDifference.transfer_price', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_quantity', full_name='material.MaterialDifference.sales_quantity', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='material.MaterialDifference.sales_price', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='material.MaterialDifference.diff_quantity', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_price', full_name='material.MaterialDifference.diff_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_percentage', full_name='material.MaterialDifference.diff_percentage', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=327,
  serialized_end=959,
)


_GETMATERIALDIFFERENCERESPONSE = _descriptor.Descriptor(
  name='GetMaterialDifferenceResponse',
  full_name='material.GetMaterialDifferenceResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='material.GetMaterialDifferenceResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='material.GetMaterialDifferenceResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=961,
  serialized_end=1051,
)

_GETMATERIALDIFFERENCEREQUEST.fields_by_name['sales_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALDIFFERENCERESPONSE.fields_by_name['rows'].message_type = _MATERIALDIFFERENCE
DESCRIPTOR.message_types_by_name['GetMaterialDifferenceRequest'] = _GETMATERIALDIFFERENCEREQUEST
DESCRIPTOR.message_types_by_name['MaterialDifference'] = _MATERIALDIFFERENCE
DESCRIPTOR.message_types_by_name['GetMaterialDifferenceResponse'] = _GETMATERIALDIFFERENCERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetMaterialDifferenceRequest = _reflection.GeneratedProtocolMessageType('GetMaterialDifferenceRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALDIFFERENCEREQUEST,
  __module__ = 'material_pb2'
  # @@protoc_insertion_point(class_scope:material.GetMaterialDifferenceRequest)
  ))
_sym_db.RegisterMessage(GetMaterialDifferenceRequest)

MaterialDifference = _reflection.GeneratedProtocolMessageType('MaterialDifference', (_message.Message,), dict(
  DESCRIPTOR = _MATERIALDIFFERENCE,
  __module__ = 'material_pb2'
  # @@protoc_insertion_point(class_scope:material.MaterialDifference)
  ))
_sym_db.RegisterMessage(MaterialDifference)

GetMaterialDifferenceResponse = _reflection.GeneratedProtocolMessageType('GetMaterialDifferenceResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALDIFFERENCERESPONSE,
  __module__ = 'material_pb2'
  # @@protoc_insertion_point(class_scope:material.GetMaterialDifferenceResponse)
  ))
_sym_db.RegisterMessage(GetMaterialDifferenceResponse)



_MATERIAL = _descriptor.ServiceDescriptor(
  name='material',
  full_name='material.material',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=1054,
  serialized_end=1215,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetMaterialDifference',
    full_name='material.material.GetMaterialDifference',
    index=0,
    containing_service=None,
    input_type=_GETMATERIALDIFFERENCEREQUEST,
    output_type=_GETMATERIALDIFFERENCERESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/supply/material_difference'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MATERIAL)

DESCRIPTOR.services_by_name['material'] = _MATERIAL

# @@protoc_insertion_point(module_scope)
