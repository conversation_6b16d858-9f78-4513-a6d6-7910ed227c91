syntax = "proto3";
option go_package = "proto";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
//import "protoc-gen-swagger/options/annotations.proto";

package inventory;
//
//option (grpc.gateway.protoc_gen_swagger.options.openapiv2_swagger) = {
//    info: {
//      version: "1.0"
//      title:"库存引擎"
//      description:"HWS统一库存引擎"
//      contact:{
//                name:"<PERSON>"
//                email:"<PERSON>@hex-tech.net"
//
//        };
//    };
//    external_docs: {
//      url: "https://gitlab.hexcloud.cn/histore/inventory"
//      description: "统一库存引擎开放接口文档"
//    }
//    schemes: [HTTPS,HTTP]
//
//};


/**
库存管理服务

提供几个关键的服务
1. 库存调整，包括增加、减少和冻结
2. 统一的批次管理
3. 查询服务

*/
service InventoryService {
  //提交一笔库存记录服务
  rpc Account (BatchRequest) returns (BatchResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/accounting",
      body:"*"
    };
  }

  //库存转移
  rpc Transfer (BatchRequest) returns (BatchResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/transfer",
      body:"*"
    };
  }
  //库存转移提交
  rpc TransferCommit (BatchQuery) returns (BatchResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/transfer/{id}",
      body:"*"
    };
  }
  //库存批处理请求，可以视为唯一的入口
  rpc Batch (BatchRequest) returns (BatchResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/batch",
      body:"*"
    };
  }
  //查询库存记录情况
  rpc QueryAccouting (BatchQuery) returns (BatchQueryResponse) {
    option (google.api.http) = {
      get:"/api/v1/inventory/accounting/{id}"
    };
  }
  //查询单笔库存调拨
  rpc QueryTransfer (BatchQuery) returns (BatchQueryResponse) {
    option (google.api.http) = {
      get:"/api/v1/inventory/transfer/{id}"
    };
  }
  //查询库存批次请求的处理结果
  rpc QueryBatch (BatchQuery) returns (BatchQueryResponse) {
    option (google.api.http) = {
      get:"/api/v1/inventory/batch/{id}"
    };
  }

  //查询库存批次请求的处理结果
  rpc QueryBatchParameter (BatchQuery) returns (BatchQueryResponse) {
    option (google.api.http) = {
      get:"/api/v1/inventory/batch/parameter"
    };
  }

  //批次查询库存明细信息
  rpc QueryStockBatch (StockQueryRequest) returns (StockQueryResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/stock"
      body:"*"
    };
  }

  //确认库存账号，如果没有，会自动创建
  rpc EnsureSkuAccount (SkuAccountRequest) returns (SkuAccountResponse) {
    option (google.api.http) = {
      get:"/api/v1/inventory/sku/ensure"
    };
  }
  //按照BranchID查询所有的商品实时库存，支持多Branch和多Product条件查询
  rpc QueryBranchInventory (InventoryQueryRequest) returns (InventoryQueryResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/branch"
      body:"*"
    };
  };
  //批量查询Account的实时库存
  rpc QueryAccountInventoryList(InventoryQueryLsitRequest) returns (InventoryQueryResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/accounts"
      body:"*"
    };
  }
  //按照批次查询这个批次商品的实时库存
  rpc QueryLatestInventoryByBatch (BatchQuery) returns (InventoryQueryResponse) {
    option (google.api.http) = {
      get:"/api/v1/inventory/batch/{id}/latest"
    };
  }
  //查询库存异动
  rpc QueryHooks (HooksRequest) returns (HooksResponse) {
    option (google.api.http) = {
      get:"/api/v1/inventory/hooks"
    };
  }
  //创建一个库存异动通知
  rpc CreateHook (HooksCreateRequest) returns (HooksCreateResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/hooks"
      body:"*"
    };
  }

  // 实时调整冻结库存
  rpc Freeze(FreezeRequest) returns (FreezeResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/freeze"
      body:"*"
    };
  }

  // 释放冻结的库存
  rpc Unfreeze (UnfreezeRequest) returns (FreezeResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/unfreeze"
      body:"*"
    };
  }

  // 查询冻结数据
  rpc QueryFreezeList(FreezeQueryRequest) returns (FreezeResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/freeze/query"
      body:"*"
    };
  }

  //查询库存流水【弃用】
  rpc QueryLogList(ListQueryRequest) returns (ListLogQueryResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/log/query"
      body:"*"
    };
  }

  //查询单一商品的库存快照【功能未实现】
  rpc QuerySnapshot(SnapshotQueryRequest) returns (SnapshotQueryResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/snapshot/single"
      body:"*"
    };
  }

  //批量查询库存快照数据
  rpc QuerySnapshotList(ListQueryRequest) returns(SnapshotBatchQueryResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/snapshot/query"
      body:"*"
    };
  }

  //时段汇总
  rpc QuerySnapshotSumList(SumListQueryRequest) returns(SumListQueryResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/snapshot/query/sum"
      body:"*"
    };
  }

  //批量查询库存快照数据
  rpc QueryGroupSnapshotList(ListGroupQueryRequest) returns(SnapshotBatchQueryResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/snapshot/querygroup"
      body:"*"
    };
  }

  //批量查询库存快照统计数据
  rpc QuerySnapshotStats (SnapshotStatRequest) returns (SnapshotStatResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/snapshot/querystats"
      body:"*"
    };
  };

  //查询branch里 有流水的商品
  rpc QuerySnapshotStatGroup(SnapshotStatGroupRequest) returns(SnapshotStatGroupResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/snapshot/stat"
      body:"*"
    };
  }

  rpc ListInventoryLog(ListInventoryLogRequest) returns (ListInventoryLogResponse) {
    option (google.api.http) = {
      post:"/api/v1/inventory/log/list"
      body:"*"
    };
  }

  rpc ListGroupEventLog(ListGroupEventLogRequest) returns (ListGroupEventLogResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/log/event"
      body:"*"
    };
  }

  rpc ListGroupSnapshot(ListGroupSnapshotRequest) returns (ListGroupSnapshotResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/snapshot/group"
      body:"*"
    };
  }

  // 门店群组增删改，例如成本中心
  rpc Group(GroupRequest) returns (GroupResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/group"
      body:"*"
    };
  }

  // 门店群组查询
  rpc QueryGroupBranch(QueryGroupBranchRequest) returns (QueryGroupBranchResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/group/query"
      body:"*"
    };
  }

  rpc QueryGroupProduct(QueryGroupProductRequest) returns (QueryGroupProductResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/group/product"
      body:"*"
    };
  }

  // 盘点单查询
  rpc QueryStocktake(QueryStocktakeRequest) returns (QueryStocktakeResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/stocktake/query"
      body:"*"
    };
  }
  // 商品实时库存查询并排序
  rpc QueryBranchInventorySort(QueryBranchInventorySortRequest)returns(QueryBranchInventorySortResponse){
    option (google.api.http) = {
      post:"/api/v1/inventory/branch-inventory-sort/query"
      body:"*"
    };
  }
};

message UnfreezeRequest{
  string trace_id = 1;
}

message ListGroupSnapshotRequest {
  int64 branch_id = 1;
  repeated int64 product_ids = 2;
  string code = 3;
  google.protobuf.Timestamp start_time = 6;
  google.protobuf.Timestamp end_time = 7;
}

message ListGroupSnapshotResponse {
  ListGroupSnapshotRequest request = 2;
  repeated GroupSnapShot group_snapshots = 3;
}

message GroupSnapShot {
  message GroupItem {
    string code = 1;
    Amount amount = 2;
  }
  //SKU账号
  Account account = 1;
  repeated GroupItem items = 2;
}


message ListGroupEventLogRequest {
  int64 branch_id = 1;
  repeated int64 product_ids = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
  int64 limit = 5;
  int64 offset = 6;
}

message ListGroupEventLogResponse {
  ListGroupEventLogRequest request = 1;
  int64 total = 2;
  repeated GroupEventLog group_logs = 3;
}

message GroupEventLog {
  int64 product_id = 3;
  string code = 4;
  string action = 5;
  double qty = 6;
  int64 count = 7;
}

message ListInventoryLogRequest {
  int64 branch_id = 2;   // eric确认，每次只能查询一个门店
  repeated int64 product_ids = 3;
  repeated string codes = 4;
  repeated string actions = 5;
  int64 limit = 6;
  int64 offset = 7;

  google.protobuf.Timestamp start_time = 8;
  google.protobuf.Timestamp end_time = 9;

  repeated int64 batch_nos = 10;
  repeated string trace_ids = 11;
  string account_type = 12;
  repeated int64 sub_account_ids = 13;
}

message ListInventoryLogResponse {
  ListInventoryLogRequest request = 1;
  repeated InventoryLog logs = 2;
  int64 total = 4;
  double amount_sum = 5;
}

//库存数量
message Amount {
  double qty = 1;
  double round = 2;
  map<string, int32> extra = 3;
  string status_account = 4;
    // 成本单价
  double price = 5;
  // 库存金额
  double amount = 6;
}
//分页
message Pagination {
  int32 offset = 1;
  int32 limit = 2;
  int32 page_size = 3;
  bool total = 4;
}
//批次选项
message BatchOption {
  int64 id = 1;
  string batch_no = 2;
  string code = 3;
}


// 库存账号，对于一个SKU，由BranchID + ProductID组成
message Account {
  enum ExtraType {
    SKU = 0; //主账号
    SUB_ACCOUNT = 1; //子账号
    FREEZE_CODE = 2; //冻结账户
    BROKER = 3; //中间账户
    TRANSER = 4; //调拨
    GROUP = 5; //群组类账户
  }
  message Extra {
    int64 id = 1; //扩展账号ID
    string code = 2; //业务编码
    ExtraType type = 3; //账号类型
  }
  message SubAccount{
    int64 id = 1; //子账户业务ID
    string code = 2; //子账户业务编码
    int64 level = 3; //子账户层级
    SubAccount sub_account = 4; //子子账户
  }
  int64 branch_id = 1; //分支ID,比如一个门店、配送中心
  int64 product_id = 2; //产品ID，对应到具体的一个sku
  Extra extra = 3; //扩展信息，用来指示具体的账号信息
  oneof locate {//定位子账户
    SubAccount sub_account = 4; //子账号
  };
}

//库存操作类型
enum AccoutingAction {
  WITHDRAW = 0; //增加库存
  DEPOSIT = 1; //减少库存
  FREEZE = 2; //冻结库存
}

//库存操作明细信息
message AccountingItem {
  Account account = 1; //账户
  AccoutingAction action = 2; //操作类型
  double amount = 3; //数量
  int64 unit = 4; //单位
  int32 numerator = 5; //分子
  int32 denominator = 6; //分母
  google.protobuf.Timestamp expired_time = 7; //冻结库存结束时间
}
//调拨记录
message TransferItem {
  Account from = 1; //发起方
  Account to = 2; //接收方
  double amount = 3; //数量
  int64 unit = 4; //单位
  int32 numerator = 5; //分子
  int32 denominator = 6; //分母
  bool broker = 7; //是否支持中间状态，默认false,表示实时的库存转移，如果为true，表示库存的转移是需要一笔确认的
}

//库存快照明细
message SnapshotItem {
  Account account = 1; //账户
  string code = 2; //快照编码
  string source = 3; //快照批次号，可以不填写，默认为批次的Batch_NO
  google.protobuf.Timestamp end_time = 4; //快照结束时间，如果没有制定，则为当前时间
  google.protobuf.Timestamp start_time = 5; //快照开始时间，补切时会用到
}

//库存盘点明细
message StocktakeItem {
  Account account = 1; //账户
  double amount = 2; //数量
  int64 stocktake_id = 3; //盘点单号
  string batch_no = 4; //重盘时的批次号
}

//库存批处理请求
message BatchRequest {
  //批处理操作业务
  enum Action {
    UNSET = 0; //默认值,未设置
    WITHDRAW = 1; //减少库存
    DEPOSIT = 2; //增加库存
    TRANSFER = 3; //两个库存账户之间的转账
    TRANSFER_INIT = 4; //准备转账
    TRANSFER_CANCEL = 5; //取消转账
    TRANSFER_COMMIT = 6; //确认转账
    FREEZE = 7; //冻结
    UNFREEZE = 8; //解冻
    SNAPSHOT = 9; //快照
    ACCOUNT_SUB = 10; //创建子账号
    ACCOUNT_SUSPEND = 11; //账号锁定
    ACCOUNT_CANCEL = 12; //账号注销
    STOCKTAKE = 13; //库存盘点
    MIXED = 100; //混合操作，允许Detail提交不同的操作明细
  }
  int64 id = 1; //批次流水号，发起请求是不用传，会自动生成
  string batch_no = 2; //业务流水号,必须唯一，用于做幂等性检查
  string code = 3; //业务编码，表示各个具体的业务
  Action action = 4; //业务操作，如果detail里没有设置，将会自动自动从这里继承
  string description = 5; //业务批处理详细说明
  repeated BatchDetailItem detail = 6; //详细业务数据
  string trace_id = 7; //本次业务跟踪ID
  string pre_trace_id = 8; //上个流程业务跟踪ID
  bool auto_unfreeze = 9; //发货后解冻所有未解冻的商品
  bool replay = 10; //执行重算逻辑
}

//批处理明细项
message BatchDetailItem {
  enum Action {
    UNSET = 0; //默认值,未设置
    WITHDRAW = 1; //减少库存
    DEPOSIT = 2; //增加库存
    TRANSFER = 3; //两个库存账户之间的转账
    TRANSFER_INIT = 4; //准备转账
    TRANSFER_CANCEL = 5; //取消转账
    TRANSFER_COMMIT = 6; //确认转账
    FREEZE = 7; //冻结
    UNFREEZE = 8; //解冻
    SNAPSHOT = 9;//快照
    ACCOUNT_SUB = 10; //创建子账号
    ACCOUNT_SUSPEND = 11; //账号锁定
    ACCOUNT_CANCEL = 12; //账号注销
    STOCKTAKE = 13; //库存盘点
  }
  //批次明细状态
  enum Status {
    INIT = 0; //初始化
    PROCESSING = 1; //处理中
    SUCCESS = 2; //成功
    FAIL = 3; //失败
  }
  int32 sequence_id = 1; //序号，数组从0开始
  string checksum = 2; //哈希检查
  Action action = 3; //业务处理类型
  Status status = 4; //明细状态
  google.protobuf.Timestamp business_time = 5; //业务时间，原则上不传，如果传了，大多为业务补偿而实现的
  //数据，其中一种类型
  oneof data {
    AccountingItem accounting = 9; //库存操作
    TransferItem transfer = 10; //转移操作
    SnapshotItem snapshot = 11; //快照操作
    StocktakeItem stocktake = 12; //盘点操作
  };

}

//批次状态
enum BatchStatus {
  INIT = 0; //初始化
  PROCESSING = 1; //处理中
  SUCCESS = 2; //成功
  COMPLETE = 4; //已完成
  HASH_CHECK_ERROR = 5; //哈希检查错误
}
//批处理结果响应
message BatchResponse {
  bool success = 1; //是否处理成功
  int64 id = 2; //批次处理ID
  string batch_no = 3; //客户端上传的批次编号
  string status = 4; //状态码
  string description = 5; //详细说明
}

//批处理查询
message BatchQuery {
  message BatchBase {
    string batch_no = 2; //批次编号，幂等性检查的依据
    string code = 3; //业务编码
    string action = 4; //业务操作，如果detail里没有设置，将会自动自动从这里继承
  }
  oneof query {
    int64 id = 1; //批次编号
    BatchBase batch = 2; //批次详细请求
  }
  bool detail = 3;
}

//库存查询请求
message InventoryQueryRequest {
  oneof query {
    BranchIDs branch_ids = 1;
    BatchOption batch = 2;
  }

  Pagination pagination = 3;
  StockOption options = 4;
  repeated int64 product_ids = 5;
  repeated OrderBy orderby_col = 6;//order by字段
  map<string, string> extra = 7;
  repeated int64 sub_account_ids = 8;//查询子账户ID
  // 多门店查询
  message BranchIDs{
    repeated int64 branch_id = 1;
  }
}
//排序
message OrderBy{
  Relation relation = 1; //降序或升序
  string col_name = 2; //列名
  enum Relation{
    ASC = 0;
    DESC = 1;
  }
}
// 批量查询单Branch、单Product库存
message InventoryQueryLsitRequest{
  repeated Account accounts = 1;
}

//库存查询请求结果
message InventoryQueryResponse {
  int32 total = 1;
  repeated StockInfo rows = 2;
  //扩展信息
  map<string, string> extra = 3;
}

//SKU库存查询请求
message SkuAccountRequest {
  int64 branch_id = 1;
  int64 product_id = 2;
}
//库存账号结果
message SkuAccountResponse {
  int64 id = 1;
  int64 partner_id = 2;
  int64 branch_id = 3;
  int64 product_id = 4;
  string status = 5;
  google.protobuf.Timestamp created = 6;
  int64 created_by = 7;
  google.protobuf.Timestamp updated = 8;
  int64 updated_by = 9;
}

//批次查询明细响应，用于返回详细的批次处理结果
message BatchQueryResponse {
  int64 id = 1; //批次流水号，发起请求是不用传，会自动生成
  string batch_no = 2; //业务流水号,必须唯一，用于做幂等性检查
  string code = 3; //业务编码
  string action = 4; //业务操作，如果detail里没有设置，将会自动自动从这里继承
  string description = 5; //业务批处理详细说明
  string status = 6; //批次状态
  repeated BatchDetailItem detail = 7; //明细数据

}

//库存查询选项
message StockOption {
  bool aggregate = 1; //是否聚合所有库存，包括子库存、冻结库存、中间库存等
  bool detail = 2; //是否包括相关库存的明细
  bool snapshot = 3; //是否包括快照
}

//完整的库存信息
message StockInfo {
  //库存聚合数据
  message Aggregate {
    double total = 1; //总库存
    double sub_account = 2; //子库存
    double freeze = 3; //冻结库存
    double broker = 4; //中间库存
  }
  //快照基本信息
  message Snapshot {
    int64 id = 1;
    double amount = 2;
    int64 ref = 3;
  }
  message Detail {
    repeated StockInfo child = 1;//子账户信息
    repeated FreezeInfo freeze = 2;
    repeated StockInfo broker = 3;
  }
  int64 id = 1; //库存编号
  Account account = 2; //账号信息

  Amount amount = 3;
  Aggregate aggregate = 4; //聚合信息
  Snapshot snapshot = 5; //快照信息
  Detail detail = 6; //子账号明细
}
//库存查询
message StockQueryRequest {
  repeated Account account = 1; //需要查询的账户
  StockOption options = 2; //库存查询选项
}


//库存查询反馈
message StockQueryResponse {
  bool success = 1; //查询是否成功
  repeated StockInfo data = 2; //库存明细信息列表
  string message = 3; //处理消息
}

/*
Snapshot BEGIN
*/
//库存快照
message Snapshot {
  message StatItem {
    string code = 1;
    string action = 2;
    Amount amount = 3;
    string account_type = 4; //账户类型
  }
  message Previous {
    int64 id = 1;
    Amount amount = 2;
  }
  //快照编号
  int64 id = 1;
  //SKU账号
  Account account = 2;
  //库存数量
  Amount amount = 3;
  //上一次库存快照信息
  Previous previous = 4;
  //快照类型
  string code = 5;
  //快照请求来源
  string source = 6;
  bool empty = 7;
  //第一条流水
  int64 first_id = 8;
  //最后一条流水
  int64 last_id = 9;
  //快照第一个数据起始时间
  google.protobuf.Timestamp start = 10;
  //快照最后一个数据结束时间
  google.protobuf.Timestamp end = 11;
  //快照时间
  google.protobuf.Timestamp created = 12;
  string status_account = 13;//在途、冻结等状态账户切片，json格式数据
  //本次快照的库存统计
  repeated StatItem stats = 20;
}

//库存快照批次查询请求
message SnapshotBatchQueryRequest{
  //BranchId 如果只有这个参数，直接取门店的所有商品
  int64 branch_id = 1;
  //指定库存列表
  repeated Account account = 2;
  //快照编码
  string code = 3;
  //开始时间
  google.protobuf.Timestamp from = 4;
  //结束时间
  google.protobuf.Timestamp to = 5;
}

//库存快照查询结果
message SnapshotBatchQueryResponse{
  //条数
  int32 total = 1;
  //详细库存快照记录
  repeated Snapshot rows = 2;
}

//库存快照查询结果
message SumListQueryResponse{
  //条数
  int32 total = 1;
  //详细库存快照记录
  repeated SnapshotSum rows = 2;
}

/*
Snapshot BEGIN
*/
//库存快照
message SnapshotSum {
  message StatItem {
    string code = 1;
    string action = 2;
    Amount amount = 3;
    string account_type = 4; //账户类型
  }

  //    message Previous {
  //      int64 id = 1;
  //      Amount amount = 2;
  //    }
  //快照编号
  int64 id = 1;
  //SKU账号
  Account account = 2;
  //库存数量
  Amount start_amount = 3;
  //库存数量
  Amount end_amount = 4;
  //上一次库存快照信息
  //  Previous previous = 4;
  //快照类型
  string code = 5;
  repeated StatItem stats = 6;
  //快照请求来源
  //  string source = 6;
  //  bool empty = 7;
  //第一条流水
  //  int64 first_id = 8;
  //最后一条流水
  //  int64 last_id = 9;
  //快照第一个数据起始时间
  //  google.protobuf.Timestamp start = 10;
  //快照最后一个数据结束时间
  //  google.protobuf.Timestamp end = 11;
  //快照时间
  //  google.protobuf.Timestamp created = 12;
  //  string status_account = 13;//在途、冻结等状态账户切片，json格式数据
  //本次快照的库存统计

}

//快照查询请求
message SnapshotQueryRequest {
  int64 sku_id = 1;
  Account account = 2;
  bool detail = 3;
  int32 limit = 4;
  google.protobuf.Timestamp start = 5;
  google.protobuf.Timestamp end = 6;
  int64 branch_id = 7;
}
message SnapshotQueryResponse {
  int32 total = 1;
  repeated Snapshot rows = 2;
}
message SnapshotStatItem {
  string code = 1;
  string action = 2;
  double qty = 3;
  int64 branch_id = 4;
  int64 product_id = 5;
  string account_type = 6;
}


message SnapshotStatRequest{
  int64 branch_id = 1;
  int64 product_id = 2;
  google.protobuf.Timestamp start = 3;
  google.protobuf.Timestamp end = 4;
  string code = 5;
  string action = 6;
}

message SnapshotStatGroupRequest{
  int64 branch_id = 1;
  int64 product_id = 2;
  google.protobuf.Timestamp start = 3;
  google.protobuf.Timestamp end = 4;
}


message SnapshotStatGroupResponse{
  int32 total = 1;
  repeated SnapshotStatGroupItem snapshot_stat = 2;
}

message SnapshotStatGroupItem {
  int64 branch_id = 4;
  int64 product_id = 5;
}

message SnapshotStatResponse{
  int32 total = 1;
  repeated SnapshotStatItem snapshot_stat = 2;
}

message ListGroupQueryRequest{
  int64 group_id = 1; //业务群组编号
  int64 product_id = 2; //商品编号
  google.protobuf.Timestamp start = 3;
  google.protobuf.Timestamp end = 4;
  int64 limit = 5;
  int64 offset = 6;
  map<string, string> extra = 10;
}

/*
Snapshot END
*/

/*
Hooks Begin
*/

message HooksCreateRequest {

  string name = 1;
  string description = 2;
  string code = 3;
  string action = 4;
}
message HooksCreateResponse {
  int64 id = 1;
  string name = 2;
  string description = 3;
  string code = 4;
  string action = 5;
  google.protobuf.Timestamp created = 6;
}
message HooksRequest {
  bool all = 1;
}
message HooksResponse {
  message Config {
    string name = 1;
    string description = 2;
    string code = 3;
    string action = 4;
    bool enable = 5;
  }
  int64 partner_id = 1;
  repeated Config hooks = 2;
}
/*
Hooks End
*/

/*
Inventory Log Begin
*/

//库存流水日志
message InventoryLog{
  int64 id = 1;
  int64 stock_id = 2;
  string code = 3;
  string action = 4;

  google.protobuf.Timestamp business_time = 5;
  string status = 6;
  double qty = 7;
  int32 numerator = 8; //分子
  int32 denominator = 9; //分母
  string source = 10;
  int64 created_by = 11;
  google.protobuf.Timestamp created = 12;

  string batch_no = 13;

  int64 partner_id = 14;
  int64 branch_id = 15;
  int64 product_id = 16;
  string trace_id = 17;
  string account_type = 18;
  int64 sub_account_id = 19;
}


message   SumListQueryRequest{
  repeated int64 branch_id = 1;
  google.protobuf.Timestamp start = 2;
  google.protobuf.Timestamp end = 3;
  //  bool filterNull = 4;
  int64 limit = 4;
  int64 offset = 5;
  //  repeated int64 stock_id = 7;
  repeated int64  product_id = 6;
  string  if_end = 7;  //zero 等于0 negative 小于0 positive 大于0
  string  if_pre = 8;  //zero 等于0 negative 小于0 positive 大于0
}


message    QuerySnapshotStatRequest{
  repeated int64 branch_id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
}

message  QuerySnapshotStatResponse{
  //条数
  int32 total = 1;
  //详细
  repeated QuerySnapshotStat rows = 2;
}

message  QuerySnapshotStat{
  int64 ProductId = 1;
}

message ListQueryRequest {
  repeated int64 branch_id = 1;
  repeated Account account = 2;
  repeated int64 stock_id = 3;
  string code = 4;
  string action = 5;
  google.protobuf.Timestamp start = 6;
  google.protobuf.Timestamp end = 7;
  int64 limit = 8;
  int64 offset = 9;
  map<string, string> extra = 10;
  repeated int64 sub_account_ids = 11;//根据子账户编号查询,-1获取所有的子账户
}

message ListItemLogQueryResponse{
  Account account = 1;
  InventoryLog data = 2;
}
message ListLogQueryResponse {
  ListQueryRequest query = 1;
  repeated ListItemLogQueryResponse data = 2;
  int64 total = 3;
}
//库存流水请求
message LogQueryRequest{
  Account account = 1;
  google.protobuf.Timestamp start = 2;
  google.protobuf.Timestamp end = 3;
}
//库存流水处理结果
message LogQueryResponse{
  Account account = 1;
  int32 total = 2;
  repeated InventoryLog detail = 3;
}
/*
Inventory Log END
*/


/*
Inventory Freeze Start
*/

message FreezeInfo{
  //明细状态
  enum Status {
    ACTIVE = 0; //有冻结库存存在
    FINISH = 1; //冻结已结清
  }
  int64 stock_id = 1; //库存商品编号
  Amount amount = 2; //冻结数据
  double init_amount = 3; //数量
  int64 init_unit = 4; //单位
  google.protobuf.Timestamp expired_time = 5; //冻结库存结束时间
  string code = 6; //业务编码，表示各个具体的业务
  string description = 7; //业务批处理详细说明
  string trace_id = 8; //业务跟踪ID
  Status status = 9; //冻结状态
}

//库存冻结请求
message FreezeRequest{
  string trace_id = 1; //业务跟踪ID
  string code = 2; //业务编码，表示各个具体的业务
  string description = 3; //业务批处理详细说明
  repeated FreezeItem detail = 4; //业务冻结处理详情
}

//库存冻结请求结果
message FreezeResponse{
  bool success = 1; //是否处理成功
  repeated FreezeItem detail = 2; //冻结明细
  string description = 3; //详细说明
}

// 库存冻结信息
message FreezeItem{
  //明细状态
  enum Status {
    SUCCESS = 0; //成功
    FAIL = 1; //失败
  }
  Account freeze = 1; //账户
  double init_amount = 2; //要冻结的数量
  double freezed_amount = 3; //已经冻结的数量
  double available_amount = 4; //可订货数量
  google.protobuf.Timestamp expired_time = 5; //冻结库存结束时间
  Status status = 6; // 冻结状态
  string description = 7; //详细说明
}

message FreezeQueryRequest{
  string trace_id = 1; //业务跟踪ID
  repeated Account account = 2; //需要查询的账户
}

/*
Inventory Freeze END
*/

/*
Group 群组，例如成本中心。 START
*/

message GroupRequest{
  enum Action{
    ADD = 0;
    UPDATE = 1;
    DELETE = 2;
  }
  message GroupItem{
    int64 group_id = 1; //业务群组编号
    int64 branch_id = 2;//门店编号
    string description = 3; //业务批处理详细说明
    Action action = 4;//操作
  }
  repeated GroupItem group_item = 1;
}

message GroupResponse{
  bool success = 1; //是否处理成功
}

message QueryGroupBranchRequest{
  int64 group_id = 1; //业务群组编号
  int64 branch_id = 2;//门店编号
}

message QueryGroupBranchResponse{
  message GroupItem{
    int64 group_id = 1; //业务群组编号
    int64 branch_id = 2;//门店编号
    string status = 3; //状态
    string description = 4; //业务批处理详细说明
  }
  repeated GroupItem group_item = 1;
  int64 total = 2;
}

message QueryGroupProductRequest{
  int64 group_id = 1; //业务群组编号
}

message QueryGroupProductResponse{
  message ProductInfo{
    int64 product_id = 1;//商品编号
    string status = 2;//状态
  }
  repeated ProductInfo product_info = 1;
  int64 total = 2;
}

/*
Group END
*/

/*
Stocktake 库存盘点 START
*/
message QueryStocktakeRequest{
  repeated int64 batch_nos = 1;//批次号
  int64 branch_id = 2;
  int64 product_id = 3;
  google.protobuf.Timestamp start = 4; //开始时间
  google.protobuf.Timestamp end = 5; //结束时间
}

message QueryStocktakeResponse{
  message StocktakeItem{
    int64 batch_no = 1; //批次号
    Account account = 2; //账户
    google.protobuf.Timestamp business_time = 3; //盘点时间
    double stocktake_qty = 4; //盘点数量
    double real_qty = 5; //实时库存数量
    double diff_qty = 6; //盘点差异数量
    int64 stocktake_id = 7; //盘点单号
    int64 sub_account_id = 8; //子账户ID
    string  Description = 9;
  }
  int64 total = 1;
  repeated StocktakeItem stocktake_item = 2;
}
/*
Stocktake 库存盘点 END
*/
message    QueryBranchInventorySortRequest{
  // 门店ID必传
  //@gotags: validate:"required"
  int64 branch_id = 1;
  repeated int64 product_ids =2;
  QueryType  query_type=3;
  PageOrder   page =4;
  UseFlag  use_flag=5;
}
enum  UseFlag{
  //  默认值 返回所有商品
  UseFlagInit=0;
    // 近期使用过的商品（库存大于0 或者1天内有流水）
  UseFlagRecent=1;
  // 未使用过的商品（库存小于等于0 且 1天内无流水）‘=
  UseFlagMiss=2;

}
enum QueryType{
  //  默认值 仅返回实时库存有记录的商品
  QueryTypeInit= 0;
  //  查询全部 返回全部商品
  QueryTypeAll =1 ;
  //  查询实时库存无记录商品
  QueryTypeMiss =2;
}
message PageOrder {
  string order = 1;
  string sort = 2;
  int32 limit = 3;
  int32 offset = 4;
}
message QueryBranchInventorySortResponse{
  message QueryBranchInventorySort{
    int64 product_id=1;
    double    qty=2;
    // "1" ：活跃（库存大于0 或者 当天（自然日）有变动），"0"： 库存小于等于0，且当日未变动
    string   flag=3;
  }
  int64 total = 1;
  repeated  QueryBranchInventorySort rows=2;
}