# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: inventory/inventory.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='inventory/inventory.proto',
  package='inventory',
  syntax='proto3',
  serialized_options=_b('Z\005proto'),
  serialized_pb=_b('\n\x19inventory/inventory.proto\x12\tinventory\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"#\n\x0fUnfreezeRequest\x12\x10\n\x08trace_id\x18\x01 \x01(\t\"\xae\x01\n\x18ListGroupSnapshotRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x03\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12.\n\nstart_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x84\x01\n\x19ListGroupSnapshotResponse\x12\x34\n\x07request\x18\x02 \x01(\x0b\x32#.inventory.ListGroupSnapshotRequest\x12\x31\n\x0fgroup_snapshots\x18\x03 \x03(\x0b\x32\x18.inventory.GroupSnapShot\"\xa5\x01\n\rGroupSnapShot\x12#\n\x07\x61\x63\x63ount\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12\x31\n\x05items\x18\x02 \x03(\x0b\x32\".inventory.GroupSnapShot.GroupItem\x1a<\n\tGroupItem\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12!\n\x06\x61mount\x18\x02 \x01(\x0b\x32\x11.inventory.Amount\"\xbf\x01\n\x18ListGroupEventLogRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x03\x12.\n\nstart_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\x03\x12\x0e\n\x06offset\x18\x06 \x01(\x03\"\x8e\x01\n\x19ListGroupEventLogResponse\x12\x34\n\x07request\x18\x01 \x01(\x0b\x32#.inventory.ListGroupEventLogRequest\x12\r\n\x05total\x18\x02 \x01(\x03\x12,\n\ngroup_logs\x18\x03 \x03(\x0b\x32\x18.inventory.GroupEventLog\"]\n\rGroupEventLog\x12\x12\n\nproduct_id\x18\x03 \x01(\x03\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\x12\x0b\n\x03qty\x18\x06 \x01(\x01\x12\r\n\x05\x63ount\x18\x07 \x01(\x03\"\xb3\x02\n\x17ListInventoryLogRequest\x12\x11\n\tbranch_id\x18\x02 \x01(\x03\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x03\x12\r\n\x05\x63odes\x18\x04 \x03(\t\x12\x0f\n\x07\x61\x63tions\x18\x05 \x03(\t\x12\r\n\x05limit\x18\x06 \x01(\x03\x12\x0e\n\x06offset\x18\x07 \x01(\x03\x12.\n\nstart_time\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbatch_nos\x18\n \x03(\x03\x12\x11\n\ttrace_ids\x18\x0b \x03(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x0c \x01(\t\x12\x17\n\x0fsub_account_ids\x18\r \x03(\x03\"\x99\x01\n\x18ListInventoryLogResponse\x12\x33\n\x07request\x18\x01 \x01(\x0b\x32\".inventory.ListInventoryLogRequest\x12%\n\x04logs\x18\x02 \x03(\x0b\x32\x17.inventory.InventoryLog\x12\r\n\x05total\x18\x04 \x01(\x03\x12\x12\n\namount_sum\x18\x05 \x01(\x01\"\xb6\x01\n\x06\x41mount\x12\x0b\n\x03qty\x18\x01 \x01(\x01\x12\r\n\x05round\x18\x02 \x01(\x01\x12+\n\x05\x65xtra\x18\x03 \x03(\x0b\x32\x1c.inventory.Amount.ExtraEntry\x12\x16\n\x0estatus_account\x18\x04 \x01(\t\x12\r\n\x05price\x18\x05 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x1a,\n\nExtraEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\"M\n\nPagination\x12\x0e\n\x06offset\x18\x01 \x01(\x05\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x11\n\tpage_size\x18\x03 \x01(\x05\x12\r\n\x05total\x18\x04 \x01(\x08\"9\n\x0b\x42\x61tchOption\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x10\n\x08\x62\x61tch_no\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\"\xaf\x03\n\x07\x41\x63\x63ount\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12\x12\n\nproduct_id\x18\x02 \x01(\x03\x12\'\n\x05\x65xtra\x18\x03 \x01(\x0b\x32\x18.inventory.Account.Extra\x12\x34\n\x0bsub_account\x18\x04 \x01(\x0b\x32\x1d.inventory.Account.SubAccountH\x00\x1aM\n\x05\x45xtra\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12*\n\x04type\x18\x03 \x01(\x0e\x32\x1c.inventory.Account.ExtraType\x1ai\n\nSubAccount\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\r\n\x05level\x18\x03 \x01(\x03\x12\x32\n\x0bsub_account\x18\x04 \x01(\x0b\x32\x1d.inventory.Account.SubAccount\"Z\n\tExtraType\x12\x07\n\x03SKU\x10\x00\x12\x0f\n\x0bSUB_ACCOUNT\x10\x01\x12\x0f\n\x0b\x46REEZE_CODE\x10\x02\x12\n\n\x06\x42ROKER\x10\x03\x12\x0b\n\x07TRANSER\x10\x04\x12\t\n\x05GROUP\x10\x05\x42\x08\n\x06locate\"\xd9\x01\n\x0e\x41\x63\x63ountingItem\x12#\n\x07\x61\x63\x63ount\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12*\n\x06\x61\x63tion\x18\x02 \x01(\x0e\x32\x1a.inventory.AccoutingAction\x12\x0e\n\x06\x61mount\x18\x03 \x01(\x01\x12\x0c\n\x04unit\x18\x04 \x01(\x03\x12\x11\n\tnumerator\x18\x05 \x01(\x05\x12\x13\n\x0b\x64\x65nominator\x18\x06 \x01(\x05\x12\x30\n\x0c\x65xpired_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xa6\x01\n\x0cTransferItem\x12 \n\x04\x66rom\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12\x1e\n\x02to\x18\x02 \x01(\x0b\x32\x12.inventory.Account\x12\x0e\n\x06\x61mount\x18\x03 \x01(\x01\x12\x0c\n\x04unit\x18\x04 \x01(\x03\x12\x11\n\tnumerator\x18\x05 \x01(\x05\x12\x13\n\x0b\x64\x65nominator\x18\x06 \x01(\x05\x12\x0e\n\x06\x62roker\x18\x07 \x01(\x08\"\xaf\x01\n\x0cSnapshotItem\x12#\n\x07\x61\x63\x63ount\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06source\x18\x03 \x01(\t\x12,\n\x08\x65nd_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nstart_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"l\n\rStocktakeItem\x12#\n\x07\x61\x63\x63ount\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12\x0e\n\x06\x61mount\x18\x02 \x01(\x01\x12\x14\n\x0cstocktake_id\x18\x03 \x01(\x03\x12\x10\n\x08\x62\x61tch_no\x18\x04 \x01(\t\"\xf2\x03\n\x0c\x42\x61tchRequest\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x10\n\x08\x62\x61tch_no\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12.\n\x06\x61\x63tion\x18\x04 \x01(\x0e\x32\x1e.inventory.BatchRequest.Action\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12*\n\x06\x64\x65tail\x18\x06 \x03(\x0b\x32\x1a.inventory.BatchDetailItem\x12\x10\n\x08trace_id\x18\x07 \x01(\t\x12\x14\n\x0cpre_trace_id\x18\x08 \x01(\t\x12\x15\n\rauto_unfreeze\x18\t \x01(\x08\x12\x0e\n\x06replay\x18\n \x01(\x08\"\xf5\x01\n\x06\x41\x63tion\x12\t\n\x05UNSET\x10\x00\x12\x0c\n\x08WITHDRAW\x10\x01\x12\x0b\n\x07\x44\x45POSIT\x10\x02\x12\x0c\n\x08TRANSFER\x10\x03\x12\x11\n\rTRANSFER_INIT\x10\x04\x12\x13\n\x0fTRANSFER_CANCEL\x10\x05\x12\x13\n\x0fTRANSFER_COMMIT\x10\x06\x12\n\n\x06\x46REEZE\x10\x07\x12\x0c\n\x08UNFREEZE\x10\x08\x12\x0c\n\x08SNAPSHOT\x10\t\x12\x0f\n\x0b\x41\x43\x43OUNT_SUB\x10\n\x12\x13\n\x0f\x41\x43\x43OUNT_SUSPEND\x10\x0b\x12\x12\n\x0e\x41\x43\x43OUNT_CANCEL\x10\x0c\x12\r\n\tSTOCKTAKE\x10\r\x12\t\n\x05MIXED\x10\x64\"\xbb\x05\n\x0f\x42\x61tchDetailItem\x12\x13\n\x0bsequence_id\x18\x01 \x01(\x05\x12\x10\n\x08\x63hecksum\x18\x02 \x01(\t\x12\x31\n\x06\x61\x63tion\x18\x03 \x01(\x0e\x32!.inventory.BatchDetailItem.Action\x12\x31\n\x06status\x18\x04 \x01(\x0e\x32!.inventory.BatchDetailItem.Status\x12\x31\n\rbusiness_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\naccounting\x18\t \x01(\x0b\x32\x19.inventory.AccountingItemH\x00\x12+\n\x08transfer\x18\n \x01(\x0b\x32\x17.inventory.TransferItemH\x00\x12+\n\x08snapshot\x18\x0b \x01(\x0b\x32\x17.inventory.SnapshotItemH\x00\x12-\n\tstocktake\x18\x0c \x01(\x0b\x32\x18.inventory.StocktakeItemH\x00\"\xea\x01\n\x06\x41\x63tion\x12\t\n\x05UNSET\x10\x00\x12\x0c\n\x08WITHDRAW\x10\x01\x12\x0b\n\x07\x44\x45POSIT\x10\x02\x12\x0c\n\x08TRANSFER\x10\x03\x12\x11\n\rTRANSFER_INIT\x10\x04\x12\x13\n\x0fTRANSFER_CANCEL\x10\x05\x12\x13\n\x0fTRANSFER_COMMIT\x10\x06\x12\n\n\x06\x46REEZE\x10\x07\x12\x0c\n\x08UNFREEZE\x10\x08\x12\x0c\n\x08SNAPSHOT\x10\t\x12\x0f\n\x0b\x41\x43\x43OUNT_SUB\x10\n\x12\x13\n\x0f\x41\x43\x43OUNT_SUSPEND\x10\x0b\x12\x12\n\x0e\x41\x43\x43OUNT_CANCEL\x10\x0c\x12\r\n\tSTOCKTAKE\x10\r\"9\n\x06Status\x12\x08\n\x04INIT\x10\x00\x12\x0e\n\nPROCESSING\x10\x01\x12\x0b\n\x07SUCCESS\x10\x02\x12\x08\n\x04\x46\x41IL\x10\x03\x42\x06\n\x04\x64\x61ta\"c\n\rBatchResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\n\n\x02id\x18\x02 \x01(\x03\x12\x10\n\x08\x62\x61tch_no\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\"\xa2\x01\n\nBatchQuery\x12\x0c\n\x02id\x18\x01 \x01(\x03H\x00\x12\x30\n\x05\x62\x61tch\x18\x02 \x01(\x0b\x32\x1f.inventory.BatchQuery.BatchBaseH\x00\x12\x0e\n\x06\x64\x65tail\x18\x03 \x01(\x08\x1a;\n\tBatchBase\x12\x10\n\x08\x62\x61tch_no\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\tB\x07\n\x05query\"\xc0\x03\n\x15InventoryQueryRequest\x12@\n\nbranch_ids\x18\x01 \x01(\x0b\x32*.inventory.InventoryQueryRequest.BranchIDsH\x00\x12\'\n\x05\x62\x61tch\x18\x02 \x01(\x0b\x32\x16.inventory.BatchOptionH\x00\x12)\n\npagination\x18\x03 \x01(\x0b\x32\x15.inventory.Pagination\x12\'\n\x07options\x18\x04 \x01(\x0b\x32\x16.inventory.StockOption\x12\x13\n\x0bproduct_ids\x18\x05 \x03(\x03\x12\'\n\x0borderby_col\x18\x06 \x03(\x0b\x32\x12.inventory.OrderBy\x12:\n\x05\x65xtra\x18\x07 \x03(\x0b\x32+.inventory.InventoryQueryRequest.ExtraEntry\x12\x17\n\x0fsub_account_ids\x18\x08 \x03(\x03\x1a,\n\nExtraEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x1e\n\tBranchIDs\x12\x11\n\tbranch_id\x18\x01 \x03(\x03\x42\x07\n\x05query\"i\n\x07OrderBy\x12-\n\x08relation\x18\x01 \x01(\x0e\x32\x1b.inventory.OrderBy.Relation\x12\x10\n\x08\x63ol_name\x18\x02 \x01(\t\"\x1d\n\x08Relation\x12\x07\n\x03\x41SC\x10\x00\x12\x08\n\x04\x44\x45SC\x10\x01\"A\n\x19InventoryQueryLsitRequest\x12$\n\x08\x61\x63\x63ounts\x18\x01 \x03(\x0b\x32\x12.inventory.Account\"\xb6\x01\n\x16InventoryQueryResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12\"\n\x04rows\x18\x02 \x03(\x0b\x32\x14.inventory.StockInfo\x12;\n\x05\x65xtra\x18\x03 \x03(\x0b\x32,.inventory.InventoryQueryResponse.ExtraEntry\x1a,\n\nExtraEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\":\n\x11SkuAccountRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12\x12\n\nproduct_id\x18\x02 \x01(\x03\"\xed\x01\n\x12SkuAccountResponse\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x12\n\npartner_id\x18\x02 \x01(\x03\x12\x11\n\tbranch_id\x18\x03 \x01(\x03\x12\x12\n\nproduct_id\x18\x04 \x01(\x03\x12\x0e\n\x06status\x18\x05 \x01(\t\x12+\n\x07\x63reated\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x07 \x01(\x03\x12+\n\x07updated\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\t \x01(\x03\"\xa1\x01\n\x12\x42\x61tchQueryResponse\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x10\n\x08\x62\x61tch_no\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\x0e\n\x06status\x18\x06 \x01(\t\x12*\n\x06\x64\x65tail\x18\x07 \x03(\x0b\x32\x1a.inventory.BatchDetailItem\"B\n\x0bStockOption\x12\x11\n\taggregate\x18\x01 \x01(\x08\x12\x0e\n\x06\x64\x65tail\x18\x02 \x01(\x08\x12\x10\n\x08snapshot\x18\x03 \x01(\x08\"\xf2\x03\n\tStockInfo\x12\n\n\x02id\x18\x01 \x01(\x03\x12#\n\x07\x61\x63\x63ount\x18\x02 \x01(\x0b\x32\x12.inventory.Account\x12!\n\x06\x61mount\x18\x03 \x01(\x0b\x32\x11.inventory.Amount\x12\x31\n\taggregate\x18\x04 \x01(\x0b\x32\x1e.inventory.StockInfo.Aggregate\x12/\n\x08snapshot\x18\x05 \x01(\x0b\x32\x1d.inventory.StockInfo.Snapshot\x12+\n\x06\x64\x65tail\x18\x06 \x01(\x0b\x32\x1b.inventory.StockInfo.Detail\x1aO\n\tAggregate\x12\r\n\x05total\x18\x01 \x01(\x01\x12\x13\n\x0bsub_account\x18\x02 \x01(\x01\x12\x0e\n\x06\x66reeze\x18\x03 \x01(\x01\x12\x0e\n\x06\x62roker\x18\x04 \x01(\x01\x1a\x33\n\x08Snapshot\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0e\n\x06\x61mount\x18\x02 \x01(\x01\x12\x0b\n\x03ref\x18\x03 \x01(\x03\x1az\n\x06\x44\x65tail\x12#\n\x05\x63hild\x18\x01 \x03(\x0b\x32\x14.inventory.StockInfo\x12%\n\x06\x66reeze\x18\x02 \x03(\x0b\x32\x15.inventory.FreezeInfo\x12$\n\x06\x62roker\x18\x03 \x03(\x0b\x32\x14.inventory.StockInfo\"a\n\x11StockQueryRequest\x12#\n\x07\x61\x63\x63ount\x18\x01 \x03(\x0b\x32\x12.inventory.Account\x12\'\n\x07options\x18\x02 \x01(\x0b\x32\x16.inventory.StockOption\"Z\n\x12StockQueryResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\"\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x14.inventory.StockInfo\x12\x0f\n\x07message\x18\x03 \x01(\t\"\xc2\x04\n\x08Snapshot\x12\n\n\x02id\x18\x01 \x01(\x03\x12#\n\x07\x61\x63\x63ount\x18\x02 \x01(\x0b\x32\x12.inventory.Account\x12!\n\x06\x61mount\x18\x03 \x01(\x0b\x32\x11.inventory.Amount\x12.\n\x08previous\x18\x04 \x01(\x0b\x32\x1c.inventory.Snapshot.Previous\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0e\n\x06source\x18\x06 \x01(\t\x12\r\n\x05\x65mpty\x18\x07 \x01(\x08\x12\x10\n\x08\x66irst_id\x18\x08 \x01(\x03\x12\x0f\n\x07last_id\x18\t \x01(\x03\x12)\n\x05start\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x63reated\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x16\n\x0estatus_account\x18\r \x01(\t\x12+\n\x05stats\x18\x14 \x03(\x0b\x32\x1c.inventory.Snapshot.StatItem\x1a\x61\n\x08StatItem\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12!\n\x06\x61mount\x18\x03 \x01(\x0b\x32\x11.inventory.Amount\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x04 \x01(\t\x1a\x39\n\x08Previous\x12\n\n\x02id\x18\x01 \x01(\x03\x12!\n\x06\x61mount\x18\x02 \x01(\x0b\x32\x11.inventory.Amount\"\xb3\x01\n\x19SnapshotBatchQueryRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12#\n\x07\x61\x63\x63ount\x18\x02 \x03(\x0b\x32\x12.inventory.Account\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12(\n\x04\x66rom\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12&\n\x02to\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"N\n\x1aSnapshotBatchQueryResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12!\n\x04rows\x18\x02 \x03(\x0b\x32\x13.inventory.Snapshot\"K\n\x14SumListQueryResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12$\n\x04rows\x18\x02 \x03(\x0b\x32\x16.inventory.SnapshotSum\"\xaf\x02\n\x0bSnapshotSum\x12\n\n\x02id\x18\x01 \x01(\x03\x12#\n\x07\x61\x63\x63ount\x18\x02 \x01(\x0b\x32\x12.inventory.Account\x12\'\n\x0cstart_amount\x18\x03 \x01(\x0b\x32\x11.inventory.Amount\x12%\n\nend_amount\x18\x04 \x01(\x0b\x32\x11.inventory.Amount\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12.\n\x05stats\x18\x06 \x03(\x0b\x32\x1f.inventory.SnapshotSum.StatItem\x1a\x61\n\x08StatItem\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12!\n\x06\x61mount\x18\x03 \x01(\x0b\x32\x11.inventory.Amount\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x04 \x01(\t\"\xd1\x01\n\x14SnapshotQueryRequest\x12\x0e\n\x06sku_id\x18\x01 \x01(\x03\x12#\n\x07\x61\x63\x63ount\x18\x02 \x01(\x0b\x32\x12.inventory.Account\x12\x0e\n\x06\x64\x65tail\x18\x03 \x01(\x08\x12\r\n\x05limit\x18\x04 \x01(\x05\x12)\n\x05start\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x07 \x01(\x03\"I\n\x15SnapshotQueryResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12!\n\x04rows\x18\x02 \x03(\x0b\x32\x13.inventory.Snapshot\"z\n\x10SnapshotStatItem\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x0b\n\x03qty\x18\x03 \x01(\x01\x12\x11\n\tbranch_id\x18\x04 \x01(\x03\x12\x12\n\nproduct_id\x18\x05 \x01(\x03\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x06 \x01(\t\"\xae\x01\n\x13SnapshotStatRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12\x12\n\nproduct_id\x18\x02 \x01(\x03\x12)\n\x05start\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x06 \x01(\t\"\x95\x01\n\x18SnapshotStatGroupRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12\x12\n\nproduct_id\x18\x02 \x01(\x03\x12)\n\x05start\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"c\n\x19SnapshotStatGroupResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12\x37\n\rsnapshot_stat\x18\x02 \x03(\x0b\x32 .inventory.SnapshotStatGroupItem\">\n\x15SnapshotStatGroupItem\x12\x11\n\tbranch_id\x18\x04 \x01(\x03\x12\x12\n\nproduct_id\x18\x05 \x01(\x03\"Y\n\x14SnapshotStatResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12\x32\n\rsnapshot_stat\x18\x02 \x03(\x0b\x32\x1b.inventory.SnapshotStatItem\"\x9a\x02\n\x15ListGroupQueryRequest\x12\x10\n\x08group_id\x18\x01 \x01(\x03\x12\x12\n\nproduct_id\x18\x02 \x01(\x03\x12)\n\x05start\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\x03\x12\x0e\n\x06offset\x18\x06 \x01(\x03\x12:\n\x05\x65xtra\x18\n \x03(\x0b\x32+.inventory.ListGroupQueryRequest.ExtraEntry\x1a,\n\nExtraEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"U\n\x12HooksCreateRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\"\x8f\x01\n\x13HooksCreateResponse\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\x12+\n\x07\x63reated\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x1b\n\x0cHooksRequest\x12\x0b\n\x03\x61ll\x18\x01 \x01(\x08\"\xae\x01\n\rHooksResponse\x12\x12\n\npartner_id\x18\x01 \x01(\x03\x12.\n\x05hooks\x18\x02 \x03(\x0b\x32\x1f.inventory.HooksResponse.Config\x1aY\n\x06\x43onfig\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12\x0e\n\x06\x65nable\x18\x05 \x01(\x08\"\xa0\x03\n\x0cInventoryLog\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x10\n\x08stock_id\x18\x02 \x01(\x03\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12\x31\n\rbusiness_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x06 \x01(\t\x12\x0b\n\x03qty\x18\x07 \x01(\x01\x12\x11\n\tnumerator\x18\x08 \x01(\x05\x12\x13\n\x0b\x64\x65nominator\x18\t \x01(\x05\x12\x0e\n\x06source\x18\n \x01(\t\x12\x12\n\ncreated_by\x18\x0b \x01(\x03\x12+\n\x07\x63reated\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08\x62\x61tch_no\x18\r \x01(\t\x12\x12\n\npartner_id\x18\x0e \x01(\x03\x12\x11\n\tbranch_id\x18\x0f \x01(\x03\x12\x12\n\nproduct_id\x18\x10 \x01(\x03\x12\x10\n\x08trace_id\x18\x11 \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x12 \x01(\t\x12\x16\n\x0esub_account_id\x18\x13 \x01(\x03\"\xcf\x01\n\x13SumListQueryRequest\x12\x11\n\tbranch_id\x18\x01 \x03(\x03\x12)\n\x05start\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x04 \x01(\x03\x12\x0e\n\x06offset\x18\x05 \x01(\x03\x12\x12\n\nproduct_id\x18\x06 \x03(\x03\x12\x0e\n\x06if_end\x18\x07 \x01(\t\x12\x0e\n\x06if_pre\x18\x08 \x01(\t\"\x8b\x01\n\x18QuerySnapshotStatRequest\x12\x11\n\tbranch_id\x18\x01 \x03(\x03\x12.\n\nstart_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"V\n\x19QuerySnapshotStatResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12*\n\x04rows\x18\x02 \x03(\x0b\x32\x1c.inventory.QuerySnapshotStat\"&\n\x11QuerySnapshotStat\x12\x11\n\tProductId\x18\x01 \x01(\x03\"\xeb\x02\n\x10ListQueryRequest\x12\x11\n\tbranch_id\x18\x01 \x03(\x03\x12#\n\x07\x61\x63\x63ount\x18\x02 \x03(\x0b\x32\x12.inventory.Account\x12\x10\n\x08stock_id\x18\x03 \x03(\x03\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\x12)\n\x05start\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x08 \x01(\x03\x12\x0e\n\x06offset\x18\t \x01(\x03\x12\x35\n\x05\x65xtra\x18\n \x03(\x0b\x32&.inventory.ListQueryRequest.ExtraEntry\x12\x17\n\x0fsub_account_ids\x18\x0b \x03(\x03\x1a,\n\nExtraEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"f\n\x18ListItemLogQueryResponse\x12#\n\x07\x61\x63\x63ount\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12%\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x17.inventory.InventoryLog\"\x84\x01\n\x14ListLogQueryResponse\x12*\n\x05query\x18\x01 \x01(\x0b\x32\x1b.inventory.ListQueryRequest\x12\x31\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32#.inventory.ListItemLogQueryResponse\x12\r\n\x05total\x18\x03 \x01(\x03\"\x8a\x01\n\x0fLogQueryRequest\x12#\n\x07\x61\x63\x63ount\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12)\n\x05start\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"o\n\x10LogQueryResponse\x12#\n\x07\x61\x63\x63ount\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12\r\n\x05total\x18\x02 \x01(\x05\x12\'\n\x06\x64\x65tail\x18\x03 \x03(\x0b\x32\x17.inventory.InventoryLog\"\xa0\x02\n\nFreezeInfo\x12\x10\n\x08stock_id\x18\x01 \x01(\x03\x12!\n\x06\x61mount\x18\x02 \x01(\x0b\x32\x11.inventory.Amount\x12\x13\n\x0binit_amount\x18\x03 \x01(\x01\x12\x11\n\tinit_unit\x18\x04 \x01(\x03\x12\x30\n\x0c\x65xpired_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\x12\x10\n\x08trace_id\x18\x08 \x01(\t\x12,\n\x06status\x18\t \x01(\x0e\x32\x1c.inventory.FreezeInfo.Status\" \n\x06Status\x12\n\n\x06\x41\x43TIVE\x10\x00\x12\n\n\x06\x46INISH\x10\x01\"k\n\rFreezeRequest\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12%\n\x06\x64\x65tail\x18\x04 \x03(\x0b\x32\x15.inventory.FreezeItem\"]\n\x0e\x46reezeResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12%\n\x06\x64\x65tail\x18\x02 \x03(\x0b\x32\x15.inventory.FreezeItem\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\"\x8d\x02\n\nFreezeItem\x12\"\n\x06\x66reeze\x18\x01 \x01(\x0b\x32\x12.inventory.Account\x12\x13\n\x0binit_amount\x18\x02 \x01(\x01\x12\x16\n\x0e\x66reezed_amount\x18\x03 \x01(\x01\x12\x18\n\x10\x61vailable_amount\x18\x04 \x01(\x01\x12\x30\n\x0c\x65xpired_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x06status\x18\x06 \x01(\x0e\x32\x1c.inventory.FreezeItem.Status\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\"\x1f\n\x06Status\x12\x0b\n\x07SUCCESS\x10\x00\x12\x08\n\x04\x46\x41IL\x10\x01\"K\n\x12\x46reezeQueryRequest\x12\x10\n\x08trace_id\x18\x01 \x01(\t\x12#\n\x07\x61\x63\x63ount\x18\x02 \x03(\x0b\x32\x12.inventory.Account\"\xe7\x01\n\x0cGroupRequest\x12\x35\n\ngroup_item\x18\x01 \x03(\x0b\x32!.inventory.GroupRequest.GroupItem\x1au\n\tGroupItem\x12\x10\n\x08group_id\x18\x01 \x01(\x03\x12\x11\n\tbranch_id\x18\x02 \x01(\x03\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12.\n\x06\x61\x63tion\x18\x04 \x01(\x0e\x32\x1e.inventory.GroupRequest.Action\")\n\x06\x41\x63tion\x12\x07\n\x03\x41\x44\x44\x10\x00\x12\n\n\x06UPDATE\x10\x01\x12\n\n\x06\x44\x45LETE\x10\x02\" \n\rGroupResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\">\n\x17QueryGroupBranchRequest\x12\x10\n\x08group_id\x18\x01 \x01(\x03\x12\x11\n\tbranch_id\x18\x02 \x01(\x03\"\xc3\x01\n\x18QueryGroupBranchResponse\x12\x41\n\ngroup_item\x18\x01 \x03(\x0b\x32-.inventory.QueryGroupBranchResponse.GroupItem\x12\r\n\x05total\x18\x02 \x01(\x03\x1aU\n\tGroupItem\x12\x10\n\x08group_id\x18\x01 \x01(\x03\x12\x11\n\tbranch_id\x18\x02 \x01(\x03\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\",\n\x18QueryGroupProductRequest\x12\x10\n\x08group_id\x18\x01 \x01(\x03\"\xa5\x01\n\x19QueryGroupProductResponse\x12\x46\n\x0cproduct_info\x18\x01 \x03(\x0b\x32\x30.inventory.QueryGroupProductResponse.ProductInfo\x12\r\n\x05total\x18\x02 \x01(\x03\x1a\x31\n\x0bProductInfo\x12\x12\n\nproduct_id\x18\x01 \x01(\x03\x12\x0e\n\x06status\x18\x02 \x01(\t\"\xa5\x01\n\x15QueryStocktakeRequest\x12\x11\n\tbatch_nos\x18\x01 \x03(\x03\x12\x11\n\tbranch_id\x18\x02 \x01(\x03\x12\x12\n\nproduct_id\x18\x03 \x01(\x03\x12)\n\x05start\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xea\x02\n\x16QueryStocktakeResponse\x12\r\n\x05total\x18\x01 \x01(\x03\x12G\n\x0estocktake_item\x18\x02 \x03(\x0b\x32/.inventory.QueryStocktakeResponse.StocktakeItem\x1a\xf7\x01\n\rStocktakeItem\x12\x10\n\x08\x62\x61tch_no\x18\x01 \x01(\x03\x12#\n\x07\x61\x63\x63ount\x18\x02 \x01(\x0b\x32\x12.inventory.Account\x12\x31\n\rbusiness_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rstocktake_qty\x18\x04 \x01(\x01\x12\x10\n\x08real_qty\x18\x05 \x01(\x01\x12\x10\n\x08\x64iff_qty\x18\x06 \x01(\x01\x12\x14\n\x0cstocktake_id\x18\x07 \x01(\x03\x12\x16\n\x0esub_account_id\x18\x08 \x01(\x03\x12\x13\n\x0b\x44\x65scription\x18\t \x01(\t\"\xbd\x01\n\x1fQueryBranchInventorySortRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x03\x12(\n\nquery_type\x18\x03 \x01(\x0e\x32\x14.inventory.QueryType\x12\"\n\x04page\x18\x04 \x01(\x0b\x32\x14.inventory.PageOrder\x12$\n\x08use_flag\x18\x05 \x01(\x0e\x32\x12.inventory.UseFlag\"G\n\tPageOrder\x12\r\n\x05order\x18\x01 \x01(\t\x12\x0c\n\x04sort\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\"\xd0\x01\n QueryBranchInventorySortResponse\x12\r\n\x05total\x18\x01 \x01(\x03\x12R\n\x04rows\x18\x02 \x03(\x0b\x32\x44.inventory.QueryBranchInventorySortResponse.QueryBranchInventorySort\x1aI\n\x18QueryBranchInventorySort\x12\x12\n\nproduct_id\x18\x01 \x01(\x03\x12\x0b\n\x03qty\x18\x02 \x01(\x01\x12\x0c\n\x04\x66lag\x18\x03 \x01(\t*8\n\x0f\x41\x63\x63outingAction\x12\x0c\n\x08WITHDRAW\x10\x00\x12\x0b\n\x07\x44\x45POSIT\x10\x01\x12\n\n\x06\x46REEZE\x10\x02*X\n\x0b\x42\x61tchStatus\x12\x08\n\x04INIT\x10\x00\x12\x0e\n\nPROCESSING\x10\x01\x12\x0b\n\x07SUCCESS\x10\x02\x12\x0c\n\x08\x43OMPLETE\x10\x04\x12\x14\n\x10HASH_CHECK_ERROR\x10\x05*>\n\x07UseFlag\x12\x0f\n\x0bUseFlagInit\x10\x00\x12\x11\n\rUseFlagRecent\x10\x01\x12\x0f\n\x0bUseFlagMiss\x10\x02*C\n\tQueryType\x12\x11\n\rQueryTypeInit\x10\x00\x12\x10\n\x0cQueryTypeAll\x10\x01\x12\x11\n\rQueryTypeMiss\x10\x02\x32\x93 \n\x10InventoryService\x12\x65\n\x07\x41\x63\x63ount\x12\x17.inventory.BatchRequest\x1a\x18.inventory.BatchResponse\"\'\x82\xd3\xe4\x93\x02!\"\x1c/api/v1/inventory/accounting:\x01*\x12\x64\n\x08Transfer\x12\x17.inventory.BatchRequest\x1a\x18.inventory.BatchResponse\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v1/inventory/transfer:\x01*\x12m\n\x0eTransferCommit\x12\x15.inventory.BatchQuery\x1a\x18.inventory.BatchResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v1/inventory/transfer/{id}:\x01*\x12^\n\x05\x42\x61tch\x12\x17.inventory.BatchRequest\x1a\x18.inventory.BatchResponse\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v1/inventory/batch:\x01*\x12q\n\x0eQueryAccouting\x12\x15.inventory.BatchQuery\x1a\x1d.inventory.BatchQueryResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v1/inventory/accounting/{id}\x12n\n\rQueryTransfer\x12\x15.inventory.BatchQuery\x1a\x1d.inventory.BatchQueryResponse\"\'\x82\xd3\xe4\x93\x02!\x12\x1f/api/v1/inventory/transfer/{id}\x12h\n\nQueryBatch\x12\x15.inventory.BatchQuery\x1a\x1d.inventory.BatchQueryResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v1/inventory/batch/{id}\x12v\n\x13QueryBatchParameter\x12\x15.inventory.BatchQuery\x1a\x1d.inventory.BatchQueryResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v1/inventory/batch/parameter\x12r\n\x0fQueryStockBatch\x12\x1c.inventory.StockQueryRequest\x1a\x1d.inventory.StockQueryResponse\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v1/inventory/stock:\x01*\x12u\n\x10\x45nsureSkuAccount\x12\x1c.inventory.SkuAccountRequest\x1a\x1d.inventory.SkuAccountResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v1/inventory/sku/ensure\x12\x80\x01\n\x14QueryBranchInventory\x12 .inventory.InventoryQueryRequest\x1a!.inventory.InventoryQueryResponse\"#\x82\xd3\xe4\x93\x02\x1d\"\x18/api/v1/inventory/branch:\x01*\x12\x8b\x01\n\x19QueryAccountInventoryList\x12$.inventory.InventoryQueryLsitRequest\x1a!.inventory.InventoryQueryResponse\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v1/inventory/accounts:\x01*\x12\x84\x01\n\x1bQueryLatestInventoryByBatch\x12\x15.inventory.BatchQuery\x1a!.inventory.InventoryQueryResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v1/inventory/batch/{id}/latest\x12`\n\nQueryHooks\x12\x17.inventory.HooksRequest\x1a\x18.inventory.HooksResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/api/v1/inventory/hooks\x12o\n\nCreateHook\x12\x1d.inventory.HooksCreateRequest\x1a\x1e.inventory.HooksCreateResponse\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v1/inventory/hooks:\x01*\x12\x62\n\x06\x46reeze\x12\x18.inventory.FreezeRequest\x1a\x19.inventory.FreezeResponse\"#\x82\xd3\xe4\x93\x02\x1d\"\x18/api/v1/inventory/freeze:\x01*\x12h\n\x08Unfreeze\x12\x1a.inventory.UnfreezeRequest\x1a\x19.inventory.FreezeResponse\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v1/inventory/unfreeze:\x01*\x12v\n\x0fQueryFreezeList\x12\x1d.inventory.FreezeQueryRequest\x1a\x19.inventory.FreezeResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v1/inventory/freeze/query:\x01*\x12t\n\x0cQueryLogList\x12\x1b.inventory.ListQueryRequest\x1a\x1f.inventory.ListLogQueryResponse\"&\x82\xd3\xe4\x93\x02 \"\x1b/api/v1/inventory/log/query:\x01*\x12\x80\x01\n\rQuerySnapshot\x12\x1f.inventory.SnapshotQueryRequest\x1a .inventory.SnapshotQueryResponse\",\x82\xd3\xe4\x93\x02&\"!/api/v1/inventory/snapshot/single:\x01*\x12\x84\x01\n\x11QuerySnapshotList\x12\x1b.inventory.ListQueryRequest\x1a%.inventory.SnapshotBatchQueryResponse\"+\x82\xd3\xe4\x93\x02%\" /api/v1/inventory/snapshot/query:\x01*\x12\x88\x01\n\x14QuerySnapshotSumList\x12\x1e.inventory.SumListQueryRequest\x1a\x1f.inventory.SumListQueryResponse\"/\x82\xd3\xe4\x93\x02)\"$/api/v1/inventory/snapshot/query/sum:\x01*\x12\x93\x01\n\x16QueryGroupSnapshotList\x12 .inventory.ListGroupQueryRequest\x1a%.inventory.SnapshotBatchQueryResponse\"0\x82\xd3\xe4\x93\x02*\"%/api/v1/inventory/snapshot/querygroup:\x01*\x12\x87\x01\n\x12QuerySnapshotStats\x12\x1e.inventory.SnapshotStatRequest\x1a\x1f.inventory.SnapshotStatResponse\"0\x82\xd3\xe4\x93\x02*\"%/api/v1/inventory/snapshot/querystats:\x01*\x12\x8f\x01\n\x16QuerySnapshotStatGroup\x12#.inventory.SnapshotStatGroupRequest\x1a$.inventory.SnapshotStatGroupResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v1/inventory/snapshot/stat:\x01*\x12\x82\x01\n\x10ListInventoryLog\x12\".inventory.ListInventoryLogRequest\x1a#.inventory.ListInventoryLogResponse\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v1/inventory/log/list:\x01*\x12\x86\x01\n\x11ListGroupEventLog\x12#.inventory.ListGroupEventLogRequest\x1a$.inventory.ListGroupEventLogResponse\"&\x82\xd3\xe4\x93\x02 \"\x1b/api/v1/inventory/log/event:\x01*\x12\x8b\x01\n\x11ListGroupSnapshot\x12#.inventory.ListGroupSnapshotRequest\x1a$.inventory.ListGroupSnapshotResponse\"+\x82\xd3\xe4\x93\x02%\" /api/v1/inventory/snapshot/group:\x01*\x12^\n\x05Group\x12\x17.inventory.GroupRequest\x1a\x18.inventory.GroupResponse\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v1/inventory/group:\x01*\x12\x85\x01\n\x10QueryGroupBranch\x12\".inventory.QueryGroupBranchRequest\x1a#.inventory.QueryGroupBranchResponse\"(\x82\xd3\xe4\x93\x02\"\"\x1d/api/v1/inventory/group/query:\x01*\x12\x8a\x01\n\x11QueryGroupProduct\x12#.inventory.QueryGroupProductRequest\x1a$.inventory.QueryGroupProductResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v1/inventory/group/product:\x01*\x12\x83\x01\n\x0eQueryStocktake\x12 .inventory.QueryStocktakeRequest\x1a!.inventory.QueryStocktakeResponse\",\x82\xd3\xe4\x93\x02&\"!/api/v1/inventory/stocktake/query:\x01*\x12\xad\x01\n\x18QueryBranchInventorySort\x12*.inventory.QueryBranchInventorySortRequest\x1a+.inventory.QueryBranchInventorySortResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v1/inventory/branch-inventory-sort/query:\x01*B\x07Z\x05protob\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_ACCOUTINGACTION = _descriptor.EnumDescriptor(
  name='AccoutingAction',
  full_name='inventory.AccoutingAction',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WITHDRAW', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEPOSIT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FREEZE', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13758,
  serialized_end=13814,
)
_sym_db.RegisterEnumDescriptor(_ACCOUTINGACTION)

AccoutingAction = enum_type_wrapper.EnumTypeWrapper(_ACCOUTINGACTION)
_BATCHSTATUS = _descriptor.EnumDescriptor(
  name='BatchStatus',
  full_name='inventory.BatchStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INIT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PROCESSING', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUCCESS', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='COMPLETE', index=3, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='HASH_CHECK_ERROR', index=4, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13816,
  serialized_end=13904,
)
_sym_db.RegisterEnumDescriptor(_BATCHSTATUS)

BatchStatus = enum_type_wrapper.EnumTypeWrapper(_BATCHSTATUS)
_USEFLAG = _descriptor.EnumDescriptor(
  name='UseFlag',
  full_name='inventory.UseFlag',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UseFlagInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UseFlagRecent', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UseFlagMiss', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13906,
  serialized_end=13968,
)
_sym_db.RegisterEnumDescriptor(_USEFLAG)

UseFlag = enum_type_wrapper.EnumTypeWrapper(_USEFLAG)
_QUERYTYPE = _descriptor.EnumDescriptor(
  name='QueryType',
  full_name='inventory.QueryType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='QueryTypeInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QueryTypeAll', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='QueryTypeMiss', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13970,
  serialized_end=14037,
)
_sym_db.RegisterEnumDescriptor(_QUERYTYPE)

QueryType = enum_type_wrapper.EnumTypeWrapper(_QUERYTYPE)
WITHDRAW = 0
DEPOSIT = 1
FREEZE = 2
INIT = 0
PROCESSING = 1
SUCCESS = 2
COMPLETE = 4
HASH_CHECK_ERROR = 5
UseFlagInit = 0
UseFlagRecent = 1
UseFlagMiss = 2
QueryTypeInit = 0
QueryTypeAll = 1
QueryTypeMiss = 2


_ACCOUNT_EXTRATYPE = _descriptor.EnumDescriptor(
  name='ExtraType',
  full_name='inventory.Account.ExtraType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SKU', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUB_ACCOUNT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FREEZE_CODE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BROKER', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSER', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GROUP', index=5, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2175,
  serialized_end=2265,
)
_sym_db.RegisterEnumDescriptor(_ACCOUNT_EXTRATYPE)

_BATCHREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='inventory.BatchRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSET', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WITHDRAW', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEPOSIT', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSFER', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSFER_INIT', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSFER_CANCEL', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSFER_COMMIT', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FREEZE', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UNFREEZE', index=8, number=8,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SNAPSHOT', index=9, number=9,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACCOUNT_SUB', index=10, number=10,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACCOUNT_SUSPEND', index=11, number=11,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACCOUNT_CANCEL', index=12, number=12,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STOCKTAKE', index=13, number=13,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MIXED', index=14, number=100,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3208,
  serialized_end=3453,
)
_sym_db.RegisterEnumDescriptor(_BATCHREQUEST_ACTION)

_BATCHDETAILITEM_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='inventory.BatchDetailItem.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSET', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WITHDRAW', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DEPOSIT', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSFER', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSFER_INIT', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSFER_CANCEL', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TRANSFER_COMMIT', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FREEZE', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UNFREEZE', index=8, number=8,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SNAPSHOT', index=9, number=9,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACCOUNT_SUB', index=10, number=10,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACCOUNT_SUSPEND', index=11, number=11,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACCOUNT_CANCEL', index=12, number=12,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STOCKTAKE', index=13, number=13,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3208,
  serialized_end=3442,
)
_sym_db.RegisterEnumDescriptor(_BATCHDETAILITEM_ACTION)

_BATCHDETAILITEM_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='inventory.BatchDetailItem.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INIT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PROCESSING', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUCCESS', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FAIL', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4090,
  serialized_end=4147,
)
_sym_db.RegisterEnumDescriptor(_BATCHDETAILITEM_STATUS)

_ORDERBY_RELATION = _descriptor.EnumDescriptor(
  name='Relation',
  full_name='inventory.OrderBy.Relation',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ASC', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DESC', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4950,
  serialized_end=4979,
)
_sym_db.RegisterEnumDescriptor(_ORDERBY_RELATION)

_FREEZEINFO_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='inventory.FreezeInfo.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ACTIVE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FINISH', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=11418,
  serialized_end=11450,
)
_sym_db.RegisterEnumDescriptor(_FREEZEINFO_STATUS)

_FREEZEITEM_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='inventory.FreezeItem.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SUCCESS', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FAIL', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=11895,
  serialized_end=11926,
)
_sym_db.RegisterEnumDescriptor(_FREEZEITEM_STATUS)

_GROUPREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='inventory.GroupRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ADD', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UPDATE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DELETE', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=12196,
  serialized_end=12237,
)
_sym_db.RegisterEnumDescriptor(_GROUPREQUEST_ACTION)


_UNFREEZEREQUEST = _descriptor.Descriptor(
  name='UnfreezeRequest',
  full_name='inventory.UnfreezeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='inventory.UnfreezeRequest.trace_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=103,
  serialized_end=138,
)


_LISTGROUPSNAPSHOTREQUEST = _descriptor.Descriptor(
  name='ListGroupSnapshotRequest',
  full_name='inventory.ListGroupSnapshotRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.ListGroupSnapshotRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory.ListGroupSnapshotRequest.product_ids', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.ListGroupSnapshotRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory.ListGroupSnapshotRequest.start_time', index=3,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory.ListGroupSnapshotRequest.end_time', index=4,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=141,
  serialized_end=315,
)


_LISTGROUPSNAPSHOTRESPONSE = _descriptor.Descriptor(
  name='ListGroupSnapshotResponse',
  full_name='inventory.ListGroupSnapshotResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request', full_name='inventory.ListGroupSnapshotResponse.request', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_snapshots', full_name='inventory.ListGroupSnapshotResponse.group_snapshots', index=1,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=318,
  serialized_end=450,
)


_GROUPSNAPSHOT_GROUPITEM = _descriptor.Descriptor(
  name='GroupItem',
  full_name='inventory.GroupSnapShot.GroupItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.GroupSnapShot.GroupItem.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.GroupSnapShot.GroupItem.amount', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=558,
  serialized_end=618,
)

_GROUPSNAPSHOT = _descriptor.Descriptor(
  name='GroupSnapShot',
  full_name='inventory.GroupSnapShot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.GroupSnapShot.account', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='inventory.GroupSnapShot.items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GROUPSNAPSHOT_GROUPITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=453,
  serialized_end=618,
)


_LISTGROUPEVENTLOGREQUEST = _descriptor.Descriptor(
  name='ListGroupEventLogRequest',
  full_name='inventory.ListGroupEventLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.ListGroupEventLogRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory.ListGroupEventLogRequest.product_ids', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory.ListGroupEventLogRequest.start_time', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory.ListGroupEventLogRequest.end_time', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory.ListGroupEventLogRequest.limit', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory.ListGroupEventLogRequest.offset', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=621,
  serialized_end=812,
)


_LISTGROUPEVENTLOGRESPONSE = _descriptor.Descriptor(
  name='ListGroupEventLogResponse',
  full_name='inventory.ListGroupEventLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request', full_name='inventory.ListGroupEventLogResponse.request', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.ListGroupEventLogResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_logs', full_name='inventory.ListGroupEventLogResponse.group_logs', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=815,
  serialized_end=957,
)


_GROUPEVENTLOG = _descriptor.Descriptor(
  name='GroupEventLog',
  full_name='inventory.GroupEventLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.GroupEventLog.product_id', index=0,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.GroupEventLog.code', index=1,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.GroupEventLog.action', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory.GroupEventLog.qty', index=3,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='inventory.GroupEventLog.count', index=4,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=959,
  serialized_end=1052,
)


_LISTINVENTORYLOGREQUEST = _descriptor.Descriptor(
  name='ListInventoryLogRequest',
  full_name='inventory.ListInventoryLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.ListInventoryLogRequest.branch_id', index=0,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory.ListInventoryLogRequest.product_ids', index=1,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='codes', full_name='inventory.ListInventoryLogRequest.codes', index=2,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actions', full_name='inventory.ListInventoryLogRequest.actions', index=3,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory.ListInventoryLogRequest.limit', index=4,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory.ListInventoryLogRequest.offset', index=5,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory.ListInventoryLogRequest.start_time', index=6,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory.ListInventoryLogRequest.end_time', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_nos', full_name='inventory.ListInventoryLogRequest.batch_nos', index=8,
      number=10, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_ids', full_name='inventory.ListInventoryLogRequest.trace_ids', index=9,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='inventory.ListInventoryLogRequest.account_type', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_ids', full_name='inventory.ListInventoryLogRequest.sub_account_ids', index=11,
      number=13, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1055,
  serialized_end=1362,
)


_LISTINVENTORYLOGRESPONSE = _descriptor.Descriptor(
  name='ListInventoryLogResponse',
  full_name='inventory.ListInventoryLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request', full_name='inventory.ListInventoryLogResponse.request', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logs', full_name='inventory.ListInventoryLogResponse.logs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.ListInventoryLogResponse.total', index=2,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_sum', full_name='inventory.ListInventoryLogResponse.amount_sum', index=3,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1365,
  serialized_end=1518,
)


_AMOUNT_EXTRAENTRY = _descriptor.Descriptor(
  name='ExtraEntry',
  full_name='inventory.Amount.ExtraEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='inventory.Amount.ExtraEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='inventory.Amount.ExtraEntry.value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1659,
  serialized_end=1703,
)

_AMOUNT = _descriptor.Descriptor(
  name='Amount',
  full_name='inventory.Amount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory.Amount.qty', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='round', full_name='inventory.Amount.round', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra', full_name='inventory.Amount.extra', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status_account', full_name='inventory.Amount.status_account', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='inventory.Amount.price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.Amount.amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_AMOUNT_EXTRAENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1521,
  serialized_end=1703,
)


_PAGINATION = _descriptor.Descriptor(
  name='Pagination',
  full_name='inventory.Pagination',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory.Pagination.offset', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory.Pagination.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page_size', full_name='inventory.Pagination.page_size', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.Pagination.total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1705,
  serialized_end=1782,
)


_BATCHOPTION = _descriptor.Descriptor(
  name='BatchOption',
  full_name='inventory.BatchOption',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.BatchOption.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory.BatchOption.batch_no', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.BatchOption.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1784,
  serialized_end=1841,
)


_ACCOUNT_EXTRA = _descriptor.Descriptor(
  name='Extra',
  full_name='inventory.Account.Extra',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.Account.Extra.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.Account.Extra.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='inventory.Account.Extra.type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1989,
  serialized_end=2066,
)

_ACCOUNT_SUBACCOUNT = _descriptor.Descriptor(
  name='SubAccount',
  full_name='inventory.Account.SubAccount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.Account.SubAccount.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.Account.SubAccount.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='level', full_name='inventory.Account.SubAccount.level', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='inventory.Account.SubAccount.sub_account', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2068,
  serialized_end=2173,
)

_ACCOUNT = _descriptor.Descriptor(
  name='Account',
  full_name='inventory.Account',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.Account.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.Account.product_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra', full_name='inventory.Account.extra', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='inventory.Account.sub_account', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_ACCOUNT_EXTRA, _ACCOUNT_SUBACCOUNT, ],
  enum_types=[
    _ACCOUNT_EXTRATYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='locate', full_name='inventory.Account.locate',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=1844,
  serialized_end=2275,
)


_ACCOUNTINGITEM = _descriptor.Descriptor(
  name='AccountingItem',
  full_name='inventory.AccountingItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.AccountingItem.account', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.AccountingItem.action', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.AccountingItem.amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='inventory.AccountingItem.unit', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='numerator', full_name='inventory.AccountingItem.numerator', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='denominator', full_name='inventory.AccountingItem.denominator', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expired_time', full_name='inventory.AccountingItem.expired_time', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2278,
  serialized_end=2495,
)


_TRANSFERITEM = _descriptor.Descriptor(
  name='TransferItem',
  full_name='inventory.TransferItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='from', full_name='inventory.TransferItem.from', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='to', full_name='inventory.TransferItem.to', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.TransferItem.amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='inventory.TransferItem.unit', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='numerator', full_name='inventory.TransferItem.numerator', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='denominator', full_name='inventory.TransferItem.denominator', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker', full_name='inventory.TransferItem.broker', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2498,
  serialized_end=2664,
)


_SNAPSHOTITEM = _descriptor.Descriptor(
  name='SnapshotItem',
  full_name='inventory.SnapshotItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.SnapshotItem.account', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.SnapshotItem.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='inventory.SnapshotItem.source', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory.SnapshotItem.end_time', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory.SnapshotItem.start_time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2667,
  serialized_end=2842,
)


_STOCKTAKEITEM = _descriptor.Descriptor(
  name='StocktakeItem',
  full_name='inventory.StocktakeItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.StocktakeItem.account', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.StocktakeItem.amount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_id', full_name='inventory.StocktakeItem.stocktake_id', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory.StocktakeItem.batch_no', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2844,
  serialized_end=2952,
)


_BATCHREQUEST = _descriptor.Descriptor(
  name='BatchRequest',
  full_name='inventory.BatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.BatchRequest.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory.BatchRequest.batch_no', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.BatchRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.BatchRequest.action', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.BatchRequest.description', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.BatchRequest.detail', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='inventory.BatchRequest.trace_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_trace_id', full_name='inventory.BatchRequest.pre_trace_id', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_unfreeze', full_name='inventory.BatchRequest.auto_unfreeze', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='replay', full_name='inventory.BatchRequest.replay', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _BATCHREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2955,
  serialized_end=3453,
)


_BATCHDETAILITEM = _descriptor.Descriptor(
  name='BatchDetailItem',
  full_name='inventory.BatchDetailItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sequence_id', full_name='inventory.BatchDetailItem.sequence_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='checksum', full_name='inventory.BatchDetailItem.checksum', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.BatchDetailItem.action', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.BatchDetailItem.status', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='business_time', full_name='inventory.BatchDetailItem.business_time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting', full_name='inventory.BatchDetailItem.accounting', index=5,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='inventory.BatchDetailItem.transfer', index=6,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='snapshot', full_name='inventory.BatchDetailItem.snapshot', index=7,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='inventory.BatchDetailItem.stocktake', index=8,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _BATCHDETAILITEM_ACTION,
    _BATCHDETAILITEM_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='data', full_name='inventory.BatchDetailItem.data',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=3456,
  serialized_end=4155,
)


_BATCHRESPONSE = _descriptor.Descriptor(
  name='BatchResponse',
  full_name='inventory.BatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='inventory.BatchResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.BatchResponse.id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory.BatchResponse.batch_no', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.BatchResponse.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.BatchResponse.description', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4157,
  serialized_end=4256,
)


_BATCHQUERY_BATCHBASE = _descriptor.Descriptor(
  name='BatchBase',
  full_name='inventory.BatchQuery.BatchBase',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory.BatchQuery.BatchBase.batch_no', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.BatchQuery.BatchBase.code', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.BatchQuery.BatchBase.action', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4353,
  serialized_end=4412,
)

_BATCHQUERY = _descriptor.Descriptor(
  name='BatchQuery',
  full_name='inventory.BatchQuery',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.BatchQuery.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch', full_name='inventory.BatchQuery.batch', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.BatchQuery.detail', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_BATCHQUERY_BATCHBASE, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='query', full_name='inventory.BatchQuery.query',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=4259,
  serialized_end=4421,
)


_INVENTORYQUERYREQUEST_EXTRAENTRY = _descriptor.Descriptor(
  name='ExtraEntry',
  full_name='inventory.InventoryQueryRequest.ExtraEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='inventory.InventoryQueryRequest.ExtraEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='inventory.InventoryQueryRequest.ExtraEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4787,
  serialized_end=4831,
)

_INVENTORYQUERYREQUEST_BRANCHIDS = _descriptor.Descriptor(
  name='BranchIDs',
  full_name='inventory.InventoryQueryRequest.BranchIDs',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.InventoryQueryRequest.BranchIDs.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4833,
  serialized_end=4863,
)

_INVENTORYQUERYREQUEST = _descriptor.Descriptor(
  name='InventoryQueryRequest',
  full_name='inventory.InventoryQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='inventory.InventoryQueryRequest.branch_ids', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch', full_name='inventory.InventoryQueryRequest.batch', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pagination', full_name='inventory.InventoryQueryRequest.pagination', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='options', full_name='inventory.InventoryQueryRequest.options', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory.InventoryQueryRequest.product_ids', index=4,
      number=5, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderby_col', full_name='inventory.InventoryQueryRequest.orderby_col', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra', full_name='inventory.InventoryQueryRequest.extra', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_ids', full_name='inventory.InventoryQueryRequest.sub_account_ids', index=7,
      number=8, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_INVENTORYQUERYREQUEST_EXTRAENTRY, _INVENTORYQUERYREQUEST_BRANCHIDS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='query', full_name='inventory.InventoryQueryRequest.query',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=4424,
  serialized_end=4872,
)


_ORDERBY = _descriptor.Descriptor(
  name='OrderBy',
  full_name='inventory.OrderBy',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='relation', full_name='inventory.OrderBy.relation', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='col_name', full_name='inventory.OrderBy.col_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ORDERBY_RELATION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4874,
  serialized_end=4979,
)


_INVENTORYQUERYLSITREQUEST = _descriptor.Descriptor(
  name='InventoryQueryLsitRequest',
  full_name='inventory.InventoryQueryLsitRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounts', full_name='inventory.InventoryQueryLsitRequest.accounts', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4981,
  serialized_end=5046,
)


_INVENTORYQUERYRESPONSE_EXTRAENTRY = _descriptor.Descriptor(
  name='ExtraEntry',
  full_name='inventory.InventoryQueryResponse.ExtraEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='inventory.InventoryQueryResponse.ExtraEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='inventory.InventoryQueryResponse.ExtraEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4787,
  serialized_end=4831,
)

_INVENTORYQUERYRESPONSE = _descriptor.Descriptor(
  name='InventoryQueryResponse',
  full_name='inventory.InventoryQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.InventoryQueryResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory.InventoryQueryResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra', full_name='inventory.InventoryQueryResponse.extra', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_INVENTORYQUERYRESPONSE_EXTRAENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5049,
  serialized_end=5231,
)


_SKUACCOUNTREQUEST = _descriptor.Descriptor(
  name='SkuAccountRequest',
  full_name='inventory.SkuAccountRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SkuAccountRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.SkuAccountRequest.product_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5233,
  serialized_end=5291,
)


_SKUACCOUNTRESPONSE = _descriptor.Descriptor(
  name='SkuAccountResponse',
  full_name='inventory.SkuAccountResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.SkuAccountResponse.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='inventory.SkuAccountResponse.partner_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SkuAccountResponse.branch_id', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.SkuAccountResponse.product_id', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.SkuAccountResponse.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='inventory.SkuAccountResponse.created', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='inventory.SkuAccountResponse.created_by', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='inventory.SkuAccountResponse.updated', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='inventory.SkuAccountResponse.updated_by', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5294,
  serialized_end=5531,
)


_BATCHQUERYRESPONSE = _descriptor.Descriptor(
  name='BatchQueryResponse',
  full_name='inventory.BatchQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.BatchQueryResponse.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory.BatchQueryResponse.batch_no', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.BatchQueryResponse.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.BatchQueryResponse.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.BatchQueryResponse.description', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.BatchQueryResponse.status', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.BatchQueryResponse.detail', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5534,
  serialized_end=5695,
)


_STOCKOPTION = _descriptor.Descriptor(
  name='StockOption',
  full_name='inventory.StockOption',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='aggregate', full_name='inventory.StockOption.aggregate', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.StockOption.detail', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='snapshot', full_name='inventory.StockOption.snapshot', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5697,
  serialized_end=5763,
)


_STOCKINFO_AGGREGATE = _descriptor.Descriptor(
  name='Aggregate',
  full_name='inventory.StockInfo.Aggregate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.StockInfo.Aggregate.total', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='inventory.StockInfo.Aggregate.sub_account', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze', full_name='inventory.StockInfo.Aggregate.freeze', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker', full_name='inventory.StockInfo.Aggregate.broker', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6008,
  serialized_end=6087,
)

_STOCKINFO_SNAPSHOT = _descriptor.Descriptor(
  name='Snapshot',
  full_name='inventory.StockInfo.Snapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.StockInfo.Snapshot.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.StockInfo.Snapshot.amount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ref', full_name='inventory.StockInfo.Snapshot.ref', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6089,
  serialized_end=6140,
)

_STOCKINFO_DETAIL = _descriptor.Descriptor(
  name='Detail',
  full_name='inventory.StockInfo.Detail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='child', full_name='inventory.StockInfo.Detail.child', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze', full_name='inventory.StockInfo.Detail.freeze', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker', full_name='inventory.StockInfo.Detail.broker', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6142,
  serialized_end=6264,
)

_STOCKINFO = _descriptor.Descriptor(
  name='StockInfo',
  full_name='inventory.StockInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.StockInfo.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.StockInfo.account', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.StockInfo.amount', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='aggregate', full_name='inventory.StockInfo.aggregate', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='snapshot', full_name='inventory.StockInfo.snapshot', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.StockInfo.detail', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_STOCKINFO_AGGREGATE, _STOCKINFO_SNAPSHOT, _STOCKINFO_DETAIL, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5766,
  serialized_end=6264,
)


_STOCKQUERYREQUEST = _descriptor.Descriptor(
  name='StockQueryRequest',
  full_name='inventory.StockQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.StockQueryRequest.account', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='options', full_name='inventory.StockQueryRequest.options', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6266,
  serialized_end=6363,
)


_STOCKQUERYRESPONSE = _descriptor.Descriptor(
  name='StockQueryResponse',
  full_name='inventory.StockQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='inventory.StockQueryResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='inventory.StockQueryResponse.data', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='inventory.StockQueryResponse.message', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6365,
  serialized_end=6455,
)


_SNAPSHOT_STATITEM = _descriptor.Descriptor(
  name='StatItem',
  full_name='inventory.Snapshot.StatItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.Snapshot.StatItem.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.Snapshot.StatItem.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.Snapshot.StatItem.amount', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='inventory.Snapshot.StatItem.account_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6880,
  serialized_end=6977,
)

_SNAPSHOT_PREVIOUS = _descriptor.Descriptor(
  name='Previous',
  full_name='inventory.Snapshot.Previous',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.Snapshot.Previous.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.Snapshot.Previous.amount', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6979,
  serialized_end=7036,
)

_SNAPSHOT = _descriptor.Descriptor(
  name='Snapshot',
  full_name='inventory.Snapshot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.Snapshot.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.Snapshot.account', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.Snapshot.amount', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='previous', full_name='inventory.Snapshot.previous', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.Snapshot.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='inventory.Snapshot.source', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='empty', full_name='inventory.Snapshot.empty', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='first_id', full_name='inventory.Snapshot.first_id', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_id', full_name='inventory.Snapshot.last_id', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.Snapshot.start', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.Snapshot.end', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='inventory.Snapshot.created', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status_account', full_name='inventory.Snapshot.status_account', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats', full_name='inventory.Snapshot.stats', index=13,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SNAPSHOT_STATITEM, _SNAPSHOT_PREVIOUS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6458,
  serialized_end=7036,
)


_SNAPSHOTBATCHQUERYREQUEST = _descriptor.Descriptor(
  name='SnapshotBatchQueryRequest',
  full_name='inventory.SnapshotBatchQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SnapshotBatchQueryRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.SnapshotBatchQueryRequest.account', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.SnapshotBatchQueryRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='from', full_name='inventory.SnapshotBatchQueryRequest.from', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='to', full_name='inventory.SnapshotBatchQueryRequest.to', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7039,
  serialized_end=7218,
)


_SNAPSHOTBATCHQUERYRESPONSE = _descriptor.Descriptor(
  name='SnapshotBatchQueryResponse',
  full_name='inventory.SnapshotBatchQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.SnapshotBatchQueryResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory.SnapshotBatchQueryResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7220,
  serialized_end=7298,
)


_SUMLISTQUERYRESPONSE = _descriptor.Descriptor(
  name='SumListQueryResponse',
  full_name='inventory.SumListQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.SumListQueryResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory.SumListQueryResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7300,
  serialized_end=7375,
)


_SNAPSHOTSUM_STATITEM = _descriptor.Descriptor(
  name='StatItem',
  full_name='inventory.SnapshotSum.StatItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.SnapshotSum.StatItem.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.SnapshotSum.StatItem.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.SnapshotSum.StatItem.amount', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='inventory.SnapshotSum.StatItem.account_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6880,
  serialized_end=6977,
)

_SNAPSHOTSUM = _descriptor.Descriptor(
  name='SnapshotSum',
  full_name='inventory.SnapshotSum',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.SnapshotSum.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.SnapshotSum.account', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_amount', full_name='inventory.SnapshotSum.start_amount', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_amount', full_name='inventory.SnapshotSum.end_amount', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.SnapshotSum.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats', full_name='inventory.SnapshotSum.stats', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SNAPSHOTSUM_STATITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7378,
  serialized_end=7681,
)


_SNAPSHOTQUERYREQUEST = _descriptor.Descriptor(
  name='SnapshotQueryRequest',
  full_name='inventory.SnapshotQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sku_id', full_name='inventory.SnapshotQueryRequest.sku_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.SnapshotQueryRequest.account', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.SnapshotQueryRequest.detail', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory.SnapshotQueryRequest.limit', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.SnapshotQueryRequest.start', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.SnapshotQueryRequest.end', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SnapshotQueryRequest.branch_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7684,
  serialized_end=7893,
)


_SNAPSHOTQUERYRESPONSE = _descriptor.Descriptor(
  name='SnapshotQueryResponse',
  full_name='inventory.SnapshotQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.SnapshotQueryResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory.SnapshotQueryResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7895,
  serialized_end=7968,
)


_SNAPSHOTSTATITEM = _descriptor.Descriptor(
  name='SnapshotStatItem',
  full_name='inventory.SnapshotStatItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.SnapshotStatItem.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.SnapshotStatItem.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory.SnapshotStatItem.qty', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SnapshotStatItem.branch_id', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.SnapshotStatItem.product_id', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='inventory.SnapshotStatItem.account_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7970,
  serialized_end=8092,
)


_SNAPSHOTSTATREQUEST = _descriptor.Descriptor(
  name='SnapshotStatRequest',
  full_name='inventory.SnapshotStatRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SnapshotStatRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.SnapshotStatRequest.product_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.SnapshotStatRequest.start', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.SnapshotStatRequest.end', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.SnapshotStatRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.SnapshotStatRequest.action', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8095,
  serialized_end=8269,
)


_SNAPSHOTSTATGROUPREQUEST = _descriptor.Descriptor(
  name='SnapshotStatGroupRequest',
  full_name='inventory.SnapshotStatGroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SnapshotStatGroupRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.SnapshotStatGroupRequest.product_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.SnapshotStatGroupRequest.start', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.SnapshotStatGroupRequest.end', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8272,
  serialized_end=8421,
)


_SNAPSHOTSTATGROUPRESPONSE = _descriptor.Descriptor(
  name='SnapshotStatGroupResponse',
  full_name='inventory.SnapshotStatGroupResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.SnapshotStatGroupResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='snapshot_stat', full_name='inventory.SnapshotStatGroupResponse.snapshot_stat', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8423,
  serialized_end=8522,
)


_SNAPSHOTSTATGROUPITEM = _descriptor.Descriptor(
  name='SnapshotStatGroupItem',
  full_name='inventory.SnapshotStatGroupItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SnapshotStatGroupItem.branch_id', index=0,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.SnapshotStatGroupItem.product_id', index=1,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8524,
  serialized_end=8586,
)


_SNAPSHOTSTATRESPONSE = _descriptor.Descriptor(
  name='SnapshotStatResponse',
  full_name='inventory.SnapshotStatResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.SnapshotStatResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='snapshot_stat', full_name='inventory.SnapshotStatResponse.snapshot_stat', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8588,
  serialized_end=8677,
)


_LISTGROUPQUERYREQUEST_EXTRAENTRY = _descriptor.Descriptor(
  name='ExtraEntry',
  full_name='inventory.ListGroupQueryRequest.ExtraEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='inventory.ListGroupQueryRequest.ExtraEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='inventory.ListGroupQueryRequest.ExtraEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4787,
  serialized_end=4831,
)

_LISTGROUPQUERYREQUEST = _descriptor.Descriptor(
  name='ListGroupQueryRequest',
  full_name='inventory.ListGroupQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_id', full_name='inventory.ListGroupQueryRequest.group_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.ListGroupQueryRequest.product_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.ListGroupQueryRequest.start', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.ListGroupQueryRequest.end', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory.ListGroupQueryRequest.limit', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory.ListGroupQueryRequest.offset', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra', full_name='inventory.ListGroupQueryRequest.extra', index=6,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTGROUPQUERYREQUEST_EXTRAENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8680,
  serialized_end=8962,
)


_HOOKSCREATEREQUEST = _descriptor.Descriptor(
  name='HooksCreateRequest',
  full_name='inventory.HooksCreateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='inventory.HooksCreateRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.HooksCreateRequest.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.HooksCreateRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.HooksCreateRequest.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8964,
  serialized_end=9049,
)


_HOOKSCREATERESPONSE = _descriptor.Descriptor(
  name='HooksCreateResponse',
  full_name='inventory.HooksCreateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.HooksCreateResponse.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='inventory.HooksCreateResponse.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.HooksCreateResponse.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.HooksCreateResponse.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.HooksCreateResponse.action', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='inventory.HooksCreateResponse.created', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9052,
  serialized_end=9195,
)


_HOOKSREQUEST = _descriptor.Descriptor(
  name='HooksRequest',
  full_name='inventory.HooksRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='all', full_name='inventory.HooksRequest.all', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9197,
  serialized_end=9224,
)


_HOOKSRESPONSE_CONFIG = _descriptor.Descriptor(
  name='Config',
  full_name='inventory.HooksResponse.Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='inventory.HooksResponse.Config.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.HooksResponse.Config.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.HooksResponse.Config.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.HooksResponse.Config.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enable', full_name='inventory.HooksResponse.Config.enable', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9312,
  serialized_end=9401,
)

_HOOKSRESPONSE = _descriptor.Descriptor(
  name='HooksResponse',
  full_name='inventory.HooksResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='inventory.HooksResponse.partner_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hooks', full_name='inventory.HooksResponse.hooks', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_HOOKSRESPONSE_CONFIG, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9227,
  serialized_end=9401,
)


_INVENTORYLOG = _descriptor.Descriptor(
  name='InventoryLog',
  full_name='inventory.InventoryLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory.InventoryLog.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stock_id', full_name='inventory.InventoryLog.stock_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.InventoryLog.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.InventoryLog.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='business_time', full_name='inventory.InventoryLog.business_time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.InventoryLog.status', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory.InventoryLog.qty', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='numerator', full_name='inventory.InventoryLog.numerator', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='denominator', full_name='inventory.InventoryLog.denominator', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='inventory.InventoryLog.source', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='inventory.InventoryLog.created_by', index=10,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='inventory.InventoryLog.created', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory.InventoryLog.batch_no', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='inventory.InventoryLog.partner_id', index=13,
      number=14, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.InventoryLog.branch_id', index=14,
      number=15, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.InventoryLog.product_id', index=15,
      number=16, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='inventory.InventoryLog.trace_id', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='inventory.InventoryLog.account_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='inventory.InventoryLog.sub_account_id', index=18,
      number=19, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9404,
  serialized_end=9820,
)


_SUMLISTQUERYREQUEST = _descriptor.Descriptor(
  name='SumListQueryRequest',
  full_name='inventory.SumListQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.SumListQueryRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.SumListQueryRequest.start', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.SumListQueryRequest.end', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory.SumListQueryRequest.limit', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory.SumListQueryRequest.offset', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.SumListQueryRequest.product_id', index=5,
      number=6, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_end', full_name='inventory.SumListQueryRequest.if_end', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_pre', full_name='inventory.SumListQueryRequest.if_pre', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9823,
  serialized_end=10030,
)


_QUERYSNAPSHOTSTATREQUEST = _descriptor.Descriptor(
  name='QuerySnapshotStatRequest',
  full_name='inventory.QuerySnapshotStatRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.QuerySnapshotStatRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory.QuerySnapshotStatRequest.start_time', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory.QuerySnapshotStatRequest.end_time', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10033,
  serialized_end=10172,
)


_QUERYSNAPSHOTSTATRESPONSE = _descriptor.Descriptor(
  name='QuerySnapshotStatResponse',
  full_name='inventory.QuerySnapshotStatResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.QuerySnapshotStatResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory.QuerySnapshotStatResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10174,
  serialized_end=10260,
)


_QUERYSNAPSHOTSTAT = _descriptor.Descriptor(
  name='QuerySnapshotStat',
  full_name='inventory.QuerySnapshotStat',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ProductId', full_name='inventory.QuerySnapshotStat.ProductId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10262,
  serialized_end=10300,
)


_LISTQUERYREQUEST_EXTRAENTRY = _descriptor.Descriptor(
  name='ExtraEntry',
  full_name='inventory.ListQueryRequest.ExtraEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='inventory.ListQueryRequest.ExtraEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='inventory.ListQueryRequest.ExtraEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4787,
  serialized_end=4831,
)

_LISTQUERYREQUEST = _descriptor.Descriptor(
  name='ListQueryRequest',
  full_name='inventory.ListQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.ListQueryRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.ListQueryRequest.account', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stock_id', full_name='inventory.ListQueryRequest.stock_id', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.ListQueryRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.ListQueryRequest.action', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.ListQueryRequest.start', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.ListQueryRequest.end', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory.ListQueryRequest.limit', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory.ListQueryRequest.offset', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra', full_name='inventory.ListQueryRequest.extra', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_ids', full_name='inventory.ListQueryRequest.sub_account_ids', index=10,
      number=11, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTQUERYREQUEST_EXTRAENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10303,
  serialized_end=10666,
)


_LISTITEMLOGQUERYRESPONSE = _descriptor.Descriptor(
  name='ListItemLogQueryResponse',
  full_name='inventory.ListItemLogQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.ListItemLogQueryResponse.account', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='inventory.ListItemLogQueryResponse.data', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10668,
  serialized_end=10770,
)


_LISTLOGQUERYRESPONSE = _descriptor.Descriptor(
  name='ListLogQueryResponse',
  full_name='inventory.ListLogQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='query', full_name='inventory.ListLogQueryResponse.query', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='inventory.ListLogQueryResponse.data', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.ListLogQueryResponse.total', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10773,
  serialized_end=10905,
)


_LOGQUERYREQUEST = _descriptor.Descriptor(
  name='LogQueryRequest',
  full_name='inventory.LogQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.LogQueryRequest.account', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.LogQueryRequest.start', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.LogQueryRequest.end', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10908,
  serialized_end=11046,
)


_LOGQUERYRESPONSE = _descriptor.Descriptor(
  name='LogQueryResponse',
  full_name='inventory.LogQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.LogQueryResponse.account', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.LogQueryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.LogQueryResponse.detail', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11048,
  serialized_end=11159,
)


_FREEZEINFO = _descriptor.Descriptor(
  name='FreezeInfo',
  full_name='inventory.FreezeInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stock_id', full_name='inventory.FreezeInfo.stock_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory.FreezeInfo.amount', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='init_amount', full_name='inventory.FreezeInfo.init_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='init_unit', full_name='inventory.FreezeInfo.init_unit', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expired_time', full_name='inventory.FreezeInfo.expired_time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.FreezeInfo.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.FreezeInfo.description', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='inventory.FreezeInfo.trace_id', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.FreezeInfo.status', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _FREEZEINFO_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11162,
  serialized_end=11450,
)


_FREEZEREQUEST = _descriptor.Descriptor(
  name='FreezeRequest',
  full_name='inventory.FreezeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='inventory.FreezeRequest.trace_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory.FreezeRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.FreezeRequest.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.FreezeRequest.detail', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11452,
  serialized_end=11559,
)


_FREEZERESPONSE = _descriptor.Descriptor(
  name='FreezeResponse',
  full_name='inventory.FreezeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='inventory.FreezeResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory.FreezeResponse.detail', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.FreezeResponse.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11561,
  serialized_end=11654,
)


_FREEZEITEM = _descriptor.Descriptor(
  name='FreezeItem',
  full_name='inventory.FreezeItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='freeze', full_name='inventory.FreezeItem.freeze', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='init_amount', full_name='inventory.FreezeItem.init_amount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freezed_amount', full_name='inventory.FreezeItem.freezed_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='available_amount', full_name='inventory.FreezeItem.available_amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expired_time', full_name='inventory.FreezeItem.expired_time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.FreezeItem.status', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.FreezeItem.description', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _FREEZEITEM_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11657,
  serialized_end=11926,
)


_FREEZEQUERYREQUEST = _descriptor.Descriptor(
  name='FreezeQueryRequest',
  full_name='inventory.FreezeQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='inventory.FreezeQueryRequest.trace_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.FreezeQueryRequest.account', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11928,
  serialized_end=12003,
)


_GROUPREQUEST_GROUPITEM = _descriptor.Descriptor(
  name='GroupItem',
  full_name='inventory.GroupRequest.GroupItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_id', full_name='inventory.GroupRequest.GroupItem.group_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.GroupRequest.GroupItem.branch_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.GroupRequest.GroupItem.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory.GroupRequest.GroupItem.action', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12077,
  serialized_end=12194,
)

_GROUPREQUEST = _descriptor.Descriptor(
  name='GroupRequest',
  full_name='inventory.GroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_item', full_name='inventory.GroupRequest.group_item', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GROUPREQUEST_GROUPITEM, ],
  enum_types=[
    _GROUPREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12006,
  serialized_end=12237,
)


_GROUPRESPONSE = _descriptor.Descriptor(
  name='GroupResponse',
  full_name='inventory.GroupResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='inventory.GroupResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12239,
  serialized_end=12271,
)


_QUERYGROUPBRANCHREQUEST = _descriptor.Descriptor(
  name='QueryGroupBranchRequest',
  full_name='inventory.QueryGroupBranchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_id', full_name='inventory.QueryGroupBranchRequest.group_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.QueryGroupBranchRequest.branch_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12273,
  serialized_end=12335,
)


_QUERYGROUPBRANCHRESPONSE_GROUPITEM = _descriptor.Descriptor(
  name='GroupItem',
  full_name='inventory.QueryGroupBranchResponse.GroupItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_id', full_name='inventory.QueryGroupBranchResponse.GroupItem.group_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.QueryGroupBranchResponse.GroupItem.branch_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.QueryGroupBranchResponse.GroupItem.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='inventory.QueryGroupBranchResponse.GroupItem.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12448,
  serialized_end=12533,
)

_QUERYGROUPBRANCHRESPONSE = _descriptor.Descriptor(
  name='QueryGroupBranchResponse',
  full_name='inventory.QueryGroupBranchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_item', full_name='inventory.QueryGroupBranchResponse.group_item', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.QueryGroupBranchResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_QUERYGROUPBRANCHRESPONSE_GROUPITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12338,
  serialized_end=12533,
)


_QUERYGROUPPRODUCTREQUEST = _descriptor.Descriptor(
  name='QueryGroupProductRequest',
  full_name='inventory.QueryGroupProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_id', full_name='inventory.QueryGroupProductRequest.group_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12535,
  serialized_end=12579,
)


_QUERYGROUPPRODUCTRESPONSE_PRODUCTINFO = _descriptor.Descriptor(
  name='ProductInfo',
  full_name='inventory.QueryGroupProductResponse.ProductInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.QueryGroupProductResponse.ProductInfo.product_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory.QueryGroupProductResponse.ProductInfo.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12698,
  serialized_end=12747,
)

_QUERYGROUPPRODUCTRESPONSE = _descriptor.Descriptor(
  name='QueryGroupProductResponse',
  full_name='inventory.QueryGroupProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_info', full_name='inventory.QueryGroupProductResponse.product_info', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.QueryGroupProductResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_QUERYGROUPPRODUCTRESPONSE_PRODUCTINFO, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12582,
  serialized_end=12747,
)


_QUERYSTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='QueryStocktakeRequest',
  full_name='inventory.QueryStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_nos', full_name='inventory.QueryStocktakeRequest.batch_nos', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.QueryStocktakeRequest.branch_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.QueryStocktakeRequest.product_id', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='inventory.QueryStocktakeRequest.start', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='inventory.QueryStocktakeRequest.end', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12750,
  serialized_end=12915,
)


_QUERYSTOCKTAKERESPONSE_STOCKTAKEITEM = _descriptor.Descriptor(
  name='StocktakeItem',
  full_name='inventory.QueryStocktakeResponse.StocktakeItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory.QueryStocktakeResponse.StocktakeItem.batch_no', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='inventory.QueryStocktakeResponse.StocktakeItem.account', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='business_time', full_name='inventory.QueryStocktakeResponse.StocktakeItem.business_time', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_qty', full_name='inventory.QueryStocktakeResponse.StocktakeItem.stocktake_qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_qty', full_name='inventory.QueryStocktakeResponse.StocktakeItem.real_qty', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_qty', full_name='inventory.QueryStocktakeResponse.StocktakeItem.diff_qty', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_id', full_name='inventory.QueryStocktakeResponse.StocktakeItem.stocktake_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='inventory.QueryStocktakeResponse.StocktakeItem.sub_account_id', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='Description', full_name='inventory.QueryStocktakeResponse.StocktakeItem.Description', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13033,
  serialized_end=13280,
)

_QUERYSTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='QueryStocktakeResponse',
  full_name='inventory.QueryStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.QueryStocktakeResponse.total', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_item', full_name='inventory.QueryStocktakeResponse.stocktake_item', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_QUERYSTOCKTAKERESPONSE_STOCKTAKEITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12918,
  serialized_end=13280,
)


_QUERYBRANCHINVENTORYSORTREQUEST = _descriptor.Descriptor(
  name='QueryBranchInventorySortRequest',
  full_name='inventory.QueryBranchInventorySortRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory.QueryBranchInventorySortRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory.QueryBranchInventorySortRequest.product_ids', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='query_type', full_name='inventory.QueryBranchInventorySortRequest.query_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='inventory.QueryBranchInventorySortRequest.page', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_flag', full_name='inventory.QueryBranchInventorySortRequest.use_flag', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13283,
  serialized_end=13472,
)


_PAGEORDER = _descriptor.Descriptor(
  name='PageOrder',
  full_name='inventory.PageOrder',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order', full_name='inventory.PageOrder.order', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='inventory.PageOrder.sort', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory.PageOrder.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory.PageOrder.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13474,
  serialized_end=13545,
)


_QUERYBRANCHINVENTORYSORTRESPONSE_QUERYBRANCHINVENTORYSORT = _descriptor.Descriptor(
  name='QueryBranchInventorySort',
  full_name='inventory.QueryBranchInventorySortResponse.QueryBranchInventorySort',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory.QueryBranchInventorySortResponse.QueryBranchInventorySort.product_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory.QueryBranchInventorySortResponse.QueryBranchInventorySort.qty', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='flag', full_name='inventory.QueryBranchInventorySortResponse.QueryBranchInventorySort.flag', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13683,
  serialized_end=13756,
)

_QUERYBRANCHINVENTORYSORTRESPONSE = _descriptor.Descriptor(
  name='QueryBranchInventorySortResponse',
  full_name='inventory.QueryBranchInventorySortResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory.QueryBranchInventorySortResponse.total', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory.QueryBranchInventorySortResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_QUERYBRANCHINVENTORYSORTRESPONSE_QUERYBRANCHINVENTORYSORT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13548,
  serialized_end=13756,
)

_LISTGROUPSNAPSHOTREQUEST.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTGROUPSNAPSHOTREQUEST.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTGROUPSNAPSHOTRESPONSE.fields_by_name['request'].message_type = _LISTGROUPSNAPSHOTREQUEST
_LISTGROUPSNAPSHOTRESPONSE.fields_by_name['group_snapshots'].message_type = _GROUPSNAPSHOT
_GROUPSNAPSHOT_GROUPITEM.fields_by_name['amount'].message_type = _AMOUNT
_GROUPSNAPSHOT_GROUPITEM.containing_type = _GROUPSNAPSHOT
_GROUPSNAPSHOT.fields_by_name['account'].message_type = _ACCOUNT
_GROUPSNAPSHOT.fields_by_name['items'].message_type = _GROUPSNAPSHOT_GROUPITEM
_LISTGROUPEVENTLOGREQUEST.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTGROUPEVENTLOGREQUEST.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTGROUPEVENTLOGRESPONSE.fields_by_name['request'].message_type = _LISTGROUPEVENTLOGREQUEST
_LISTGROUPEVENTLOGRESPONSE.fields_by_name['group_logs'].message_type = _GROUPEVENTLOG
_LISTINVENTORYLOGREQUEST.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTINVENTORYLOGREQUEST.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTINVENTORYLOGRESPONSE.fields_by_name['request'].message_type = _LISTINVENTORYLOGREQUEST
_LISTINVENTORYLOGRESPONSE.fields_by_name['logs'].message_type = _INVENTORYLOG
_AMOUNT_EXTRAENTRY.containing_type = _AMOUNT
_AMOUNT.fields_by_name['extra'].message_type = _AMOUNT_EXTRAENTRY
_ACCOUNT_EXTRA.fields_by_name['type'].enum_type = _ACCOUNT_EXTRATYPE
_ACCOUNT_EXTRA.containing_type = _ACCOUNT
_ACCOUNT_SUBACCOUNT.fields_by_name['sub_account'].message_type = _ACCOUNT_SUBACCOUNT
_ACCOUNT_SUBACCOUNT.containing_type = _ACCOUNT
_ACCOUNT.fields_by_name['extra'].message_type = _ACCOUNT_EXTRA
_ACCOUNT.fields_by_name['sub_account'].message_type = _ACCOUNT_SUBACCOUNT
_ACCOUNT_EXTRATYPE.containing_type = _ACCOUNT
_ACCOUNT.oneofs_by_name['locate'].fields.append(
  _ACCOUNT.fields_by_name['sub_account'])
_ACCOUNT.fields_by_name['sub_account'].containing_oneof = _ACCOUNT.oneofs_by_name['locate']
_ACCOUNTINGITEM.fields_by_name['account'].message_type = _ACCOUNT
_ACCOUNTINGITEM.fields_by_name['action'].enum_type = _ACCOUTINGACTION
_ACCOUNTINGITEM.fields_by_name['expired_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERITEM.fields_by_name['from'].message_type = _ACCOUNT
_TRANSFERITEM.fields_by_name['to'].message_type = _ACCOUNT
_SNAPSHOTITEM.fields_by_name['account'].message_type = _ACCOUNT
_SNAPSHOTITEM.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTITEM.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEITEM.fields_by_name['account'].message_type = _ACCOUNT
_BATCHREQUEST.fields_by_name['action'].enum_type = _BATCHREQUEST_ACTION
_BATCHREQUEST.fields_by_name['detail'].message_type = _BATCHDETAILITEM
_BATCHREQUEST_ACTION.containing_type = _BATCHREQUEST
_BATCHDETAILITEM.fields_by_name['action'].enum_type = _BATCHDETAILITEM_ACTION
_BATCHDETAILITEM.fields_by_name['status'].enum_type = _BATCHDETAILITEM_STATUS
_BATCHDETAILITEM.fields_by_name['business_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BATCHDETAILITEM.fields_by_name['accounting'].message_type = _ACCOUNTINGITEM
_BATCHDETAILITEM.fields_by_name['transfer'].message_type = _TRANSFERITEM
_BATCHDETAILITEM.fields_by_name['snapshot'].message_type = _SNAPSHOTITEM
_BATCHDETAILITEM.fields_by_name['stocktake'].message_type = _STOCKTAKEITEM
_BATCHDETAILITEM_ACTION.containing_type = _BATCHDETAILITEM
_BATCHDETAILITEM_STATUS.containing_type = _BATCHDETAILITEM
_BATCHDETAILITEM.oneofs_by_name['data'].fields.append(
  _BATCHDETAILITEM.fields_by_name['accounting'])
_BATCHDETAILITEM.fields_by_name['accounting'].containing_oneof = _BATCHDETAILITEM.oneofs_by_name['data']
_BATCHDETAILITEM.oneofs_by_name['data'].fields.append(
  _BATCHDETAILITEM.fields_by_name['transfer'])
_BATCHDETAILITEM.fields_by_name['transfer'].containing_oneof = _BATCHDETAILITEM.oneofs_by_name['data']
_BATCHDETAILITEM.oneofs_by_name['data'].fields.append(
  _BATCHDETAILITEM.fields_by_name['snapshot'])
_BATCHDETAILITEM.fields_by_name['snapshot'].containing_oneof = _BATCHDETAILITEM.oneofs_by_name['data']
_BATCHDETAILITEM.oneofs_by_name['data'].fields.append(
  _BATCHDETAILITEM.fields_by_name['stocktake'])
_BATCHDETAILITEM.fields_by_name['stocktake'].containing_oneof = _BATCHDETAILITEM.oneofs_by_name['data']
_BATCHQUERY_BATCHBASE.containing_type = _BATCHQUERY
_BATCHQUERY.fields_by_name['batch'].message_type = _BATCHQUERY_BATCHBASE
_BATCHQUERY.oneofs_by_name['query'].fields.append(
  _BATCHQUERY.fields_by_name['id'])
_BATCHQUERY.fields_by_name['id'].containing_oneof = _BATCHQUERY.oneofs_by_name['query']
_BATCHQUERY.oneofs_by_name['query'].fields.append(
  _BATCHQUERY.fields_by_name['batch'])
_BATCHQUERY.fields_by_name['batch'].containing_oneof = _BATCHQUERY.oneofs_by_name['query']
_INVENTORYQUERYREQUEST_EXTRAENTRY.containing_type = _INVENTORYQUERYREQUEST
_INVENTORYQUERYREQUEST_BRANCHIDS.containing_type = _INVENTORYQUERYREQUEST
_INVENTORYQUERYREQUEST.fields_by_name['branch_ids'].message_type = _INVENTORYQUERYREQUEST_BRANCHIDS
_INVENTORYQUERYREQUEST.fields_by_name['batch'].message_type = _BATCHOPTION
_INVENTORYQUERYREQUEST.fields_by_name['pagination'].message_type = _PAGINATION
_INVENTORYQUERYREQUEST.fields_by_name['options'].message_type = _STOCKOPTION
_INVENTORYQUERYREQUEST.fields_by_name['orderby_col'].message_type = _ORDERBY
_INVENTORYQUERYREQUEST.fields_by_name['extra'].message_type = _INVENTORYQUERYREQUEST_EXTRAENTRY
_INVENTORYQUERYREQUEST.oneofs_by_name['query'].fields.append(
  _INVENTORYQUERYREQUEST.fields_by_name['branch_ids'])
_INVENTORYQUERYREQUEST.fields_by_name['branch_ids'].containing_oneof = _INVENTORYQUERYREQUEST.oneofs_by_name['query']
_INVENTORYQUERYREQUEST.oneofs_by_name['query'].fields.append(
  _INVENTORYQUERYREQUEST.fields_by_name['batch'])
_INVENTORYQUERYREQUEST.fields_by_name['batch'].containing_oneof = _INVENTORYQUERYREQUEST.oneofs_by_name['query']
_ORDERBY.fields_by_name['relation'].enum_type = _ORDERBY_RELATION
_ORDERBY_RELATION.containing_type = _ORDERBY
_INVENTORYQUERYLSITREQUEST.fields_by_name['accounts'].message_type = _ACCOUNT
_INVENTORYQUERYRESPONSE_EXTRAENTRY.containing_type = _INVENTORYQUERYRESPONSE
_INVENTORYQUERYRESPONSE.fields_by_name['rows'].message_type = _STOCKINFO
_INVENTORYQUERYRESPONSE.fields_by_name['extra'].message_type = _INVENTORYQUERYRESPONSE_EXTRAENTRY
_SKUACCOUNTRESPONSE.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SKUACCOUNTRESPONSE.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BATCHQUERYRESPONSE.fields_by_name['detail'].message_type = _BATCHDETAILITEM
_STOCKINFO_AGGREGATE.containing_type = _STOCKINFO
_STOCKINFO_SNAPSHOT.containing_type = _STOCKINFO
_STOCKINFO_DETAIL.fields_by_name['child'].message_type = _STOCKINFO
_STOCKINFO_DETAIL.fields_by_name['freeze'].message_type = _FREEZEINFO
_STOCKINFO_DETAIL.fields_by_name['broker'].message_type = _STOCKINFO
_STOCKINFO_DETAIL.containing_type = _STOCKINFO
_STOCKINFO.fields_by_name['account'].message_type = _ACCOUNT
_STOCKINFO.fields_by_name['amount'].message_type = _AMOUNT
_STOCKINFO.fields_by_name['aggregate'].message_type = _STOCKINFO_AGGREGATE
_STOCKINFO.fields_by_name['snapshot'].message_type = _STOCKINFO_SNAPSHOT
_STOCKINFO.fields_by_name['detail'].message_type = _STOCKINFO_DETAIL
_STOCKQUERYREQUEST.fields_by_name['account'].message_type = _ACCOUNT
_STOCKQUERYREQUEST.fields_by_name['options'].message_type = _STOCKOPTION
_STOCKQUERYRESPONSE.fields_by_name['data'].message_type = _STOCKINFO
_SNAPSHOT_STATITEM.fields_by_name['amount'].message_type = _AMOUNT
_SNAPSHOT_STATITEM.containing_type = _SNAPSHOT
_SNAPSHOT_PREVIOUS.fields_by_name['amount'].message_type = _AMOUNT
_SNAPSHOT_PREVIOUS.containing_type = _SNAPSHOT
_SNAPSHOT.fields_by_name['account'].message_type = _ACCOUNT
_SNAPSHOT.fields_by_name['amount'].message_type = _AMOUNT
_SNAPSHOT.fields_by_name['previous'].message_type = _SNAPSHOT_PREVIOUS
_SNAPSHOT.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOT.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOT.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOT.fields_by_name['stats'].message_type = _SNAPSHOT_STATITEM
_SNAPSHOTBATCHQUERYREQUEST.fields_by_name['account'].message_type = _ACCOUNT
_SNAPSHOTBATCHQUERYREQUEST.fields_by_name['from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTBATCHQUERYREQUEST.fields_by_name['to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTBATCHQUERYRESPONSE.fields_by_name['rows'].message_type = _SNAPSHOT
_SUMLISTQUERYRESPONSE.fields_by_name['rows'].message_type = _SNAPSHOTSUM
_SNAPSHOTSUM_STATITEM.fields_by_name['amount'].message_type = _AMOUNT
_SNAPSHOTSUM_STATITEM.containing_type = _SNAPSHOTSUM
_SNAPSHOTSUM.fields_by_name['account'].message_type = _ACCOUNT
_SNAPSHOTSUM.fields_by_name['start_amount'].message_type = _AMOUNT
_SNAPSHOTSUM.fields_by_name['end_amount'].message_type = _AMOUNT
_SNAPSHOTSUM.fields_by_name['stats'].message_type = _SNAPSHOTSUM_STATITEM
_SNAPSHOTQUERYREQUEST.fields_by_name['account'].message_type = _ACCOUNT
_SNAPSHOTQUERYREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTQUERYREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTQUERYRESPONSE.fields_by_name['rows'].message_type = _SNAPSHOT
_SNAPSHOTSTATREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTSTATREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTSTATGROUPREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTSTATGROUPREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SNAPSHOTSTATGROUPRESPONSE.fields_by_name['snapshot_stat'].message_type = _SNAPSHOTSTATGROUPITEM
_SNAPSHOTSTATRESPONSE.fields_by_name['snapshot_stat'].message_type = _SNAPSHOTSTATITEM
_LISTGROUPQUERYREQUEST_EXTRAENTRY.containing_type = _LISTGROUPQUERYREQUEST
_LISTGROUPQUERYREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTGROUPQUERYREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTGROUPQUERYREQUEST.fields_by_name['extra'].message_type = _LISTGROUPQUERYREQUEST_EXTRAENTRY
_HOOKSCREATERESPONSE.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_HOOKSRESPONSE_CONFIG.containing_type = _HOOKSRESPONSE
_HOOKSRESPONSE.fields_by_name['hooks'].message_type = _HOOKSRESPONSE_CONFIG
_INVENTORYLOG.fields_by_name['business_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_INVENTORYLOG.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMLISTQUERYREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMLISTQUERYREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYSNAPSHOTSTATREQUEST.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYSNAPSHOTSTATREQUEST.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYSNAPSHOTSTATRESPONSE.fields_by_name['rows'].message_type = _QUERYSNAPSHOTSTAT
_LISTQUERYREQUEST_EXTRAENTRY.containing_type = _LISTQUERYREQUEST
_LISTQUERYREQUEST.fields_by_name['account'].message_type = _ACCOUNT
_LISTQUERYREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTQUERYREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTQUERYREQUEST.fields_by_name['extra'].message_type = _LISTQUERYREQUEST_EXTRAENTRY
_LISTITEMLOGQUERYRESPONSE.fields_by_name['account'].message_type = _ACCOUNT
_LISTITEMLOGQUERYRESPONSE.fields_by_name['data'].message_type = _INVENTORYLOG
_LISTLOGQUERYRESPONSE.fields_by_name['query'].message_type = _LISTQUERYREQUEST
_LISTLOGQUERYRESPONSE.fields_by_name['data'].message_type = _LISTITEMLOGQUERYRESPONSE
_LOGQUERYREQUEST.fields_by_name['account'].message_type = _ACCOUNT
_LOGQUERYREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LOGQUERYREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LOGQUERYRESPONSE.fields_by_name['account'].message_type = _ACCOUNT
_LOGQUERYRESPONSE.fields_by_name['detail'].message_type = _INVENTORYLOG
_FREEZEINFO.fields_by_name['amount'].message_type = _AMOUNT
_FREEZEINFO.fields_by_name['expired_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_FREEZEINFO.fields_by_name['status'].enum_type = _FREEZEINFO_STATUS
_FREEZEINFO_STATUS.containing_type = _FREEZEINFO
_FREEZEREQUEST.fields_by_name['detail'].message_type = _FREEZEITEM
_FREEZERESPONSE.fields_by_name['detail'].message_type = _FREEZEITEM
_FREEZEITEM.fields_by_name['freeze'].message_type = _ACCOUNT
_FREEZEITEM.fields_by_name['expired_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_FREEZEITEM.fields_by_name['status'].enum_type = _FREEZEITEM_STATUS
_FREEZEITEM_STATUS.containing_type = _FREEZEITEM
_FREEZEQUERYREQUEST.fields_by_name['account'].message_type = _ACCOUNT
_GROUPREQUEST_GROUPITEM.fields_by_name['action'].enum_type = _GROUPREQUEST_ACTION
_GROUPREQUEST_GROUPITEM.containing_type = _GROUPREQUEST
_GROUPREQUEST.fields_by_name['group_item'].message_type = _GROUPREQUEST_GROUPITEM
_GROUPREQUEST_ACTION.containing_type = _GROUPREQUEST
_QUERYGROUPBRANCHRESPONSE_GROUPITEM.containing_type = _QUERYGROUPBRANCHRESPONSE
_QUERYGROUPBRANCHRESPONSE.fields_by_name['group_item'].message_type = _QUERYGROUPBRANCHRESPONSE_GROUPITEM
_QUERYGROUPPRODUCTRESPONSE_PRODUCTINFO.containing_type = _QUERYGROUPPRODUCTRESPONSE
_QUERYGROUPPRODUCTRESPONSE.fields_by_name['product_info'].message_type = _QUERYGROUPPRODUCTRESPONSE_PRODUCTINFO
_QUERYSTOCKTAKEREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYSTOCKTAKEREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYSTOCKTAKERESPONSE_STOCKTAKEITEM.fields_by_name['account'].message_type = _ACCOUNT
_QUERYSTOCKTAKERESPONSE_STOCKTAKEITEM.fields_by_name['business_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYSTOCKTAKERESPONSE_STOCKTAKEITEM.containing_type = _QUERYSTOCKTAKERESPONSE
_QUERYSTOCKTAKERESPONSE.fields_by_name['stocktake_item'].message_type = _QUERYSTOCKTAKERESPONSE_STOCKTAKEITEM
_QUERYBRANCHINVENTORYSORTREQUEST.fields_by_name['query_type'].enum_type = _QUERYTYPE
_QUERYBRANCHINVENTORYSORTREQUEST.fields_by_name['page'].message_type = _PAGEORDER
_QUERYBRANCHINVENTORYSORTREQUEST.fields_by_name['use_flag'].enum_type = _USEFLAG
_QUERYBRANCHINVENTORYSORTRESPONSE_QUERYBRANCHINVENTORYSORT.containing_type = _QUERYBRANCHINVENTORYSORTRESPONSE
_QUERYBRANCHINVENTORYSORTRESPONSE.fields_by_name['rows'].message_type = _QUERYBRANCHINVENTORYSORTRESPONSE_QUERYBRANCHINVENTORYSORT
DESCRIPTOR.message_types_by_name['UnfreezeRequest'] = _UNFREEZEREQUEST
DESCRIPTOR.message_types_by_name['ListGroupSnapshotRequest'] = _LISTGROUPSNAPSHOTREQUEST
DESCRIPTOR.message_types_by_name['ListGroupSnapshotResponse'] = _LISTGROUPSNAPSHOTRESPONSE
DESCRIPTOR.message_types_by_name['GroupSnapShot'] = _GROUPSNAPSHOT
DESCRIPTOR.message_types_by_name['ListGroupEventLogRequest'] = _LISTGROUPEVENTLOGREQUEST
DESCRIPTOR.message_types_by_name['ListGroupEventLogResponse'] = _LISTGROUPEVENTLOGRESPONSE
DESCRIPTOR.message_types_by_name['GroupEventLog'] = _GROUPEVENTLOG
DESCRIPTOR.message_types_by_name['ListInventoryLogRequest'] = _LISTINVENTORYLOGREQUEST
DESCRIPTOR.message_types_by_name['ListInventoryLogResponse'] = _LISTINVENTORYLOGRESPONSE
DESCRIPTOR.message_types_by_name['Amount'] = _AMOUNT
DESCRIPTOR.message_types_by_name['Pagination'] = _PAGINATION
DESCRIPTOR.message_types_by_name['BatchOption'] = _BATCHOPTION
DESCRIPTOR.message_types_by_name['Account'] = _ACCOUNT
DESCRIPTOR.message_types_by_name['AccountingItem'] = _ACCOUNTINGITEM
DESCRIPTOR.message_types_by_name['TransferItem'] = _TRANSFERITEM
DESCRIPTOR.message_types_by_name['SnapshotItem'] = _SNAPSHOTITEM
DESCRIPTOR.message_types_by_name['StocktakeItem'] = _STOCKTAKEITEM
DESCRIPTOR.message_types_by_name['BatchRequest'] = _BATCHREQUEST
DESCRIPTOR.message_types_by_name['BatchDetailItem'] = _BATCHDETAILITEM
DESCRIPTOR.message_types_by_name['BatchResponse'] = _BATCHRESPONSE
DESCRIPTOR.message_types_by_name['BatchQuery'] = _BATCHQUERY
DESCRIPTOR.message_types_by_name['InventoryQueryRequest'] = _INVENTORYQUERYREQUEST
DESCRIPTOR.message_types_by_name['OrderBy'] = _ORDERBY
DESCRIPTOR.message_types_by_name['InventoryQueryLsitRequest'] = _INVENTORYQUERYLSITREQUEST
DESCRIPTOR.message_types_by_name['InventoryQueryResponse'] = _INVENTORYQUERYRESPONSE
DESCRIPTOR.message_types_by_name['SkuAccountRequest'] = _SKUACCOUNTREQUEST
DESCRIPTOR.message_types_by_name['SkuAccountResponse'] = _SKUACCOUNTRESPONSE
DESCRIPTOR.message_types_by_name['BatchQueryResponse'] = _BATCHQUERYRESPONSE
DESCRIPTOR.message_types_by_name['StockOption'] = _STOCKOPTION
DESCRIPTOR.message_types_by_name['StockInfo'] = _STOCKINFO
DESCRIPTOR.message_types_by_name['StockQueryRequest'] = _STOCKQUERYREQUEST
DESCRIPTOR.message_types_by_name['StockQueryResponse'] = _STOCKQUERYRESPONSE
DESCRIPTOR.message_types_by_name['Snapshot'] = _SNAPSHOT
DESCRIPTOR.message_types_by_name['SnapshotBatchQueryRequest'] = _SNAPSHOTBATCHQUERYREQUEST
DESCRIPTOR.message_types_by_name['SnapshotBatchQueryResponse'] = _SNAPSHOTBATCHQUERYRESPONSE
DESCRIPTOR.message_types_by_name['SumListQueryResponse'] = _SUMLISTQUERYRESPONSE
DESCRIPTOR.message_types_by_name['SnapshotSum'] = _SNAPSHOTSUM
DESCRIPTOR.message_types_by_name['SnapshotQueryRequest'] = _SNAPSHOTQUERYREQUEST
DESCRIPTOR.message_types_by_name['SnapshotQueryResponse'] = _SNAPSHOTQUERYRESPONSE
DESCRIPTOR.message_types_by_name['SnapshotStatItem'] = _SNAPSHOTSTATITEM
DESCRIPTOR.message_types_by_name['SnapshotStatRequest'] = _SNAPSHOTSTATREQUEST
DESCRIPTOR.message_types_by_name['SnapshotStatGroupRequest'] = _SNAPSHOTSTATGROUPREQUEST
DESCRIPTOR.message_types_by_name['SnapshotStatGroupResponse'] = _SNAPSHOTSTATGROUPRESPONSE
DESCRIPTOR.message_types_by_name['SnapshotStatGroupItem'] = _SNAPSHOTSTATGROUPITEM
DESCRIPTOR.message_types_by_name['SnapshotStatResponse'] = _SNAPSHOTSTATRESPONSE
DESCRIPTOR.message_types_by_name['ListGroupQueryRequest'] = _LISTGROUPQUERYREQUEST
DESCRIPTOR.message_types_by_name['HooksCreateRequest'] = _HOOKSCREATEREQUEST
DESCRIPTOR.message_types_by_name['HooksCreateResponse'] = _HOOKSCREATERESPONSE
DESCRIPTOR.message_types_by_name['HooksRequest'] = _HOOKSREQUEST
DESCRIPTOR.message_types_by_name['HooksResponse'] = _HOOKSRESPONSE
DESCRIPTOR.message_types_by_name['InventoryLog'] = _INVENTORYLOG
DESCRIPTOR.message_types_by_name['SumListQueryRequest'] = _SUMLISTQUERYREQUEST
DESCRIPTOR.message_types_by_name['QuerySnapshotStatRequest'] = _QUERYSNAPSHOTSTATREQUEST
DESCRIPTOR.message_types_by_name['QuerySnapshotStatResponse'] = _QUERYSNAPSHOTSTATRESPONSE
DESCRIPTOR.message_types_by_name['QuerySnapshotStat'] = _QUERYSNAPSHOTSTAT
DESCRIPTOR.message_types_by_name['ListQueryRequest'] = _LISTQUERYREQUEST
DESCRIPTOR.message_types_by_name['ListItemLogQueryResponse'] = _LISTITEMLOGQUERYRESPONSE
DESCRIPTOR.message_types_by_name['ListLogQueryResponse'] = _LISTLOGQUERYRESPONSE
DESCRIPTOR.message_types_by_name['LogQueryRequest'] = _LOGQUERYREQUEST
DESCRIPTOR.message_types_by_name['LogQueryResponse'] = _LOGQUERYRESPONSE
DESCRIPTOR.message_types_by_name['FreezeInfo'] = _FREEZEINFO
DESCRIPTOR.message_types_by_name['FreezeRequest'] = _FREEZEREQUEST
DESCRIPTOR.message_types_by_name['FreezeResponse'] = _FREEZERESPONSE
DESCRIPTOR.message_types_by_name['FreezeItem'] = _FREEZEITEM
DESCRIPTOR.message_types_by_name['FreezeQueryRequest'] = _FREEZEQUERYREQUEST
DESCRIPTOR.message_types_by_name['GroupRequest'] = _GROUPREQUEST
DESCRIPTOR.message_types_by_name['GroupResponse'] = _GROUPRESPONSE
DESCRIPTOR.message_types_by_name['QueryGroupBranchRequest'] = _QUERYGROUPBRANCHREQUEST
DESCRIPTOR.message_types_by_name['QueryGroupBranchResponse'] = _QUERYGROUPBRANCHRESPONSE
DESCRIPTOR.message_types_by_name['QueryGroupProductRequest'] = _QUERYGROUPPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['QueryGroupProductResponse'] = _QUERYGROUPPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['QueryStocktakeRequest'] = _QUERYSTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['QueryStocktakeResponse'] = _QUERYSTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['QueryBranchInventorySortRequest'] = _QUERYBRANCHINVENTORYSORTREQUEST
DESCRIPTOR.message_types_by_name['PageOrder'] = _PAGEORDER
DESCRIPTOR.message_types_by_name['QueryBranchInventorySortResponse'] = _QUERYBRANCHINVENTORYSORTRESPONSE
DESCRIPTOR.enum_types_by_name['AccoutingAction'] = _ACCOUTINGACTION
DESCRIPTOR.enum_types_by_name['BatchStatus'] = _BATCHSTATUS
DESCRIPTOR.enum_types_by_name['UseFlag'] = _USEFLAG
DESCRIPTOR.enum_types_by_name['QueryType'] = _QUERYTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

UnfreezeRequest = _reflection.GeneratedProtocolMessageType('UnfreezeRequest', (_message.Message,), dict(
  DESCRIPTOR = _UNFREEZEREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.UnfreezeRequest)
  ))
_sym_db.RegisterMessage(UnfreezeRequest)

ListGroupSnapshotRequest = _reflection.GeneratedProtocolMessageType('ListGroupSnapshotRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTGROUPSNAPSHOTREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListGroupSnapshotRequest)
  ))
_sym_db.RegisterMessage(ListGroupSnapshotRequest)

ListGroupSnapshotResponse = _reflection.GeneratedProtocolMessageType('ListGroupSnapshotResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTGROUPSNAPSHOTRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListGroupSnapshotResponse)
  ))
_sym_db.RegisterMessage(ListGroupSnapshotResponse)

GroupSnapShot = _reflection.GeneratedProtocolMessageType('GroupSnapShot', (_message.Message,), dict(

  GroupItem = _reflection.GeneratedProtocolMessageType('GroupItem', (_message.Message,), dict(
    DESCRIPTOR = _GROUPSNAPSHOT_GROUPITEM,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.GroupSnapShot.GroupItem)
    ))
  ,
  DESCRIPTOR = _GROUPSNAPSHOT,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.GroupSnapShot)
  ))
_sym_db.RegisterMessage(GroupSnapShot)
_sym_db.RegisterMessage(GroupSnapShot.GroupItem)

ListGroupEventLogRequest = _reflection.GeneratedProtocolMessageType('ListGroupEventLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTGROUPEVENTLOGREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListGroupEventLogRequest)
  ))
_sym_db.RegisterMessage(ListGroupEventLogRequest)

ListGroupEventLogResponse = _reflection.GeneratedProtocolMessageType('ListGroupEventLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTGROUPEVENTLOGRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListGroupEventLogResponse)
  ))
_sym_db.RegisterMessage(ListGroupEventLogResponse)

GroupEventLog = _reflection.GeneratedProtocolMessageType('GroupEventLog', (_message.Message,), dict(
  DESCRIPTOR = _GROUPEVENTLOG,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.GroupEventLog)
  ))
_sym_db.RegisterMessage(GroupEventLog)

ListInventoryLogRequest = _reflection.GeneratedProtocolMessageType('ListInventoryLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTINVENTORYLOGREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListInventoryLogRequest)
  ))
_sym_db.RegisterMessage(ListInventoryLogRequest)

ListInventoryLogResponse = _reflection.GeneratedProtocolMessageType('ListInventoryLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTINVENTORYLOGRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListInventoryLogResponse)
  ))
_sym_db.RegisterMessage(ListInventoryLogResponse)

Amount = _reflection.GeneratedProtocolMessageType('Amount', (_message.Message,), dict(

  ExtraEntry = _reflection.GeneratedProtocolMessageType('ExtraEntry', (_message.Message,), dict(
    DESCRIPTOR = _AMOUNT_EXTRAENTRY,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.Amount.ExtraEntry)
    ))
  ,
  DESCRIPTOR = _AMOUNT,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.Amount)
  ))
_sym_db.RegisterMessage(Amount)
_sym_db.RegisterMessage(Amount.ExtraEntry)

Pagination = _reflection.GeneratedProtocolMessageType('Pagination', (_message.Message,), dict(
  DESCRIPTOR = _PAGINATION,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.Pagination)
  ))
_sym_db.RegisterMessage(Pagination)

BatchOption = _reflection.GeneratedProtocolMessageType('BatchOption', (_message.Message,), dict(
  DESCRIPTOR = _BATCHOPTION,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.BatchOption)
  ))
_sym_db.RegisterMessage(BatchOption)

Account = _reflection.GeneratedProtocolMessageType('Account', (_message.Message,), dict(

  Extra = _reflection.GeneratedProtocolMessageType('Extra', (_message.Message,), dict(
    DESCRIPTOR = _ACCOUNT_EXTRA,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.Account.Extra)
    ))
  ,

  SubAccount = _reflection.GeneratedProtocolMessageType('SubAccount', (_message.Message,), dict(
    DESCRIPTOR = _ACCOUNT_SUBACCOUNT,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.Account.SubAccount)
    ))
  ,
  DESCRIPTOR = _ACCOUNT,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.Account)
  ))
_sym_db.RegisterMessage(Account)
_sym_db.RegisterMessage(Account.Extra)
_sym_db.RegisterMessage(Account.SubAccount)

AccountingItem = _reflection.GeneratedProtocolMessageType('AccountingItem', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNTINGITEM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.AccountingItem)
  ))
_sym_db.RegisterMessage(AccountingItem)

TransferItem = _reflection.GeneratedProtocolMessageType('TransferItem', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERITEM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.TransferItem)
  ))
_sym_db.RegisterMessage(TransferItem)

SnapshotItem = _reflection.GeneratedProtocolMessageType('SnapshotItem', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTITEM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotItem)
  ))
_sym_db.RegisterMessage(SnapshotItem)

StocktakeItem = _reflection.GeneratedProtocolMessageType('StocktakeItem', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEITEM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.StocktakeItem)
  ))
_sym_db.RegisterMessage(StocktakeItem)

BatchRequest = _reflection.GeneratedProtocolMessageType('BatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.BatchRequest)
  ))
_sym_db.RegisterMessage(BatchRequest)

BatchDetailItem = _reflection.GeneratedProtocolMessageType('BatchDetailItem', (_message.Message,), dict(
  DESCRIPTOR = _BATCHDETAILITEM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.BatchDetailItem)
  ))
_sym_db.RegisterMessage(BatchDetailItem)

BatchResponse = _reflection.GeneratedProtocolMessageType('BatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.BatchResponse)
  ))
_sym_db.RegisterMessage(BatchResponse)

BatchQuery = _reflection.GeneratedProtocolMessageType('BatchQuery', (_message.Message,), dict(

  BatchBase = _reflection.GeneratedProtocolMessageType('BatchBase', (_message.Message,), dict(
    DESCRIPTOR = _BATCHQUERY_BATCHBASE,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.BatchQuery.BatchBase)
    ))
  ,
  DESCRIPTOR = _BATCHQUERY,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.BatchQuery)
  ))
_sym_db.RegisterMessage(BatchQuery)
_sym_db.RegisterMessage(BatchQuery.BatchBase)

InventoryQueryRequest = _reflection.GeneratedProtocolMessageType('InventoryQueryRequest', (_message.Message,), dict(

  ExtraEntry = _reflection.GeneratedProtocolMessageType('ExtraEntry', (_message.Message,), dict(
    DESCRIPTOR = _INVENTORYQUERYREQUEST_EXTRAENTRY,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.InventoryQueryRequest.ExtraEntry)
    ))
  ,

  BranchIDs = _reflection.GeneratedProtocolMessageType('BranchIDs', (_message.Message,), dict(
    DESCRIPTOR = _INVENTORYQUERYREQUEST_BRANCHIDS,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.InventoryQueryRequest.BranchIDs)
    ))
  ,
  DESCRIPTOR = _INVENTORYQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.InventoryQueryRequest)
  ))
_sym_db.RegisterMessage(InventoryQueryRequest)
_sym_db.RegisterMessage(InventoryQueryRequest.ExtraEntry)
_sym_db.RegisterMessage(InventoryQueryRequest.BranchIDs)

OrderBy = _reflection.GeneratedProtocolMessageType('OrderBy', (_message.Message,), dict(
  DESCRIPTOR = _ORDERBY,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.OrderBy)
  ))
_sym_db.RegisterMessage(OrderBy)

InventoryQueryLsitRequest = _reflection.GeneratedProtocolMessageType('InventoryQueryLsitRequest', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORYQUERYLSITREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.InventoryQueryLsitRequest)
  ))
_sym_db.RegisterMessage(InventoryQueryLsitRequest)

InventoryQueryResponse = _reflection.GeneratedProtocolMessageType('InventoryQueryResponse', (_message.Message,), dict(

  ExtraEntry = _reflection.GeneratedProtocolMessageType('ExtraEntry', (_message.Message,), dict(
    DESCRIPTOR = _INVENTORYQUERYRESPONSE_EXTRAENTRY,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.InventoryQueryResponse.ExtraEntry)
    ))
  ,
  DESCRIPTOR = _INVENTORYQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.InventoryQueryResponse)
  ))
_sym_db.RegisterMessage(InventoryQueryResponse)
_sym_db.RegisterMessage(InventoryQueryResponse.ExtraEntry)

SkuAccountRequest = _reflection.GeneratedProtocolMessageType('SkuAccountRequest', (_message.Message,), dict(
  DESCRIPTOR = _SKUACCOUNTREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SkuAccountRequest)
  ))
_sym_db.RegisterMessage(SkuAccountRequest)

SkuAccountResponse = _reflection.GeneratedProtocolMessageType('SkuAccountResponse', (_message.Message,), dict(
  DESCRIPTOR = _SKUACCOUNTRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SkuAccountResponse)
  ))
_sym_db.RegisterMessage(SkuAccountResponse)

BatchQueryResponse = _reflection.GeneratedProtocolMessageType('BatchQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.BatchQueryResponse)
  ))
_sym_db.RegisterMessage(BatchQueryResponse)

StockOption = _reflection.GeneratedProtocolMessageType('StockOption', (_message.Message,), dict(
  DESCRIPTOR = _STOCKOPTION,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.StockOption)
  ))
_sym_db.RegisterMessage(StockOption)

StockInfo = _reflection.GeneratedProtocolMessageType('StockInfo', (_message.Message,), dict(

  Aggregate = _reflection.GeneratedProtocolMessageType('Aggregate', (_message.Message,), dict(
    DESCRIPTOR = _STOCKINFO_AGGREGATE,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.StockInfo.Aggregate)
    ))
  ,

  Snapshot = _reflection.GeneratedProtocolMessageType('Snapshot', (_message.Message,), dict(
    DESCRIPTOR = _STOCKINFO_SNAPSHOT,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.StockInfo.Snapshot)
    ))
  ,

  Detail = _reflection.GeneratedProtocolMessageType('Detail', (_message.Message,), dict(
    DESCRIPTOR = _STOCKINFO_DETAIL,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.StockInfo.Detail)
    ))
  ,
  DESCRIPTOR = _STOCKINFO,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.StockInfo)
  ))
_sym_db.RegisterMessage(StockInfo)
_sym_db.RegisterMessage(StockInfo.Aggregate)
_sym_db.RegisterMessage(StockInfo.Snapshot)
_sym_db.RegisterMessage(StockInfo.Detail)

StockQueryRequest = _reflection.GeneratedProtocolMessageType('StockQueryRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.StockQueryRequest)
  ))
_sym_db.RegisterMessage(StockQueryRequest)

StockQueryResponse = _reflection.GeneratedProtocolMessageType('StockQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.StockQueryResponse)
  ))
_sym_db.RegisterMessage(StockQueryResponse)

Snapshot = _reflection.GeneratedProtocolMessageType('Snapshot', (_message.Message,), dict(

  StatItem = _reflection.GeneratedProtocolMessageType('StatItem', (_message.Message,), dict(
    DESCRIPTOR = _SNAPSHOT_STATITEM,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.Snapshot.StatItem)
    ))
  ,

  Previous = _reflection.GeneratedProtocolMessageType('Previous', (_message.Message,), dict(
    DESCRIPTOR = _SNAPSHOT_PREVIOUS,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.Snapshot.Previous)
    ))
  ,
  DESCRIPTOR = _SNAPSHOT,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.Snapshot)
  ))
_sym_db.RegisterMessage(Snapshot)
_sym_db.RegisterMessage(Snapshot.StatItem)
_sym_db.RegisterMessage(Snapshot.Previous)

SnapshotBatchQueryRequest = _reflection.GeneratedProtocolMessageType('SnapshotBatchQueryRequest', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTBATCHQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotBatchQueryRequest)
  ))
_sym_db.RegisterMessage(SnapshotBatchQueryRequest)

SnapshotBatchQueryResponse = _reflection.GeneratedProtocolMessageType('SnapshotBatchQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTBATCHQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotBatchQueryResponse)
  ))
_sym_db.RegisterMessage(SnapshotBatchQueryResponse)

SumListQueryResponse = _reflection.GeneratedProtocolMessageType('SumListQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUMLISTQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SumListQueryResponse)
  ))
_sym_db.RegisterMessage(SumListQueryResponse)

SnapshotSum = _reflection.GeneratedProtocolMessageType('SnapshotSum', (_message.Message,), dict(

  StatItem = _reflection.GeneratedProtocolMessageType('StatItem', (_message.Message,), dict(
    DESCRIPTOR = _SNAPSHOTSUM_STATITEM,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.SnapshotSum.StatItem)
    ))
  ,
  DESCRIPTOR = _SNAPSHOTSUM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotSum)
  ))
_sym_db.RegisterMessage(SnapshotSum)
_sym_db.RegisterMessage(SnapshotSum.StatItem)

SnapshotQueryRequest = _reflection.GeneratedProtocolMessageType('SnapshotQueryRequest', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotQueryRequest)
  ))
_sym_db.RegisterMessage(SnapshotQueryRequest)

SnapshotQueryResponse = _reflection.GeneratedProtocolMessageType('SnapshotQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotQueryResponse)
  ))
_sym_db.RegisterMessage(SnapshotQueryResponse)

SnapshotStatItem = _reflection.GeneratedProtocolMessageType('SnapshotStatItem', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTSTATITEM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotStatItem)
  ))
_sym_db.RegisterMessage(SnapshotStatItem)

SnapshotStatRequest = _reflection.GeneratedProtocolMessageType('SnapshotStatRequest', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTSTATREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotStatRequest)
  ))
_sym_db.RegisterMessage(SnapshotStatRequest)

SnapshotStatGroupRequest = _reflection.GeneratedProtocolMessageType('SnapshotStatGroupRequest', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTSTATGROUPREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotStatGroupRequest)
  ))
_sym_db.RegisterMessage(SnapshotStatGroupRequest)

SnapshotStatGroupResponse = _reflection.GeneratedProtocolMessageType('SnapshotStatGroupResponse', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTSTATGROUPRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotStatGroupResponse)
  ))
_sym_db.RegisterMessage(SnapshotStatGroupResponse)

SnapshotStatGroupItem = _reflection.GeneratedProtocolMessageType('SnapshotStatGroupItem', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTSTATGROUPITEM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotStatGroupItem)
  ))
_sym_db.RegisterMessage(SnapshotStatGroupItem)

SnapshotStatResponse = _reflection.GeneratedProtocolMessageType('SnapshotStatResponse', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTSTATRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SnapshotStatResponse)
  ))
_sym_db.RegisterMessage(SnapshotStatResponse)

ListGroupQueryRequest = _reflection.GeneratedProtocolMessageType('ListGroupQueryRequest', (_message.Message,), dict(

  ExtraEntry = _reflection.GeneratedProtocolMessageType('ExtraEntry', (_message.Message,), dict(
    DESCRIPTOR = _LISTGROUPQUERYREQUEST_EXTRAENTRY,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.ListGroupQueryRequest.ExtraEntry)
    ))
  ,
  DESCRIPTOR = _LISTGROUPQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListGroupQueryRequest)
  ))
_sym_db.RegisterMessage(ListGroupQueryRequest)
_sym_db.RegisterMessage(ListGroupQueryRequest.ExtraEntry)

HooksCreateRequest = _reflection.GeneratedProtocolMessageType('HooksCreateRequest', (_message.Message,), dict(
  DESCRIPTOR = _HOOKSCREATEREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.HooksCreateRequest)
  ))
_sym_db.RegisterMessage(HooksCreateRequest)

HooksCreateResponse = _reflection.GeneratedProtocolMessageType('HooksCreateResponse', (_message.Message,), dict(
  DESCRIPTOR = _HOOKSCREATERESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.HooksCreateResponse)
  ))
_sym_db.RegisterMessage(HooksCreateResponse)

HooksRequest = _reflection.GeneratedProtocolMessageType('HooksRequest', (_message.Message,), dict(
  DESCRIPTOR = _HOOKSREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.HooksRequest)
  ))
_sym_db.RegisterMessage(HooksRequest)

HooksResponse = _reflection.GeneratedProtocolMessageType('HooksResponse', (_message.Message,), dict(

  Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), dict(
    DESCRIPTOR = _HOOKSRESPONSE_CONFIG,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.HooksResponse.Config)
    ))
  ,
  DESCRIPTOR = _HOOKSRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.HooksResponse)
  ))
_sym_db.RegisterMessage(HooksResponse)
_sym_db.RegisterMessage(HooksResponse.Config)

InventoryLog = _reflection.GeneratedProtocolMessageType('InventoryLog', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORYLOG,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.InventoryLog)
  ))
_sym_db.RegisterMessage(InventoryLog)

SumListQueryRequest = _reflection.GeneratedProtocolMessageType('SumListQueryRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUMLISTQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.SumListQueryRequest)
  ))
_sym_db.RegisterMessage(SumListQueryRequest)

QuerySnapshotStatRequest = _reflection.GeneratedProtocolMessageType('QuerySnapshotStatRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYSNAPSHOTSTATREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QuerySnapshotStatRequest)
  ))
_sym_db.RegisterMessage(QuerySnapshotStatRequest)

QuerySnapshotStatResponse = _reflection.GeneratedProtocolMessageType('QuerySnapshotStatResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYSNAPSHOTSTATRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QuerySnapshotStatResponse)
  ))
_sym_db.RegisterMessage(QuerySnapshotStatResponse)

QuerySnapshotStat = _reflection.GeneratedProtocolMessageType('QuerySnapshotStat', (_message.Message,), dict(
  DESCRIPTOR = _QUERYSNAPSHOTSTAT,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QuerySnapshotStat)
  ))
_sym_db.RegisterMessage(QuerySnapshotStat)

ListQueryRequest = _reflection.GeneratedProtocolMessageType('ListQueryRequest', (_message.Message,), dict(

  ExtraEntry = _reflection.GeneratedProtocolMessageType('ExtraEntry', (_message.Message,), dict(
    DESCRIPTOR = _LISTQUERYREQUEST_EXTRAENTRY,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.ListQueryRequest.ExtraEntry)
    ))
  ,
  DESCRIPTOR = _LISTQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListQueryRequest)
  ))
_sym_db.RegisterMessage(ListQueryRequest)
_sym_db.RegisterMessage(ListQueryRequest.ExtraEntry)

ListItemLogQueryResponse = _reflection.GeneratedProtocolMessageType('ListItemLogQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTITEMLOGQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListItemLogQueryResponse)
  ))
_sym_db.RegisterMessage(ListItemLogQueryResponse)

ListLogQueryResponse = _reflection.GeneratedProtocolMessageType('ListLogQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTLOGQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.ListLogQueryResponse)
  ))
_sym_db.RegisterMessage(ListLogQueryResponse)

LogQueryRequest = _reflection.GeneratedProtocolMessageType('LogQueryRequest', (_message.Message,), dict(
  DESCRIPTOR = _LOGQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.LogQueryRequest)
  ))
_sym_db.RegisterMessage(LogQueryRequest)

LogQueryResponse = _reflection.GeneratedProtocolMessageType('LogQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _LOGQUERYRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.LogQueryResponse)
  ))
_sym_db.RegisterMessage(LogQueryResponse)

FreezeInfo = _reflection.GeneratedProtocolMessageType('FreezeInfo', (_message.Message,), dict(
  DESCRIPTOR = _FREEZEINFO,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.FreezeInfo)
  ))
_sym_db.RegisterMessage(FreezeInfo)

FreezeRequest = _reflection.GeneratedProtocolMessageType('FreezeRequest', (_message.Message,), dict(
  DESCRIPTOR = _FREEZEREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.FreezeRequest)
  ))
_sym_db.RegisterMessage(FreezeRequest)

FreezeResponse = _reflection.GeneratedProtocolMessageType('FreezeResponse', (_message.Message,), dict(
  DESCRIPTOR = _FREEZERESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.FreezeResponse)
  ))
_sym_db.RegisterMessage(FreezeResponse)

FreezeItem = _reflection.GeneratedProtocolMessageType('FreezeItem', (_message.Message,), dict(
  DESCRIPTOR = _FREEZEITEM,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.FreezeItem)
  ))
_sym_db.RegisterMessage(FreezeItem)

FreezeQueryRequest = _reflection.GeneratedProtocolMessageType('FreezeQueryRequest', (_message.Message,), dict(
  DESCRIPTOR = _FREEZEQUERYREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.FreezeQueryRequest)
  ))
_sym_db.RegisterMessage(FreezeQueryRequest)

GroupRequest = _reflection.GeneratedProtocolMessageType('GroupRequest', (_message.Message,), dict(

  GroupItem = _reflection.GeneratedProtocolMessageType('GroupItem', (_message.Message,), dict(
    DESCRIPTOR = _GROUPREQUEST_GROUPITEM,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.GroupRequest.GroupItem)
    ))
  ,
  DESCRIPTOR = _GROUPREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.GroupRequest)
  ))
_sym_db.RegisterMessage(GroupRequest)
_sym_db.RegisterMessage(GroupRequest.GroupItem)

GroupResponse = _reflection.GeneratedProtocolMessageType('GroupResponse', (_message.Message,), dict(
  DESCRIPTOR = _GROUPRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.GroupResponse)
  ))
_sym_db.RegisterMessage(GroupResponse)

QueryGroupBranchRequest = _reflection.GeneratedProtocolMessageType('QueryGroupBranchRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYGROUPBRANCHREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QueryGroupBranchRequest)
  ))
_sym_db.RegisterMessage(QueryGroupBranchRequest)

QueryGroupBranchResponse = _reflection.GeneratedProtocolMessageType('QueryGroupBranchResponse', (_message.Message,), dict(

  GroupItem = _reflection.GeneratedProtocolMessageType('GroupItem', (_message.Message,), dict(
    DESCRIPTOR = _QUERYGROUPBRANCHRESPONSE_GROUPITEM,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.QueryGroupBranchResponse.GroupItem)
    ))
  ,
  DESCRIPTOR = _QUERYGROUPBRANCHRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QueryGroupBranchResponse)
  ))
_sym_db.RegisterMessage(QueryGroupBranchResponse)
_sym_db.RegisterMessage(QueryGroupBranchResponse.GroupItem)

QueryGroupProductRequest = _reflection.GeneratedProtocolMessageType('QueryGroupProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYGROUPPRODUCTREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QueryGroupProductRequest)
  ))
_sym_db.RegisterMessage(QueryGroupProductRequest)

QueryGroupProductResponse = _reflection.GeneratedProtocolMessageType('QueryGroupProductResponse', (_message.Message,), dict(

  ProductInfo = _reflection.GeneratedProtocolMessageType('ProductInfo', (_message.Message,), dict(
    DESCRIPTOR = _QUERYGROUPPRODUCTRESPONSE_PRODUCTINFO,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.QueryGroupProductResponse.ProductInfo)
    ))
  ,
  DESCRIPTOR = _QUERYGROUPPRODUCTRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QueryGroupProductResponse)
  ))
_sym_db.RegisterMessage(QueryGroupProductResponse)
_sym_db.RegisterMessage(QueryGroupProductResponse.ProductInfo)

QueryStocktakeRequest = _reflection.GeneratedProtocolMessageType('QueryStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYSTOCKTAKEREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QueryStocktakeRequest)
  ))
_sym_db.RegisterMessage(QueryStocktakeRequest)

QueryStocktakeResponse = _reflection.GeneratedProtocolMessageType('QueryStocktakeResponse', (_message.Message,), dict(

  StocktakeItem = _reflection.GeneratedProtocolMessageType('StocktakeItem', (_message.Message,), dict(
    DESCRIPTOR = _QUERYSTOCKTAKERESPONSE_STOCKTAKEITEM,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.QueryStocktakeResponse.StocktakeItem)
    ))
  ,
  DESCRIPTOR = _QUERYSTOCKTAKERESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QueryStocktakeResponse)
  ))
_sym_db.RegisterMessage(QueryStocktakeResponse)
_sym_db.RegisterMessage(QueryStocktakeResponse.StocktakeItem)

QueryBranchInventorySortRequest = _reflection.GeneratedProtocolMessageType('QueryBranchInventorySortRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYBRANCHINVENTORYSORTREQUEST,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QueryBranchInventorySortRequest)
  ))
_sym_db.RegisterMessage(QueryBranchInventorySortRequest)

PageOrder = _reflection.GeneratedProtocolMessageType('PageOrder', (_message.Message,), dict(
  DESCRIPTOR = _PAGEORDER,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.PageOrder)
  ))
_sym_db.RegisterMessage(PageOrder)

QueryBranchInventorySortResponse = _reflection.GeneratedProtocolMessageType('QueryBranchInventorySortResponse', (_message.Message,), dict(

  QueryBranchInventorySort = _reflection.GeneratedProtocolMessageType('QueryBranchInventorySort', (_message.Message,), dict(
    DESCRIPTOR = _QUERYBRANCHINVENTORYSORTRESPONSE_QUERYBRANCHINVENTORYSORT,
    __module__ = 'inventory.inventory_pb2'
    # @@protoc_insertion_point(class_scope:inventory.QueryBranchInventorySortResponse.QueryBranchInventorySort)
    ))
  ,
  DESCRIPTOR = _QUERYBRANCHINVENTORYSORTRESPONSE,
  __module__ = 'inventory.inventory_pb2'
  # @@protoc_insertion_point(class_scope:inventory.QueryBranchInventorySortResponse)
  ))
_sym_db.RegisterMessage(QueryBranchInventorySortResponse)
_sym_db.RegisterMessage(QueryBranchInventorySortResponse.QueryBranchInventorySort)


DESCRIPTOR._options = None
_AMOUNT_EXTRAENTRY._options = None
_INVENTORYQUERYREQUEST_EXTRAENTRY._options = None
_INVENTORYQUERYRESPONSE_EXTRAENTRY._options = None
_LISTGROUPQUERYREQUEST_EXTRAENTRY._options = None
_LISTQUERYREQUEST_EXTRAENTRY._options = None

_INVENTORYSERVICE = _descriptor.ServiceDescriptor(
  name='InventoryService',
  full_name='inventory.InventoryService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=14040,
  serialized_end=18155,
  methods=[
  _descriptor.MethodDescriptor(
    name='Account',
    full_name='inventory.InventoryService.Account',
    index=0,
    containing_service=None,
    input_type=_BATCHREQUEST,
    output_type=_BATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002!\"\034/api/v1/inventory/accounting:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='Transfer',
    full_name='inventory.InventoryService.Transfer',
    index=1,
    containing_service=None,
    input_type=_BATCHREQUEST,
    output_type=_BATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v1/inventory/transfer:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='TransferCommit',
    full_name='inventory.InventoryService.TransferCommit',
    index=2,
    containing_service=None,
    input_type=_BATCHQUERY,
    output_type=_BATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v1/inventory/transfer/{id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='Batch',
    full_name='inventory.InventoryService.Batch',
    index=3,
    containing_service=None,
    input_type=_BATCHREQUEST,
    output_type=_BATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v1/inventory/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryAccouting',
    full_name='inventory.InventoryService.QueryAccouting',
    index=4,
    containing_service=None,
    input_type=_BATCHQUERY,
    output_type=_BATCHQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v1/inventory/accounting/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryTransfer',
    full_name='inventory.InventoryService.QueryTransfer',
    index=5,
    containing_service=None,
    input_type=_BATCHQUERY,
    output_type=_BATCHQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002!\022\037/api/v1/inventory/transfer/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryBatch',
    full_name='inventory.InventoryService.QueryBatch',
    index=6,
    containing_service=None,
    input_type=_BATCHQUERY,
    output_type=_BATCHQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v1/inventory/batch/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryBatchParameter',
    full_name='inventory.InventoryService.QueryBatchParameter',
    index=7,
    containing_service=None,
    input_type=_BATCHQUERY,
    output_type=_BATCHQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v1/inventory/batch/parameter'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryStockBatch',
    full_name='inventory.InventoryService.QueryStockBatch',
    index=8,
    containing_service=None,
    input_type=_STOCKQUERYREQUEST,
    output_type=_STOCKQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v1/inventory/stock:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='EnsureSkuAccount',
    full_name='inventory.InventoryService.EnsureSkuAccount',
    index=9,
    containing_service=None,
    input_type=_SKUACCOUNTREQUEST,
    output_type=_SKUACCOUNTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v1/inventory/sku/ensure'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryBranchInventory',
    full_name='inventory.InventoryService.QueryBranchInventory',
    index=10,
    containing_service=None,
    input_type=_INVENTORYQUERYREQUEST,
    output_type=_INVENTORYQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002\035\"\030/api/v1/inventory/branch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryAccountInventoryList',
    full_name='inventory.InventoryService.QueryAccountInventoryList',
    index=11,
    containing_service=None,
    input_type=_INVENTORYQUERYLSITREQUEST,
    output_type=_INVENTORYQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v1/inventory/accounts:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryLatestInventoryByBatch',
    full_name='inventory.InventoryService.QueryLatestInventoryByBatch',
    index=12,
    containing_service=None,
    input_type=_BATCHQUERY,
    output_type=_INVENTORYQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v1/inventory/batch/{id}/latest'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryHooks',
    full_name='inventory.InventoryService.QueryHooks',
    index=13,
    containing_service=None,
    input_type=_HOOKSREQUEST,
    output_type=_HOOKSRESPONSE,
    serialized_options=_b('\202\323\344\223\002\031\022\027/api/v1/inventory/hooks'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateHook',
    full_name='inventory.InventoryService.CreateHook',
    index=14,
    containing_service=None,
    input_type=_HOOKSCREATEREQUEST,
    output_type=_HOOKSCREATERESPONSE,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v1/inventory/hooks:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='Freeze',
    full_name='inventory.InventoryService.Freeze',
    index=15,
    containing_service=None,
    input_type=_FREEZEREQUEST,
    output_type=_FREEZERESPONSE,
    serialized_options=_b('\202\323\344\223\002\035\"\030/api/v1/inventory/freeze:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='Unfreeze',
    full_name='inventory.InventoryService.Unfreeze',
    index=16,
    containing_service=None,
    input_type=_UNFREEZEREQUEST,
    output_type=_FREEZERESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v1/inventory/unfreeze:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryFreezeList',
    full_name='inventory.InventoryService.QueryFreezeList',
    index=17,
    containing_service=None,
    input_type=_FREEZEQUERYREQUEST,
    output_type=_FREEZERESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v1/inventory/freeze/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryLogList',
    full_name='inventory.InventoryService.QueryLogList',
    index=18,
    containing_service=None,
    input_type=_LISTQUERYREQUEST,
    output_type=_LISTLOGQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \"\033/api/v1/inventory/log/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QuerySnapshot',
    full_name='inventory.InventoryService.QuerySnapshot',
    index=19,
    containing_service=None,
    input_type=_SNAPSHOTQUERYREQUEST,
    output_type=_SNAPSHOTQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v1/inventory/snapshot/single:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QuerySnapshotList',
    full_name='inventory.InventoryService.QuerySnapshotList',
    index=20,
    containing_service=None,
    input_type=_LISTQUERYREQUEST,
    output_type=_SNAPSHOTBATCHQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\" /api/v1/inventory/snapshot/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QuerySnapshotSumList',
    full_name='inventory.InventoryService.QuerySnapshotSumList',
    index=21,
    containing_service=None,
    input_type=_SUMLISTQUERYREQUEST,
    output_type=_SUMLISTQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v1/inventory/snapshot/query/sum:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryGroupSnapshotList',
    full_name='inventory.InventoryService.QueryGroupSnapshotList',
    index=22,
    containing_service=None,
    input_type=_LISTGROUPQUERYREQUEST,
    output_type=_SNAPSHOTBATCHQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\"%/api/v1/inventory/snapshot/querygroup:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QuerySnapshotStats',
    full_name='inventory.InventoryService.QuerySnapshotStats',
    index=23,
    containing_service=None,
    input_type=_SNAPSHOTSTATREQUEST,
    output_type=_SNAPSHOTSTATRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\"%/api/v1/inventory/snapshot/querystats:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QuerySnapshotStatGroup',
    full_name='inventory.InventoryService.QuerySnapshotStatGroup',
    index=24,
    containing_service=None,
    input_type=_SNAPSHOTSTATGROUPREQUEST,
    output_type=_SNAPSHOTSTATGROUPRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v1/inventory/snapshot/stat:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListInventoryLog',
    full_name='inventory.InventoryService.ListInventoryLog',
    index=25,
    containing_service=None,
    input_type=_LISTINVENTORYLOGREQUEST,
    output_type=_LISTINVENTORYLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v1/inventory/log/list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListGroupEventLog',
    full_name='inventory.InventoryService.ListGroupEventLog',
    index=26,
    containing_service=None,
    input_type=_LISTGROUPEVENTLOGREQUEST,
    output_type=_LISTGROUPEVENTLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \"\033/api/v1/inventory/log/event:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListGroupSnapshot',
    full_name='inventory.InventoryService.ListGroupSnapshot',
    index=27,
    containing_service=None,
    input_type=_LISTGROUPSNAPSHOTREQUEST,
    output_type=_LISTGROUPSNAPSHOTRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\" /api/v1/inventory/snapshot/group:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='Group',
    full_name='inventory.InventoryService.Group',
    index=28,
    containing_service=None,
    input_type=_GROUPREQUEST,
    output_type=_GROUPRESPONSE,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v1/inventory/group:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryGroupBranch',
    full_name='inventory.InventoryService.QueryGroupBranch',
    index=29,
    containing_service=None,
    input_type=_QUERYGROUPBRANCHREQUEST,
    output_type=_QUERYGROUPBRANCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\"\035/api/v1/inventory/group/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryGroupProduct',
    full_name='inventory.InventoryService.QueryGroupProduct',
    index=30,
    containing_service=None,
    input_type=_QUERYGROUPPRODUCTREQUEST,
    output_type=_QUERYGROUPPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v1/inventory/group/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryStocktake',
    full_name='inventory.InventoryService.QueryStocktake',
    index=31,
    containing_service=None,
    input_type=_QUERYSTOCKTAKEREQUEST,
    output_type=_QUERYSTOCKTAKERESPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v1/inventory/stocktake/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryBranchInventorySort',
    full_name='inventory.InventoryService.QueryBranchInventorySort',
    index=32,
    containing_service=None,
    input_type=_QUERYBRANCHINVENTORYSORTREQUEST,
    output_type=_QUERYBRANCHINVENTORYSORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v1/inventory/branch-inventory-sort/query:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_INVENTORYSERVICE)

DESCRIPTOR.services_by_name['InventoryService'] = _INVENTORYSERVICE

# @@protoc_insertion_point(module_scope)
