# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from inventory import inventory_pb2 as inventory_dot_inventory__pb2


class InventoryServiceStub(object):
  """*
  库存管理服务

  提供几个关键的服务
  1. 库存调整，包括增加、减少和冻结
  2. 统一的批次管理
  3. 查询服务

  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Account = channel.unary_unary(
        '/inventory.InventoryService/Account',
        request_serializer=inventory_dot_inventory__pb2.BatchRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.BatchResponse.FromString,
        )
    self.Transfer = channel.unary_unary(
        '/inventory.InventoryService/Transfer',
        request_serializer=inventory_dot_inventory__pb2.BatchRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.BatchResponse.FromString,
        )
    self.TransferCommit = channel.unary_unary(
        '/inventory.InventoryService/TransferCommit',
        request_serializer=inventory_dot_inventory__pb2.BatchQuery.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.BatchResponse.FromString,
        )
    self.Batch = channel.unary_unary(
        '/inventory.InventoryService/Batch',
        request_serializer=inventory_dot_inventory__pb2.BatchRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.BatchResponse.FromString,
        )
    self.QueryAccouting = channel.unary_unary(
        '/inventory.InventoryService/QueryAccouting',
        request_serializer=inventory_dot_inventory__pb2.BatchQuery.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.BatchQueryResponse.FromString,
        )
    self.QueryTransfer = channel.unary_unary(
        '/inventory.InventoryService/QueryTransfer',
        request_serializer=inventory_dot_inventory__pb2.BatchQuery.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.BatchQueryResponse.FromString,
        )
    self.QueryBatch = channel.unary_unary(
        '/inventory.InventoryService/QueryBatch',
        request_serializer=inventory_dot_inventory__pb2.BatchQuery.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.BatchQueryResponse.FromString,
        )
    self.QueryBatchParameter = channel.unary_unary(
        '/inventory.InventoryService/QueryBatchParameter',
        request_serializer=inventory_dot_inventory__pb2.BatchQuery.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.BatchQueryResponse.FromString,
        )
    self.QueryStockBatch = channel.unary_unary(
        '/inventory.InventoryService/QueryStockBatch',
        request_serializer=inventory_dot_inventory__pb2.StockQueryRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.StockQueryResponse.FromString,
        )
    self.EnsureSkuAccount = channel.unary_unary(
        '/inventory.InventoryService/EnsureSkuAccount',
        request_serializer=inventory_dot_inventory__pb2.SkuAccountRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.SkuAccountResponse.FromString,
        )
    self.QueryBranchInventory = channel.unary_unary(
        '/inventory.InventoryService/QueryBranchInventory',
        request_serializer=inventory_dot_inventory__pb2.InventoryQueryRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.InventoryQueryResponse.FromString,
        )
    self.QueryAccountInventoryList = channel.unary_unary(
        '/inventory.InventoryService/QueryAccountInventoryList',
        request_serializer=inventory_dot_inventory__pb2.InventoryQueryLsitRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.InventoryQueryResponse.FromString,
        )
    self.QueryLatestInventoryByBatch = channel.unary_unary(
        '/inventory.InventoryService/QueryLatestInventoryByBatch',
        request_serializer=inventory_dot_inventory__pb2.BatchQuery.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.InventoryQueryResponse.FromString,
        )
    self.QueryHooks = channel.unary_unary(
        '/inventory.InventoryService/QueryHooks',
        request_serializer=inventory_dot_inventory__pb2.HooksRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.HooksResponse.FromString,
        )
    self.CreateHook = channel.unary_unary(
        '/inventory.InventoryService/CreateHook',
        request_serializer=inventory_dot_inventory__pb2.HooksCreateRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.HooksCreateResponse.FromString,
        )
    self.Freeze = channel.unary_unary(
        '/inventory.InventoryService/Freeze',
        request_serializer=inventory_dot_inventory__pb2.FreezeRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.FreezeResponse.FromString,
        )
    self.Unfreeze = channel.unary_unary(
        '/inventory.InventoryService/Unfreeze',
        request_serializer=inventory_dot_inventory__pb2.UnfreezeRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.FreezeResponse.FromString,
        )
    self.QueryFreezeList = channel.unary_unary(
        '/inventory.InventoryService/QueryFreezeList',
        request_serializer=inventory_dot_inventory__pb2.FreezeQueryRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.FreezeResponse.FromString,
        )
    self.QueryLogList = channel.unary_unary(
        '/inventory.InventoryService/QueryLogList',
        request_serializer=inventory_dot_inventory__pb2.ListQueryRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.ListLogQueryResponse.FromString,
        )
    self.QuerySnapshot = channel.unary_unary(
        '/inventory.InventoryService/QuerySnapshot',
        request_serializer=inventory_dot_inventory__pb2.SnapshotQueryRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.SnapshotQueryResponse.FromString,
        )
    self.QuerySnapshotList = channel.unary_unary(
        '/inventory.InventoryService/QuerySnapshotList',
        request_serializer=inventory_dot_inventory__pb2.ListQueryRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.SnapshotBatchQueryResponse.FromString,
        )
    self.QuerySnapshotSumList = channel.unary_unary(
        '/inventory.InventoryService/QuerySnapshotSumList',
        request_serializer=inventory_dot_inventory__pb2.SumListQueryRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.SumListQueryResponse.FromString,
        )
    self.QueryGroupSnapshotList = channel.unary_unary(
        '/inventory.InventoryService/QueryGroupSnapshotList',
        request_serializer=inventory_dot_inventory__pb2.ListGroupQueryRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.SnapshotBatchQueryResponse.FromString,
        )
    self.QuerySnapshotStats = channel.unary_unary(
        '/inventory.InventoryService/QuerySnapshotStats',
        request_serializer=inventory_dot_inventory__pb2.SnapshotStatRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.SnapshotStatResponse.FromString,
        )
    self.QuerySnapshotStatGroup = channel.unary_unary(
        '/inventory.InventoryService/QuerySnapshotStatGroup',
        request_serializer=inventory_dot_inventory__pb2.SnapshotStatGroupRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.SnapshotStatGroupResponse.FromString,
        )
    self.ListInventoryLog = channel.unary_unary(
        '/inventory.InventoryService/ListInventoryLog',
        request_serializer=inventory_dot_inventory__pb2.ListInventoryLogRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.ListInventoryLogResponse.FromString,
        )
    self.ListGroupEventLog = channel.unary_unary(
        '/inventory.InventoryService/ListGroupEventLog',
        request_serializer=inventory_dot_inventory__pb2.ListGroupEventLogRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.ListGroupEventLogResponse.FromString,
        )
    self.ListGroupSnapshot = channel.unary_unary(
        '/inventory.InventoryService/ListGroupSnapshot',
        request_serializer=inventory_dot_inventory__pb2.ListGroupSnapshotRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.ListGroupSnapshotResponse.FromString,
        )
    self.Group = channel.unary_unary(
        '/inventory.InventoryService/Group',
        request_serializer=inventory_dot_inventory__pb2.GroupRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.GroupResponse.FromString,
        )
    self.QueryGroupBranch = channel.unary_unary(
        '/inventory.InventoryService/QueryGroupBranch',
        request_serializer=inventory_dot_inventory__pb2.QueryGroupBranchRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.QueryGroupBranchResponse.FromString,
        )
    self.QueryGroupProduct = channel.unary_unary(
        '/inventory.InventoryService/QueryGroupProduct',
        request_serializer=inventory_dot_inventory__pb2.QueryGroupProductRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.QueryGroupProductResponse.FromString,
        )
    self.QueryStocktake = channel.unary_unary(
        '/inventory.InventoryService/QueryStocktake',
        request_serializer=inventory_dot_inventory__pb2.QueryStocktakeRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.QueryStocktakeResponse.FromString,
        )
    self.QueryBranchInventorySort = channel.unary_unary(
        '/inventory.InventoryService/QueryBranchInventorySort',
        request_serializer=inventory_dot_inventory__pb2.QueryBranchInventorySortRequest.SerializeToString,
        response_deserializer=inventory_dot_inventory__pb2.QueryBranchInventorySortResponse.FromString,
        )


class InventoryServiceServicer(object):
  """*
  库存管理服务

  提供几个关键的服务
  1. 库存调整，包括增加、减少和冻结
  2. 统一的批次管理
  3. 查询服务

  """

  def Account(self, request, context):
    """提交一笔库存记录服务
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Transfer(self, request, context):
    """库存转移
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def TransferCommit(self, request, context):
    """库存转移提交
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Batch(self, request, context):
    """库存批处理请求，可以视为唯一的入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryAccouting(self, request, context):
    """查询库存记录情况
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryTransfer(self, request, context):
    """查询单笔库存调拨
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryBatch(self, request, context):
    """查询库存批次请求的处理结果
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryBatchParameter(self, request, context):
    """查询库存批次请求的处理结果
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryStockBatch(self, request, context):
    """批次查询库存明细信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def EnsureSkuAccount(self, request, context):
    """确认库存账号，如果没有，会自动创建
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryBranchInventory(self, request, context):
    """按照BranchID查询所有的商品实时库存，支持多Branch和多Product条件查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryAccountInventoryList(self, request, context):
    """批量查询Account的实时库存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryLatestInventoryByBatch(self, request, context):
    """按照批次查询这个批次商品的实时库存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryHooks(self, request, context):
    """查询库存异动
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateHook(self, request, context):
    """创建一个库存异动通知
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Freeze(self, request, context):
    """实时调整冻结库存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Unfreeze(self, request, context):
    """释放冻结的库存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryFreezeList(self, request, context):
    """查询冻结数据
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryLogList(self, request, context):
    """查询库存流水【弃用】
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QuerySnapshot(self, request, context):
    """查询单一商品的库存快照【功能未实现】
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QuerySnapshotList(self, request, context):
    """批量查询库存快照数据
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QuerySnapshotSumList(self, request, context):
    """时段汇总
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryGroupSnapshotList(self, request, context):
    """批量查询库存快照数据
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QuerySnapshotStats(self, request, context):
    """批量查询库存快照统计数据
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QuerySnapshotStatGroup(self, request, context):
    """查询branch里 有流水的商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListInventoryLog(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListGroupEventLog(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListGroupSnapshot(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Group(self, request, context):
    """门店群组增删改，例如成本中心
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryGroupBranch(self, request, context):
    """门店群组查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryGroupProduct(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryStocktake(self, request, context):
    """盘点单查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryBranchInventorySort(self, request, context):
    """商品实时库存查询并排序
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_InventoryServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Account': grpc.unary_unary_rpc_method_handler(
          servicer.Account,
          request_deserializer=inventory_dot_inventory__pb2.BatchRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.BatchResponse.SerializeToString,
      ),
      'Transfer': grpc.unary_unary_rpc_method_handler(
          servicer.Transfer,
          request_deserializer=inventory_dot_inventory__pb2.BatchRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.BatchResponse.SerializeToString,
      ),
      'TransferCommit': grpc.unary_unary_rpc_method_handler(
          servicer.TransferCommit,
          request_deserializer=inventory_dot_inventory__pb2.BatchQuery.FromString,
          response_serializer=inventory_dot_inventory__pb2.BatchResponse.SerializeToString,
      ),
      'Batch': grpc.unary_unary_rpc_method_handler(
          servicer.Batch,
          request_deserializer=inventory_dot_inventory__pb2.BatchRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.BatchResponse.SerializeToString,
      ),
      'QueryAccouting': grpc.unary_unary_rpc_method_handler(
          servicer.QueryAccouting,
          request_deserializer=inventory_dot_inventory__pb2.BatchQuery.FromString,
          response_serializer=inventory_dot_inventory__pb2.BatchQueryResponse.SerializeToString,
      ),
      'QueryTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.QueryTransfer,
          request_deserializer=inventory_dot_inventory__pb2.BatchQuery.FromString,
          response_serializer=inventory_dot_inventory__pb2.BatchQueryResponse.SerializeToString,
      ),
      'QueryBatch': grpc.unary_unary_rpc_method_handler(
          servicer.QueryBatch,
          request_deserializer=inventory_dot_inventory__pb2.BatchQuery.FromString,
          response_serializer=inventory_dot_inventory__pb2.BatchQueryResponse.SerializeToString,
      ),
      'QueryBatchParameter': grpc.unary_unary_rpc_method_handler(
          servicer.QueryBatchParameter,
          request_deserializer=inventory_dot_inventory__pb2.BatchQuery.FromString,
          response_serializer=inventory_dot_inventory__pb2.BatchQueryResponse.SerializeToString,
      ),
      'QueryStockBatch': grpc.unary_unary_rpc_method_handler(
          servicer.QueryStockBatch,
          request_deserializer=inventory_dot_inventory__pb2.StockQueryRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.StockQueryResponse.SerializeToString,
      ),
      'EnsureSkuAccount': grpc.unary_unary_rpc_method_handler(
          servicer.EnsureSkuAccount,
          request_deserializer=inventory_dot_inventory__pb2.SkuAccountRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.SkuAccountResponse.SerializeToString,
      ),
      'QueryBranchInventory': grpc.unary_unary_rpc_method_handler(
          servicer.QueryBranchInventory,
          request_deserializer=inventory_dot_inventory__pb2.InventoryQueryRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.InventoryQueryResponse.SerializeToString,
      ),
      'QueryAccountInventoryList': grpc.unary_unary_rpc_method_handler(
          servicer.QueryAccountInventoryList,
          request_deserializer=inventory_dot_inventory__pb2.InventoryQueryLsitRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.InventoryQueryResponse.SerializeToString,
      ),
      'QueryLatestInventoryByBatch': grpc.unary_unary_rpc_method_handler(
          servicer.QueryLatestInventoryByBatch,
          request_deserializer=inventory_dot_inventory__pb2.BatchQuery.FromString,
          response_serializer=inventory_dot_inventory__pb2.InventoryQueryResponse.SerializeToString,
      ),
      'QueryHooks': grpc.unary_unary_rpc_method_handler(
          servicer.QueryHooks,
          request_deserializer=inventory_dot_inventory__pb2.HooksRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.HooksResponse.SerializeToString,
      ),
      'CreateHook': grpc.unary_unary_rpc_method_handler(
          servicer.CreateHook,
          request_deserializer=inventory_dot_inventory__pb2.HooksCreateRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.HooksCreateResponse.SerializeToString,
      ),
      'Freeze': grpc.unary_unary_rpc_method_handler(
          servicer.Freeze,
          request_deserializer=inventory_dot_inventory__pb2.FreezeRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.FreezeResponse.SerializeToString,
      ),
      'Unfreeze': grpc.unary_unary_rpc_method_handler(
          servicer.Unfreeze,
          request_deserializer=inventory_dot_inventory__pb2.UnfreezeRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.FreezeResponse.SerializeToString,
      ),
      'QueryFreezeList': grpc.unary_unary_rpc_method_handler(
          servicer.QueryFreezeList,
          request_deserializer=inventory_dot_inventory__pb2.FreezeQueryRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.FreezeResponse.SerializeToString,
      ),
      'QueryLogList': grpc.unary_unary_rpc_method_handler(
          servicer.QueryLogList,
          request_deserializer=inventory_dot_inventory__pb2.ListQueryRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.ListLogQueryResponse.SerializeToString,
      ),
      'QuerySnapshot': grpc.unary_unary_rpc_method_handler(
          servicer.QuerySnapshot,
          request_deserializer=inventory_dot_inventory__pb2.SnapshotQueryRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.SnapshotQueryResponse.SerializeToString,
      ),
      'QuerySnapshotList': grpc.unary_unary_rpc_method_handler(
          servicer.QuerySnapshotList,
          request_deserializer=inventory_dot_inventory__pb2.ListQueryRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.SnapshotBatchQueryResponse.SerializeToString,
      ),
      'QuerySnapshotSumList': grpc.unary_unary_rpc_method_handler(
          servicer.QuerySnapshotSumList,
          request_deserializer=inventory_dot_inventory__pb2.SumListQueryRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.SumListQueryResponse.SerializeToString,
      ),
      'QueryGroupSnapshotList': grpc.unary_unary_rpc_method_handler(
          servicer.QueryGroupSnapshotList,
          request_deserializer=inventory_dot_inventory__pb2.ListGroupQueryRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.SnapshotBatchQueryResponse.SerializeToString,
      ),
      'QuerySnapshotStats': grpc.unary_unary_rpc_method_handler(
          servicer.QuerySnapshotStats,
          request_deserializer=inventory_dot_inventory__pb2.SnapshotStatRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.SnapshotStatResponse.SerializeToString,
      ),
      'QuerySnapshotStatGroup': grpc.unary_unary_rpc_method_handler(
          servicer.QuerySnapshotStatGroup,
          request_deserializer=inventory_dot_inventory__pb2.SnapshotStatGroupRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.SnapshotStatGroupResponse.SerializeToString,
      ),
      'ListInventoryLog': grpc.unary_unary_rpc_method_handler(
          servicer.ListInventoryLog,
          request_deserializer=inventory_dot_inventory__pb2.ListInventoryLogRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.ListInventoryLogResponse.SerializeToString,
      ),
      'ListGroupEventLog': grpc.unary_unary_rpc_method_handler(
          servicer.ListGroupEventLog,
          request_deserializer=inventory_dot_inventory__pb2.ListGroupEventLogRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.ListGroupEventLogResponse.SerializeToString,
      ),
      'ListGroupSnapshot': grpc.unary_unary_rpc_method_handler(
          servicer.ListGroupSnapshot,
          request_deserializer=inventory_dot_inventory__pb2.ListGroupSnapshotRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.ListGroupSnapshotResponse.SerializeToString,
      ),
      'Group': grpc.unary_unary_rpc_method_handler(
          servicer.Group,
          request_deserializer=inventory_dot_inventory__pb2.GroupRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.GroupResponse.SerializeToString,
      ),
      'QueryGroupBranch': grpc.unary_unary_rpc_method_handler(
          servicer.QueryGroupBranch,
          request_deserializer=inventory_dot_inventory__pb2.QueryGroupBranchRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.QueryGroupBranchResponse.SerializeToString,
      ),
      'QueryGroupProduct': grpc.unary_unary_rpc_method_handler(
          servicer.QueryGroupProduct,
          request_deserializer=inventory_dot_inventory__pb2.QueryGroupProductRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.QueryGroupProductResponse.SerializeToString,
      ),
      'QueryStocktake': grpc.unary_unary_rpc_method_handler(
          servicer.QueryStocktake,
          request_deserializer=inventory_dot_inventory__pb2.QueryStocktakeRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.QueryStocktakeResponse.SerializeToString,
      ),
      'QueryBranchInventorySort': grpc.unary_unary_rpc_method_handler(
          servicer.QueryBranchInventorySort,
          request_deserializer=inventory_dot_inventory__pb2.QueryBranchInventorySortRequest.FromString,
          response_serializer=inventory_dot_inventory__pb2.QueryBranchInventorySortResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'inventory.InventoryService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
