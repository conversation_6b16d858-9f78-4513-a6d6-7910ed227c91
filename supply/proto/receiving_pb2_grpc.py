# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import receiving_pb2 as receiving__pb2


class ReceivingServiceStub(object):
  """ReceivingService 收货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateReceiving = channel.unary_unary(
        '/receiving.ReceivingService/CreateReceiving',
        request_serializer=receiving__pb2.CreateReceivingRequest.SerializeToString,
        response_deserializer=receiving__pb2.CreateReceivingResponse.FromString,
        )
    self.ListReceiving = channel.unary_unary(
        '/receiving.ReceivingService/ListReceiving',
        request_serializer=receiving__pb2.ListReceivingRequest.SerializeToString,
        response_deserializer=receiving__pb2.ListReceivingResponse.FromString,
        )
    self.ListReceivingCold = channel.unary_unary(
        '/receiving.ReceivingService/ListReceivingCold',
        request_serializer=receiving__pb2.ListReceivingColdRequest.SerializeToString,
        response_deserializer=receiving__pb2.ListReceivingColdResponse.FromString,
        )
    self.GetReceivingById = channel.unary_unary(
        '/receiving.ReceivingService/GetReceivingById',
        request_serializer=receiving__pb2.GetReceivingByIdRequest.SerializeToString,
        response_deserializer=receiving__pb2.Receiving.FromString,
        )
    self.GetReceivingProductById = channel.unary_unary(
        '/receiving.ReceivingService/GetReceivingProductById',
        request_serializer=receiving__pb2.GetReceivingProductByIdRequest.SerializeToString,
        response_deserializer=receiving__pb2.GetReceivingProductByIdResponse.FromString,
        )
    self.ConfirmReceiving = channel.unary_unary(
        '/receiving.ReceivingService/ConfirmReceiving',
        request_serializer=receiving__pb2.ConfirmReceivingRequest.SerializeToString,
        response_deserializer=receiving__pb2.ConfirmReceivingResponse.FromString,
        )
    self.UpdateReceiving = channel.unary_unary(
        '/receiving.ReceivingService/UpdateReceiving',
        request_serializer=receiving__pb2.UpdateReceivingRequest.SerializeToString,
        response_deserializer=receiving__pb2.UpdateReceivingResponse.FromString,
        )
    self.SuspendReceiving = channel.unary_unary(
        '/receiving.ReceivingService/SuspendReceiving',
        request_serializer=receiving__pb2.SuspendReceivingRequest.SerializeToString,
        response_deserializer=receiving__pb2.SuspendReceivingResponse.FromString,
        )


class ReceivingServiceServicer(object):
  """ReceivingService 收货相关服务
  """

  def CreateReceiving(self, request, context):
    """创建收货单
    response?
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReceiving(self, request, context):
    """查询收货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReceivingCold(self, request, context):
    """查询收货单--冷链
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingById(self, request, context):
    """根据id查询收货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingProductById(self, request, context):
    """根据receiving_id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmReceiving(self, request, context):
    """确认收货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReceiving(self, request, context):
    """更新收货单
    delete
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SuspendReceiving(self, request, context):
    """货品未到店，收货单挂起
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ReceivingServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateReceiving': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReceiving,
          request_deserializer=receiving__pb2.CreateReceivingRequest.FromString,
          response_serializer=receiving__pb2.CreateReceivingResponse.SerializeToString,
      ),
      'ListReceiving': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceiving,
          request_deserializer=receiving__pb2.ListReceivingRequest.FromString,
          response_serializer=receiving__pb2.ListReceivingResponse.SerializeToString,
      ),
      'ListReceivingCold': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceivingCold,
          request_deserializer=receiving__pb2.ListReceivingColdRequest.FromString,
          response_serializer=receiving__pb2.ListReceivingColdResponse.SerializeToString,
      ),
      'GetReceivingById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingById,
          request_deserializer=receiving__pb2.GetReceivingByIdRequest.FromString,
          response_serializer=receiving__pb2.Receiving.SerializeToString,
      ),
      'GetReceivingProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingProductById,
          request_deserializer=receiving__pb2.GetReceivingProductByIdRequest.FromString,
          response_serializer=receiving__pb2.GetReceivingProductByIdResponse.SerializeToString,
      ),
      'ConfirmReceiving': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmReceiving,
          request_deserializer=receiving__pb2.ConfirmReceivingRequest.FromString,
          response_serializer=receiving__pb2.ConfirmReceivingResponse.SerializeToString,
      ),
      'UpdateReceiving': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReceiving,
          request_deserializer=receiving__pb2.UpdateReceivingRequest.FromString,
          response_serializer=receiving__pb2.UpdateReceivingResponse.SerializeToString,
      ),
      'SuspendReceiving': grpc.unary_unary_rpc_method_handler(
          servicer.SuspendReceiving,
          request_deserializer=receiving__pb2.SuspendReceivingRequest.FromString,
          response_serializer=receiving__pb2.SuspendReceivingResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'receiving.ReceivingService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
