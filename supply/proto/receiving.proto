syntax = "proto3";
package receiving;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// ReceivingService 收货相关服务
service ReceivingService{
    
    // 创建收货单
    // response?
    rpc CreateReceiving(CreateReceivingRequest) returns (CreateReceivingResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/receiving"
            body:"*"
        };
    }

    // 查询收货单
    rpc ListReceiving(ListReceivingRequest) returns (ListReceivingResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/receiving"
        };
    }

    // 查询收货单--冷链
    rpc ListReceivingCold(ListReceivingColdRequest) returns (ListReceivingColdResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/receiving/cold"
        };
    }

    // 根据id查询收货单详情
    rpc GetReceivingById(GetReceivingByIdRequest) returns (Receiving){
        option (google.api.http) = {
            get:"/api/v2/supply/receiving/{id}"
        };
    }

    // 根据receiving_id查商品详情
    rpc GetReceivingProductById (GetReceivingProductByIdRequest) returns (GetReceivingProductByIdResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/receiving/{id}/product"
        };
    }

    // 确认收货
    rpc ConfirmReceiving(ConfirmReceivingRequest) returns (ConfirmReceivingResponse){
        option (google.api.http) = {
            put:"/api/v2/supply/receiving/{id}/confirm"
            body:"*"
        };
    }

    // 更新收货单
    // delete
    rpc UpdateReceiving(UpdateReceivingRequest) returns (UpdateReceivingResponse){
        option (google.api.http) = {
            put:"/api/v2/supply/receiving/{id}/update"
            body:"*"
        };
    }

    // 货品未到店，收货单挂起
    rpc SuspendReceiving(SuspendReceivingRequest) returns (SuspendReceivingResponse){
        option (google.api.http) = {
            put:"/api/v2/supply/receiving/{id}/suspend"
            body:"*"
        };
    }

}

message SuspendReceivingRequest{
    // 收货单id
    uint64 id = 1;
    // 备注
    string remark = 2;
    // 延迟原因
    string delay_reason = 3;
    // 附件
    string attachments = 4;
    // 不签名的原因
    string nosign_reason = 5;
    // 签名
    string signature = 6;
}

message SuspendReceivingResponse{
    bool result = 1;
}


message CreateReceivingRequest{
    // request_id 校验请求是否唯一
    // Mia：是否可以用要货单id
    uint64 request_id = 1;
    // 收货单单号
    string code = 2;
    //订货单id
    // 原master_id
    uint64 master_id = 3;
    // 订货单单号
    string master_code = 4;
    // 要货单id
    uint64 demand_order_id = 5;
    // 要货单单号
    // 原order_id
    string demand_order_code = 6;
    // JDE订单号
    // 原sap_order_id, 类型需确认
    uint64 jde_order_id = 7;
    // 储藏方式
    string storage_type = 8;
    // 接收门店-门店id
    uint64 received_by = 9;
    // jde公司编号-kcoo
    string store_secondary_id = 10;
    // 配送单号 
    // Mia：？
    string delivery_note_number = 12;
    // 配送方
    string delivery_by = 13;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 14;
    // 订货日期
    google.protobuf.Timestamp demand_date = 15;
    // 配送方式，True：直送，False：配送
    bool is_direct = 16;
    // 单据物流来源类型: BREAD 面包工厂
    string logistics_type = 17;
    repeated Product products = 18;
    // 收货单生成来源：TRANSFER-调拨单；None-要货单
    string master_type = 19;
    google.protobuf.Timestamp expected_arrival_date = 20;
    google.protobuf.Timestamp actual_arrival_date = 21;
    // 调整单标志
    bool is_adjust = 22;
}

// Mia：是否可以直接返回操作结果，而不附加数据
message CreateReceivingResponse{
    uint64 receiving_id = 1;
}

message ListReceivingRequest{
    // 订货单号
    uint64 order_id = 1;
    //
    uint64 batch_id = 2;
    //
    uint64 delivery_id = 3;
    // 订货单号
    string batch_code = 4;
    // 发货单号
    string delivery_code = 5;
    // 收货单号
    string code = 6;
    // 要货单号
    string order_code = 7;
    // 接受方id
    repeated uint64 receive_bys = 8;
    // 发送方
    repeated uint64 delivery_bys = 9;
    // 订单状态
    repeated string status = 10;
    // 订单状态
    repeated string process_status = 11;
    // 订货开始日期
    google.protobuf.Timestamp start_date = 12;
    // 订货结束日期
    google.protobuf.Timestamp end_date = 13;
    // 物流类型
    string distr_type = 14;
    // 业务类型
    repeated string batch_type = 15;
    // 分页大小
    int32 limit = 16;
    // 跳过行数
    int32 offset = 17;
    // 排序字段
    string sort = 18;
    // 排序顺序
    string order = 19;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 收货单receive_by是main_branch
    string main_branch_type = 20;
    // 配送开始日期
    google.protobuf.Timestamp delivery_start_date = 21;
    // 配送结束日期
    google.protobuf.Timestamp delivery_end_date = 22;
    // 传输成本引擎 0:未传输，1:已传输
    string cost_trans_status = 23;
    // 收货开始时间
    google.protobuf.Timestamp received_start_date = 24;
    // 收货结束时间
    google.protobuf.Timestamp received_end_date = 25;
    // 子账户类型
    string sub_account_type = 26;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 27;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 28;
    // 收货方地理区域 —— 目前只支持门店
    repeated uint64 geo_regions = 29;
    // 订单类型
    string demand_type = 30;
    // 商品ids
    repeated uint64 product_ids = 31;
    // 是否包含商品
    string include_products = 32;
    repeated uint64 ids = 33;
    // 预计到货日期开始时间
    google.protobuf.Timestamp expect_start_date = 34;
    // 预计到货日期结束时间
    google.protobuf.Timestamp expect_end_date = 35;
}

message ListReceivingResponse{
    repeated Receiving rows = 1;
    uint64 total = 2;
}

message ListReceivingColdRequest{
    // 订货单号
    uint64 order_id = 1;
    // 门店id
    repeated uint64 store_ids = 2;
    // 订单状态
    // enum Status{
    //     INITED = 0;
    //     CONFIRMED = 1;
    // }
    repeated string status = 3;
    //
    bool has_diffs = 4;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 5;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 6;
    // 要货订单号/ 订货单号
    // uint64 order_id = 9;
    // 分页大小
    int32 limit = 7;
    // 跳过行数
    int32 offset = 8;
    // 返回总条数
    bool include_total = 9;
    // 是否直送
    bool is_direct = 10;
    // jde单号
    uint64 jde_order_id = 11;
    // 收货单号
    string code = 12;
    // 地理区域
    repeated uint64 geo_regions = 13;
    // 单据物流来源类型:BREAD 面包工厂
    string logistics_type = 14;
    // 调整单标志
    bool is_adjust = 15;
    // 排序字段
    string sort = 16;
    // 排序顺序
    string order = 17;
}

message ListReceivingColdResponse{
    repeated Receiving rows = 1;
    uint64 total = 2;
}

message GetReceivingByIdRequest{
    // 收货单id(receiving_id)
    uint64 id = 1;

}

message GetReceivingByIdResponse{
    // wrapper.Status status = 1;
    Receiving receiving = 1;
}

message GetReceivingProductByIdRequest{
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
}

message GetReceivingProductByIdResponse{
    // wrapper.Status status = 1;
    repeated Product rows = 1;
    uint64 total = 2;
}

message ConfirmReceivingRequest{
    // 收货单id
    uint64 id = 1;
    // 确认数量
    repeated Product confirmed_products = 2;
    // 是否盲点？
    bool has_checked = 3;
    // 备注
    string remark = 4;
    // 延迟原因
    string delay_reason = 5;
    // 附件
    string attachments = 6;
    // 不签名的原因
    string nosign_reason = 7;
    // 签名
    string signature = 8;
}

message ConfirmReceivingResponse{
    bool result = 1;
}

message UpdateReceivingRequest{
    // 收货单id
    uint64 id = 1;
    // product
    repeated Product product= 2;
}

message UpdateReceivingResponse{
    bool result = 1;
    Receiving payload = 2;
}

message Product{
    // id 
    uint64 id = 1; 
    // 收货单id
    uint64 receiving_id = 2;
    // 收货门店
    uint64 received_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 核算单位id
    uint64 accounting_unit_id = 5;
    // 单位id
    uint64 unit_id = 6;

    // 商品编码
    string product_code = 7;
    // 商品名称
    string product_name = 8; 
    // 物料编码
    string material_number = 9;
    // 单位名称
    string accounting_unit_name = 10;
    // 单位规格
    string accounting_unit_spec = 11;
    // 核算单位名称
    string unit_name = 12;
    // 核算单位规格
    string unit_spec= 13;
    // 转换比率
    double unit_rate = 14;
    // 存储方式
    // enum STORAGE {
    //     "干货" = 1;
    //     不干货 = 2;
    // }
    string storage_type = 15;

    // 订货数量
    double required_accounting_quantity = 16;
    // 核算订货数量
    double required_quantity = 17;
    // 收货数量
    double received_accounting_quantity = 18;
    // 核算收货数量
    double received_quantity = 19;
    // 确认收货数量
    double confirmed_accounting_quantity = 20;
    // 核算确认收货数量
    double confirmed_quantity = 21;

    // 收货状态
    bool is_confirmed = 22;

    // 创建时间
    google.protobuf.Timestamp created_at = 23;
    // 创建人id
    uint64 created_by= 24;
    // 更新日期
    google.protobuf.Timestamp updated_at = 25;
    // 更新人id
    uint64 updated_by = 26;
    // jde预留商品id
    uint64 jde_product_id = 27;
    // 订货日期
    google.protobuf.Timestamp demand_date = 28;
    // 商户id
    uint64 partner_id = 29;
    // 创建人
    string created_name = 30;
    // 更新人
    string updated_name = 31;
    double jde_line = 32;
    uint32 hws_line = 33;
    // 首配标志
    string is_first_delivery = 34;
    // 成本价
    double price = 35;
    // 含税价
    double tax_price = 36;
    // 税率
    double tax_rate = 37;
    // 销售类型
    string sale_type = 39;

}

message Receiving{
    // 收货单id
    uint64 id = 1;
    // 收货单单号
    string code = 2;
    //订货单id
    // 原master_id
    uint64 master_id = 3;
    // 订货单单号
    string master_code = 4;
    // 要货单id
    uint64 demand_order_id = 5;
    // 要货单单号
    // 原order_id
    string demand_order_code = 6;
    // 收货差异单id
    // 为空，则无收货差异，增减库存；不为空，在确认收货差异单后再增减库存
    uint64 receiving_diff_id = 7;
    // JDE订单号
    uint64 jde_order_id = 8;

    // 接收门店-门店id
    uint64 received_by = 9;
    // jde公司编号-kcoo
    string store_secondary_id = 10;

    // 收货单状态
    // enum Status {
    //     // 新建/未收货
    //     INITED = 0;
    //     // 已确认
    //     CONFIRMED = 1;
    // }
    string status = 11;
    
    // 配送单号 
    // Mia：？
    string delivery_note_number = 12;
    // 配送方
    string delivery_by = 13;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 14;
    // 是否直送
    // Mia：冷链/ 大货？
    bool is_direct = 15;
    
    // 是否需要增减库存
    bool calculate_inventory = 16;
    // 存储类型 - 干货/？？
    string storage_type = 17;
    // 备注
    string remark = 18;

    // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 1; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 19;
    // 库存id预留字段
    uint64 inventory_req_id = 20;

    // 差异单生成状态
    // enum DiffProcessStatus {
    //     INIT = 0; // 
    //     CREATED = 1; // 收货差异单生成
    // }
    string process_status = 21;
    
    // 创建时间
    google.protobuf.Timestamp created_at = 22;
    // 更新时间
    google.protobuf.Timestamp updated_at = 23;
    // 创建人id
    int64 created_by = 24;
    // 更新人id
    int64 updated_by = 25;
    // 订货时间
    google.protobuf.Timestamp demand_date = 26;
    // 收货类型
    string received_type = 27;
    // 是否已验证
    bool has_checked = 28;
    // 商户id
    uint64 partner_id = 29;
    // 延迟原因
    string delay_reason = 30;
    // 附件
    string attachments = 31;
    // 商品数量
    uint64 product_nums = 32;
    // 创建人
    string created_name = 33;
    // 更新人
    string updated_name = 34;
    // 预计到货日期
    google.protobuf.Timestamp expected_arrival_date = 35;
    // 实际到货日期-JDE
    google.protobuf.Timestamp actual_arrival_date = 36;
    // 不签名的原因
    string nosign_reason = 37;
    // 签名
    string signature = 38;
    // 单据物流来源类型:BREAD 面包工厂
    string logistics_type = 39;
    // 调整单标志
    bool is_adjust = 40;
    // 对接三方的渠道
    string send_type = 41;

}

 