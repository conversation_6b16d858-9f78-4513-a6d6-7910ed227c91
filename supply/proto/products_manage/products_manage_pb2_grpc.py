# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from products_manage import products_manage_pb2 as products__manage_dot_products__manage__pb2


class ProductManageStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.AddOrUpdateTrussActivity = channel.unary_unary(
        '/products_manage.ProductManage/AddOrUpdateTrussActivity',
        request_serializer=products__manage_dot_products__manage__pb2.AddOrUpdateTrussActivityRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdateTrussActivityStatus = channel.unary_unary(
        '/products_manage.ProductManage/UpdateTrussActivityStatus',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateTrussActivityStatusRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.GetTrussActivity = channel.unary_unary(
        '/products_manage.ProductManage/GetTrussActivity',
        request_serializer=products__manage_dot_products__manage__pb2.GetTrussActivityRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetTrussActivityResponse.FromString,
        )
    self.GetTrussActivityProduct = channel.unary_unary(
        '/products_manage.ProductManage/GetTrussActivityProduct',
        request_serializer=products__manage_dot_products__manage__pb2.GetTrussActivityProductRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetTrussActivityProductResponse.FromString,
        )
    self.UpdateTrussActivityProduct = channel.unary_unary(
        '/products_manage.ProductManage/UpdateTrussActivityProduct',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateTrussActivityProductRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.GetAgentProducts = channel.unary_unary(
        '/products_manage.ProductManage/GetAgentProducts',
        request_serializer=products__manage_dot_products__manage__pb2.GetAgentProductsRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetAgentProductsResponse.FromString,
        )
    self.GetAgentProductsFilterRules = channel.unary_unary(
        '/products_manage.ProductManage/GetAgentProductsFilterRules',
        request_serializer=products__manage_dot_products__manage__pb2.GetAgentProductsRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetAgentProductsResponse.FromString,
        )
    self.GetOrderTypeByPrice = channel.unary_unary(
        '/products_manage.ProductManage/GetOrderTypeByPrice',
        request_serializer=products__manage_dot_products__manage__pb2.OrderTypeByPriceRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.OrderTypeByPriceResponse.FromString,
        )
    self.GetSkuProductInfo = channel.unary_unary(
        '/products_manage.ProductManage/GetSkuProductInfo',
        request_serializer=products__manage_dot_products__manage__pb2.GetSkuProductInfoRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetSkuProductInfoResponse.FromString,
        )
    self.UpdateSkuPriceStatus = channel.unary_unary(
        '/products_manage.ProductManage/UpdateSkuPriceStatus',
        request_serializer=products__manage_dot_products__manage__pb2.UpdatePriceStatusRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.DeleteSkuPrice = channel.unary_unary(
        '/products_manage.ProductManage/DeleteSkuPrice',
        request_serializer=products__manage_dot_products__manage__pb2.DeleteSkuPriceRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.MoveSkuPrice = channel.unary_unary(
        '/products_manage.ProductManage/MoveSkuPrice',
        request_serializer=products__manage_dot_products__manage__pb2.MoveSkuPriceRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.DeleteOrderRule = channel.unary_unary(
        '/products_manage.ProductManage/DeleteOrderRule',
        request_serializer=products__manage_dot_products__manage__pb2.DeleteOrderRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.GetPriceSkuByProduct = channel.unary_unary(
        '/products_manage.ProductManage/GetPriceSkuByProduct',
        request_serializer=products__manage_dot_products__manage__pb2.GetPriceSkuByProductRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetPriceSkuByProductResponse.FromString,
        )
    self.GetPriceSkuByProductDetail = channel.unary_unary(
        '/products_manage.ProductManage/GetPriceSkuByProductDetail',
        request_serializer=products__manage_dot_products__manage__pb2.GetPriceSkuByProductDetailRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetPriceSkuByProductDetailResponse.FromString,
        )
    self.GetPriceSkuByStore = channel.unary_unary(
        '/products_manage.ProductManage/GetPriceSkuByStore',
        request_serializer=products__manage_dot_products__manage__pb2.GetPriceSkuByStoreRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetPriceSkuByStoreResponse.FromString,
        )
    self.GetOrderRuleByProduct = channel.unary_unary(
        '/products_manage.ProductManage/GetOrderRuleByProduct',
        request_serializer=products__manage_dot_products__manage__pb2.GetOrderRuleByProductRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetOrderRuleByProductResponse.FromString,
        )
    self.GetOrderRuleByStore = channel.unary_unary(
        '/products_manage.ProductManage/GetOrderRuleByStore',
        request_serializer=products__manage_dot_products__manage__pb2.GetOrderRuleByStoreRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetOrderRuleByStoreResponse.FromString,
        )
    self.UpdateSkuPrice = channel.unary_unary(
        '/products_manage.ProductManage/UpdateSkuPrice',
        request_serializer=products__manage_dot_products__manage__pb2.UpdatePriceRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdateSkuPriceByProduct = channel.unary_unary(
        '/products_manage.ProductManage/UpdateSkuPriceByProduct',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateSkuPriceByProductRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.AddPlan = channel.unary_unary(
        '/products_manage.ProductManage/AddPlan',
        request_serializer=products__manage_dot_products__manage__pb2.AddPlanRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.AddProductPrice = channel.unary_unary(
        '/products_manage.ProductManage/AddProductPrice',
        request_serializer=products__manage_dot_products__manage__pb2.AddProductPriceRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.ProductPriceCheck = channel.unary_unary(
        '/products_manage.ProductManage/ProductPriceCheck',
        request_serializer=products__manage_dot_products__manage__pb2.AddProductPriceRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.ProductPriceCheckResponse.FromString,
        )
    self.AddOrderRuleCheck = channel.unary_unary(
        '/products_manage.ProductManage/AddOrderRuleCheck',
        request_serializer=products__manage_dot_products__manage__pb2.AddOrderRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.OrderRuleCheckResponse.FromString,
        )
    self.AddOrderRule = channel.unary_unary(
        '/products_manage.ProductManage/AddOrderRule',
        request_serializer=products__manage_dot_products__manage__pb2.AddOrderRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdateOrderRule = channel.unary_unary(
        '/products_manage.ProductManage/UpdateOrderRule',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateOrderRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.AddGroup = channel.unary_unary(
        '/products_manage.ProductManage/AddGroup',
        request_serializer=products__manage_dot_products__manage__pb2.AddGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.DataChangeGroup = channel.unary_unary(
        '/products_manage.ProductManage/DataChangeGroup',
        request_serializer=products__manage_dot_products__manage__pb2.DataChangeGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdateGroup = channel.unary_unary(
        '/products_manage.ProductManage/UpdateGroup',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.GetGroup = channel.unary_unary(
        '/products_manage.ProductManage/GetGroup',
        request_serializer=products__manage_dot_products__manage__pb2.GetGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetGroupResponse.FromString,
        )
    self.GetGroupProduct = channel.unary_unary(
        '/products_manage.ProductManage/GetGroupProduct',
        request_serializer=products__manage_dot_products__manage__pb2.GetGroupProductRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetGroupProductResponse.FromString,
        )
    self.AddDistributionRuleCheck = channel.unary_unary(
        '/products_manage.ProductManage/AddDistributionRuleCheck',
        request_serializer=products__manage_dot_products__manage__pb2.AddDistributionRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.DistributionRuleCheckResponse.FromString,
        )
    self.AddDistributionRule = channel.unary_unary(
        '/products_manage.ProductManage/AddDistributionRule',
        request_serializer=products__manage_dot_products__manage__pb2.AddDistributionRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdateDistributionRule = channel.unary_unary(
        '/products_manage.ProductManage/UpdateDistributionRule',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateDistributionRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.DeleteDistributionRule = channel.unary_unary(
        '/products_manage.ProductManage/DeleteDistributionRule',
        request_serializer=products__manage_dot_products__manage__pb2.DeleteDistributionRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.GetDistributionRuleByProduct = channel.unary_unary(
        '/products_manage.ProductManage/GetDistributionRuleByProduct',
        request_serializer=products__manage_dot_products__manage__pb2.GetDistributionRuleByProductRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetDistributionRuleByProductResponse.FromString,
        )
    self.GetDistributionRule = channel.unary_unary(
        '/products_manage.ProductManage/GetDistributionRule',
        request_serializer=products__manage_dot_products__manage__pb2.GetDistributionRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetDistributionRuleResponse.FromString,
        )
    self.GetSaleTax = channel.unary_unary(
        '/products_manage.ProductManage/GetSaleTax',
        request_serializer=products__manage_dot_products__manage__pb2.GetSaleTaxListRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetSaleTaxListResponse.FromString,
        )
    self.AddSaleTax = channel.unary_unary(
        '/products_manage.ProductManage/AddSaleTax',
        request_serializer=products__manage_dot_products__manage__pb2.SaleTaxTotal.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdateSaleTax = channel.unary_unary(
        '/products_manage.ProductManage/UpdateSaleTax',
        request_serializer=products__manage_dot_products__manage__pb2.SaleTaxTotal.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.GetSaleTaxLog = channel.unary_unary(
        '/products_manage.ProductManage/GetSaleTaxLog',
        request_serializer=products__manage_dot_products__manage__pb2.SaleTaxLogRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.SaleTaxLogResponse.FromString,
        )
    self.GetPlan = channel.unary_unary(
        '/products_manage.ProductManage/GetPlan',
        request_serializer=products__manage_dot_products__manage__pb2.GetPlanRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetPlanResponse.FromString,
        )
    self.GetPlanItemList = channel.unary_unary(
        '/products_manage.ProductManage/GetPlanItemList',
        request_serializer=products__manage_dot_products__manage__pb2.GetPlanItemRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetPlanItemResponse.FromString,
        )
    self.CancelPlan = channel.unary_unary(
        '/products_manage.ProductManage/CancelPlan',
        request_serializer=products__manage_dot_products__manage__pb2.CancelPlanRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdatePlanItem = channel.unary_unary(
        '/products_manage.ProductManage/UpdatePlanItem',
        request_serializer=products__manage_dot_products__manage__pb2.UpdatePlanItemRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.AddOrderTypeGroup = channel.unary_unary(
        '/products_manage.ProductManage/AddOrderTypeGroup',
        request_serializer=products__manage_dot_products__manage__pb2.AddOrderTypeGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.QueryOrderTypeGroup = channel.unary_unary(
        '/products_manage.ProductManage/QueryOrderTypeGroup',
        request_serializer=products__manage_dot_products__manage__pb2.QueryOrderTypeGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.QueryOrderTypeGroupResponse.FromString,
        )
    self.DeleteOrderTypeGroup = channel.unary_unary(
        '/products_manage.ProductManage/DeleteOrderTypeGroup',
        request_serializer=products__manage_dot_products__manage__pb2.DeleteOrderTypeGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdateOrderTypeGroup = channel.unary_unary(
        '/products_manage.ProductManage/UpdateOrderTypeGroup',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateOrderTypeGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.QueryOrderTypeGroupByStore = channel.unary_unary(
        '/products_manage.ProductManage/QueryOrderTypeGroupByStore',
        request_serializer=products__manage_dot_products__manage__pb2.QueryOrderTypeGroupRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.QueryOrderTypeGroupByStoreResponse.FromString,
        )
    self.UpdateOrderTypeGroupStatus = channel.unary_unary(
        '/products_manage.ProductManage/UpdateOrderTypeGroupStatus',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateOrderTypeGroupStatusRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.UpdateOrderTypeGroupBatch = channel.unary_unary(
        '/products_manage.ProductManage/UpdateOrderTypeGroupBatch',
        request_serializer=products__manage_dot_products__manage__pb2.UpdateOrderTypeGroupBatchRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.UpdateOrderTypeGroupBatchResponse.FromString,
        )
    self.QueryRangeOfOrder = channel.unary_unary(
        '/products_manage.ProductManage/QueryRangeOfOrder',
        request_serializer=products__manage_dot_products__manage__pb2.QueryRangeOfOrderRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.QueryRangeOfOrderResponse.FromString,
        )
    self.GetDistributionRuleExport = channel.unary_unary(
        '/products_manage.ProductManage/GetDistributionRuleExport',
        request_serializer=products__manage_dot_products__manage__pb2.GetDistributionRuleRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetDistributionRuleExportResponse.FromString,
        )
    self.GetOrderRuleExport = channel.unary_unary(
        '/products_manage.ProductManage/GetOrderRuleExport',
        request_serializer=products__manage_dot_products__manage__pb2.GetOrderRuleByStoreRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.GetOrderRuleExportResponse.FromString,
        )
    self.OrderRangePriceChangeReport = channel.unary_unary(
        '/products_manage.ProductManage/OrderRangePriceChangeReport',
        request_serializer=products__manage_dot_products__manage__pb2.OrderRangeChangeRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.OrderRangeChangeResponse.FromString,
        )
    self.SyncMetaData = channel.unary_unary(
        '/products_manage.ProductManage/SyncMetaData',
        request_serializer=products__manage_dot_products__manage__pb2.SyncMetaDataRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.Response.FromString,
        )
    self.QueryFreeOrderLimit = channel.unary_unary(
        '/products_manage.ProductManage/QueryFreeOrderLimit',
        request_serializer=products__manage_dot_products__manage__pb2.QueryFreeOrderLimitRequest.SerializeToString,
        response_deserializer=products__manage_dot_products__manage__pb2.QueryFreeOrderLimitResponse.FromString,
        )


class ProductManageServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def AddOrUpdateTrussActivity(self, request, context):
    """新增或修改捆绑销售
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateTrussActivityStatus(self, request, context):
    """修改捆绑销售活动状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTrussActivity(self, request, context):
    """获取捆绑销售活动
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTrussActivityProduct(self, request, context):
    """获取捆绑销售活动商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateTrussActivityProduct(self, request, context):
    """修改捆绑销售活动商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAgentProducts(self, request, context):
    """获取加盟商可购买商品列表  已经废弃 不要调用 请调用 GetAgentProductsFilterRules
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAgentProductsFilterRules(self, request, context):
    """获取加盟商可购买商品列表过滤
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderTypeByPrice(self, request, context):
    """获取订货类型 根据价格维度
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSkuProductInfo(self, request, context):
    """获取主档商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSkuPriceStatus(self, request, context):
    """修改价格中心状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteSkuPrice(self, request, context):
    """删除价格中心
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def MoveSkuPrice(self, request, context):
    """移动价格
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteOrderRule(self, request, context):
    """删除订货规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceSkuByProduct(self, request, context):
    """获取价格中心商品 维度:商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceSkuByProductDetail(self, request, context):
    """获取价格中心商品 维度:商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceSkuByStore(self, request, context):
    """获取价格中心商品 维度:门店
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderRuleByProduct(self, request, context):
    """订货规则 商品维度
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderRuleByStore(self, request, context):
    """订货规则 门店维度
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSkuPrice(self, request, context):
    """调整价格中心
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSkuPriceByProduct(self, request, context):
    """调整价格中心
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddPlan(self, request, context):
    """创建订货计划
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddProductPrice(self, request, context):
    """添加门店商品价格
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ProductPriceCheck(self, request, context):
    """添加门店商品价格
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddOrderRuleCheck(self, request, context):
    """添加订货规则检查
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddOrderRule(self, request, context):
    """添加订货规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateOrderRule(self, request, context):
    """修改订货规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddGroup(self, request, context):
    """添加分组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DataChangeGroup(self, request, context):
    """保存组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateGroup(self, request, context):
    """修改分组 只传入有变化的商品 删除商品是 is_delete = true
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetGroup(self, request, context):
    """查询分组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetGroupProduct(self, request, context):
    """查询分组商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddDistributionRuleCheck(self, request, context):
    """// 查询分组订货商品
    rpc GetOrderRuleGroupProduct (GetGroupOrderRuleRequest) returns (GetGroupOrderRuleResponse) {
    option (google.api.http) = {
    post: "/api/v1/products-manage/group/query"
    body: "*"
    };
    }

    添加配送规则检查
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddDistributionRule(self, request, context):
    """添加配送规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDistributionRule(self, request, context):
    """修改配送规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteDistributionRule(self, request, context):
    """删除配送规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDistributionRuleByProduct(self, request, context):
    """查询配送规则(商品视角)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDistributionRule(self, request, context):
    """查询配送规则(门店视角)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSaleTax(self, request, context):
    """查询销项税信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddSaleTax(self, request, context):
    """增加税率信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSaleTax(self, request, context):
    """更新税率信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSaleTaxLog(self, request, context):
    """查询税率变更日志
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPlan(self, request, context):
    """查询价格中心生效计划主表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPlanItemList(self, request, context):
    """查询价格中心生效计划主表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelPlan(self, request, context):
    """撤销价格中心生效计划
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdatePlanItem(self, request, context):
    """更新价格中心生效计划明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddOrderTypeGroup(self, request, context):
    """添加订货类型组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryOrderTypeGroup(self, request, context):
    """查询订货类型组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteOrderTypeGroup(self, request, context):
    """删除订货类型组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateOrderTypeGroup(self, request, context):
    """更新订货类型组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryOrderTypeGroupByStore(self, request, context):
    """查询订货类型组门店视角
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateOrderTypeGroupStatus(self, request, context):
    """更新订货类型组状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateOrderTypeGroupBatch(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryRangeOfOrder(self, request, context):
    """门店可订货范围查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDistributionRuleExport(self, request, context):
    """导出配送规则(门店视角)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderRuleExport(self, request, context):
    """订货规则导出
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def OrderRangePriceChangeReport(self, request, context):
    """门店订货范围, 价格变动报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SyncMetaData(self, request, context):
    """主档数据同步
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryFreeOrderLimit(self, request, context):
    """查询免品上限
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ProductManageServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'AddOrUpdateTrussActivity': grpc.unary_unary_rpc_method_handler(
          servicer.AddOrUpdateTrussActivity,
          request_deserializer=products__manage_dot_products__manage__pb2.AddOrUpdateTrussActivityRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdateTrussActivityStatus': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateTrussActivityStatus,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateTrussActivityStatusRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'GetTrussActivity': grpc.unary_unary_rpc_method_handler(
          servicer.GetTrussActivity,
          request_deserializer=products__manage_dot_products__manage__pb2.GetTrussActivityRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetTrussActivityResponse.SerializeToString,
      ),
      'GetTrussActivityProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetTrussActivityProduct,
          request_deserializer=products__manage_dot_products__manage__pb2.GetTrussActivityProductRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetTrussActivityProductResponse.SerializeToString,
      ),
      'UpdateTrussActivityProduct': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateTrussActivityProduct,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateTrussActivityProductRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'GetAgentProducts': grpc.unary_unary_rpc_method_handler(
          servicer.GetAgentProducts,
          request_deserializer=products__manage_dot_products__manage__pb2.GetAgentProductsRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetAgentProductsResponse.SerializeToString,
      ),
      'GetAgentProductsFilterRules': grpc.unary_unary_rpc_method_handler(
          servicer.GetAgentProductsFilterRules,
          request_deserializer=products__manage_dot_products__manage__pb2.GetAgentProductsRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetAgentProductsResponse.SerializeToString,
      ),
      'GetOrderTypeByPrice': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderTypeByPrice,
          request_deserializer=products__manage_dot_products__manage__pb2.OrderTypeByPriceRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.OrderTypeByPriceResponse.SerializeToString,
      ),
      'GetSkuProductInfo': grpc.unary_unary_rpc_method_handler(
          servicer.GetSkuProductInfo,
          request_deserializer=products__manage_dot_products__manage__pb2.GetSkuProductInfoRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetSkuProductInfoResponse.SerializeToString,
      ),
      'UpdateSkuPriceStatus': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSkuPriceStatus,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdatePriceStatusRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'DeleteSkuPrice': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteSkuPrice,
          request_deserializer=products__manage_dot_products__manage__pb2.DeleteSkuPriceRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'MoveSkuPrice': grpc.unary_unary_rpc_method_handler(
          servicer.MoveSkuPrice,
          request_deserializer=products__manage_dot_products__manage__pb2.MoveSkuPriceRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'DeleteOrderRule': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteOrderRule,
          request_deserializer=products__manage_dot_products__manage__pb2.DeleteOrderRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'GetPriceSkuByProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceSkuByProduct,
          request_deserializer=products__manage_dot_products__manage__pb2.GetPriceSkuByProductRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetPriceSkuByProductResponse.SerializeToString,
      ),
      'GetPriceSkuByProductDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceSkuByProductDetail,
          request_deserializer=products__manage_dot_products__manage__pb2.GetPriceSkuByProductDetailRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetPriceSkuByProductDetailResponse.SerializeToString,
      ),
      'GetPriceSkuByStore': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceSkuByStore,
          request_deserializer=products__manage_dot_products__manage__pb2.GetPriceSkuByStoreRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetPriceSkuByStoreResponse.SerializeToString,
      ),
      'GetOrderRuleByProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderRuleByProduct,
          request_deserializer=products__manage_dot_products__manage__pb2.GetOrderRuleByProductRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetOrderRuleByProductResponse.SerializeToString,
      ),
      'GetOrderRuleByStore': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderRuleByStore,
          request_deserializer=products__manage_dot_products__manage__pb2.GetOrderRuleByStoreRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetOrderRuleByStoreResponse.SerializeToString,
      ),
      'UpdateSkuPrice': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSkuPrice,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdatePriceRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdateSkuPriceByProduct': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSkuPriceByProduct,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateSkuPriceByProductRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'AddPlan': grpc.unary_unary_rpc_method_handler(
          servicer.AddPlan,
          request_deserializer=products__manage_dot_products__manage__pb2.AddPlanRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'AddProductPrice': grpc.unary_unary_rpc_method_handler(
          servicer.AddProductPrice,
          request_deserializer=products__manage_dot_products__manage__pb2.AddProductPriceRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'ProductPriceCheck': grpc.unary_unary_rpc_method_handler(
          servicer.ProductPriceCheck,
          request_deserializer=products__manage_dot_products__manage__pb2.AddProductPriceRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.ProductPriceCheckResponse.SerializeToString,
      ),
      'AddOrderRuleCheck': grpc.unary_unary_rpc_method_handler(
          servicer.AddOrderRuleCheck,
          request_deserializer=products__manage_dot_products__manage__pb2.AddOrderRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.OrderRuleCheckResponse.SerializeToString,
      ),
      'AddOrderRule': grpc.unary_unary_rpc_method_handler(
          servicer.AddOrderRule,
          request_deserializer=products__manage_dot_products__manage__pb2.AddOrderRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdateOrderRule': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateOrderRule,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateOrderRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'AddGroup': grpc.unary_unary_rpc_method_handler(
          servicer.AddGroup,
          request_deserializer=products__manage_dot_products__manage__pb2.AddGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'DataChangeGroup': grpc.unary_unary_rpc_method_handler(
          servicer.DataChangeGroup,
          request_deserializer=products__manage_dot_products__manage__pb2.DataChangeGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdateGroup': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateGroup,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'GetGroup': grpc.unary_unary_rpc_method_handler(
          servicer.GetGroup,
          request_deserializer=products__manage_dot_products__manage__pb2.GetGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetGroupResponse.SerializeToString,
      ),
      'GetGroupProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetGroupProduct,
          request_deserializer=products__manage_dot_products__manage__pb2.GetGroupProductRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetGroupProductResponse.SerializeToString,
      ),
      'AddDistributionRuleCheck': grpc.unary_unary_rpc_method_handler(
          servicer.AddDistributionRuleCheck,
          request_deserializer=products__manage_dot_products__manage__pb2.AddDistributionRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.DistributionRuleCheckResponse.SerializeToString,
      ),
      'AddDistributionRule': grpc.unary_unary_rpc_method_handler(
          servicer.AddDistributionRule,
          request_deserializer=products__manage_dot_products__manage__pb2.AddDistributionRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdateDistributionRule': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDistributionRule,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateDistributionRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'DeleteDistributionRule': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteDistributionRule,
          request_deserializer=products__manage_dot_products__manage__pb2.DeleteDistributionRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'GetDistributionRuleByProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetDistributionRuleByProduct,
          request_deserializer=products__manage_dot_products__manage__pb2.GetDistributionRuleByProductRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetDistributionRuleByProductResponse.SerializeToString,
      ),
      'GetDistributionRule': grpc.unary_unary_rpc_method_handler(
          servicer.GetDistributionRule,
          request_deserializer=products__manage_dot_products__manage__pb2.GetDistributionRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetDistributionRuleResponse.SerializeToString,
      ),
      'GetSaleTax': grpc.unary_unary_rpc_method_handler(
          servicer.GetSaleTax,
          request_deserializer=products__manage_dot_products__manage__pb2.GetSaleTaxListRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetSaleTaxListResponse.SerializeToString,
      ),
      'AddSaleTax': grpc.unary_unary_rpc_method_handler(
          servicer.AddSaleTax,
          request_deserializer=products__manage_dot_products__manage__pb2.SaleTaxTotal.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdateSaleTax': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSaleTax,
          request_deserializer=products__manage_dot_products__manage__pb2.SaleTaxTotal.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'GetSaleTaxLog': grpc.unary_unary_rpc_method_handler(
          servicer.GetSaleTaxLog,
          request_deserializer=products__manage_dot_products__manage__pb2.SaleTaxLogRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.SaleTaxLogResponse.SerializeToString,
      ),
      'GetPlan': grpc.unary_unary_rpc_method_handler(
          servicer.GetPlan,
          request_deserializer=products__manage_dot_products__manage__pb2.GetPlanRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetPlanResponse.SerializeToString,
      ),
      'GetPlanItemList': grpc.unary_unary_rpc_method_handler(
          servicer.GetPlanItemList,
          request_deserializer=products__manage_dot_products__manage__pb2.GetPlanItemRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetPlanItemResponse.SerializeToString,
      ),
      'CancelPlan': grpc.unary_unary_rpc_method_handler(
          servicer.CancelPlan,
          request_deserializer=products__manage_dot_products__manage__pb2.CancelPlanRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdatePlanItem': grpc.unary_unary_rpc_method_handler(
          servicer.UpdatePlanItem,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdatePlanItemRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'AddOrderTypeGroup': grpc.unary_unary_rpc_method_handler(
          servicer.AddOrderTypeGroup,
          request_deserializer=products__manage_dot_products__manage__pb2.AddOrderTypeGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'QueryOrderTypeGroup': grpc.unary_unary_rpc_method_handler(
          servicer.QueryOrderTypeGroup,
          request_deserializer=products__manage_dot_products__manage__pb2.QueryOrderTypeGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.QueryOrderTypeGroupResponse.SerializeToString,
      ),
      'DeleteOrderTypeGroup': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteOrderTypeGroup,
          request_deserializer=products__manage_dot_products__manage__pb2.DeleteOrderTypeGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdateOrderTypeGroup': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateOrderTypeGroup,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateOrderTypeGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'QueryOrderTypeGroupByStore': grpc.unary_unary_rpc_method_handler(
          servicer.QueryOrderTypeGroupByStore,
          request_deserializer=products__manage_dot_products__manage__pb2.QueryOrderTypeGroupRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.QueryOrderTypeGroupByStoreResponse.SerializeToString,
      ),
      'UpdateOrderTypeGroupStatus': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateOrderTypeGroupStatus,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateOrderTypeGroupStatusRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'UpdateOrderTypeGroupBatch': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateOrderTypeGroupBatch,
          request_deserializer=products__manage_dot_products__manage__pb2.UpdateOrderTypeGroupBatchRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.UpdateOrderTypeGroupBatchResponse.SerializeToString,
      ),
      'QueryRangeOfOrder': grpc.unary_unary_rpc_method_handler(
          servicer.QueryRangeOfOrder,
          request_deserializer=products__manage_dot_products__manage__pb2.QueryRangeOfOrderRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.QueryRangeOfOrderResponse.SerializeToString,
      ),
      'GetDistributionRuleExport': grpc.unary_unary_rpc_method_handler(
          servicer.GetDistributionRuleExport,
          request_deserializer=products__manage_dot_products__manage__pb2.GetDistributionRuleRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetDistributionRuleExportResponse.SerializeToString,
      ),
      'GetOrderRuleExport': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderRuleExport,
          request_deserializer=products__manage_dot_products__manage__pb2.GetOrderRuleByStoreRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.GetOrderRuleExportResponse.SerializeToString,
      ),
      'OrderRangePriceChangeReport': grpc.unary_unary_rpc_method_handler(
          servicer.OrderRangePriceChangeReport,
          request_deserializer=products__manage_dot_products__manage__pb2.OrderRangeChangeRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.OrderRangeChangeResponse.SerializeToString,
      ),
      'SyncMetaData': grpc.unary_unary_rpc_method_handler(
          servicer.SyncMetaData,
          request_deserializer=products__manage_dot_products__manage__pb2.SyncMetaDataRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.Response.SerializeToString,
      ),
      'QueryFreeOrderLimit': grpc.unary_unary_rpc_method_handler(
          servicer.QueryFreeOrderLimit,
          request_deserializer=products__manage_dot_products__manage__pb2.QueryFreeOrderLimitRequest.FromString,
          response_serializer=products__manage_dot_products__manage__pb2.QueryFreeOrderLimitResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'products_manage.ProductManage', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
