syntax = "proto3";

package products_manage;
option go_package = "./products_manage";
import "google/protobuf/struct.proto";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
//import "google/protobuf/timestamp.proto";
//import "google/protobuf/any.proto";
//import "gitlab.hexcloud.cn/saas/auth-golang/protoc-gen-hex-auth/annotations/annotations.proto";
//protoc-go-inject-tag 使用

service ProductManage {

  // 新增或修改捆绑销售
  rpc AddOrUpdateTrussActivity (AddOrUpdateTrussActivityRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/add-or-update-truss-activity"
    };
  }

  // 修改捆绑销售活动状态
  rpc UpdateTrussActivityStatus (UpdateTrussActivityStatusRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/update-truss-activity-status"
    };
  }


  // 获取捆绑销售活动
  rpc GetTrussActivity (GetTrussActivityRequest) returns (GetTrussActivityResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/get-truss-activity"
    };
  }


  // 获取捆绑销售活动商品
  rpc GetTrussActivityProduct (GetTrussActivityProductRequest) returns (GetTrussActivityProductResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/get-truss-activity-product"
    };
  }

  // 修改捆绑销售活动商品
  rpc UpdateTrussActivityProduct (UpdateTrussActivityProductRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/update-truss-activity-product"
    };
  }

  // 获取加盟商可购买商品列表  已经废弃 不要调用 请调用 GetAgentProductsFilterRules
  rpc GetAgentProducts (GetAgentProductsRequest) returns (GetAgentProductsResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/agent-products"
    };
  }

  // 获取加盟商可购买商品列表过滤
  rpc GetAgentProductsFilterRules (GetAgentProductsRequest) returns (GetAgentProductsResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/agent-products-filter-rules"
    };
  }

  // 获取订货类型 根据价格维度
  rpc GetOrderTypeByPrice (OrderTypeByPriceRequest) returns (OrderTypeByPriceResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/order-type-by-price"
    };
  }

  // 获取主档商品
  rpc GetSkuProductInfo (GetSkuProductInfoRequest) returns (GetSkuProductInfoResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/sku-price/product"
    };
  }

  // 修改价格中心状态
  rpc UpdateSkuPriceStatus (UpdatePriceStatusRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/update/sku/price/status"
    };
  }

  // 删除价格中心
  rpc DeleteSkuPrice (DeleteSkuPriceRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/delete/sku/price"
    };
  }

  // 移动价格
  rpc MoveSkuPrice (MoveSkuPriceRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/move/sku/price"
    };
  }

  // 删除订货规则
  rpc DeleteOrderRule (DeleteOrderRuleRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/delete-order-rule"
    };
  }

  // 获取价格中心商品 维度:商品
  rpc GetPriceSkuByProduct (GetPriceSkuByProductRequest) returns (GetPriceSkuByProductResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/price-sku-by-product"
    };
  }

  // 获取价格中心商品 维度:商品
  rpc GetPriceSkuByProductDetail (GetPriceSkuByProductDetailRequest) returns (GetPriceSkuByProductDetailResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/price-sku-by-product-detail"
    };
  }

  // 获取价格中心商品 维度:门店
  rpc GetPriceSkuByStore (GetPriceSkuByStoreRequest) returns (GetPriceSkuByStoreResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/price-sku-by-store"
    };
  }


  // 订货规则 商品维度
  rpc GetOrderRuleByProduct (GetOrderRuleByProductRequest) returns (GetOrderRuleByProductResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/order-rule-by-product"
      body: "*"
    };
  }

  // 订货规则 门店维度
  rpc GetOrderRuleByStore (GetOrderRuleByStoreRequest) returns (GetOrderRuleByStoreResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/order-rule-by-store"
      body: "*"
    };
  }

  // 调整价格中心
  rpc UpdateSkuPrice (UpdatePriceRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/update/sku/price"
      body: "*"
    };
  }

  // 调整价格中心
  rpc UpdateSkuPriceByProduct (UpdateSkuPriceByProductRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/update/sku/price/by/product"
      body: "*"
    };
  }

  // 创建订货计划
  rpc AddPlan (AddPlanRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/add-plan"
      body: "*"
    };
  }

  // 添加门店商品价格
  rpc AddProductPrice (AddProductPriceRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/store-product-price"
      body: "*"
    };
  }

  // 添加门店商品价格
  rpc ProductPriceCheck (AddProductPriceRequest) returns (ProductPriceCheckResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/product-price-check"
      body: "*"
    };
  }

  // 添加订货规则检查
  rpc AddOrderRuleCheck (AddOrderRuleRequest) returns (OrderRuleCheckResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/add-order-rule-check"
      body: "*"
    };
  }
  // 添加订货规则
  rpc AddOrderRule (AddOrderRuleRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/add-order-rule"
      body: "*"
    };
  }

  // 修改订货规则
  rpc UpdateOrderRule (UpdateOrderRuleRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/update-order-rule"
      body: "*"
    };
  }

  // 添加分组
  rpc AddGroup (AddGroupRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/group/add"
      body: "*"
    };
  }

  // 保存组
  rpc DataChangeGroup (DataChangeGroupRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/data-change-group"
      body: "*"
    };
  }


  // 修改分组 只传入有变化的商品 删除商品是 is_delete = true
  rpc UpdateGroup (UpdateGroupRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/group/update"
      body: "*"
    };
  }


  // 查询分组
  rpc GetGroup (GetGroupRequest) returns (GetGroupResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/group/get"
      body: "*"
    };
  }


  // 查询分组商品
  rpc GetGroupProduct (GetGroupProductRequest) returns (GetGroupProductResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/group/product/get"
      body: "*"
    };
  }

  //  // 查询分组订货商品
  //  rpc GetOrderRuleGroupProduct (GetGroupOrderRuleRequest) returns (GetGroupOrderRuleResponse) {
  //    option (google.api.http) = {
  //      post: "/api/v1/products-manage/group/query"
  //      body: "*"
  //    };
  //  }

  // 添加配送规则检查
  rpc AddDistributionRuleCheck (AddDistributionRuleRequest) returns (DistributionRuleCheckResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/add-distribution-rule-check"
      body: "*"
    };
  }
  // 添加配送规则
  rpc AddDistributionRule (AddDistributionRuleRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/add-distribution-rule"
      body: "*"
    };
  }

  // 修改配送规则
  rpc UpdateDistributionRule (UpdateDistributionRuleRequest) returns (Response) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/update-distribution-rule"
      body: "*"
    };
  }
  // 删除配送规则
  rpc DeleteDistributionRule (DeleteDistributionRuleRequest)returns(Response){
    option (google.api.http) = {
      post: "/api/v1/products-manage/delete-distribution-rule"
      body: "*"
    };
  }
  // 查询配送规则(商品视角)
  rpc GetDistributionRuleByProduct(GetDistributionRuleByProductRequest)returns(GetDistributionRuleByProductResponse){
    option (google.api.http) = {
      post:"/api/v1/products-manage/distribution-rule-by-product"
      body: "*"
    };
  }
  // 查询配送规则(门店视角)
  rpc GetDistributionRule(GetDistributionRuleRequest)returns(GetDistributionRuleResponse){
    option (google.api.http) = {
      post:"/api/v1/products-manage/distribution-rule"
      body: "*"
    };
  }
  // 查询销项税信息
  rpc GetSaleTax(GetSaleTaxListRequest)returns(GetSaleTaxListResponse){
    option(google.api.http) = {
      post:"/api/v1/products-manage/sales-tax"
      body: "*"
    };
  }
  // 增加税率信息
  rpc AddSaleTax(SaleTaxTotal)returns(Response){
    option(google.api.http) = {
      post:"/api/v1/products-manage/add-sales-tax"
      body: "*"
    };
  }
  // 更新税率信息
  rpc UpdateSaleTax(SaleTaxTotal)returns(Response){
    option(google.api.http) = {
      post:"/api/v1/products-manage/update-sales-tax"
      body: "*"
    };
  }
  // 查询税率变更日志
  rpc GetSaleTaxLog(SaleTaxLogRequest)returns(SaleTaxLogResponse){
    option(google.api.http) = {
      post:"/api/v1/products-manage/sales-tax-log"
      body: "*"
    };
  }
  //查询价格中心生效计划主表
  rpc GetPlan(GetPlanRequest)returns(GetPlanResponse){
    option(google.api.http) = {
      post: "/api/v1/products-manage/plan"
      body:"*"
    };
  }
  //查询价格中心生效计划主表
  rpc GetPlanItemList(GetPlanItemRequest)returns(GetPlanItemResponse){
    option(google.api.http) = {
      post: "/api/v1/products-manage/plan-item"
      body:"*"
    };
  }
  //撤销价格中心生效计划
  rpc CancelPlan(CancelPlanRequest)returns(Response){
    option(google.api.http) = {
      post: "/api/v1/products-manage/cancel-plan"
      body:"*"
    };
  }
  //更新价格中心生效计划明细
  rpc UpdatePlanItem(UpdatePlanItemRequest)returns(Response){
    option(google.api.http) = {
      post: "/api/v1/products-manage/update-plan-item"
      body:"*"
    };
  }
  // 添加订货类型组
  rpc AddOrderTypeGroup(AddOrderTypeGroupRequest)returns(Response){
    option(google.api.http) = {
      post: "/api/v1/products-manage/add-order-type"
      body:"*"
    };
  }
  // 查询订货类型组
  rpc QueryOrderTypeGroup(QueryOrderTypeGroupRequest)returns(QueryOrderTypeGroupResponse){
    option(google.api.http) = {
      post: "/api/v1/products-manage/query-order-type"
      body:"*"
    };
  }
  // 删除订货类型组
  rpc DeleteOrderTypeGroup(DeleteOrderTypeGroupRequest)returns(Response){
    option(google.api.http) = {
      post: "/api/v1/products-manage/delete-order-type"
      body:"*"
    };
  }
  // 更新订货类型组
  rpc UpdateOrderTypeGroup(UpdateOrderTypeGroupRequest)returns(Response){
    option(google.api.http) = {
      post: "/api/v1/products-manage/update-order-type"
      body:"*"
    };
  }
  // 查询订货类型组门店视角
  rpc QueryOrderTypeGroupByStore(QueryOrderTypeGroupRequest)returns(QueryOrderTypeGroupByStoreResponse){
    option(google.api.http) = {
      post: "/api/v1/products-manage/query-order-type-by-store"
      body:"*"
    };
  }
  // 更新订货类型组状态
  rpc UpdateOrderTypeGroupStatus(UpdateOrderTypeGroupStatusRequest)returns(Response){
    option(google.api.http) = {
      post: "/api/v1/products-manage/update-order-type-status"
      body:"*"
    };
  }
  rpc UpdateOrderTypeGroupBatch(UpdateOrderTypeGroupBatchRequest)returns(UpdateOrderTypeGroupBatchResponse){
    option(google.api.http) = {
      post: "/api/v1/products-manage/update-order-type-batch"
      body:"*"
    };
  }
  //门店可订货范围查询
  rpc QueryRangeOfOrder(QueryRangeOfOrderRequest)returns(QueryRangeOfOrderResponse){
    option(google.api.http) = {
      post: "/api/v1/products-manage/query-range-of-order"
      body:"*"
    };
  }
  // 导出配送规则(门店视角)
  rpc GetDistributionRuleExport(GetDistributionRuleRequest)returns(GetDistributionRuleExportResponse){
    option (google.api.http) = {
      post:"/api/v1/products-manage/distribution-rule-export"
      body: "*"
    };
  }
  // 订货规则导出
  rpc GetOrderRuleExport (GetOrderRuleByStoreRequest) returns (GetOrderRuleExportResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/order-rule-export"
      body: "*"
    };
  }

  // 门店订货范围, 价格变动报表
  rpc OrderRangePriceChangeReport (OrderRangeChangeRequest) returns (OrderRangeChangeResponse) {
    option (google.api.http) = {
      post: "/api/v1/products-manage/order-change-report"
      body: "*"
    };
  }
  // 主档数据同步
  rpc SyncMetaData(SyncMetaDataRequest)returns(Response){
    option (google.api.http) = {
      post: "/api/v1/products-manage/sync-mata-data"
      body: "*"
    };
  }
  // 查询免品上限
  rpc QueryFreeOrderLimit(QueryFreeOrderLimitRequest)returns(QueryFreeOrderLimitResponse){
    option (google.api.http) = {
      post: "/api/v1/products-manage/query-free-order-limit"
      body: "*"
    };
  }
}
message   SyncMetaDataRequest{
  //schema_name
  string schema_name = 1;
  //schema_id
  uint64  schema_id = 2;
  //操作类型
  string action = 3;
  google.protobuf.Struct fields_from = 4;
  google.protobuf.Struct fields_to = 5;
}

enum Status{
  //状态未定义
  Undefinition = 0;
  // 禁用
  DISABLED = 1 ;
  //可用
  ENABLED = 2;
}

message PageOrder {
  string order = 1;
  string sort = 2;
  int32 limit = 3;
  int32 offset = 4;
}

message   UpdatePriceStatusRequest{
  //id
  repeated uint64 products = 1;
  //0沽清 1制满 2 等待计划生效
  uint64 status = 2;
  //渠道id
  uint64 ditch_id = 3;
  repeated uint64  ids = 4;
  uint64 company_id = 5;
}

message   MoveSkuPriceRequest{
  //商品
  repeated uint64 products = 1;
  // 渠道id
  uint64 ditch_id = 3;
  // 门店id
  repeated uint64 store_ids = 5;
  uint64 price_type_id = 6;

  //目标价格id
  uint64 to_price_id = 7;
  //  uint64 price_type_id = 6;
  //  string tax_price = 7;//含税
  //  string tax_ratio = 8; //税率
  //  string price = 9; //未税
  //  string adjust_tax_price = 10; //核算含税
  //  string adjust_price = 11;//核算未含税
}


message  DeleteSkuPriceRequest{
  //商品
  repeated uint64 products = 1;
  //sku id
  repeated uint64 id = 2;
  // 渠道id
  uint64 ditch_id = 3;
  // 价格id
  repeated uint64 price_id = 4;
  // 门店id
  repeated uint64 store_ids = 5;
}



message   DeleteOrderRuleRequest{
  // @gotags: validate:"required_without=UniqueKey"
  repeated uint64 id = 2;
  repeated RuleUniqueKey unique_key = 3;
}



message   GetPriceSkuByProductDetailRequest{
  uint64 product_id = 1;
  uint64 unit_id = 2;
  uint64 adjust_unit_id = 3;
  //  uint64 tax_price = 4;
  // 渠道id
  uint64 ditch_id = 4;
  // 公司id
  uint64  company_id = 5;
}

// 商品纬度查看价格中心
message GetPriceSkuByProductDetailResponse {
  GetPriceSkuByProductList rows = 1;
  uint64 total = 2;
}

message   GetPriceSkuByProductRequest{
  // 筛选条件: 商品code, 商品名称
  string search = 1;
  repeated uint64 category_ids = 2;
  // 门店id
  uint64 store_id = 3;
  // 渠道id
  uint64 ditch_id = 4;
  PageOrder page = 5;
  repeated uint64 product_ids = 6;
  uint64  company_id = 7;
}

message   GetOrderRuleByProductRequest{
  // 筛选条件: 商品code, 商品名称
  string search = 1;
  // 商品类别ID
  repeated uint64 category_ids = 2;
  // 门店id
  uint64 store_id = 3;
  // 渠道id
  uint64 ditch_id = 4;
  uint64 company_id = 5;
  PageOrder page = 6;
}

message   GetOrderRuleByStoreRequest{
  // 筛选条件: 商品code, 商品名称
  string search = 1;
  // 商品类别ID
  repeated uint64 category_ids = 2;
  // 门店id
  uint64 store_id = 3;
  // 渠道id
  uint64 ditch_id = 4;
  uint64  company_id = 5;
  repeated uint64 product_ids = 6;
  PageOrder page = 7;
}
message   GetPriceSkuByStoreRequest{
  // 筛选条件: 商品code, 商品名称
  string search = 1;
  // 商品类别ID
  repeated uint64 category_ids = 2;
  // 门店id
  uint64 store_id = 3;
  // 渠道id
  uint64 ditch_id = 4;
  PageOrder page = 5;
  repeated uint64 product_ids = 6;
  uint64  company_id = 7;
}


//
message GetSkuProductInfoRequest {
  // 筛选条件: 商品code, 商品名称
  string search = 1;
  repeated uint64 product_ids = 2;
  //分页
  PageOrder page = 3;
  uint64 company_id = 4;
  // 按字段过滤
  google.protobuf.Struct filters = 5;
  string order = 6;
  string sort = 7;
  filter  filter = 8;

}

message filter {
  bool not_order_unit = 1 ;
}


//
message GetSkuProductInfoResponse {
  repeated GetSkuProductInfoList rows = 1;
  uint64 total = 2;
}

//
message  GetSkuProductInfoList{
  //id
  uint64  id = 1 ;
  //订货单位id
  uint64  unit_id = 2;
  //订货单位name
  string unit_name = 3;
  //核算单位id
  uint64 adjust_unit_id = 4;
  //核算单位name
  string adjust_unit_name = 5;
  //商品名称
  string product_name = 6;
  //商品编码
  string product_code = 7;
  //核算 含税价格
  string   adjust_tax_price = 8;
  //核算 未含税价格
  string   adjust_price = 9;
  //订货 未含税价格
  string   price = 10;
  //订货 含税价格
  string   tax_price = 11;
  //税率
  string   tax_ratio = 12;
  //单位转换率
  string   unit_rate = 13;
  //规格
  string  spec = 14;
  //商品分类id
  string product_category_id = 15;
}

enum OrderPriceType //枚举消息类型
{
  default_price = 0;
  order_price = 1;
  sell_price = 2;
}

message  UpdateTrussActivityStatusRequest{
  // @gotags: validate:"required"
  repeated uint64                     id = 9;
  // @gotags: validate:"required"
  Status                               status = 3; //0未设置 1未启用 2启用
}


message    UpdateTrussActivityProductRequest{
  // @gotags: validate:"required"
  uint64                     id = 9;
  // @gotags: validate:"required"
  uint64                    product_id = 6;
  // @gotags: validate:"required"
  repeated TrussActivityProduct TrussActivityProduct = 14;
}

message   AddOrUpdateTrussActivityRequest {
  // @gotags: validate:"required"
  string                    code = 1;
  // @gotags: validate:"required"
  string                    name = 2;
  // @gotags: validate:"required"
  Status                     status = 3; //0未设置 1未启用 2启用
  // @gotags: validate:"required"
  google.protobuf.Timestamp start_time = 4;
  // @gotags: validate:"required"
  google.protobuf.Timestamp end_time = 5;
  // @gotags: validate:"required"
  uint64                    product_id = 6;
  // @gotags: validate:"required"
  uint64                    company_id = 7;
  // @gotags: validate:"required"
  repeated uint64           type_id = 8;
  uint64                     id = 9;
  repeated TrussActivityProduct truss_activity_product = 10;
}


message   TrussActivityProduct{
  uint64  id = 1;
  string  product_name = 2;
  string  product_code = 3;
  string  product_id = 4;
  string  ratio = 5;
  string  truss_activity_id = 6;
  uint64  configure = 7; //0 未设置 1 进一 2 去尾
  ProductsList product = 8;
  string order_unit_name = 9;
  uint64 order_unit_id = 10;
  string model_name = 11;
}

message   GetTrussActivityResponse{
  repeated TrussActivity rows = 1;
  uint64 total = 2;
}

message   GetTrussActivityProductResponse{
  TrussActivity rows = 1;
  uint64 total = 2;
}

message  TrussActivity{
  string                    code = 1;
  string                    name = 2;
  Status                     status = 3; //0未设置 1未启用 2启用
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
  uint64                    product_id = 6;
  uint64                    company_id = 7;
  repeated uint64           type_id = 8;
  uint64                    id = 9;
  string                    product_name = 10;
  string                    product_code = 11;
  uint64                    updated = 12;
  string                    updated_name = 13;
  repeated TrussActivityProduct truss_activity_product = 14;
  string company_name = 15;
  int64  count = 16;
  string order_unit_name = 17;
  uint64 order_unit_id = 18;
  string model_name = 19;
}


message  GetTrussActivityProductRequest{
  // @gotags: validate:"required"
  uint64                    id = 1; //必传入
  PageOrder                 page = 2;
}

message   GetTrussActivityRequest{
  string                    code = 1;
  string                    name = 2;
  Status                     status = 3;
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
  uint64                    company_id = 6;
  PageOrder                 page = 7;
  repeated uint64  product_id = 8;
  bool   with_detail = 9;
}

// 获取加盟商可购买商品列表
message GetAgentProductsRequest {
  // 筛选条件: 商品code, 商品名称
  string search = 1;
  // 筛选当前商品类别
  uint64 category_id = 2;
  // 门店id
  uint64 store_id = 3;
  PageOrder page = 4;
  //所有商品类型 需要获取的商品个数
  repeated uint64  category_ids = 5;
  repeated uint64  product_ids = 6;

  uint64  type_id = 7;

  AgentProductsFilter agent_products_filter = 8;
  string price_type = 9;

  //是否需要库存数据
  bool  inventory_data = 10;

  //不需要返回的商品
  repeated uint64  exclusive_product_ids = 11;
  // 专门给商品主配的选择器使用
  repeated uint64  store_ids = 12;

}


message  AgentProductsFilter{
  int32 status = 1; //过滤的状态 -1返回全部 0过滤估清默认 1过滤置顶
  bool allow_order = 2; //可订货状态 true返回可订货 false全部返回
  bool tax_price_eq_less_zero = 3;//是否过滤小于等于0的价格
  bool  order_rule = 4;//订货规则过滤
  bool  order_rule_not_time_seconds = 5;//订货规则过滤 是否不过滤每天的时分秒区间
  google.protobuf.Timestamp  order_time = 6;
  bool truss_activity = 7; //是否过滤捆绑销售数据
}



// 获取加盟商可购买商品列表
message OrderTypeByPriceRequest {
  // 门店id
  uint64 store_id = 1;
  //商品id
  repeated uint64  product_ids = 2;
  //分页排序
  PageOrder page = 3;
  repeated uint64  type_ids = 4;
}

// 获取加盟商可购买商品列表
message OrderTypeByPriceResponse {
  repeated OrderTypeByPrice rows = 1;
  uint64 total = 2;
}


message OrderTypeByPrice {
  uint64 id = 1;
  // 订货类型id
  uint64 type_id = 2;
  //订货类型名称
  string  name = 3;
  //关联的门店和商品
  repeated StoreProductRelation storeProduct = 4;
  //订货类型编号
  string  code = 5;
  google.protobuf.Struct fields = 6;
}

//门店于商品关系
message   StoreProductRelation {
  //门店id
  uint64 store_id = 1;
  //商品id
  uint64 product_id = 2;
}




// 加盟商可购买商品列表
message GetAgentProductsResponse {
  ProductsCategoryList rows = 1;
  uint64 total = 2;
}



// 门店纬度查看价格中心
message GetPriceSkuByStoreResponse {
  repeated GetPriceSkuByStoreList rows = 1;
  uint64 total = 2;
}

message  GetPriceSkuByStoreList{
  //id
  uint64  id = 1 ;
  //租户
  uint64  partner_id = 2;
  //商品id
  uint64  product_id = 3;
  //订货单位id
  uint64  unit_id = 4;
  //单位转换率
  string   unit_rate = 5;
  //规格
  string  spec = 6;
  //核算单位id
  uint64 adjust_unit_id = 7;
  //核算单位name
  string adjust_unit_name = 8;
  //订货单位name
  string unit_name = 9;
  //商品名称
  string product_name = 10;
  //商品编码
  string product_code = 11;
  //核算 含税价格
  string   adjust_tax_price = 12;
  //核算 未含税价格
  string   adjust_price = 13;
  //订货 未含税价格
  string   price = 14;
  //订货 含税价格
  string   tax_price = 15;
  //税率
  string   tax_ratio = 16 ;
  //门店id
  uint64 store_id = 17;
  string status = 18;
  repeated  PriceInfo price_info = 19;
  string  code = 20;
  repeated PlanSkuByPriceSku plan_sku_by_price_sku = 21;
  //商品状态
  string product_status = 22;
}

// 商品纬度查看价格中心
message GetPriceSkuByProductResponse {
  repeated GetPriceSkuByProductList rows = 1;
  uint64 total = 2;
}



message   GetOrderRuleByStoreResponse{
  repeated GetOrderRuleByStoreList rows = 1;
  uint64 total = 2;
}


message  GetOrderRuleByStoreList{
  //id
  uint64  id = 1 ;
  //租户
  uint64  partner_id = 2;
  //商品id
  uint64  product_id = 3;
  //订货单位id
  uint64  unit_id = 4;
  //单位转换率
  string   unit_rate = 5;
  //规格
  string  spec = 6;
  //核算单位id
  uint64 adjust_unit_id = 7;
  //核算单位name
  string adjust_unit_name = 8;
  //订货单位name
  string unit_name = 9;
  //商品名称
  string product_name = 10;
  //商品编码
  string product_code = 11;
  //订货规则
  OrderRule order_rule = 12;
}


message   GetOrderRuleByProductResponse{
  repeated GetOrderRuleByProductList rows = 1;
  uint64 total = 2;
}


message  GetOrderRuleByProductList{
  //id
  uint64  id = 1 ;
  //租户
  uint64  partner_id = 2;
  //商品id
  uint64  product_id = 3;
  //订货单位id
  uint64  unit_id = 4;
  //单位转换率
  string   unit_rate = 5;
  //规格
  string  spec = 6;
  //核算单位id
  uint64 adjust_unit_id = 7;
  //核算单位name
  string adjust_unit_name = 8;
  //订货单位name
  string unit_name = 9;
  //商品名称
  string product_name = 10;
  //商品编码
  string product_code = 11;
  //订货规则
  repeated OrderRuleInfo order_rule = 13;

}
message OrderRuleInfo{
  OrderRule order_rule = 3;
  repeated OrderRuleIdInfo  ruleId = 4;
}

message OrderRuleIdInfo{
  uint64 order_rule_ids = 1;
  uint64 store_ids = 2;
}

message  GetPriceSkuByProductList{
  //id
  uint64  id = 1 ;
  //租户
  uint64  partner_id = 2;
  //商品id
  uint64  product_id = 3;
  //订货单位id
  uint64  unit_id = 4;
  //单位转换率
  string   unit_rate = 5;
  //规格
  string  spec = 6;
  //核算单位id
  uint64 adjust_unit_id = 7;
  //核算单位name
  string adjust_unit_name = 8;
  //订货单位name
  string unit_name = 9;
  //商品名称
  string product_name = 10;
  //商品编码
  string product_code = 11;
  //状态
  string status = 13;
  repeated  PriceInfo price_info = 14;
  //税率
  string   tax_ratio = 15 ;
  repeated PlanSkuByPriceSku plan_sku_by_price_sku = 16;
  //商品状态
  string product_status = 17;
}


message   PlanSkuByPriceSku{
  uint64 id = 1; //id
  string  code = 2; //编号
  repeated  PriceInfo price_info = 3; //价格
  int64  effective_time = 4;//生效时间

}

message    PriceInfo {
  //核算 含税价格
  string   adjust_tax_price = 1;
  //核算 未含税价格
  string   adjust_price = 2;
  //包含门店数量
  uint32  store_count = 3;
  //订货 未含税价格
  string   price = 4;
  //订货 含税价格
  string   tax_price = 5;
  //税率
  string   tax_ratio = 6 ;
  //价格id
  uint64  id = 7;
  uint64  price_type_id = 8;
  string  price_type_name = 9;
  repeated GetPriceSkuByStoreList sku_by_store = 10;
  uint64 sku_id = 11;
  //核算单位name
  string adjust_unit_name = 12;
  //订货单位name
  string unit_name = 13;
}




// 商品分类列表
message ProductsCategoryList {
  // 商品分类信息
  repeated ProductCategory       category = 1;
  // 商品列表
  repeated ProductsList list = 2;
}

// 商品分类信息
message ProductCategory {
  // 分类id
  uint64 id = 1;
  // 该分类下的总数
  uint32 total = 2;
}

// 商品列表
message ProductsList {
  //id和商品id
  uint64 id = 1;
  // 商品名称 - 修改/添加时不传
  string name = 2;
  // 商品code - 修改/添加时不传
  string code = 3;
  // 商品价格
  string tax_price = 4;
  // 规格名称 - 修改/添加时不传
  string spec = 5;
  // 商品id - 修改/添加时不传
  uint64 unit_id = 6;
  // 商品单位 - 修改/添加时不传
  string unit_name = 7;
  //0未删除 1已删除
  //  uint32 is_delete = 8;
  //核算
  uint64 adjust_unit_id = 8;
  string adjust_unit_name = 9;
  //  float adjust_tax_price = 10;
  //订货规则
  OrderRule order_rule = 10;
  //配送规则
  DistributionRule distribution_rule = 11;
  // 门店id
  repeated uint64 stores_id = 12;
  repeated ProductPrice product_price = 13;
  //税率
  string tax_ratio = 14;
  //test
  repeated uint64 test = 15;
  //销售数量
  string sales_qty = 16;
  //实时库存
  string  real_qty = 17;
  //待收货数量
  string wait_receiving_qty = 18;
  //可订货
  bool  allow_order = 19;
  //商品分类id
  string product_category_id = 20;
  //商品分类code
  string product_category_code = 21;
  //商品分类name
  string product_category_name = 22;
  // 门店id
  uint64 store_id = 23;
  // 门店code
  string store_code = 24;
  // 门店name
  string store_name = 25;
  //状态
  uint32 status = 26;
  // 商品类别
  repeated uint64 category_ids = 27;
  repeated uint64 type_ids = 28;
  //预计到货数量
  string expire_quantity = 29;
  repeated TrussActivityProduct truss_activity_product = 30;
}


message ProductPrice{
  //含税价格
  string tax_price = 1;
  uint64 price_type_id = 2;
  string  price_type_name = 3;
  //税率
  string tax_ratio = 4;
  //单位转换
  string adjust_tax_price = 5;
  uint64  id = 6;
  //税率
  string unit_rate = 7;
}


// 规则组
message GroupOrderRuleData {
  // 商品id
  uint64 id = 1;
  // 商品名称 - 修改/添加时不传
  string name = 2;
  // 商品code - 修改/添加时不传
  string code = 3;
  // 规格名称 - 修改/添加时不传
  string spec = 4;
  // 商品单位id - 修改/添加时不传
  uint64 unit_id = 5;
  // 商品单位 - 修改/添加时不传
  string unit_name = 6;
  // 单位比率
  string unit_rate = 7;
  //0未删除 1已删除
  uint32  is_delete = 8;
  //核算
  uint64 adjust_unit_id = 9;
  string adjust_unit_name = 10;
  // 商品类别
  repeated uint64 category_ids = 11;
  //订货规则
  OrderRule order_rule = 12;
}


message OrderRule {
  //起订量
  string min_qty = 1;
  //最大量
  string  max_qty = 2;
  //递增量
  string increase_qty = 3;
  // 订货日期规则
  string  data_rule = 4 ;
  //核算
  string adjust_min_qty = 5;
  string adjust_max_qty = 6;
  string adjust_increase_qty = 7;
}

// 添加门店商品价格请求
message AddProductPriceRequest {
  // 渠道id
  uint64 ditch_id = 1;
  // 公司id
  uint64 company_id = 2;
  // 商品列表
  repeated ProductsList products = 3;
  // 生效方式 必传
  EffectiveType   effective_type = 4;
  // 生效日期 生效方式为计划生效时必传
  int64 effective_date = 5;
}
enum EffectiveType{
  //初始值意义
  EffectiveTypeInit = 0;
  // 立即执行
  EffectiveTypeImmediate = 1;
  //计划执行
  EffectiveTypePlaned = 2 ;
}

// 添加门店商品价格请求
message AddPlanRequest {
  // 渠道id
  uint64 ditch_id = 1;
  // 公司id
  uint64 company_id = 2;
  // 商品列表
  repeated PlanList plans = 3;
}

message   PlanList{
  uint64  id = 1;
  uint64  partner_id = 2;
  uint64  code = 3;
  uint64 effective_time = 4;
  uint64  plan_status = 5;
  string  reason = 6;
}

// 添加门店商品价格请求
message UpdateOrderRuleRequest {
  // 渠道id
  uint64 ditch_id = 1;
  // 规则列表
  repeated  OrderRuleModel order_rule = 4;
  // ture 不进行数据库插入
  bool is_check = 5;
}

message   OrderRuleCheckResponse{
  repeated  OrderRuleCheckRow rows = 1 ;
  uint64  total = 2;
}

message   OrderRuleCheckRow{
  //id
  uint64  id = 1 ;
  //租户
  uint64  partner_id = 2;
  //商品id
  uint64  product_id = 3;
  //订货单位id
  uint64  unit_id = 4;
  //单位转换率
  string   unit_rate = 5;
  //规格
  string  spec = 6;
  //核算单位id
  uint64 adjust_unit_id = 7;
  //核算单位name
  string adjust_unit_name = 8;
  //订货单位name
  string unit_name = 9;
  //商品名称
  string product_name = 10;
  //商品编码
  string product_code = 11;
  //门店ID
  uint64 store_id = 12;
  //门店Code
  string store_code = 13;
  //门店name
  string store_name = 14;
  //原有订货规则
  OrderRule old_rule = 15;
  //新订货规则
  OrderRule new_rule = 16;
  // 渠道id
  uint64 ditch_id = 17;
}

// 添加门店商品价格请求
message AddOrderRuleRequest {
  // 渠道id
  uint64 ditch_id = 1;
  // 规则列表
  repeated OrderRuleModel order_rule = 2;
  // 冲突处理（true 只新增 false 覆盖）
  bool only_add = 3;
  // ture 不进行数据库插入
  bool is_check = 4;
}

message OrderRuleModel{
  // 规则id
  int64 id = 1;
  //商品id
  int64  product_id = 2 ;
  // 商品名称 - 修改/添加时不传
  string name = 3;
  // 商品code - 修改/添加时不传
  string code = 4;
  // 商品价格
  string price = 5;
  // 规格名称 - 修改/添加时不传
  string spec = 6;
  // 商品id - 修改/添加时不传
  uint64 unit_id = 7;
  // 商品单位 - 修改/添加时不传
  string unit_name = 8;
  //核算
  uint64 adjust_unit_id = 11;
  string adjust_unit_name = 12;
  string adjust_tax_price = 13;
  //订货规则
  OrderRule order_rule = 14;
  //单独新增接口的时候 不传入
  int64 store_id = 15;
  // 门店id list(新增/更新时传入)
  repeated uint64 stores_id = 16;
}


message   UpdateSkuPriceByProductRequest{
  // 渠道id
  uint64 ditch_id = 1;
  //公司id
  uint64  company_id = 2;
  // 生效方式 必传
  EffectiveType   effective_type = 3;
  // 生效日期 生效方式为计划生效时必传
  int64 effective_date = 4;
  repeated GetPriceSkuByProductList products = 5;

}

message   UpdatePriceRequest{
  // 渠道id
  uint64 ditch_id = 1;
  //公司id
  uint64  company_id = 2;
  // 生效方式 必传
  EffectiveType   effective_type = 3;
  // 生效日期 生效方式为计划生效时必传
  int64 effective_date = 4;
  repeated GetPriceSkuByStoreList products = 5;
  //  uint64 total = 2;
  // 商品列表
  //  repeated UpdatePriceSku products = 3;
}


message  UpdatePriceSku{
  int64 id = 1;
  int64  store_id = 2;
  int64 product_id = 3;
  int64 unit_id = 4;
  string unit_rate = 5;
  string spec = 6;
  uint32 is_delete = 7;
  int64 adjust_unit_id = 8;
  string adjust_unit_name = 9;
  string unit_name = 10;
  string product_name = 11;
  string product_code = 12;
  uint32 status = 13;
  repeated ProductPrice price_info = 14;
}

message  UpdatePrice{
  int64 id = 1;
  //必须传入
  int64 sku_id = 2;
  string  price = 3;
  string  tax_price = 4;
  string  tax_ratio = 5;
  string  adjust_tax_price = 7;
  uint32  is_delete = 8;
}


message  DataChangeGroupRequest{
  // 公司 id 必传
  uint64 company_id = 1;
  // 渠道 id 必传
  uint64 distrcenter_id = 2 ;
  string code = 3;
  string name = 4;
  //1价格组 2规则组  必传
  uint32 group_type = 5 ;
  //需要保存成租的门店  必传
  repeated uint64 store_ids = 6;
}

message  AddGroupRequest{
  uint64 company_id = 1;
  string code = 2;
  string name = 3;
  string  remark = 4;
  //1价格组 2规则组
  uint32 group_type = 5 ;
  //组里生效的门店
  repeated uint64 store_ids = 6;
  //组里的商品
  repeated ProductsList group_product = 7;
}

message  UpdateGroupRequest{
  uint64 company_id = 1;
  string code = 2;
  string name = 3;
  string  remark = 4;
  //1价格组 2订货规则组 3配送规则组
  uint32 group_type = 5 ;
  //组里生效的门店 全部传入
  repeated GroupStore stores = 6;
  //组里的商品 全部传入
  repeated ProductsList group_product = 7;
  uint64 id = 8;
}

message  GetGroupRequest{
  string name = 1;
  string code = 2;
  string product_name = 3;
  string product_code = 4;
  uint64 company_id = 7;
  PageOrder page = 5;
  uint32 group_type = 6;
}

message  GetGroupProductRequest{
  uint64 group_id = 1;
  string product_name = 2;
  string product_code = 3;
  repeated uint64 category_ids = 4;
  repeated uint64 return_product_ids = 5;
  PageOrder page = 6;
  // 传入分组类型
  uint32 group_type = 7;
  uint64  company_id = 8;
}

message  GetGroupOrderRuleRequest{
  uint64 group_id = 1;
  string product_name = 2;
  string product_code = 3;
  repeated uint64 category_ids = 4;
  repeated uint64 return_product_ids = 5;
  PageOrder page = 6;
}

message  GetGroupResponse{
  uint32 code = 1;
  string message = 2;
  repeated Group rows = 3;
  uint64 total = 4;
}

//message   GetGroup{
//  uint64  id = 1;
//  uint64  partner_id = 2;
//  uint64  company_id = 3;
//  string  code = 4;
//  string  name = 5;
//  string  remark = 6;
//  uint32  group_type = 7;//1价格组 2规则组
//  uint64  updated_by = 8;
//  uint64  created_by = 9;
//  uint64 created = 10;
//  uint64 updated = 11;
//}


message  GetGroupOrderRuleResponse{
  uint32 code = 1;
  string message = 2;
  Group rows = 3;
  uint64 total = 4;
}



message  Group{
  uint64  id = 1;
  uint64  partner_id = 2;
  uint64  company_id = 3;
  string  code = 4;
  string  name = 5;
  string  remark = 6;
  //1价格组 2订货规则组 3配送规则组
  uint32  group_type = 7;
  uint64  updated_by = 8;
  uint64  created_by = 9;
  uint64 created = 10;
  uint64 updated = 11;
  //分组商品
  repeated ProductsList group_product = 12;
  //分组订单规则
  repeated GroupOrderRuleData group_order_rule_data = 13;
  //分组配送规则
  repeated GroupDistributionData group_distribution_rule_data = 14;
  string created_name = 15;
  string updated_name = 16;
  //商品数量
  uint64  product_count = 17;
  string  company_name = 18;
}

message GroupDistributionData {
  // 商品id
  uint64 id = 1;
  // 商品名称 - 修改/添加时不传
  string name = 2;
  // 商品code - 修改/添加时不传
  string code = 3;
  // 规格名称 - 修改/添加时不传
  string spec = 4;
  // 商品id - 修改/添加时不传
  uint64 unit_id = 5;
  // 商品单位 - 修改/添加时不传
  string unit_name = 6;
  //0未删除 1已删除
  uint32  is_delete = 7;
  //核算
  uint64 adjust_unit_id = 8;
  string adjust_unit_name = 9;
  // 商品类别
  repeated uint64 category_ids = 10;
  //配送规则
  DistributionRule distribution_rule = 11;
}


message  GetGroupProductResponse{
  uint32 code = 1;
  string message = 2;
  Group rows = 3;
  uint64 total = 4;
}


message GroupStore{
  //修改操作时传入
  uint64 id = 4;
  uint64 group_id = 2;
  uint64 store_id = 5; //
}

message  Response{
  uint32 code = 1;
  string message = 2;
}


message ProductPriceCheckRow {
  // 价格中心id
  uint64 id = 1;
  //商品id
  uint64  product_id = 2 ;
  // 商品名称 - 修改/添加时不传
  string name = 3;
  // 商品code - 修改/添加时不传
  string code = 4;
  // 门店id
  uint64 store_id = 5;
  // 门店code
  string store_code = 6;
  // 门店name
  string store_name = 7;
  //旧的价格
  string old_tax_price = 8;
  //新的价格
  string new_tax_price = 9;
  int64  price_type_id = 10;
}

message   ProductPriceCheckResponse{
  repeated  ProductPriceCheckRow rows = 1 ;
  uint64    total = 2;
}

// 配送规则
message DistributionRuleModel{
  // 规则id
  uint64 id = 1;
  //商品id
  uint64  product_id = 2 ;
  // 商品名称 - 修改/添加时不传
  string name = 3;
  // 商品code - 修改/添加时不传
  string code = 4;
  // 规格名称 - 修改/添加时不传
  string spec = 5;
  // 商品id - 修改/添加时不传
  uint64 unit_id = 6;
  // 商品单位 - 修改/添加时不传
  string unit_name = 7;
  // 仓库ID必传
  uint64 distrcenter_id = 8;
  // 公司ID 必传
  uint64 company_id = 9;
  //false 存在 true删除
  uint32 is_delete = 10;
  //核算
  uint64 adjust_unit_id = 11;
  string adjust_unit_name = 12;
  string adjust_tax_price = 13;
  //配送规则
  DistributionRule rule = 14;
  //单独新增接口的时候 不传入
  uint64 store_id = 15;
  // 门店id list(新增/更新时传入)
  repeated uint64 stores_id = 16;
  // 物流模式
  string distr_type = 17 ;
}

message DistributionRule{
  //到货天数
  string planned_arrival_days = 1 ;
  // 物流模式
  string distr_type = 2 ;
  // 仓库ID
  uint64 distrcenter_id = 3;
}

message AddDistributionRuleRequest{
  // 渠道 id 必传
  uint64 distrcenter_id = 1 ;
  // 公司ID 必传
  uint64 company_id = 2;
  // 物流模式
  string distr_type = 3 ;
  // 配送规则
  repeated DistributionRuleModel distribution_rule = 4;
  // 冲突处理（true 只新增 false 覆盖）
  bool only_add = 5;
  // ture 不进行数据库插入
  bool is_check = 6;
}
message  UpdateDistributionRuleRequest{
  // 渠道id
  uint64 distrcenter_id = 1 ;
  // 公司ID 必传
  uint64 company_id = 2;
  // 物流模式
  string distr_type = 3 ;
  repeated DistributionRuleModel distribution_rule = 4;
  // ture 不进行数据库插入
  bool is_check = 5;
}
message  DeleteDistributionRuleRequest{
  // @gotags: validate:"required_without=UniqueKey"
  repeated uint64  id = 2;
  repeated RuleUniqueKey unique_key = 3;
}
message  RuleUniqueKey{
  // @gotags: validate:"required"
  uint64 store_id = 1;
  // @gotags: validate:"required"
  uint64 product_id = 2;
}

message   GetDistributionRuleByProductRequest{
  // 筛选条件: 商品code, 商品名称
  string search = 1;
  repeated uint64 category_ids = 2;
  // 仓库ID必传
  uint64 distrcenter_id = 3;
  // 公司ID 必传
  uint64 company_id = 4;
  PageOrder page = 5;
}
message DistributionRuleCheckResponse{
  repeated  DistributionRuleCheckRow rows = 1;
  uint64  total = 2;
}
message DistributionRuleCheckRow{
  // 规则id
  uint64 id = 1;
  //商品id
  uint64  product_id = 2 ;
  // 商品名称 - 修改/添加时不传
  string product_name = 3;
  // 商品code - 修改/添加时不传
  string product_code = 4;
  // 规格名称 - 修改/添加时不传
  string spec = 5;
  // 商品id - 修改/添加时不传
  uint64 unit_id = 6;
  // 商品单位 - 修改/添加时不传
  string unit_name = 7;
  // 仓库ID必传
  uint64 distrcenter_id = 8;
  // 公司ID 必传
  uint64 company_id = 9;
  //false 存在 true删除
  uint32 is_delete = 10;
  //核算
  uint64 adjust_unit_id = 11;
  string adjust_unit_name = 12;
  string adjust_tax_price = 13;
  //配送规则
  DistributionRule new_rule = 14;
  DistributionRule old_rule = 15;
  //门店ID
  uint64 store_id = 17;
  //门店编码
  string store_code = 18;
  //门店名称
  string store_name = 19;
  // 物流模式
  string distr_type = 20 ;
}
message  GetDistributionRuleByProductResponse{
  repeated  GetDistinctRuleByProductList rows = 1;
  uint64    total = 2;
}

message   GetDistinctRuleByProductList{
  //租户iD
  uint64 partner_id = 1;
  // 渠道ID
  uint64  distrcenter_id = 2;
  // 商品ID
  uint64 product_id = 3;
  //商品名称
  string  product_name = 4;
  //商品编码
  string product_code = 5;
  //规格
  string spec = 6;
  //单位ID
  uint64 unit_id = 7;
  //单位名称
  string unit_name = 8;
  //公司ID
  uint64 company_id = 9;
  // 物流模式
  string distr_type = 10 ;
  //规则
  repeated DistributionRuleInfo  distribution_rule = 11;

}
message DistributionRuleInfo{
  DistributionRule distribution_rule = 3;
  repeated DistributionRuleIdInfo  ruleId = 4;
}

message DistributionRuleIdInfo{
  uint64 distribution_rule_ids = 1;
  uint64 store_ids = 2;
}
message    GetDistributionRuleRequest{
  //  除page外，查询时至少提供一个参数
  // 筛选条件: 商品code, 商品名称
  string search = 1;
  repeated uint64 category_ids = 2;
  repeated uint64 store_ids = 3;
  // 仓库ID
  uint64 distrcenter_id = 4;
  // 公司ID
  uint64 company_id = 5;
  PageOrder page = 6;
  repeated uint64 product_ids = 7 ;
}

message GetDistributionRuleResponse{
  repeated  DistributionRuleModel rows = 1;
  uint64    total = 2;
}

//税率
message GetSaleTaxListRequest{
  // 筛选条件: 税率规则code/name
  string search = 1;
  //税率规则ID
  repeated uint64 id = 2;
  //商品信息：需要提供商品ID和商品属性编码
  repeated  ItemValues  item_values = 3;
  //公司ID
  repeated  uint64  company_ids = 4;
  //状态
  string    status = 5; //状态
  //分页信息
  PageOrder  page = 6;
}
message ItemValues{
  //商品属性编码
  string  product_type = 1;
  //商品id
  uint64  product_id = 2;
}
message GetSaleTaxListResponse{
  repeated  SaleTaxTotal rows = 1;
  uint64    total = 2;
}

message SaleTaxTotal{
  //税率规则ID 新增时不需传
  uint64 id = 1;
  //租户iD
  uint64 partner_id = 2;
  //税率规则编号
  string code = 3;
  //税率规则名称
  string  name = 4 ;
  //税率
  string  rate = 5;
  //公司ID
  uint64 company_id = 6;
  //公司名称
  string company_name = 7;
  //更新人
  int64  updated_by = 8;
  //更新时间
  int64  updated = 9;
  //税率规则明细
  repeated SaleTaxItem items = 10;
  //状态 新增是不需要传
  string status = 11 ;
  //生效日期
  int64 effective_date = 12  ;
  // 是否全部门店 1 是 0 否（新增、更新时传入）
  uint32  is_all = 13;
  // 备注
  string  remark = 14;
  //创建人名称
  string created_name = 15;
  //更新人信息
  string updated_name = 16;
}

message SaleTaxItem{
  //税率规则ID
  uint64 sales_tax_rule_id = 1;
  //明细类型：'1:属性、2：商品',
  int32  item_type = 2;
  //商品id   item_type 为1 时为空
  uint64 product_id = 3;
  //商品编码 item_type 为1 时为空
  string product_code = 4;
  //商品名称 item_type 为1 时为空
  string product_name = 5;
  //商品规格 //item_type 为1 时为空
  string spec = 6;
  //属性编码  item_type 为2 时为空
  string product_type = 7;
  //明细ID
  uint64 id = 8;
  // 明细状态
  uint32  is_delete = 9;
}

message SaleTaxLogRequest{
  uint64 doc_id = 1;
}
message SaleTaxLogResponse{
  repeated ExecLog log = 1;
}

message ExecLog{
  //日志变更类型
  LogData log_data = 1;
  //变更时间
  int64 updated = 2;
  //变更人ID
  int64 updated_by = 3;
  //变更人姓名
  string updated_name = 4;
  //操作类型
  string operation_type = 5;
  //单据
  uint64 doc_id = 6;
}
message LogData{
  //更新信息
  repeated  FieldInfo updated = 1;
  //新增信息
  repeated  FieldInfo inserted = 2;
  //删除信息
  repeated  FieldInfo deleted = 3;
}
message FieldInfo{
  //字段名称
  string field_name = 1;
  //原有值
  string old_value = 2;
  //更新值
  string new_value = 3;
  //操作类型
}

message GetPlanRequest{
  // 渠道id
  uint64 ditch_id = 1;
  // 公司id
  uint64 company_id = 2;
  //  状态 0待执行 1执行失败 2 已撤销 3成功
  string plan_status = 7;
  //分页条件
  PageOrder page = 4 ;
}
message GetPlanResponse{
  uint32 code = 1;
  string message = 2;
  repeated PlanModel rows = 3;
  uint64 total = 4;
}
message PlanModel{
  // 计划ID
  uint64 id = 1 ;
  //租户ID
  uint64 partner_id = 2;
  // 渠道id
  uint64 ditch_id = 3;
  // 公司id
  uint64 company_id = 4;
  // 计划编码
  string code = 5 ;
  // 生效计划
  int64  effective_time = 6 ;
  //  状态 0待执行 1执行失败 2 已撤销 3成功
  string plan_status = 7;
  //执行备注
  string reason = 8;
  //创建人ID
  uint64 created_by = 9;
  string created_name = 10;
  //创建时间
  int64  created = 11;
  //更新人ID
  uint64 updated_by = 12;
  //更新人名称
  string  updated_name = 13;
  //更新时间
  int64  updated = 14;
}
message GetPlanItemRequest{
  uint64  plan_id = 1;
  PageOrder page = 2;
}
message GetPlanItemResponse{
  repeated GetPlanItemList rows = 1;
  uint64 total = 2;
}

message GetPlanItemList{

  // 计划ID
  uint64  plan_id = 1;
  //sku_id
  uint64  id = 2 ;
  //租户
  uint64  partner_id = 3;
  // 渠道id
  uint64 ditch_id = 4;
  // 公司id
  uint64 company_id = 5;
  //商品id
  uint64  product_id = 6;
  //订货单位id
  uint64  unit_id = 7;
  //单位转换率
  string   unit_rate = 8;
  //规格
  string  spec = 9;
  //核算单位id
  uint64 adjust_unit_id = 10;
  //核算单位name
  string adjust_unit_name = 11;
  //订货单位name
  string unit_name = 12;
  //商品名称
  string product_name = 13;
  //商品编码
  string product_code = 14;
  //状态
  string status = 15;
  repeated  PriceInfo price_info = 16;
  //税率
  string   tax_ratio = 17 ;
  // 计划编号
  string code = 18;
  // 计划状态
  string plan_status = 19;
  //创建人ID
  uint64 created_by = 20;
  string created_name = 21;
  //创建时间
  int64  created = 22;
  //更新人ID
  uint64 updated_by = 23;
  //更新人名称
  string  updated_name = 24;
  //更新时间
  int64  updated = 25;
  // 生效时间
  int64  effective_time = 26  ;
}
message CancelPlanRequest{
  uint64  plan_id = 1;
}
message UpdatePlanItemRequest{
  //计划id
  uint64  plan_id = 1;
  // 渠道id
  uint64 ditch_id = 3;
  // 公司id
  uint64 company_id = 4;
  repeated GetPlanItemList  products = 2;
}

//订货类型组
message AddOrderTypeGroupRequest{
  //订货类型id
  uint64 type_id = 1;
  //商品ids
  repeated uint64 product_ids = 2;
  //门店ids
  repeated uint64 store_ids = 3;
}
message QueryOrderTypeGroupRequest{
  //视角，枚举值（product，store）
  string visual_angle = 1;
  string product_name = 2;
  string product_code = 3;
  string store_name = 4;
  string store_code = 5;
  uint64 product_id = 6;
  uint64 store_id = 7;
  uint64 type_id = 8;
  PageOrder page = 9;
  //是否返回详情
  bool with_detail = 10;
  uint64 company_id = 11;
}
message QueryOrderTypeGroupResponse{
  repeated  OrderTypeGroup rows = 1;
  uint64 total = 2;
}
message QueryOrderTypeGroupByStoreResponse{
  repeated  OrderTypeGroupByStore rows = 1;
  uint64 total = 2;
}
message OrderTypeGroupByStore{
  //门店
  string store_name = 14;
  string store_code = 15;
  uint64 store_id = 16;
  //订货类型
  string type_name = 17;
  string type_code = 18;
  //订货类型时段
  repeated string time_bucket = 19;
  uint64 type_id = 20;
  repeated EntityDetail details = 21;
  int64 total = 22;
  string status = 23;
}
message OrderTypeGroup{
  uint64 id = 1;
  uint64 partner_id = 2;
  //商品
  string product_name = 3;
  string product_code = 4;
  //商品规格
  string spec = 5;
  uint64 product_id = 6;
  //订货单位
  string unit_name = 7;
  string unit_code = 8;
  uint64 unit_id = 9;

  //门店
  string store_name = 14;
  string store_code = 15;
  uint64 store_id = 16;
  int64 total = 17;
  //聚合详情
  repeated OrderTypeGroupChildrenDetail aggregation_detail = 18;
}
message EntityDetail {
  string product_name = 1;
  string product_code = 2;
  //商品规格
  string spec = 3;
  uint64 product_id = 4;
  string store_name = 5;
  string store_code = 6;
  uint64 store_id = 7;
  //订货单位
  string unit_name = 8;
  string unit_code = 9;
  uint64 unit_id = 10;
}
message OrderTypeGroupChildrenDetail{
  //订货类型
  string type_name = 1;
  string type_code = 2;
  //订货类型时段
  repeated string time_bucket = 3;
  uint64 type_id = 4;
  repeated EntityDetail entitys = 5;
  int64 total = 6;
  string status = 7;
}
message DeleteOrderTypeGroupRequest{
  uint64 product_id = 1;
  uint64 store_id = 2;
  //订货类型id
  uint64 type_id = 3;
  //视角，枚举值（product，store）
  string visual_angle = 4;
  uint64 company_id = 5;
}
message UpdateOrderTypeGroupRequest{
  uint64 product_id = 1;
  uint64 store_id = 2;
  string visual_angle = 3;
  repeated OrderTypeGroupChildrenDetail aggregation_detail = 4;
}
message UpdateOrderTypeGroupStatusRequest{
  uint64 product_id = 1;
  uint64 store_id = 2;
  uint64 type_id = 3;
  string visual_angle = 4;
  string status = 5;
}
message UpdateOrderTypeGroupBatchRequest{
  map<int32, UpdateOrderTypeGroupRequest>  batch = 1;
}
message UpdateOrderTypeGroupBatchResponse{
  uint32 code = 1;
  map<int32, string> err_message = 2;
}
message QueryRangeOfOrderRequest{
  //订货类型时段
  // @gotags: validate:"required"
  string order_date = 1;
  // @gotags: validate:"required"
  uint64 store_id = 2;
  // @gotags: validate:"required"
  uint64 type_id = 3;
  uint64 price_id = 4;
  PageOrder page = 5;
  repeated uint64 store_ids = 6;
  repeated uint64 type_ids = 7;
  repeated uint64 product_ids = 8;
}
message RangeOfOrder{
  //门店
  string store_name = 1;
  string store_code = 2;
  uint64 store_id = 3;
  string product_name = 4;
  string product_code = 5;
  //商品规格
  string spec = 6;
  uint64 product_id = 7;
  //订货单位
  string unit_name = 8;
  string unit_code = 9;
  uint64 unit_id = 10;
  //订货类型
  string type_name = 11;
  string type_code = 12;
  //订货类型时段
  string order_date = 13;
  uint64 type_id = 14;
  //价格类型
  string price_type = 15;
  string order_price = 16;
  //订货规则
  //起订量
  string min_qty = 17;
  //最大量
  string  max_qty = 18;
  //递增量
  string increase_qty = 19;
  //仓库
  uint64 distrcenter_id = 20;
  string distrcenter_name = 21;
  string arrival_days = 22;
}
message QueryRangeOfOrderResponse{
  repeated  RangeOfOrder rows = 1;
  int64 total = 6;
}

message GetDistributionRuleExportResponse{
  repeated  DistributionRuleExportRow rows = 1;
  uint64    total = 2;
}

message    DistributionRuleExportRow{
  // 规则id
  uint64 id = 1;
  //商品id
  uint64  product_id = 2 ;
  // 商品名称
  string name = 3;
  // 商品code
  string code = 4;
  // 规格名称
  string spec = 5;
  // 商品id
  uint64 unit_id = 6;
  // 商品单位
  string unit_name = 7;
  // 仓库ID
  uint64 distrcenter_id = 8;
  //仓库/加工中心code
  string distrcenter_code = 9;
  //仓库name
  string distrcenter_name = 10;
  // 公司ID
  uint64 company_id = 11;
  // 公司编码
  string company_code = 12;
  // 公司名称
  string company_name = 13;
  //到货天数
  string planned_arrival_days = 14;
  //门店ID
  uint64 store_id = 15;
  //门店编码
  string store_code = 16;
  //门店编码
  string store_name = 17;
  //配送类型名称
  string distr_type_name = 18 ;
}
message GetOrderRuleExportResponse{
  repeated  OrderRuleExportRow rows = 1;
  uint64    total = 2;
}
message OrderRuleExportRow{
  //id
  uint64  id = 1 ;
  //商品id
  uint64  product_id = 2;
  //商品名称
  string product_name = 3;
  //商品编码
  string product_code = 4;
  //订货单位id
  uint64  unit_id = 5;
  string unit_name = 6;
  //规格
  string  spec = 7;
  // 公司ID
  uint64 company_id = 8;
  // 公司编码
  string company_code = 9;
  // 公司名称
  string company_name = 10;
  //渠道iD
  uint64 ditch_id = 11;
  //渠道名称
  string ditch_name = 12;
  //渠道编码
  string ditch_code = 13;
  //门店ID
  uint64 store_id = 14;
  //门店名称
  string store_name = 15;
  //门店编码
  string store_code = 16;

  //起订量(订货单位)
  string min_qty = 17;
  //最大量（订货单位）
  string  max_qty = 18;
  //递增量（订货单位）
  string increase_qty = 19;
  // 订货日期规则
  string  data_rule_text = 20 ;
  //核算单位id
  uint64 adjust_unit_id = 21;
  //核算单位name
  string adjust_unit_name = 22;
  //起订量(订货单位)
  string adjust_min_qty = 23;
  //最大量（订货单位）
  string  adjust_max_qty = 24;
  //递增量（订货单位）
  string adjust_increase_qty = 25;
}
//订货单位name

message OrderRangeChangeRequest{
  // 基准日
  // @gotags: validate:"required"
  string base_date = 1;
  // 门店id
  // @gotags: validate:"required"
  uint64 store_id = 2;
  // @gotags: validate:"required"
  uint64 type_id = 3;
  uint64 price_id = 4;
  PageOrder page = 5;
  repeated uint64 store_ids = 6;
  repeated uint64 type_ids = 7;
  repeated uint64 product_ids = 8;
  // 比对日
  // @gotags: validate:"required"
  string compare_date = 9;
  // 是否实时 (true- 勾选实时, false-未勾选)
  bool realtime = 10;
  // 默认传 DIFF- 只看变动商品， 传ALL - 查看全部商品
  string include = 11;
}

message OrderRangeChangeResponse {
  repeated  OrderRangeChange rows = 1;
  int64 total = 6;
}

message OrderRangeChange {
  //门店
  string store_name = 1;
  string store_code = 2;
  uint64 store_id = 3;
  string product_name = 4;
  string product_code = 5;
  //商品规格
  string spec = 6;
  uint64 product_id = 7;
  //订货单位
  string unit_name = 8;
  string unit_code = 9;
  uint64 unit_id = 10;
  //订货类型
  string type_name = 11;
  string type_code = 12;
  //订货类型时段
  string order_date = 13;
  uint64 type_id = 14;
  //价格类型
  string price_type = 15;
  // 基准日订货价格
  string base_order_price = 16;
  // 基准日零售价格
  string base_retail_price = 17;
  // 比对日订货价格
  string compare_order_price = 18;
  // 比对日零售价格
  string compare_retail_price = 19;
  // 订货价差异金额
  string diff_order_price = 20;
  // 零售价差异金额
  string diff_retail_price = 21;
  //起订量
  string min_qty = 22;
  //最大量
  string  max_qty = 23;
  //递增量
  string increase_qty = 24;
  //仓库
  uint64 distrcenter_id = 25;
  string distrcenter_name = 26;
  string arrival_days = 27;
}

message QueryFreeOrderLimitRequest{
  string search = 1;
  repeated uint64 product_ids = 2;
  PageOrder page = 3;
}

message QueryFreeOrderLimitResponse{
  repeated FreeOrderLimit rows = 1;
  uint64 total = 2;
}

message FreeOrderLimit{
  // id
  uint64 id = 1;
  // 租户id
  uint64 partner_id = 2;
  // 商品id
  // @gotags: validate:"required"
  uint64 product_id = 3;
  // 商品名称
  string product_name = 4;
  // 商品编码
  string product_code = 5;
  // 订货单位id
  uint64 order_unit_id = 6;
  // 订货单位名称
  string order_unit_name = 7;
  // 限订数量
  string limit_qty_order_unit = 8;
  // 更新人id
  uint64 updated_by = 9;
  // 更新人名称
  string updated_name = 10;
  // 更新时间
  google.protobuf.Timestamp updated = 11;
  // 核算单位数量(查询用)
  string limit_qty=12;
}