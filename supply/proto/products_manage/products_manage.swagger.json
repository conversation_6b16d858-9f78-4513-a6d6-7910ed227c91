{"swagger": "2.0", "info": {"title": "products_manage/products_manage.proto", "version": "version not set"}, "tags": [{"name": "ProductManage"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/products-manage/add-distribution-rule": {"post": {"summary": "添加配送规则", "operationId": "ProductManage_AddDistributionRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddDistributionRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/add-distribution-rule-check": {"post": {"summary": "添加配送规则检查", "operationId": "ProductManage_AddDistributionRuleCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageDistributionRuleCheckResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddDistributionRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/add-or-update-truss-activity": {"post": {"summary": "新增或修改捆绑销售", "operationId": "ProductManage_AddOrUpdateTrussActivity", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "code", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string"}, {"name": "name", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string"}, {"name": "status", "description": "@gotags: validate:\"required\"\n\n - Undefinition: 状态未定义\n - DISABLED: 禁用\n - ENABLED: 可用", "in": "query", "required": false, "type": "string", "enum": ["Undefinition", "DISABLED", "ENABLED"], "default": "Undefinition"}, {"name": "start_time", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_time", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "product_id", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "company_id", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "type_id", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/add-order-rule": {"post": {"summary": "添加订货规则", "operationId": "ProductManage_AddOrderRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddOrderRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/add-order-rule-check": {"post": {"summary": "添加订货规则检查", "operationId": "ProductManage_AddOrderRuleCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageOrderRuleCheckResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddOrderRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/add-order-type": {"post": {"summary": "添加订货类型组", "operationId": "ProductManage_AddOrderTypeGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddOrderTypeGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/add-plan": {"post": {"summary": "创建订货计划", "operationId": "ProductManage_AddPlan", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddPlanRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/add-sales-tax": {"post": {"summary": "增加税率信息", "operationId": "ProductManage_AddSaleTax", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageSaleTaxTotal"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/agent-products": {"post": {"summary": "获取加盟商可购买商品列表  已经废弃 不要调用 请调用 GetAgentProductsFilterRules", "operationId": "ProductManage_GetAgentProducts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetAgentProductsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "search", "description": "筛选条件: 商品code, 商品名称", "in": "query", "required": false, "type": "string"}, {"name": "category_id", "description": "筛选当前商品类别", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "store_id", "description": "门店id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "page.order", "in": "query", "required": false, "type": "string"}, {"name": "page.sort", "in": "query", "required": false, "type": "string"}, {"name": "page.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "category_ids", "description": "所有商品类型 需要获取的商品个数", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "type_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "agent_products_filter.status", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "agent_products_filter.allow_order", "in": "query", "required": false, "type": "boolean"}, {"name": "agent_products_filter.tax_price_eq_less_zero", "in": "query", "required": false, "type": "boolean"}, {"name": "agent_products_filter.order_rule", "in": "query", "required": false, "type": "boolean"}, {"name": "agent_products_filter.order_rule_not_time_seconds", "in": "query", "required": false, "type": "boolean"}, {"name": "agent_products_filter.order_time", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "agent_products_filter.truss_activity", "in": "query", "required": false, "type": "boolean"}, {"name": "price_type", "in": "query", "required": false, "type": "string"}, {"name": "inventory_data", "description": "是否需要库存数据", "in": "query", "required": false, "type": "boolean"}, {"name": "exclusive_product_ids", "description": "不需要返回的商品", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "store_ids", "description": "专门给商品主配的选择器使用", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/agent-products-filter-rules": {"post": {"summary": "获取加盟商可购买商品列表过滤", "operationId": "ProductManage_GetAgentProductsFilterRules", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetAgentProductsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "search", "description": "筛选条件: 商品code, 商品名称", "in": "query", "required": false, "type": "string"}, {"name": "category_id", "description": "筛选当前商品类别", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "store_id", "description": "门店id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "page.order", "in": "query", "required": false, "type": "string"}, {"name": "page.sort", "in": "query", "required": false, "type": "string"}, {"name": "page.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "category_ids", "description": "所有商品类型 需要获取的商品个数", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "type_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "agent_products_filter.status", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "agent_products_filter.allow_order", "in": "query", "required": false, "type": "boolean"}, {"name": "agent_products_filter.tax_price_eq_less_zero", "in": "query", "required": false, "type": "boolean"}, {"name": "agent_products_filter.order_rule", "in": "query", "required": false, "type": "boolean"}, {"name": "agent_products_filter.order_rule_not_time_seconds", "in": "query", "required": false, "type": "boolean"}, {"name": "agent_products_filter.order_time", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "agent_products_filter.truss_activity", "in": "query", "required": false, "type": "boolean"}, {"name": "price_type", "in": "query", "required": false, "type": "string"}, {"name": "inventory_data", "description": "是否需要库存数据", "in": "query", "required": false, "type": "boolean"}, {"name": "exclusive_product_ids", "description": "不需要返回的商品", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "store_ids", "description": "专门给商品主配的选择器使用", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/cancel-plan": {"post": {"summary": "撤销价格中心生效计划", "operationId": "ProductManage_CancelPlan", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageCancelPlanRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/data-change-group": {"post": {"summary": "保存组", "operationId": "ProductManage_DataChangeGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageDataChangeGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/delete-distribution-rule": {"post": {"summary": "删除配送规则", "operationId": "ProductManage_DeleteDistributionRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageDeleteDistributionRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/delete-order-rule": {"post": {"summary": "删除订货规则", "operationId": "ProductManage_DeleteOrderRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "id", "description": "@gotags: validate:\"required_without=UniqueKey\"", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/delete-order-type": {"post": {"summary": "删除订货类型组", "operationId": "ProductManage_DeleteOrderTypeGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageDeleteOrderTypeGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/delete/sku/price": {"post": {"summary": "删除价格中心", "operationId": "ProductManage_DeleteSkuPrice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "products", "description": "商品", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "id", "description": "sku id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "ditch_id", "description": "渠道id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "price_id", "description": "价格id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "store_ids", "description": "门店id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/distribution-rule": {"post": {"summary": "查询配送规则(门店视角)", "operationId": "ProductManage_GetDistributionRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetDistributionRuleResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetDistributionRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/distribution-rule-by-product": {"post": {"summary": "查询配送规则(商品视角)", "operationId": "ProductManage_GetDistributionRuleByProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetDistributionRuleByProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetDistributionRuleByProductRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/distribution-rule-export": {"post": {"summary": "导出配送规则(门店视角)", "operationId": "ProductManage_GetDistributionRuleExport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetDistributionRuleExportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetDistributionRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/get-truss-activity": {"post": {"summary": "获取捆绑销售活动", "operationId": "ProductManage_GetTrussActivity", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetTrussActivityResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "name", "in": "query", "required": false, "type": "string"}, {"name": "status", "description": " - Undefinition: 状态未定义\n - DISABLED: 禁用\n - ENABLED: 可用", "in": "query", "required": false, "type": "string", "enum": ["Undefinition", "DISABLED", "ENABLED"], "default": "Undefinition"}, {"name": "start_time", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_time", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "company_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "page.order", "in": "query", "required": false, "type": "string"}, {"name": "page.sort", "in": "query", "required": false, "type": "string"}, {"name": "page.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "product_id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "with_detail", "in": "query", "required": false, "type": "boolean"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/get-truss-activity-product": {"post": {"summary": "获取捆绑销售活动商品", "operationId": "ProductManage_GetTrussActivityProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetTrussActivityProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "id", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "page.order", "in": "query", "required": false, "type": "string"}, {"name": "page.sort", "in": "query", "required": false, "type": "string"}, {"name": "page.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/group/add": {"post": {"summary": "添加分组", "operationId": "ProductManage_AddGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/group/get": {"post": {"summary": "查询分组", "operationId": "ProductManage_GetGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetGroupResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/group/product/get": {"post": {"summary": "查询分组商品", "operationId": "ProductManage_GetGroupProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetGroupProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetGroupProductRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/group/update": {"post": {"summary": "修改分组 只传入有变化的商品 删除商品是 is_delete = true", "operationId": "ProductManage_UpdateGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdateGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/move/sku/price": {"post": {"summary": "移动价格", "operationId": "ProductManage_MoveSkuPrice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "products", "description": "商品", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "ditch_id", "description": "渠道id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "store_ids", "description": "门店id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "price_type_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "to_price_id", "description": "目标价格id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/order-change-report": {"post": {"summary": "门店订货范围, 价格变动报表", "operationId": "ProductManage_OrderRangePriceChangeReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageOrderRangeChangeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageOrderRangeChangeRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/order-rule-by-product": {"post": {"summary": "订货规则 商品维度", "operationId": "ProductManage_GetOrderRuleByProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetOrderRuleByProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetOrderRuleByProductRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/order-rule-by-store": {"post": {"summary": "订货规则 门店维度", "operationId": "ProductManage_GetOrderRuleByStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetOrderRuleByStoreResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetOrderRuleByStoreRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/order-rule-export": {"post": {"summary": "订货规则导出", "operationId": "ProductManage_GetOrderRuleExport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetOrderRuleExportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetOrderRuleByStoreRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/order-type-by-price": {"post": {"summary": "获取订货类型 根据价格维度", "operationId": "ProductManage_GetOrderTypeByPrice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageOrderTypeByPriceResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "store_id", "description": "门店id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "product_ids", "description": "商品id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "page.order", "in": "query", "required": false, "type": "string"}, {"name": "page.sort", "in": "query", "required": false, "type": "string"}, {"name": "page.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "type_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/plan": {"post": {"summary": "查询价格中心生效计划主表", "operationId": "ProductManage_GetPlan", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetPlanResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetPlanRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/plan-item": {"post": {"summary": "查询价格中心生效计划主表", "operationId": "ProductManage_GetPlanItemList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetPlanItemResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetPlanItemRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/price-sku-by-product": {"post": {"summary": "获取价格中心商品 维度:商品", "operationId": "ProductManage_GetPriceSkuByProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetPriceSkuByProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "search", "description": "筛选条件: 商品code, 商品名称", "in": "query", "required": false, "type": "string"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "store_id", "description": "门店id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "ditch_id", "description": "渠道id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "page.order", "in": "query", "required": false, "type": "string"}, {"name": "page.sort", "in": "query", "required": false, "type": "string"}, {"name": "page.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "company_id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/price-sku-by-product-detail": {"post": {"summary": "获取价格中心商品 维度:商品", "operationId": "ProductManage_GetPriceSkuByProductDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetPriceSkuByProductDetailResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "product_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "unit_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "adjust_unit_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "ditch_id", "description": "uint64 tax_price = 4;\n渠道id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "company_id", "description": "公司id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/price-sku-by-store": {"post": {"summary": "获取价格中心商品 维度:门店", "operationId": "ProductManage_GetPriceSkuByStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetPriceSkuByStoreResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "search", "description": "筛选条件: 商品code, 商品名称", "in": "query", "required": false, "type": "string"}, {"name": "category_ids", "description": "商品类别ID", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "store_id", "description": "门店id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "ditch_id", "description": "渠道id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "page.order", "in": "query", "required": false, "type": "string"}, {"name": "page.sort", "in": "query", "required": false, "type": "string"}, {"name": "page.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "company_id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/product-price-check": {"post": {"summary": "添加门店商品价格", "operationId": "ProductManage_ProductPriceCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageProductPriceCheckResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddProductPriceRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/query-order-type": {"post": {"summary": "查询订货类型组", "operationId": "ProductManage_QueryOrderTypeGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageQueryOrderTypeGroupResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageQueryOrderTypeGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/query-order-type-by-store": {"post": {"summary": "查询订货类型组门店视角", "operationId": "ProductManage_QueryOrderTypeGroupByStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageQueryOrderTypeGroupByStoreResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageQueryOrderTypeGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/query-range-of-order": {"post": {"summary": "门店可订货范围查询", "operationId": "ProductManage_QueryRangeOfOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageQueryRangeOfOrderResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageQueryRangeOfOrderRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/sales-tax": {"post": {"summary": "查询销项税信息", "operationId": "ProductManage_GetSaleTax", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetSaleTaxListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageGetSaleTaxListRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/sales-tax-log": {"post": {"summary": "查询税率变更日志", "operationId": "ProductManage_GetSaleTaxLog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageSaleTaxLogResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageSaleTaxLogRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/sku-price/product": {"post": {"summary": "获取主档商品", "operationId": "ProductManage_GetSkuProductInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageGetSkuProductInfoResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "search", "description": "筛选条件: 商品code, 商品名称", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "page.order", "in": "query", "required": false, "type": "string"}, {"name": "page.sort", "in": "query", "required": false, "type": "string"}, {"name": "page.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "page.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "company_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "order", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "filter.not_order_unit", "in": "query", "required": false, "type": "boolean"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/store-product-price": {"post": {"summary": "添加门店商品价格", "operationId": "ProductManage_AddProductPrice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageAddProductPriceRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/sync-mata-data": {"post": {"summary": "主档数据同步", "operationId": "ProductManage_SyncMetaData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageSyncMetaDataRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-distribution-rule": {"post": {"summary": "修改配送规则", "operationId": "ProductManage_UpdateDistributionRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdateDistributionRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-order-rule": {"post": {"summary": "修改订货规则", "operationId": "ProductManage_UpdateOrderRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdateOrderRuleRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-order-type": {"post": {"summary": "更新订货类型组", "operationId": "ProductManage_UpdateOrderTypeGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdateOrderTypeGroupRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-order-type-batch": {"post": {"operationId": "ProductManage_UpdateOrderTypeGroupBatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageUpdateOrderTypeGroupBatchResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdateOrderTypeGroupBatchRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-order-type-status": {"post": {"summary": "更新订货类型组状态", "operationId": "ProductManage_UpdateOrderTypeGroupStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdateOrderTypeGroupStatusRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-plan-item": {"post": {"summary": "更新价格中心生效计划明细", "operationId": "ProductManage_UpdatePlanItem", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdatePlanItemRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-sales-tax": {"post": {"summary": "更新税率信息", "operationId": "ProductManage_UpdateSaleTax", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageSaleTaxTotal"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-truss-activity-product": {"post": {"summary": "修改捆绑销售活动商品", "operationId": "ProductManage_UpdateTrussActivityProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "id", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "product_id", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update-truss-activity-status": {"post": {"summary": "修改捆绑销售活动状态", "operationId": "ProductManage_UpdateTrussActivityStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "id", "description": "@gotags: validate:\"required\"", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "status", "description": "@gotags: validate:\"required\"\n\n - Undefinition: 状态未定义\n - DISABLED: 禁用\n - ENABLED: 可用", "in": "query", "required": false, "type": "string", "enum": ["Undefinition", "DISABLED", "ENABLED"], "default": "Undefinition"}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update/sku/price": {"post": {"summary": "调整价格中心", "operationId": "ProductManage_UpdateSkuPrice", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdatePriceRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update/sku/price/by/product": {"post": {"summary": "调整价格中心", "operationId": "ProductManage_UpdateSkuPriceByProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/products_manageUpdateSkuPriceByProductRequest"}}], "tags": ["ProductManage"]}}, "/api/v1/products-manage/update/sku/price/status": {"post": {"summary": "修改价格中心状态", "operationId": "ProductManage_UpdateSkuPriceStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/products_manageResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "products", "description": "id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "status", "description": "0沽清 1制满 2 等待计划生效", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "ditch_id", "description": "渠道id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "company_id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["ProductManage"]}}}, "definitions": {"googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "products_manageAddDistributionRuleRequest": {"type": "object", "properties": {"distrcenter_id": {"type": "string", "format": "uint64", "title": "渠道 id 必传"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID 必传"}, "distr_type": {"type": "string", "title": "物流模式"}, "distribution_rule": {"type": "array", "items": {"$ref": "#/definitions/products_manageDistributionRuleModel"}, "title": "配送规则"}, "only_add": {"type": "boolean", "title": "冲突处理（true 只新增 false 覆盖）"}, "is_check": {"type": "boolean", "title": "ture 不进行数据库插入"}}}, "products_manageAddGroupRequest": {"type": "object", "properties": {"company_id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "name": {"type": "string"}, "remark": {"type": "string"}, "group_type": {"type": "integer", "format": "int64", "title": "1价格组 2规则组"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "组里生效的门店"}, "group_product": {"type": "array", "items": {"$ref": "#/definitions/products_manageProductsList"}, "title": "组里的商品"}}}, "products_manageAddOrderRuleRequest": {"type": "object", "properties": {"ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "order_rule": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderRuleModel"}, "title": "规则列表"}, "only_add": {"type": "boolean", "title": "冲突处理（true 只新增 false 覆盖）"}, "is_check": {"type": "boolean", "title": "ture 不进行数据库插入"}}, "title": "添加门店商品价格请求"}, "products_manageAddOrderTypeGroupRequest": {"type": "object", "properties": {"type_id": {"type": "string", "format": "uint64", "title": "订货类型id"}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品ids"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店ids"}}, "title": "订货类型组"}, "products_manageAddPlanRequest": {"type": "object", "properties": {"ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "plans": {"type": "array", "items": {"$ref": "#/definitions/products_managePlanList"}, "title": "商品列表"}}, "title": "添加门店商品价格请求"}, "products_manageAddProductPriceRequest": {"type": "object", "properties": {"ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "products": {"type": "array", "items": {"$ref": "#/definitions/products_manageProductsList"}, "title": "商品列表"}, "effective_type": {"$ref": "#/definitions/products_manageEffectiveType", "title": "生效方式 必传"}, "effective_date": {"type": "string", "format": "int64", "title": "生效日期 生效方式为计划生效时必传"}}, "title": "添加门店商品价格请求"}, "products_manageAgentProductsFilter": {"type": "object", "properties": {"status": {"type": "integer", "format": "int32"}, "allow_order": {"type": "boolean"}, "tax_price_eq_less_zero": {"type": "boolean"}, "order_rule": {"type": "boolean"}, "order_rule_not_time_seconds": {"type": "boolean"}, "order_time": {"type": "string", "format": "date-time"}, "truss_activity": {"type": "boolean"}}}, "products_manageCancelPlanRequest": {"type": "object", "properties": {"plan_id": {"type": "string", "format": "uint64"}}}, "products_manageDataChangeGroupRequest": {"type": "object", "properties": {"company_id": {"type": "string", "format": "uint64", "title": "公司 id 必传"}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "渠道 id 必传"}, "code": {"type": "string"}, "name": {"type": "string"}, "group_type": {"type": "integer", "format": "int64", "title": "1价格组 2规则组  必传"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "需要保存成租的门店  必传"}}}, "products_manageDeleteDistributionRuleRequest": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "@gotags: validate:\"required_without=UniqueKey\""}, "unique_key": {"type": "array", "items": {"$ref": "#/definitions/products_manageRuleUniqueKey"}}}}, "products_manageDeleteOrderTypeGroupRequest": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "type_id": {"type": "string", "format": "uint64", "title": "订货类型id"}, "visual_angle": {"type": "string", "title": "视角，枚举值（product，store）"}, "company_id": {"type": "string", "format": "uint64"}}}, "products_manageDistributionRule": {"type": "object", "properties": {"planned_arrival_days": {"type": "string", "title": "到货天数"}, "distr_type": {"type": "string", "title": "物流模式"}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "仓库ID"}}}, "products_manageDistributionRuleCheckResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageDistributionRuleCheckRow"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageDistributionRuleCheckRow": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "规则id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称 - 修改/添加时不传"}, "product_code": {"type": "string", "title": "商品code - 修改/添加时不传"}, "spec": {"type": "string", "title": "规格名称 - 修改/添加时不传"}, "unit_id": {"type": "string", "format": "uint64", "title": "商品id - 修改/添加时不传"}, "unit_name": {"type": "string", "title": "商品单位 - 修改/添加时不传"}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "仓库ID必传"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID 必传"}, "is_delete": {"type": "integer", "format": "int64", "title": "false 存在 true删除"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算"}, "adjust_unit_name": {"type": "string"}, "adjust_tax_price": {"type": "string"}, "new_rule": {"$ref": "#/definitions/products_manageDistributionRule", "title": "配送规则"}, "old_rule": {"$ref": "#/definitions/products_manageDistributionRule"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_code": {"type": "string", "title": "门店编码"}, "store_name": {"type": "string", "title": "门店名称"}, "distr_type": {"type": "string", "title": "物流模式"}}}, "products_manageDistributionRuleExportRow": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "规则id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "name": {"type": "string", "title": "商品名称"}, "code": {"type": "string", "title": "商品code"}, "spec": {"type": "string", "title": "规格名称"}, "unit_id": {"type": "string", "format": "uint64", "title": "商品id"}, "unit_name": {"type": "string", "title": "商品单位"}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "仓库ID"}, "distrcenter_code": {"type": "string", "title": "仓库/加工中心code"}, "distrcenter_name": {"type": "string", "title": "仓库name"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID"}, "company_code": {"type": "string", "title": "公司编码"}, "company_name": {"type": "string", "title": "公司名称"}, "planned_arrival_days": {"type": "string", "title": "到货天数"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_code": {"type": "string", "title": "门店编码"}, "store_name": {"type": "string", "title": "门店编码"}, "distr_type_name": {"type": "string", "title": "配送类型名称"}}}, "products_manageDistributionRuleIdInfo": {"type": "object", "properties": {"distribution_rule_ids": {"type": "string", "format": "uint64"}, "store_ids": {"type": "string", "format": "uint64"}}}, "products_manageDistributionRuleInfo": {"type": "object", "properties": {"distribution_rule": {"$ref": "#/definitions/products_manageDistributionRule"}, "ruleId": {"type": "array", "items": {"$ref": "#/definitions/products_manageDistributionRuleIdInfo"}}}}, "products_manageDistributionRuleModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "规则id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "name": {"type": "string", "title": "商品名称 - 修改/添加时不传"}, "code": {"type": "string", "title": "商品code - 修改/添加时不传"}, "spec": {"type": "string", "title": "规格名称 - 修改/添加时不传"}, "unit_id": {"type": "string", "format": "uint64", "title": "商品id - 修改/添加时不传"}, "unit_name": {"type": "string", "title": "商品单位 - 修改/添加时不传"}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "仓库ID必传"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID 必传"}, "is_delete": {"type": "integer", "format": "int64", "title": "false 存在 true删除"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算"}, "adjust_unit_name": {"type": "string"}, "adjust_tax_price": {"type": "string"}, "rule": {"$ref": "#/definitions/products_manageDistributionRule", "title": "配送规则"}, "store_id": {"type": "string", "format": "uint64", "title": "单独新增接口的时候 不传入"}, "stores_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id list(新增/更新时传入)"}, "distr_type": {"type": "string", "title": "物流模式"}}, "title": "配送规则"}, "products_manageEffectiveType": {"type": "string", "enum": ["EffectiveTypeInit", "EffectiveTypeImmediate", "EffectiveTypePlaned"], "default": "EffectiveTypeInit", "title": "- EffectiveTypeInit: 初始值意义\n - EffectiveTypeImmediate: 立即执行\n - EffectiveTypePlaned: 计划执行"}, "products_manageEntityDetail": {"type": "object", "properties": {"product_name": {"type": "string"}, "product_code": {"type": "string"}, "spec": {"type": "string", "title": "商品规格"}, "product_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string", "title": "订货单位"}, "unit_code": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}}}, "products_manageExecLog": {"type": "object", "properties": {"log_data": {"$ref": "#/definitions/products_manageLogData", "title": "日志变更类型"}, "updated": {"type": "string", "format": "int64", "title": "变更时间"}, "updated_by": {"type": "string", "format": "int64", "title": "变更人ID"}, "updated_name": {"type": "string", "title": "变更人姓名"}, "operation_type": {"type": "string", "title": "操作类型"}, "doc_id": {"type": "string", "format": "uint64", "title": "单据"}}}, "products_manageFieldInfo": {"type": "object", "properties": {"field_name": {"type": "string", "title": "字段名称"}, "old_value": {"type": "string", "title": "原有值"}, "new_value": {"type": "string", "title": "更新值"}}}, "products_manageGetAgentProductsResponse": {"type": "object", "properties": {"rows": {"$ref": "#/definitions/products_manageProductsCategoryList"}, "total": {"type": "string", "format": "uint64"}}, "title": "加盟商可购买商品列表"}, "products_manageGetDistinctRuleByProductList": {"type": "object", "properties": {"partner_id": {"type": "string", "format": "uint64", "title": "租户iD"}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "渠道ID"}, "product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "spec": {"type": "string", "title": "规格"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位ID"}, "unit_name": {"type": "string", "title": "单位名称"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID"}, "distr_type": {"type": "string", "title": "物流模式"}, "distribution_rule": {"type": "array", "items": {"$ref": "#/definitions/products_manageDistributionRuleInfo"}, "title": "规则"}}}, "products_manageGetDistributionRuleByProductRequest": {"type": "object", "properties": {"search": {"type": "string", "title": "筛选条件: 商品code, 商品名称"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "仓库ID必传"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID 必传"}, "page": {"$ref": "#/definitions/products_managePageOrder"}}}, "products_manageGetDistributionRuleByProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetDistinctRuleByProductList"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetDistributionRuleExportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageDistributionRuleExportRow"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetDistributionRuleRequest": {"type": "object", "properties": {"search": {"type": "string", "title": "除page外，查询时至少提供一个参数\n筛选条件: 商品code, 商品名称"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "仓库ID"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID"}, "page": {"$ref": "#/definitions/products_managePageOrder"}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "products_manageGetDistributionRuleResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageDistributionRuleModel"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetGroupProductRequest": {"type": "object", "properties": {"group_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_code": {"type": "string"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "return_product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "page": {"$ref": "#/definitions/products_managePageOrder"}, "group_type": {"type": "integer", "format": "int64", "title": "传入分组类型"}, "company_id": {"type": "string", "format": "uint64"}}}, "products_manageGetGroupProductResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "rows": {"$ref": "#/definitions/products_manageGroup"}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetGroupRequest": {"type": "object", "properties": {"name": {"type": "string"}, "code": {"type": "string"}, "product_name": {"type": "string"}, "product_code": {"type": "string"}, "company_id": {"type": "string", "format": "uint64"}, "page": {"$ref": "#/definitions/products_managePageOrder"}, "group_type": {"type": "integer", "format": "int64"}}}, "products_manageGetGroupResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageGroup"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetOrderRuleByProductList": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_rate": {"type": "string", "title": "单位转换率"}, "spec": {"type": "string", "title": "规格"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "unit_name": {"type": "string", "title": "订货单位name"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "order_rule": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderRuleInfo"}, "title": "订货规则"}}}, "products_manageGetOrderRuleByProductRequest": {"type": "object", "properties": {"search": {"type": "string", "title": "筛选条件: 商品code, 商品名称"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品类别ID"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64"}, "page": {"$ref": "#/definitions/products_managePageOrder"}}}, "products_manageGetOrderRuleByProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetOrderRuleByProductList"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetOrderRuleByStoreList": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_rate": {"type": "string", "title": "单位转换率"}, "spec": {"type": "string", "title": "规格"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "unit_name": {"type": "string", "title": "订货单位name"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "order_rule": {"$ref": "#/definitions/products_manageOrderRule", "title": "订货规则"}}}, "products_manageGetOrderRuleByStoreRequest": {"type": "object", "properties": {"search": {"type": "string", "title": "筛选条件: 商品code, 商品名称"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品类别ID"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64"}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "page": {"$ref": "#/definitions/products_managePageOrder"}}}, "products_manageGetOrderRuleByStoreResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetOrderRuleByStoreList"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetOrderRuleExportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderRuleExportRow"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetPlanItemList": {"type": "object", "properties": {"plan_id": {"type": "string", "format": "uint64", "title": "计划ID"}, "id": {"type": "string", "format": "uint64", "title": "sku_id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户"}, "ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_rate": {"type": "string", "title": "单位转换率"}, "spec": {"type": "string", "title": "规格"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "unit_name": {"type": "string", "title": "订货单位name"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "status": {"type": "string", "title": "状态"}, "price_info": {"type": "array", "items": {"$ref": "#/definitions/products_managePriceInfo"}}, "tax_ratio": {"type": "string", "title": "税率"}, "code": {"type": "string", "title": "计划编号"}, "plan_status": {"type": "string", "title": "计划状态"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人ID"}, "created_name": {"type": "string"}, "created": {"type": "string", "format": "int64", "title": "创建时间"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人ID"}, "updated_name": {"type": "string", "title": "更新人名称"}, "updated": {"type": "string", "format": "int64", "title": "更新时间"}, "effective_time": {"type": "string", "format": "int64", "title": "生效时间"}}}, "products_manageGetPlanItemRequest": {"type": "object", "properties": {"plan_id": {"type": "string", "format": "uint64"}, "page": {"$ref": "#/definitions/products_managePageOrder"}}}, "products_manageGetPlanItemResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetPlanItemList"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetPlanRequest": {"type": "object", "properties": {"ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "plan_status": {"type": "string", "title": "状态 0待执行 1执行失败 2 已撤销 3成功"}, "page": {"$ref": "#/definitions/products_managePageOrder", "title": "分页条件"}}}, "products_manageGetPlanResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/products_managePlanModel"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetPriceSkuByProductDetailResponse": {"type": "object", "properties": {"rows": {"$ref": "#/definitions/products_manageGetPriceSkuByProductList"}, "total": {"type": "string", "format": "uint64"}}, "title": "商品纬度查看价格中心"}, "products_manageGetPriceSkuByProductList": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_rate": {"type": "string", "title": "单位转换率"}, "spec": {"type": "string", "title": "规格"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "unit_name": {"type": "string", "title": "订货单位name"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "status": {"type": "string", "title": "状态"}, "price_info": {"type": "array", "items": {"$ref": "#/definitions/products_managePriceInfo"}}, "tax_ratio": {"type": "string", "title": "税率"}, "plan_sku_by_price_sku": {"type": "array", "items": {"$ref": "#/definitions/products_managePlanSkuByPriceSku"}}, "product_status": {"type": "string", "title": "商品状态"}}}, "products_manageGetPriceSkuByProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetPriceSkuByProductList"}}, "total": {"type": "string", "format": "uint64"}}, "title": "商品纬度查看价格中心"}, "products_manageGetPriceSkuByStoreList": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_rate": {"type": "string", "title": "单位转换率"}, "spec": {"type": "string", "title": "规格"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "unit_name": {"type": "string", "title": "订货单位name"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "adjust_tax_price": {"type": "string", "title": "核算 含税价格"}, "adjust_price": {"type": "string", "title": "核算 未含税价格"}, "price": {"type": "string", "title": "订货 未含税价格"}, "tax_price": {"type": "string", "title": "订货 含税价格"}, "tax_ratio": {"type": "string", "title": "税率"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "status": {"type": "string"}, "price_info": {"type": "array", "items": {"$ref": "#/definitions/products_managePriceInfo"}}, "code": {"type": "string"}, "plan_sku_by_price_sku": {"type": "array", "items": {"$ref": "#/definitions/products_managePlanSkuByPriceSku"}}, "product_status": {"type": "string", "title": "商品状态"}}}, "products_manageGetPriceSkuByStoreResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetPriceSkuByStoreList"}}, "total": {"type": "string", "format": "uint64"}}, "title": "门店纬度查看价格中心"}, "products_manageGetSaleTaxListRequest": {"type": "object", "properties": {"search": {"type": "string", "title": "筛选条件: 税率规则code/name"}, "id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "税率规则ID"}, "item_values": {"type": "array", "items": {"$ref": "#/definitions/products_manageItemValues"}, "title": "商品信息：需要提供商品ID和商品属性编码"}, "company_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "公司ID"}, "status": {"type": "string", "title": "状态"}, "page": {"$ref": "#/definitions/products_managePageOrder", "title": "分页信息"}}, "title": "税率"}, "products_manageGetSaleTaxListResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageSaleTaxTotal"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetSkuProductInfoList": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_name": {"type": "string", "title": "订货单位name"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "adjust_tax_price": {"type": "string", "title": "核算 含税价格"}, "adjust_price": {"type": "string", "title": "核算 未含税价格"}, "price": {"type": "string", "title": "订货 未含税价格"}, "tax_price": {"type": "string", "title": "订货 含税价格"}, "tax_ratio": {"type": "string", "title": "税率"}, "unit_rate": {"type": "string", "title": "单位转换率"}, "spec": {"type": "string", "title": "规格"}, "product_category_id": {"type": "string", "title": "商品分类id"}}}, "products_manageGetSkuProductInfoResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetSkuProductInfoList"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetTrussActivityProductResponse": {"type": "object", "properties": {"rows": {"$ref": "#/definitions/products_manageTrussActivity"}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGetTrussActivityResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageTrussActivity"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageGroup": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "company_id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "name": {"type": "string"}, "remark": {"type": "string"}, "group_type": {"type": "integer", "format": "int64", "title": "1价格组 2订货规则组 3配送规则组"}, "updated_by": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "created": {"type": "string", "format": "uint64"}, "updated": {"type": "string", "format": "uint64"}, "group_product": {"type": "array", "items": {"$ref": "#/definitions/products_manageProductsList"}, "title": "分组商品"}, "group_order_rule_data": {"type": "array", "items": {"$ref": "#/definitions/products_manageGroupOrderRuleData"}, "title": "分组订单规则"}, "group_distribution_rule_data": {"type": "array", "items": {"$ref": "#/definitions/products_manageGroupDistributionData"}, "title": "分组配送规则"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "product_count": {"type": "string", "format": "uint64", "title": "商品数量"}, "company_name": {"type": "string"}}}, "products_manageGroupDistributionData": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "商品id"}, "name": {"type": "string", "title": "商品名称 - 修改/添加时不传"}, "code": {"type": "string", "title": "商品code - 修改/添加时不传"}, "spec": {"type": "string", "title": "规格名称 - 修改/添加时不传"}, "unit_id": {"type": "string", "format": "uint64", "title": "商品id - 修改/添加时不传"}, "unit_name": {"type": "string", "title": "商品单位 - 修改/添加时不传"}, "is_delete": {"type": "integer", "format": "int64", "title": "0未删除 1已删除"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算"}, "adjust_unit_name": {"type": "string"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品类别"}, "distribution_rule": {"$ref": "#/definitions/products_manageDistributionRule", "title": "配送规则"}}}, "products_manageGroupOrderRuleData": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "商品id"}, "name": {"type": "string", "title": "商品名称 - 修改/添加时不传"}, "code": {"type": "string", "title": "商品code - 修改/添加时不传"}, "spec": {"type": "string", "title": "规格名称 - 修改/添加时不传"}, "unit_id": {"type": "string", "format": "uint64", "title": "商品单位id - 修改/添加时不传"}, "unit_name": {"type": "string", "title": "商品单位 - 修改/添加时不传"}, "unit_rate": {"type": "string", "title": "单位比率"}, "is_delete": {"type": "integer", "format": "int64", "title": "0未删除 1已删除"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算"}, "adjust_unit_name": {"type": "string"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品类别"}, "order_rule": {"$ref": "#/definitions/products_manageOrderRule", "title": "订货规则"}}, "title": "规则组"}, "products_manageGroupStore": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "修改操作时传入"}, "group_id": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}}}, "products_manageItemValues": {"type": "object", "properties": {"product_type": {"type": "string", "title": "商品属性编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}}}, "products_manageLogData": {"type": "object", "properties": {"updated": {"type": "array", "items": {"$ref": "#/definitions/products_manageFieldInfo"}, "title": "更新信息"}, "inserted": {"type": "array", "items": {"$ref": "#/definitions/products_manageFieldInfo"}, "title": "新增信息"}, "deleted": {"type": "array", "items": {"$ref": "#/definitions/products_manageFieldInfo"}, "title": "删除信息"}}}, "products_manageOrderRangeChange": {"type": "object", "properties": {"store_name": {"type": "string", "title": "门店"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_code": {"type": "string"}, "spec": {"type": "string", "title": "商品规格"}, "product_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string", "title": "订货单位"}, "unit_code": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "type_name": {"type": "string", "title": "订货类型"}, "type_code": {"type": "string"}, "order_date": {"type": "string", "title": "订货类型时段"}, "type_id": {"type": "string", "format": "uint64"}, "price_type": {"type": "string", "title": "价格类型"}, "base_order_price": {"type": "string", "title": "基准日订货价格"}, "base_retail_price": {"type": "string", "title": "基准日零售价格"}, "compare_order_price": {"type": "string", "title": "比对日订货价格"}, "compare_retail_price": {"type": "string", "title": "比对日零售价格"}, "diff_order_price": {"type": "string", "title": "订货价差异金额"}, "diff_retail_price": {"type": "string", "title": "零售价差异金额"}, "min_qty": {"type": "string", "title": "起订量"}, "max_qty": {"type": "string", "title": "最大量"}, "increase_qty": {"type": "string", "title": "递增量"}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "仓库"}, "distrcenter_name": {"type": "string"}, "arrival_days": {"type": "string"}}}, "products_manageOrderRangeChangeRequest": {"type": "object", "properties": {"base_date": {"type": "string", "title": "基准日\n@gotags: validate:\"required\""}, "store_id": {"type": "string", "format": "uint64", "title": "门店id\n@gotags: validate:\"required\""}, "type_id": {"type": "string", "format": "uint64", "title": "@gotags: validate:\"required\""}, "price_id": {"type": "string", "format": "uint64"}, "page": {"$ref": "#/definitions/products_managePageOrder"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "type_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "compare_date": {"type": "string", "title": "比对日\n@gotags: validate:\"required\""}, "realtime": {"type": "boolean", "title": "是否实时 (true- 勾选实时, false-未勾选)"}, "include": {"type": "string", "title": "默认传 DIFF- 只看变动商品， 传ALL - 查看全部商品"}}}, "products_manageOrderRangeChangeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderRangeChange"}}, "total": {"type": "string", "format": "int64"}}}, "products_manageOrderRule": {"type": "object", "properties": {"min_qty": {"type": "string", "title": "起订量"}, "max_qty": {"type": "string", "title": "最大量"}, "increase_qty": {"type": "string", "title": "递增量"}, "data_rule": {"type": "string", "title": "订货日期规则"}, "adjust_min_qty": {"type": "string", "title": "核算"}, "adjust_max_qty": {"type": "string"}, "adjust_increase_qty": {"type": "string"}}}, "products_manageOrderRuleCheckResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderRuleCheckRow"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageOrderRuleCheckRow": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_rate": {"type": "string", "title": "单位转换率"}, "spec": {"type": "string", "title": "规格"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "unit_name": {"type": "string", "title": "订货单位name"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_code": {"type": "string", "title": "门店Code"}, "store_name": {"type": "string", "title": "门店name"}, "old_rule": {"$ref": "#/definitions/products_manageOrderRule", "title": "原有订货规则"}, "new_rule": {"$ref": "#/definitions/products_manageOrderRule", "title": "新订货规则"}, "ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}}}, "products_manageOrderRuleExportRow": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_name": {"type": "string"}, "spec": {"type": "string", "title": "规格"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID"}, "company_code": {"type": "string", "title": "公司编码"}, "company_name": {"type": "string", "title": "公司名称"}, "ditch_id": {"type": "string", "format": "uint64", "title": "渠道iD"}, "ditch_name": {"type": "string", "title": "渠道名称"}, "ditch_code": {"type": "string", "title": "渠道编码"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_name": {"type": "string", "title": "门店名称"}, "store_code": {"type": "string", "title": "门店编码"}, "min_qty": {"type": "string", "title": "起订量(订货单位)"}, "max_qty": {"type": "string", "title": "最大量（订货单位）"}, "increase_qty": {"type": "string", "title": "递增量（订货单位）"}, "data_rule_text": {"type": "string", "title": "订货日期规则"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "adjust_min_qty": {"type": "string", "title": "起订量(订货单位)"}, "adjust_max_qty": {"type": "string", "title": "最大量（订货单位）"}, "adjust_increase_qty": {"type": "string", "title": "递增量（订货单位）"}}}, "products_manageOrderRuleIdInfo": {"type": "object", "properties": {"order_rule_ids": {"type": "string", "format": "uint64"}, "store_ids": {"type": "string", "format": "uint64"}}}, "products_manageOrderRuleInfo": {"type": "object", "properties": {"order_rule": {"$ref": "#/definitions/products_manageOrderRule"}, "ruleId": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderRuleIdInfo"}}}}, "products_manageOrderRuleModel": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "规则id"}, "product_id": {"type": "string", "format": "int64", "title": "商品id"}, "name": {"type": "string", "title": "商品名称 - 修改/添加时不传"}, "code": {"type": "string", "title": "商品code - 修改/添加时不传"}, "price": {"type": "string", "title": "商品价格"}, "spec": {"type": "string", "title": "规格名称 - 修改/添加时不传"}, "unit_id": {"type": "string", "format": "uint64", "title": "商品id - 修改/添加时不传"}, "unit_name": {"type": "string", "title": "商品单位 - 修改/添加时不传"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "核算"}, "adjust_unit_name": {"type": "string"}, "adjust_tax_price": {"type": "string"}, "order_rule": {"$ref": "#/definitions/products_manageOrderRule", "title": "订货规则"}, "store_id": {"type": "string", "format": "int64", "title": "单独新增接口的时候 不传入"}, "stores_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id list(新增/更新时传入)"}}}, "products_manageOrderTypeByPrice": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "type_id": {"type": "string", "format": "uint64", "title": "订货类型id"}, "name": {"type": "string", "title": "订货类型名称"}, "storeProduct": {"type": "array", "items": {"$ref": "#/definitions/products_manageStoreProductRelation"}, "title": "关联的门店和商品"}, "code": {"type": "string", "title": "订货类型编号"}, "fields": {"type": "object"}}}, "products_manageOrderTypeByPriceResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderTypeByPrice"}}, "total": {"type": "string", "format": "uint64"}}, "title": "获取加盟商可购买商品列表"}, "products_manageOrderTypeGroup": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string", "title": "商品"}, "product_code": {"type": "string"}, "spec": {"type": "string", "title": "商品规格"}, "product_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string", "title": "订货单位"}, "unit_code": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string", "title": "门店"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "total": {"type": "string", "format": "int64"}, "aggregation_detail": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderTypeGroupChildrenDetail"}, "title": "聚合详情"}}}, "products_manageOrderTypeGroupByStore": {"type": "object", "properties": {"store_name": {"type": "string", "title": "门店"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "type_name": {"type": "string", "title": "订货类型"}, "type_code": {"type": "string"}, "time_bucket": {"type": "array", "items": {"type": "string"}, "title": "订货类型时段"}, "type_id": {"type": "string", "format": "uint64"}, "details": {"type": "array", "items": {"$ref": "#/definitions/products_manageEntityDetail"}}, "total": {"type": "string", "format": "int64"}, "status": {"type": "string"}}}, "products_manageOrderTypeGroupChildrenDetail": {"type": "object", "properties": {"type_name": {"type": "string", "title": "订货类型"}, "type_code": {"type": "string"}, "time_bucket": {"type": "array", "items": {"type": "string"}, "title": "订货类型时段"}, "type_id": {"type": "string", "format": "uint64"}, "entitys": {"type": "array", "items": {"$ref": "#/definitions/products_manageEntityDetail"}}, "total": {"type": "string", "format": "int64"}, "status": {"type": "string"}}}, "products_managePageOrder": {"type": "object", "properties": {"order": {"type": "string"}, "sort": {"type": "string"}, "limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}}}, "products_managePlanList": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "code": {"type": "string", "format": "uint64"}, "effective_time": {"type": "string", "format": "uint64"}, "plan_status": {"type": "string", "format": "uint64"}, "reason": {"type": "string"}}}, "products_managePlanModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "计划ID"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户ID"}, "ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "code": {"type": "string", "title": "计划编码"}, "effective_time": {"type": "string", "format": "int64", "title": "生效计划"}, "plan_status": {"type": "string", "title": "状态 0待执行 1执行失败 2 已撤销 3成功"}, "reason": {"type": "string", "title": "执行备注"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人ID"}, "created_name": {"type": "string"}, "created": {"type": "string", "format": "int64", "title": "创建时间"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人ID"}, "updated_name": {"type": "string", "title": "更新人名称"}, "updated": {"type": "string", "format": "int64", "title": "更新时间"}}}, "products_managePlanSkuByPriceSku": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "price_info": {"type": "array", "items": {"$ref": "#/definitions/products_managePriceInfo"}}, "effective_time": {"type": "string", "format": "int64"}}}, "products_managePriceInfo": {"type": "object", "properties": {"adjust_tax_price": {"type": "string", "title": "核算 含税价格"}, "adjust_price": {"type": "string", "title": "核算 未含税价格"}, "store_count": {"type": "integer", "format": "int64", "title": "包含门店数量"}, "price": {"type": "string", "title": "订货 未含税价格"}, "tax_price": {"type": "string", "title": "订货 含税价格"}, "tax_ratio": {"type": "string", "title": "税率"}, "id": {"type": "string", "format": "uint64", "title": "价格id"}, "price_type_id": {"type": "string", "format": "uint64"}, "price_type_name": {"type": "string"}, "sku_by_store": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetPriceSkuByStoreList"}}, "sku_id": {"type": "string", "format": "uint64"}, "adjust_unit_name": {"type": "string", "title": "核算单位name"}, "unit_name": {"type": "string", "title": "订货单位name"}}}, "products_manageProductCategory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "分类id"}, "total": {"type": "integer", "format": "int64", "title": "该分类下的总数"}}, "title": "商品分类信息"}, "products_manageProductPrice": {"type": "object", "properties": {"tax_price": {"type": "string", "title": "含税价格"}, "price_type_id": {"type": "string", "format": "uint64"}, "price_type_name": {"type": "string"}, "tax_ratio": {"type": "string", "title": "税率"}, "adjust_tax_price": {"type": "string", "title": "单位转换"}, "id": {"type": "string", "format": "uint64"}, "unit_rate": {"type": "string", "title": "税率"}}}, "products_manageProductPriceCheckResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageProductPriceCheckRow"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageProductPriceCheckRow": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "价格中心id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "name": {"type": "string", "title": "商品名称 - 修改/添加时不传"}, "code": {"type": "string", "title": "商品code - 修改/添加时不传"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店name"}, "old_tax_price": {"type": "string", "title": "旧的价格"}, "new_tax_price": {"type": "string", "title": "新的价格"}, "price_type_id": {"type": "string", "format": "int64"}}}, "products_manageProductsCategoryList": {"type": "object", "properties": {"category": {"type": "array", "items": {"$ref": "#/definitions/products_manageProductCategory"}, "title": "商品分类信息"}, "list": {"type": "array", "items": {"$ref": "#/definitions/products_manageProductsList"}, "title": "商品列表"}}, "title": "商品分类列表"}, "products_manageProductsList": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id和商品id"}, "name": {"type": "string", "title": "商品名称 - 修改/添加时不传"}, "code": {"type": "string", "title": "商品code - 修改/添加时不传"}, "tax_price": {"type": "string", "title": "商品价格"}, "spec": {"type": "string", "title": "规格名称 - 修改/添加时不传"}, "unit_id": {"type": "string", "format": "uint64", "title": "商品id - 修改/添加时不传"}, "unit_name": {"type": "string", "title": "商品单位 - 修改/添加时不传"}, "adjust_unit_id": {"type": "string", "format": "uint64", "title": "0未删除 1已删除\n uint32 is_delete = 8;\n核算"}, "adjust_unit_name": {"type": "string"}, "order_rule": {"$ref": "#/definitions/products_manageOrderRule", "title": "float adjust_tax_price = 10;\n订货规则"}, "distribution_rule": {"$ref": "#/definitions/products_manageDistributionRule", "title": "配送规则"}, "stores_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id"}, "product_price": {"type": "array", "items": {"$ref": "#/definitions/products_manageProductPrice"}}, "tax_ratio": {"type": "string", "title": "税率"}, "test": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "test"}, "sales_qty": {"type": "string", "title": "销售数量"}, "real_qty": {"type": "string", "title": "实时库存"}, "wait_receiving_qty": {"type": "string", "title": "待收货数量"}, "allow_order": {"type": "boolean", "title": "可订货"}, "product_category_id": {"type": "string", "title": "商品分类id"}, "product_category_code": {"type": "string", "title": "商品分类code"}, "product_category_name": {"type": "string", "title": "商品分类name"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店name"}, "status": {"type": "integer", "format": "int64", "title": "状态"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品类别"}, "type_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "expire_quantity": {"type": "string", "title": "预计到货数量"}, "truss_activity_product": {"type": "array", "items": {"$ref": "#/definitions/products_manageTrussActivityProduct"}}}, "title": "商品列表"}, "products_manageQueryOrderTypeGroupByStoreResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderTypeGroupByStore"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageQueryOrderTypeGroupRequest": {"type": "object", "properties": {"visual_angle": {"type": "string", "title": "视角，枚举值（product，store）"}, "product_name": {"type": "string"}, "product_code": {"type": "string"}, "store_name": {"type": "string"}, "store_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "type_id": {"type": "string", "format": "uint64"}, "page": {"$ref": "#/definitions/products_managePageOrder"}, "with_detail": {"type": "boolean", "title": "是否返回详情"}, "company_id": {"type": "string", "format": "uint64"}}}, "products_manageQueryOrderTypeGroupResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderTypeGroup"}}, "total": {"type": "string", "format": "uint64"}}}, "products_manageQueryRangeOfOrderRequest": {"type": "object", "properties": {"order_date": {"type": "string", "title": "订货类型时段\n@gotags: validate:\"required\""}, "store_id": {"type": "string", "format": "uint64", "title": "@gotags: validate:\"required\""}, "type_id": {"type": "string", "format": "uint64", "title": "@gotags: validate:\"required\""}, "price_id": {"type": "string", "format": "uint64"}, "page": {"$ref": "#/definitions/products_managePageOrder"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "type_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "products_manageQueryRangeOfOrderResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/products_manageRangeOfOrder"}}, "total": {"type": "string", "format": "int64"}}}, "products_manageRangeOfOrder": {"type": "object", "properties": {"store_name": {"type": "string", "title": "门店"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_code": {"type": "string"}, "spec": {"type": "string", "title": "商品规格"}, "product_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string", "title": "订货单位"}, "unit_code": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "type_name": {"type": "string", "title": "订货类型"}, "type_code": {"type": "string"}, "order_date": {"type": "string", "title": "订货类型时段"}, "type_id": {"type": "string", "format": "uint64"}, "price_type": {"type": "string", "title": "价格类型"}, "order_price": {"type": "string"}, "min_qty": {"type": "string", "title": "订货规则\n起订量"}, "max_qty": {"type": "string", "title": "最大量"}, "increase_qty": {"type": "string", "title": "递增量"}, "distrcenter_id": {"type": "string", "format": "uint64", "title": "仓库"}, "distrcenter_name": {"type": "string"}, "arrival_days": {"type": "string"}}}, "products_manageResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "products_manageRuleUniqueKey": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64", "title": "@gotags: validate:\"required\""}, "product_id": {"type": "string", "format": "uint64", "title": "@gotags: validate:\"required\""}}}, "products_manageSaleTaxItem": {"type": "object", "properties": {"sales_tax_rule_id": {"type": "string", "format": "uint64", "title": "税率规则ID"}, "item_type": {"type": "integer", "format": "int32", "title": "明细类型：'1:属性、2：商品',"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id   item_type 为1 时为空"}, "product_code": {"type": "string", "title": "商品编码 item_type 为1 时为空"}, "product_name": {"type": "string", "title": "商品名称 item_type 为1 时为空"}, "spec": {"type": "string", "title": "商品规格 //item_type 为1 时为空"}, "product_type": {"type": "string", "title": "属性编码  item_type 为2 时为空"}, "id": {"type": "string", "format": "uint64", "title": "明细ID"}, "is_delete": {"type": "integer", "format": "int64", "title": "明细状态"}}}, "products_manageSaleTaxLogRequest": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64"}}}, "products_manageSaleTaxLogResponse": {"type": "object", "properties": {"log": {"type": "array", "items": {"$ref": "#/definitions/products_manageExecLog"}}}}, "products_manageSaleTaxTotal": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "税率规则ID 新增时不需传"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户iD"}, "code": {"type": "string", "title": "税率规则编号"}, "name": {"type": "string", "title": "税率规则名称"}, "rate": {"type": "string", "title": "税率"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID"}, "company_name": {"type": "string", "title": "公司名称"}, "updated_by": {"type": "string", "format": "int64", "title": "更新人"}, "updated": {"type": "string", "format": "int64", "title": "更新时间"}, "items": {"type": "array", "items": {"$ref": "#/definitions/products_manageSaleTaxItem"}, "title": "税率规则明细"}, "status": {"type": "string", "title": "状态 新增是不需要传"}, "effective_date": {"type": "string", "format": "int64", "title": "生效日期"}, "is_all": {"type": "integer", "format": "int64", "title": "是否全部门店 1 是 0 否（新增、更新时传入）"}, "remark": {"type": "string", "title": "备注"}, "created_name": {"type": "string", "title": "创建人名称"}, "updated_name": {"type": "string", "title": "更新人信息"}}}, "products_manageStatus": {"type": "string", "enum": ["Undefinition", "DISABLED", "ENABLED"], "default": "Undefinition", "title": "- Undefinition: 状态未定义\n - DISABLED: 禁用\n - ENABLED: 可用"}, "products_manageStoreProductRelation": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}}, "title": "门店于商品关系"}, "products_manageSyncMetaDataRequest": {"type": "object", "properties": {"schema_name": {"type": "string", "title": "schema_name"}, "schema_id": {"type": "string", "format": "uint64", "title": "schema_id"}, "action": {"type": "string", "title": "操作类型"}, "fields_from": {"type": "object"}, "fields_to": {"type": "object"}}}, "products_manageTrussActivity": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "status": {"$ref": "#/definitions/products_manageStatus"}, "start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}, "product_id": {"type": "string", "format": "uint64"}, "company_id": {"type": "string", "format": "uint64"}, "type_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_code": {"type": "string"}, "updated": {"type": "string", "format": "uint64"}, "updated_name": {"type": "string"}, "truss_activity_product": {"type": "array", "items": {"$ref": "#/definitions/products_manageTrussActivityProduct"}}, "company_name": {"type": "string"}, "count": {"type": "string", "format": "int64"}, "order_unit_name": {"type": "string"}, "order_unit_id": {"type": "string", "format": "uint64"}, "model_name": {"type": "string"}}}, "products_manageTrussActivityProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_code": {"type": "string"}, "product_id": {"type": "string"}, "ratio": {"type": "string"}, "truss_activity_id": {"type": "string"}, "configure": {"type": "string", "format": "uint64"}, "product": {"$ref": "#/definitions/products_manageProductsList"}, "order_unit_name": {"type": "string"}, "order_unit_id": {"type": "string", "format": "uint64"}, "model_name": {"type": "string"}}}, "products_manageUpdateDistributionRuleRequest": {"type": "object", "properties": {"distrcenter_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司ID 必传"}, "distr_type": {"type": "string", "title": "物流模式"}, "distribution_rule": {"type": "array", "items": {"$ref": "#/definitions/products_manageDistributionRuleModel"}}, "is_check": {"type": "boolean", "title": "ture 不进行数据库插入"}}}, "products_manageUpdateGroupRequest": {"type": "object", "properties": {"company_id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "name": {"type": "string"}, "remark": {"type": "string"}, "group_type": {"type": "integer", "format": "int64", "title": "1价格组 2订货规则组 3配送规则组"}, "stores": {"type": "array", "items": {"$ref": "#/definitions/products_manageGroupStore"}, "title": "组里生效的门店 全部传入"}, "group_product": {"type": "array", "items": {"$ref": "#/definitions/products_manageProductsList"}, "title": "组里的商品 全部传入"}, "id": {"type": "string", "format": "uint64"}}}, "products_manageUpdateOrderRuleRequest": {"type": "object", "properties": {"ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "order_rule": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderRuleModel"}, "title": "规则列表"}, "is_check": {"type": "boolean", "title": "ture 不进行数据库插入"}}, "title": "添加门店商品价格请求"}, "products_manageUpdateOrderTypeGroupBatchRequest": {"type": "object", "properties": {"batch": {"type": "object", "additionalProperties": {"$ref": "#/definitions/products_manageUpdateOrderTypeGroupRequest"}}}}, "products_manageUpdateOrderTypeGroupBatchResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "err_message": {"type": "object", "additionalProperties": {"type": "string"}}}}, "products_manageUpdateOrderTypeGroupRequest": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "visual_angle": {"type": "string"}, "aggregation_detail": {"type": "array", "items": {"$ref": "#/definitions/products_manageOrderTypeGroupChildrenDetail"}}}}, "products_manageUpdateOrderTypeGroupStatusRequest": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "type_id": {"type": "string", "format": "uint64"}, "visual_angle": {"type": "string"}, "status": {"type": "string"}}}, "products_manageUpdatePlanItemRequest": {"type": "object", "properties": {"plan_id": {"type": "string", "format": "uint64", "title": "计划id"}, "ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "products": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetPlanItemList"}}}}, "products_manageUpdatePriceRequest": {"type": "object", "properties": {"ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "effective_type": {"$ref": "#/definitions/products_manageEffectiveType", "title": "生效方式 必传"}, "effective_date": {"type": "string", "format": "int64", "title": "生效日期 生效方式为计划生效时必传"}, "products": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetPriceSkuByStoreList"}}}}, "products_manageUpdateSkuPriceByProductRequest": {"type": "object", "properties": {"ditch_id": {"type": "string", "format": "uint64", "title": "渠道id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "effective_type": {"$ref": "#/definitions/products_manageEffectiveType", "title": "生效方式 必传"}, "effective_date": {"type": "string", "format": "int64", "title": "生效日期 生效方式为计划生效时必传"}, "products": {"type": "array", "items": {"$ref": "#/definitions/products_manageGetPriceSkuByProductList"}}}}, "products_managefilter": {"type": "object", "properties": {"not_order_unit": {"type": "boolean"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\nThe JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}}}