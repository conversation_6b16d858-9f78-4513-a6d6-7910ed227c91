syntax = "proto3";
package receiving;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// 收货报表相关服务
service ReceivingBiService{
    
    // 查询收货单汇总报表
    rpc GetReceivingCollect(GetReceivingCollectRequest) returns (GetReceivingCollectResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/receiving/bi/collect"
        };
    }
    // 查询收货单详情报表
    rpc GetReceivingDetail(GetReceivingDetailRequest) returns (GetReceivingDetailResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/receiving/bi/detailed"
        };
    }

}


message GetReceivingCollectRequest{
    // 门店id
    repeated uint64 st_ids = 1;
    // 商品类别id
    repeated uint64 category_ids = 2;
    // 是否直送
    bool is_direct = 3;
    // 商品名称
    string product_name = 4;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 5;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 6;
    // 分页大小
    int32 limit = 7;
    // 跳过行数
    int32 offset = 8;
    // 返回总条数
    bool include_total = 9;
}


message GetReceivingCollectResponse{
    repeated ReceivingCollect rows = 1;
    Total total = 2;
}


message GetReceivingDetailRequest{

    // 门店id
    repeated uint64 st_ids = 1;
    // 商品类别id
    repeated uint64 category_ids = 2;
    // 是否直送
    bool is_direct = 3;
    // 商品名称
    string product_name = 4;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 5;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 6;
    // 分页大小
    int32 limit = 7;
    // 跳过行数
    int32 offset = 8;
    // 返回总条数
    bool include_total = 9;
    // 单据号
    string code = 10;

}

message GetReceivingDetailResponse{
    repeated ReceivingDetailed rows = 1;
    Total total = 2;
}

message Total{
    // 条数
    double count = 1;
    // 合计数量
    double sum_all_quantity = 2;
    // 合计确认数量
    double sum_confirmed_quantity = 3;
    // 合计配送数量
    double sum_received_quantity = 4;
    // 合计订货数量
    double sum_required_quantity = 5;
    // 合计差异数量
    double sum_s_diff_quantity = 6;
}

message ReceivingCollect{
    // 核算单位id
    uint64 accounting_unit_id = 1;
    // 核算单位名称
    string accounting_unit_name = 2;
    // 商品类别id
    uint64 category_id = 3;
    // 商品类别编码
    string category_code = 4;
    // 商品类别名称
    string category_name = 5;
    // 商品类别父级
    string category_parent = 6;
    // 确认数量
    double confirmed_quantity = 7;
    // 商品id
    uint64 product_id = 8;
    // 商品编码
    string product_code = 9;
    // 商品名称
    string product_name = 10;
    // 商品规格
    string product_spec = 11;
    // 数量
    double quantity = 12;
    // 配送数量
    double received_quantity = 13;
    // 订货数量
    double required_quantity = 14;
    // 差异数量
    double s_diff_quantity = 15;
    // 门店id
    uint64 store_id = 16;
    // 门店编码
    string store_code = 17;
    // 门店名称
    string store_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
}

message ReceivingDetailed{
    // 核算单位id
    uint64 accounting_unit_id = 1;
    // 核算单位名称
    string accounting_unit_name = 2;
    // 商品类别id
    uint64 category_id = 3;
    // 商品类别编码
    string category_code = 4;
    // 商品类别名称
    string category_name = 5;
    // 商品类别父级
    string category_parent = 6;
    // 确认数量
    double confirmed_quantity = 7;
    // 商品id
    uint64 product_id = 8;
    // 商品编码
    string product_code = 9;
    // 商品名称
    string product_name = 10;
    // 商品规格
    string product_spec = 11;
    // 数量
    double quantity = 12;
    // 配送数量
    double received_quantity = 13;
    // 订货数量
    double required_quantity = 14;
    // 差异数量
    double s_diff_quantity = 15;
    // 门店id
    uint64 store_id = 16;
    // 门店编码
    string store_code = 17;
    // 门店名称
    string store_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 配送日期
    string delivery_date = 21;
    // 订货日期
    string demand_date = 22;
    // 订货单编码
    string receiving_code = 23;
    // 收货日期
    string receiving_date = 24;
    // 收货单id
    uint64 receiving_id = 25;
    // id
    uint64 id = 26;
    // 
    uint64 jde_order_id = 27;
    string jde_order_type = 28;
    string jde_mcu = 29;
    
}






// message Product{
//     // id 
//     uint64 id = 1; 
//     // 收货单id
//     uint64 receiving_id = 2;
//     // 收货门店
//     uint64 received_by = 3;
//     // 商品id
//     uint64 product_id = 4;
//     // 核算单位id
//     uint64 accounting_unit_id = 5;
//     // 单位id
//     uint64 unit_id = 6;

//     // 商品编码
//     string product_code = 7;
//     // 商品名称
//     string product_name = 8; 
//     // 物料编码
//     string material_number = 9;
//     // 核算单位名称
//     string accounting_unit_name = 10;
//     // 核算单位规格
//     string accounting_unit_spec = 11;
//     // 单位名称
//     string unit_name = 12;
//     // 单位规格
//     string unit_spec= 13;
//     // 转换比率
//     double unit_rate = 14;
//     // 存储方式
//     // enum STORAGE {
//     //     "干货" = 1;
//     //     不干货 = 2;
//     // }
//     string storage_type = 15;

//     // 核算订货数量
//     double required_accounting_quantity = 16;
//     // 订货数量
//     double required_quantity = 17;
//     // 核算收货数量
//     double received_accounting_quantity = 18;
//     // 收货数量
//     double received_quantity = 19;
//     // 确认核算收货数量
//     double confirmed_accounting_quantity = 20;
//     // 确认收货数量
//     double confirmed_quantity = 21;

//     // 收货状态
//     bool is_confirmed = 22;

//     // 创建时间
//     google.protobuf.Timestamp created_at = 23;
//     // 创建人id
//     uint64 created_by= 24;
//     // 更新日期
//     google.protobuf.Timestamp updated_at = 25;
//     // 更新人id
//     uint64 updated_by = 26;
//     // jde预留商品id
//     uint64 jde_product_id = 27;
//     // 订货日期
//     google.protobuf.Timestamp demand_date = 28;
//     // 商户id
//     uint64 partner_id = 29;
//     // 创建人
//     string created_name = 30;
//     // 更新人
//     string updated_name = 31;

// }


// message product{



//     // 收货单id
//     uint64 id = 1;
//     // 收货单单号
//     string code = 2;
//     //订货单id
//     // 原master_id
//     uint64 master_id = 3;
//     // 订货单单号
//     string master_code = 4;
//     // 要货单id
//     uint64 demand_order_id = 5;
//     // 要货单单号
//     // 原order_id
//     string demand_order_code = 6;
//     // 收货差异单id
//     // 为空，则无收货差异，增减库存；不为空，在确认收货差异单后再增减库存
//     uint64 receiving_diff_id = 7;
//     // JDE订单号
//     // 原sap_order_id
//     // Mia：类型需确认
//     uint64 jde_order_id = 8;

//     // 接收门店-门店id
//     uint64 received_by = 9;
//     // 预留JDE/heytea门店号 
//     // 字段类型需要确认
//     uint64 store_secondary_id = 10;

//     // 收货单状态
//     // enum Status {
//     //     // 新建/未收货
//     //     INITED = 0;
//     //     // 已确认
//     //     CONFIRMED = 1;
//     // }
//     string status = 11;
    
//     // 配送单号 
//     // Mia：？
//     string delivery_note_number = 12;
//     // 配送方
//     string delivery_by = 13;
//     // 配送时间
//     google.protobuf.Timestamp delivery_date = 14;
//     // 是否直送
//     // Mia：冷链/ 大货？
//     bool is_direct = 15;
    
//     // 是否需要增减库存
//     bool calculate_inventory = 16;
//     // 存储类型 - 干货/？？
//     string storage_type = 17;
//     // 备注
//     string remark = 18;

//     // 库存引擎调用
//     // enum inventoryStatus {
//     //     SENT = 1; // 已发送
//     //     SENT_SUC = 2; //调用成功
//     //     SENT_FAIL = 3; //调用失败
//     // }
//     string inventory_status = 19;
//     // 库存id预留字段
//     uint64 inventory_req_id = 20;

//     // 差异单生成状态
//     // enum DiffProcessStatus {
//     //     INIT = 0; // 
//     //     CREATED = 1; // 收货差异单生成
//     // }
//     string process_status = 21;
    
//     // 创建时间
//     google.protobuf.Timestamp created_at = 22;
//     // 更新时间
//     google.protobuf.Timestamp updated_at = 23;
//     // 创建人id
//     int64 created_by = 24;
//     // 更新人id
//     int64 updated_by = 25;
//     // 订货时间
//     google.protobuf.Timestamp demand_date = 26;

//     // 收货类型
//     string received_type = 27;
//     // 是否已验证
//     bool has_checked = 28;
//     // 商户id
//     uint64 partner_id = 29;
//     // 延迟原因
//     string delay_reason = 30;
//     // 附件
//     string attachments = 31;
//     // 商品数量
//     uint64 product_nums = 32;
//     // 创建人
//     string created_name = 33;
//     // 更新人
//     string updated_name = 34;
// }

 


