{"swagger": "2.0", "info": {"title": "frs_store_extra/return.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/frs_store_extra/adjust_returns": {"post": {"summary": "创建退货单（进行拆单）", "operationId": "CreateAdjustReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnCreateAdjustReturnResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnCreateReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/return/check": {"post": {"summary": "校验按收货原单退货是否超退", "operationId": "CheckReturnAvailableByrec", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnCommonReply"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnCheckReturnAvailableRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/return/product": {"get": {"summary": "查询可退货商品", "operationId": "GetValidProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnValidProduct"}}}, "parameters": [{"name": "id", "description": "id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "product_name", "description": "商户id\nuint64 partner_id = 5;\n商品名称.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns": {"get": {"summary": "查询退货单", "operationId": "ListReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnListReturnResponse"}}}, "parameters": [{"name": "store_ids", "description": "门店id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "status", "description": "订单状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "start_date", "description": "退货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "退货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "description": "退货单号\ndq目前没有.", "in": "query", "required": false, "type": "string"}, {"name": "limit", "description": "收货单号\ndq目前没有\nstring receiving_ids = 10;\n分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_direct", "description": "是否直送.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order_by", "description": "排序.", "in": "query", "required": false, "type": "string"}, {"name": "logistics_type", "description": "单据物流来源类型:BREAD 面包工厂.", "in": "query", "required": false, "type": "string"}, {"name": "is_adjust", "description": "调整单标志.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}, {"name": "storage_type", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "来源：原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD.", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "branch类型： store/ warehouse/ machining.", "in": "query", "required": false, "type": "string"}, {"name": "receive_code", "description": "收货单号/退货入库单.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "return_date_from", "description": "退货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "return_date_to", "description": "退货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_from", "description": "提货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_to", "description": "提货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "return_number", "description": "差异单号.", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "description": "商品ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "return_by", "description": "退货方id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "return_to", "description": "退货方接收方.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "source_id", "description": "来源id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "source_code", "in": "query", "required": false, "type": "string"}, {"name": "payment_ways", "in": "query", "required": false, "type": "string"}, {"name": "delivery_code", "description": "发货单.", "in": "query", "required": false, "type": "string"}], "tags": ["FrsStoreExtraReturnService"]}, "post": {"summary": "创建退货单", "operationId": "CreateReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnCreateReturnResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnCreateReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/cold": {"get": {"summary": "查询退货单--冷链", "operationId": "ListReturnCold", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnListReturnResponse"}}}, "parameters": [{"name": "store_ids", "description": "门店id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "status", "description": "订单状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "start_date", "description": "退货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "退货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "description": "退货单号\ndq目前没有.", "in": "query", "required": false, "type": "string"}, {"name": "limit", "description": "收货单号\ndq目前没有\nstring receiving_ids = 10;\n分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_direct", "description": "是否直送.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "logistics_type", "description": "单据物流来源类型:BREAD 面包工厂.", "in": "query", "required": false, "type": "string"}, {"name": "is_adjust", "description": "调整单标志.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}, {"name": "storage_type", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "return_date_from", "description": "退货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "return_date_to", "description": "退货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_from", "description": "提货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_to", "description": "提货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}": {"get": {"summary": "根据id查询退货单详情", "operationId": "GetReturnById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnReturns"}}}, "parameters": [{"name": "id", "description": "退货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "description": "是否显示详情\n可否删除？\nbool is_details = 2;.", "in": "query", "required": false, "type": "string"}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/approve": {"put": {"summary": "审核退货单", "operationId": "ApproveReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnApproveReturnResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnApproveReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/confirm": {"put": {"summary": "确认退货", "operationId": "ConfirmReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnConfirmReturnResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnConfirmReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/delete": {"put": {"summary": "删除新建的退货单", "operationId": "DeleteReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnDeleteReturnResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnDeleteReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/delivery": {"put": {"summary": "确认提货", "operationId": "DeliveryReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnDeliveryReturnResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnDeliveryReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/product": {"get": {"summary": "根据id查询退货单商品详情\n查询退货单详情时需调用", "operationId": "GetReturnProductById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnGetReturnProductByIdResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "商户id\nuint64 partner_id = 2;\n分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/reject": {"put": {"summary": "驳回退货单", "operationId": "RejectReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnRejectReturnResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnRejectReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/submit": {"put": {"summary": "提交退货单", "operationId": "SubmitReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnSubmitReturnResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnSubmitReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/update": {"put": {"summary": "更新退货单\n修改退货数量 - 无保存修改按钮\n添加退货商品", "operationId": "UpdateReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnUpdateReturnResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnUpdateReturnRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}, "/api/v2/supply/frs_store_extra/returns/{id}/update/remark": {"put": {"summary": "修改备注", "operationId": "UpdateRemark", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_returnUpdateRemarkResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_returnUpdateRemarkRequest"}}], "tags": ["FrsStoreExtraReturnService"]}}}, "definitions": {"frs_store_extra_returnApproveReturnRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "trans_type": {"type": "string", "title": "提货方式"}, "lan": {"type": "string"}}}, "frs_store_extra_returnApproveReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnCheckReturnAvailableRequest": {"type": "object", "properties": {"return_by": {"type": "string", "format": "uint64", "title": "退货门店\n= 门店id"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "交货日期"}, "type": {"type": "string", "title": "退货类型"}, "sub_type": {"type": "string", "title": "子类型"}, "return_reason": {"type": "string", "title": "退货原因"}, "remark": {"type": "string", "title": "备注"}, "products": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_returnProduct"}, "title": "商品"}, "is_direct": {"type": "boolean", "format": "boolean", "title": "是否直送"}, "return_to": {"type": "string", "format": "uint64", "title": "仓库/配送中心"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "附件"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:BREAD 面包工厂"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "调整单标志"}, "receiving_id": {"type": "string", "format": "uint64"}, "lan": {"type": "string"}, "return_id": {"type": "string", "format": "uint64"}}}, "frs_store_extra_returnCommonReply": {"type": "object", "properties": {"success": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnConfirmReturnRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "lan": {"type": "string"}}}, "frs_store_extra_returnConfirmReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnCreateAdjustReturnResponse": {"type": "object", "properties": {"return_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "frs_store_extra_returnCreateReturnRequest": {"type": "object", "properties": {"return_by": {"type": "string", "format": "uint64", "title": "退货方id"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:PUR 直送/ NMD 配送"}, "type": {"type": "string", "title": "退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD/ FRS 加盟商退货"}, "sub_type": {"type": "string", "title": "退货方类型： store/ warehouse/ machining"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "交货日期"}, "source_id": {"type": "string", "format": "uint64", "title": "退货单来源id"}, "source_code": {"type": "string"}, "return_reason": {"type": "string", "title": "退货原因"}, "remark": {"type": "string", "title": "备注"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "附件"}, "request_id": {"type": "string", "format": "uint64", "title": "唯一请求号，保证不重复请求"}, "products": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_returnProduct"}, "title": "商品"}, "lan": {"type": "string", "title": "语言"}}}, "frs_store_extra_returnCreateReturnResponse": {"type": "object", "properties": {"return_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnDeleteReturnRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "lan": {"type": "string"}}}, "frs_store_extra_returnDeleteReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnDeliveryReturnRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "lan": {"type": "string"}, "fs_code": {"type": "string", "title": "快递单号"}}}, "frs_store_extra_returnDeliveryReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnGetReturnProductByIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_returnProductDetail"}}, "total": {"type": "string", "format": "uint64"}}}, "frs_store_extra_returnListReturnResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_returnReturns"}}, "total": {"type": "string", "format": "uint64"}}}, "frs_store_extra_returnProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品表里的id（supply_receiving_diff_product.id)"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "confirmed_quantity": {"type": "number", "format": "double", "title": "收货数量"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "unit_rate": {"type": "number", "format": "float", "title": "单位换算比例"}, "tax_rate": {"type": "number", "format": "float"}, "price": {"type": "number", "format": "float"}, "price_tax": {"type": "number", "format": "float"}, "id": {"type": "string", "format": "uint64"}, "return_to": {"type": "string", "format": "uint64", "title": "退货接收方"}, "logistics_type": {"type": "string"}, "refund_amount": {"type": "number", "format": "double", "title": "退款金额"}}}, "frs_store_extra_returnProductDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "return_id": {"type": "string", "format": "uint64", "title": "退货单id"}, "return_by": {"type": "string", "format": "uint64", "title": "退货门店"}, "material_number": {"type": "string", "title": "物料编码"}, "product_code": {"type": "string", "title": "商品编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "accounting_unit_name": {"type": "string", "title": "单位名称"}, "accounting_unit_spec": {"type": "string", "title": "单位规格"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_rate": {"type": "number", "format": "double", "title": "单位换算比例"}, "unit_spec": {"type": "string", "title": "单位规格"}, "accounting_confirmed_quantity": {"type": "number", "format": "double", "title": "确认退货数量"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_returned_quantity": {"type": "number", "format": "double"}, "confirmed_quantity": {"type": "number", "format": "double", "title": "确认收货数量"}, "returned_quantity": {"type": "number", "format": "double", "title": "退货数量"}, "is_confirmed": {"type": "boolean", "format": "boolean", "title": "状态"}, "return_date": {"type": "string", "format": "date-time", "title": "退货日期"}, "return_to": {"type": "string", "format": "uint64", "title": "接收方"}, "inventory_status": {"type": "string", "title": "库存引擎调用\nenum inventoryStatus {\n    SENT = 1; // 已发送\n    SENT_SUC = 2; //调用成功\n    SENT_FAIL = 3; //调用失败\n}"}, "inventory_req_id": {"type": "string", "format": "uint64", "title": "库存id预留字段"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新日期"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "storage_type": {"type": "string"}, "price": {"type": "number", "format": "double", "title": "未税单价"}, "tax_rate": {"type": "number", "format": "float", "title": "税率"}, "price_tax": {"type": "number", "format": "double", "title": "含税单价"}, "sum_price": {"type": "number", "format": "double", "title": "采购总价"}, "tax_amount": {"type": "number", "format": "double", "title": "税额"}, "return_price": {"type": "number", "format": "double", "title": "退货金额"}}}, "frs_store_extra_returnRejectReturnRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "lan": {"type": "string"}}}, "frs_store_extra_returnRejectReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnReturns": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "code": {"type": "string", "title": "退货单单号"}, "return_by": {"type": "string", "format": "uint64", "title": "退货门店"}, "return_number": {"type": "string", "title": "退货编号"}, "status": {"type": "string", "title": "退货单状态"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人"}, "is_returned": {"type": "boolean", "format": "boolean", "title": "提货状态\n原extends"}, "return_delivery_number": {"type": "string", "title": "退货提货单单单号"}, "return_date": {"type": "string", "format": "date-time", "title": "退货日期"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "预计退货提货时间"}, "return_reason": {"type": "string", "title": "退货原因"}, "type": {"type": "string", "title": "退货方式\n{ '01': '统退', '02': '直退' }"}, "sub_type": {"type": "string", "title": "退货类型\n{ '01': '自主退货', '02': '召回' }"}, "remark": {"type": "string", "title": "备注"}, "store_secondary_id": {"type": "string", "title": "预留门店号"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "return_to": {"type": "string", "format": "uint64", "title": "退货接受方 门店id"}, "inventory_status": {"type": "string", "title": "// 库存引擎调用\nenum inventoryStatus {\n    SENT = 0; // 已发送\n    SENT_SUC = 2; //调用成功\n    SENT_FAIL = 3; //调用失败\n}"}, "inventory_req_id": {"type": "string", "format": "uint64", "title": "库存id预留字段"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "is_direct": {"type": "boolean", "format": "boolean", "title": "收否直送"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "附件"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "product_nums": {"type": "string", "format": "uint64", "title": "商品数量"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:BREAD 面包工厂"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "调整单标志"}, "send_type": {"type": "string", "title": "对接三方的渠道"}, "trans_type": {"type": "string", "title": "提货方式"}, "storage_type": {"type": "string"}, "receiving_id": {"type": "string", "format": "uint64"}, "receiving_code": {"type": "string"}, "delivery_date": {"type": "string", "format": "date-time", "title": "实际提货日期"}, "request_id": {"type": "string", "format": "uint64"}, "refund_amount": {"type": "number", "format": "double", "title": "退款金额"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商信息"}, "franchisee_code": {"type": "string"}, "franchisee_name": {"type": "string"}, "payment_way": {"type": "string", "title": "退款方式"}, "delivery_code": {"type": "string"}, "receive_code": {"type": "string"}, "source_id": {"type": "string", "format": "uint64", "title": "来源"}, "source_code": {"type": "string"}, "return_to_code": {"type": "string"}, "return_to_name": {"type": "string"}, "returns_price": {"type": "number", "format": "double", "title": "商品的报表信息"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "unit_spec": {"type": "string"}, "price_tax": {"type": "number", "format": "double"}, "quantity": {"type": "number", "format": "double"}, "return_by_code": {"type": "string"}, "return_by_name": {"type": "string"}}}, "frs_store_extra_returnSubmitReturnRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "lan": {"type": "string"}}}, "frs_store_extra_returnSubmitReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnUnit": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "quantity": {"type": "integer", "format": "int32", "title": "单位数量"}, "rate": {"type": "number", "format": "double"}}}, "frs_store_extra_returnUpdateRemarkRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "remark": {"type": "string", "title": "remark"}, "lan": {"type": "string", "title": "user_id\nuint64 user_id = 3;"}}}, "frs_store_extra_returnUpdateRemarkResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnUpdateReturnRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_returnProduct"}}, "return_reason": {"type": "string", "title": "退货原因"}, "remark": {"type": "string", "title": "备注"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "退货交货时间"}, "return_to": {"type": "string", "format": "uint64", "title": "供应商/配送中心"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "附件"}, "lan": {"type": "string"}, "logistics_type": {"type": "string"}}}, "frs_store_extra_returnUpdateReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_returnValidProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "spec": {"type": "string", "title": "规格"}, "unit": {"$ref": "#/definitions/frs_store_extra_returnUnit", "title": "单位"}}}}}