syntax = "proto3";
package frs_store_extra_receive_diff;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// ReceivingDiffService 收货差异相关服务
service FrsStoreExtraReceiveDiffService{

    // 查询收货差异单
    rpc ListReceiveDiff(ListReceiveDiffRequest) returns (ListReceiveDiffResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/receive-diff"
        };
    }

    // 根据id查询收货差异单详情
    rpc GetReceiveDiffById(GetReceiveDiffByIdRequest) returns (ReceiveDiff){
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/receive-diff/{id}"
        };
    }

    // 根据id查商品详情
    rpc GetReceiveDiffProductById (GetReceiveDiffProductByIdRequest) returns (GetReceiveDiffProductByIdResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/receive-diff/{id}/product"
        };
    }

    // 提交收货差异单
    rpc SubmitReceiveDiff (SubmitReceiveDiffRequest) returns (SubmitReceiveDiffResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/frs_store_extra/receive-diff/{id}/submit"
            body:"*"
        };
    }

    // 确认收货差异单
    rpc ConfirmReceiveDiff(ConfirmReceiveDiffRequest) returns (ConfirmReceiveDiffResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/frs_store_extra/receive-diff/{id}/confirm"
            body:"*"
        };
    }

    // 驳回收货差异单
    rpc RejectReceiveDiff(RejectReceiveDiffRequest) returns (RejectReceiveDiffResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/frs_store_extra/receive-diff/{id}/reject"
            body:"*"
        };

    }

    // 更新收货单差异单
    rpc UpdateReceiveDiff(UpdateReceiveDiffRequest) returns (UpdateReceiveDiffResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/frs_store_extra/receive-diff/{id}/update"
            body:"*"
        };
    }

    // 删除收货差异单
    rpc DeleteReceiveDiff(DeleteReceiveDiffRequest) returns (DeleteReceiveDiffResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/frs_store_extra/receive-diff/{id}/delete"
            body:"*"
        };
    }

    // 根据id查询单据历史记录
    rpc GetHistoryById(GetHistoryByIdRequest) returns (HistoryResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/receive-diff/{id}/history"
        };
    }

}

message GetHistoryByIdRequest{
    // 差异单id
    uint64 id = 1;
}

message HistoryResponse{
    repeated History rows = 1;
    uint64 total = 2;
}

message History{
    // 历史记录id
    uint64 id = 1;
    // 差异单状态
    string status = 2;
    // 操作人名称或者系统自动操作
    uint64 updated_by = 3;
    // 操作时间
    google.protobuf.Timestamp updated_at = 4;
    string updated_by_name = 5;
}

message CreateReceiveDiffRequest{
    // 对应收货单id
    uint64 receiving_id = 1;
    // 确认的商品详情
    repeated ReceiveDiffProduct confirmed_products = 2;
    // 审核人
    uint64 review_by = 3;
    string lan = 4;
    // 附件
    repeated Attachments attachments = 5;
    // 提货日期
    google.protobuf.Timestamp delivery_date = 6;
    // 预计提货日期
    google.protobuf.Timestamp pre_delivery_date = 7;
}

message CreateReceiveDiffResponse{
    uint64 receiving_diff_id = 1;
}

message ListReceiveDiffRequest{
    // 门店id
    repeated uint64 store_ids = 1;
    // 订单状态
    repeated string status = 2;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 3;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 4;
    // 分页大小
    int32 limit = 5;
    // 跳过行数
    int32 offset = 6;
    // 返回总条数
    bool include_total = 7;
    // 是否直送
    bool is_direct = 8;
    // 收货单id
    uint64 receiving_ids = 9;
    // 收货单单号
    string receiving_code = 10;
    // 商品ids
    repeated uint64 product_ids = 11;
    // 差异单单号
    string code = 12;
    // 单据物流来源类型:BREAD 面包工厂
    repeated string logistics_type = 13;
    // 排序字段
    string sort = 14;
    // 排序顺序
    string order = 15;
    string lan = 17;
    // 退货开始日期
    google.protobuf.Timestamp return_date_from = 18;
    // 退货结束日期
    google.protobuf.Timestamp return_date_to = 19;
    // 提货开始日期
    google.protobuf.Timestamp delivery_date_from = 20;
    // 提货结束日期
    google.protobuf.Timestamp delivery_date_to = 21;


    // 收货开始日期
    google.protobuf.Timestamp receive_date_from = 22;
    // 收货结束日期
    google.protobuf.Timestamp receive_date_to = 23;
    // 订货开始日期
    google.protobuf.Timestamp demand_date_from = 24;
    // 订货结束日期
    google.protobuf.Timestamp demand_date_to = 25;

    //
    repeated uint64 ids = 26;
    // 配送方id
    repeated uint64 delivery_bys = 27;
    // 收货差异单的主档类型
    string branch_type = 28;
    // 差异单来源 AUTO, HC
    string diff_type = 29;

}

message ListReceiveDiffResponse{
    repeated ReceiveDiff rows = 1;
    uint64 total = 2;
}


message GetReceiveDiffByIdRequest{
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    string lan = 5;
}

message GetReceiveDiffProductByIdRequest{
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    string lan = 7;
}

message GetReceiveDiffProductByIdResponse{
    repeated ReceiveDiffProduct rows = 1;
    uint64 total = 2;
    // 配送方承担金额
    double total_d_amount = 3;
    // 门店承担金额
    double total_s_amount = 4;
}

message SubmitReceiveDiffRequest{
    // id
    uint64 id  = 1;
    string lan = 2;
}

message SubmitReceiveDiffResponse{
    bool payload = 1;
    // wrapper.Status status = 2;
}

message ConfirmReceiveDiffRequest{
    //id
    uint64 id = 1;
    string lan = 2;
}

message ConfirmReceiveDiffResponse{
    bool payload = 1;
    // wrapper.Status status = 2;
}

message RejectReceiveDiffRequest{
    //id
    uint64 id = 1;
    string reject_reason = 2;
    repeated Attachments attachments = 3;
    string lan = 4;
}

message RejectReceiveDiffResponse{
    bool payload = 1;

}

message DeleteReceiveDiffRequest{
    uint64 id = 1;
    string lan = 2;
}

message DeleteReceiveDiffResponse{
    bool payload = 1;
}

message UpdateReceiveDiffRequest{
    uint64 id = 1;
    repeated Product products = 2;
    repeated Attachments attachments = 3;
    string lan = 4;
    string remark = 5;
    string reason = 6;
}

message UpdateReceiveDiffResponse{
    // wrapper.Status status = 1;
    bool payload = 1;
}

message ReceiveDiff{
    // id
    uint64 id = 1;
    // 差异单单号
    string code = 2;
    // 接收门店 - store_id
    uint64 received_by = 3;
    // 收货单id
    uint64 receiving_id = 4;
    // 收货单单号
    string receiving_code = 5;
    // 订货单id
    uint64 master_id = 6;
    // 订货单单号
    // 原order_id
    string master_code = 7;

    // 收货差异单单状态
    // enum Status {
    //     INITED = 0;
    //     SUBMITTED = 1;
    //     CONFIRMED = 2;
    //     REJECTED = 3;
    // }
    string status =8;

    // 订单时间
    google.protobuf.Timestamp demand_date = 9;

    // 配送单号
    string delivery_note_number = 10;
    // 配送方
    uint64 delivery_by = 11;
    // 配送时间=实际提货时间
    google.protobuf.Timestamp delivery_date = 12;
    // 差异原因类型
    string reason_type = 13;
    // 审核人
    uint64 review_by = 14;
    // 备注
    string remark = 15;
    // 预留门店id
    string store_secondary_id = 16;

    // // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 0; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 17;
    // 库存id预留字段
    uint64 inventory_req_id = 18;
    // 创建时间
    google.protobuf.Timestamp created_at = 19;
    // 更新时间
    google.protobuf.Timestamp updated_at = 20;
    // 创建人id
    uint64 created_by = 21;
    // 更新人id
    uint64 updated_by = 22;
    // 商户id
    uint64 partner_id = 23;
    // 是否直送
    bool is_direct = 24;
    // 创建人
    string created_name = 25;
    // 更新人
    string updated_name = 26;
    // 驳回原因
    string reject_reason = 27;
    // 附件
    repeated Attachments attachments = 28;
    // 商品数量
    uint64 product_nums = 29;
    // 单据类型
    string received_type = 31;
    // 请求id
    uint64 request_id = 32;
    // 单据物流来源类型:BREAD 面包工厂
    string logistics_type = 33;
    // 对接三方的渠道
    string send_type = 34;
    // 预计退货提货时间
    google.protobuf.Timestamp pre_delivery_date = 36;
    uint64 return_id = 37;
    // 配送时间
    google.protobuf.Timestamp send_date = 38;
    string branch_type = 39;
    uint64 sub_receive_by = 40;
    // 配送方名称
    string delivery_by_name = 41;

    // 收货日期
    google.protobuf.Timestamp receive_date = 42;
    // 自动确认时间
    google.protobuf.Timestamp auto_confirm_date = 43;
    // 最多五个商品名称预览
    repeated string product_names_brief = 44;
    // 配送方编号
    string delivery_by_code = 45;
    // 加盟商id
    uint64 franchisee_id = 46;
    // 差异单来源 AUTO, HC
    string diff_type = 47;
    // 收货门店
    string received_by_name = 48;
    string received_by_code = 49;
    uint64 refund_id = 50;
    string refund_code = 51;
    string refund_status = 52;

}

message ReceiveDiffProduct{
    // id
    uint64 id = 1;
    // 收货单id
    uint64 receiving_id = 2;
    // 收货门店
    uint64 received_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 单位id
    uint64 accounting_unit_id = 5;
    // 主单位id
    uint64 unit_id = 6;

    // 商品编码
    string product_code = 7;
    // 商品名称
    string product_name = 8;
    // 物料编码
    string material_number = 9;

    // 单位名称
    string accounting_unit_name = 10;
    // 单位规格
    string accounting_unit_spec = 11;
    // 核算单位名称
    string unit_name = 12;
    // 核算单位规格
    string unit_spec= 13;
    // 转换比率
    double unit_rate = 14;

    // 数量
    double quantity = 17;

    // 配送数量
    double received_accounting_quantity = 18;
    // 核算配送数量
    double received_quantity = 19;
    // 确认数量
    double confirmed_accounting_quantity = 20;
    // 核算确认数量
    double confirmed_quantity = 21;
    // 差异数量
    double diff_accounting_quantity = 22;
    //核算差异数量
    double diff_quantity = 23;

    // 门店承担退货数量
    double s_diff_accounting_quantity = 24;
    //
    double s_diff_quantity = 25;
    // 第三方承担数量
    double d_diff_accounting_quantity = 26;
    //
    double d_diff_quantity = 27;

    // 订单日期
    google.protobuf.Timestamp demand_date = 28;
    // 创建时间
    google.protobuf.Timestamp created_at = 29;
    // 创建人id
    uint64 created_by = 30;
    // 更新日期
    google.protobuf.Timestamp updated_at = 31;
    // 更新人id
    uint64 updated_by = 32;
    // 商户id
    uint64 partner_id = 33;
    //差异单id
    uint64 diff_id = 34;
    // 差异原因
    string reason_type = 35;
    // 驳回原因
    string reject_reason = 36;
    // 附件
    repeated Attachments attachments = 37;
    // 创建人
    string created_name = 38;
    // 更新人
    string updated_name = 39;
    // 备注
    string remark = 40;
    double cost_price = 41;
    double tax_price = 42;
    double tax_rate = 43;
    uint64 sub_receive_by = 44;

    //规格
    string spec = 45;
    // 零售价
    double retail_price = 47;
    // 配送方承担金额
    double d_amount = 48;
    double s_amount = 49;
}

message Product{
    // id
    uint64 id = 1;
    // 门店承担数量
    double s_diff_quantity = 2;
    // 配送费承担数量
    double d_diff_quantity = 3;
    // 差异原因
    string reason_type = 4;
    // 备注
    string remark = 5;

}

message DiffProduct{
    // id
    uint64 id = 1;
    // remark
    string remark = 2;
    //
    string reason_type = 3;
    // 更新人
    uint64 created_by = 4;
}

message Attachments {
    string type = 1;
    string url = 2;
}