syntax = "proto3";
package frs_store_extra_return;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// ReturnService 退货相关服务
service FrsStoreExtraReturnService {

    // 创建退货单
    rpc CreateReturn (CreateReturnRequest) returns (CreateReturnResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/frs_store_extra/returns"
            body:"*"
        };
    }

    // 确认退货
    rpc ConfirmReturn (ConfirmReturnRequest) returns (ConfirmReturnResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs_store_extra/returns/{id}/confirm"
            body:"*"
        };
    }

    // 查询退货单
    rpc ListReturn (ListReturnRequest) returns (ListReturnResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/returns"
        };
    }
    // 查询可退货商品
    rpc GetValidProduct (GetValidProductRequest) returns (GetValidProductResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/return/product"
        };
    }

    // 查询退货单--冷链
    rpc ListReturnCold (ListReturnColdRequest) returns (ListReturnResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/returns/cold"
        };
    }

    // 根据id查询退货单详情
    rpc GetReturnById (GetReturnByIdRequest) returns (Returns) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/returns/{id}"
        };
    }

    // 根据id查询退货单商品详情
    // 查询退货单详情时需调用
    rpc GetReturnProductById (GetReturnProductByIdRequest) returns (GetReturnProductByIdResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/returns/{id}/product"
        };
    }

    // 提交退货单
    rpc SubmitReturn (SubmitReturnRequest) returns (SubmitReturnResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs_store_extra/returns/{id}/submit"
            body:"*"
        };
    }

    // 驳回退货单
    rpc RejectReturn (RejectReturnRequest) returns (RejectReturnResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs_store_extra/returns/{id}/reject"
            body:"*"
        };
    }

    // 审核退货单
    rpc ApproveReturn (ApproveReturnRequest) returns (ApproveReturnResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs_store_extra/returns/{id}/approve"
            body:"*"
        };
    }

    // 确认提货
    rpc DeliveryReturn (DeliveryReturnRequest) returns (DeliveryReturnResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs_store_extra/returns/{id}/delivery"
            body:"*"
        };
    }

    // 更新退货单
    // 修改退货数量 - 无保存修改按钮
    // 添加退货商品
    rpc UpdateReturn (UpdateReturnRequest) returns (UpdateReturnResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs_store_extra/returns/{id}/update"
            body:"*"
        };
    }

    // 修改备注
    rpc UpdateRemark (UpdateRemarkRequest) returns (UpdateRemarkResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs_store_extra/returns/{id}/update/remark"
            body:"*"
        };
    }

    // 删除新建的退货单
    rpc DeleteReturn (DeleteReturnRequest) returns (DeleteReturnResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs_store_extra/returns/{id}/delete"
            body:"*"
        };
    }
    // 创建退货单（进行拆单）
    rpc CreateAdjustReturn (CreateReturnRequest) returns (CreateAdjustReturnResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/frs_store_extra/adjust_returns"
            body:"*"
        };
    }

    // 校验按收货原单退货是否超退
    rpc CheckReturnAvailableByrec (CheckReturnAvailableRequest) returns (CommonReply) {
        option (google.api.http) = {
            post:"/api/v2/supply/frs_store_extra/return/check"
            body:"*"
        };
    }

    // 根据id查询单据历史记录
    rpc GetHistoryById(GetHistoryByIdRequest) returns (HistoryResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/frs_store_extra/returns/{id}/history"
        };
    }
}

message ListReturnRequest {
    // 门店id
    repeated uint64 store_ids = 1;
    // 订单状态
    repeated string status = 2;
    // 退货开始日期
    google.protobuf.Timestamp start_date = 3;
    // 退货结束日期
    google.protobuf.Timestamp end_date = 4;
    // 退货单号
    // dq目前没有
    string code = 5;
    // 收货单号
    // dq目前没有
    // string receiving_ids = 10;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 返回总条数
    bool include_total = 8;
    // 是否直送
    bool is_direct = 9;
    // 排序
    string order_by = 10;
    // 单据物流来源类型:BREAD 面包工厂
    string logistics_type = 12;
    // 调整单标志
    bool is_adjust = 13;
    // 排序字段
    string sort = 14;
    // 排序顺序
    string order = 15;
    string storage_type = 17;
    // 来源：原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 18;
    // branch类型： store/ warehouse/ machining
    string sub_type = 19;
    // 收货单号/退货入库单
    string receive_code = 20;
    string lan = 21;
    // 退货开始日期
    google.protobuf.Timestamp return_date_from = 22;
    // 退货结束日期
    google.protobuf.Timestamp return_date_to = 23;
    // 提货开始日期
    google.protobuf.Timestamp delivery_date_from = 24;
    // 提货结束日期
    google.protobuf.Timestamp delivery_date_to = 25;
    // 差异单号
    string return_number = 26;
    // 商品ids
    repeated uint64 product_ids = 27;
     // 退货方id
    repeated uint64 return_by = 28;
    // 退货方接收方
    repeated uint64 return_to = 29;
    // 来源id
    uint64 source_id = 30;
    string source_code = 31;
    string payment_ways = 32;
    // 发货单
    string delivery_code = 33;
}

message ListReturnResponse {

    repeated Returns rows = 1;
    uint64 total = 2;
}

message Returns {
    // id
    uint64 id = 1;
    // 退货单单号
    string code = 2;
    // 退货门店
    uint64 return_by = 3;
    // 退货编号
    string return_number = 4;
    // 退货单状态
    string status = 5;
    // 审核人
    uint64 review_by = 6;
    // 提货状态
    // 原extends
    bool is_returned = 7;
    // 退货提货单单单号
    string return_delivery_number = 8;
    // 退货日期
    google.protobuf.Timestamp return_date = 9;
    // 预计退货提货时间
    google.protobuf.Timestamp return_delivery_date = 10;
    // 退货原因
    string return_reason = 11;
    // 退货方式
    // { '01': '统退', '02': '直退' }
    string type = 12;
    // 退货类型
    // { '01': '自主退货', '02': '召回' }
    string sub_type = 13;
    // 备注
    string remark = 14;
    // 预留门店号
    string store_secondary_id = 15;
    // 创建时间
    google.protobuf.Timestamp created_at = 16;
    // 更新时间
    google.protobuf.Timestamp updated_at = 17;
    // 创建人id
    uint64 created_by = 18;
    // 更新人id
    uint64 updated_by = 19;
    // 退货接受方 门店id
    uint64 return_to = 20;
    // // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 0; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 21;
    // 库存id预留字段
    uint64 inventory_req_id = 22;
    // 商户id
    uint64 partner_id = 23;
    // 收否直送
    bool is_direct = 24;
    // 驳回原因
    string reject_reason = 25;
    // 附件
    repeated Attachments attachments = 26;
    // 创建人
    string created_name = 27;
    // 更新人
    string updated_name = 28;
    // 商品数量
    uint64 product_nums = 29;
    // 单据物流来源类型:BREAD 面包工厂
    string logistics_type = 31;
    // 调整单标志
    bool is_adjust = 32;
    // 对接三方的渠道
    string send_type = 33;
    // 提货方式
    string trans_type = 35;
    string storage_type = 36;
    //
    uint64 receiving_id = 37;
    string receiving_code = 38;
    // 实际提货日期
    google.protobuf.Timestamp delivery_date = 39;
    uint64 request_id = 40;
    // 退款金额
    double refund_amount = 41;
    // 加盟商信息
    uint64 franchisee_id = 55;
    string franchisee_code = 56;
    string franchisee_name = 57;
    // 退款方式
    string payment_way = 58;
    string delivery_code = 59;
    string receive_code = 60;
    // 来源
    uint64 source_id = 61;
    string source_code = 62;
    string return_to_code = 63;
    string return_to_name = 64;
    // 商品的报表信息
    double returns_price = 65;
    string product_code = 66;
    string product_name = 67;
    string unit_spec = 68;
    double price_tax = 69;
    double quantity = 70;
    string return_by_code = 71;
    string return_by_name = 72;
    // 退款单号
    uint64 refund_id = 50;
    string refund_code = 51;
    string warehouse_type = 52;
        // 是否长效
    string long_effect = 53;
}

message CreateReturnRequest {

    // 退货方id
    uint64 return_by = 1;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 2;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD/ FRS 加盟商退货
    string type = 3;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 4;
    // 交货日期
    google.protobuf.Timestamp return_delivery_date = 5;
    // 退货单来源id
    uint64 source_id = 6;
    string source_code = 7;
    // 退货原因
    string return_reason = 8;
    // 备注
    string remark = 9;
    // 附件
    repeated Attachments attachments = 10;
    // 唯一请求号，保证不重复请求
    uint64 request_id = 11;
    // 商品
    repeated Product products = 12;
    // 语言
    string lan = 20;

}

message CreateReturnResponse {
    repeated uint64 return_id = 1;
    bool payload = 2;
}

message Product {
    // 商品表里的id（supply_receiving_diff_product.id)
    uint64 product_id = 1;
    // 订货单位id
    uint64 unit_id = 2;
    // 数量
    double quantity = 3;
    // 收货数量
    double confirmed_quantity = 4;
    //
    string product_code = 5;
    string product_name = 6;
    //
    string unit_name = 7;
    string unit_spec = 8;
    // 单位换算比例
    float unit_rate = 9;
    //
    float tax_rate = 10;
    float price = 11;
    float price_tax = 12;
    uint64 id = 13;
    // 退货接收方
    uint64 return_to = 14;
    //
    string logistics_type = 15;
    // 退款金额
    double refund_amount = 16;
}

message ConfirmReturnRequest {
    // id
    uint64 id = 1;
    string lan = 2;
}

message ConfirmReturnResponse {
    bool payload = 1;
    // wrapper.Status status = 2;
}



message CommonReply {
    bool success = 1;
}

message CheckReturnAvailableRequest {

    // 退货门店
    // = 门店id
    uint64 return_by = 1;
    // 交货日期
    google.protobuf.Timestamp return_delivery_date = 2;
    // 退货类型
    string type = 3;
    // 子类型
    string sub_type = 4;
    // 退货原因
    string return_reason = 5;
    // 备注
    string remark = 6;
    // 商品
    repeated Product products = 7;
    // 是否直送
    bool is_direct = 8;
    // 仓库/配送中心
    uint64 return_to = 9;
    // 附件
    repeated Attachments attachments = 10;
    // 单据物流来源类型:BREAD 面包工厂
    string logistics_type = 11;
    // 调整单标志
    bool is_adjust = 12;
    uint64 receiving_id = 13;
    string lan = 14;
    uint64 return_id = 15;
}

message GetValidProductRequest {
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 商户id
    // uint64 partner_id = 5;
    // 商品名称
    string product_name = 6;
    string lan = 7;

}

message GetValidProductResponse {
    uint32 total = 1;
    repeated ValidProduct rows = 2;
}

message ListReturnColdRequest {
    // 门店id
    repeated uint64 store_ids = 1;
    // 订单状态
    repeated string status = 2;
    // 退货开始日期
    google.protobuf.Timestamp start_date = 3;
    // 退货结束日期
    google.protobuf.Timestamp end_date = 4;
    // 退货单号
    // dq目前没有
    string code = 5;
    // 收货单号
    // dq目前没有
    // string receiving_ids = 10;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 返回总条数
    bool include_total = 8;
    // 是否直送
    bool is_direct = 9;
    // 单据物流来源类型:BREAD 面包工厂
    string logistics_type = 12;
    // 调整单标志
    bool is_adjust = 13;
    // 排序字段
    string sort = 14;
    // 排序顺序
    string order = 15;
    string storage_type = 17;
    string lan = 18;
    // 退货开始日期
    google.protobuf.Timestamp return_date_from = 19;
    // 退货结束日期
    google.protobuf.Timestamp return_date_to = 20;
    // 提货开始日期
    google.protobuf.Timestamp delivery_date_from = 21;
    // 提货结束日期
    google.protobuf.Timestamp delivery_date_to = 22;
}

message ListReturnColdResponse {

    repeated Returns rows = 1;
    uint64 total = 2;
}

message GetReturnByIdRequest {
    // 退货单id
    uint64 id = 1;
    // 是否显示详情
    // 可否删除？
    // bool is_details = 2;
    string lan = 2;
}

message GetReturnByIdResponse {
    Returns return_detail = 1;
}

message GetReturnProductByIdRequest {
    // id
    uint64 id = 1;
    // 商户id
    // uint64 partner_id = 2;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 排序字段
    string sort = 14;
    // 排序顺序
    string order = 15;
    string lan = 16;

}

message GetReturnProductByIdResponse {
    repeated ProductDetail rows = 1;
    uint64 total = 2;
    double total_amount = 3;
}

message SubmitReturnRequest {
    // id
    uint64 id = 1;
    string lan = 2;
}

message SubmitReturnResponse {
    bool payload = 1;
    // wrapper.Status status = 2;
}

message RejectReturnRequest {
    // id
    uint64 id = 1;
    // 驳回原因
    string reject_reason = 2;
    string lan = 3;
}

message RejectReturnResponse {
    bool payload = 1;
    // wrapper.Status status = 2;
}

message ApproveReturnRequest {
    // id
    uint64 id = 1;
    // 提货方式
    string trans_type = 2;
    string lan = 3;
    // 退回仓库类型(Scrap/ Normal)
    string warehouse_type = 4;
        // 是否长效
    string long_effect = 53;
}

message ApproveReturnResponse {
    bool payload = 1;
}

message DeliveryReturnRequest {
    uint64 id = 1;
    string lan = 2;
    // 快递单号
    string fs_code = 3;
}

message DeliveryReturnResponse {
    bool payload = 1;
}

message UpdateReturnRequest {
    //
    uint64 id = 1;
    //
    repeated Product products = 2;
    // 退货原因
    string return_reason = 3;
    // 备注
    string remark = 4;
    // 退货交货时间
    google.protobuf.Timestamp return_delivery_date = 5;
    // 供应商/配送中心
    uint64 return_to = 6;
    // 附件
    repeated Attachments attachments = 7;
    string lan = 8;
    string logistics_type = 9;
}

message UpdateReturnResponse {
    bool payload = 1;
    // wrapper.Status status = 2;
}

message UpdateRemarkRequest {
    // id
    uint64 id = 1;
    // remark
    string remark = 2;
    // user_id
    // uint64 user_id = 3;
    string lan = 3;
}

message UpdateRemarkResponse {
    bool payload = 1;
    // wrapper.Status status = 2;
}

message DeleteReturnRequest {
    //
    uint64 id = 1;
    string lan = 2;
}

message DeleteReturnResponse {
    bool payload = 1;
}

message ValidProduct {
    // id
    uint64 product_id = 1;
    // 商品编号
    string product_code = 2;
    // 商品名称
    string product_name = 3;
    // 规格
    string spec = 4;
    // 单位
    Unit unit = 5;
}

message Unit {
    // id
    uint64 id = 1;
    // 单位数量
    int32 quantity = 2;
    //
    double rate = 3;
}

message ProductDetail {
    // id
    uint64 id = 1;
    // 退货单id
    uint64 return_id = 2;
    // 退货门店
    uint64 return_by = 3;

    // 物料编码
    string material_number = 4;
    // 商品编码
    string product_code = 5;
    // 商品id
    uint64 product_id = 6;
    // 商品名称
    string product_name = 7;
    // 数量
    double quantity = 8;

    // 单位id
    uint64 accounting_unit_id = 9;
    // 单位名称
    string accounting_unit_name = 10;
    // 单位规格
    string accounting_unit_spec = 11;
    // 单位id
    uint64 unit_id = 12;
    // 单位名称
    string unit_name = 13;
    // 单位换算比例
    double unit_rate = 14;
    // 单位规格
    string unit_spec = 15;
    // 确认退货数量
    double accounting_confirmed_quantity = 16;
    //
    double accounting_quantity = 17;
    //
    double accounting_returned_quantity = 18;
    // 确认收货数量
    double confirmed_quantity = 19;
    // 退货数量
    double returned_quantity = 20;

    // 状态
    bool is_confirmed = 21;
    // 退货日期
    google.protobuf.Timestamp return_date = 22;
    // 接收方
    uint64 return_to = 23;
    // 订货日期
    // google.protobuf.Timestamp demand_date = 23;

    // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 1; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 24;
    // 库存id预留字段
    uint64 inventory_req_id = 25;
    // 创建时间
    google.protobuf.Timestamp created_at = 26;
    // 创建人id
    uint64 created_by = 27;
    // 更新日期
    google.protobuf.Timestamp updated_at = 28;
    // 更新人id
    uint64 updated_by = 29;
    // 商户id
    uint64 partner_id = 30;
    // 创建人
    string created_name = 31;
    // 更新人
    string updated_name = 32;
    string storage_type = 35;
    // 未税单价
    double price = 36;
    // 税率
    float tax_rate = 37;
    // 含税单价
    double price_tax = 38;
    // 采购总价
    double sum_price = 39;
    // 税额
    double tax_amount = 40;
    // 退货金额
    double return_price = 41;
    repeated Attachments attachments = 42;
    // 零售价
    double retail_price = 43;
}
message CreateAdjustReturnResponse{
    repeated uint64 return_ids = 1;
}

message GetHistoryByIdRequest{
    // 退货单id
    uint64 id = 1;
}
message HistoryResponse{
    repeated History rows = 1;
    uint64 total = 2;
}

message History{
    // 历史记录id
    uint64 id = 1;
    // 退货单状态
    string status = 2;
    // 操作人名称或者系统自动操作
    uint64 updated_by = 3;
    // 操作时间
    google.protobuf.Timestamp updated_at = 4;
    string updated_by_name = 5;
}

message Attachments {
    string type = 1;
    string url = 2;
}