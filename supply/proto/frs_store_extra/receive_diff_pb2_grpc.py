# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from frs_store_extra import receive_diff_pb2 as frs__store__extra_dot_receive__diff__pb2


class FrsStoreExtraReceiveDiffServiceStub(object):
  """ReceivingDiffService 收货差异相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListReceiveDiff = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/ListReceiveDiff',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.ListReceiveDiffRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.ListReceiveDiffResponse.FromString,
        )
    self.GetReceiveDiffById = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/GetReceiveDiffById',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.GetReceiveDiffByIdRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.ReceiveDiff.FromString,
        )
    self.GetReceiveDiffProductById = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/GetReceiveDiffProductById',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.GetReceiveDiffProductByIdRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.GetReceiveDiffProductByIdResponse.FromString,
        )
    self.SubmitReceiveDiff = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/SubmitReceiveDiff',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.SubmitReceiveDiffRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.SubmitReceiveDiffResponse.FromString,
        )
    self.ConfirmReceiveDiff = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/ConfirmReceiveDiff',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.ConfirmReceiveDiffRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.ConfirmReceiveDiffResponse.FromString,
        )
    self.RejectReceiveDiff = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/RejectReceiveDiff',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.RejectReceiveDiffRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.RejectReceiveDiffResponse.FromString,
        )
    self.UpdateReceiveDiff = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/UpdateReceiveDiff',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.UpdateReceiveDiffRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.UpdateReceiveDiffResponse.FromString,
        )
    self.DeleteReceiveDiff = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/DeleteReceiveDiff',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.DeleteReceiveDiffRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.DeleteReceiveDiffResponse.FromString,
        )
    self.GetHistoryById = channel.unary_unary(
        '/frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService/GetHistoryById',
        request_serializer=frs__store__extra_dot_receive__diff__pb2.GetHistoryByIdRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_receive__diff__pb2.HistoryResponse.FromString,
        )


class FrsStoreExtraReceiveDiffServiceServicer(object):
  """ReceivingDiffService 收货差异相关服务
  """

  def ListReceiveDiff(self, request, context):
    """查询收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveDiffById(self, request, context):
    """根据id查询收货差异单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveDiffProductById(self, request, context):
    """根据id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitReceiveDiff(self, request, context):
    """提交收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmReceiveDiff(self, request, context):
    """确认收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectReceiveDiff(self, request, context):
    """驳回收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReceiveDiff(self, request, context):
    """更新收货单差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteReceiveDiff(self, request, context):
    """删除收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHistoryById(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FrsStoreExtraReceiveDiffServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceiveDiff,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.ListReceiveDiffRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.ListReceiveDiffResponse.SerializeToString,
      ),
      'GetReceiveDiffById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffById,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.GetReceiveDiffByIdRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.ReceiveDiff.SerializeToString,
      ),
      'GetReceiveDiffProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffProductById,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.GetReceiveDiffProductByIdRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.GetReceiveDiffProductByIdResponse.SerializeToString,
      ),
      'SubmitReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitReceiveDiff,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.SubmitReceiveDiffRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.SubmitReceiveDiffResponse.SerializeToString,
      ),
      'ConfirmReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmReceiveDiff,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.ConfirmReceiveDiffRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.ConfirmReceiveDiffResponse.SerializeToString,
      ),
      'RejectReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.RejectReceiveDiff,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.RejectReceiveDiffRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.RejectReceiveDiffResponse.SerializeToString,
      ),
      'UpdateReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReceiveDiff,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.UpdateReceiveDiffRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.UpdateReceiveDiffResponse.SerializeToString,
      ),
      'DeleteReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteReceiveDiff,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.DeleteReceiveDiffRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.DeleteReceiveDiffResponse.SerializeToString,
      ),
      'GetHistoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetHistoryById,
          request_deserializer=frs__store__extra_dot_receive__diff__pb2.GetHistoryByIdRequest.FromString,
          response_serializer=frs__store__extra_dot_receive__diff__pb2.HistoryResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'frs_store_extra_receive_diff.FrsStoreExtraReceiveDiffService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
