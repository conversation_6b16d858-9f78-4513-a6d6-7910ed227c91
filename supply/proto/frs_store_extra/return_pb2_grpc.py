# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from frs_store_extra import return_pb2 as frs__store__extra_dot_return__pb2


class FrsStoreExtraReturnServiceStub(object):
  """ReturnService 退货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/CreateReturn',
        request_serializer=frs__store__extra_dot_return__pb2.CreateReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.CreateReturnResponse.FromString,
        )
    self.ConfirmReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/ConfirmReturn',
        request_serializer=frs__store__extra_dot_return__pb2.ConfirmReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.ConfirmReturnResponse.FromString,
        )
    self.ListReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/ListReturn',
        request_serializer=frs__store__extra_dot_return__pb2.ListReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.ListReturnResponse.FromString,
        )
    self.GetValidProduct = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/GetValidProduct',
        request_serializer=frs__store__extra_dot_return__pb2.GetValidProductRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.GetValidProductResponse.FromString,
        )
    self.ListReturnCold = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/ListReturnCold',
        request_serializer=frs__store__extra_dot_return__pb2.ListReturnColdRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.ListReturnResponse.FromString,
        )
    self.GetReturnById = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/GetReturnById',
        request_serializer=frs__store__extra_dot_return__pb2.GetReturnByIdRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.Returns.FromString,
        )
    self.GetReturnProductById = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/GetReturnProductById',
        request_serializer=frs__store__extra_dot_return__pb2.GetReturnProductByIdRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.GetReturnProductByIdResponse.FromString,
        )
    self.SubmitReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/SubmitReturn',
        request_serializer=frs__store__extra_dot_return__pb2.SubmitReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.SubmitReturnResponse.FromString,
        )
    self.RejectReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/RejectReturn',
        request_serializer=frs__store__extra_dot_return__pb2.RejectReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.RejectReturnResponse.FromString,
        )
    self.ApproveReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/ApproveReturn',
        request_serializer=frs__store__extra_dot_return__pb2.ApproveReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.ApproveReturnResponse.FromString,
        )
    self.DeliveryReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/DeliveryReturn',
        request_serializer=frs__store__extra_dot_return__pb2.DeliveryReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.DeliveryReturnResponse.FromString,
        )
    self.UpdateReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/UpdateReturn',
        request_serializer=frs__store__extra_dot_return__pb2.UpdateReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.UpdateReturnResponse.FromString,
        )
    self.UpdateRemark = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/UpdateRemark',
        request_serializer=frs__store__extra_dot_return__pb2.UpdateRemarkRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.UpdateRemarkResponse.FromString,
        )
    self.DeleteReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/DeleteReturn',
        request_serializer=frs__store__extra_dot_return__pb2.DeleteReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.DeleteReturnResponse.FromString,
        )
    self.CreateAdjustReturn = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/CreateAdjustReturn',
        request_serializer=frs__store__extra_dot_return__pb2.CreateReturnRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.CreateAdjustReturnResponse.FromString,
        )
    self.CheckReturnAvailableByrec = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/CheckReturnAvailableByrec',
        request_serializer=frs__store__extra_dot_return__pb2.CheckReturnAvailableRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.CommonReply.FromString,
        )
    self.GetHistoryById = channel.unary_unary(
        '/frs_store_extra_return.FrsStoreExtraReturnService/GetHistoryById',
        request_serializer=frs__store__extra_dot_return__pb2.GetHistoryByIdRequest.SerializeToString,
        response_deserializer=frs__store__extra_dot_return__pb2.HistoryResponse.FromString,
        )


class FrsStoreExtraReturnServiceServicer(object):
  """ReturnService 退货相关服务
  """

  def CreateReturn(self, request, context):
    """创建退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmReturn(self, request, context):
    """确认退货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReturn(self, request, context):
    """查询退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetValidProduct(self, request, context):
    """查询可退货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReturnCold(self, request, context):
    """查询退货单--冷链
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnById(self, request, context):
    """根据id查询退货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnProductById(self, request, context):
    """根据id查询退货单商品详情
    查询退货单详情时需调用
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitReturn(self, request, context):
    """提交退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectReturn(self, request, context):
    """驳回退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveReturn(self, request, context):
    """审核退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeliveryReturn(self, request, context):
    """确认提货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReturn(self, request, context):
    """更新退货单
    修改退货数量 - 无保存修改按钮
    添加退货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateRemark(self, request, context):
    """修改备注
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteReturn(self, request, context):
    """删除新建的退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateAdjustReturn(self, request, context):
    """创建退货单（进行拆单）
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckReturnAvailableByrec(self, request, context):
    """校验按收货原单退货是否超退
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHistoryById(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FrsStoreExtraReturnServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateReturn': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.CreateReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.CreateReturnResponse.SerializeToString,
      ),
      'ConfirmReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.ConfirmReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.ConfirmReturnResponse.SerializeToString,
      ),
      'ListReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ListReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.ListReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.ListReturnResponse.SerializeToString,
      ),
      'GetValidProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidProduct,
          request_deserializer=frs__store__extra_dot_return__pb2.GetValidProductRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.GetValidProductResponse.SerializeToString,
      ),
      'ListReturnCold': grpc.unary_unary_rpc_method_handler(
          servicer.ListReturnCold,
          request_deserializer=frs__store__extra_dot_return__pb2.ListReturnColdRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.ListReturnResponse.SerializeToString,
      ),
      'GetReturnById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnById,
          request_deserializer=frs__store__extra_dot_return__pb2.GetReturnByIdRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.Returns.SerializeToString,
      ),
      'GetReturnProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnProductById,
          request_deserializer=frs__store__extra_dot_return__pb2.GetReturnProductByIdRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.GetReturnProductByIdResponse.SerializeToString,
      ),
      'SubmitReturn': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.SubmitReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.SubmitReturnResponse.SerializeToString,
      ),
      'RejectReturn': grpc.unary_unary_rpc_method_handler(
          servicer.RejectReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.RejectReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.RejectReturnResponse.SerializeToString,
      ),
      'ApproveReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.ApproveReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.ApproveReturnResponse.SerializeToString,
      ),
      'DeliveryReturn': grpc.unary_unary_rpc_method_handler(
          servicer.DeliveryReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.DeliveryReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.DeliveryReturnResponse.SerializeToString,
      ),
      'UpdateReturn': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.UpdateReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.UpdateReturnResponse.SerializeToString,
      ),
      'UpdateRemark': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateRemark,
          request_deserializer=frs__store__extra_dot_return__pb2.UpdateRemarkRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.UpdateRemarkResponse.SerializeToString,
      ),
      'DeleteReturn': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.DeleteReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.DeleteReturnResponse.SerializeToString,
      ),
      'CreateAdjustReturn': grpc.unary_unary_rpc_method_handler(
          servicer.CreateAdjustReturn,
          request_deserializer=frs__store__extra_dot_return__pb2.CreateReturnRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.CreateAdjustReturnResponse.SerializeToString,
      ),
      'CheckReturnAvailableByrec': grpc.unary_unary_rpc_method_handler(
          servicer.CheckReturnAvailableByrec,
          request_deserializer=frs__store__extra_dot_return__pb2.CheckReturnAvailableRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.CommonReply.SerializeToString,
      ),
      'GetHistoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetHistoryById,
          request_deserializer=frs__store__extra_dot_return__pb2.GetHistoryByIdRequest.FromString,
          response_serializer=frs__store__extra_dot_return__pb2.HistoryResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'frs_store_extra_return.FrsStoreExtraReturnService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
