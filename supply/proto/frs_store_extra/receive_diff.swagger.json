{"swagger": "2.0", "info": {"title": "frs_store_extra/receive_diff.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/frs_store_extra/receive-diff": {"get": {"summary": "查询收货差异单", "operationId": "ListReceiveDiff", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffListReceiveDiffResponse"}}}, "parameters": [{"name": "store_ids", "description": "门店id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "status", "description": "订单状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "start_date", "description": "配送开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "配送结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_direct", "description": "是否直送.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "receiving_ids", "description": "收货单id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "receiving_code", "description": "收货单单号.", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "description": "商品ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "code", "description": "差异单单号.", "in": "query", "required": false, "type": "string"}, {"name": "logistics_type", "description": "单据物流来源类型:BREAD 面包工厂.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "return_date_from", "description": "退货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "return_date_to", "description": "退货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_from", "description": "提货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_to", "description": "提货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "receive_date_from", "description": "收货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "receive_date_to", "description": "收货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "demand_date_from", "description": "订货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "demand_date_to", "description": "订货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "delivery_bys", "description": "配送方id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "branch_type", "description": "收货差异单的主档类型.", "in": "query", "required": false, "type": "string"}], "tags": ["FrsStoreExtraReceiveDiffService"]}}, "/api/v2/supply/frs_store_extra/receive-diff/{id}": {"get": {"summary": "根据id查询收货差异单详情", "operationId": "GetReceiveDiffById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffReceiveDiff"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FrsStoreExtraReceiveDiffService"]}}, "/api/v2/supply/frs_store_extra/receive-diff/{id}/confirm": {"post": {"summary": "确认收货差异单", "operationId": "ConfirmReceiveDiff", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffConfirmReceiveDiffResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffConfirmReceiveDiffRequest"}}], "tags": ["FrsStoreExtraReceiveDiffService"]}}, "/api/v2/supply/frs_store_extra/receive-diff/{id}/delete": {"post": {"summary": "删除收货差异单", "operationId": "DeleteReceiveDiff", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffDeleteReceiveDiffResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffDeleteReceiveDiffRequest"}}], "tags": ["FrsStoreExtraReceiveDiffService"]}}, "/api/v2/supply/frs_store_extra/receive-diff/{id}/history": {"get": {"summary": "根据id查询单据历史记录", "operationId": "GetHistoryById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffHistoryResponse"}}}, "parameters": [{"name": "id", "description": "差异单id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["FrsStoreExtraReceiveDiffService"]}}, "/api/v2/supply/frs_store_extra/receive-diff/{id}/product": {"get": {"summary": "根据id查商品详情", "operationId": "GetReceiveDiffProductById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffGetReceiveDiffProductByIdResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FrsStoreExtraReceiveDiffService"]}}, "/api/v2/supply/frs_store_extra/receive-diff/{id}/reject": {"post": {"summary": "驳回收货差异单", "operationId": "RejectReceiveDiff", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffRejectReceiveDiffResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffRejectReceiveDiffRequest"}}], "tags": ["FrsStoreExtraReceiveDiffService"]}}, "/api/v2/supply/frs_store_extra/receive-diff/{id}/submit": {"post": {"summary": "提交收货差异单", "operationId": "SubmitReceiveDiff", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffSubmitReceiveDiffResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffSubmitReceiveDiffRequest"}}], "tags": ["FrsStoreExtraReceiveDiffService"]}}, "/api/v2/supply/frs_store_extra/receive-diff/{id}/update": {"post": {"summary": "更新收货单差异单", "operationId": "UpdateReceiveDiff", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffUpdateReceiveDiffResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frs_store_extra_receive_diffUpdateReceiveDiffRequest"}}], "tags": ["FrsStoreExtraReceiveDiffService"]}}}, "definitions": {"frs_store_extra_receive_diffConfirmReceiveDiffRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "lan": {"type": "string"}}}, "frs_store_extra_receive_diffConfirmReceiveDiffResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_receive_diffDeleteReceiveDiffRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "lan": {"type": "string"}}}, "frs_store_extra_receive_diffDeleteReceiveDiffResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_receive_diffGetReceiveDiffProductByIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_receive_diffReceiveDiffProduct"}}, "total": {"type": "string", "format": "uint64"}}}, "frs_store_extra_receive_diffHistory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "历史记录id"}, "status": {"type": "string", "title": "差异单状态"}, "updated_by": {"type": "string", "format": "uint64", "title": "操作人名称或者系统自动操作"}, "updated_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "updated_by_name": {"type": "string"}}}, "frs_store_extra_receive_diffHistoryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_receive_diffHistory"}}, "total": {"type": "string", "format": "uint64"}}}, "frs_store_extra_receive_diffListReceiveDiffResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_receive_diffReceiveDiff"}}, "total": {"type": "string", "format": "uint64"}}}, "frs_store_extra_receive_diffProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "s_diff_quantity": {"type": "number", "format": "double", "title": "门店承担数量"}, "d_diff_quantity": {"type": "number", "format": "double", "title": "配送费承担数量"}, "reason_type": {"type": "string", "title": "差异原因"}, "remark": {"type": "string", "title": "备注"}}}, "frs_store_extra_receive_diffReceiveDiff": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "code": {"type": "string", "title": "差异单单号"}, "received_by": {"type": "string", "format": "uint64", "title": "接收门店 - store_id"}, "receiving_id": {"type": "string", "format": "uint64", "title": "收货单id"}, "receiving_code": {"type": "string", "title": "收货单单号"}, "master_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "master_code": {"type": "string", "title": "订货单单号\n原order_id"}, "status": {"type": "string", "title": "收货差异单单状态\nenum Status {\n    INITED = 0;\n    SUBMITTED = 1;\n    CONFIRMED = 2;\n    REJECTED = 3;\n}"}, "demand_date": {"type": "string", "format": "date-time", "title": "订单时间"}, "delivery_note_number": {"type": "string", "title": "配送单号"}, "delivery_by": {"type": "string", "format": "uint64", "title": "配送方"}, "delivery_date": {"type": "string", "format": "date-time", "title": "配送时间=实际提货时间"}, "reason_type": {"type": "string", "title": "差异原因类型"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人"}, "remark": {"type": "string", "title": "备注"}, "store_secondary_id": {"type": "string", "title": "预留门店id"}, "inventory_status": {"type": "string", "title": "// 库存引擎调用\nenum inventoryStatus {\n    SENT = 0; // 已发送\n    SENT_SUC = 2; //调用成功\n    SENT_FAIL = 3; //调用失败\n}"}, "inventory_req_id": {"type": "string", "format": "uint64", "title": "库存id预留字段"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "is_direct": {"type": "boolean", "format": "boolean", "title": "是否直送"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "附件"}, "product_nums": {"type": "string", "format": "uint64", "title": "商品数量"}, "received_type": {"type": "string", "title": "单据类型"}, "request_id": {"type": "string", "format": "uint64", "title": "请求id"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:BREAD 面包工厂"}, "send_type": {"type": "string", "title": "对接三方的渠道"}, "pre_delivery_date": {"type": "string", "format": "date-time", "title": "预计退货提货时间"}, "return_id": {"type": "string", "format": "uint64"}, "send_date": {"type": "string", "format": "date-time", "title": "配送时间"}, "branch_type": {"type": "string"}, "sub_receive_by": {"type": "string", "format": "uint64"}, "delivery_by_name": {"type": "string", "title": "配送方名称"}, "receive_date": {"type": "string", "format": "date-time", "title": "收货日期"}, "auto_confirm_date": {"type": "string", "format": "date-time", "title": "自动确认时间"}, "product_names_brief": {"type": "array", "items": {"type": "string"}, "title": "最多五个商品名称预览"}, "delivery_by_code": {"type": "string", "title": "配送方编号"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商id"}}}, "frs_store_extra_receive_diffReceiveDiffProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "receiving_id": {"type": "string", "format": "uint64", "title": "收货单id"}, "received_by": {"type": "string", "format": "uint64", "title": "收货门店"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_id": {"type": "string", "format": "uint64", "title": "主单位id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "material_number": {"type": "string", "title": "物料编码"}, "accounting_unit_name": {"type": "string", "title": "单位名称"}, "accounting_unit_spec": {"type": "string", "title": "单位规格"}, "unit_name": {"type": "string", "title": "核算单位名称"}, "unit_spec": {"type": "string", "title": "核算单位规格"}, "unit_rate": {"type": "number", "format": "double", "title": "转换比率"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "received_accounting_quantity": {"type": "number", "format": "double", "title": "配送数量"}, "received_quantity": {"type": "number", "format": "double", "title": "核算配送数量"}, "confirmed_accounting_quantity": {"type": "number", "format": "double", "title": "确认数量"}, "confirmed_quantity": {"type": "number", "format": "double", "title": "核算确认数量"}, "diff_accounting_quantity": {"type": "number", "format": "double", "title": "差异数量"}, "diff_quantity": {"type": "number", "format": "double", "title": "核算差异数量"}, "s_diff_accounting_quantity": {"type": "number", "format": "double", "title": "门店承担退货数量"}, "s_diff_quantity": {"type": "number", "format": "double"}, "d_diff_accounting_quantity": {"type": "number", "format": "double", "title": "第三方承担数量"}, "d_diff_quantity": {"type": "number", "format": "double"}, "demand_date": {"type": "string", "format": "date-time", "title": "订单日期"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新日期"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "diff_id": {"type": "string", "format": "uint64", "title": "差异单id"}, "reason_type": {"type": "string", "title": "差异原因"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "attachments": {"type": "string", "title": "附件"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "remark": {"type": "string", "title": "备注"}, "cost_price": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "tax_rate": {"type": "number", "format": "double"}, "sub_receive_by": {"type": "string", "format": "uint64"}, "spec": {"type": "string", "title": "规格"}}}, "frs_store_extra_receive_diffRejectReceiveDiffRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "reject_reason": {"type": "string"}, "attachments": {"type": "string"}, "lan": {"type": "string"}}}, "frs_store_extra_receive_diffRejectReceiveDiffResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_receive_diffSubmitReceiveDiffRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "lan": {"type": "string"}}}, "frs_store_extra_receive_diffSubmitReceiveDiffResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean"}}}, "frs_store_extra_receive_diffUpdateReceiveDiffRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/frs_store_extra_receive_diffProduct"}}, "attachments": {"type": "array", "items": {"type": "string"}}, "lan": {"type": "string"}, "remark": {"type": "string"}, "reason": {"type": "string"}}}, "frs_store_extra_receive_diffUpdateReceiveDiffResponse": {"type": "object", "properties": {"payload": {"type": "boolean", "format": "boolean", "title": "wrapper.Status status = 1;"}}}}}