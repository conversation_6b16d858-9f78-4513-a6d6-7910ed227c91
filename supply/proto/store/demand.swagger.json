{"swagger": "2.0", "info": {"title": "store/demand.proto", "version": "version not set"}, "tags": [{"name": "StoreDemandService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/meta/close_order/set": {"put": {"operationId": "StoreDemandService_SetCloseOrderData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeSetCloseOrderDataRequest"}}], "tags": ["StoreDemandService"]}}, "/api/v2/supply/meta/close_order/switch": {"put": {"operationId": "StoreDemandService_SwitchCloseOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeSwitchCloseOrderRequest"}}], "tags": ["StoreDemandService"]}}, "/api/v2/supply/store/demand": {"get": {"summary": "查询门店订货单", "operationId": "StoreDemandService_ListDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeQueryDemandResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_ids", "description": "门店ids（,分割）", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "has_product", "description": "是否包含商品('0'是不含，'1'是含, 不传查询所有, 主意这里是字符0, 1)", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "门市订货(SD)、紧急订货(HD)、主配订货(MD)", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)", "in": "query", "required": false, "type": "string"}, {"name": "store_type", "description": "门店类型(热麦:MIX, 如果要查询非的字段使用neq_开头，例如:[\"neq_MIX\", \"TEA\"], 不传查询所有)", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "start_date", "description": "订货日期起始时间", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "订货日期结束时间", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "description": "订单状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "product_ids", "description": "商品ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "offset", "description": "偏移(默认0)", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "description": "查询的每页数量", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "codes", "description": "订货单号", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "is_plan", "in": "query", "required": false, "type": "boolean"}, {"name": "is_adjust", "in": "query", "required": false, "type": "boolean"}, {"name": "send_type", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc)", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "havi_code", "in": "query", "required": false, "type": "string"}, {"name": "pre_havi_code", "in": "query", "required": false, "type": "string"}, {"name": "bus_type", "in": "query", "required": false, "type": "string"}, {"name": "plan_name", "description": "计划名称模糊查询", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "types", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "tags": ["StoreDemandService"]}}, "/api/v2/supply/store/demand/product": {"put": {"summary": "更新门店订货商品", "operationId": "StoreDemandService_UpdateDemandProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeUpdateDemandProductRequest"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeUpdateDemandProductRequest"}}], "tags": ["StoreDemandService"]}}, "/api/v2/supply/store/demand/{id}": {"get": {"summary": "获取订货单详细", "operationId": "StoreDemandService_GetDemandDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeDemandEntity"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["StoreDemandService"]}, "post": {"summary": "更新订货单信息", "operationId": "StoreDemandService_UpdateDemandInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"remark": {"type": "string", "title": "备注"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期(只有主配单和紧急订货单才有的参数，门市订货单请不要传)"}, "attachments": {"type": "string"}, "expect_time": {"type": "string", "format": "date-time", "title": "期待到货日期"}}, "title": "更新订货单信息参数"}}], "tags": ["StoreDemandService"]}}, "/api/v2/supply/store/demand/{id}/product": {"get": {"summary": "获取订货商品信息", "operationId": "StoreDemandService_GetDemandProductDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeQueryDemandProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "include_inv", "description": "是否返回实时库存", "in": "query", "required": false, "type": "string"}, {"name": "include_unfinish", "description": "是否返回待收货数量", "in": "query", "required": false, "type": "string"}], "tags": ["StoreDemandService"]}}, "/api/v2/supply/store/demand/{id}/{action}": {"put": {"summary": "修改订单状态统一入口", "operationId": "StoreDemandService_DealDemandById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "action", "description": "单据业务状态(INITED,F_COMMIT(成品订货提交),COMMIT(提交), CAL_DONE(原料计算完成, 这步不能通过前端修改), CAL_FAILED(原料计算失败，这步也不能通过前端修改),REJECTED(驳回订货单),APPROVED(审核订货单通过),FREEZE(冻结订货单),CANCELLED", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"description": {"type": "string"}, "attachments": {"type": "string", "title": "附件"}}, "title": "处理单据"}}], "tags": ["StoreDemandService"]}}}, "definitions": {"UpdateDemandProductRequestproducts": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "订货记录id(如果有此id则要传此id)"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "tag_type": {"type": "string", "title": "订货热麦类型(\"FINISHED\",\"成品订货\"),(\"TEA\", \"茶饮订货\"),(\"BREAD\", \"面包订货\")(\"RAW\", \"原料订货\")"}, "distribution_type": {"type": "string", "title": "商品订货物流模式(配送:NMD, 直送:PUR)， 成品订货不用传"}, "distribution_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心(不修改就不需要传)"}, "unit_id": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "vendor_id": {"type": "string", "format": "uint64", "title": "供应商id"}, "order_type": {"type": "string", "title": "订货方式"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "storeDemandEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "单据id"}, "batch_id": {"type": "string", "format": "uint64", "title": "生成的批次id"}, "code": {"type": "string", "title": "编码"}, "type": {"type": "string", "title": "门市订货(SD)、紧急订货(HD)、主配订货(MD)"}, "reason_type": {"type": "string", "title": "原因类型"}, "receive_by": {"type": "string", "format": "uint64", "title": "收获门店id"}, "receive_name": {"type": "string", "title": "收获门店名称"}, "distribute_by": {"type": "string", "format": "uint64", "title": "供应商id"}, "store_secondary_id": {"type": "string", "title": "门店编码"}, "store_type": {"type": "string", "title": "门店类型"}, "distribution_type": {"type": "string", "title": "供应商类别"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人id"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "description": {"type": "string", "title": "描述"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "sub_type": {"type": "string", "title": "主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)"}, "remark": {"type": "string", "title": "备注"}, "has_product": {"type": "integer", "format": "int32", "title": "是否有商品"}, "is_sys": {"type": "integer", "format": "int32", "title": "是否是系统操作(1是，0不是)"}, "status": {"type": "string", "title": "单据业务状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)"}, "process_status": {"type": "string", "title": "生成要货单处理状态(INITED,PROCESSING,SUCCESS,FAILED)"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "product_count": {"type": "integer", "format": "int32", "title": "订货品相"}, "is_plan": {"type": "boolean"}, "is_adjust": {"type": "boolean"}, "send_type": {"type": "string"}, "havi_code": {"type": "string"}, "pre_havi_code": {"type": "string"}, "bus_type": {"type": "string"}, "attachments": {"type": "string"}, "plan_method": {"type": "string", "title": "计划相关"}, "plan_name": {"type": "string"}, "expect_time": {"type": "string", "title": "期待到货日期"}}}, "storeQueryDemandProductEntity": {"type": "object", "properties": {"arrival_days": {"type": "integer", "format": "int32", "title": "到货天数"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心"}, "distribution_type": {"type": "string", "title": "配送(NMD)、采购(PUR)"}, "increment_quantity": {"type": "number", "format": "float", "title": "递增订量"}, "max_quantity": {"type": "number", "format": "float", "title": "最大订货量"}, "min_quantity": {"type": "number", "format": "float", "title": "最小订货量"}, "product_category_id": {"type": "string", "format": "uint64", "title": "商品类别id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "spec": {"type": "string", "title": "规格"}, "storage_type": {"type": "string", "title": "商品储藏类型编码"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_name": {"type": "string", "title": "订货单位名称"}, "unit_spec": {"type": "string", "title": "订货规格"}, "sale_type": {"type": "string", "title": "销售类型"}, "product_type": {"type": "string", "title": "商品属性"}, "suggest_quantity": {"type": "number", "format": "float", "title": "建议订货量"}, "finished_quantity": {"type": "number", "format": "float", "title": "成品订货量"}, "bread_quantity": {"type": "number", "format": "float", "title": "热麦订货量"}, "tea_quantity": {"type": "number", "format": "float", "title": "茶饮订货量"}, "raw_quantity": {"type": "number", "format": "float", "title": "原料订货量"}, "purchase_price": {"type": "number", "format": "float", "title": "直采价格"}, "purchase_tax": {"type": "number", "format": "float", "title": "直采税率"}, "id": {"type": "string", "format": "uint64", "title": "订货记录id(如果有此id则要传此id)"}, "distribution_circle": {"type": "string", "title": "配送周期类型"}, "distribution_center_id": {"type": "string", "format": "uint64", "title": "配送中心"}, "vendor_id": {"type": "string", "format": "uint64", "title": "供应商"}, "cycle_extends": {"type": "string"}, "is_first_delivery": {"type": "string"}, "yesterday_sales": {"type": "number", "format": "double", "title": "昨日销售"}, "average_week_sales": {"type": "number", "format": "double", "title": "一周平均销量"}, "yesterday_order_qty": {"type": "number", "format": "double", "title": "昨日订货量"}, "unit_rate": {"type": "number", "format": "double", "title": "单位转换率"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算订货单位id"}, "accounting_unit_name": {"type": "string", "title": "核算订货单位名称"}, "accounting_unit_spec": {"type": "string", "title": "核算订货规格"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算订货数量"}, "has_new_suggest_qty": {"type": "boolean"}, "barcode": {"type": "array", "items": {"type": "string"}}, "store_unfinish_qty": {"type": "number", "format": "double", "title": "门店待收货数量"}, "store_inv_qty": {"type": "number", "format": "double", "title": "门店实时库存"}, "partner_id": {"type": "string", "format": "uint64"}}, "title": "可订货商品实体"}, "storeQueryDemandProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeQueryDemandProductEntity"}}, "total": {"type": "integer", "format": "int32"}}, "title": "获取订货商品返回参数"}, "storeQueryDemandResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeDemandEntity"}}, "total": {"type": "string", "format": "uint64"}}, "title": "查询门店订货单"}, "storeResponse": {"type": "object", "properties": {"description": {"type": "string"}}, "title": "统一返回对象"}, "storeSetCloseOrderDataRequest": {"type": "object", "properties": {"product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品id"}, "close_order_start_time": {"type": "string", "format": "date-time", "title": "关闭订货开始时间"}, "close_order_end_time": {"type": "string", "format": "date-time", "title": "关闭订货结束时间"}, "close_order_stores": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "关闭订货的门店"}, "close_order_companies": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "关闭订货的公司"}, "close_order_factories": {"type": "array", "items": {"type": "string"}, "title": "关闭订货的工厂"}, "close_order_branch_regions": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "关闭订货的管理区域"}, "status": {"type": "boolean", "title": "关闭订货的状态"}}}, "storeSwitchCloseOrderRequest": {"type": "object", "properties": {"product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品id"}, "status": {"type": "boolean", "title": "关闭订货的状态"}}}, "storeUpdateDemandProductRequest": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "主配单id"}, "product": {"type": "array", "items": {"$ref": "#/definitions/UpdateDemandProductRequestproducts"}, "title": "更新的商品信息"}, "order_date": {"type": "string", "format": "date-time", "title": "订货日期(不传默认当天)"}, "attachments": {"type": "string", "title": "附件"}}, "title": "更新订货商品参数"}}}