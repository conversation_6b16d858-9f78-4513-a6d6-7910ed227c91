# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from store import self_picking_pb2 as store_dot_self__picking__pb2


class StoreSelfPickingStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateStoreSelfPicking = channel.unary_unary(
        '/self_picking.StoreSelfPicking/CreateStoreSelfPicking',
        request_serializer=store_dot_self__picking__pb2.CreateStoreSelfPickingRequest.SerializeToString,
        response_deserializer=store_dot_self__picking__pb2.CreateStoreSelfPickingResponse.FromString,
        )
    self.ListStoreSelfPicking = channel.unary_unary(
        '/self_picking.StoreSelfPicking/ListStoreSelfPicking',
        request_serializer=store_dot_self__picking__pb2.ListStoreSelfPickingRequest.SerializeToString,
        response_deserializer=store_dot_self__picking__pb2.ListStoreSelfPickingResponse.FromString,
        )
    self.GetStoreSelfPickingDetail = channel.unary_unary(
        '/self_picking.StoreSelfPicking/GetStoreSelfPickingDetail',
        request_serializer=store_dot_self__picking__pb2.GetStoreSelfPickingDetailRequest.SerializeToString,
        response_deserializer=store_dot_self__picking__pb2.GetStoreSelfPickingDetailResponse.FromString,
        )
    self.UpdateStoreSelfPicking = channel.unary_unary(
        '/self_picking.StoreSelfPicking/UpdateStoreSelfPicking',
        request_serializer=store_dot_self__picking__pb2.UpdateStoreSelfPickingRequest.SerializeToString,
        response_deserializer=store_dot_self__picking__pb2.UpdateStoreSelfPickingResponse.FromString,
        )
    self.ChangeSelfPickingStatus = channel.unary_unary(
        '/self_picking.StoreSelfPicking/ChangeSelfPickingStatus',
        request_serializer=store_dot_self__picking__pb2.ChangeSelfPickingStatusRequest.SerializeToString,
        response_deserializer=store_dot_self__picking__pb2.ChangeSelfPickingStatusResponse.FromString,
        )
    self.GetPickingProductByStoreId = channel.unary_unary(
        '/self_picking.StoreSelfPicking/GetPickingProductByStoreId',
        request_serializer=store_dot_self__picking__pb2.GetPickingProductByStoreIdRequest.SerializeToString,
        response_deserializer=store_dot_self__picking__pb2.GetPickingProductByStoreIdResponse.FromString,
        )


class StoreSelfPickingServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateStoreSelfPicking(self, request, context):
    """创建门店自采单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListStoreSelfPicking(self, request, context):
    """门店自采单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStoreSelfPickingDetail(self, request, context):
    """查询门店自采单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateStoreSelfPicking(self, request, context):
    """更新门店自采单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangeSelfPickingStatus(self, request, context):
    """门店自采单变更状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPickingProductByStoreId(self, request, context):
    """取得门店可自采商品（相同属性区域）
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreSelfPickingServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateStoreSelfPicking': grpc.unary_unary_rpc_method_handler(
          servicer.CreateStoreSelfPicking,
          request_deserializer=store_dot_self__picking__pb2.CreateStoreSelfPickingRequest.FromString,
          response_serializer=store_dot_self__picking__pb2.CreateStoreSelfPickingResponse.SerializeToString,
      ),
      'ListStoreSelfPicking': grpc.unary_unary_rpc_method_handler(
          servicer.ListStoreSelfPicking,
          request_deserializer=store_dot_self__picking__pb2.ListStoreSelfPickingRequest.FromString,
          response_serializer=store_dot_self__picking__pb2.ListStoreSelfPickingResponse.SerializeToString,
      ),
      'GetStoreSelfPickingDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreSelfPickingDetail,
          request_deserializer=store_dot_self__picking__pb2.GetStoreSelfPickingDetailRequest.FromString,
          response_serializer=store_dot_self__picking__pb2.GetStoreSelfPickingDetailResponse.SerializeToString,
      ),
      'UpdateStoreSelfPicking': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateStoreSelfPicking,
          request_deserializer=store_dot_self__picking__pb2.UpdateStoreSelfPickingRequest.FromString,
          response_serializer=store_dot_self__picking__pb2.UpdateStoreSelfPickingResponse.SerializeToString,
      ),
      'ChangeSelfPickingStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangeSelfPickingStatus,
          request_deserializer=store_dot_self__picking__pb2.ChangeSelfPickingStatusRequest.FromString,
          response_serializer=store_dot_self__picking__pb2.ChangeSelfPickingStatusResponse.SerializeToString,
      ),
      'GetPickingProductByStoreId': grpc.unary_unary_rpc_method_handler(
          servicer.GetPickingProductByStoreId,
          request_deserializer=store_dot_self__picking__pb2.GetPickingProductByStoreIdRequest.FromString,
          response_serializer=store_dot_self__picking__pb2.GetPickingProductByStoreIdResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'self_picking.StoreSelfPicking', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
