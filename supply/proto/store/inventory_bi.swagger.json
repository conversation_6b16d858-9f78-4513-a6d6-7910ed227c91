{"swagger": "2.0", "info": {"title": "store/inventory_bi.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/store/inventory/daily": {"get": {"operationId": "DailyInventory", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeDailyInventoryResponse"}}}, "parameters": [{"name": "branch_id", "description": "批次号.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "category_ids", "description": "商品类别.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_ids", "description": "商品id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "start_date", "description": "开始时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "结束时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "action", "in": "query", "required": false, "type": "string"}, {"name": "if_pre", "in": "query", "required": false, "type": "string"}, {"name": "if_end", "in": "query", "required": false, "type": "string"}, {"name": "exclude_empty", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "description": "用来区分门店和仓库 STORE/WAREHOUSE.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "position_ids", "description": "子账户(仓位)id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "int64"}}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/import": {"post": {"operationId": "UploadInventory", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeUploadInventoryResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeUploadInventoryRequest"}}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/import/list": {"get": {"operationId": "GetInventoryUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeGetInventoryUploadResponse"}}}, "parameters": [{"name": "id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "batch_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/import/{id}/approve": {"post": {"operationId": "ApproveInventoryUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeCommonRes"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeCommonReq"}}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/import/{id}/cancel": {"post": {"operationId": "CancelInventoryUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeCommonRes"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeCommonReq"}}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/import/{id}/details": {"get": {"operationId": "GetInventoryUploadDetails", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeGetInventoryUploadDetailsResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "batch_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/log": {"get": {"operationId": "QueryInventoryLog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeQueryInventoryLogResponse"}}}, "parameters": [{"name": "branch_id", "description": "分支ID,比如一个门店、配送中心.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "product_ids", "description": "产品ID，对应到具体的一个sku.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "category_ids", "description": "商品类别.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "code", "description": "单据编号.", "in": "query", "required": false, "type": "string"}, {"name": "action", "description": "操作.", "in": "query", "required": false, "type": "string"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "order_type", "description": "单据类型.", "in": "query", "required": false, "type": "string"}, {"name": "account_type", "description": "账户类型.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "position_ids", "description": "子账户(仓位)id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/realtime": {"get": {"operationId": "RealtimeInventory", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeRealtimeInventoryResponse"}}}, "parameters": [{"name": "branch_ids", "description": "批次号.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "geo_regions", "description": "地理区域.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "category_ids", "description": "商品类别.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "product_ids", "description": "商品id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "order", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序方式.", "in": "query", "required": false, "type": "string"}, {"name": "exclude", "description": "是否排零.", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "description": "区分门店和仓库 STORE/WAREHOUSE.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "position_ids", "description": "子账户(仓位)id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/realtime/by/accounts": {"post": {"summary": "按账户(目前只兼容仓库/加工中心查询发货仓位库存)和指定商品查询实时库存，包装给前端用\n查询库存前校验当前账户是否有子账户，若有返回子账户的库存", "operationId": "RealtimeInventoryByAccounts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeRealtimeInventoryByAccountsResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeRealtimeInventoryByAccountsRequest"}}], "tags": ["StoreInventoryBiService"]}}, "/api/v2/supply/store/inventory/summary": {"post": {"operationId": "SummaryInventoryByTime", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeSummaryInventoryByTimeResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeSummaryInventoryByTimeRequest"}}], "tags": ["StoreInventoryBiService"]}}}, "definitions": {"storeAccount": {"type": "object", "properties": {"branch_id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "distribution_type": {"type": "string"}}, "title": "账户信息"}, "storeAccounts": {"type": "object", "properties": {"branch_id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "qty": {"type": "number", "format": "double"}, "round": {"type": "number", "format": "double"}, "sub_accounts": {"type": "array", "items": {"$ref": "#/definitions/storeAccounts"}}, "branch_name": {"type": "string"}}}, "storeCommonReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "batch_id": {"type": "string", "format": "uint64"}, "limit": {"type": "string", "format": "uint64"}, "offset": {"type": "string", "format": "uint64"}}}, "storeCommonRes": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "msg": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "batch_id": {"type": "string", "format": "uint64"}}}, "storeDailyInventory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "预留字段id"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_name": {"type": "string", "title": "门店名称"}, "store_code": {"type": "string", "title": "门店编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "category_id": {"type": "string", "format": "uint64", "title": "类别id"}, "category_code": {"type": "string", "title": "类别编码"}, "category_name": {"type": "string", "title": "类别名称"}, "spec": {"type": "string", "title": "规格"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "accounting_unit_code": {"type": "string", "title": "核算单位编码"}, "accounting_unit_name": {"type": "string", "title": "核算单位名称"}, "pre_qty": {"type": "number", "format": "double", "title": "期初库存"}, "qty": {"type": "number", "format": "double", "title": "期末库存"}, "trans_deposit": {"type": "number", "format": "double", "title": "调拨入库"}, "trans_withdraw": {"type": "number", "format": "double", "title": "调拨出库"}, "stoc_deposit": {"type": "number", "format": "double", "title": "盘点入库"}, "stoc_withdraw": {"type": "number", "format": "double", "title": "盘点出库"}, "adj_deposit": {"type": "number", "format": "double", "title": "报废入库"}, "adj_withdraw": {"type": "number", "format": "double", "title": "报废出库"}, "rec_deposit": {"type": "number", "format": "double", "title": "收货入库数量"}, "rec_diff_deposit": {"type": "number", "format": "double", "title": "收货差异入库"}, "rec_diff_withdraw": {"type": "number", "format": "double", "title": "收货差异出库"}, "ret_withdraw": {"type": "number", "format": "double", "title": "退货出库"}, "start_time": {"type": "string", "title": "上次切片节点"}, "end_time": {"type": "string", "title": "切片节点结束时间"}, "sales_deposit": {"type": "number", "format": "double", "title": "销售入库"}, "sales_withdraw": {"type": "number", "format": "double", "title": "销售出库"}, "llyl_deposit": {"type": "number", "format": "double", "title": "热卖领料入库"}, "llyl_withdraw": {"type": "number", "format": "double", "title": "热卖领料出库"}, "cpcrk_deposit": {"type": "number", "format": "double", "title": "热卖成品入库"}, "cpcrk_withdraw": {"type": "number", "format": "double", "title": "热卖成品出库"}, "rec_withdraw": {"type": "number", "format": "double", "title": "收货出库数量"}, "ret_deposit": {"type": "number", "format": "double", "title": "退货入库"}, "freeze_qty": {"type": "number", "format": "double", "title": "冻结"}, "purchase_deposit": {"type": "number", "format": "double", "title": "采购收货入库"}, "low_cost": {"type": "number", "format": "double", "title": "低耗费用化"}, "inventory_adjust": {"type": "number", "format": "double", "title": "特殊库存调整"}, "inventory_init": {"type": "number", "format": "double", "title": "库存初始化"}, "sub_account": {"type": "string", "title": "库存初始化"}, "children": {"type": "array", "items": {"$ref": "#/definitions/storeDailyInventory"}}, "position_id": {"type": "string", "format": "uint64", "title": "仓位id"}, "position_name": {"type": "string", "title": "仓位名称"}, "position_code": {"type": "string", "title": "仓位编码"}, "spick_deposit": {"type": "number", "format": "double", "title": "自采入库"}, "material_trans_deposit": {"type": "number", "format": "double", "title": "物料转换"}, "material_trans_withdraw": {"type": "number", "format": "double"}, "processing_deposit": {"type": "number", "format": "double", "title": "加工入库"}, "processing_withdraw": {"type": "number", "format": "double", "title": "加工出库"}, "packing_deposit": {"type": "number", "format": "double", "title": "包装入库"}, "packing_withdraw": {"type": "number", "format": "double", "title": "包装出库"}, "ret_transfer": {"type": "number", "format": "double", "title": "退货在途"}, "trans_transfer": {"type": "number", "format": "double", "title": "调拨在途"}, "trans_delivery": {"type": "number", "format": "double", "title": "发货在途"}, "trans_purchase": {"type": "number", "format": "double", "title": "采购在途"}, "trans_return_release": {"type": "number", "format": "double", "title": "退货在途释放"}, "trans_transfer_release": {"type": "number", "format": "double", "title": "调拨在途释放"}, "trans_delivery_release": {"type": "number", "format": "double", "title": "发货在途释放"}, "trans_purchase_release": {"type": "number", "format": "double", "title": "采购在途释放"}, "trans_begin": {"type": "number", "format": "double", "title": "在途期初库存"}, "trans_end": {"type": "number", "format": "double", "title": "在途期末库存"}}}, "storeDailyInventoryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeDailyInventory"}}, "total": {"type": "integer", "format": "int32"}}}, "storeExtraDetail": {"type": "object", "properties": {"code": {"type": "string", "title": "业务类型"}, "qty": {"type": "number", "format": "double", "title": "数量"}, "sku_type": {"type": "string", "title": "账户类型"}, "sub_account_id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string"}, "quantity_avail": {"type": "number", "format": "double"}, "quantity_freeze": {"type": "number", "format": "double"}, "quantity_broker": {"type": "number", "format": "double"}}}, "storeGetInventoryUploadDetailsResponse": {"type": "object", "properties": {"total": {"type": "string", "format": "uint64"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/storeInventoryUploadDetails"}}}}, "storeGetInventoryUploadResponse": {"type": "object", "properties": {"total": {"type": "string", "format": "uint64"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/storeInventoryUploadBatch"}}}}, "storeInventory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "预留字段id"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_name": {"type": "string", "title": "门店名称"}, "store_code": {"type": "string", "title": "门店编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "category_id": {"type": "string", "format": "uint64", "title": "类别id"}, "category_code": {"type": "string", "title": "类别编码"}, "category_name": {"type": "string", "title": "类别名称"}, "spec": {"type": "string", "title": "规格"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "accounting_unit_code": {"type": "string", "title": "核算单位编码"}, "accounting_unit_name": {"type": "string", "title": "核算单位名称"}, "qty": {"type": "number", "format": "double", "title": "实时在店库存"}, "demand_unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "demand_unit_code": {"type": "string", "title": "订货单位编码"}, "demand_unit_name": {"type": "string", "title": "订货单位名称"}, "demand_qty": {"type": "number", "format": "double", "title": "订货流水数量"}, "product_status": {"type": "string", "title": "商品状态"}, "freeze_qty": {"type": "number", "format": "double", "title": "冻结库存"}, "broker_qty": {"type": "number", "format": "double", "title": "在途库存"}, "extra_detail": {"type": "array", "items": {"$ref": "#/definitions/storeExtraDetail"}, "title": "库存详情"}, "purchase_unit_id": {"type": "string", "format": "uint64", "title": "采购单位id"}, "purchase_unit_code": {"type": "string", "title": "采购单位编码"}, "purchase_unit_name": {"type": "string", "title": "采购单位名称"}, "purchase_qty": {"type": "number", "format": "double", "title": "采购流水数量"}, "children": {"type": "array", "items": {"$ref": "#/definitions/storeInventory"}}, "position_id": {"type": "string", "format": "uint64", "title": "仓位id"}, "position_name": {"type": "string", "title": "仓位名称"}, "position_code": {"type": "string", "title": "仓位编码"}, "primary_id": {"type": "string", "format": "uint64"}}}, "storeInventoryLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "预留字段id"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_name": {"type": "string", "title": "门店名称"}, "store_code": {"type": "string", "title": "门店编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "spec": {"type": "string", "title": "规格"}, "order_code": {"type": "string", "title": "订单编码"}, "order_type": {"type": "string", "title": "订单类型"}, "order_time": {"type": "string", "format": "date-time", "title": "订单时间"}, "action": {"type": "string", "title": "操作"}, "status": {"type": "string", "title": "状态"}, "qty": {"type": "number", "format": "double", "title": "流水数量"}, "stock_id": {"type": "string", "title": "库存id"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "accounting_unit_code": {"type": "string", "title": "核算单位编码"}, "accounting_unit_name": {"type": "string", "title": "核算单位名称"}, "demand_unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "demand_unit_code": {"type": "string", "title": "订货单位编码"}, "demand_unit_name": {"type": "string", "title": "订货单位名称"}, "demand_qty": {"type": "number", "format": "double", "title": "订货流水数量"}, "category_id": {"type": "string", "format": "uint64", "title": "类别id"}, "category_code": {"type": "string", "title": "类别编码"}, "category_name": {"type": "string", "title": "类别名称"}, "jde_order_id": {"type": "string", "title": "jde相关"}, "jde_order_type": {"type": "string"}, "jde_mcu": {"type": "string"}, "account_type": {"type": "string", "title": "账户类型"}, "sub_account_id": {"type": "string", "format": "uint64", "title": "子账户信息"}, "sub_account_code": {"type": "string"}, "sub_account_name": {"type": "string"}}}, "storeInventoryUploadBatch": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "filename": {"type": "string"}, "branch_type": {"type": "string"}, "updated_name": {"type": "string"}, "created_name": {"type": "string"}}}, "storeInventoryUploadDetails": {"type": "object", "properties": {"branch_code": {"type": "string", "title": "组织编号"}, "branch_name": {"type": "string", "title": "组织名称"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "quantity": {"type": "number", "format": "double", "title": "期初库存"}, "branch_id": {"type": "string", "format": "uint64", "title": "组织id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "error": {"type": "string", "title": "错误信息"}, "partner_id": {"type": "string", "format": "uint64"}, "batch_id": {"type": "string", "format": "uint64", "title": "批次id"}, "row_num": {"type": "number", "format": "double", "title": "数据行号"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "id": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "storeQueryInventoryLogResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeInventoryLog"}}, "total": {"type": "integer", "format": "int32"}, "amount_sum": {"type": "number", "format": "double"}}}, "storeRealtimeInventoryByAccountsRequest": {"type": "object", "properties": {"accounts": {"type": "array", "items": {"$ref": "#/definitions/storeAccount"}}, "check_type": {"type": "string", "title": "拉取组织仓位配置校验的业务类型:例如\"发货\"deliverGoods、\"采购收货\"purchaseReceive"}}, "title": "批量查询单Branch、单Product库存"}, "storeRealtimeInventoryByAccountsResponse": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/storeAccounts"}}}, "title": "实时库存查询请求结果"}, "storeRealtimeInventoryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeInventory"}}, "total": {"type": "integer", "format": "int32"}}}, "storeSummaryInventory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "预留字段id"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_name": {"type": "string", "title": "门店名称"}, "store_code": {"type": "string", "title": "门店编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "product_status": {"type": "string", "title": "商品状态"}, "spec": {"type": "string", "title": "规格"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "accounting_unit_code": {"type": "string", "title": "核算单位编码"}, "accounting_unit_name": {"type": "string", "title": "核算单位名称"}, "pre_qty": {"type": "number", "format": "double", "title": "期初库存"}, "qty": {"type": "number", "format": "double", "title": "期末库存"}, "trans_deposit": {"type": "number", "format": "double", "title": "调拨入库"}, "trans_withdraw": {"type": "number", "format": "double", "title": "调拨出库"}, "stoc_deposit": {"type": "number", "format": "double", "title": "盘点入库"}, "stoc_withdraw": {"type": "number", "format": "double", "title": "盘点出库"}, "adj_deposit": {"type": "number", "format": "double", "title": "报废入库"}, "adj_withdraw": {"type": "number", "format": "double", "title": "报废出库"}, "rec_deposit": {"type": "number", "format": "double", "title": "收货入库数量"}, "rec_diff_deposit": {"type": "number", "format": "double", "title": "收货差异入库"}, "rec_diff_withdraw": {"type": "number", "format": "double", "title": "收货差异出库"}, "ret_withdraw": {"type": "number", "format": "double", "title": "退货出库"}, "start_time": {"type": "string", "format": "date-time", "title": "上次切片节点"}, "end_time": {"type": "string", "format": "date-time", "title": "切片节点结束时间"}, "sales_deposit": {"type": "number", "format": "double", "title": "销售入库"}, "sales_withdraw": {"type": "number", "format": "double", "title": "销售出库"}, "llyl_deposit": {"type": "number", "format": "double", "title": "热卖领料入库"}, "llyl_withdraw": {"type": "number", "format": "double", "title": "热卖领料出库"}, "cpcrk_deposit": {"type": "number", "format": "double", "title": "热卖成品入库"}, "cpcrk_withdraw": {"type": "number", "format": "double", "title": "热卖成品出库"}, "rec_withdraw": {"type": "number", "format": "double", "title": "收货出库数量"}, "ret_deposit": {"type": "number", "format": "double", "title": "退货入库"}, "freeze_qty": {"type": "number", "format": "double", "title": "冻结"}, "purchase_deposit": {"type": "number", "format": "double", "title": "采购收货入库"}, "low_cost": {"type": "number", "format": "double", "title": "低耗费用化"}, "inventory_adjust": {"type": "number", "format": "double", "title": "特殊库存调整"}, "inventory_init": {"type": "number", "format": "double", "title": "库存初始化"}, "sub_account": {"type": "string", "title": "库存初始化"}, "children": {"type": "array", "items": {"$ref": "#/definitions/storeDailyInventory"}}, "position_id": {"type": "string", "format": "uint64", "title": "仓位id"}, "position_name": {"type": "string", "title": "仓位名称"}, "position_code": {"type": "string", "title": "仓位编码"}, "spick_deposit": {"type": "number", "format": "double", "title": "自采入库"}, "material_trans_deposit": {"type": "number", "format": "double", "title": "物料转换"}, "material_trans_withdraw": {"type": "number", "format": "double"}, "processing_deposit": {"type": "number", "format": "double", "title": "加工入库"}, "processing_withdraw": {"type": "number", "format": "double", "title": "加工出库"}, "packing_deposit": {"type": "number", "format": "double", "title": "包装入库"}, "packing_withdraw": {"type": "number", "format": "double", "title": "包装出库"}, "ret_transfer": {"type": "number", "format": "double", "title": "退货在途"}, "trans_transfer": {"type": "number", "format": "double", "title": "调拨在途"}, "trans_delivery": {"type": "number", "format": "double", "title": "发货在途"}, "trans_purchase": {"type": "number", "format": "double", "title": "采购在途"}, "trans_return_release": {"type": "number", "format": "double", "title": "退货在途释放"}, "trans_transfer_release": {"type": "number", "format": "double", "title": "调拨在途释放"}, "trans_delivery_release": {"type": "number", "format": "double", "title": "发货在途释放"}, "trans_purchase_release": {"type": "number", "format": "double", "title": "采购在途释放"}, "trans_begin": {"type": "number", "format": "double", "title": "在途期初库存"}, "trans_end": {"type": "number", "format": "double", "title": "在途期末库存"}, "category1": {"type": "string", "title": "一级商品类别"}, "category2": {"type": "string", "title": "二级商品类别"}, "category3": {"type": "string", "title": "三级商品类别"}, "category4": {"type": "string", "title": "四级商品类别"}, "category5": {"type": "string", "title": "五级商品类别"}, "category6": {"type": "string", "title": "六级商品类别"}, "category7": {"type": "string", "title": "七级商品类别"}, "category8": {"type": "string", "title": "八级商品类别"}, "category9": {"type": "string", "title": "九级商品类别"}, "category10": {"type": "string", "title": "十级商品类别"}}}, "storeSummaryInventoryByTimeRequest": {"type": "object", "properties": {"branch_id": {"type": "string", "format": "uint64", "title": "门店id"}, "start_date": {"type": "string", "format": "date-time", "title": "开始时间"}, "end_date": {"type": "string", "format": "date-time", "title": "结束时间"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品类别id"}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品id"}, "if_pre": {"type": "string", "title": "期初库存"}, "if_end": {"type": "string", "title": "期末库存"}, "branch_type": {"type": "string"}, "limit": {"type": "string", "format": "int64"}, "offset": {"type": "string", "format": "int64"}, "position_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "storeSummaryInventoryByTimeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeSummaryInventory"}}, "total": {"type": "integer", "format": "int32"}}}, "storeUploadInventoryRequest": {"type": "object", "properties": {"filename": {"type": "string", "title": "文件名称"}, "branch_type": {"type": "string", "title": "导入主体:store门店/discenter仓库/machining-center加工中心"}, "file": {"type": "string", "title": "文件流"}, "lan": {"type": "string"}}, "title": "导入期初库存文件参数"}, "storeUploadInventoryResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean", "title": "数据导入结果"}, "filename": {"type": "string", "title": "导入文件名"}, "batch_id": {"type": "string", "format": "uint64", "title": "导入批次id"}, "msg": {"type": "string", "title": "返回结果"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/storeInventoryUploadDetails"}}}}}}