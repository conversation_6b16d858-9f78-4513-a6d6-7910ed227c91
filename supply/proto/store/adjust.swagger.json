{"swagger": "2.0", "info": {"title": "store/adjust.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/store/adjust": {"get": {"summary": "GetAdjust查询每日损耗表9", "operationId": "GetAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustGetAdjustResponse"}}}, "parameters": [{"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "reason_type", "in": "query", "required": false, "type": "string"}, {"name": "branches", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_ids", "description": "商品ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "status", "description": "每日损耗计划状态.\n\n - INITED: 新建\n - SUBMITTED: 提交\n - APPROVED: 审核\n - REJECTED: 驳回\n - CANCELLED: 作废\n - CONFIRMED: 确认(弃用)", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "APPROVED", "REJECTED", "CANCELLED", "CONFIRMED"]}}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "ids", "description": "单据id列表查询.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "sources", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/auto_create/cancel": {"put": {"summary": "CancelAdjust 取消每日损耗表20(状态变为已作废CANCELLED)", "operationId": "CancelAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustCancelAdjustResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustCancelAdjustRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/bi/collect": {"get": {"summary": "GetAdjustBiCollect每日损耗表汇总报表18", "operationId": "GetAdjustBiCollect", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustGetAdjustBiCollectResponse"}}}, "parameters": [{"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "bom_product_id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "period_symbol", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "reason_type", "description": "报废原因.", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "hour_offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "position_ids", "description": "暂不使用.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/bi/detailed": {"get": {"summary": "GetAdjustCollectDetailed损耗表明细汇总报表21", "operationId": "GetAdjustCollectDetailed", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustGetAdjustCollectDetailedResponse"}}}, "parameters": [{"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "bom_product_id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "position_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "reason_type", "description": "报废原因.", "in": "query", "required": false, "type": "string"}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/close/auto": {"get": {"summary": "AutoCloseCreatedAdjust自动关闭未确认的损耗表,不传参数默认前一天，传date操作date日期的单子22", "operationId": "AutoCloseCreatedAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustAutoCloseCreatedAdjustResponse"}}}, "parameters": [{"name": "adjust_date", "in": "query", "required": false, "type": "string"}, {"name": "partner_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "user_id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/create": {"post": {"summary": "CreatedAdjust手动创建一个每日损耗表14", "operationId": "CreatedAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustAdjust"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustCreatedAdjustRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/material/cost": {"get": {"summary": "物料报废量成本报表", "operationId": "GetMaterialAdjustData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustGetMaterialAdjustDataResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "material_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/pos": {"post": {"summary": "第三方根据code创建报废", "operationId": "CreatedAdjustByCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustAdjust"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustCreatedAdjustByCodeRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/product/{adjust_id}/delete": {"put": {"summary": "DeleteAdjustProduct删除每日损耗表的商品17", "operationId": "DeleteAdjustProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustDeleteAdjustProductResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustDeleteAdjustProductRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/store/products": {"get": {"summary": "GetAdjustProductByStoreID查询门店可损耗商品13", "operationId": "GetAdjustProductByStoreID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustGetAdjustProductByStoreIDResponse"}}}, "parameters": [{"name": "store_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "adjust_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "search", "in": "query", "required": false, "type": "string"}, {"name": "search_fields", "in": "query", "required": false, "type": "string"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/{adjust_id}": {"get": {"summary": "GetAdjustByID查询一个每日损耗表11", "operationId": "GetAdjustByID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustAdjust"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/{adjust_id}/approve": {"put": {"summary": "审核一个报废单", "operationId": "ApproveAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustApproveAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustApproveAdjustRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/{adjust_id}/confirm": {"put": {"summary": "确认一个报废单", "operationId": "ConfirmAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustConfirmAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustConfirmAdjustRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/{adjust_id}/delete": {"put": {"summary": "DeleteAdjust删除一个每日损耗表16", "operationId": "DeleteAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustDeleteAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustDeleteAdjustRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/{adjust_id}/product": {"get": {"summary": "GetAdjustProduct每日损耗表商品查询10", "operationId": "GetAdjustProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustGetAdjustProductResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "in": "query", "required": false, "type": "string"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/{adjust_id}/reject": {"put": {"summary": "驳回一个报废单", "operationId": "RejectAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustRejectAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustRejectAdjustRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/{adjust_id}/submit": {"put": {"summary": "提交一个报废单", "operationId": "SubmitAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustSubmitAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustSubmitAdjustRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/adjust/{adjust_id}/update": {"put": {"summary": "UpdateAdjust更新一个每日损耗表15", "operationId": "UpdateAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustAdjust"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/store_adjustUpdateAdjustRequest"}}], "tags": ["StoreAdjust"]}}, "/api/v2/supply/store/ping": {"get": {"summary": "Ping 健康检查", "operationId": "<PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/store_adjustPong"}}}, "tags": ["StoreAdjust"]}}}, "definitions": {"GetAdjustRequestSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "APPROVED", "REJECTED", "CANCELLED", "CONFIRMED"], "default": "NONE", "title": "- INITED: 新建\n - SUBMITTED: 提交\n - APPROVED: 审核\n - REJECTED: 驳回\n - CANCELLED: 作废\n - CONFIRMED: 确认(弃用)"}, "SkuRemarkTag": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "name": {"type": "string"}}}, "store_adjustAD_total": {"type": "object", "properties": {"count": {"type": "string", "format": "uint64"}, "sum_quantity": {"type": "number", "format": "double"}, "sum_accounting_quantity": {"type": "number", "format": "double"}, "sum_qty": {"type": "number", "format": "double"}, "sum_accounting_qty": {"type": "number", "format": "double"}}}, "store_adjustAdjust": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "adjust_order_number": {"type": "string", "format": "uint64"}, "adjust_store": {"type": "string", "format": "uint64"}, "adjust_store_secondary_id": {"type": "string"}, "code": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "process_status": {"type": "string"}, "reason_type": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "branch_batch_id": {"type": "string", "format": "uint64"}, "schedule_code": {"type": "string"}, "schedule_id": {"type": "string", "format": "uint64"}, "request_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "receive_id": {"type": "string", "format": "uint64"}, "receive_code": {"type": "string"}, "schedule_name": {"type": "string"}, "branch_type": {"type": "string"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAttachments"}, "title": "附件"}, "source": {"type": "string", "title": "单据来源\n\"POS_ADJUST\"      # POS端报废\n\"MANUAL_CREATED\"  # 手动新建\n\"PLAN_CREATED\"    # 报废计划创建"}, "reason_name": {"type": "string"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "total_amount": {"type": "number", "format": "double"}, "total_sales_amount": {"type": "number", "format": "double"}}}, "store_adjustAdjustBiCollectResponse": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "product_code": {"type": "string", "title": "\"category_parent\""}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "reason_type": {"type": "string"}, "reason_name": {"type": "string"}, "bom_product_id": {"type": "string", "format": "uint64", "title": "拆解原料"}, "bom_product_code": {"type": "string"}, "bom_product_name": {"type": "string"}, "bom_unit_id": {"type": "string", "format": "uint64", "title": "bom配方单位"}, "bom_unit_code": {"type": "string"}, "bom_unit_name": {"type": "string"}, "bom_accounting_unit_id": {"type": "string", "format": "uint64", "title": "bom核算单位"}, "bom_accounting_unit_code": {"type": "string"}, "bom_accounting_unit_name": {"type": "string"}, "qty": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "accounting_qty": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "period_symbol": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}}}, "store_adjustAdjustCollectDetailed": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "adjust_code": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}, "adjust_id": {"type": "string", "format": "uint64"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "reason_type": {"type": "string"}, "reason_name": {"type": "string"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "bom_product_id": {"type": "string", "format": "uint64", "title": "拆解原料"}, "bom_product_code": {"type": "string"}, "bom_product_name": {"type": "string"}, "bom_unit_id": {"type": "string", "format": "uint64", "title": "bom配方单位"}, "bom_unit_code": {"type": "string"}, "bom_unit_name": {"type": "string"}, "bom_accounting_unit_id": {"type": "string", "format": "uint64", "title": "bom核算单位"}, "bom_accounting_unit_code": {"type": "string"}, "bom_accounting_unit_name": {"type": "string"}, "qty": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "accounting_qty": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "branch_type": {"type": "string"}, "remark": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}}}, "store_adjustAdjustPositionProducts": {"type": "object", "properties": {"position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAdjustProduct"}}, "total": {"type": "string", "format": "uint64"}}}, "store_adjustAdjustProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "adjust_id": {"type": "string", "format": "uint64"}, "adjust_store": {"type": "string", "format": "uint64"}, "confirmed_quantity": {"type": "number", "format": "double"}, "created_by": {"type": "string", "format": "uint64"}, "is_confirmed": {"type": "boolean", "format": "boolean"}, "item_number": {"type": "integer", "format": "int64"}, "material_number": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "spec": {"type": "string"}, "stocktake_quantity": {"type": "number", "format": "double"}, "stocktake_unit_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64"}, "adjust_date": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "user_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "reason_type": {"type": "string"}, "convert_accounting_quantity": {"type": "number", "format": "double"}, "is_bom": {"type": "boolean", "format": "boolean"}, "position_id": {"type": "string", "format": "uint64"}, "sku_remark": {"type": "string"}, "units": {"type": "array", "items": {"$ref": "#/definitions/store_adjustCreateAdjustProductUint"}}, "model_name": {"type": "string"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "tax_amount": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}}}, "store_adjustAdjustProductByStore": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "loss_report_order": {"type": "string"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "model_name": {"type": "string"}, "storage_type": {"type": "string"}, "category_id": {"type": "string", "format": "uint64", "title": "单位分类"}, "category_name": {"type": "string"}, "units": {"type": "array", "items": {"$ref": "#/definitions/store_adjustCreateAdjustProductUint"}}, "barcode": {"type": "array", "items": {"type": "string"}}}}, "store_adjustAdjustResponse": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "product_code": {"type": "string", "title": "\"category_parent\""}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double", "title": "报废数量"}, "store_code": {"type": "string", "title": "门店编号，id,名称"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位"}, "unit_name": {"type": "string"}, "reason_type": {"type": "string"}, "bom_product_id": {"type": "string", "format": "uint64", "title": "拆解原料"}, "bom_product_code": {"type": "string"}, "bom_product_name": {"type": "string"}, "qty": {"type": "number", "format": "double"}, "bom_unit_id": {"type": "string", "format": "uint64", "title": "bom配方单位"}, "bom_unit_code": {"type": "string"}, "bom_unit_name": {"type": "string"}, "bom_accounting_unit_id": {"type": "string", "format": "uint64", "title": "bom核算单位"}, "bom_accounting_unit_code": {"type": "string"}, "bom_accounting_unit_name": {"type": "string"}, "accounting_qty": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "title": "单位成本"}, "cost": {"type": "number", "format": "double", "title": "物料成本"}, "adjust_date": {"type": "string", "title": "报废日期"}}}, "store_adjustApproveAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "store_adjustApproveAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "store_adjustAttachments": {"type": "object", "properties": {"type": {"type": "string"}, "url": {"type": "string"}}}, "store_adjustAutoCloseCreatedAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "store_adjustCancelAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "store_adjustCancelAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "store_adjustConfirmAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}, "branch_type": {"type": "string"}}}, "store_adjustConfirmAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "store_adjustCreateAdjustProductUint": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "数据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "scope_id": {"type": "string", "format": "uint64", "title": "scope_id"}, "name": {"type": "string"}, "code": {"type": "string"}, "updated": {"type": "string"}, "rate": {"type": "number", "format": "double"}, "default": {"type": "boolean", "format": "boolean"}, "order": {"type": "boolean", "format": "boolean"}, "purchase": {"type": "boolean", "format": "boolean"}, "sales": {"type": "boolean", "format": "boolean"}, "stocktake": {"type": "boolean", "format": "boolean"}, "bom": {"type": "boolean", "format": "boolean"}, "default_stocktake": {"type": "boolean", "format": "boolean"}, "transfer": {"type": "boolean", "format": "boolean"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}}}, "store_adjustCreatedAdjustByCodeRequest": {"type": "object", "properties": {"adjust_store": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/definitions/store_adjustCreatedAdjustProductByCode"}}, "reason_type": {"type": "string"}, "remark": {"type": "string"}, "request_id": {"type": "string"}, "adjust_date": {"type": "string"}, "lan": {"type": "string"}}}, "store_adjustCreatedAdjustProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}, "quantity": {"type": "number", "format": "double"}, "reason_type": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "skuRemark": {"type": "array", "items": {"$ref": "#/definitions/store_adjustSkuRemark"}}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "tax_amount": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}}}, "store_adjustCreatedAdjustProductByCode": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "unit_code": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "reason_type": {"type": "string"}, "skuRemark": {"type": "array", "items": {"$ref": "#/definitions/store_adjustSkuRemark"}}}}, "store_adjustCreatedAdjustRequest": {"type": "object", "properties": {"adjust_store": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/store_adjustCreatedAdjustProduct"}}, "reason_type": {"type": "string"}, "remark": {"type": "string"}, "request_id": {"type": "string", "format": "uint64"}, "adjust_date": {"type": "string", "format": "date-time"}, "branch_type": {"type": "string", "title": "区分门店还是仓库: STORE or WAREHOUSE"}, "position_id": {"type": "string", "format": "uint64"}, "source": {"type": "string", "title": "单据来源\n\"POS_ADJUST\"      # POS端报废\n\"MANUAL_CREATED\"  # 手动新建\n\"PLAN_CREATED\"    # 报废计划创建"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAttachments"}, "title": "附件"}}}, "store_adjustDeleteAdjustProductRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "store_adjustDeleteAdjustProductResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "store_adjustDeleteAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "store_adjustDeleteAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "store_adjustGetAdjustBiCollectResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAdjustBiCollectResponse"}}, "total": {"$ref": "#/definitions/store_adjustAD_total"}}}, "store_adjustGetAdjustCollectDetailedResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAdjustCollectDetailed"}}, "total": {"$ref": "#/definitions/store_adjustAD_total"}}}, "store_adjustGetAdjustProductByStoreIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAdjustProductByStore"}}, "total": {"type": "integer", "format": "int64"}}}, "store_adjustGetAdjustProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAdjustProduct"}}, "total": {"type": "integer", "format": "int64"}, "position_rows": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAdjustPositionProducts"}}}}, "store_adjustGetAdjustResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAdjust"}}, "total": {"type": "integer", "format": "int64"}}}, "store_adjustGetMaterialAdjustDataResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAdjustResponse"}}, "total": {"$ref": "#/definitions/store_adjustAD_total"}}}, "store_adjustPong": {"type": "object", "properties": {"msg": {"type": "string"}}}, "store_adjustRejectAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}, "reject_reason": {"type": "string", "title": "驳回原因"}}}, "store_adjustRejectAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "store_adjustSkuRemark": {"type": "object", "properties": {"name": {"$ref": "#/definitions/SkuRemarkTag"}, "values": {"$ref": "#/definitions/SkuRemarkTag"}}}, "store_adjustSubmitAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "store_adjustSubmitAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "store_adjustUpdateAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/store_adjustCreatedAdjustProduct"}}, "remark": {"type": "string"}, "reason_type": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}, "branch_type": {"type": "string", "title": "区分门店还是仓库: STORE or WAREHOUSE"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/store_adjustAttachments"}, "title": "附件"}, "position_id": {"type": "string", "format": "uint64"}}}}}