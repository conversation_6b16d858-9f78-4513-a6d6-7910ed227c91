{"swagger": "2.0", "info": {"title": "store/production_process_receipts.proto", "version": "version not set"}, "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/store/production_process/list": {"get": {"summary": "生产单列表查询", "operationId": "StoreProductionProcessReceipts_ListProductionProcessReceipts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeListProductionProcessReceiptsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "start_date", "description": "开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "process_store_ids", "description": "门店、仓库、加工中心.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "code", "description": "单号.", "in": "query", "required": false, "type": "string"}, {"name": "status", "description": "订单状态 'INITED'新建 'SUBMITTED'已提交 'APPROVED'已审核 'REJECTED'已驳回， 多选传数组.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}], "tags": ["StoreProductionProcessReceipts"]}}, "/api/v2/supply/store/production_process/rate": {"get": {"summary": "生产转化率查询", "operationId": "StoreProductionProcessReceipts_GetMaterialToProductRate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeGetMaterialToProductRateResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "include_total", "description": "是否包含总数.", "in": "query", "required": false, "type": "boolean"}, {"name": "store_ids", "description": "门店ID.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "start_date", "description": "查询开始时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "查询结束时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "offset", "description": "分页开始处.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "分页限制数.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "material_ids", "description": "生产原料.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_ids", "description": "产出物.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "conversion_rate", "description": "是否对比(no_comparison:不对比，higher_than:高于，lower_than:低于，equal_to:相等).", "in": "query", "required": false, "type": "string"}], "tags": ["StoreProductionProcessReceipts"]}}, "/api/v2/supply/store/production_process/receipts": {"post": {"summary": "创建生产单", "operationId": "StoreProductionProcessReceipts_CreateProductionProcessReceipts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeCreateProductionProcessReceiptsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeCreateProductionProcessReceiptsRequest"}}], "tags": ["StoreProductionProcessReceipts"]}}, "/api/v2/supply/store/production_process/{branch_id}/rule/get": {"get": {"summary": "根据选择的门店获取对应区域、门店下的规则ID", "operationId": "StoreProductionProcessReceipts_GetRuleByBranchId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeGetRuleByBranchIdResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "branch_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["StoreProductionProcessReceipts"]}}, "/api/v2/supply/store/production_process/{receipt_id}": {"put": {"summary": "更新生产单", "operationId": "StoreProductionProcessReceipts_UpdateProductionProcessReceipts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeUpdateProductionProcessReceiptsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "receipt_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeUpdateProductionProcessReceiptsRequest"}}], "tags": ["StoreProductionProcessReceipts"]}}, "/api/v2/supply/store/production_process/{receipt_id}/detail": {"get": {"summary": "生产单详情查询", "operationId": "StoreProductionProcessReceipts_GetProductionProcessReceiptDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeGetProductionProcessReceiptDetailResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "receipt_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["StoreProductionProcessReceipts"]}}, "/api/v2/supply/store/production_process/{receipt_id}/{status}": {"put": {"summary": "生产单状态变更", "operationId": "StoreProductionProcessReceipts_ChangeProductionProcessReceiptsStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/storeChangeProductionProcessReceiptsStatusResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/runtimeError"}}}, "parameters": [{"name": "receipt_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "status", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/storeChangeProductionProcessReceiptsStatusRequest"}}], "tags": ["StoreProductionProcessReceipts"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"type_url": {"type": "string"}, "value": {"type": "string", "format": "byte"}}}, "runtimeError": {"type": "object", "properties": {"error": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "storeChangeProductionProcessReceiptsStatusRequest": {"type": "object", "properties": {"receipt_id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}}}, "storeChangeProductionProcessReceiptsStatusResponse": {"type": "object", "properties": {"receipt_id": {"type": "string", "format": "uint64"}, "result": {"type": "string"}}}, "storeCreateProductionProcessReceiptsRequest": {"type": "object", "properties": {"process_date": {"type": "string", "format": "date-time", "title": "生产日期"}, "process_store_id": {"type": "string", "format": "uint64", "title": "生产门店、仓库、加工中心ID"}, "process_store_name": {"type": "string", "title": "生产门店、仓库、加工中心name"}, "process_store_code": {"type": "string", "title": "生产门店、仓库、加工中心code"}, "remark": {"type": "string", "title": "备注"}, "request_id": {"type": "string", "format": "uint64", "title": "幂等性校验请求id"}, "details": {"type": "array", "items": {"$ref": "#/definitions/storeProductionDetails"}, "title": "生产明细\n   repeated ProductionReceiptsRows rows = 7;\n生产明细"}, "items": {"type": "array", "items": {"$ref": "#/definitions/storeProductionItems"}, "title": "生产理论率和实际率"}}, "title": "创建生产单请求参数"}, "storeCreateProductionProcessReceiptsResponse": {"type": "object", "properties": {"receipt_id": {"type": "string", "format": "uint64", "title": "包装单id"}, "result": {"type": "string"}}, "title": "创建生产单返回"}, "storeGetMaterialToProductRateResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeMaterialToProductRateRow"}}, "total": {"type": "string", "format": "uint64"}}}, "storeGetProductionProcessReceiptDetailResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "main_id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "code": {"type": "string"}, "process_date": {"type": "string", "format": "date-time", "title": "生产单日期"}, "process_store_id": {"type": "string", "format": "uint64", "title": "生产门店、仓库、加工中心ID"}, "process_store_name": {"type": "string", "title": "生产门店、仓库、加工中心name"}, "process_store_code": {"type": "string", "title": "生产门店、仓库、加工中心code"}, "remark": {"type": "string", "title": "备注"}, "request_id": {"type": "string", "format": "uint64", "title": "幂等性校验请求id"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/storeProductionReceiptsRows"}, "title": "规则以及下属商品比率信息"}, "type": {"type": "string", "title": "生产明细\n   repeated ProductionDetails details = 18;\n   // 生产理论率和实际率\n   repeated ProductionItems items = 19;\n类型"}}}, "storeGetRuleByBranchIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeRules"}}}}, "storeListProductionProcessReceiptsResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/storeProductionProcessRow"}}, "total": {"type": "string", "format": "uint64"}}}, "storeMaterialToProductRateRow": {"type": "object", "properties": {"material_id": {"type": "string", "format": "uint64", "title": "生产原料"}, "material_code": {"type": "string"}, "material_name": {"type": "string"}, "product_id": {"type": "string", "format": "uint64", "title": "产出物"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "process_date": {"type": "string", "format": "date-time", "title": "生产日期"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_name": {"type": "string", "title": "门店编号"}, "store_code": {"type": "string"}, "theoretical_rate": {"type": "number", "format": "double", "title": "理论转换率"}, "actual_rate": {"type": "number", "format": "double", "title": "实际转换率"}, "code": {"type": "string", "title": "单号"}, "material_quantity": {"type": "number", "format": "double", "title": "原料数量"}, "product_quantity": {"type": "number", "format": "double", "title": "产出物数量"}}}, "storeProductionDetails": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "没有不传"}, "product_id": {"type": "string", "format": "uint64", "title": "物料/商品id"}, "product_code": {"type": "string", "title": "物料/商品code"}, "product_name": {"type": "string", "title": "物料/商品名称"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_code": {"type": "string"}, "unit_name": {"type": "string"}, "type": {"type": "string", "title": "商品类型 material:原料；product：产出物"}, "quantity": {"type": "number", "format": "double", "title": "物料数量"}, "production_rule": {"type": "string", "format": "uint64", "title": "规则ID"}, "production_rule_code": {"type": "string"}, "production_rule_name": {"type": "string"}, "specification_id": {"type": "string", "format": "uint64", "title": "规格"}, "specification_code": {"type": "string"}, "specification_name": {"type": "string"}}}, "storeProductionItems": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "没有不传"}, "from_material_id": {"type": "string", "format": "uint64", "title": "原料ID"}, "from_material_code": {"type": "string", "title": "物料code"}, "from_material_name": {"type": "string", "title": "物料名称"}, "to_material_id": {"type": "string", "format": "uint64", "title": "产出物ID"}, "to_material_code": {"type": "string", "title": "产出物code"}, "to_material_name": {"type": "string", "title": "产出物名称"}, "theoretical_rate": {"type": "number", "format": "double", "title": "理论转换率"}, "actual_rate": {"type": "number", "format": "double", "title": "实际转换率"}, "production_rule": {"type": "string", "format": "uint64", "title": "规则ID"}, "production_rule_code": {"type": "string"}, "production_rule_name": {"type": "string"}}}, "storeProductionProcessRow": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "status": {"type": "string"}, "code": {"type": "string"}, "process_date": {"type": "string", "format": "date-time", "title": "生产单日期"}, "process_store_id": {"type": "string", "format": "uint64", "title": "生产门店、仓库、加工中心ID"}, "process_store_name": {"type": "string", "title": "生产门店、仓库、加工中心name"}, "process_store_code": {"type": "string", "title": "生产门店、仓库、加工中心code"}, "remark": {"type": "string", "title": "备注"}, "request_id": {"type": "string", "format": "uint64", "title": "幂等性校验请求id"}, "type": {"type": "string", "title": "类型"}}}, "storeProductionReceiptsRows": {"type": "object", "properties": {"production_rule": {"type": "string", "format": "uint64", "title": "规则ID"}, "production_rule_code": {"type": "string"}, "production_rule_name": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/storeProductionDetails"}, "title": "生产明细"}, "items": {"type": "array", "items": {"$ref": "#/definitions/storeProductionItems"}, "title": "生产理论率和实际率"}}}, "storeRules": {"type": "object", "properties": {"id": {"type": "string"}, "code": {"type": "string"}, "name": {"type": "string"}}}, "storeUpdateProductionProcessReceiptsRequest": {"type": "object", "properties": {"receipt_id": {"type": "string", "format": "uint64"}, "process_date": {"type": "string", "format": "date-time", "title": "包装日期"}, "process_store_id": {"type": "string", "format": "uint64", "title": "生产门店、仓库、加工中心ID"}, "process_store_name": {"type": "string", "title": "生产门店、仓库、加工中心name"}, "process_store_code": {"type": "string", "title": "生产门店、仓库、加工中心code"}, "remark": {"type": "string", "title": "备注"}, "details": {"type": "array", "items": {"$ref": "#/definitions/storeProductionDetails"}, "title": "生产明细"}, "items": {"type": "array", "items": {"$ref": "#/definitions/storeProductionItems"}, "title": "生产理论率和实际率"}, "receipt_rule_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "删除的规则<预留字段>"}}}, "storeUpdateProductionProcessReceiptsResponse": {"type": "object", "properties": {"receipt_id": {"type": "string", "format": "uint64"}, "result": {"type": "string"}}}}}