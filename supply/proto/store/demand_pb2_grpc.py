# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from store import demand_pb2 as store_dot_demand__pb2


class StoreDemandServiceStub(object):
  """门店订货单相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListDemand = channel.unary_unary(
        '/store.StoreDemandService/ListDemand',
        request_serializer=store_dot_demand__pb2.QueryDemandRequest.SerializeToString,
        response_deserializer=store_dot_demand__pb2.QueryDemandResponse.FromString,
        )
    self.GetDemandDetail = channel.unary_unary(
        '/store.StoreDemandService/GetDemandDetail',
        request_serializer=store_dot_demand__pb2.IdRequest.SerializeToString,
        response_deserializer=store_dot_demand__pb2.DemandEntity.FromString,
        )
    self.UpdateDemandInfo = channel.unary_unary(
        '/store.StoreDemandService/UpdateDemandInfo',
        request_serializer=store_dot_demand__pb2.UpdateDemandInfoRequest.SerializeToString,
        response_deserializer=store_dot_demand__pb2.Response.FromString,
        )
    self.GetDemandProductDetail = channel.unary_unary(
        '/store.StoreDemandService/GetDemandProductDetail',
        request_serializer=store_dot_demand__pb2.GetDemandProductDetailRequest.SerializeToString,
        response_deserializer=store_dot_demand__pb2.QueryDemandProductResponse.FromString,
        )
    self.UpdateDemandProduct = channel.unary_unary(
        '/store.StoreDemandService/UpdateDemandProduct',
        request_serializer=store_dot_demand__pb2.UpdateDemandProductRequest.SerializeToString,
        response_deserializer=store_dot_demand__pb2.UpdateDemandProductRequest.FromString,
        )
    self.DealDemandById = channel.unary_unary(
        '/store.StoreDemandService/DealDemandById',
        request_serializer=store_dot_demand__pb2.DealDemandByIdRequest.SerializeToString,
        response_deserializer=store_dot_demand__pb2.Response.FromString,
        )
    self.SetCloseOrderData = channel.unary_unary(
        '/store.StoreDemandService/SetCloseOrderData',
        request_serializer=store_dot_demand__pb2.SetCloseOrderDataRequest.SerializeToString,
        response_deserializer=store_dot_demand__pb2.Response.FromString,
        )
    self.SwitchCloseOrder = channel.unary_unary(
        '/store.StoreDemandService/SwitchCloseOrder',
        request_serializer=store_dot_demand__pb2.SwitchCloseOrderRequest.SerializeToString,
        response_deserializer=store_dot_demand__pb2.Response.FromString,
        )


class StoreDemandServiceServicer(object):
  """门店订货单相关服务
  """

  def ListDemand(self, request, context):
    """查询门店订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandDetail(self, request, context):
    """获取订货单详细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemandInfo(self, request, context):
    """更新订货单信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandProductDetail(self, request, context):
    """获取订货商品信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemandProduct(self, request, context):
    """更新门店订货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealDemandById(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SetCloseOrderData(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SwitchCloseOrder(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreDemandServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListDemand': grpc.unary_unary_rpc_method_handler(
          servicer.ListDemand,
          request_deserializer=store_dot_demand__pb2.QueryDemandRequest.FromString,
          response_serializer=store_dot_demand__pb2.QueryDemandResponse.SerializeToString,
      ),
      'GetDemandDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandDetail,
          request_deserializer=store_dot_demand__pb2.IdRequest.FromString,
          response_serializer=store_dot_demand__pb2.DemandEntity.SerializeToString,
      ),
      'UpdateDemandInfo': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemandInfo,
          request_deserializer=store_dot_demand__pb2.UpdateDemandInfoRequest.FromString,
          response_serializer=store_dot_demand__pb2.Response.SerializeToString,
      ),
      'GetDemandProductDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandProductDetail,
          request_deserializer=store_dot_demand__pb2.GetDemandProductDetailRequest.FromString,
          response_serializer=store_dot_demand__pb2.QueryDemandProductResponse.SerializeToString,
      ),
      'UpdateDemandProduct': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemandProduct,
          request_deserializer=store_dot_demand__pb2.UpdateDemandProductRequest.FromString,
          response_serializer=store_dot_demand__pb2.UpdateDemandProductRequest.SerializeToString,
      ),
      'DealDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.DealDemandById,
          request_deserializer=store_dot_demand__pb2.DealDemandByIdRequest.FromString,
          response_serializer=store_dot_demand__pb2.Response.SerializeToString,
      ),
      'SetCloseOrderData': grpc.unary_unary_rpc_method_handler(
          servicer.SetCloseOrderData,
          request_deserializer=store_dot_demand__pb2.SetCloseOrderDataRequest.FromString,
          response_serializer=store_dot_demand__pb2.Response.SerializeToString,
      ),
      'SwitchCloseOrder': grpc.unary_unary_rpc_method_handler(
          servicer.SwitchCloseOrder,
          request_deserializer=store_dot_demand__pb2.SwitchCloseOrderRequest.FromString,
          response_serializer=store_dot_demand__pb2.Response.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'store.StoreDemandService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
