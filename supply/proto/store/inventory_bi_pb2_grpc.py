# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from store import inventory_bi_pb2 as store_dot_inventory__bi__pb2


class StoreInventoryBiServiceStub(object):
  """库存报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.RealtimeInventory = channel.unary_unary(
        '/store.StoreInventoryBiService/RealtimeInventory',
        request_serializer=store_dot_inventory__bi__pb2.RealtimeInventoryRequest.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.RealtimeInventoryResponse.FromString,
        )
    self.DailyInventory = channel.unary_unary(
        '/store.StoreInventoryBiService/DailyInventory',
        request_serializer=store_dot_inventory__bi__pb2.DailyInventoryRequest.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.DailyInventoryResponse.FromString,
        )
    self.QueryInventoryLog = channel.unary_unary(
        '/store.StoreInventoryBiService/QueryInventoryLog',
        request_serializer=store_dot_inventory__bi__pb2.QueryInventoryLogRequest.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.QueryInventoryLogResponse.FromString,
        )
    self.RealtimeInventoryByAccounts = channel.unary_unary(
        '/store.StoreInventoryBiService/RealtimeInventoryByAccounts',
        request_serializer=store_dot_inventory__bi__pb2.RealtimeInventoryByAccountsRequest.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.RealtimeInventoryByAccountsResponse.FromString,
        )
    self.SummaryInventoryByTime = channel.unary_unary(
        '/store.StoreInventoryBiService/SummaryInventoryByTime',
        request_serializer=store_dot_inventory__bi__pb2.SummaryInventoryByTimeRequest.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.SummaryInventoryByTimeResponse.FromString,
        )
    self.UploadInventory = channel.unary_unary(
        '/store.StoreInventoryBiService/UploadInventory',
        request_serializer=store_dot_inventory__bi__pb2.UploadInventoryRequest.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.UploadInventoryResponse.FromString,
        )
    self.GetInventoryUpload = channel.unary_unary(
        '/store.StoreInventoryBiService/GetInventoryUpload',
        request_serializer=store_dot_inventory__bi__pb2.CommonReq.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.GetInventoryUploadResponse.FromString,
        )
    self.GetInventoryUploadDetails = channel.unary_unary(
        '/store.StoreInventoryBiService/GetInventoryUploadDetails',
        request_serializer=store_dot_inventory__bi__pb2.CommonReq.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.GetInventoryUploadDetailsResponse.FromString,
        )
    self.ApproveInventoryUpload = channel.unary_unary(
        '/store.StoreInventoryBiService/ApproveInventoryUpload',
        request_serializer=store_dot_inventory__bi__pb2.CommonReq.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.CommonRes.FromString,
        )
    self.CancelInventoryUpload = channel.unary_unary(
        '/store.StoreInventoryBiService/CancelInventoryUpload',
        request_serializer=store_dot_inventory__bi__pb2.CommonReq.SerializeToString,
        response_deserializer=store_dot_inventory__bi__pb2.CommonRes.FromString,
        )


class StoreInventoryBiServiceServicer(object):
  """库存报表相关服务
  """

  def RealtimeInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DailyInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryInventoryLog(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RealtimeInventoryByAccounts(self, request, context):
    """按账户(目前只兼容仓库/加工中心查询发货仓位库存)和指定商品查询实时库存，包装给前端用
    查询库存前校验当前账户是否有子账户，若有返回子账户的库存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SummaryInventoryByTime(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UploadInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetInventoryUpload(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetInventoryUploadDetails(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveInventoryUpload(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelInventoryUpload(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreInventoryBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'RealtimeInventory': grpc.unary_unary_rpc_method_handler(
          servicer.RealtimeInventory,
          request_deserializer=store_dot_inventory__bi__pb2.RealtimeInventoryRequest.FromString,
          response_serializer=store_dot_inventory__bi__pb2.RealtimeInventoryResponse.SerializeToString,
      ),
      'DailyInventory': grpc.unary_unary_rpc_method_handler(
          servicer.DailyInventory,
          request_deserializer=store_dot_inventory__bi__pb2.DailyInventoryRequest.FromString,
          response_serializer=store_dot_inventory__bi__pb2.DailyInventoryResponse.SerializeToString,
      ),
      'QueryInventoryLog': grpc.unary_unary_rpc_method_handler(
          servicer.QueryInventoryLog,
          request_deserializer=store_dot_inventory__bi__pb2.QueryInventoryLogRequest.FromString,
          response_serializer=store_dot_inventory__bi__pb2.QueryInventoryLogResponse.SerializeToString,
      ),
      'RealtimeInventoryByAccounts': grpc.unary_unary_rpc_method_handler(
          servicer.RealtimeInventoryByAccounts,
          request_deserializer=store_dot_inventory__bi__pb2.RealtimeInventoryByAccountsRequest.FromString,
          response_serializer=store_dot_inventory__bi__pb2.RealtimeInventoryByAccountsResponse.SerializeToString,
      ),
      'SummaryInventoryByTime': grpc.unary_unary_rpc_method_handler(
          servicer.SummaryInventoryByTime,
          request_deserializer=store_dot_inventory__bi__pb2.SummaryInventoryByTimeRequest.FromString,
          response_serializer=store_dot_inventory__bi__pb2.SummaryInventoryByTimeResponse.SerializeToString,
      ),
      'UploadInventory': grpc.unary_unary_rpc_method_handler(
          servicer.UploadInventory,
          request_deserializer=store_dot_inventory__bi__pb2.UploadInventoryRequest.FromString,
          response_serializer=store_dot_inventory__bi__pb2.UploadInventoryResponse.SerializeToString,
      ),
      'GetInventoryUpload': grpc.unary_unary_rpc_method_handler(
          servicer.GetInventoryUpload,
          request_deserializer=store_dot_inventory__bi__pb2.CommonReq.FromString,
          response_serializer=store_dot_inventory__bi__pb2.GetInventoryUploadResponse.SerializeToString,
      ),
      'GetInventoryUploadDetails': grpc.unary_unary_rpc_method_handler(
          servicer.GetInventoryUploadDetails,
          request_deserializer=store_dot_inventory__bi__pb2.CommonReq.FromString,
          response_serializer=store_dot_inventory__bi__pb2.GetInventoryUploadDetailsResponse.SerializeToString,
      ),
      'ApproveInventoryUpload': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveInventoryUpload,
          request_deserializer=store_dot_inventory__bi__pb2.CommonReq.FromString,
          response_serializer=store_dot_inventory__bi__pb2.CommonRes.SerializeToString,
      ),
      'CancelInventoryUpload': grpc.unary_unary_rpc_method_handler(
          servicer.CancelInventoryUpload,
          request_deserializer=store_dot_inventory__bi__pb2.CommonReq.FromString,
          response_serializer=store_dot_inventory__bi__pb2.CommonRes.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'store.StoreInventoryBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
