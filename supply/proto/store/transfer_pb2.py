# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: store/transfer.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='store/transfer.proto',
  package='store_transfer',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x14store/transfer.proto\x12\x0estore_transfer\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"\xc2\x04\n\x12GetTransferRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\x39\n\x06status\x18\x02 \x03(\x0e\x32).store_transfer.GetTransferRequest.STATUS\x12\x17\n\x0fshipping_stores\x18\x03 \x03(\x04\x12\x18\n\x10receiving_stores\x18\x04 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x05 \x03(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\r\n\x05limit\x18\x07 \x01(\r\x12.\n\nstart_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x0b\n\x03ids\x18\x0c \x03(\x04\x12\x14\n\x0c\x61uto_confirm\x18\r \x01(\x08\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0c\n\x04sort\x18\x10 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x11 \x01(\t\x12\x0b\n\x03lan\x18\x12 \x01(\t\x12\r\n\x05types\x18\x13 \x03(\t\x12\x10\n\x08sub_type\x18\x14 \x01(\t\x12\x1b\n\x13receiving_positions\x18\x15 \x03(\x04\x12\x1a\n\x12shipping_positions\x18\x16 \x03(\x04\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"\xb8\t\n\x08Transfer\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x11\n\tmaster_id\x18\x02 \x01(\x04\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x1d\n\x15transfer_order_number\x18\x04 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x05 \x01(\x04\x12\x1b\n\x13shipping_store_name\x18\x06 \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x07 \x01(\t\x12\x17\n\x0freceiving_store\x18\x08 \x01(\x04\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\n \x01(\t\x12\x13\n\x0breason_type\x18\x0b \x01(\t\x12\x10\n\x08receiver\x18\x0c \x01(\x04\x12\x0e\n\x06remark\x18\r \x01(\t\x12\x0f\n\x07shipper\x18\x0e \x01(\x04\x12\x0f\n\x07\x65xtends\x18\x0f \x01(\t\x12\x0c\n\x04type\x18\x10 \x01(\t\x12/\n\x06status\x18\x11 \x01(\x0e\x32\x1f.store_transfer.Transfer.STATUS\x12\x30\n\x0eprocess_status\x18\x12 \x01(\x0e\x32\x18.store_transfer.P_STATUS\x12\x10\n\x08sub_type\x18\x13 \x01(\t\x12\x12\n\ncreated_by\x18\x14 \x01(\x04\x12\x12\n\nupdated_by\x18\x15 \x01(\x04\x12\x31\n\rtransfer_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereceiving_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rshipping_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nrequest_id\x18\x1b \x01(\x04\x12\x0f\n\x07user_id\x18\x1c \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1d \x01(\t\x12\x14\n\x0cupdated_name\x18\x1e \x01(\t\x12\x14\n\x0cjde_order_id\x18\x1f \x01(\t\x12\x14\n\x0c\x61uto_confirm\x18  \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18! \x01(\t\x12\x13\n\x0b\x61ttachments\x18\" \x01(\t\x12\x1a\n\x12receiving_position\x18# \x01(\x04\x12\x1f\n\x17receiving_position_name\x18$ \x01(\t\x12\x19\n\x11shipping_position\x18% \x01(\x04\x12\x1e\n\x16shipping_position_name\x18& \x01(\t\x12\x18\n\x10sub_account_type\x18\' \x01(\t\x12\x15\n\rcross_company\x18( \x01(\t\x12\x14\n\x0ctotal_amount\x18) \x01(\x01\x12\x1a\n\x12total_sales_amount\x18* \x01(\x01\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"L\n\x13GetTransferResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.store_transfer.Transfer\x12\r\n\x05total\x18\x02 \x01(\r\":\n\x16GetTransferByIDRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\xc2\t\n\x17GetTransferByIDResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x11\n\tmaster_id\x18\x02 \x01(\x04\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x1d\n\x15transfer_order_number\x18\x04 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x05 \x01(\x04\x12\x1b\n\x13shipping_store_name\x18\x06 \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x07 \x01(\t\x12\x17\n\x0freceiving_store\x18\x08 \x01(\x04\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\n \x01(\t\x12\x13\n\x0breason_type\x18\x0b \x01(\t\x12\x10\n\x08receiver\x18\x0c \x01(\x04\x12\x0e\n\x06remark\x18\r \x01(\t\x12\x0f\n\x07shipper\x18\x0e \x01(\x04\x12\x0f\n\x07\x65xtends\x18\x0f \x01(\t\x12\x0c\n\x04type\x18\x10 \x01(\t\x12>\n\x06status\x18\x11 \x01(\x0e\x32..store_transfer.GetTransferByIDResponse.STATUS\x12\x30\n\x0eprocess_status\x18\x12 \x01(\x0e\x32\x18.store_transfer.P_STATUS\x12\x10\n\x08sub_type\x18\x13 \x01(\t\x12\x12\n\ncreated_by\x18\x14 \x01(\x04\x12\x12\n\nupdated_by\x18\x15 \x01(\x04\x12\x31\n\rtransfer_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereceiving_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rshipping_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x1b \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1c \x01(\t\x12\x14\n\x0cupdated_name\x18\x1d \x01(\t\x12\x14\n\x0cjde_order_id\x18\x1e \x01(\t\x12\x14\n\x0c\x61uto_confirm\x18\x1f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18  \x01(\t\x12\x13\n\x0b\x61ttachments\x18! \x01(\t\x12\x1a\n\x12receiving_position\x18# \x01(\x04\x12\x1f\n\x17receiving_position_name\x18$ \x01(\t\x12\x19\n\x11shipping_position\x18% \x01(\x04\x12\x1e\n\x16shipping_position_name\x18& \x01(\t\x12\x18\n\x10sub_account_type\x18\' \x01(\t\x12\x15\n\rcross_company\x18( \x01(\t\x12\x14\n\x0ctotal_amount\x18) \x01(\x01\x12\x1a\n\x12total_sales_amount\x18* \x01(\x01\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"\xe6\x01\n#GetTransferProductByBranchIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rinclude_total\x18\x02 \x01(\x08\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x15\n\rsearch_fields\x18\x06 \x01(\t\x12\x0e\n\x06search\x18\x07 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x08 \x03(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x10\n\x08order_by\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\"\xa2\x01\n\x13TransferProductUnit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x10\n\x08tax_rate\x18\x04 \x01(\x01\x12\x11\n\ttax_price\x18\x05 \x01(\x01\x12\x12\n\ncost_price\x18\x06 \x01(\x01\x12\x13\n\x0bsales_price\x18\x07 \x01(\x01\x12\x11\n\tunit_rate\x18\x08 \x01(\x01\"\xe8\x01\n\x15SelectTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x1b\n\x13product_category_id\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x31\n\x04unit\x18\x05 \x03(\x0b\x32#.store_transfer.TransferProductUnit\x12\x0f\n\x07\x62\x61rcode\x18\x06 \x03(\t\x12\x12\n\nmodel_name\x18\t \x01(\t\x12\x1a\n\x12real_inventory_qty\x18\n \x01(\x01\"\xb3\x01\n$GetTransferProductByBranchIDResponse\x12\x33\n\x04rows\x18\x01 \x03(\x0b\x32%.store_transfer.SelectTransferProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12G\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32%.store_transfer.SelectTransferProduct\"C\n\"GetTransferRegionByBranchIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"G\n\x0cRegionBranch\x12\x0f\n\x07\x61\x64\x64ress\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\"Q\n#GetTransferRegionByBranchIDResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.store_transfer.RegionBranch\"\x8e\x01\n%GetTransferProductByTransferIDRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\r\n\x05order\x18\x02 \x01(\t\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x13\n\x0btransfer_id\x18\x05 \x01(\x04\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xc5\x07\n\x0fTransferProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x13\n\x0btransfer_id\x18\x03 \x01(\x04\x12\x0f\n\x07unit_id\x18\x04 \x01(\x04\x12\x11\n\tunit_name\x18\x05 \x01(\t\x12\x11\n\tunit_spec\x18\x06 \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x07 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x08 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\n \x01(\t\x12$\n\x1c\x61\x63\x63ounting_received_quantity\x18\x0b \x01(\x01\x12\x13\n\x0b\x64\x65scription\x18\x0c \x01(\t\x12\x14\n\x0cis_confirmed\x18\r \x01(\x08\x12\x13\n\x0bitem_number\x18\x0e \x01(\r\x12\x17\n\x0fmaterial_number\x18\x0f \x01(\t\x12\x14\n\x0cproduct_code\x18\x10 \x01(\t\x12\x14\n\x0cproduct_name\x18\x11 \x01(\t\x12\x10\n\x08quantity\x18\x12 \x01(\x01\x12\x17\n\x0freceiving_store\x18\x13 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x14 \x01(\x04\x12\x12\n\ncreated_by\x18\x15 \x01(\x04\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x31\n\rtransfer_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07\x65xtends\x18\x1a \x01(\t\x12\x12\n\npartner_id\x18\x1b \x01(\x04\x12\x0f\n\x07user_id\x18\x1c \x01(\x04\x12#\n\x1b\x63onfirmed_received_quantity\x18\x1d \x01(\x01\x12\x14\n\x0c\x63reated_name\x18\x1e \x01(\t\x12\x14\n\x0cupdated_name\x18\x1f \x01(\t\x12\x10\n\x08tax_rate\x18# \x01(\x01\x12\x11\n\ttax_price\x18$ \x01(\x01\x12\x12\n\ncost_price\x18% \x01(\x01\x12\x0e\n\x06\x61mount\x18& \x01(\x01\x12\x13\n\x0bsales_price\x18\' \x01(\x01\x12\x14\n\x0csales_amount\x18( \x01(\x01\x12\x11\n\tunit_rate\x18) \x01(\x01\x12\x1a\n\x12real_inventory_qty\x18* \x01(\x01\"f\n&GetTransferProductByTransferIDResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.store_transfer.TransferProduct\x12\r\n\x05total\x18\x02 \x01(\r\"\xc0\x01\n\x13PostTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x0f\n\x07unit_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x10\n\x08tax_rate\x18\x04 \x01(\x01\x12\x11\n\ttax_price\x18\x05 \x01(\x01\x12\x12\n\ncost_price\x18\x06 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x07 \x01(\x01\x12\x13\n\x0bsales_price\x18\x08 \x01(\x01\x12\x14\n\x0csales_amount\x18\t \x01(\x01\"\xe5\x03\n\x15\x43reateTransferRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x35\n\x08products\x18\x04 \x03(\x0b\x32#.store_transfer.PostTransferProduct\x12\x0e\n\x06remark\x18\x05 \x01(\t\x12\x31\n\rshipping_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rtransfer_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x08 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\t \x01(\t\x12\x0c\n\x04type\x18\n \x01(\t\x12\x10\n\x08sub_type\x18\x0b \x01(\t\x12\x1a\n\x12receiving_position\x18\x0c \x01(\x04\x12\x19\n\x11shipping_position\x18\r \x01(\x04\x12\x1f\n\x17receiving_position_name\x18\x0e \x01(\t\x12\x1e\n\x16shipping_position_name\x18\x0f \x01(\t\x12\x18\n\x10sub_account_type\x18\x10 \x01(\t\"\xea\x03\n\x1a\x43reateInnerTransferRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x35\n\x08products\x18\x04 \x03(\x0b\x32#.store_transfer.PostTransferProduct\x12\x0e\n\x06remark\x18\x05 \x01(\t\x12\x31\n\rshipping_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rtransfer_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x08 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\t \x01(\t\x12\x0c\n\x04type\x18\n \x01(\t\x12\x10\n\x08sub_type\x18\x0b \x01(\t\x12\x1a\n\x12receiving_position\x18\x0c \x01(\x04\x12\x19\n\x11shipping_position\x18\r \x01(\x04\x12\x1f\n\x17receiving_position_name\x18\x0e \x01(\t\x12\x1e\n\x16shipping_position_name\x18\x0f \x01(\t\x12\x18\n\x10sub_account_type\x18\x10 \x01(\t\"2\n\x1b\x43reateInnerTransferResponse\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"\xf0\x01\n\x15UpdateTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x35\n\x08products\x18\x02 \x03(\x0b\x32#.store_transfer.PostTransferProduct\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x31\n\rtransfer_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\n \x01(\t\x12\x10\n\x08sub_type\x18\x0b \x01(\t\"(\n\x16UpdateTransferResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"f\n\x1a\x43onfirmPostTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x0f\n\x07unit_id\x18\x02 \x01(\x04\x12#\n\x1b\x63onfirmed_received_quantity\x18\x03 \x01(\x01\"\xab\x01\n\x16\x43onfirmTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12<\n\x08products\x18\x04 \x03(\x0b\x32*.store_transfer.ConfirmPostTransferProduct\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\";\n\x1c\x44\x65leteTransferProductRequest\x12\x1b\n\x13transfer_product_id\x18\x01 \x01(\x04\"/\n\x1d\x44\x65leteTransferProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\",\n\x15\x44\x65leteTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"(\n\x16\x44\x65leteTransferResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xa3\x01\n\x15SubmitTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x35\n\x08products\x18\x04 \x03(\x0b\x32#.store_transfer.PostTransferProduct\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\"\x97\x01\n\x1eSubmitTransferReceivingRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x35\n\x08products\x18\x04 \x03(\x0b\x32#.store_transfer.PostTransferProduct\",\n\x15\x43\x61ncelTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"\xb9\x03\n\x19GetTransferCollectRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x0e\n\x06offset\x18\x08 \x01(\r\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\r\n\x05is_in\x18\n \x01(\x08\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x10\n\x08jde_code\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x10\n\x08sub_type\x18\x13 \x01(\t\x12\x15\n\rcross_company\x18\x14 \x01(\t\"\xef\x06\n\x0fTransferCollect\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x30\n\x0f\x63\x61tegory_parent\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x14\n\x0cproduct_code\x18\x0c \x01(\t\x12\x12\n\nproduct_id\x18\r \x01(\x04\x12\x14\n\x0cproduct_name\x18\x0e \x01(\t\x12\x15\n\rproduct_price\x18\x0f \x01(\x01\x12\x17\n\x0freceiving_store\x18\x10 \x01(\x04\x12\x1c\n\x14receiving_store_code\x18\x11 \x01(\t\x12\x1d\n\x15receiving_store_us_id\x18\x12 \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x13 \x01(\t\x12\x10\n\x08quantity\x18\x14 \x01(\x01\x12\x16\n\x0eshipping_store\x18\x15 \x01(\x04\x12\x1b\n\x13shipping_store_code\x18\x16 \x01(\t\x12\x1b\n\x13shipping_store_name\x18\x17 \x01(\t\x12\x1c\n\x14shipping_store_us_id\x18\x18 \x01(\t\x12\x0f\n\x07unit_id\x18\x19 \x01(\x04\x12\x11\n\tunit_name\x18\x1a \x01(\t\x12\x14\n\x0cproduct_spec\x18\x1b \x01(\t\x12\x12\n\npcdp_price\x18\x1c \x01(\t\x12\x1a\n\x12receiving_position\x18\x1d \x01(\x04\x12\x1f\n\x17receiving_position_code\x18\x1e \x01(\t\x12\x1f\n\x17receiving_position_name\x18\x1f \x01(\t\x12\x19\n\x11shipping_position\x18  \x01(\x04\x12\x1e\n\x16shipping_position_code\x18! \x01(\t\x12\x1e\n\x16shipping_position_name\x18\" \x01(\t\x12\x15\n\rcross_company\x18# \x01(\t\x12\x13\n\x0bpcdp_amount\x18$ \x01(\t\x12\x10\n\x08\x63urrency\x18% \x01(\t\"u\n\x14TransferCollectTotal\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x1f\n\x17sum_accounting_quantity\x18\x02 \x01(\x01\x12\x14\n\x0csum_quantity\x18\x03 \x01(\x01\x12\x17\n\x0fsum_pcdp_amount\x18\x04 \x01(\t\"\x80\x01\n\x1aGetTransferCollectResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.store_transfer.TransferCollect\x12\x33\n\x05total\x18\x02 \x01(\x0b\x32$.store_transfer.TransferCollectTotal\"\xc1\x03\n!GetTransferCollectDetailedRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x0e\n\x06offset\x18\x08 \x01(\r\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\r\n\x05is_in\x18\n \x01(\x08\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x10\n\x08jde_code\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x10\n\x08sub_type\x18\x13 \x01(\t\x12\x15\n\rcross_company\x18\x14 \x01(\t\"\xb7\x07\n\x17TransferCollectDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\n\n\x02id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x17\n\x0freceiving_store\x18\r \x01(\x04\x12\x1c\n\x14receiving_store_code\x18\x0e \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x0f \x01(\t\x12\x16\n\x0eshipping_store\x18\x10 \x01(\x04\x12\x1b\n\x13shipping_store_code\x18\x11 \x01(\t\x12\x1b\n\x13shipping_store_name\x18\x12 \x01(\t\x12\x0e\n\x06status\x18\x13 \x01(\t\x12\x15\n\rtransfer_code\x18\x14 \x01(\t\x12\x31\n\rtransfer_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0btransfer_id\x18\x16 \x01(\x04\x12\x0f\n\x07unit_id\x18\x17 \x01(\x04\x12\x11\n\tunit_name\x18\x18 \x01(\t\x12\x1c\n\x14shipping_store_us_id\x18\x19 \x01(\t\x12\x1d\n\x15receiving_store_us_id\x18\x1a \x01(\t\x12\x12\n\npcdp_price\x18\x1b \x01(\t\x12\x0c\n\x04\x63ode\x18\" \x01(\t\x12\x1a\n\x12receiving_position\x18( \x01(\x04\x12\x1f\n\x17receiving_position_code\x18) \x01(\t\x12\x1f\n\x17receiving_position_name\x18* \x01(\t\x12\x19\n\x11shipping_position\x18+ \x01(\x04\x12\x1e\n\x16shipping_position_code\x18, \x01(\t\x12\x1e\n\x16shipping_position_name\x18- \x01(\t\x12\x15\n\rcross_company\x18# \x01(\t\x12\x13\n\x0bpcdp_amount\x18$ \x01(\t\x12\x10\n\x08\x63urrency\x18% \x01(\t\"\x90\x01\n\"GetTransferCollectDetailedResponse\x12\x35\n\x04rows\x18\x01 \x03(\x0b\x32\'.store_transfer.TransferCollectDetailed\x12\x33\n\x05total\x18\x02 \x01(\x0b\x32$.store_transfer.TransferCollectTotal\".\n\x16GetTransferListRequest\x12\x14\n\x0ctransfer_ids\x18\x01 \x03(\x04\"V\n\x17GetTransferListResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.store_transfer.TransferDetail\x12\r\n\x05total\x18\x02 \x01(\r\"\xcc\x02\n\x0eTransferDetail\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x1b\n\x13shipping_store_name\x18\x03 \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x04 \x01(\t\x12\x35\n\x06status\x18\x05 \x01(\x0e\x32%.store_transfer.TransferDetail.STATUS\x12\x15\n\rtransfer_date\x18\x06 \x01(\x04\x12\x0e\n\x06remark\x18\x07 \x01(\t\x12\x31\n\x08products\x18\x08 \x03(\x0b\x32\x1f.store_transfer.TransferProduct\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04*K\n\x08P_STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\x0e\n\nPROCESSING\x10\x02\x12\r\n\tSUCCESSED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x32\xc9\x15\n\rStoreTransfer\x12\x43\n\x04Ping\x12\x16.google.protobuf.Empty\x1a\x14.store_transfer.Pong\"\r\x82\xd3\xe4\x93\x02\x07\x12\x05/ping\x12}\n\x0bGetTransfer\x12\".store_transfer.GetTransferRequest\x1a#.store_transfer.GetTransferResponse\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v2/supply/store/transfer\x12\x97\x01\n\x0fGetTransferByID\x12&.store_transfer.GetTransferByIDRequest\x1a\'.store_transfer.GetTransferByIDResponse\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/supply/store/transfer/{transfer_id}\x12\xc9\x01\n\x1cGetTransferProductByBranchID\x12\x33.store_transfer.GetTransferProductByBranchIDRequest\x1a\x34.store_transfer.GetTransferProductByBranchIDResponse\">\x82\xd3\xe4\x93\x02\x38\x12\x36/api/v2/supply/store/transfer/store/{store_id}/product\x12\xc5\x01\n\x1bGetTransferRegionByBranchID\x12\x32.store_transfer.GetTransferRegionByBranchIDRequest\x1a\x33.store_transfer.GetTransferRegionByBranchIDResponse\"=\x82\xd3\xe4\x93\x02\x37\x12\x35/api/v2/supply/store/transfer/store/{store_id}/region\x12\xcc\x01\n\x1eGetTransferProductByTransferID\x12\x35.store_transfer.GetTransferProductByTransferIDRequest\x1a\x36.store_transfer.GetTransferProductByTransferIDResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/store/transfer/{transfer_id}/product\x12\x80\x01\n\x0e\x43reateTransfer\x12%.store_transfer.CreateTransferRequest\x1a\x18.store_transfer.Transfer\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/supply/store/transfer/main:\x01*\x12\xa3\x01\n\x0eUpdateTransfer\x12%.store_transfer.UpdateTransferRequest\x1a&.store_transfer.UpdateTransferResponse\"B\x82\xd3\xe4\x93\x02<\x1a\x37/api/v2/supply/store/transfer/main/{transfer_id}/update:\x01*\x12\x98\x01\n\x0f\x43onfirmTransfer\x12&.store_transfer.ConfirmTransferRequest\x1a\x18.store_transfer.Transfer\"C\x82\xd3\xe4\x93\x02=\x1a\x38/api/v2/supply/store/transfer/main/{transfer_id}/confirm:\x01*\x12\xc3\x01\n\x15\x44\x65leteTransferProduct\x12,.store_transfer.DeleteTransferProductRequest\x1a-.store_transfer.DeleteTransferProductResponse\"M\x82\xd3\xe4\x93\x02G\x1a\x42/api/v2/supply/store/transfer/product/{transfer_product_id}/delete:\x01*\x12\xa3\x01\n\x0e\x44\x65leteTransfer\x12%.store_transfer.DeleteTransferRequest\x1a&.store_transfer.DeleteTransferResponse\"B\x82\xd3\xe4\x93\x02<\x1a\x37/api/v2/supply/store/transfer/main/{transfer_id}/delete:\x01*\x12\x95\x01\n\x0eSubmitTransfer\x12%.store_transfer.SubmitTransferRequest\x1a\x18.store_transfer.Transfer\"B\x82\xd3\xe4\x93\x02<\x1a\x37/api/v2/supply/store/transfer/main/{transfer_id}/submit:\x01*\x12\x95\x01\n\x0e\x43\x61ncelTransfer\x12%.store_transfer.CancelTransferRequest\x1a\x18.store_transfer.Transfer\"B\x82\xd3\xe4\x93\x02<\x1a\x37/api/v2/supply/store/transfer/main/{transfer_id}/cancel:\x01*\x12\xa1\x01\n\x12GetTransferCollect\x12).store_transfer.GetTransferCollectRequest\x1a*.store_transfer.GetTransferCollectResponse\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/store/transfer/collect/report\x12\xb6\x01\n\x1aGetTransferCollectDetailed\x12\x31.store_transfer.GetTransferCollectDetailedRequest\x1a\x32.store_transfer.GetTransferCollectDetailedResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/store/transfer/bi/detailed\x12\x9e\x01\n\x13\x43reateInnerTransfer\x12*.store_transfer.CreateInnerTransferRequest\x1a+.store_transfer.CreateInnerTransferResponse\".\x82\xd3\xe4\x93\x02(\"#/api/v2/supply/store/inner/transfer:\x01*\x12\x98\x01\n\x0fGetTransferList\x12&.store_transfer.GetTransferListRequest\x1a\'.store_transfer.GetTransferListResponse\"4\x82\xd3\xe4\x93\x02.\x12)/api/v2/supply/store/transfer/detail/list:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])

_P_STATUS = _descriptor.EnumDescriptor(
  name='P_STATUS',
  full_name='store_transfer.P_STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PROCESSING', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUCCESSED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FAILED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=11546,
  serialized_end=11621,
)
_sym_db.RegisterEnumDescriptor(_P_STATUS)

P_STATUS = enum_type_wrapper.EnumTypeWrapper(_P_STATUS)
NONE = 0
INITED = 1
PROCESSING = 2
SUCCESSED = 3
FAILED = 4


_GETTRANSFERREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='store_transfer.GetTransferRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=687,
  serialized_end=762,
)
_sym_db.RegisterEnumDescriptor(_GETTRANSFERREQUEST_STATUS)

_TRANSFER_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='store_transfer.Transfer.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=687,
  serialized_end=762,
)
_sym_db.RegisterEnumDescriptor(_TRANSFER_STATUS)

_GETTRANSFERBYIDRESPONSE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='store_transfer.GetTransferByIDResponse.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=687,
  serialized_end=762,
)
_sym_db.RegisterEnumDescriptor(_GETTRANSFERBYIDRESPONSE_STATUS)

_TRANSFERDETAIL_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='store_transfer.TransferDetail.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=687,
  serialized_end=762,
)
_sym_db.RegisterEnumDescriptor(_TRANSFERDETAIL_STATUS)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='store_transfer.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='store_transfer.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=162,
  serialized_end=181,
)


_GETTRANSFERREQUEST = _descriptor.Descriptor(
  name='GetTransferRequest',
  full_name='store_transfer.GetTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_transfer.GetTransferRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_transfer.GetTransferRequest.status', index=1,
      number=2, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_stores', full_name='store_transfer.GetTransferRequest.shipping_stores', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_stores', full_name='store_transfer.GetTransferRequest.receiving_stores', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='store_transfer.GetTransferRequest.product_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_transfer.GetTransferRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_transfer.GetTransferRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_transfer.GetTransferRequest.start_date', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_transfer.GetTransferRequest.end_date', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_transfer.GetTransferRequest.code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='store_transfer.GetTransferRequest.ids', index=10,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_confirm', full_name='store_transfer.GetTransferRequest.auto_confirm', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_transfer.GetTransferRequest.order', index=12,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_transfer.GetTransferRequest.sort', index=13,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.GetTransferRequest.branch_type', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_transfer.GetTransferRequest.lan', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='store_transfer.GetTransferRequest.types', index=16,
      number=19, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_transfer.GetTransferRequest.sub_type', index=17,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_positions', full_name='store_transfer.GetTransferRequest.receiving_positions', index=18,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_positions', full_name='store_transfer.GetTransferRequest.shipping_positions', index=19,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETTRANSFERREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=184,
  serialized_end=762,
)


_TRANSFER = _descriptor.Descriptor(
  name='Transfer',
  full_name='store_transfer.Transfer',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_transfer.Transfer.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_id', full_name='store_transfer.Transfer.master_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_transfer.Transfer.partner_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_order_number', full_name='store_transfer.Transfer.transfer_order_number', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='store_transfer.Transfer.shipping_store', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='store_transfer.Transfer.shipping_store_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='store_transfer.Transfer.receiving_store_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.Transfer.receiving_store', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_transfer.Transfer.code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='store_transfer.Transfer.description', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='store_transfer.Transfer.reason_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='store_transfer.Transfer.receiver', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_transfer.Transfer.remark', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipper', full_name='store_transfer.Transfer.shipper', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='store_transfer.Transfer.extends', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_transfer.Transfer.type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_transfer.Transfer.status', index=16,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='store_transfer.Transfer.process_status', index=17,
      number=18, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_transfer.Transfer.sub_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_transfer.Transfer.created_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_transfer.Transfer.updated_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='store_transfer.Transfer.transfer_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_date', full_name='store_transfer.Transfer.receiving_date', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='store_transfer.Transfer.shipping_date', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_transfer.Transfer.created_at', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_transfer.Transfer.updated_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='store_transfer.Transfer.request_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='store_transfer.Transfer.user_id', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_transfer.Transfer.created_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_transfer.Transfer.updated_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='store_transfer.Transfer.jde_order_id', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_confirm', full_name='store_transfer.Transfer.auto_confirm', index=31,
      number=32, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.Transfer.branch_type', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_transfer.Transfer.attachments', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='store_transfer.Transfer.receiving_position', index=34,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='store_transfer.Transfer.receiving_position_name', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='store_transfer.Transfer.shipping_position', index=36,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='store_transfer.Transfer.shipping_position_name', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='store_transfer.Transfer.sub_account_type', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cross_company', full_name='store_transfer.Transfer.cross_company', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='store_transfer.Transfer.total_amount', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='store_transfer.Transfer.total_sales_amount', index=41,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TRANSFER_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=765,
  serialized_end=1973,
)


_GETTRANSFERRESPONSE = _descriptor.Descriptor(
  name='GetTransferResponse',
  full_name='store_transfer.GetTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_transfer.GetTransferResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_transfer.GetTransferResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1975,
  serialized_end=2051,
)


_GETTRANSFERBYIDREQUEST = _descriptor.Descriptor(
  name='GetTransferByIDRequest',
  full_name='store_transfer.GetTransferByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.GetTransferByIDRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_transfer.GetTransferByIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2053,
  serialized_end=2111,
)


_GETTRANSFERBYIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferByIDResponse',
  full_name='store_transfer.GetTransferByIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_transfer.GetTransferByIDResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_id', full_name='store_transfer.GetTransferByIDResponse.master_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_transfer.GetTransferByIDResponse.partner_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_order_number', full_name='store_transfer.GetTransferByIDResponse.transfer_order_number', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='store_transfer.GetTransferByIDResponse.shipping_store', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='store_transfer.GetTransferByIDResponse.shipping_store_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='store_transfer.GetTransferByIDResponse.receiving_store_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.GetTransferByIDResponse.receiving_store', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_transfer.GetTransferByIDResponse.code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='store_transfer.GetTransferByIDResponse.description', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='store_transfer.GetTransferByIDResponse.reason_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='store_transfer.GetTransferByIDResponse.receiver', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_transfer.GetTransferByIDResponse.remark', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipper', full_name='store_transfer.GetTransferByIDResponse.shipper', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='store_transfer.GetTransferByIDResponse.extends', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_transfer.GetTransferByIDResponse.type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_transfer.GetTransferByIDResponse.status', index=16,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='store_transfer.GetTransferByIDResponse.process_status', index=17,
      number=18, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_transfer.GetTransferByIDResponse.sub_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_transfer.GetTransferByIDResponse.created_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_transfer.GetTransferByIDResponse.updated_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='store_transfer.GetTransferByIDResponse.transfer_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_date', full_name='store_transfer.GetTransferByIDResponse.receiving_date', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='store_transfer.GetTransferByIDResponse.shipping_date', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_transfer.GetTransferByIDResponse.created_at', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_transfer.GetTransferByIDResponse.updated_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='store_transfer.GetTransferByIDResponse.user_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_transfer.GetTransferByIDResponse.created_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_transfer.GetTransferByIDResponse.updated_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='store_transfer.GetTransferByIDResponse.jde_order_id', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_confirm', full_name='store_transfer.GetTransferByIDResponse.auto_confirm', index=30,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.GetTransferByIDResponse.branch_type', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_transfer.GetTransferByIDResponse.attachments', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='store_transfer.GetTransferByIDResponse.receiving_position', index=33,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='store_transfer.GetTransferByIDResponse.receiving_position_name', index=34,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='store_transfer.GetTransferByIDResponse.shipping_position', index=35,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='store_transfer.GetTransferByIDResponse.shipping_position_name', index=36,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='store_transfer.GetTransferByIDResponse.sub_account_type', index=37,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cross_company', full_name='store_transfer.GetTransferByIDResponse.cross_company', index=38,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='store_transfer.GetTransferByIDResponse.total_amount', index=39,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='store_transfer.GetTransferByIDResponse.total_sales_amount', index=40,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETTRANSFERBYIDRESPONSE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2114,
  serialized_end=3332,
)


_GETTRANSFERPRODUCTBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetTransferProductByBranchIDRequest',
  full_name='store_transfer.GetTransferProductByBranchIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store_transfer.GetTransferProductByBranchIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_transfer.GetTransferProductByBranchIDRequest.include_total', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_transfer.GetTransferProductByBranchIDRequest.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_transfer.GetTransferProductByBranchIDRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_transfer.GetTransferProductByBranchIDRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='store_transfer.GetTransferProductByBranchIDRequest.search_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='store_transfer.GetTransferProductByBranchIDRequest.search', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store_transfer.GetTransferProductByBranchIDRequest.category_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_transfer.GetTransferProductByBranchIDRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='store_transfer.GetTransferProductByBranchIDRequest.order_by', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_transfer.GetTransferProductByBranchIDRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3335,
  serialized_end=3565,
)


_TRANSFERPRODUCTUNIT = _descriptor.Descriptor(
  name='TransferProductUnit',
  full_name='store_transfer.TransferProductUnit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_transfer.TransferProductUnit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_transfer.TransferProductUnit.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='store_transfer.TransferProductUnit.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='store_transfer.TransferProductUnit.tax_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='store_transfer.TransferProductUnit.tax_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='store_transfer.TransferProductUnit.cost_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='store_transfer.TransferProductUnit.sales_price', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_transfer.TransferProductUnit.unit_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3568,
  serialized_end=3730,
)


_SELECTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='SelectTransferProduct',
  full_name='store_transfer.SelectTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_transfer.SelectTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_transfer.SelectTransferProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='store_transfer.SelectTransferProduct.product_category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_transfer.SelectTransferProduct.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='store_transfer.SelectTransferProduct.unit', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='store_transfer.SelectTransferProduct.barcode', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='store_transfer.SelectTransferProduct.model_name', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='store_transfer.SelectTransferProduct.real_inventory_qty', index=7,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3733,
  serialized_end=3965,
)


_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferProductByBranchIDResponse',
  full_name='store_transfer.GetTransferProductByBranchIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_transfer.GetTransferProductByBranchIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_transfer.GetTransferProductByBranchIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='store_transfer.GetTransferProductByBranchIDResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3968,
  serialized_end=4147,
)


_GETTRANSFERREGIONBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetTransferRegionByBranchIDRequest',
  full_name='store_transfer.GetTransferRegionByBranchIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store_transfer.GetTransferRegionByBranchIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_transfer.GetTransferRegionByBranchIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4149,
  serialized_end=4216,
)


_REGIONBRANCH = _descriptor.Descriptor(
  name='RegionBranch',
  full_name='store_transfer.RegionBranch',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='address', full_name='store_transfer.RegionBranch.address', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store_transfer.RegionBranch.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='store_transfer.RegionBranch.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_transfer.RegionBranch.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4218,
  serialized_end=4289,
)


_GETTRANSFERREGIONBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferRegionByBranchIDResponse',
  full_name='store_transfer.GetTransferRegionByBranchIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_transfer.GetTransferRegionByBranchIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4291,
  serialized_end=4372,
)


_GETTRANSFERPRODUCTBYTRANSFERIDREQUEST = _descriptor.Descriptor(
  name='GetTransferProductByTransferIDRequest',
  full_name='store_transfer.GetTransferProductByTransferIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_transfer.GetTransferProductByTransferIDRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_transfer.GetTransferProductByTransferIDRequest.order', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_transfer.GetTransferProductByTransferIDRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_transfer.GetTransferProductByTransferIDRequest.limit', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.GetTransferProductByTransferIDRequest.transfer_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_transfer.GetTransferProductByTransferIDRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4375,
  serialized_end=4517,
)


_TRANSFERPRODUCT = _descriptor.Descriptor(
  name='TransferProduct',
  full_name='store_transfer.TransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_transfer.TransferProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_transfer.TransferProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.TransferProduct.transfer_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_transfer.TransferProduct.unit_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_transfer.TransferProduct.unit_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='store_transfer.TransferProduct.unit_spec', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='store_transfer.TransferProduct.accounting_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store_transfer.TransferProduct.accounting_unit_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store_transfer.TransferProduct.accounting_unit_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='store_transfer.TransferProduct.accounting_unit_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_received_quantity', full_name='store_transfer.TransferProduct.accounting_received_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='store_transfer.TransferProduct.description', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='store_transfer.TransferProduct.is_confirmed', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='store_transfer.TransferProduct.item_number', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='store_transfer.TransferProduct.material_number', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_transfer.TransferProduct.product_code', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_transfer.TransferProduct.product_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_transfer.TransferProduct.quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.TransferProduct.receiving_store', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='store_transfer.TransferProduct.shipping_store', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_transfer.TransferProduct.created_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_transfer.TransferProduct.updated_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='store_transfer.TransferProduct.transfer_date', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_transfer.TransferProduct.created_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_transfer.TransferProduct.updated_at', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='store_transfer.TransferProduct.extends', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_transfer.TransferProduct.partner_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='store_transfer.TransferProduct.user_id', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_received_quantity', full_name='store_transfer.TransferProduct.confirmed_received_quantity', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_transfer.TransferProduct.created_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_transfer.TransferProduct.updated_name', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='store_transfer.TransferProduct.tax_rate', index=31,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='store_transfer.TransferProduct.tax_price', index=32,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='store_transfer.TransferProduct.cost_price', index=33,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='store_transfer.TransferProduct.amount', index=34,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='store_transfer.TransferProduct.sales_price', index=35,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='store_transfer.TransferProduct.sales_amount', index=36,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_transfer.TransferProduct.unit_rate', index=37,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='store_transfer.TransferProduct.real_inventory_qty', index=38,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4520,
  serialized_end=5485,
)


_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferProductByTransferIDResponse',
  full_name='store_transfer.GetTransferProductByTransferIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_transfer.GetTransferProductByTransferIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_transfer.GetTransferProductByTransferIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5487,
  serialized_end=5589,
)


_POSTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='PostTransferProduct',
  full_name='store_transfer.PostTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_transfer.PostTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_transfer.PostTransferProduct.unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_transfer.PostTransferProduct.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='store_transfer.PostTransferProduct.tax_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='store_transfer.PostTransferProduct.tax_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='store_transfer.PostTransferProduct.cost_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='store_transfer.PostTransferProduct.amount', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='store_transfer.PostTransferProduct.sales_price', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='store_transfer.PostTransferProduct.sales_amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5592,
  serialized_end=5784,
)


_CREATETRANSFERREQUEST = _descriptor.Descriptor(
  name='CreateTransferRequest',
  full_name='store_transfer.CreateTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='store_transfer.CreateTransferRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='store_transfer.CreateTransferRequest.shipping_store', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.CreateTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_transfer.CreateTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_transfer.CreateTransferRequest.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='store_transfer.CreateTransferRequest.shipping_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='store_transfer.CreateTransferRequest.transfer_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.CreateTransferRequest.branch_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_transfer.CreateTransferRequest.attachments', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_transfer.CreateTransferRequest.type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_transfer.CreateTransferRequest.sub_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='store_transfer.CreateTransferRequest.receiving_position', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='store_transfer.CreateTransferRequest.shipping_position', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='store_transfer.CreateTransferRequest.receiving_position_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='store_transfer.CreateTransferRequest.shipping_position_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='store_transfer.CreateTransferRequest.sub_account_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5787,
  serialized_end=6272,
)


_CREATEINNERTRANSFERREQUEST = _descriptor.Descriptor(
  name='CreateInnerTransferRequest',
  full_name='store_transfer.CreateInnerTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='store_transfer.CreateInnerTransferRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='store_transfer.CreateInnerTransferRequest.shipping_store', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.CreateInnerTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_transfer.CreateInnerTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_transfer.CreateInnerTransferRequest.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='store_transfer.CreateInnerTransferRequest.shipping_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='store_transfer.CreateInnerTransferRequest.transfer_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.CreateInnerTransferRequest.branch_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_transfer.CreateInnerTransferRequest.attachments', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_transfer.CreateInnerTransferRequest.type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_transfer.CreateInnerTransferRequest.sub_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='store_transfer.CreateInnerTransferRequest.receiving_position', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='store_transfer.CreateInnerTransferRequest.shipping_position', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='store_transfer.CreateInnerTransferRequest.receiving_position_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='store_transfer.CreateInnerTransferRequest.shipping_position_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='store_transfer.CreateInnerTransferRequest.sub_account_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6275,
  serialized_end=6765,
)


_CREATEINNERTRANSFERRESPONSE = _descriptor.Descriptor(
  name='CreateInnerTransferResponse',
  full_name='store_transfer.CreateInnerTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.CreateInnerTransferResponse.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6767,
  serialized_end=6817,
)


_UPDATETRANSFERREQUEST = _descriptor.Descriptor(
  name='UpdateTransferRequest',
  full_name='store_transfer.UpdateTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.UpdateTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_transfer.UpdateTransferRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_transfer.UpdateTransferRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='store_transfer.UpdateTransferRequest.transfer_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.UpdateTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_transfer.UpdateTransferRequest.attachments', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_transfer.UpdateTransferRequest.type', index=6,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_transfer.UpdateTransferRequest.sub_type', index=7,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6820,
  serialized_end=7060,
)


_UPDATETRANSFERRESPONSE = _descriptor.Descriptor(
  name='UpdateTransferResponse',
  full_name='store_transfer.UpdateTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store_transfer.UpdateTransferResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7062,
  serialized_end=7102,
)


_CONFIRMPOSTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='ConfirmPostTransferProduct',
  full_name='store_transfer.ConfirmPostTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_transfer.ConfirmPostTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_transfer.ConfirmPostTransferProduct.unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_received_quantity', full_name='store_transfer.ConfirmPostTransferProduct.confirmed_received_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7104,
  serialized_end=7206,
)


_CONFIRMTRANSFERREQUEST = _descriptor.Descriptor(
  name='ConfirmTransferRequest',
  full_name='store_transfer.ConfirmTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.ConfirmTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='store_transfer.ConfirmTransferRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.ConfirmTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_transfer.ConfirmTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.ConfirmTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7209,
  serialized_end=7380,
)


_DELETETRANSFERPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteTransferProductRequest',
  full_name='store_transfer.DeleteTransferProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_product_id', full_name='store_transfer.DeleteTransferProductRequest.transfer_product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7382,
  serialized_end=7441,
)


_DELETETRANSFERPRODUCTRESPONSE = _descriptor.Descriptor(
  name='DeleteTransferProductResponse',
  full_name='store_transfer.DeleteTransferProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store_transfer.DeleteTransferProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7443,
  serialized_end=7490,
)


_DELETETRANSFERREQUEST = _descriptor.Descriptor(
  name='DeleteTransferRequest',
  full_name='store_transfer.DeleteTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.DeleteTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7492,
  serialized_end=7536,
)


_DELETETRANSFERRESPONSE = _descriptor.Descriptor(
  name='DeleteTransferResponse',
  full_name='store_transfer.DeleteTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store_transfer.DeleteTransferResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7538,
  serialized_end=7578,
)


_SUBMITTRANSFERREQUEST = _descriptor.Descriptor(
  name='SubmitTransferRequest',
  full_name='store_transfer.SubmitTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.SubmitTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='store_transfer.SubmitTransferRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.SubmitTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_transfer.SubmitTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.SubmitTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7581,
  serialized_end=7744,
)


_SUBMITTRANSFERRECEIVINGREQUEST = _descriptor.Descriptor(
  name='SubmitTransferReceivingRequest',
  full_name='store_transfer.SubmitTransferReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.SubmitTransferReceivingRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='store_transfer.SubmitTransferReceivingRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.SubmitTransferReceivingRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_transfer.SubmitTransferReceivingRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7747,
  serialized_end=7898,
)


_CANCELTRANSFERREQUEST = _descriptor.Descriptor(
  name='CancelTransferRequest',
  full_name='store_transfer.CancelTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.CancelTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7900,
  serialized_end=7944,
)


_GETTRANSFERCOLLECTREQUEST = _descriptor.Descriptor(
  name='GetTransferCollectRequest',
  full_name='store_transfer.GetTransferCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='store_transfer.GetTransferCollectRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='store_transfer.GetTransferCollectRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store_transfer.GetTransferCollectRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_transfer.GetTransferCollectRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_transfer.GetTransferCollectRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_transfer.GetTransferCollectRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_transfer.GetTransferCollectRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_transfer.GetTransferCollectRequest.offset', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_transfer.GetTransferCollectRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_in', full_name='store_transfer.GetTransferCollectRequest.is_in', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_transfer.GetTransferCollectRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_transfer.GetTransferCollectRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_transfer.GetTransferCollectRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_code', full_name='store_transfer.GetTransferCollectRequest.jde_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='store_transfer.GetTransferCollectRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.GetTransferCollectRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_transfer.GetTransferCollectRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_transfer.GetTransferCollectRequest.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_transfer.GetTransferCollectRequest.sub_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cross_company', full_name='store_transfer.GetTransferCollectRequest.cross_company', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7947,
  serialized_end=8388,
)


_TRANSFERCOLLECT = _descriptor.Descriptor(
  name='TransferCollect',
  full_name='store_transfer.TransferCollect',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='store_transfer.TransferCollect.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store_transfer.TransferCollect.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store_transfer.TransferCollect.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='store_transfer.TransferCollect.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='store_transfer.TransferCollect.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='store_transfer.TransferCollect.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='store_transfer.TransferCollect.category_parent', index=6,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_transfer.TransferCollect.product_code', index=7,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_transfer.TransferCollect.product_id', index=8,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_transfer.TransferCollect.product_name', index=9,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_price', full_name='store_transfer.TransferCollect.product_price', index=10,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.TransferCollect.receiving_store', index=11,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_code', full_name='store_transfer.TransferCollect.receiving_store_code', index=12,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_us_id', full_name='store_transfer.TransferCollect.receiving_store_us_id', index=13,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='store_transfer.TransferCollect.receiving_store_name', index=14,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_transfer.TransferCollect.quantity', index=15,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='store_transfer.TransferCollect.shipping_store', index=16,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_code', full_name='store_transfer.TransferCollect.shipping_store_code', index=17,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='store_transfer.TransferCollect.shipping_store_name', index=18,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_us_id', full_name='store_transfer.TransferCollect.shipping_store_us_id', index=19,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_transfer.TransferCollect.unit_id', index=20,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_transfer.TransferCollect.unit_name', index=21,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='store_transfer.TransferCollect.product_spec', index=22,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pcdp_price', full_name='store_transfer.TransferCollect.pcdp_price', index=23,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='store_transfer.TransferCollect.receiving_position', index=24,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_code', full_name='store_transfer.TransferCollect.receiving_position_code', index=25,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='store_transfer.TransferCollect.receiving_position_name', index=26,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='store_transfer.TransferCollect.shipping_position', index=27,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_code', full_name='store_transfer.TransferCollect.shipping_position_code', index=28,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='store_transfer.TransferCollect.shipping_position_name', index=29,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cross_company', full_name='store_transfer.TransferCollect.cross_company', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pcdp_amount', full_name='store_transfer.TransferCollect.pcdp_amount', index=31,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='store_transfer.TransferCollect.currency', index=32,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8391,
  serialized_end=9270,
)


_TRANSFERCOLLECTTOTAL = _descriptor.Descriptor(
  name='TransferCollectTotal',
  full_name='store_transfer.TransferCollectTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='store_transfer.TransferCollectTotal.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='store_transfer.TransferCollectTotal.sum_accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='store_transfer.TransferCollectTotal.sum_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_pcdp_amount', full_name='store_transfer.TransferCollectTotal.sum_pcdp_amount', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9272,
  serialized_end=9389,
)


_GETTRANSFERCOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetTransferCollectResponse',
  full_name='store_transfer.GetTransferCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_transfer.GetTransferCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_transfer.GetTransferCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9392,
  serialized_end=9520,
)


_GETTRANSFERCOLLECTDETAILEDREQUEST = _descriptor.Descriptor(
  name='GetTransferCollectDetailedRequest',
  full_name='store_transfer.GetTransferCollectDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='store_transfer.GetTransferCollectDetailedRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='store_transfer.GetTransferCollectDetailedRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store_transfer.GetTransferCollectDetailedRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_transfer.GetTransferCollectDetailedRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_transfer.GetTransferCollectDetailedRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_transfer.GetTransferCollectDetailedRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_transfer.GetTransferCollectDetailedRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_transfer.GetTransferCollectDetailedRequest.offset', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_transfer.GetTransferCollectDetailedRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_in', full_name='store_transfer.GetTransferCollectDetailedRequest.is_in', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_transfer.GetTransferCollectDetailedRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_transfer.GetTransferCollectDetailedRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_transfer.GetTransferCollectDetailedRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_code', full_name='store_transfer.GetTransferCollectDetailedRequest.jde_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='store_transfer.GetTransferCollectDetailedRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_transfer.GetTransferCollectDetailedRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_transfer.GetTransferCollectDetailedRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_transfer.GetTransferCollectDetailedRequest.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_transfer.GetTransferCollectDetailedRequest.sub_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cross_company', full_name='store_transfer.GetTransferCollectDetailedRequest.cross_company', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9523,
  serialized_end=9972,
)


_TRANSFERCOLLECTDETAILED = _descriptor.Descriptor(
  name='TransferCollectDetailed',
  full_name='store_transfer.TransferCollectDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='store_transfer.TransferCollectDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store_transfer.TransferCollectDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store_transfer.TransferCollectDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='store_transfer.TransferCollectDetailed.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='store_transfer.TransferCollectDetailed.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='store_transfer.TransferCollectDetailed.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store_transfer.TransferCollectDetailed.id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_transfer.TransferCollectDetailed.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_transfer.TransferCollectDetailed.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_transfer.TransferCollectDetailed.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='store_transfer.TransferCollectDetailed.product_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_transfer.TransferCollectDetailed.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='store_transfer.TransferCollectDetailed.receiving_store', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_code', full_name='store_transfer.TransferCollectDetailed.receiving_store_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='store_transfer.TransferCollectDetailed.receiving_store_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='store_transfer.TransferCollectDetailed.shipping_store', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_code', full_name='store_transfer.TransferCollectDetailed.shipping_store_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='store_transfer.TransferCollectDetailed.shipping_store_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_transfer.TransferCollectDetailed.status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_code', full_name='store_transfer.TransferCollectDetailed.transfer_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='store_transfer.TransferCollectDetailed.transfer_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.TransferCollectDetailed.transfer_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_transfer.TransferCollectDetailed.unit_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_transfer.TransferCollectDetailed.unit_name', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_us_id', full_name='store_transfer.TransferCollectDetailed.shipping_store_us_id', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_us_id', full_name='store_transfer.TransferCollectDetailed.receiving_store_us_id', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pcdp_price', full_name='store_transfer.TransferCollectDetailed.pcdp_price', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_transfer.TransferCollectDetailed.code', index=27,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='store_transfer.TransferCollectDetailed.receiving_position', index=28,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_code', full_name='store_transfer.TransferCollectDetailed.receiving_position_code', index=29,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='store_transfer.TransferCollectDetailed.receiving_position_name', index=30,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='store_transfer.TransferCollectDetailed.shipping_position', index=31,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_code', full_name='store_transfer.TransferCollectDetailed.shipping_position_code', index=32,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='store_transfer.TransferCollectDetailed.shipping_position_name', index=33,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cross_company', full_name='store_transfer.TransferCollectDetailed.cross_company', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pcdp_amount', full_name='store_transfer.TransferCollectDetailed.pcdp_amount', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='store_transfer.TransferCollectDetailed.currency', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9975,
  serialized_end=10926,
)


_GETTRANSFERCOLLECTDETAILEDRESPONSE = _descriptor.Descriptor(
  name='GetTransferCollectDetailedResponse',
  full_name='store_transfer.GetTransferCollectDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_transfer.GetTransferCollectDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_transfer.GetTransferCollectDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10929,
  serialized_end=11073,
)


_GETTRANSFERLISTREQUEST = _descriptor.Descriptor(
  name='GetTransferListRequest',
  full_name='store_transfer.GetTransferListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_ids', full_name='store_transfer.GetTransferListRequest.transfer_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11075,
  serialized_end=11121,
)


_GETTRANSFERLISTRESPONSE = _descriptor.Descriptor(
  name='GetTransferListResponse',
  full_name='store_transfer.GetTransferListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_transfer.GetTransferListResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_transfer.GetTransferListResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11123,
  serialized_end=11209,
)


_TRANSFERDETAIL = _descriptor.Descriptor(
  name='TransferDetail',
  full_name='store_transfer.TransferDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='store_transfer.TransferDetail.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_transfer.TransferDetail.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='store_transfer.TransferDetail.shipping_store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='store_transfer.TransferDetail.receiving_store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_transfer.TransferDetail.status', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='store_transfer.TransferDetail.transfer_date', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_transfer.TransferDetail.remark', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_transfer.TransferDetail.products', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TRANSFERDETAIL_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11212,
  serialized_end=11544,
)

_GETTRANSFERREQUEST.fields_by_name['status'].enum_type = _GETTRANSFERREQUEST_STATUS
_GETTRANSFERREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERREQUEST_STATUS.containing_type = _GETTRANSFERREQUEST
_TRANSFER.fields_by_name['status'].enum_type = _TRANSFER_STATUS
_TRANSFER.fields_by_name['process_status'].enum_type = _P_STATUS
_TRANSFER.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['receiving_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER_STATUS.containing_type = _TRANSFER
_GETTRANSFERRESPONSE.fields_by_name['rows'].message_type = _TRANSFER
_GETTRANSFERBYIDRESPONSE.fields_by_name['status'].enum_type = _GETTRANSFERBYIDRESPONSE_STATUS
_GETTRANSFERBYIDRESPONSE.fields_by_name['process_status'].enum_type = _P_STATUS
_GETTRANSFERBYIDRESPONSE.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['receiving_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE_STATUS.containing_type = _GETTRANSFERBYIDRESPONSE
_SELECTTRANSFERPRODUCT.fields_by_name['unit'].message_type = _TRANSFERPRODUCTUNIT
_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE.fields_by_name['rows'].message_type = _SELECTTRANSFERPRODUCT
_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _SELECTTRANSFERPRODUCT
_GETTRANSFERREGIONBYBRANCHIDRESPONSE.fields_by_name['rows'].message_type = _REGIONBRANCH
_TRANSFERPRODUCT.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE.fields_by_name['rows'].message_type = _TRANSFERPRODUCT
_CREATETRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_CREATETRANSFERREQUEST.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATETRANSFERREQUEST.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEINNERTRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_CREATEINNERTRANSFERREQUEST.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEINNERTRANSFERREQUEST.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATETRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_UPDATETRANSFERREQUEST.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CONFIRMTRANSFERREQUEST.fields_by_name['products'].message_type = _CONFIRMPOSTTRANSFERPRODUCT
_SUBMITTRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_SUBMITTRANSFERRECEIVINGREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_GETTRANSFERCOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERCOLLECT.fields_by_name['category_parent'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_GETTRANSFERCOLLECTRESPONSE.fields_by_name['rows'].message_type = _TRANSFERCOLLECT
_GETTRANSFERCOLLECTRESPONSE.fields_by_name['total'].message_type = _TRANSFERCOLLECTTOTAL
_GETTRANSFERCOLLECTDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERCOLLECTDETAILED.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTDETAILEDRESPONSE.fields_by_name['rows'].message_type = _TRANSFERCOLLECTDETAILED
_GETTRANSFERCOLLECTDETAILEDRESPONSE.fields_by_name['total'].message_type = _TRANSFERCOLLECTTOTAL
_GETTRANSFERLISTRESPONSE.fields_by_name['rows'].message_type = _TRANSFERDETAIL
_TRANSFERDETAIL.fields_by_name['status'].enum_type = _TRANSFERDETAIL_STATUS
_TRANSFERDETAIL.fields_by_name['products'].message_type = _TRANSFERPRODUCT
_TRANSFERDETAIL_STATUS.containing_type = _TRANSFERDETAIL
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['GetTransferRequest'] = _GETTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['Transfer'] = _TRANSFER
DESCRIPTOR.message_types_by_name['GetTransferResponse'] = _GETTRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferByIDRequest'] = _GETTRANSFERBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetTransferByIDResponse'] = _GETTRANSFERBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferProductByBranchIDRequest'] = _GETTRANSFERPRODUCTBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['TransferProductUnit'] = _TRANSFERPRODUCTUNIT
DESCRIPTOR.message_types_by_name['SelectTransferProduct'] = _SELECTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['GetTransferProductByBranchIDResponse'] = _GETTRANSFERPRODUCTBYBRANCHIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferRegionByBranchIDRequest'] = _GETTRANSFERREGIONBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['RegionBranch'] = _REGIONBRANCH
DESCRIPTOR.message_types_by_name['GetTransferRegionByBranchIDResponse'] = _GETTRANSFERREGIONBYBRANCHIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferProductByTransferIDRequest'] = _GETTRANSFERPRODUCTBYTRANSFERIDREQUEST
DESCRIPTOR.message_types_by_name['TransferProduct'] = _TRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['GetTransferProductByTransferIDResponse'] = _GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE
DESCRIPTOR.message_types_by_name['PostTransferProduct'] = _POSTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['CreateTransferRequest'] = _CREATETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['CreateInnerTransferRequest'] = _CREATEINNERTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['CreateInnerTransferResponse'] = _CREATEINNERTRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['UpdateTransferRequest'] = _UPDATETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['UpdateTransferResponse'] = _UPDATETRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmPostTransferProduct'] = _CONFIRMPOSTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['ConfirmTransferRequest'] = _CONFIRMTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferProductRequest'] = _DELETETRANSFERPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferProductResponse'] = _DELETETRANSFERPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['DeleteTransferRequest'] = _DELETETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferResponse'] = _DELETETRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['SubmitTransferRequest'] = _SUBMITTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['SubmitTransferReceivingRequest'] = _SUBMITTRANSFERRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['CancelTransferRequest'] = _CANCELTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['GetTransferCollectRequest'] = _GETTRANSFERCOLLECTREQUEST
DESCRIPTOR.message_types_by_name['TransferCollect'] = _TRANSFERCOLLECT
DESCRIPTOR.message_types_by_name['TransferCollectTotal'] = _TRANSFERCOLLECTTOTAL
DESCRIPTOR.message_types_by_name['GetTransferCollectResponse'] = _GETTRANSFERCOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferCollectDetailedRequest'] = _GETTRANSFERCOLLECTDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['TransferCollectDetailed'] = _TRANSFERCOLLECTDETAILED
DESCRIPTOR.message_types_by_name['GetTransferCollectDetailedResponse'] = _GETTRANSFERCOLLECTDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferListRequest'] = _GETTRANSFERLISTREQUEST
DESCRIPTOR.message_types_by_name['GetTransferListResponse'] = _GETTRANSFERLISTRESPONSE
DESCRIPTOR.message_types_by_name['TransferDetail'] = _TRANSFERDETAIL
DESCRIPTOR.enum_types_by_name['P_STATUS'] = _P_STATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.Pong)
  ))
_sym_db.RegisterMessage(Pong)

GetTransferRequest = _reflection.GeneratedProtocolMessageType('GetTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferRequest)
  ))
_sym_db.RegisterMessage(GetTransferRequest)

Transfer = _reflection.GeneratedProtocolMessageType('Transfer', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFER,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.Transfer)
  ))
_sym_db.RegisterMessage(Transfer)

GetTransferResponse = _reflection.GeneratedProtocolMessageType('GetTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferResponse)
  ))
_sym_db.RegisterMessage(GetTransferResponse)

GetTransferByIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERBYIDREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferByIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferByIDRequest)

GetTransferByIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferByIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERBYIDRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferByIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferByIDResponse)

GetTransferProductByBranchIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferProductByBranchIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYBRANCHIDREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferProductByBranchIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferProductByBranchIDRequest)

TransferProductUnit = _reflection.GeneratedProtocolMessageType('TransferProductUnit', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERPRODUCTUNIT,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.TransferProductUnit)
  ))
_sym_db.RegisterMessage(TransferProductUnit)

SelectTransferProduct = _reflection.GeneratedProtocolMessageType('SelectTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _SELECTTRANSFERPRODUCT,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.SelectTransferProduct)
  ))
_sym_db.RegisterMessage(SelectTransferProduct)

GetTransferProductByBranchIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferProductByBranchIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYBRANCHIDRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferProductByBranchIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferProductByBranchIDResponse)

GetTransferRegionByBranchIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferRegionByBranchIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREGIONBYBRANCHIDREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferRegionByBranchIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferRegionByBranchIDRequest)

RegionBranch = _reflection.GeneratedProtocolMessageType('RegionBranch', (_message.Message,), dict(
  DESCRIPTOR = _REGIONBRANCH,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.RegionBranch)
  ))
_sym_db.RegisterMessage(RegionBranch)

GetTransferRegionByBranchIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferRegionByBranchIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREGIONBYBRANCHIDRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferRegionByBranchIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferRegionByBranchIDResponse)

GetTransferProductByTransferIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferProductByTransferIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYTRANSFERIDREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferProductByTransferIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferProductByTransferIDRequest)

TransferProduct = _reflection.GeneratedProtocolMessageType('TransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERPRODUCT,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.TransferProduct)
  ))
_sym_db.RegisterMessage(TransferProduct)

GetTransferProductByTransferIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferProductByTransferIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferProductByTransferIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferProductByTransferIDResponse)

PostTransferProduct = _reflection.GeneratedProtocolMessageType('PostTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _POSTTRANSFERPRODUCT,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.PostTransferProduct)
  ))
_sym_db.RegisterMessage(PostTransferProduct)

CreateTransferRequest = _reflection.GeneratedProtocolMessageType('CreateTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATETRANSFERREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.CreateTransferRequest)
  ))
_sym_db.RegisterMessage(CreateTransferRequest)

CreateInnerTransferRequest = _reflection.GeneratedProtocolMessageType('CreateInnerTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEINNERTRANSFERREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.CreateInnerTransferRequest)
  ))
_sym_db.RegisterMessage(CreateInnerTransferRequest)

CreateInnerTransferResponse = _reflection.GeneratedProtocolMessageType('CreateInnerTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEINNERTRANSFERRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.CreateInnerTransferResponse)
  ))
_sym_db.RegisterMessage(CreateInnerTransferResponse)

UpdateTransferRequest = _reflection.GeneratedProtocolMessageType('UpdateTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATETRANSFERREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.UpdateTransferRequest)
  ))
_sym_db.RegisterMessage(UpdateTransferRequest)

UpdateTransferResponse = _reflection.GeneratedProtocolMessageType('UpdateTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATETRANSFERRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.UpdateTransferResponse)
  ))
_sym_db.RegisterMessage(UpdateTransferResponse)

ConfirmPostTransferProduct = _reflection.GeneratedProtocolMessageType('ConfirmPostTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMPOSTTRANSFERPRODUCT,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.ConfirmPostTransferProduct)
  ))
_sym_db.RegisterMessage(ConfirmPostTransferProduct)

ConfirmTransferRequest = _reflection.GeneratedProtocolMessageType('ConfirmTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMTRANSFERREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.ConfirmTransferRequest)
  ))
_sym_db.RegisterMessage(ConfirmTransferRequest)

DeleteTransferProductRequest = _reflection.GeneratedProtocolMessageType('DeleteTransferProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERPRODUCTREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.DeleteTransferProductRequest)
  ))
_sym_db.RegisterMessage(DeleteTransferProductRequest)

DeleteTransferProductResponse = _reflection.GeneratedProtocolMessageType('DeleteTransferProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERPRODUCTRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.DeleteTransferProductResponse)
  ))
_sym_db.RegisterMessage(DeleteTransferProductResponse)

DeleteTransferRequest = _reflection.GeneratedProtocolMessageType('DeleteTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.DeleteTransferRequest)
  ))
_sym_db.RegisterMessage(DeleteTransferRequest)

DeleteTransferResponse = _reflection.GeneratedProtocolMessageType('DeleteTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.DeleteTransferResponse)
  ))
_sym_db.RegisterMessage(DeleteTransferResponse)

SubmitTransferRequest = _reflection.GeneratedProtocolMessageType('SubmitTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITTRANSFERREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.SubmitTransferRequest)
  ))
_sym_db.RegisterMessage(SubmitTransferRequest)

SubmitTransferReceivingRequest = _reflection.GeneratedProtocolMessageType('SubmitTransferReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITTRANSFERRECEIVINGREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.SubmitTransferReceivingRequest)
  ))
_sym_db.RegisterMessage(SubmitTransferReceivingRequest)

CancelTransferRequest = _reflection.GeneratedProtocolMessageType('CancelTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELTRANSFERREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.CancelTransferRequest)
  ))
_sym_db.RegisterMessage(CancelTransferRequest)

GetTransferCollectRequest = _reflection.GeneratedProtocolMessageType('GetTransferCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferCollectRequest)
  ))
_sym_db.RegisterMessage(GetTransferCollectRequest)

TransferCollect = _reflection.GeneratedProtocolMessageType('TransferCollect', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECT,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.TransferCollect)
  ))
_sym_db.RegisterMessage(TransferCollect)

TransferCollectTotal = _reflection.GeneratedProtocolMessageType('TransferCollectTotal', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECTTOTAL,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.TransferCollectTotal)
  ))
_sym_db.RegisterMessage(TransferCollectTotal)

GetTransferCollectResponse = _reflection.GeneratedProtocolMessageType('GetTransferCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferCollectResponse)
  ))
_sym_db.RegisterMessage(GetTransferCollectResponse)

GetTransferCollectDetailedRequest = _reflection.GeneratedProtocolMessageType('GetTransferCollectDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTDETAILEDREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferCollectDetailedRequest)
  ))
_sym_db.RegisterMessage(GetTransferCollectDetailedRequest)

TransferCollectDetailed = _reflection.GeneratedProtocolMessageType('TransferCollectDetailed', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECTDETAILED,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.TransferCollectDetailed)
  ))
_sym_db.RegisterMessage(TransferCollectDetailed)

GetTransferCollectDetailedResponse = _reflection.GeneratedProtocolMessageType('GetTransferCollectDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTDETAILEDRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferCollectDetailedResponse)
  ))
_sym_db.RegisterMessage(GetTransferCollectDetailedResponse)

GetTransferListRequest = _reflection.GeneratedProtocolMessageType('GetTransferListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERLISTREQUEST,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferListRequest)
  ))
_sym_db.RegisterMessage(GetTransferListRequest)

GetTransferListResponse = _reflection.GeneratedProtocolMessageType('GetTransferListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERLISTRESPONSE,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.GetTransferListResponse)
  ))
_sym_db.RegisterMessage(GetTransferListResponse)

TransferDetail = _reflection.GeneratedProtocolMessageType('TransferDetail', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERDETAIL,
  __module__ = 'store.transfer_pb2'
  # @@protoc_insertion_point(class_scope:store_transfer.TransferDetail)
  ))
_sym_db.RegisterMessage(TransferDetail)



_STORETRANSFER = _descriptor.ServiceDescriptor(
  name='StoreTransfer',
  full_name='store_transfer.StoreTransfer',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=11624,
  serialized_end=14385,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='store_transfer.StoreTransfer.Ping',
    index=0,
    containing_service=None,
    input_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    output_type=_PONG,
    serialized_options=_b('\202\323\344\223\002\007\022\005/ping'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransfer',
    full_name='store_transfer.StoreTransfer.GetTransfer',
    index=1,
    containing_service=None,
    input_type=_GETTRANSFERREQUEST,
    output_type=_GETTRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\022\035/api/v2/supply/store/transfer'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferByID',
    full_name='store_transfer.StoreTransfer.GetTransferByID',
    index=2,
    containing_service=None,
    input_type=_GETTRANSFERBYIDREQUEST,
    output_type=_GETTRANSFERBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/supply/store/transfer/{transfer_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferProductByBranchID',
    full_name='store_transfer.StoreTransfer.GetTransferProductByBranchID',
    index=3,
    containing_service=None,
    input_type=_GETTRANSFERPRODUCTBYBRANCHIDREQUEST,
    output_type=_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0226/api/v2/supply/store/transfer/store/{store_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferRegionByBranchID',
    full_name='store_transfer.StoreTransfer.GetTransferRegionByBranchID',
    index=4,
    containing_service=None,
    input_type=_GETTRANSFERREGIONBYBRANCHIDREQUEST,
    output_type=_GETTRANSFERREGIONBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0225/api/v2/supply/store/transfer/store/{store_id}/region'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferProductByTransferID',
    full_name='store_transfer.StoreTransfer.GetTransferProductByTransferID',
    index=5,
    containing_service=None,
    input_type=_GETTRANSFERPRODUCTBYTRANSFERIDREQUEST,
    output_type=_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/store/transfer/{transfer_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateTransfer',
    full_name='store_transfer.StoreTransfer.CreateTransfer',
    index=6,
    containing_service=None,
    input_type=_CREATETRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/supply/store/transfer/main:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateTransfer',
    full_name='store_transfer.StoreTransfer.UpdateTransfer',
    index=7,
    containing_service=None,
    input_type=_UPDATETRANSFERREQUEST,
    output_type=_UPDATETRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002<\0327/api/v2/supply/store/transfer/main/{transfer_id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmTransfer',
    full_name='store_transfer.StoreTransfer.ConfirmTransfer',
    index=8,
    containing_service=None,
    input_type=_CONFIRMTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002=\0328/api/v2/supply/store/transfer/main/{transfer_id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteTransferProduct',
    full_name='store_transfer.StoreTransfer.DeleteTransferProduct',
    index=9,
    containing_service=None,
    input_type=_DELETETRANSFERPRODUCTREQUEST,
    output_type=_DELETETRANSFERPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002G\032B/api/v2/supply/store/transfer/product/{transfer_product_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteTransfer',
    full_name='store_transfer.StoreTransfer.DeleteTransfer',
    index=10,
    containing_service=None,
    input_type=_DELETETRANSFERREQUEST,
    output_type=_DELETETRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002<\0327/api/v2/supply/store/transfer/main/{transfer_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitTransfer',
    full_name='store_transfer.StoreTransfer.SubmitTransfer',
    index=11,
    containing_service=None,
    input_type=_SUBMITTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002<\0327/api/v2/supply/store/transfer/main/{transfer_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelTransfer',
    full_name='store_transfer.StoreTransfer.CancelTransfer',
    index=12,
    containing_service=None,
    input_type=_CANCELTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002<\0327/api/v2/supply/store/transfer/main/{transfer_id}/cancel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferCollect',
    full_name='store_transfer.StoreTransfer.GetTransferCollect',
    index=13,
    containing_service=None,
    input_type=_GETTRANSFERCOLLECTREQUEST,
    output_type=_GETTRANSFERCOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/store/transfer/collect/report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferCollectDetailed',
    full_name='store_transfer.StoreTransfer.GetTransferCollectDetailed',
    index=14,
    containing_service=None,
    input_type=_GETTRANSFERCOLLECTDETAILEDREQUEST,
    output_type=_GETTRANSFERCOLLECTDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/store/transfer/bi/detailed'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateInnerTransfer',
    full_name='store_transfer.StoreTransfer.CreateInnerTransfer',
    index=15,
    containing_service=None,
    input_type=_CREATEINNERTRANSFERREQUEST,
    output_type=_CREATEINNERTRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v2/supply/store/inner/transfer:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferList',
    full_name='store_transfer.StoreTransfer.GetTransferList',
    index=16,
    containing_service=None,
    input_type=_GETTRANSFERLISTREQUEST,
    output_type=_GETTRANSFERLISTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\022)/api/v2/supply/store/transfer/detail/list:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STORETRANSFER)

DESCRIPTOR.services_by_name['StoreTransfer'] = _STORETRANSFER

# @@protoc_insertion_point(module_scope)
