# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: store/inventory_bi.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='store/inventory_bi.proto',
  package='store',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x18store/inventory_bi.proto\x12\x05store\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8a\x02\n\x18RealtimeInventoryRequest\x12\x12\n\nbranch_ids\x18\x01 \x03(\x04\x12\x13\n\x0bgeo_regions\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\t\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\r\n\x05order\x18\x07 \x01(\t\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\x0f\n\x07\x65xclude\x18\t \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\n \x01(\t\x12\x0b\n\x03lan\x18\x0b \x01(\t\x12\x14\n\x0cposition_ids\x18\x0c \x03(\x04\x12\x15\n\rreturn_fields\x18\r \x01(\t\"J\n\x19RealtimeInventoryResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.store.Inventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xf6\x02\n\x15\x44\x61ilyInventoryRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\n \x01(\t\x12\x0e\n\x06if_pre\x18\x0b \x01(\t\x12\x0e\n\x06if_end\x18\x0c \x01(\t\x12\x15\n\rexclude_empty\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0e \x01(\t\x12\x0b\n\x03lan\x18\x0f \x01(\t\x12\x14\n\x0cposition_ids\x18\x10 \x03(\x03\x12\x15\n\rreturn_fields\x18\x11 \x01(\t\"L\n\x16\x44\x61ilyInventoryResponse\x12#\n\x04rows\x18\x01 \x03(\x0b\x32\x15.store.DailyInventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\x8c\x0c\n\x0e\x44\x61ilyInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0f\n\x07pre_qty\x18\x0f \x01(\x01\x12\x0b\n\x03qty\x18\x10 \x01(\x01\x12\x15\n\rtrans_deposit\x18\x11 \x01(\x01\x12\x16\n\x0etrans_withdraw\x18\x12 \x01(\x01\x12\x14\n\x0cstoc_deposit\x18\x13 \x01(\x01\x12\x15\n\rstoc_withdraw\x18\x14 \x01(\x01\x12\x13\n\x0b\x61\x64j_deposit\x18\x15 \x01(\x01\x12\x14\n\x0c\x61\x64j_withdraw\x18\x16 \x01(\x01\x12\x13\n\x0brec_deposit\x18\x17 \x01(\x01\x12\x18\n\x10rec_diff_deposit\x18\x18 \x01(\x01\x12\x19\n\x11rec_diff_withdraw\x18\x19 \x01(\x01\x12\x14\n\x0cret_withdraw\x18\x1a \x01(\x01\x12\x12\n\nstart_time\x18\x1b \x01(\t\x12\x10\n\x08\x65nd_time\x18\x1c \x01(\t\x12\x15\n\rsales_deposit\x18\x1d \x01(\x01\x12\x16\n\x0esales_withdraw\x18\x1e \x01(\x01\x12\x14\n\x0cllyl_deposit\x18\x1f \x01(\x01\x12\x15\n\rllyl_withdraw\x18  \x01(\x01\x12\x15\n\rcpcrk_deposit\x18! \x01(\x01\x12\x16\n\x0e\x63pcrk_withdraw\x18\" \x01(\x01\x12\x14\n\x0crec_withdraw\x18# \x01(\x01\x12\x13\n\x0bret_deposit\x18$ \x01(\x01\x12\x12\n\nfreeze_qty\x18% \x01(\x01\x12\x18\n\x10purchase_deposit\x18& \x01(\x01\x12\x10\n\x08low_cost\x18\' \x01(\x01\x12\x18\n\x10inventory_adjust\x18( \x01(\x01\x12\x16\n\x0einventory_init\x18) \x01(\x01\x12\x13\n\x0bsub_account\x18* \x01(\t\x12\'\n\x08\x63hildren\x18+ \x03(\x0b\x32\x15.store.DailyInventory\x12\x13\n\x0bposition_id\x18, \x01(\x04\x12\x15\n\rposition_name\x18- \x01(\t\x12\x15\n\rposition_code\x18. \x01(\t\x12\x15\n\rspick_deposit\x18/ \x01(\x01\x12\x1e\n\x16material_trans_deposit\x18\x30 \x01(\x01\x12\x1f\n\x17material_trans_withdraw\x18\x31 \x01(\x01\x12\x1a\n\x12processing_deposit\x18\x32 \x01(\x01\x12\x1b\n\x13processing_withdraw\x18\x33 \x01(\x01\x12\x17\n\x0fpacking_deposit\x18\x34 \x01(\x01\x12\x18\n\x10packing_withdraw\x18\x35 \x01(\x01\x12\x14\n\x0cret_transfer\x18\x36 \x01(\x01\x12\x16\n\x0etrans_transfer\x18\x37 \x01(\x01\x12\x16\n\x0etrans_delivery\x18\x38 \x01(\x01\x12\x16\n\x0etrans_purchase\x18\x39 \x01(\x01\x12\x1c\n\x14trans_return_release\x18: \x01(\x01\x12\x1e\n\x16trans_transfer_release\x18; \x01(\x01\x12\x1e\n\x16trans_delivery_release\x18< \x01(\x01\x12\x1e\n\x16trans_purchase_release\x18= \x01(\x01\x12\x13\n\x0btrans_begin\x18> \x01(\x01\x12\x11\n\ttrans_end\x18? \x01(\x01\x12\r\n\x05price\x18@ \x01(\x01\x12\x0e\n\x06\x61mount\x18\x41 \x01(\x01\x12\x10\n\x08\x63urrency\x18\x42 \x01(\t\"\xd7\x02\n\x18QueryInventoryLogRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x03\x12\x0e\n\x06offset\x18\x07 \x01(\x03\x12.\n\nstart_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\norder_type\x18\n \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x0b \x01(\t\x12\x0b\n\x03lan\x18\x0c \x01(\t\x12\x14\n\x0cposition_ids\x18\r \x03(\x04\x12\x15\n\rreturn_fields\x18\x0e \x01(\t\"a\n\x19QueryInventoryLogResponse\x12!\n\x04rows\x18\x01 \x03(\x0b\x32\x13.store.InventoryLog\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x12\n\namount_sum\x18\x03 \x01(\x01\"\x86\x06\n\x0cInventoryLog\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x0c\n\x04spec\x18\x08 \x01(\t\x12\x12\n\norder_code\x18\t \x01(\t\x12\x12\n\norder_type\x18\n \x01(\t\x12.\n\norder_time\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x61\x63tion\x18\x0c \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12\x0b\n\x03qty\x18\x0e \x01(\x01\x12\x10\n\x08stock_id\x18\x0f \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x10 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x11 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x12 \x01(\t\x12\x16\n\x0e\x64\x65mand_unit_id\x18\x13 \x01(\x04\x12\x18\n\x10\x64\x65mand_unit_code\x18\x14 \x01(\t\x12\x18\n\x10\x64\x65mand_unit_name\x18\x15 \x01(\t\x12\x12\n\ndemand_qty\x18\x16 \x01(\x01\x12\x13\n\x0b\x63\x61tegory_id\x18\x17 \x01(\x04\x12\x15\n\rcategory_code\x18\x18 \x01(\t\x12\x15\n\rcategory_name\x18\x19 \x01(\t\x12\x14\n\x0cjde_order_id\x18\x1a \x01(\t\x12\x16\n\x0ejde_order_type\x18\x1b \x01(\t\x12\x0f\n\x07jde_mcu\x18\x1c \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x1d \x01(\t\x12\x16\n\x0esub_account_id\x18\x1e \x01(\x04\x12\x18\n\x10sub_account_code\x18\x1f \x01(\t\x12\x18\n\x10sub_account_name\x18  \x01(\t\x12\r\n\x05price\x18! \x01(\x01\x12\x0e\n\x06\x61mount\x18\" \x01(\x01\x12\x10\n\x08\x63urrency\x18# \x01(\t\"\x9a\x07\n\tInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0b\n\x03qty\x18\x0f \x01(\x01\x12\x16\n\x0e\x64\x65mand_unit_id\x18\x10 \x01(\x04\x12\x18\n\x10\x64\x65mand_unit_code\x18\x11 \x01(\t\x12\x18\n\x10\x64\x65mand_unit_name\x18\x12 \x01(\t\x12\x12\n\ndemand_qty\x18\x13 \x01(\x01\x12\x16\n\x0eproduct_status\x18\x14 \x01(\t\x12\x12\n\nfreeze_qty\x18\x15 \x01(\x01\x12\x12\n\nbroker_qty\x18\x16 \x01(\x01\x12(\n\x0c\x65xtra_detail\x18\x17 \x03(\x0b\x32\x12.store.ExtraDetail\x12\x18\n\x10purchase_unit_id\x18\x18 \x01(\x04\x12\x1a\n\x12purchase_unit_code\x18\x19 \x01(\t\x12\x1a\n\x12purchase_unit_name\x18\x1a \x01(\t\x12\x14\n\x0cpurchase_qty\x18\x1b \x01(\x01\x12\"\n\x08\x63hildren\x18\x1c \x03(\x0b\x32\x10.store.Inventory\x12\x13\n\x0bposition_id\x18\x1d \x01(\x04\x12\x15\n\rposition_name\x18\x1e \x01(\t\x12\x15\n\rposition_code\x18\x1f \x01(\t\x12\x12\n\nprimary_id\x18\" \x01(\x04\x12\x11\n\ttax_price\x18# \x01(\x01\x12\x12\n\ncost_price\x18$ \x01(\x01\x12\x12\n\nsku_amount\x18% \x01(\x01\x12\x19\n\x11\x64\x65mand_broker_qty\x18& \x01(\x01\x12\x14\n\x0c\x64\x65mand_price\x18\' \x01(\x01\x12\x16\n\x0epurchase_price\x18( \x01(\x01\x12\x0e\n\x06\x61mount\x18) \x01(\x01\x12\x10\n\x08\x63urrency\x18* \x01(\t\"\xd4\x01\n\x0b\x45xtraDetail\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0b\n\x03qty\x18\x02 \x01(\x01\x12\x10\n\x08sku_type\x18\x03 \x01(\t\x12\x16\n\x0esub_account_id\x18\x04 \x01(\x04\x12\x12\n\nproduct_id\x18\x05 \x01(\t\x12\x16\n\x0equantity_avail\x18\x06 \x01(\x01\x12\x17\n\x0fquantity_freeze\x18\x07 \x01(\x01\x12\x17\n\x0fquantity_broker\x18\x08 \x01(\x01\x12\x0e\n\x06\x61mount\x18\t \x01(\x01\x12\x12\n\ndemand_qty\x18\n \x01(\x01\"K\n\x07\x41\x63\x63ount\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\"Z\n\"RealtimeInventoryByAccountsRequest\x12 \n\x08\x61\x63\x63ounts\x18\x01 \x03(\x0b\x32\x0e.store.Account\x12\x12\n\ncheck_type\x18\x02 \x01(\t\"S\n#RealtimeInventoryByAccountsResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12\x1d\n\x04rows\x18\x02 \x03(\x0b\x32\x0f.store.Accounts\"\x89\x01\n\x08\x41\x63\x63ounts\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x0b\n\x03qty\x18\x03 \x01(\x01\x12\r\n\x05round\x18\x04 \x01(\x01\x12%\n\x0csub_accounts\x18\x05 \x03(\x0b\x32\x0f.store.Accounts\x12\x13\n\x0b\x62ranch_name\x18\x06 \x01(\t\"\xa5\x02\n\x1dSummaryInventoryByTimeRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63\x61tegory_ids\x18\x04 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x05 \x03(\x04\x12\x0e\n\x06if_pre\x18\x06 \x01(\t\x12\x0e\n\x06if_end\x18\x07 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x08 \x01(\t\x12\r\n\x05limit\x18\t \x01(\x03\x12\x0e\n\x06offset\x18\n \x01(\x03\x12\x14\n\x0cposition_ids\x18\x0b \x03(\x04\"V\n\x1eSummaryInventoryByTimeResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.store.SummaryInventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xa9\r\n\x10SummaryInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x16\n\x0eproduct_status\x18\x08 \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0f\n\x07pre_qty\x18\x0f \x01(\x01\x12\x0b\n\x03qty\x18\x10 \x01(\x01\x12\x15\n\rtrans_deposit\x18\x11 \x01(\x01\x12\x16\n\x0etrans_withdraw\x18\x12 \x01(\x01\x12\x14\n\x0cstoc_deposit\x18\x13 \x01(\x01\x12\x15\n\rstoc_withdraw\x18\x14 \x01(\x01\x12\x13\n\x0b\x61\x64j_deposit\x18\x15 \x01(\x01\x12\x14\n\x0c\x61\x64j_withdraw\x18\x16 \x01(\x01\x12\x13\n\x0brec_deposit\x18\x17 \x01(\x01\x12\x18\n\x10rec_diff_deposit\x18\x18 \x01(\x01\x12\x19\n\x11rec_diff_withdraw\x18\x19 \x01(\x01\x12\x14\n\x0cret_withdraw\x18\x1a \x01(\x01\x12.\n\nstart_time\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rsales_deposit\x18\x1d \x01(\x01\x12\x16\n\x0esales_withdraw\x18\x1e \x01(\x01\x12\x14\n\x0cllyl_deposit\x18\x1f \x01(\x01\x12\x15\n\rllyl_withdraw\x18  \x01(\x01\x12\x15\n\rcpcrk_deposit\x18! \x01(\x01\x12\x16\n\x0e\x63pcrk_withdraw\x18\" \x01(\x01\x12\x14\n\x0crec_withdraw\x18# \x01(\x01\x12\x13\n\x0bret_deposit\x18$ \x01(\x01\x12\x12\n\nfreeze_qty\x18% \x01(\x01\x12\x18\n\x10purchase_deposit\x18& \x01(\x01\x12\x10\n\x08low_cost\x18\' \x01(\x01\x12\x18\n\x10inventory_adjust\x18( \x01(\x01\x12\x16\n\x0einventory_init\x18) \x01(\x01\x12\x13\n\x0bsub_account\x18* \x01(\t\x12\'\n\x08\x63hildren\x18+ \x03(\x0b\x32\x15.store.DailyInventory\x12\x13\n\x0bposition_id\x18, \x01(\x04\x12\x15\n\rposition_name\x18- \x01(\t\x12\x15\n\rposition_code\x18. \x01(\t\x12\x15\n\rspick_deposit\x18/ \x01(\x01\x12\x1e\n\x16material_trans_deposit\x18\x30 \x01(\x01\x12\x1f\n\x17material_trans_withdraw\x18\x31 \x01(\x01\x12\x1a\n\x12processing_deposit\x18\x32 \x01(\x01\x12\x1b\n\x13processing_withdraw\x18\x33 \x01(\x01\x12\x17\n\x0fpacking_deposit\x18\x34 \x01(\x01\x12\x18\n\x10packing_withdraw\x18\x35 \x01(\x01\x12\x14\n\x0cret_transfer\x18\x36 \x01(\x01\x12\x16\n\x0etrans_transfer\x18\x37 \x01(\x01\x12\x16\n\x0etrans_delivery\x18\x38 \x01(\x01\x12\x16\n\x0etrans_purchase\x18\x39 \x01(\x01\x12\x1c\n\x14trans_return_release\x18: \x01(\x01\x12\x1e\n\x16trans_transfer_release\x18; \x01(\x01\x12\x1e\n\x16trans_delivery_release\x18< \x01(\x01\x12\x1e\n\x16trans_purchase_release\x18= \x01(\x01\x12\x13\n\x0btrans_begin\x18> \x01(\x01\x12\x11\n\ttrans_end\x18? \x01(\x01\x12\x11\n\tcategory1\x18@ \x01(\t\x12\x11\n\tcategory2\x18\x41 \x01(\t\x12\x11\n\tcategory3\x18\x42 \x01(\t\x12\x11\n\tcategory4\x18\x43 \x01(\t\x12\x11\n\tcategory5\x18\x44 \x01(\t\x12\x11\n\tcategory6\x18\x45 \x01(\t\x12\x11\n\tcategory7\x18\x46 \x01(\t\x12\x11\n\tcategory8\x18G \x01(\t\x12\x11\n\tcategory9\x18H \x01(\t\x12\x12\n\ncategory10\x18I \x01(\t\"Z\n\x16UploadInventoryRequest\x12\x10\n\x08\x66ilename\x18\x01 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\x12\x0c\n\x04\x66ile\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"\x81\x03\n\x16InventoryUploadDetails\x12\x13\n\x0b\x62ranch_code\x18\x01 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x02 \x01(\t\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x10\n\x08quantity\x18\x05 \x01(\x01\x12\x11\n\tbranch_id\x18\x06 \x01(\x04\x12\x12\n\nproduct_id\x18\x07 \x01(\x04\x12\r\n\x05\x65rror\x18\x08 \x01(\t\x12\x12\n\npartner_id\x18\t \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\n \x01(\x04\x12\x0f\n\x07row_num\x18\x0b \x01(\x01\x12\x12\n\ncreated_by\x18\x0c \x01(\x04\x12\x12\n\nupdated_by\x18\r \x01(\x04\x12\n\n\x02id\x18\x0e \x01(\x04\x12.\n\ncreated_at\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x87\x01\n\x17UploadInventoryResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x10\n\x08\x66ilename\x18\x02 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x0b\n\x03msg\x18\x04 \x01(\t\x12+\n\x04rows\x18\x05 \x03(\x0b\x32\x1d.store.InventoryUploadDetails\"H\n\tCommonReq\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12\r\n\x05limit\x18\x03 \x01(\x04\x12\x0e\n\x06offset\x18\x04 \x01(\x04\"F\n\tCommonRes\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\n\n\x02id\x18\x03 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x04 \x01(\x04\"V\n\x1aGetInventoryUploadResponse\x12\r\n\x05total\x18\x01 \x01(\x04\x12)\n\x04rows\x18\x02 \x03(\x0b\x32\x1b.store.InventoryUploadBatch\"_\n!GetInventoryUploadDetailsResponse\x12\r\n\x05total\x18\x01 \x01(\x04\x12+\n\x04rows\x18\x02 \x03(\x0b\x32\x1d.store.InventoryUploadDetails\"\xa1\x02\n\x14InventoryUploadBatch\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x12\n\nupdated_by\x18\x04 \x01(\x04\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x10\n\x08\x66ilename\x18\x06 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x07 \x01(\t\x12\x14\n\x0cupdated_name\x18\x08 \x01(\t\x12\x14\n\x0c\x63reated_name\x18\t \x01(\t\x12.\n\ncreated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp2\x89\x0b\n\x17StoreInventoryBiService\x12\x87\x01\n\x11RealtimeInventory\x12\x1f.store.RealtimeInventoryRequest\x1a .store.RealtimeInventoryResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/store/inventory/realtime\x12{\n\x0e\x44\x61ilyInventory\x12\x1c.store.DailyInventoryRequest\x1a\x1d.store.DailyInventoryResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/store/inventory/daily\x12\x82\x01\n\x11QueryInventoryLog\x12\x1f.store.QueryInventoryLogRequest\x1a .store.QueryInventoryLogResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/supply/store/inventory/log\x12\xb4\x01\n\x1bRealtimeInventoryByAccounts\x12).store.RealtimeInventoryByAccountsRequest\x1a*.store.RealtimeInventoryByAccountsResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/store/inventory/realtime/by/accounts:\x01*\x12\x98\x01\n\x16SummaryInventoryByTime\x12$.store.SummaryInventoryByTimeRequest\x1a%.store.SummaryInventoryByTimeResponse\"1\x82\xd3\xe4\x93\x02+\"&/api/v2/supply/store/inventory/summary:\x01*\x12\x82\x01\n\x0fUploadInventory\x12\x1d.store.UploadInventoryRequest\x1a\x1e.store.UploadInventoryResponse\"0\x82\xd3\xe4\x93\x02*\"%/api/v2/supply/store/inventory/import:\x01*\x12}\n\x12GetInventoryUpload\x12\x10.store.CommonReq\x1a!.store.GetInventoryUploadResponse\"2\x82\xd3\xe4\x93\x02,\x12*/api/v2/supply/store/inventory/import/list\x12\x93\x01\n\x19GetInventoryUploadDetails\x12\x10.store.CommonReq\x1a(.store.GetInventoryUploadDetailsResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/supply/store/inventory/import/{id}/details\x12{\n\x16\x41pproveInventoryUpload\x12\x10.store.CommonReq\x1a\x10.store.CommonRes\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/supply/store/inventory/import/{id}/approve:\x01*\x12y\n\x15\x43\x61ncelInventoryUpload\x12\x10.store.CommonReq\x1a\x10.store.CommonRes\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/supply/store/inventory/import/{id}/cancel:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_REALTIMEINVENTORYREQUEST = _descriptor.Descriptor(
  name='RealtimeInventoryRequest',
  full_name='store.RealtimeInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='store.RealtimeInventoryRequest.branch_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_regions', full_name='store.RealtimeInventoryRequest.geo_regions', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store.RealtimeInventoryRequest.category_ids', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='store.RealtimeInventoryRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.RealtimeInventoryRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.RealtimeInventoryRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store.RealtimeInventoryRequest.order', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store.RealtimeInventoryRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude', full_name='store.RealtimeInventoryRequest.exclude', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store.RealtimeInventoryRequest.branch_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.RealtimeInventoryRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='store.RealtimeInventoryRequest.position_ids', index=11,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.RealtimeInventoryRequest.return_fields', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=99,
  serialized_end=365,
)


_REALTIMEINVENTORYRESPONSE = _descriptor.Descriptor(
  name='RealtimeInventoryResponse',
  full_name='store.RealtimeInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.RealtimeInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store.RealtimeInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=367,
  serialized_end=441,
)


_DAILYINVENTORYREQUEST = _descriptor.Descriptor(
  name='DailyInventoryRequest',
  full_name='store.DailyInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='store.DailyInventoryRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store.DailyInventoryRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='store.DailyInventoryRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store.DailyInventoryRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store.DailyInventoryRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.DailyInventoryRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.DailyInventoryRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.DailyInventoryRequest.code', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='store.DailyInventoryRequest.action', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_pre', full_name='store.DailyInventoryRequest.if_pre', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_end', full_name='store.DailyInventoryRequest.if_end', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_empty', full_name='store.DailyInventoryRequest.exclude_empty', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store.DailyInventoryRequest.branch_type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.DailyInventoryRequest.lan', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='store.DailyInventoryRequest.position_ids', index=14,
      number=16, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.DailyInventoryRequest.return_fields', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=444,
  serialized_end=818,
)


_DAILYINVENTORYRESPONSE = _descriptor.Descriptor(
  name='DailyInventoryResponse',
  full_name='store.DailyInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.DailyInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store.DailyInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=820,
  serialized_end=896,
)


_DAILYINVENTORY = _descriptor.Descriptor(
  name='DailyInventory',
  full_name='store.DailyInventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.DailyInventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store.DailyInventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='store.DailyInventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='store.DailyInventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store.DailyInventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store.DailyInventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store.DailyInventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='store.DailyInventory.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='store.DailyInventory.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='store.DailyInventory.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='store.DailyInventory.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store.DailyInventory.accounting_unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='store.DailyInventory.accounting_unit_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store.DailyInventory.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_qty', full_name='store.DailyInventory.pre_qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='store.DailyInventory.qty', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_deposit', full_name='store.DailyInventory.trans_deposit', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_withdraw', full_name='store.DailyInventory.trans_withdraw', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_deposit', full_name='store.DailyInventory.stoc_deposit', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_withdraw', full_name='store.DailyInventory.stoc_withdraw', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_deposit', full_name='store.DailyInventory.adj_deposit', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_withdraw', full_name='store.DailyInventory.adj_withdraw', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_deposit', full_name='store.DailyInventory.rec_deposit', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_deposit', full_name='store.DailyInventory.rec_diff_deposit', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_withdraw', full_name='store.DailyInventory.rec_diff_withdraw', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_withdraw', full_name='store.DailyInventory.ret_withdraw', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='store.DailyInventory.start_time', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='store.DailyInventory.end_time', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_deposit', full_name='store.DailyInventory.sales_deposit', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_withdraw', full_name='store.DailyInventory.sales_withdraw', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_deposit', full_name='store.DailyInventory.llyl_deposit', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_withdraw', full_name='store.DailyInventory.llyl_withdraw', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_deposit', full_name='store.DailyInventory.cpcrk_deposit', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_withdraw', full_name='store.DailyInventory.cpcrk_withdraw', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_withdraw', full_name='store.DailyInventory.rec_withdraw', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_deposit', full_name='store.DailyInventory.ret_deposit', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='store.DailyInventory.freeze_qty', index=36,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deposit', full_name='store.DailyInventory.purchase_deposit', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='low_cost', full_name='store.DailyInventory.low_cost', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_adjust', full_name='store.DailyInventory.inventory_adjust', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_init', full_name='store.DailyInventory.inventory_init', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='store.DailyInventory.sub_account', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='store.DailyInventory.children', index=42,
      number=43, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='store.DailyInventory.position_id', index=43,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='store.DailyInventory.position_name', index=44,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='store.DailyInventory.position_code', index=45,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spick_deposit', full_name='store.DailyInventory.spick_deposit', index=46,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_deposit', full_name='store.DailyInventory.material_trans_deposit', index=47,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_withdraw', full_name='store.DailyInventory.material_trans_withdraw', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_deposit', full_name='store.DailyInventory.processing_deposit', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_withdraw', full_name='store.DailyInventory.processing_withdraw', index=50,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_deposit', full_name='store.DailyInventory.packing_deposit', index=51,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_withdraw', full_name='store.DailyInventory.packing_withdraw', index=52,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_transfer', full_name='store.DailyInventory.ret_transfer', index=53,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer', full_name='store.DailyInventory.trans_transfer', index=54,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery', full_name='store.DailyInventory.trans_delivery', index=55,
      number=56, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase', full_name='store.DailyInventory.trans_purchase', index=56,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_return_release', full_name='store.DailyInventory.trans_return_release', index=57,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer_release', full_name='store.DailyInventory.trans_transfer_release', index=58,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery_release', full_name='store.DailyInventory.trans_delivery_release', index=59,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase_release', full_name='store.DailyInventory.trans_purchase_release', index=60,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_begin', full_name='store.DailyInventory.trans_begin', index=61,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_end', full_name='store.DailyInventory.trans_end', index=62,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='store.DailyInventory.price', index=63,
      number=64, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='store.DailyInventory.amount', index=64,
      number=65, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='store.DailyInventory.currency', index=65,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=899,
  serialized_end=2447,
)


_QUERYINVENTORYLOGREQUEST = _descriptor.Descriptor(
  name='QueryInventoryLogRequest',
  full_name='store.QueryInventoryLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='store.QueryInventoryLogRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='store.QueryInventoryLogRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store.QueryInventoryLogRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.QueryInventoryLogRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='store.QueryInventoryLogRequest.action', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.QueryInventoryLogRequest.limit', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.QueryInventoryLogRequest.offset', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store.QueryInventoryLogRequest.start_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store.QueryInventoryLogRequest.end_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='store.QueryInventoryLogRequest.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='store.QueryInventoryLogRequest.account_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.QueryInventoryLogRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='store.QueryInventoryLogRequest.position_ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.QueryInventoryLogRequest.return_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2450,
  serialized_end=2793,
)


_QUERYINVENTORYLOGRESPONSE = _descriptor.Descriptor(
  name='QueryInventoryLogResponse',
  full_name='store.QueryInventoryLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.QueryInventoryLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store.QueryInventoryLogResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_sum', full_name='store.QueryInventoryLogResponse.amount_sum', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2795,
  serialized_end=2892,
)


_INVENTORYLOG = _descriptor.Descriptor(
  name='InventoryLog',
  full_name='store.InventoryLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.InventoryLog.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store.InventoryLog.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='store.InventoryLog.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='store.InventoryLog.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store.InventoryLog.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store.InventoryLog.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store.InventoryLog.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='store.InventoryLog.spec', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='store.InventoryLog.order_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='store.InventoryLog.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_time', full_name='store.InventoryLog.order_time', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='store.InventoryLog.action', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store.InventoryLog.status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='store.InventoryLog.qty', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stock_id', full_name='store.InventoryLog.stock_id', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store.InventoryLog.accounting_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='store.InventoryLog.accounting_unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store.InventoryLog.accounting_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_id', full_name='store.InventoryLog.demand_unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_code', full_name='store.InventoryLog.demand_unit_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_name', full_name='store.InventoryLog.demand_unit_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='store.InventoryLog.demand_qty', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='store.InventoryLog.category_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='store.InventoryLog.category_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='store.InventoryLog.category_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='store.InventoryLog.jde_order_id', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_type', full_name='store.InventoryLog.jde_order_type', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_mcu', full_name='store.InventoryLog.jde_mcu', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='store.InventoryLog.account_type', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='store.InventoryLog.sub_account_id', index=29,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_code', full_name='store.InventoryLog.sub_account_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_name', full_name='store.InventoryLog.sub_account_name', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='store.InventoryLog.price', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='store.InventoryLog.amount', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='store.InventoryLog.currency', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2895,
  serialized_end=3669,
)


_INVENTORY = _descriptor.Descriptor(
  name='Inventory',
  full_name='store.Inventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.Inventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store.Inventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='store.Inventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='store.Inventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store.Inventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store.Inventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store.Inventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='store.Inventory.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='store.Inventory.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='store.Inventory.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='store.Inventory.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store.Inventory.accounting_unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='store.Inventory.accounting_unit_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store.Inventory.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='store.Inventory.qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_id', full_name='store.Inventory.demand_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_code', full_name='store.Inventory.demand_unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_name', full_name='store.Inventory.demand_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='store.Inventory.demand_qty', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='store.Inventory.product_status', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='store.Inventory.freeze_qty', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker_qty', full_name='store.Inventory.broker_qty', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra_detail', full_name='store.Inventory.extra_detail', index=22,
      number=23, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_id', full_name='store.Inventory.purchase_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_code', full_name='store.Inventory.purchase_unit_code', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_name', full_name='store.Inventory.purchase_unit_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_qty', full_name='store.Inventory.purchase_qty', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='store.Inventory.children', index=27,
      number=28, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='store.Inventory.position_id', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='store.Inventory.position_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='store.Inventory.position_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='primary_id', full_name='store.Inventory.primary_id', index=31,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='store.Inventory.tax_price', index=32,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='store.Inventory.cost_price', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_amount', full_name='store.Inventory.sku_amount', index=34,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_broker_qty', full_name='store.Inventory.demand_broker_qty', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_price', full_name='store.Inventory.demand_price', index=36,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='store.Inventory.purchase_price', index=37,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='store.Inventory.amount', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='store.Inventory.currency', index=39,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3672,
  serialized_end=4594,
)


_EXTRADETAIL = _descriptor.Descriptor(
  name='ExtraDetail',
  full_name='store.ExtraDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='store.ExtraDetail.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='store.ExtraDetail.qty', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_type', full_name='store.ExtraDetail.sku_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='store.ExtraDetail.sub_account_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store.ExtraDetail.product_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_avail', full_name='store.ExtraDetail.quantity_avail', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_freeze', full_name='store.ExtraDetail.quantity_freeze', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_broker', full_name='store.ExtraDetail.quantity_broker', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='store.ExtraDetail.amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='store.ExtraDetail.demand_qty', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4597,
  serialized_end=4809,
)


_ACCOUNT = _descriptor.Descriptor(
  name='Account',
  full_name='store.Account',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='store.Account.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store.Account.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store.Account.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4811,
  serialized_end=4886,
)


_REALTIMEINVENTORYBYACCOUNTSREQUEST = _descriptor.Descriptor(
  name='RealtimeInventoryByAccountsRequest',
  full_name='store.RealtimeInventoryByAccountsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounts', full_name='store.RealtimeInventoryByAccountsRequest.accounts', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_type', full_name='store.RealtimeInventoryByAccountsRequest.check_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4888,
  serialized_end=4978,
)


_REALTIMEINVENTORYBYACCOUNTSRESPONSE = _descriptor.Descriptor(
  name='RealtimeInventoryByAccountsResponse',
  full_name='store.RealtimeInventoryByAccountsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='store.RealtimeInventoryByAccountsResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.RealtimeInventoryByAccountsResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4980,
  serialized_end=5063,
)


_ACCOUNTS = _descriptor.Descriptor(
  name='Accounts',
  full_name='store.Accounts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='store.Accounts.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store.Accounts.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='store.Accounts.qty', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='round', full_name='store.Accounts.round', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_accounts', full_name='store.Accounts.sub_accounts', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='store.Accounts.branch_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5066,
  serialized_end=5203,
)


_SUMMARYINVENTORYBYTIMEREQUEST = _descriptor.Descriptor(
  name='SummaryInventoryByTimeRequest',
  full_name='store.SummaryInventoryByTimeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='store.SummaryInventoryByTimeRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store.SummaryInventoryByTimeRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store.SummaryInventoryByTimeRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store.SummaryInventoryByTimeRequest.category_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='store.SummaryInventoryByTimeRequest.product_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_pre', full_name='store.SummaryInventoryByTimeRequest.if_pre', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_end', full_name='store.SummaryInventoryByTimeRequest.if_end', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store.SummaryInventoryByTimeRequest.branch_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.SummaryInventoryByTimeRequest.limit', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.SummaryInventoryByTimeRequest.offset', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='store.SummaryInventoryByTimeRequest.position_ids', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5206,
  serialized_end=5499,
)


_SUMMARYINVENTORYBYTIMERESPONSE = _descriptor.Descriptor(
  name='SummaryInventoryByTimeResponse',
  full_name='store.SummaryInventoryByTimeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.SummaryInventoryByTimeResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store.SummaryInventoryByTimeResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5501,
  serialized_end=5587,
)


_SUMMARYINVENTORY = _descriptor.Descriptor(
  name='SummaryInventory',
  full_name='store.SummaryInventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.SummaryInventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store.SummaryInventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='store.SummaryInventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='store.SummaryInventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store.SummaryInventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store.SummaryInventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store.SummaryInventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='store.SummaryInventory.product_status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='store.SummaryInventory.spec', index=8,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store.SummaryInventory.accounting_unit_id', index=9,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='store.SummaryInventory.accounting_unit_code', index=10,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store.SummaryInventory.accounting_unit_name', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_qty', full_name='store.SummaryInventory.pre_qty', index=12,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='store.SummaryInventory.qty', index=13,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_deposit', full_name='store.SummaryInventory.trans_deposit', index=14,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_withdraw', full_name='store.SummaryInventory.trans_withdraw', index=15,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_deposit', full_name='store.SummaryInventory.stoc_deposit', index=16,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_withdraw', full_name='store.SummaryInventory.stoc_withdraw', index=17,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_deposit', full_name='store.SummaryInventory.adj_deposit', index=18,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_withdraw', full_name='store.SummaryInventory.adj_withdraw', index=19,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_deposit', full_name='store.SummaryInventory.rec_deposit', index=20,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_deposit', full_name='store.SummaryInventory.rec_diff_deposit', index=21,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_withdraw', full_name='store.SummaryInventory.rec_diff_withdraw', index=22,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_withdraw', full_name='store.SummaryInventory.ret_withdraw', index=23,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='store.SummaryInventory.start_time', index=24,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='store.SummaryInventory.end_time', index=25,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_deposit', full_name='store.SummaryInventory.sales_deposit', index=26,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_withdraw', full_name='store.SummaryInventory.sales_withdraw', index=27,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_deposit', full_name='store.SummaryInventory.llyl_deposit', index=28,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_withdraw', full_name='store.SummaryInventory.llyl_withdraw', index=29,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_deposit', full_name='store.SummaryInventory.cpcrk_deposit', index=30,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_withdraw', full_name='store.SummaryInventory.cpcrk_withdraw', index=31,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_withdraw', full_name='store.SummaryInventory.rec_withdraw', index=32,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_deposit', full_name='store.SummaryInventory.ret_deposit', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='store.SummaryInventory.freeze_qty', index=34,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deposit', full_name='store.SummaryInventory.purchase_deposit', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='low_cost', full_name='store.SummaryInventory.low_cost', index=36,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_adjust', full_name='store.SummaryInventory.inventory_adjust', index=37,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_init', full_name='store.SummaryInventory.inventory_init', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='store.SummaryInventory.sub_account', index=39,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='store.SummaryInventory.children', index=40,
      number=43, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='store.SummaryInventory.position_id', index=41,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='store.SummaryInventory.position_name', index=42,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='store.SummaryInventory.position_code', index=43,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spick_deposit', full_name='store.SummaryInventory.spick_deposit', index=44,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_deposit', full_name='store.SummaryInventory.material_trans_deposit', index=45,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_withdraw', full_name='store.SummaryInventory.material_trans_withdraw', index=46,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_deposit', full_name='store.SummaryInventory.processing_deposit', index=47,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_withdraw', full_name='store.SummaryInventory.processing_withdraw', index=48,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_deposit', full_name='store.SummaryInventory.packing_deposit', index=49,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_withdraw', full_name='store.SummaryInventory.packing_withdraw', index=50,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_transfer', full_name='store.SummaryInventory.ret_transfer', index=51,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer', full_name='store.SummaryInventory.trans_transfer', index=52,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery', full_name='store.SummaryInventory.trans_delivery', index=53,
      number=56, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase', full_name='store.SummaryInventory.trans_purchase', index=54,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_return_release', full_name='store.SummaryInventory.trans_return_release', index=55,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer_release', full_name='store.SummaryInventory.trans_transfer_release', index=56,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery_release', full_name='store.SummaryInventory.trans_delivery_release', index=57,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase_release', full_name='store.SummaryInventory.trans_purchase_release', index=58,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_begin', full_name='store.SummaryInventory.trans_begin', index=59,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_end', full_name='store.SummaryInventory.trans_end', index=60,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category1', full_name='store.SummaryInventory.category1', index=61,
      number=64, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category2', full_name='store.SummaryInventory.category2', index=62,
      number=65, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category3', full_name='store.SummaryInventory.category3', index=63,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category4', full_name='store.SummaryInventory.category4', index=64,
      number=67, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category5', full_name='store.SummaryInventory.category5', index=65,
      number=68, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category6', full_name='store.SummaryInventory.category6', index=66,
      number=69, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category7', full_name='store.SummaryInventory.category7', index=67,
      number=70, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category8', full_name='store.SummaryInventory.category8', index=68,
      number=71, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category9', full_name='store.SummaryInventory.category9', index=69,
      number=72, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category10', full_name='store.SummaryInventory.category10', index=70,
      number=73, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5590,
  serialized_end=7295,
)


_UPLOADINVENTORYREQUEST = _descriptor.Descriptor(
  name='UploadInventoryRequest',
  full_name='store.UploadInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='filename', full_name='store.UploadInventoryRequest.filename', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store.UploadInventoryRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file', full_name='store.UploadInventoryRequest.file', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.UploadInventoryRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7297,
  serialized_end=7387,
)


_INVENTORYUPLOADDETAILS = _descriptor.Descriptor(
  name='InventoryUploadDetails',
  full_name='store.InventoryUploadDetails',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='store.InventoryUploadDetails.branch_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='store.InventoryUploadDetails.branch_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store.InventoryUploadDetails.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store.InventoryUploadDetails.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store.InventoryUploadDetails.quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='store.InventoryUploadDetails.branch_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store.InventoryUploadDetails.product_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='store.InventoryUploadDetails.error', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store.InventoryUploadDetails.partner_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store.InventoryUploadDetails.batch_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='row_num', full_name='store.InventoryUploadDetails.row_num', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store.InventoryUploadDetails.created_by', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store.InventoryUploadDetails.updated_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store.InventoryUploadDetails.id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store.InventoryUploadDetails.created_at', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store.InventoryUploadDetails.updated_at', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7390,
  serialized_end=7775,
)


_UPLOADINVENTORYRESPONSE = _descriptor.Descriptor(
  name='UploadInventoryResponse',
  full_name='store.UploadInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store.UploadInventoryResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filename', full_name='store.UploadInventoryResponse.filename', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store.UploadInventoryResponse.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='store.UploadInventoryResponse.msg', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.UploadInventoryResponse.rows', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7778,
  serialized_end=7913,
)


_COMMONREQ = _descriptor.Descriptor(
  name='CommonReq',
  full_name='store.CommonReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.CommonReq.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store.CommonReq.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.CommonReq.limit', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.CommonReq.offset', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7915,
  serialized_end=7987,
)


_COMMONRES = _descriptor.Descriptor(
  name='CommonRes',
  full_name='store.CommonRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store.CommonRes.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='store.CommonRes.msg', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store.CommonRes.id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store.CommonRes.batch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7989,
  serialized_end=8059,
)


_GETINVENTORYUPLOADRESPONSE = _descriptor.Descriptor(
  name='GetInventoryUploadResponse',
  full_name='store.GetInventoryUploadResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='store.GetInventoryUploadResponse.total', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.GetInventoryUploadResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8061,
  serialized_end=8147,
)


_GETINVENTORYUPLOADDETAILSRESPONSE = _descriptor.Descriptor(
  name='GetInventoryUploadDetailsResponse',
  full_name='store.GetInventoryUploadDetailsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='store.GetInventoryUploadDetailsResponse.total', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.GetInventoryUploadDetailsResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8149,
  serialized_end=8244,
)


_INVENTORYUPLOADBATCH = _descriptor.Descriptor(
  name='InventoryUploadBatch',
  full_name='store.InventoryUploadBatch',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.InventoryUploadBatch.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store.InventoryUploadBatch.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store.InventoryUploadBatch.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store.InventoryUploadBatch.updated_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store.InventoryUploadBatch.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filename', full_name='store.InventoryUploadBatch.filename', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store.InventoryUploadBatch.branch_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store.InventoryUploadBatch.updated_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store.InventoryUploadBatch.created_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store.InventoryUploadBatch.created_at', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store.InventoryUploadBatch.updated_at', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8247,
  serialized_end=8536,
)

_REALTIMEINVENTORYRESPONSE.fields_by_name['rows'].message_type = _INVENTORY
_DAILYINVENTORYREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYRESPONSE.fields_by_name['rows'].message_type = _DAILYINVENTORY
_DAILYINVENTORY.fields_by_name['children'].message_type = _DAILYINVENTORY
_QUERYINVENTORYLOGREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGRESPONSE.fields_by_name['rows'].message_type = _INVENTORYLOG
_INVENTORYLOG.fields_by_name['order_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_INVENTORY.fields_by_name['extra_detail'].message_type = _EXTRADETAIL
_INVENTORY.fields_by_name['children'].message_type = _INVENTORY
_REALTIMEINVENTORYBYACCOUNTSREQUEST.fields_by_name['accounts'].message_type = _ACCOUNT
_REALTIMEINVENTORYBYACCOUNTSRESPONSE.fields_by_name['rows'].message_type = _ACCOUNTS
_ACCOUNTS.fields_by_name['sub_accounts'].message_type = _ACCOUNTS
_SUMMARYINVENTORYBYTIMEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMMARYINVENTORYBYTIMEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMMARYINVENTORYBYTIMERESPONSE.fields_by_name['rows'].message_type = _SUMMARYINVENTORY
_SUMMARYINVENTORY.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMMARYINVENTORY.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMMARYINVENTORY.fields_by_name['children'].message_type = _DAILYINVENTORY
_INVENTORYUPLOADDETAILS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_INVENTORYUPLOADDETAILS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPLOADINVENTORYRESPONSE.fields_by_name['rows'].message_type = _INVENTORYUPLOADDETAILS
_GETINVENTORYUPLOADRESPONSE.fields_by_name['rows'].message_type = _INVENTORYUPLOADBATCH
_GETINVENTORYUPLOADDETAILSRESPONSE.fields_by_name['rows'].message_type = _INVENTORYUPLOADDETAILS
_INVENTORYUPLOADBATCH.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_INVENTORYUPLOADBATCH.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['RealtimeInventoryRequest'] = _REALTIMEINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['RealtimeInventoryResponse'] = _REALTIMEINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['DailyInventoryRequest'] = _DAILYINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['DailyInventoryResponse'] = _DAILYINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['DailyInventory'] = _DAILYINVENTORY
DESCRIPTOR.message_types_by_name['QueryInventoryLogRequest'] = _QUERYINVENTORYLOGREQUEST
DESCRIPTOR.message_types_by_name['QueryInventoryLogResponse'] = _QUERYINVENTORYLOGRESPONSE
DESCRIPTOR.message_types_by_name['InventoryLog'] = _INVENTORYLOG
DESCRIPTOR.message_types_by_name['Inventory'] = _INVENTORY
DESCRIPTOR.message_types_by_name['ExtraDetail'] = _EXTRADETAIL
DESCRIPTOR.message_types_by_name['Account'] = _ACCOUNT
DESCRIPTOR.message_types_by_name['RealtimeInventoryByAccountsRequest'] = _REALTIMEINVENTORYBYACCOUNTSREQUEST
DESCRIPTOR.message_types_by_name['RealtimeInventoryByAccountsResponse'] = _REALTIMEINVENTORYBYACCOUNTSRESPONSE
DESCRIPTOR.message_types_by_name['Accounts'] = _ACCOUNTS
DESCRIPTOR.message_types_by_name['SummaryInventoryByTimeRequest'] = _SUMMARYINVENTORYBYTIMEREQUEST
DESCRIPTOR.message_types_by_name['SummaryInventoryByTimeResponse'] = _SUMMARYINVENTORYBYTIMERESPONSE
DESCRIPTOR.message_types_by_name['SummaryInventory'] = _SUMMARYINVENTORY
DESCRIPTOR.message_types_by_name['UploadInventoryRequest'] = _UPLOADINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['InventoryUploadDetails'] = _INVENTORYUPLOADDETAILS
DESCRIPTOR.message_types_by_name['UploadInventoryResponse'] = _UPLOADINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['CommonReq'] = _COMMONREQ
DESCRIPTOR.message_types_by_name['CommonRes'] = _COMMONRES
DESCRIPTOR.message_types_by_name['GetInventoryUploadResponse'] = _GETINVENTORYUPLOADRESPONSE
DESCRIPTOR.message_types_by_name['GetInventoryUploadDetailsResponse'] = _GETINVENTORYUPLOADDETAILSRESPONSE
DESCRIPTOR.message_types_by_name['InventoryUploadBatch'] = _INVENTORYUPLOADBATCH
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RealtimeInventoryRequest = _reflection.GeneratedProtocolMessageType('RealtimeInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYREQUEST,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.RealtimeInventoryRequest)
  ))
_sym_db.RegisterMessage(RealtimeInventoryRequest)

RealtimeInventoryResponse = _reflection.GeneratedProtocolMessageType('RealtimeInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYRESPONSE,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.RealtimeInventoryResponse)
  ))
_sym_db.RegisterMessage(RealtimeInventoryResponse)

DailyInventoryRequest = _reflection.GeneratedProtocolMessageType('DailyInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORYREQUEST,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.DailyInventoryRequest)
  ))
_sym_db.RegisterMessage(DailyInventoryRequest)

DailyInventoryResponse = _reflection.GeneratedProtocolMessageType('DailyInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORYRESPONSE,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.DailyInventoryResponse)
  ))
_sym_db.RegisterMessage(DailyInventoryResponse)

DailyInventory = _reflection.GeneratedProtocolMessageType('DailyInventory', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORY,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.DailyInventory)
  ))
_sym_db.RegisterMessage(DailyInventory)

QueryInventoryLogRequest = _reflection.GeneratedProtocolMessageType('QueryInventoryLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGREQUEST,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.QueryInventoryLogRequest)
  ))
_sym_db.RegisterMessage(QueryInventoryLogRequest)

QueryInventoryLogResponse = _reflection.GeneratedProtocolMessageType('QueryInventoryLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGRESPONSE,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.QueryInventoryLogResponse)
  ))
_sym_db.RegisterMessage(QueryInventoryLogResponse)

InventoryLog = _reflection.GeneratedProtocolMessageType('InventoryLog', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORYLOG,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.InventoryLog)
  ))
_sym_db.RegisterMessage(InventoryLog)

Inventory = _reflection.GeneratedProtocolMessageType('Inventory', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORY,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.Inventory)
  ))
_sym_db.RegisterMessage(Inventory)

ExtraDetail = _reflection.GeneratedProtocolMessageType('ExtraDetail', (_message.Message,), dict(
  DESCRIPTOR = _EXTRADETAIL,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.ExtraDetail)
  ))
_sym_db.RegisterMessage(ExtraDetail)

Account = _reflection.GeneratedProtocolMessageType('Account', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNT,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.Account)
  ))
_sym_db.RegisterMessage(Account)

RealtimeInventoryByAccountsRequest = _reflection.GeneratedProtocolMessageType('RealtimeInventoryByAccountsRequest', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYBYACCOUNTSREQUEST,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.RealtimeInventoryByAccountsRequest)
  ))
_sym_db.RegisterMessage(RealtimeInventoryByAccountsRequest)

RealtimeInventoryByAccountsResponse = _reflection.GeneratedProtocolMessageType('RealtimeInventoryByAccountsResponse', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYBYACCOUNTSRESPONSE,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.RealtimeInventoryByAccountsResponse)
  ))
_sym_db.RegisterMessage(RealtimeInventoryByAccountsResponse)

Accounts = _reflection.GeneratedProtocolMessageType('Accounts', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNTS,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.Accounts)
  ))
_sym_db.RegisterMessage(Accounts)

SummaryInventoryByTimeRequest = _reflection.GeneratedProtocolMessageType('SummaryInventoryByTimeRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUMMARYINVENTORYBYTIMEREQUEST,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.SummaryInventoryByTimeRequest)
  ))
_sym_db.RegisterMessage(SummaryInventoryByTimeRequest)

SummaryInventoryByTimeResponse = _reflection.GeneratedProtocolMessageType('SummaryInventoryByTimeResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUMMARYINVENTORYBYTIMERESPONSE,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.SummaryInventoryByTimeResponse)
  ))
_sym_db.RegisterMessage(SummaryInventoryByTimeResponse)

SummaryInventory = _reflection.GeneratedProtocolMessageType('SummaryInventory', (_message.Message,), dict(
  DESCRIPTOR = _SUMMARYINVENTORY,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.SummaryInventory)
  ))
_sym_db.RegisterMessage(SummaryInventory)

UploadInventoryRequest = _reflection.GeneratedProtocolMessageType('UploadInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADINVENTORYREQUEST,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.UploadInventoryRequest)
  ))
_sym_db.RegisterMessage(UploadInventoryRequest)

InventoryUploadDetails = _reflection.GeneratedProtocolMessageType('InventoryUploadDetails', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORYUPLOADDETAILS,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.InventoryUploadDetails)
  ))
_sym_db.RegisterMessage(InventoryUploadDetails)

UploadInventoryResponse = _reflection.GeneratedProtocolMessageType('UploadInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADINVENTORYRESPONSE,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.UploadInventoryResponse)
  ))
_sym_db.RegisterMessage(UploadInventoryResponse)

CommonReq = _reflection.GeneratedProtocolMessageType('CommonReq', (_message.Message,), dict(
  DESCRIPTOR = _COMMONREQ,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.CommonReq)
  ))
_sym_db.RegisterMessage(CommonReq)

CommonRes = _reflection.GeneratedProtocolMessageType('CommonRes', (_message.Message,), dict(
  DESCRIPTOR = _COMMONRES,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.CommonRes)
  ))
_sym_db.RegisterMessage(CommonRes)

GetInventoryUploadResponse = _reflection.GeneratedProtocolMessageType('GetInventoryUploadResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETINVENTORYUPLOADRESPONSE,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.GetInventoryUploadResponse)
  ))
_sym_db.RegisterMessage(GetInventoryUploadResponse)

GetInventoryUploadDetailsResponse = _reflection.GeneratedProtocolMessageType('GetInventoryUploadDetailsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETINVENTORYUPLOADDETAILSRESPONSE,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.GetInventoryUploadDetailsResponse)
  ))
_sym_db.RegisterMessage(GetInventoryUploadDetailsResponse)

InventoryUploadBatch = _reflection.GeneratedProtocolMessageType('InventoryUploadBatch', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORYUPLOADBATCH,
  __module__ = 'store.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:store.InventoryUploadBatch)
  ))
_sym_db.RegisterMessage(InventoryUploadBatch)



_STOREINVENTORYBISERVICE = _descriptor.ServiceDescriptor(
  name='StoreInventoryBiService',
  full_name='store.StoreInventoryBiService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=8539,
  serialized_end=9956,
  methods=[
  _descriptor.MethodDescriptor(
    name='RealtimeInventory',
    full_name='store.StoreInventoryBiService.RealtimeInventory',
    index=0,
    containing_service=None,
    input_type=_REALTIMEINVENTORYREQUEST,
    output_type=_REALTIMEINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/store/inventory/realtime'),
  ),
  _descriptor.MethodDescriptor(
    name='DailyInventory',
    full_name='store.StoreInventoryBiService.DailyInventory',
    index=1,
    containing_service=None,
    input_type=_DAILYINVENTORYREQUEST,
    output_type=_DAILYINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/store/inventory/daily'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryInventoryLog',
    full_name='store.StoreInventoryBiService.QueryInventoryLog',
    index=2,
    containing_service=None,
    input_type=_QUERYINVENTORYLOGREQUEST,
    output_type=_QUERYINVENTORYLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/supply/store/inventory/log'),
  ),
  _descriptor.MethodDescriptor(
    name='RealtimeInventoryByAccounts',
    full_name='store.StoreInventoryBiService.RealtimeInventoryByAccounts',
    index=3,
    containing_service=None,
    input_type=_REALTIMEINVENTORYBYACCOUNTSREQUEST,
    output_type=_REALTIMEINVENTORYBYACCOUNTSRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/store/inventory/realtime/by/accounts:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SummaryInventoryByTime',
    full_name='store.StoreInventoryBiService.SummaryInventoryByTime',
    index=4,
    containing_service=None,
    input_type=_SUMMARYINVENTORYBYTIMEREQUEST,
    output_type=_SUMMARYINVENTORYBYTIMERESPONSE,
    serialized_options=_b('\202\323\344\223\002+\"&/api/v2/supply/store/inventory/summary:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UploadInventory',
    full_name='store.StoreInventoryBiService.UploadInventory',
    index=5,
    containing_service=None,
    input_type=_UPLOADINVENTORYREQUEST,
    output_type=_UPLOADINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\"%/api/v2/supply/store/inventory/import:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetInventoryUpload',
    full_name='store.StoreInventoryBiService.GetInventoryUpload',
    index=6,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_GETINVENTORYUPLOADRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\022*/api/v2/supply/store/inventory/import/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetInventoryUploadDetails',
    full_name='store.StoreInventoryBiService.GetInventoryUploadDetails',
    index=7,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_GETINVENTORYUPLOADDETAILSRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/supply/store/inventory/import/{id}/details'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveInventoryUpload',
    full_name='store.StoreInventoryBiService.ApproveInventoryUpload',
    index=8,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_COMMONRES,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/supply/store/inventory/import/{id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelInventoryUpload',
    full_name='store.StoreInventoryBiService.CancelInventoryUpload',
    index=9,
    containing_service=None,
    input_type=_COMMONREQ,
    output_type=_COMMONRES,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/supply/store/inventory/import/{id}/cancel:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STOREINVENTORYBISERVICE)

DESCRIPTOR.services_by_name['StoreInventoryBiService'] = _STOREINVENTORYBISERVICE

# @@protoc_insertion_point(module_scope)
