{"swagger": "2.0", "info": {"title": "supply.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/bi/order/list": {"get": {"summary": "要货单报表", "operationId": "QueryBiOrderReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyQueryOrderBiReportResponse"}}}, "parameters": [{"name": "start_demand_date", "description": "开始订货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_demand_date", "description": "结束订货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "start_arrival_date", "description": "开始到货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_arrival_date", "description": "结束到货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "classification", "description": "查询分类（汇总:sum, 明细:detail）.", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "订单分类(门市订货单:SD, 门市紧急订货单:HD, 主配单:MD).", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "description": "商品id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "category_ids", "description": "商品类别.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "store_ids", "description": "门店id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "description": "每页数量.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "偏移量.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "havi_code", "in": "query", "required": false, "type": "string"}, {"name": "jde_order_id", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/delivery/list": {"get": {"summary": "查询首配信息", "operationId": "ListFirstDelivery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyListFirstDeliveryResponse"}}}, "parameters": [{"name": "store_code", "description": "门店编号.", "in": "query", "required": false, "type": "string"}, {"name": "product_code", "description": "商品编号.", "in": "query", "required": false, "type": "string"}, {"name": "start_order_date", "description": "配送开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_order_date", "description": "配送结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "description": "每页数量.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "偏移量.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "order", "description": "排序.", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand": {"get": {"summary": "查询门店订货单", "operationId": "ListDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyQueryDemandResponse"}}}, "parameters": [{"name": "store_ids", "description": "门店ids（,分割）.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "has_product", "description": "是否包含商品('0'是不含，'1'是含, 不传查询所有, 主意这里是字符0, 1).", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "门市订货(SD)、紧急订货(HD)、主配订货(MD).", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段).", "in": "query", "required": false, "type": "string"}, {"name": "store_type", "description": "门店类型(热麦:MIX, 如果要查询非的字段使用neq_开头，例如:[\"neq_MIX\", \"TEA\"], 不传查询所有).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "start_date", "description": "订货日期起始时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "订货日期结束时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "description": "订单状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "offset", "description": "偏移(默认0).", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "description": "查询的每页数量.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "codes", "description": "订货单号.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "is_plan", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_adjust", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "send_type", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "havi_code", "in": "query", "required": false, "type": "string"}, {"name": "pre_havi_code", "in": "query", "required": false, "type": "string"}, {"name": "bus_type", "in": "query", "required": false, "type": "string"}, {"name": "plan_name", "description": "计划名称模糊查询.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand/adjust/create": {"post": {"summary": "创建订货调整单", "operationId": "CreateDemandAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyCreateDemandAdjustResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyCreateDemandAdjustRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/adjust/product/{store_id}": {"get": {"summary": "根据门店id查询可调整订货商品", "operationId": "GetDemandAdjustProductByStoreId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetDemandAdjustProductByStoreIdResponse"}}}, "parameters": [{"name": "store_id", "description": "门店id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "type", "description": "订货类型:门市订货(SD)、紧急订货(HD)、主配订货(MD).", "in": "query", "required": false, "type": "string"}, {"name": "order_date", "description": "订货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "distribution_type", "description": "物流模式: 配送-NMD, 直送-PUR.", "in": "query", "required": false, "type": "string"}, {"name": "vendor_id", "description": "配送中心.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand/check_handle": {"put": {"summary": "夜间检查未发送订货单，生成门市要货单", "operationId": "CheckAndHandleDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyBizdt"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/delete/{demand_id}/product": {"put": {"summary": "删除紧急订货单或者主配单订货商品", "operationId": "DeleteDemandProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "demand_id", "description": "订货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyDeleteDemandProductRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/export": {"get": {"summary": "导出订单商品", "operationId": "ExportDemandMain", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyExportDemandMainResponse"}}}, "parameters": [{"name": "type", "description": "门市订货(SD)、紧急订货(HD)、主配订货(MD).", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段).", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "description": "起始日期2018-12-29.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "结束日期2018-12-29.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "description": "单据状态.", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand/main/order": {"get": {"summary": "查询要货订单", "operationId": "ListDemandOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyListDemandOrderResponse"}}}, "parameters": [{"name": "start_date", "description": "起始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "start_arrival_date", "description": "起始到货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_arrival_date", "description": "结束到货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "description": "要货单状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "store_ids", "description": "门店id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "demand_code", "description": "订货单号.", "in": "query", "required": false, "type": "string"}, {"name": "order_code", "description": "要货单号.", "in": "query", "required": false, "type": "string"}, {"name": "storage_type", "description": "储藏类型.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "demand_type", "description": "订货类型(SD, HD, MD).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "store_type", "description": "门店类型(热麦:MIX, 如果要查询非的字段使用neq_开头，例如:[\"neq_MIX\", \"TEA\"], 不传查询所有).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "trans_mode", "description": "物流状态(状态-INITED:创建, CHECKED:检核完成, CHECKING:正在检核, SENT:发送成功, SEND_FAILED:发送失败, CANCELLED:缺货取消).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "offset", "description": "偏移.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "description": "每页数量.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "is_received", "description": "是否收货('0':未，'1':已收货).", "in": "query", "required": false, "type": "string"}, {"name": "jde_order_id", "description": "jde单号.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "distribution_type", "description": "物流模式(配送:NMD, 直送:PUR).", "in": "query", "required": false, "type": "string"}, {"name": "distribute_bys", "description": "配送中心/供应商.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "is_adjust", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "send_type", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "havi_code", "in": "query", "required": false, "type": "string"}, {"name": "pre_havi_code", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand/main/product": {"post": {"summary": "创建商品主配单", "operationId": "CreateProductMain", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyIdRequest"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyProductMainRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/main/store": {"post": {"summary": "创建门店紧急订货订单和门店主配单", "operationId": "C<PERSON><PERSON><PERSON>dMain", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyIdRequest"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyDemandMainRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/main/{id}/delete": {"put": {"summary": "删除订货单(门市订货单不能删除)", "operationId": "DeleteDemandMain", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/demand/main/{id}/reset": {"put": {"summary": "重置门店订单", "operationId": "ResetDemandMain", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/demand/master/upload": {"post": {"summary": "导入主配单", "operationId": "UploadDemandMaster", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyUploadDemandMasterResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyUploadDemandMasterRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/order/cancel": {"put": {"operationId": "CancelDemandOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyCancelDemandOrderResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyCancelDemandOrderRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/order/{id}/detail": {"get": {"summary": "获取要货单详细", "operationId": "GetDemandOrderDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetDemandOrderDetailResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand/order/{id}/product": {"get": {"summary": "获取要货单商品详细", "operationId": "GetDemandOrderProductDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetDemandOrderProductDetailResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand/product": {"put": {"summary": "门店订货", "operationId": "UpdateDemandProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyUpdateDemandProductRequest"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyUpdateDemandProductRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/status/{id}": {"put": {"summary": "修改订单状态统一入口", "operationId": "ChangeDemandStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyChangeDemandStatusRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/store/batch": {"post": {"summary": "夜间生成门市订货单批次", "operationId": "ActDemandBatchStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyDemandBatch"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyActDemandBatchRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/valid/store": {"post": {"summary": "根据商品id获取可订货门店(此接口会很慢)", "operationId": "GetValidStoreByProductId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetValidStoreByProductIdResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyGetValidStoreByProductIdRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/{id}": {"get": {"summary": "获取门市订货单详细", "operationId": "GetDemandDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyDemandEntity"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand/{id}/distribution": {"get": {"summary": "根据订单id获取订货单商品对应的仓库", "operationId": "GetDistributionByDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetDistributionByDemandResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand/{id}/info": {"post": {"summary": "修改订货单信息", "operationId": "UpdateDemandInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyUpdateDemandInfoRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand/{id}/product": {"get": {"summary": "获取订货商品信息", "operationId": "GetDemandProductDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyQueryDemandProductResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand_master_upload": {"get": {"summary": "查询导入记录", "operationId": "GetDemandMasterUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetDemandMasterUploadResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "file_name", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "file_type", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/demand_master_upload/{batch_id}": {"get": {"summary": "查询导入明细", "operationId": "GetDemandMasterUploadByBatchId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetDemandMasterUploadByBatchIdResponse"}}}, "parameters": [{"name": "batch_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}], "tags": ["supply"]}, "put": {"summary": "审核主配导入记录开始干活", "operationId": "ApproveDemandMasterUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyApproveDemandMasterUploadResponse"}}}, "parameters": [{"name": "batch_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/demand_master_upload/{batch_id}/cancel": {"put": {"summary": "取消主配导入", "operationId": "CancelDemandMasterUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyCancelDemandMasterUploadResponse"}}}, "parameters": [{"name": "batch_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/demand_suggest/forecast": {"post": {"summary": "建议订货量查询", "operationId": "GetProductSaleForecast", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetProductSaleForecastResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyGetProductSaleForecastRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand_suggest/forecast/confirm": {"post": {"summary": "确认面包和茶饮的一周内的预测销售量", "operationId": "ConfirmProductSaleForecast", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyConfirmProductSaleForecastResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyConfirmProductSaleForecastRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand_suggest/forecast/products": {"post": {"summary": "查询面包和茶饮的一周内的预测销售量", "operationId": "ListProductSaleForecast", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyListProductSaleForecastResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyListProductSaleForecastRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demand_suggest/forecast/stores": {"post": {"operationId": "ListForecastStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyListForecastStoreResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyListForecastStoreRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demo/method": {"post": {"summary": "DEMOMETHOD", "operationId": "TestMethod", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyNote"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyPoint"}}], "tags": ["supply"]}}, "/api/v2/supply/demo/{store_id}/order_product": {"get": {"summary": "熊猫不走demo环境演示api", "operationId": "DemoGetStoreProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyDemoGetStoreProductResponse"}}}, "parameters": [{"name": "store_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/demo/{store_id}/order_save": {"put": {"summary": "熊猫不走demo环境保存订货单", "operationId": "DemoSaveStoreProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyDemoSaveStoreProductResponse"}}}, "parameters": [{"name": "store_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyDemoSaveStoreProductRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/demo/{store_id}/order_split": {"put": {"summary": "熊猫不走demo环境提交订货成品", "operationId": "DemoSplitStoreProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyDemoSplitStoreProductResponse"}}}, "parameters": [{"name": "store_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyDemoSplitStoreProductRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/order/collection/upload": {"post": {"summary": "调整单导入", "operationId": "UploadOrderCollection", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyUploadOrderCollectionResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyUploadOrderCollectionRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/order/vacancy": {"put": {"summary": "创建缺货检核单", "operationId": "CreateVacancyOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyIdRequest"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyCreateVacancyOrderRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/order/vacancy/list": {"get": {"summary": "查询缺货检核单", "operationId": "ListVacancy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyListVacancyResponse"}}}, "parameters": [{"name": "start_date", "description": "起始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "offset", "description": "偏移.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "limit", "description": "每页数量.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "order", "description": "asc|desc.", "in": "query", "required": false, "type": "string"}, {"name": "status", "description": "状态(INITED:新建, CHECKING:正在检核, CHECKED:检核完成, EXECUTING:正在执行,SUCCESS:执行成功).", "in": "query", "required": false, "type": "string"}, {"name": "distrcenter_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["supply"]}}, "/api/v2/supply/order_collection_upload": {"get": {"summary": "查询调整单导入记录", "operationId": "GetOrderCollectionUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetOrderCollectionUploadResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "file_name", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "file_type", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/order_collection_upload/{batch_id}": {"get": {"summary": "查询调整单导入明细", "operationId": "GetOrderCollectionUploadByBatchId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetOrderCollectionUploadByBatchIdResponse"}}}, "parameters": [{"name": "batch_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}], "tags": ["supply"]}, "put": {"summary": "审核调整单导入记录开始干活", "operationId": "ApproveOrderCollectionUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyApproveOrderCollectionUploadResponse"}}}, "parameters": [{"name": "batch_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/order_collection_upload/{batch_id}/cancel": {"put": {"summary": "取消调整单导入导入", "operationId": "CancelOrderCollectionUpload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyCancelOrderCollectionUploadResponse"}}}, "parameters": [{"name": "batch_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/ping": {"get": {"operationId": "<PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyPong"}}}, "tags": ["supply"]}}, "/api/v2/supply/returns/orders/list": {"post": {"summary": "退货单报表", "operationId": "QueryReturnOrders", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyQueryReturnOrdersResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyQueryReturnOrdersRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/smart_demand_rule": {"get": {"summary": "获取智能订货配置列表加明细", "operationId": "GetSmartDemandRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetSmartDemandRuleResponse"}}}, "parameters": [{"name": "id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "is_detail", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}, "post": {"summary": "智能订货配置 smart-demand", "operationId": "CreateSmartDemandRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyCreateSmartDemandRuleResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyCreateSmartDemandRuleRequest"}}], "tags": ["supply"]}, "put": {"summary": "更新智能订货配置 smart-demand", "operationId": "PutSmartDemandRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyPutSmartDemandRuleResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyPutSmartDemandRuleRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/smart_demand_rule_status": {"put": {"summary": "状态智能订货配置 smart-demand", "operationId": "ActionSmartDemandRule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyActionSmartDemandRuleResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyActionSmartDemandRuleRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/store/{id}/valid/product": {"get": {"summary": "根据门店id查询可订货商品", "operationId": "GetValidProductByStoreId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyQueryDemandProductResponse"}}}, "parameters": [{"name": "id", "description": "门店id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "type", "description": "订货类型:门市订货(SD)、紧急订货(HD)、主配订货(MD).", "in": "query", "required": false, "type": "string"}, {"name": "order_date", "description": "订货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "distribution_type", "description": "物流模式: 配送-NMD, 直送-PUR.", "in": "query", "required": false, "type": "string"}, {"name": "vendor_id", "description": "配送中心.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "category_ids", "description": "商品分类.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/vacancy/execute/{id}": {"put": {"summary": "执行发送缺货检核成功的单子", "operationId": "ExecuteVacancy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/vacancy/order/{id}/product": {"get": {"summary": "查看要货单缺货商品记录", "operationId": "GetVacancyOrderProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetVacancyOrderProductResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/vacancy/re_check/{id}": {"put": {"summary": "重新检核", "operationId": "ReVacancyQuantity", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/vacancy/remark/{vacancy_id}": {"put": {"summary": "修改缺货检核单备注", "operationId": "UpdateVacancyRemark", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "vacancy_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyUpdateVacancyRemarkRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/vacancy/update/{vacancy_id}/product": {"post": {"summary": "保存缺货检核配送数量", "operationId": "UpdateVacancyQuantity", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyResponse"}}}, "parameters": [{"name": "vacancy_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/supplyUpdateVacancyQuantityRequest"}}], "tags": ["supply"]}}, "/api/v2/supply/vacancy/{id}/detail": {"get": {"summary": "查看缺货检核单详细信息", "operationId": "GetVacancyDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyVacancy"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/vacancy/{id}/product": {"get": {"summary": "查看缺货检核单缺货商品", "operationId": "GetVacancyProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyGetVacancyProductResponse"}}}, "parameters": [{"name": "id", "description": "对象id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["supply"]}}, "/api/v2/supply/wms_boh_doc_detail": {"get": {"summary": "Wms-boh单据核对表", "operationId": "WmsBohDocDetailReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyWmsBohDocDetailReportResponse"}}}, "parameters": [{"name": "bus_date", "in": "query", "required": false, "type": "string"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "boh_types", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "is_boh_wms_diff", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_boh_jde_diff", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_wms_jde_diff", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["supply"]}}, "/api/v2/supply/wms_boh_doc_summary": {"get": {"summary": "Wms-boh单据核对表", "operationId": "WmsBohDocSummaryReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/supplyWmsBohDocSummaryReportResponse"}}}, "parameters": [{"name": "bus_date", "in": "query", "required": false, "type": "string"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["supply"]}}}, "definitions": {"ActionSmartDemandRuleRequestAction": {"type": "string", "enum": ["none", "confirm", "cancel", "callback"], "default": "none", "title": "动作"}, "CreateDemandAdjustRequestProducts": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "门店id或者商品id"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "distribution_type": {"type": "string", "title": "商品订货物流模式(NMD:配送, PUR:直送)"}, "centre_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}}}, "DemandMainRequestitem_id": {"type": "object", "properties": {"object_by": {"type": "string", "format": "uint64", "title": "门店id或者商品id"}, "quantity": {"type": "number", "format": "float", "title": "订货数量"}, "distribution_type": {"type": "string", "title": "商品订货物流模式(NMD:配送, PUR:直送)"}, "vendor_id": {"type": "string", "format": "uint64", "title": "供应商id"}, "order_type": {"type": "string", "title": "订货方式"}}}, "DemandRuleDataInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "name": {"type": "string"}}}, "DemoGetStoreProductResponseGetStoreProduct": {"type": "object", "properties": {"product_code": {"type": "string"}, "product_name": {"type": "string"}, "unit_name": {"type": "string"}, "qty": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "product_id": {"type": "string", "format": "uint64"}}}, "DemoSplitStoreProductRequestSplitStoreProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "qty": {"type": "number", "format": "double"}}}, "GetSmartDemandRuleResponseDemandRule": {"type": "object", "properties": {"name": {"type": "string"}, "start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}, "branch_method": {"type": "string"}, "product_method": {"type": "string"}, "branch_rows": {"type": "array", "items": {"$ref": "#/definitions/DemandRuleDataInfo"}}, "product_rows": {"type": "array", "items": {"$ref": "#/definitions/DemandRuleDataInfo"}}, "remark": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "updated_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_name": {"type": "string"}, "code": {"type": "string"}}}, "GetValidStoreByProductIdResponseStoreProduct": {"type": "object", "properties": {"arrival_days": {"type": "integer", "format": "int32", "title": "到货天数"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心"}, "distribution_type": {"type": "string", "title": "配送(NMD)、采购(PUR)"}, "increment_quantity": {"type": "number", "format": "float", "title": "递增订量"}, "max_quantity": {"type": "number", "format": "float", "title": "最大订货量"}, "min_quantity": {"type": "number", "format": "float", "title": "最小订货量"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_name": {"type": "string", "title": "订货单位名称"}, "unit_spec": {"type": "string", "title": "订货规格"}, "purchase_price": {"type": "number", "format": "float", "title": "直采价格"}, "purchase_tax": {"type": "number", "format": "float", "title": "直采税率"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}}}, "GetValidStoreByProductIdResponseValidStore": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店名称"}, "store_products": {"type": "array", "items": {"$ref": "#/definitions/GetValidStoreByProductIdResponseStoreProduct"}, "title": "商品信息"}}}, "ListFirstDeliveryResponseFirstDelivery": {"type": "object", "properties": {"store_code": {"type": "string", "title": "门店编号"}, "store_name": {"type": "string", "title": "门店名称"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "distr_quantity": {"type": "number", "format": "float", "title": "配送数量"}, "unit_name": {"type": "string", "title": "订货单位"}, "order_date": {"type": "string", "format": "date-time", "title": "配送日期"}}}, "ListForecastStoreResponseForecastStore": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64"}, "forecast_date": {"type": "string", "format": "date-time"}, "updated_by": {"type": "string", "format": "uint64"}, "updated_at": {"type": "string", "format": "date-time"}}}, "ListProductSaleForecastResponseProductSaleForecast": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "forecast_infos": {"type": "array", "items": {"$ref": "#/definitions/supplyListProductSaleForecastResponseForecastInfo"}}}}, "ProductMainRequestProductItem": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "quantity": {"type": "number", "format": "float", "title": "订货数量"}, "distribution_type": {"type": "string", "title": "商品订货物流模式(NMD:配送, PUR:直送)"}, "distribution_by": {"type": "string", "format": "uint64", "title": "配送中心(不修改则可以不传)"}, "vendor_id": {"type": "string", "format": "uint64", "title": "供应商id"}, "order_type": {"type": "string", "title": "订货方式"}}}, "ProductMainRequestStoreItem": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "product_items": {"type": "array", "items": {"$ref": "#/definitions/ProductMainRequestProductItem"}, "title": "订货的详细信息"}}}, "QueryOrderBiReportResponseOrderBiReport": {"type": "object", "properties": {"code": {"type": "string"}, "demand_date": {"type": "string", "format": "date-time"}, "expect_date": {"type": "string", "format": "date-time"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "spec": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "quantity": {"type": "number", "format": "float"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "jde_order_id": {"type": "string", "format": "uint64"}, "jde_mcu": {"type": "string"}, "jde_order_type": {"type": "string"}, "havi_code": {"type": "string"}}}, "UpdateDemandProductRequestproducts": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "订货记录id(如果有此id则要传此id)"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "tag_type": {"type": "string", "title": "订货热麦类型(\"FINISHED\",\"成品订货\"),(\"TEA\", \"茶饮订货\"),(\"BREAD\", \"面包订货\")(\"RAW\", \"原料订货\")"}, "distribution_type": {"type": "string", "title": "商品订货物流模式(配送:NMD, 直送:PUR)， 成品订货不用传"}, "distribution_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心(不修改就不需要传)"}, "unit_id": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "vendor_id": {"type": "string", "format": "uint64", "title": "供应商id"}, "order_type": {"type": "string", "title": "订货方式"}}}, "UpdateVacancyQuantityRequestvacancy_product": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "要货单商品记录id"}, "quantity": {"type": "number", "format": "float", "title": "修改的配送数量"}}}, "WmsBohDocDetailReportResponseWmsBohDocDetail": {"type": "object", "properties": {"bus_date": {"type": "string"}, "boh_type": {"type": "string"}, "boh_doc_code": {"type": "string"}, "boh_status": {"type": "string"}, "boh_count": {"type": "string", "format": "uint64"}, "boh_out_qty": {"type": "number", "format": "double"}, "boh_rec_qty": {"type": "number", "format": "double"}, "jde_type": {"type": "string"}, "jde_doc_code": {"type": "string"}, "jde_status": {"type": "string"}, "jde_count": {"type": "string", "format": "uint64"}, "jde_out_qty": {"type": "number", "format": "double"}, "jde_rec_qty": {"type": "number", "format": "double"}, "wms_type": {"type": "string"}, "wms_doc_code": {"type": "string"}, "wms_status": {"type": "string"}, "wms_count": {"type": "string", "format": "uint64"}, "wms_out_qty": {"type": "number", "format": "double"}, "wms_rec_qty": {"type": "number", "format": "double"}, "store_id": {"type": "string", "format": "uint64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "is_boh_wms_diff": {"type": "boolean", "format": "boolean"}, "is_boh_jde_diff": {"type": "boolean", "format": "boolean"}, "is_wms_jde_diff": {"type": "boolean", "format": "boolean"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "send_type": {"type": "string"}}}, "WmsBohDocSummaryReportResponseWmsBohDocSummary": {"type": "object", "properties": {"bus_date": {"type": "string"}, "boh_type": {"type": "string"}, "boh_doc": {"type": "string"}, "boh_status": {"type": "string"}, "boh_count": {"type": "string", "format": "uint64"}, "boh_out_qty": {"type": "number", "format": "double"}, "boh_rec_qty": {"type": "number", "format": "double"}, "jde_type": {"type": "string"}, "jde_doc": {"type": "string"}, "jde_status": {"type": "string"}, "jde_count": {"type": "string", "format": "uint64"}, "jde_out_qty": {"type": "number", "format": "double"}, "jde_rec_qty": {"type": "number", "format": "double"}, "wms_type": {"type": "string"}, "wms_doc": {"type": "string"}, "wms_status": {"type": "string"}, "wms_count": {"type": "string", "format": "uint64"}, "wms_out_qty": {"type": "number", "format": "double"}, "wms_rec_qty": {"type": "number", "format": "double"}, "store_id": {"type": "string", "format": "uint64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}}}, "supplyActDemandBatchRequest": {"type": "object", "properties": {"mode": {"type": "string", "title": "执行模式: create, recover"}, "bizdt": {"type": "string", "format": "date-time", "title": "营业日期"}}, "title": "夜间执行门市订货批处理请求参数"}, "supplyActionSmartDemandRuleRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "action": {"$ref": "#/definitions/ActionSmartDemandRuleRequestAction"}}}, "supplyActionSmartDemandRuleResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "result": {"type": "boolean", "format": "boolean"}}}, "supplyApproveDemandMasterUploadResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "supplyApproveOrderCollectionUploadResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "supplyAttachments": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "attachment": {"type": "string"}}}, "supplyBizdt": {"type": "object", "properties": {"bizdt": {"type": "string", "format": "date-time", "title": "营业日期"}}}, "supplyCancelDemandMasterUploadResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "supplyCancelDemandOrderRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "action": {"type": "string"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/supplyAttachments"}, "title": "附件\n   string attachments = 3;\n   string action = 1;\n   // id 和附件关联"}}, "title": "取消冷链要货单"}, "supplyCancelDemandOrderResponse": {"type": "object", "properties": {"result": {"type": "string"}}}, "supplyCancelOrderCollectionUploadResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "supplyChangeDemandStatusRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "status": {"type": "string", "title": "单据业务状态(INITED,F_COMMIT(成品订货提交),COMMIT(提交), CAL_DONE(原料计算完成, 这步不能通过前端修改), CAL_FAILED(原料计算失败，这步也不能通过前端修改),REJECTED(驳回订货单),APPROVED(审核订货单通过),FREEZE(冻结订货单),CANCELLED"}, "description": {"type": "string"}, "attachments": {"type": "string", "title": "附件"}}}, "supplyConfirmProductSaleForecastRequest": {"type": "object", "properties": {"forecast_infos": {"type": "array", "items": {"$ref": "#/definitions/supplyConfirmProductSaleForecastRequestForecastInfo"}}}}, "supplyConfirmProductSaleForecastRequestForecastInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "confirm_amount": {"type": "string", "format": "int64"}}}, "supplyConfirmProductSaleForecastResponse": {"type": "object", "properties": {"success": {"type": "boolean", "format": "boolean"}}}, "supplyCreateDemandAdjustRequest": {"type": "object", "properties": {"demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "remark": {"type": "string", "title": "备注"}, "type": {"type": "string", "title": "门市紧急订货单-HD，主配单-MD"}, "sub_type": {"type": "string", "title": "主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT)(主配单才有的字段)"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "products": {"type": "array", "items": {"$ref": "#/definitions/CreateDemandAdjustRequestProducts"}, "title": "商品"}}}, "supplyCreateDemandAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "supplyCreateSmartDemandRuleRequest": {"type": "object", "properties": {"name": {"type": "string"}, "start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}, "branch_method": {"type": "string"}, "product_method": {"type": "string"}, "branch_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "remark": {"type": "string"}}}, "supplyCreateSmartDemandRuleResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "result": {"type": "boolean", "format": "boolean"}}}, "supplyCreateVacancyOrderRequest": {"type": "object", "properties": {"order_date": {"type": "string", "format": "date-time", "title": "检核日期"}, "remark": {"type": "string", "title": "备注"}, "distrcenter_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}, "title": "创建缺货检核单请求"}, "supplyDeleteDemandProductRequest": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "demand_product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "订货单订货记录id"}}, "title": "删除订货商品请求"}, "supplyDemandAdjustProduct": {"type": "object", "properties": {"arrival_days": {"type": "integer", "format": "int32", "title": "到货天数"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心"}, "distribution_type": {"type": "string", "title": "配送(NMD)、采购(PUR)"}, "increment_quantity": {"type": "number", "format": "float", "title": "递增订量"}, "max_quantity": {"type": "number", "format": "float", "title": "最大订货量"}, "min_quantity": {"type": "number", "format": "float", "title": "最小订货量"}, "product_category_id": {"type": "string", "format": "uint64", "title": "商品类别id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "spec": {"type": "string", "title": "规格"}, "storage_type": {"type": "string", "title": "商品储藏类型编码"}, "units": {"type": "array", "items": {"$ref": "#/definitions/supplyDemandAdjustProductUnit"}, "title": "多单位调整"}, "sale_type": {"type": "string", "title": "销售类型"}, "product_type": {"type": "string", "title": "商品属性"}, "suggest_quantity": {"type": "number", "format": "float", "title": "建议订货量"}, "finished_quantity": {"type": "number", "format": "float", "title": "成品订货量"}, "bread_quantity": {"type": "number", "format": "float", "title": "热麦订货量"}, "tea_quantity": {"type": "number", "format": "float", "title": "茶饮订货量"}, "raw_quantity": {"type": "number", "format": "float", "title": "原料订货量"}, "purchase_price": {"type": "number", "format": "float", "title": "直采价格"}, "purchase_tax": {"type": "number", "format": "float", "title": "直采税率"}, "id": {"type": "string", "format": "uint64", "title": "订货记录id(如果有此id则要传此id)"}, "distribution_circle": {"type": "string", "title": "配送周期类型"}, "distribution_center_id": {"type": "string", "format": "uint64", "title": "配送中心"}, "vendor_id": {"type": "string", "format": "uint64", "title": "供应商"}, "cycle_extends": {"type": "string"}}}, "supplyDemandAdjustProductUnit": {"type": "object", "properties": {"unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_name": {"type": "string", "title": "订货单位名称"}, "unit_code": {"type": "string", "title": "订货code"}, "unit_rate": {"type": "number", "format": "double", "title": "比率"}}}, "supplyDemandBatch": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "bizdt": {"type": "string", "format": "date-time", "title": "营业日期"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "user_id": {"type": "string", "format": "uint64", "title": "人员id"}, "status": {"type": "string", "title": "批次状态"}, "success": {"type": "string", "format": "uint64", "title": "门市订货单生成成功的个数"}, "failed": {"type": "string", "format": "uint64", "title": "门市订货单生成失败的个数"}, "processing": {"type": "string", "format": "uint64", "title": "门市订货单生成处理中的个数"}, "retry_count": {"type": "integer", "format": "int32", "title": "重试次数"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "reason_type": {"type": "string", "title": "描述"}}, "title": "订货批次"}, "supplyDemandEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "单据id"}, "batch_id": {"type": "string", "format": "uint64", "title": "生成的批次id"}, "code": {"type": "string", "title": "编码"}, "type": {"type": "string", "title": "门市订货(SD)、紧急订货(HD)、主配订货(MD)"}, "reason_type": {"type": "string", "title": "原因类型"}, "receive_by": {"type": "string", "format": "uint64", "title": "收获门店id"}, "receive_name": {"type": "string", "title": "收获门店名称"}, "distribute_by": {"type": "string", "format": "uint64", "title": "供应商id"}, "store_secondary_id": {"type": "string", "title": "门店编码"}, "store_type": {"type": "string", "title": "门店类型"}, "distribution_type": {"type": "string", "title": "供应商类别"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人id"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "description": {"type": "string", "title": "描述"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "sub_type": {"type": "string", "title": "主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)"}, "remark": {"type": "string", "title": "备注"}, "has_product": {"type": "integer", "format": "int32", "title": "是否有商品"}, "is_sys": {"type": "integer", "format": "int32", "title": "是否是系统操作(1是，0不是)"}, "status": {"type": "string", "title": "单据业务状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)"}, "process_status": {"type": "string", "title": "生成要货单处理状态(INITED,PROCESSING,SUCCESS,FAILED)"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "product_count": {"type": "integer", "format": "int32", "title": "订货品相"}, "is_plan": {"type": "boolean", "format": "boolean"}, "is_adjust": {"type": "boolean", "format": "boolean"}, "send_type": {"type": "string"}, "havi_code": {"type": "string"}, "pre_havi_code": {"type": "string"}, "bus_type": {"type": "string"}, "attachments": {"type": "string"}}}, "supplyDemandMainRequest": {"type": "object", "properties": {"demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "remark": {"type": "string", "title": "备注"}, "type": {"type": "string", "title": "门市紧急订货单-HD，主配单-MD"}, "sub_type": {"type": "string", "title": "主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT)(主配单才有的字段)"}, "object_id": {"type": "string", "format": "uint64", "title": "紧急订货单和主配单的参数(门店id或者商品id)"}, "item_ids": {"type": "array", "items": {"$ref": "#/definitions/DemandMainRequestitem_id"}, "title": "紧急订货单和主配单的参数"}, "bus_type": {"type": "string"}, "attachments": {"type": "string", "title": "附件"}}, "title": "创建订单请求参数"}, "supplyDemandMasterUploadByBatchDetail": {"type": "object", "properties": {"store_code": {"type": "string"}, "store_name": {"type": "string"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "start_date": {"type": "string", "format": "date-time"}, "end_date": {"type": "string", "format": "date-time"}, "row_num": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "error": {"type": "string"}, "batch_id": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "master_id": {"type": "string", "format": "uint64"}, "master_code": {"type": "string"}, "centre_id": {"type": "string", "format": "uint64"}, "centre_name": {"type": "string"}, "centre_code": {"type": "string"}, "remark": {"type": "string"}}}, "supplyDemandMasterUploadSummary": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "file_name": {"type": "string"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "upload_date": {"type": "string", "format": "date-time"}, "file_type": {"type": "string"}}}, "supplyDemandOrder": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "要货单id"}, "code": {"type": "string", "title": "要货单号"}, "type": {"type": "string", "title": "门市订货(SD)、紧急订货(HD)、主配订货(MD)"}, "sub_type": {"type": "string", "title": "主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)"}, "reason_type": {"type": "string"}, "receive_by": {"type": "string", "format": "uint64", "title": "收货门店"}, "store_secondary_id": {"type": "string", "title": "门店编码"}, "store_name": {"type": "string", "title": "收货门店名称"}, "store_type": {"type": "string", "title": "门店类型"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送中心/供应商id"}, "distribution_type": {"type": "string", "title": "配送/直送"}, "order_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "description": {"type": "string", "title": "描述"}, "is_received": {"type": "integer", "format": "int32", "title": "是否收货"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "demand_code": {"type": "string", "title": "订货单号"}, "storage_type": {"type": "string", "title": "储藏类型编码"}, "status": {"type": "string", "title": "要货单(配送状态-INITED:创建, CHECKED:检核完成, CHECKING:正在检核, SENT:发送成功, SEND_FAILED:发送失败)"}, "process_status": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "created_name": {"type": "string", "title": "创建人姓名"}, "updated_name": {"type": "string", "title": "更信任姓名"}, "jde_order_id": {"type": "string", "format": "uint64", "title": "jde单号"}, "product_count": {"type": "integer", "format": "int32", "title": "商品品项数"}, "is_adjust": {"type": "boolean", "format": "boolean"}, "send_type": {"type": "string"}, "havi_code": {"type": "string"}, "pre_havi_code": {"type": "string"}, "is_plan": {"type": "boolean", "format": "boolean", "title": "是否每日订货"}, "attachments": {"type": "string", "title": "订单附件"}}, "title": "要货单对象"}, "supplyDemoGetStoreProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/DemoGetStoreProductResponseGetStoreProduct"}}, "total": {"type": "string", "format": "uint64"}}}, "supplyDemoSaveStoreProductRequest": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64"}, "order_date": {"type": "string", "format": "date-time"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/supplyDemoSaveStoreProductRequestSplitStoreProductResponse"}}}}, "supplyDemoSaveStoreProductRequestSplitStoreProductResponse": {"type": "object", "properties": {"product_code": {"type": "string"}, "product_name": {"type": "string"}, "unit_name": {"type": "string"}, "qty": {"type": "number", "format": "double"}, "inv_qty": {"type": "number", "format": "double"}, "order_qty": {"type": "number", "format": "double"}, "product_id": {"type": "string", "format": "uint64"}}}, "supplyDemoSaveStoreProductResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "supplyDemoSplitStoreProductRequest": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/DemoSplitStoreProductRequestSplitStoreProduct"}}}}, "supplyDemoSplitStoreProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyDemoSplitStoreProductResponseSplitStoreProductResponse"}}, "total": {"type": "string", "format": "uint64"}}}, "supplyDemoSplitStoreProductResponseSplitStoreProductResponse": {"type": "object", "properties": {"product_code": {"type": "string"}, "product_name": {"type": "string"}, "unit_name": {"type": "string"}, "qty": {"type": "number", "format": "double"}, "inv_qty": {"type": "number", "format": "double"}, "order_qty": {"type": "number", "format": "double"}, "product_id": {"type": "string", "format": "uint64"}}}, "supplyDistributionProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "distribute_by": {"type": "string", "format": "uint64"}, "distribution_type": {"type": "string"}}}, "supplyExportDemandMainDetail": {"type": "object", "properties": {"code": {"type": "string", "title": "订货单号"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "store_secondary_id": {"type": "string", "title": "门店编码"}, "store_name": {"type": "string", "title": "门店名称"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "product_spec": {"type": "string", "title": "规格"}, "category_code": {"type": "string", "title": "类别编码"}, "category_name": {"type": "string", "title": "类别(DQ产品>干货>原材料-包装材料>干货-包材)"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "unit_name": {"type": "string", "title": "订货单位"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "accounting_unit_name": {"type": "string", "title": "核算单位"}, "is_plan": {"type": "boolean", "format": "boolean"}, "is_adjust": {"type": "boolean", "format": "boolean"}, "send_type": {"type": "string"}, "havi_code": {"type": "string"}}, "title": "订货单对象"}, "supplyExportDemandMainResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyExportDemandMainDetail"}}}, "title": "导出订货商品返回参数"}, "supplyGetDemandAdjustProductByStoreIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyDemandAdjustProduct"}}, "total": {"type": "string", "format": "uint64"}}}, "supplyGetDemandMasterUploadByBatchIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyDemandMasterUploadByBatchDetail"}}, "total": {"type": "string", "format": "uint64"}, "file_name": {"type": "string"}, "status": {"type": "string"}, "updated_name": {"type": "string"}}}, "supplyGetDemandMasterUploadResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyDemandMasterUploadSummary"}}, "total": {"type": "string", "format": "uint64"}}}, "supplyGetDemandOrderDetailResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "要货单id"}, "code": {"type": "string", "title": "要货单单号"}, "type": {"type": "string", "title": "要货单关联订单类型(SD, HD, MD)"}, "sub_type": {"type": "string", "title": "要货单关联订单主配类型(STORE, PRODUCT, MASTER)"}, "reason_type": {"type": "string", "title": "原因"}, "receive_by": {"type": "string", "format": "uint64", "title": "收货门店id"}, "store_secondary_id": {"type": "string", "title": "门店编码"}, "store_name": {"type": "string", "title": "门店名称"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送中心id/供应商id"}, "distribution_type": {"type": "string", "title": "配送:NMD/直送:PUR"}, "order_date": {"type": "string", "format": "date-time", "title": "要货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "description": {"type": "string", "title": "描述"}, "is_received": {"type": "integer", "format": "int64", "title": "是否收货"}, "partner_id": {"type": "string", "format": "uint64"}, "demand_code": {"type": "string", "title": "订货单编号"}, "storage_type": {"type": "string", "title": "存储类型"}, "status": {"type": "string", "title": "物流状态"}, "process_status": {"type": "string", "title": "此字段暂时对前端无用"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_name": {"type": "string", "title": "创建人姓名"}, "updated_name": {"type": "string", "title": "更新人姓名"}, "jde_order_id": {"type": "string", "format": "uint64", "title": "jde单号"}, "is_adjust": {"type": "boolean", "format": "boolean"}, "send_type": {"type": "string"}, "havi_code": {"type": "string"}, "pre_havi_code": {"type": "string"}, "attachments": {"type": "string"}}, "title": "获取要货单详细返回对象"}, "supplyGetDemandOrderProductDetailResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyOrderProduct"}}, "total": {"type": "string", "format": "uint64"}}}, "supplyGetDistributionByDemandResponse": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/supplyDistributionProduct"}}}}, "supplyGetOrderCollectionUploadByBatchIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyUploadOrderCollection"}}, "total": {"type": "string", "format": "uint64"}, "file_name": {"type": "string"}, "status": {"type": "string"}, "updated_name": {"type": "string"}}}, "supplyGetOrderCollectionUploadResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyOrderCollectionUploadSummary"}}, "total": {"type": "string", "format": "uint64"}}}, "supplyGetProductSaleForecastRequest": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64"}, "demand_day": {"type": "string", "format": "date-time"}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "is_tea_and_bread": {"type": "boolean", "format": "boolean"}}}, "supplyGetProductSaleForecastResponse": {"type": "object", "properties": {"map_field": {"type": "object", "additionalProperties": {"type": "number", "format": "double"}}}}, "supplyGetSmartDemandRuleResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/GetSmartDemandRuleResponseDemandRule"}}, "total": {"type": "string", "format": "uint64"}}}, "supplyGetVacancyOrderProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyVacancyOrderProduct"}}}, "title": "缺货商品的配送单信息"}, "supplyGetVacancyProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyVacancyProduct"}}, "total": {"type": "integer", "format": "int32"}}, "title": "查看缺货检核单缺货商品返回参数"}, "supplyGetValidStoreByProductIdRequest": {"type": "object", "properties": {"product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品id"}, "type": {"type": "string", "title": "订单类型:MD(只有MD)"}, "order_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "branch_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "区域id"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分类id"}, "lan": {"type": "string"}}, "title": "根据商品id获得可订货门店请求参数"}, "supplyGetValidStoreByProductIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/GetValidStoreByProductIdResponseValidStore"}}, "description": {"type": "string"}}, "title": "根据商品id获得可订货门店返回参数"}, "supplyIdRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "对象id"}, "lan": {"type": "string"}}}, "supplyListDemandOrderResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyDemandOrder"}}, "total": {"type": "string", "format": "uint64"}}, "title": "查询要货订单返回"}, "supplyListFirstDeliveryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ListFirstDeliveryResponseFirstDelivery"}}, "total": {"type": "integer", "format": "int32"}}, "title": "首配查询返回参数"}, "supplyListForecastStoreRequest": {"type": "object", "properties": {"forecast_date": {"type": "string", "format": "date-time"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "limit": {"type": "string", "format": "int64"}, "offset": {"type": "string", "format": "int64"}, "store_type": {"type": "string"}}}, "supplyListForecastStoreResponse": {"type": "object", "properties": {"total": {"type": "string", "format": "uint64"}, "forecast_stores": {"type": "array", "items": {"$ref": "#/definitions/ListForecastStoreResponseForecastStore"}}}}, "supplyListProductSaleForecastRequest": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64"}, "forecast_date": {"type": "string", "format": "date-time"}, "product_sale_type": {"type": "string"}, "order": {"type": "string", "title": "排序(默认asc)"}, "sort": {"type": "string"}}}, "supplyListProductSaleForecastResponse": {"type": "object", "properties": {"total": {"type": "string", "format": "int64"}, "sale_forecasts": {"type": "array", "items": {"$ref": "#/definitions/ListProductSaleForecastResponseProductSaleForecast"}}}}, "supplyListProductSaleForecastResponseForecastInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "forecast_amount": {"type": "string", "format": "int64"}, "confirm_amount": {"type": "string", "format": "int64"}, "forecast_date": {"type": "string", "format": "date-time"}}}, "supplyListVacancyResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyVacancy"}}, "total": {"type": "integer", "format": "int32"}}}, "supplyNote": {"type": "object", "properties": {"data": {"type": "string", "format": "byte"}, "desc": {"type": "string"}}}, "supplyNull": {"type": "object"}, "supplyOrderCollectionUploadSummary": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "file_name": {"type": "string"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "upload_date": {"type": "string", "format": "date-time"}, "file_type": {"type": "string"}}}, "supplyOrderProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "要货商品记录id"}, "demand_order_id": {"type": "string", "format": "uint64", "title": "要货单id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "category_id": {"type": "string", "format": "uint64", "title": "商品分类id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "line": {"type": "integer", "format": "int32", "title": "订货记录商品行号"}, "sale_type": {"type": "string", "title": "销售类型"}, "product_type": {"type": "string", "title": "商品属性"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_spec": {"type": "string", "title": "单位规格"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "accounting_unit_name": {"type": "string", "title": "核算单位名称"}, "accounting_unit_spec": {"type": "string", "title": "核算单位规格"}, "quantity": {"type": "number", "format": "float", "title": "订货数量"}, "accounting_quantity": {"type": "number", "format": "float", "title": "核算数量"}, "received_quantity": {"type": "number", "format": "float", "title": "收货数量"}, "received_accounting_quantity": {"type": "number", "format": "float", "title": "收货核算数量"}, "purchase_tax": {"type": "number", "format": "float", "title": "采购税率"}, "purchase_price": {"type": "number", "format": "float", "title": "采购价格"}, "is_received": {"type": "integer", "format": "int64", "title": "是否收货"}, "unit_rate": {"type": "number", "format": "float", "title": "比率"}, "order_date": {"type": "string", "format": "date-time", "title": "要货日期"}, "distr_quantity": {"type": "number", "format": "float", "title": "配送数量"}}, "title": "要货单商品对象"}, "supplyPoint": {"type": "object", "properties": {"x": {"type": "string", "format": "byte"}, "y": {"type": "integer", "format": "int32"}}}, "supplyPong": {"type": "object", "properties": {"pong": {"type": "string"}}}, "supplyProductMainRequest": {"type": "object", "properties": {"demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "remark": {"type": "string", "title": "备注"}, "type": {"type": "string", "title": "主配单:MD"}, "sub_type": {"type": "string", "title": "商品主配:PRODUCT"}, "items": {"type": "array", "items": {"$ref": "#/definitions/ProductMainRequestStoreItem"}, "title": "商品主配单的参数"}, "batch_id": {"type": "string", "format": "uint64", "title": "唯一请求id"}}, "title": "创建商品主配单请求参数"}, "supplyPutSmartDemandRuleRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}, "branch_method": {"type": "string"}, "product_method": {"type": "string"}, "branch_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "remark": {"type": "string"}, "only_remark": {"type": "boolean", "format": "boolean"}}}, "supplyPutSmartDemandRuleResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "result": {"type": "boolean", "format": "boolean"}, "remark": {"type": "string"}}}, "supplyQueryDemandProductEntity": {"type": "object", "properties": {"arrival_days": {"type": "integer", "format": "int32", "title": "到货天数"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心"}, "distribution_type": {"type": "string", "title": "配送(NMD)、采购(PUR)"}, "increment_quantity": {"type": "number", "format": "float", "title": "递增订量"}, "max_quantity": {"type": "number", "format": "float", "title": "最大订货量"}, "min_quantity": {"type": "number", "format": "float", "title": "最小订货量"}, "product_category_id": {"type": "string", "format": "uint64", "title": "商品类别id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "spec": {"type": "string", "title": "规格"}, "storage_type": {"type": "string", "title": "商品储藏类型编码"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_name": {"type": "string", "title": "订货单位名称"}, "unit_spec": {"type": "string", "title": "订货规格"}, "sale_type": {"type": "string", "title": "销售类型"}, "product_type": {"type": "string", "title": "商品属性"}, "suggest_quantity": {"type": "number", "format": "float", "title": "建议订货量"}, "finished_quantity": {"type": "number", "format": "float", "title": "成品订货量"}, "bread_quantity": {"type": "number", "format": "float", "title": "热麦订货量"}, "tea_quantity": {"type": "number", "format": "float", "title": "茶饮订货量"}, "raw_quantity": {"type": "number", "format": "float", "title": "原料订货量"}, "purchase_price": {"type": "number", "format": "float", "title": "直采价格"}, "purchase_tax": {"type": "number", "format": "float", "title": "直采税率"}, "id": {"type": "string", "format": "uint64", "title": "订货记录id(如果有此id则要传此id)"}, "distribution_circle": {"type": "string", "title": "配送周期类型"}, "distribution_center_id": {"type": "string", "format": "uint64", "title": "配送中心"}, "vendor_id": {"type": "string", "format": "uint64", "title": "供应商"}, "cycle_extends": {"type": "string"}, "is_first_delivery": {"type": "string"}, "yesterday_sales": {"type": "number", "format": "double", "title": "昨日销售"}, "average_week_sales": {"type": "number", "format": "double", "title": "一周平均销量"}, "yesterday_order_qty": {"type": "number", "format": "double", "title": "昨日订货量"}, "unit_rate": {"type": "number", "format": "double", "title": "单位转换率"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算订货单位id"}, "accounting_unit_name": {"type": "string", "title": "核算订货单位名称"}, "accounting_unit_spec": {"type": "string", "title": "核算订货规格"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算订货数量"}, "has_new_suggest_qty": {"type": "boolean", "format": "boolean"}, "barcode": {"type": "array", "items": {"type": "string"}}, "store_unfinish_qty": {"type": "number", "format": "double", "title": "门店待收货数量"}, "store_inv_qty": {"type": "number", "format": "double", "title": "门店实时库存"}, "partner_id": {"type": "string", "format": "uint64"}}, "title": "可订货商品实体"}, "supplyQueryDemandProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyQueryDemandProductEntity"}}, "total": {"type": "integer", "format": "int32"}}, "title": "获取订货商品返回参数"}, "supplyQueryDemandResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyDemandEntity"}}, "total": {"type": "string", "format": "uint64"}}, "title": "查询门店订货单"}, "supplyQueryOrderBiReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/QueryOrderBiReportResponseOrderBiReport"}}, "total": {"type": "integer", "format": "int32"}, "classification": {"type": "string"}, "sum_quantity": {"type": "number", "format": "float"}, "sum_accounting_quantity": {"type": "number", "format": "float"}}, "title": "要货单报表请求返回"}, "supplyQueryReturnOrdersRequest": {"type": "object", "properties": {"return_date_from": {"type": "string", "format": "date-time", "title": "退货日期start"}, "return_date_to": {"type": "string", "format": "date-time", "title": "退货日期end"}, "delivery_date_from": {"type": "string", "format": "date-time", "title": "实际提货日期start"}, "delivery_date_to": {"type": "string", "format": "date-time", "title": "实际提货日期end"}, "is_direct": {"type": "integer", "format": "int64", "title": "物流模式(0, 配送，1直送， 2全部)"}, "return_code": {"type": "string", "title": "退货单号"}, "havi_code": {"type": "string", "title": "wms单号"}, "jde_code": {"type": "string", "title": "jde单号"}, "receiving_code": {"type": "string", "title": "收货单号,只有冷链原单退货的有"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id"}, "product_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品id"}, "limit": {"type": "integer", "format": "int64", "title": "分页"}, "offset": {"type": "integer", "format": "int64", "title": "偏离"}, "is_wms_store": {"type": "boolean", "format": "boolean", "title": "是否夏辉门店"}, "is_sum": {"type": "boolean", "format": "boolean", "title": "是否汇总， 若不汇总则查明细"}, "category_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "商品ids"}, "lan": {"type": "string", "title": "lan"}}, "title": "退货单报表查询"}, "supplyQueryReturnOrdersResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/supplyReturnOrder"}, "title": "退货单数据"}, "total": {"type": "integer", "format": "int32", "title": "查询出的总条数"}, "classification": {"type": "string", "title": "汇总还是明细"}, "sum_quantity": {"type": "number", "format": "float", "title": "退货数量"}, "sum_accounting_quantity": {"type": "number", "format": "float", "title": "退货核算数量"}}, "title": "退货单报表明细"}, "supplyResponse": {"type": "object", "properties": {"description": {"type": "string"}}, "title": "统一返回对象"}, "supplyReturnOrder": {"type": "object", "properties": {"code": {"type": "string", "title": "退货单号"}, "is_direct": {"type": "boolean", "format": "boolean", "title": "物流模式(0, 配送，1直送)"}, "havi_code": {"type": "string", "title": "夏辉单号"}, "jde_order_id": {"type": "string", "title": "jde_单号"}, "receiving_code": {"type": "string", "title": "收货单号"}, "created_at": {"type": "string", "title": "退货日期"}, "return_delivery_date": {"type": "string", "title": "非原单,配送退货预计提货日期"}, "pre_return_date": {"type": "string", "title": "原单预计提货日期"}, "delivery_date": {"type": "string", "title": "实际提货日期"}, "store_code": {"type": "string", "title": "门店编号"}, "store_name": {"type": "string", "title": "门店名称"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "storage_type": {"type": "string", "title": "储藏类型"}, "quantity": {"type": "number", "format": "double", "title": "退货数量"}, "accounting_quantity": {"type": "number", "format": "double", "title": "退货核算数量"}, "is_confirmed": {"type": "boolean", "format": "boolean", "title": "是否确认"}, "status": {"type": "string", "title": "退货单状态"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_spec": {"type": "string", "title": "spec"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "account_unit_id\n核算订货单位"}, "accounting_unit_name": {"type": "string", "title": "单位名称"}, "accounting_unit_spec": {"type": "string", "title": "spec"}, "store_id": {"type": "string", "format": "uint64", "title": "store_id"}, "category_id": {"type": "string", "format": "uint64", "title": "商品类别"}, "send_date": {"type": "string", "title": "配送时间"}, "distribute_by": {"type": "string", "format": "uint64", "title": "仓库或者供应商id"}, "return_reason": {"type": "string", "title": "退货原因"}}}, "supplyUpdateDemandInfoRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "remark": {"type": "string", "title": "备注"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期(只有主配单和紧急订货单才有的参数，门市订货单请不要传)"}, "attachments": {"type": "string"}}, "title": "更新订货单信息参数"}, "supplyUpdateDemandProductRequest": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "主配单id"}, "product": {"type": "array", "items": {"$ref": "#/definitions/UpdateDemandProductRequestproducts"}, "title": "更新的商品信息"}, "order_date": {"type": "string", "format": "date-time", "title": "订货日期(不传默认当天)"}, "attachments": {"type": "string", "title": "附件"}}, "title": "更新订货商品参数"}, "supplyUpdateVacancyQuantityRequest": {"type": "object", "properties": {"vacancy_id": {"type": "string", "format": "uint64"}, "vacancy_products": {"type": "array", "items": {"$ref": "#/definitions/UpdateVacancyQuantityRequestvacancy_product"}}}, "title": "保存缺货检核配送数量请求参数"}, "supplyUpdateVacancyRemarkRequest": {"type": "object", "properties": {"vacancy_id": {"type": "string", "format": "uint64"}, "remark": {"type": "string"}}}, "supplyUploadDemandMaster": {"type": "object", "properties": {"store_code": {"type": "string"}, "store_name": {"type": "string"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "start_date": {"type": "string"}, "end_date": {"type": "string"}, "row_num": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "error": {"type": "string"}, "batch_id": {"type": "string", "format": "uint64"}, "store_type": {"type": "string"}, "centre_id": {"type": "string", "format": "uint64"}, "centre_name": {"type": "string"}, "centre_code": {"type": "string"}, "remark": {"type": "string"}}}, "supplyUploadDemandMasterRequest": {"type": "object", "properties": {"file_name": {"type": "string", "title": "文件名称"}, "file_type": {"type": "string", "title": "导入类型"}, "file": {"type": "string", "title": "文件流"}, "lan": {"type": "string"}}, "title": "导入主配单参数"}, "supplyUploadDemandMasterResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/supplyUploadDemandMaster"}}, "file_name": {"type": "string"}}}, "supplyUploadOrderCollection": {"type": "object", "properties": {"store_code": {"type": "string"}, "store_name": {"type": "string"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "start_date": {"type": "string", "format": "date-time"}, "end_date": {"type": "string", "format": "date-time"}, "row_num": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "error": {"type": "string"}, "batch_id": {"type": "string", "format": "uint64"}, "store_type": {"type": "string"}, "vendor_id": {"type": "string", "format": "uint64"}, "vendor_name": {"type": "string"}, "vendor_code": {"type": "string"}, "remark": {"type": "string"}, "return_reason_id": {"type": "string", "format": "uint64"}, "return_reason_name": {"type": "string"}, "return_reason_code": {"type": "string"}, "storage_type": {"type": "string"}, "master_id": {"type": "string", "format": "uint64"}, "master_code": {"type": "string"}}}, "supplyUploadOrderCollectionRequest": {"type": "object", "properties": {"file_name": {"type": "string", "title": "文件名称"}, "file_type": {"type": "string", "title": "导入类型"}, "file": {"type": "string", "title": "文件流"}}}, "supplyUploadOrderCollectionResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/supplyUploadOrderCollection"}}, "file_name": {"type": "string"}}}, "supplyVacancy": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string", "title": "检核单编号"}, "check_date": {"type": "string", "format": "date-time", "title": "要货日期"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "created_name": {"type": "string", "title": "创建人"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "updated_name": {"type": "string", "title": "更新人名称"}, "status": {"type": "string", "title": "状态(INITED:新建, CHECKING:正在检核, CHECKED:检核完成, EXECUTING:正在执行,SUCCESS:执行成功, FAILED:失败(原因在reason))"}, "distrcenter_ids": {"type": "string"}}, "title": "查询缺货检核单返回参数"}, "supplyVacancyOrderProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "配送单商品记录id"}, "store_name": {"type": "string", "title": "门店名称"}, "store_code": {"type": "string", "title": "门店编码"}, "order_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "order_code": {"type": "string", "title": "要货单编号"}, "order_id": {"type": "string", "format": "uint64", "title": "要货单id"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_name": {"type": "string", "title": "单位名称"}, "max_quantity": {"type": "number", "format": "float", "title": "最大订货量"}, "min_quantity": {"type": "number", "format": "float", "title": "最小订货量"}, "increment_quantity": {"type": "number", "format": "float", "title": "递增订货量"}, "center_id": {"type": "string", "format": "uint64", "title": "配送中心id"}, "quantity": {"type": "number", "format": "float", "title": "要货单订货数量"}, "distr_quantity": {"type": "number", "format": "float", "title": "配送数量"}, "valid_quantity": {"type": "number", "format": "float", "title": "仓库可用库存"}, "vacancy_quantity": {"type": "number", "format": "float", "title": "仓库缺货数量"}, "repo_name": {"type": "string", "title": "仓库名称"}}, "title": "缺货商品关联的配送单商品"}, "supplyVacancyProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "vacancy_id": {"type": "string", "format": "uint64", "title": "缺货主表id"}, "product_id": {"type": "string", "format": "uint64", "title": "缺货商品id"}, "product_name": {"type": "string", "title": "缺货商品名称"}, "product_code": {"type": "string", "title": "缺货商品编号"}, "order_quantity": {"type": "number", "format": "float", "title": "总订货数量"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_name": {"type": "string", "title": "单位名称"}, "repo_code": {"type": "string", "title": "仓库编号"}, "repo_name": {"type": "string", "title": "仓库名称"}, "valid_quantity": {"type": "number", "format": "float", "title": "可用数量"}, "promise_quantity": {"type": "number", "format": "float", "title": "可用数量"}, "vacancy_quantity": {"type": "number", "format": "float", "title": "缺少数量"}}, "title": "缺货商品"}, "supplyWmsBohDocDetailReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/WmsBohDocDetailReportResponseWmsBohDocDetail"}}, "total": {"type": "string", "format": "uint64"}}}, "supplyWmsBohDocSummaryReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/WmsBohDocSummaryReportResponseWmsBohDocSummary"}}, "total": {"type": "string", "format": "uint64"}}}}}