# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: receipt/receive.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='receipt/receive.proto',
  package='receive',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x15receipt/receive.proto\x12\x07receive\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe6\x04\n\x1cGetReturnAmountReportRequest\x12\x37\n\x13\x64\x65livery_start_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11\x64\x65livery_end_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11\x64\x65mand_start_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x0f\x64\x65mand_end_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x61rrival_start_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x61rrival_end_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x07 \x03(\x04\x12\x12\n\nvendor_ids\x18\x08 \x03(\x04\x12\x12\n\ncompany_id\x18\t \x01(\x04\x12\x13\n\x0bproduct_ids\x18\n \x03(\x04\x12\x0e\n\x06status\x18\x0b \x03(\t\x12\x13\n\x0breturn_code\x18\x0c \x01(\t\x12\x12\n\norder_code\x18\r \x01(\t\x12\x12\n\nbatch_code\x18\x0e \x01(\t\x12\r\n\x05limit\x18\x0f \x01(\x05\x12\x0e\n\x06offset\x18\x10 \x01(\x05\x12\x11\n\tdime_type\x18\x11 \x01(\t\x12\x13\n\x0b\x62\x61tch_types\x18\x12 \x03(\t\x12\x18\n\x10main_branch_type\x18\x13 \x01(\t\"\x80\x01\n\x1dGetReturnAmountReportResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.receive.ReturnAmountDetail\x12\r\n\x05total\x18\x02 \x01(\r\x12%\n\x03sum\x18\x03 \x01(\x0b\x32\x18.receive.ReturnAmountSum\"\xcc\x01\n\x0fReturnAmountSum\x12\x1b\n\x13sum_return_quantity\x18\x01 \x01(\x01\x12\x1e\n\x16sum_return_cost_amount\x18\x02 \x01(\x01\x12\x1d\n\x15sum_return_tax_amount\x18\x03 \x01(\x01\x12\x1c\n\x14sum_receive_quantity\x18\x04 \x01(\x01\x12\x1f\n\x17sum_receive_cost_amount\x18\x05 \x01(\x01\x12\x1e\n\x16sum_receive_tax_amount\x18\x06 \x01(\x01\"\xca\x05\n\x12ReturnAmountDetail\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x15\n\rdelivery_code\x18\x02 \x01(\t\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x11\n\tunit_name\x18\x07 \x01(\t\x12\x12\n\ncost_price\x18\x08 \x01(\x01\x12\x11\n\ttax_price\x18\t \x01(\x01\x12\x10\n\x08tax_rate\x18\n \x01(\x01\x12\x0b\n\x03tax\x18\x0b \x01(\x01\x12\x19\n\x11\x64\x65livery_quantity\x18\x0c \x01(\x01\x12\x1a\n\x12return_cost_amount\x18\r \x01(\x01\x12\x19\n\x11return_tax_amount\x18\x0e \x01(\x01\x12\x17\n\x0freturn_quantity\x18\x0f \x01(\x01\x12\x1b\n\x13receive_cost_amount\x18\x10 \x01(\x01\x12\x1a\n\x12receive_tax_amount\x18\x11 \x01(\x01\x12\x14\n\x0c\x63ompany_code\x18\x12 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x13 \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bvendor_code\x18\x17 \x01(\t\x12\x13\n\x0bvendor_name\x18\x18 \x01(\t\x12\x13\n\x0breturn_code\x18\x19 \x01(\t\x12\x12\n\norder_code\x18\x1a \x01(\t\x12\x13\n\x0bprimary_key\x18\x1b \x01(\x04\x12\x12\n\nbatch_code\x18\x1c \x01(\t\"7\n!GetUnfinishReceiveProductsRequest\x12\x12\n\nreceive_by\x18\x01 \x01(\x04\"\xc5\x01\n\"GetUnfinishReceiveProductsResponse\x12\x63\n\x15unfinish_products_map\x18\x01 \x03(\x0b\x32\x44.receive.GetUnfinishReceiveProductsResponse.UnfinishProductsMapEntry\x1a:\n\x18UnfinishProductsMapEntry\x12\x0b\n\x03key\x18\x01 \x01(\x04\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"3\n\x08Response\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\"\xfe\t\n\x07Receive\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x12\n\nbatch_type\x18\x05 \x01(\t\x12\x10\n\x08order_id\x18\x06 \x01(\x04\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0b\x64\x65livery_id\x18\x08 \x01(\x04\x12\x15\n\rdelivery_code\x18\t \x01(\t\x12\x12\n\nreceive_by\x18\n \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x0b \x01(\x04\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x16\n\x0eprocess_status\x18\r \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cstorage_type\x18\x12 \x01(\t\x12\x12\n\ndistr_type\x18\x13 \x01(\t\x12\x16\n\x0ethird_party_id\x18\x14 \x01(\t\x12\x18\n\x10third_party_type\x18\x15 \x01(\t\x12\x14\n\x0cproduct_nums\x18\x16 \x01(\x04\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x03\x12\x12\n\nupdated_by\x18\x1a \x01(\x03\x12\x12\n\npartner_id\x18\x1b \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1c \x01(\t\x12\x14\n\x0cupdated_name\x18\x1d \x01(\t\x12\x18\n\x10main_branch_type\x18\x1e \x01(\t\x12\x19\n\x11\x63ost_trans_status\x18\x1f \x01(\t\x12\x13\n\x0b\x63ost_update\x18  \x01(\x03\x12\x16\n\x0e\x63ost_center_id\x18! \x01(\x04\x12\x0e\n\x06remark\x18\" \x01(\t\x12\x0e\n\x06reason\x18# \x01(\t\x12\x18\n\x10sub_account_type\x18$ \x01(\t\x12\x16\n\x0esub_receive_by\x18% \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18& \x01(\x04\x12\x13\n\x0b\x64\x65mand_type\x18\' \x01(\t\x12\x18\n\x10\x64\x65livery_by_code\x18( \x01(\t\x12\x18\n\x10\x64\x65livery_by_name\x18) \x01(\t\x12\x15\n\rfranchisee_id\x18* \x01(\x04\x12\x15\n\rsum_price_tax\x18, \x01(\x01\x12\"\n\x08products\x18- \x03(\x0b\x32\x10.receive.Product\x12\x17\n\x0freceive_by_code\x18. \x01(\t\x12\x17\n\x0freceive_by_name\x18/ \x01(\t\x12\x0f\n\x07\x63ontact\x18\x30 \x01(\t\x12\x0b\n\x03tel\x18\x31 \x01(\t\x12\x15\n\rsource_remark\x18\x32 \x01(\t\x12\x15\n\rorder_type_id\x18\x33 \x01(\x04\x12\x15\n\rdemand_remark\x18\x34 \x01(\t\"\x82\x07\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nreceive_id\x18\x02 \x01(\x04\x12\x12\n\nreceive_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x14\n\x0cstorage_type\x18\x07 \x01(\t\x12\x16\n\x0eorder_quantity\x18\x08 \x01(\x01\x12\x19\n\x11\x64\x65livery_quantity\x18\t \x01(\x01\x12\x18\n\x10receive_quantity\x18\n \x01(\x01\x12\x14\n\x0cis_confirmed\x18\x0b \x01(\x08\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\r \x01(\x04\x12.\n\nupdated_at\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x0f \x01(\x04\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x11 \x01(\t\x12\x14\n\x0cupdated_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x11\n\tunit_spec\x18\x15 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x16 \x01(\x04\x12\x15\n\rcategory_name\x18\x17 \x01(\t\x12\x15\n\rcategory_code\x18\x18 \x01(\t\x12\x12\n\ncost_price\x18\x19 \x01(\x01\x12\x11\n\ttax_price\x18\x1a \x01(\x01\x12\x10\n\x08tax_rate\x18\x1b \x01(\x01\x12\x11\n\tunit_rate\x18\x1c \x01(\x01\x12\x11\n\tsale_type\x18\x1d \x01(\t\x12\x18\n\x10sub_account_type\x18\x1e \x01(\t\x12\x16\n\x0esub_receive_by\x18\x1f \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18  \x01(\x04\x12\x1f\n\x17\x61\x63tual_receive_quantity\x18! \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\" \x01(\x01\x12\x19\n\x11min_remaining_qty\x18# \x01(\x01\x12\x19\n\x11max_remaining_qty\x18$ \x01(\x01\x12\x17\n\x0f\x63heck_deviation\x18% \x01(\x05\x12\x15\n\rtemp_quantity\x18& \x01(\x01\"\xd9\x05\n\x14\x43reateReceiveRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x12\n\nbatch_type\x18\x05 \x01(\t\x12\x10\n\x08order_id\x18\x06 \x01(\x04\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0b\x64\x65livery_id\x18\x08 \x01(\x04\x12\x15\n\rdelivery_code\x18\t \x01(\t\x12\x12\n\nreceive_by\x18\n \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x0b \x01(\x04\x12\x14\n\x0cstorage_type\x18\x0c \x01(\t\x12\x12\n\ndistr_type\x18\r \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\"\n\x08products\x18\x12 \x03(\x0b\x32\x10.receive.Product\x12\x18\n\x10main_branch_type\x18\x13 \x01(\t\x12\x0e\n\x06remark\x18\x14 \x01(\t\x12\x0e\n\x06reason\x18\x15 \x01(\t\x12\x18\n\x10sub_account_type\x18\x16 \x01(\t\x12\x16\n\x0esub_receive_by\x18\x17 \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18\x18 \x01(\x04\x12\x13\n\x0b\x64\x65mand_type\x18\x19 \x01(\t\x12\x15\n\rsource_remark\x18\x1e \x01(\t\x12\x15\n\rorder_type_id\x18\x1f \x01(\x04\x12\x15\n\rfranchisee_id\x18  \x01(\x04\"\xbf\x02\n\x1cUpdateReceiveProductsRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x16\n\x0eprocess_status\x18\x03 \x01(\t\x12\"\n\x08products\x18\x04 \x03(\x0b\x32\x10.receive.Product\x12\x10\n\x08\x62\x61tch_id\x18\x05 \x01(\x04\x12\x10\n\x08order_id\x18\x06 \x01(\x04\x12\x0e\n\x06remark\x18\x07 \x01(\t\x12\x0e\n\x06reason\x18\x08 \x01(\t\x12\x12\n\ndistr_type\x18\t \x01(\t\x12\x12\n\nreceive_by\x18\n \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x0b \x01(\x04\x12\x15\n\rsource_remark\x18\x0c \x01(\t\x12/\n\x0b\x65xpect_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xf6\x07\n\x12ListReceiveRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x64\x65livery_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x15\n\rdelivery_code\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0breceive_bys\x18\x08 \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\t \x03(\x04\x12\x0e\n\x06status\x18\n \x03(\t\x12\x16\n\x0eprocess_status\x18\x0b \x03(\t\x12.\n\nstart_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ndistr_type\x18\x0e \x01(\t\x12\x12\n\nbatch_type\x18\x0f \x03(\t\x12\r\n\x05limit\x18\x10 \x01(\x05\x12\x0e\n\x06offset\x18\x11 \x01(\x05\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\r\n\x05order\x18\x13 \x01(\t\x12\x18\n\x10main_branch_type\x18\x14 \x01(\t\x12\x37\n\x13\x64\x65livery_start_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11\x64\x65livery_end_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11\x63ost_trans_status\x18\x17 \x01(\t\x12\x37\n\x13received_start_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11received_end_date\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10sub_account_type\x18\x1a \x01(\t\x12\x17\n\x0fsub_receive_bys\x18\x1b \x03(\x04\x12\x18\n\x10sub_delivery_bys\x18\x1c \x03(\x04\x12\x13\n\x0bgeo_regions\x18\x1d \x03(\x04\x12\x13\n\x0b\x64\x65mand_type\x18\x1e \x01(\t\x12\x13\n\x0bproduct_ids\x18\x1f \x03(\x04\x12\x18\n\x10include_products\x18  \x01(\t\x12\x0b\n\x03ids\x18! \x03(\x04\x12\x35\n\x11\x65xpect_start_date\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x0f\x65xpect_end_date\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbatch_ids\x18$ \x03(\x04\"\xe4\x07\n\x13ListReceivesRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x03(\x04\x12\x13\n\x0b\x64\x65livery_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x15\n\rdelivery_code\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0breceive_bys\x18\x08 \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\t \x03(\x04\x12\x0e\n\x06status\x18\n \x03(\t\x12\x16\n\x0eprocess_status\x18\x0b \x03(\t\x12.\n\nstart_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ndistr_type\x18\x0e \x01(\t\x12\x12\n\nbatch_type\x18\x0f \x03(\t\x12\r\n\x05limit\x18\x10 \x01(\x05\x12\x0e\n\x06offset\x18\x11 \x01(\x05\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\r\n\x05order\x18\x13 \x01(\t\x12\x18\n\x10main_branch_type\x18\x14 \x01(\t\x12\x37\n\x13\x64\x65livery_start_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11\x64\x65livery_end_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11\x63ost_trans_status\x18\x17 \x01(\t\x12\x37\n\x13received_start_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11received_end_date\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10sub_account_type\x18\x1a \x01(\t\x12\x17\n\x0fsub_receive_bys\x18\x1b \x03(\x04\x12\x18\n\x10sub_delivery_bys\x18\x1c \x03(\x04\x12\x13\n\x0bgeo_regions\x18\x1d \x03(\x04\x12\x13\n\x0b\x64\x65mand_type\x18\x1e \x01(\t\x12\x13\n\x0bproduct_ids\x18\x1f \x03(\x04\x12\x18\n\x10include_products\x18  \x01(\t\x12\x0b\n\x03ids\x18! \x03(\x04\x12\x35\n\x11\x65xpect_start_date\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x0f\x65xpect_end_date\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"D\n\x13ListReceiveResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.receive.Receive\x12\r\n\x05total\x18\x02 \x01(\x04\"#\n\x15GetReceiveByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\";\n\x16GetReceiveByIdResponse\x12!\n\x07receive\x18\x01 \x01(\x0b\x32\x10.receive.Receive\"}\n\x1cGetReceiveProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\"X\n\x16\x44\x65\x61lReceiveByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\"\n\x08products\x18\x04 \x03(\x0b\x32\x10.receive.Product\"N\n\x1dGetReceiveProductByIdResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.receive.Product\x12\r\n\x05total\x18\x02 \x01(\x04\"\xd7\x03\n\x1aGetReceivingCollectRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x18\n\x10main_branch_type\x18\t \x01(\t\x12\x14\n\x0c\x64\x65livery_bys\x18\n \x03(\x04\x12\x13\n\x0b\x62\x61tch_types\x18\x0b \x03(\t\x12\x37\n\x13received_start_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11received_end_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ndistr_type\x18\x0e \x03(\t\x12\x10\n\x08group_by\x18\x0f \x01(\t\x12\x0e\n\x06status\x18\x10 \x03(\t\"e\n\x1bGetReceivingCollectResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.receive.ReceivingCollect\x12\x1d\n\x05total\x18\x02 \x01(\x0b\x32\x0e.receive.Total\"\xfc\x03\n\x19GetReceivingDetailRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x18\n\x10main_branch_type\x18\n \x01(\t\x12\x14\n\x0c\x64\x65livery_bys\x18\x0b \x03(\x04\x12\x13\n\x0b\x62\x61tch_types\x18\x0c \x03(\t\x12\x13\n\x0bproduct_ids\x18\r \x03(\x04\x12\x37\n\x13received_start_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11received_end_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ndistr_type\x18\x10 \x03(\t\x12\x0e\n\x06status\x18\x11 \x03(\t\x12\x13\n\x0b\x64\x65mand_type\x18\x12 \x01(\t\"e\n\x1aGetReceivingDetailResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.receive.ReceivingDetailed\x12\x1d\n\x05total\x18\x02 \x01(\x0b\x32\x0e.receive.Total\"\x9e\x02\n\x05Total\x12\r\n\x05\x63ount\x18\x01 \x01(\x01\x12\x1a\n\x12sum_order_quantity\x18\x02 \x01(\x01\x12\x1d\n\x15sum_delivery_quantity\x18\x03 \x01(\x01\x12\x1c\n\x14sum_receive_quantity\x18\x04 \x01(\x01\x12\x16\n\x0esum_cost_price\x18\x05 \x01(\x01\x12\x15\n\rsum_tax_price\x18\x06 \x01(\x01\x12\x10\n\x08tax_rate\x18\x07 \x01(\x01\x12\x19\n\x11sum_pick_quantity\x18\x08 \x01(\x01\x12\x18\n\x10sum_order_amount\x18\t \x01(\x01\x12\x1b\n\x13sum_delivery_amount\x18\n \x01(\x01\x12\x1a\n\x12sum_receive_amount\x18\x0b \x01(\x01\"\xb4\x06\n\x10ReceivingCollect\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x19\n\x11\x64\x65livery_quantity\x18\x07 \x01(\x01\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x18\n\x10receive_quantity\x18\r \x01(\x01\x12\x16\n\x0eorder_quantity\x18\x0e \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\x0f \x01(\x01\x12\x12\n\nreceive_by\x18\x10 \x01(\x04\x12\x17\n\x0freceive_by_code\x18\x11 \x01(\t\x12\x17\n\x0freceive_by_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x12\n\ncost_price\x18\x15 \x01(\x01\x12\x11\n\ttax_price\x18\x16 \x01(\x01\x12\x10\n\x08tax_rate\x18\x17 \x01(\x01\x12\x0b\n\x03tax\x18\x18 \x01(\x01\x12\x18\n\x10sub_account_type\x18\x19 \x01(\t\x12\x16\n\x0esub_receive_by\x18\x1a \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18\x1b \x01(\x04\x12\x1b\n\x13sub_receive_by_code\x18\x1e \x01(\t\x12\x1b\n\x13sub_receive_by_name\x18\x1f \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18  \x01(\x04\x12\x18\n\x10\x64\x65livery_by_code\x18! \x01(\t\x12\x18\n\x10\x64\x65livery_by_name\x18\" \x01(\t\x12\x12\n\ndistr_type\x18# \x01(\t\x12\x15\n\rfranchisee_id\x18$ \x01(\x04\x12\x13\n\x0b\x64\x65mand_type\x18% \x01(\t\"\xeb\x08\n\x11ReceivingDetailed\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x19\n\x11\x64\x65livery_quantity\x18\x07 \x01(\x01\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x18\n\x10receive_quantity\x18\r \x01(\x01\x12\x16\n\x0eorder_quantity\x18\x0e \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\x0f \x01(\x01\x12\x12\n\nreceive_by\x18\x10 \x01(\x04\x12\x17\n\x0freceive_by_code\x18\x11 \x01(\t\x12\x17\n\x0freceive_by_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x31\n\rdelivery_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x64\x65mand_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x16\n\x0ereceiving_code\x18\x17 \x01(\t\x12\x32\n\x0ereceiving_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0creceiving_id\x18\x19 \x01(\x04\x12\n\n\x02id\x18\x1a \x01(\x04\x12\x12\n\ncost_price\x18\x1b \x01(\x01\x12\x11\n\ttax_price\x18\x1c \x01(\x01\x12\x10\n\x08tax_rate\x18\x1d \x01(\x01\x12\x13\n\x0b\x64\x65livery_by\x18\x1e \x01(\x04\x12\x18\n\x10\x64\x65livery_by_code\x18\x1f \x01(\t\x12\x18\n\x10\x64\x65livery_by_name\x18  \x01(\t\x12\x0b\n\x03tax\x18! \x01(\x01\x12\x18\n\x10sub_account_type\x18\" \x01(\t\x12\x16\n\x0esub_receive_by\x18# \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18$ \x01(\x04\x12\x1b\n\x13sub_receive_by_code\x18% \x01(\t\x12\x1b\n\x13sub_receive_by_name\x18& \x01(\t\x12\x12\n\nbatch_code\x18\' \x01(\t\x12\x12\n\ncreated_by\x18( \x01(\x04\x12/\n\x0b\x65xpect_date\x18) \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18* \x01(\t\x12\x0e\n\x06reason\x18+ \x01(\t\x12\x12\n\ndistr_type\x18, \x01(\t\x12\x15\n\rfranchisee_id\x18- \x01(\x04\"\xd0\x02\n\x15GetTotalReportRequest\x12\x13\n\x0breceive_bys\x18\x01 \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x13\n\x0b\x62\x61tch_types\x18\x04 \x03(\t\x12\x18\n\x10main_branch_type\x18\x05 \x01(\t\x12.\n\nstart_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x08 \x01(\x05\x12\x0e\n\x06offset\x18\t \x01(\x05\x12\x18\n\x10sub_account_type\x18\n \x03(\t\x12\x17\n\x0fsub_receive_bys\x18\x0b \x03(\x04\x12\x18\n\x10sub_delivery_bys\x18\x0c \x03(\x04\"]\n\x16GetTotalReportResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.receive.TotalDetailed\x12\x1d\n\x05total\x18\x02 \x01(\x0b\x32\x0e.receive.Total\"\xda\x05\n\rTotalDetailed\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x18\n\x10receive_quantity\x18\x06 \x01(\x01\x12\x19\n\x11\x64\x65livery_quantity\x18\x07 \x01(\x01\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\r \x01(\x04\x12\x18\n\x10\x64\x65livery_by_code\x18\x0e \x01(\t\x12\x18\n\x10\x64\x65livery_by_name\x18\x0f \x01(\t\x12\x12\n\nreceive_by\x18\x10 \x01(\x04\x12\x17\n\x0freceive_by_code\x18\x11 \x01(\t\x12\x17\n\x0freceive_by_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x18\n\x10sub_account_type\x18\x16 \x01(\t\x12\x16\n\x0esub_receive_by\x18\x17 \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18\x18 \x01(\x04\x12\n\n\x02id\x18\x1a \x01(\x04\x12\x16\n\x0ereceive_amount\x18\x1b \x01(\x01\x12\x17\n\x0f\x64\x65livery_amount\x18\x1c \x01(\x01\x12\x1a\n\x12receive_tax_amount\x18\x1d \x01(\x01\x12\x1b\n\x13\x64\x65livery_tax_amount\x18\x1e \x01(\x01\x12\x14\n\x0corder_amount\x18\x1f \x01(\x01\x12\x18\n\x10order_tax_amount\x18  \x01(\x01\x12\x16\n\x0eorder_quantity\x18! \x01(\x01\x12\x10\n\x08\x62\x61tch_id\x18\" \x01(\x04\x12\x16\n\x0eproduct_status\x18# \x01(\t\x12\x15\n\rfranchisee_id\x18$ \x01(\x04\".\n\x1eGetReceiveProductByCodeRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\"P\n\x1fGetReceiveProductByCodeResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.receive.Product\x12\r\n\x05total\x18\x02 \x01(\x04*W\n\rReceiptStatus\x12\x08\n\x04INIT\x10\x00\x12\n\n\x06SUBMIT\x10\x01\x12\x0b\n\x07\x41PPROVE\x10\x02\x12\n\n\x06REJECT\x10\x03\x12\x0b\n\x07\x43ONFIRM\x10\x04\x12\n\n\x06\x43\x41NCEL\x10\x05*\\\n\rProcessStatus\x12\x0c\n\x08\x41\x43TIVATE\x10\x00\x12\x08\n\x04SENT\x10\x01\x12\x0e\n\nPROCESSING\x10\x02\x12\x0b\n\x07SUCCESS\x10\x03\x12\n\n\x06\x46INISH\x10\x04\x12\n\n\x06\x46\x41ILED\x10\x05\x32\xaa\r\n\x0eReceiveService\x12\x65\n\rCreateReceive\x12\x1d.receive.CreateReceiveRequest\x1a\x11.receive.Response\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v2/receipt/receive:\x01*\x12i\n\x0bListReceive\x12\x1b.receive.ListReceiveRequest\x1a\x1c.receive.ListReceiveResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/api/v2/receipt/receive\x12h\n\x0eGetReceiveById\x12\x1e.receive.GetReceiveByIdRequest\x1a\x10.receive.Receive\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v2/receipt/receive/{id}\x12\x94\x01\n\x15GetReceiveProductById\x12%.receive.GetReceiveProductByIdRequest\x1a&.receive.GetReceiveProductByIdResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/receipt/receive/{id}/product\x12w\n\x0f\x44\x65\x61lReceiveById\x12\x1f.receive.DealReceiveByIdRequest\x1a\x11.receive.Response\"0\x82\xd3\xe4\x93\x02*\x1a%/api/v2/receipt/receive/{id}/{action}:\x01*\x12\x81\x01\n\x15UpdateReceiveProducts\x12%.receive.UpdateReceiveProductsRequest\x1a\x11.receive.Response\".\x82\xd3\xe4\x93\x02(\"#/api/v2/receipt/receive/{id}/update:\x01*\x12\x8e\x01\n\x13GetReceivingCollect\x12#.receive.GetReceivingCollectRequest\x1a$.receive.GetReceivingCollectResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/receipt/receiving/bi/collect\x12\x8c\x01\n\x12GetReceivingDetail\x12\".receive.GetReceivingDetailRequest\x1a#.receive.GetReceivingDetailResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/receipt/receiving/bi/detailed\x12w\n\x0eGetTotalReport\x12\x1e.receive.GetTotalReportRequest\x1a\x1f.receive.GetTotalReportResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v2/receipt/total/report\x12\xa3\x01\n\x17GetReceiveProductByCode\x12\'.receive.GetReceiveProductByCodeRequest\x1a(.receive.GetReceiveProductByCodeResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/receipt/receive/{code}/product/bycode\x12}\n\x1aGetUnfinishReceiveProducts\x12*.receive.GetUnfinishReceiveProductsRequest\x1a+.receive.GetUnfinishReceiveProductsResponse\"\x06\x82\xd3\xe4\x93\x02\x00\x12l\n\x0cListReceives\x12\x1c.receive.ListReceivesRequest\x1a\x1c.receive.ListReceiveResponse\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v2/receipt/receives\x12\x9b\x01\n\x15GetReturnAmountReport\x12%.receive.GetReturnAmountReportRequest\x1a&.receive.GetReturnAmountReportResponse\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/receipt/vendor/return/amount/reportb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_RECEIPTSTATUS = _descriptor.EnumDescriptor(
  name='ReceiptStatus',
  full_name='receive.ReceiptStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INIT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMIT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECT', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRM', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCEL', index=5, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=12530,
  serialized_end=12617,
)
_sym_db.RegisterEnumDescriptor(_RECEIPTSTATUS)

ReceiptStatus = enum_type_wrapper.EnumTypeWrapper(_RECEIPTSTATUS)
_PROCESSSTATUS = _descriptor.EnumDescriptor(
  name='ProcessStatus',
  full_name='receive.ProcessStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ACTIVATE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SENT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PROCESSING', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUCCESS', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FINISH', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FAILED', index=5, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=12619,
  serialized_end=12711,
)
_sym_db.RegisterEnumDescriptor(_PROCESSSTATUS)

ProcessStatus = enum_type_wrapper.EnumTypeWrapper(_PROCESSSTATUS)
INIT = 0
SUBMIT = 1
APPROVE = 2
REJECT = 3
CONFIRM = 4
CANCEL = 5
ACTIVATE = 0
SENT = 1
PROCESSING = 2
SUCCESS = 3
FINISH = 4
FAILED = 5



_GETRETURNAMOUNTREPORTREQUEST = _descriptor.Descriptor(
  name='GetReturnAmountReportRequest',
  full_name='receive.GetReturnAmountReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='delivery_start_date', full_name='receive.GetReturnAmountReportRequest.delivery_start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_end_date', full_name='receive.GetReturnAmountReportRequest.delivery_end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_start_date', full_name='receive.GetReturnAmountReportRequest.demand_start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_end_date', full_name='receive.GetReturnAmountReportRequest.demand_end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_start_date', full_name='receive.GetReturnAmountReportRequest.arrival_start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_end_date', full_name='receive.GetReturnAmountReportRequest.arrival_end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='receive.GetReturnAmountReportRequest.store_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_ids', full_name='receive.GetReturnAmountReportRequest.vendor_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='receive.GetReturnAmountReportRequest.company_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='receive.GetReturnAmountReportRequest.product_ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.GetReturnAmountReportRequest.status', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_code', full_name='receive.GetReturnAmountReportRequest.return_code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='receive.GetReturnAmountReportRequest.order_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='receive.GetReturnAmountReportRequest.batch_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receive.GetReturnAmountReportRequest.limit', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receive.GetReturnAmountReportRequest.offset', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dime_type', full_name='receive.GetReturnAmountReportRequest.dime_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_types', full_name='receive.GetReturnAmountReportRequest.batch_types', index=17,
      number=18, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receive.GetReturnAmountReportRequest.main_branch_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=98,
  serialized_end=712,
)


_GETRETURNAMOUNTREPORTRESPONSE = _descriptor.Descriptor(
  name='GetReturnAmountReportResponse',
  full_name='receive.GetReturnAmountReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receive.GetReturnAmountReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receive.GetReturnAmountReportResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum', full_name='receive.GetReturnAmountReportResponse.sum', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=715,
  serialized_end=843,
)


_RETURNAMOUNTSUM = _descriptor.Descriptor(
  name='ReturnAmountSum',
  full_name='receive.ReturnAmountSum',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sum_return_quantity', full_name='receive.ReturnAmountSum.sum_return_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_return_cost_amount', full_name='receive.ReturnAmountSum.sum_return_cost_amount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_return_tax_amount', full_name='receive.ReturnAmountSum.sum_return_tax_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_receive_quantity', full_name='receive.ReturnAmountSum.sum_receive_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_receive_cost_amount', full_name='receive.ReturnAmountSum.sum_receive_cost_amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_receive_tax_amount', full_name='receive.ReturnAmountSum.sum_receive_tax_amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=846,
  serialized_end=1050,
)


_RETURNAMOUNTDETAIL = _descriptor.Descriptor(
  name='ReturnAmountDetail',
  full_name='receive.ReturnAmountDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.ReturnAmountDetail.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='receive.ReturnAmountDetail.delivery_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='receive.ReturnAmountDetail.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='receive.ReturnAmountDetail.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receive.ReturnAmountDetail.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receive.ReturnAmountDetail.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receive.ReturnAmountDetail.unit_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='receive.ReturnAmountDetail.cost_price', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='receive.ReturnAmountDetail.tax_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='receive.ReturnAmountDetail.tax_rate', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='receive.ReturnAmountDetail.tax', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='receive.ReturnAmountDetail.delivery_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_cost_amount', full_name='receive.ReturnAmountDetail.return_cost_amount', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_tax_amount', full_name='receive.ReturnAmountDetail.return_tax_amount', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_quantity', full_name='receive.ReturnAmountDetail.return_quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_cost_amount', full_name='receive.ReturnAmountDetail.receive_cost_amount', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_tax_amount', full_name='receive.ReturnAmountDetail.receive_tax_amount', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='receive.ReturnAmountDetail.company_code', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='receive.ReturnAmountDetail.company_name', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receive.ReturnAmountDetail.demand_date', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='receive.ReturnAmountDetail.delivery_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='receive.ReturnAmountDetail.arrival_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_code', full_name='receive.ReturnAmountDetail.vendor_code', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_name', full_name='receive.ReturnAmountDetail.vendor_name', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_code', full_name='receive.ReturnAmountDetail.return_code', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='receive.ReturnAmountDetail.order_code', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='primary_key', full_name='receive.ReturnAmountDetail.primary_key', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='receive.ReturnAmountDetail.batch_code', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1053,
  serialized_end=1767,
)


_GETUNFINISHRECEIVEPRODUCTSREQUEST = _descriptor.Descriptor(
  name='GetUnfinishReceiveProductsRequest',
  full_name='receive.GetUnfinishReceiveProductsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='receive.GetUnfinishReceiveProductsRequest.receive_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1769,
  serialized_end=1824,
)


_GETUNFINISHRECEIVEPRODUCTSRESPONSE_UNFINISHPRODUCTSMAPENTRY = _descriptor.Descriptor(
  name='UnfinishProductsMapEntry',
  full_name='receive.GetUnfinishReceiveProductsResponse.UnfinishProductsMapEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='receive.GetUnfinishReceiveProductsResponse.UnfinishProductsMapEntry.key', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='receive.GetUnfinishReceiveProductsResponse.UnfinishProductsMapEntry.value', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1966,
  serialized_end=2024,
)

_GETUNFINISHRECEIVEPRODUCTSRESPONSE = _descriptor.Descriptor(
  name='GetUnfinishReceiveProductsResponse',
  full_name='receive.GetUnfinishReceiveProductsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='unfinish_products_map', full_name='receive.GetUnfinishReceiveProductsResponse.unfinish_products_map', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETUNFINISHRECEIVEPRODUCTSRESPONSE_UNFINISHPRODUCTSMAPENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1827,
  serialized_end=2024,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='receive.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.Response.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.Response.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='receive.Response.msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2026,
  serialized_end=2077,
)


_RECEIVE = _descriptor.Descriptor(
  name='Receive',
  full_name='receive.Receive',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.Receive.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receive.Receive.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='receive.Receive.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='receive.Receive.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='receive.Receive.batch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='receive.Receive.order_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='receive.Receive.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='receive.Receive.delivery_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='receive.Receive.delivery_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='receive.Receive.receive_by', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receive.Receive.delivery_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.Receive.status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='receive.Receive.process_status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receive.Receive.demand_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='receive.Receive.delivery_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='receive.Receive.expect_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='receive.Receive.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='receive.Receive.storage_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.Receive.distr_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='third_party_id', full_name='receive.Receive.third_party_id', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='third_party_type', full_name='receive.Receive.third_party_type', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='receive.Receive.product_nums', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='receive.Receive.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='receive.Receive.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='receive.Receive.created_by', index=24,
      number=25, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='receive.Receive.updated_by', index=25,
      number=26, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='receive.Receive.partner_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='receive.Receive.created_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='receive.Receive.updated_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receive.Receive.main_branch_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='receive.Receive.cost_trans_status', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_update', full_name='receive.Receive.cost_update', index=31,
      number=32, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='receive.Receive.cost_center_id', index=32,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='receive.Receive.remark', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='receive.Receive.reason', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.Receive.sub_account_type', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='receive.Receive.sub_receive_by', index=36,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='receive.Receive.sub_delivery_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='receive.Receive.demand_type', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='receive.Receive.delivery_by_code', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='receive.Receive.delivery_by_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='receive.Receive.franchisee_id', index=41,
      number=42, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='receive.Receive.sum_price_tax', index=42,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='receive.Receive.products', index=43,
      number=45, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_code', full_name='receive.Receive.receive_by_code', index=44,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_name', full_name='receive.Receive.receive_by_name', index=45,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='receive.Receive.contact', index=46,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='receive.Receive.tel', index=47,
      number=49, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_remark', full_name='receive.Receive.source_remark', index=48,
      number=50, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='receive.Receive.order_type_id', index=49,
      number=51, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_remark', full_name='receive.Receive.demand_remark', index=50,
      number=52, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2080,
  serialized_end=3358,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='receive.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='receive.Product.receive_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='receive.Product.receive_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receive.Product.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receive.Product.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receive.Product.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='receive.Product.storage_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='receive.Product.order_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='receive.Product.delivery_quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_quantity', full_name='receive.Product.receive_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='receive.Product.is_confirmed', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='receive.Product.created_at', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='receive.Product.created_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='receive.Product.updated_at', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='receive.Product.updated_by', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='receive.Product.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='receive.Product.created_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='receive.Product.updated_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receive.Product.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receive.Product.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='receive.Product.unit_spec', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='receive.Product.category_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='receive.Product.category_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='receive.Product.category_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='receive.Product.cost_price', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='receive.Product.tax_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='receive.Product.tax_rate', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='receive.Product.unit_rate', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='receive.Product.sale_type', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.Product.sub_account_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='receive.Product.sub_receive_by', index=30,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='receive.Product.sub_delivery_by', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_receive_quantity', full_name='receive.Product.actual_receive_quantity', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='receive.Product.s_diff_quantity', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_remaining_qty', full_name='receive.Product.min_remaining_qty', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_remaining_qty', full_name='receive.Product.max_remaining_qty', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_deviation', full_name='receive.Product.check_deviation', index=36,
      number=37, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='temp_quantity', full_name='receive.Product.temp_quantity', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3361,
  serialized_end=4259,
)


_CREATERECEIVEREQUEST = _descriptor.Descriptor(
  name='CreateReceiveRequest',
  full_name='receive.CreateReceiveRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.CreateReceiveRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receive.CreateReceiveRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='receive.CreateReceiveRequest.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='receive.CreateReceiveRequest.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='receive.CreateReceiveRequest.batch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='receive.CreateReceiveRequest.order_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='receive.CreateReceiveRequest.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='receive.CreateReceiveRequest.delivery_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='receive.CreateReceiveRequest.delivery_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='receive.CreateReceiveRequest.receive_by', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receive.CreateReceiveRequest.delivery_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='receive.CreateReceiveRequest.storage_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.CreateReceiveRequest.distr_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receive.CreateReceiveRequest.demand_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='receive.CreateReceiveRequest.delivery_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='receive.CreateReceiveRequest.expect_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='receive.CreateReceiveRequest.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='receive.CreateReceiveRequest.products', index=17,
      number=18, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receive.CreateReceiveRequest.main_branch_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='receive.CreateReceiveRequest.remark', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='receive.CreateReceiveRequest.reason', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.CreateReceiveRequest.sub_account_type', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='receive.CreateReceiveRequest.sub_receive_by', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='receive.CreateReceiveRequest.sub_delivery_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='receive.CreateReceiveRequest.demand_type', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_remark', full_name='receive.CreateReceiveRequest.source_remark', index=25,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='receive.CreateReceiveRequest.order_type_id', index=26,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='receive.CreateReceiveRequest.franchisee_id', index=27,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4262,
  serialized_end=4991,
)


_UPDATERECEIVEPRODUCTSREQUEST = _descriptor.Descriptor(
  name='UpdateReceiveProductsRequest',
  full_name='receive.UpdateReceiveProductsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.UpdateReceiveProductsRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.UpdateReceiveProductsRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='receive.UpdateReceiveProductsRequest.process_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='receive.UpdateReceiveProductsRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='receive.UpdateReceiveProductsRequest.batch_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='receive.UpdateReceiveProductsRequest.order_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='receive.UpdateReceiveProductsRequest.remark', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='receive.UpdateReceiveProductsRequest.reason', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.UpdateReceiveProductsRequest.distr_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='receive.UpdateReceiveProductsRequest.receive_by', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receive.UpdateReceiveProductsRequest.delivery_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_remark', full_name='receive.UpdateReceiveProductsRequest.source_remark', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='receive.UpdateReceiveProductsRequest.expect_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4994,
  serialized_end=5313,
)


_LISTRECEIVEREQUEST = _descriptor.Descriptor(
  name='ListReceiveRequest',
  full_name='receive.ListReceiveRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='receive.ListReceiveRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='receive.ListReceiveRequest.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='receive.ListReceiveRequest.delivery_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='receive.ListReceiveRequest.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='receive.ListReceiveRequest.delivery_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receive.ListReceiveRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='receive.ListReceiveRequest.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='receive.ListReceiveRequest.receive_bys', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='receive.ListReceiveRequest.delivery_bys', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.ListReceiveRequest.status', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='receive.ListReceiveRequest.process_status', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receive.ListReceiveRequest.start_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receive.ListReceiveRequest.end_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.ListReceiveRequest.distr_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='receive.ListReceiveRequest.batch_type', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receive.ListReceiveRequest.limit', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receive.ListReceiveRequest.offset', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='receive.ListReceiveRequest.sort', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='receive.ListReceiveRequest.order', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receive.ListReceiveRequest.main_branch_type', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_start_date', full_name='receive.ListReceiveRequest.delivery_start_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_end_date', full_name='receive.ListReceiveRequest.delivery_end_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='receive.ListReceiveRequest.cost_trans_status', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_start_date', full_name='receive.ListReceiveRequest.received_start_date', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_end_date', full_name='receive.ListReceiveRequest.received_end_date', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.ListReceiveRequest.sub_account_type', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_bys', full_name='receive.ListReceiveRequest.sub_receive_bys', index=26,
      number=27, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_bys', full_name='receive.ListReceiveRequest.sub_delivery_bys', index=27,
      number=28, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_regions', full_name='receive.ListReceiveRequest.geo_regions', index=28,
      number=29, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='receive.ListReceiveRequest.demand_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='receive.ListReceiveRequest.product_ids', index=30,
      number=31, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_products', full_name='receive.ListReceiveRequest.include_products', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='receive.ListReceiveRequest.ids', index=32,
      number=33, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_start_date', full_name='receive.ListReceiveRequest.expect_start_date', index=33,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_end_date', full_name='receive.ListReceiveRequest.expect_end_date', index=34,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_ids', full_name='receive.ListReceiveRequest.batch_ids', index=35,
      number=36, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5316,
  serialized_end=6330,
)


_LISTRECEIVESREQUEST = _descriptor.Descriptor(
  name='ListReceivesRequest',
  full_name='receive.ListReceivesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='receive.ListReceivesRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='receive.ListReceivesRequest.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='receive.ListReceivesRequest.delivery_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='receive.ListReceivesRequest.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='receive.ListReceivesRequest.delivery_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receive.ListReceivesRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='receive.ListReceivesRequest.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='receive.ListReceivesRequest.receive_bys', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='receive.ListReceivesRequest.delivery_bys', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.ListReceivesRequest.status', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='receive.ListReceivesRequest.process_status', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receive.ListReceivesRequest.start_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receive.ListReceivesRequest.end_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.ListReceivesRequest.distr_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='receive.ListReceivesRequest.batch_type', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receive.ListReceivesRequest.limit', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receive.ListReceivesRequest.offset', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='receive.ListReceivesRequest.sort', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='receive.ListReceivesRequest.order', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receive.ListReceivesRequest.main_branch_type', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_start_date', full_name='receive.ListReceivesRequest.delivery_start_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_end_date', full_name='receive.ListReceivesRequest.delivery_end_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='receive.ListReceivesRequest.cost_trans_status', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_start_date', full_name='receive.ListReceivesRequest.received_start_date', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_end_date', full_name='receive.ListReceivesRequest.received_end_date', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.ListReceivesRequest.sub_account_type', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_bys', full_name='receive.ListReceivesRequest.sub_receive_bys', index=26,
      number=27, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_bys', full_name='receive.ListReceivesRequest.sub_delivery_bys', index=27,
      number=28, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_regions', full_name='receive.ListReceivesRequest.geo_regions', index=28,
      number=29, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='receive.ListReceivesRequest.demand_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='receive.ListReceivesRequest.product_ids', index=30,
      number=31, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_products', full_name='receive.ListReceivesRequest.include_products', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='receive.ListReceivesRequest.ids', index=32,
      number=33, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_start_date', full_name='receive.ListReceivesRequest.expect_start_date', index=33,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_end_date', full_name='receive.ListReceivesRequest.expect_end_date', index=34,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6333,
  serialized_end=7329,
)


_LISTRECEIVERESPONSE = _descriptor.Descriptor(
  name='ListReceiveResponse',
  full_name='receive.ListReceiveResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receive.ListReceiveResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receive.ListReceiveResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7331,
  serialized_end=7399,
)


_GETRECEIVEBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceiveByIdRequest',
  full_name='receive.GetReceiveByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.GetReceiveByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7401,
  serialized_end=7436,
)


_GETRECEIVEBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReceiveByIdResponse',
  full_name='receive.GetReceiveByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receive', full_name='receive.GetReceiveByIdResponse.receive', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7438,
  serialized_end=7497,
)


_GETRECEIVEPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceiveProductByIdRequest',
  full_name='receive.GetReceiveProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.GetReceiveProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receive.GetReceiveProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receive.GetReceiveProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receive.GetReceiveProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='receive.GetReceiveProductByIdRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='receive.GetReceiveProductByIdRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7499,
  serialized_end=7624,
)


_DEALRECEIVEBYIDREQUEST = _descriptor.Descriptor(
  name='DealReceiveByIdRequest',
  full_name='receive.DealReceiveByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.DealReceiveByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='receive.DealReceiveByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='receive.DealReceiveByIdRequest.products', index=2,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7626,
  serialized_end=7714,
)


_GETRECEIVEPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReceiveProductByIdResponse',
  full_name='receive.GetReceiveProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receive.GetReceiveProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receive.GetReceiveProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7716,
  serialized_end=7794,
)


_GETRECEIVINGCOLLECTREQUEST = _descriptor.Descriptor(
  name='GetReceivingCollectRequest',
  full_name='receive.GetReceivingCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='receive.GetReceivingCollectRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='receive.GetReceivingCollectRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receive.GetReceivingCollectRequest.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receive.GetReceivingCollectRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receive.GetReceivingCollectRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receive.GetReceivingCollectRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receive.GetReceivingCollectRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receive.GetReceivingCollectRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receive.GetReceivingCollectRequest.main_branch_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='receive.GetReceivingCollectRequest.delivery_bys', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_types', full_name='receive.GetReceivingCollectRequest.batch_types', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_start_date', full_name='receive.GetReceivingCollectRequest.received_start_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_end_date', full_name='receive.GetReceivingCollectRequest.received_end_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.GetReceivingCollectRequest.distr_type', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_by', full_name='receive.GetReceivingCollectRequest.group_by', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.GetReceivingCollectRequest.status', index=15,
      number=16, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7797,
  serialized_end=8268,
)


_GETRECEIVINGCOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetReceivingCollectResponse',
  full_name='receive.GetReceivingCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receive.GetReceivingCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receive.GetReceivingCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8270,
  serialized_end=8371,
)


_GETRECEIVINGDETAILREQUEST = _descriptor.Descriptor(
  name='GetReceivingDetailRequest',
  full_name='receive.GetReceivingDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='receive.GetReceivingDetailRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='receive.GetReceivingDetailRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receive.GetReceivingDetailRequest.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receive.GetReceivingDetailRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receive.GetReceivingDetailRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receive.GetReceivingDetailRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receive.GetReceivingDetailRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receive.GetReceivingDetailRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receive.GetReceivingDetailRequest.code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receive.GetReceivingDetailRequest.main_branch_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='receive.GetReceivingDetailRequest.delivery_bys', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_types', full_name='receive.GetReceivingDetailRequest.batch_types', index=11,
      number=12, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='receive.GetReceivingDetailRequest.product_ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_start_date', full_name='receive.GetReceivingDetailRequest.received_start_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_end_date', full_name='receive.GetReceivingDetailRequest.received_end_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.GetReceivingDetailRequest.distr_type', index=15,
      number=16, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.GetReceivingDetailRequest.status', index=16,
      number=17, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='receive.GetReceivingDetailRequest.demand_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8374,
  serialized_end=8882,
)


_GETRECEIVINGDETAILRESPONSE = _descriptor.Descriptor(
  name='GetReceivingDetailResponse',
  full_name='receive.GetReceivingDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receive.GetReceivingDetailResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receive.GetReceivingDetailResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8884,
  serialized_end=8985,
)


_TOTAL = _descriptor.Descriptor(
  name='Total',
  full_name='receive.Total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='receive.Total.count', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_order_quantity', full_name='receive.Total.sum_order_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_delivery_quantity', full_name='receive.Total.sum_delivery_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_receive_quantity', full_name='receive.Total.sum_receive_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_cost_price', full_name='receive.Total.sum_cost_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_tax_price', full_name='receive.Total.sum_tax_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='receive.Total.tax_rate', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_pick_quantity', full_name='receive.Total.sum_pick_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_order_amount', full_name='receive.Total.sum_order_amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_delivery_amount', full_name='receive.Total.sum_delivery_amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_receive_amount', full_name='receive.Total.sum_receive_amount', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8988,
  serialized_end=9274,
)


_RECEIVINGCOLLECT = _descriptor.Descriptor(
  name='ReceivingCollect',
  full_name='receive.ReceivingCollect',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='receive.ReceivingCollect.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='receive.ReceivingCollect.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='receive.ReceivingCollect.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='receive.ReceivingCollect.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='receive.ReceivingCollect.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='receive.ReceivingCollect.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='receive.ReceivingCollect.delivery_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receive.ReceivingCollect.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receive.ReceivingCollect.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receive.ReceivingCollect.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='receive.ReceivingCollect.product_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='receive.ReceivingCollect.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_quantity', full_name='receive.ReceivingCollect.receive_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='receive.ReceivingCollect.order_quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='receive.ReceivingCollect.s_diff_quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='receive.ReceivingCollect.receive_by', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_code', full_name='receive.ReceivingCollect.receive_by_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_name', full_name='receive.ReceivingCollect.receive_by_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receive.ReceivingCollect.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receive.ReceivingCollect.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='receive.ReceivingCollect.cost_price', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='receive.ReceivingCollect.tax_price', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='receive.ReceivingCollect.tax_rate', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='receive.ReceivingCollect.tax', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.ReceivingCollect.sub_account_type', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='receive.ReceivingCollect.sub_receive_by', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='receive.ReceivingCollect.sub_delivery_by', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by_code', full_name='receive.ReceivingCollect.sub_receive_by_code', index=27,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by_name', full_name='receive.ReceivingCollect.sub_receive_by_name', index=28,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receive.ReceivingCollect.delivery_by', index=29,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='receive.ReceivingCollect.delivery_by_code', index=30,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='receive.ReceivingCollect.delivery_by_name', index=31,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.ReceivingCollect.distr_type', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='receive.ReceivingCollect.franchisee_id', index=33,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='receive.ReceivingCollect.demand_type', index=34,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9277,
  serialized_end=10097,
)


_RECEIVINGDETAILED = _descriptor.Descriptor(
  name='ReceivingDetailed',
  full_name='receive.ReceivingDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='receive.ReceivingDetailed.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='receive.ReceivingDetailed.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='receive.ReceivingDetailed.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='receive.ReceivingDetailed.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='receive.ReceivingDetailed.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='receive.ReceivingDetailed.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='receive.ReceivingDetailed.delivery_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receive.ReceivingDetailed.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receive.ReceivingDetailed.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receive.ReceivingDetailed.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='receive.ReceivingDetailed.product_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='receive.ReceivingDetailed.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_quantity', full_name='receive.ReceivingDetailed.receive_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='receive.ReceivingDetailed.order_quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='receive.ReceivingDetailed.s_diff_quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='receive.ReceivingDetailed.receive_by', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_code', full_name='receive.ReceivingDetailed.receive_by_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_name', full_name='receive.ReceivingDetailed.receive_by_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receive.ReceivingDetailed.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receive.ReceivingDetailed.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='receive.ReceivingDetailed.delivery_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receive.ReceivingDetailed.demand_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='receive.ReceivingDetailed.receiving_code', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_date', full_name='receive.ReceivingDetailed.receiving_date', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='receive.ReceivingDetailed.receiving_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.ReceivingDetailed.id', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='receive.ReceivingDetailed.cost_price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='receive.ReceivingDetailed.tax_price', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='receive.ReceivingDetailed.tax_rate', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receive.ReceivingDetailed.delivery_by', index=29,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='receive.ReceivingDetailed.delivery_by_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='receive.ReceivingDetailed.delivery_by_name', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='receive.ReceivingDetailed.tax', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.ReceivingDetailed.sub_account_type', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='receive.ReceivingDetailed.sub_receive_by', index=34,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='receive.ReceivingDetailed.sub_delivery_by', index=35,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by_code', full_name='receive.ReceivingDetailed.sub_receive_by_code', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by_name', full_name='receive.ReceivingDetailed.sub_receive_by_name', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='receive.ReceivingDetailed.batch_code', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='receive.ReceivingDetailed.created_by', index=39,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='receive.ReceivingDetailed.expect_date', index=40,
      number=41, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receive.ReceivingDetailed.status', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='receive.ReceivingDetailed.reason', index=42,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receive.ReceivingDetailed.distr_type', index=43,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='receive.ReceivingDetailed.franchisee_id', index=44,
      number=45, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10100,
  serialized_end=11231,
)


_GETTOTALREPORTREQUEST = _descriptor.Descriptor(
  name='GetTotalReportRequest',
  full_name='receive.GetTotalReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='receive.GetTotalReportRequest.receive_bys', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='receive.GetTotalReportRequest.delivery_bys', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='receive.GetTotalReportRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_types', full_name='receive.GetTotalReportRequest.batch_types', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receive.GetTotalReportRequest.main_branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receive.GetTotalReportRequest.start_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receive.GetTotalReportRequest.end_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receive.GetTotalReportRequest.limit', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receive.GetTotalReportRequest.offset', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.GetTotalReportRequest.sub_account_type', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_bys', full_name='receive.GetTotalReportRequest.sub_receive_bys', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_bys', full_name='receive.GetTotalReportRequest.sub_delivery_bys', index=11,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11234,
  serialized_end=11570,
)


_GETTOTALREPORTRESPONSE = _descriptor.Descriptor(
  name='GetTotalReportResponse',
  full_name='receive.GetTotalReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receive.GetTotalReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receive.GetTotalReportResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11572,
  serialized_end=11665,
)


_TOTALDETAILED = _descriptor.Descriptor(
  name='TotalDetailed',
  full_name='receive.TotalDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_id', full_name='receive.TotalDetailed.category_id', index=0,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='receive.TotalDetailed.category_code', index=1,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='receive.TotalDetailed.category_name', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_quantity', full_name='receive.TotalDetailed.receive_quantity', index=3,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='receive.TotalDetailed.delivery_quantity', index=4,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receive.TotalDetailed.product_id', index=5,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receive.TotalDetailed.product_code', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receive.TotalDetailed.product_name', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='receive.TotalDetailed.product_spec', index=8,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receive.TotalDetailed.delivery_by', index=9,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='receive.TotalDetailed.delivery_by_code', index=10,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='receive.TotalDetailed.delivery_by_name', index=11,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='receive.TotalDetailed.receive_by', index=12,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_code', full_name='receive.TotalDetailed.receive_by_code', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_name', full_name='receive.TotalDetailed.receive_by_name', index=14,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receive.TotalDetailed.unit_id', index=15,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receive.TotalDetailed.unit_name', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receive.TotalDetailed.sub_account_type', index=17,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='receive.TotalDetailed.sub_receive_by', index=18,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='receive.TotalDetailed.sub_delivery_by', index=19,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='receive.TotalDetailed.id', index=20,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_amount', full_name='receive.TotalDetailed.receive_amount', index=21,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_amount', full_name='receive.TotalDetailed.delivery_amount', index=22,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_tax_amount', full_name='receive.TotalDetailed.receive_tax_amount', index=23,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_tax_amount', full_name='receive.TotalDetailed.delivery_tax_amount', index=24,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_amount', full_name='receive.TotalDetailed.order_amount', index=25,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_tax_amount', full_name='receive.TotalDetailed.order_tax_amount', index=26,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='receive.TotalDetailed.order_quantity', index=27,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='receive.TotalDetailed.batch_id', index=28,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='receive.TotalDetailed.product_status', index=29,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='receive.TotalDetailed.franchisee_id', index=30,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11668,
  serialized_end=12398,
)


_GETRECEIVEPRODUCTBYCODEREQUEST = _descriptor.Descriptor(
  name='GetReceiveProductByCodeRequest',
  full_name='receive.GetReceiveProductByCodeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='receive.GetReceiveProductByCodeRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12400,
  serialized_end=12446,
)


_GETRECEIVEPRODUCTBYCODERESPONSE = _descriptor.Descriptor(
  name='GetReceiveProductByCodeResponse',
  full_name='receive.GetReceiveProductByCodeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receive.GetReceiveProductByCodeResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receive.GetReceiveProductByCodeResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12448,
  serialized_end=12528,
)

_GETRETURNAMOUNTREPORTREQUEST.fields_by_name['delivery_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRETURNAMOUNTREPORTREQUEST.fields_by_name['delivery_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRETURNAMOUNTREPORTREQUEST.fields_by_name['demand_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRETURNAMOUNTREPORTREQUEST.fields_by_name['demand_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRETURNAMOUNTREPORTREQUEST.fields_by_name['arrival_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRETURNAMOUNTREPORTREQUEST.fields_by_name['arrival_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRETURNAMOUNTREPORTRESPONSE.fields_by_name['rows'].message_type = _RETURNAMOUNTDETAIL
_GETRETURNAMOUNTREPORTRESPONSE.fields_by_name['sum'].message_type = _RETURNAMOUNTSUM
_RETURNAMOUNTDETAIL.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNAMOUNTDETAIL.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNAMOUNTDETAIL.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNFINISHRECEIVEPRODUCTSRESPONSE_UNFINISHPRODUCTSMAPENTRY.containing_type = _GETUNFINISHRECEIVEPRODUCTSRESPONSE
_GETUNFINISHRECEIVEPRODUCTSRESPONSE.fields_by_name['unfinish_products_map'].message_type = _GETUNFINISHRECEIVEPRODUCTSRESPONSE_UNFINISHPRODUCTSMAPENTRY
_RECEIVE.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVE.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVE.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVE.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVE.fields_by_name['products'].message_type = _PRODUCT
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVEREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVEREQUEST.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVEREQUEST.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVEREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVEREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATERECEIVEPRODUCTSREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATERECEIVEPRODUCTSREQUEST.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEREQUEST.fields_by_name['delivery_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEREQUEST.fields_by_name['delivery_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEREQUEST.fields_by_name['received_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEREQUEST.fields_by_name['received_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEREQUEST.fields_by_name['expect_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEREQUEST.fields_by_name['expect_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVESREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVESREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVESREQUEST.fields_by_name['delivery_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVESREQUEST.fields_by_name['delivery_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVESREQUEST.fields_by_name['received_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVESREQUEST.fields_by_name['received_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVESREQUEST.fields_by_name['expect_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVESREQUEST.fields_by_name['expect_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVERESPONSE.fields_by_name['rows'].message_type = _RECEIVE
_GETRECEIVEBYIDRESPONSE.fields_by_name['receive'].message_type = _RECEIVE
_DEALRECEIVEBYIDREQUEST.fields_by_name['products'].message_type = _PRODUCT
_GETRECEIVEPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
_GETRECEIVINGCOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGCOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGCOLLECTREQUEST.fields_by_name['received_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGCOLLECTREQUEST.fields_by_name['received_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGCOLLECTRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGCOLLECT
_GETRECEIVINGCOLLECTRESPONSE.fields_by_name['total'].message_type = _TOTAL
_GETRECEIVINGDETAILREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDETAILREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDETAILREQUEST.fields_by_name['received_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDETAILREQUEST.fields_by_name['received_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDETAILRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGDETAILED
_GETRECEIVINGDETAILRESPONSE.fields_by_name['total'].message_type = _TOTAL
_RECEIVINGDETAILED.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDETAILED.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDETAILED.fields_by_name['receiving_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDETAILED.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTOTALREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTOTALREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTOTALREPORTRESPONSE.fields_by_name['rows'].message_type = _TOTALDETAILED
_GETTOTALREPORTRESPONSE.fields_by_name['total'].message_type = _TOTAL
_GETRECEIVEPRODUCTBYCODERESPONSE.fields_by_name['rows'].message_type = _PRODUCT
DESCRIPTOR.message_types_by_name['GetReturnAmountReportRequest'] = _GETRETURNAMOUNTREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetReturnAmountReportResponse'] = _GETRETURNAMOUNTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['ReturnAmountSum'] = _RETURNAMOUNTSUM
DESCRIPTOR.message_types_by_name['ReturnAmountDetail'] = _RETURNAMOUNTDETAIL
DESCRIPTOR.message_types_by_name['GetUnfinishReceiveProductsRequest'] = _GETUNFINISHRECEIVEPRODUCTSREQUEST
DESCRIPTOR.message_types_by_name['GetUnfinishReceiveProductsResponse'] = _GETUNFINISHRECEIVEPRODUCTSRESPONSE
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['Receive'] = _RECEIVE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['CreateReceiveRequest'] = _CREATERECEIVEREQUEST
DESCRIPTOR.message_types_by_name['UpdateReceiveProductsRequest'] = _UPDATERECEIVEPRODUCTSREQUEST
DESCRIPTOR.message_types_by_name['ListReceiveRequest'] = _LISTRECEIVEREQUEST
DESCRIPTOR.message_types_by_name['ListReceivesRequest'] = _LISTRECEIVESREQUEST
DESCRIPTOR.message_types_by_name['ListReceiveResponse'] = _LISTRECEIVERESPONSE
DESCRIPTOR.message_types_by_name['GetReceiveByIdRequest'] = _GETRECEIVEBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveByIdResponse'] = _GETRECEIVEBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetReceiveProductByIdRequest'] = _GETRECEIVEPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['DealReceiveByIdRequest'] = _DEALRECEIVEBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveProductByIdResponse'] = _GETRECEIVEPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetReceivingCollectRequest'] = _GETRECEIVINGCOLLECTREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingCollectResponse'] = _GETRECEIVINGCOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['GetReceivingDetailRequest'] = _GETRECEIVINGDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingDetailResponse'] = _GETRECEIVINGDETAILRESPONSE
DESCRIPTOR.message_types_by_name['Total'] = _TOTAL
DESCRIPTOR.message_types_by_name['ReceivingCollect'] = _RECEIVINGCOLLECT
DESCRIPTOR.message_types_by_name['ReceivingDetailed'] = _RECEIVINGDETAILED
DESCRIPTOR.message_types_by_name['GetTotalReportRequest'] = _GETTOTALREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetTotalReportResponse'] = _GETTOTALREPORTRESPONSE
DESCRIPTOR.message_types_by_name['TotalDetailed'] = _TOTALDETAILED
DESCRIPTOR.message_types_by_name['GetReceiveProductByCodeRequest'] = _GETRECEIVEPRODUCTBYCODEREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveProductByCodeResponse'] = _GETRECEIVEPRODUCTBYCODERESPONSE
DESCRIPTOR.enum_types_by_name['ReceiptStatus'] = _RECEIPTSTATUS
DESCRIPTOR.enum_types_by_name['ProcessStatus'] = _PROCESSSTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetReturnAmountReportRequest = _reflection.GeneratedProtocolMessageType('GetReturnAmountReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNAMOUNTREPORTREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReturnAmountReportRequest)
  ))
_sym_db.RegisterMessage(GetReturnAmountReportRequest)

GetReturnAmountReportResponse = _reflection.GeneratedProtocolMessageType('GetReturnAmountReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNAMOUNTREPORTRESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReturnAmountReportResponse)
  ))
_sym_db.RegisterMessage(GetReturnAmountReportResponse)

ReturnAmountSum = _reflection.GeneratedProtocolMessageType('ReturnAmountSum', (_message.Message,), dict(
  DESCRIPTOR = _RETURNAMOUNTSUM,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.ReturnAmountSum)
  ))
_sym_db.RegisterMessage(ReturnAmountSum)

ReturnAmountDetail = _reflection.GeneratedProtocolMessageType('ReturnAmountDetail', (_message.Message,), dict(
  DESCRIPTOR = _RETURNAMOUNTDETAIL,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.ReturnAmountDetail)
  ))
_sym_db.RegisterMessage(ReturnAmountDetail)

GetUnfinishReceiveProductsRequest = _reflection.GeneratedProtocolMessageType('GetUnfinishReceiveProductsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETUNFINISHRECEIVEPRODUCTSREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetUnfinishReceiveProductsRequest)
  ))
_sym_db.RegisterMessage(GetUnfinishReceiveProductsRequest)

GetUnfinishReceiveProductsResponse = _reflection.GeneratedProtocolMessageType('GetUnfinishReceiveProductsResponse', (_message.Message,), dict(

  UnfinishProductsMapEntry = _reflection.GeneratedProtocolMessageType('UnfinishProductsMapEntry', (_message.Message,), dict(
    DESCRIPTOR = _GETUNFINISHRECEIVEPRODUCTSRESPONSE_UNFINISHPRODUCTSMAPENTRY,
    __module__ = 'receipt.receive_pb2'
    # @@protoc_insertion_point(class_scope:receive.GetUnfinishReceiveProductsResponse.UnfinishProductsMapEntry)
    ))
  ,
  DESCRIPTOR = _GETUNFINISHRECEIVEPRODUCTSRESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetUnfinishReceiveProductsResponse)
  ))
_sym_db.RegisterMessage(GetUnfinishReceiveProductsResponse)
_sym_db.RegisterMessage(GetUnfinishReceiveProductsResponse.UnfinishProductsMapEntry)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.Response)
  ))
_sym_db.RegisterMessage(Response)

Receive = _reflection.GeneratedProtocolMessageType('Receive', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.Receive)
  ))
_sym_db.RegisterMessage(Receive)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.Product)
  ))
_sym_db.RegisterMessage(Product)

CreateReceiveRequest = _reflection.GeneratedProtocolMessageType('CreateReceiveRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVEREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.CreateReceiveRequest)
  ))
_sym_db.RegisterMessage(CreateReceiveRequest)

UpdateReceiveProductsRequest = _reflection.GeneratedProtocolMessageType('UpdateReceiveProductsRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVEPRODUCTSREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.UpdateReceiveProductsRequest)
  ))
_sym_db.RegisterMessage(UpdateReceiveProductsRequest)

ListReceiveRequest = _reflection.GeneratedProtocolMessageType('ListReceiveRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVEREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.ListReceiveRequest)
  ))
_sym_db.RegisterMessage(ListReceiveRequest)

ListReceivesRequest = _reflection.GeneratedProtocolMessageType('ListReceivesRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVESREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.ListReceivesRequest)
  ))
_sym_db.RegisterMessage(ListReceivesRequest)

ListReceiveResponse = _reflection.GeneratedProtocolMessageType('ListReceiveResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVERESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.ListReceiveResponse)
  ))
_sym_db.RegisterMessage(ListReceiveResponse)

GetReceiveByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceiveByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEBYIDREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceiveByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceiveByIdRequest)

GetReceiveByIdResponse = _reflection.GeneratedProtocolMessageType('GetReceiveByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEBYIDRESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceiveByIdResponse)
  ))
_sym_db.RegisterMessage(GetReceiveByIdResponse)

GetReceiveProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceiveProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEPRODUCTBYIDREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceiveProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceiveProductByIdRequest)

DealReceiveByIdRequest = _reflection.GeneratedProtocolMessageType('DealReceiveByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALRECEIVEBYIDREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.DealReceiveByIdRequest)
  ))
_sym_db.RegisterMessage(DealReceiveByIdRequest)

GetReceiveProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReceiveProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEPRODUCTBYIDRESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceiveProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReceiveProductByIdResponse)

GetReceivingCollectRequest = _reflection.GeneratedProtocolMessageType('GetReceivingCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGCOLLECTREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceivingCollectRequest)
  ))
_sym_db.RegisterMessage(GetReceivingCollectRequest)

GetReceivingCollectResponse = _reflection.GeneratedProtocolMessageType('GetReceivingCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGCOLLECTRESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceivingCollectResponse)
  ))
_sym_db.RegisterMessage(GetReceivingCollectResponse)

GetReceivingDetailRequest = _reflection.GeneratedProtocolMessageType('GetReceivingDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDETAILREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceivingDetailRequest)
  ))
_sym_db.RegisterMessage(GetReceivingDetailRequest)

GetReceivingDetailResponse = _reflection.GeneratedProtocolMessageType('GetReceivingDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDETAILRESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceivingDetailResponse)
  ))
_sym_db.RegisterMessage(GetReceivingDetailResponse)

Total = _reflection.GeneratedProtocolMessageType('Total', (_message.Message,), dict(
  DESCRIPTOR = _TOTAL,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.Total)
  ))
_sym_db.RegisterMessage(Total)

ReceivingCollect = _reflection.GeneratedProtocolMessageType('ReceivingCollect', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGCOLLECT,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.ReceivingCollect)
  ))
_sym_db.RegisterMessage(ReceivingCollect)

ReceivingDetailed = _reflection.GeneratedProtocolMessageType('ReceivingDetailed', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGDETAILED,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.ReceivingDetailed)
  ))
_sym_db.RegisterMessage(ReceivingDetailed)

GetTotalReportRequest = _reflection.GeneratedProtocolMessageType('GetTotalReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTOTALREPORTREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetTotalReportRequest)
  ))
_sym_db.RegisterMessage(GetTotalReportRequest)

GetTotalReportResponse = _reflection.GeneratedProtocolMessageType('GetTotalReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTOTALREPORTRESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetTotalReportResponse)
  ))
_sym_db.RegisterMessage(GetTotalReportResponse)

TotalDetailed = _reflection.GeneratedProtocolMessageType('TotalDetailed', (_message.Message,), dict(
  DESCRIPTOR = _TOTALDETAILED,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.TotalDetailed)
  ))
_sym_db.RegisterMessage(TotalDetailed)

GetReceiveProductByCodeRequest = _reflection.GeneratedProtocolMessageType('GetReceiveProductByCodeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEPRODUCTBYCODEREQUEST,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceiveProductByCodeRequest)
  ))
_sym_db.RegisterMessage(GetReceiveProductByCodeRequest)

GetReceiveProductByCodeResponse = _reflection.GeneratedProtocolMessageType('GetReceiveProductByCodeResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEPRODUCTBYCODERESPONSE,
  __module__ = 'receipt.receive_pb2'
  # @@protoc_insertion_point(class_scope:receive.GetReceiveProductByCodeResponse)
  ))
_sym_db.RegisterMessage(GetReceiveProductByCodeResponse)


_GETUNFINISHRECEIVEPRODUCTSRESPONSE_UNFINISHPRODUCTSMAPENTRY._options = None

_RECEIVESERVICE = _descriptor.ServiceDescriptor(
  name='ReceiveService',
  full_name='receive.ReceiveService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=12714,
  serialized_end=14420,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateReceive',
    full_name='receive.ReceiveService.CreateReceive',
    index=0,
    containing_service=None,
    input_type=_CREATERECEIVEREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v2/receipt/receive:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReceive',
    full_name='receive.ReceiveService.ListReceive',
    index=1,
    containing_service=None,
    input_type=_LISTRECEIVEREQUEST,
    output_type=_LISTRECEIVERESPONSE,
    serialized_options=_b('\202\323\344\223\002\031\022\027/api/v2/receipt/receive'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceiveById',
    full_name='receive.ReceiveService.GetReceiveById',
    index=2,
    containing_service=None,
    input_type=_GETRECEIVEBYIDREQUEST,
    output_type=_RECEIVE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v2/receipt/receive/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceiveProductById',
    full_name='receive.ReceiveService.GetReceiveProductById',
    index=3,
    containing_service=None,
    input_type=_GETRECEIVEPRODUCTBYIDREQUEST,
    output_type=_GETRECEIVEPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/receipt/receive/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='DealReceiveById',
    full_name='receive.ReceiveService.DealReceiveById',
    index=4,
    containing_service=None,
    input_type=_DEALRECEIVEBYIDREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002*\032%/api/v2/receipt/receive/{id}/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReceiveProducts',
    full_name='receive.ReceiveService.UpdateReceiveProducts',
    index=5,
    containing_service=None,
    input_type=_UPDATERECEIVEPRODUCTSREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v2/receipt/receive/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingCollect',
    full_name='receive.ReceiveService.GetReceivingCollect',
    index=6,
    containing_service=None,
    input_type=_GETRECEIVINGCOLLECTREQUEST,
    output_type=_GETRECEIVINGCOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/receipt/receiving/bi/collect'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingDetail',
    full_name='receive.ReceiveService.GetReceivingDetail',
    index=7,
    containing_service=None,
    input_type=_GETRECEIVINGDETAILREQUEST,
    output_type=_GETRECEIVINGDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/receipt/receiving/bi/detailed'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTotalReport',
    full_name='receive.ReceiveService.GetTotalReport',
    index=8,
    containing_service=None,
    input_type=_GETTOTALREPORTREQUEST,
    output_type=_GETTOTALREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v2/receipt/total/report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceiveProductByCode',
    full_name='receive.ReceiveService.GetReceiveProductByCode',
    index=9,
    containing_service=None,
    input_type=_GETRECEIVEPRODUCTBYCODEREQUEST,
    output_type=_GETRECEIVEPRODUCTBYCODERESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/receipt/receive/{code}/product/bycode'),
  ),
  _descriptor.MethodDescriptor(
    name='GetUnfinishReceiveProducts',
    full_name='receive.ReceiveService.GetUnfinishReceiveProducts',
    index=10,
    containing_service=None,
    input_type=_GETUNFINISHRECEIVEPRODUCTSREQUEST,
    output_type=_GETUNFINISHRECEIVEPRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002\000'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReceives',
    full_name='receive.ReceiveService.ListReceives',
    index=11,
    containing_service=None,
    input_type=_LISTRECEIVESREQUEST,
    output_type=_LISTRECEIVERESPONSE,
    serialized_options=_b('\202\323\344\223\002\032\022\030/api/v2/receipt/receives'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnAmountReport',
    full_name='receive.ReceiveService.GetReturnAmountReport',
    index=12,
    containing_service=None,
    input_type=_GETRETURNAMOUNTREPORTREQUEST,
    output_type=_GETRETURNAMOUNTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/receipt/vendor/return/amount/report'),
  ),
])
_sym_db.RegisterServiceDescriptor(_RECEIVESERVICE)

DESCRIPTOR.services_by_name['ReceiveService'] = _RECEIVESERVICE

# @@protoc_insertion_point(module_scope)
