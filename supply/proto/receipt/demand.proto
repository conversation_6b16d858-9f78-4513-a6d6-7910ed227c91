syntax = "proto3";
package demand;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// DemandService 订单相关服务
service DemandService{
    
    // 创建订单
    rpc CreateDemand(CreateDemandRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/demand"
            body:"*"
        };
    }

    // 更新订单
    rpc UpdateDemand(UpdateDemandRequest) returns (Response){
        option (google.api.http) = {
            put:"/api/v2/receipt/demand/{id}/update"
            body:"*"
        };
    }

    // 查询订单
    rpc ListDemand(ListDemandRequest) returns (ListDemandResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/demand"
        };
    }

    // 根据id查询订单详情
    rpc GetDemandById(GetDemandByIdRequest) returns (Demand){
        option (google.api.http) = {
            get:"/api/v2/receipt/demand/{id}"
        };
    }

    // 根据demand_id查商品详情
    rpc GetDemandProductById (GetDemandProductByIdRequest) returns (GetDemandProductByIdResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/demand/{id}/product"
        };
    }

}

message Response{
    uint64 id = 1;
    string status = 2;
    string msg = 3;
}

message Demand{
    // 订单id
    uint64 id = 1;
    // 订单单号
    string code = 2;
    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型
    string batch_type = 5;

    // 订货单id
    uint64 order_id = 6;
    // 订货单单号
    string order_code = 7;
    // 发货单id
    uint64 delivery_id = 8;
    // 发货单单号
    string delivery_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 单据状态
    string status = 12;
    // 业务状态
    string process_status = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    // 储藏方式
    string storage_type = 18;
    // 物流类型
    string distr_type = 19;

    // 
    uint64 third_party_id = 20;
    string third_party_type = 21;
    // 商品数量
    uint64 product_nums = 22;

    // 创建时间
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人id
    int64 created_by = 25;
    // 更新人id
    int64 updated_by = 26;
    // 商户id
    uint64 partner_id = 27;
    // 创建人
    string created_name = 28;
    // 更新人
    string updated_name = 29;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 订单delivery_by是main_branch
    string main_branch_type = 30;
    // 子账户类型
    string sub_account_type = 31;
    // 子账户接收方
    uint64 sub_receive_by = 32;
    // 子账户发送方
    uint64 sub_delivery_by = 33;
    // 备注
    string remark = 34;
    // 原因
    string reason = 35;
}

message Product{
    // id
    uint64 id = 1;
    // 订单id
    uint64 demand_id = 2;
    // 收货门店
    uint64 receive_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6;
    //
    string storage_type = 7;

    // 订货数量
    double order_quantity = 8;
    // 收货数量
    double delivery_quantity = 9;
    // 拣货数量
    double pick_quantity = 10;

    // 收货状态
    bool is_confirmed = 11;

    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 创建人id
    uint64 created_by= 13;
    // 更新日期
    google.protobuf.Timestamp updated_at = 14;
    // 更新人id
    uint64 updated_by = 15;
    // 商户id
    uint64 partner_id = 16;
    // 创建人
    string created_name = 17;
    // 更新人
    string updated_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 单位规格
    string unit_spec = 21;
    // 商品分类
    uint64 category_id = 22;
    // 商品分类名称
    string category_name = 23;
    // 商品分类编号
    string category_code = 24;
    // 成本价
    double cost_price = 25;
    // 含税价
    double tax_price = 26;
    // 税率
    double tax_rate = 27;
    // 单位换算比例
    double unit_rate = 28;
    // 销售类型
    string sale_type = 29;
    // 子账户类型
    string sub_account_type = 30;
    // 子账户接收方
    uint64 sub_receive_by = 31;
    // 子账户发送方
    uint64 sub_delivery_by = 32;
}

message CreateDemandRequest{

    // 主单id
    uint64 batch_id = 1;
    // 主单单号
    string batch_code = 2;
    // 主单类型
    string batch_type = 3;

    // 订货单id
    uint64 order_id = 4;
    // 订货单单号
    string order_code = 5;
    // 发货单id
    uint64 delivery_id = 6;
    // 发货单单号
    string delivery_code = 7;

    // 接收门店-门店id
    uint64 receive_by = 8;
    // 配送方
    uint64 delivery_by = 9;

    // 储藏方式
    string storage_type = 10;
    // 物流类型
    string distr_type = 11;

    // 订货日期
    google.protobuf.Timestamp demand_date = 12;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 13;
    // 期望日期
    google.protobuf.Timestamp expect_date = 14;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 15;

    repeated Product products = 16;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 订单delivery_by是main_branch
    string main_branch_type = 17;
    // 子账户类型
    string sub_account_type = 18;
    // 子账户接收方
    uint64 sub_receive_by = 19;
    // 子账户发送方
    uint64 sub_delivery_by = 20;
}

message UpdateDemandRequest{
    // 订货单id
    uint64 id = 1;
    //
    string status = 2;
    //
    string process_status = 3;
    // product
    repeated Product product= 4;
}

message ListDemandRequest{
    // 订货单号
    uint64 order_id = 1;
    //
    uint64 batch_id = 2;
    //
    uint64 delivery_id = 3;
    // 订货单号
    string batch_code = 4;
    // 发货单号
    string delivery_code = 5;
    // 收货单号
    string code = 6;
    // 要货单号
    string order_code = 7;
    // 接受方id
    repeated uint64 receive_bys = 8;
    // 发送方
    repeated uint64 delivery_bys = 9;
    // 订单状态
    repeated string status = 10;
    // 订单状态
    repeated string process_status = 11;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 12;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 13;
    // 物流类型
    string distr_type = 14;
    // 业务类型
    repeated string batch_type = 15;
    // 分页大小
    int32 limit = 16;
    // 跳过行数
    int32 offset = 17;
    // 排序字段
    string sort = 18;
    // 排序顺序
    string order = 19;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 订单delivery_by是main_branch
    string main_branch_type = 20;
    // 子账户类型
    string sub_account_type = 21;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 22;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 23;
}

message ListDemandResponse{
    repeated Demand rows = 1;
    uint64 total = 2;
}

message GetDemandByIdRequest{
    // 订货单id(demand_id)
    uint64 id = 1;

}

message GetDemandByIdResponse{
    // wrapper.Status status = 1;
    Demand demand = 1;
}

message GetDemandProductByIdRequest{
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
}

message GetDemandProductByIdResponse{
    // wrapper.Status status = 1;
    repeated Product rows = 1;
    uint64 total = 2;
}





 