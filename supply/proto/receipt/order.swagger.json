{"swagger": "2.0", "info": {"title": "receipt/order.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/receipt/bi/order/report": {"get": {"summary": "要货单报表", "operationId": "QueryBiOrderReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderQueryOrderBiReportResponse"}}}, "parameters": [{"name": "start_demand_date", "description": "开始订货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_demand_date", "description": "结束订货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "start_arrival_date", "description": "开始到货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_arrival_date", "description": "结束到货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "classification", "description": "查询分类（汇总:sum, 明细:detail）.", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "订单分类(门市订货单:SD, 门市紧急订货单:HD, 主配单:MD).", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "description": "商品id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "category_ids", "description": "商品类别.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "store_ids", "description": "门店id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "description": "每页数量.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "偏移量.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}], "tags": ["OrderService"]}}, "/api/v2/receipt/order": {"get": {"summary": "查询订货单", "operationId": "ListOrderByBatchIds", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderListOrderResponse"}}}, "parameters": [{"name": "batch_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["OrderService"]}, "post": {"summary": "创建订货单", "operationId": "CreateOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderCreateOrderRequest"}}], "tags": ["OrderService"]}}, "/api/v2/receipt/order/{id}": {"get": {"summary": "根据id查询订货单详情", "operationId": "GetOrderById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderOrder"}}}, "parameters": [{"name": "id", "description": "订货单id(order_id)", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["OrderService"]}}, "/api/v2/receipt/order/{id}/product": {"get": {"summary": "根据Order_id查商品详情", "operationId": "GetOrderProductById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderGetOrderProductByIdResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}], "tags": ["OrderService"]}}, "/api/v2/receipt/order/{id}/update": {"post": {"summary": "更新订货单", "operationId": "UpdateOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderResponse"}}}, "parameters": [{"name": "id", "description": "订货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderUpdateOrderRequest"}}], "tags": ["OrderService"]}}}, "definitions": {"QueryOrderBiReportResponseOrderBiReport": {"type": "object", "properties": {"code": {"type": "string"}, "demand_date": {"type": "string", "format": "date-time"}, "expect_date": {"type": "string", "format": "date-time"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "store_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "quantity": {"type": "number", "format": "float"}, "unit_id": {"type": "string", "format": "uint64"}, "spec": {"type": "string"}, "unit_name": {"type": "string"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_quantity": {"type": "number", "format": "double"}, "product_second_code": {"type": "string"}}}, "orderCreateOrderRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "code": {"type": "string", "title": "code"}, "batch_id": {"type": "string", "format": "uint64", "title": "主单id"}, "batch_code": {"type": "string", "title": "主单单号"}, "batch_type": {"type": "string", "title": "主单类型 FRS_DEMAND-加盟商"}, "order_id": {"type": "string", "format": "uint64", "title": "订单id"}, "order_code": {"type": "string", "title": "订单单号"}, "delivery_id": {"type": "string", "format": "uint64", "title": "发货单id"}, "delivery_code": {"type": "string", "title": "发货单单号"}, "receive_by": {"type": "string", "format": "uint64", "title": "接收门店-门店id"}, "delivery_by": {"type": "string", "format": "uint64", "title": "配送方"}, "storage_type": {"type": "string", "title": "储藏方式"}, "distr_type": {"type": "string", "title": "物流类型"}, "demand_type": {"type": "string", "title": "订单类型"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "delivery_date": {"type": "string", "format": "date-time", "title": "配送时间"}, "expect_date": {"type": "string", "format": "date-time", "title": "期望日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "products": {"type": "array", "items": {"$ref": "#/definitions/orderProduct"}}, "main_branch_type": {"type": "string", "title": "单据main_branch类型：S-门店，W-仓库，V-供应商, FS-加盟店\n订货单receive_by是main_branch"}, "separate_no": {"type": "string", "title": "拆单编号"}, "sub_account_type": {"type": "string", "title": "子账户类型"}, "sub_receive_by": {"type": "string", "format": "uint64", "title": "子账户接收方"}, "sub_delivery_by": {"type": "string", "format": "uint64", "title": "子账户发送方"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商id"}, "source_remark": {"type": "string", "title": "订货单备注"}, "order_type_id": {"type": "string", "format": "uint64", "title": "订货类型"}}}, "orderGetOrderProductByIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/orderProduct"}, "title": "wrapper.Status status = 1;"}, "total": {"type": "string", "format": "uint64"}}}, "orderListOrderResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/orderOrder"}}, "total": {"type": "string", "format": "uint64"}}}, "orderOrder": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "订货单id"}, "code": {"type": "string", "title": "订货单单号"}, "batch_id": {"type": "string", "format": "uint64", "title": "主单id"}, "batch_code": {"type": "string", "title": "主单单号"}, "batch_type": {"type": "string", "title": "主单类型"}, "order_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "order_code": {"type": "string", "title": "订货单单号"}, "receive_id": {"type": "string", "format": "uint64", "title": "收货单id"}, "receive_code": {"type": "string", "title": "收货单单号"}, "receive_by": {"type": "string", "format": "uint64", "title": "接收门店-门店id"}, "delivery_by": {"type": "string", "format": "uint64", "title": "配送方"}, "status": {"type": "string", "title": "单据状态"}, "process_status": {"type": "string", "title": "业务状态"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "delivery_date": {"type": "string", "format": "date-time", "title": "配送时间"}, "expect_date": {"type": "string", "format": "date-time", "title": "期望日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "storage_type": {"type": "string", "title": "储藏方式"}, "distr_type": {"type": "string", "title": "物流类型"}, "demand_type": {"type": "string", "title": "订单类型"}, "third_party_id": {"type": "string"}, "third_party_type": {"type": "string"}, "product_nums": {"type": "string", "format": "uint64", "title": "商品数量"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "int64", "title": "创建人id"}, "updated_by": {"type": "string", "format": "int64", "title": "更新人id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "main_branch_type": {"type": "string", "title": "单据main_branch类型：S-门店，W-仓库，V-供应商\n订货单receive_by是main_branch"}, "separate_no": {"type": "string", "title": "拆单编号"}, "cost_center_id": {"type": "string", "format": "uint64", "title": "成本中心"}, "sub_account_type": {"type": "string", "title": "子账户类型"}, "sub_receive_by": {"type": "string", "format": "uint64", "title": "子账户接收方"}, "sub_delivery_by": {"type": "string", "format": "uint64", "title": "子账户发送方"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商id"}, "delivery_id": {"type": "string", "format": "uint64", "title": "发货单id"}, "delivery_code": {"type": "string", "title": "发货单单号"}, "source_remark": {"type": "string", "title": "订货单备注"}}}, "orderProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "order_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "order_by": {"type": "string", "format": "uint64", "title": "收货门店"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "storage_type": {"type": "string"}, "order_quantity": {"type": "number", "format": "double", "title": "订货数量"}, "delivery_quantity": {"type": "number", "format": "double", "title": "收货数量"}, "receive_quantity": {"type": "number", "format": "double", "title": "收货数量"}, "is_confirmed": {"type": "boolean", "format": "boolean", "title": "收货状态"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新日期"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_spec": {"type": "string", "title": "单位规格"}, "category_id": {"type": "string", "format": "uint64", "title": "商品分类"}, "category_name": {"type": "string", "title": "商品分类名称"}, "category_code": {"type": "string", "title": "商品分类编号"}, "cost_price": {"type": "number", "format": "double", "title": "成本价"}, "tax_price": {"type": "number", "format": "double", "title": "含税价"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "unit_rate": {"type": "number", "format": "double", "title": "单位换算比例"}, "sale_type": {"type": "string", "title": "销售类型"}}}, "orderQueryOrderBiReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/QueryOrderBiReportResponseOrderBiReport"}}, "total": {"type": "integer", "format": "int32"}, "classification": {"type": "string"}, "sum_quantity": {"type": "number", "format": "float"}, "sum_accounting_quantity": {"type": "number", "format": "float"}}, "title": "要货单报表请求返回"}, "orderResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "msg": {"type": "string"}}}, "orderUpdateOrderRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "订货单id"}, "status": {"type": "string"}, "process_status": {"type": "string"}, "product": {"type": "array", "items": {"$ref": "#/definitions/orderProduct"}, "title": "product"}, "remark": {"type": "string", "title": "订货单备注"}, "batch_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "source_remark": {"type": "string", "title": "来源备注"}}}}}