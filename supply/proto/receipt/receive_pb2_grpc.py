# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from receipt import receive_pb2 as receipt_dot_receive__pb2


class ReceiveServiceStub(object):
  """ReceiveService 收货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateReceive = channel.unary_unary(
        '/receive.ReceiveService/CreateReceive',
        request_serializer=receipt_dot_receive__pb2.CreateReceiveRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.Response.FromString,
        )
    self.ListReceive = channel.unary_unary(
        '/receive.ReceiveService/ListReceive',
        request_serializer=receipt_dot_receive__pb2.ListReceiveRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.ListReceiveResponse.FromString,
        )
    self.GetReceiveById = channel.unary_unary(
        '/receive.ReceiveService/GetReceiveById',
        request_serializer=receipt_dot_receive__pb2.GetReceiveByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.Receive.FromString,
        )
    self.GetReceiveProductById = channel.unary_unary(
        '/receive.ReceiveService/GetReceiveProductById',
        request_serializer=receipt_dot_receive__pb2.GetReceiveProductByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.GetReceiveProductByIdResponse.FromString,
        )
    self.DealReceiveById = channel.unary_unary(
        '/receive.ReceiveService/DealReceiveById',
        request_serializer=receipt_dot_receive__pb2.DealReceiveByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.Response.FromString,
        )
    self.UpdateReceiveProducts = channel.unary_unary(
        '/receive.ReceiveService/UpdateReceiveProducts',
        request_serializer=receipt_dot_receive__pb2.UpdateReceiveProductsRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.Response.FromString,
        )
    self.GetReceivingCollect = channel.unary_unary(
        '/receive.ReceiveService/GetReceivingCollect',
        request_serializer=receipt_dot_receive__pb2.GetReceivingCollectRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.GetReceivingCollectResponse.FromString,
        )
    self.GetReceivingDetail = channel.unary_unary(
        '/receive.ReceiveService/GetReceivingDetail',
        request_serializer=receipt_dot_receive__pb2.GetReceivingDetailRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.GetReceivingDetailResponse.FromString,
        )
    self.GetTotalReport = channel.unary_unary(
        '/receive.ReceiveService/GetTotalReport',
        request_serializer=receipt_dot_receive__pb2.GetTotalReportRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.GetTotalReportResponse.FromString,
        )
    self.GetReceiveProductByCode = channel.unary_unary(
        '/receive.ReceiveService/GetReceiveProductByCode',
        request_serializer=receipt_dot_receive__pb2.GetReceiveProductByCodeRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.GetReceiveProductByCodeResponse.FromString,
        )
    self.GetUnfinishReceiveProducts = channel.unary_unary(
        '/receive.ReceiveService/GetUnfinishReceiveProducts',
        request_serializer=receipt_dot_receive__pb2.GetUnfinishReceiveProductsRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.GetUnfinishReceiveProductsResponse.FromString,
        )
    self.ListReceives = channel.unary_unary(
        '/receive.ReceiveService/ListReceives',
        request_serializer=receipt_dot_receive__pb2.ListReceivesRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.ListReceiveResponse.FromString,
        )
    self.GetReturnAmountReport = channel.unary_unary(
        '/receive.ReceiveService/GetReturnAmountReport',
        request_serializer=receipt_dot_receive__pb2.GetReturnAmountReportRequest.SerializeToString,
        response_deserializer=receipt_dot_receive__pb2.GetReturnAmountReportResponse.FromString,
        )


class ReceiveServiceServicer(object):
  """ReceiveService 收货相关服务
  """

  def CreateReceive(self, request, context):
    """创建收货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReceive(self, request, context):
    """查询收货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveById(self, request, context):
    """根据id查询收货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveProductById(self, request, context):
    """根据receive_id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealReceiveById(self, request, context):
    """处理收货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReceiveProducts(self, request, context):
    """更新收货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingCollect(self, request, context):
    """查询收货单汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingDetail(self, request, context):
    """查询收货单详情报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTotalReport(self, request, context):
    """单据汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveProductByCode(self, request, context):
    """根据receive_code查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetUnfinishReceiveProducts(self, request, context):
    """根据receive_by查未收货的商品数量
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReceives(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnAmountReport(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ReceiveServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateReceive': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReceive,
          request_deserializer=receipt_dot_receive__pb2.CreateReceiveRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.Response.SerializeToString,
      ),
      'ListReceive': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceive,
          request_deserializer=receipt_dot_receive__pb2.ListReceiveRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.ListReceiveResponse.SerializeToString,
      ),
      'GetReceiveById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveById,
          request_deserializer=receipt_dot_receive__pb2.GetReceiveByIdRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.Receive.SerializeToString,
      ),
      'GetReceiveProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveProductById,
          request_deserializer=receipt_dot_receive__pb2.GetReceiveProductByIdRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.GetReceiveProductByIdResponse.SerializeToString,
      ),
      'DealReceiveById': grpc.unary_unary_rpc_method_handler(
          servicer.DealReceiveById,
          request_deserializer=receipt_dot_receive__pb2.DealReceiveByIdRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.Response.SerializeToString,
      ),
      'UpdateReceiveProducts': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReceiveProducts,
          request_deserializer=receipt_dot_receive__pb2.UpdateReceiveProductsRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.Response.SerializeToString,
      ),
      'GetReceivingCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingCollect,
          request_deserializer=receipt_dot_receive__pb2.GetReceivingCollectRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.GetReceivingCollectResponse.SerializeToString,
      ),
      'GetReceivingDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingDetail,
          request_deserializer=receipt_dot_receive__pb2.GetReceivingDetailRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.GetReceivingDetailResponse.SerializeToString,
      ),
      'GetTotalReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetTotalReport,
          request_deserializer=receipt_dot_receive__pb2.GetTotalReportRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.GetTotalReportResponse.SerializeToString,
      ),
      'GetReceiveProductByCode': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveProductByCode,
          request_deserializer=receipt_dot_receive__pb2.GetReceiveProductByCodeRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.GetReceiveProductByCodeResponse.SerializeToString,
      ),
      'GetUnfinishReceiveProducts': grpc.unary_unary_rpc_method_handler(
          servicer.GetUnfinishReceiveProducts,
          request_deserializer=receipt_dot_receive__pb2.GetUnfinishReceiveProductsRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.GetUnfinishReceiveProductsResponse.SerializeToString,
      ),
      'ListReceives': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceives,
          request_deserializer=receipt_dot_receive__pb2.ListReceivesRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.ListReceiveResponse.SerializeToString,
      ),
      'GetReturnAmountReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnAmountReport,
          request_deserializer=receipt_dot_receive__pb2.GetReturnAmountReportRequest.FromString,
          response_serializer=receipt_dot_receive__pb2.GetReturnAmountReportResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'receive.ReceiveService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
