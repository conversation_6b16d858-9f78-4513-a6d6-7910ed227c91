# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from receipt.franchisee import delivery_pb2 as receipt_dot_franchisee_dot_delivery__pb2


class DeliveryServiceStub(object):
  """DeliveryService 发货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListDelivery = channel.unary_unary(
        '/franchisee_delivery.DeliveryService/ListDelivery',
        request_serializer=receipt_dot_franchisee_dot_delivery__pb2.ListDeliveryRequest.SerializeToString,
        response_deserializer=receipt_dot_franchisee_dot_delivery__pb2.ListDeliveryResponse.FromString,
        )
    self.GetDeliveryDetail = channel.unary_unary(
        '/franchisee_delivery.DeliveryService/GetDeliveryDetail',
        request_serializer=receipt_dot_franchisee_dot_delivery__pb2.GetDeliveryDetailRequest.SerializeToString,
        response_deserializer=receipt_dot_franchisee_dot_delivery__pb2.GetDeliveryDetailResponse.FromString,
        )
    self.GetDeliveryById = channel.unary_unary(
        '/franchisee_delivery.DeliveryService/GetDeliveryById',
        request_serializer=receipt_dot_franchisee_dot_delivery__pb2.GetDeliveryByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_franchisee_dot_delivery__pb2.Delivery.FromString,
        )
    self.ListFranchiseeDeliverys = channel.unary_unary(
        '/franchisee_delivery.DeliveryService/ListFranchiseeDeliverys',
        request_serializer=receipt_dot_franchisee_dot_delivery__pb2.ListFranchiseeDeliverysRequest.SerializeToString,
        response_deserializer=receipt_dot_franchisee_dot_delivery__pb2.ListFranchiseeDeliverysRespone.FromString,
        )


class DeliveryServiceServicer(object):
  """DeliveryService 发货相关服务
  """

  def ListDelivery(self, request, context):
    """查询发货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDeliveryDetail(self, request, context):
    """查询发货单明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDeliveryById(self, request, context):
    """根据id查询发货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListFranchiseeDeliverys(self, request, context):
    """加盟商通过batch_ids反查接口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_DeliveryServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListDelivery': grpc.unary_unary_rpc_method_handler(
          servicer.ListDelivery,
          request_deserializer=receipt_dot_franchisee_dot_delivery__pb2.ListDeliveryRequest.FromString,
          response_serializer=receipt_dot_franchisee_dot_delivery__pb2.ListDeliveryResponse.SerializeToString,
      ),
      'GetDeliveryDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDeliveryDetail,
          request_deserializer=receipt_dot_franchisee_dot_delivery__pb2.GetDeliveryDetailRequest.FromString,
          response_serializer=receipt_dot_franchisee_dot_delivery__pb2.GetDeliveryDetailResponse.SerializeToString,
      ),
      'GetDeliveryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetDeliveryById,
          request_deserializer=receipt_dot_franchisee_dot_delivery__pb2.GetDeliveryByIdRequest.FromString,
          response_serializer=receipt_dot_franchisee_dot_delivery__pb2.Delivery.SerializeToString,
      ),
      'ListFranchiseeDeliverys': grpc.unary_unary_rpc_method_handler(
          servicer.ListFranchiseeDeliverys,
          request_deserializer=receipt_dot_franchisee_dot_delivery__pb2.ListFranchiseeDeliverysRequest.FromString,
          response_serializer=receipt_dot_franchisee_dot_delivery__pb2.ListFranchiseeDeliverysRespone.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_delivery.DeliveryService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
