# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from receipt.franchisee import order_pb2 as receipt_dot_franchisee_dot_order__pb2


class FranchiseeOrderServiceStub(object):
  """OrderService 订货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateOrder = channel.unary_unary(
        '/franchisee_order.FranchiseeOrderService/CreateOrder',
        request_serializer=receipt_dot_franchisee_dot_order__pb2.CreateOrderRequest.SerializeToString,
        response_deserializer=receipt_dot_franchisee_dot_order__pb2.Response.FromString,
        )


class FranchiseeOrderServiceServicer(object):
  """OrderService 订货相关服务
  """

  def CreateOrder(self, request, context):
    """创建订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FranchiseeOrderServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateOrder': grpc.unary_unary_rpc_method_handler(
          servicer.CreateOrder,
          request_deserializer=receipt_dot_franchisee_dot_order__pb2.CreateOrderRequest.FromString,
          response_serializer=receipt_dot_franchisee_dot_order__pb2.Response.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_order.FranchiseeOrderService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
