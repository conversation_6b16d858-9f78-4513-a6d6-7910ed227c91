syntax = "proto3";
package franchisee_order;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// OrderService 订货相关服务
service FranchiseeOrderService{

    // 创建订货单
    rpc CreateOrder(CreateOrderRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/franchisee/order"
            body:"*"
        };
    }

}

message Response{
    uint64 id = 1;
    string status = 2;
    string msg = 3;
}

message Order{
    // 订货单id
    uint64 id = 1;
    // 订货单单号
    string code = 2;
    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型
    string batch_type = 5;

    // 订货单id
    uint64 order_id = 6;
    // 订货单单号
    string order_code = 7;
    // 收货单id
    uint64 receive_id = 8;
    // 收货单单号
    string receive_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 单据状态
    string status = 12;
    // 业务状态
    string process_status = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    // 储藏方式
    string storage_type = 18;
    // 物流类型
    string distr_type = 19;
    // 订单类型
    string demand_type = 20;

    //
    uint64 third_party_id = 21;
    string third_party_type = 22;
    // 商品数量
    uint64 product_nums = 23;

    // 创建时间
    google.protobuf.Timestamp created_at = 24;
    // 更新时间
    google.protobuf.Timestamp updated_at = 25;
    // 创建人id
    int64 created_by = 26;
    // 更新人id
    int64 updated_by = 27;
    // 商户id
    uint64 partner_id = 28;
    // 创建人
    string created_name = 29;
    // 更新人
    string updated_name = 30;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 订货单receive_by是main_branch
    string main_branch_type = 31;
    // 拆单编号
    uint64 separate_no = 32;
    // 成本中心
    uint64 cost_center_id = 33;
    // 子账户类型
    string sub_account_type = 34;
    // 子账户接收方
    uint64 sub_receive_by = 35;
    // 子账户发送方
    uint64 sub_delivery_by = 36;
    // 备注
    string remark = 37;
    // 原因
    string reason = 38;
    // 加盟商id
    uint64 franchisee_id = 39;
    // 发货单id
    uint64 delivery_id = 50;
    // 发货单单号
    string delivery_code = 51;
    // 订货类型
    uint64 order_type_id = 52;
}

message Product{
    // id
    uint64 id = 1;
    // 订货单id
    uint64 order_id = 2;
    // 收货门店
    uint64 order_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6;
    //
    string storage_type = 7;

    // 订货数量
    double order_quantity = 8;
    // 收货数量
    double delivery_quantity = 9;
    // 收货数量
    double receive_quantity = 10;

    // 收货状态
    bool is_confirmed = 11;

    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 创建人id
    uint64 created_by= 13;
    // 更新日期
    google.protobuf.Timestamp updated_at = 14;
    // 更新人id
    uint64 updated_by = 15;
    // 商户id
    uint64 partner_id = 16;
    // 创建人
    string created_name = 17;
    // 更新人
    string updated_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 单位规格
    string unit_spec = 21;
    // 商品分类
    uint64 category_id = 22;
    // 商品分类名称
    string category_name = 23;
    // 商品分类编号
    string category_code = 24;
    // 成本价
    double cost_price = 25;
    // 含税价
    double tax_price = 26;
    // 税率
    double tax_rate = 27;
    // 单位换算比例
    double unit_rate = 28;
    // 销售类型
    string sale_type = 29;

}

message CreateOrderRequest{

    // id
    uint64 id = 1;
    // code
    string code = 2;

    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型 FRS_DEMAND-加盟商
    string batch_type = 5;

    // 订单id
    uint64 order_id = 6;
    // 订单单号
    string order_code = 7;
    // 发货单id
    uint64 delivery_id = 8;
    // 发货单单号
    string delivery_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 储藏方式
    string storage_type = 12;
    // 物流类型
    string distr_type = 13;
    // 订单类型
    string demand_type = 14;

    // 订货日期
    google.protobuf.Timestamp demand_date = 15;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 16;
    // 期望日期
    google.protobuf.Timestamp expect_date = 17;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 18;

    repeated Product products = 19;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商, FS-加盟店
    // 订货单receive_by是main_branch
    string main_branch_type = 20;
    // 拆单编号
    string separate_no = 21;
    // 子账户类型
    string sub_account_type = 22;
    // 子账户接收方
    uint64 sub_receive_by = 23;
    // 子账户发送方
    uint64 sub_delivery_by = 24;
    // 备注
    string remark = 34;
    // 原因
    string reason = 35;
    // 加盟商id
    uint64 franchisee_id = 36;
    // 订货类型
    uint64 order_type_id = 37;
    // 订单来源
    string bus_type = 38;
}
