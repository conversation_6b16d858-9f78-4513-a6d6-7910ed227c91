# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: receipt/franchisee/delivery.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='receipt/franchisee/delivery.proto',
  package='franchisee_delivery',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n!receipt/franchisee/delivery.proto\x12\x13\x66ranchisee_delivery\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"J\n\x1eListFranchiseeDeliverysRequest\x12\x11\n\tbatch_ids\x18\x01 \x03(\x04\x12\x15\n\rdelivery_code\x18\x02 \x01(\t\"M\n\x1eListFranchiseeDeliverysRespone\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.franchisee_delivery.Delivery\"\x9b\x07\n\x13ListDeliveryRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12\x12\n\nreceive_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x14\n\x0creceive_code\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0breceive_bys\x18\x08 \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\t \x03(\x04\x12\x0e\n\x06status\x18\n \x03(\t\x12\x16\n\x0eprocess_status\x18\x0b \x03(\t\x12.\n\nstart_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ndistr_type\x18\x0e \x01(\t\x12\x12\n\nbatch_type\x18\x0f \x03(\t\x12\r\n\x05limit\x18\x10 \x01(\x05\x12\x0e\n\x06offset\x18\x11 \x01(\x05\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\r\n\x05order\x18\x13 \x01(\t\x12\x18\n\x10main_branch_type\x18\x14 \x01(\t\x12\x11\n\tdemand_id\x18\x15 \x01(\x04\x12\x13\n\x0b\x64\x65mand_code\x18\x16 \x01(\t\x12\x17\n\x0fsub_receive_bys\x18\x17 \x03(\x04\x12\x18\n\x10sub_delivery_bys\x18\x18 \x03(\x04\x12\x18\n\x10sub_account_type\x18\x19 \x01(\t\x12\x33\n\x0forder_date_from\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rorder_date_to\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x65xpect_date_from\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0e\x65xpect_date_to\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bproduct_ids\x18\x1e \x03(\x04\x12\r\n\x05\x63odes\x18\x1f \x03(\t\x12\x0b\n\x03ids\x18  \x03(\x04\x12\x18\n\x10include_products\x18! \x01(\t\x12\x14\n\x0cstorage_type\x18\" \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18# \x03(\x04\x12\x13\n\x0b\x62\x61tch_types\x18$ \x03(\t\"&\n\x18GetDeliveryDetailRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"\xb8\x05\n\x19GetDeliveryDetailResponse\x12\x13\n\x0b\x64\x65livery_id\x18\x01 \x01(\x04\x12\x15\n\rdelivery_code\x18\x02 \x01(\t\x12\x11\n\tdemand_id\x18\x03 \x01(\x04\x12\x13\n\x0b\x64\x65mand_code\x18\x04 \x01(\t\x12\x10\n\x08order_id\x18\x05 \x01(\x04\x12\x12\n\norder_code\x18\x06 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x07 \x01(\x04\x12\x12\n\nbatch_code\x18\x08 \x01(\t\x12\x12\n\nreceive_id\x18\t \x01(\x04\x12\x14\n\x0creceive_code\x18\n \x01(\t\x12\x15\n\rfranchisee_id\x18\x0b \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x0c \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\r \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x12 \x01(\x04\x12-\n\x07product\x18\x13 \x03(\x0b\x32\x1c.franchisee_delivery.Product\x12\x17\n\x0freceive_by_code\x18\x14 \x01(\t\x12\x18\n\x10\x64\x65livery_by_code\x18\x15 \x01(\t\x12\x17\n\x0freceive_by_name\x18\x16 \x01(\t\x12\x18\n\x10\x64\x65livery_by_name\x18\x17 \x01(\t\x12\x0e\n\x06status\x18\x18 \x01(\t\x12\x14\n\x0cupdated_name\x18\x19 \x01(\t\x12\x12\n\nreceive_by\x18\x1a \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x1b \x01(\x04\"\x87\n\n\x08\x44\x65livery\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x12\n\nbatch_type\x18\x05 \x01(\t\x12\x10\n\x08order_id\x18\x06 \x01(\x04\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x11\n\tdemand_id\x18\x08 \x01(\x04\x12\x13\n\x0b\x64\x65mand_code\x18\t \x01(\t\x12\x12\n\nreceive_id\x18\n \x01(\x04\x12\x14\n\x0creceive_code\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x16\n\x0eprocess_status\x18\r \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cstorage_type\x18\x12 \x01(\t\x12\x12\n\ndistr_type\x18\x13 \x01(\t\x12\x16\n\x0ethird_party_id\x18\x14 \x01(\t\x12\x18\n\x10third_party_type\x18\x15 \x01(\t\x12\x14\n\x0cproduct_nums\x18\x16 \x01(\x04\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x03\x12\x12\n\nupdated_by\x18\x1a \x01(\x03\x12\x12\n\npartner_id\x18\x1b \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1c \x01(\t\x12\x14\n\x0cupdated_name\x18\x1d \x01(\t\x12\x18\n\x10main_branch_type\x18\x1e \x01(\t\x12\x12\n\nreceive_by\x18\x1f \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18  \x01(\x04\x12\x19\n\x11\x63ost_trans_status\x18! \x01(\t\x12\x13\n\x0b\x63ost_update\x18\" \x01(\x03\x12\x16\n\x0e\x63ost_center_id\x18# \x01(\x04\x12\x0e\n\x06remark\x18$ \x01(\t\x12\x0e\n\x06reason\x18% \x01(\t\x12\x16\n\x0esub_receive_by\x18& \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18\' \x01(\x04\x12\x18\n\x10sub_account_type\x18( \x01(\t\x12\x13\n\x0b\x64\x65mand_type\x18) \x01(\t\x12\x17\n\x0freceive_by_code\x18* \x01(\t\x12\x17\n\x0freceive_by_name\x18+ \x01(\t\x12\x18\n\x10\x64\x65livery_by_code\x18, \x01(\t\x12\x18\n\x10\x64\x65livery_by_name\x18- \x01(\t\x12.\n\x08products\x18. \x03(\x0b\x32\x1c.franchisee_delivery.Product\x12\x0b\n\x03tel\x18/ \x01(\t\x12\x0f\n\x07\x63ontact\x18\x30 \x01(\t\x12\x15\n\rfranchisee_id\x18\x31 \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x32 \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x33 \x01(\t\"\x8b\x06\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0b\x64\x65livery_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x14\n\x0cstorage_type\x18\x07 \x01(\t\x12\x16\n\x0eorder_quantity\x18\x08 \x01(\x01\x12\x19\n\x11\x64\x65livery_quantity\x18\t \x01(\x01\x12\x15\n\rpick_quantity\x18\n \x01(\x01\x12\x18\n\x10\x63onfirm_quantity\x18\x0b \x01(\x01\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\r \x01(\x04\x12.\n\nupdated_at\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x0f \x01(\x04\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x11 \x01(\t\x12\x14\n\x0cupdated_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x11\n\tunit_spec\x18\x15 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x16 \x01(\x04\x12\x15\n\rcategory_name\x18\x17 \x01(\t\x12\x15\n\rcategory_code\x18\x18 \x01(\t\x12\x14\n\x0cis_confirmed\x18\x19 \x01(\x08\x12\x12\n\ncost_price\x18\x1a \x01(\x01\x12\x11\n\ttax_price\x18\x1b \x01(\x01\x12\x10\n\x08tax_rate\x18\x1c \x01(\x01\x12\x11\n\tunit_rate\x18\x1d \x01(\x01\x12\x11\n\tsale_type\x18\x1e \x01(\t\x12\x18\n\x10receive_quantity\x18\x1f \x01(\x01\x12\x13\n\x0border_price\x18  \x01(\x01\x12\x16\n\x0e\x64\x65livery_price\x18! \x01(\x01\x12\x12\n\ntax_amount\x18\" \x01(\x01\"R\n\x14ListDeliveryResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.franchisee_delivery.Delivery\x12\r\n\x05total\x18\x02 \x01(\x04\"$\n\x16GetDeliveryByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x32\xa6\x05\n\x0f\x44\x65liveryService\x12\x94\x01\n\x0cListDelivery\x12(.franchisee_delivery.ListDeliveryRequest\x1a).franchisee_delivery.ListDeliveryResponse\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/receipt/franchisee/deliverys:\x01*\x12\xac\x01\n\x11GetDeliveryDetail\x12-.franchisee_delivery.GetDeliveryDetailRequest\x1a..franchisee_delivery.GetDeliveryDetailResponse\"8\x82\xd3\xe4\x93\x02\x32\"0/api/v2/receipt/franchisee/delivery/{id}/details\x12\x8f\x01\n\x0fGetDeliveryById\x12+.franchisee_delivery.GetDeliveryByIdRequest\x1a\x1d.franchisee_delivery.Delivery\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/receipt/franchisee/delivery/{id}\x12\xba\x01\n\x17ListFranchiseeDeliverys\x12\x33.franchisee_delivery.ListFranchiseeDeliverysRequest\x1a\x33.franchisee_delivery.ListFranchiseeDeliverysRespone\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/receipt/franchisee/deliverys/internalb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_LISTFRANCHISEEDELIVERYSREQUEST = _descriptor.Descriptor(
  name='ListFranchiseeDeliverysRequest',
  full_name='franchisee_delivery.ListFranchiseeDeliverysRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_ids', full_name='franchisee_delivery.ListFranchiseeDeliverysRequest.batch_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='franchisee_delivery.ListFranchiseeDeliverysRequest.delivery_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=121,
  serialized_end=195,
)


_LISTFRANCHISEEDELIVERYSRESPONE = _descriptor.Descriptor(
  name='ListFranchiseeDeliverysRespone',
  full_name='franchisee_delivery.ListFranchiseeDeliverysRespone',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_delivery.ListFranchiseeDeliverysRespone.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=197,
  serialized_end=274,
)


_LISTDELIVERYREQUEST = _descriptor.Descriptor(
  name='ListDeliveryRequest',
  full_name='franchisee_delivery.ListDeliveryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='franchisee_delivery.ListDeliveryRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_delivery.ListDeliveryRequest.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='franchisee_delivery.ListDeliveryRequest.receive_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='franchisee_delivery.ListDeliveryRequest.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='franchisee_delivery.ListDeliveryRequest.receive_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_delivery.ListDeliveryRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='franchisee_delivery.ListDeliveryRequest.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='franchisee_delivery.ListDeliveryRequest.receive_bys', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='franchisee_delivery.ListDeliveryRequest.delivery_bys', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_delivery.ListDeliveryRequest.status', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='franchisee_delivery.ListDeliveryRequest.process_status', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_delivery.ListDeliveryRequest.start_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_delivery.ListDeliveryRequest.end_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='franchisee_delivery.ListDeliveryRequest.distr_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='franchisee_delivery.ListDeliveryRequest.batch_type', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_delivery.ListDeliveryRequest.limit', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_delivery.ListDeliveryRequest.offset', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_delivery.ListDeliveryRequest.sort', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_delivery.ListDeliveryRequest.order', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='franchisee_delivery.ListDeliveryRequest.main_branch_type', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_delivery.ListDeliveryRequest.demand_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='franchisee_delivery.ListDeliveryRequest.demand_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_bys', full_name='franchisee_delivery.ListDeliveryRequest.sub_receive_bys', index=22,
      number=23, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_bys', full_name='franchisee_delivery.ListDeliveryRequest.sub_delivery_bys', index=23,
      number=24, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='franchisee_delivery.ListDeliveryRequest.sub_account_type', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date_from', full_name='franchisee_delivery.ListDeliveryRequest.order_date_from', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date_to', full_name='franchisee_delivery.ListDeliveryRequest.order_date_to', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date_from', full_name='franchisee_delivery.ListDeliveryRequest.expect_date_from', index=27,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date_to', full_name='franchisee_delivery.ListDeliveryRequest.expect_date_to', index=28,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='franchisee_delivery.ListDeliveryRequest.product_ids', index=29,
      number=30, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='codes', full_name='franchisee_delivery.ListDeliveryRequest.codes', index=30,
      number=31, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='franchisee_delivery.ListDeliveryRequest.ids', index=31,
      number=32, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_products', full_name='franchisee_delivery.ListDeliveryRequest.include_products', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_delivery.ListDeliveryRequest.storage_type', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='franchisee_delivery.ListDeliveryRequest.category_ids', index=34,
      number=35, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_types', full_name='franchisee_delivery.ListDeliveryRequest.batch_types', index=35,
      number=36, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=277,
  serialized_end=1200,
)


_GETDELIVERYDETAILREQUEST = _descriptor.Descriptor(
  name='GetDeliveryDetailRequest',
  full_name='franchisee_delivery.GetDeliveryDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_delivery.GetDeliveryDetailRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1202,
  serialized_end=1240,
)


_GETDELIVERYDETAILRESPONSE = _descriptor.Descriptor(
  name='GetDeliveryDetailResponse',
  full_name='franchisee_delivery.GetDeliveryDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='franchisee_delivery.GetDeliveryDetailResponse.delivery_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='franchisee_delivery.GetDeliveryDetailResponse.delivery_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_delivery.GetDeliveryDetailResponse.demand_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='franchisee_delivery.GetDeliveryDetailResponse.demand_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='franchisee_delivery.GetDeliveryDetailResponse.order_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='franchisee_delivery.GetDeliveryDetailResponse.order_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_delivery.GetDeliveryDetailResponse.batch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='franchisee_delivery.GetDeliveryDetailResponse.batch_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='franchisee_delivery.GetDeliveryDetailResponse.receive_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='franchisee_delivery.GetDeliveryDetailResponse.receive_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='franchisee_delivery.GetDeliveryDetailResponse.franchisee_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='franchisee_delivery.GetDeliveryDetailResponse.franchisee_code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='franchisee_delivery.GetDeliveryDetailResponse.franchisee_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_delivery.GetDeliveryDetailResponse.demand_date', index=13,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='franchisee_delivery.GetDeliveryDetailResponse.expect_date', index=14,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_delivery.GetDeliveryDetailResponse.updated_at', index=15,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_delivery.GetDeliveryDetailResponse.updated_by', index=16,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='franchisee_delivery.GetDeliveryDetailResponse.product', index=17,
      number=19, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_code', full_name='franchisee_delivery.GetDeliveryDetailResponse.receive_by_code', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='franchisee_delivery.GetDeliveryDetailResponse.delivery_by_code', index=19,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_name', full_name='franchisee_delivery.GetDeliveryDetailResponse.receive_by_name', index=20,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='franchisee_delivery.GetDeliveryDetailResponse.delivery_by_name', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_delivery.GetDeliveryDetailResponse.status', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_delivery.GetDeliveryDetailResponse.updated_name', index=23,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='franchisee_delivery.GetDeliveryDetailResponse.receive_by', index=24,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='franchisee_delivery.GetDeliveryDetailResponse.delivery_by', index=25,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1243,
  serialized_end=1939,
)


_DELIVERY = _descriptor.Descriptor(
  name='Delivery',
  full_name='franchisee_delivery.Delivery',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_delivery.Delivery.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_delivery.Delivery.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_delivery.Delivery.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='franchisee_delivery.Delivery.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='franchisee_delivery.Delivery.batch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='franchisee_delivery.Delivery.order_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='franchisee_delivery.Delivery.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_delivery.Delivery.demand_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='franchisee_delivery.Delivery.demand_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='franchisee_delivery.Delivery.receive_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='franchisee_delivery.Delivery.receive_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_delivery.Delivery.status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='franchisee_delivery.Delivery.process_status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_delivery.Delivery.demand_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='franchisee_delivery.Delivery.delivery_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='franchisee_delivery.Delivery.expect_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='franchisee_delivery.Delivery.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_delivery.Delivery.storage_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='franchisee_delivery.Delivery.distr_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='third_party_id', full_name='franchisee_delivery.Delivery.third_party_id', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='third_party_type', full_name='franchisee_delivery.Delivery.third_party_type', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='franchisee_delivery.Delivery.product_nums', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_delivery.Delivery.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_delivery.Delivery.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_delivery.Delivery.created_by', index=24,
      number=25, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_delivery.Delivery.updated_by', index=25,
      number=26, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_delivery.Delivery.partner_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_delivery.Delivery.created_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_delivery.Delivery.updated_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='franchisee_delivery.Delivery.main_branch_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='franchisee_delivery.Delivery.receive_by', index=30,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='franchisee_delivery.Delivery.delivery_by', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='franchisee_delivery.Delivery.cost_trans_status', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_update', full_name='franchisee_delivery.Delivery.cost_update', index=33,
      number=34, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='franchisee_delivery.Delivery.cost_center_id', index=34,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_delivery.Delivery.remark', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='franchisee_delivery.Delivery.reason', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='franchisee_delivery.Delivery.sub_receive_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='franchisee_delivery.Delivery.sub_delivery_by', index=38,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='franchisee_delivery.Delivery.sub_account_type', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='franchisee_delivery.Delivery.demand_type', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_code', full_name='franchisee_delivery.Delivery.receive_by_code', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_name', full_name='franchisee_delivery.Delivery.receive_by_name', index=42,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='franchisee_delivery.Delivery.delivery_by_code', index=43,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='franchisee_delivery.Delivery.delivery_by_name', index=44,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_delivery.Delivery.products', index=45,
      number=46, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='franchisee_delivery.Delivery.tel', index=46,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='franchisee_delivery.Delivery.contact', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='franchisee_delivery.Delivery.franchisee_id', index=48,
      number=49, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='franchisee_delivery.Delivery.franchisee_code', index=49,
      number=50, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='franchisee_delivery.Delivery.franchisee_name', index=50,
      number=51, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1942,
  serialized_end=3229,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='franchisee_delivery.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_delivery.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='franchisee_delivery.Product.delivery_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='franchisee_delivery.Product.delivery_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_delivery.Product.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_delivery.Product.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_delivery.Product.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_delivery.Product.storage_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='franchisee_delivery.Product.order_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='franchisee_delivery.Product.delivery_quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pick_quantity', full_name='franchisee_delivery.Product.pick_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_quantity', full_name='franchisee_delivery.Product.confirm_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_delivery.Product.created_at', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_delivery.Product.created_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_delivery.Product.updated_at', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_delivery.Product.updated_by', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_delivery.Product.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_delivery.Product.created_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_delivery.Product.updated_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_delivery.Product.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_delivery.Product.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_delivery.Product.unit_spec', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='franchisee_delivery.Product.category_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='franchisee_delivery.Product.category_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='franchisee_delivery.Product.category_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='franchisee_delivery.Product.is_confirmed', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_delivery.Product.cost_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_delivery.Product.tax_price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_delivery.Product.tax_rate', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_delivery.Product.unit_rate', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='franchisee_delivery.Product.sale_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_quantity', full_name='franchisee_delivery.Product.receive_quantity', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_price', full_name='franchisee_delivery.Product.order_price', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_price', full_name='franchisee_delivery.Product.delivery_price', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='franchisee_delivery.Product.tax_amount', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3232,
  serialized_end=4011,
)


_LISTDELIVERYRESPONSE = _descriptor.Descriptor(
  name='ListDeliveryResponse',
  full_name='franchisee_delivery.ListDeliveryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_delivery.ListDeliveryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_delivery.ListDeliveryResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4013,
  serialized_end=4095,
)


_GETDELIVERYBYIDREQUEST = _descriptor.Descriptor(
  name='GetDeliveryByIdRequest',
  full_name='franchisee_delivery.GetDeliveryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_delivery.GetDeliveryByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4097,
  serialized_end=4133,
)

_LISTFRANCHISEEDELIVERYSRESPONE.fields_by_name['rows'].message_type = _DELIVERY
_LISTDELIVERYREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['order_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['order_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['expect_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['expect_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYDETAILRESPONSE.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYDETAILRESPONSE.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYDETAILRESPONSE.fields_by_name['product'].message_type = _PRODUCT
_DELIVERY.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['products'].message_type = _PRODUCT
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYRESPONSE.fields_by_name['rows'].message_type = _DELIVERY
DESCRIPTOR.message_types_by_name['ListFranchiseeDeliverysRequest'] = _LISTFRANCHISEEDELIVERYSREQUEST
DESCRIPTOR.message_types_by_name['ListFranchiseeDeliverysRespone'] = _LISTFRANCHISEEDELIVERYSRESPONE
DESCRIPTOR.message_types_by_name['ListDeliveryRequest'] = _LISTDELIVERYREQUEST
DESCRIPTOR.message_types_by_name['GetDeliveryDetailRequest'] = _GETDELIVERYDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetDeliveryDetailResponse'] = _GETDELIVERYDETAILRESPONSE
DESCRIPTOR.message_types_by_name['Delivery'] = _DELIVERY
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['ListDeliveryResponse'] = _LISTDELIVERYRESPONSE
DESCRIPTOR.message_types_by_name['GetDeliveryByIdRequest'] = _GETDELIVERYBYIDREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListFranchiseeDeliverysRequest = _reflection.GeneratedProtocolMessageType('ListFranchiseeDeliverysRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTFRANCHISEEDELIVERYSREQUEST,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.ListFranchiseeDeliverysRequest)
  ))
_sym_db.RegisterMessage(ListFranchiseeDeliverysRequest)

ListFranchiseeDeliverysRespone = _reflection.GeneratedProtocolMessageType('ListFranchiseeDeliverysRespone', (_message.Message,), dict(
  DESCRIPTOR = _LISTFRANCHISEEDELIVERYSRESPONE,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.ListFranchiseeDeliverysRespone)
  ))
_sym_db.RegisterMessage(ListFranchiseeDeliverysRespone)

ListDeliveryRequest = _reflection.GeneratedProtocolMessageType('ListDeliveryRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTDELIVERYREQUEST,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.ListDeliveryRequest)
  ))
_sym_db.RegisterMessage(ListDeliveryRequest)

GetDeliveryDetailRequest = _reflection.GeneratedProtocolMessageType('GetDeliveryDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYDETAILREQUEST,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.GetDeliveryDetailRequest)
  ))
_sym_db.RegisterMessage(GetDeliveryDetailRequest)

GetDeliveryDetailResponse = _reflection.GeneratedProtocolMessageType('GetDeliveryDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYDETAILRESPONSE,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.GetDeliveryDetailResponse)
  ))
_sym_db.RegisterMessage(GetDeliveryDetailResponse)

Delivery = _reflection.GeneratedProtocolMessageType('Delivery', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERY,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.Delivery)
  ))
_sym_db.RegisterMessage(Delivery)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.Product)
  ))
_sym_db.RegisterMessage(Product)

ListDeliveryResponse = _reflection.GeneratedProtocolMessageType('ListDeliveryResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTDELIVERYRESPONSE,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.ListDeliveryResponse)
  ))
_sym_db.RegisterMessage(ListDeliveryResponse)

GetDeliveryByIdRequest = _reflection.GeneratedProtocolMessageType('GetDeliveryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYBYIDREQUEST,
  __module__ = 'receipt.franchisee.delivery_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_delivery.GetDeliveryByIdRequest)
  ))
_sym_db.RegisterMessage(GetDeliveryByIdRequest)



_DELIVERYSERVICE = _descriptor.ServiceDescriptor(
  name='DeliveryService',
  full_name='franchisee_delivery.DeliveryService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=4136,
  serialized_end=4814,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListDelivery',
    full_name='franchisee_delivery.DeliveryService.ListDelivery',
    index=0,
    containing_service=None,
    input_type=_LISTDELIVERYREQUEST,
    output_type=_LISTDELIVERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/receipt/franchisee/deliverys:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDeliveryDetail',
    full_name='franchisee_delivery.DeliveryService.GetDeliveryDetail',
    index=1,
    containing_service=None,
    input_type=_GETDELIVERYDETAILREQUEST,
    output_type=_GETDELIVERYDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"0/api/v2/receipt/franchisee/delivery/{id}/details'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDeliveryById',
    full_name='franchisee_delivery.DeliveryService.GetDeliveryById',
    index=2,
    containing_service=None,
    input_type=_GETDELIVERYBYIDREQUEST,
    output_type=_DELIVERY,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/receipt/franchisee/delivery/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListFranchiseeDeliverys',
    full_name='franchisee_delivery.DeliveryService.ListFranchiseeDeliverys',
    index=3,
    containing_service=None,
    input_type=_LISTFRANCHISEEDELIVERYSREQUEST,
    output_type=_LISTFRANCHISEEDELIVERYSRESPONE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/receipt/franchisee/deliverys/internal'),
  ),
])
_sym_db.RegisterServiceDescriptor(_DELIVERYSERVICE)

DESCRIPTOR.services_by_name['DeliveryService'] = _DELIVERYSERVICE

# @@protoc_insertion_point(module_scope)
