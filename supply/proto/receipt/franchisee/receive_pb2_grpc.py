# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from receipt.franchisee import receive_pb2 as receipt_dot_franchisee_dot_receive__pb2


class FranchiseeReceiveServiceStub(object):
  """ReceiveService 收货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListFranchiseeReceives = channel.unary_unary(
        '/franchisee_receive.FranchiseeReceiveService/ListFranchiseeReceives',
        request_serializer=receipt_dot_franchisee_dot_receive__pb2.ListFranchiseeReceivesRequest.SerializeToString,
        response_deserializer=receipt_dot_franchisee_dot_receive__pb2.ListFranchiseeReceivesRespone.FromString,
        )


class FranchiseeReceiveServiceServicer(object):
  """ReceiveService 收货相关服务
  """

  def ListFranchiseeReceives(self, request, context):
    """加盟商通过batch_ids反查接口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FranchiseeReceiveServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListFranchiseeReceives': grpc.unary_unary_rpc_method_handler(
          servicer.ListFranchiseeReceives,
          request_deserializer=receipt_dot_franchisee_dot_receive__pb2.ListFranchiseeReceivesRequest.FromString,
          response_serializer=receipt_dot_franchisee_dot_receive__pb2.ListFranchiseeReceivesRespone.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_receive.FranchiseeReceiveService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
