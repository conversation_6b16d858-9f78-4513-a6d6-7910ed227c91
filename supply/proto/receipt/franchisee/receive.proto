syntax = "proto3";
package franchisee_receive;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// ReceiveService 收货相关服务
service FranchiseeReceiveService{
    
    // 加盟商通过batch_ids反查接口
    rpc ListFranchiseeReceives(ListFranchiseeReceivesRequest) returns (ListFranchiseeReceivesRespone){
        option (google.api.http) = {
            get:"/api/v2/receipt/franchisee/deliverys/internal"
        };
    }
}

message ListFranchiseeReceivesRequest{
    repeated uint64 batch_ids = 1;
    string receive_code = 2;
}

message ListFranchiseeReceivesRespone{
    repeated Receive rows = 1;
}

message Receive{
    // 收货单id
    uint64 id = 1;
    // 收货单单号
    string code = 2;
    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型
    string batch_type = 5;

    // 订货单id
    uint64 order_id = 6;
    // 订货单单号
    string order_code = 7;
    // 发货单id
    uint64 delivery_id = 8;
    // 发货单单号
    string delivery_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 单据状态
    string status = 12;
    // 业务状态
    string process_status = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    // 储藏方式
    string storage_type = 18;
    // 物流类型
    string distr_type = 19;

    // 
    uint64 third_party_id = 20;
    string third_party_type = 21;
    // 商品数量
    uint64 product_nums = 22;

    // 创建时间
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人id
    int64 created_by = 25;
    // 更新人id
    int64 updated_by = 26;
    // 商户id
    uint64 partner_id = 27;
    // 创建人
    string created_name = 28;
    // 更新人
    string updated_name = 29;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 收货单receive_by是main_branch
    string main_branch_type = 30;
    // 传输成本引擎 0:未传输，1:已传输
    string cost_trans_status = 31;
    // 更新次数
    int64 cost_update = 32;
    // 成本中心
    uint64 cost_center_id = 33;
    // 备注
    string remark = 34;
    // 原因
    string reason = 35;
    // 子账户类型
    string sub_account_type = 36;
    // 子账户接收方
    uint64 sub_receive_by = 37;
    // 子账户发送方
    uint64 sub_delivery_by = 38;
    // 
    string demand_type = 39;
    // 配送方
    string delivery_by_code = 40;
    // 配送方
    string delivery_by_name = 41;
    // 合计金额
    double sum_price_tax = 44;
    repeated Product products = 45;
    // 收货方
    string receive_by_code = 46;
    // 收货方
    string receive_by_name = 47;
    // 送货方联系人
    string contact = 48;
    // 联系电话
    string tel = 49;
    // 发货单备注
    string source_remark = 50;
    // 加盟商id
    uint64 franchisee_id = 51;
}

message Product{
    // id 
    uint64 id = 1; 
    // 收货单id
    uint64 receive_id = 2;
    // 收货门店
    uint64 receive_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6; 
    // 
    string storage_type = 7;

    // 订货数量
    double order_quantity = 8;
    // 收货数量
    double delivery_quantity = 9;
    // 收货数量
    double receive_quantity = 10;

    // 收货状态
    bool is_confirmed = 11;

    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 创建人id
    uint64 created_by= 13;
    // 更新日期
    google.protobuf.Timestamp updated_at = 14;
    // 更新人id
    uint64 updated_by = 15;
    // 商户id
    uint64 partner_id = 16;
    // 创建人
    string created_name = 17;
    // 更新人
    string updated_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 单位规格
    string unit_spec = 21;
    // 商品分类
    uint64 category_id = 22;
    // 商品分类名称
    string category_name = 23;
    // 商品分类编号
    string category_code = 24;
    // 成本价
    double cost_price = 25;
    // 含税价
    double tax_price = 26;
    // 税率
    double tax_rate = 27;
    // 单位换算比例
    double unit_rate = 28;
    // 销售类型
    string sale_type = 29;
    // 子账户类型
    string sub_account_type = 30;
    // 子账户接收方
    uint64 sub_receive_by = 31;
    // 子账户发送方
    uint64 sub_delivery_by = 32;
    // 实际收货数量=收货数+门店承担差异数
    double actual_receive_quantity = 33;
    // 门店承担差异数
    double s_diff_quantity = 34;
    // 剩余最小收货数量（收货容差）
    double min_remaining_qty = 35;
    // 剩余最大收货数量（收货容差）
    double max_remaining_qty = 36;
    // 是否校验容差，0=不校验，1=校验
    int32 check_deviation = 37;
    // 单次收货数量
    double temp_quantity = 38;
}