syntax = "proto3";
package franchisee_delivery;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// DeliveryService 发货相关服务
service DeliveryService{

    // 查询发货单
    rpc ListDelivery(ListDeliveryRequest) returns (ListDeliveryResponse){
        option (google.api.http) = {
            post:"/api/v2/receipt/franchisee/deliverys"
            body:"*"
        };
    }
    
    // 查询发货单明细
    rpc GetDeliveryDetail (GetDeliveryDetailRequest) returns (GetDeliveryDetailResponse){
        option (google.api.http) = {
            post:"/api/v2/receipt/franchisee/delivery/{id}/details"
        };
    }

    // 根据id查询发货单详情
    rpc GetDeliveryById(GetDeliveryByIdRequest) returns (Delivery){
        option (google.api.http) = {
            get:"/api/v2/receipt/franchisee/delivery/{id}"
        };
    }

    // 加盟商通过batch_ids反查接口
    rpc ListFranchiseeDeliverys(ListFranchiseeDeliverysRequest) returns (ListFranchiseeDeliverysRespone){
        option (google.api.http) = {
            get:"/api/v2/receipt/franchisee/deliverys/internal"
        };
    }

}

message ListFranchiseeDeliverysRequest{
    repeated uint64 batch_ids = 1;
    string delivery_code = 2;
}

message ListFranchiseeDeliverysRespone{
    repeated Delivery rows = 1;
}

message ListDeliveryRequest{
    // 订货单号
    uint64 order_id = 1;
    // 
    uint64 batch_id = 2;
    // 
    uint64 receive_id = 3;
    // 订货单号
    string batch_code = 4;
    // 收货单号
    string receive_code = 5;
    // 发货单号
    string code = 6;
    // 要货单号
    string order_code = 7;
    // 接受方id
    repeated uint64 receive_bys = 8;
    // 发送方
    repeated uint64 delivery_bys = 9;
    // 订单状态
    repeated string status = 10;
    // 订单状态
    repeated string process_status = 11;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 12;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 13;
    // 物流类型
    string distr_type = 14;
    // 业务类型
    repeated string batch_type = 15;
    // 分页大小
    int32 limit = 16;
    // 跳过行数
    int32 offset = 17;
    // 排序字段
    string sort = 18;
    // 排序顺序
    string order = 19;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 发货单delivery_by是main_branch
    string main_branch_type = 20;
    // 订单id
    uint64 demand_id = 21;
    // 订单单号
    string demand_code = 22;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 23;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 24;
    // 子账户类型
    string sub_account_type = 25;
    // 订货开始日期
    google.protobuf.Timestamp order_date_from = 26;
    // 订货结束日期
    google.protobuf.Timestamp order_date_to = 27;
    // 预计到货开始日期
    google.protobuf.Timestamp expect_date_from = 28;
    // 预计到货结束日期
    google.protobuf.Timestamp expect_date_to = 29;
    // 商品id
    repeated uint64 product_ids = 30;
    // 批量发货
    repeated string codes = 31;
    repeated uint64 ids = 32;
    // 是否包含商品
    string include_products = 33;
    // 储藏类型
    string storage_type = 34;
    // 商品分类
    repeated uint64 category_ids = 35;
    // 业务类型 —— 暂时兼容
    repeated string batch_types = 36;
}

message GetDeliveryDetailRequest{
    // 发货单号
    uint64 id = 1;
}
message GetDeliveryDetailResponse{
    // 发货单id
    uint64 delivery_id = 1;
    // 发货单code
    string delivery_code = 2;
    // 要货单id
    uint64 demand_id = 3;
    // 要货单code
    string demand_code = 4;
    // 门店订货单id
    uint64 order_id = 5;
    // 门店订货单code
    string order_code = 6;
    // 订货单code
    uint64 batch_id = 7;
    // 订货单code
    string batch_code = 8;
    // 收货单id
    uint64 receive_id = 9;
    // 收货单code
    string receive_code = 10;
    // 加盟商id
    uint64 franchisee_id = 11;
    // 加盟商code
    string franchisee_code = 12;
    string franchisee_name = 13;
    // 订货日期
    google.protobuf.Timestamp demand_date = 15;
    // 预计到货日期
    google.protobuf.Timestamp expect_date = 16;
    // 更新时间
    google.protobuf.Timestamp updated_at = 17;
    // 更新人id
    uint64 updated_by = 18;
    repeated Product product = 19;
    string receive_by_code = 20;
    // 配送方
    string delivery_by_code = 21;
    string receive_by_name = 22;
    // 配送方
    string delivery_by_name = 23;
    string status = 24;
    // 更新人名字
    string updated_name = 25;
    uint64 receive_by = 26;
    uint64 delivery_by = 27;
}


message Delivery{
    // 发货单id
    uint64 id = 1;
    // 发货单单号
    string code = 2;
    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型
    string batch_type = 5;

    // 订货单id
    uint64 order_id = 6;
    // 订货单单号
    string order_code = 7;
    // 订单id
    uint64 demand_id = 8;
    // 订单单号
    string demand_code = 9;
    // 收货单id
    uint64 receive_id = 10;
    // 收货单单号
    string receive_code = 11;

    // 单据状态 INITED初始, SUCCESS已收货, UNCONFIRMED差异待确认
    string status = 12;
    // 业务状态
    string process_status = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    // 储藏方式
    string storage_type = 18;
    // 物流类型
    string distr_type = 19;
    // 物流方式
    // 物流号

    // 
    string third_party_id = 20;
    string third_party_type = 21;
    // 商品数量
    uint64 product_nums = 22;

    // 创建时间
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人id
    int64 created_by = 25;
    // 更新人id
    int64 updated_by = 26;
    // 商户id
    uint64 partner_id = 27;
    // 创建人
    string created_name = 28;
    // 更新人
    string updated_name = 29;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 发货单delivery_by是main_branch
    string main_branch_type = 30;
    // 接收门店-门店id
    uint64 receive_by = 31;
    // 配送方
    uint64 delivery_by = 32;
    // 传输成本引擎 0:未传输，1:已传输
    string cost_trans_status = 33;
    // 更新次数
    int64 cost_update = 34;
    // 成本中心
    uint64 cost_center_id = 35;
    // 备注
    string remark = 36;
    // 原因
    string reason = 37;
    // 子账户接收方
    uint64 sub_receive_by = 38;
    // 子账户发送方
    uint64 sub_delivery_by = 39;
    // 子账户类型
    string sub_account_type = 40;
    // 
    string demand_type = 41;
    // 接收方编码
    string receive_by_code = 42;
    // 接收方名称
    string receive_by_name = 43;
    // 配送方编码
    string delivery_by_code = 44;
    // 配送方名称
    string delivery_by_name = 45;
    repeated Product products = 46;
    // 
    string tel = 47;
    string contact = 48;
    // 加盟商id
    uint64 franchisee_id = 49;
    // 加盟商code
    string franchisee_code = 50;
    string franchisee_name = 51;
}

message Product{
    // id 
    uint64 id = 1; 
    // 发货单id
    uint64 delivery_id = 2;
    // 发货门店
    uint64 delivery_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6;
    // 
    string storage_type = 7;

    // 订货数量
    double order_quantity = 8;
    // 发货数量
    double delivery_quantity = 9;
    // 拣货数量
    double pick_quantity = 10;
    // 审核数量
    double confirm_quantity = 11;

    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 创建人id
    uint64 created_by= 13;
    // 更新日期
    google.protobuf.Timestamp updated_at = 14;
    // 更新人id
    uint64 updated_by = 15;
    // 商户id
    uint64 partner_id = 16;
    // 创建人
    string created_name = 17;
    // 更新人
    string updated_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 单位规格
    string unit_spec = 21;
    // 商品分类
    uint64 category_id = 22;
    // 商品分类名称
    string category_name = 23;
    // 商品分类编号
    string category_code = 24;
    // 发货状态
    bool is_confirmed = 25;
    // 成本价
    double cost_price = 26;
    // 含税价
    double tax_price = 27;
    // 税率
    double tax_rate = 28;
    // 单位换算比例
    double unit_rate = 29;
    // 销售类型
    string sale_type = 30;
    // 收货数量
    double receive_quantity = 31;
    // 订货金额
    double order_price = 32;
    // 发货金额
    double delivery_price = 33;
    // 税额
    double tax_amount = 34;
}


message ListDeliveryResponse{
    repeated Delivery rows = 1;
    uint64 total = 2;
}

message GetDeliveryByIdRequest{
    // 发货单id(delivery_id)
    uint64 id = 1;

}
