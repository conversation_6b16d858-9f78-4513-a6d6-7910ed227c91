# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from receipt import delivery_pb2 as receipt_dot_delivery__pb2


class DeliveryServiceStub(object):
  """DeliveryService 发货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateDelivery = channel.unary_unary(
        '/delivery.DeliveryService/CreateDelivery',
        request_serializer=receipt_dot_delivery__pb2.CreateDeliveryRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.Response.FromString,
        )
    self.UpdateDelivery = channel.unary_unary(
        '/delivery.DeliveryService/UpdateDelivery',
        request_serializer=receipt_dot_delivery__pb2.UpdateDeliveryRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.Response.FromString,
        )
    self.BulkUpdateDelivery = channel.unary_unary(
        '/delivery.DeliveryService/BulkUpdateDelivery',
        request_serializer=receipt_dot_delivery__pb2.BulkUpdateDeliveryRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.Response.FromString,
        )
    self.ListDelivery = channel.unary_unary(
        '/delivery.DeliveryService/ListDelivery',
        request_serializer=receipt_dot_delivery__pb2.ListDeliveryRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.ListDeliveryResponse.FromString,
        )
    self.GetDeliveryById = channel.unary_unary(
        '/delivery.DeliveryService/GetDeliveryById',
        request_serializer=receipt_dot_delivery__pb2.GetDeliveryByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.Delivery.FromString,
        )
    self.GetDeliveryProductById = channel.unary_unary(
        '/delivery.DeliveryService/GetDeliveryProductById',
        request_serializer=receipt_dot_delivery__pb2.GetDeliveryProductByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.GetDeliveryProductByIdResponse.FromString,
        )
    self.DealDeliveryById = channel.unary_unary(
        '/delivery.DeliveryService/DealDeliveryById',
        request_serializer=receipt_dot_delivery__pb2.DealDeliveryByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.Response.FromString,
        )
    self.BulkDealDeliverys = channel.unary_unary(
        '/delivery.DeliveryService/BulkDealDeliverys',
        request_serializer=receipt_dot_delivery__pb2.BulkDealDeliverysRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.Response.FromString,
        )
    self.GetDeliveryOrderReport = channel.unary_unary(
        '/delivery.DeliveryService/GetDeliveryOrderReport',
        request_serializer=receipt_dot_delivery__pb2.GetDeliveryOrderReportRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.GetDeliveryOrderReportResponse.FromString,
        )
    self.GetDeliveryOrderReportTotal = channel.unary_unary(
        '/delivery.DeliveryService/GetDeliveryOrderReportTotal',
        request_serializer=receipt_dot_delivery__pb2.GetDeliveryOrderReportTotalRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.GetDeliveryOrderReportTotalResponse.FromString,
        )
    self.ListDeliveryDetails = channel.unary_unary(
        '/delivery.DeliveryService/ListDeliveryDetails',
        request_serializer=receipt_dot_delivery__pb2.DeliveryDetailsRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.ListDeliveryResponse.FromString,
        )
    self.DealDeliveryByCode = channel.unary_unary(
        '/delivery.DeliveryService/DealDeliveryByCode',
        request_serializer=receipt_dot_delivery__pb2.DealDeliveryByCodeRequest.SerializeToString,
        response_deserializer=receipt_dot_delivery__pb2.Response.FromString,
        )


class DeliveryServiceServicer(object):
  """DeliveryService 发货相关服务
  """

  def CreateDelivery(self, request, context):
    """创建发货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDelivery(self, request, context):
    """更新发货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BulkUpdateDelivery(self, request, context):
    """更新发货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDelivery(self, request, context):
    """查询发货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDeliveryById(self, request, context):
    """根据id查询发货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDeliveryProductById(self, request, context):
    """根据delivery_id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealDeliveryById(self, request, context):
    """处理收货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BulkDealDeliverys(self, request, context):
    """批量处理收货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDeliveryOrderReport(self, request, context):
    """发货单报表(仓库)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDeliveryOrderReportTotal(self, request, context):
    """发货单汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDeliveryDetails(self, request, context):
    """批量发货查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealDeliveryByCode(self, request, context):
    """处理收货单 —— 一般是给三方调用使用
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_DeliveryServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateDelivery': grpc.unary_unary_rpc_method_handler(
          servicer.CreateDelivery,
          request_deserializer=receipt_dot_delivery__pb2.CreateDeliveryRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.Response.SerializeToString,
      ),
      'UpdateDelivery': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDelivery,
          request_deserializer=receipt_dot_delivery__pb2.UpdateDeliveryRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.Response.SerializeToString,
      ),
      'BulkUpdateDelivery': grpc.unary_unary_rpc_method_handler(
          servicer.BulkUpdateDelivery,
          request_deserializer=receipt_dot_delivery__pb2.BulkUpdateDeliveryRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.Response.SerializeToString,
      ),
      'ListDelivery': grpc.unary_unary_rpc_method_handler(
          servicer.ListDelivery,
          request_deserializer=receipt_dot_delivery__pb2.ListDeliveryRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.ListDeliveryResponse.SerializeToString,
      ),
      'GetDeliveryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetDeliveryById,
          request_deserializer=receipt_dot_delivery__pb2.GetDeliveryByIdRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.Delivery.SerializeToString,
      ),
      'GetDeliveryProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetDeliveryProductById,
          request_deserializer=receipt_dot_delivery__pb2.GetDeliveryProductByIdRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.GetDeliveryProductByIdResponse.SerializeToString,
      ),
      'DealDeliveryById': grpc.unary_unary_rpc_method_handler(
          servicer.DealDeliveryById,
          request_deserializer=receipt_dot_delivery__pb2.DealDeliveryByIdRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.Response.SerializeToString,
      ),
      'BulkDealDeliverys': grpc.unary_unary_rpc_method_handler(
          servicer.BulkDealDeliverys,
          request_deserializer=receipt_dot_delivery__pb2.BulkDealDeliverysRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.Response.SerializeToString,
      ),
      'GetDeliveryOrderReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetDeliveryOrderReport,
          request_deserializer=receipt_dot_delivery__pb2.GetDeliveryOrderReportRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.GetDeliveryOrderReportResponse.SerializeToString,
      ),
      'GetDeliveryOrderReportTotal': grpc.unary_unary_rpc_method_handler(
          servicer.GetDeliveryOrderReportTotal,
          request_deserializer=receipt_dot_delivery__pb2.GetDeliveryOrderReportTotalRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.GetDeliveryOrderReportTotalResponse.SerializeToString,
      ),
      'ListDeliveryDetails': grpc.unary_unary_rpc_method_handler(
          servicer.ListDeliveryDetails,
          request_deserializer=receipt_dot_delivery__pb2.DeliveryDetailsRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.ListDeliveryResponse.SerializeToString,
      ),
      'DealDeliveryByCode': grpc.unary_unary_rpc_method_handler(
          servicer.DealDeliveryByCode,
          request_deserializer=receipt_dot_delivery__pb2.DealDeliveryByCodeRequest.FromString,
          response_serializer=receipt_dot_delivery__pb2.Response.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'delivery.DeliveryService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
