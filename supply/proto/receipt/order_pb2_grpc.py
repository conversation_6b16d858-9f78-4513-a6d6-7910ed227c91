# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from receipt import order_pb2 as receipt_dot_order__pb2


class OrderServiceStub(object):
  """OrderService 订货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateOrder = channel.unary_unary(
        '/order.OrderService/CreateOrder',
        request_serializer=receipt_dot_order__pb2.CreateOrderRequest.SerializeToString,
        response_deserializer=receipt_dot_order__pb2.Response.FromString,
        )
    self.UpdateOrder = channel.unary_unary(
        '/order.OrderService/UpdateOrder',
        request_serializer=receipt_dot_order__pb2.UpdateOrderRequest.SerializeToString,
        response_deserializer=receipt_dot_order__pb2.Response.FromString,
        )
    self.ListOrder = channel.unary_unary(
        '/order.OrderService/ListOrder',
        request_serializer=receipt_dot_order__pb2.ListOrderRequest.SerializeToString,
        response_deserializer=receipt_dot_order__pb2.ListOrderResponse.FromString,
        )
    self.GetOrderById = channel.unary_unary(
        '/order.OrderService/GetOrderById',
        request_serializer=receipt_dot_order__pb2.GetOrderByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_order__pb2.Order.FromString,
        )
    self.GetOrderProductById = channel.unary_unary(
        '/order.OrderService/GetOrderProductById',
        request_serializer=receipt_dot_order__pb2.GetOrderProductByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_order__pb2.GetOrderProductByIdResponse.FromString,
        )
    self.QueryBiOrderReport = channel.unary_unary(
        '/order.OrderService/QueryBiOrderReport',
        request_serializer=receipt_dot_order__pb2.QueryOrderBiReportRequest.SerializeToString,
        response_deserializer=receipt_dot_order__pb2.QueryOrderBiReportResponse.FromString,
        )
    self.ListOrderByBatchIds = channel.unary_unary(
        '/order.OrderService/ListOrderByBatchIds',
        request_serializer=receipt_dot_order__pb2.ListOrderByBatchIdsRequest.SerializeToString,
        response_deserializer=receipt_dot_order__pb2.ListOrderResponse.FromString,
        )


class OrderServiceServicer(object):
  """OrderService 订货相关服务
  """

  def CreateOrder(self, request, context):
    """创建订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateOrder(self, request, context):
    """更新订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListOrder(self, request, context):
    """查询订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderById(self, request, context):
    """根据id查询订货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderProductById(self, request, context):
    """根据Order_id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryBiOrderReport(self, request, context):
    """要货单报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListOrderByBatchIds(self, request, context):
    """查询订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_OrderServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateOrder': grpc.unary_unary_rpc_method_handler(
          servicer.CreateOrder,
          request_deserializer=receipt_dot_order__pb2.CreateOrderRequest.FromString,
          response_serializer=receipt_dot_order__pb2.Response.SerializeToString,
      ),
      'UpdateOrder': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateOrder,
          request_deserializer=receipt_dot_order__pb2.UpdateOrderRequest.FromString,
          response_serializer=receipt_dot_order__pb2.Response.SerializeToString,
      ),
      'ListOrder': grpc.unary_unary_rpc_method_handler(
          servicer.ListOrder,
          request_deserializer=receipt_dot_order__pb2.ListOrderRequest.FromString,
          response_serializer=receipt_dot_order__pb2.ListOrderResponse.SerializeToString,
      ),
      'GetOrderById': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderById,
          request_deserializer=receipt_dot_order__pb2.GetOrderByIdRequest.FromString,
          response_serializer=receipt_dot_order__pb2.Order.SerializeToString,
      ),
      'GetOrderProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderProductById,
          request_deserializer=receipt_dot_order__pb2.GetOrderProductByIdRequest.FromString,
          response_serializer=receipt_dot_order__pb2.GetOrderProductByIdResponse.SerializeToString,
      ),
      'QueryBiOrderReport': grpc.unary_unary_rpc_method_handler(
          servicer.QueryBiOrderReport,
          request_deserializer=receipt_dot_order__pb2.QueryOrderBiReportRequest.FromString,
          response_serializer=receipt_dot_order__pb2.QueryOrderBiReportResponse.SerializeToString,
      ),
      'ListOrderByBatchIds': grpc.unary_unary_rpc_method_handler(
          servicer.ListOrderByBatchIds,
          request_deserializer=receipt_dot_order__pb2.ListOrderByBatchIdsRequest.FromString,
          response_serializer=receipt_dot_order__pb2.ListOrderResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'order.OrderService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
