# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from receipt import demand_pb2 as receipt_dot_demand__pb2


class DemandServiceStub(object):
  """DemandService 订单相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateDemand = channel.unary_unary(
        '/demand.DemandService/CreateDemand',
        request_serializer=receipt_dot_demand__pb2.CreateDemandRequest.SerializeToString,
        response_deserializer=receipt_dot_demand__pb2.Response.FromString,
        )
    self.UpdateDemand = channel.unary_unary(
        '/demand.DemandService/UpdateDemand',
        request_serializer=receipt_dot_demand__pb2.UpdateDemandRequest.SerializeToString,
        response_deserializer=receipt_dot_demand__pb2.Response.FromString,
        )
    self.ListDemand = channel.unary_unary(
        '/demand.DemandService/ListDemand',
        request_serializer=receipt_dot_demand__pb2.ListDemandRequest.SerializeToString,
        response_deserializer=receipt_dot_demand__pb2.ListDemandResponse.FromString,
        )
    self.GetDemandById = channel.unary_unary(
        '/demand.DemandService/GetDemandById',
        request_serializer=receipt_dot_demand__pb2.GetDemandByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_demand__pb2.Demand.FromString,
        )
    self.GetDemandProductById = channel.unary_unary(
        '/demand.DemandService/GetDemandProductById',
        request_serializer=receipt_dot_demand__pb2.GetDemandProductByIdRequest.SerializeToString,
        response_deserializer=receipt_dot_demand__pb2.GetDemandProductByIdResponse.FromString,
        )


class DemandServiceServicer(object):
  """DemandService 订单相关服务
  """

  def CreateDemand(self, request, context):
    """创建订单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemand(self, request, context):
    """更新订单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDemand(self, request, context):
    """查询订单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandById(self, request, context):
    """根据id查询订单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandProductById(self, request, context):
    """根据demand_id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_DemandServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateDemand': grpc.unary_unary_rpc_method_handler(
          servicer.CreateDemand,
          request_deserializer=receipt_dot_demand__pb2.CreateDemandRequest.FromString,
          response_serializer=receipt_dot_demand__pb2.Response.SerializeToString,
      ),
      'UpdateDemand': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemand,
          request_deserializer=receipt_dot_demand__pb2.UpdateDemandRequest.FromString,
          response_serializer=receipt_dot_demand__pb2.Response.SerializeToString,
      ),
      'ListDemand': grpc.unary_unary_rpc_method_handler(
          servicer.ListDemand,
          request_deserializer=receipt_dot_demand__pb2.ListDemandRequest.FromString,
          response_serializer=receipt_dot_demand__pb2.ListDemandResponse.SerializeToString,
      ),
      'GetDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandById,
          request_deserializer=receipt_dot_demand__pb2.GetDemandByIdRequest.FromString,
          response_serializer=receipt_dot_demand__pb2.Demand.SerializeToString,
      ),
      'GetDemandProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandProductById,
          request_deserializer=receipt_dot_demand__pb2.GetDemandProductByIdRequest.FromString,
          response_serializer=receipt_dot_demand__pb2.GetDemandProductByIdResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'demand.DemandService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
