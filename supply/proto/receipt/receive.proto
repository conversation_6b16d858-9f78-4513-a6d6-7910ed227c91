syntax = "proto3";
package receive;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// ReceiveService 收货相关服务
service ReceiveService{

    // 创建收货单
    rpc CreateReceive(CreateReceiveRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/receive"
            body:"*"
        };
    }

    // 查询收货单
    rpc ListReceive(ListReceiveRequest) returns (ListReceiveResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/receive"
        };
    }

    // 根据id查询收货单详情
    rpc GetReceiveById(GetReceiveByIdRequest) returns (Receive){
        option (google.api.http) = {
            get:"/api/v2/receipt/receive/{id}"
        };
    }

    // 根据receive_id查商品详情
    rpc GetReceiveProductById (GetReceiveProductByIdRequest) returns (GetReceiveProductByIdResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/receive/{id}/product"
        };
    }

    // 处理收货单
    rpc DealReceiveById (DealReceiveByIdRequest) returns (Response){
        option (google.api.http) = {
            put:"/api/v2/receipt/receive/{id}/{action}"
            body:"*"
        };
    }

    // 更新收货单
    rpc UpdateReceiveProducts(UpdateReceiveProductsRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/receive/{id}/update"
            body:"*"
        };
    }

    // 查询收货单汇总报表
    rpc GetReceivingCollect(GetReceivingCollectRequest) returns (GetReceivingCollectResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/receiving/bi/collect"
        };
    }
    // 查询收货单详情报表
    rpc GetReceivingDetail(GetReceivingDetailRequest) returns (GetReceivingDetailResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/receiving/bi/detailed"
        };
    }

    // 单据汇总报表
    rpc GetTotalReport (GetTotalReportRequest) returns (GetTotalReportResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/total/report"
        };
    }

    // 根据receive_code查商品详情
    rpc GetReceiveProductByCode (GetReceiveProductByCodeRequest) returns (GetReceiveProductByCodeResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/receive/{code}/product/bycode"
        };
    }

    // 根据receive_by查未收货的商品数量
    rpc GetUnfinishReceiveProducts (GetUnfinishReceiveProductsRequest) returns (GetUnfinishReceiveProductsResponse){
        option (google.api.http) = {
        };
    }

    rpc ListReceives(ListReceivesRequest) returns (ListReceiveResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/receives"
        };
    }

    rpc GetReturnAmountReport (GetReturnAmountReportRequest) returns (GetReturnAmountReportResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/vendor/return/amount/report"
        };
    }
}

message GetReturnAmountReportRequest{
    // 发货开始日期
    google.protobuf.Timestamp delivery_start_date = 1;
    // 发货结束日期
    google.protobuf.Timestamp delivery_end_date = 2;
    // 订货开始时间
    google.protobuf.Timestamp demand_start_date = 3;
    // 订货结束时间
    google.protobuf.Timestamp demand_end_date = 4;
    // 收货开始时间
    google.protobuf.Timestamp arrival_start_date = 5;
    // 收货结束时间
    google.protobuf.Timestamp arrival_end_date = 6;
    // 门店id
    repeated uint64 store_ids = 7;
    // 供应商id
    repeated uint64 vendor_ids = 8;
    // 公司id
    uint64 company_id = 9;
    // 商品id
    repeated uint64 product_ids = 10;
    // 状态
    repeated string status = 11;
    // 退货单号(门店单号)
    string return_code = 12;
    // 要货单号
    string order_code = 13;
    // 收货单号(退货单号)
    string batch_code = 14;
    // 分页大小
    int32 limit = 15;
    // 跳过行数
    int32 offset = 16;
    // 汇总方式: details-按明细； product_sum-按商品汇总；store_sum-按门店汇总
    string dime_type = 17;
    // 单据类型
    repeated string batch_types = 18;
    string main_branch_type = 19;
}

// 供应商发货明细对账报表通用返回参数
message GetReturnAmountReportResponse{
    repeated ReturnAmountDetail rows = 1;
    uint32 total = 2;
    ReturnAmountSum sum = 3;
}

message ReturnAmountSum{
    // 发货数量
    double sum_return_quantity = 1;
    // 发货未税金额
    double sum_return_cost_amount = 2;
    // 发货含税金额
    double sum_return_tax_amount = 3;
    // 收货数量
    double sum_receive_quantity = 4;
    // 收货未税金额
    double sum_receive_cost_amount = 5;
    // 收货含税金额
    double sum_receive_tax_amount = 6;
}

// 供应商发货对账明细内容
message ReturnAmountDetail {
    // 单据状态
    string status = 1;
    // 发货单号
    string delivery_code = 2;
    // 门店code
    string store_code = 3;
    // 门店名称
    string store_name = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6;
    // 单位名称
    string unit_name = 7;
    // 成本价
    double cost_price = 8;
    // 含税价
    double tax_price = 9;
    // 税率
    double tax_rate = 10;
    // 税额
    double tax = 11;
    // 发货数量
    double delivery_quantity = 12;
    // 发货未税金额
    double return_cost_amount= 13;
    // 发货含税金额
    double return_tax_amount= 14;
    // 收货数量
    double return_quantity = 15;
    // 收货未税金额
    double receive_cost_amount = 16;
    // 收货含税金额
    double receive_tax_amount = 17;
    // 公司编号
    string company_code = 18;
    // 公司名称
    string company_name = 19;
    // 订货日期
    google.protobuf.Timestamp demand_date = 20;
    // 发货日期
    google.protobuf.Timestamp delivery_date = 21;
    // 收货日期
    google.protobuf.Timestamp arrival_date = 22;
    // 供应商编码
    string vendor_code = 23;
    // 供应商名称
    string vendor_name = 24;
    // 退货单号
    string return_code = 25;
    // 要货单号
    string order_code = 26;
    // 供前端组件使用
    uint64 primary_key = 27;
    string batch_code = 28;

}

message GetUnfinishReceiveProductsRequest{
    uint64 receive_by = 1;
}

message GetUnfinishReceiveProductsResponse{
    map<uint64, double> unfinish_products_map = 1;
}

// 业务状态
enum ReceiptStatus {
    INIT = 0; //初始化
    SUBMIT = 1; //已提交
    APPROVE = 2; //已审核
    REJECT = 3; //已驳回
    CONFIRM = 4; //已确认
    CANCEL = 5; //已取消
}

// 处理状态
enum ProcessStatus {
    ACTIVATE = 0; //已激活
    SENT = 1; //发送
    PROCESSING = 2; //处理中
    SUCCESS = 3; //成功
    FINISH = 4; //结束
    FAILED = 5; //失败

}

message Response{
    uint64 id = 1;
    string status = 2;
    string msg = 3;
}

message Receive{
    // 收货单id
    uint64 id = 1;
    // 收货单单号
    string code = 2;
    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型
    string batch_type = 5;

    // 订货单id
    uint64 order_id = 6;
    // 订货单单号
    string order_code = 7;
    // 发货单id
    uint64 delivery_id = 8;
    // 发货单单号
    string delivery_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 单据状态
    string status = 12;
    // 业务状态
    string process_status = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    // 储藏方式
    string storage_type = 18;
    // 物流类型
    string distr_type = 19;

    //
    string third_party_id = 20;
    string third_party_type = 21;
    // 商品数量
    uint64 product_nums = 22;

    // 创建时间
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人id
    int64 created_by = 25;
    // 更新人id
    int64 updated_by = 26;
    // 商户id
    uint64 partner_id = 27;
    // 创建人
    string created_name = 28;
    // 更新人
    string updated_name = 29;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 收货单receive_by是main_branch
    string main_branch_type = 30;
    // 传输成本引擎 0:未传输，1:已传输
    string cost_trans_status = 31;
    // 更新次数
    int64 cost_update = 32;
    // 成本中心
    uint64 cost_center_id = 33;
    // 备注
    string remark = 34;
    // 原因
    string reason = 35;
    // 子账户类型
    string sub_account_type = 36;
    // 子账户接收方
    uint64 sub_receive_by = 37;
    // 子账户发送方
    uint64 sub_delivery_by = 38;
    // 订单类型： SD/ MD/ ...
    string demand_type = 39;
    // 配送方
    string delivery_by_code = 40;
    // 配送方
    string delivery_by_name = 41;
    // 加盟商id
    uint64 franchisee_id = 42;
    // 合计金额
    double sum_price_tax = 44;
    repeated Product products = 45;
    // 收货方
    string receive_by_code = 46;
    // 收货方
    string receive_by_name = 47;
    // 送货方联系人
    string contact = 48;
    // 联系电话
    string tel = 49;
    // 发货单备注
    string source_remark = 50;
    // 订货类型
    uint64 order_type_id = 51;
    // 发货单备注
    string demand_remark = 52;
}

message Product{
    // id
    uint64 id = 1;
    // 收货单id
    uint64 receive_id = 2;
    // 收货门店
    uint64 receive_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6;
    //
    string storage_type = 7;

    // 订货数量
    double order_quantity = 8;
    // 收货数量
    double delivery_quantity = 9;
    // 收货数量
    double receive_quantity = 10;

    // 收货状态
    bool is_confirmed = 11;

    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 创建人id
    uint64 created_by= 13;
    // 更新日期
    google.protobuf.Timestamp updated_at = 14;
    // 更新人id
    uint64 updated_by = 15;
    // 商户id
    uint64 partner_id = 16;
    // 创建人
    string created_name = 17;
    // 更新人
    string updated_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 单位规格
    string unit_spec = 21;
    // 商品分类
    uint64 category_id = 22;
    // 商品分类名称
    string category_name = 23;
    // 商品分类编号
    string category_code = 24;
    // 成本价
    double cost_price = 25;
    // 含税价
    double tax_price = 26;
    // 税率
    double tax_rate = 27;
    // 单位换算比例
    double unit_rate = 28;
    // 销售类型
    string sale_type = 29;
    // 子账户类型
    string sub_account_type = 30;
    // 子账户接收方
    uint64 sub_receive_by = 31;
    // 子账户发送方
    uint64 sub_delivery_by = 32;
    // 实际收货数量=收货数+门店承担差异数
    double actual_receive_quantity = 33;
    // 门店承担差异数
    double s_diff_quantity = 34;
    // 剩余最小收货数量（收货容差）
    double min_remaining_qty = 35;
    // 剩余最大收货数量（收货容差）
    double max_remaining_qty = 36;
    // 是否校验容差，0=不校验，1=校验
    int32 check_deviation = 37;
    // 单次收货数量
    double temp_quantity = 38;
}

message CreateReceiveRequest{
    //
    uint64 id = 1;
    string code = 2;

    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型
    string batch_type = 5;

    // 订货单id
    uint64 order_id = 6;
    // 订货单单号
    string order_code = 7;
    // 发货单id
    uint64 delivery_id = 8;
    // 发货单单号
    string delivery_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 储藏方式
    string storage_type = 12;
    // 物流类型
    string distr_type = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    repeated Product products = 18;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 收货单receive_by是main_branch
    string main_branch_type = 19;
     // 备注
     string remark = 20;
     // 原因
     string reason = 21;
     // 子账户类型
    string sub_account_type = 22;
    // 子账户接收方
    uint64 sub_receive_by = 23;
    // 子账户发送方
    uint64 sub_delivery_by = 24;
    // 订单类型
    string demand_type = 25;
    // 发货单备注
    string source_remark = 30;
    // 订货类型
    uint64 order_type_id = 31;
    // 加盟商id
    uint64 franchisee_id = 32;
}

message UpdateReceiveProductsRequest{
    // 收货单id
    uint64 id = 1;

    string status = 2;
    //
    string process_status = 3;
    // product
    repeated Product products= 4;
    //
    uint64 batch_id = 5;
    //
    uint64 order_id = 6;
    // 备注
    string remark = 7;
    // 原因
    string reason = 8;
    // 原因
    string distr_type = 9;
    uint64 receive_by = 10;
    uint64 delivery_by = 11;
    // 发货单备注
    string source_remark = 12;
    // 预计提货日期
    google.protobuf.Timestamp expect_date = 13;

}

message ListReceiveRequest{
    // 订货单号
    uint64 order_id = 1;
    //
    uint64 batch_id = 2;
    //
    uint64 delivery_id = 3;
    // 订货单号
    string batch_code = 4;
    // 发货单号
    string delivery_code = 5;
    // 收货单号
    string code = 6;
    // 要货单号
    string order_code = 7;
    // 接受方id
    repeated uint64 receive_bys = 8;
    // 发送方
    repeated uint64 delivery_bys = 9;
    // 订单状态
    repeated string status = 10;
    // 订单状态
    repeated string process_status = 11;
    // 订货开始日期
    google.protobuf.Timestamp start_date = 12;
    // 订货结束日期
    google.protobuf.Timestamp end_date = 13;
    // 物流类型
    string distr_type = 14;
    // 业务类型
    repeated string batch_type = 15;
    // 分页大小
    int32 limit = 16;
    // 跳过行数
    int32 offset = 17;
    // 排序字段
    string sort = 18;
    // 排序顺序
    string order = 19;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 收货单receive_by是main_branch
    string main_branch_type = 20;
    // 配送开始日期
    google.protobuf.Timestamp delivery_start_date = 21;
    // 配送结束日期
    google.protobuf.Timestamp delivery_end_date = 22;
    // 传输成本引擎 0:未传输，1:已传输
    string cost_trans_status = 23;
    // 收货开始时间
    google.protobuf.Timestamp received_start_date = 24;
    // 收货结束时间
    google.protobuf.Timestamp received_end_date = 25;
    // 子账户类型
    string sub_account_type = 26;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 27;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 28;
    // 收货方地理区域 —— 目前只支持门店
    repeated uint64 geo_regions = 29;
    // 订单类型
    string demand_type = 30;
    // 商品ids
    repeated uint64 product_ids = 31;
    // 是否包含商品
    string include_products = 32;
    repeated uint64 ids = 33;
    // 预计到货日期开始时间
    google.protobuf.Timestamp expect_start_date = 34;
    // 预计到货日期结束时间
    google.protobuf.Timestamp expect_end_date = 35;
    repeated uint64 batch_ids = 36;
}

message ListReceivesRequest{
    // 订货单号
    uint64 order_id = 1;
    //
    repeated uint64 batch_id = 2;
    //
    uint64 delivery_id = 3;
    // 订货单号
    string batch_code = 4;
    // 发货单号
    string delivery_code = 5;
    // 收货单号
    string code = 6;
    // 要货单号
    string order_code = 7;
    // 接受方id
    repeated uint64 receive_bys = 8;
    // 发送方
    repeated uint64 delivery_bys = 9;
    // 订单状态
    repeated string status = 10;
    // 订单状态
    repeated string process_status = 11;
    // 订货开始日期
    google.protobuf.Timestamp start_date = 12;
    // 订货结束日期
    google.protobuf.Timestamp end_date = 13;
    // 物流类型
    string distr_type = 14;
    // 业务类型
    repeated string batch_type = 15;
    // 分页大小
    int32 limit = 16;
    // 跳过行数
    int32 offset = 17;
    // 排序字段
    string sort = 18;
    // 排序顺序
    string order = 19;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 收货单receive_by是main_branch
    string main_branch_type = 20;
    // 配送开始日期
    google.protobuf.Timestamp delivery_start_date = 21;
    // 配送结束日期
    google.protobuf.Timestamp delivery_end_date = 22;
    // 传输成本引擎 0:未传输，1:已传输
    string cost_trans_status = 23;
    // 收货开始时间
    google.protobuf.Timestamp received_start_date = 24;
    // 收货结束时间
    google.protobuf.Timestamp received_end_date = 25;
    // 子账户类型
    string sub_account_type = 26;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 27;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 28;
    // 收货方地理区域 —— 目前只支持门店
    repeated uint64 geo_regions = 29;
    // 订单类型
    string demand_type = 30;
    // 商品ids
    repeated uint64 product_ids = 31;
    // 是否包含商品
    string include_products = 32;
    repeated uint64 ids = 33;
    // 预计到货日期开始时间
    google.protobuf.Timestamp expect_start_date = 34;
    // 预计到货日期结束时间
    google.protobuf.Timestamp expect_end_date = 35;
}

message ListReceiveResponse{
    repeated Receive rows = 1;
    uint64 total = 2;
}

message GetReceiveByIdRequest{
    // 收货单id(receive_id)
    uint64 id = 1;

}

message GetReceiveByIdResponse{
    // wrapper.Status status = 1;
    Receive receive = 1;
}

message GetReceiveProductByIdRequest{
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
}

message DealReceiveByIdRequest{
    uint64 id = 1;
    string action = 2;
    repeated Product products= 4;

}

message GetReceiveProductByIdResponse{
    // wrapper.Status status = 1;
    repeated Product rows = 1;
    uint64 total = 2;
}

// 收货单汇总报表请求参数
message GetReceivingCollectRequest{
    // 门店id
    repeated uint64 st_ids = 1;
    // 商品类别id
    repeated uint64 category_ids = 2;
    // 商品名称
    string product_name = 3;
    // 采购开始日期
    google.protobuf.Timestamp start_date = 4;
    // 采购结束日期
    google.protobuf.Timestamp end_date = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 返回总条数
    bool include_total = 8;
    // 区分门店仓库W/S
    string main_branch_type = 9;
    // 发货branch id
    repeated uint64 delivery_bys = 10;
    //
    repeated string batch_types = 11;
    // 收货开始时间
    google.protobuf.Timestamp received_start_date = 12;
    // 收货结束时间
    google.protobuf.Timestamp received_end_date = 13;
    // 物流模式
    repeated string distr_type = 14;
    // 汇总方式
    string group_by = 15;
    // 单据状态
    repeated string status = 16;

}

// 收货单汇总报表返回
message GetReceivingCollectResponse{
    repeated ReceivingCollect rows = 1;
    Total total = 2;
}

// 收货单详情报表请求
message GetReceivingDetailRequest{

    // 门店id
    repeated uint64 st_ids = 1;
    // 商品类别id
    repeated uint64 category_ids = 2;
    // 商品名称
    string product_name = 3;
    // 采购开始日期
    google.protobuf.Timestamp start_date = 4;
    // 采购结束日期
    google.protobuf.Timestamp end_date = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 返回总条数
    bool include_total = 8;
    // 单据号
    string code = 9;
    // 区分门店仓库W/S
    string main_branch_type = 10;
    // 发货branch id
    repeated uint64 delivery_bys = 11;
    //
    repeated string batch_types = 12;
    repeated uint64 product_ids = 13;
    // 收货开始时间
    google.protobuf.Timestamp received_start_date = 14;
    // 收货结束时间
    google.protobuf.Timestamp received_end_date = 15;
    // 物流模式
    repeated string distr_type = 16;
    // 单据状态
    repeated string status = 17;
    // 单据来源
    string demand_type = 18;
}
// 收货单详情报表返回
message GetReceivingDetailResponse{
    repeated ReceivingDetailed rows = 1;
    Total total = 2;
}

message Total{
    // 条数
    double count = 1;
    // 订货数量
    double sum_order_quantity = 2;
    // 收货数量
    double sum_delivery_quantity = 3;
    // 收货数量
    double sum_receive_quantity = 4;
    // 成本价
    double sum_cost_price = 5;
    // 含税价
    double sum_tax_price = 6;
    // 税率
    double tax_rate = 7;
    // 收货数量
    double sum_pick_quantity = 8;
    // 订货数量
    double sum_order_amount = 9;
    // 收货数量
    double sum_delivery_amount = 10;
    // 收货数量
    double sum_receive_amount = 11;
}

message ReceivingCollect{
    // 核算单位id
    uint64 accounting_unit_id = 1;
    // 核算单位名称
    string accounting_unit_name = 2;
    // 商品类别id
    uint64 category_id = 3;
    // 商品类别编码
    string category_code = 4;
    // 商品类别名称
    string category_name = 5;
    // 商品类别父级
    string category_parent = 6;
    // 配送数量
    double delivery_quantity = 7;
    // 商品id
    uint64 product_id = 8;
    // 商品编码
    string product_code = 9;
    // 商品名称
    string product_name = 10;
    // 商品规格
    string product_spec = 11;
    // 数量
    double quantity = 12;
    // 配送数量
    double receive_quantity = 13;
    // 订货数量
    double order_quantity = 14;
    // 差异数量
    double s_diff_quantity = 15;
    // 门店id
    uint64 receive_by = 16;
    // 门店编码
    string receive_by_code = 17;
    // 门店名称
    string receive_by_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 成本价
    double cost_price = 21;
    // 含税价
    double tax_price = 22;
    // 税率
    double tax_rate = 23;
    // 税额
    double tax = 24;
    // 子账户类型
    string sub_account_type = 25;
    // 子账户接收方
    uint64 sub_receive_by = 26;
    // 子账户发送方
    uint64 sub_delivery_by = 27;
    string sub_receive_by_code = 30;
    string sub_receive_by_name = 31;
    // 供货方id
    uint64 delivery_by = 32;
    // 供货方编码
    string delivery_by_code = 33;
    // 供货方名称
    string delivery_by_name = 34;
    // 物流模式
    string distr_type = 35;
    // 加盟商id
    uint64 franchisee_id = 36;
    // 单据来源
    string demand_type = 37;
}

message ReceivingDetailed{
    // 核算单位id
    uint64 accounting_unit_id = 1;
    // 核算单位名称
    string accounting_unit_name = 2;
    // 商品类别id
    uint64 category_id = 3;
    // 商品类别编码
    string category_code = 4;
    // 商品类别名称
    string category_name = 5;
    // 商品类别父级
    string category_parent = 6;
    // 配送数量
    double delivery_quantity = 7;
    // 商品id
    uint64 product_id = 8;
    // 商品编码
    string product_code = 9;
    // 商品名称
    string product_name = 10;
    // 商品规格
    string product_spec = 11;
    // 数量
    double quantity = 12;
    // 配送数量
    double receive_quantity = 13;
    // 订货数量
    double order_quantity = 14;
    // 差异数量
    double s_diff_quantity = 15;
    // 门店id
    uint64 receive_by = 16;
    // 门店编码
    string receive_by_code = 17;
    // 门店名称
    string receive_by_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 配送日期
    google.protobuf.Timestamp delivery_date = 21;
    // 订货日期
    google.protobuf.Timestamp demand_date = 22;
    // 订货单编码
    string receiving_code = 23;
    // 收货日期
    google.protobuf.Timestamp receiving_date = 24;
    // 收货单id
    uint64 receiving_id = 25;
    // id
    uint64 id = 26;
    // 成本价
    double cost_price = 27;
    // 含税价
    double tax_price = 28;
    // 税率
    double tax_rate = 29;
    //
    uint64 delivery_by = 30;
    // 门店编码
    string delivery_by_code = 31;
    // 门店名称
    string delivery_by_name = 32;
    // 税额
    double tax = 33;
    // 子账户类型
    string sub_account_type = 34;
    // 子账户接收方
    uint64 sub_receive_by = 35;
    // 子账户发送方
    uint64 sub_delivery_by = 36;
    string sub_receive_by_code = 37;
    string sub_receive_by_name = 38;
    // 采购/订货单号
    string batch_code = 39;
    // 创建人
    uint64 created_by = 40;
    // 预计到货日期
    google.protobuf.Timestamp expect_date = 41;
    // 状态
    string status = 42;
    // 原因
    string reason = 43;
    // 物流模式
    string distr_type = 44;
    // 加盟商id
    uint64 franchisee_id = 45;
}

message GetTotalReportRequest{
    repeated uint64 receive_bys = 1;
    repeated uint64 delivery_bys = 2;
    repeated uint64 product_ids = 3;
    repeated string batch_types = 4;
    string main_branch_type = 5;
    google.protobuf.Timestamp start_date = 6;
    google.protobuf.Timestamp end_date = 7;
    int32 limit = 8;
    int32 offset = 9;
    // 子账户类型
    repeated string sub_account_type = 10;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 11;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 12;
}

message GetTotalReportResponse{
    repeated TotalDetailed rows = 1;
    Total total = 2;
}

message TotalDetailed{
    // 商品类别id
    uint64 category_id = 3;
    // 商品类别编码
    string category_code = 4;
    // 商品类别名称
    string category_name = 5;
    // 配送数量
    double receive_quantity = 6;
    // 配送数量
    double delivery_quantity = 7;
    // 商品id
    uint64 product_id = 8;
    // 商品编码
    string product_code = 9;
    // 商品名称
    string product_name = 10;
    // 商品规格
    string product_spec = 11;
    //
    uint64 delivery_by = 13;
    // 门店编码
    string delivery_by_code = 14;
    // 门店名称
    string delivery_by_name = 15;
    // 门店id
    uint64 receive_by = 16;
    // 门店编码
    string receive_by_code = 17;
    // 门店名称
    string receive_by_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 子账户类型
    string sub_account_type = 22;
    // 子账户接收方
    uint64 sub_receive_by = 23;
    // 子账户发送方
    uint64 sub_delivery_by = 24;
    // id
    uint64 id = 26;
    // 收货金额
    double receive_amount = 27;
    // 退货金额
    double delivery_amount = 28;
    // 收货含税金额
    double receive_tax_amount = 29;
    // 退货含税金额
    double delivery_tax_amount = 30;
    // 采购金额
    double order_amount = 31;
    // 采购含税金额
    double order_tax_amount = 32;
    // 采购数量
    double order_quantity = 33;
    // 主单id(这里指订货单)用作跳转
    uint64 batch_id = 34;
    // SKU商品状态
    string product_status = 35;
    // 加盟商id
    uint64 franchisee_id = 36;
}

message GetReceiveProductByCodeRequest{
    // id
    string code = 1;
}

message GetReceiveProductByCodeResponse{
    // wrapper.Status status = 1;
    repeated Product rows = 1;
    uint64 total = 2;
}