syntax = "proto3";
package delivery;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// DeliveryService 发货相关服务
service DeliveryService{

    // 创建发货单
    rpc CreateDelivery(CreateDeliveryRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/delivery"
            body:"*"
        };
    }

    // 更新发货单
    rpc UpdateDelivery(UpdateDeliveryRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/delivery/{id}/update"
            body:"*"
        };
    }

    // 更新发货单
    rpc BulkUpdateDelivery(BulkUpdateDeliveryRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/delivery/update"
            body:"*"
        };
    }

    // 查询发货单
    rpc ListDelivery(ListDeliveryRequest) returns (ListDeliveryResponse){
        option (google.api.http) = {
            post:"/api/v2/receipt/delivery"
        };
    }

    // 根据id查询发货单详情
    rpc GetDeliveryById(GetDeliveryByIdRequest) returns (Delivery){
        option (google.api.http) = {
            get:"/api/v2/receipt/delivery/{id}"
        };
    }

    // 根据delivery_id查商品详情
    rpc GetDeliveryProductById (GetDeliveryProductByIdRequest) returns (GetDeliveryProductByIdResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/delivery/{id}/product"
        };
    }

    // 处理收货单
    rpc DealDeliveryById (DealDeliveryByIdRequest) returns (Response){
        option (google.api.http) = {
            put:"/api/v2/receipt/delivery/{id}/{action}"
            body:"*"
        };
    };

    // 批量处理收货单
    rpc BulkDealDeliverys (BulkDealDeliverysRequest) returns (Response){
        option (google.api.http) = {
            put:"/api/v2/receipt/delivery/{action}"
            body:"*"
        };
    };

    // 发货单报表(仓库)
    rpc GetDeliveryOrderReport (GetDeliveryOrderReportRequest) returns (GetDeliveryOrderReportResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/delivery/order/report"
        };
    }

    // 发货单汇总报表
    rpc GetDeliveryOrderReportTotal (GetDeliveryOrderReportTotalRequest) returns (GetDeliveryOrderReportTotalResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/delivery/order/report/total"
        };
    }
    
    // 批量发货查询
    rpc ListDeliveryDetails (DeliveryDetailsRequest) returns (ListDeliveryResponse){
        option (google.api.http) = {
            post:"/api/v2/receipt/delivery/details"
        };
    }

    // 处理收货单 —— 一般是给三方调用使用
    rpc DealDeliveryByCode (DealDeliveryByCodeRequest) returns (Response){
        option (google.api.http) = {
            put:"/api/v2/receipt/vendor/delivery/{action}"
            body:"*"
        };
    };

}

message DeliveryDetailsRequest{
    repeated uint64 ids = 1;
}

message Response{
    uint64 id = 1;
    string status = 2;
    string msg = 3;
    string code = 4;
    // 三方要货单单号
    string demand_code = 5;
    // 门店要货单单号
    string order_code = 6;
}

message Delivery{
    // 发货单id
    uint64 id = 1;
    // 发货单单号
    string code = 2;
    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型
    string batch_type = 5;

    // 订货单id
    uint64 order_id = 6;
    // 订货单单号
    string order_code = 7;
    // 订单id
    uint64 demand_id = 8;
    // 订单单号
    string demand_code = 9;
    // 收货单id
    uint64 receive_id = 10;
    // 收货单单号
    string receive_code = 11;

    // 单据状态
    string status = 12;
    // 业务状态
    string process_status = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    // 储藏方式
    string storage_type = 18;
    // 物流类型
    string distr_type = 19;
    // 物流方式
    // 物流号

    // 
    string third_party_id = 20;
    string third_party_type = 21;
    // 商品数量
    uint64 product_nums = 22;

    // 创建时间
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人id
    int64 created_by = 25;
    // 更新人id
    int64 updated_by = 26;
    // 商户id
    uint64 partner_id = 27;
    // 创建人
    string created_name = 28;
    // 更新人
    string updated_name = 29;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 发货单delivery_by是main_branch
    string main_branch_type = 30;
    // 接收门店-门店id
    uint64 receive_by = 31;
    // 配送方
    uint64 delivery_by = 32;
    // 传输成本引擎 0:未传输，1:已传输
    string cost_trans_status = 33;
    // 更新次数
    int64 cost_update = 34;
    // 成本中心
    uint64 cost_center_id = 35;
    // 备注
    string remark = 36;
    // 原因
    string reason = 37;
    // 子账户接收方
    uint64 sub_receive_by = 38;
    // 子账户发送方
    uint64 sub_delivery_by = 39;
    // 子账户类型
    string sub_account_type = 40;
    // 
    string demand_type = 41;
    // 接收方编码
    string receive_by_code = 42;
    // 接收方名称
    string receive_by_name = 43;
    // 配送方编码
    string delivery_by_code = 44;
    // 配送方名称
    string delivery_by_name = 45;
    repeated Product products = 46;
    // 加盟商id
    uint64 franchisee_id = 47;
    // 联系人
    string tel = 48;
    string contact = 49;
}

message Product{
    // id 
    uint64 id = 1; 
    // 发货单id
    uint64 delivery_id = 2;
    // 发货门店
    uint64 delivery_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6;
    // 
    string storage_type = 7;

    // 订货数量
    double order_quantity = 8;
    // 发货数量
    double delivery_quantity = 9;
    // 拣货数量
    double pick_quantity = 10;
    // 审核数量
    double confirm_quantity = 11;

    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 创建人id
    uint64 created_by= 13;
    // 更新日期
    google.protobuf.Timestamp updated_at = 14;
    // 更新人id
    uint64 updated_by = 15;
    // 商户id
    uint64 partner_id = 16;
    // 创建人
    string created_name = 17;
    // 更新人
    string updated_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 单位规格
    string unit_spec = 21;
    // 商品分类
    uint64 category_id = 22;
    // 商品分类名称
    string category_name = 23;
    // 商品分类编号
    string category_code = 24;
    // 发货状态
    bool is_confirmed = 25;
    // 成本价
    double cost_price = 26;
    // 含税价
    double tax_price = 27;
    // 税率
    double tax_rate = 28;
    // 单位换算比例
    double unit_rate = 29;
    // 销售类型
    string sale_type = 30;
    // 收货数量
    double receive_quantity = 31;
}

message CreateDeliveryRequest{

    // 主单id
    uint64 batch_id = 1;
    // 主单单号
    string batch_code = 2;
    // 主单类型
    string batch_type = 3;

    // 订货单id
    uint64 order_id = 4;
    // 订货单单号
    string order_code = 5;
    // 订单id
    uint64 demand_id = 6;
    // 订单单号
    string demand_code = 7;
    // 收货单id
    uint64 receive_id = 8;
    // 收货单单号
    string receive_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 储藏方式
    string storage_type = 12;
    // 物流类型
    string distr_type = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    repeated Product products = 18;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 发货单delivery_by是main_branch
    string main_branch_type = 19;
    // 备注
    string remark = 20;
    // 原因
    string reason = 21;
    // 子账户类型
    string sub_account_type = 22;
    // 子账户接收方
    uint64 sub_receive_by = 23;
    // 子账户发送方
    uint64 sub_delivery_by = 24;
    // 订单类型
    string demand_type = 25;
    // 订货单备注
    string source_remark = 30;
    // 订货类型
    uint64 order_type_id = 31;
    // 加盟商id
    uint64 franchisee_id = 32;
}

message UpdateDeliveryRequest{
    // 发货货单id
    uint64 id = 1;

    string status = 2;
    // 
    string process_status = 3;
    // product
    repeated Product products= 4;
    // 
    uint64 batch_id = 5;
    // 
    uint64 order_id = 6;
    // 备注
    string remark = 7;
    // 原因
    string reason = 8;
    // 原因
    string distr_type = 9;
    // 预计到货日期
    google.protobuf.Timestamp expect_date = 10;
    uint64 receive_by = 11;
    uint64 delivery_by = 12;
}

message BulkUpdateDeliveryRequest{
    repeated UpdateDeliveryRequest update_list = 1; 
}

message ListDeliveryRequest{
    // 订货单号
    uint64 order_id = 1;
    // 
    uint64 batch_id = 2;
    // 
    uint64 receive_id = 3;
    // 订货单号
    string batch_code = 4;
    // 收货单号
    string receive_code = 5;
    // 发货单号
    string code = 6;
    // 要货单号
    string order_code = 7;
    // 接受方id
    repeated uint64 receive_bys = 8;
    // 发送方
    repeated uint64 delivery_bys = 9;
    // 订单状态
    repeated string status = 10;
    // 订单状态
    repeated string process_status = 11;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 12;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 13;
    // 物流类型
    string distr_type = 14;
    // 业务类型
    repeated string batch_type = 15;
    // 分页大小
    int32 limit = 16;
    // 跳过行数
    int32 offset = 17;
    // 排序字段
    string sort = 18;
    // 排序顺序
    string order = 19;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 发货单delivery_by是main_branch
    string main_branch_type = 20;
    // 订单id
    uint64 demand_id = 21;
    // 订单单号
    string demand_code = 22;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 23;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 24;
    // 子账户类型
    string sub_account_type = 25;
    // 订货开始日期
    google.protobuf.Timestamp order_date_from = 26;
    // 订货结束日期
    google.protobuf.Timestamp order_date_to = 27;
    // 预计到货开始日期
    google.protobuf.Timestamp expect_date_from = 28;
    // 预计到货结束日期
    google.protobuf.Timestamp expect_date_to = 29;
    // 商品id
    repeated uint64 product_ids = 30;
    // 批量发货
    repeated string codes = 31;
    repeated uint64 ids = 32;
    // 是否包含商品
    string include_products = 33;
    // 储藏类型
    string storage_type = 34;
    // 商品分类
    repeated uint64 category_ids = 35;
    // 业务类型 —— 暂时兼容
    repeated string batch_types = 36;
}

message ListDeliveryResponse{
    repeated Delivery rows = 1;
    uint64 total = 2;
}

message GetDeliveryByIdRequest{
    // 发货单id(delivery_id)
    uint64 id = 1;

}

message GetDeliveryByIdResponse{
    // wrapper.Status status = 1;
    Delivery delivery = 1;
}

message GetDeliveryProductByIdRequest{
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
}

message DealDeliveryByIdRequest{
    uint64 id = 1;
    string action = 2;
    repeated Product products = 3;
    google.protobuf.Timestamp expect_date = 4;
    google.protobuf.Timestamp delivery_date = 5;
    string reason = 6;
    string remark = 7;
}

message BulkDealDeliverysRequest{
    message DealDetail {
        uint64 id = 1;
        google.protobuf.Timestamp expect_date = 2;
    }
    repeated uint64 ids = 1;
    string action = 2;
    repeated DealDetail details = 3;

}

message GetDeliveryProductByIdResponse{
    // wrapper.Status status = 1;
    repeated Product rows = 1;
    uint64 total = 2;
}

// 发货单报表请求参数
message GetDeliveryOrderReportRequest{
    // 开始日期
    google.protobuf.Timestamp start_date = 1;
    // 结束日期
    google.protobuf.Timestamp end_date = 2;
    // 接收方ids（这里对应门店id）
    repeated uint64 receive_bys = 3;
    // 发送方ids（这里对应仓库id）
    repeated uint64 delivery_bys = 4;
    // 商品ids
    repeated uint64 product_ids = 5;
    // 订单编号
    string order_code = 6;
    // 分页大小
    int32 limit = 7;
    // 跳过行数
    int32 offset = 8;
    // 排序字段
    string sort = 9;
    // 排序顺序
    string order = 10;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 发货单delivery_by是main_branch
    string main_branch_type = 12;
    // 查询分类（汇总:sum, 明细:detail）
    string classification = 13;
    // 
    repeated string batch_types = 14;
    // 单据状态
    repeated string status = 15;
    // 储藏类型
    string storage_type = 16;
    // 采购开始时间-采购退货单报表用
    google.protobuf.Timestamp purchase_start_date = 17;
    // 采购结束时间-采购退货单报表用
    google.protobuf.Timestamp purchase_end_date = 18;
    // 主单code(这里指订货单)筛选
    string batch_code = 19;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 20;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 21;
    // 子账户类型
    string sub_account_type = 22;
    // 物流类型
    string distr_type = 24;
    // 商品分类id
    repeated uint64 category_ids = 25;
}

// 发货单报表返回参数
message GetDeliveryOrderReportResponse{
    message DeliveryReport {
        // 订货日期
        google.protobuf.Timestamp demand_date = 1;
        // 发货日期
        google.protobuf.Timestamp delivery_date = 2;
        // 预计到货日期
        google.protobuf.Timestamp expect_date = 3;
        // 实际到货日期
        google.protobuf.Timestamp arrival_date = 4;
        // 仓库id
        uint64 delivery_by = 5;
        // 仓库编码
        string delivery_code = 6;
        // 仓库名称
        string delivery_name = 7;
        // 门店id
        uint64 receive_by = 8;
        // 门店code
        string receive_code = 9;
        // 门店名称
        string receive_name = 10;
        // 商品id
        uint64 product_id = 11;
        // 商品编码
        string product_code = 12;
        // 商品名称
        string product_name = 13;
        // 已发数量
        double delivery_quantity = 14;
        // 单位id
        uint64 unit_id = 15;
        // 单位名称
        string unit_name = 16;
        // 单位规格
        string unit_spec = 17;
        // 商品分类id
        uint64 category_id = 18;
        // 商品分类名称
        string category_name = 19;
        // 商品分类编号
        string category_code = 20;
        // 单据编码
        string order_code = 21;
        // 成本价
        double cost_price = 22;
        // 含税价
        double tax_price = 23;
        // 税率
        double tax_rate = 24;
        // 税额
        double tax = 25;
        // 订货单号 当单据类型是DEMAND时batch_code就是订货单号
        string batch_code = 26;
        // 单据状态
        string status = 27;
        // 储藏类型
        string storage_type = 28;
        // 门店锁定库存
        double store_frozen_qty = 29;
        // 订货数量
        double order_quantity = 30;
        // 主单id(这里指订货单)用作跳转
        uint64 batch_id = 31;
        // SKU商品状态
        string product_status = 32;
        // 子账户接收方
        uint64 sub_receive_by = 33;
        // 子账户发送方
        uint64 sub_delivery_by = 34;
        // 子账户类型
        string sub_account_type = 35;
        // 子账户code
        string sub_delivery_by_code = 36;
        // 子账户name
        string sub_delivery_by_name = 37;
        // 加盟商id
        uint64 franchisee_id = 38;
        // 待发数量
        double pick_quantity = 39;
    }
    repeated DeliveryReport rows = 1;
    uint32 total = 2;
}


// 发货单汇总报表请求参数
message GetDeliveryOrderReportTotalRequest {
    // 发货开始日期
    google.protobuf.Timestamp start_date = 1;
    // 发货结束日期
    google.protobuf.Timestamp end_date = 2;
    // 发送方id列表（这里对应仓库/加工中心id）
    repeated uint64 delivery_bys = 3;
    // 商品分类id列表
    repeated uint64 category_ids = 4;
    // 商品ids
    repeated uint64 product_ids = 5;
    // 分页大小
    int32 limit = 7;
    // 跳过行数
    int32 offset = 8;
    // 排序字段
    string sort = 9;
    // 排序顺序
    string order = 10;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商，M-加工中心
    // 比如加工中心发货报表传："M"
    string main_branch_type = 12;
    // batch_types：这里查门店订货传："DEMAND"
    repeated string batch_types = 14;
    // 单据状态（预留）
    repeated string status = 15;
    // 主单code(这里指订货单)筛选（预留）
    string batch_code = 19;
    // 订货开始日期
    google.protobuf.Timestamp order_date_from = 20;
    // 订货结束日期
    google.protobuf.Timestamp order_date_to = 21;
    // 预计到货日期开始日期
    google.protobuf.Timestamp expect_date_from = 22;
    // 预计到货日期结束日期
    google.protobuf.Timestamp expect_date_to = 23;
    // 收货方
    repeated uint64 receive_bys = 24;
    // code 筛选（预留）
    string code = 25;
    // 要货单号 筛选（预留）
    string order_code = 26;
    string group_by = 27;
    // 商品储藏类型
    string storage_type = 28;
}

// 发货单汇总报表返回参数
message GetDeliveryOrderReportTotalResponse {
    message DeliveryReportTotal {
        // 订货日期
        google.protobuf.Timestamp demand_date = 1;
        // 发货日期
        google.protobuf.Timestamp delivery_date = 2;
        // 预计到货日期
        google.protobuf.Timestamp expect_date = 3;
        // 实际到货日期
        google.protobuf.Timestamp arrival_date = 4;
        // 仓库id
        uint64 delivery_by = 5;
        // 仓库编码
        string delivery_code = 6;
        // 仓库名称
        string delivery_name = 7;
        // 商品id
        uint64 product_id = 11;
        // 商品编码
        string product_code = 12;
        // 商品名称
        string product_name = 13;
        // 汇总实发货数量
        double delivery_quantity = 14;
        // 单位id
        uint64 unit_id = 15;
        // 单位名称
        string unit_name = 16;
        // 单位规格
        string unit_spec = 17;
        // 商品分类id
        uint64 category_id = 18;
        // 商品分类名称
        string category_name = 19;
        // 商品分类编号
        string category_code = 20;
        // 汇总订货数量
        double order_quantity = 30;
        
        // 门店id
        uint64 receive_by = 31;
        // 门店编码
        string receive_by_code = 32;
        // 门店名称
        string receive_by_name = 33;
        // 储藏类型
        string storage_type = 34;
        // 待发数量
        double pick_quantity = 35;
    }
    repeated DeliveryReportTotal rows = 1;
    uint32 total = 2;
}

message DealDeliveryByCodeRequest{
    // 三方要货单单号
    string demand_code = 1;
    // 门店要货单单号
    string order_code = 2;
    // 三方发货单单号
    string code = 3;
    string action = 4;
    repeated Product products = 5;
    google.protobuf.Timestamp expect_date = 6;
    string reason = 7;
    string remark = 8;
}