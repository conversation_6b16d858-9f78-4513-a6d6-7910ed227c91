# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from receipt import operation_log_pb2 as receipt_dot_operation__log__pb2


class OperationLogServiceStub(object):
  """操作日志服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateFlowLogs = channel.unary_unary(
        '/operation_log.OperationLogService/CreateFlowLogs',
        request_serializer=receipt_dot_operation__log__pb2.CreateFlowLogsRequest.SerializeToString,
        response_deserializer=receipt_dot_operation__log__pb2.CreateFlowLogsResponse.FromString,
        )


class OperationLogServiceServicer(object):
  """操作日志服务
  """

  def CreateFlowLogs(self, request, context):
    """创建一条工作流记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_OperationLogServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateFlowLogs': grpc.unary_unary_rpc_method_handler(
          servicer.CreateFlowLogs,
          request_deserializer=receipt_dot_operation__log__pb2.CreateFlowLogsRequest.FromString,
          response_serializer=receipt_dot_operation__log__pb2.CreateFlowLogsResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'operation_log.OperationLogService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
