# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: receipt/delivery.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='receipt/delivery.proto',
  package='delivery',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x16receipt/delivery.proto\x12\x08\x64\x65livery\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"%\n\x16\x44\x65liveryDetailsRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x04\"j\n\x08Response\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65mand_code\x18\x05 \x01(\t\x12\x12\n\norder_code\x18\x06 \x01(\t\"\xca\t\n\x08\x44\x65livery\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x12\n\nbatch_type\x18\x05 \x01(\t\x12\x10\n\x08order_id\x18\x06 \x01(\x04\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x11\n\tdemand_id\x18\x08 \x01(\x04\x12\x13\n\x0b\x64\x65mand_code\x18\t \x01(\t\x12\x12\n\nreceive_id\x18\n \x01(\x04\x12\x14\n\x0creceive_code\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x16\n\x0eprocess_status\x18\r \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cstorage_type\x18\x12 \x01(\t\x12\x12\n\ndistr_type\x18\x13 \x01(\t\x12\x16\n\x0ethird_party_id\x18\x14 \x01(\t\x12\x18\n\x10third_party_type\x18\x15 \x01(\t\x12\x14\n\x0cproduct_nums\x18\x16 \x01(\x04\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x03\x12\x12\n\nupdated_by\x18\x1a \x01(\x03\x12\x12\n\npartner_id\x18\x1b \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1c \x01(\t\x12\x14\n\x0cupdated_name\x18\x1d \x01(\t\x12\x18\n\x10main_branch_type\x18\x1e \x01(\t\x12\x12\n\nreceive_by\x18\x1f \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18  \x01(\x04\x12\x19\n\x11\x63ost_trans_status\x18! \x01(\t\x12\x13\n\x0b\x63ost_update\x18\" \x01(\x03\x12\x16\n\x0e\x63ost_center_id\x18# \x01(\x04\x12\x0e\n\x06remark\x18$ \x01(\t\x12\x0e\n\x06reason\x18% \x01(\t\x12\x16\n\x0esub_receive_by\x18& \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18\' \x01(\x04\x12\x18\n\x10sub_account_type\x18( \x01(\t\x12\x13\n\x0b\x64\x65mand_type\x18) \x01(\t\x12\x17\n\x0freceive_by_code\x18* \x01(\t\x12\x17\n\x0freceive_by_name\x18+ \x01(\t\x12\x18\n\x10\x64\x65livery_by_code\x18, \x01(\t\x12\x18\n\x10\x64\x65livery_by_name\x18- \x01(\t\x12#\n\x08products\x18. \x03(\x0b\x32\x11.delivery.Product\x12\x15\n\rfranchisee_id\x18/ \x01(\x04\x12\x0b\n\x03tel\x18\x30 \x01(\t\x12\x0f\n\x07\x63ontact\x18\x31 \x01(\t\"\xca\x05\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0b\x64\x65livery_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x14\n\x0cstorage_type\x18\x07 \x01(\t\x12\x16\n\x0eorder_quantity\x18\x08 \x01(\x01\x12\x19\n\x11\x64\x65livery_quantity\x18\t \x01(\x01\x12\x15\n\rpick_quantity\x18\n \x01(\x01\x12\x18\n\x10\x63onfirm_quantity\x18\x0b \x01(\x01\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\r \x01(\x04\x12.\n\nupdated_at\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x0f \x01(\x04\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x11 \x01(\t\x12\x14\n\x0cupdated_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x11\n\tunit_spec\x18\x15 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x16 \x01(\x04\x12\x15\n\rcategory_name\x18\x17 \x01(\t\x12\x15\n\rcategory_code\x18\x18 \x01(\t\x12\x14\n\x0cis_confirmed\x18\x19 \x01(\x08\x12\x12\n\ncost_price\x18\x1a \x01(\x01\x12\x11\n\ttax_price\x18\x1b \x01(\x01\x12\x10\n\x08tax_rate\x18\x1c \x01(\x01\x12\x11\n\tunit_rate\x18\x1d \x01(\x01\x12\x11\n\tsale_type\x18\x1e \x01(\t\x12\x18\n\x10receive_quantity\x18\x1f \x01(\x01\"\xe7\x05\n\x15\x43reateDeliveryRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x12\n\nbatch_code\x18\x02 \x01(\t\x12\x12\n\nbatch_type\x18\x03 \x01(\t\x12\x10\n\x08order_id\x18\x04 \x01(\x04\x12\x12\n\norder_code\x18\x05 \x01(\t\x12\x11\n\tdemand_id\x18\x06 \x01(\x04\x12\x13\n\x0b\x64\x65mand_code\x18\x07 \x01(\t\x12\x12\n\nreceive_id\x18\x08 \x01(\x04\x12\x14\n\x0creceive_code\x18\t \x01(\t\x12\x12\n\nreceive_by\x18\n \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x0b \x01(\x04\x12\x14\n\x0cstorage_type\x18\x0c \x01(\t\x12\x12\n\ndistr_type\x18\r \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12#\n\x08products\x18\x12 \x03(\x0b\x32\x11.delivery.Product\x12\x18\n\x10main_branch_type\x18\x13 \x01(\t\x12\x0e\n\x06remark\x18\x14 \x01(\t\x12\x0e\n\x06reason\x18\x15 \x01(\t\x12\x18\n\x10sub_account_type\x18\x16 \x01(\t\x12\x16\n\x0esub_receive_by\x18\x17 \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18\x18 \x01(\x04\x12\x13\n\x0b\x64\x65mand_type\x18\x19 \x01(\t\x12\x15\n\rsource_remark\x18\x1e \x01(\t\x12\x15\n\rorder_type_id\x18\x1f \x01(\x04\x12\x15\n\rfranchisee_id\x18  \x01(\x04\"\xa2\x02\n\x15UpdateDeliveryRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x16\n\x0eprocess_status\x18\x03 \x01(\t\x12#\n\x08products\x18\x04 \x03(\x0b\x32\x11.delivery.Product\x12\x10\n\x08\x62\x61tch_id\x18\x05 \x01(\x04\x12\x10\n\x08order_id\x18\x06 \x01(\x04\x12\x0e\n\x06remark\x18\x07 \x01(\t\x12\x0e\n\x06reason\x18\x08 \x01(\t\x12\x12\n\ndistr_type\x18\t \x01(\t\x12/\n\x0b\x65xpect_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nreceive_by\x18\x0b \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x0c \x01(\x04\"Q\n\x19\x42ulkUpdateDeliveryRequest\x12\x34\n\x0bupdate_list\x18\x01 \x03(\x0b\x32\x1f.delivery.UpdateDeliveryRequest\"\x9b\x07\n\x13ListDeliveryRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12\x12\n\nreceive_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x14\n\x0creceive_code\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0breceive_bys\x18\x08 \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\t \x03(\x04\x12\x0e\n\x06status\x18\n \x03(\t\x12\x16\n\x0eprocess_status\x18\x0b \x03(\t\x12.\n\nstart_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ndistr_type\x18\x0e \x01(\t\x12\x12\n\nbatch_type\x18\x0f \x03(\t\x12\r\n\x05limit\x18\x10 \x01(\x05\x12\x0e\n\x06offset\x18\x11 \x01(\x05\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\r\n\x05order\x18\x13 \x01(\t\x12\x18\n\x10main_branch_type\x18\x14 \x01(\t\x12\x11\n\tdemand_id\x18\x15 \x01(\x04\x12\x13\n\x0b\x64\x65mand_code\x18\x16 \x01(\t\x12\x17\n\x0fsub_receive_bys\x18\x17 \x03(\x04\x12\x18\n\x10sub_delivery_bys\x18\x18 \x03(\x04\x12\x18\n\x10sub_account_type\x18\x19 \x01(\t\x12\x33\n\x0forder_date_from\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rorder_date_to\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x65xpect_date_from\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0e\x65xpect_date_to\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bproduct_ids\x18\x1e \x03(\x04\x12\r\n\x05\x63odes\x18\x1f \x03(\t\x12\x0b\n\x03ids\x18  \x03(\x04\x12\x18\n\x10include_products\x18! \x01(\t\x12\x14\n\x0cstorage_type\x18\" \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18# \x03(\x04\x12\x13\n\x0b\x62\x61tch_types\x18$ \x03(\t\"G\n\x14ListDeliveryResponse\x12 \n\x04rows\x18\x01 \x03(\x0b\x32\x12.delivery.Delivery\x12\r\n\x05total\x18\x02 \x01(\x04\"$\n\x16GetDeliveryByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"?\n\x17GetDeliveryByIdResponse\x12$\n\x08\x64\x65livery\x18\x01 \x01(\x0b\x32\x12.delivery.Delivery\"~\n\x1dGetDeliveryProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\"\xde\x01\n\x17\x44\x65\x61lDeliveryByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12#\n\x08products\x18\x03 \x03(\x0b\x32\x11.delivery.Product\x12/\n\x0b\x65xpect_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06reason\x18\x06 \x01(\t\x12\x0e\n\x06remark\x18\x07 \x01(\t\"\xc2\x01\n\x18\x42ulkDealDeliverysRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12>\n\x07\x64\x65tails\x18\x03 \x03(\x0b\x32-.delivery.BulkDealDeliverysRequest.DealDetail\x1aI\n\nDealDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12/\n\x0b\x65xpect_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"P\n\x1eGetDeliveryProductByIdResponse\x12\x1f\n\x04rows\x18\x01 \x03(\x0b\x32\x11.delivery.Product\x12\r\n\x05total\x18\x02 \x01(\x04\"\xf5\x04\n\x1dGetDeliveryOrderReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceive_bys\x18\x03 \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\x04 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x05 \x03(\x04\x12\x12\n\norder_code\x18\x06 \x01(\t\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x0c\n\x04sort\x18\t \x01(\t\x12\r\n\x05order\x18\n \x01(\t\x12\x18\n\x10main_branch_type\x18\x0c \x01(\t\x12\x16\n\x0e\x63lassification\x18\r \x01(\t\x12\x13\n\x0b\x62\x61tch_types\x18\x0e \x03(\t\x12\x0e\n\x06status\x18\x0f \x03(\t\x12\x14\n\x0cstorage_type\x18\x10 \x01(\t\x12\x37\n\x13purchase_start_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11purchase_end_date\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nbatch_code\x18\x13 \x01(\t\x12\x17\n\x0fsub_receive_bys\x18\x14 \x03(\x04\x12\x18\n\x10sub_delivery_bys\x18\x15 \x03(\x04\x12\x18\n\x10sub_account_type\x18\x16 \x01(\t\x12\x12\n\ndistr_type\x18\x18 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x19 \x03(\x04\"\xcd\x08\n\x1eGetDeliveryOrderReportResponse\x12\x45\n\x04rows\x18\x01 \x03(\x0b\x32\x37.delivery.GetDeliveryOrderReportResponse.DeliveryReport\x12\r\n\x05total\x18\x02 \x01(\r\x1a\xd4\x07\n\x0e\x44\x65liveryReport\x12/\n\x0b\x64\x65mand_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x64\x65livery_by\x18\x05 \x01(\x04\x12\x15\n\rdelivery_code\x18\x06 \x01(\t\x12\x15\n\rdelivery_name\x18\x07 \x01(\t\x12\x12\n\nreceive_by\x18\x08 \x01(\x04\x12\x14\n\x0creceive_code\x18\t \x01(\t\x12\x14\n\x0creceive_name\x18\n \x01(\t\x12\x12\n\nproduct_id\x18\x0b \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0c \x01(\t\x12\x14\n\x0cproduct_name\x18\r \x01(\t\x12\x19\n\x11\x64\x65livery_quantity\x18\x0e \x01(\x01\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x11\n\tunit_spec\x18\x11 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x12 \x01(\x04\x12\x15\n\rcategory_name\x18\x13 \x01(\t\x12\x15\n\rcategory_code\x18\x14 \x01(\t\x12\x12\n\norder_code\x18\x15 \x01(\t\x12\x12\n\ncost_price\x18\x16 \x01(\x01\x12\x11\n\ttax_price\x18\x17 \x01(\x01\x12\x10\n\x08tax_rate\x18\x18 \x01(\x01\x12\x0b\n\x03tax\x18\x19 \x01(\x01\x12\x12\n\nbatch_code\x18\x1a \x01(\t\x12\x0e\n\x06status\x18\x1b \x01(\t\x12\x14\n\x0cstorage_type\x18\x1c \x01(\t\x12\x18\n\x10store_frozen_qty\x18\x1d \x01(\x01\x12\x16\n\x0eorder_quantity\x18\x1e \x01(\x01\x12\x10\n\x08\x62\x61tch_id\x18\x1f \x01(\x04\x12\x16\n\x0eproduct_status\x18  \x01(\t\x12\x16\n\x0esub_receive_by\x18! \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18\" \x01(\x04\x12\x18\n\x10sub_account_type\x18# \x01(\t\x12\x1c\n\x14sub_delivery_by_code\x18$ \x01(\t\x12\x1c\n\x14sub_delivery_by_name\x18% \x01(\t\x12\x15\n\rfranchisee_id\x18& \x01(\x04\x12\x15\n\rpick_quantity\x18\' \x01(\x01\"\x83\x05\n\"GetDeliveryOrderReportTotalRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x64\x65livery_bys\x18\x03 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x04 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x05 \x03(\x04\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x0c\n\x04sort\x18\t \x01(\t\x12\r\n\x05order\x18\n \x01(\t\x12\x18\n\x10main_branch_type\x18\x0c \x01(\t\x12\x13\n\x0b\x62\x61tch_types\x18\x0e \x03(\t\x12\x0e\n\x06status\x18\x0f \x03(\t\x12\x12\n\nbatch_code\x18\x13 \x01(\t\x12\x33\n\x0forder_date_from\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rorder_date_to\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x65xpect_date_from\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0e\x65xpect_date_to\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceive_bys\x18\x18 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x19 \x01(\t\x12\x12\n\norder_code\x18\x1a \x01(\t\x12\x10\n\x08group_by\x18\x1b \x01(\t\x12\x14\n\x0cstorage_type\x18\x1c \x01(\t\"\x87\x06\n#GetDeliveryOrderReportTotalResponse\x12O\n\x04rows\x18\x01 \x03(\x0b\x32\x41.delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal\x12\r\n\x05total\x18\x02 \x01(\r\x1a\xff\x04\n\x13\x44\x65liveryReportTotal\x12/\n\x0b\x64\x65mand_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x64\x65livery_by\x18\x05 \x01(\x04\x12\x15\n\rdelivery_code\x18\x06 \x01(\t\x12\x15\n\rdelivery_name\x18\x07 \x01(\t\x12\x12\n\nproduct_id\x18\x0b \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0c \x01(\t\x12\x14\n\x0cproduct_name\x18\r \x01(\t\x12\x19\n\x11\x64\x65livery_quantity\x18\x0e \x01(\x01\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x11\n\tunit_spec\x18\x11 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x12 \x01(\x04\x12\x15\n\rcategory_name\x18\x13 \x01(\t\x12\x15\n\rcategory_code\x18\x14 \x01(\t\x12\x16\n\x0eorder_quantity\x18\x1e \x01(\x01\x12\x12\n\nreceive_by\x18\x1f \x01(\x04\x12\x17\n\x0freceive_by_code\x18  \x01(\t\x12\x17\n\x0freceive_by_name\x18! \x01(\t\x12\x14\n\x0cstorage_type\x18\" \x01(\t\x12\x15\n\rpick_quantity\x18# \x01(\x01\"\xd8\x01\n\x19\x44\x65\x61lDeliveryByCodeRequest\x12\x13\n\x0b\x64\x65mand_code\x18\x01 \x01(\t\x12\x12\n\norder_code\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12#\n\x08products\x18\x05 \x03(\x0b\x32\x11.delivery.Product\x12/\n\x0b\x65xpect_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06reason\x18\x07 \x01(\t\x12\x0e\n\x06remark\x18\x08 \x01(\t2\xbf\x0c\n\x0f\x44\x65liveryService\x12j\n\x0e\x43reateDelivery\x12\x1f.delivery.CreateDeliveryRequest\x1a\x12.delivery.Response\"#\x82\xd3\xe4\x93\x02\x1d\"\x18/api/v2/receipt/delivery:\x01*\x12v\n\x0eUpdateDelivery\x12\x1f.delivery.UpdateDeliveryRequest\x1a\x12.delivery.Response\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/receipt/delivery/{id}/update:\x01*\x12y\n\x12\x42ulkUpdateDelivery\x12#.delivery.BulkUpdateDeliveryRequest\x1a\x12.delivery.Response\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v2/receipt/delivery/update:\x01*\x12o\n\x0cListDelivery\x12\x1d.delivery.ListDeliveryRequest\x1a\x1e.delivery.ListDeliveryResponse\" \x82\xd3\xe4\x93\x02\x1a\"\x18/api/v2/receipt/delivery\x12n\n\x0fGetDeliveryById\x12 .delivery.GetDeliveryByIdRequest\x1a\x12.delivery.Delivery\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v2/receipt/delivery/{id}\x12\x9a\x01\n\x16GetDeliveryProductById\x12\'.delivery.GetDeliveryProductByIdRequest\x1a(.delivery.GetDeliveryProductByIdResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/receipt/delivery/{id}/product\x12|\n\x10\x44\x65\x61lDeliveryById\x12!.delivery.DealDeliveryByIdRequest\x1a\x12.delivery.Response\"1\x82\xd3\xe4\x93\x02+\x1a&/api/v2/receipt/delivery/{id}/{action}:\x01*\x12y\n\x11\x42ulkDealDeliverys\x12\".delivery.BulkDealDeliverysRequest\x1a\x12.delivery.Response\",\x82\xd3\xe4\x93\x02&\x1a!/api/v2/receipt/delivery/{action}:\x01*\x12\x9a\x01\n\x16GetDeliveryOrderReport\x12\'.delivery.GetDeliveryOrderReportRequest\x1a(.delivery.GetDeliveryOrderReportResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/receipt/delivery/order/report\x12\xaf\x01\n\x1bGetDeliveryOrderReportTotal\x12,.delivery.GetDeliveryOrderReportTotalRequest\x1a-.delivery.GetDeliveryOrderReportTotalResponse\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/receipt/delivery/order/report/total\x12\x81\x01\n\x13ListDeliveryDetails\x12 .delivery.DeliveryDetailsRequest\x1a\x1e.delivery.ListDeliveryResponse\"(\x82\xd3\xe4\x93\x02\"\" /api/v2/receipt/delivery/details\x12\x82\x01\n\x12\x44\x65\x61lDeliveryByCode\x12#.delivery.DealDeliveryByCodeRequest\x1a\x12.delivery.Response\"3\x82\xd3\xe4\x93\x02-\x1a(/api/v2/receipt/vendor/delivery/{action}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_DELIVERYDETAILSREQUEST = _descriptor.Descriptor(
  name='DeliveryDetailsRequest',
  full_name='delivery.DeliveryDetailsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='delivery.DeliveryDetailsRequest.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=99,
  serialized_end=136,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='delivery.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='delivery.Response.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='delivery.Response.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='delivery.Response.msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='delivery.Response.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='delivery.Response.demand_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='delivery.Response.order_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=138,
  serialized_end=244,
)


_DELIVERY = _descriptor.Descriptor(
  name='Delivery',
  full_name='delivery.Delivery',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='delivery.Delivery.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='delivery.Delivery.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='delivery.Delivery.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='delivery.Delivery.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='delivery.Delivery.batch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='delivery.Delivery.order_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='delivery.Delivery.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='delivery.Delivery.demand_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='delivery.Delivery.demand_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='delivery.Delivery.receive_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='delivery.Delivery.receive_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='delivery.Delivery.status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='delivery.Delivery.process_status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='delivery.Delivery.demand_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='delivery.Delivery.delivery_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='delivery.Delivery.expect_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='delivery.Delivery.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='delivery.Delivery.storage_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='delivery.Delivery.distr_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='third_party_id', full_name='delivery.Delivery.third_party_id', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='third_party_type', full_name='delivery.Delivery.third_party_type', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='delivery.Delivery.product_nums', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='delivery.Delivery.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='delivery.Delivery.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='delivery.Delivery.created_by', index=24,
      number=25, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='delivery.Delivery.updated_by', index=25,
      number=26, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='delivery.Delivery.partner_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='delivery.Delivery.created_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='delivery.Delivery.updated_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='delivery.Delivery.main_branch_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='delivery.Delivery.receive_by', index=30,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='delivery.Delivery.delivery_by', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='delivery.Delivery.cost_trans_status', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_update', full_name='delivery.Delivery.cost_update', index=33,
      number=34, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='delivery.Delivery.cost_center_id', index=34,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='delivery.Delivery.remark', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='delivery.Delivery.reason', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='delivery.Delivery.sub_receive_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='delivery.Delivery.sub_delivery_by', index=38,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='delivery.Delivery.sub_account_type', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='delivery.Delivery.demand_type', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_code', full_name='delivery.Delivery.receive_by_code', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_name', full_name='delivery.Delivery.receive_by_name', index=42,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='delivery.Delivery.delivery_by_code', index=43,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='delivery.Delivery.delivery_by_name', index=44,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='delivery.Delivery.products', index=45,
      number=46, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='delivery.Delivery.franchisee_id', index=46,
      number=47, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='delivery.Delivery.tel', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='delivery.Delivery.contact', index=48,
      number=49, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=247,
  serialized_end=1473,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='delivery.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='delivery.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='delivery.Product.delivery_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='delivery.Product.delivery_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='delivery.Product.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='delivery.Product.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='delivery.Product.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='delivery.Product.storage_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='delivery.Product.order_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='delivery.Product.delivery_quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pick_quantity', full_name='delivery.Product.pick_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_quantity', full_name='delivery.Product.confirm_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='delivery.Product.created_at', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='delivery.Product.created_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='delivery.Product.updated_at', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='delivery.Product.updated_by', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='delivery.Product.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='delivery.Product.created_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='delivery.Product.updated_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='delivery.Product.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='delivery.Product.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='delivery.Product.unit_spec', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='delivery.Product.category_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='delivery.Product.category_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='delivery.Product.category_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='delivery.Product.is_confirmed', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='delivery.Product.cost_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='delivery.Product.tax_price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='delivery.Product.tax_rate', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='delivery.Product.unit_rate', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='delivery.Product.sale_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_quantity', full_name='delivery.Product.receive_quantity', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1476,
  serialized_end=2190,
)


_CREATEDELIVERYREQUEST = _descriptor.Descriptor(
  name='CreateDeliveryRequest',
  full_name='delivery.CreateDeliveryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='delivery.CreateDeliveryRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='delivery.CreateDeliveryRequest.batch_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='delivery.CreateDeliveryRequest.batch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='delivery.CreateDeliveryRequest.order_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='delivery.CreateDeliveryRequest.order_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='delivery.CreateDeliveryRequest.demand_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='delivery.CreateDeliveryRequest.demand_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='delivery.CreateDeliveryRequest.receive_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='delivery.CreateDeliveryRequest.receive_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='delivery.CreateDeliveryRequest.receive_by', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='delivery.CreateDeliveryRequest.delivery_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='delivery.CreateDeliveryRequest.storage_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='delivery.CreateDeliveryRequest.distr_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='delivery.CreateDeliveryRequest.demand_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='delivery.CreateDeliveryRequest.delivery_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='delivery.CreateDeliveryRequest.expect_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='delivery.CreateDeliveryRequest.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='delivery.CreateDeliveryRequest.products', index=17,
      number=18, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='delivery.CreateDeliveryRequest.main_branch_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='delivery.CreateDeliveryRequest.remark', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='delivery.CreateDeliveryRequest.reason', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='delivery.CreateDeliveryRequest.sub_account_type', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='delivery.CreateDeliveryRequest.sub_receive_by', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='delivery.CreateDeliveryRequest.sub_delivery_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='delivery.CreateDeliveryRequest.demand_type', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_remark', full_name='delivery.CreateDeliveryRequest.source_remark', index=25,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='delivery.CreateDeliveryRequest.order_type_id', index=26,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='delivery.CreateDeliveryRequest.franchisee_id', index=27,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2193,
  serialized_end=2936,
)


_UPDATEDELIVERYREQUEST = _descriptor.Descriptor(
  name='UpdateDeliveryRequest',
  full_name='delivery.UpdateDeliveryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='delivery.UpdateDeliveryRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='delivery.UpdateDeliveryRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='delivery.UpdateDeliveryRequest.process_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='delivery.UpdateDeliveryRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='delivery.UpdateDeliveryRequest.batch_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='delivery.UpdateDeliveryRequest.order_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='delivery.UpdateDeliveryRequest.remark', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='delivery.UpdateDeliveryRequest.reason', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='delivery.UpdateDeliveryRequest.distr_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='delivery.UpdateDeliveryRequest.expect_date', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='delivery.UpdateDeliveryRequest.receive_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='delivery.UpdateDeliveryRequest.delivery_by', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2939,
  serialized_end=3229,
)


_BULKUPDATEDELIVERYREQUEST = _descriptor.Descriptor(
  name='BulkUpdateDeliveryRequest',
  full_name='delivery.BulkUpdateDeliveryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='update_list', full_name='delivery.BulkUpdateDeliveryRequest.update_list', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3231,
  serialized_end=3312,
)


_LISTDELIVERYREQUEST = _descriptor.Descriptor(
  name='ListDeliveryRequest',
  full_name='delivery.ListDeliveryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='delivery.ListDeliveryRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='delivery.ListDeliveryRequest.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='delivery.ListDeliveryRequest.receive_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='delivery.ListDeliveryRequest.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='delivery.ListDeliveryRequest.receive_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='delivery.ListDeliveryRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='delivery.ListDeliveryRequest.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='delivery.ListDeliveryRequest.receive_bys', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='delivery.ListDeliveryRequest.delivery_bys', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='delivery.ListDeliveryRequest.status', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='delivery.ListDeliveryRequest.process_status', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='delivery.ListDeliveryRequest.start_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='delivery.ListDeliveryRequest.end_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='delivery.ListDeliveryRequest.distr_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='delivery.ListDeliveryRequest.batch_type', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='delivery.ListDeliveryRequest.limit', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='delivery.ListDeliveryRequest.offset', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='delivery.ListDeliveryRequest.sort', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='delivery.ListDeliveryRequest.order', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='delivery.ListDeliveryRequest.main_branch_type', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='delivery.ListDeliveryRequest.demand_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='delivery.ListDeliveryRequest.demand_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_bys', full_name='delivery.ListDeliveryRequest.sub_receive_bys', index=22,
      number=23, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_bys', full_name='delivery.ListDeliveryRequest.sub_delivery_bys', index=23,
      number=24, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='delivery.ListDeliveryRequest.sub_account_type', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date_from', full_name='delivery.ListDeliveryRequest.order_date_from', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date_to', full_name='delivery.ListDeliveryRequest.order_date_to', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date_from', full_name='delivery.ListDeliveryRequest.expect_date_from', index=27,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date_to', full_name='delivery.ListDeliveryRequest.expect_date_to', index=28,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='delivery.ListDeliveryRequest.product_ids', index=29,
      number=30, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='codes', full_name='delivery.ListDeliveryRequest.codes', index=30,
      number=31, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='delivery.ListDeliveryRequest.ids', index=31,
      number=32, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_products', full_name='delivery.ListDeliveryRequest.include_products', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='delivery.ListDeliveryRequest.storage_type', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='delivery.ListDeliveryRequest.category_ids', index=34,
      number=35, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_types', full_name='delivery.ListDeliveryRequest.batch_types', index=35,
      number=36, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3315,
  serialized_end=4238,
)


_LISTDELIVERYRESPONSE = _descriptor.Descriptor(
  name='ListDeliveryResponse',
  full_name='delivery.ListDeliveryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='delivery.ListDeliveryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='delivery.ListDeliveryResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4240,
  serialized_end=4311,
)


_GETDELIVERYBYIDREQUEST = _descriptor.Descriptor(
  name='GetDeliveryByIdRequest',
  full_name='delivery.GetDeliveryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='delivery.GetDeliveryByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4313,
  serialized_end=4349,
)


_GETDELIVERYBYIDRESPONSE = _descriptor.Descriptor(
  name='GetDeliveryByIdResponse',
  full_name='delivery.GetDeliveryByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='delivery', full_name='delivery.GetDeliveryByIdResponse.delivery', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4351,
  serialized_end=4414,
)


_GETDELIVERYPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetDeliveryProductByIdRequest',
  full_name='delivery.GetDeliveryProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='delivery.GetDeliveryProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='delivery.GetDeliveryProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='delivery.GetDeliveryProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='delivery.GetDeliveryProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='delivery.GetDeliveryProductByIdRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='delivery.GetDeliveryProductByIdRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4416,
  serialized_end=4542,
)


_DEALDELIVERYBYIDREQUEST = _descriptor.Descriptor(
  name='DealDeliveryByIdRequest',
  full_name='delivery.DealDeliveryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='delivery.DealDeliveryByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='delivery.DealDeliveryByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='delivery.DealDeliveryByIdRequest.products', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='delivery.DealDeliveryByIdRequest.expect_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='delivery.DealDeliveryByIdRequest.delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='delivery.DealDeliveryByIdRequest.reason', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='delivery.DealDeliveryByIdRequest.remark', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4545,
  serialized_end=4767,
)


_BULKDEALDELIVERYSREQUEST_DEALDETAIL = _descriptor.Descriptor(
  name='DealDetail',
  full_name='delivery.BulkDealDeliverysRequest.DealDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='delivery.BulkDealDeliverysRequest.DealDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='delivery.BulkDealDeliverysRequest.DealDetail.expect_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4891,
  serialized_end=4964,
)

_BULKDEALDELIVERYSREQUEST = _descriptor.Descriptor(
  name='BulkDealDeliverysRequest',
  full_name='delivery.BulkDealDeliverysRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='delivery.BulkDealDeliverysRequest.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='delivery.BulkDealDeliverysRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='delivery.BulkDealDeliverysRequest.details', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_BULKDEALDELIVERYSREQUEST_DEALDETAIL, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4770,
  serialized_end=4964,
)


_GETDELIVERYPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetDeliveryProductByIdResponse',
  full_name='delivery.GetDeliveryProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='delivery.GetDeliveryProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='delivery.GetDeliveryProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4966,
  serialized_end=5046,
)


_GETDELIVERYORDERREPORTREQUEST = _descriptor.Descriptor(
  name='GetDeliveryOrderReportRequest',
  full_name='delivery.GetDeliveryOrderReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='delivery.GetDeliveryOrderReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='delivery.GetDeliveryOrderReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='delivery.GetDeliveryOrderReportRequest.receive_bys', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='delivery.GetDeliveryOrderReportRequest.delivery_bys', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='delivery.GetDeliveryOrderReportRequest.product_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='delivery.GetDeliveryOrderReportRequest.order_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='delivery.GetDeliveryOrderReportRequest.limit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='delivery.GetDeliveryOrderReportRequest.offset', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='delivery.GetDeliveryOrderReportRequest.sort', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='delivery.GetDeliveryOrderReportRequest.order', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='delivery.GetDeliveryOrderReportRequest.main_branch_type', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='classification', full_name='delivery.GetDeliveryOrderReportRequest.classification', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_types', full_name='delivery.GetDeliveryOrderReportRequest.batch_types', index=12,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='delivery.GetDeliveryOrderReportRequest.status', index=13,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='delivery.GetDeliveryOrderReportRequest.storage_type', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_start_date', full_name='delivery.GetDeliveryOrderReportRequest.purchase_start_date', index=15,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_end_date', full_name='delivery.GetDeliveryOrderReportRequest.purchase_end_date', index=16,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='delivery.GetDeliveryOrderReportRequest.batch_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_bys', full_name='delivery.GetDeliveryOrderReportRequest.sub_receive_bys', index=18,
      number=20, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_bys', full_name='delivery.GetDeliveryOrderReportRequest.sub_delivery_bys', index=19,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='delivery.GetDeliveryOrderReportRequest.sub_account_type', index=20,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='delivery.GetDeliveryOrderReportRequest.distr_type', index=21,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='delivery.GetDeliveryOrderReportRequest.category_ids', index=22,
      number=25, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5049,
  serialized_end=5678,
)


_GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT = _descriptor.Descriptor(
  name='DeliveryReport',
  full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.demand_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.delivery_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.expect_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.arrival_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.delivery_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.delivery_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_name', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.delivery_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.receive_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.receive_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_name', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.receive_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.product_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.product_code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.product_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.delivery_quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.unit_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.unit_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.unit_spec', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.category_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.category_name', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.category_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.order_code', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.cost_price', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.tax_price', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.tax_rate', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.tax', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.batch_code', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.status', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.storage_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_frozen_qty', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.store_frozen_qty', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.order_quantity', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.batch_id', index=30,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.product_status', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.sub_receive_by', index=32,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.sub_delivery_by', index=33,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.sub_account_type', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by_code', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.sub_delivery_by_code', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by_name', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.sub_delivery_by_name', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.franchisee_id', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pick_quantity', full_name='delivery.GetDeliveryOrderReportResponse.DeliveryReport.pick_quantity', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5802,
  serialized_end=6782,
)

_GETDELIVERYORDERREPORTRESPONSE = _descriptor.Descriptor(
  name='GetDeliveryOrderReportResponse',
  full_name='delivery.GetDeliveryOrderReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='delivery.GetDeliveryOrderReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='delivery.GetDeliveryOrderReportResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5681,
  serialized_end=6782,
)


_GETDELIVERYORDERREPORTTOTALREQUEST = _descriptor.Descriptor(
  name='GetDeliveryOrderReportTotalRequest',
  full_name='delivery.GetDeliveryOrderReportTotalRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='delivery.GetDeliveryOrderReportTotalRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='delivery.GetDeliveryOrderReportTotalRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='delivery.GetDeliveryOrderReportTotalRequest.delivery_bys', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='delivery.GetDeliveryOrderReportTotalRequest.category_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='delivery.GetDeliveryOrderReportTotalRequest.product_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='delivery.GetDeliveryOrderReportTotalRequest.limit', index=5,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='delivery.GetDeliveryOrderReportTotalRequest.offset', index=6,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='delivery.GetDeliveryOrderReportTotalRequest.sort', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='delivery.GetDeliveryOrderReportTotalRequest.order', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='delivery.GetDeliveryOrderReportTotalRequest.main_branch_type', index=9,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_types', full_name='delivery.GetDeliveryOrderReportTotalRequest.batch_types', index=10,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='delivery.GetDeliveryOrderReportTotalRequest.status', index=11,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='delivery.GetDeliveryOrderReportTotalRequest.batch_code', index=12,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date_from', full_name='delivery.GetDeliveryOrderReportTotalRequest.order_date_from', index=13,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date_to', full_name='delivery.GetDeliveryOrderReportTotalRequest.order_date_to', index=14,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date_from', full_name='delivery.GetDeliveryOrderReportTotalRequest.expect_date_from', index=15,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date_to', full_name='delivery.GetDeliveryOrderReportTotalRequest.expect_date_to', index=16,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='delivery.GetDeliveryOrderReportTotalRequest.receive_bys', index=17,
      number=24, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='delivery.GetDeliveryOrderReportTotalRequest.code', index=18,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='delivery.GetDeliveryOrderReportTotalRequest.order_code', index=19,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_by', full_name='delivery.GetDeliveryOrderReportTotalRequest.group_by', index=20,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='delivery.GetDeliveryOrderReportTotalRequest.storage_type', index=21,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6785,
  serialized_end=7428,
)


_GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL = _descriptor.Descriptor(
  name='DeliveryReportTotal',
  full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.demand_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.delivery_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.expect_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.arrival_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.delivery_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.delivery_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_name', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.delivery_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.product_id', index=7,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.product_code', index=8,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.product_name', index=9,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.delivery_quantity', index=10,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.unit_id', index=11,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.unit_name', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.unit_spec', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.category_id', index=14,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.category_name', index=15,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.category_code', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.order_quantity', index=17,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.receive_by', index=18,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_code', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.receive_by_code', index=19,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by_name', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.receive_by_name', index=20,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.storage_type', index=21,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pick_quantity', full_name='delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal.pick_quantity', index=22,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7567,
  serialized_end=8206,
)

_GETDELIVERYORDERREPORTTOTALRESPONSE = _descriptor.Descriptor(
  name='GetDeliveryOrderReportTotalResponse',
  full_name='delivery.GetDeliveryOrderReportTotalResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='delivery.GetDeliveryOrderReportTotalResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='delivery.GetDeliveryOrderReportTotalResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7431,
  serialized_end=8206,
)


_DEALDELIVERYBYCODEREQUEST = _descriptor.Descriptor(
  name='DealDeliveryByCodeRequest',
  full_name='delivery.DealDeliveryByCodeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='delivery.DealDeliveryByCodeRequest.demand_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='delivery.DealDeliveryByCodeRequest.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='delivery.DealDeliveryByCodeRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='delivery.DealDeliveryByCodeRequest.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='delivery.DealDeliveryByCodeRequest.products', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='delivery.DealDeliveryByCodeRequest.expect_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='delivery.DealDeliveryByCodeRequest.reason', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='delivery.DealDeliveryByCodeRequest.remark', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8209,
  serialized_end=8425,
)

_DELIVERY.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DELIVERY.fields_by_name['products'].message_type = _PRODUCT
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDELIVERYREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDELIVERYREQUEST.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDELIVERYREQUEST.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDELIVERYREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDELIVERYREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATEDELIVERYREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATEDELIVERYREQUEST.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BULKUPDATEDELIVERYREQUEST.fields_by_name['update_list'].message_type = _UPDATEDELIVERYREQUEST
_LISTDELIVERYREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['order_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['order_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['expect_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYREQUEST.fields_by_name['expect_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDELIVERYRESPONSE.fields_by_name['rows'].message_type = _DELIVERY
_GETDELIVERYBYIDRESPONSE.fields_by_name['delivery'].message_type = _DELIVERY
_DEALDELIVERYBYIDREQUEST.fields_by_name['products'].message_type = _PRODUCT
_DEALDELIVERYBYIDREQUEST.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEALDELIVERYBYIDREQUEST.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BULKDEALDELIVERYSREQUEST_DEALDETAIL.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BULKDEALDELIVERYSREQUEST_DEALDETAIL.containing_type = _BULKDEALDELIVERYSREQUEST
_BULKDEALDELIVERYSREQUEST.fields_by_name['details'].message_type = _BULKDEALDELIVERYSREQUEST_DEALDETAIL
_GETDELIVERYPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
_GETDELIVERYORDERREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTREQUEST.fields_by_name['purchase_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTREQUEST.fields_by_name['purchase_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT.containing_type = _GETDELIVERYORDERREPORTRESPONSE
_GETDELIVERYORDERREPORTRESPONSE.fields_by_name['rows'].message_type = _GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT
_GETDELIVERYORDERREPORTTOTALREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALREQUEST.fields_by_name['order_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALREQUEST.fields_by_name['order_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALREQUEST.fields_by_name['expect_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALREQUEST.fields_by_name['expect_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL.containing_type = _GETDELIVERYORDERREPORTTOTALRESPONSE
_GETDELIVERYORDERREPORTTOTALRESPONSE.fields_by_name['rows'].message_type = _GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL
_DEALDELIVERYBYCODEREQUEST.fields_by_name['products'].message_type = _PRODUCT
_DEALDELIVERYBYCODEREQUEST.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['DeliveryDetailsRequest'] = _DELIVERYDETAILSREQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['Delivery'] = _DELIVERY
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['CreateDeliveryRequest'] = _CREATEDELIVERYREQUEST
DESCRIPTOR.message_types_by_name['UpdateDeliveryRequest'] = _UPDATEDELIVERYREQUEST
DESCRIPTOR.message_types_by_name['BulkUpdateDeliveryRequest'] = _BULKUPDATEDELIVERYREQUEST
DESCRIPTOR.message_types_by_name['ListDeliveryRequest'] = _LISTDELIVERYREQUEST
DESCRIPTOR.message_types_by_name['ListDeliveryResponse'] = _LISTDELIVERYRESPONSE
DESCRIPTOR.message_types_by_name['GetDeliveryByIdRequest'] = _GETDELIVERYBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetDeliveryByIdResponse'] = _GETDELIVERYBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetDeliveryProductByIdRequest'] = _GETDELIVERYPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['DealDeliveryByIdRequest'] = _DEALDELIVERYBYIDREQUEST
DESCRIPTOR.message_types_by_name['BulkDealDeliverysRequest'] = _BULKDEALDELIVERYSREQUEST
DESCRIPTOR.message_types_by_name['GetDeliveryProductByIdResponse'] = _GETDELIVERYPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetDeliveryOrderReportRequest'] = _GETDELIVERYORDERREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetDeliveryOrderReportResponse'] = _GETDELIVERYORDERREPORTRESPONSE
DESCRIPTOR.message_types_by_name['GetDeliveryOrderReportTotalRequest'] = _GETDELIVERYORDERREPORTTOTALREQUEST
DESCRIPTOR.message_types_by_name['GetDeliveryOrderReportTotalResponse'] = _GETDELIVERYORDERREPORTTOTALRESPONSE
DESCRIPTOR.message_types_by_name['DealDeliveryByCodeRequest'] = _DEALDELIVERYBYCODEREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DeliveryDetailsRequest = _reflection.GeneratedProtocolMessageType('DeliveryDetailsRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYDETAILSREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.DeliveryDetailsRequest)
  ))
_sym_db.RegisterMessage(DeliveryDetailsRequest)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.Response)
  ))
_sym_db.RegisterMessage(Response)

Delivery = _reflection.GeneratedProtocolMessageType('Delivery', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERY,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.Delivery)
  ))
_sym_db.RegisterMessage(Delivery)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.Product)
  ))
_sym_db.RegisterMessage(Product)

CreateDeliveryRequest = _reflection.GeneratedProtocolMessageType('CreateDeliveryRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDELIVERYREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.CreateDeliveryRequest)
  ))
_sym_db.RegisterMessage(CreateDeliveryRequest)

UpdateDeliveryRequest = _reflection.GeneratedProtocolMessageType('UpdateDeliveryRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEDELIVERYREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.UpdateDeliveryRequest)
  ))
_sym_db.RegisterMessage(UpdateDeliveryRequest)

BulkUpdateDeliveryRequest = _reflection.GeneratedProtocolMessageType('BulkUpdateDeliveryRequest', (_message.Message,), dict(
  DESCRIPTOR = _BULKUPDATEDELIVERYREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.BulkUpdateDeliveryRequest)
  ))
_sym_db.RegisterMessage(BulkUpdateDeliveryRequest)

ListDeliveryRequest = _reflection.GeneratedProtocolMessageType('ListDeliveryRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTDELIVERYREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.ListDeliveryRequest)
  ))
_sym_db.RegisterMessage(ListDeliveryRequest)

ListDeliveryResponse = _reflection.GeneratedProtocolMessageType('ListDeliveryResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTDELIVERYRESPONSE,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.ListDeliveryResponse)
  ))
_sym_db.RegisterMessage(ListDeliveryResponse)

GetDeliveryByIdRequest = _reflection.GeneratedProtocolMessageType('GetDeliveryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYBYIDREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.GetDeliveryByIdRequest)
  ))
_sym_db.RegisterMessage(GetDeliveryByIdRequest)

GetDeliveryByIdResponse = _reflection.GeneratedProtocolMessageType('GetDeliveryByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYBYIDRESPONSE,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.GetDeliveryByIdResponse)
  ))
_sym_db.RegisterMessage(GetDeliveryByIdResponse)

GetDeliveryProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetDeliveryProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYPRODUCTBYIDREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.GetDeliveryProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetDeliveryProductByIdRequest)

DealDeliveryByIdRequest = _reflection.GeneratedProtocolMessageType('DealDeliveryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALDELIVERYBYIDREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.DealDeliveryByIdRequest)
  ))
_sym_db.RegisterMessage(DealDeliveryByIdRequest)

BulkDealDeliverysRequest = _reflection.GeneratedProtocolMessageType('BulkDealDeliverysRequest', (_message.Message,), dict(

  DealDetail = _reflection.GeneratedProtocolMessageType('DealDetail', (_message.Message,), dict(
    DESCRIPTOR = _BULKDEALDELIVERYSREQUEST_DEALDETAIL,
    __module__ = 'receipt.delivery_pb2'
    # @@protoc_insertion_point(class_scope:delivery.BulkDealDeliverysRequest.DealDetail)
    ))
  ,
  DESCRIPTOR = _BULKDEALDELIVERYSREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.BulkDealDeliverysRequest)
  ))
_sym_db.RegisterMessage(BulkDealDeliverysRequest)
_sym_db.RegisterMessage(BulkDealDeliverysRequest.DealDetail)

GetDeliveryProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetDeliveryProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYPRODUCTBYIDRESPONSE,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.GetDeliveryProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetDeliveryProductByIdResponse)

GetDeliveryOrderReportRequest = _reflection.GeneratedProtocolMessageType('GetDeliveryOrderReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYORDERREPORTREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.GetDeliveryOrderReportRequest)
  ))
_sym_db.RegisterMessage(GetDeliveryOrderReportRequest)

GetDeliveryOrderReportResponse = _reflection.GeneratedProtocolMessageType('GetDeliveryOrderReportResponse', (_message.Message,), dict(

  DeliveryReport = _reflection.GeneratedProtocolMessageType('DeliveryReport', (_message.Message,), dict(
    DESCRIPTOR = _GETDELIVERYORDERREPORTRESPONSE_DELIVERYREPORT,
    __module__ = 'receipt.delivery_pb2'
    # @@protoc_insertion_point(class_scope:delivery.GetDeliveryOrderReportResponse.DeliveryReport)
    ))
  ,
  DESCRIPTOR = _GETDELIVERYORDERREPORTRESPONSE,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.GetDeliveryOrderReportResponse)
  ))
_sym_db.RegisterMessage(GetDeliveryOrderReportResponse)
_sym_db.RegisterMessage(GetDeliveryOrderReportResponse.DeliveryReport)

GetDeliveryOrderReportTotalRequest = _reflection.GeneratedProtocolMessageType('GetDeliveryOrderReportTotalRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDELIVERYORDERREPORTTOTALREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.GetDeliveryOrderReportTotalRequest)
  ))
_sym_db.RegisterMessage(GetDeliveryOrderReportTotalRequest)

GetDeliveryOrderReportTotalResponse = _reflection.GeneratedProtocolMessageType('GetDeliveryOrderReportTotalResponse', (_message.Message,), dict(

  DeliveryReportTotal = _reflection.GeneratedProtocolMessageType('DeliveryReportTotal', (_message.Message,), dict(
    DESCRIPTOR = _GETDELIVERYORDERREPORTTOTALRESPONSE_DELIVERYREPORTTOTAL,
    __module__ = 'receipt.delivery_pb2'
    # @@protoc_insertion_point(class_scope:delivery.GetDeliveryOrderReportTotalResponse.DeliveryReportTotal)
    ))
  ,
  DESCRIPTOR = _GETDELIVERYORDERREPORTTOTALRESPONSE,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.GetDeliveryOrderReportTotalResponse)
  ))
_sym_db.RegisterMessage(GetDeliveryOrderReportTotalResponse)
_sym_db.RegisterMessage(GetDeliveryOrderReportTotalResponse.DeliveryReportTotal)

DealDeliveryByCodeRequest = _reflection.GeneratedProtocolMessageType('DealDeliveryByCodeRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALDELIVERYBYCODEREQUEST,
  __module__ = 'receipt.delivery_pb2'
  # @@protoc_insertion_point(class_scope:delivery.DealDeliveryByCodeRequest)
  ))
_sym_db.RegisterMessage(DealDeliveryByCodeRequest)



_DELIVERYSERVICE = _descriptor.ServiceDescriptor(
  name='DeliveryService',
  full_name='delivery.DeliveryService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=8428,
  serialized_end=10027,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateDelivery',
    full_name='delivery.DeliveryService.CreateDelivery',
    index=0,
    containing_service=None,
    input_type=_CREATEDELIVERYREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002\035\"\030/api/v2/receipt/delivery:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateDelivery',
    full_name='delivery.DeliveryService.UpdateDelivery',
    index=1,
    containing_service=None,
    input_type=_UPDATEDELIVERYREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/receipt/delivery/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BulkUpdateDelivery',
    full_name='delivery.DeliveryService.BulkUpdateDelivery',
    index=2,
    containing_service=None,
    input_type=_BULKUPDATEDELIVERYREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v2/receipt/delivery/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListDelivery',
    full_name='delivery.DeliveryService.ListDelivery',
    index=3,
    containing_service=None,
    input_type=_LISTDELIVERYREQUEST,
    output_type=_LISTDELIVERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002\032\"\030/api/v2/receipt/delivery'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDeliveryById',
    full_name='delivery.DeliveryService.GetDeliveryById',
    index=4,
    containing_service=None,
    input_type=_GETDELIVERYBYIDREQUEST,
    output_type=_DELIVERY,
    serialized_options=_b('\202\323\344\223\002\037\022\035/api/v2/receipt/delivery/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDeliveryProductById',
    full_name='delivery.DeliveryService.GetDeliveryProductById',
    index=5,
    containing_service=None,
    input_type=_GETDELIVERYPRODUCTBYIDREQUEST,
    output_type=_GETDELIVERYPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/receipt/delivery/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='DealDeliveryById',
    full_name='delivery.DeliveryService.DealDeliveryById',
    index=6,
    containing_service=None,
    input_type=_DEALDELIVERYBYIDREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002+\032&/api/v2/receipt/delivery/{id}/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BulkDealDeliverys',
    full_name='delivery.DeliveryService.BulkDealDeliverys',
    index=7,
    containing_service=None,
    input_type=_BULKDEALDELIVERYSREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002&\032!/api/v2/receipt/delivery/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDeliveryOrderReport',
    full_name='delivery.DeliveryService.GetDeliveryOrderReport',
    index=8,
    containing_service=None,
    input_type=_GETDELIVERYORDERREPORTREQUEST,
    output_type=_GETDELIVERYORDERREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/receipt/delivery/order/report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDeliveryOrderReportTotal',
    full_name='delivery.DeliveryService.GetDeliveryOrderReportTotal',
    index=9,
    containing_service=None,
    input_type=_GETDELIVERYORDERREPORTTOTALREQUEST,
    output_type=_GETDELIVERYORDERREPORTTOTALRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/receipt/delivery/order/report/total'),
  ),
  _descriptor.MethodDescriptor(
    name='ListDeliveryDetails',
    full_name='delivery.DeliveryService.ListDeliveryDetails',
    index=10,
    containing_service=None,
    input_type=_DELIVERYDETAILSREQUEST,
    output_type=_LISTDELIVERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\" /api/v2/receipt/delivery/details'),
  ),
  _descriptor.MethodDescriptor(
    name='DealDeliveryByCode',
    full_name='delivery.DeliveryService.DealDeliveryByCode',
    index=11,
    containing_service=None,
    input_type=_DEALDELIVERYBYCODEREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002-\032(/api/v2/receipt/vendor/delivery/{action}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_DELIVERYSERVICE)

DESCRIPTOR.services_by_name['DeliveryService'] = _DELIVERYSERVICE

# @@protoc_insertion_point(module_scope)
