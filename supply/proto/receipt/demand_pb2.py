# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: receipt/demand.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='receipt/demand.proto',
  package='demand',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x14receipt/demand.proto\x12\x06\x64\x65mand\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"3\n\x08Response\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\"\x85\x07\n\x06\x44\x65mand\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x12\n\nbatch_type\x18\x05 \x01(\t\x12\x10\n\x08order_id\x18\x06 \x01(\x04\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0b\x64\x65livery_id\x18\x08 \x01(\x04\x12\x15\n\rdelivery_code\x18\t \x01(\t\x12\x12\n\nreceive_by\x18\n \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\x0b \x01(\x04\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x16\n\x0eprocess_status\x18\r \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cstorage_type\x18\x12 \x01(\t\x12\x12\n\ndistr_type\x18\x13 \x01(\t\x12\x16\n\x0ethird_party_id\x18\x14 \x01(\x04\x12\x18\n\x10third_party_type\x18\x15 \x01(\t\x12\x14\n\x0cproduct_nums\x18\x16 \x01(\x04\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x03\x12\x12\n\nupdated_by\x18\x1a \x01(\x03\x12\x12\n\npartner_id\x18\x1b \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1c \x01(\t\x12\x14\n\x0cupdated_name\x18\x1d \x01(\t\x12\x18\n\x10main_branch_type\x18\x1e \x01(\t\x12\x18\n\x10sub_account_type\x18\x1f \x01(\t\x12\x16\n\x0esub_receive_by\x18  \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18! \x01(\x04\x12\x0e\n\x06remark\x18\" \x01(\t\x12\x0e\n\x06reason\x18# \x01(\t\"\xde\x05\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x11\n\tdemand_id\x18\x02 \x01(\x04\x12\x12\n\nreceive_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x14\n\x0cstorage_type\x18\x07 \x01(\t\x12\x16\n\x0eorder_quantity\x18\x08 \x01(\x01\x12\x19\n\x11\x64\x65livery_quantity\x18\t \x01(\x01\x12\x15\n\rpick_quantity\x18\n \x01(\x01\x12\x14\n\x0cis_confirmed\x18\x0b \x01(\x08\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\r \x01(\x04\x12.\n\nupdated_at\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x0f \x01(\x04\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x11 \x01(\t\x12\x14\n\x0cupdated_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x11\n\tunit_spec\x18\x15 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x16 \x01(\x04\x12\x15\n\rcategory_name\x18\x17 \x01(\t\x12\x15\n\rcategory_code\x18\x18 \x01(\t\x12\x12\n\ncost_price\x18\x19 \x01(\x01\x12\x11\n\ttax_price\x18\x1a \x01(\x01\x12\x10\n\x08tax_rate\x18\x1b \x01(\x01\x12\x11\n\tunit_rate\x18\x1c \x01(\x01\x12\x11\n\tsale_type\x18\x1d \x01(\t\x12\x18\n\x10sub_account_type\x18\x1e \x01(\t\x12\x16\n\x0esub_receive_by\x18\x1f \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18  \x01(\x04\"\xc3\x04\n\x13\x43reateDemandRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x12\n\nbatch_code\x18\x02 \x01(\t\x12\x12\n\nbatch_type\x18\x03 \x01(\t\x12\x10\n\x08order_id\x18\x04 \x01(\x04\x12\x12\n\norder_code\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65livery_id\x18\x06 \x01(\x04\x12\x15\n\rdelivery_code\x18\x07 \x01(\t\x12\x12\n\nreceive_by\x18\x08 \x01(\x04\x12\x13\n\x0b\x64\x65livery_by\x18\t \x01(\x04\x12\x14\n\x0cstorage_type\x18\n \x01(\t\x12\x12\n\ndistr_type\x18\x0b \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rdelivery_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x65xpect_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12!\n\x08products\x18\x10 \x03(\x0b\x32\x0f.demand.Product\x12\x18\n\x10main_branch_type\x18\x11 \x01(\t\x12\x18\n\x10sub_account_type\x18\x12 \x01(\t\x12\x16\n\x0esub_receive_by\x18\x13 \x01(\x04\x12\x17\n\x0fsub_delivery_by\x18\x14 \x01(\x04\"k\n\x13UpdateDemandRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x16\n\x0eprocess_status\x18\x03 \x01(\t\x12 \n\x07product\x18\x04 \x03(\x0b\x32\x0f.demand.Product\"\x95\x04\n\x11ListDemandRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x64\x65livery_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x15\n\rdelivery_code\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0breceive_bys\x18\x08 \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\t \x03(\x04\x12\x0e\n\x06status\x18\n \x03(\t\x12\x16\n\x0eprocess_status\x18\x0b \x03(\t\x12.\n\nstart_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ndistr_type\x18\x0e \x01(\t\x12\x12\n\nbatch_type\x18\x0f \x03(\t\x12\r\n\x05limit\x18\x10 \x01(\x05\x12\x0e\n\x06offset\x18\x11 \x01(\x05\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\r\n\x05order\x18\x13 \x01(\t\x12\x18\n\x10main_branch_type\x18\x14 \x01(\t\x12\x18\n\x10sub_account_type\x18\x15 \x01(\t\x12\x17\n\x0fsub_receive_bys\x18\x16 \x03(\x04\x12\x18\n\x10sub_delivery_bys\x18\x17 \x03(\x04\"A\n\x12ListDemandResponse\x12\x1c\n\x04rows\x18\x01 \x03(\x0b\x32\x0e.demand.Demand\x12\r\n\x05total\x18\x02 \x01(\x04\"\"\n\x14GetDemandByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"7\n\x15GetDemandByIdResponse\x12\x1e\n\x06\x64\x65mand\x18\x01 \x01(\x0b\x32\x0e.demand.Demand\"|\n\x1bGetDemandProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\"L\n\x1cGetDemandProductByIdResponse\x12\x1d\n\x04rows\x18\x01 \x03(\x0b\x32\x0f.demand.Product\x12\r\n\x05total\x18\x02 \x01(\x04\x32\xb9\x04\n\rDemandService\x12`\n\x0c\x43reateDemand\x12\x1b.demand.CreateDemandRequest\x1a\x10.demand.Response\"!\x82\xd3\xe4\x93\x02\x1b\"\x16/api/v2/receipt/demand:\x01*\x12l\n\x0cUpdateDemand\x12\x1b.demand.UpdateDemandRequest\x1a\x10.demand.Response\"-\x82\xd3\xe4\x93\x02\'\x1a\"/api/v2/receipt/demand/{id}/update:\x01*\x12\x63\n\nListDemand\x12\x19.demand.ListDemandRequest\x1a\x1a.demand.ListDemandResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v2/receipt/demand\x12\x62\n\rGetDemandById\x12\x1c.demand.GetDemandByIdRequest\x1a\x0e.demand.Demand\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/api/v2/receipt/demand/{id}\x12\x8e\x01\n\x14GetDemandProductById\x12#.demand.GetDemandProductByIdRequest\x1a$.demand.GetDemandProductByIdResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/receipt/demand/{id}/productb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='demand.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='demand.Response.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='demand.Response.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='demand.Response.msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=95,
  serialized_end=146,
)


_DEMAND = _descriptor.Descriptor(
  name='Demand',
  full_name='demand.Demand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='demand.Demand.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='demand.Demand.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='demand.Demand.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='demand.Demand.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='demand.Demand.batch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='demand.Demand.order_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='demand.Demand.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='demand.Demand.delivery_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='demand.Demand.delivery_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='demand.Demand.receive_by', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='demand.Demand.delivery_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='demand.Demand.status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='demand.Demand.process_status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='demand.Demand.demand_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='demand.Demand.delivery_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='demand.Demand.expect_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='demand.Demand.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='demand.Demand.storage_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='demand.Demand.distr_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='third_party_id', full_name='demand.Demand.third_party_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='third_party_type', full_name='demand.Demand.third_party_type', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='demand.Demand.product_nums', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='demand.Demand.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='demand.Demand.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='demand.Demand.created_by', index=24,
      number=25, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='demand.Demand.updated_by', index=25,
      number=26, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='demand.Demand.partner_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='demand.Demand.created_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='demand.Demand.updated_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='demand.Demand.main_branch_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='demand.Demand.sub_account_type', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='demand.Demand.sub_receive_by', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='demand.Demand.sub_delivery_by', index=32,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='demand.Demand.remark', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='demand.Demand.reason', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=149,
  serialized_end=1050,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='demand.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='demand.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='demand.Product.demand_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='demand.Product.receive_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='demand.Product.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='demand.Product.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='demand.Product.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='demand.Product.storage_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_quantity', full_name='demand.Product.order_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_quantity', full_name='demand.Product.delivery_quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pick_quantity', full_name='demand.Product.pick_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='demand.Product.is_confirmed', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='demand.Product.created_at', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='demand.Product.created_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='demand.Product.updated_at', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='demand.Product.updated_by', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='demand.Product.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='demand.Product.created_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='demand.Product.updated_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='demand.Product.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='demand.Product.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='demand.Product.unit_spec', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='demand.Product.category_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='demand.Product.category_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='demand.Product.category_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='demand.Product.cost_price', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='demand.Product.tax_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='demand.Product.tax_rate', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='demand.Product.unit_rate', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='demand.Product.sale_type', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='demand.Product.sub_account_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='demand.Product.sub_receive_by', index=30,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='demand.Product.sub_delivery_by', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1053,
  serialized_end=1787,
)


_CREATEDEMANDREQUEST = _descriptor.Descriptor(
  name='CreateDemandRequest',
  full_name='demand.CreateDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='demand.CreateDemandRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='demand.CreateDemandRequest.batch_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='demand.CreateDemandRequest.batch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='demand.CreateDemandRequest.order_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='demand.CreateDemandRequest.order_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='demand.CreateDemandRequest.delivery_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='demand.CreateDemandRequest.delivery_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='demand.CreateDemandRequest.receive_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='demand.CreateDemandRequest.delivery_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='demand.CreateDemandRequest.storage_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='demand.CreateDemandRequest.distr_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='demand.CreateDemandRequest.demand_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='demand.CreateDemandRequest.delivery_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='demand.CreateDemandRequest.expect_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='demand.CreateDemandRequest.arrival_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='demand.CreateDemandRequest.products', index=15,
      number=16, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='demand.CreateDemandRequest.main_branch_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='demand.CreateDemandRequest.sub_account_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='demand.CreateDemandRequest.sub_receive_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_by', full_name='demand.CreateDemandRequest.sub_delivery_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1790,
  serialized_end=2369,
)


_UPDATEDEMANDREQUEST = _descriptor.Descriptor(
  name='UpdateDemandRequest',
  full_name='demand.UpdateDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='demand.UpdateDemandRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='demand.UpdateDemandRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='demand.UpdateDemandRequest.process_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='demand.UpdateDemandRequest.product', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2371,
  serialized_end=2478,
)


_LISTDEMANDREQUEST = _descriptor.Descriptor(
  name='ListDemandRequest',
  full_name='demand.ListDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='demand.ListDemandRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='demand.ListDemandRequest.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='demand.ListDemandRequest.delivery_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='demand.ListDemandRequest.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='demand.ListDemandRequest.delivery_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='demand.ListDemandRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='demand.ListDemandRequest.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='demand.ListDemandRequest.receive_bys', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='demand.ListDemandRequest.delivery_bys', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='demand.ListDemandRequest.status', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='demand.ListDemandRequest.process_status', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='demand.ListDemandRequest.start_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='demand.ListDemandRequest.end_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='demand.ListDemandRequest.distr_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='demand.ListDemandRequest.batch_type', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='demand.ListDemandRequest.limit', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='demand.ListDemandRequest.offset', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='demand.ListDemandRequest.sort', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='demand.ListDemandRequest.order', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='demand.ListDemandRequest.main_branch_type', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='demand.ListDemandRequest.sub_account_type', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_bys', full_name='demand.ListDemandRequest.sub_receive_bys', index=21,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_bys', full_name='demand.ListDemandRequest.sub_delivery_bys', index=22,
      number=23, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2481,
  serialized_end=3014,
)


_LISTDEMANDRESPONSE = _descriptor.Descriptor(
  name='ListDemandResponse',
  full_name='demand.ListDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='demand.ListDemandResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='demand.ListDemandResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3016,
  serialized_end=3081,
)


_GETDEMANDBYIDREQUEST = _descriptor.Descriptor(
  name='GetDemandByIdRequest',
  full_name='demand.GetDemandByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='demand.GetDemandByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3083,
  serialized_end=3117,
)


_GETDEMANDBYIDRESPONSE = _descriptor.Descriptor(
  name='GetDemandByIdResponse',
  full_name='demand.GetDemandByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand', full_name='demand.GetDemandByIdResponse.demand', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3119,
  serialized_end=3174,
)


_GETDEMANDPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetDemandProductByIdRequest',
  full_name='demand.GetDemandProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='demand.GetDemandProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='demand.GetDemandProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='demand.GetDemandProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='demand.GetDemandProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='demand.GetDemandProductByIdRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='demand.GetDemandProductByIdRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3176,
  serialized_end=3300,
)


_GETDEMANDPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetDemandProductByIdResponse',
  full_name='demand.GetDemandProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='demand.GetDemandProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='demand.GetDemandProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3302,
  serialized_end=3378,
)

_DEMAND.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDREQUEST.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDREQUEST.fields_by_name['expect_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATEDEMANDREQUEST.fields_by_name['product'].message_type = _PRODUCT
_LISTDEMANDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDEMANDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDEMANDRESPONSE.fields_by_name['rows'].message_type = _DEMAND
_GETDEMANDBYIDRESPONSE.fields_by_name['demand'].message_type = _DEMAND
_GETDEMANDPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['Demand'] = _DEMAND
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['CreateDemandRequest'] = _CREATEDEMANDREQUEST
DESCRIPTOR.message_types_by_name['UpdateDemandRequest'] = _UPDATEDEMANDREQUEST
DESCRIPTOR.message_types_by_name['ListDemandRequest'] = _LISTDEMANDREQUEST
DESCRIPTOR.message_types_by_name['ListDemandResponse'] = _LISTDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['GetDemandByIdRequest'] = _GETDEMANDBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetDemandByIdResponse'] = _GETDEMANDBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetDemandProductByIdRequest'] = _GETDEMANDPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetDemandProductByIdResponse'] = _GETDEMANDPRODUCTBYIDRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.Response)
  ))
_sym_db.RegisterMessage(Response)

Demand = _reflection.GeneratedProtocolMessageType('Demand', (_message.Message,), dict(
  DESCRIPTOR = _DEMAND,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.Demand)
  ))
_sym_db.RegisterMessage(Demand)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.Product)
  ))
_sym_db.RegisterMessage(Product)

CreateDemandRequest = _reflection.GeneratedProtocolMessageType('CreateDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDEMANDREQUEST,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.CreateDemandRequest)
  ))
_sym_db.RegisterMessage(CreateDemandRequest)

UpdateDemandRequest = _reflection.GeneratedProtocolMessageType('UpdateDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEDEMANDREQUEST,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.UpdateDemandRequest)
  ))
_sym_db.RegisterMessage(UpdateDemandRequest)

ListDemandRequest = _reflection.GeneratedProtocolMessageType('ListDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTDEMANDREQUEST,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.ListDemandRequest)
  ))
_sym_db.RegisterMessage(ListDemandRequest)

ListDemandResponse = _reflection.GeneratedProtocolMessageType('ListDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTDEMANDRESPONSE,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.ListDemandResponse)
  ))
_sym_db.RegisterMessage(ListDemandResponse)

GetDemandByIdRequest = _reflection.GeneratedProtocolMessageType('GetDemandByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDBYIDREQUEST,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.GetDemandByIdRequest)
  ))
_sym_db.RegisterMessage(GetDemandByIdRequest)

GetDemandByIdResponse = _reflection.GeneratedProtocolMessageType('GetDemandByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDBYIDRESPONSE,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.GetDemandByIdResponse)
  ))
_sym_db.RegisterMessage(GetDemandByIdResponse)

GetDemandProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetDemandProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDPRODUCTBYIDREQUEST,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.GetDemandProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetDemandProductByIdRequest)

GetDemandProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetDemandProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDPRODUCTBYIDRESPONSE,
  __module__ = 'receipt.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.GetDemandProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetDemandProductByIdResponse)



_DEMANDSERVICE = _descriptor.ServiceDescriptor(
  name='DemandService',
  full_name='demand.DemandService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3381,
  serialized_end=3950,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateDemand',
    full_name='demand.DemandService.CreateDemand',
    index=0,
    containing_service=None,
    input_type=_CREATEDEMANDREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002\033\"\026/api/v2/receipt/demand:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateDemand',
    full_name='demand.DemandService.UpdateDemand',
    index=1,
    containing_service=None,
    input_type=_UPDATEDEMANDREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\032\"/api/v2/receipt/demand/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListDemand',
    full_name='demand.DemandService.ListDemand',
    index=2,
    containing_service=None,
    input_type=_LISTDEMANDREQUEST,
    output_type=_LISTDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002\030\022\026/api/v2/receipt/demand'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandById',
    full_name='demand.DemandService.GetDemandById',
    index=3,
    containing_service=None,
    input_type=_GETDEMANDBYIDREQUEST,
    output_type=_DEMAND,
    serialized_options=_b('\202\323\344\223\002\035\022\033/api/v2/receipt/demand/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandProductById',
    full_name='demand.DemandService.GetDemandProductById',
    index=4,
    containing_service=None,
    input_type=_GETDEMANDPRODUCTBYIDREQUEST,
    output_type=_GETDEMANDPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/receipt/demand/{id}/product'),
  ),
])
_sym_db.RegisterServiceDescriptor(_DEMANDSERVICE)

DESCRIPTOR.services_by_name['DemandService'] = _DEMANDSERVICE

# @@protoc_insertion_point(module_scope)
