# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: receipt/operation_log.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='receipt/operation_log.proto',
  package='operation_log',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1breceipt/operation_log.proto\x12\roperation_log\x1a\x1cgoogle/api/annotations.proto\"\xe5\x01\n\x15\x43reateFlowLogsRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x10\n\x08\x64oc_type\x18\x02 \x01(\t\x12\x12\n\ndoc_status\x18\x03 \x01(\t\x12\x14\n\x0csub_doc_type\x18\x04 \x01(\t\x12\x11\n\tbranch_id\x18\x05 \x01(\x04\x12\x15\n\rsub_branch_id\x18\x06 \x01(\x04\x12\x11\n\toperation\x18\x07 \x01(\t\x12\x1b\n\x13posterior_operation\x18\x08 \x01(\t\x12\x16\n\x0eprocess_status\x18\t \x01(\t\x12\x0e\n\x06remark\x18\n \x01(\t\")\n\x16\x43reateFlowLogsResponse\x12\x0f\n\x07\x66low_id\x18\x01 \x01(\x04\x32\x9f\x01\n\x13OperationLogService\x12\x87\x01\n\x0e\x43reateFlowLogs\x12$.operation_log.CreateFlowLogsRequest\x1a%.operation_log.CreateFlowLogsResponse\"(\x82\xd3\xe4\x93\x02\"\"\x1d/api/v2/receipt/operation/log:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,])




_CREATEFLOWLOGSREQUEST = _descriptor.Descriptor(
  name='CreateFlowLogsRequest',
  full_name='operation_log.CreateFlowLogsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='operation_log.CreateFlowLogsRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_type', full_name='operation_log.CreateFlowLogsRequest.doc_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_status', full_name='operation_log.CreateFlowLogsRequest.doc_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_doc_type', full_name='operation_log.CreateFlowLogsRequest.sub_doc_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='operation_log.CreateFlowLogsRequest.branch_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_branch_id', full_name='operation_log.CreateFlowLogsRequest.sub_branch_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation', full_name='operation_log.CreateFlowLogsRequest.operation', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='posterior_operation', full_name='operation_log.CreateFlowLogsRequest.posterior_operation', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='operation_log.CreateFlowLogsRequest.process_status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='operation_log.CreateFlowLogsRequest.remark', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=77,
  serialized_end=306,
)


_CREATEFLOWLOGSRESPONSE = _descriptor.Descriptor(
  name='CreateFlowLogsResponse',
  full_name='operation_log.CreateFlowLogsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='flow_id', full_name='operation_log.CreateFlowLogsResponse.flow_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=308,
  serialized_end=349,
)

DESCRIPTOR.message_types_by_name['CreateFlowLogsRequest'] = _CREATEFLOWLOGSREQUEST
DESCRIPTOR.message_types_by_name['CreateFlowLogsResponse'] = _CREATEFLOWLOGSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateFlowLogsRequest = _reflection.GeneratedProtocolMessageType('CreateFlowLogsRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEFLOWLOGSREQUEST,
  __module__ = 'receipt.operation_log_pb2'
  # @@protoc_insertion_point(class_scope:operation_log.CreateFlowLogsRequest)
  ))
_sym_db.RegisterMessage(CreateFlowLogsRequest)

CreateFlowLogsResponse = _reflection.GeneratedProtocolMessageType('CreateFlowLogsResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEFLOWLOGSRESPONSE,
  __module__ = 'receipt.operation_log_pb2'
  # @@protoc_insertion_point(class_scope:operation_log.CreateFlowLogsResponse)
  ))
_sym_db.RegisterMessage(CreateFlowLogsResponse)



_OPERATIONLOGSERVICE = _descriptor.ServiceDescriptor(
  name='OperationLogService',
  full_name='operation_log.OperationLogService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=352,
  serialized_end=511,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateFlowLogs',
    full_name='operation_log.OperationLogService.CreateFlowLogs',
    index=0,
    containing_service=None,
    input_type=_CREATEFLOWLOGSREQUEST,
    output_type=_CREATEFLOWLOGSRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\"\035/api/v2/receipt/operation/log:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_OPERATIONLOGSERVICE)

DESCRIPTOR.services_by_name['OperationLogService'] = _OPERATIONLOGSERVICE

# @@protoc_insertion_point(module_scope)
