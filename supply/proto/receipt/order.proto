syntax = "proto3";
package order;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// OrderService 订货相关服务
service OrderService{
    
    // 创建订货单
    rpc CreateOrder(CreateOrderRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/order"
            body:"*"
        };
    }

    // 更新订货单
    rpc UpdateOrder(UpdateOrderRequest) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/receipt/order/{id}/update"
            body:"*"
        };
    }

    // 查询订货单
    rpc ListOrder(ListOrderRequest) returns (ListOrderResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/order"
        };
    }

    // 根据id查询订货单详情
    rpc GetOrderById(GetOrderByIdRequest) returns (Order){
        option (google.api.http) = {
            get:"/api/v2/receipt/order/{id}"
        };
    }

    // 根据Order_id查商品详情
    rpc GetOrderProductById (GetOrderProductByIdRequest) returns (GetOrderProductByIdResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/order/{id}/product"
        };
    }

    // 要货单报表
    rpc QueryBiOrderReport (QueryOrderBiReportRequest) returns (QueryOrderBiReportResponse) {
        option (google.api.http) = {
        get: "/api/v2/receipt/bi/order/report"
        };
    }

    // 查询订货单
    rpc ListOrderByBatchIds(ListOrderByBatchIdsRequest) returns (ListOrderResponse){
        option (google.api.http) = {
            get:"/api/v2/receipt/order"
        };
    }

}

message Response{
    uint64 id = 1;
    string status = 2;
    string msg = 3;
}

message Order{
    // 订货单id
    uint64 id = 1;
    // 订货单单号
    string code = 2;
    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型
    string batch_type = 5;

    // 订货单id
    uint64 order_id = 6;
    // 订货单单号
    string order_code = 7;
    // 收货单id
    uint64 receive_id = 8;
    // 收货单单号
    string receive_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 单据状态
    string status = 12;
    // 业务状态
    string process_status = 13;

    // 订货日期
    google.protobuf.Timestamp demand_date = 14;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 15;
    // 期望日期
    google.protobuf.Timestamp expect_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;

    // 储藏方式
    string storage_type = 18;
    // 物流类型
    string distr_type = 19;
    // 订单类型
    string demand_type = 20;

    //
    string third_party_id = 21;
    string third_party_type = 22;
    // 商品数量
    uint64 product_nums = 23;

    // 创建时间
    google.protobuf.Timestamp created_at = 24;
    // 更新时间
    google.protobuf.Timestamp updated_at = 25;
    // 创建人id
    int64 created_by = 26;
    // 更新人id
    int64 updated_by = 27;
    // 商户id
    uint64 partner_id = 28;
    // 创建人
    string created_name = 29;
    // 更新人
    string updated_name = 30;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商, FS-加盟店
    // 订货单receive_by是main_branch
    string main_branch_type = 31;
    // 拆单编号
    string separate_no = 32;
    // 成本中心
    uint64 cost_center_id = 33;
    // 子账户类型
    string sub_account_type = 34;
    // 子账户接收方
    uint64 sub_receive_by = 35;
    // 子账户发送方
    uint64 sub_delivery_by = 36;
    // 备注
    string remark = 37;
    // 原因
    string reason = 38;
    // 加盟商id
    uint64 franchisee_id = 39;
    // 发货单id
    uint64 delivery_id = 50;
    // 发货单单号
    string delivery_code = 51;
    // 订货单备注
    string source_remark = 55;
    // 订货类型
    uint64 order_type_id = 56;
    string tp_code = 57;
    string bus_type = 58;


}

message Product{
    // id
    uint64 id = 1;
    // 订货单id
    uint64 order_id = 2;
    // 收货门店
    uint64 order_by = 3;
    // 商品id
    uint64 product_id = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6;
    //
    string storage_type = 7;

    // 订货数量
    double order_quantity = 8;
    // 收货数量
    double delivery_quantity = 9;
    // 收货数量
    double receive_quantity = 10;

    // 收货状态
    bool is_confirmed = 11;

    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 创建人id
    uint64 created_by= 13;
    // 更新日期
    google.protobuf.Timestamp updated_at = 14;
    // 更新人id
    uint64 updated_by = 15;
    // 商户id
    uint64 partner_id = 16;
    // 创建人
    string created_name = 17;
    // 更新人
    string updated_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 单位规格
    string unit_spec = 21;
    // 商品分类
    uint64 category_id = 22;
    // 商品分类名称
    string category_name = 23;
    // 商品分类编号
    string category_code = 24;
    // 成本价
    double cost_price = 25;
    // 含税价
    double tax_price = 26;
    // 税率
    double tax_rate = 27;
    // 单位换算比例
    double unit_rate = 28;
    // 销售类型
    string sale_type = 29;

}

message CreateOrderRequest{

    // id
    uint64 id = 1;
    // code
    string code = 2;

    // 主单id
    uint64 batch_id = 3;
    // 主单单号
    string batch_code = 4;
    // 主单类型 FRS_DEMAND-加盟商
    string batch_type = 5;

    // 订单id
    uint64 order_id = 6;
    // 订单单号
    string order_code = 7;
    // 发货单id
    uint64 delivery_id = 8;
    // 发货单单号
    string delivery_code = 9;

    // 接收门店-门店id
    uint64 receive_by = 10;
    // 配送方
    uint64 delivery_by = 11;

    // 储藏方式
    string storage_type = 12;
    // 物流类型
    string distr_type = 13;
    // 订单类型
    string demand_type = 14;

    // 订货日期
    google.protobuf.Timestamp demand_date = 15;
    // 配送时间
    google.protobuf.Timestamp delivery_date = 16;
    // 期望日期
    google.protobuf.Timestamp expect_date = 17;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 18;

    repeated Product products = 19;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商, FS-加盟店
    // 订货单receive_by是main_branch
    string main_branch_type = 20;
    // 拆单编号
    string separate_no = 21;
    // 子账户类型
    string sub_account_type = 22;
    // 子账户接收方
    uint64 sub_receive_by = 23;
    // 子账户发送方
    uint64 sub_delivery_by = 24;
    // 备注
    string remark = 34;
    // 原因
    string reason = 35;
    // 加盟商id
    uint64 franchisee_id = 36;
    // 订货单备注
    string source_remark = 37;
    // 订货类型
    uint64 order_type_id = 38;
}

message UpdateOrderRequest{
    // 订货单id
    uint64 id = 1;
    //
    string status = 2;
    //
    string process_status = 3;
    // product
    repeated Product product= 4;
    // 订货单备注
    string remark = 5;
    // 订货单id
    uint64 batch_id = 6;
    // 来源备注
    string source_remark = 7;
}

message ListOrderRequest{
    // 订货单号
    uint64 order_id = 1;
    //
    uint64 batch_id = 2;
    //
    uint64 receive_id = 3;
    // 订货单号
    string batch_code = 4;
    // 收货单号
    string receive_code = 5;
    // 要货单号
    string code = 6;
    // 仓库要货单号
    string order_code = 7;
    // 接受方id
    repeated uint64 receive_bys = 8;
    // 发送方
    repeated uint64 delivery_bys = 9;
    // 订单状态
    repeated string status = 10;
    // 订单状态
    repeated string process_status = 11;
    // 配送开始日期
    google.protobuf.Timestamp demand_start_date = 12;
    // 配送结束日期
    google.protobuf.Timestamp demand_end_date = 13;
    // 物流类型
    string distr_type = 14;
    // 业务类型
    repeated string batch_type = 15;
    // 分页大小
    int32 limit = 16;
    // 跳过行数
    int32 offset = 17;
    // 排序字段
    string sort = 18;
    // 排序顺序
    string order = 19;
    // 储藏方式
    string storage_type = 20;
    // 订单类型
    string demand_type = 21;
    // 配送开始日期
    google.protobuf.Timestamp arrival_start_date = 22;
    // 配送结束日期
    google.protobuf.Timestamp arrival_end_date = 23;
    // 单据main_branch类型：S-门店，W-仓库，V-供应商
    // 订货单receive_by是main_branch
    string main_branch_type = 24;
    // 子账户类型
    string sub_account_type = 25;
    // 子账户接收方
    repeated uint64 sub_receive_bys = 26;
    // 子账户发送方
    repeated uint64 sub_delivery_bys = 27;
    // 商品ids
    repeated uint64 product_ids = 28;
}

message ListOrderByBatchIdsRequest{
    repeated uint64 batch_ids= 1;
}

message ListOrderResponse{
    repeated Order rows = 1;
    uint64 total = 2;
}

message GetOrderByIdRequest{
    // 订货单id(order_id)
    uint64 id = 1;

}

message GetOrderByIdResponse{
    // wrapper.Status status = 1;
    Order order = 1;
}

message GetOrderProductByIdRequest{
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
}

message GetOrderProductByIdResponse{
    // wrapper.Status status = 1;
    repeated Product rows = 1;
    uint64 total = 2;
}

// 要货单报表请求参数
message QueryOrderBiReportRequest {
    // 开始订货日期
    google.protobuf.Timestamp start_demand_date = 1;
    // 结束订货日期
    google.protobuf.Timestamp end_demand_date = 2;
    // 开始到货日期
    google.protobuf.Timestamp start_arrival_date = 3;
    // 结束到货日期
    google.protobuf.Timestamp end_arrival_date = 4;
    // 查询分类（汇总:sum, 明细:detail）
    string classification = 5;
    // 订单分类(门市订货单:SD, 门市紧急订货单:HD, 主配单:MD)
    string type = 6;
    // 商品id
    repeated uint64 product_ids = 7;
    // 商品类别
    repeated uint64 category_ids = 8;
    // 门店id
    repeated uint64 store_ids = 9;
    // 每页数量
    int32 limit = 10;
    // 偏移量
    int32 offset = 11;
    // 排序(默认asc)
    string order = 12;
    string sort = 13;
}
// 要货单报表请求返回
message QueryOrderBiReportResponse {
    message OrderBiReport {
        string code = 1;
        google.protobuf.Timestamp demand_date = 2;
        google.protobuf.Timestamp expect_date = 3;
        uint64 store_id = 4;
        string store_name = 5;
        string store_code = 6;
        uint64 product_id = 7;
        string product_code = 8;
        string product_name = 9;
        uint64 category_id = 10;
        float quantity = 11;
        uint64 unit_id = 12;
        string spec = 13;
        string unit_name = 14;
        uint64 accounting_unit_id = 15;
        string accounting_unit_name = 16;
        double accounting_quantity = 17;
        string product_second_code = 18;
    }
    repeated OrderBiReport rows = 1;
    int32 total = 2;
    string classification = 3;
    float sum_quantity = 4;
    float sum_accounting_quantity = 5;
}



