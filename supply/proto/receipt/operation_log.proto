syntax = "proto3";
package operation_log;
import "google/api/annotations.proto";

// 操作日志服务
service OperationLogService{
    // 创建一条工作流记录
    rpc CreateFlowLogs(CreateFlowLogsRequest) returns (CreateFlowLogsResponse){
        option (google.api.http) = {
            post:"/api/v2/receipt/operation/log"
            body:"*"
        };
    }
}


message CreateFlowLogsRequest {
    // 单据id
    uint64 doc_id = 1;
    // 单据类型(和业务配置中对应的业务的值一致）
    string doc_type = 2;
    // 单据状态
    string doc_status = 3;
    // 单据sub类型(标记该单据是门店层面收货还是仓位收货：position/store)
    string sub_doc_type= 4;
    // 组织(门店/仓库/加工中心)id
    uint64 branch_id = 5;
    // 仓位id
    uint64 sub_branch_id = 6;
    // 操作 目前默认传 INNER_TRANSFER
    string operation = 7;
    // 下一个操作 目前默认传 MATERIAL_CONVERT
    string posterior_operation = 8;
    // 操作状态 INITED/PROCESSING/COMPLETED
    string process_status = 9;
    // 备注
    string remark = 10;
}

message CreateFlowLogsResponse {
    uint64 flow_id = 1;
}