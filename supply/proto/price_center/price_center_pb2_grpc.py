# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from price_center import price_center_pb2 as price__center_dot_price__center__pb2


class PriceCenterServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Ping = channel.unary_unary(
        '/price_center.PriceCenterService/Ping',
        request_serializer=price__center_dot_price__center__pb2.Request.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.Response.FromString,
        )
    self.CreatePriceGroup = channel.unary_unary(
        '/price_center.PriceCenterService/CreatePriceGroup',
        request_serializer=price__center_dot_price__center__pb2.CreatePriceGroupRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.CreatePriceGroupResponse.FromString,
        )
    self.GetPriceGroupProductList = channel.unary_unary(
        '/price_center.PriceCenterService/GetPriceGroupProductList',
        request_serializer=price__center_dot_price__center__pb2.GetPriceGroupProductListRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetPriceGroupProductListResponse.FromString,
        )
    self.GetProductPriceInfo = channel.unary_unary(
        '/price_center.PriceCenterService/GetProductPriceInfo',
        request_serializer=price__center_dot_price__center__pb2.GetProductPriceInfoRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetProductPriceInfoResponse.FromString,
        )
    self.UpdatePriceGroup = channel.unary_unary(
        '/price_center.PriceCenterService/UpdatePriceGroup',
        request_serializer=price__center_dot_price__center__pb2.UpdatePriceGroupRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.CreatePriceGroupResponse.FromString,
        )
    self.GetPriceGroupTaskHeader = channel.unary_unary(
        '/price_center.PriceCenterService/GetPriceGroupTaskHeader',
        request_serializer=price__center_dot_price__center__pb2.GetPriceGroupTaskHeaderRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetPriceGroupTaskHeaderResponse.FromString,
        )
    self.GetPriceGroupTaskDetail = channel.unary_unary(
        '/price_center.PriceCenterService/GetPriceGroupTaskDetail',
        request_serializer=price__center_dot_price__center__pb2.GetPriceGroupTaskDetailRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetPriceGroupTaskDetailResponse.FromString,
        )
    self.GetPriceChangeLog = channel.unary_unary(
        '/price_center.PriceCenterService/GetPriceChangeLog',
        request_serializer=price__center_dot_price__center__pb2.GetPriceChangeLogRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetPriceChangeLogResponse.FromString,
        )
    self.GetProductPriceAgent = channel.unary_unary(
        '/price_center.PriceCenterService/GetProductPriceAgent',
        request_serializer=price__center_dot_price__center__pb2.GetProductPriceAgentRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetProductPriceAgentResponse.FromString,
        )
    self.DeletePriceGroup = channel.unary_unary(
        '/price_center.PriceCenterService/DeletePriceGroup',
        request_serializer=price__center_dot_price__center__pb2.DeletePriceGroupRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.Response.FromString,
        )
    self.PriceGroupDataCheck = channel.unary_unary(
        '/price_center.PriceCenterService/PriceGroupDataCheck',
        request_serializer=price__center_dot_price__center__pb2.PriceGroupDataCheckRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.PriceGroupDataCheckResponse.FromString,
        )
    self.GetUnsetPriceReport = channel.unary_unary(
        '/price_center.PriceCenterService/GetUnsetPriceReport',
        request_serializer=price__center_dot_price__center__pb2.GetUnsetPriceReportRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetUnsetPriceReportResponse.FromString,
        )
    self.SyncPriceChangeLog = channel.unary_unary(
        '/price_center.PriceCenterService/SyncPriceChangeLog',
        request_serializer=price__center_dot_price__center__pb2.syncPriceChangeLogRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.Response.FromString,
        )
    self.GetPriceGroupTag = channel.unary_unary(
        '/price_center.PriceCenterService/GetPriceGroupTag',
        request_serializer=price__center_dot_price__center__pb2.GetPriceGroupTagRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetPriceGroupTagResponse.FromString,
        )
    self.DeletePriceGroupTag = channel.unary_unary(
        '/price_center.PriceCenterService/DeletePriceGroupTag',
        request_serializer=price__center_dot_price__center__pb2.DeletePriceGroupRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.Response.FromString,
        )
    self.GetPgpTagCategory = channel.unary_unary(
        '/price_center.PriceCenterService/GetPgpTagCategory',
        request_serializer=price__center_dot_price__center__pb2.GetPgpTagCategoryRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.GetPgpTagCategoryResponse.FromString,
        )
    self.DeletePgpTagCategory = channel.unary_unary(
        '/price_center.PriceCenterService/DeletePgpTagCategory',
        request_serializer=price__center_dot_price__center__pb2.DeletePriceGroupRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.Response.FromString,
        )
    self.CreatePriceGroupProductTag = channel.unary_unary(
        '/price_center.PriceCenterService/CreatePriceGroupProductTag',
        request_serializer=price__center_dot_price__center__pb2.CreatePriceGroupProductTagRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.CreatePriceGroupProductTagResponse.FromString,
        )
    self.UpdatePriceGroupProductTag = channel.unary_unary(
        '/price_center.PriceCenterService/UpdatePriceGroupProductTag',
        request_serializer=price__center_dot_price__center__pb2.CreatePriceGroupProductTagRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.CreatePriceGroupProductTagResponse.FromString,
        )
    self.DeletePriceGroupProductTag = channel.unary_unary(
        '/price_center.PriceCenterService/DeletePriceGroupProductTag',
        request_serializer=price__center_dot_price__center__pb2.DeletePriceGroupProductRequest.SerializeToString,
        response_deserializer=price__center_dot_price__center__pb2.Response.FromString,
        )


class PriceCenterServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Ping(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreatePriceGroup(self, request, context):
    """创建价格组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceGroupProductList(self, request, context):
    """查询价格组商品明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductPriceInfo(self, request, context):
    """价格中心查询商品价格信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdatePriceGroup(self, request, context):
    """更新价格组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceGroupTaskHeader(self, request, context):
    """查询计划header
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceGroupTaskDetail(self, request, context):
    """查询计划明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceChangeLog(self, request, context):
    """改价记录-查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductPriceAgent(self, request, context):
    """商品价格查询(其他业务调用)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeletePriceGroup(self, request, context):
    """删除价格组
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def PriceGroupDataCheck(self, request, context):
    """创建/新建check priceGroup
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetUnsetPriceReport(self, request, context):
    """未定价商品分页
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SyncPriceChangeLog(self, request, context):
    """同步改价记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceGroupTag(self, request, context):
    """查询价格组标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeletePriceGroupTag(self, request, context):
    """删除价格组标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPgpTagCategory(self, request, context):
    """查询价格组商品标签类别
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeletePgpTagCategory(self, request, context):
    """删除价格组商品标签类别
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreatePriceGroupProductTag(self, request, context):
    """创建价格组商品标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdatePriceGroupProductTag(self, request, context):
    """更新价格组商品标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeletePriceGroupProductTag(self, request, context):
    """删除价格组商品标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_PriceCenterServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Ping': grpc.unary_unary_rpc_method_handler(
          servicer.Ping,
          request_deserializer=price__center_dot_price__center__pb2.Request.FromString,
          response_serializer=price__center_dot_price__center__pb2.Response.SerializeToString,
      ),
      'CreatePriceGroup': grpc.unary_unary_rpc_method_handler(
          servicer.CreatePriceGroup,
          request_deserializer=price__center_dot_price__center__pb2.CreatePriceGroupRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.CreatePriceGroupResponse.SerializeToString,
      ),
      'GetPriceGroupProductList': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceGroupProductList,
          request_deserializer=price__center_dot_price__center__pb2.GetPriceGroupProductListRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetPriceGroupProductListResponse.SerializeToString,
      ),
      'GetProductPriceInfo': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductPriceInfo,
          request_deserializer=price__center_dot_price__center__pb2.GetProductPriceInfoRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetProductPriceInfoResponse.SerializeToString,
      ),
      'UpdatePriceGroup': grpc.unary_unary_rpc_method_handler(
          servicer.UpdatePriceGroup,
          request_deserializer=price__center_dot_price__center__pb2.UpdatePriceGroupRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.CreatePriceGroupResponse.SerializeToString,
      ),
      'GetPriceGroupTaskHeader': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceGroupTaskHeader,
          request_deserializer=price__center_dot_price__center__pb2.GetPriceGroupTaskHeaderRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetPriceGroupTaskHeaderResponse.SerializeToString,
      ),
      'GetPriceGroupTaskDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceGroupTaskDetail,
          request_deserializer=price__center_dot_price__center__pb2.GetPriceGroupTaskDetailRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetPriceGroupTaskDetailResponse.SerializeToString,
      ),
      'GetPriceChangeLog': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceChangeLog,
          request_deserializer=price__center_dot_price__center__pb2.GetPriceChangeLogRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetPriceChangeLogResponse.SerializeToString,
      ),
      'GetProductPriceAgent': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductPriceAgent,
          request_deserializer=price__center_dot_price__center__pb2.GetProductPriceAgentRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetProductPriceAgentResponse.SerializeToString,
      ),
      'DeletePriceGroup': grpc.unary_unary_rpc_method_handler(
          servicer.DeletePriceGroup,
          request_deserializer=price__center_dot_price__center__pb2.DeletePriceGroupRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.Response.SerializeToString,
      ),
      'PriceGroupDataCheck': grpc.unary_unary_rpc_method_handler(
          servicer.PriceGroupDataCheck,
          request_deserializer=price__center_dot_price__center__pb2.PriceGroupDataCheckRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.PriceGroupDataCheckResponse.SerializeToString,
      ),
      'GetUnsetPriceReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetUnsetPriceReport,
          request_deserializer=price__center_dot_price__center__pb2.GetUnsetPriceReportRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetUnsetPriceReportResponse.SerializeToString,
      ),
      'SyncPriceChangeLog': grpc.unary_unary_rpc_method_handler(
          servicer.SyncPriceChangeLog,
          request_deserializer=price__center_dot_price__center__pb2.syncPriceChangeLogRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.Response.SerializeToString,
      ),
      'GetPriceGroupTag': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceGroupTag,
          request_deserializer=price__center_dot_price__center__pb2.GetPriceGroupTagRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetPriceGroupTagResponse.SerializeToString,
      ),
      'DeletePriceGroupTag': grpc.unary_unary_rpc_method_handler(
          servicer.DeletePriceGroupTag,
          request_deserializer=price__center_dot_price__center__pb2.DeletePriceGroupRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.Response.SerializeToString,
      ),
      'GetPgpTagCategory': grpc.unary_unary_rpc_method_handler(
          servicer.GetPgpTagCategory,
          request_deserializer=price__center_dot_price__center__pb2.GetPgpTagCategoryRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.GetPgpTagCategoryResponse.SerializeToString,
      ),
      'DeletePgpTagCategory': grpc.unary_unary_rpc_method_handler(
          servicer.DeletePgpTagCategory,
          request_deserializer=price__center_dot_price__center__pb2.DeletePriceGroupRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.Response.SerializeToString,
      ),
      'CreatePriceGroupProductTag': grpc.unary_unary_rpc_method_handler(
          servicer.CreatePriceGroupProductTag,
          request_deserializer=price__center_dot_price__center__pb2.CreatePriceGroupProductTagRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.CreatePriceGroupProductTagResponse.SerializeToString,
      ),
      'UpdatePriceGroupProductTag': grpc.unary_unary_rpc_method_handler(
          servicer.UpdatePriceGroupProductTag,
          request_deserializer=price__center_dot_price__center__pb2.CreatePriceGroupProductTagRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.CreatePriceGroupProductTagResponse.SerializeToString,
      ),
      'DeletePriceGroupProductTag': grpc.unary_unary_rpc_method_handler(
          servicer.DeletePriceGroupProductTag,
          request_deserializer=price__center_dot_price__center__pb2.DeletePriceGroupProductRequest.FromString,
          response_serializer=price__center_dot_price__center__pb2.Response.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'price_center.PriceCenterService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
