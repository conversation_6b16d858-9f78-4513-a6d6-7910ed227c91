# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: price_center/price_center.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='price_center/price_center.proto',
  package='price_center',
  syntax='proto3',
  serialized_options=_b('Z\016./price_center'),
  serialized_pb=_b('\n\x1fprice_center/price_center.proto\x12\x0cprice_center\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x17\n\x07Request\x12\x0c\n\x04ping\x18\x01 \x01(\t\")\n\x08Response\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t\"B\n\x04Page\x12\r\n\x05order\x18\x01 \x01(\t\x12\x0c\n\x04sort\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\"\xba\x01\n\x17\x43reatePriceGroupRequest\x12-\n\x0bprice_group\x18\x01 \x01(\x0b\x32\x18.price_center.PriceGroup\x12)\n\x08products\x18\x02 \x03(\x0b\x32\x17.price_center.PriceInfo\x12\x11\n\timmediate\x18\x03 \x01(\x08\x12\x32\n\x0e\x65\x66\x66\x65\x63tive_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xb2\x03\n\nPriceGroup\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x12\n\nregion_ids\x18\x04 \x03(\t\x12\x11\n\tstore_ids\x18\x05 \x03(\t\x12\x15\n\rexc_store_ids\x18\x06 \x03(\t\x12\x15\n\rproduct_count\x18\x07 \x01(\x04\x12\x0e\n\x06status\x18\x08 \x01(\t\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12+\n\x07updated\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cregion_names\x18\r \x03(\t\x12\x13\n\x0bstore_names\x18\x0e \x03(\t\x12\x17\n\x0f\x65xc_store_names\x18\x0f \x03(\t\x12\x16\n\x0e\x65xc_region_ids\x18\x10 \x03(\t\x12\x18\n\x10\x65xc_region_names\x18\x11 \x03(\t\x12\x0e\n\x06tag_id\x18\x12 \x01(\t\x12\x10\n\x08tag_name\x18\x13 \x01(\t\x12\x13\n\x0bregion_type\x18\x14 \x01(\t\x12\x15\n\rprice_type_id\x18\x15 \x01(\t\x12\x10\n\x08\x63urrency\x18\x16 \x01(\t\"\xac\x02\n\x13\x45rrProductPriceInfo\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x16\n\x0eprice_group_id\x18\x04 \x01(\x04\x12\x18\n\x10price_group_code\x18\x05 \x01(\t\x12\x18\n\x10price_group_name\x18\x06 \x01(\t\x12\x15\n\rprice_type_id\x18\x07 \x01(\x04\x12\x17\n\x0fprice_type_name\x18\x08 \x01(\t\x12\r\n\x05price\x18\t \x01(\t\x12\x12\n\nstore_name\x18\n \x01(\t\x12\x12\n\nstore_code\x18\x0b \x01(\t\x12\x0f\n\x07task_id\x18\x0c \x01(\x04\x12\x11\n\ttask_name\x18\r \x01(\t\"\x94\x01\n\x18\x43reatePriceGroupResponse\x12#\n\x04\x63ode\x18\x01 \x01(\x0e\x32\x15.price_center.ErrCode\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x33\n\x08\x65rr_list\x18\x03 \x03(\x0b\x32!.price_center.ErrProductPriceInfo\x12\r\n\x05total\x18\x04 \x01(\x03\"\n\n\x08Relation\"\xfa\x02\n\tPriceInfo\x12\x0f\n\x07item_id\x18\x01 \x01(\x04\x12\x11\n\titem_code\x18\x02 \x01(\t\x12\x11\n\titem_name\x18\x03 \x01(\t\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12\x15\n\rorder_unit_id\x18\x05 \x01(\x04\x12\x17\n\x0forder_unit_name\x18\x06 \x01(\t\x12\x10\n\x08tax_rate\x18\x07 \x01(\t\x12\x16\n\x0epurchase_price\x18\t \x01(\t\x12\x11\n\tsend_type\x18\n \x01(\t\x12\x0c\n\x04rule\x18\x0b \x01(\t\x12\x15\n\rprice_type_id\x18\x0c \x01(\t\x12\x17\n\x0fprice_type_name\x18\r \x01(\t\x12\r\n\x05price\x18\x0e \x01(\t\x12\x16\n\x0eprice_group_id\x18\x0f \x01(\x04\x12\n\n\x02id\x18\x10 \x01(\x04\x12\x11\n\told_price\x18\x11 \x01(\t\x12\x10\n\x08use_date\x18\x12 \x01(\t\x12\x0e\n\x06hidden\x18\x13 \x01(\x08\x12\x15\n\rtakeout_price\x18\x14 \x01(\t\"\xab\x01\n\x1fGetPriceGroupProductListRequest\x12\x17\n\x0fprice_group_ids\x18\x01 \x03(\x04\x12\x0b\n\x03ids\x18\x02 \x03(\x04\x12\x0e\n\x06search\x18\x03 \x01(\t\x12 \n\x04page\x18\x04 \x01(\x0b\x32\x12.price_center.Page\x12\x18\n\x10include_disabled\x18\x05 \x01(\x08\x12\x16\n\x0eprice_type_ids\x18\x06 \x03(\x04\"X\n GetPriceGroupProductListResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.price_center.PriceInfo\x12\r\n\x05total\x18\x02 \x01(\x03\"\x8d\x02\n\x1aGetProductPriceInfoRequest\x12\x10\n\x08item_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x16\n\x0eprice_type_ids\x18\x03 \x03(\x04\x12\x12\n\nregion_ids\x18\x04 \x03(\x04\x12\x13\n\x0bregion_type\x18\x05 \x01(\t\x12 \n\x04page\x18\x06 \x01(\x0b\x32\x12.price_center.Page\x12\x0f\n\x07pending\x18\x07 \x01(\t\x12#\n\x1bprice_group_product_tag_ids\x18\x08 \x03(\x04\x12\x14\n\x0cgroup_search\x18\t \x01(\t\x12\x1b\n\x13price_group_tag_ids\x18\n \x03(\x04\"\xed\x04\n\x1bGetProductPriceInfoResponse\x12;\n\x04rows\x18\x01 \x03(\x0b\x32-.price_center.GetProductPriceInfoResponse.row\x12\r\n\x05total\x18\x02 \x01(\x03\x1a\x81\x04\n\x03row\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_name\x18\x02 \x01(\t\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x0f\n\x07item_id\x18\x04 \x01(\x04\x12\x11\n\titem_code\x18\x05 \x01(\t\x12\x11\n\titem_name\x18\x06 \x01(\t\x12\x0c\n\x04spec\x18\x07 \x01(\t\x12\x10\n\x08tax_rate\x18\n \x01(\t\x12\x16\n\x0epurchase_price\x18\x0b \x01(\t\x12\x11\n\tsend_type\x18\x0c \x01(\t\x12\x0c\n\x04rule\x18\r \x01(\t\x12\x15\n\rprice_type_id\x18\x0e \x01(\x04\x12\x17\n\x0fprice_type_name\x18\x0f \x01(\t\x12\r\n\x05price\x18\x10 \x01(\t\x12\x15\n\runtaxed_price\x18\x11 \x01(\t\x12\x16\n\x0eprice_group_id\x18\x12 \x01(\x04\x12\x18\n\x10price_group_name\x18\x13 \x01(\t\x12\x18\n\x10price_group_code\x18\x14 \x01(\t\x12\x15\n\rorder_unit_id\x18\x15 \x01(\x04\x12\x17\n\x0forder_unit_name\x18\x16 \x01(\t\x12%\n\x1dprice_group_product_tag_names\x18\x17 \x01(\t\x12\x14\n\x0cupdated_name\x18\x18 \x01(\t\x12\x0f\n\x07updated\x18\x19 \x01(\t\x12\x10\n\x08\x63urrency\x18\x1a \x01(\t\"\xd0\x01\n\x17UpdatePriceGroupRequest\x12-\n\x0bprice_group\x18\x01 \x01(\x0b\x32\x18.price_center.PriceGroup\x12)\n\x08products\x18\x02 \x03(\x0b\x32\x17.price_center.PriceInfo\x12\x11\n\timmediate\x18\x03 \x01(\x08\x12\x14\n\x0c\x64\x65l_item_ids\x18\x04 \x03(\x04\x12\x32\n\x0e\x65\x66\x66\x65\x63tive_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"1\n\x1eGetPriceGroupTaskHeaderRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\x04\"\xdc\x01\n\x1fGetPriceGroupTaskHeaderResponse\x12\x0f\n\x07task_id\x18\x01 \x01(\x04\x12\x11\n\ttask_code\x18\x02 \x01(\t\x12\x0f\n\x07\x63reated\x18\x03 \x01(\t\x12\x16\n\x0e\x65\x66\x66\x65\x63tive_time\x18\x04 \x01(\t\x12-\n\x0bprice_group\x18\x05 \x01(\x0b\x32\x18.price_center.PriceGroup\x12\x0f\n\x07updated\x18\x06 \x01(\t\x12\x16\n\x0eprocess_status\x18\x07 \x01(\t\x12\x14\n\x0cupdated_name\x18\x08 \x01(\t\"\x8d\x01\n\x1eGetPriceGroupTaskDetailRequest\x12\x0f\n\x07task_id\x18\x01 \x03(\x04\x12\x0e\n\x06search\x18\x02 \x01(\t\x12 \n\x04page\x18\x03 \x01(\x0b\x32\x12.price_center.Page\x12\x16\n\x0ereturn_compare\x18\x04 \x01(\x08\x12\x10\n\x08store_id\x18\x05 \x01(\x04\"\xe2\x01\n\x1fGetPriceGroupTaskDetailResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.price_center.PriceInfo\x12-\n\x0cupdated_rows\x18\x02 \x03(\x0b\x32\x17.price_center.PriceInfo\x12-\n\x0c\x64\x65leted_rows\x18\x03 \x03(\x0b\x32\x17.price_center.PriceInfo\x12+\n\nadded_rows\x18\x04 \x03(\x0b\x32\x17.price_center.PriceInfo\x12\r\n\x05total\x18\x05 \x01(\x03\"\x91\x02\n\x18GetPriceChangeLogRequest\x12\x10\n\x08item_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x16\n\x0eprice_type_ids\x18\x03 \x03(\x04\x12\x13\n\x0bregion_type\x18\x04 \x01(\t\x12\x12\n\nregion_ids\x18\x05 \x03(\x04\x12-\n\tactivated\x18\x07 \x01(\x0b\x32\x1a.price_center.TimeSelector\x12\x11\n\tgroup_ids\x18\x08 \x03(\x04\x12\x14\n\x0cgroup_search\x18\t \x01(\t\x12 \n\x04page\x18\n \x01(\x0b\x32\x12.price_center.Page\x12\x15\n\rneed_group_by\x18\x0b \x01(\x08\"\x8d\x01\n\x19GetPriceChangeLogResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.price_center.PriceChangeLog\x12\x35\n\ngroup_rows\x18\x02 \x03(\x0b\x32!.price_center.PriceChangeLogGroup\x12\r\n\x05total\x18\x03 \x01(\x03\"\xb1\x03\n\x0ePriceChangeLog\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_name\x18\x02 \x01(\t\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x0f\n\x07item_id\x18\x04 \x01(\x04\x12\x11\n\titem_code\x18\x05 \x01(\t\x12\x11\n\titem_name\x18\x06 \x01(\t\x12\x15\n\rprice_type_id\x18\x07 \x01(\x04\x12\x11\n\told_price\x18\x08 \x01(\t\x12\x11\n\tnew_price\x18\t \x01(\t\x12\x12\n\nupdated_by\x18\n \x01(\x04\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12\x0f\n\x07\x63reated\x18\x0c \x01(\t\x12\x11\n\tactivated\x18\r \x01(\t\x12\x18\n\x10price_group_code\x18\x0e \x01(\t\x12\x18\n\x10price_group_name\x18\x0f \x01(\t\x12\x16\n\x0eprice_group_id\x18\x10 \x01(\x04\x12\x17\n\x0fprice_type_code\x18\x11 \x01(\t\x12\x17\n\x0fprice_type_name\x18\x12 \x01(\t\x12\x13\n\x0b\x61\x63tion_type\x18\x13 \x01(\t\x12\x10\n\x08\x63urrency\x18\x14 \x01(\t\"\x8e\x01\n\x1bGetProductPriceAgentRequest\x12\x10\n\x08item_ids\x18\x01 \x03(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x15\n\rprice_type_id\x18\x03 \x01(\x04\x12 \n\x04page\x18\x05 \x01(\x0b\x32\x12.price_center.Page\x12\x12\n\nitem_codes\x18\x06 \x03(\t\"\xbb\x02\n\x1cGetProductPriceAgentResponse\x12\x43\n\x04rows\x18\x01 \x03(\x0b\x32\x35.price_center.GetProductPriceAgentResponse.PriceAgent\x12\r\n\x05total\x18\x02 \x01(\x03\x1a\xc6\x01\n\nPriceAgent\x12\x0f\n\x07item_id\x18\x01 \x01(\x04\x12\x11\n\titem_code\x18\x02 \x01(\t\x12\x11\n\titem_name\x18\x03 \x01(\t\x12\x15\n\rprice_type_id\x18\x04 \x01(\x04\x12\r\n\x05price\x18\x05 \x01(\t\x12\x10\n\x08tax_rate\x18\x06 \x01(\t\x12\x10\n\x08use_date\x18\x07 \x01(\t\x12\x0e\n\x06hidden\x18\x08 \x01(\x08\x12\x15\n\rtakeout_price\x18\t \x01(\t\x12\x10\n\x08\x63urrency\x18\n \x01(\t\"%\n\x17\x44\x65letePriceGroupRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"v\n\x1aPriceGroupDataCheckRequest\x12-\n\x0bprice_group\x18\x01 \x01(\x0b\x32\x18.price_center.PriceGroup\x12)\n\x08products\x18\x02 \x03(\x0b\x32\x17.price_center.PriceInfo\"]\n\x1bPriceGroupDataCheckResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.price_center.ErrProductPriceInfo\x12\r\n\x05total\x18\x02 \x01(\x03\"\xee\x03\n\x10UnsetPriceReport\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x12\n\npartner_id\x18\x02 \x01(\x03\x12\x10\n\x08store_id\x18\x03 \x01(\x03\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nstore_name\x18\x05 \x01(\t\x12\x14\n\x0cstore_status\x18\x06 \x01(\t\x12\x12\n\nproduct_id\x18\x07 \x01(\x03\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x16\n\x0eproduct_status\x18\n \x01(\t\x12-\n\tcalc_time\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nis_deleted\x18\x0e \x01(\x05\x12\x0c\n\x04spec\x18\x0f \x01(\t\x12\x18\n\x10purchase_unit_id\x18\x10 \x01(\x03\x12\x1a\n\x12purchase_unit_name\x18\x11 \x01(\t\x12\x16\n\x0epurchase_price\x18\x12 \x01(\t\x12\x13\n\x0b\x64istri_type\x18\x13 \x01(\t\"\xa6\x01\n\x1aGetUnsetPriceReportRequest\x12\x10\n\x08item_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x14\n\x0cstore_status\x18\x03 \x01(\t\x12\x16\n\x0eproduct_status\x18\x04 \x01(\t\x12\x13\n\x0b\x64istri_type\x18\x05 \x01(\t\x12 \n\x04page\x18\x06 \x01(\x0b\x32\x12.price_center.Page\"Z\n\x1bGetUnsetPriceReportResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.price_center.UnsetPriceReport\x12\r\n\x05total\x18\x02 \x01(\x03\"S\n\x19syncPriceChangeLogRequest\x12\x12\n\npartner_id\x18\x01 \x01(\x03\x12\x10\n\x08trace_id\x18\x02 \x01(\x03\x12\x10\n\x08group_id\x18\x03 \x01(\x03\"\\\n\x17GetPriceGroupTagRequest\x12\x0f\n\x07tag_ids\x18\x01 \x03(\x04\x12\x0e\n\x06search\x18\x02 \x01(\t\x12 \n\x04page\x18\x05 \x01(\x0b\x32\x12.price_center.Page\"\xc7\x01\n\rPriceGroupTag\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12.\n\x0cprice_groups\x18\x05 \x03(\x0b\x32\x18.price_center.PriceGroup\x12\x0f\n\x07\x63reated\x18\x06 \x01(\t\x12\x0f\n\x07updated\x18\x07 \x01(\t\x12\x14\n\x0cupdated_name\x18\x08 \x01(\t\x12\x12\n\nupdated_by\x18\t \x01(\t\"T\n\x18GetPriceGroupTagResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.price_center.PriceGroupTag\x12\r\n\x05total\x18\x02 \x01(\x03\"Y\n\x18GetPgpTagCategoryRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x04\x12\x0e\n\x06search\x18\x02 \x01(\t\x12 \n\x04page\x18\x05 \x01(\x0b\x32\x12.price_center.Page\"Q\n\x0ePgpTagCategory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x11\n\ttag_count\x18\x04 \x01(\x04\"V\n\x19GetPgpTagCategoryResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.price_center.PgpTagCategory\x12\r\n\x05total\x18\x02 \x01(\x03\"\xd1\x01\n\x14PriceGroupProductTag\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x1b\n\x13pgp_tag_category_id\x18\x05 \x01(\t\x12\x1c\n\x14product_category_ids\x18\x06 \x03(\t\x12\x13\n\x0bproduct_ids\x18\x07 \x03(\t\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12+\n\x07updated\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"h\n!CreatePriceGroupProductTagRequest\x12\x43\n\x17price_group_product_tag\x18\x01 \x01(\x0b\x32\".price_center.PriceGroupProductTag\"i\n\"CreatePriceGroupProductTagResponse\x12#\n\x04\x63ode\x18\x01 \x01(\x0e\x32\x15.price_center.ErrCode\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\r\n\x05total\x18\x04 \x01(\x03\",\n\x1e\x44\x65letePriceGroupProductRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"l\n\x0cTimeSelector\x12.\n\nstart_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xe3\x03\n\x13PriceChangeLogGroup\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_name\x18\x02 \x01(\t\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x18\n\x10price_group_code\x18\x05 \x01(\t\x12\x18\n\x10price_group_name\x18\x06 \x01(\t\x12\x16\n\x0eprice_group_id\x18\x07 \x01(\x04\x12\x15\n\rprice_type_id\x18\x08 \x01(\x04\x12\x17\n\x0fprice_type_name\x18\t \x01(\t\x12\x17\n\x0fprice_type_code\x18\n \x01(\t\x12G\n\x07\x61\x63tions\x18\x0b \x01(\x0b\x32\x36.price_center.PriceChangeLogGroup.PriceChangeLogAction\x12\x10\n\x08\x63urrency\x18\x0c \x01(\t\x1a\xa1\x01\n\x14PriceChangeLogAction\x12-\n\x07updated\x18\x01 \x03(\x0b\x32\x1c.price_center.PriceChangeLog\x12-\n\x07\x64\x65leted\x18\x02 \x03(\x0b\x32\x1c.price_center.PriceChangeLog\x12+\n\x05\x61\x64\x64\x65\x64\x18\x03 \x03(\x0b\x32\x1c.price_center.PriceChangeLog*l\n\x07\x45rrCode\x12\x0b\n\x07Success\x10\x00\x12\x11\n\rDuplicateCode\x10\x01\x12\x11\n\rDuplicateName\x10\x02\x12\x14\n\x10\x44uplicateProduct\x10\x03\x12\x18\n\x14\x44uplicateCodeAndName\x10\x04\x32\xf0\x18\n\x12PriceCenterService\x12\x37\n\x04Ping\x12\x15.price_center.Request\x1a\x16.price_center.Response\"\x00\x12\x95\x01\n\x10\x43reatePriceGroup\x12%.price_center.CreatePriceGroupRequest\x1a&.price_center.CreatePriceGroupResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v1/price-center/create-price-group:\x01*\x12\xb4\x01\n\x18GetPriceGroupProductList\x12-.price_center.GetPriceGroupProductListRequest\x1a..price_center.GetPriceGroupProductListResponse\"9\x82\xd3\xe4\x93\x02\x33\"./api/v1/price-center/get-price-group-item-list:\x01*\x12\x9f\x01\n\x13GetProductPriceInfo\x12(.price_center.GetProductPriceInfoRequest\x1a).price_center.GetProductPriceInfoResponse\"3\x82\xd3\xe4\x93\x02-\"(/api/v1/price-center/get-item-price-info:\x01*\x12\x95\x01\n\x10UpdatePriceGroup\x12%.price_center.UpdatePriceGroupRequest\x1a&.price_center.CreatePriceGroupResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v1/price-center/update-price-group:\x01*\x12\xb3\x01\n\x17GetPriceGroupTaskHeader\x12,.price_center.GetPriceGroupTaskHeaderRequest\x1a-.price_center.GetPriceGroupTaskHeaderResponse\";\x82\xd3\xe4\x93\x02\x35\"0/api/v1/price-center/get-price-group-task-header:\x01*\x12\xb3\x01\n\x17GetPriceGroupTaskDetail\x12,.price_center.GetPriceGroupTaskDetailRequest\x1a-.price_center.GetPriceGroupTaskDetailResponse\";\x82\xd3\xe4\x93\x02\x35\"0/api/v1/price-center/get-price-group-task-detail:\x01*\x12\x9a\x01\n\x11GetPriceChangeLog\x12&.price_center.GetPriceChangeLogRequest\x1a\'.price_center.GetPriceChangeLogResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v1/price-center/get-price-change-log:\x01*\x12\xa3\x01\n\x14GetProductPriceAgent\x12).price_center.GetProductPriceAgentRequest\x1a*.price_center.GetProductPriceAgentResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v1/price-center/get-item-price-agent:\x01*\x12\x85\x01\n\x10\x44\x65letePriceGroup\x12%.price_center.DeletePriceGroupRequest\x1a\x16.price_center.Response\"2\x82\xd3\xe4\x93\x02,\"\'/api/v1/price-center/delete-price-group:\x01*\x12\xa3\x01\n\x13PriceGroupDataCheck\x12(.price_center.PriceGroupDataCheckRequest\x1a).price_center.PriceGroupDataCheckResponse\"7\x82\xd3\xe4\x93\x02\x31\",/api/v1/price-center/create-check-price-task:\x01*\x12\xa2\x01\n\x13GetUnsetPriceReport\x12(.price_center.GetUnsetPriceReportRequest\x1a).price_center.GetUnsetPriceReportResponse\"6\x82\xd3\xe4\x93\x02\x30\"+/api/v1/price-center/get-unset-price-report:\x01*\x12\x86\x01\n\x12SyncPriceChangeLog\x12\'.price_center.syncPriceChangeLogRequest\x1a\x16.price_center.Response\"/\x82\xd3\xe4\x93\x02)\"$/api/v1/price-center/change_log/sync:\x01*\x12\x96\x01\n\x10GetPriceGroupTag\x12%.price_center.GetPriceGroupTagRequest\x1a&.price_center.GetPriceGroupTagResponse\"3\x82\xd3\xe4\x93\x02-\"(/api/v1/price-center/get-price-group-tag:\x01*\x12\x8c\x01\n\x13\x44\x65letePriceGroupTag\x12%.price_center.DeletePriceGroupRequest\x1a\x16.price_center.Response\"6\x82\xd3\xe4\x93\x02\x30\"+/api/v1/price-center/delete-price-group-tag:\x01*\x12\x9a\x01\n\x11GetPgpTagCategory\x12&.price_center.GetPgpTagCategoryRequest\x1a\'.price_center.GetPgpTagCategoryResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v1/price-center/get-pgp-tag-category:\x01*\x12\x9e\x01\n\x14\x44\x65letePgpTagCategory\x12%.price_center.DeletePriceGroupRequest\x1a\x16.price_center.Response\"G\x82\xd3\xe4\x93\x02\x41\"</api/v1/price-center/delete-price-group-product-tag-category:\x01*\x12\xbf\x01\n\x1a\x43reatePriceGroupProductTag\x12/.price_center.CreatePriceGroupProductTagRequest\x1a\x30.price_center.CreatePriceGroupProductTagResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v1/price-center/create-price-group-product-tag:\x01*\x12\xbf\x01\n\x1aUpdatePriceGroupProductTag\x12/.price_center.CreatePriceGroupProductTagRequest\x1a\x30.price_center.CreatePriceGroupProductTagResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v1/price-center/update-price-group-product-tag:\x01*\x12\xa2\x01\n\x1a\x44\x65letePriceGroupProductTag\x12,.price_center.DeletePriceGroupProductRequest\x1a\x16.price_center.Response\">\x82\xd3\xe4\x93\x02\x38\"3/api/v1/price-center/delete-price-group-product-tag:\x01*B\x10Z\x0e./price_centerb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_ERRCODE = _descriptor.EnumDescriptor(
  name='ErrCode',
  full_name='price_center.ErrCode',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Success', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateCode', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateName', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateProduct', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateCodeAndName', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7866,
  serialized_end=7974,
)
_sym_db.RegisterEnumDescriptor(_ERRCODE)

ErrCode = enum_type_wrapper.EnumTypeWrapper(_ERRCODE)
Success = 0
DuplicateCode = 1
DuplicateName = 2
DuplicateProduct = 3
DuplicateCodeAndName = 4



_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='price_center.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ping', full_name='price_center.Request.ping', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=112,
  serialized_end=135,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='price_center.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='price_center.Response.code', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='price_center.Response.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=137,
  serialized_end=178,
)


_PAGE = _descriptor.Descriptor(
  name='Page',
  full_name='price_center.Page',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order', full_name='price_center.Page.order', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='price_center.Page.sort', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='price_center.Page.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='price_center.Page.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=180,
  serialized_end=246,
)


_CREATEPRICEGROUPREQUEST = _descriptor.Descriptor(
  name='CreatePriceGroupRequest',
  full_name='price_center.CreatePriceGroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='price_group', full_name='price_center.CreatePriceGroupRequest.price_group', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='price_center.CreatePriceGroupRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='immediate', full_name='price_center.CreatePriceGroupRequest.immediate', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='effective_time', full_name='price_center.CreatePriceGroupRequest.effective_time', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=249,
  serialized_end=435,
)


_PRICEGROUP = _descriptor.Descriptor(
  name='PriceGroup',
  full_name='price_center.PriceGroup',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_center.PriceGroup.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='price_center.PriceGroup.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='price_center.PriceGroup.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_ids', full_name='price_center.PriceGroup.region_ids', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='price_center.PriceGroup.store_ids', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exc_store_ids', full_name='price_center.PriceGroup.exc_store_ids', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_count', full_name='price_center.PriceGroup.product_count', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='price_center.PriceGroup.status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_center.PriceGroup.updated_name', index=8,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='price_center.PriceGroup.updated', index=9,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_names', full_name='price_center.PriceGroup.region_names', index=10,
      number=13, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_names', full_name='price_center.PriceGroup.store_names', index=11,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exc_store_names', full_name='price_center.PriceGroup.exc_store_names', index=12,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exc_region_ids', full_name='price_center.PriceGroup.exc_region_ids', index=13,
      number=16, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exc_region_names', full_name='price_center.PriceGroup.exc_region_names', index=14,
      number=17, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='price_center.PriceGroup.tag_id', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='price_center.PriceGroup.tag_name', index=16,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='price_center.PriceGroup.region_type', index=17,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_id', full_name='price_center.PriceGroup.price_type_id', index=18,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='price_center.PriceGroup.currency', index=19,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=438,
  serialized_end=872,
)


_ERRPRODUCTPRICEINFO = _descriptor.Descriptor(
  name='ErrProductPriceInfo',
  full_name='price_center.ErrProductPriceInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='price_center.ErrProductPriceInfo.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='price_center.ErrProductPriceInfo.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='price_center.ErrProductPriceInfo.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_id', full_name='price_center.ErrProductPriceInfo.price_group_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_code', full_name='price_center.ErrProductPriceInfo.price_group_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_name', full_name='price_center.ErrProductPriceInfo.price_group_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_id', full_name='price_center.ErrProductPriceInfo.price_type_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_name', full_name='price_center.ErrProductPriceInfo.price_type_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='price_center.ErrProductPriceInfo.price', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='price_center.ErrProductPriceInfo.store_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='price_center.ErrProductPriceInfo.store_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_id', full_name='price_center.ErrProductPriceInfo.task_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_name', full_name='price_center.ErrProductPriceInfo.task_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=875,
  serialized_end=1175,
)


_CREATEPRICEGROUPRESPONSE = _descriptor.Descriptor(
  name='CreatePriceGroupResponse',
  full_name='price_center.CreatePriceGroupResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='price_center.CreatePriceGroupResponse.code', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='price_center.CreatePriceGroupResponse.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='err_list', full_name='price_center.CreatePriceGroupResponse.err_list', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.CreatePriceGroupResponse.total', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1178,
  serialized_end=1326,
)


_RELATION = _descriptor.Descriptor(
  name='Relation',
  full_name='price_center.Relation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1328,
  serialized_end=1338,
)


_PRICEINFO = _descriptor.Descriptor(
  name='PriceInfo',
  full_name='price_center.PriceInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_id', full_name='price_center.PriceInfo.item_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_code', full_name='price_center.PriceInfo.item_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_name', full_name='price_center.PriceInfo.item_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='price_center.PriceInfo.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_id', full_name='price_center.PriceInfo.order_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_name', full_name='price_center.PriceInfo.order_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='price_center.PriceInfo.tax_rate', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='price_center.PriceInfo.purchase_price', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='price_center.PriceInfo.send_type', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule', full_name='price_center.PriceInfo.rule', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_id', full_name='price_center.PriceInfo.price_type_id', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_name', full_name='price_center.PriceInfo.price_type_name', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='price_center.PriceInfo.price', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_id', full_name='price_center.PriceInfo.price_group_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='price_center.PriceInfo.id', index=14,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='old_price', full_name='price_center.PriceInfo.old_price', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_date', full_name='price_center.PriceInfo.use_date', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hidden', full_name='price_center.PriceInfo.hidden', index=17,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='takeout_price', full_name='price_center.PriceInfo.takeout_price', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1341,
  serialized_end=1719,
)


_GETPRICEGROUPPRODUCTLISTREQUEST = _descriptor.Descriptor(
  name='GetPriceGroupProductListRequest',
  full_name='price_center.GetPriceGroupProductListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='price_group_ids', full_name='price_center.GetPriceGroupProductListRequest.price_group_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='price_center.GetPriceGroupProductListRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='price_center.GetPriceGroupProductListRequest.search', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='price_center.GetPriceGroupProductListRequest.page', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_disabled', full_name='price_center.GetPriceGroupProductListRequest.include_disabled', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_ids', full_name='price_center.GetPriceGroupProductListRequest.price_type_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1722,
  serialized_end=1893,
)


_GETPRICEGROUPPRODUCTLISTRESPONSE = _descriptor.Descriptor(
  name='GetPriceGroupProductListResponse',
  full_name='price_center.GetPriceGroupProductListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.GetPriceGroupProductListResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.GetPriceGroupProductListResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1895,
  serialized_end=1983,
)


_GETPRODUCTPRICEINFOREQUEST = _descriptor.Descriptor(
  name='GetProductPriceInfoRequest',
  full_name='price_center.GetProductPriceInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_ids', full_name='price_center.GetProductPriceInfoRequest.item_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='price_center.GetProductPriceInfoRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_ids', full_name='price_center.GetProductPriceInfoRequest.price_type_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_ids', full_name='price_center.GetProductPriceInfoRequest.region_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='price_center.GetProductPriceInfoRequest.region_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='price_center.GetProductPriceInfoRequest.page', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pending', full_name='price_center.GetProductPriceInfoRequest.pending', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_product_tag_ids', full_name='price_center.GetProductPriceInfoRequest.price_group_product_tag_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_search', full_name='price_center.GetProductPriceInfoRequest.group_search', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_tag_ids', full_name='price_center.GetProductPriceInfoRequest.price_group_tag_ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1986,
  serialized_end=2255,
)


_GETPRODUCTPRICEINFORESPONSE_ROW = _descriptor.Descriptor(
  name='row',
  full_name='price_center.GetProductPriceInfoResponse.row',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='price_center.GetProductPriceInfoResponse.row.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='price_center.GetProductPriceInfoResponse.row.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='price_center.GetProductPriceInfoResponse.row.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_id', full_name='price_center.GetProductPriceInfoResponse.row.item_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_code', full_name='price_center.GetProductPriceInfoResponse.row.item_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_name', full_name='price_center.GetProductPriceInfoResponse.row.item_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='price_center.GetProductPriceInfoResponse.row.spec', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='price_center.GetProductPriceInfoResponse.row.tax_rate', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='price_center.GetProductPriceInfoResponse.row.purchase_price', index=8,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='price_center.GetProductPriceInfoResponse.row.send_type', index=9,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule', full_name='price_center.GetProductPriceInfoResponse.row.rule', index=10,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_id', full_name='price_center.GetProductPriceInfoResponse.row.price_type_id', index=11,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_name', full_name='price_center.GetProductPriceInfoResponse.row.price_type_name', index=12,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='price_center.GetProductPriceInfoResponse.row.price', index=13,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='untaxed_price', full_name='price_center.GetProductPriceInfoResponse.row.untaxed_price', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_id', full_name='price_center.GetProductPriceInfoResponse.row.price_group_id', index=15,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_name', full_name='price_center.GetProductPriceInfoResponse.row.price_group_name', index=16,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_code', full_name='price_center.GetProductPriceInfoResponse.row.price_group_code', index=17,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_id', full_name='price_center.GetProductPriceInfoResponse.row.order_unit_id', index=18,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_name', full_name='price_center.GetProductPriceInfoResponse.row.order_unit_name', index=19,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_product_tag_names', full_name='price_center.GetProductPriceInfoResponse.row.price_group_product_tag_names', index=20,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_center.GetProductPriceInfoResponse.row.updated_name', index=21,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='price_center.GetProductPriceInfoResponse.row.updated', index=22,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='price_center.GetProductPriceInfoResponse.row.currency', index=23,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2366,
  serialized_end=2879,
)

_GETPRODUCTPRICEINFORESPONSE = _descriptor.Descriptor(
  name='GetProductPriceInfoResponse',
  full_name='price_center.GetProductPriceInfoResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.GetProductPriceInfoResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.GetProductPriceInfoResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETPRODUCTPRICEINFORESPONSE_ROW, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2258,
  serialized_end=2879,
)


_UPDATEPRICEGROUPREQUEST = _descriptor.Descriptor(
  name='UpdatePriceGroupRequest',
  full_name='price_center.UpdatePriceGroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='price_group', full_name='price_center.UpdatePriceGroupRequest.price_group', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='price_center.UpdatePriceGroupRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='immediate', full_name='price_center.UpdatePriceGroupRequest.immediate', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='del_item_ids', full_name='price_center.UpdatePriceGroupRequest.del_item_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='effective_time', full_name='price_center.UpdatePriceGroupRequest.effective_time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2882,
  serialized_end=3090,
)


_GETPRICEGROUPTASKHEADERREQUEST = _descriptor.Descriptor(
  name='GetPriceGroupTaskHeaderRequest',
  full_name='price_center.GetPriceGroupTaskHeaderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='price_center.GetPriceGroupTaskHeaderRequest.task_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3092,
  serialized_end=3141,
)


_GETPRICEGROUPTASKHEADERRESPONSE = _descriptor.Descriptor(
  name='GetPriceGroupTaskHeaderResponse',
  full_name='price_center.GetPriceGroupTaskHeaderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='price_center.GetPriceGroupTaskHeaderResponse.task_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_code', full_name='price_center.GetPriceGroupTaskHeaderResponse.task_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='price_center.GetPriceGroupTaskHeaderResponse.created', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='effective_time', full_name='price_center.GetPriceGroupTaskHeaderResponse.effective_time', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group', full_name='price_center.GetPriceGroupTaskHeaderResponse.price_group', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='price_center.GetPriceGroupTaskHeaderResponse.updated', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='price_center.GetPriceGroupTaskHeaderResponse.process_status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_center.GetPriceGroupTaskHeaderResponse.updated_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3144,
  serialized_end=3364,
)


_GETPRICEGROUPTASKDETAILREQUEST = _descriptor.Descriptor(
  name='GetPriceGroupTaskDetailRequest',
  full_name='price_center.GetPriceGroupTaskDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='price_center.GetPriceGroupTaskDetailRequest.task_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='price_center.GetPriceGroupTaskDetailRequest.search', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='price_center.GetPriceGroupTaskDetailRequest.page', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_compare', full_name='price_center.GetPriceGroupTaskDetailRequest.return_compare', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='price_center.GetPriceGroupTaskDetailRequest.store_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3367,
  serialized_end=3508,
)


_GETPRICEGROUPTASKDETAILRESPONSE = _descriptor.Descriptor(
  name='GetPriceGroupTaskDetailResponse',
  full_name='price_center.GetPriceGroupTaskDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.GetPriceGroupTaskDetailResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_rows', full_name='price_center.GetPriceGroupTaskDetailResponse.updated_rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted_rows', full_name='price_center.GetPriceGroupTaskDetailResponse.deleted_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='added_rows', full_name='price_center.GetPriceGroupTaskDetailResponse.added_rows', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.GetPriceGroupTaskDetailResponse.total', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3511,
  serialized_end=3737,
)


_GETPRICECHANGELOGREQUEST = _descriptor.Descriptor(
  name='GetPriceChangeLogRequest',
  full_name='price_center.GetPriceChangeLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_ids', full_name='price_center.GetPriceChangeLogRequest.item_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='price_center.GetPriceChangeLogRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_ids', full_name='price_center.GetPriceChangeLogRequest.price_type_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='price_center.GetPriceChangeLogRequest.region_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_ids', full_name='price_center.GetPriceChangeLogRequest.region_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='activated', full_name='price_center.GetPriceChangeLogRequest.activated', index=5,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_ids', full_name='price_center.GetPriceChangeLogRequest.group_ids', index=6,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_search', full_name='price_center.GetPriceChangeLogRequest.group_search', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='price_center.GetPriceChangeLogRequest.page', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='need_group_by', full_name='price_center.GetPriceChangeLogRequest.need_group_by', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3740,
  serialized_end=4013,
)


_GETPRICECHANGELOGRESPONSE = _descriptor.Descriptor(
  name='GetPriceChangeLogResponse',
  full_name='price_center.GetPriceChangeLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.GetPriceChangeLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_rows', full_name='price_center.GetPriceChangeLogResponse.group_rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.GetPriceChangeLogResponse.total', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4016,
  serialized_end=4157,
)


_PRICECHANGELOG = _descriptor.Descriptor(
  name='PriceChangeLog',
  full_name='price_center.PriceChangeLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='price_center.PriceChangeLog.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='price_center.PriceChangeLog.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='price_center.PriceChangeLog.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_id', full_name='price_center.PriceChangeLog.item_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_code', full_name='price_center.PriceChangeLog.item_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_name', full_name='price_center.PriceChangeLog.item_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_id', full_name='price_center.PriceChangeLog.price_type_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='old_price', full_name='price_center.PriceChangeLog.old_price', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='new_price', full_name='price_center.PriceChangeLog.new_price', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='price_center.PriceChangeLog.updated_by', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_center.PriceChangeLog.updated_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='price_center.PriceChangeLog.created', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='activated', full_name='price_center.PriceChangeLog.activated', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_code', full_name='price_center.PriceChangeLog.price_group_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_name', full_name='price_center.PriceChangeLog.price_group_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_id', full_name='price_center.PriceChangeLog.price_group_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_code', full_name='price_center.PriceChangeLog.price_type_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_name', full_name='price_center.PriceChangeLog.price_type_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action_type', full_name='price_center.PriceChangeLog.action_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='price_center.PriceChangeLog.currency', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4160,
  serialized_end=4593,
)


_GETPRODUCTPRICEAGENTREQUEST = _descriptor.Descriptor(
  name='GetProductPriceAgentRequest',
  full_name='price_center.GetProductPriceAgentRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_ids', full_name='price_center.GetProductPriceAgentRequest.item_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='price_center.GetProductPriceAgentRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_id', full_name='price_center.GetProductPriceAgentRequest.price_type_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='price_center.GetProductPriceAgentRequest.page', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_codes', full_name='price_center.GetProductPriceAgentRequest.item_codes', index=4,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4596,
  serialized_end=4738,
)


_GETPRODUCTPRICEAGENTRESPONSE_PRICEAGENT = _descriptor.Descriptor(
  name='PriceAgent',
  full_name='price_center.GetProductPriceAgentResponse.PriceAgent',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_id', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.item_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_code', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.item_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_name', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.item_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_id', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.price_type_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.price', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.tax_rate', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='use_date', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.use_date', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hidden', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.hidden', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='takeout_price', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.takeout_price', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='price_center.GetProductPriceAgentResponse.PriceAgent.currency', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4858,
  serialized_end=5056,
)

_GETPRODUCTPRICEAGENTRESPONSE = _descriptor.Descriptor(
  name='GetProductPriceAgentResponse',
  full_name='price_center.GetProductPriceAgentResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.GetProductPriceAgentResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.GetProductPriceAgentResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETPRODUCTPRICEAGENTRESPONSE_PRICEAGENT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4741,
  serialized_end=5056,
)


_DELETEPRICEGROUPREQUEST = _descriptor.Descriptor(
  name='DeletePriceGroupRequest',
  full_name='price_center.DeletePriceGroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_center.DeletePriceGroupRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5058,
  serialized_end=5095,
)


_PRICEGROUPDATACHECKREQUEST = _descriptor.Descriptor(
  name='PriceGroupDataCheckRequest',
  full_name='price_center.PriceGroupDataCheckRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='price_group', full_name='price_center.PriceGroupDataCheckRequest.price_group', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='price_center.PriceGroupDataCheckRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5097,
  serialized_end=5215,
)


_PRICEGROUPDATACHECKRESPONSE = _descriptor.Descriptor(
  name='PriceGroupDataCheckResponse',
  full_name='price_center.PriceGroupDataCheckResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.PriceGroupDataCheckResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.PriceGroupDataCheckResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5217,
  serialized_end=5310,
)


_UNSETPRICEREPORT = _descriptor.Descriptor(
  name='UnsetPriceReport',
  full_name='price_center.UnsetPriceReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_center.UnsetPriceReport.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='price_center.UnsetPriceReport.partner_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='price_center.UnsetPriceReport.store_id', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='price_center.UnsetPriceReport.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='price_center.UnsetPriceReport.store_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_status', full_name='price_center.UnsetPriceReport.store_status', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='price_center.UnsetPriceReport.product_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='price_center.UnsetPriceReport.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='price_center.UnsetPriceReport.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='price_center.UnsetPriceReport.product_status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calc_time', full_name='price_center.UnsetPriceReport.calc_time', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='price_center.UnsetPriceReport.created_at', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='price_center.UnsetPriceReport.updated_at', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_deleted', full_name='price_center.UnsetPriceReport.is_deleted', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='price_center.UnsetPriceReport.spec', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_id', full_name='price_center.UnsetPriceReport.purchase_unit_id', index=15,
      number=16, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_name', full_name='price_center.UnsetPriceReport.purchase_unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='price_center.UnsetPriceReport.purchase_price', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distri_type', full_name='price_center.UnsetPriceReport.distri_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5313,
  serialized_end=5807,
)


_GETUNSETPRICEREPORTREQUEST = _descriptor.Descriptor(
  name='GetUnsetPriceReportRequest',
  full_name='price_center.GetUnsetPriceReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='item_ids', full_name='price_center.GetUnsetPriceReportRequest.item_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='price_center.GetUnsetPriceReportRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_status', full_name='price_center.GetUnsetPriceReportRequest.store_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='price_center.GetUnsetPriceReportRequest.product_status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distri_type', full_name='price_center.GetUnsetPriceReportRequest.distri_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='price_center.GetUnsetPriceReportRequest.page', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5810,
  serialized_end=5976,
)


_GETUNSETPRICEREPORTRESPONSE = _descriptor.Descriptor(
  name='GetUnsetPriceReportResponse',
  full_name='price_center.GetUnsetPriceReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.GetUnsetPriceReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.GetUnsetPriceReportResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5978,
  serialized_end=6068,
)


_SYNCPRICECHANGELOGREQUEST = _descriptor.Descriptor(
  name='syncPriceChangeLogRequest',
  full_name='price_center.syncPriceChangeLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='price_center.syncPriceChangeLogRequest.partner_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='price_center.syncPriceChangeLogRequest.trace_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='group_id', full_name='price_center.syncPriceChangeLogRequest.group_id', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6070,
  serialized_end=6153,
)


_GETPRICEGROUPTAGREQUEST = _descriptor.Descriptor(
  name='GetPriceGroupTagRequest',
  full_name='price_center.GetPriceGroupTagRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_ids', full_name='price_center.GetPriceGroupTagRequest.tag_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='price_center.GetPriceGroupTagRequest.search', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='price_center.GetPriceGroupTagRequest.page', index=2,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6155,
  serialized_end=6247,
)


_PRICEGROUPTAG = _descriptor.Descriptor(
  name='PriceGroupTag',
  full_name='price_center.PriceGroupTag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_center.PriceGroupTag.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='price_center.PriceGroupTag.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='price_center.PriceGroupTag.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='price_center.PriceGroupTag.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_groups', full_name='price_center.PriceGroupTag.price_groups', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='price_center.PriceGroupTag.created', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='price_center.PriceGroupTag.updated', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_center.PriceGroupTag.updated_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='price_center.PriceGroupTag.updated_by', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6250,
  serialized_end=6449,
)


_GETPRICEGROUPTAGRESPONSE = _descriptor.Descriptor(
  name='GetPriceGroupTagResponse',
  full_name='price_center.GetPriceGroupTagResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.GetPriceGroupTagResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.GetPriceGroupTagResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6451,
  serialized_end=6535,
)


_GETPGPTAGCATEGORYREQUEST = _descriptor.Descriptor(
  name='GetPgpTagCategoryRequest',
  full_name='price_center.GetPgpTagCategoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='price_center.GetPgpTagCategoryRequest.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='price_center.GetPgpTagCategoryRequest.search', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='price_center.GetPgpTagCategoryRequest.page', index=2,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6537,
  serialized_end=6626,
)


_PGPTAGCATEGORY = _descriptor.Descriptor(
  name='PgpTagCategory',
  full_name='price_center.PgpTagCategory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_center.PgpTagCategory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='price_center.PgpTagCategory.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='price_center.PgpTagCategory.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_count', full_name='price_center.PgpTagCategory.tag_count', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6628,
  serialized_end=6709,
)


_GETPGPTAGCATEGORYRESPONSE = _descriptor.Descriptor(
  name='GetPgpTagCategoryResponse',
  full_name='price_center.GetPgpTagCategoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_center.GetPgpTagCategoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.GetPgpTagCategoryResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6711,
  serialized_end=6797,
)


_PRICEGROUPPRODUCTTAG = _descriptor.Descriptor(
  name='PriceGroupProductTag',
  full_name='price_center.PriceGroupProductTag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_center.PriceGroupProductTag.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='price_center.PriceGroupProductTag.code', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='price_center.PriceGroupProductTag.name', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pgp_tag_category_id', full_name='price_center.PriceGroupProductTag.pgp_tag_category_id', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_ids', full_name='price_center.PriceGroupProductTag.product_category_ids', index=4,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='price_center.PriceGroupProductTag.product_ids', index=5,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_center.PriceGroupProductTag.updated_name', index=6,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='price_center.PriceGroupProductTag.updated', index=7,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6800,
  serialized_end=7009,
)


_CREATEPRICEGROUPPRODUCTTAGREQUEST = _descriptor.Descriptor(
  name='CreatePriceGroupProductTagRequest',
  full_name='price_center.CreatePriceGroupProductTagRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='price_group_product_tag', full_name='price_center.CreatePriceGroupProductTagRequest.price_group_product_tag', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7011,
  serialized_end=7115,
)


_CREATEPRICEGROUPPRODUCTTAGRESPONSE = _descriptor.Descriptor(
  name='CreatePriceGroupProductTagResponse',
  full_name='price_center.CreatePriceGroupProductTagResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='price_center.CreatePriceGroupProductTagResponse.code', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='price_center.CreatePriceGroupProductTagResponse.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_center.CreatePriceGroupProductTagResponse.total', index=2,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7117,
  serialized_end=7222,
)


_DELETEPRICEGROUPPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeletePriceGroupProductRequest',
  full_name='price_center.DeletePriceGroupProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_center.DeletePriceGroupProductRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7224,
  serialized_end=7268,
)


_TIMESELECTOR = _descriptor.Descriptor(
  name='TimeSelector',
  full_name='price_center.TimeSelector',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_time', full_name='price_center.TimeSelector.start_time', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='price_center.TimeSelector.end_time', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7270,
  serialized_end=7378,
)


_PRICECHANGELOGGROUP_PRICECHANGELOGACTION = _descriptor.Descriptor(
  name='PriceChangeLogAction',
  full_name='price_center.PriceChangeLogGroup.PriceChangeLogAction',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='updated', full_name='price_center.PriceChangeLogGroup.PriceChangeLogAction.updated', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='price_center.PriceChangeLogGroup.PriceChangeLogAction.deleted', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='added', full_name='price_center.PriceChangeLogGroup.PriceChangeLogAction.added', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7703,
  serialized_end=7864,
)

_PRICECHANGELOGGROUP = _descriptor.Descriptor(
  name='PriceChangeLogGroup',
  full_name='price_center.PriceChangeLogGroup',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='price_center.PriceChangeLogGroup.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='price_center.PriceChangeLogGroup.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='price_center.PriceChangeLogGroup.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_code', full_name='price_center.PriceChangeLogGroup.price_group_code', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_name', full_name='price_center.PriceChangeLogGroup.price_group_name', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_group_id', full_name='price_center.PriceChangeLogGroup.price_group_id', index=5,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_id', full_name='price_center.PriceChangeLogGroup.price_type_id', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_name', full_name='price_center.PriceChangeLogGroup.price_type_name', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type_code', full_name='price_center.PriceChangeLogGroup.price_type_code', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actions', full_name='price_center.PriceChangeLogGroup.actions', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='price_center.PriceChangeLogGroup.currency', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_PRICECHANGELOGGROUP_PRICECHANGELOGACTION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7381,
  serialized_end=7864,
)

_CREATEPRICEGROUPREQUEST.fields_by_name['price_group'].message_type = _PRICEGROUP
_CREATEPRICEGROUPREQUEST.fields_by_name['products'].message_type = _PRICEINFO
_CREATEPRICEGROUPREQUEST.fields_by_name['effective_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRICEGROUP.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPRICEGROUPRESPONSE.fields_by_name['code'].enum_type = _ERRCODE
_CREATEPRICEGROUPRESPONSE.fields_by_name['err_list'].message_type = _ERRPRODUCTPRICEINFO
_GETPRICEGROUPPRODUCTLISTREQUEST.fields_by_name['page'].message_type = _PAGE
_GETPRICEGROUPPRODUCTLISTRESPONSE.fields_by_name['rows'].message_type = _PRICEINFO
_GETPRODUCTPRICEINFOREQUEST.fields_by_name['page'].message_type = _PAGE
_GETPRODUCTPRICEINFORESPONSE_ROW.containing_type = _GETPRODUCTPRICEINFORESPONSE
_GETPRODUCTPRICEINFORESPONSE.fields_by_name['rows'].message_type = _GETPRODUCTPRICEINFORESPONSE_ROW
_UPDATEPRICEGROUPREQUEST.fields_by_name['price_group'].message_type = _PRICEGROUP
_UPDATEPRICEGROUPREQUEST.fields_by_name['products'].message_type = _PRICEINFO
_UPDATEPRICEGROUPREQUEST.fields_by_name['effective_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRICEGROUPTASKHEADERRESPONSE.fields_by_name['price_group'].message_type = _PRICEGROUP
_GETPRICEGROUPTASKDETAILREQUEST.fields_by_name['page'].message_type = _PAGE
_GETPRICEGROUPTASKDETAILRESPONSE.fields_by_name['rows'].message_type = _PRICEINFO
_GETPRICEGROUPTASKDETAILRESPONSE.fields_by_name['updated_rows'].message_type = _PRICEINFO
_GETPRICEGROUPTASKDETAILRESPONSE.fields_by_name['deleted_rows'].message_type = _PRICEINFO
_GETPRICEGROUPTASKDETAILRESPONSE.fields_by_name['added_rows'].message_type = _PRICEINFO
_GETPRICECHANGELOGREQUEST.fields_by_name['activated'].message_type = _TIMESELECTOR
_GETPRICECHANGELOGREQUEST.fields_by_name['page'].message_type = _PAGE
_GETPRICECHANGELOGRESPONSE.fields_by_name['rows'].message_type = _PRICECHANGELOG
_GETPRICECHANGELOGRESPONSE.fields_by_name['group_rows'].message_type = _PRICECHANGELOGGROUP
_GETPRODUCTPRICEAGENTREQUEST.fields_by_name['page'].message_type = _PAGE
_GETPRODUCTPRICEAGENTRESPONSE_PRICEAGENT.containing_type = _GETPRODUCTPRICEAGENTRESPONSE
_GETPRODUCTPRICEAGENTRESPONSE.fields_by_name['rows'].message_type = _GETPRODUCTPRICEAGENTRESPONSE_PRICEAGENT
_PRICEGROUPDATACHECKREQUEST.fields_by_name['price_group'].message_type = _PRICEGROUP
_PRICEGROUPDATACHECKREQUEST.fields_by_name['products'].message_type = _PRICEINFO
_PRICEGROUPDATACHECKRESPONSE.fields_by_name['rows'].message_type = _ERRPRODUCTPRICEINFO
_UNSETPRICEREPORT.fields_by_name['calc_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UNSETPRICEREPORT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UNSETPRICEREPORT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNSETPRICEREPORTREQUEST.fields_by_name['page'].message_type = _PAGE
_GETUNSETPRICEREPORTRESPONSE.fields_by_name['rows'].message_type = _UNSETPRICEREPORT
_GETPRICEGROUPTAGREQUEST.fields_by_name['page'].message_type = _PAGE
_PRICEGROUPTAG.fields_by_name['price_groups'].message_type = _PRICEGROUP
_GETPRICEGROUPTAGRESPONSE.fields_by_name['rows'].message_type = _PRICEGROUPTAG
_GETPGPTAGCATEGORYREQUEST.fields_by_name['page'].message_type = _PAGE
_GETPGPTAGCATEGORYRESPONSE.fields_by_name['rows'].message_type = _PGPTAGCATEGORY
_PRICEGROUPPRODUCTTAG.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPRICEGROUPPRODUCTTAGREQUEST.fields_by_name['price_group_product_tag'].message_type = _PRICEGROUPPRODUCTTAG
_CREATEPRICEGROUPPRODUCTTAGRESPONSE.fields_by_name['code'].enum_type = _ERRCODE
_TIMESELECTOR.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TIMESELECTOR.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRICECHANGELOGGROUP_PRICECHANGELOGACTION.fields_by_name['updated'].message_type = _PRICECHANGELOG
_PRICECHANGELOGGROUP_PRICECHANGELOGACTION.fields_by_name['deleted'].message_type = _PRICECHANGELOG
_PRICECHANGELOGGROUP_PRICECHANGELOGACTION.fields_by_name['added'].message_type = _PRICECHANGELOG
_PRICECHANGELOGGROUP_PRICECHANGELOGACTION.containing_type = _PRICECHANGELOGGROUP
_PRICECHANGELOGGROUP.fields_by_name['actions'].message_type = _PRICECHANGELOGGROUP_PRICECHANGELOGACTION
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['Page'] = _PAGE
DESCRIPTOR.message_types_by_name['CreatePriceGroupRequest'] = _CREATEPRICEGROUPREQUEST
DESCRIPTOR.message_types_by_name['PriceGroup'] = _PRICEGROUP
DESCRIPTOR.message_types_by_name['ErrProductPriceInfo'] = _ERRPRODUCTPRICEINFO
DESCRIPTOR.message_types_by_name['CreatePriceGroupResponse'] = _CREATEPRICEGROUPRESPONSE
DESCRIPTOR.message_types_by_name['Relation'] = _RELATION
DESCRIPTOR.message_types_by_name['PriceInfo'] = _PRICEINFO
DESCRIPTOR.message_types_by_name['GetPriceGroupProductListRequest'] = _GETPRICEGROUPPRODUCTLISTREQUEST
DESCRIPTOR.message_types_by_name['GetPriceGroupProductListResponse'] = _GETPRICEGROUPPRODUCTLISTRESPONSE
DESCRIPTOR.message_types_by_name['GetProductPriceInfoRequest'] = _GETPRODUCTPRICEINFOREQUEST
DESCRIPTOR.message_types_by_name['GetProductPriceInfoResponse'] = _GETPRODUCTPRICEINFORESPONSE
DESCRIPTOR.message_types_by_name['UpdatePriceGroupRequest'] = _UPDATEPRICEGROUPREQUEST
DESCRIPTOR.message_types_by_name['GetPriceGroupTaskHeaderRequest'] = _GETPRICEGROUPTASKHEADERREQUEST
DESCRIPTOR.message_types_by_name['GetPriceGroupTaskHeaderResponse'] = _GETPRICEGROUPTASKHEADERRESPONSE
DESCRIPTOR.message_types_by_name['GetPriceGroupTaskDetailRequest'] = _GETPRICEGROUPTASKDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetPriceGroupTaskDetailResponse'] = _GETPRICEGROUPTASKDETAILRESPONSE
DESCRIPTOR.message_types_by_name['GetPriceChangeLogRequest'] = _GETPRICECHANGELOGREQUEST
DESCRIPTOR.message_types_by_name['GetPriceChangeLogResponse'] = _GETPRICECHANGELOGRESPONSE
DESCRIPTOR.message_types_by_name['PriceChangeLog'] = _PRICECHANGELOG
DESCRIPTOR.message_types_by_name['GetProductPriceAgentRequest'] = _GETPRODUCTPRICEAGENTREQUEST
DESCRIPTOR.message_types_by_name['GetProductPriceAgentResponse'] = _GETPRODUCTPRICEAGENTRESPONSE
DESCRIPTOR.message_types_by_name['DeletePriceGroupRequest'] = _DELETEPRICEGROUPREQUEST
DESCRIPTOR.message_types_by_name['PriceGroupDataCheckRequest'] = _PRICEGROUPDATACHECKREQUEST
DESCRIPTOR.message_types_by_name['PriceGroupDataCheckResponse'] = _PRICEGROUPDATACHECKRESPONSE
DESCRIPTOR.message_types_by_name['UnsetPriceReport'] = _UNSETPRICEREPORT
DESCRIPTOR.message_types_by_name['GetUnsetPriceReportRequest'] = _GETUNSETPRICEREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetUnsetPriceReportResponse'] = _GETUNSETPRICEREPORTRESPONSE
DESCRIPTOR.message_types_by_name['syncPriceChangeLogRequest'] = _SYNCPRICECHANGELOGREQUEST
DESCRIPTOR.message_types_by_name['GetPriceGroupTagRequest'] = _GETPRICEGROUPTAGREQUEST
DESCRIPTOR.message_types_by_name['PriceGroupTag'] = _PRICEGROUPTAG
DESCRIPTOR.message_types_by_name['GetPriceGroupTagResponse'] = _GETPRICEGROUPTAGRESPONSE
DESCRIPTOR.message_types_by_name['GetPgpTagCategoryRequest'] = _GETPGPTAGCATEGORYREQUEST
DESCRIPTOR.message_types_by_name['PgpTagCategory'] = _PGPTAGCATEGORY
DESCRIPTOR.message_types_by_name['GetPgpTagCategoryResponse'] = _GETPGPTAGCATEGORYRESPONSE
DESCRIPTOR.message_types_by_name['PriceGroupProductTag'] = _PRICEGROUPPRODUCTTAG
DESCRIPTOR.message_types_by_name['CreatePriceGroupProductTagRequest'] = _CREATEPRICEGROUPPRODUCTTAGREQUEST
DESCRIPTOR.message_types_by_name['CreatePriceGroupProductTagResponse'] = _CREATEPRICEGROUPPRODUCTTAGRESPONSE
DESCRIPTOR.message_types_by_name['DeletePriceGroupProductRequest'] = _DELETEPRICEGROUPPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['TimeSelector'] = _TIMESELECTOR
DESCRIPTOR.message_types_by_name['PriceChangeLogGroup'] = _PRICECHANGELOGGROUP
DESCRIPTOR.enum_types_by_name['ErrCode'] = _ERRCODE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.Response)
  ))
_sym_db.RegisterMessage(Response)

Page = _reflection.GeneratedProtocolMessageType('Page', (_message.Message,), dict(
  DESCRIPTOR = _PAGE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.Page)
  ))
_sym_db.RegisterMessage(Page)

CreatePriceGroupRequest = _reflection.GeneratedProtocolMessageType('CreatePriceGroupRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPRICEGROUPREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.CreatePriceGroupRequest)
  ))
_sym_db.RegisterMessage(CreatePriceGroupRequest)

PriceGroup = _reflection.GeneratedProtocolMessageType('PriceGroup', (_message.Message,), dict(
  DESCRIPTOR = _PRICEGROUP,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PriceGroup)
  ))
_sym_db.RegisterMessage(PriceGroup)

ErrProductPriceInfo = _reflection.GeneratedProtocolMessageType('ErrProductPriceInfo', (_message.Message,), dict(
  DESCRIPTOR = _ERRPRODUCTPRICEINFO,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.ErrProductPriceInfo)
  ))
_sym_db.RegisterMessage(ErrProductPriceInfo)

CreatePriceGroupResponse = _reflection.GeneratedProtocolMessageType('CreatePriceGroupResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPRICEGROUPRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.CreatePriceGroupResponse)
  ))
_sym_db.RegisterMessage(CreatePriceGroupResponse)

Relation = _reflection.GeneratedProtocolMessageType('Relation', (_message.Message,), dict(
  DESCRIPTOR = _RELATION,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.Relation)
  ))
_sym_db.RegisterMessage(Relation)

PriceInfo = _reflection.GeneratedProtocolMessageType('PriceInfo', (_message.Message,), dict(
  DESCRIPTOR = _PRICEINFO,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PriceInfo)
  ))
_sym_db.RegisterMessage(PriceInfo)

GetPriceGroupProductListRequest = _reflection.GeneratedProtocolMessageType('GetPriceGroupProductListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEGROUPPRODUCTLISTREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceGroupProductListRequest)
  ))
_sym_db.RegisterMessage(GetPriceGroupProductListRequest)

GetPriceGroupProductListResponse = _reflection.GeneratedProtocolMessageType('GetPriceGroupProductListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEGROUPPRODUCTLISTRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceGroupProductListResponse)
  ))
_sym_db.RegisterMessage(GetPriceGroupProductListResponse)

GetProductPriceInfoRequest = _reflection.GeneratedProtocolMessageType('GetProductPriceInfoRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTPRICEINFOREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetProductPriceInfoRequest)
  ))
_sym_db.RegisterMessage(GetProductPriceInfoRequest)

GetProductPriceInfoResponse = _reflection.GeneratedProtocolMessageType('GetProductPriceInfoResponse', (_message.Message,), dict(

  row = _reflection.GeneratedProtocolMessageType('row', (_message.Message,), dict(
    DESCRIPTOR = _GETPRODUCTPRICEINFORESPONSE_ROW,
    __module__ = 'price_center.price_center_pb2'
    # @@protoc_insertion_point(class_scope:price_center.GetProductPriceInfoResponse.row)
    ))
  ,
  DESCRIPTOR = _GETPRODUCTPRICEINFORESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetProductPriceInfoResponse)
  ))
_sym_db.RegisterMessage(GetProductPriceInfoResponse)
_sym_db.RegisterMessage(GetProductPriceInfoResponse.row)

UpdatePriceGroupRequest = _reflection.GeneratedProtocolMessageType('UpdatePriceGroupRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRICEGROUPREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.UpdatePriceGroupRequest)
  ))
_sym_db.RegisterMessage(UpdatePriceGroupRequest)

GetPriceGroupTaskHeaderRequest = _reflection.GeneratedProtocolMessageType('GetPriceGroupTaskHeaderRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEGROUPTASKHEADERREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceGroupTaskHeaderRequest)
  ))
_sym_db.RegisterMessage(GetPriceGroupTaskHeaderRequest)

GetPriceGroupTaskHeaderResponse = _reflection.GeneratedProtocolMessageType('GetPriceGroupTaskHeaderResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEGROUPTASKHEADERRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceGroupTaskHeaderResponse)
  ))
_sym_db.RegisterMessage(GetPriceGroupTaskHeaderResponse)

GetPriceGroupTaskDetailRequest = _reflection.GeneratedProtocolMessageType('GetPriceGroupTaskDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEGROUPTASKDETAILREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceGroupTaskDetailRequest)
  ))
_sym_db.RegisterMessage(GetPriceGroupTaskDetailRequest)

GetPriceGroupTaskDetailResponse = _reflection.GeneratedProtocolMessageType('GetPriceGroupTaskDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEGROUPTASKDETAILRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceGroupTaskDetailResponse)
  ))
_sym_db.RegisterMessage(GetPriceGroupTaskDetailResponse)

GetPriceChangeLogRequest = _reflection.GeneratedProtocolMessageType('GetPriceChangeLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICECHANGELOGREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceChangeLogRequest)
  ))
_sym_db.RegisterMessage(GetPriceChangeLogRequest)

GetPriceChangeLogResponse = _reflection.GeneratedProtocolMessageType('GetPriceChangeLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICECHANGELOGRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceChangeLogResponse)
  ))
_sym_db.RegisterMessage(GetPriceChangeLogResponse)

PriceChangeLog = _reflection.GeneratedProtocolMessageType('PriceChangeLog', (_message.Message,), dict(
  DESCRIPTOR = _PRICECHANGELOG,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PriceChangeLog)
  ))
_sym_db.RegisterMessage(PriceChangeLog)

GetProductPriceAgentRequest = _reflection.GeneratedProtocolMessageType('GetProductPriceAgentRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTPRICEAGENTREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetProductPriceAgentRequest)
  ))
_sym_db.RegisterMessage(GetProductPriceAgentRequest)

GetProductPriceAgentResponse = _reflection.GeneratedProtocolMessageType('GetProductPriceAgentResponse', (_message.Message,), dict(

  PriceAgent = _reflection.GeneratedProtocolMessageType('PriceAgent', (_message.Message,), dict(
    DESCRIPTOR = _GETPRODUCTPRICEAGENTRESPONSE_PRICEAGENT,
    __module__ = 'price_center.price_center_pb2'
    # @@protoc_insertion_point(class_scope:price_center.GetProductPriceAgentResponse.PriceAgent)
    ))
  ,
  DESCRIPTOR = _GETPRODUCTPRICEAGENTRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetProductPriceAgentResponse)
  ))
_sym_db.RegisterMessage(GetProductPriceAgentResponse)
_sym_db.RegisterMessage(GetProductPriceAgentResponse.PriceAgent)

DeletePriceGroupRequest = _reflection.GeneratedProtocolMessageType('DeletePriceGroupRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEPRICEGROUPREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.DeletePriceGroupRequest)
  ))
_sym_db.RegisterMessage(DeletePriceGroupRequest)

PriceGroupDataCheckRequest = _reflection.GeneratedProtocolMessageType('PriceGroupDataCheckRequest', (_message.Message,), dict(
  DESCRIPTOR = _PRICEGROUPDATACHECKREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PriceGroupDataCheckRequest)
  ))
_sym_db.RegisterMessage(PriceGroupDataCheckRequest)

PriceGroupDataCheckResponse = _reflection.GeneratedProtocolMessageType('PriceGroupDataCheckResponse', (_message.Message,), dict(
  DESCRIPTOR = _PRICEGROUPDATACHECKRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PriceGroupDataCheckResponse)
  ))
_sym_db.RegisterMessage(PriceGroupDataCheckResponse)

UnsetPriceReport = _reflection.GeneratedProtocolMessageType('UnsetPriceReport', (_message.Message,), dict(
  DESCRIPTOR = _UNSETPRICEREPORT,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.UnsetPriceReport)
  ))
_sym_db.RegisterMessage(UnsetPriceReport)

GetUnsetPriceReportRequest = _reflection.GeneratedProtocolMessageType('GetUnsetPriceReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETUNSETPRICEREPORTREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetUnsetPriceReportRequest)
  ))
_sym_db.RegisterMessage(GetUnsetPriceReportRequest)

GetUnsetPriceReportResponse = _reflection.GeneratedProtocolMessageType('GetUnsetPriceReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETUNSETPRICEREPORTRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetUnsetPriceReportResponse)
  ))
_sym_db.RegisterMessage(GetUnsetPriceReportResponse)

syncPriceChangeLogRequest = _reflection.GeneratedProtocolMessageType('syncPriceChangeLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _SYNCPRICECHANGELOGREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.syncPriceChangeLogRequest)
  ))
_sym_db.RegisterMessage(syncPriceChangeLogRequest)

GetPriceGroupTagRequest = _reflection.GeneratedProtocolMessageType('GetPriceGroupTagRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEGROUPTAGREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceGroupTagRequest)
  ))
_sym_db.RegisterMessage(GetPriceGroupTagRequest)

PriceGroupTag = _reflection.GeneratedProtocolMessageType('PriceGroupTag', (_message.Message,), dict(
  DESCRIPTOR = _PRICEGROUPTAG,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PriceGroupTag)
  ))
_sym_db.RegisterMessage(PriceGroupTag)

GetPriceGroupTagResponse = _reflection.GeneratedProtocolMessageType('GetPriceGroupTagResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEGROUPTAGRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPriceGroupTagResponse)
  ))
_sym_db.RegisterMessage(GetPriceGroupTagResponse)

GetPgpTagCategoryRequest = _reflection.GeneratedProtocolMessageType('GetPgpTagCategoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPGPTAGCATEGORYREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPgpTagCategoryRequest)
  ))
_sym_db.RegisterMessage(GetPgpTagCategoryRequest)

PgpTagCategory = _reflection.GeneratedProtocolMessageType('PgpTagCategory', (_message.Message,), dict(
  DESCRIPTOR = _PGPTAGCATEGORY,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PgpTagCategory)
  ))
_sym_db.RegisterMessage(PgpTagCategory)

GetPgpTagCategoryResponse = _reflection.GeneratedProtocolMessageType('GetPgpTagCategoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPGPTAGCATEGORYRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.GetPgpTagCategoryResponse)
  ))
_sym_db.RegisterMessage(GetPgpTagCategoryResponse)

PriceGroupProductTag = _reflection.GeneratedProtocolMessageType('PriceGroupProductTag', (_message.Message,), dict(
  DESCRIPTOR = _PRICEGROUPPRODUCTTAG,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PriceGroupProductTag)
  ))
_sym_db.RegisterMessage(PriceGroupProductTag)

CreatePriceGroupProductTagRequest = _reflection.GeneratedProtocolMessageType('CreatePriceGroupProductTagRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPRICEGROUPPRODUCTTAGREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.CreatePriceGroupProductTagRequest)
  ))
_sym_db.RegisterMessage(CreatePriceGroupProductTagRequest)

CreatePriceGroupProductTagResponse = _reflection.GeneratedProtocolMessageType('CreatePriceGroupProductTagResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPRICEGROUPPRODUCTTAGRESPONSE,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.CreatePriceGroupProductTagResponse)
  ))
_sym_db.RegisterMessage(CreatePriceGroupProductTagResponse)

DeletePriceGroupProductRequest = _reflection.GeneratedProtocolMessageType('DeletePriceGroupProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEPRICEGROUPPRODUCTREQUEST,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.DeletePriceGroupProductRequest)
  ))
_sym_db.RegisterMessage(DeletePriceGroupProductRequest)

TimeSelector = _reflection.GeneratedProtocolMessageType('TimeSelector', (_message.Message,), dict(
  DESCRIPTOR = _TIMESELECTOR,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.TimeSelector)
  ))
_sym_db.RegisterMessage(TimeSelector)

PriceChangeLogGroup = _reflection.GeneratedProtocolMessageType('PriceChangeLogGroup', (_message.Message,), dict(

  PriceChangeLogAction = _reflection.GeneratedProtocolMessageType('PriceChangeLogAction', (_message.Message,), dict(
    DESCRIPTOR = _PRICECHANGELOGGROUP_PRICECHANGELOGACTION,
    __module__ = 'price_center.price_center_pb2'
    # @@protoc_insertion_point(class_scope:price_center.PriceChangeLogGroup.PriceChangeLogAction)
    ))
  ,
  DESCRIPTOR = _PRICECHANGELOGGROUP,
  __module__ = 'price_center.price_center_pb2'
  # @@protoc_insertion_point(class_scope:price_center.PriceChangeLogGroup)
  ))
_sym_db.RegisterMessage(PriceChangeLogGroup)
_sym_db.RegisterMessage(PriceChangeLogGroup.PriceChangeLogAction)


DESCRIPTOR._options = None

_PRICECENTERSERVICE = _descriptor.ServiceDescriptor(
  name='PriceCenterService',
  full_name='price_center.PriceCenterService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7977,
  serialized_end=11161,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='price_center.PriceCenterService.Ping',
    index=0,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='CreatePriceGroup',
    full_name='price_center.PriceCenterService.CreatePriceGroup',
    index=1,
    containing_service=None,
    input_type=_CREATEPRICEGROUPREQUEST,
    output_type=_CREATEPRICEGROUPRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v1/price-center/create-price-group:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPriceGroupProductList',
    full_name='price_center.PriceCenterService.GetPriceGroupProductList',
    index=2,
    containing_service=None,
    input_type=_GETPRICEGROUPPRODUCTLISTREQUEST,
    output_type=_GETPRICEGROUPPRODUCTLISTRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\"./api/v1/price-center/get-price-group-item-list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductPriceInfo',
    full_name='price_center.PriceCenterService.GetProductPriceInfo',
    index=3,
    containing_service=None,
    input_type=_GETPRODUCTPRICEINFOREQUEST,
    output_type=_GETPRODUCTPRICEINFORESPONSE,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v1/price-center/get-item-price-info:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdatePriceGroup',
    full_name='price_center.PriceCenterService.UpdatePriceGroup',
    index=4,
    containing_service=None,
    input_type=_UPDATEPRICEGROUPREQUEST,
    output_type=_CREATEPRICEGROUPRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v1/price-center/update-price-group:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPriceGroupTaskHeader',
    full_name='price_center.PriceCenterService.GetPriceGroupTaskHeader',
    index=5,
    containing_service=None,
    input_type=_GETPRICEGROUPTASKHEADERREQUEST,
    output_type=_GETPRICEGROUPTASKHEADERRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\"0/api/v1/price-center/get-price-group-task-header:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPriceGroupTaskDetail',
    full_name='price_center.PriceCenterService.GetPriceGroupTaskDetail',
    index=6,
    containing_service=None,
    input_type=_GETPRICEGROUPTASKDETAILREQUEST,
    output_type=_GETPRICEGROUPTASKDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\"0/api/v1/price-center/get-price-group-task-detail:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPriceChangeLog',
    full_name='price_center.PriceCenterService.GetPriceChangeLog',
    index=7,
    containing_service=None,
    input_type=_GETPRICECHANGELOGREQUEST,
    output_type=_GETPRICECHANGELOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v1/price-center/get-price-change-log:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductPriceAgent',
    full_name='price_center.PriceCenterService.GetProductPriceAgent',
    index=8,
    containing_service=None,
    input_type=_GETPRODUCTPRICEAGENTREQUEST,
    output_type=_GETPRODUCTPRICEAGENTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v1/price-center/get-item-price-agent:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeletePriceGroup',
    full_name='price_center.PriceCenterService.DeletePriceGroup',
    index=9,
    containing_service=None,
    input_type=_DELETEPRICEGROUPREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v1/price-center/delete-price-group:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='PriceGroupDataCheck',
    full_name='price_center.PriceCenterService.PriceGroupDataCheck',
    index=10,
    containing_service=None,
    input_type=_PRICEGROUPDATACHECKREQUEST,
    output_type=_PRICEGROUPDATACHECKRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\",/api/v1/price-center/create-check-price-task:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetUnsetPriceReport',
    full_name='price_center.PriceCenterService.GetUnsetPriceReport',
    index=11,
    containing_service=None,
    input_type=_GETUNSETPRICEREPORTREQUEST,
    output_type=_GETUNSETPRICEREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\"+/api/v1/price-center/get-unset-price-report:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SyncPriceChangeLog',
    full_name='price_center.PriceCenterService.SyncPriceChangeLog',
    index=12,
    containing_service=None,
    input_type=_SYNCPRICECHANGELOGREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v1/price-center/change_log/sync:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPriceGroupTag',
    full_name='price_center.PriceCenterService.GetPriceGroupTag',
    index=13,
    containing_service=None,
    input_type=_GETPRICEGROUPTAGREQUEST,
    output_type=_GETPRICEGROUPTAGRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v1/price-center/get-price-group-tag:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeletePriceGroupTag',
    full_name='price_center.PriceCenterService.DeletePriceGroupTag',
    index=14,
    containing_service=None,
    input_type=_DELETEPRICEGROUPREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\0020\"+/api/v1/price-center/delete-price-group-tag:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPgpTagCategory',
    full_name='price_center.PriceCenterService.GetPgpTagCategory',
    index=15,
    containing_service=None,
    input_type=_GETPGPTAGCATEGORYREQUEST,
    output_type=_GETPGPTAGCATEGORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v1/price-center/get-pgp-tag-category:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeletePgpTagCategory',
    full_name='price_center.PriceCenterService.DeletePgpTagCategory',
    index=16,
    containing_service=None,
    input_type=_DELETEPRICEGROUPREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002A\"</api/v1/price-center/delete-price-group-product-tag-category:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatePriceGroupProductTag',
    full_name='price_center.PriceCenterService.CreatePriceGroupProductTag',
    index=17,
    containing_service=None,
    input_type=_CREATEPRICEGROUPPRODUCTTAGREQUEST,
    output_type=_CREATEPRICEGROUPPRODUCTTAGRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v1/price-center/create-price-group-product-tag:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdatePriceGroupProductTag',
    full_name='price_center.PriceCenterService.UpdatePriceGroupProductTag',
    index=18,
    containing_service=None,
    input_type=_CREATEPRICEGROUPPRODUCTTAGREQUEST,
    output_type=_CREATEPRICEGROUPPRODUCTTAGRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v1/price-center/update-price-group-product-tag:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeletePriceGroupProductTag',
    full_name='price_center.PriceCenterService.DeletePriceGroupProductTag',
    index=19,
    containing_service=None,
    input_type=_DELETEPRICEGROUPPRODUCTREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v1/price-center/delete-price-group-product-tag:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_PRICECENTERSERVICE)

DESCRIPTOR.services_by_name['PriceCenterService'] = _PRICECENTERSERVICE

# @@protoc_insertion_point(module_scope)
