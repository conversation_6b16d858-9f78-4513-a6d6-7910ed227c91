syntax = "proto3";
package price_center;
option go_package = "./price_center";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
//import "gitlab.hexcloud.cn/saas/auth-golang/protoc-gen-hex-auth/annotations/annotations.proto";


service PriceCenterService {
  rpc Ping(Request)returns(Response){}

  // 创建价格组
  rpc CreatePriceGroup(CreatePriceGroupRequest)returns(CreatePriceGroupResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/create-price-group"
      body: "*"
    };
  }
  // 查询价格组商品明细
  rpc GetPriceGroupProductList(GetPriceGroupProductListRequest)returns(GetPriceGroupProductListResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-price-group-item-list"
      body: "*"
    };
  }

  // 价格中心查询商品价格信息
  rpc GetProductPriceInfo(GetProductPriceInfoRequest)returns(GetProductPriceInfoResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-item-price-info"
      body: "*"
    };
//    option (annotations.auth) = {
//      domain: "boh.frs_supply_config"
//      action: "boh.frs_supply_config.price_center.view"
//    };
  }

  // 更新价格组
  rpc UpdatePriceGroup(UpdatePriceGroupRequest)returns(CreatePriceGroupResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/update-price-group"
      body: "*"
    };
  }

  // 查询计划header
  rpc GetPriceGroupTaskHeader(GetPriceGroupTaskHeaderRequest)returns(GetPriceGroupTaskHeaderResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-price-group-task-header"
      body: "*"
    };
  }

  // 查询计划明细
  rpc GetPriceGroupTaskDetail(GetPriceGroupTaskDetailRequest)returns(GetPriceGroupTaskDetailResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-price-group-task-detail"
      body: "*"
    };
  }

  // 改价记录-查询
  rpc GetPriceChangeLog(GetPriceChangeLogRequest)returns(GetPriceChangeLogResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-price-change-log"
      body: "*"
    };
//    option (annotations.auth) = {
//      domain: "boh.frs_supply_config"
//      action: "boh.frs_supply_config.price_center.view"
//    };
  }

  // 商品价格查询(其他业务调用)
  rpc GetProductPriceAgent(GetProductPriceAgentRequest)returns(GetProductPriceAgentResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-item-price-agent"
      body: "*"
    };
  }

  // 删除价格组
  rpc DeletePriceGroup(DeletePriceGroupRequest)returns(Response){
    option (google.api.http) = {
      post: "/api/v1/price-center/delete-price-group"
      body: "*"
    };
  }

  // 创建/新建check priceGroup
  rpc PriceGroupDataCheck(PriceGroupDataCheckRequest)returns(PriceGroupDataCheckResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/create-check-price-task"
      body: "*"
    };
  }

  // 未定价商品分页
  rpc GetUnsetPriceReport(GetUnsetPriceReportRequest)returns(GetUnsetPriceReportResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-unset-price-report"
      body: "*"
    };
  }

  // 同步改价记录
  rpc SyncPriceChangeLog(syncPriceChangeLogRequest)returns(Response){
    option (google.api.http) = {
      post: "/api/v1/price-center/change_log/sync"
      body: "*"
    };
  }

  // 查询价格组标签
rpc GetPriceGroupTag(GetPriceGroupTagRequest)returns(GetPriceGroupTagResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-price-group-tag"
      body: "*"
    };
  }
 // 删除价格组标签
  rpc DeletePriceGroupTag(DeletePriceGroupRequest)returns(Response){
    option (google.api.http) = {
      post: "/api/v1/price-center/delete-price-group-tag"
      body: "*"
    };
  }

  // 查询价格组商品标签类别
  rpc GetPgpTagCategory(GetPgpTagCategoryRequest)returns(GetPgpTagCategoryResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/get-pgp-tag-category"
      body: "*"
    };
  }

  //删除价格组商品标签类别
  rpc DeletePgpTagCategory(DeletePriceGroupRequest)returns(Response){
    option (google.api.http) = {
      post: "/api/v1/price-center/delete-price-group-product-tag-category"
      body: "*"
    };
  }

  // 创建价格组商品标签
  rpc CreatePriceGroupProductTag(CreatePriceGroupProductTagRequest)returns(CreatePriceGroupProductTagResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/create-price-group-product-tag"
      body: "*"
    };
  }

  //更新价格组商品标签
  rpc UpdatePriceGroupProductTag(CreatePriceGroupProductTagRequest)returns(CreatePriceGroupProductTagResponse){
    option (google.api.http) = {
      post: "/api/v1/price-center/update-price-group-product-tag"
      body: "*"
    };
  }
  // 删除价格组商品标签
  rpc DeletePriceGroupProductTag(DeletePriceGroupProductRequest)returns(Response){
    option (google.api.http) = {
      post: "/api/v1/price-center/delete-price-group-product-tag"
      body: "*"
    };
  }



}



message Request {
  string ping = 1;
}
message  Response{
  uint32 code = 1;
  string message = 2;
}

message Page {
  string order = 1;
  string sort = 2;
  int32 limit = 3;
  int32 offset = 4;
}

enum ErrCode {
  Success = 0;
  DuplicateCode = 1;
  DuplicateName = 2;
  DuplicateProduct = 3;
  DuplicateCodeAndName = 4;
}

message CreatePriceGroupRequest{
  // 价格组
  // @gotags: validate:"required"
  PriceGroup price_group = 1;
  // @gotags: validate:"required,unique=ItemId,dive"
  repeated PriceInfo products = 2;
  bool immediate = 3;
  // 生效时间效时间
  // @gotags: validate:"required_without=Immediate"
  google.protobuf.Timestamp effective_time = 4;
}
message PriceGroup{
  // 价格组id
  uint64 id = 1;
  // 价格组名称
  // @gotags: validate:"required"
  string name = 2;
  // @gotags: validate:"required"
  // 价格组编号
  string code = 3;
  // 区域id
  // @gotags: validate:"required_without=StoreIds"
  repeated string region_ids = 4;
  // 门店
  // @gotags: validate:"required_without=RegionIds"
  repeated string store_ids = 5;
  // 排除门店
  repeated string exc_store_ids = 6;
  // 包含商品数量
  uint64  product_count = 7;
  // 价格组状态
  string status = 8;
  // 更新人名称
  string updated_name = 11;
  // 更新时间
  google.protobuf.Timestamp updated = 12;
  // 地理区域名称(计划查询时返回)
  repeated string region_names = 13;
  // 门店名称（计划查询时返回）
  repeated string store_names = 14;
  // 排除门店名称（计划查询时返回）
  repeated string exc_store_names = 15;
  // 排除地理区域id
  repeated string exc_region_ids = 16;
  // 排除地理区域名称
  repeated string exc_region_names = 17;
  // 标签id
  // @gotags: validate:"required"
  string tag_id = 18;
  //标签名称
  string tag_name = 19;
  // 区域类型
  // @gotags: validate:"required_with=RegionIds"
  string region_type = 20;
  // 价格类型id
  string price_type_id = 21;
  // 币种
  // @gotags: validate:"required"
  string currency = 22;
}
message ErrProductPriceInfo{
  // 商品ID
  uint64 product_id = 1;
  // 商品编码
  string product_code = 2;
  // 商品名称
  string product_name = 3;
  // 价格组id
  uint64 price_group_id = 4;
  // 价格组Code
  string price_group_code = 5;
  // 价格组Code
  string price_group_name = 6;
  // 价格类型id
  uint64 price_type_id = 7;
  // 价格类型名称
  string price_type_name = 8;
  // 价格
  string price = 9;
  // 门店名称
  string store_name = 10;
  // 门店编码
  string store_code = 11;
  // task_id
  uint64 task_id = 12;
  // task_name
  string task_name = 13;

}
message CreatePriceGroupResponse{
  // 异常code
  ErrCode code = 1;
  // 异常原因
  string message = 2;
  repeated ErrProductPriceInfo err_list = 3;
  int64 total = 4;
}


message Relation{
  // 价格组商品明细

}
message PriceInfo{
  // 商品id
  // @gotags: validate:"required"
  uint64 item_id = 1;
  // 商品编码
  string item_code = 2;
  // 商品名称
  string item_name = 3;
  // 商品规格
  string spec = 4;
  // 商品订货单位id
  uint64 order_unit_id = 5;
  // 商品订货单位名称
  string order_unit_name = 6;
  // 销项税率
  string tax_rate = 7;
  // 采购价格(预留)
  string purchase_price = 9;
  // 物流模式(预留)
  string send_type = 10;
  // 规则
  string rule = 11;
  // 价格类型ID
  // @gotags: validate:"required"
  string price_type_id = 12;
  string price_type_name = 13;
  //  价格
  // @gotags: validate:"required"
  string price = 14;
  uint64 price_group_id = 15;
  // 数据ID
  uint64 id = 16;
  // 原价格（TZ task 明细查询专用）
  string old_price = 17;
  string use_date = 18;
  bool hidden = 19;
  string takeout_price = 20;
}

message GetPriceGroupProductListRequest{
  // 订货规则ID
  repeated uint64 price_group_ids = 1;
  // 数据id
  // @gotags: validate:"required_without=PriceGroupIds"
  repeated uint64 ids = 2;
  string search = 3;
  Page page = 4;
  // 是否包含DISABLED状态的数据
  bool include_disabled = 5;
  // 价格类型ID
  repeated uint64 price_type_ids = 6;

}
message    GetPriceGroupProductListResponse{
  repeated PriceInfo rows = 1;
  int64 total = 2;
}

message GetProductPriceInfoRequest{
  // 商品ID
  repeated uint64 item_ids = 1;
  // 门店ID
  repeated uint64 store_ids = 2 ;
  // 价格类型ID
  repeated uint64 price_type_ids = 3;
  // 区域
  repeated uint64 region_ids = 4 ;
  // 区域类型
  // @gotags: validate:"required_with=RegionIds"
  string region_type = 5;
  // 分页
  // @gotags: validate:"required_without_all=StoreIds ItemIds"
  Page page = 6;
  // 是否包含计划价格
  string pending = 7;
  // 价格组商品标签id
  repeated uint64 price_group_product_tag_ids = 8;
  // 价格组模糊查询（名称/编号）
  string group_search = 9;
  //  价格组标签ID
  repeated uint64 price_group_tag_ids = 10;
}

message GetProductPriceInfoResponse{
  message row{
    // 门店ID
    uint64 store_id = 1;
    // 门店名称
    string store_name = 2;
    // 门店编码
    string store_code = 3;
    uint64 item_id = 4;
    // 商品编码
    string item_code = 5;
    // 商品名称
    string item_name = 6;
    // 商品规格
    string spec = 7;
    // 销项税率
    string tax_rate = 10;
    // 采购价格(预留)
    string purchase_price = 11;
    // 物流模式(预留)
    string send_type = 12;
    // 规则
    string rule = 13;
    uint64 price_type_id = 14;
    string price_type_name = 15;
    //  含价格
    string price = 16;
    //  未税价
    string untaxed_price = 17;
    // 价格组ID
    uint64 price_group_id = 18;
    // 价格组名称
    string price_group_name = 19;
    // 价格组code
    string price_group_code = 20;
    uint64 order_unit_id = 21;
    // 商品订货单位名称
    string order_unit_name = 22;
    // 价格组商品标签名称
    string price_group_product_tag_names = 23;
    // 更新人名称
    string updated_name = 24;
    // 更新时间
    string updated = 25;
    // 币种
    string currency = 26;
  }
  repeated row rows = 1;
  int64 total = 2;
}

message UpdatePriceGroupRequest{
  // 价格组
  // @gotags: validate:"required,dive"
  PriceGroup price_group = 1;
  // @gotags: validate:"unique=ItemId,dive"
  repeated PriceInfo products = 2;
  bool immediate = 3;
  // 删除的价格明细ID
  repeated uint64  del_item_ids = 4;
  // 生效时间效时间
  // @gotags: validate:"required_without=Immediate"
  google.protobuf.Timestamp effective_time = 5;
}

message GetPriceGroupTaskHeaderRequest{
  // 计划ID
  // @gotags: validate:"required"
  uint64 task_id = 1;
}

message GetPriceGroupTaskHeaderResponse{
  // 计划ID
  uint64 task_id = 1;
  //计划编号
  string task_code = 2;
  // 创建时间
  string created = 3;
  // 生效时间
  string effective_time = 4;
  // 价格组信息
  PriceGroup price_group = 5;
  // 更新时间
  string updated = 6;
  // 状态
  string process_status = 7;
  // 更新人名称
  string updated_name = 8;
}

message GetPriceGroupTaskDetailRequest{
  // 计划ID
  repeated uint64 task_id = 1;
  // 模糊查询
  string search = 2;
  // 分页
  Page page = 3;
  // 是否与原对比明细数据
  bool return_compare  = 4;
  // 门店ID
  // @gotags： validate:"required_without=TaskIds"
  uint64 store_id = 5;
}

message GetPriceGroupTaskDetailResponse{
  repeated PriceInfo rows = 1;
  repeated PriceInfo updated_rows = 2;
  repeated PriceInfo deleted_rows = 3;
  repeated PriceInfo added_rows = 4;
  int64 total = 5;
}

message  GetPriceChangeLogRequest{
  // 商品ID
  repeated uint64 item_ids = 1;
  // 门店ID
  repeated uint64 store_ids = 2;
  // 价格类型ID
  repeated uint64 price_type_ids = 3;
  //区域类型
  // 区域类型
  string region_type = 4;
  // 区域ID
  repeated uint64 region_ids = 5;
  // 改价时间
  TimeSelector activated = 7;
  // GroupID
  repeated uint64 group_ids = 8;
  // 价格组模糊查询（名称/编号）
  string group_search = 9;
  // 分页
  // @gotags: validate:"required"
  Page page = 10;
  // 是否返回分组数据
  bool need_group_by= 11;
}

message GetPriceChangeLogResponse{
  repeated PriceChangeLog rows = 1;
  repeated PriceChangeLogGroup group_rows = 2;
  int64 total = 3;
}

message PriceChangeLog{
  // 门店ID
  uint64 store_id = 1;
  // 门店名称
  string store_name = 2;
  // 门店编码
  string store_code = 3;
  uint64 item_id = 4;
  // 商品编码
  string item_code = 5;
  // 商品名称
  string item_name = 6;
  // 修改前价格
  uint64 price_type_id = 7;
  // 修改前价格
  string old_price = 8;
  // 修改后价格
  string new_price = 9;
  // 修改人id
  uint64 updated_by = 10;
  // 修改人name
  string updated_name = 11;
  // 修改时间
  string created = 12;
  // 生效时间
  string activated = 13;
  // 价格组编号
  string price_group_code = 14;
  // 价格组name
  string price_group_name = 15;
  // 价格组id
  uint64 price_group_id = 16;
  // 价格类型编码
  string price_type_code = 17;
  // 价格类型名称
  string price_type_name = 18;
  // 动作类型（add update delete）
  string action_type = 19;
  // 币种
  string currency = 20;
}


message GetProductPriceAgentRequest{
  // 商品ID
  repeated uint64 item_ids = 1;
  // 门店ID
  // @gotags: validate:"required"
  uint64 store_id = 2;
  // 价格类型ID
  uint64 price_type_id = 3;
  // 分页
  Page page = 5;
  // 商品编码
  repeated string item_codes = 6;
}

message GetProductPriceAgentResponse{
  message PriceAgent {
    uint64 item_id = 1;
    string item_code = 2;
    string item_name =3;
    uint64 price_type_id = 4;
    string price = 5;
    string tax_rate = 6;
    string use_date = 7;
    bool hidden = 8;
    string takeout_price = 9;
    // 币种
    string currency = 10;
  }
  repeated PriceAgent rows = 1;
  int64 total = 2;
}

message  DeletePriceGroupRequest{
  // 价格组ID
  // @gotags: validate:"required"
  uint64 id = 1;
}

message PriceGroupDataCheckRequest{
  // 价格组
  // @gotags: validate:"required"
  PriceGroup price_group = 1;
  // @gotags: validate:"required,unique=ProductId,dive"
  repeated PriceInfo products = 2;
}

message PriceGroupDataCheckResponse{
  repeated ErrProductPriceInfo rows = 1;
  int64 total = 2;
}

// 未定价商品
message UnsetPriceReport {
  // 记录ID
  int64 id = 1;
  // 租户ID
  int64 partner_id = 2;
  // 门店ID
  int64 store_id = 3;
  // 门店编码
  string store_code = 4;
  // 门店名称
  string store_name = 5;
  // 门店状态
  string store_status = 6;
  // 商品ID
  int64 product_id = 7;
  // 商品编码
  string product_code = 8;
  // 商品名称
  string product_name = 9;
  // 商品状态
  string product_status = 10;
  // 计算时间
  google.protobuf.Timestamp calc_time = 11;
  // 创建时间
  google.protobuf.Timestamp created_at = 12;
  // 更新时间
  google.protobuf.Timestamp updated_at = 13;
  // 是否删除
  int32 is_deleted = 14;
  // 商品规格
  string spec = 15;
  // 商品采购单位id
  int64  purchase_unit_id = 16;
  // 商品采购单位名称
  string purchase_unit_name = 17;
  // 采购价格
  string purchase_price = 18;
  // 物流模式
  string distri_type = 19;
}

// 未定价商品列表入参
message  GetUnsetPriceReportRequest{
  // 商品ID
  repeated uint64 item_ids = 1;
  // 门店ID
  repeated uint64 store_ids = 2;
  // 门店状态 启用ENABLED  禁用 DISABLED
  string store_status = 3;
  // 商品状态 启用ENABLED  禁用 DISABLED
  string product_status = 4;
  // 物流模式 NMD:仓库配送 PUR:供应商直送
  string distri_type = 5;
  // 分页
  // @gotags: validate:"required"
  Page page = 6;
}

// 未定价商品列表出参
message GetUnsetPriceReportResponse{
  repeated UnsetPriceReport rows = 1;
  int64 total = 2;
}

// syncPriceChangeLogRequest
message syncPriceChangeLogRequest{
  int64 partner_id = 1;
  int64 trace_id = 2;
  int64 group_id = 3;
}

message GetPriceGroupTagRequest{
  // tag-ids
 repeated uint64 tag_ids = 1;
  //search
  string search = 2;
  // 分页
  // @gotags: validate:"required"
  Page page = 5;
}

message PriceGroupTag{
  // id
  uint64 id = 1;
  //partner_id
  uint64 partner_id = 2;
  // code
  string code = 3;
  // name
  string name = 4;
  // 关联价格组
  repeated PriceGroup price_groups = 5;
  // 创建时间
  string created = 6;
  // 更新时间
  string updated = 7;
  // 更新人名称
  string updated_name = 8;
  // 更新人id
  string updated_by = 9;
}
message GetPriceGroupTagResponse{
  repeated PriceGroupTag rows = 1;
  int64 total = 2;
}

message GetPgpTagCategoryRequest{
  repeated  uint64 ids = 1;
  //search
  string search = 2;
  // 分页
  Page page = 5;
}

message PgpTagCategory{
  // id
  uint64 id = 1;
  //partner_id
  uint64 partner_id = 2;
  // name
  string name = 3;
  // 包含标签数量
  uint64  tag_count = 4;
}

message  GetPgpTagCategoryResponse{
  repeated PgpTagCategory rows = 1;
  int64 total = 2;
}

message PriceGroupProductTag{
  //id
  uint64 id = 1;
  //partner_id
//  uint64 partner_id = 2;
  // code
  //@gotags: validate:"required"
  string code = 3;
  // name
  //@gotags: validate:"required"
  string name = 4;
  // 标签类别id
  //@gotags: validate:"required"
  string pgp_tag_category_id = 5;
 // 包含商品分类
  repeated string product_category_ids =6;
  // 包含商品id
  repeated string product_ids = 7;
  // 更新人名称
  string updated_name = 11;
  // 更新时间
  google.protobuf.Timestamp updated = 12;
}

message CreatePriceGroupProductTagRequest{
  // 价格组
  // @gotags: validate:"required"
  PriceGroupProductTag price_group_product_tag = 1;
}

message  CreatePriceGroupProductTagResponse{
  // 异常code
  ErrCode code = 1;
  // 异常原因
  string message = 2;
  int64 total = 4;
}
message DeletePriceGroupProductRequest{
  //@gotags: validate:"required"
  uint64 id=1;
}

message TimeSelector{
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
}


 message   PriceChangeLogGroup{
   // 门店ID
   uint64 store_id = 1;
   // 门店名称
   string store_name = 2;
   // 门店编码
   string store_code = 3;
   // 价格组编号
   string price_group_code = 5;
   // 价格组name
   string price_group_name = 6;
   // 价格组id
    uint64 price_group_id = 7;
    // 价格类型(渠道)id
    uint64 price_type_id = 8;
    // 价格类型(渠道)名称
    string price_type_name = 9;
    // 价格类型(渠道)编码
   string price_type_code = 10;
  message  PriceChangeLogAction{
    repeated PriceChangeLog updated =1;
    repeated PriceChangeLog deleted =2;
    repeated PriceChangeLog added =3;
  }
  PriceChangeLogAction actions = 11;
  // 币种
  string  currency = 12;
 }


