// Copyright 2018 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.api;

option go_package = "google.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig";
option java_multiple_files = true;
option java_outer_classname = "BackendProto";
option java_package = "com.google.api";
option objc_class_prefix = "GAPI";


// `Backend` defines the backend configuration for a service.
message Backend {
  // A list of API backend rules that apply to individual API methods.
  //
  // **NOTE:** All service configuration rules follow "last one wins" order.
  repeated BackendRule rules = 1;
}

// A backend rule provides configuration for an individual API element.
message BackendRule {
  // Selects the methods to which this rule applies.
  //
  // Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
  string selector = 1;

  // The address of the API backend.
  string address = 2;

  // The number of seconds to wait for a response from a request.  The default
  // deadline for gRPC is infinite (no deadline) and HTTP requests is 5 seconds.
  double deadline = 3;

  // Minimum deadline in seconds needed for this method. Calls having deadline
  // value lower than this will be rejected.
  double min_deadline = 4;
}
