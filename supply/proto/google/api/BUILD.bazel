# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
proto_library(
    name = "annotations_proto",
    srcs = ["annotations.proto"],
    deps = [
        ":http_proto",
        "@com_google_protobuf//:descriptor_proto",
    ],
)

proto_library(
    name = "auth_proto",
    srcs = ["auth.proto"],
    deps = [":annotations_proto"],
)

proto_library(
    name = "backend_proto",
    srcs = ["backend.proto"],
)

proto_library(
    name = "billing_proto",
    srcs = ["billing.proto"],
    deps = [
        ":annotations_proto",
        ":metric_proto",
    ],
)

proto_library(
    name = "config_change_proto",
    srcs = ["config_change.proto"],
)

proto_library(
    name = "consumer_proto",
    srcs = ["consumer.proto"],
)

proto_library(
    name = "context_proto",
    srcs = ["context.proto"],
)

proto_library(
    name = "control_proto",
    srcs = ["control.proto"],
)

proto_library(
    name = "distribution_proto",
    srcs = ["distribution.proto"],
    deps = [
        ":annotations_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "documentation_proto",
    srcs = ["documentation.proto"],
)

proto_library(
    name = "endpoint_proto",
    srcs = ["endpoint.proto"],
    deps = [":annotations_proto"],
)

proto_library(
    name = "http_proto",
    srcs = ["http.proto"],
)

proto_library(
    name = "httpbody_proto",
    srcs = ["httpbody.proto"],
    deps = ["@com_google_protobuf//:any_proto"],
)

proto_library(
    name = "label_proto",
    srcs = ["label.proto"],
)

proto_library(
    name = "launch_stage_proto",
    srcs = ["launch_stage.proto"],
)

proto_library(
    name = "log_proto",
    srcs = ["log.proto"],
    deps = [":label_proto"],
)

proto_library(
    name = "logging_proto",
    srcs = ["logging.proto"],
    deps = [":annotations_proto"],
)

proto_library(
    name = "metric_proto",
    srcs = ["metric.proto"],
    deps = [
        ":label_proto",
        ":launch_stage_proto",
        "@com_google_protobuf//:duration_proto",
    ],
)

proto_library(
    name = "monitored_resource_proto",
    srcs = ["monitored_resource.proto"],
    deps = [
        ":label_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library(
    name = "monitoring_proto",
    srcs = ["monitoring.proto"],
    deps = [":annotations_proto"],
)

proto_library(
    name = "quota_proto",
    srcs = ["quota.proto"],
    deps = [":annotations_proto"],
)

proto_library(
    name = "service_proto",
    srcs = ["service.proto"],
    deps = [
        ":annotations_proto",
        ":auth_proto",
        ":backend_proto",
        ":billing_proto",
        ":context_proto",
        ":control_proto",
        ":documentation_proto",
        ":endpoint_proto",
        ":http_proto",
        ":label_proto",
        ":log_proto",
        ":logging_proto",
        ":metric_proto",
        ":monitored_resource_proto",
        ":monitoring_proto",
        ":quota_proto",
        ":source_info_proto",
        ":system_parameter_proto",
        ":usage_proto",
        "//google/api/experimental:authorization_config_proto",
        "//google/api/experimental:experimental_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:api_proto",
        "@com_google_protobuf//:type_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library(
    name = "source_info_proto",
    srcs = ["source_info.proto"],
    deps = ["@com_google_protobuf//:any_proto"],
)

proto_library(
    name = "system_parameter_proto",
    srcs = ["system_parameter.proto"],
)

proto_library(
    name = "usage_proto",
    srcs = ["usage.proto"],
    deps = [":annotations_proto"],
)

##############################################################################
# Java
##############################################################################
java_proto_library(
    name = "api_java_proto",
    deps = [
        "annotations_proto",
        "auth_proto",
        "backend_proto",
        "billing_proto",
        "config_change_proto",
        "consumer_proto",
        "context_proto",
        "control_proto",
        "distribution_proto",
        "documentation_proto",
        "endpoint_proto",
        "http_proto",
        "httpbody_proto",
        "label_proto",
        "launch_stage_proto",
        "log_proto",
        "logging_proto",
        "metric_proto",
        "monitored_resource_proto",
        "monitoring_proto",
        "quota_proto",
        "service_proto",
        "source_info_proto",
        "system_parameter_proto",
        "usage_proto",
    ],
)
