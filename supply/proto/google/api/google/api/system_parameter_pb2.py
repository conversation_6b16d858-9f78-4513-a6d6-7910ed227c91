# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/system_parameter.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='google/api/system_parameter.proto',
  package='google.api',
  syntax='proto3',
  serialized_options=_b('\n\016com.google.apiB\024SystemParameterProtoP\001ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\242\002\004GAPI'),
  serialized_pb=_b('\n!google/api/system_parameter.proto\x12\ngoogle.api\"B\n\x10SystemParameters\x12.\n\x05rules\x18\x01 \x03(\x0b\x32\x1f.google.api.SystemParameterRule\"X\n\x13SystemParameterRule\x12\x10\n\x08selector\x18\x01 \x01(\t\x12/\n\nparameters\x18\x02 \x03(\x0b\x32\x1b.google.api.SystemParameter\"Q\n\x0fSystemParameter\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bhttp_header\x18\x02 \x01(\t\x12\x1b\n\x13url_query_parameter\x18\x03 \x01(\tBv\n\x0e\x63om.google.apiB\x14SystemParameterProtoP\x01ZEgoogle.golang.org/genproto/googleapis/api/serviceconfig;serviceconfig\xa2\x02\x04GAPIb\x06proto3')
)




_SYSTEMPARAMETERS = _descriptor.Descriptor(
  name='SystemParameters',
  full_name='google.api.SystemParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rules', full_name='google.api.SystemParameters.rules', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=49,
  serialized_end=115,
)


_SYSTEMPARAMETERRULE = _descriptor.Descriptor(
  name='SystemParameterRule',
  full_name='google.api.SystemParameterRule',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='selector', full_name='google.api.SystemParameterRule.selector', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parameters', full_name='google.api.SystemParameterRule.parameters', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=117,
  serialized_end=205,
)


_SYSTEMPARAMETER = _descriptor.Descriptor(
  name='SystemParameter',
  full_name='google.api.SystemParameter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='google.api.SystemParameter.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_header', full_name='google.api.SystemParameter.http_header', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url_query_parameter', full_name='google.api.SystemParameter.url_query_parameter', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=207,
  serialized_end=288,
)

_SYSTEMPARAMETERS.fields_by_name['rules'].message_type = _SYSTEMPARAMETERRULE
_SYSTEMPARAMETERRULE.fields_by_name['parameters'].message_type = _SYSTEMPARAMETER
DESCRIPTOR.message_types_by_name['SystemParameters'] = _SYSTEMPARAMETERS
DESCRIPTOR.message_types_by_name['SystemParameterRule'] = _SYSTEMPARAMETERRULE
DESCRIPTOR.message_types_by_name['SystemParameter'] = _SYSTEMPARAMETER
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SystemParameters = _reflection.GeneratedProtocolMessageType('SystemParameters', (_message.Message,), dict(
  DESCRIPTOR = _SYSTEMPARAMETERS,
  __module__ = 'google.api.system_parameter_pb2'
  # @@protoc_insertion_point(class_scope:google.api.SystemParameters)
  ))
_sym_db.RegisterMessage(SystemParameters)

SystemParameterRule = _reflection.GeneratedProtocolMessageType('SystemParameterRule', (_message.Message,), dict(
  DESCRIPTOR = _SYSTEMPARAMETERRULE,
  __module__ = 'google.api.system_parameter_pb2'
  # @@protoc_insertion_point(class_scope:google.api.SystemParameterRule)
  ))
_sym_db.RegisterMessage(SystemParameterRule)

SystemParameter = _reflection.GeneratedProtocolMessageType('SystemParameter', (_message.Message,), dict(
  DESCRIPTOR = _SYSTEMPARAMETER,
  __module__ = 'google.api.system_parameter_pb2'
  # @@protoc_insertion_point(class_scope:google.api.SystemParameter)
  ))
_sym_db.RegisterMessage(SystemParameter)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
