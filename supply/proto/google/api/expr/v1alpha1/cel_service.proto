// Copyright 2018 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.api.expr.v1alpha1;

import "google/api/expr/v1alpha1/conformance_service.proto";

option cc_enable_arenas = true;
option go_package = "google.golang.org/genproto/googleapis/api/expr/v1alpha1;expr";
option java_multiple_files = true;
option java_outer_classname = "CelServiceProto";
option java_package = "com.google.api.expr.v1alpha1";


// Access a CEL implementation from another process or machine.
// A CEL implementation is decomposed as a parser, a static checker,
// and an evaluator.  Every CEL implementation is expected to provide
// a server for this API.  The API will be used for conformance testing,
// utilities, and execution as a service.
service CelService {
  // Transforms CEL source text into a parsed representation.
  rpc Parse(ParseRequest) returns (ParseResponse) {
  }

  // Runs static checks on a parsed CEL representation and return
  // an annotated representation, or a set of issues.
  rpc Check(CheckRequest) returns (CheckResponse) {
  }

  // Evaluates a parsed or annotation CEL representation given
  // values of external bindings.
  rpc Eval(EvalRequest) returns (EvalResponse) {
  }
}
