# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "checked_proto",
    srcs = ["checked.proto"],
    deps = [
        ":syntax_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library(
    name = "conformance_service_proto",
    srcs = ["conformance_service.proto"],
    deps = [
        ":checked_proto",
        ":eval_proto",
        ":syntax_proto",
        "//google/rpc:status_proto",
    ],
)

proto_library(
    name = "eval_proto",
    srcs = ["eval.proto"],
    deps = [
        ":value_proto",
        "//google/rpc:status_proto",
    ],
)

proto_library(
    name = "explain_proto",
    srcs = ["explain.proto"],
    deps = [
        ":value_proto",
    ],
)

proto_library(
    name = "syntax_proto",
    srcs = ["syntax.proto"],
    deps = [
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "value_proto",
    srcs = ["value.proto"],
    deps = [
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library(
    name = "v1alpha1",
    srcs = [
        "checked_proto",
        "eval_proto",
        "explain_proto",
        "syntax_proto",
        "value_proto",
    ],
    deps = [
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)
