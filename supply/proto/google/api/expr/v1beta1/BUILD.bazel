# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "decl_proto",
    srcs = ["decl.proto"],
    deps = [
        ":expr_proto",
    ],
)

proto_library(
    name = "eval_proto",
    srcs = ["eval.proto"],
    deps = [
        ":value_proto",
        "//google/rpc:status_proto",
    ],
)

proto_library(
    name = "expr_proto",
    srcs = ["expr.proto"],
    deps = [
        ":source_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library(
    name = "source_proto",
    srcs = ["source.proto"],
)

proto_library(
    name = "value_proto",
    srcs = ["value.proto"],
    deps = [
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

proto_library(
    name = "v1beta1",
    srcs = [
        "decl.proto",
        "eval.proto",
        "expr.proto",
        "source.proto",
        "value.proto",
    ],
    deps = [
        "//google/rpc:status_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)
