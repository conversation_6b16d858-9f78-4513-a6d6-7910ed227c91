load("@io_grpc_grpc_java//:java_grpc_library.bzl", "java_grpc_library")

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
proto_library(
    name = "authorization_config_proto",
    srcs = [
        "authorization_config.proto",
    ],
    deps = [],
)

proto_library(
    name = "experimental_proto",
    srcs = [
        "experimental.proto",
    ],
    deps = [
        ":authorization_config_proto",
        "//google/api:annotations_proto",
    ],
)

##############################################################################
# Java
##############################################################################
java_proto_library(
    name = "experimental_java_proto",
    deps = [
        ":authorization_config_proto",
        ":experimental_proto",
    ],
)
