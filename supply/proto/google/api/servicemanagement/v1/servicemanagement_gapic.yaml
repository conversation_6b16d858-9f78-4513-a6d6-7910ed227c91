type: com.google.api.codegen.ConfigProto
config_schema_version: 1.0.0
# The settings of generated code in a specific language.
language_settings:
  java:
    package_name: com.google.cloud.api.servicemanagement.v1
  python:
    package_name: google.cloud.api.servicemanagement_v1.gapic
  go:
    package_name: cloud.google.com/go/api/servicemanagement/apiv1
  csharp:
    package_name: Google.Api.Servicemanagement.V1
  ruby:
    package_name: Google::Cloud::Api::Servicemanagement::V1
  php:
    package_name: Google\Cloud\Api\Servicemanagement\V1
  nodejs:
    package_name: servicemanagement.v1
# The configuration for the license header to put on generated files.
license_header:
  # The file containing the copyright line(s).
  copyright_file: copyright-google.txt
  # The file containing the raw license header without any copyright line(s).
  license_file: license-header-apache-2.0.txt
# A list of API interface configurations.
interfaces:
  # The fully qualified name of the API interface.
- name: google.api.servicemanagement.v1.ServiceManager
  # A list of resource collection configurations.
  # Consists of a name_pattern and an entity_name.
  # The name_pattern is a pattern to describe the names of the resources of this
  # collection, using the platform's conventions for URI patterns. A generator
  # may use this to generate methods to compose and decompose such names. The
  # pattern should use named placeholders as in `shelves/{shelf}/books/{book}`;
  # those will be taken as hints for the parameter names of the generated
  # methods. If empty, no name methods are generated.
  # The entity_name is the name to be used as a basis for generated methods and
  # classes.
  smoke_test:
    method: ListServices
    init_fields:
    - producer_project_id=$PROJECT_ID
  collections: []
  # Definition for retryable codes.
  retry_codes_def:
  - name: idempotent
    retry_codes:
    - UNAVAILABLE
    - DEADLINE_EXCEEDED
  - name: non_idempotent
    retry_codes: []
  # Definition for retry/backoff parameters.
  retry_params_def:
  - name: default
    initial_retry_delay_millis: 100
    retry_delay_multiplier: 1.3
    max_retry_delay_millis: 60000
    initial_rpc_timeout_millis: 20000
    rpc_timeout_multiplier: 1
    max_rpc_timeout_millis: 20000
    total_timeout_millis: 600000
  # A list of method configurations.
  # Common properties:
  #
  #   name - The simple name of the method.
  #
  #   flattening - Specifies the configuration for parameter flattening.
  #   Describes the parameter groups for which a generator should produce method
  #   overloads which allow a client to directly pass request message fields as
  #   method parameters. This information may or may not be used, depending on
  #   the target language.
  #   Consists of groups, which each represent a list of parameters to be
  #   flattened. Each parameter listed must be a field of the request message.
  #
  #   required_fields - Fields that are always required for a request to be
  #   valid.
  #
  #   request_object_method - Turns on or off the generation of a method whose
  #   sole parameter is a request object. Not all languages will generate this
  #   method.
  #
  #   resource_name_treatment - An enum that specifies how to treat the resource
  #   name formats defined in the field_name_patterns and
  #   response_field_name_patterns fields.
  #   UNSET: default value
  #   NONE: the collection configs will not be used by the generated code.
  #   VALIDATE: string fields will be validated by the client against the
  #   specified resource name formats.
  #   STATIC_TYPES: the client will use generated types for resource names.
  #
  #   page_streaming - Specifies the configuration for paging.
  #   Describes information for generating a method which transforms a paging
  #   list RPC into a stream of resources.
  #   Consists of a request and a response.
  #   The request specifies request information of the list method. It defines
  #   which fields match the paging pattern in the request. The request consists
  #   of a page_size_field and a token_field. The page_size_field is the name of
  #   the optional field specifying the maximum number of elements to be
  #   returned in the response. The token_field is the name of the field in the
  #   request containing the page token.
  #   The response specifies response information of the list method. It defines
  #   which fields match the paging pattern in the response. The response
  #   consists of a token_field and a resources_field. The token_field is the
  #   name of the field in the response containing the next page token. The
  #   resources_field is the name of the field in the response containing the
  #   list of resources belonging to the page.
  #
  #   retry_codes_name - Specifies the configuration for retryable codes. The
  #   name must be defined in interfaces.retry_codes_def.
  #
  #   retry_params_name - Specifies the configuration for retry/backoff
  #   parameters. The name must be defined in interfaces.retry_params_def.
  #
  #   field_name_patterns - Maps the field name of the request type to
  #   entity_name of interfaces.collections.
  #   Specifies the string pattern that the field must follow.
  #
  #   timeout_millis - Specifies the default timeout for a non-retrying call. If
  #   the call is retrying, refer to retry_params_name instead.
  methods:
  - name: ListServices
    flattening:
      groups:
      - parameters:
        - producer_project_id
        - consumer_id
    required_fields:
    request_object_method: false
    page_streaming:
      request:
        page_size_field: page_size
        token_field: page_token
      response:
        token_field: next_page_token
        resources_field: services
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: GetService
    flattening:
      groups:
      - parameters:
        - service_name
    required_fields:
    - service_name
    request_object_method: false
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: CreateService
    flattening:
      groups:
      - parameters:
        - service
    required_fields:
    - service
    request_object_method: false
    retry_codes_name: non_idempotent
    retry_params_name: default
    timeout_millis: 20000
  - name: DeleteService
    flattening:
      groups:
      - parameters:
        - service_name
    required_fields:
    - service_name
    request_object_method: false
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 60000
  - name: UndeleteService
    flattening:
      groups:
      - parameters:
        - service_name
    required_fields:
    - service_name
    request_object_method: false
    retry_codes_name: non_idempotent
    retry_params_name: default
    # REVIEW: Could this operation take a long time?
    timeout_millis: 60000
  - name: ListServiceConfigs
    flattening:
      groups:
      - parameters:
        - service_name
    required_fields:
    - service_name
    request_object_method: false
    page_streaming:
      request:
        page_size_field: page_size
        token_field: page_token
      response:
        token_field: next_page_token
        resources_field: service_configs
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: GetServiceConfig
    flattening:
      groups:
      - parameters:
        - service_name
        - config_id
        - view
    required_fields:
    - service_name
    - config_id
    request_object_method: false
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: CreateServiceConfig
    flattening:
      groups:
      - parameters:
        - service_name
        - service_config
    required_fields:
    - service_name
    - service_config
    request_object_method: false
    retry_codes_name: non_idempotent
    retry_params_name: default
    timeout_millis: 20000
  - name: SubmitConfigSource
    flattening:
      groups:
      - parameters:
        - service_name
        - config_source
        - validate_only
    required_fields:
    - service_name
    - config_source
    request_object_method: false
    retry_codes_name: non_idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: ListServiceRollouts
    flattening:
      groups:
      - parameters:
        - service_name
        - filter
    required_fields:
    - service_name
    request_object_method: false
    page_streaming:
      request:
        page_size_field: page_size
        token_field: page_token
      response:
        token_field: next_page_token
        resources_field: rollouts
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: GetServiceRollout
    flattening:
      groups:
      - parameters:
        - service_name
        - rollout_id
    required_fields:
    - service_name
    - rollout_id
    request_object_method: false
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: CreateServiceRollout
    flattening:
      groups:
      - parameters:
        - service_name
        - rollout
    required_fields:
    - service_name
    - rollout
    request_object_method: false
    retry_codes_name: non_idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: GenerateConfigReport
    flattening:
      groups:
      - parameters:
        - new_config
        - old_config
    required_fields:
    - new_config
    - old_config
    request_object_method: false
    retry_codes_name: non_idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: EnableService
    flattening:
      groups:
      - parameters:
        - service_name
        - consumer_id
    required_fields:
      - service_name
      - consumer_id
    request_object_method: false
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 10000
  - name: DisableService
    flattening:
      groups:
      - parameters:
        - service_name
        - consumer_id
    required_fields:
      - service_name
      - consumer_id
    request_object_method: false
    retry_codes_name: idempotent
    retry_params_name: default
    timeout_millis: 10000
