type: google.api.Service
config_version: 1
name: serviceconfig.googleapis.com
title: Service Config API

types:
- name: google.api.ConfigChange
- name: google.api.Distribution
- name: google.api.HttpBody
- name: google.api.LabelDescriptor
- name: google.api.Metric
- name: google.api.MonitoredResource
- name: google.api.MonitoredResourceDescriptor
- name: google.api.MonitoredResourceMetadata
- name: google.api.Service
- name: google.api.DocumentationRule

documentation:
  summary: Lets you define and config your API service.
