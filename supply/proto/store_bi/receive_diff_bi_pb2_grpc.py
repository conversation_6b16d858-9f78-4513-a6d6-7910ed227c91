# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from store_bi import receive_diff_bi_pb2 as store__bi_dot_receive__diff__bi__pb2


class StoreReceiveDiffBiServiceStub(object):
  """收货差异单报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetReceiveDiffCollect = channel.unary_unary(
        '/store_bi.StoreReceiveDiffBiService/GetReceiveDiffCollect',
        request_serializer=store__bi_dot_receive__diff__bi__pb2.GetReceiveDiffCollectRequest.SerializeToString,
        response_deserializer=store__bi_dot_receive__diff__bi__pb2.GetReceiveDiffCollectResponse.FromString,
        )
    self.GetReceiveDiffDetail = channel.unary_unary(
        '/store_bi.StoreReceiveDiffBiService/GetReceiveDiffDetail',
        request_serializer=store__bi_dot_receive__diff__bi__pb2.GetReceiveDiffDetailRequest.SerializeToString,
        response_deserializer=store__bi_dot_receive__diff__bi__pb2.GetReceiveDiffDetailResponse.FromString,
        )


class StoreReceiveDiffBiServiceServicer(object):
  """收货差异单报表相关服务
  """

  def GetReceiveDiffCollect(self, request, context):
    """查询收货差异汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveDiffDetail(self, request, context):
    """查询收货差异详情报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreReceiveDiffBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetReceiveDiffCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffCollect,
          request_deserializer=store__bi_dot_receive__diff__bi__pb2.GetReceiveDiffCollectRequest.FromString,
          response_serializer=store__bi_dot_receive__diff__bi__pb2.GetReceiveDiffCollectResponse.SerializeToString,
      ),
      'GetReceiveDiffDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffDetail,
          request_deserializer=store__bi_dot_receive__diff__bi__pb2.GetReceiveDiffDetailRequest.FromString,
          response_serializer=store__bi_dot_receive__diff__bi__pb2.GetReceiveDiffDetailResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'store_bi.StoreReceiveDiffBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
