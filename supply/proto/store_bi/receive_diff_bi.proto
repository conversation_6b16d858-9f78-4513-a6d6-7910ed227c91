syntax = "proto3";
package store_bi;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// 收货差异单报表相关服务
service StoreReceiveDiffBiService{
    
    // 查询收货差异汇总报表
    rpc GetReceiveDiffCollect(GetReceiveDiffCollectRequest) returns (GetReceiveDiffCollectResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/bi/receive_diff/collect"
        };
    }
    // 查询收货差异详情报表
    rpc GetReceiveDiffDetail(GetReceiveDiffDetailRequest) returns (GetReceiveDiffDetailResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/bi/receive_diff/detailed"
        };
    }

}


message GetReceiveDiffCollectRequest{
    // 门店id
    repeated uint64 st_ids = 1;
    // 商品类别id
    repeated uint64 category_ids = 2;
    // 物流模式
    repeated string logistics_type = 3;
    // 商品名称
    string product_name = 4;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 5;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 6;
    // 分页大小
    int32 limit = 7;
    // 跳过行数
    int32 offset = 8;
    // 返回总条数
    bool include_total = 9;
    // 区分门店仓库STORE/WAREHOUSE
    string branch_type = 11;
    // 供货方id
    repeated uint64 delivery_bys = 12;
    // 商品id
    repeated uint64 product_ids = 13;
    // 收货类型
    repeated string received_types = 14;
}

message GetReceiveDiffCollectResponse{
    repeated ReceiveDiffCollect rows = 1;
    DiffTotal total = 2;
}

message GetReceiveDiffDetailRequest{

    // 门店id
    repeated uint64 st_ids = 1;
    // 商品类别id
    repeated uint64 category_ids = 2;
    // 物流模式
    repeated string logistics_type = 3;
    // 商品名称
    string product_name = 4;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 5;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 6;
    // 分页大小
    int32 limit = 7;
    // 跳过行数
    int32 offset = 8;
    // 返回总条数
    bool include_total = 9;
    // 单据编号
    string code = 10;
    // 区分门店仓库STORE/WAREHOUSE
    string branch_type = 11;
    // 供货方id
    repeated uint64 delivery_bys = 12;
    // 商品id
    repeated uint64 product_ids = 13;
    // 收货类型
    repeated string received_types = 14;

}

message GetReceiveDiffDetailResponse{
    repeated ReceiveDiffDetailed rows = 1;
    DiffTotal total = 2;
}

message DiffTotal{
    // 条数
    double count = 1;
    // 合计数量
    double sum_quantity = 2;
    // 合计确认数量
    double sum_accounting_quantity = 3;
    // 合计配送方承担数量
    double sum_d_diff_quantity = 4;
    // 合计门店承担数量
    double sum_s_diff_quantity = 5;
    string sum_pcdp_amount = 6;
}

message ReceiveDiffCollect{
    // 核算单位id
    uint64 accounting_unit_id = 1;
    // 核算单位名称
    string accounting_unit_name = 2;
    // 商品类别id
    uint64 category_id = 3;
    // 商品类别编码
    string category_code = 4;
    // 商品类别名称
    string category_name = 5;
    // 商品类别父级
    string category_parent = 6;
    // 商品id
    uint64 product_id = 7;
    // 商品编码
    string product_code = 8;
    // 商品名称
    string product_name = 9;
    // 商品规格
    string product_spec = 10;
    // 
    double accounting_quantity = 11;
    // 数量
    double quantity = 12;
    // 门店id
    uint64 store_id = 13;
    // 门店编码
    string store_code = 14;
    // 门店名称
    string store_name = 15;
    // 单位id
    uint64 unit_id = 16;
    // 单位名称
    string unit_name = 17;
    //
    uint64 id = 18;
    uint64 position_id = 25;
    string position_code = 26;
    string position_name = 27;
    // 
    uint64 delivery_by = 28;
    string delivery_by_name = 29;
    string delivery_by_code = 30;
    // 
    string logistics_type = 31;
    // 门店承担差异数量
    double s_diff_quantity = 32;
    // 配送方承担差异数量
    double d_diff_quantity = 33;
    string pcdp_price = 34;
    string pcdp_amount = 35;

}

message ReceiveDiffDetailed{
    // 核算单位id
    uint64 accounting_unit_id = 1;
    // 核算单位名称
    string accounting_unit_name = 2;
    // 商品类别id
    uint64 category_id = 3;
    // 商品类别编码
    string category_code = 4;
    // 商品类别名称
    string category_name = 5;
    // 商品类别父级
    string category_parent = 6;
    //
    uint64 diff_id = 7;
    //
    string diff_date = 8;
    //
    string diff_code = 9;
    // 商品id
    uint64 product_id = 10;
    // 商品编码
    string product_code = 11;
    // 商品名称
    string product_name = 12;
    // 商品规格
    string product_spec = 13;
    // 
    double accounting_quantity = 14;
    // 数量
    double quantity = 15;
    // 门店id
    uint64 store_id = 16;
    // 门店编码
    string store_code = 17;
    // 门店名称
    string store_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    //
    uint64 id = 21;
    //
    string reason_type = 22;
    uint64 position_id = 25;
    string position_code = 26;
    string position_name = 27;

    uint64 delivery_by = 28;
    string delivery_by_name = 29;
    string delivery_by_code = 30;
    // 
    string logistics_type = 31;
    // 差异单对应的收货单订货日期
    google.protobuf.Timestamp demand_date = 32;
    // 差异单创建日期
    google.protobuf.Timestamp created_at = 33;
    // 收货单单号
    string receiving_code = 34;
    // 门店承担差异数量
    double s_diff_quantity = 35;
    // 配送方承担差异数量
    double d_diff_quantity = 36;
    string pcdp_price = 37;
    string pcdp_amount = 38;
}