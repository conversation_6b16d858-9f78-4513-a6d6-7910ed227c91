# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: reduction_augmentation_demand.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='reduction_augmentation_demand.proto',
  package='reduction_augmentation_demand',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n#reduction_augmentation_demand.proto\x12\x1dreduction_augmentation_demand\x1a\x1cgoogle/api/annotations.proto\"\x9d\x01\n&ListReductionAugmentationDemandRequest\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x0f\n\x07\x63ompany\x18\x02 \x01(\t\x12\x17\n\x0f\x61llocation_type\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\"\xd8\x02\n\'ListReductionAugmentationDemandResponse\x12[\n\x04rows\x18\x01 \x03(\x0b\x32M.reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x1a\xc0\x01\n\x06\x44\x65tail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0f\n\x07\x63ompany\x18\x03 \x03(\x04\x12\x17\n\x0f\x61llocation_type\x18\x04 \x01(\t\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x19\n\x11start_demand_date\x18\x07 \x01(\t\x12\x17\n\x0f\x65nd_demand_date\x18\x08 \x01(\t\x12\x0c\n\x04rate\x18\t \x01(\x01\x12\x12\n\nupdated_at\x18\n \x01(\t\"\xac\x03\n+RetrieveReductionAugmentationDemandResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0f\n\x07\x63ompany\x18\x03 \x03(\x04\x12\x17\n\x0f\x61llocation_type\x18\x04 \x01(\t\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x19\n\x11start_demand_date\x18\x07 \x01(\t\x12\x17\n\x0f\x65nd_demand_date\x18\x08 \x01(\t\x12\x0c\n\x04rate\x18\t \x01(\x01\x12\x12\n\nupdated_at\x18\n \x01(\t\x12\x1b\n\x13order_by_demand_day\x18\x0c \x01(\x05\x12\x19\n\x11quantity_per_time\x18\r \x01(\t\x12\x1c\n\x14\x61llow_reduce_to_zero\x18\x0e \x01(\x08\x12\x13\n\x0border_types\x18\x0f \x03(\x04\x12\x11\n\tstore_sum\x18\x10 \x01(\x05\x12\x13\n\x0bproduct_sum\x18\x11 \x01(\x05\x12\x1b\n\x13\x61llocation_quantity\x18\x12 \x01(\x01\x12\x15\n\rorder_by_type\x18\x13 \x01(\t\"8\n*RetrieveReductionAugmentationDemandRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"_\n-ListReductionAugmentationDemandProductRequest\x12\x0f\n\x07rule_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\"\xc8\x02\n.ListReductionAugmentationDemandProductResponse\x12m\n\x04rows\x18\x01 \x03(\x0b\x32_.reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x1a\x97\x01\n\x11\x41llocationProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x1b\n\x13\x61llocation_quantity\x18\x04 \x01(\x01\x12\x17\n\x0fremain_quantity\x18\x05 \x01(\x01\x12\x0c\n\x04rate\x18\x06 \x01(\x01\"]\n+ListReductionAugmentationDemandStoreRequest\x12\x0f\n\x07rule_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\"\xf5\x01\n,ListReductionAugmentationDemandStoreResponse\x12i\n\x04rows\x18\x01 \x03(\x0b\x32[.reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse.AllocationStore\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x1aK\n\x0f\x41llocationStore\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\"\xaa\x01\n\x18SumDemandQuantityRequest\x12\x13\n\x0bproduct_ids\x18\x01 \x03(\t\x12\x11\n\tstore_ids\x18\x02 \x03(\t\x12\x19\n\x11start_demand_date\x18\x03 \x01(\t\x12\x17\n\x0f\x65nd_demand_date\x18\x04 \x01(\t\x12\x13\n\x0border_types\x18\x05 \x03(\x04\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\"\xd3\x01\n\x19SumDemandQuantityResponse\x12W\n\x04rows\x18\x01 \x03(\x0b\x32I.reduction_augmentation_demand.SumDemandQuantityResponse.SumDemandProduct\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x1aN\n\x10SumDemandProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12\x10\n\x08quantity\x18\x03 \x01(\x01\"\xbc\x03\n\x1a\x43\x61lculateAllocationRequest\x12S\n\x08products\x18\x01 \x03(\x0b\x32\x41.reduction_augmentation_demand.CalculateAllocationRequest.Product\x12\x17\n\x0f\x61llocation_type\x18\x02 \x01(\t\x12\x19\n\x11start_demand_date\x18\x05 \x01(\t\x12\x17\n\x0f\x65nd_demand_date\x18\x06 \x01(\t\x12\x0c\n\x04rate\x18\x07 \x01(\x01\x12\x1b\n\x13order_by_demand_day\x18\t \x01(\x05\x12\x19\n\x11quantity_per_time\x18\n \x01(\t\x12\x1c\n\x14\x61llow_reduce_to_zero\x18\x0b \x01(\x08\x12\x13\n\x0border_types\x18\x0c \x03(\x04\x12\x11\n\tstore_ids\x18\r \x03(\x04\x12\x0f\n\x07rule_id\x18\x0e \x01(\x04\x12\x15\n\rorder_by_type\x18\x0f \x01(\t\x1aH\n\x07Product\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61llocation_quantity\x18\x02 \x01(\x01\x12\x0c\n\x04rate\x18\x03 \x01(\x01\"\xea\x03\n\x1b\x43\x61lculateAllocationResponse\x12\x61\n\x0fremain_products\x18\x01 \x03(\x0b\x32H.reduction_augmentation_demand.CalculateAllocationResponse.RemainProduct\x12\x66\n\x11\x61llocation_result\x18\x02 \x03(\x0b\x32K.reduction_augmentation_demand.CalculateAllocationResponse.AllocationResult\x12\x0f\n\x07rule_id\x18\x03 \x01(\x04\x1ah\n\rRemainProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x17\n\x0fremain_quantity\x18\x04 \x01(\x01\x1a\x84\x01\n\x10\x41llocationResult\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x13\n\x0bstore_count\x18\x04 \x01(\x05\x12\x1b\n\x13\x61llocation_quantity\x18\x05 \x01(\x01\"V\n$ListCalculateAllocationResultRequest\x12\x0f\n\x07rule_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\"\xb6\x03\n%ListCalculateAllocationResultResponse\x12\x63\n\x04rows\x18\x01 \x03(\x0b\x32U.reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x1a\x98\x02\n\x10\x41llocationDetail\x12\x14\n\x0cproduct_code\x18\x01 \x01(\t\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x17\n\x0f\x64\x65mand_quantity\x18\x05 \x01(\x01\x12\x1b\n\x13\x61llocation_quantity\x18\x06 \x01(\x01\x12\x16\n\x0e\x66inal_quantity\x18\x07 \x01(\x01\x12\x12\n\norder_type\x18\x08 \x01(\t\x12\x14\n\x0corder_source\x18\t \x01(\t\x12\x12\n\norder_code\x18\n \x01(\t\x12\x12\n\nproduct_id\x18\x0b \x01(\x04\x12\x10\n\x08store_id\x18\x0c \x01(\x04\"\xa9\x04\n(CreateReductionAugmentationDemandRequest\x12\x0f\n\x07\x63ompany\x18\x01 \x03(\t\x12\x17\n\x0f\x61llocation_type\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x19\n\x11start_demand_date\x18\x05 \x01(\t\x12\x17\n\x0f\x65nd_demand_date\x18\x06 \x01(\t\x12\x0c\n\x04rate\x18\x07 \x01(\x01\x12\x1b\n\x13order_by_demand_day\x18\t \x01(\x05\x12\x19\n\x11quantity_per_time\x18\n \x01(\t\x12\x1c\n\x14\x61llow_reduce_to_zero\x18\x0b \x01(\x08\x12\x13\n\x0border_types\x18\x0c \x03(\x04\x12\x11\n\tstore_ids\x18\r \x03(\x04\x12\x61\n\x08products\x18\x0e \x03(\x0b\x32O.reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.Product\x12\x1a\n\x12\x63\x61lculated_rule_id\x18\x0f \x01(\x04\x12\x15\n\rorder_by_type\x18\x10 \x01(\t\x1a\x61\n\x07Product\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61llocation_quantity\x18\x02 \x01(\x01\x12\x0c\n\x04rate\x18\x03 \x01(\x01\x12\x17\n\x0fremain_quantity\x18\x04 \x01(\x01\"7\n)CreateReductionAugmentationDemandResponse\x12\n\n\x02id\x18\x01 \x01(\x04\"\xb7\x04\n(UpdateReductionAugmentationDemandRequest\x12\x0f\n\x07\x63ompany\x18\x01 \x03(\t\x12\x17\n\x0f\x61llocation_type\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x19\n\x11start_demand_date\x18\x05 \x01(\t\x12\x17\n\x0f\x65nd_demand_date\x18\x06 \x01(\t\x12\x0c\n\x04rate\x18\x07 \x01(\x01\x12\x1b\n\x13order_by_demand_day\x18\t \x01(\x05\x12\x19\n\x11quantity_per_time\x18\n \x01(\t\x12\x1c\n\x14\x61llow_reduce_to_zero\x18\x0b \x01(\x08\x12\x13\n\x0border_types\x18\x0c \x03(\x04\x12\x11\n\tstore_ids\x18\r \x03(\x04\x12\x61\n\x08products\x18\x0e \x03(\x0b\x32O.reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.Product\x12\x1a\n\x12\x63\x61lculated_rule_id\x18\x0f \x01(\x04\x12\n\n\x02id\x18\x10 \x01(\x04\x12\x0e\n\x06status\x18\x11 \x01(\t\x12\x15\n\rorder_by_type\x18\x12 \x01(\t\x1a\x61\n\x07Product\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61llocation_quantity\x18\x02 \x01(\x01\x12\x0c\n\x04rate\x18\x03 \x01(\x01\x12\x17\n\x0fremain_quantity\x18\x04 \x01(\x01\";\n)UpdateReductionAugmentationDemandResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"4\n!DestroyCalculateAllocationRequest\x12\x0f\n\x07rule_id\x18\x01 \x01(\x04\"4\n\"DestroyCalculateAllocationResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"h\n#ListCalculateAllocationStoreRequest\x12\x0f\n\x07rule_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x11\n\tstore_ids\x18\x04 \x03(\x04\"\xc2\x01\n$ListCalculateAllocationStoreResponse\x12W\n\x04rows\x18\x01 \x03(\x0b\x32I.reduction_augmentation_demand.ListCalculateAllocationStoreResponse.Store\x1a\x41\n\x05Store\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\"\x7f\n3ListReductionAugmentationDemandExcludedStoreRequest\x12\x17\n\x0f\x61llocation_type\x18\x01 \x01(\t\x12\x10\n\x08store_id\x18\x02 \x03(\x04\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\"\x98\x02\n4ListReductionAugmentationDemandExcludedStoreResponse\x12i\n\x06stores\x18\x01 \x03(\x0b\x32Y.reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.Store\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x1a\x66\n\x05Store\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x17\n\x0f\x61llocation_type\x18\x05 \x01(\t\"\xeb\x01\n5CreateReductionAugmentationDemandExcludedStoreRequest\x12j\n\x06stores\x18\x01 \x03(\x0b\x32Z.reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest.Store\x12\x17\n\x0f\x61llocation_type\x18\x02 \x01(\t\x1a-\n\x05Store\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\"H\n6CreateReductionAugmentationDemandExcludedStoreResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"D\n6DestroyReductionAugmentationDemandExcludedStoreRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"I\n7DestroyReductionAugmentationDemandExcludedStoreResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"^\n,ReductionAugmentationDemandProductAggRequest\x12\x0f\n\x07rule_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\"\xb2\x02\n-ReductionAugmentationDemandProductAggResponse\x12k\n\x04rows\x18\x01 \x03(\x0b\x32].reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.AllocationResult\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\x1a\x84\x01\n\x10\x41llocationResult\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x13\n\x0bstore_count\x18\x04 \x01(\x05\x12\x1b\n\x13\x61llocation_quantity\x18\x05 \x01(\x01\x32\xde\x1b\n\x1bReductionAugmentationDemand\x12\xe6\x01\n\x1fListReductionAugmentationDemand\x12\x45.reduction_augmentation_demand.ListReductionAugmentationDemandRequest\x1a\x46.reduction_augmentation_demand.ListReductionAugmentationDemandResponse\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/reduction_augmentation_demand\x12\xff\x01\n#RetrieveReductionAugmentationDemand\x12I.reduction_augmentation_demand.RetrieveReductionAugmentationDemandRequest\x1aJ.reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse\"A\x82\xd3\xe4\x93\x02;\x12\x39/api/v2/supply/reduction_augmentation_demand/with/id/{id}\x12\xec\x01\n!CreateReductionAugmentationDemand\x12G.reduction_augmentation_demand.CreateReductionAugmentationDemandRequest\x1aH.reduction_augmentation_demand.CreateReductionAugmentationDemandResponse\"4\x82\xd3\xe4\x93\x02.\",/api/v2/supply/reduction_augmentation_demand\x12\xf1\x01\n!UpdateReductionAugmentationDemand\x12G.reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest\x1aH.reduction_augmentation_demand.UpdateReductionAugmentationDemandResponse\"9\x82\xd3\xe4\x93\x02\x33\x32\x31/api/v2/supply/reduction_augmentation_demand/{id}\x12\x83\x02\n&ListReductionAugmentationDemandProduct\x12L.reduction_augmentation_demand.ListReductionAugmentationDemandProductRequest\x1aM.reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse\"<\x82\xd3\xe4\x93\x02\x36\x12\x34/api/v2/supply/reduction_augmentation_demand/product\x12\xfb\x01\n$ListReductionAugmentationDemandStore\x12J.reduction_augmentation_demand.ListReductionAugmentationDemandStoreRequest\x1aK.reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/supply/reduction_augmentation_demand/store\x12\xc0\x01\n\x11SumDemandQuantity\x12\x37.reduction_augmentation_demand.SumDemandQuantityRequest\x1a\x38.reduction_augmentation_demand.SumDemandQuantityResponse\"8\x82\xd3\xe4\x93\x02\x32\"0/api/v2/supply/reduction_augmentation_demand/sum\x12\xcc\x01\n\x13\x43\x61lculateAllocation\x12\x39.reduction_augmentation_demand.CalculateAllocationRequest\x1a:.reduction_augmentation_demand.CalculateAllocationResponse\">\x82\xd3\xe4\x93\x02\x38\"6/api/v2/supply/reduction_augmentation_demand/calculate\x12\xf4\x01\n\x1dListCalculateAllocationResult\x12\x43.reduction_augmentation_demand.ListCalculateAllocationResultRequest\x1a\x44.reduction_augmentation_demand.ListCalculateAllocationResultResponse\"H\x82\xd3\xe4\x93\x02\x42\x12@/api/v2/supply/reduction_augmentation_demand/calculate/{rule_id}\x12\xeb\x01\n\x1a\x44\x65stroyCalculateAllocation\x12@.reduction_augmentation_demand.DestroyCalculateAllocationRequest\x1a\x41.reduction_augmentation_demand.DestroyCalculateAllocationResponse\"H\x82\xd3\xe4\x93\x02\x42*@/api/v2/supply/reduction_augmentation_demand/calculate/{rule_id}\x12\x9c\x02\n,ListReductionAugmentationDemandExcludedStore\x12R.reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreRequest\x1aS.reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse\"C\x82\xd3\xe4\x93\x02=\x12;/api/v2/supply/reduction_augmentation_demand/excluded/store\x12\xa2\x02\n.CreateReductionAugmentationDemandExcludedStore\x12T.reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest\x1aU.reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreResponse\"C\x82\xd3\xe4\x93\x02=\";/api/v2/supply/reduction_augmentation_demand/excluded/store\x12\xaa\x02\n/DestroyReductionAugmentationDemandExcludedStore\x12U.reduction_augmentation_demand.DestroyReductionAugmentationDemandExcludedStoreRequest\x1aV.reduction_augmentation_demand.DestroyReductionAugmentationDemandExcludedStoreResponse\"H\x82\xd3\xe4\x93\x02\x42*@/api/v2/supply/reduction_augmentation_demand/excluded/store/{id}\x12\x84\x02\n%ReductionAugmentationDemandProductAgg\x12K.reduction_augmentation_demand.ReductionAugmentationDemandProductAggRequest\x1aL.reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse\"@\x82\xd3\xe4\x93\x02:\x12\x38/api/v2/supply/reduction_augmentation_demand/product/aggb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,])




_LISTREDUCTIONAUGMENTATIONDEMANDREQUEST = _descriptor.Descriptor(
  name='ListReductionAugmentationDemandRequest',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandRequest.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandRequest.company', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandRequest.allocation_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandRequest.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=101,
  serialized_end=258,
)


_LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE_DETAIL = _descriptor.Descriptor(
  name='Detail',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.company', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.allocation_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_demand_date', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.start_demand_date', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_demand_date', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.end_demand_date', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.rate', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail.updated_at', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=413,
  serialized_end=605,
)

_LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE = _descriptor.Descriptor(
  name='ListReductionAugmentationDemandResponse',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandResponse.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE_DETAIL, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=261,
  serialized_end=605,
)


_RETRIEVEREDUCTIONAUGMENTATIONDEMANDRESPONSE = _descriptor.Descriptor(
  name='RetrieveReductionAugmentationDemandResponse',
  full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.company', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.allocation_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_demand_date', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.start_demand_date', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_demand_date', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.end_demand_date', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.rate', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.updated_at', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by_demand_day', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.order_by_demand_day', index=10,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_per_time', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.quantity_per_time', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_reduce_to_zero', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.allow_reduce_to_zero', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_types', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.order_types', index=13,
      number=15, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_sum', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.store_sum', index=14,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_sum', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.product_sum', index=15,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_quantity', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.allocation_quantity', index=16,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by_type', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse.order_by_type', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=608,
  serialized_end=1036,
)


_RETRIEVEREDUCTIONAUGMENTATIONDEMANDREQUEST = _descriptor.Descriptor(
  name='RetrieveReductionAugmentationDemandRequest',
  full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='reduction_augmentation_demand.RetrieveReductionAugmentationDemandRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1038,
  serialized_end=1094,
)


_LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTREQUEST = _descriptor.Descriptor(
  name='ListReductionAugmentationDemandProductRequest',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rule_id', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductRequest.rule_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1096,
  serialized_end=1191,
)


_LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE_ALLOCATIONPRODUCT = _descriptor.Descriptor(
  name='AllocationProduct',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_quantity', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct.allocation_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remain_quantity', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct.remain_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct.rate', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1371,
  serialized_end=1522,
)

_LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='ListReductionAugmentationDemandProductResponse',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE_ALLOCATIONPRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1194,
  serialized_end=1522,
)


_LISTREDUCTIONAUGMENTATIONDEMANDSTOREREQUEST = _descriptor.Descriptor(
  name='ListReductionAugmentationDemandStoreRequest',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rule_id', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreRequest.rule_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1524,
  serialized_end=1617,
)


_LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE_ALLOCATIONSTORE = _descriptor.Descriptor(
  name='AllocationStore',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse.AllocationStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse.AllocationStore.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse.AllocationStore.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse.AllocationStore.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1790,
  serialized_end=1865,
)

_LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE = _descriptor.Descriptor(
  name='ListReductionAugmentationDemandStoreResponse',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE_ALLOCATIONSTORE, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1620,
  serialized_end=1865,
)


_SUMDEMANDQUANTITYREQUEST = _descriptor.Descriptor(
  name='SumDemandQuantityRequest',
  full_name='reduction_augmentation_demand.SumDemandQuantityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='reduction_augmentation_demand.SumDemandQuantityRequest.product_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='reduction_augmentation_demand.SumDemandQuantityRequest.store_ids', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_demand_date', full_name='reduction_augmentation_demand.SumDemandQuantityRequest.start_demand_date', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_demand_date', full_name='reduction_augmentation_demand.SumDemandQuantityRequest.end_demand_date', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_types', full_name='reduction_augmentation_demand.SumDemandQuantityRequest.order_types', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='reduction_augmentation_demand.SumDemandQuantityRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reduction_augmentation_demand.SumDemandQuantityRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1868,
  serialized_end=2038,
)


_SUMDEMANDQUANTITYRESPONSE_SUMDEMANDPRODUCT = _descriptor.Descriptor(
  name='SumDemandProduct',
  full_name='reduction_augmentation_demand.SumDemandQuantityResponse.SumDemandProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.SumDemandQuantityResponse.SumDemandProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='reduction_augmentation_demand.SumDemandQuantityResponse.SumDemandProduct.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='reduction_augmentation_demand.SumDemandQuantityResponse.SumDemandProduct.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2174,
  serialized_end=2252,
)

_SUMDEMANDQUANTITYRESPONSE = _descriptor.Descriptor(
  name='SumDemandQuantityResponse',
  full_name='reduction_augmentation_demand.SumDemandQuantityResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='reduction_augmentation_demand.SumDemandQuantityResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='reduction_augmentation_demand.SumDemandQuantityResponse.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SUMDEMANDQUANTITYRESPONSE_SUMDEMANDPRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2041,
  serialized_end=2252,
)


_CALCULATEALLOCATIONREQUEST_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='reduction_augmentation_demand.CalculateAllocationRequest.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.CalculateAllocationRequest.Product.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_quantity', full_name='reduction_augmentation_demand.CalculateAllocationRequest.Product.allocation_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.CalculateAllocationRequest.Product.rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2627,
  serialized_end=2699,
)

_CALCULATEALLOCATIONREQUEST = _descriptor.Descriptor(
  name='CalculateAllocationRequest',
  full_name='reduction_augmentation_demand.CalculateAllocationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='products', full_name='reduction_augmentation_demand.CalculateAllocationRequest.products', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.CalculateAllocationRequest.allocation_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_demand_date', full_name='reduction_augmentation_demand.CalculateAllocationRequest.start_demand_date', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_demand_date', full_name='reduction_augmentation_demand.CalculateAllocationRequest.end_demand_date', index=3,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.CalculateAllocationRequest.rate', index=4,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by_demand_day', full_name='reduction_augmentation_demand.CalculateAllocationRequest.order_by_demand_day', index=5,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_per_time', full_name='reduction_augmentation_demand.CalculateAllocationRequest.quantity_per_time', index=6,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_reduce_to_zero', full_name='reduction_augmentation_demand.CalculateAllocationRequest.allow_reduce_to_zero', index=7,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_types', full_name='reduction_augmentation_demand.CalculateAllocationRequest.order_types', index=8,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='reduction_augmentation_demand.CalculateAllocationRequest.store_ids', index=9,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_id', full_name='reduction_augmentation_demand.CalculateAllocationRequest.rule_id', index=10,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by_type', full_name='reduction_augmentation_demand.CalculateAllocationRequest.order_by_type', index=11,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CALCULATEALLOCATIONREQUEST_PRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2255,
  serialized_end=2699,
)


_CALCULATEALLOCATIONRESPONSE_REMAINPRODUCT = _descriptor.Descriptor(
  name='RemainProduct',
  full_name='reduction_augmentation_demand.CalculateAllocationResponse.RemainProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.CalculateAllocationResponse.RemainProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='reduction_augmentation_demand.CalculateAllocationResponse.RemainProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='reduction_augmentation_demand.CalculateAllocationResponse.RemainProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remain_quantity', full_name='reduction_augmentation_demand.CalculateAllocationResponse.RemainProduct.remain_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2953,
  serialized_end=3057,
)

_CALCULATEALLOCATIONRESPONSE_ALLOCATIONRESULT = _descriptor.Descriptor(
  name='AllocationResult',
  full_name='reduction_augmentation_demand.CalculateAllocationResponse.AllocationResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.CalculateAllocationResponse.AllocationResult.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='reduction_augmentation_demand.CalculateAllocationResponse.AllocationResult.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='reduction_augmentation_demand.CalculateAllocationResponse.AllocationResult.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_count', full_name='reduction_augmentation_demand.CalculateAllocationResponse.AllocationResult.store_count', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_quantity', full_name='reduction_augmentation_demand.CalculateAllocationResponse.AllocationResult.allocation_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3060,
  serialized_end=3192,
)

_CALCULATEALLOCATIONRESPONSE = _descriptor.Descriptor(
  name='CalculateAllocationResponse',
  full_name='reduction_augmentation_demand.CalculateAllocationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='remain_products', full_name='reduction_augmentation_demand.CalculateAllocationResponse.remain_products', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_result', full_name='reduction_augmentation_demand.CalculateAllocationResponse.allocation_result', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_id', full_name='reduction_augmentation_demand.CalculateAllocationResponse.rule_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CALCULATEALLOCATIONRESPONSE_REMAINPRODUCT, _CALCULATEALLOCATIONRESPONSE_ALLOCATIONRESULT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2702,
  serialized_end=3192,
)


_LISTCALCULATEALLOCATIONRESULTREQUEST = _descriptor.Descriptor(
  name='ListCalculateAllocationResultRequest',
  full_name='reduction_augmentation_demand.ListCalculateAllocationResultRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rule_id', full_name='reduction_augmentation_demand.ListCalculateAllocationResultRequest.rule_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='reduction_augmentation_demand.ListCalculateAllocationResultRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reduction_augmentation_demand.ListCalculateAllocationResultRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3194,
  serialized_end=3280,
)


_LISTCALCULATEALLOCATIONRESULTRESPONSE_ALLOCATIONDETAIL = _descriptor.Descriptor(
  name='AllocationDetail',
  full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_code', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.product_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_quantity', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.demand_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_quantity', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.allocation_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='final_quantity', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.final_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.order_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_source', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.order_source', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.order_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.product_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail.store_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3441,
  serialized_end=3721,
)

_LISTCALCULATEALLOCATIONRESULTRESPONSE = _descriptor.Descriptor(
  name='ListCalculateAllocationResultResponse',
  full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='reduction_augmentation_demand.ListCalculateAllocationResultResponse.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTCALCULATEALLOCATIONRESULTRESPONSE_ALLOCATIONDETAIL, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3283,
  serialized_end=3721,
)


_CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.Product.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_quantity', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.Product.allocation_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.Product.rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remain_quantity', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.Product.remain_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4180,
  serialized_end=4277,
)

_CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST = _descriptor.Descriptor(
  name='CreateReductionAugmentationDemandRequest',
  full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='company', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.company', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.allocation_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_demand_date', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.start_demand_date', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_demand_date', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.end_demand_date', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.rate', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by_demand_day', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.order_by_demand_day', index=7,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_per_time', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.quantity_per_time', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_reduce_to_zero', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.allow_reduce_to_zero', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_types', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.order_types', index=10,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.store_ids', index=11,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.products', index=12,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculated_rule_id', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.calculated_rule_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by_type', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.order_by_type', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3724,
  serialized_end=4277,
)


_CREATEREDUCTIONAUGMENTATIONDEMANDRESPONSE = _descriptor.Descriptor(
  name='CreateReductionAugmentationDemandResponse',
  full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4279,
  serialized_end=4334,
)


_UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.Product.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_quantity', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.Product.allocation_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.Product.rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remain_quantity', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.Product.remain_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4180,
  serialized_end=4277,
)

_UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST = _descriptor.Descriptor(
  name='UpdateReductionAugmentationDemandRequest',
  full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='company', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.company', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.allocation_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_demand_date', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.start_demand_date', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_demand_date', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.end_demand_date', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.rate', index=5,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by_demand_day', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.order_by_demand_day', index=6,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_per_time', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.quantity_per_time', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_reduce_to_zero', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.allow_reduce_to_zero', index=8,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_types', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.order_types', index=9,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.store_ids', index=10,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.products', index=11,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculated_rule_id', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.calculated_rule_id', index=12,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.id', index=13,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.status', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by_type', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.order_by_type', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4337,
  serialized_end=4904,
)


_UPDATEREDUCTIONAUGMENTATIONDEMANDRESPONSE = _descriptor.Descriptor(
  name='UpdateReductionAugmentationDemandResponse',
  full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='reduction_augmentation_demand.UpdateReductionAugmentationDemandResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4906,
  serialized_end=4965,
)


_DESTROYCALCULATEALLOCATIONREQUEST = _descriptor.Descriptor(
  name='DestroyCalculateAllocationRequest',
  full_name='reduction_augmentation_demand.DestroyCalculateAllocationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rule_id', full_name='reduction_augmentation_demand.DestroyCalculateAllocationRequest.rule_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4967,
  serialized_end=5019,
)


_DESTROYCALCULATEALLOCATIONRESPONSE = _descriptor.Descriptor(
  name='DestroyCalculateAllocationResponse',
  full_name='reduction_augmentation_demand.DestroyCalculateAllocationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='reduction_augmentation_demand.DestroyCalculateAllocationResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5021,
  serialized_end=5073,
)


_LISTCALCULATEALLOCATIONSTOREREQUEST = _descriptor.Descriptor(
  name='ListCalculateAllocationStoreRequest',
  full_name='reduction_augmentation_demand.ListCalculateAllocationStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rule_id', full_name='reduction_augmentation_demand.ListCalculateAllocationStoreRequest.rule_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='reduction_augmentation_demand.ListCalculateAllocationStoreRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reduction_augmentation_demand.ListCalculateAllocationStoreRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='reduction_augmentation_demand.ListCalculateAllocationStoreRequest.store_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5075,
  serialized_end=5179,
)


_LISTCALCULATEALLOCATIONSTORERESPONSE_STORE = _descriptor.Descriptor(
  name='Store',
  full_name='reduction_augmentation_demand.ListCalculateAllocationStoreResponse.Store',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='reduction_augmentation_demand.ListCalculateAllocationStoreResponse.Store.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='reduction_augmentation_demand.ListCalculateAllocationStoreResponse.Store.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='reduction_augmentation_demand.ListCalculateAllocationStoreResponse.Store.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5311,
  serialized_end=5376,
)

_LISTCALCULATEALLOCATIONSTORERESPONSE = _descriptor.Descriptor(
  name='ListCalculateAllocationStoreResponse',
  full_name='reduction_augmentation_demand.ListCalculateAllocationStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='reduction_augmentation_demand.ListCalculateAllocationStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTCALCULATEALLOCATIONSTORERESPONSE_STORE, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5182,
  serialized_end=5376,
)


_LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST = _descriptor.Descriptor(
  name='ListReductionAugmentationDemandExcludedStoreRequest',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreRequest.allocation_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5378,
  serialized_end=5505,
)


_LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE_STORE = _descriptor.Descriptor(
  name='Store',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.Store',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.Store.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.Store.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.Store.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.Store.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.Store.allocation_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5686,
  serialized_end=5788,
)

_LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE = _descriptor.Descriptor(
  name='ListReductionAugmentationDemandExcludedStoreResponse',
  full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stores', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.stores', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE_STORE, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5508,
  serialized_end=5788,
)


_CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST_STORE = _descriptor.Descriptor(
  name='Store',
  full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest.Store',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest.Store.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest.Store.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5311,
  serialized_end=5356,
)

_CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST = _descriptor.Descriptor(
  name='CreateReductionAugmentationDemandExcludedStoreRequest',
  full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stores', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest.stores', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_type', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest.allocation_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST_STORE, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5791,
  serialized_end=6026,
)


_CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE = _descriptor.Descriptor(
  name='CreateReductionAugmentationDemandExcludedStoreResponse',
  full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6028,
  serialized_end=6100,
)


_DESTROYREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST = _descriptor.Descriptor(
  name='DestroyReductionAugmentationDemandExcludedStoreRequest',
  full_name='reduction_augmentation_demand.DestroyReductionAugmentationDemandExcludedStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='reduction_augmentation_demand.DestroyReductionAugmentationDemandExcludedStoreRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6102,
  serialized_end=6170,
)


_DESTROYREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE = _descriptor.Descriptor(
  name='DestroyReductionAugmentationDemandExcludedStoreResponse',
  full_name='reduction_augmentation_demand.DestroyReductionAugmentationDemandExcludedStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='reduction_augmentation_demand.DestroyReductionAugmentationDemandExcludedStoreResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6172,
  serialized_end=6245,
)


_REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGREQUEST = _descriptor.Descriptor(
  name='ReductionAugmentationDemandProductAggRequest',
  full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rule_id', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggRequest.rule_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6247,
  serialized_end=6341,
)


_REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE_ALLOCATIONRESULT = _descriptor.Descriptor(
  name='AllocationResult',
  full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.AllocationResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.AllocationResult.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.AllocationResult.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.AllocationResult.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_count', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.AllocationResult.store_count', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allocation_quantity', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.AllocationResult.allocation_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3060,
  serialized_end=3192,
)

_REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE = _descriptor.Descriptor(
  name='ReductionAugmentationDemandProductAggResponse',
  full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE_ALLOCATIONRESULT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6344,
  serialized_end=6650,
)

_LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE_DETAIL.containing_type = _LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE
_LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE.fields_by_name['rows'].message_type = _LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE_DETAIL
_LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE_ALLOCATIONPRODUCT.containing_type = _LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE
_LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE.fields_by_name['rows'].message_type = _LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE_ALLOCATIONPRODUCT
_LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE_ALLOCATIONSTORE.containing_type = _LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE
_LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE.fields_by_name['rows'].message_type = _LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE_ALLOCATIONSTORE
_SUMDEMANDQUANTITYRESPONSE_SUMDEMANDPRODUCT.containing_type = _SUMDEMANDQUANTITYRESPONSE
_SUMDEMANDQUANTITYRESPONSE.fields_by_name['rows'].message_type = _SUMDEMANDQUANTITYRESPONSE_SUMDEMANDPRODUCT
_CALCULATEALLOCATIONREQUEST_PRODUCT.containing_type = _CALCULATEALLOCATIONREQUEST
_CALCULATEALLOCATIONREQUEST.fields_by_name['products'].message_type = _CALCULATEALLOCATIONREQUEST_PRODUCT
_CALCULATEALLOCATIONRESPONSE_REMAINPRODUCT.containing_type = _CALCULATEALLOCATIONRESPONSE
_CALCULATEALLOCATIONRESPONSE_ALLOCATIONRESULT.containing_type = _CALCULATEALLOCATIONRESPONSE
_CALCULATEALLOCATIONRESPONSE.fields_by_name['remain_products'].message_type = _CALCULATEALLOCATIONRESPONSE_REMAINPRODUCT
_CALCULATEALLOCATIONRESPONSE.fields_by_name['allocation_result'].message_type = _CALCULATEALLOCATIONRESPONSE_ALLOCATIONRESULT
_LISTCALCULATEALLOCATIONRESULTRESPONSE_ALLOCATIONDETAIL.containing_type = _LISTCALCULATEALLOCATIONRESULTRESPONSE
_LISTCALCULATEALLOCATIONRESULTRESPONSE.fields_by_name['rows'].message_type = _LISTCALCULATEALLOCATIONRESULTRESPONSE_ALLOCATIONDETAIL
_CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT.containing_type = _CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST
_CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST.fields_by_name['products'].message_type = _CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT
_UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT.containing_type = _UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST
_UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST.fields_by_name['products'].message_type = _UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT
_LISTCALCULATEALLOCATIONSTORERESPONSE_STORE.containing_type = _LISTCALCULATEALLOCATIONSTORERESPONSE
_LISTCALCULATEALLOCATIONSTORERESPONSE.fields_by_name['rows'].message_type = _LISTCALCULATEALLOCATIONSTORERESPONSE_STORE
_LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE_STORE.containing_type = _LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE
_LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE.fields_by_name['stores'].message_type = _LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE_STORE
_CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST_STORE.containing_type = _CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST
_CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST.fields_by_name['stores'].message_type = _CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST_STORE
_REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE_ALLOCATIONRESULT.containing_type = _REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE
_REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE.fields_by_name['rows'].message_type = _REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE_ALLOCATIONRESULT
DESCRIPTOR.message_types_by_name['ListReductionAugmentationDemandRequest'] = _LISTREDUCTIONAUGMENTATIONDEMANDREQUEST
DESCRIPTOR.message_types_by_name['ListReductionAugmentationDemandResponse'] = _LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['RetrieveReductionAugmentationDemandResponse'] = _RETRIEVEREDUCTIONAUGMENTATIONDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['RetrieveReductionAugmentationDemandRequest'] = _RETRIEVEREDUCTIONAUGMENTATIONDEMANDREQUEST
DESCRIPTOR.message_types_by_name['ListReductionAugmentationDemandProductRequest'] = _LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['ListReductionAugmentationDemandProductResponse'] = _LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['ListReductionAugmentationDemandStoreRequest'] = _LISTREDUCTIONAUGMENTATIONDEMANDSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListReductionAugmentationDemandStoreResponse'] = _LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE
DESCRIPTOR.message_types_by_name['SumDemandQuantityRequest'] = _SUMDEMANDQUANTITYREQUEST
DESCRIPTOR.message_types_by_name['SumDemandQuantityResponse'] = _SUMDEMANDQUANTITYRESPONSE
DESCRIPTOR.message_types_by_name['CalculateAllocationRequest'] = _CALCULATEALLOCATIONREQUEST
DESCRIPTOR.message_types_by_name['CalculateAllocationResponse'] = _CALCULATEALLOCATIONRESPONSE
DESCRIPTOR.message_types_by_name['ListCalculateAllocationResultRequest'] = _LISTCALCULATEALLOCATIONRESULTREQUEST
DESCRIPTOR.message_types_by_name['ListCalculateAllocationResultResponse'] = _LISTCALCULATEALLOCATIONRESULTRESPONSE
DESCRIPTOR.message_types_by_name['CreateReductionAugmentationDemandRequest'] = _CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST
DESCRIPTOR.message_types_by_name['CreateReductionAugmentationDemandResponse'] = _CREATEREDUCTIONAUGMENTATIONDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['UpdateReductionAugmentationDemandRequest'] = _UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST
DESCRIPTOR.message_types_by_name['UpdateReductionAugmentationDemandResponse'] = _UPDATEREDUCTIONAUGMENTATIONDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['DestroyCalculateAllocationRequest'] = _DESTROYCALCULATEALLOCATIONREQUEST
DESCRIPTOR.message_types_by_name['DestroyCalculateAllocationResponse'] = _DESTROYCALCULATEALLOCATIONRESPONSE
DESCRIPTOR.message_types_by_name['ListCalculateAllocationStoreRequest'] = _LISTCALCULATEALLOCATIONSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListCalculateAllocationStoreResponse'] = _LISTCALCULATEALLOCATIONSTORERESPONSE
DESCRIPTOR.message_types_by_name['ListReductionAugmentationDemandExcludedStoreRequest'] = _LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListReductionAugmentationDemandExcludedStoreResponse'] = _LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE
DESCRIPTOR.message_types_by_name['CreateReductionAugmentationDemandExcludedStoreRequest'] = _CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST
DESCRIPTOR.message_types_by_name['CreateReductionAugmentationDemandExcludedStoreResponse'] = _CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE
DESCRIPTOR.message_types_by_name['DestroyReductionAugmentationDemandExcludedStoreRequest'] = _DESTROYREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST
DESCRIPTOR.message_types_by_name['DestroyReductionAugmentationDemandExcludedStoreResponse'] = _DESTROYREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE
DESCRIPTOR.message_types_by_name['ReductionAugmentationDemandProductAggRequest'] = _REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGREQUEST
DESCRIPTOR.message_types_by_name['ReductionAugmentationDemandProductAggResponse'] = _REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListReductionAugmentationDemandRequest = _reflection.GeneratedProtocolMessageType('ListReductionAugmentationDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandRequest)
  ))
_sym_db.RegisterMessage(ListReductionAugmentationDemandRequest)

ListReductionAugmentationDemandResponse = _reflection.GeneratedProtocolMessageType('ListReductionAugmentationDemandResponse', (_message.Message,), dict(

  Detail = _reflection.GeneratedProtocolMessageType('Detail', (_message.Message,), dict(
    DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE_DETAIL,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandResponse.Detail)
    ))
  ,
  DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandResponse)
  ))
_sym_db.RegisterMessage(ListReductionAugmentationDemandResponse)
_sym_db.RegisterMessage(ListReductionAugmentationDemandResponse.Detail)

RetrieveReductionAugmentationDemandResponse = _reflection.GeneratedProtocolMessageType('RetrieveReductionAugmentationDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _RETRIEVEREDUCTIONAUGMENTATIONDEMANDRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.RetrieveReductionAugmentationDemandResponse)
  ))
_sym_db.RegisterMessage(RetrieveReductionAugmentationDemandResponse)

RetrieveReductionAugmentationDemandRequest = _reflection.GeneratedProtocolMessageType('RetrieveReductionAugmentationDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _RETRIEVEREDUCTIONAUGMENTATIONDEMANDREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.RetrieveReductionAugmentationDemandRequest)
  ))
_sym_db.RegisterMessage(RetrieveReductionAugmentationDemandRequest)

ListReductionAugmentationDemandProductRequest = _reflection.GeneratedProtocolMessageType('ListReductionAugmentationDemandProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandProductRequest)
  ))
_sym_db.RegisterMessage(ListReductionAugmentationDemandProductRequest)

ListReductionAugmentationDemandProductResponse = _reflection.GeneratedProtocolMessageType('ListReductionAugmentationDemandProductResponse', (_message.Message,), dict(

  AllocationProduct = _reflection.GeneratedProtocolMessageType('AllocationProduct', (_message.Message,), dict(
    DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE_ALLOCATIONPRODUCT,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse.AllocationProduct)
    ))
  ,
  DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandProductResponse)
  ))
_sym_db.RegisterMessage(ListReductionAugmentationDemandProductResponse)
_sym_db.RegisterMessage(ListReductionAugmentationDemandProductResponse.AllocationProduct)

ListReductionAugmentationDemandStoreRequest = _reflection.GeneratedProtocolMessageType('ListReductionAugmentationDemandStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDSTOREREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandStoreRequest)
  ))
_sym_db.RegisterMessage(ListReductionAugmentationDemandStoreRequest)

ListReductionAugmentationDemandStoreResponse = _reflection.GeneratedProtocolMessageType('ListReductionAugmentationDemandStoreResponse', (_message.Message,), dict(

  AllocationStore = _reflection.GeneratedProtocolMessageType('AllocationStore', (_message.Message,), dict(
    DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE_ALLOCATIONSTORE,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse.AllocationStore)
    ))
  ,
  DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandStoreResponse)
  ))
_sym_db.RegisterMessage(ListReductionAugmentationDemandStoreResponse)
_sym_db.RegisterMessage(ListReductionAugmentationDemandStoreResponse.AllocationStore)

SumDemandQuantityRequest = _reflection.GeneratedProtocolMessageType('SumDemandQuantityRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUMDEMANDQUANTITYREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.SumDemandQuantityRequest)
  ))
_sym_db.RegisterMessage(SumDemandQuantityRequest)

SumDemandQuantityResponse = _reflection.GeneratedProtocolMessageType('SumDemandQuantityResponse', (_message.Message,), dict(

  SumDemandProduct = _reflection.GeneratedProtocolMessageType('SumDemandProduct', (_message.Message,), dict(
    DESCRIPTOR = _SUMDEMANDQUANTITYRESPONSE_SUMDEMANDPRODUCT,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.SumDemandQuantityResponse.SumDemandProduct)
    ))
  ,
  DESCRIPTOR = _SUMDEMANDQUANTITYRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.SumDemandQuantityResponse)
  ))
_sym_db.RegisterMessage(SumDemandQuantityResponse)
_sym_db.RegisterMessage(SumDemandQuantityResponse.SumDemandProduct)

CalculateAllocationRequest = _reflection.GeneratedProtocolMessageType('CalculateAllocationRequest', (_message.Message,), dict(

  Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
    DESCRIPTOR = _CALCULATEALLOCATIONREQUEST_PRODUCT,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CalculateAllocationRequest.Product)
    ))
  ,
  DESCRIPTOR = _CALCULATEALLOCATIONREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CalculateAllocationRequest)
  ))
_sym_db.RegisterMessage(CalculateAllocationRequest)
_sym_db.RegisterMessage(CalculateAllocationRequest.Product)

CalculateAllocationResponse = _reflection.GeneratedProtocolMessageType('CalculateAllocationResponse', (_message.Message,), dict(

  RemainProduct = _reflection.GeneratedProtocolMessageType('RemainProduct', (_message.Message,), dict(
    DESCRIPTOR = _CALCULATEALLOCATIONRESPONSE_REMAINPRODUCT,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CalculateAllocationResponse.RemainProduct)
    ))
  ,

  AllocationResult = _reflection.GeneratedProtocolMessageType('AllocationResult', (_message.Message,), dict(
    DESCRIPTOR = _CALCULATEALLOCATIONRESPONSE_ALLOCATIONRESULT,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CalculateAllocationResponse.AllocationResult)
    ))
  ,
  DESCRIPTOR = _CALCULATEALLOCATIONRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CalculateAllocationResponse)
  ))
_sym_db.RegisterMessage(CalculateAllocationResponse)
_sym_db.RegisterMessage(CalculateAllocationResponse.RemainProduct)
_sym_db.RegisterMessage(CalculateAllocationResponse.AllocationResult)

ListCalculateAllocationResultRequest = _reflection.GeneratedProtocolMessageType('ListCalculateAllocationResultRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTCALCULATEALLOCATIONRESULTREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListCalculateAllocationResultRequest)
  ))
_sym_db.RegisterMessage(ListCalculateAllocationResultRequest)

ListCalculateAllocationResultResponse = _reflection.GeneratedProtocolMessageType('ListCalculateAllocationResultResponse', (_message.Message,), dict(

  AllocationDetail = _reflection.GeneratedProtocolMessageType('AllocationDetail', (_message.Message,), dict(
    DESCRIPTOR = _LISTCALCULATEALLOCATIONRESULTRESPONSE_ALLOCATIONDETAIL,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListCalculateAllocationResultResponse.AllocationDetail)
    ))
  ,
  DESCRIPTOR = _LISTCALCULATEALLOCATIONRESULTRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListCalculateAllocationResultResponse)
  ))
_sym_db.RegisterMessage(ListCalculateAllocationResultResponse)
_sym_db.RegisterMessage(ListCalculateAllocationResultResponse.AllocationDetail)

CreateReductionAugmentationDemandRequest = _reflection.GeneratedProtocolMessageType('CreateReductionAugmentationDemandRequest', (_message.Message,), dict(

  Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
    DESCRIPTOR = _CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CreateReductionAugmentationDemandRequest.Product)
    ))
  ,
  DESCRIPTOR = _CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CreateReductionAugmentationDemandRequest)
  ))
_sym_db.RegisterMessage(CreateReductionAugmentationDemandRequest)
_sym_db.RegisterMessage(CreateReductionAugmentationDemandRequest.Product)

CreateReductionAugmentationDemandResponse = _reflection.GeneratedProtocolMessageType('CreateReductionAugmentationDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEREDUCTIONAUGMENTATIONDEMANDRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CreateReductionAugmentationDemandResponse)
  ))
_sym_db.RegisterMessage(CreateReductionAugmentationDemandResponse)

UpdateReductionAugmentationDemandRequest = _reflection.GeneratedProtocolMessageType('UpdateReductionAugmentationDemandRequest', (_message.Message,), dict(

  Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
    DESCRIPTOR = _UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST_PRODUCT,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest.Product)
    ))
  ,
  DESCRIPTOR = _UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.UpdateReductionAugmentationDemandRequest)
  ))
_sym_db.RegisterMessage(UpdateReductionAugmentationDemandRequest)
_sym_db.RegisterMessage(UpdateReductionAugmentationDemandRequest.Product)

UpdateReductionAugmentationDemandResponse = _reflection.GeneratedProtocolMessageType('UpdateReductionAugmentationDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEREDUCTIONAUGMENTATIONDEMANDRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.UpdateReductionAugmentationDemandResponse)
  ))
_sym_db.RegisterMessage(UpdateReductionAugmentationDemandResponse)

DestroyCalculateAllocationRequest = _reflection.GeneratedProtocolMessageType('DestroyCalculateAllocationRequest', (_message.Message,), dict(
  DESCRIPTOR = _DESTROYCALCULATEALLOCATIONREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.DestroyCalculateAllocationRequest)
  ))
_sym_db.RegisterMessage(DestroyCalculateAllocationRequest)

DestroyCalculateAllocationResponse = _reflection.GeneratedProtocolMessageType('DestroyCalculateAllocationResponse', (_message.Message,), dict(
  DESCRIPTOR = _DESTROYCALCULATEALLOCATIONRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.DestroyCalculateAllocationResponse)
  ))
_sym_db.RegisterMessage(DestroyCalculateAllocationResponse)

ListCalculateAllocationStoreRequest = _reflection.GeneratedProtocolMessageType('ListCalculateAllocationStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTCALCULATEALLOCATIONSTOREREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListCalculateAllocationStoreRequest)
  ))
_sym_db.RegisterMessage(ListCalculateAllocationStoreRequest)

ListCalculateAllocationStoreResponse = _reflection.GeneratedProtocolMessageType('ListCalculateAllocationStoreResponse', (_message.Message,), dict(

  Store = _reflection.GeneratedProtocolMessageType('Store', (_message.Message,), dict(
    DESCRIPTOR = _LISTCALCULATEALLOCATIONSTORERESPONSE_STORE,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListCalculateAllocationStoreResponse.Store)
    ))
  ,
  DESCRIPTOR = _LISTCALCULATEALLOCATIONSTORERESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListCalculateAllocationStoreResponse)
  ))
_sym_db.RegisterMessage(ListCalculateAllocationStoreResponse)
_sym_db.RegisterMessage(ListCalculateAllocationStoreResponse.Store)

ListReductionAugmentationDemandExcludedStoreRequest = _reflection.GeneratedProtocolMessageType('ListReductionAugmentationDemandExcludedStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreRequest)
  ))
_sym_db.RegisterMessage(ListReductionAugmentationDemandExcludedStoreRequest)

ListReductionAugmentationDemandExcludedStoreResponse = _reflection.GeneratedProtocolMessageType('ListReductionAugmentationDemandExcludedStoreResponse', (_message.Message,), dict(

  Store = _reflection.GeneratedProtocolMessageType('Store', (_message.Message,), dict(
    DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE_STORE,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse.Store)
    ))
  ,
  DESCRIPTOR = _LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ListReductionAugmentationDemandExcludedStoreResponse)
  ))
_sym_db.RegisterMessage(ListReductionAugmentationDemandExcludedStoreResponse)
_sym_db.RegisterMessage(ListReductionAugmentationDemandExcludedStoreResponse.Store)

CreateReductionAugmentationDemandExcludedStoreRequest = _reflection.GeneratedProtocolMessageType('CreateReductionAugmentationDemandExcludedStoreRequest', (_message.Message,), dict(

  Store = _reflection.GeneratedProtocolMessageType('Store', (_message.Message,), dict(
    DESCRIPTOR = _CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST_STORE,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest.Store)
    ))
  ,
  DESCRIPTOR = _CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreRequest)
  ))
_sym_db.RegisterMessage(CreateReductionAugmentationDemandExcludedStoreRequest)
_sym_db.RegisterMessage(CreateReductionAugmentationDemandExcludedStoreRequest.Store)

CreateReductionAugmentationDemandExcludedStoreResponse = _reflection.GeneratedProtocolMessageType('CreateReductionAugmentationDemandExcludedStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.CreateReductionAugmentationDemandExcludedStoreResponse)
  ))
_sym_db.RegisterMessage(CreateReductionAugmentationDemandExcludedStoreResponse)

DestroyReductionAugmentationDemandExcludedStoreRequest = _reflection.GeneratedProtocolMessageType('DestroyReductionAugmentationDemandExcludedStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _DESTROYREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.DestroyReductionAugmentationDemandExcludedStoreRequest)
  ))
_sym_db.RegisterMessage(DestroyReductionAugmentationDemandExcludedStoreRequest)

DestroyReductionAugmentationDemandExcludedStoreResponse = _reflection.GeneratedProtocolMessageType('DestroyReductionAugmentationDemandExcludedStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _DESTROYREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.DestroyReductionAugmentationDemandExcludedStoreResponse)
  ))
_sym_db.RegisterMessage(DestroyReductionAugmentationDemandExcludedStoreResponse)

ReductionAugmentationDemandProductAggRequest = _reflection.GeneratedProtocolMessageType('ReductionAugmentationDemandProductAggRequest', (_message.Message,), dict(
  DESCRIPTOR = _REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGREQUEST,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ReductionAugmentationDemandProductAggRequest)
  ))
_sym_db.RegisterMessage(ReductionAugmentationDemandProductAggRequest)

ReductionAugmentationDemandProductAggResponse = _reflection.GeneratedProtocolMessageType('ReductionAugmentationDemandProductAggResponse', (_message.Message,), dict(

  AllocationResult = _reflection.GeneratedProtocolMessageType('AllocationResult', (_message.Message,), dict(
    DESCRIPTOR = _REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE_ALLOCATIONRESULT,
    __module__ = 'reduction_augmentation_demand_pb2'
    # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse.AllocationResult)
    ))
  ,
  DESCRIPTOR = _REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE,
  __module__ = 'reduction_augmentation_demand_pb2'
  # @@protoc_insertion_point(class_scope:reduction_augmentation_demand.ReductionAugmentationDemandProductAggResponse)
  ))
_sym_db.RegisterMessage(ReductionAugmentationDemandProductAggResponse)
_sym_db.RegisterMessage(ReductionAugmentationDemandProductAggResponse.AllocationResult)



_REDUCTIONAUGMENTATIONDEMAND = _descriptor.ServiceDescriptor(
  name='ReductionAugmentationDemand',
  full_name='reduction_augmentation_demand.ReductionAugmentationDemand',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=6653,
  serialized_end=10203,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListReductionAugmentationDemand',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.ListReductionAugmentationDemand',
    index=0,
    containing_service=None,
    input_type=_LISTREDUCTIONAUGMENTATIONDEMANDREQUEST,
    output_type=_LISTREDUCTIONAUGMENTATIONDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/reduction_augmentation_demand'),
  ),
  _descriptor.MethodDescriptor(
    name='RetrieveReductionAugmentationDemand',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.RetrieveReductionAugmentationDemand',
    index=1,
    containing_service=None,
    input_type=_RETRIEVEREDUCTIONAUGMENTATIONDEMANDREQUEST,
    output_type=_RETRIEVEREDUCTIONAUGMENTATIONDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002;\0229/api/v2/supply/reduction_augmentation_demand/with/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateReductionAugmentationDemand',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.CreateReductionAugmentationDemand',
    index=2,
    containing_service=None,
    input_type=_CREATEREDUCTIONAUGMENTATIONDEMANDREQUEST,
    output_type=_CREATEREDUCTIONAUGMENTATIONDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\",/api/v2/supply/reduction_augmentation_demand'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReductionAugmentationDemand',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.UpdateReductionAugmentationDemand',
    index=3,
    containing_service=None,
    input_type=_UPDATEREDUCTIONAUGMENTATIONDEMANDREQUEST,
    output_type=_UPDATEREDUCTIONAUGMENTATIONDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002321/api/v2/supply/reduction_augmentation_demand/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReductionAugmentationDemandProduct',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.ListReductionAugmentationDemandProduct',
    index=4,
    containing_service=None,
    input_type=_LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTREQUEST,
    output_type=_LISTREDUCTIONAUGMENTATIONDEMANDPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\0224/api/v2/supply/reduction_augmentation_demand/product'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReductionAugmentationDemandStore',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.ListReductionAugmentationDemandStore',
    index=5,
    containing_service=None,
    input_type=_LISTREDUCTIONAUGMENTATIONDEMANDSTOREREQUEST,
    output_type=_LISTREDUCTIONAUGMENTATIONDEMANDSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/supply/reduction_augmentation_demand/store'),
  ),
  _descriptor.MethodDescriptor(
    name='SumDemandQuantity',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.SumDemandQuantity',
    index=6,
    containing_service=None,
    input_type=_SUMDEMANDQUANTITYREQUEST,
    output_type=_SUMDEMANDQUANTITYRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"0/api/v2/supply/reduction_augmentation_demand/sum'),
  ),
  _descriptor.MethodDescriptor(
    name='CalculateAllocation',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.CalculateAllocation',
    index=7,
    containing_service=None,
    input_type=_CALCULATEALLOCATIONREQUEST,
    output_type=_CALCULATEALLOCATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"6/api/v2/supply/reduction_augmentation_demand/calculate'),
  ),
  _descriptor.MethodDescriptor(
    name='ListCalculateAllocationResult',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.ListCalculateAllocationResult',
    index=8,
    containing_service=None,
    input_type=_LISTCALCULATEALLOCATIONRESULTREQUEST,
    output_type=_LISTCALCULATEALLOCATIONRESULTRESPONSE,
    serialized_options=_b('\202\323\344\223\002B\022@/api/v2/supply/reduction_augmentation_demand/calculate/{rule_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='DestroyCalculateAllocation',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.DestroyCalculateAllocation',
    index=9,
    containing_service=None,
    input_type=_DESTROYCALCULATEALLOCATIONREQUEST,
    output_type=_DESTROYCALCULATEALLOCATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002B*@/api/v2/supply/reduction_augmentation_demand/calculate/{rule_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReductionAugmentationDemandExcludedStore',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.ListReductionAugmentationDemandExcludedStore',
    index=10,
    containing_service=None,
    input_type=_LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST,
    output_type=_LISTREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002=\022;/api/v2/supply/reduction_augmentation_demand/excluded/store'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateReductionAugmentationDemandExcludedStore',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.CreateReductionAugmentationDemandExcludedStore',
    index=11,
    containing_service=None,
    input_type=_CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST,
    output_type=_CREATEREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002=\";/api/v2/supply/reduction_augmentation_demand/excluded/store'),
  ),
  _descriptor.MethodDescriptor(
    name='DestroyReductionAugmentationDemandExcludedStore',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.DestroyReductionAugmentationDemandExcludedStore',
    index=12,
    containing_service=None,
    input_type=_DESTROYREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTOREREQUEST,
    output_type=_DESTROYREDUCTIONAUGMENTATIONDEMANDEXCLUDEDSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002B*@/api/v2/supply/reduction_augmentation_demand/excluded/store/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ReductionAugmentationDemandProductAgg',
    full_name='reduction_augmentation_demand.ReductionAugmentationDemand.ReductionAugmentationDemandProductAgg',
    index=13,
    containing_service=None,
    input_type=_REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGREQUEST,
    output_type=_REDUCTIONAUGMENTATIONDEMANDPRODUCTAGGRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0228/api/v2/supply/reduction_augmentation_demand/product/agg'),
  ),
])
_sym_db.RegisterServiceDescriptor(_REDUCTIONAUGMENTATIONDEMAND)

DESCRIPTOR.services_by_name['ReductionAugmentationDemand'] = _REDUCTIONAUGMENTATIONDEMAND

# @@protoc_insertion_point(module_scope)
