#!/usr/bin/env bash
python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. *.proto ./google/api/*.proto ./metadata/*.proto ./inventory/*.proto ./bom/*.proto ./report/*.proto ./cost/*.proto ./metadata/*/*.proto ./receipt/*.proto ./receipt/franchisee/*.proto ./mobile/*.proto ./warehouse/*.proto ./manufactory/*.proto ./store/*.proto  ./store_extra/*.proto ./ianvs/*.proto ./supply_config/*.proto ./hd_management/*.proto ./frs_management/*.proto ./products_manage/*.proto  ./frs_store_extra/*.proto ./franchisee/*.proto ./frs_store/*.proto ./store_bi/*.proto ./business_rule/*.proto ./price_center/*.proto ./store_management/*.proto
#python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. supply.proto metadata.proto demand.proto ./google/api/*.proto
#### 生成schema swagger文档
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. supply.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. adjust.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. receiving_diff.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. receiving.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. returns.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. stocktake.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. transfer.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. hd_management/purchase_review.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. frs_management/franchisee_demand.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. frs_management/franchisee_refund.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. franchisee/vouchers.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. frs_management/franchise_hd_assignment.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. mobile/mobile_franchisee_demand.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. mobile/mobile_franchisee_refund.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. mobile/mobile_transfer.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. mobile/mobile_adjust.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. warehouse/adjust.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. store/adjust.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. receipt/order.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. manufactory/adjust.proto
#protoc -I. -I./google/api --swagger_out=logtostderr=true:. ./reduction_augmentation_demand.proto
