# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: attachments.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='attachments.proto',
  package='attachments',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x11\x61ttachments.proto\x12\x0b\x61ttachments\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"3\n\x08Response\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\"u\n\x12NewAttachmentsList\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x10\n\x08\x64oc_type\x18\x02 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x03 \x03(\t\x12\x11\n\tsignature\x18\x04 \x01(\t\x12\x15\n\rnosign_reason\x18\x05 \x01(\t\"\x8e\x02\n\x0f\x41ttachmentsList\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x10\n\x08\x64oc_type\x18\x02 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x03 \x03(\t\x12\x11\n\tsignature\x18\x04 \x01(\t\x12\x15\n\rnosign_reason\x18\x05 \x01(\t\x12.\n\ncreated_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x0c \x01(\x04\x12.\n\nupdated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x0e \x01(\x04\x12\x12\n\npartner_id\x18\x0f \x01(\x04\"0\n\x0c\x44ocIdRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x10\n\x08\x64oc_type\x18\x02 \x01(\t2\xb9\x04\n\x12\x41ttachmentsService\x12r\n\x11\x43reateAttachments\x12\x1f.attachments.NewAttachmentsList\x1a\x15.attachments.Response\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v2/supply/attachments:\x01*\x12\x88\x01\n\x15GetAttachmentsByDocId\x12\x19.attachments.DocIdRequest\x1a\x1c.attachments.AttachmentsList\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/supply/attachments/{doc_type}/{doc_id}\x12\x93\x01\n\x1eUpdateAttachmentsByDocIdMobile\x12\x1f.attachments.NewAttachmentsList\x1a\x15.attachments.Response\"9\x82\xd3\xe4\x93\x02\x33\"./api/v2/supply/attachments/{doc_type}/{doc_id}:\x01*\x12\x8d\x01\n\x18UpdateAttachmentsByDocId\x12\x1f.attachments.NewAttachmentsList\x1a\x15.attachments.Response\"9\x82\xd3\xe4\x93\x02\x33\x1a./api/v2/supply/attachments/{doc_type}/{doc_id}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='attachments.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='attachments.Response.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='attachments.Response.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='attachments.Response.msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=97,
  serialized_end=148,
)


_NEWATTACHMENTSLIST = _descriptor.Descriptor(
  name='NewAttachmentsList',
  full_name='attachments.NewAttachmentsList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='attachments.NewAttachmentsList.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_type', full_name='attachments.NewAttachmentsList.doc_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='attachments.NewAttachmentsList.attachments', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='signature', full_name='attachments.NewAttachmentsList.signature', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nosign_reason', full_name='attachments.NewAttachmentsList.nosign_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=150,
  serialized_end=267,
)


_ATTACHMENTSLIST = _descriptor.Descriptor(
  name='AttachmentsList',
  full_name='attachments.AttachmentsList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='attachments.AttachmentsList.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_type', full_name='attachments.AttachmentsList.doc_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='attachments.AttachmentsList.attachments', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='signature', full_name='attachments.AttachmentsList.signature', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nosign_reason', full_name='attachments.AttachmentsList.nosign_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='attachments.AttachmentsList.created_at', index=5,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='attachments.AttachmentsList.created_by', index=6,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='attachments.AttachmentsList.updated_at', index=7,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='attachments.AttachmentsList.updated_by', index=8,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='attachments.AttachmentsList.partner_id', index=9,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=270,
  serialized_end=540,
)


_DOCIDREQUEST = _descriptor.Descriptor(
  name='DocIdRequest',
  full_name='attachments.DocIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='attachments.DocIdRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_type', full_name='attachments.DocIdRequest.doc_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=542,
  serialized_end=590,
)

_ATTACHMENTSLIST.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ATTACHMENTSLIST.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['NewAttachmentsList'] = _NEWATTACHMENTSLIST
DESCRIPTOR.message_types_by_name['AttachmentsList'] = _ATTACHMENTSLIST
DESCRIPTOR.message_types_by_name['DocIdRequest'] = _DOCIDREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'attachments_pb2'
  # @@protoc_insertion_point(class_scope:attachments.Response)
  ))
_sym_db.RegisterMessage(Response)

NewAttachmentsList = _reflection.GeneratedProtocolMessageType('NewAttachmentsList', (_message.Message,), dict(
  DESCRIPTOR = _NEWATTACHMENTSLIST,
  __module__ = 'attachments_pb2'
  # @@protoc_insertion_point(class_scope:attachments.NewAttachmentsList)
  ))
_sym_db.RegisterMessage(NewAttachmentsList)

AttachmentsList = _reflection.GeneratedProtocolMessageType('AttachmentsList', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTSLIST,
  __module__ = 'attachments_pb2'
  # @@protoc_insertion_point(class_scope:attachments.AttachmentsList)
  ))
_sym_db.RegisterMessage(AttachmentsList)

DocIdRequest = _reflection.GeneratedProtocolMessageType('DocIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DOCIDREQUEST,
  __module__ = 'attachments_pb2'
  # @@protoc_insertion_point(class_scope:attachments.DocIdRequest)
  ))
_sym_db.RegisterMessage(DocIdRequest)



_ATTACHMENTSSERVICE = _descriptor.ServiceDescriptor(
  name='AttachmentsService',
  full_name='attachments.AttachmentsService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=593,
  serialized_end=1162,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateAttachments',
    full_name='attachments.AttachmentsService.CreateAttachments',
    index=0,
    containing_service=None,
    input_type=_NEWATTACHMENTSLIST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v2/supply/attachments:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAttachmentsByDocId',
    full_name='attachments.AttachmentsService.GetAttachmentsByDocId',
    index=1,
    containing_service=None,
    input_type=_DOCIDREQUEST,
    output_type=_ATTACHMENTSLIST,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/supply/attachments/{doc_type}/{doc_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateAttachmentsByDocIdMobile',
    full_name='attachments.AttachmentsService.UpdateAttachmentsByDocIdMobile',
    index=2,
    containing_service=None,
    input_type=_NEWATTACHMENTSLIST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\0023\"./api/v2/supply/attachments/{doc_type}/{doc_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateAttachmentsByDocId',
    full_name='attachments.AttachmentsService.UpdateAttachmentsByDocId',
    index=3,
    containing_service=None,
    input_type=_NEWATTACHMENTSLIST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\0023\032./api/v2/supply/attachments/{doc_type}/{doc_id}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_ATTACHMENTSSERVICE)

DESCRIPTOR.services_by_name['AttachmentsService'] = _ATTACHMENTSSERVICE

# @@protoc_insertion_point(module_scope)
