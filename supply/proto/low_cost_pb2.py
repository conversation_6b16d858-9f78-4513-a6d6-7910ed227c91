# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: low_cost.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='low_cost.proto',
  package='low_cost',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x0elow_cost.proto\x12\x08low_cost\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc7\x01\n\x12ListLowCostRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04glpt\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\r\x12\x0e\n\x06offset\x18\x07 \x01(\r\"E\n\x13ListLowCostResponse\x12\x1f\n\x04rows\x18\x01 \x03(\x0b\x32\x11.low_cost.LowCost\x12\r\n\x05total\x18\x02 \x01(\r\"\x8d\x06\n\x07LowCost\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x12\n\norder_code\x18\x0c \x01(\t\x12\x12\n\norder_type\x18\r \x01(\t\x12\x14\n\x0cjde_order_id\x18\x0e \x01(\t\x12\x16\n\x0ejde_order_type\x18\x0f \x01(\t\x12\x0f\n\x07jde_mcu\x18\x10 \x01(\t\x12.\n\norder_time\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x61\x63tion\x18\x12 \x01(\t\x12\x0e\n\x06status\x18\x13 \x01(\t\x12\x0b\n\x03qty\x18\x14 \x01(\x01\x12\x10\n\x08stock_id\x18\x15 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x16 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x17 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x18 \x01(\t\x12\x0c\n\x04glpt\x18\x19 \x01(\t\x12\x12\n\npartner_id\x18\x1a \x01(\x04\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11origin_order_code\x18\x1d \x01(\t\x12\x14\n\x0cinventory_id\x18\x1e \x01(\t\x12\x16\n\x0eproduct_status\x18\x1f \x01(\t\x12\x32\n\x0eoperation_time\x18  \x01(\x0b\x32\x1a.google.protobuf.Timestamp2}\n\x0eLowCostService\x12k\n\x0bListLowCost\x12\x1c.low_cost.ListLowCostRequest\x1a\x1d.low_cost.ListLowCostResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/api/v2/supply/low_costb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_LISTLOWCOSTREQUEST = _descriptor.Descriptor(
  name='ListLowCostRequest',
  full_name='low_cost.ListLowCostRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='low_cost.ListLowCostRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='low_cost.ListLowCostRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='low_cost.ListLowCostRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='low_cost.ListLowCostRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='glpt', full_name='low_cost.ListLowCostRequest.glpt', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='low_cost.ListLowCostRequest.limit', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='low_cost.ListLowCostRequest.offset', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=92,
  serialized_end=291,
)


_LISTLOWCOSTRESPONSE = _descriptor.Descriptor(
  name='ListLowCostResponse',
  full_name='low_cost.ListLowCostResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='low_cost.ListLowCostResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='low_cost.ListLowCostResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=293,
  serialized_end=362,
)


_LOWCOST = _descriptor.Descriptor(
  name='LowCost',
  full_name='low_cost.LowCost',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='low_cost.LowCost.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='low_cost.LowCost.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='low_cost.LowCost.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='low_cost.LowCost.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='low_cost.LowCost.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='low_cost.LowCost.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='low_cost.LowCost.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='low_cost.LowCost.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='low_cost.LowCost.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='low_cost.LowCost.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='low_cost.LowCost.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='low_cost.LowCost.order_code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='low_cost.LowCost.order_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='low_cost.LowCost.jde_order_id', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_type', full_name='low_cost.LowCost.jde_order_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_mcu', full_name='low_cost.LowCost.jde_mcu', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_time', full_name='low_cost.LowCost.order_time', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='low_cost.LowCost.action', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='low_cost.LowCost.status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='low_cost.LowCost.qty', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stock_id', full_name='low_cost.LowCost.stock_id', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='low_cost.LowCost.accounting_unit_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='low_cost.LowCost.accounting_unit_code', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='low_cost.LowCost.accounting_unit_name', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='glpt', full_name='low_cost.LowCost.glpt', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='low_cost.LowCost.partner_id', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='low_cost.LowCost.created_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='low_cost.LowCost.updated_at', index=27,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_order_code', full_name='low_cost.LowCost.origin_order_code', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_id', full_name='low_cost.LowCost.inventory_id', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='low_cost.LowCost.product_status', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_time', full_name='low_cost.LowCost.operation_time', index=31,
      number=32, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=365,
  serialized_end=1146,
)

_LISTLOWCOSTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTLOWCOSTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTLOWCOSTRESPONSE.fields_by_name['rows'].message_type = _LOWCOST
_LOWCOST.fields_by_name['order_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LOWCOST.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LOWCOST.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LOWCOST.fields_by_name['operation_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['ListLowCostRequest'] = _LISTLOWCOSTREQUEST
DESCRIPTOR.message_types_by_name['ListLowCostResponse'] = _LISTLOWCOSTRESPONSE
DESCRIPTOR.message_types_by_name['LowCost'] = _LOWCOST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListLowCostRequest = _reflection.GeneratedProtocolMessageType('ListLowCostRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTLOWCOSTREQUEST,
  __module__ = 'low_cost_pb2'
  # @@protoc_insertion_point(class_scope:low_cost.ListLowCostRequest)
  ))
_sym_db.RegisterMessage(ListLowCostRequest)

ListLowCostResponse = _reflection.GeneratedProtocolMessageType('ListLowCostResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTLOWCOSTRESPONSE,
  __module__ = 'low_cost_pb2'
  # @@protoc_insertion_point(class_scope:low_cost.ListLowCostResponse)
  ))
_sym_db.RegisterMessage(ListLowCostResponse)

LowCost = _reflection.GeneratedProtocolMessageType('LowCost', (_message.Message,), dict(
  DESCRIPTOR = _LOWCOST,
  __module__ = 'low_cost_pb2'
  # @@protoc_insertion_point(class_scope:low_cost.LowCost)
  ))
_sym_db.RegisterMessage(LowCost)



_LOWCOSTSERVICE = _descriptor.ServiceDescriptor(
  name='LowCostService',
  full_name='low_cost.LowCostService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=1148,
  serialized_end=1273,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListLowCost',
    full_name='low_cost.LowCostService.ListLowCost',
    index=0,
    containing_service=None,
    input_type=_LISTLOWCOSTREQUEST,
    output_type=_LISTLOWCOSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\031\022\027/api/v2/supply/low_cost'),
  ),
])
_sym_db.RegisterServiceDescriptor(_LOWCOSTSERVICE)

DESCRIPTOR.services_by_name['LowCostService'] = _LOWCOSTSERVICE

# @@protoc_insertion_point(module_scope)
