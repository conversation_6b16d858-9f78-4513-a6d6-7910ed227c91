import logging
import sys
import traceback

from sqlalchemy import func
from supply.driver.mysql import session_maker
from datetime import datetime
from supply.model import DeclarativeBase, TimestampMixin, AsDict
from sqlalchemy import (Column, BigInteger, String, DateTime, DECIMAL, Boolean)
from supply.utils.helper import get_guid


class MaterialConvert(DeclarativeBase, TimestampMixin, AsDict):
    """物料转换单"""
    __tablename__ = "supply_material_convert"

    class Status(object):
        INITED = 'INITED'           # 新建
        CONFIRMED = 'CONFIRMED'     # 已确认
        INVALID = 'INVALID'         # 已作废

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    branch_type = Column(String(30))                    # 区分门店/仓库/加工中心：STORE/WAREHOUSE/MACHINING_CENTER
    code = Column(String(50), index=True)               # 编号
    status = Column(String(20), default=Status.INITED)  # 订单状态
    convert_type = Column(String(15))                   # 转换类型（自动AUTO、手动MANUAL）
    convert_date = Column(DateTime, index=True)         # 物料转换日期
    branch_id = Column(BigInteger, index=True)          # 门店/仓库/加工中心id
    branch_code = Column(String(50))                    # 门店/仓库/加工中心code
    branch_name = Column(String(50))                    # 门店/仓库/加工中心名称
    position_id = Column(BigInteger, index=True)        # 仓位id
    position_name = Column(String(50))                  # 仓位名称
    position_code = Column(String(50))                  # 仓位code
    convert_rule = Column(BigInteger)                   # 转换规则ID
    opened_position = Column(Boolean)                   # 是否开启多仓位
    remark = Column(String(50))                         # 备注
    process_status = Column(String(30))                 # (预留)传输成本中心状态(INITED、PROCESSING、SUCCESS)*
    request_id = Column(BigInteger)                     # 幂等性校验请求id
    cost_center_id = Column(BigInteger)  # 成本中心id


class MaterialConvertDetail(DeclarativeBase, TimestampMixin, AsDict):
    """物料转换单明细"""
    __tablename__ = "supply_material_convert_detail"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 商户id
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    main_id = Column(BigInteger, index=True)            # 关联的物料转换id
    product_id = Column(BigInteger)                     # 物料/商品id
    product_code = Column(String(50))                   # 商品code
    product_name = Column(String(50))                   # 商品名称
    product_type = Column(String(50))                   # 商品类型
    unit_id = Column(BigInteger)                        # 单位id(门店中为订货单位/仓库为采购单位)
    unit_name = Column(String(50))                      # 单位名称
    unit_spec = Column(String(50))                      # 单位规格
    unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 单位转换率
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 物料数量
    type = Column(String(20))                           # 物料类型(原始物料origin/目标物料target)


class MaterialConvertLog(DeclarativeBase, AsDict):
    __tablename__ = "supply_material_convert_log"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    main_id = Column(BigInteger, index=True)            # 关联的物料转换id
    action = Column(String(50))                         # 操作状态（确认/作废/物料转换）等
    created_at = Column(DateTime, default=datetime.utcnow())


class MaterialConvertDB(object):
    def __init__(self):
        pass

    def get_material_convert_by_id(self, convert_id=None, request_id=None, partner_id=None):
        """获取一条物料转换单记录"""
        db_session = session_maker()
        try:
            query = db_session.query(MaterialConvert)
            if partner_id:
                query = query.filter(MaterialConvert.partner_id == partner_id)
            if convert_id:
                query = query.filter(MaterialConvert.id == convert_id)
                return query.first()
            if request_id:
                query = query.filter(MaterialConvert.request_id == request_id)
                return query.first()
        except Exception as e:
            logging.error("Get Material Convert Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                       traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_material_convert(self, order_data, material_detail):
        """保存物料转换单和商品详情
        :param order_data 物料转换单对应字段数据 dict
        :param material_detail 物料转换单商品详情对应数据 list[detail,..]"""
        db_session = session_maker()
        try:
            order_list = [order_data]
            db_session.bulk_insert_mappings(MaterialConvert, order_list)
            db_session.bulk_insert_mappings(MaterialConvertDetail, material_detail)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Material Convert Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                          traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_material_convert_log(self, material_log):
        """保存物料转换单操作日志
        :param material_log 操作日志 dict
        """
        db_session = session_maker()
        try:
            material_data = [material_log]
            db_session.bulk_insert_mappings(MaterialConvertLog, material_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Material Convert Log Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                              traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_material_convert(self, partner_id=None, user_id=None, start_date=None, end_date=None, code=None,
                              status=None, convert_type=None, branch_ids=None, limit=-1, offset=None, 
                              include_total=False, order=None, sort=None, branch_type=None, convert_rules=None,
                              position_ids=None, process_status=None):
        """物料转换单列表"""
        db_session = session_maker()
        try:
            query = db_session.query(MaterialConvert).filter(MaterialConvert.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(MaterialConvert.convert_date >= start_date)
            if end_date and isinstance(end_date, datetime) and start_date.year != 1970:
                query = query.filter(MaterialConvert.convert_date <= end_date)
            if code:
                query = query.filter(MaterialConvert.code == code)
            if branch_type:
                query = query.filter(MaterialConvert.branch_type == branch_type)
            if status:
                query = query.filter(MaterialConvert.status == status)
            if process_status and isinstance(process_status, list):
                query = query.filter(MaterialConvert.process_status.in_(process_status))
            if convert_type:
                query = query.filter(MaterialConvert.convert_type == convert_type)
            if branch_ids:
                query = query.filter(MaterialConvert.branch_id.in_(branch_ids))
            if convert_rules:
                query = query.filter(MaterialConvert.convert_rule.in_(convert_rules))
            if position_ids:
                query = query.filter(MaterialConvert.position_id.in_(position_ids))
            if sort in ["convert_date", "branch_code", "code"]:
                if order == "asc":
                    query_str = "MaterialConvert.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "MaterialConvert.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(MaterialConvert.updated_at)
                else:
                    query = query.order_by(MaterialConvert.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(MaterialConvert.id)).filter(
                    MaterialConvert.partner_id == partner_id)
                if start_date and isinstance(start_date, datetime):
                    query_total = query_total.filter(MaterialConvert.convert_date >= start_date)
                if end_date and isinstance(end_date, datetime):
                    query_total = query_total.filter(MaterialConvert.convert_date <= end_date)
                if code:
                    query_total = query_total.filter(MaterialConvert.code == code)
                if branch_type:
                    query_total = query_total.filter(MaterialConvert.branch_type == branch_type)
                if status:
                    query_total = query_total.filter(MaterialConvert.status == status)
                if process_status and isinstance(process_status, list):
                    query_total = query_total.filter(MaterialConvert.process_status.in_(process_status))
                if convert_type:
                    query_total = query_total.filter(MaterialConvert.convert_type == convert_type)
                if branch_ids:
                    query_total = query_total.filter(MaterialConvert.branch_id.in_(branch_ids))
                if convert_rules:
                    query_total = query_total.filter(MaterialConvert.convert_rule.in_(convert_rules))
                if position_ids:
                    query_total = query_total.filter(MaterialConvert.position_id.in_(position_ids))
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Material Convert Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                        traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_material_convert_detail(self, partner_id=None, user_id=None, main_id=None):
        """根据id拉取物料转换单详情信息"""
        db_session = session_maker()
        try:
            query = db_session.query(MaterialConvertDetail).filter(
                MaterialConvertDetail.main_id == main_id).filter(
                MaterialConvertDetail.partner_id == partner_id)
            return query.all()
        except Exception as e:
            logging.error("Get Material Convert Detail Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                               traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_material_convert(self, update_data=None, material_detail=None, update_detail=False):
        """物料转换单确认/作废, 支持批量，这里没有做状态校验
        :param update_data -> dict 待更新的数据
        :param material_detail -> list 待更新的物料明细
        :param update_detail -> bool 是否更新明细，为false时只更新状态
        """
        db_session = session_maker()
        try:
            if not update_detail:
                update_db = db_session.query(MaterialConvert).filter(
                    MaterialConvert.partner_id == update_data.get('partner_id')).filter(
                    MaterialConvert.id == update_data.get('id')).with_lockmode('update').first()
                if update_data.get('updated_by'):
                    update_db.updated_by = update_data.get('updated_by')
                if update_data.get('updated_name'):
                    update_db.updated_name = update_data.get('updated_name')
                if update_data.get('status'):
                    update_db.status = update_data.get('status')
                if update_data.get('process_status'):
                    update_db.process_status = update_data.get('process_status')
                db_session.add(update_db)
            else:
                db_session.bulk_update_mappings(MaterialConvert, [update_data])
                if material_detail:
                    db_session.bulk_update_mappings(MaterialConvertDetail, material_detail)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Material Convert Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                           traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()


material_convert_db = MaterialConvertDB()
