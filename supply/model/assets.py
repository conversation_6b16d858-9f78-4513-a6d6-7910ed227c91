# -*- coding:utf-8 -*-

from datetime import datetime, date, timedelta
from sqlalchemy import Index, Column, DECIMAL, BigInteger, Integer, String, Text, DateTime, Boolean, UniqueConstraint, func
from sqlalchemy.orm.exc import NoResultFound
from decimal import Decimal

from supply.model import DeclarativeBase, MethodMixin, TimestampMixin, AsDict
from ..utils.snowflake import gen_snowflake_id
from ..utils.helper import set_db, set_model_from_db, update_db
from ..driver.mysql import session, session_maker
from google.protobuf.timestamp_pb2 import Timestamp




class AssetsModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    ''' 
    固定资产收货单
    '''
    __tablename__ = 'supply_assets'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 单号
    code = Column(String(50))
    # jde公司
    kcoo = Column(String(10))
    # jde单据编号
    doco = Column(BigInteger)
    # jde单据类型
    dcto = Column(String(10))
    # jde行号
    lnid = Column(BigInteger)
    # 仓库编号
    mcu = Column(String(20))
    mcu_id = Column(BigInteger)
    mcu_name = Column(String(50))
    # 门店编号
    an8 = Column(BigInteger)
    store_id = Column(BigInteger)
    store_name = Column(String(50))
    # 订货日期
    trdj = Column(DateTime)
    # 期望到货日期
    qrdj = Column(DateTime)
    # HWS单据id
    dl03 = Column(BigInteger)
    # HWS行号
    ktln = Column(BigInteger)
    # 状态
    status = Column(String(20))
    # 
    partner_id = Column(BigInteger)
    # 商品数量
    products_num = Column(BigInteger)
    # 
    updated_by = Column(BigInteger)
    # 
    updated_name = Column(String(20))
    # 
    created_by = Column(BigInteger)
    # 
    created_name = Column(String(20))
    

    @classmethod
    def get_assets_by_doco(cls, doco):
        try:
            # DBSession = session_maker()
            q = session.query(cls).filter(
                cls.doco==doco)
            if q.count() > 0:
                return q.first().id
            return False
        except Exception as e:
            raise e
        # finally:
        #     DBSession.close()
        return None


    @classmethod
    def list_assets(cls, partner_id, user_id, limit=None, offset=None, store_ids=None, status=None, start_date=None, end_date=None, 
                        asset_ids=None, doco=None, code=None):
        try:
            # DBSession = session_maker()
            query = session.query(cls)
            if partner_id:
                query = query.filter(cls.partner_id == partner_id)
            if store_ids and isinstance(store_ids, list):
                query = query.filter(cls.store_id.in_(store_ids))
            if asset_ids and isinstance(asset_ids, list) and len(asset_ids) > 0:
                query = query.filter(cls.id.in_(asset_ids))
            if doco:
                query = query.filter(cls.doco == doco)
            if code:
                query = query.filter(cls.code == code)
            if not isinstance(start_date, datetime):
                timestamp = Timestamp()
                timestamp.seconds = start_date.seconds
                start_date = timestamp.ToDatetime()
                start_date = start_date
            if not isinstance(end_date, datetime):
                timestamp = Timestamp()
                timestamp.seconds = end_date.seconds
                end_date = timestamp.ToDatetime()
                end_date = end_date
            if isinstance(start_date, datetime):
                query = query.filter(cls.trdj >= start_date)
            if isinstance(end_date, datetime):
                query = query.filter(cls.trdj <= end_date)
            if status:
                query = query.filter(cls.status.in_(status))
            # if order_by and order_by == 'updated':
            #     query = query.order_by(cls.updated_at.desc())
            # else:
            query = query.order_by(cls.updated_at.desc())
            count = query.count()
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            
            asset_db_list = query.all()
            return count, asset_db_list
        except Exception as e:
            raise e
        





class AssetsProductModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    ''' 
    固定资产收货单
    '''
    __tablename__ = 'supply_assets_product'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 
    asset_id = Column(BigInteger)
    # 资产编号
    litm = Column(String(30))
    product_id = Column(BigInteger)
    product_name = Column(String(50))
    # 单位
    uom = Column(String(10))
    # 数量
    uorg = Column(DECIMAL(precision=15, scale=3, decimal_return_scale=3))
    # 状态
    status = Column(String(20))
    # 
    partner_id = Column(BigInteger)
    # 
    updated_by = Column(BigInteger)
    # 
    updated_name = Column(String(20))
    # 
    created_by = Column(BigInteger)
    # 
    created_name = Column(String(20))


    
    @classmethod
    def list_products_by_asset_id(cls, asset_id, limit=None, offset=None):
        try:
            # DBSession = session_maker()
            query = session.query(cls)
            query = query.filter(cls.asset_id == asset_id)
            count = query.count()
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            
            asset_product_db_list = query.all() 
            return count, asset_product_db_list
        except Exception as e:
            raise e
        

    