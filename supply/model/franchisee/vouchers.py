# -*-: coding:utf-8 -*-
from supply.utils.helper import get_guid
from datetime import datetime
from sqlalchemy import (BigInteger, Column, DateTime, String, DECIMAL, func)
from sqlalchemy.orm import Session
from supply import logger
from supply.driver.mysql import session_maker
from supply.model import (DeclarativeBase, MethodMixin, TimestampMixin)
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand


class FranchiseeVouchers(DeclarativeBase, TimestampMixin, MethodMixin):
    """加盟商代金券"""
    __tablename__ = "franchisee_vouchers"

    class Status:
        INITED = "INITED"        # 新建
        INVALID = "INVALID"      # 作废
        CONFIRMED = "CONFIRMED"  # 已确认

    class ProcessStatus:
        INITED = "INITED"
        PROCESSING = "PROCESSING"
        SUCCESS = "SUCCESS"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 租户id
    request_id = Column(BigInteger, index=True)  # 唯一请求ID
    name = Column(String)                        # 代金券名称
    code = Column(String, index=True)            # 代金券编号
    status = Column(String, index=True, default=Status.INITED)     # 代金券状态
    process_status = Column(String, default=ProcessStatus.INITED)  # 处理状态
    start_date = Column(DateTime, index=True)    # 生效开始日期
    end_date = Column(DateTime, index=True)      # 生效结束日期
    amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 代金券金额
    remark = Column(String)                      # 备注
    reason = Column(String)                      # 原因
    created_by = Column(BigInteger)              # 创建人
    created_name = Column(String)                # 创建人名称
    updated_by = Column(BigInteger)              # 更新人
    updated_name = Column(String)                # 更新人名称

    @classmethod
    def get_voucher(cls, partner_id, request_id=None, voucher_id=None, name=None):
        """获取一条代金券"""
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if voucher_id:
                query = query.filter(cls.id == voucher_id)
            if request_id:
                query = query.filter(cls.request_id == request_id)
            if name:
                query = query.filter(cls.name == name)
            return query.first()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def create_vouchers(cls, vouchers: list, vouchers_logs: list, stores: list, regions: list, order_types: list):
        """
        创建代金券、商品、记录log
        :param vouchers: 代金券详情
        :param vouchers_logs: 代金券操作logs
        :param stores: 代金券关联门店
        :param regions: 代金券关联区域
        :param order_types: 代金券关联订货类型
        :return:
        """
        db_session = session_maker()
        try:
            if vouchers and isinstance(vouchers, list):
                db_session.bulk_insert_mappings(cls, vouchers)
            if stores and isinstance(stores, list):
                db_session.bulk_insert_mappings(FranchiseeVouchersStore, stores)
            if regions and isinstance(regions, list):
                db_session.bulk_insert_mappings(FranchiseeVouchersRegion, regions)
            if order_types and isinstance(order_types, list):
                db_session.bulk_insert_mappings(FranchiseeVouchersOrderType, order_types)
            if vouchers_logs and isinstance(vouchers_logs, list):
                db_session.bulk_insert_mappings(FranchiseeVouchersLog, vouchers_logs)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_vouchers(cls, partner_id=None, name=None, code=None, ids=None, status=None, order_type_ids=None,
                      limit=-1, offset=None, include_total=None, order=None, sort=None):
        """代金券列表"""
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            if order_type_ids and isinstance(order_type_ids, list):
                query = query.outerjoin(FranchiseeVouchersOrderType, cls.id == FranchiseeVouchersOrderType.voucher_id)
                query = query.filter(FranchiseeVouchersOrderType.order_type_id.in_(order_type_ids))
            if partner_id:
                query = query.filter(cls.partner_id == partner_id)
            if ids and isinstance(ids, list):
                query = query.filter(cls.id.in_(ids))
            if code:
                query = query.filter(cls.code == code)
            if name:
                query = query.filter(cls.name.like(f"%{name}%"))
            if status and isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            if order_type_ids and isinstance(order_type_ids, list):
                query = query.group_by(cls.id)
            # 排序方式
            if not sort:
                sort = "updated_at"
            if order == "asc":
                sort_str = "cls.{}".format(sort)
                query = query.order_by(eval(sort_str))
            else:
                sort_str = "cls.{}.desc()".format(sort)
                query = query.order_by(eval(sort_str))
            if offset:
                query = query.offset(offset)
            if limit and limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(cls.id)
                if order_type_ids and isinstance(order_type_ids, list):
                    query_total = query_total.outerjoin(FranchiseeVouchersOrderType,
                                                        cls.id == FranchiseeVouchersOrderType.voucher_id)
                    query_total = query_total.filter(FranchiseeVouchersOrderType.order_type_id.in_(order_type_ids))
                if partner_id:
                    query_total = query_total.filter(cls.partner_id == partner_id)
                if ids and isinstance(ids, list):
                    query_total = query_total.filter(cls.id.in_(ids))
                if code:
                    query_total = query_total.filter(cls.code == code)
                if name:
                    query_total = query_total.filter(cls.name.like(f"%{name}%"))
                if status and isinstance(status, list):
                    query_total = query_total.filter(cls.status.in_(status))
                if order_type_ids and isinstance(order_type_ids, list):
                    query_total = query_total.group_by(cls.id)
                count = query_total.count()
                return count, query_set
            return query_set
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_vouchers(cls, update_data, voucher_logs=None, allow_status=None):
        """更新加盟商订货退款单
        :param update_data -> dict/list 待更新的数据
        :param voucher_logs -> list 操作log
        :param allow_status -> list 允许更新单状态
        """
        db_session = session_maker()
        try:
            if isinstance(update_data, dict):
                update_db = db_session.query(cls).filter(
                    cls.partner_id == update_data.get('partner_id')).filter(
                    cls.id == update_data.get('id')).with_lockmode('update').first()
                if update_db and allow_status and update_db.status in allow_status:
                    if update_data.get('updated_by'):
                        update_db.updated_by = update_data.get('updated_by')
                    if update_data.get('updated_name'):
                        update_db.updated_name = update_data.get('updated_name')
                    update_db.updated_at = datetime.utcnow()
                    if update_data.get('name'):
                        update_db.name = update_data.get('name')
                    if update_data.get('status'):
                        update_db.status = update_data.get('status')
                    if update_data.get('process_status'):
                        update_db.process_status = update_data.get('process_status')
                    if update_data.get("remark"):
                        update_db.remark = update_data.get('remark')
                    if update_data.get('amount') is not None:
                        update_db.amount = update_data.get('amount')
                    if update_data.get('start_date'):
                        update_db.start_date = update_data.get('start_date')
                    if update_data.get('end_date'):
                        update_db.end_date = update_data.get('end_date')
                    db_session.add(update_db)
                    order_types = update_data.get('order_types', [])
                    stores = update_data.get('stores', [])
                    regions = update_data.get('regions', [])
                    if stores:
                        db_session.query(FranchiseeVouchersStore).filter(
                            FranchiseeVouchersStore.voucher_id == update_data.get('id')).delete(
                            synchronize_session=False)
                        db_session.bulk_insert_mappings(FranchiseeVouchersStore, stores)
                    if regions:
                        db_session.query(FranchiseeVouchersRegion).filter(
                            FranchiseeVouchersRegion.voucher_id == update_data.get('id')).delete(
                            synchronize_session=False)
                        db_session.bulk_insert_mappings(FranchiseeVouchersRegion, regions)
                    if order_types:
                        db_session.query(FranchiseeVouchersOrderType).filter(
                            FranchiseeVouchersOrderType.voucher_id == update_data.get('id')).delete(
                            synchronize_session=False)
                        db_session.bulk_insert_mappings(FranchiseeVouchersOrderType, order_types)
            elif isinstance(update_data, list):
                db_session.bulk_update_mappings(cls, update_data)
            else:
                raise Exception("更新数据不合法！")
            if voucher_logs and isinstance(voucher_logs, list):
                db_session.bulk_insert_mappings(FranchiseeVouchersLog, voucher_logs)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_vouchers_relations(cls, voucher_ids: list, partner_id: int, get_store=False, get_region=False,
                               get_order_type=False):
        """查询代金券关联门店、区域、订货类型等
        :param voucher_ids
        :param partner_id
        :param get_store
        :param get_region
        :param get_order_type
        """
        db_session = session_maker()
        try:
            store_dbs = None
            region_dbs = None
            order_type_dbs = None
            if get_store:
                store_dbs = db_session.query(FranchiseeVouchersStore).filter(
                    FranchiseeVouchersStore.partner_id == partner_id,
                    FranchiseeVouchersStore.voucher_id.in_(voucher_ids)).all()
            if get_region:
                region_dbs = db_session.query(FranchiseeVouchersRegion).filter(
                    FranchiseeVouchersRegion.partner_id == partner_id,
                    FranchiseeVouchersRegion.voucher_id.in_(voucher_ids)).all()
            if get_order_type:
                order_type_dbs = db_session.query(FranchiseeVouchersOrderType).filter(
                    FranchiseeVouchersOrderType.partner_id == partner_id,
                    FranchiseeVouchersOrderType.voucher_id.in_(voucher_ids)).all()
            return store_dbs, region_dbs, order_type_dbs
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()


class FranchiseeVouchersStore(DeclarativeBase, TimestampMixin, MethodMixin):
    """代金券关联门店"""
    __tablename__ = "franchisee_vouchers_store"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 租户id
    voucher_id = Column(BigInteger, index=True)  # 代金券ID
    store_id = Column(BigInteger)                # 门店ID
    store_name = Column(String)                  # 门店名称
    store_code = Column(String)                  # 门店编号
    store_type = Column(String)                  # 门店类型(*预留*)
    amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 代金券金额
    created_by = Column(BigInteger)              # 创建人
    created_name = Column(String)                # 创建人名称
    updated_by = Column(BigInteger)              # 更新人
    updated_name = Column(String)                # 更新人名称


class FranchiseeVouchersRegion(DeclarativeBase, TimestampMixin, MethodMixin):
    """代金券关联区域"""
    __tablename__ = "franchisee_vouchers_region"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 租户id
    voucher_id = Column(BigInteger, index=True)  # 代金券ID
    region_id = Column(BigInteger)               # 区域ID
    region_name = Column(String)                 # 区域名称
    region_code = Column(String)                 # 区域编号
    region_type = Column(String)                 # 区域类型
    created_by = Column(BigInteger)              # 创建人
    created_name = Column(String)                # 创建人名称
    updated_by = Column(BigInteger)              # 更新人
    updated_name = Column(String)                # 更新人名称


class FranchiseeVouchersOrderType(DeclarativeBase, TimestampMixin, MethodMixin):
    """代金券关联订货类型"""
    __tablename__ = "franchisee_vouchers_order_type"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 租户id
    voucher_id = Column(BigInteger, index=True)  # 代金券ID
    order_type_id = Column(BigInteger)           # 订货类型ID
    order_type_name = Column(String)             # 订货类型名称
    order_type_code = Column(String)             # 订货类型编号
    created_by = Column(BigInteger)              # 创建人
    created_name = Column(String)                # 创建人名称
    updated_by = Column(BigInteger)              # 更新人
    updated_name = Column(String)                # 更新人名称


class FranchiseeVouchersLog(DeclarativeBase, MethodMixin):
    """加盟商代金券操作日志"""
    __tablename__ = "franchisee_vouchers_log"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)  # 租户ID
    voucher_id = Column(BigInteger)  # 退款单ID
    action = Column(String)  # 操作
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String(50))  # 创建人名称
    created_at = Column(DateTime, default=datetime.utcnow())  # 创建时间

    @classmethod
    def get_franchisee_vouchers_log(cls, voucher_id, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.voucher_id == voucher_id,
                                                 cls.partner_id == partner_id)
            query = query.order_by(cls.created_at.desc())
            demand_logs = query.all()
            if not demand_logs:
                return 0, []
            query_count = db_session.query(func.count(cls.id))
            if partner_id:
                query_count = query_count.filter(cls.partner_id == partner_id)
            if voucher_id:
                query_count = query_count.filter(cls.voucher_id == voucher_id)
            count = query_count.scalar()
            return count, demand_logs
        except Exception as e:
            logger.error("** method[%s] error. Error message:%s **" % ('get_franchisee_demand_log', e))
            raise e
        finally:
            db_session.close()
