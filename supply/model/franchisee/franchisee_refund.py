# -*-: coding:utf-8 -*-
from supply.utils.helper import get_guid
from datetime import datetime
from sqlalchemy import (BigInteger, Column, DateTime, String, DECIMAL, func)
from sqlalchemy.orm import Session
from supply import logger
from supply.driver.mysql import session_maker
from supply.model import (DeclarativeBase, MethodMixin, TimestampMixin)
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand, SupplyFranchiseeDemandLog


class SupplyFranchiseeRefund(DeclarativeBase, TimestampMixin, MethodMixin):
    """加盟商订货退款单"""
    __tablename__ = "supply_franchisee_refund"

    class Status:
        INITED = "INITED"  # 新建-待审核
        REJECTED = "REJECTED"  # 已驳回
        CANCELLED = "CANCELLED"  # 取消/作废
        APPROVED = "APPROVED"  # 已退款

    class ProcessStatus:
        INITED = "INITED"
        PROCESSING = "PROCESSING"
        SUCCESS = "SUCCESS"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 租户id
    batch_id = Column(BigInteger)                # 关联订货单ID
    type = Column(String)     # 退款单类型：订货退款ORDER_REFUND、缺货退款OOS_REFUND、退货退款RETURN_REFUND、收货差异退款DIFF_REFUND
    main_id = Column(BigInteger, index=True)    # 关联主单ID(订货单/退货单/要货单/收货差异单)
    main_code = Column(String, index=True)      # 关联主单号
    main_type = Column(String)                  # 关联主单类型：订货单FRS_DEMAND、要货单FRS_WHS_DEMAND、退货单FRS_RETURN、收货差异单FRS_REC_DIFF
    code = Column(String, index=True)               # 退款单号
    received_by = Column(BigInteger, index=True)    # 收货方id
    franchisee_id = Column(BigInteger, index=True)  # 加盟商id
    trade_company = Column(BigInteger)              # 贸易公司ID
    refund_date = Column(DateTime, index=True)      # 退款申请日期
    approved_date = Column(DateTime, index=True)    # 退款审核日期
    payment_way = Column(String)         # 退款付款方式(线下支付、信用付、线上支付等)
    status = Column(String, index=True, default=Status.INITED)  # 单据状态
    process_status = Column(String, default=ProcessStatus.INITED)  # 处理状态
    reject_reason = Column(String)  # 驳回原因
    remark = Column(String)  # 备注
    reason = Column(String)  # 退款原因
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)  # 创建人名称
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)  # 更新人名称
    refund_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 退款金额
    pay_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 实际付款金额

    @classmethod
    def get_refund_by_id(cls, partner_id, main_id=None, refund_id=None):
        """获取一条退款记录"""
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if refund_id:
                query = query.filter(cls.id == refund_id)
                return query.first()
            if main_id:
                query = query.filter(cls.main_id == main_id)
                return query.first()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def insert_franchisee_refund(cls, refund_details: list, refund_products: list, refund_logs: list):
        """
        创建退款单、商品、记录log
        :param refund_details: 退款单详情
        :param refund_products: 退款单商品
        :param refund_logs: 退款单创建log
        :return:
        """
        db_session = session_maker()
        try:
            if refund_details and isinstance(refund_details, list):
                db_session.bulk_insert_mappings(cls, refund_details)
            if refund_products and isinstance(refund_products, list):
                db_session.bulk_insert_mappings(SupplyFranchiseeRefundProduct, refund_products)
            if refund_logs and isinstance(refund_logs, list):
                db_session.bulk_insert_mappings(SupplyFranchiseeRefundLog, refund_logs)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def create_franchisee_refund(cls, refund_details: list, refund_products: list, refund_logs: list,
                                 db_session: Session = None):
        """
        创建退款单、商品、记录log
        此方法不负责提交事务，事务由外部管理
        :param db_session: session对象
        :param refund_details: 退款单详情
        :param refund_products: 退款单商品
        :param refund_logs: 退款单创建log
        :return:
        """
        db_session = session_maker()
        try:
            if refund_details and isinstance(refund_details, list):
                db_session.bulk_insert_mappings(cls, refund_details)
            if refund_products and isinstance(refund_products, list):
                db_session.bulk_insert_mappings(SupplyFranchiseeRefundProduct, refund_products)
            if refund_logs and isinstance(refund_logs, list):
                db_session.bulk_insert_mappings(SupplyFranchiseeRefundLog, refund_logs)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_franchisee_refund(cls, partner_id=None, refund_start_date=None, refund_end_date=None, code=None,
                               main_code=None, approved_start_date=None, approved_end_date=None, status=None,
                               received_bys=None, payment_ways=None, types=None, main_type=None, ids=None, limit=-1,
                               offset=None, franchisee_ids=None, include_total=None, order=None, sort=None):
        """加盟商退款列表"""
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if ids and isinstance(ids, list):
                query = query.filter(cls.id.in_(ids))
            if refund_start_date and isinstance(refund_start_date, datetime) and refund_start_date.year != 1970:
                query = query.filter(cls.refund_date >= refund_start_date)
            if refund_end_date and isinstance(refund_end_date, datetime) and refund_end_date.year != 1970:
                query = query.filter(cls.refund_date <= refund_end_date)
            if approved_start_date and isinstance(approved_start_date, datetime) and approved_start_date.year != 1970:
                query = query.filter(cls.approved_date >= approved_start_date)
            if approved_end_date and isinstance(approved_end_date, datetime) and approved_end_date.year != 1970:
                query = query.filter(cls.approved_date <= approved_end_date)
            if franchisee_ids and isinstance(franchisee_ids, list):
                query = query.filter(cls.franchisee_id.in_(franchisee_ids))
            if received_bys and isinstance(received_bys, list):
                query = query.filter(cls.received_by.in_(received_bys))
            if types and isinstance(types, list):
                query = query.filter(cls.type.in_(types))
            if main_type:
                query = query.filter(cls.main_type == main_type)
            if payment_ways and isinstance(payment_ways, list):
                query = query.filter(cls.payment_way.in_(payment_ways))
            if code:
                query = query.filter(cls.code == code)
            if main_code:
                query = query.filter(cls.main_code == main_code)
            if status and isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            # 排序方式
            if not sort:
                sort = "updated_at"
            if order == "asc":
                sort_str = "cls.{}".format(sort)
                query = query.order_by(eval(sort_str))
            else:
                sort_str = "cls.{}.desc()".format(sort)
                query = query.order_by(eval(sort_str))
            if offset:
                query = query.offset(offset)
            if limit and limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(cls.id)).filter(
                    cls.partner_id == partner_id)
                if ids and isinstance(ids, list):
                    query_total = query_total.filter(cls.id.in_(ids))
                if refund_start_date and isinstance(refund_start_date, datetime) and refund_start_date.year != 1970:
                    query_total = query_total.filter(cls.refund_date >= refund_start_date)
                if refund_end_date and isinstance(refund_end_date, datetime) and refund_end_date.year != 1970:
                    query_total = query_total.filter(cls.refund_date <= refund_end_date)
                if approved_start_date and isinstance(approved_start_date,
                                                      datetime) and approved_start_date.year != 1970:
                    query_total = query_total.filter(cls.approved_date >= approved_start_date)
                if approved_end_date and isinstance(approved_end_date, datetime) and approved_end_date.year != 1970:
                    query_total = query_total.filter(cls.approved_date <= approved_end_date)
                if franchisee_ids and isinstance(franchisee_ids, list):
                    query_total = query_total.filter(cls.franchisee_id.in_(franchisee_ids))
                if received_bys and isinstance(received_bys, list):
                    query_total = query_total.filter(cls.received_by.in_(received_bys))
                if types and isinstance(types, list):
                    query_total = query_total.filter(cls.type.in_(types))
                if main_type:
                    query_total = query_total.filter(cls.main_type == main_type)
                if payment_ways and isinstance(payment_ways, list):
                    query_total = query_total.filter(cls.payment_way.in_(payment_ways))
                if code:
                    query_total = query_total.filter(cls.code == code)
                if main_code:
                    query_total = query_total.filter(cls.main_code == main_code)
                if status and isinstance(status, list):
                    query_total = query_total.filter(cls.status.in_(status))
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logger.error("List Franchisee Refunds Error-{}".format(e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_franchisee_refund(cls, update_data, refund_logs=None, allow_status=None, demand_detail=None,
                                 demand_logs=None, refund_update_logs=None):
        """更新加盟商订货退款单
        :param update_data -> dict/list 待更新的数据
        :param refund_logs -> list 操作log
        :param allow_status -> list 允许更新单状态
        :param demand_detail -> list 同步更新订货单状态(可选)
        :param demand_logs -> list  更新订货单log(可选)
        """
        db_session = session_maker()
        try:
            if isinstance(update_data, dict):
                update_db = db_session.query(cls).filter(
                    cls.partner_id == update_data.get('partner_id')).filter(
                    cls.id == update_data.get('id')).with_lockmode('update').first()
                if update_db and allow_status and update_db.status in allow_status:
                    if update_data.get('updated_by'):
                        update_db.updated_by = update_data.get('updated_by')
                    if update_data.get('updated_name'):
                        update_db.updated_name = update_data.get('updated_name')
                    update_db.updated_at = datetime.utcnow()
                    if update_data.get('status'):
                        update_db.status = update_data.get('status')
                    if update_data.get('process_status'):
                        update_db.process_status = update_data.get('process_status')
                    if update_data.get("remark"):
                        update_db.remark = update_data.get('remark')
                    if update_data.get('reject_reason'):
                        update_db.reject_reason = update_data.get('reject_reason')
                    if update_data.get('pay_amount'):
                        update_db.pay_amount = update_data.get('pay_amount')
                    if update_data.get('payment_way'):
                        update_db.payment_way = update_data.get('payment_way')
                    db_session.add(update_db)
                else:
                    return False
            elif isinstance(update_data, list):
                db_session.bulk_update_mappings(cls, update_data)
            else:
                raise Exception("更新数据不合法！")
            if refund_logs and isinstance(refund_logs, list):
                db_session.bulk_insert_mappings(SupplyFranchiseeRefundLog, refund_logs)
            if demand_detail and isinstance(demand_detail, list):
                db_session.bulk_update_mappings(SupplyFranchiseeDemand, demand_detail)
            if demand_logs and isinstance(demand_logs, list):
                db_session.bulk_insert_mappings(SupplyFranchiseeDemandLog, demand_logs)
            if refund_update_logs:
                db_session.bulk_update_mappings(SupplyFranchiseeRefundLog, refund_update_logs)
            db_session.commit()
            return True
        except Exception as e:
            logger.error("** method[%s] error. Error message:%s **" % ('update_f_demand', e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def query_refund_by_main_ids(cls, partner_id, main_ids: list, limit=0, offset=0):
        """根据主单ID查退款单个别字段"""
        db_session = session_maker()
        try:
            query = db_session.query(cls.id, cls.main_id, cls.refund_date, cls.code, cls.status).filter(
                cls.partner_id == partner_id)
            if main_ids and isinstance(main_ids, list):
                query = query.filter(cls.main_id.in_(main_ids))
            query = query.order_by(cls.id)
            if limit:
                query = query.limit(limit).offset(offset)
            return query.all()
        except Exception as e:
            logger.error("query_refund_by_main_ids Error-{}".format(e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_undone_frs_refund(cls, partner_id, start_time, end_time, status=None, process_status=None):
        """获取未处理退款单"""
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if start_time and isinstance(start_time, datetime):
                query = query.filter(cls.updated_at >= start_time)
            if end_time and isinstance(end_time, datetime):
                query = query.filter(cls.updated_at <= end_time)
            if status and isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            if process_status and isinstance(process_status, list):
                query = query.filter(cls.process_status.in_(process_status))
            return query.all(), query.count()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()


class SupplyFranchiseeRefundProduct(DeclarativeBase, TimestampMixin, MethodMixin):
    """加盟商退款单商品"""
    __tablename__ = "supply_franchisee_refund_product"

    refund_id = Column(BigInteger, index=True)  # 订单ID
    partner_id = Column(BigInteger, index=True)  # 租户id
    product_id = Column(BigInteger)  # 商品ID
    product_code = Column(String)  # 商品编号
    product_name = Column(String)  # 商品名称
    category_id = Column(BigInteger)  # 商品分类ID
    unit_id = Column(BigInteger)  # 单位ID
    unit_name = Column(String)  # 单位名称
    unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 单位转换比例
    unit_spec = Column(String)  # 单位规格
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 退货数量
    tax_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税单价
    cost_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 成本单价
    tax_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=6), default=0.0)  # 税率
    amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 合计金额
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)  # 创建人名称
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)  # 更新人名称

    @classmethod
    def list_refund_product(cls, partner_id, refund_id, limit=-1, offset=None, include_total=None, order=None,
                            sort=None):
        """查询订单商品"""
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id, cls.refund_id == refund_id)
            # 排序方式
            if not sort:
                sort = "updated_at"
            if order == "asc":
                sort_str = "cls.{}".format(sort)
                query = query.order_by(eval(sort_str))
            else:
                sort_str = "cls.{}.desc()".format(sort)
                query = query.order_by(eval(sort_str))
            if offset:
                query = query.offset(offset)
            if limit and limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(cls.id)).filter(
                    cls.partner_id == partner_id, cls.refund_id == refund_id)
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logger.error("** method[%s] error. Error message:%s **" % ('list_refund_product', e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_refund_products(cls, products: list):
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(cls, products)
            db_session.commit()
            return True
        except Exception as e:
            logger.error("** method[%s] error. Error message:%s **" % ('update_refund_products', e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def query_product_name_by_refund_ids(cls, refund_ids=None, partner_id=None):
        """根据单据id查询商品名称，给移动端用"""
        db_session = session_maker()
        try:
            query = db_session.query(cls.refund_id, cls.product_name).filter(cls.refund_id.in_(refund_ids),
                                                                             cls.partner_id == partner_id)
            return query.all()
        except Exception as e:
            logger.error("** method[%s] error. Error message:%s **" % ('query_product_name_by_refund_ids', e))
            raise e
        finally:
            db_session.close()


class SupplyFranchiseeRefundLog(DeclarativeBase, MethodMixin):
    """加盟商订货退款操作日志"""
    __tablename__ = "supply_franchisee_refund_log"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)  # 租户ID
    refund_id = Column(BigInteger)  # 退款单ID
    action = Column(String)  # 操作
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String(50))  # 创建人名称
    created_at = Column(DateTime, default=datetime.utcnow())  # 创建时间

    @classmethod
    def get_franchisee_refund_log(cls, refund_id, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.refund_id == refund_id,
                                                 cls.partner_id == partner_id)
            query = query.order_by(cls.created_at.desc())
            demand_logs = query.all()
            if not demand_logs:
                return 0, []
            query_count = db_session.query(func.count(cls.id))
            if partner_id:
                query_count = query_count.filter(cls.partner_id == partner_id)
            if refund_id:
                query_count = query_count.filter(cls.refund_id == refund_id)
            count = query_count.scalar()
            return count, demand_logs
        except Exception as e:
            logger.error("** method[%s] error. Error message:%s **" % ('get_franchisee_demand_log', e))
            raise e
        finally:
            db_session.close()
