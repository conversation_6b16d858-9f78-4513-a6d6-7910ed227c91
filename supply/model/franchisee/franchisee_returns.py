from datetime import datetime

from supply.model.returns import ReturnModel, ReturnProductModel
from supply.driver.mysql import session_maker
from sqlalchemy import or_, and_, case, func


class FranchiseeReturnModel(ReturnModel):

    @classmethod
    def list_returns_by_store(cls, partner_id, code=None, return_bys=None, status=None, logistics_type=None, type=None,
                              sub_type=None, source_code=None, source_id=None, start_date=None, end_date=None,
                              delivery_start_date=None, delivery_end_date=None, sort=None, order=None, limit=None,
                              offset=None,
                              ids=None, return_tos=None, product_ids=None, payment_way=None, query_type=None):
        db_session = session_maker()
        try:
            returns_price = (FranchiseeReturnProductModel.quantity * FranchiseeReturnProductModel.price_tax).label(
                'returns_price')
            query = db_session.query(
                cls.return_by,
                cls.return_to,
                FranchiseeReturnProductModel.product_id,
                FranchiseeReturnProductModel.product_code,
                FranchiseeReturnProductModel.product_name,
                FranchiseeReturnProductModel.unit_spec,
                FranchiseeReturnProductModel.unit_name,
                FranchiseeReturnProductModel.price_tax,
                func.sum(FranchiseeReturnProductModel.quantity).label("quantity"),
                func.sum(returns_price).label("returns_price")
            ).join(FranchiseeReturnProductModel, cls.id == FranchiseeReturnProductModel.return_id)
            query = cls.list_returns_base_query(partner_id=partner_id, code=code, return_bys=return_bys,
                                                status=status, logistics_type=logistics_type, type=type,
                                                sub_type=sub_type, source_code=source_code, source_id=source_id,
                                                start_date=start_date, end_date=end_date,
                                                delivery_start_date=delivery_start_date,
                                                delivery_end_date=delivery_end_date, ids=ids,
                                                sort=sort, order=order, limit=limit, offset=offset,
                                                return_tos=return_tos, product_ids=product_ids,
                                                payment_way=payment_way, query=query, query_type=query_type)
            query = query.group_by(cls.return_by,
                                   cls.return_delivery_number,
                                   FranchiseeReturnProductModel.product_id,
                                   FranchiseeReturnProductModel.product_code,
                                   FranchiseeReturnProductModel.product_name,
                                   FranchiseeReturnProductModel.unit_spec,
                                   FranchiseeReturnProductModel.unit_name,
                                   FranchiseeReturnProductModel.price_tax)
            count = query.count()
            return count, query.all()
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_returns_by_product(cls, partner_id, code=None, return_bys=None, status=None, logistics_type=None,
                                type=None,
                                sub_type=None, source_code=None, source_id=None, start_date=None, end_date=None,
                                delivery_start_date=None, delivery_end_date=None, sort=None, order=None, limit=None,
                                offset=None,
                                ids=None, return_tos=None, product_ids=None, payment_way=None, query_type=None):
        db_session = session_maker()
        try:
            returns_price = (FranchiseeReturnProductModel.quantity * FranchiseeReturnProductModel.price_tax).label(
                'returns_price')
            query = db_session.query(
                cls.return_to,
                FranchiseeReturnProductModel.product_id,
                FranchiseeReturnProductModel.product_code,
                FranchiseeReturnProductModel.product_name,
                FranchiseeReturnProductModel.unit_spec,
                FranchiseeReturnProductModel.price_tax,
                FranchiseeReturnProductModel.unit_name,
                func.sum(FranchiseeReturnProductModel.quantity).label("quantity"),
                func.sum(returns_price).label("returns_price"),
            ).join(FranchiseeReturnProductModel, cls.id == FranchiseeReturnProductModel.return_id)
            query = cls.list_returns_base_query(partner_id=partner_id, code=code, return_bys=return_bys,
                                                status=status, logistics_type=logistics_type, type=type,
                                                sub_type=sub_type, source_code=source_code, source_id=source_id,
                                                start_date=start_date, end_date=end_date,
                                                delivery_start_date=delivery_start_date,
                                                delivery_end_date=delivery_end_date, ids=ids,
                                                sort=sort, order=order, limit=limit, offset=offset,
                                                return_tos=return_tos, product_ids=product_ids, payment_way=payment_way,
                                                query=query, query_type=query_type)
            query = query.group_by(cls.return_delivery_number,
                                   FranchiseeReturnProductModel.product_id,
                                   FranchiseeReturnProductModel.product_code,
                                   FranchiseeReturnProductModel.product_name,
                                   FranchiseeReturnProductModel.unit_spec,
                                   FranchiseeReturnProductModel.price_tax,
                                   FranchiseeReturnProductModel.unit_name)
            count = query.count()
            return count, query.all()
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_returns_base_query(cls, partner_id, code=None, return_bys=None, status=None, logistics_type=None,
                                type=None,
                                sub_type=None, source_code=None, source_id=None, start_date=None, end_date=None,
                                delivery_start_date=None, delivery_end_date=None, sort=None, order=None, limit=None,
                                offset=None,
                                ids=None, return_tos=None, product_ids=None, payment_way=None, query=None,
                                query_type=None):

        group_flag = False
        if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
            # query = query.outerjoin(ReturnProductModel, cls.id == ReturnProductModel.return_id)
            query = query.filter(ReturnProductModel.product_id.in_(product_ids))
        query = query.filter(cls.partner_id == partner_id)
        query = query.filter(or_(and_(cls.status.in_(["APPROVED", "DELIVERYED", "CONFIRMED"]), cls.type == "IAD"),
                                 cls.type != "IAD"))  # 状态为/ 新建/已驳回/已提交/已作废状态的 库存调整退货单，不应显示在退货单列表
        if payment_way:
            query = query.filter(cls.payment_way.in_(payment_way))
        if ids:
            query = query.filter(cls.id.in_(ids))
        if code:
            query = query.filter(cls.code == code)
        if return_bys and isinstance(return_bys, list) and len(return_bys) > 0:
            query = query.filter(cls.return_by.in_(return_bys))
        if return_tos and isinstance(return_tos, list) and len(return_tos) > 0:
            query = query.filter(cls.return_to.in_(return_tos))
        if logistics_type:
            if isinstance(logistics_type, str):
                query = query.filter(cls.logistics_type == logistics_type)
            else:
                query = query.filter(cls.logistics_type.in_(logistics_type))
        if type:
            query = query.filter(cls.type == type)
        if status:
            query = query.filter(cls.status.in_(status))
        if sub_type:
            query = query.filter(cls.sub_type == sub_type)
        if source_code:
            query = query.filter(cls.source_code == source_code)
        if source_id:
            query = query.filter(cls.source_id == source_id)

        if start_date and not isinstance(start_date, datetime):
            start_date = datetime.fromtimestamp(start_date.seconds)
        if isinstance(start_date, datetime) and start_date.year != 1970:
            query = query.filter(cls.return_date >= start_date)
        if end_date and not isinstance(end_date, datetime):
            end_date = datetime.fromtimestamp(end_date.seconds)
        if isinstance(end_date, datetime) and end_date.year != 1970:
            query = query.filter(cls.return_date <= end_date)

        if delivery_start_date and not isinstance(delivery_start_date, datetime):
            delivery_start_date = datetime.fromtimestamp(
                delivery_start_date.seconds)
        if isinstance(delivery_start_date, datetime) and delivery_start_date.year != 1970:
            query = query.filter(
                cls.return_delivery_date >= delivery_start_date)
        if delivery_end_date and not isinstance(delivery_end_date, datetime):
            delivery_end_date = datetime.fromtimestamp(
                delivery_end_date.seconds)
        if isinstance(delivery_end_date, datetime) and delivery_end_date.year != 1970:
            query = query.filter(cls.return_delivery_date <= delivery_end_date)
        query = query.order_by(case(value=cls.status,
                                    whens={"INITED": 1, "SUBMITTED": 2, "REJECTED": 3, "APPROVED": 4, "CONFIRMED": 5,
                                           "CANCELLED": 6, "DELETED": 7}),
                               cls.updated_at.desc())
        return query


class FranchiseeReturnProductModel(ReturnProductModel):
    pass
