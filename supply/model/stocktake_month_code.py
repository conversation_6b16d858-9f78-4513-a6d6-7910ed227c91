from datetime import datetime
from sqlalchemy import (<PERSON><PERSON><PERSON><PERSON>, Column, String)
from sqlalchemy.orm.exc import NoResultFound
from supply.driver.mysql import DummyTransaction
from supply.utils.helper import get_guid
from . import (DeclarativeBase, MethodMixin, TimestampMixin)
#月盘jde要8位,
class Stocktake_month_code(DeclarativeBase, TimestampMixin, MethodMixin):

    __tablename__ = "stocktake_month_code"

    id = Column(BigInteger,primary_key=True,default=get_guid() )
    value = Column(BigInteger)
    created_by = Column(String)
    updated_by = Column(String)
    partner_id = Column(BigInteger)

    @classmethod
    def get_stocktake_code(cls,partner_id):
        """
        获得月盘单据编号
        """
        if not partner_id:
            raise ValueError('partner_id cannot be none')
        doc_value_str = cls.__lock_code_for_update(partner_id)
        #以计数开头补o，跳过计数10的倍数
        if int(doc_value_str)%10==0:
            doc_value_str = cls.__lock_code_for_update(partner_id)
        doc_value_str=doc_value_str.ljust(8,'0')
        return doc_value_str

    @classmethod
    def __lock_code_for_update(cls,partner_id,user_id=None) -> str:
        with DummyTransaction(auto_commit=False) as trans:
            DBSession = trans.scope_session
            q = DBSession.query(cls).filter(cls.partner_id == partner_id)
            doc_code_db = None
            try:
                doc_code_db = q.with_lockmode("update").first()
            except NoResultFound as e:
                doc_code_db = None
            if not doc_code_db:
                doc_code_db = Stocktake_month_code()
                doc_code_db.id=get_guid()
                doc_code_db.partner_id = partner_id
                doc_code_db.value = 1
                if user_id:
                    doc_code_db.created_by = doc_code_db.updated_by = str(user_id)
                doc_code_db.created = doc_code_db.updated = datetime.utcnow()
                DBSession.add(doc_code_db)
            else:
                doc_code_db.value += 1
                doc_code_db.updated_at = datetime.utcnow()
                if user_id:
                    doc_code_db.updated_by = str(user_id)
            DBSession.commit()
            doc_value = doc_code_db.value
            doc_value_str = str(doc_value)
            return doc_value_str