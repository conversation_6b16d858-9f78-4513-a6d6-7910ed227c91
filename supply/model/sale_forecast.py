import functools

from sqlalchemy import (Column, BigInteger,String, Integer, Text, DateTime, )
from sqlalchemy.dialects.mysql import DOUBLE

from supply.model import AsDict
from supply.utils.helper import get_guid
from . import DeclarativeBase, TimestampMixin
from ..driver.mysql import session, session_maker


class DemandSuggestSaleForecast(DeclarativeBase, TimestampMixin, AsDict):
    """面包类和茶饮类的一周内的销量预测
    DDL
    create table supply_xicha_stage.supply_demand_suggest_sale_forecast
(
	id bigint not null comment '主键id' primary key,
	partner_id bigint not null comment '商户id',
	store_id bigint not null comment '门店id',
	product_id bigint not null comment '商品id',
	store_type varchar(20) null comment '门店类型(INIT,CONFIRM)',
    product_sale_type varchar(20) null comment '商品类型',
	forecast_date datetime not null comment '预测日期',
	forecast_amount int not null comment '预测量',
    confirm_amount int null comment '客户确认的预测量'
	status varchar(20) not null comment '预测值状态(INIT,CONFIRM)',
    history_sale_data text null comment '历史销售数据(便于debug)
	updated_by bigint not null comment '更新人',
	created_at datetime null comment '创建时间',
	updated_at datetime null comment '更新时间'
)
comment '茶饮和面包类商品的一周内的销量预测(建议订货量使用)' collate=utf8_bin;

create index supply_demand_suggest_sale_forecast_status_idx
	on supply_xicha_stage.supply_demand_suggest_sale_forecast (status);

create index supply_demand_suggest_sale_forecast_partner_id_idx
	on supply_xicha_stage.supply_demand_suggest_sale_forecast (partner_id);

create index supply_demand_suggest_sale_forecast_store_id_idx
	on supply_xicha_stage.supply_demand_suggest_sale_forecast (store_id);

create index supply_demand_suggest_sale_forecast_product_id_idx
	on supply_xicha_stage.supply_demand_suggest_sale_forecast (product_id);

create index _forecast_date_idx
	on supply_xicha_stage.supply_demand_suggest_sale_forecast (forecast_date);
    """
    __tablename__ = 'supply_demand_suggest_sale_forecast'

    class Status:
        INIT = 'INIT'
        CONFIRM = 'CONFIRM'

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)
    store_id = Column(BigInteger)
    store_type = Column(String)
    product_sale_type = Column(String)
    product_id = Column(BigInteger)
    forecast_date = Column(DateTime)
    forecast_amount = Column(Integer)
    confirm_amount = Column(Integer)
    history_sale_data = Column(Text)
    status = Column(String)
    updated_by = Column(BigInteger)


def logger_call(func):
    @functools.wraps(func)
    def _func(*args, **kwargs):
        return func(*args, **kwargs)

    return _func


class DemandSuggestSaleForecastRepo(object):
    @staticmethod
    def list(partner_id, store_id, product_ids=None,
             start_date=None, end_date=None,
             status=None, limit=20, offset=0):
        query = session.query(DemandSuggestSaleForecast). \
            filter(DemandSuggestSaleForecast.partner_id == partner_id). \
            filter(DemandSuggestSaleForecast.store_id == store_id)
        if start_date:
            query = query.filter(DemandSuggestSaleForecast.forecast_date >= start_date)
        if end_date:
            query = query.filter(DemandSuggestSaleForecast.forecast_date <= end_date)
        if status:
            query = query.filter(DemandSuggestSaleForecast.status == status)
        if product_ids:
            query = query.filter(DemandSuggestSaleForecast.product_id._in)

        query = query.order_by(DemandSuggestSaleForecast.created_at.desc())
        count = query.count()
        if limit > 0:
            query = query.limit(limit).offset(offset)
        return [row2dict(i) for i in query.all()], count

    @staticmethod
    def update_amount(forecast_data):
        session = session_maker()
        try:
            for id, amount in forecast_data.items():
                session.query(DemandSuggestSaleForecast).filter(DemandSuggestSaleForecast.id == id).update({
                    DemandSuggestSaleForecast.confirm_amount: amount,
                })
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            return False
        finally:
            session.close()

    @staticmethod
    def update_status(partner_id, forecast_ids, status):
        session = session_maker()
        try:
            query = session.query(DemandSuggestSaleForecast). \
                filter(DemandSuggestSaleForecast.partner_id == partner_id). \
                filter(DemandSuggestSaleForecast.id._in(forecast_ids))
            query.update({DemandSuggestSaleForecast.status: status})
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            return False
        finally:
            session.close()


def row2dict(row):
    d = {}
    for column in row.__table__.columns:
        d[column.name] = getattr(row, column.name)

    return d
