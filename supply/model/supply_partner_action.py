from sqlalchemy import <PERSON><PERSON>n, <PERSON><PERSON><PERSON>, BigInteger, String, INTEGER

from supply.driver.mysql import session
from supply.model import DeclarativeBase, TimestampMixin, AsDict


class SupplyPartnerAction(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_partner_action'
    id = Column(INTEGER, primary_key=True)
    partner_id = Column(BigInteger)
    module = Column(String)
    from_action = Column(String)
    to_action = Column(String)

    @classmethod
    def get_to_action(cls, partner_id, module, from_action):
        to_action = session.query(cls.to_action).filter(cls.from_action == from_action,
                                                        cls.module == module,
                                                        cls.partner_id == partner_id).first()
        return (to_action.to_action if to_action else to_action) or from_action