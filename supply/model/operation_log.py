# -*- coding:utf-8 -*-

from datetime import datetime
from sqlalchemy import Column, DECIMAL, BigInteger, String, DateTime, Boolean, distinct

from ..model import DeclarativeBase, MethodMixin, TimestampMixin, AsDict
from ..utils.snowflake import gen_snowflake_id
from ..driver.mysql import session_maker


class DocTypeEnum(object):
    """receipt_flow_log用的单据类型doc_type枚举值"""
    # 外部调拨
    TRANSFER = "transfer"
    # 内部调拨
    INSIDE_TRANSFER = "insideTransfer"
    # 配送收货
    DELIVERY_RECEIVE = "deliveryReceive"
    # 配送退货
    DELIVERY_RETURN = "deliveryReturn"
    # 直送收货
    DIRECT_RECEIVE = "directReceive"
    # 直送退货
    DIRECT_RETURN = "directReturn"
    # 销售
    SALE = "sale"
    # 发货
    DELIVER_GOODS = "deliverGoods"
    # 包装单
    PACKING_ORDER = "packingOrder"
    # 加工单
    PROCESS_ORDER = "processOrder"


# 单据工作流记录
class ReceiptFlowLogModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    __tablename__ = 'receipt_flow_log'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 单据id
    doc_id = Column(BigInteger)
    # 单据类型 值见上DocTypeEnum类枚举
    doc_type = Column(String(15))
    # 单据状态
    doc_status = Column(String(10))
    # 单据sub类型(标记该单据是门店层面收货还是仓位收货：position/store)
    sub_doc_type = Column(String(15))
    # 门店id
    branch_id = Column(BigInteger)
    # 仓位id
    sub_branch_id = Column(BigInteger)
    # 操作
    operation = Column(String(15))
    # 下一个单据id
    posterior_doc_id = Column(BigInteger)
    # 下一个单据类型
    posterior_doc_type = Column(String(15))
    # 下一个单据状态
    posterior_doc_status = Column(String(10))
    # 下一个操作
    posterior_operation = Column(String(15))
    # 操作状态 INITED/PROCESSING/COMPLETED
    process_status = Column(String(10))
    # 备注，记录该工作流为什么完成：比如没拉到规则或单据商品不在规则内等
    remark = Column(String(50))
    partner_id = Column(BigInteger, index=True)
    # 操作人
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))

    @classmethod
    def create_flow_logs(cls, **kwargs):
        obj = cls()
        for k, v in kwargs.items():
            if k in obj.__table__.columns.keys() and v is not None:
                setattr(obj, k, v)
        db_session = session_maker()
        try:
            db_session.add(obj)
            db_session.commit()
            return obj
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_flow_log(cls, _id=None, doc_id=None, partner_id=None):
        """获取一条工作流记录
        _id: 主键id
        doc_id: 原单据id
        """
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if _id:
                query = query.filter(cls.id == _id)
                return query.first()
            if doc_id:
                query = query.filter(cls.doc_id == doc_id)
                return query.first()
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_flow_logs(cls, doc_type=None, posterior_doc_type=None,
                       operation=None, posterior_operation=None,
                       doc_status=None, posterior_doc_status=None,
                       process_status=None, limit=None, offset=None, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if doc_type:
                query = query.filter(cls.doc_type == doc_type)
            if posterior_doc_type:
                query = query.filter(cls.posterior_doc_type == posterior_doc_type)
            if operation:
                query = query.filter(cls.operation == operation)
            if posterior_operation:
                query = query.filter(cls.posterior_operation == posterior_operation)
            if doc_status:
                query = query.filter(cls.doc_status == doc_status)
            if posterior_doc_status:
                query = query.filter(cls.posterior_doc_status == posterior_doc_status)
            if process_status:
                query = query.filter(cls.process_status == process_status)

            count = query.count()
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            list_flow_logs = query.all()
            return count, list_flow_logs
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_flow_logs(cls, flow_id=None, update_data=None, user_id=None, username=None, partner_id=None):
        """更新一条记录process_status，同时新建一条记录，
        operation为上一个记录的posterior_operation"""
        db_session = session_maker()
        try:
            update_flow_db = db_session.query(cls).filter(cls.id == flow_id).filter(cls.partner_id == partner_id).with_lockmode("update").first()
            if update_flow_db:
                update_flow_db.process_status = update_data.get('process_status')  # COMPLETED/PROCESSING
                update_flow_db.updated_by = user_id
                update_flow_db.updated_name = username
                update_flow_db.remark = update_data.get('remark')
                db_session.add(update_flow_db)
            if update_data.get('process_status') == "COMPLETED" and update_flow_db.posterior_operation:
                add_db = cls()
                add_db.id = gen_snowflake_id()
                add_db.doc_id = update_flow_db.doc_id
                add_db.doc_type = update_flow_db.doc_type
                add_db.doc_status = update_flow_db.doc_status
                add_db.sub_doc_type = update_flow_db.sub_doc_type
                add_db.branch_id = update_flow_db.branch_id
                add_db.sub_branch_id = update_flow_db.sub_branch_id
                add_db.operation = update_flow_db.posterior_operation
                add_db.posterior_operation = ""  # 下一个操作暂时给空
                add_db.process_status = "INITED"
                add_db.partner_id = partner_id
                add_db.created_by = user_id
                add_db.created_name = username
                add_db.updated_by = user_id
                add_db.updated_name = username
                db_session.add(add_db)
                flow_id = add_db.id
            db_session.commit()
            return flow_id
        except Exception as e:
            raise e
        finally:
            db_session.close()


class ReceiptFlowProductLog(DeclarativeBase, TimestampMixin, AsDict):
    """单据工作流商品操作记录"""

    __tablename__ = 'receipt_flow_product_log'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 操作业务类型： (AUTO_TRANSFER-自动调拨/AUTO_CONVERT-自动物料转换)
    type = Column(String(30))
    # 状态INITED、SUCCESS
    status = Column(String(20))
    # 工作流id
    flow_id = Column(BigInteger, index=True)
    # 批次id对应每次创建单据请求的request_id
    batch_id = Column(BigInteger)
    # 标记商品是否可以重复操作
    reusable = Column(Boolean, default=False)
    # 对应单据id
    doc_id = Column(BigInteger, index=True)
    # 业务发生时间
    business_date = Column(DateTime, default=datetime.utcnow())
    # 规则id(调拨规则/物料转换规则)
    rule_id = Column(BigInteger)
    # 自动调拨接收仓位
    receiving_position = Column(BigInteger)
    product_id = Column(BigInteger)
    target_product_id = Column(BigInteger)
    unit_id = Column(BigInteger)
    target_unit_id = Column(BigInteger)
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    target_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))

    @classmethod
    def create_flow_product_logs(cls, data_list: list):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(cls, data_list)
            db_session.commit()
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_flow_product_ids(cls, doc_id=None, reusable=None, partner_id=None):
        """根据业务单据id筛选出不可重复操作的商品"""
        db_session = session_maker()
        try:
            result = []
            query = db_session.query(cls.product_id).filter(cls.partner_id == partner_id).filter(cls.doc_id == doc_id)
            if reusable is not None:
                query = query.filter(cls.reusable == reusable)
            query_set = query.all()
            if query_set:
                for i in query_set:
                    result.append(i[0])
            return result
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_flow_product_logs(cls, batch_id, status, partner_id=None, operator_name=None, user_id=None):
        """通过batch_id更新批次商品状态
        status: "SUCCESS"
        """
        db_session = session_maker()
        try:
            data_list = []
            query = db_session.query(cls.id).filter(cls.partner_id == partner_id).filter(
                cls.batch_id == batch_id)
            query_set = query.all()
            if query_set:
                for i in query_set:
                    row = dict(
                        id=i[0],
                        updated_by=user_id,
                        updated_name=operator_name,
                        status=status,
                    )
                    data_list.append(row)
            db_session.bulk_update_mappings(cls, data_list)
            db_session.commit()
            return True
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_flow_product_logs(cls, _type=None, status=None, flow_id=None, batch_ids=None, doc_id=None,
                               limit=None, offset=None, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if flow_id:
                query = query.filter(cls.flow_id == flow_id)
            if batch_ids:
                query = query.filter(cls.batch_id.in_(batch_ids))
            if doc_id:
                query = query.filter(cls.doc_id == doc_id)
            if status:
                query = query.filter(cls.status == status)
            if _type:
                query = query.filter(cls.type == _type)

            count = query.count()
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            list_flow_product_logs = query.all()
            return count, list_flow_product_logs
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_flow_product_batch_ids(cls, flow_id=None, status=None):
        """查询一个单据的商品处理的批次列表(distinct)"""
        db_session = session_maker()
        batch_ids = []
        try:
            query = db_session.query(distinct(cls.batch_id))
            if flow_id:
                query = query.filter(cls.flow_id == flow_id)
            if status:
                query = query.filter(cls.status == status)

            count = query.count()
            query_set = query.all()
            if query_set:
                for q in query_set:
                    batch_ids.append(q[0])
            return count, batch_ids
        except Exception as e:
            raise e
        finally:
            db_session.close()


# 单据传输三方记录
class TpTransLogModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):

    __tablename__ = 'supply_tp_trans_log'

    # id同单据id
    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    doc_code = Column(String(30))
    doc_type = Column(String(30))
    # 处理状态
    status = Column(String(30))
    # 发送消息
    msg = Column(String(255))
    partner_id = Column(BigInteger)
    # 操作人
    created_by = Column(BigInteger)

    @classmethod
    def create_logs_list(cls, log_details):
        db_session = session_maker()
        try:
            updated_log_details = []
            for log_detail in log_details:
                q1 = db_session.query(cls).filter(cls.id == int(log_detail['id'])).filter(cls.status == 'success')
                if q1.count():
                    log_details.remove(log_detail)
                q2 = db_session.query(cls).filter(cls.id == int(log_detail['id'])).filter(cls.status != 'success')
                if q2.count():
                    log_details.remove(log_detail)
                    updated_log_details.append(log_detail)

            if log_details and len(log_details)>0:
                db_session.bulk_insert_mappings(cls, log_details)
            if updated_log_details and len(updated_log_details)>0:
                db_session.bulk_update_mappings(cls, updated_log_details)
            db_session.commit()
            return True
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_by_doc_id(cls, doc_id, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            query = query.filter(cls.id == int(doc_id)).filter(cls.partner_id == partner_id)
            return query.all(), query.count()
        except Exception as e:
            raise e
        finally:
            db_session.close()
    
    @classmethod
    def get_by_doc_code(cls, doc_code, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            query = query.filter(cls.doc_code == doc_code).filter(cls.partner_id == partner_id)
            return query.all(), query.count()
        except Exception as e:
            raise e
        finally:
            db_session.close()
    
    @classmethod
    def get_docs(cls, status, partner_id, start_time=None, end_time=None):
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            query = query.filter(cls.partner_id == partner_id).filter(cls.status.in_(status))
            query = query.filter(cls.created_at>=start_time).filter(cls.created_at<end_time)
            return query.all()
        except Exception as e:
            raise e
        finally:
            db_session.close()
    
    @classmethod
    def update_doc_status(cls, log_details):
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(cls, log_details)
            db_session.commit()
            return True
        except Exception as e:
            raise e
        finally:
            db_session.close()
