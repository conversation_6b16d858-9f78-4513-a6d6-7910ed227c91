# -*-: coding:utf-8 -*-

from sqlalchemy import (Column, BigInteger, Integer, String, Text, DateTime, DECIMAL)

from supply.utils.snowflake import gen_snowflake_id
from . import TimestampMixin, MethodMixin, DeclarativeBase
from . import DBSession, db_commit


class Supply_factor(DeclarativeBase, TimestampMixin, MethodMixin):

    __tablename__ = "supply_factor"

    partner_id = Column(BigInteger)
    store_id = Column(BigInteger)
    weather = Column(DECIMAL(precision=18, scale=8,
                decimal_return_scale=8), default=0.0)
    Prom1 = Column(DECIMAL(precision=18, scale=8,
                decimal_return_scale=8), default=0.0)
    Prom2 = Column(DECIMAL(precision=18, scale=8,
                decimal_return_scale=8), default=0.0)
    demand_date = Column(DateTime)
    created_by = Column(BigInteger)
    created_name = Column(String)
    updated_by = Column(BigInteger)
    updated_name = Column(String)
    status = Column(String)

    @classmethod
    def get_by_store_id_and_date(cls, store_id, bizdt, partner_id):
        return DBSession.query(cls).filter(cls.store_id==store_id).filter(
            cls.demand_date==bizdt).filter(cls.partner_id==partner_id).first()