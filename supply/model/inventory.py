from ..driver.mysql import session_maker
from supply.utils.helper import convert_to_decimal
import logging, sys, traceback
from ..utils.helper import get_guid, set_db
from datetime import datetime
from . import DeclarativeBase, TimestampMixin, MethodMixin, AsDict
from sqlalchemy import (Column, BigInteger, Integer, String, Boolean, DateTime, DECIMAL, TEXT, func)
import json
from ..driver.mysql import session
from supply.client.metadata_service import metadata_service
from ..utils.encode import CJsonEncoder
from supply.error.exception import DataValidationException


class InventoryDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'supply_inventory_action_doc'
    id = Column(BigInteger, primary_key=True, default=get_guid())
    batch_no = Column(String(50))
    code = Column(String(50))
    batch_action = Column(Integer)
    action_dec = Column(String(25))
    batch_id = Column(BigInteger)
    status = Column(String(10))
    partner_id = Column(BigInteger)
    user_id = Column(BigInteger)
    message = Column(TEXT)
    trace_id = Column(String(25))

class InventoryRepository(object):

    def check_has_error_calculate_send_message(self, batch_no, partner_id):
        if not partner_id:
            raise DataValidationException("缺失租户信息")
        db_session = session_maker()
        try:
            query = db_session.query(InventoryDB.id).filter(InventoryDB.batch_no == batch_no,
                                                            InventoryDB.status == 'ERROR',
                                                            InventoryDB.partner_id==partner_id).all()
            if len(query)>0:
                return True
            else:
                return False
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            raise e
        finally:
            db_session.close()

    def save_calculate_send_message(self, inventory_dict: dict, message: dict, partner_id=None, user_id=None, trace_id=None):
        if not partner_id:
            raise DataValidationException("缺失租户信息")
        db_session = session_maker()
        try:
            trace_id = trace_id if trace_id else inventory_dict.get('batch_no')
            inventory_db = InventoryDB()
            inventory_db.created_at = datetime.utcnow()
            inventory_db.updated_at = datetime.utcnow()
            set_db(inventory_db, inventory_dict)
            inventory_db.id = get_guid()
            inventory_db.partner_id = partner_id
            inventory_db.user_id = user_id
            inventory_db.message = json.dumps(message, cls=CJsonEncoder)  # CJsonEncoder使json可以存datetime
            inventory_db.trace_id = trace_id
            if user_id:
                inventory_db.created_by = inventory_db.updated_by = user_id
            db_session.add(inventory_db)
            db_session.commit()
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_need_re_calculate_send_message(self, date_time, partner_id=None):
        if not partner_id:
            raise DataValidationException("缺失租户信息")
        db_session = session_maker()
        try:
            sql = '''SELECT * from supply_inventory_action_doc WHERE created_at>'{}' and status='ERROR' and partner_id='{}';
                    '''.format(date_time, partner_id)
            # print('sql', sql)
            datas = db_session.execute(sql).fetchall()
            error_list = []
            for d in datas:
                # print(d)
                sql = '''SELECT * from supply_inventory_action_doc WHERE batch_no='{}' and status in('SUCCESS','INIT') and partner_id='{}';
                                '''.format(d["batch_no"], partner_id)
                atas = db_session.execute(sql).fetchall()
                # print('sql', sql)
                # print('atas', atas)
                if len(atas) > 0:
                    continue
                else:
                    error_list.append(d)
            return error_list
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            db_session.rollback()
            raise e
        finally:
            db_session.close()




    def get_need_re_calculate_send_message_by_id(self, id):

        db_session = session_maker()
        try:
            sql = '''SELECT * from supply_inventory_action_doc WHERE id ={};
                    '''.format(id)
            # print('sql', sql)
            datas = db_session.execute(sql).fetchall()
            error_list = []
            for d in datas:
                # print(d)
                error_list.append(d)
            return error_list
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            db_session.rollback()
            raise e
        finally:
            db_session.close()

inventory_repository = InventoryRepository()
