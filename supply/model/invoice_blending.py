# -*- coding: utf-8 -*-
import logging
import traceback
import sys

from datetime import datetime
from supply.model import DeclarativeBase, MethodMixin, TimestampMixin
from sqlalchemy import Column, BigInteger, String, func, DECIMAL, SMALLINT, TEXT
from supply.utils.helper import get_guid
from supply.driver.mysql import session_maker


class InvoiceBlendingOrder(DeclarativeBase, MethodMixin, TimestampMixin):
    """发票勾兑单"""
    __tablename__ = "invoice_blending_order"

    class Status(object):
        SUBMITTED = 'SUBMITTED'  # 已提交
        REJECTED = 'REJECTED'  # 已驳回
        APPROVED = 'APPROVED'  # 已审核
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 商户id
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)    # 创建人姓名
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)    # 更新人姓名
    code = Column(String)      # 勾兑单编号
    status = Column(String, default=Status.SUBMITTED)    # 勾兑单状态
    company_id = Column(BigInteger)  # 公司id
    company_code = Column(String)    # 公司编号
    company_name = Column(String)    # 公司名称
    supplier_id = Column(BigInteger)  # 供应商id
    supplier_code = Column(String)    # 供应商编号
    supplier_name = Column(String)    # 供应商名称
    order_ids_json = Column(TEXT)  # 勾兑的单据列表{"order_ids":[1,2,3]}
    invoice_ids_json = Column(TEXT)  # 勾兑的发票id列表 {"invoice_ids":[1,2,3]}
    # 单据含税合计 直送收货单含税金额+采购收货单含税金额-直送退货单含税金额-采购退货单含税金额
    order_sum_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    order_sum_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 单据税额合计
    invoice_sum_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 发票含税合计
    invoice_sum_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 发票税额合计
    diff_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 差异金额 发票含税合计-单据含税合计
    diff_reason = Column(String)  # 差异原因


class InvoiceBlendingOrderDB(object):
    """发票勾兑数据库操作"""
    def create_invoice_blending(self, data):
        """创建发票勾兑"""
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(InvoiceBlendingOrder, [data])
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Invoice Blending Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                          traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def change_invoice_blending_status(self, update_data):
        """修改勾兑单状态
        :param update_data 待更新的数据 <list>"""
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(InvoiceBlendingOrder, update_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Change Invoice Blending Status Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                                  traceback.format_exc().replace('\n',
                                                                                                                 ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def update_invoice_blending(self, update_data_list):
        """更新已驳回的勾兑单内容
        :param update_data_list 待更新的数据 <list>"""
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(InvoiceBlendingOrder, update_data_list)
            db_session.commit()
            return True
        except Exception as e:
            logging.error(
                "Update Invoice Blending Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                 traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def list_invoice_blending(self, code=None, status=None, company_ids=None, supplier_ids=None, limit=-1, offset=None,
                              include_total=None, sort_type=None, sort=None, partner_id=None, user_id=None):
        """查询勾兑单"""
        db_session = session_maker()
        try:
            query = db_session.query(InvoiceBlendingOrder).filter(InvoiceBlendingOrder.partner_id == partner_id)
            if code:
                query = query.filter(InvoiceBlendingOrder.code == code)
            if status:
                query = query.filter(InvoiceBlendingOrder.status == status)
            if company_ids:
                query = query.filter(InvoiceBlendingOrder.company_id.in_(company_ids))
            if supplier_ids:
                query = query.filter(InvoiceBlendingOrder.supplier_id.in_(supplier_ids))
            if sort in ["order_sum_price", "order_sum_tax", "invoice_sum_price", "invoice_sum_tax", "diff_price",
                        "updated_at"]:
                if sort_type == "asc":
                    query_str = "InvoiceBlendingOrder.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "InvoiceBlendingOrder.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(InvoiceBlendingOrder.id)).filter(
                    InvoiceBlendingOrder.partner_id == partner_id)
                if code:
                    query_total = query_total.filter(InvoiceBlendingOrder.code == code)
                if status:
                    query_total = query_total.filter(InvoiceBlendingOrder.status == status)
                if company_ids:
                    query_total = query_total.filter(InvoiceBlendingOrder.company_id.in_(company_ids))
                if supplier_ids:
                    query_total = query_total.filter(InvoiceBlendingOrder.supplier_id.in_(supplier_ids))
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Invoice Blending Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                         traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()


invoice_blending_order_db = InvoiceBlendingOrderDB()

