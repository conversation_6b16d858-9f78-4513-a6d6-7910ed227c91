# -*- coding:utf-8 -*-
import logging, sys, traceback
from datetime import datetime, date, timedelta
from decimal import Decimal
from google.protobuf.timestamp_pb2 import Timestamp

from sqlalchemy import Index, Column, DECIMAL, BigInteger, Integer, String, Text, DateTime, Boolean, UniqueConstraint, func
from sqlalchemy.dialects.postgresql import JSO<PERSON>
from sqlalchemy.orm.exc import NoResultFound


from supply.model import DeclarativeBase, MethodMixin, TimestampMixin, AsDict
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.helper import set_db, set_model_from_db, update_db
from supply.driver.mysql import session_maker
from supply.error.exception import DataValidationException




class BusinessConfigModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    ''' 
    业务配置项
    '''
    __tablename__ = 'business_config'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    partner_id = Column(BigInteger)
    # 业务编码
    business_schema = Column(String(25))
    # 业务名称
    business_name = Column(String(50))
    # 是否开启
    is_open = Column(String(10))
    # 额外配置
    extra_config = Column(String(255))

    @classmethod
    def get_business_config(cls, business_schema, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            query = query.filter(cls.business_schema == business_schema).filter(cls.partner_id == partner_id)
            return query.first()
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            raise e
        finally:
            db_session.close()
   

    