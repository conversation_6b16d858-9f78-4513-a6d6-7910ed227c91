# -*- coding: utf-8 -*-
from . import DeclarativeBase, TimestampMixin
from sqlalchemy import (Column, BigInteger, Integer, String, BOOLEAN)
from supply.utils.helper import get_guid
from ..driver.mysql import session_maker
import logging


class HaviSwitchDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'supply_havi_switch'
    id = Column(BigInteger, primary_key=True, default=get_guid())
    partner_id = Column(BigInteger)
    store_id = Column(BigInteger)
    use_wms = Column(BOOLEAN)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)


class HaviSwitchRepository(object):

    def __init__(self):
        pass

    def get_all(self):
        db_session = session_maker()
        try:
            query = db_session.query(HaviSwitchDB).all()
            return query
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_data(self, update_datas):
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(HaviSwitchDB, update_datas)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[%s] error. Error message:%s **" % ('update_transfer_product', e))
            raise e
        finally:
            db_session.close()

    def insert_data(self, insert_datas):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(HaviSwitchDB, insert_datas)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[%s] error. Error message:%s **" % ('update_transfer_product', e))
            raise e
        finally:
            db_session.close()

    def check_use_hws_by_store_id(self, store_id):
        db_session = session_maker()
        try:
            query = db_session.query(HaviSwitchDB).filter(HaviSwitchDB.store_id == store_id,
                                                          HaviSwitchDB.use_wms == True).first()
            return query
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[%s] error. Error message:%s **" % ('update_transfer_product', e))
            raise e
        finally:
            db_session.close()
