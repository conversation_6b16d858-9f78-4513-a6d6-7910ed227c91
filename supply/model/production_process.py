import logging
import sys
import traceback

from supply.model import DeclarativeBase, TimestampMixin, AsDict
from sqlalchemy import (Column, BigInteger, Integer, String, Boolean, DateTime, DECIMAL)
from supply.utils.helper import get_guid, datetime2str
from datetime import datetime
from supply.driver.mysql import session_maker
from sqlalchemy import text, func, or_, and_, desc
from ..driver.mysql import DummyTransaction
from supply.utils.time import get_timestamp


class ProductionProcessReceipts(DeclarativeBase, TimestampMixin, AsDict):
    """生产单业务类模型"""
    __tablename__ = "supply_production_process_receipts"

    class Status(object):
        INITED = 'INITED'  # 新建
        SUBMITTED = 'SUBMITTED'  # 已提交
        APPROVED = 'APPROVED'  # 已审核
        REJECTED = 'REJECTED'  # 已驳回
        DELETED = 'DELETED'  # 已删除
        CONFIRMED = 'CONFIRMED'  # 已完成
        INVALID = 'INVALID'  # 已作废

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    # created_at = Column(DateTime, default=datetime.utcnow())
    # updated_at = Column(DateTime, default=datetime.utcnow(), onupdate=datetime.utcnow())
    code = Column(String(50), index=True)  # 编号
    status = Column(String(20), default=Status.INITED)  # 状态
    process_date = Column(DateTime, index=True)  # 生产日期
    process_store_id = Column(BigInteger, index=True)  # 生产门店
    type = Column(String(20))  # 仓库：warehouse, 门店：store, 加工中心：manufactory
    process_store_code = Column(String(50))
    process_store_name = Column(String(50))
    remark = Column(String(50))  # 备注
    request_id = Column(BigInteger)  # 幂等性校验请求id


class ProductionProcessReceiptsDetail(DeclarativeBase, TimestampMixin, AsDict):
    """生产单详情"""
    __tablename__ = "supply_production_process_receipts_detail"

    # class Status(object):
    #     INITED = 'INITED'  # 新建
    #     SUBMITTED = 'SUBMITTED'  # 已提交
    #     APPROVED = 'APPROVED'  # 已审核
    #     REJECTED = 'REJECTED'  # 已驳回

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    # created_at = Column(DateTime, default=datetime.utcnow())
    # updated_at = Column(DateTime, default=datetime.utcnow(), onupdate=datetime.utcnow())
    main_id = Column(BigInteger, index=True)  # 主生产单ID
    # code = Column(String(50), index=True)  # 编号
    # status = Column(String(20), default=Status.INITED)  # 状态
    # process_date = Column(DateTime, index=True)  # 生产日期
    # process_store_id = Column(BigInteger, index=True)  # 生产门店
    # process_store_code = Column(String(50))
    # process_store_name = Column(String(50))
    # remark = Column(String(50))  # 备注
    production_rule = Column(BigInteger, index=True)  # 生产规则ID
    production_rule_code = Column(String(50))
    production_rule_name = Column(String(50))
    type = Column(String(20))  # type类型，是原料或是生产物
    product_id = Column(BigInteger)  # 原料或者生产物ID
    product_code = Column(String(50))
    product_name = Column(String(50))
    unit_id = Column(BigInteger)  # 原料或者生产物单位ID
    unit_name = Column(String(50))
    unit_code = Column(String(50))
    specification_id = Column(BigInteger)  # 规格id
    specification_name = Column(String(50))  # 规格name
    specification_code = Column(String(50))  # 规格code
    production_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)


class ProductionProcessReceiptsItems(DeclarativeBase, TimestampMixin, AsDict):
    """记录理论转换率和实际转换率"""
    __tablename__ = "supply_production_process_receipts_items"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    # created_at = Column(DateTime, default=datetime.utcnow())
    # updated_at = Column(DateTime, default=datetime.utcnow(), onupdate=datetime.utcnow())
    main_id = Column(BigInteger, index=True)  # 主生产单ID
    production_rule = Column(BigInteger, index=True)  # 生产规则ID
    production_rule_code = Column(String(50))
    production_rule_name = Column(String(50))
    from_material_id = Column(BigInteger, index=True)  # 原料
    from_material_code = Column(String(50))
    from_material_name = Column(String(50))
    to_material_id = Column(BigInteger, index=True)  # 产出物
    to_material_code = Column(String(50))
    to_material_name = Column(String(50))
    theoretical_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 理论转换率
    actual_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 实际转换率


class ProductionProcessReceiptsDB(object):
    """生产单数据操作"""

    def get_production_receipt_by_id(self, receipt_id=None, request_id=None, partner_id=None, is_detail=False,
                                     need_item=True):
        db_session = session_maker()
        try:
            if is_detail and need_item:
                details_query = db_session.query(ProductionProcessReceiptsDetail).filter(
                    ProductionProcessReceiptsDetail.main_id == receipt_id).filter(
                    ProductionProcessReceiptsDetail.partner_id == partner_id)
                items_query = db_session.query(ProductionProcessReceiptsItems).filter(
                    ProductionProcessReceiptsItems.main_id == receipt_id).filter(
                    ProductionProcessReceiptsItems.partner_id == partner_id)
                details = details_query.all()
                items = items_query.all()
                return details, items
            elif is_detail and not need_item:
                details_query = db_session.query(ProductionProcessReceiptsDetail).filter(
                    ProductionProcessReceiptsDetail.main_id == receipt_id).filter(
                    ProductionProcessReceiptsDetail.partner_id == partner_id)
                details = details_query.all()
                return details
            else:
                query = db_session.query(ProductionProcessReceipts).filter(
                    ProductionProcessReceipts.partner_id == partner_id)
                if receipt_id:
                    query = query.filter(ProductionProcessReceipts.id == receipt_id)
                if request_id:
                    query = query.filter(ProductionProcessReceipts.request_id == request_id)
                return query.first()
        except Exception as e:
            logging.error("Get ProductionProcess Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                                 traceback.format_exc().replace('\n',
                                                                                                                ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_production_receipts(self, receipt_data=None, receipt_details=None, items=None):
        """保存生产单和商品详情
        :param receipt_data 生产单对应字段数据 dict
        :param receipt_details 生产单详情对应数据 list[detail,..]
        :param items 生产单条目明细 list[item,..]
        """
        db_session = session_maker()
        try:
            if receipt_data:
                receipts = [receipt_data]
                db_session.bulk_insert_mappings(ProductionProcessReceipts, receipts)
            if receipt_details:
                db_session.bulk_insert_mappings(ProductionProcessReceiptsDetail, receipt_details)
            if items:
                db_session.bulk_insert_mappings(ProductionProcessReceiptsItems, items)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Packing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                          traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_process_receipts(self, partner_id=None, user_id=None, start_date=None, end_date=None, code=None,
                              status=None, limit=-1, offset=None, branch_ids=None, include_total=False, branch_type=None):
        db_session = session_maker()
        try:
            query = db_session.query(ProductionProcessReceipts).filter(
                ProductionProcessReceipts.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(ProductionProcessReceipts.process_date >= start_date)
            if end_date and isinstance(end_date, datetime) and start_date.year != 1970:
                query = query.filter(ProductionProcessReceipts.process_date <= end_date)
            if code:
                query = query.filter(ProductionProcessReceipts.code == code)
            if branch_ids:
                query = query.filter(ProductionProcessReceipts.process_store_id.in_(branch_ids))
            if branch_type:
                query = query.filter(ProductionProcessReceipts.type == branch_type)
            if status and isinstance(status, list):
                query = query.filter(ProductionProcessReceipts.status.in_(status))
            query = query.order_by(ProductionProcessReceipts.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(ProductionProcessReceipts.id)).filter(
                    ProductionProcessReceipts.partner_id == partner_id)
                if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                    query_total = query_total.filter(ProductionProcessReceipts.process_date >= start_date)
                if end_date and isinstance(end_date, datetime) and start_date.year != 1970:
                    query_total = query_total.filter(ProductionProcessReceipts.process_date <= end_date)
                if code:
                    query_total = query_total.filter(ProductionProcessReceipts.code == code)
                if branch_ids:
                    query_total = query_total.filter(ProductionProcessReceipts.process_store_id.in_(branch_ids))
                if branch_type:
                    query_total = query_total.filter(ProductionProcessReceipts.type == branch_type)
                if status and isinstance(status, list):
                    query_total = query_total.filter(ProductionProcessReceipts.status.in_(status))
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List ProductionProcess Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                                  traceback.format_exc().replace('\n',
                                                                                                                 ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def delete_production_process_receipts(self, partner_id=None, receipt_id=None, rule_ids=None):
        db_session = session_maker()
        try:
            db_session.query(ProductionProcessReceiptsDetail).filter(
                ProductionProcessReceiptsDetail.main_id == receipt_id).filter(
                ProductionProcessReceiptsDetail.partner_id == partner_id).filter(
                ProductionProcessReceiptsDetail.production_rule.in_(rule_ids)).delete(synchronize_session=False)
            db_session.query(ProductionProcessReceiptsItems).filter(
                ProductionProcessReceiptsItems.main_id == receipt_id).filter(
                ProductionProcessReceiptsItems.partner_id == partner_id).filter(
                ProductionProcessReceiptsItems.production_rule.in_(rule_ids)).delete(synchronize_session=False)
            db_session.commit()
        except Exception as e:
            logging.error("delete ProductionProcess Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                                    traceback.format_exc().replace('\n',
                                                                                                                   ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_production_process_receipts(self, receipt: dict, receipt_detail_list: list, items_list: list,
                                           partner_id=None):
        db_session = session_maker()
        try:
            receipt_db = db_session.query(ProductionProcessReceipts).filter(
                ProductionProcessReceipts.id == receipt.get('id'),
                ProductionProcessReceipts.partner_id == partner_id).with_lockmode('update').first()
            if receipt.get('updated_by'):
                receipt_db.updated_by = receipt.get('updated_by')
            if receipt.get('updated_name'):
                receipt_db.updated_name = receipt.get('updated_name')
            receipt_db.updated_at = datetime.utcnow()
            if receipt.get('remark'):
                receipt_db.remark = receipt.get('remark')
            if receipt.get('process_store_id'):
                receipt_db.process_store_id = receipt.get('process_store_id')
            if receipt.get('process_store_code'):
                receipt_db.process_store_code = receipt.get('process_store_code')
            if receipt.get('process_store_name'):
                receipt_db.process_store_name = receipt.get('process_store_name')
            if receipt.get('status'):
                receipt_db.status = receipt.get('status')
            if receipt_detail_list:
                db_session.bulk_update_mappings(ProductionProcessReceiptsDetail, receipt_detail_list)
            if items_list:
                db_session.bulk_update_mappings(ProductionProcessReceiptsItems, items_list)
            db_session.add(receipt_db)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update ProductProcess Receipts Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                                  traceback.format_exc().replace('\n',
                                                                                                                 ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_material_to_product_rate(self, partner_id=None, limit=None, offset=None, include_total=False,
                                     start_date=None, end_date=None, store_ids=None, code=None, material_ids=None,
                                     product_ids=None, conversion_rate=None, branch_type=None):
        try:
            total_sql = """
            select count(*)
            from ({}) as tmp
            """
            select_sql = """
            select 
                item.from_material_id,
                item.from_material_code,
                item.from_material_name,
                item.to_material_id,
                item.to_material_code,
                item.to_material_name,
                receipt.process_date,
                receipt.process_store_id,
                receipt.process_store_code,
                receipt.process_store_name,
                item.theoretical_rate,
                item.actual_rate,
                receipt.code,
                material_detail.production_quantity as t_quantity,
                product_detail.production_quantity as a_quantity
            FROM supply_production_process_receipts_items item
            JOIN supply_production_process_receipts receipt on item.main_id = receipt.id
            JOIN supply_production_process_receipts_detail material_detail on material_detail.main_id = receipt.id 
            and item.from_material_id = material_detail.product_id 
            and material_detail.production_rule = item.production_rule and material_detail.type = 'materials'
            JOIN supply_production_process_receipts_detail product_detail on product_detail.main_id = receipt.id 
            and item.to_material_id = product_detail.product_id 
            and product_detail.production_rule = item.production_rule and product_detail.type = 'product'
            """
            where_sql = " where receipt.partner_id = {} AND receipt.status = 'CONFIRMED' ".format(partner_id)
            if start_date and start_date.year != 1970:
                where_sql += " AND receipt.process_date >= '{}' ".format(datetime2str(start_date))
            if end_date and end_date.year != 1970:
                where_sql += " AND receipt.process_date <= '{}' ".format(datetime2str(end_date))
            if store_ids:
                where_sql += " AND receipt.process_store_id in ({}) ".format(','.join([str(_id) for _id in store_ids]))
            if branch_type:
                where_sql += " AND receipt.type = '{}' ".format(branch_type)
            if code:
                where_sql += " AND receipt.code = '{}' ".format(code)
            if material_ids:
                where_sql += " AND item.from_material_id in ({}) ".format(','.join([str(_id) for _id in material_ids]))
            if product_ids:
                where_sql += " AND item.to_material_id in ({}) ".format(','.join([str(_id) for _id in product_ids]))
            if conversion_rate and conversion_rate != 'no_comparison':
                if conversion_rate == 'higher_than':
                    where_sql += " AND item.actual_rate > item.theoretical_rate "
                elif conversion_rate == 'lower_than':
                    where_sql += " AND item.actual_rate < item.theoretical_rate "
                else:
                    where_sql += " AND item.actual_rate = item.theoretical_rate "
            where_sql += " order by receipt.code desc "
            limit_sql = ""
            if limit and limit != -1:
                limit_sql += " limit {} ".format(limit)
            if offset:
                limit_sql += " offset {} ".format(offset)
            total_count_sql = total_sql.format(select_sql + where_sql)
            with DummyTransaction(auto_commit=False) as trans:
                rows = trans.scope_session.execute(text(select_sql + where_sql + limit_sql)).fetchall()
                count = trans.scope_session.execute(text(total_count_sql)).fetchall()
            total = count[0][0] if count else 0
            return_rows = []
            for r in rows:
                return_rows.append({
                    'material_id': r[0],
                    'material_code': r[1],
                    'material_name': r[2],
                    'product_id': r[3],
                    'product_code': r[4],
                    'product_name': r[5],
                    'process_date': get_timestamp(r[6]),
                    'store_id': r[7],
                    'store_code': r[8],
                    'store_name': r[9],
                    'theoretical_rate': r[10],
                    'actual_rate': r[11],
                    'code': r[12],
                    'material_quantity': r[13],
                    'product_quantity': r[14]
                })
            if include_total:
                return total, return_rows
            else:
                return return_rows
        except Exception as e:
            raise e
        finally:
            pass


production_process_receipts_db = ProductionProcessReceiptsDB()
