# -*- coding: utf-8 -*-
import logging
import traceback
import sys
from datetime import datetime
from .. import DeclarativeBase, TimestampMixin
from sqlalchemy import Column, BigInteger, String, Text
from supply.utils.helper import get_guid
from supply.driver.mysql import session_maker


class PeriodInitBatch(DeclarativeBase, TimestampMixin):
    """账期初始化创建批次控制表"""
    __tablename__ = "task_batch_period_init"

    class Status(object):
        INITED = "INITED"    # 新建
        SUCCESS = "SUCCESS"  # 成功
        FAILED = "FAILED"    # 失败

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 商户id
    process_status = Column(String, default=Status.INITED)  # 操作状态
    period_name = Column(String, unique=True)  # 账期名字，和partner_id做唯一key校验
    content = Column(Text)  # 请求主档fields

    @classmethod
    def add_one(cls, period):
        """添加一条记录"""
        db_session = session_maker()
        try:
            if isinstance(period, dict):
                db_session.bulk_insert_mappings(cls, [period])
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Add PeriodInitBatch Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                       traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            return False
        finally:
            db_session.close()

    @classmethod
    def update_period(cls, partner_id, update_data: dict):
        db_session = session_maker()
        try:
            update_db = db_session.query(cls).filter(
                cls.partner_id == partner_id).filter(
                cls.id == update_data.get('id')).with_lockmode('update').first()
            if update_db:
                update_db.updated_at = datetime.utcnow()
                if update_data.get('process_status'):
                    update_db.process_status = update_data.get('process_status')
                db_session.add(update_db)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Period Batch Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                       traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_period_batch(cls, partner_id, status: list):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if status and isinstance(status, list):
                query = query.filter(cls.process_status.in_(status))
            return query.all()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
