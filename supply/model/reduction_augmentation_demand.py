from collections import namedtuple

from sqlalchemy import Index, Column, DECIMAL, BigInteger, Integer, String, \
    Text, DateTime, Boolean, UniqueConstraint, func, desc, case, or_, and_, ForeignKey, JSON
from sqlalchemy.orm import relationship

from supply.error.exception import DataValidationException
from supply.model import DeclarativeBase, MethodMixin, TimestampMixin, AsDict
from ..utils.snowflake import get_id
from supply.driver.mysql import engine
from enum import Enum


class ReductionAugmentationDemand(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_reduction_augmentation_demand'

    class OrderByType(Enum):
        N_DAYS = 'N_DAYS'
        QUANTITY = 'QUANTITY'

    id = Column(BigInteger, default=get_id, primary_key=True)
    status = Column(String(50), default='INITED', nullable=False)
    company = Column(JSON, default=[])
    name = Column(String(50), default='', index=True, nullable=False)
    code = Column(String(50), index=True, nullable=False)
    start_demand_date = Column(DateTime)
    end_demand_date = Column(DateTime)
    rate = Column(DECIMAL(16, 3), nullable=False, default=0)
    lower_limit = Column(DECIMAL(16, 3))
    order_by_demand_day = Column(Integer, default=None)
    quantity_per_time = Column(String(50), nullable=False, default='')
    allow_reduce_to_zero = Column(Boolean, default=False, nullable=False)
    store_sum = Column(Integer, default=0, nullable=False)
    product_sum = Column(Integer, default=0, nullable=False)
    allocation_quantity = Column(DECIMAL(16, 6), default=0, nullable=False)
    order_types = Column(JSON, default=[], nullable=False)
    allocation_type = Column(String(50), nullable=False, default='')
    partner_id = Column(BigInteger)  # 商户id
    order_by_type = Column(String, default='')

    # order_type = relationship('ReductionAugmentationDemandOrderType', backref='rule', lazy='dynamic')
    product = relationship('ReductionAugmentationDemandProduct', backref='rule', lazy='dynamic')
    store = relationship('ReductionAugmentationDemandStore', backref='rule', lazy='dynamic')
    calculation = relationship('ReductionAugmentationDemandCalculateResult', backref='rule', lazy='dynamic')
    calculation_agg = relationship('ReductionAugmentationDemandCalculateProductAgg', backref='rule', lazy='dynamic')
    companies = relationship('ReductionAugmentationDemandCompany', backref='rule', lazy='dynamic')


# class ReductionAugmentationDemandOrderType(DeclarativeBase, TimestampMixin, AsDict):
#     __tablename__ = 'supply_reduction_augmentation_demand_order_type'
#     id = Column(BigInteger, default=get_id, primary_key=True)
#     order_type = Column(BigInteger, nullable=False)
#     rule_id = Column(BigInteger, ForeignKey(f'{ReductionAugmentationDemand.__tablename__}.id'),  index=True)


class ReductionAugmentationDemandProduct(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_reduction_augmentation_demand_product'
    id = Column(BigInteger, default=get_id, primary_key=True, nullable=False)
    product_id = Column(BigInteger, nullable=False)
    product_code = Column(String(50), nullable=False)
    rate = Column(DECIMAL(16, 3), nullable=False)
    allocation_quantity = Column(DECIMAL(16, 6), default=0, nullable=False)
    remain_quantity = Column(DECIMAL(16, 6), default=0, nullable=False)
    rule_id = Column(BigInteger, ForeignKey(f'{ReductionAugmentationDemand.__tablename__}.id'), index=True)


class ReductionAugmentationDemandStore(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_reduction_augmentation_demand_store'
    id = Column(BigInteger, default=get_id, primary_key=True)
    store_id = Column(BigInteger, nullable=False)
    store_code = Column(String(50), nullable=False)
    rule_id = Column(BigInteger, ForeignKey(f'{ReductionAugmentationDemand.__tablename__}.id'), index=True)


class ReductionAugmentationDemandCalculateResult(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_reduction_augmentation_demand_calculate_result'
    id = Column(BigInteger, default=get_id, primary_key=True)
    # times = Column(Integer, default=1)
    product_id = Column(BigInteger, nullable=False)
    product_code = Column(String(50), nullable=False)
    store_id = Column(BigInteger, nullable=False)
    store_code = Column(String(50), nullable=False)
    demand_quantity = Column(DECIMAL(18, 8))  # 原订货数量
    final_quantity = Column(DECIMAL(18, 8), default=0)  # 订单剩余数量
    order_type = Column(BigInteger)
    order_source = Column(String(50))
    order_code = Column(String(50))
    order_id = Column(BigInteger)
    step = Column(DECIMAL(18, 8))  # 步长， 最小订货量或递增订货量
    rate = Column(DECIMAL(18, 8))  # 分配上下限
    max_quantity = Column(DECIMAL(18, 8))  # 最大分配数量
    max_times = Column(Integer)  # 最大分配次数
    remain_quantity = Column(DECIMAL(18, 8))  # 取余
    allocation_quantity = Column(DECIMAL(18, 8))  # 实际分配数量
    allocation_times = Column(DECIMAL(18, 8))  # 实际分配次数
    order_product_id = Column(BigInteger)  # 订单商品id
    rule_id = Column(BigInteger, ForeignKey(f'{ReductionAugmentationDemand.__tablename__}.id'), index=True)
    has_effect = Column(Boolean, default=False)


class ReductionAugmentationDemandCalculateProductAgg(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_reduction_augmentation_demand_product_agg'
    id = Column(BigInteger, default=get_id, primary_key=True, nullable=False)
    product_id = Column(BigInteger, nullable=False)
    product_code = Column(String(50), nullable=False)
    rate = Column(DECIMAL(16, 3), nullable=False)
    allocation_quantity = Column(DECIMAL(16, 6), default=0, nullable=False)
    remain_quantity = Column(DECIMAL(16, 6), default=0, nullable=False)
    store_count = Column(Integer, default=0, nullable=False)
    rule_id = Column(BigInteger, ForeignKey(f'{ReductionAugmentationDemand.__tablename__}.id'), index=True)


class ReductionAugmentationDemandExcludedStore(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_reduction_augmentation_demand_excluded_store'
    id = Column(BigInteger, default=get_id, primary_key=True)
    store_id = Column(BigInteger, nullable=False)
    store_code = Column(String(50), nullable=False)
    allocation_type = Column(String(50), nullable=False)
    partner_id = Column(BigInteger)  # 商户id


class ReductionAugmentationDemandCompany(DeclarativeBase, AsDict):
    __tablename__ = 'supply_reduction_augmentation_demand_company'
    id = Column(BigInteger, default=get_id, primary_key=True)
    company_id = Column(BigInteger, nullable=False)
    rule_id = Column(BigInteger, ForeignKey(f'{ReductionAugmentationDemand.__tablename__}.id'),  index=True)


if __name__ == '__main__':
    DeclarativeBase.Metadata.create_all(engine, tables=[ReductionAugmentationDemand, ReductionAugmentationDemandStore, ReductionAugmentationDemandProduct])