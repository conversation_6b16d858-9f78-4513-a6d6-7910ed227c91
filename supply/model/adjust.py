# -*- coding: utf-8 -*-
from sqlalchemy import text, func, or_, and_, desc

from ..driver.mq import mq_producer
from ..driver.mysql import DummyTransaction
from ..driver.mysql import session_maker
from datetime import datetime, timedelta
import logging, sys, traceback
from . import DeclarativeBase, TimestampMixin, MethodMixin
from sqlalchemy import (Column, BigInteger, Integer, String, Boolean, DateTime, DECIMAL, Text)
from ..module import adjust_service
from sqlalchemy.orm.exc import NoResultFound
from supply.utils.helper import get_guid, set_db, set_model_from_db, convert_to_int, convert_to_decimal, get_branch_map, \
    get_product_map, MessageTopic, get_supply_reason_map
import json
from ..utils.adjust_enum import REASON_TYPE, AUTO_REASON_TYPE
from sqlalchemy import exc
from supply.client.cost_service import cost_service
from supply.utils.time import get_timestamp


class AsDict(object):
    def as_dict(self, mapping_args=None):
        if mapping_args:
            dict_data = {}
            for c in self.__table__.columns:
                mapping_name = mapping_args.get(c.name)
                if mapping_name:
                    dict_data[mapping_name] = getattr(self, mapping_name)
        else:
            dict_data = {c.name: getattr(self, c.name) for c in self.__table__.columns}
        if "extends" in dict_data and dict_data['extends'] and isinstance(dict_data['extends'], String):
            # load string to json
            try:
                dict_data['extends'] = json.loads(dict_data['extends'])
            except Exception as e:
                dict_data['extends'] = dict(error=e)
        return dict_data


class AdjustDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_adjust'
    id = Column(BigInteger, primary_key=True, default=get_guid)
    branch_batch_id = Column(BigInteger)
    schedule_id = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    adjust_store = Column(BigInteger)
    code = Column(String(50))
    reason_type = Column(String(25))
    adjust_date = Column(DateTime)
    status = Column(String(10))
    process_status = Column(String(10))
    remark = Column(String(255))
    request_id = Column(BigInteger)
    receive_id = Column(BigInteger)
    receive_code = Column(String(50))
    branch_type = Column(String(30))
    source = Column(String(30))     # 单据来源


class AdjustDetailsDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_adjust_detail'
    id = Column(BigInteger, primary_key=True, default=get_guid)
    branch_batch_id = Column(BigInteger)
    branch_type = Column(String(10))  # 区分仓库还是门店 STORE, WAREHOUSE
    schedule_id = Column(BigInteger)
    adjust_id = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    adjust_order_number = Column(BigInteger)
    adjust_store = Column(BigInteger)
    adjust_store_secondary_id = Column(String(50))
    reason_type = Column(String(25))
    code = Column(String(50))
    schedule_code = Column(String(50))
    adjust_date = Column(DateTime)
    status = Column(String(10))
    process_status = Column(String(10))
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    remark = Column(String(255))
    request_id = Column(BigInteger)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))
    receive_id = Column(BigInteger)
    receive_code = Column(String(50))
    schedule_name = Column(String(50))
    source = Column(String(30))     # 单据来源
    reject_reason = Column(String(255))  # 驳回原因
    attachments = Column(Text)       # 附件 "{"attachments": [ats1, ats2,...]}"
    total_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0) # 含税金额总数
    total_sales_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0) # 含税零售金额总数

class AdjustProductDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = "supply_adjust_product"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    adjust_id = Column(BigInteger)
    product_id = Column(BigInteger)
    product_code = Column(String(50))
    product_name = Column(String(50))
    unit_id = Column(BigInteger)
    unit_name = Column(String(50))
    unit_spec = Column(String(50))
    accounting_unit_id = Column(BigInteger)
    accounting_unit_name = Column(String(50))
    accounting_unit_spec = Column(String(50))
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    accounting_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    confirmed_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    is_confirmed = Column(Boolean, default=False)
    item_number = Column(Integer)
    material_number = Column(String(25))
    adjust_store = Column(BigInteger)
    adjust_date = Column(DateTime)
    reason_type = Column(String(25))
    convert_accounting_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    is_bom = Column(Boolean, default=False)
    position_id = Column(BigInteger)
    sku_remark = Column(Text)
    tax_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)   # 报废单创建时商品的税率
    tax_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 报废单创建时商品的含税单价(从价格中心取)
    cost_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0) # 成本价
    amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)     # 含税金额=含税单价*数量
    tax_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0) # 税额=含税金额/(1+税率)*税率
    sales_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0) # 零售价
    sales_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0) # 零售金额

class AdjustLogDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = "supply_adjust_log"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    adjust_id = Column(BigInteger)
    adjust_status = Column(String(10))
    reason = Column(String(255))
    created_by = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)


class AdjustBomReportBatchDB(DeclarativeBase, TimestampMixin, MethodMixin):
    __tablename__ = "supply_adjust_bom_report_batch"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    adjust_id = Column(BigInteger, index=True, unique=True)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)


class AdjustBomReportDB(DeclarativeBase, TimestampMixin, MethodMixin):
    __tablename__ = "supply_adjust_bom_report"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    adjust_id = Column(BigInteger)
    batch_id = Column(BigInteger)
    store_id = Column(BigInteger, index=True)
    product_id = Column(BigInteger, index=True)
    # 单位信息
    unit_id = Column(BigInteger)
    unit_name = Column(String(50))
    unit_spec = Column(String(50))
    accounting_unit_id = Column(BigInteger)
    accounting_unit_name = Column(String(50))
    accounting_unit_spec = Column(String(50))

    bom_product_id = Column(BigInteger, index=True)

    # 单位信息
    bom_unit_id = Column(BigInteger)
    bom_unit_name = Column(String(50))
    bom_unit_spec = Column(String(50))
    bom_accounting_unit_id = Column(BigInteger)
    bom_accounting_unit_name = Column(String(50))
    bom_accounting_unit_spec = Column(String(50))

    uom_id = Column(BigInteger, index=True)
    product_qty = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    accounting_product_qty = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    reason_type = Column(String(25))

    qty = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    accounting_qty = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)

    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)
    bom_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)

    line_id = Column(BigInteger)
    line_m_qty = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    line_m_accounting_qty = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    position_id = Column(BigInteger, index=True)    # 仓位id


class AdjustRepository(object):

    def get_adjust_by_request_id(self, request_id):
        db_session = session_maker()
        try:
            adjust_db = db_session.query(AdjustDB).filter(AdjustDB.request_id == request_id).first()
            if adjust_db:
                adjust = adjust_service.Adjust()
                set_model_from_db(adjust_db.as_dict(), adjust)
                return adjust
        except NoResultFound as e:
            pass
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            raise e
        finally:
            db_session.close()
        return None

    def get_adjust_by_adjust_id(self, adjust_id, partner_id, is_detail=False):
        db_session = session_maker()
        try:
            adjust_db = db_session.query(AdjustDB).filter(AdjustDB.id == adjust_id,
                                                          AdjustDB.partner_id == partner_id).first()
            if is_detail is True:
                adjust_detail_db = db_session.query(AdjustDetailsDB).filter(AdjustDetailsDB.adjust_id == adjust_id,
                                                                            AdjustDetailsDB.partner_id == partner_id).first()
                if adjust_detail_db:
                    adjust_detail = adjust_service.AdjustDetails()
                    set_model_from_db(adjust_detail_db.as_dict(), adjust_detail)
                    return adjust_detail
            if adjust_db:
                adjust = adjust_service.Adjust()
                set_model_from_db(adjust_db.as_dict(), adjust)
                return adjust
        except NoResultFound as e:
            pass
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            raise e
        finally:
            db_session.close()
        return None

    def create_update_adjust(self, adjust_detail_obj, list_adjust_products, user_id=None, username=None, is_auto=False,
                             partner_id=None):
        db_session = session_maker()
        try:
            if not adjust_detail_obj.adjust_id:
                adjust_db = AdjustDB()
                adjust_detail_db = AdjustDetailsDB()
                set_db(adjust_db, adjust_detail_obj.no_timestamp_props())
                set_db(adjust_detail_db, adjust_detail_obj.no_timestamp_props())
                adjust_db.id = get_guid()
                adjust_db.created_at = datetime.now()
                adjust_detail_db.id = get_guid()
                adjust_detail_db.adjust_id = adjust_db.id
                adjust_detail_db.created_at = datetime.now()

                adjust_log_db = AdjustLogDB()
                adjust_log_db.id = get_guid()
                adjust_log_db.adjust_id = adjust_db.id
                adjust_log_db.adjust_status = 'INITED'
                adjust_log_db.created = datetime.utcnow()
                adjust_log_db.partner_id = adjust_db.partner_id
                if user_id:
                    adjust_detail_db.created_by = adjust_detail_db.updated_by = user_id
                    adjust_detail_db.created_name = adjust_detail_db.updated_name = username
                    adjust_log_db.created_by = user_id
                db_session.add(adjust_db)
                db_session.add(adjust_detail_db)
                db_session.add(adjust_log_db)
                if list_adjust_products and isinstance(list_adjust_products, list) and len(list_adjust_products) > 0:
                    i = 0
                    for product_obj in list_adjust_products:
                        i = i + 1
                        product_db = AdjustProductDB()
                        set_db(product_db, product_obj.no_timestamp_props())
                        product_db.id = get_guid()
                        product_db.is_confirmed = False
                        product_db.created_at = datetime.now()
                        product_db.adjust_id = adjust_db.id
                        product_db.adjust_store = adjust_detail_db.adjust_store
                        product_db.adjust_date = adjust_detail_db.adjust_date
                        if user_id:
                            product_db.created_by = user_id
                            product_db.created_name = username
                        if is_auto:
                            product_db.reason_type = AUTO_REASON_TYPE
                        product_db.item_number = i
                        db_session.add(product_db)
            else:
                adjust_db = db_session.query(AdjustDB).filter(AdjustDB.id == adjust_detail_obj.adjust_id,
                                                              AdjustDB.partner_id == partner_id).with_lockmode(
                    "update").first()
                adjust_detail_db = db_session.query(AdjustDetailsDB).filter(
                    AdjustDetailsDB.adjust_id == adjust_detail_obj.adjust_id,
                    AdjustDetailsDB.partner_id == partner_id).with_lockmode("update").first()
                list_adjust_product = db_session.query(AdjustProductDB).filter(
                    AdjustProductDB.adjust_id == adjust_detail_obj.adjust_id,
                    AdjustProductDB.partner_id == partner_id).with_lockmode("update").all()
                existed_ids = [p.id for p in list_adjust_product]
                # set_db(adjust_db, adjust_detail_obj.props)
                # set_db(adjust_detail_db, adjust_detail_obj.props)
                if adjust_detail_obj.reason_type:
                    adjust_db.reason_type = adjust_detail_obj.reason_type
                    adjust_detail_db.reason_type = adjust_detail_obj.reason_type
                if adjust_detail_obj.remark:
                    adjust_db.remark = adjust_detail_obj.remark
                    adjust_detail_db.remark = adjust_detail_obj.remark
                if adjust_detail_obj.adjust_date:
                    adjust_db.adjust_date = adjust_detail_obj.adjust_date
                    adjust_detail_db.adjust_date = adjust_detail_obj.adjust_date
                    adjust_detail_db.adjust_date = adjust_detail_obj.adjust_date
                if adjust_detail_obj.attachments:
                    adjust_detail_db.attachments = adjust_detail_obj.attachments
                adjust_db.updated_at = datetime.now()
                adjust_detail_db.updated_at = datetime.now()
                if user_id:
                    adjust_detail_db.updated_by = user_id
                    adjust_detail_db.updated_name = username
                max_item_number = 0
                if list_adjust_products and isinstance(list_adjust_products, list) and len(list_adjust_products) == 0:
                    list_adjust_products = []
                if list_adjust_product and isinstance(list_adjust_product, list) and len(list_adjust_product) == 0:
                    list_adjust_product = []
                for product_obj in list_adjust_products:
                    for product_db in list_adjust_product:
                        if product_obj.product_id == product_db.product_id:
                            if product_db.item_number and product_db.item_number > max_item_number:
                                max_item_number = product_db.item_number
                logging.info("list_adjust_products: {}".format(list_adjust_products))
                input_ids = []
                for product_obj in list_adjust_products:

                    is_exsit = False
                    if product_obj.id:
                        input_ids.append(product_obj.id)
                        for product_db in list_adjust_product:
                            if product_obj.id == product_db.id:
                                is_exsit = True
                                set_db(product_db, product_obj.no_timestamp_props())
                                # product_db.quantity = product_obj.quantity
                                # product_db.accounting_quantity = product_obj.accounting_quantity
                                product_db.updated_at = datetime.utcnow()
                                if user_id:
                                    product_db.updated_by = user_id
                                    product_db.updated_name = username
                    if not is_exsit:
                        max_item_number = max_item_number + 1
                        product_db = AdjustProductDB()
                        set_db(product_db, product_obj.no_timestamp_props())
                        product_db.id = get_guid()
                        product_db.is_confirmed = False
                        product_db.adjust_id = adjust_db.id
                        product_db.created_at = datetime.now()
                        product_db.item_number = max_item_number
                        product_db.reason_type = product_obj.reason_type
                        product_db.adjust_store = adjust_detail_db.adjust_store
                        product_db.adjust_date = adjust_detail_db.adjust_date
                        if user_id:
                            product_db.created_by = user_id
                            product_db.created_name = username
                        db_session.add(product_db)
                for id in existed_ids:
                    if id not in input_ids:
                        db_session.query(AdjustProductDB).filter(AdjustProductDB.id == id).delete(
                            synchronize_session=False)
            db_session.commit()
            adjust_obj = adjust_service.AdjustDetails()
            set_model_from_db(adjust_detail_db.as_dict(), adjust_obj)
            return adjust_obj
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[{}] error. Error message:{} - {}**".format('create_store_adjust', e, traceback.format_exc()))
            raise e
        finally:
            db_session.close()

    def update_adjust_detail(self, adjust_detail_obj, user_id=None, username=None, partner_id=None):
        db_session = session_maker()
        try:
            adjust_db = db_session.query(AdjustDB).filter(AdjustDB.id == adjust_detail_obj.adjust_id,
                                                          AdjustDB.partner_id == partner_id).with_lockmode(
                "update").first()
            adjust_detail_db = db_session.query(AdjustDetailsDB).filter(
                AdjustDetailsDB.adjust_id == adjust_detail_obj.adjust_id,
                AdjustDetailsDB.partner_id == partner_id).with_lockmode("update").first()
            list_adjust_product = db_session.query(AdjustProductDB).filter(
                AdjustProductDB.adjust_id == adjust_detail_obj.adjust_id,
                AdjustProductDB.partner_id == partner_id).with_lockmode("update").all()
            existed_ids = [p.id for p in list_adjust_product]
            # set_db(adjust_db, adjust_detail_obj.props)
            # set_db(adjust_detail_db, adjust_detail_obj.props)
            if adjust_detail_obj.reason_type:
                adjust_db.reason_type = adjust_detail_obj.reason_type
                adjust_detail_db.reason_type = adjust_detail_obj.reason_type
            if adjust_detail_obj.remark:
                adjust_db.remark = adjust_detail_obj.remark
                adjust_detail_db.remark = adjust_detail_obj.remark
            if adjust_detail_obj.adjust_date:
                adjust_db.adjust_date = adjust_detail_obj.adjust_date
                adjust_detail_db.adjust_date = adjust_detail_obj.adjust_date
            if adjust_detail_obj.attachments:
                adjust_detail_db.attachments = adjust_detail_obj.attachments
            adjust_db.updated_at = datetime.now()
            adjust_detail_db.updated_at = datetime.now()
            if user_id:
                adjust_detail_db.updated_by = user_id
                adjust_detail_db.updated_name = username
            db_session.commit()
            adjust_obj = adjust_service.AdjustDetails()
            set_model_from_db(adjust_detail_db.as_dict(), adjust_obj)
            return adjust_obj
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[{}] error. Error message:%{} - {} **".format('create_store_adjust', e, traceback.format_exc()))
            raise e
        finally:
            db_session.close()

    def list_store_adjust(self, partner_id=None, store_ids=None, status=None, reason_type=None, start_date=None,
                          end_date=None, limit=None, offset=None, include_total=False, ids=None, code=None, order=None,
                          sort=None, branch_type=None, sources=None, product_ids=None):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustDetailsDB)
            if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                query = query.outerjoin(AdjustProductDB, AdjustDetailsDB.adjust_id == AdjustProductDB.adjust_id)
                query = query.filter(AdjustProductDB.product_id.in_(product_ids))
            if partner_id:
                query = query.filter(AdjustDetailsDB.partner_id == partner_id)
            if branch_type:
                query = query.filter(AdjustDetailsDB.branch_type == branch_type)
            if code:
                query = query.filter(AdjustDetailsDB.code == code)
            if store_ids and isinstance(store_ids, list) and len(store_ids) > 0:
                query = query.filter(AdjustDetailsDB.adjust_store.in_(store_ids))
            if ids and isinstance(ids, list) and len(ids) > 0:
                query = query.filter(AdjustDetailsDB.adjust_id.in_(ids))
            if status and isinstance(status, str):
                query = query.filter(AdjustDetailsDB.status == status)
            elif status and isinstance(status, list) and len(status) > 0:
                query = query.filter(AdjustDetailsDB.status.in_(status))
            if sources and isinstance(sources, list):
                query = query.filter(AdjustDetailsDB.source.in_(sources))
            elif sources and isinstance(sources, str):
                query = query.filter(AdjustDetailsDB.source == sources)
            if reason_type:
                query = query.filter(AdjustDetailsDB.reason_type == reason_type)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(AdjustDetailsDB.adjust_date >= start_date)
            if end_date and isinstance(end_date, datetime) and end_date.year != 1970:
                query = query.filter(AdjustDetailsDB.adjust_date <= end_date)
            if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                query = query.group_by(AdjustDetailsDB.adjust_id)
            if not sort:
                sort = "updated_at"
            if order != 'asc':
                order_sort = 'AdjustDetailsDB.{}.desc()'.format(sort)
                query = query.order_by(eval(order_sort))
            else:
                order_sort = 'AdjustDetailsDB.{}'.format(sort)
                query = query.order_by(eval(order_sort))
            # query = query.order_by(AdjustDetailsDB.updated_at.desc())
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            list_adjust_obj = []
            list_adjust_db = query.all()
            if list_adjust_db and isinstance(list_adjust_db, list) and len(list_adjust_db) > 0:
                for adjust_db in list_adjust_db:
                    adjust_obj = adjust_service.AdjustDetails()
                    set_model_from_db(adjust_db.as_dict(), adjust_obj)
                    adjust_obj.id = adjust_obj.adjust_id
                    del adjust_obj.adjust_id
                    list_adjust_obj.append(adjust_obj.props())
            if include_total:
                query_total = db_session.query(AdjustDetailsDB.id)
                if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                    query_total = query_total.outerjoin(AdjustProductDB, AdjustDetailsDB.adjust_id ==
                                                        AdjustProductDB.adjust_id)
                    query_total = query_total.filter(AdjustProductDB.product_id.in_(product_ids))
                if partner_id:
                    query_total = query_total.filter(AdjustDetailsDB.partner_id == partner_id)
                if branch_type:
                    query_total = query_total.filter(AdjustDetailsDB.branch_type == branch_type)
                if ids and isinstance(ids, list) and len(ids) > 0:
                    query_total = query_total.filter(AdjustDetailsDB.adjust_id.in_(ids))
                if code:
                    query_total = query_total.filter(AdjustDetailsDB.code == code)
                if store_ids and isinstance(store_ids, list) and len(store_ids) > 0:
                    query_total = query_total.filter(AdjustDetailsDB.adjust_store.in_(store_ids))
                if start_date:
                    query_total = query_total.filter(AdjustDetailsDB.adjust_date >= start_date)
                if end_date:
                    query_total = query_total.filter(AdjustDetailsDB.adjust_date <= end_date)
                if status and isinstance(status, str):
                    query_total = query_total.filter(AdjustDetailsDB.status == status)
                elif status and isinstance(status, list) and len(status) > 0:
                    query_total = query_total.filter(AdjustDetailsDB.status.in_(status))
                if sources and isinstance(sources, list):
                    query_total = query_total.filter(AdjustDetailsDB.source.in_(sources))
                elif sources and isinstance(sources, str):
                    query_total = query_total.filter(AdjustDetailsDB.source == sources)
                if reason_type:
                    query_total = query_total.filter(AdjustDetailsDB.reason_type == reason_type)
                if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                    query_total = query_total.group_by(AdjustDetailsDB.adjust_id)
                count = query_total.count()
                return count, list_adjust_obj
            return list_adjust_obj
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('list_store_adjust', e))
            raise e
        finally:
            db_session.close()

    def list_store_adjust_product(self, adjust_id, product_ids=None, limit=None, offset=None, include_total=False,
                                  partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustProductDB).filter(AdjustProductDB.adjust_id == adjust_id,
                                                             AdjustProductDB.partner_id == partner_id)
            query = query.order_by(AdjustProductDB.updated_at.desc())
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            list_products = []
            list_products_db = query.all()
            if list_products_db and isinstance(list_products_db, list) and len(list_products_db) > 0:
                for products_db in list_products_db:
                    products_obj = adjust_service.AdjustProduct()
                    set_model_from_db(products_db.as_dict(), products_obj)
                    list_products.append(products_obj.props())
            if include_total:
                query_total = db_session.query(func.count(AdjustProductDB.id))
                query_total = query_total.filter(AdjustProductDB.adjust_id == adjust_id,
                                                 AdjustProductDB.partner_id == partner_id)
                if product_ids and isinstance(product_ids, list) and len(product_ids) > 0:
                    query_total = query_total.filter(AdjustProductDB.product_id.in_(product_ids))
                count = query_total.scalar()
                return count, list_products
            return list_products
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('list_store_adjust_product', e))
            raise e
        finally:
            db_session.close()

    def query_product_name_by_adjust_ids(self, adjust_ids=None, partner_id=None, limit=None):
        """根据单据id查询商品名称，给移动端用"""
        db_session = session_maker()
        try:
            query = db_session.query(AdjustProductDB.adjust_id,
                                     AdjustProductDB.product_name).filter(
                AdjustProductDB.adjust_id.in_(adjust_ids),
                AdjustProductDB.partner_id == partner_id)
            if limit:
                query = query.limit(limit)
            return query.all()
        except Exception as e:
            logging.error(
                "** method[%s] error. Error message:%s **" % ('query_product_name_by_adjust_ids', e))
            raise e
        finally:
            db_session.close()

    def query_adjust_product_sum_qty(self, adjust_ids=None, partner_id=None,):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustProductDB.adjust_id, func.sum(AdjustProductDB.quantity)).filter(
                AdjustProductDB.partner_id == partner_id)
            if adjust_ids and isinstance(adjust_ids, list):
                query = query.filter(AdjustProductDB.adjust_id.in_(adjust_ids))
            query = query.group_by(AdjustProductDB.adjust_id)
            return query.all()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def recalc_adjust_detail_total_amount(self, adjust_id=None):
        """报废单商品数量发生变化时, 重新计算报废单supply_adjust_detail中的total_amount汇总金额(含税)"""
        if type(adjust_id) is int:
            with DummyTransaction(auto_commit=False) as trans:
                sql_str = '''
                UPDATE supply_adjust_product
                SET amount=IFNULL(tax_price,0) * IFNULL(accounting_quantity,0),sales_amount=IFNULL(sales_price,0) * IFNULL(accounting_quantity,0)
                WHERE adjust_id={};
                '''.format(int(adjust_id))
                trans.scope_session.execute(text(sql_str))
                sql_str = '''
                UPDATE supply_adjust_detail
                SET total_amount=IFNULL((
                    select SUM(IFNULL(amount,0))
                    FROM supply_adjust_product
                    WHERE adjust_id=supply_adjust_detail.adjust_id),0),
                total_sales_amount=IFNULL((
                    select SUM(IFNULL(sales_amount,0))
                    FROM supply_adjust_product
                    WHERE adjust_id=supply_adjust_detail.adjust_id),0)
                WHERE adjust_id={};
                '''.format(int(adjust_id))
                trans.scope_session.execute(text(sql_str))
                trans.scope_session.commit()

    def store_adjust_by_id(self, adjust_id, is_detail=False, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustDetailsDB).filter(AdjustDetailsDB.partner_id == partner_id)
            if adjust_id:
                query = query.filter(AdjustDetailsDB.adjust_id == adjust_id)
            adjust_db = query.first()
            if adjust_db:
                adjust_obj = adjust_service.AdjustDetails()
                set_model_from_db(adjust_db.as_dict(), adjust_obj)
                return adjust_obj
            return None
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('store_adjust_by_id', e))
            raise e
        finally:
            db_session.close()

    def store_adjust_by_ids(self, adjust_ids, partner_id=None):
        db_session = session_maker()
        try:
            adjust_objs = []
            if not adjust_ids or not isinstance(adjust_ids, list) or len(adjust_ids) == 0:
                return adjust_objs
            query = db_session.query(AdjustDetailsDB).filter(AdjustDetailsDB.partner_id == partner_id)
            query = query.filter(AdjustDetailsDB.adjust_id.in_(adjust_ids))
            adjust_dbs = query.all()
            if len(adjust_dbs) > 0:
                for adjust_db in adjust_dbs:
                    if adjust_db:
                        adjust_obj = adjust_service.AdjustDetails()
                        set_model_from_db(adjust_db.as_dict(), adjust_obj)
                        adjust_objs.append(adjust_obj)
            return adjust_objs
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('store_adjust_by_ids', e))
            raise e
        finally:
            db_session.close()

    def store_adjust_product_by_id(self, adjust_product_id, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustProductDB).filter(AdjustProductDB.partner_id == partner_id)
            if adjust_product_id:
                query = query.filter(AdjustProductDB.id == adjust_product_id)
            adjust_db = query.first()
            if adjust_db:
                adjust_obj = adjust_service.AdjustProduct()
                set_model_from_db(adjust_db.as_dict(), adjust_obj)
                return adjust_obj
            return None
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('store_adjust_by_id', e))
            raise e
        finally:
            db_session.close()

    def confirm_adjust_status(self, adjust_id, user_id=None, username=None, bom_products_map=None,
                              is_bom_products_list=None, obj=None, partner_id=None):
        db_session = session_maker()
        try:
            adjust_db = db_session.query(AdjustDB).filter(AdjustDB.id == adjust_id,
                                                          AdjustDB.partner_id == partner_id).with_lockmode(
                "update").first()
            if not adjust_db:
                return None
            adjust_detail_db = db_session.query(AdjustDetailsDB).filter(
                AdjustDetailsDB.adjust_id == adjust_id,
                AdjustDetailsDB.partner_id == partner_id).with_lockmode("update").first()
            list_adjust_product = db_session.query(AdjustProductDB).filter(
                AdjustProductDB.adjust_id == adjust_id,
                AdjustProductDB.partner_id == partner_id).with_lockmode("update").all()

            adjust_db.status = 'CONFIRMED'
            adjust_db.process_status = 'CONFIRMED'
            adjust_detail_db.status = 'CONFIRMED'
            adjust_detail_db.process_status = 'CONFIRMED'
            adjust_db.updated_at = datetime.now()
            adjust_detail_db.updated_at = datetime.now()

            if user_id:
                adjust_db.updated_by = user_id
                adjust_detail_db.updated_by = user_id
                adjust_detail_db.updated_name = username
            if list_adjust_product and isinstance(list_adjust_product, list) and len(list_adjust_product) > 0:
                for product_db in list_adjust_product:
                    if product_db.product_id in is_bom_products_list:
                        product_db.is_bom = True
                    else:
                        product_db.is_bom = False
                        key = str(product_db.position_id) + ',' + str(product_db.product_id)
                        if bom_products_map.get(key, 0):
                            product_db.convert_accounting_quantity = convert_to_decimal(
                                product_db.accounting_quantity) + \
                                                                     convert_to_decimal(bom_products_map[key])
                        else:
                            product_db.convert_accounting_quantity = product_db.accounting_quantity
                    product_db.is_confirmed = True
                    product_db.confirmed_quantity = product_db.accounting_quantity
                    product_db.updated = datetime.now()
                    if user_id:
                        product_db.updated_by = user_id
                    db_session.add(product_db)

            adjust_log_db = AdjustLogDB()
            adjust_log_db.id = get_guid()
            adjust_log_db.adjust_id = adjust_id
            adjust_log_db.adjust_status = 'CONFIRMED'
            # adjust_log_db.reason = obj
            if user_id:
                adjust_log_db.created_by = user_id
            adjust_log_db.created = datetime.now()
            adjust_log_db.partner_id = adjust_db.partner_id
            db_session.add(adjust_log_db)
            db_session.commit()
            adjust_detail_obj = adjust_service.AdjustDetails()
            set_model_from_db(adjust_detail_db.as_dict(), adjust_detail_obj)
            return adjust_detail_obj
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('create_store_adjust', e))
            raise e
        finally:
            db_session.close()

    def approve_adjust(self, adjust_id, user_id=None, username=None, bom_products_map=None,
                       is_bom_products_list=None, partner_id=None):
        db_session = session_maker()
        try:
            adjust_db = db_session.query(AdjustDB).filter(AdjustDB.id == adjust_id,
                                                          AdjustDB.partner_id == partner_id).with_lockmode(
                "update").first()
            if not adjust_db:
                return None
            adjust_detail_db = db_session.query(AdjustDetailsDB).filter(
                AdjustDetailsDB.adjust_id == adjust_id,
                AdjustDetailsDB.partner_id == partner_id).with_lockmode("update").first()
            list_adjust_product = db_session.query(AdjustProductDB).filter(
                AdjustProductDB.adjust_id == adjust_id,
                AdjustProductDB.partner_id == partner_id).with_lockmode("update").all()

            adjust_db.status = 'APPROVED'
            adjust_db.process_status = 'APPROVED'
            adjust_detail_db.status = 'APPROVED'
            adjust_detail_db.process_status = 'APPROVED'
            adjust_db.updated_at = datetime.now()
            adjust_detail_db.updated_at = datetime.now()

            if user_id:
                adjust_db.updated_by = user_id
                adjust_detail_db.updated_by = user_id
                adjust_detail_db.updated_name = username
            if list_adjust_product and isinstance(list_adjust_product, list) and len(list_adjust_product) > 0:
                for product_db in list_adjust_product:
                    if product_db.product_id in is_bom_products_list:
                        product_db.is_bom = True
                    else:
                        product_db.is_bom = False
                        key = str(product_db.position_id) + ',' + str(product_db.product_id)
                        if bom_products_map.get(key, 0):
                            product_db.convert_accounting_quantity = convert_to_decimal(
                                product_db.accounting_quantity) + \
                                                                     convert_to_decimal(bom_products_map[key])
                        else:
                            product_db.convert_accounting_quantity = product_db.accounting_quantity
                    product_db.is_confirmed = True
                    product_db.confirmed_quantity = product_db.accounting_quantity
                    product_db.updated = datetime.now()
                    if user_id:
                        product_db.updated_by = user_id
                    db_session.add(product_db)

            adjust_log_db = AdjustLogDB()
            adjust_log_db.id = get_guid()
            adjust_log_db.adjust_id = adjust_id
            adjust_log_db.adjust_status = 'APPROVED'
            if user_id:
                adjust_log_db.created_by = user_id
            adjust_log_db.created = datetime.now()
            adjust_log_db.partner_id = adjust_db.partner_id
            db_session.add(adjust_log_db)
            db_session.commit()
            adjust_detail_obj = adjust_service.AdjustDetails()
            set_model_from_db(adjust_detail_db.as_dict(), adjust_detail_obj)
            return adjust_detail_obj
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('create_store_adjust', e))
            raise e
        finally:
            db_session.close()

    def update_adjust_status(self, adjust_id, allow_status, status, user_id=None, partner_id=None, username=None,
                             reason=None):
        db_session = session_maker()
        try:
            q = db_session.query(AdjustDB).filter(AdjustDB.id == adjust_id,
                                                  AdjustDB.partner_id == partner_id)
            if isinstance(allow_status, list) and len(allow_status) > 0:
                q = q.filter(AdjustDB.status.in_(allow_status))
            doc_db = q.with_lockmode("update").first()
            if doc_db:
                doc_db.status = status
                doc_db.process_status = status
                doc_db.updated_at = datetime.utcnow()
                if user_id:
                    doc_db.updated_by = user_id
                if username:
                    doc_db.updated_name = username
                doc_obj = adjust_service.Adjust()
                set_model_from_db(doc_db.as_dict(), doc_obj)
                doc_detail_update = {AdjustDetailsDB.status: status, AdjustDetailsDB.process_status: status,
                                     AdjustDetailsDB.updated_at: datetime.utcnow()}
                if user_id:
                    doc_detail_update.update({AdjustDetailsDB.updated_by: user_id})
                if username:
                    doc_detail_update.update({AdjustDetailsDB.updated_name: username})
                if reason:
                    doc_detail_update.update({AdjustDetailsDB.reject_reason: reason})
                db_session.query(AdjustDetailsDB).filter(AdjustDetailsDB.adjust_id == adjust_id,
                                                         AdjustDetailsDB.partner_id == partner_id).update(
                    doc_detail_update)
                adjust_log_db = AdjustLogDB()
                adjust_log_db.id = get_guid()
                adjust_log_db.adjust_id = adjust_id
                adjust_log_db.partner_id = doc_db.partner_id
                adjust_log_db.adjust_status = status
                adjust_log_db.created_at = datetime.utcnow()
                if user_id:
                    adjust_log_db.created_by = user_id
                if reason:
                    adjust_log_db.reason = reason
                db_session.add(adjust_log_db)
                db_session.commit()
                return doc_obj
            else:
                return None
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def delete_adjust(self, adjust_id, partner_id=None):
        db_session = session_maker()
        try:
            if adjust_id:
                db_session.query(AdjustProductDB).filter(AdjustProductDB.adjust_id == adjust_id,
                                                         AdjustProductDB.partner_id == partner_id).delete(
                    synchronize_session=False)
                db_session.query(AdjustDetailsDB).filter(AdjustDetailsDB.adjust_id == adjust_id,
                                                         AdjustDetailsDB.partner_id == partner_id).delete(
                    synchronize_session=False)
                db_session.query(AdjustDB).filter(AdjustDB.id == adjust_id,
                                                  AdjustDB.partner_id == partner_id).delete(synchronize_session=False)
                db_session.commit()
                return True
            return False
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('delete_adjust', e))
            raise e
        finally:
            db_session.close()

    def delete_adjust_product(self, ids=None, partner_id=None):
        db_session = session_maker()
        try:
            if ids:
                db_session.query(AdjustProductDB).filter(AdjustProductDB.id.in_(ids),
                                                         AdjustProductDB.partner_id == partner_id).delete(
                    synchronize_session=False)
                db_session.commit()
                return True
            return False
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('delete_adjust', e))
            raise e
        finally:
            db_session.close()

    def bi_get_adjust_collect(self, partner_id, user_id=None, product_ids=None, bom_product_id=None, start_date=None,
                              end_date=None, branch_type=None, limit=None, offset=None, include_total=False, store_ids=None,
                              period_symbol=None, reason_type=None, order=None, sort=None, hour_offset=0):

        _symbol = '%Y-%m-%d'
        if period_symbol=='BY_DAY':
            _symbol = '%Y-%m-%d'
        elif period_symbol=='BY_MONTH':
            _symbol = '%Y-%m'
        if sort == 'accounting_quantity':
            sort = 'accounting_product_qty'
        if sort == 'quantity':
            sort = 'product_qty'
        select_sql = """
            SELECT 
              ad.adjust_store,
              adp.product_id,
              SUM(adp.line_m_qty) AS product_qty,

              adp.unit_id,
              adp.unit_name,
              adp.accounting_unit_id,
              adp.accounting_unit_name,
              SUM(adp.line_m_accounting_qty) AS accounting_product_qty,
              ad.reason_type,

              adp.bom_product_id,
              SUM(adp.qty) AS qty,

              adp.bom_unit_id,
              adp.bom_unit_name,
              adp.bom_accounting_unit_id,
              adp.bom_accounting_unit_name,
              SUM(adp.accounting_qty) AS accounting_qty,
              date_format(date_add(ad.adjust_date, interval {} hour), '{}') as period_symbol,
              adp.position_id,
              ad.adjust_date
        """.format(hour_offset, _symbol)
        adjust_date_position = 18

        base_sql = """
              {}
              from supply_adjust_detail as ad 
              INNER JOIN supply_adjust_bom_report adp ON ad.adjust_id=adp.adjust_id
              WHERE {}
              {}
          """
        group_by_sql = """
            GROUP BY  
              ad.adjust_store,
              adp.product_id,
              adp.unit_id,
              adp.unit_name,
              adp.accounting_unit_id,
              adp.accounting_unit_name,
              ad.reason_type,

              adp.bom_product_id,
              adp.bom_unit_id,
              adp.bom_unit_name,
              adp.bom_accounting_unit_id,
              adp.bom_accounting_unit_name,
              period_symbol,
              adp.position_id
        """
        total_line_sql = """
        SELECT sum(T.product_qty),sum(T.accounting_product_qty) from ({}) as T
        """
        total_select_sql = """
                SELECT
                SUM(adp.qty),
                SUM(adp.accounting_qty)
        """
        total_line_select_sql = """
                SELECT
                SUM(adp.product_qty)/count(adp.line_id) as product_qty,
                SUM(adp.accounting_product_qty)/count(adp.line_id) as accounting_product_qty,
                adp.line_id
        """
        total_line_group_by_sql = """
                GROUP BY 
                adp.line_id
        """


        total_group_by_sql = """
        """
        # 分页total
        count_sql = """
        SELECT count(*) from 
                    ({})
        as tmp;
        """
        #####
        #####
        base_sub_sql = base_sql
        if order and sort:
            if order != 'asc':
                base_sql +=" order by -{}".format(sort)
            else:
                base_sql += " order by {}".format(sort)
        if limit:
            base_sql += " limit {}".format(limit)
            if offset:
                base_sql += " offset {}".format(offset)
        doc_date_sql = "ad.adjust_date>='{}' AND ad.adjust_date<'{}'".format(start_date, end_date)
        branch_type_sql = "ad.branch_type = '{}'".format(branch_type) if branch_type else None

        product_sql = "adp.product_id in ({})".format(
            ','.join([str(_id) for _id in product_ids])) if product_ids else None
        bom_product_id_sql = "adp.bom_product_id in ({})".format(
            ','.join([str(_id) for _id in bom_product_id])) if bom_product_id else None
        store_sql = "ad.adjust_store in ({})".format(
            ','.join([str(_id) for _id in store_ids])) if store_ids else None
        reason_sql = "ad.reason_type = '{}'".format(reason_type) if reason_type else None
        # position_sql = "adp.position_id in ({})".format(
        #     ','.join([str(_id) for _id in position_ids])) if position_ids else None
        where_sql = ' AND '.join(
            [w for w in [doc_date_sql, store_sql, product_sql, bom_product_id_sql, branch_type_sql, reason_sql] if
             w]) + "AND ad.status='APPROVED' AND ad.partner_id={}".format(partner_id)

        base_sql = text(base_sql.format(select_sql, where_sql, group_by_sql))
        total_sql = text(base_sub_sql.format(total_select_sql, where_sql, total_group_by_sql))

        total_line_sql = text(total_line_sql.format(base_sub_sql.format(total_line_select_sql, where_sql, total_line_group_by_sql)))

        print('base_sql', base_sql)
        count_sub_sql = base_sub_sql.format(select_sql, where_sql, group_by_sql)
        count_sql = text(count_sql.format(count_sub_sql))
        with DummyTransaction(auto_commit=False) as trans:
            resproxy = trans.scope_session.execute(base_sql)
            base_rows = resproxy.fetchall()
            total_row = trans.scope_session.execute(total_sql).fetchall()
            total_line_row = trans.scope_session.execute(total_line_sql).fetchall()
            count = trans.scope_session.execute(count_sql).fetchall()
        total = count[0][0] if count else 0
        product_list = []
        # 额外子查询
        sum_quantity = total_line_row[0][0] if total_row else 0
        sum_accounting_quantity = total_line_row[0][1] if total_row else 0
        # 物料汇总
        sum_qty = total_row[0][0] if total_row else 0
        sum_accounting_qty = total_row[0][1] if total_row else 0
        # 先获取成本 成本服务现在还没有暂时注释掉
        cost_store_sales_date_dict = {}
        cost_date = end_date - timedelta(days=2)
        branch_ids = []
        position_ids = []
        product_ids = []
        for s in base_rows:
            key = str(s[0]) + str(cost_date)[:10]
            cost_store_sales_date_dict[key] = [str(s[0]), str(cost_date)[:10]]
            branch_ids.append(s[0])
            if s[1] and s[1] not in product_ids:
                product_ids.append(s[1])
            if s[9] and s[9] not in product_ids:
                product_ids.append(s[9])
            if s[17]:
                position_ids.append(s[17])
        # cost_store_sales_product_dict = self.get_product_cost(cost_store_sales_date_dict, partner_id, user_id)
        # print('cost_store_sales_product_dict', cost_store_sales_product_dict)
        branch_map = get_branch_map(branch_ids=branch_ids, branch_type=branch_type, return_fields="id,code,name",
                                    partner_id=partner_id, user_id=user_id)
        position_map = get_branch_map(branch_ids=position_ids, branch_type="POSITION", partner_id=partner_id,
                                      user_id=user_id, return_fields="id,code,name")
        product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name", partner_id=partner_id,
                                      user_id=user_id)
        reason_map = get_supply_reason_map(_type="ADJUST", partner_id=partner_id, user_id=user_id)
        for row in base_rows:
            result_dict = dict()
            product = product_map.get(str(row[1])) if product_map.get(str(row[1])) else {}
            bom_product = product_map.get(str(row[9])) if product_map.get(str(row[9])) else {}

            result_dict['store_id'] = row[0]
            result_dict['store_code'] = branch_map.get(convert_to_int(row[0])).get('code')
            result_dict['store_name'] = branch_map.get(convert_to_int(row[0])).get('name')
            result_dict['product_id'] = row[1]
            result_dict['product_code'] = product.get('code')
            result_dict['product_name'] = product.get('name')
            result_dict['quantity'] = row[2]
            result_dict['unit_id'] = row[3]
            result_dict['unit_name'] = row[4]
            result_dict['accounting_unit_id'] = row[5]
            result_dict['accounting_unit_name'] = row[6]
            result_dict['accounting_quantity'] = row[7]
            result_dict['reason_type'] = row[8]
            result_dict['reason_name'] = reason_map.get(str(row[8])) if row[8] else ''
            result_dict['bom_product_id'] = row[9]
            result_dict['bom_product_code'] = bom_product.get('code')
            result_dict['bom_product_name'] = bom_product.get('name')
            result_dict['qty'] = row[10]
            result_dict['bom_unit_id'] = row[11]
            result_dict['bom_unit_name'] = row[12]
            result_dict['bom_accounting_unit_id'] = row[13]
            result_dict['bom_accounting_unit_name'] = row[14]
            result_dict['accounting_qty'] = row[15]
            result_dict['period_symbol'] = row[16]
            result_dict['position_id'] = row[17] if row[17] else 1
            position = position_map.get(row[17]) if position_map.get(row[17]) else {}
            result_dict["position_code"] = position.get('code')
            result_dict["position_name"] = position.get('name')
            result_dict['adjust_date'] = get_timestamp(row[adjust_date_position])
            # 增加商品物料对应门店、日期、成本
            # key = str(row[0]) + str(cost_date)[:10]
            # if row[13]:
            #      result_dict['price'] = cost_store_sales_product_dict[key].get(row[13], 0)
            #      result_dict['cost'] = float(result_dict['price'])*float(row[21]) if row[21] else 0
            # else:
            #      result_dict['price'] = cost_store_sales_product_dict[key].get(row[3], 0)
            #      result_dict['cost'] = float(result_dict['price'])*float(row[11]) if row[11] else 0
            product_list.append(result_dict)
        return product_list, total, sum_quantity, sum_accounting_quantity, sum_qty, sum_accounting_qty

    def adjusts_cancel(self, adjust_ids, partner_id, user_id, allow_status=None, username=None):
        db_session = session_maker()
        try:
            # CANCELLED
            if not allow_status:
                allow_status = ['INITED', 'REJECTED']
            log_db_list = []
            adjust_dbs = db_session.query(AdjustDB).filter(AdjustDB.id.in_(adjust_ids),
                                                           AdjustDB.status.in_(allow_status),
                                                           AdjustDB.partner_id == partner_id).with_lockmode(
                "update").all()
            adjust_detail_dbs = db_session.query(AdjustDetailsDB).filter(
                AdjustDetailsDB.adjust_id.in_(adjust_ids),
                AdjustDetailsDB.status.in_(allow_status),
                AdjustDetailsDB.partner_id == partner_id).with_lockmode("update").all()
            if adjust_dbs and adjust_detail_dbs:
                for adjust_db in adjust_dbs:
                    adjust_db.updated_at = datetime.now()
                    adjust_db.status = "CANCELLED"
                    adjust_db.process_status = "CANCELLED"
                    adjust_db.updated_by = user_id
                for adjust_detail_db in adjust_detail_dbs:
                    adjust_detail_db.updated_at = datetime.now()
                    adjust_detail_db.status = "CANCELLED"
                    adjust_detail_db.process_status = "CANCELLED"
                    adjust_detail_db.updated_by = user_id
                    adjust_detail_db.updated_name = username

                    adjust_log_db = AdjustLogDB()
                    adjust_log_db.id = get_guid()
                    adjust_log_db.adjust_id = adjust_detail_db.adjust_id
                    adjust_log_db.adjust_status = 'CANCELLED'
                    adjust_log_db.created_by = user_id
                    adjust_log_db.created = datetime.utcnow()
                    adjust_log_db.partner_id = partner_id
                    log_db_list.append(adjust_log_db)
                    # 清理待办缓存
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                        message=dict(partner_id=partner_id, store_id=adjust_detail_db.adjust_store,
                                                     doc_type="adjust"))
                db_session.add_all(log_db_list)
                db_session.commit()
                return True
            else:
                return False
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('adjusts_cancel', e))
            raise e
        finally:
            db_session.close()

    def bi_get_adjust(self, adjust_ids, is_detail=False, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustDetailsDB).filter(
                AdjustDetailsDB.partner_id == partner_id) if is_detail else db_session.query(AdjustDB).filter(
                AdjustDB.partner_id == partner_id)
            if adjust_ids and isinstance(adjust_ids, list) and len(adjust_ids) > 0:
                query = query.filter(AdjustDetailsDB.adjust_id.in_(adjust_ids)) if is_detail else query.filter(
                    AdjustDB.id.in_(adjust_ids))
            list_adjust_db = query.all()
            list_adjust_obj = []
            if list_adjust_db and isinstance(list_adjust_db, list) and len(list_adjust_db) > 0:
                for adjust_db in list_adjust_db:
                    adjust_obj = adjust_service.AdjustDetails()
                    set_model_from_db(adjust_db.as_dict(), adjust_obj)
                    list_adjust_obj.append(adjust_obj)
            return list_adjust_obj
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('bi_get_adjust_collect', e))
            raise e
        finally:
            db_session.close()

    # 获取成本
    def get_product_cost(self, cost_store_sales_date_dict, partner_id, user_id):
        # 获取成本
        cost_store_sales_product_dict = {}
        print('cost_store_sales_date_dict', cost_store_sales_date_dict)
        for key, info_list in cost_store_sales_date_dict.items():
            cost_store_sales_product_dict[key] = {}
            store_code = info_list[0]
            cost_date = info_list[1]
            product_cost_ret = cost_service.get_product_cost_by_store_code(store_code=store_code, date=cost_date,
                                                                           partner_id=partner_id, user_id=user_id).get(
                'rows', [])
            logging.info('分差拉取每日成本,条件{}/{}。'.format(store_code, cost_date))
            # print('product_cost_ret',product_cost_ret)
            if len(product_cost_ret) > 0:
                for pc in product_cost_ret:
                    cost_store_sales_product_dict[key][int(pc.get('product_id', 0))] = pc.get('uncs', 0)

        print('cost_store_sales_product_dict', cost_store_sales_product_dict)
        return cost_store_sales_product_dict

    def bi_get_adjust_detailed(self, partner_id, user_id=None, st_ids=None, product_ids=None, bom_product_id=None,
                               start_date=None, end_date=None, branch_type=None, reason_type=None, limit=None,
                               offset=None, include_total=False, store_ids=None, order=None, sort=None):
        if sort == 'accounting_quantity':
            sort = 'accounting_product_qty'
        if sort == 'quantity':
            sort = 'product_qty'
        select_sql = """
            SELECT 
              ad.adjust_id ,
              ad.code as doc_code,
              ad.adjust_store,
              
              adp.product_id,
              adp.product_qty AS product_qty,
    
              adp.unit_id,
              adp.unit_name,
              adp.accounting_unit_id,
              adp.accounting_unit_name,
              SUM(adp.accounting_product_qty) AS accounting_product_qty,
              ad.reason_type,
              
              adp.bom_product_id,
              adp.qty AS qty,
              
              adp.bom_unit_id,
              adp.bom_unit_name,
              adp.bom_accounting_unit_id,
              adp.bom_accounting_unit_name,
              SUM(adp.accounting_qty) AS accounting_qty,
              ad.adjust_date,
              adp.position_id
        """
        adjust_date_position = 18

        base_sql = """
              {}
              from supply_adjust_detail as ad 
              INNER JOIN supply_adjust_bom_report adp ON ad.adjust_id=adp.adjust_id
              WHERE {}
              {}
          """
        group_by_sql = """
            GROUP BY  
              adp.id,
              ad.adjust_id ,
              doc_code,
              ad.adjust_date,
              ad.adjust_store,
    
              adp.product_id,
              adp.unit_id,
              adp.unit_name,
              adp.accounting_unit_id,
              adp.accounting_unit_name,
              ad.reason_type,
              
              adp.bom_product_id,
              adp.bom_unit_id,
              adp.bom_unit_name,
              adp.bom_accounting_unit_id,
              adp.bom_accounting_unit_name,
              adp.position_id
        """

        total_line_sql = """
        SELECT sum(T.product_qty),sum(T.accounting_product_qty) from ({}) as T
        """
        total_line_select_sql = """
                SELECT
                SUM(adp.product_qty)/count(adp.line_id) as product_qty,
                SUM(adp.accounting_product_qty)/count(adp.line_id) as accounting_product_qty,
                adp.line_id
        """
        total_line_group_by_sql = """
                GROUP BY 
                adp.line_id
        """

        total_select_sql = """
                SELECT 
                SUM(adp.qty),
                SUM(adp.accounting_qty)
        """
        total_group_by_sql = """
        """
        # 分页total
        count_sql = """
        SELECT count(*) from 
                    ({})
        as tmp;
        """

        base_sub_sql = base_sql
        if order and sort:
            if order != 'asc':
                base_sql +=" order by -{}".format(sort)
            else:
                base_sql += " order by {}".format(sort)
        if limit:
            base_sql += " limit {}".format(limit)
            if offset:
                base_sql += " offset {}".format(offset)
        doc_date_sql = "ad.adjust_date>='{}' AND ad.adjust_date<'{}'".format(start_date, end_date)
        branch_type_sql = "ad.branch_type = '{}'".format(branch_type) if branch_type else None
        product_sql = "adp.product_id in ({})".format(
            ','.join([str(_id) for _id in product_ids])) if product_ids else None
        bom_product_id_sql = "adp.bom_product_id in ({}) ".format(
            ','.join([str(_id) for _id in bom_product_id])) if bom_product_id else None
        store_sql = "ad.adjust_store in ({})".format(
            ','.join([str(_id) for _id in store_ids])) if store_ids else None
        reason_sql = "ad.reason_type = '{}'".format(reason_type) if reason_type else None
        # position_sql = "adp.position_id in ({})".format(
        #     ','.join([str(_id) for _id in position_ids])) if position_ids else None
        where_sql = ' AND '.join(
            [w for w in [doc_date_sql, store_sql, product_sql, bom_product_id_sql, branch_type_sql, reason_sql] if
             w]) + "AND ad.status='APPROVED' AND ad.partner_id={}".format(partner_id)

        base_sql = text(base_sql.format(select_sql, where_sql, group_by_sql))
        total_sql = text(base_sub_sql.format(total_select_sql, where_sql, total_group_by_sql))
        total_line_sql = text(
            total_line_sql.format(base_sub_sql.format(total_line_select_sql, where_sql, total_line_group_by_sql)))
        print('base_sql', base_sql)
        count_sub_sql = base_sub_sql.format(select_sql, where_sql, group_by_sql)
        count_sql = text(count_sql.format(count_sub_sql))
        with DummyTransaction(auto_commit=False) as trans:
            resproxy = trans.scope_session.execute(base_sql)
            base_rows = resproxy.fetchall()
            total_row = trans.scope_session.execute(total_sql).fetchall()
            total_line_row = trans.scope_session.execute(total_line_sql).fetchall()
            count = trans.scope_session.execute(count_sql).fetchall()
        total = count[0][0]
        product_list = []

        # 额外子查询
        sum_quantity = total_line_row[0][0] if total_row else 0
        sum_accounting_quantity = total_line_row[0][1] if total_row else 0
        # 物料汇总
        sum_qty = total_row[0][0] if total_row else 0
        sum_accounting_qty = total_row[0][1] if total_row else 0

        # 先获取成本
        cost_store_sales_date_dict = {}
        branch_ids = []
        position_ids = []
        product_ids = []
        for s in base_rows:
            key = str(s[2]) + str(s[18] - timedelta(days=1))[:10]
            cost_store_sales_date_dict[key] = [str(s[2]), str(s[18] - timedelta(days=1))[:10]]
            branch_ids.append(s[2])
            if s[3] and s[3] not in product_ids:
                product_ids.append(s[3])
            if s[11] and s[11] not in product_ids:
                product_ids.append(s[11])
            if s[19]:
                position_ids.append(s[19])
        # cost_store_sales_product_dict = self.get_product_cost(cost_store_sales_date_dict, partner_id, user_id)
        # print('cost_store_sales_product_dict', cost_store_sales_product_dict)
        # 查询门店详情map
        branch_map = get_branch_map(branch_ids=branch_ids, branch_type=branch_type,
                                    partner_id=partner_id, user_id=user_id, return_fields="id,code,name")
        position_map = get_branch_map(branch_ids=position_ids, branch_type="POSITION", partner_id=partner_id,
                                      user_id=user_id, return_fields="id,code,name")
        product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name", partner_id=partner_id,
                                      user_id=user_id)
        reason_map = get_supply_reason_map(_type="ADJUST", partner_id=partner_id, user_id=user_id)
        for row in base_rows:
            result_dict = dict()
            product = product_map.get(str(row[3])) if product_map.get(str(row[3])) else {}
            bom_product = product_map.get(str(row[11])) if product_map.get(str(row[11])) else {}
            store = branch_map.get(row[2]) if branch_map.get(row[2]) else {}

            result_dict['adjust_id'] = row[0]
            result_dict['adjust_code'] = row[1]
            result_dict['store_id'] = row[2]
            result_dict['store_code'] = store.get('code')
            result_dict['store_name'] = store.get('name')
            result_dict['product_id'] = row[3]
            result_dict['product_code'] = product.get('code')
            result_dict['product_name'] = product.get('name')
            result_dict['quantity'] = row[4]
            result_dict['unit_id'] = row[5]
            result_dict['unit_name'] = row[6]
            result_dict['accounting_unit_id'] = row[7]
            result_dict['accounting_unit_name'] = row[8]
            result_dict['accounting_quantity'] = row[9]
            result_dict['reason_type'] = row[10]
            result_dict['reason_name'] = reason_map.get(str(row[10])) if row[10] else ''
            result_dict['bom_product_id'] = row[11]
            result_dict['bom_product_code'] = bom_product.get('code')
            result_dict['bom_product_name'] = bom_product.get('name')
            result_dict['qty'] = row[12]
            result_dict['bom_unit_id'] = row[13]
            result_dict['bom_unit_name'] = row[14]
            result_dict['bom_accounting_unit_id'] = row[15]
            result_dict['bom_accounting_unit_name'] = row[16]
            result_dict['accounting_qty'] = row[17]
            result_dict['position_id'] = row[19] if row[19] else 1
            position = position_map.get(row[19]) if position_map.get(row[19]) else {}
            result_dict["position_code"] = position.get('code')
            result_dict["position_name"] = position.get('name')
            result_dict["adjust_date"] = get_timestamp(row[adjust_date_position])

            # 增加商品物料对应门店、日期、成本
            # key = str(row[2]) + str(row[18] - timedelta(days=1))[:10]
            # if row[15]:
            #     result_dict['price'] = cost_store_sales_product_dict[key].get(row[15], 0)
            #     result_dict['cost'] = float(result_dict['price']) * float(row[23]) if row[23] else 0
            # else:
            #     result_dict['price'] = cost_store_sales_product_dict[key].get(row[5], 0)
            #     result_dict['cost'] = float(result_dict['price']) * float(row[13]) if row[13] else 0

            product_list.append(result_dict)
        return product_list, total, sum_quantity, sum_accounting_quantity, sum_qty, sum_accounting_qty

    def auto_close_created_adjust(self, adjust_date, partner_id=None, user_id=None):
        _s_adjust_date = adjust_date.strftime('%Y-%m-%d')
        _e_adjust_date = (adjust_date + timedelta(days=1)).strftime("%Y-%m-%d")
        now = str(datetime.now())
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              UPDATE supply_adjust sd JOIN supply_adjust_detail sdd ON sd.id = sdd.adjust_id
              SET sd.status='CANCELLED',sd.process_status='CANCELLED',sd.updated_at='{}',
              sdd.status='CANCELLED',sdd.process_status='CANCELLED' ,sdd.updated_at='{}'
              WHERE sd.adjust_date>='{}'
              AND sd.adjust_date<'{}'
              AND sd.partner_id={}
              AND sd.status in ('CREATED','INITED');
           '''.format(now, now, _s_adjust_date, _e_adjust_date, partner_id)
            trans.scope_session.execute(text(sql_text))
            trans.scope_session.commit()
        return True

    def get_adjust_bom_report_batch(self, adjust_id, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustBomReportBatchDB).filter(AdjustBomReportBatchDB.adjust_id == adjust_id,
                                                                    AdjustBomReportBatchDB.partner_id == partner_id)
            adjust_db = query.first()
            if adjust_db:
                return adjust_db
            return None
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('store_adjust_by_id', e))
            raise e
        finally:
            db_session.close()

    def add_adjust_bom_report_batch(self, adjust_id, partner_id):
        db_session = session_maker()
        try:
            adjust_db = AdjustBomReportBatchDB()
            adjust_db.id = get_guid()
            adjust_db.created_at = datetime.now()
            adjust_db.adjust_id = adjust_id
            adjust_db.partner_id = partner_id
            db_session.add(adjust_db)
            db_session.commit()
            return adjust_db
        except exc.IntegrityError:
            logging.info('重复创建报表请求')
            return None
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('create_store_adjust', e))
            raise e
        finally:
            db_session.close()

    def add_adjust_bom_report_list(self, adjust_bom_report_list):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(AdjustBomReportDB, adjust_bom_report_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('create_store_adjust', e))
            raise e
        finally:
            db_session.close()

    def get_adjust_by_code(self, code, partner_id=None):
        db_session = session_maker()
        try:
            adjust_db = db_session.query(AdjustDB).filter(AdjustDB.code == code,
                                                          AdjustDB.partner_id == partner_id).first()
            if adjust_db:
                adjust = adjust_service.Adjust()
                set_model_from_db(adjust_db.as_dict(), adjust)
                return adjust
        except NoResultFound as e:
            pass
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            raise e
        finally:
            db_session.close()
        return None

    def list_adjust_bom_report(self, product_ids=None, adjust_id=None, partner_id=None):
        """查询报废bom拆解列表
        :param product_ids 报废商品ID列表
        :param adjust_id 报废单ID
        :param partner_id
        """
        db_session = session_maker()
        try:
            sql_str = """
            select abt.product_id as product_id,
                   abt.bom_product_id as bom_product_id,
                   sum(abt.qty), sum(abt.accounting_qty)
            from supply_adjust_bom_report as abt
            where abt.partner_id = {}
            {}
            group by product_id, bom_product_id;
            """
            where_sql = ""
            if adjust_id:
                where_sql += " AND abt.adjust_id = {}".format(adjust_id)
            if product_ids and isinstance(product_ids, list):
                product_ids = ",".join([str(pid) for pid in product_ids])
                where_sql += " AND abt.product_id in ({})".format(product_ids)
            sql_str = sql_str.format(partner_id, where_sql)
            rows = db_session.execute(sql_str).fetchall()
            return rows
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('list_adjust_bom_report', e))
            raise e
        finally:
            db_session.close()

    def list_adjust_bom_report_detail(self, adjust_id=None, partner_id=None, product_ids=None):
        """查询报废bom拆解列表详情
        :param adjust_id 报废单ID
        :param partner_id
        """
        db_session = session_maker()
        try:
            q = db_session.query(AdjustBomReportDB).filter(AdjustBomReportDB.partner_id==partner_id,
                                AdjustBomReportDB.adjust_id==adjust_id)
            if product_ids and isinstance(product_ids, list):
                q = q.filter(AdjustBomReportDB.product_id.in_(product_ids))
            return q.all()
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('create_store_adjust', e))
            raise e
        finally:
            db_session.close()

    def list_adjust(self, partner_id=None, store_ids=None, status=None, start_date=None, end_date=None, code=None,
                    branch_type=None, cost_trans_status=None,id=None):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustDB)
            if partner_id:
                query = query.filter(AdjustDB.partner_id == partner_id)
            if branch_type:
                query = query.filter(AdjustDB.branch_type == branch_type)
            if code:
                query = query.filter(AdjustDB.code == code)
            if store_ids and isinstance(store_ids, list) and len(store_ids) > 0:
                query = query.filter(AdjustDB.adjust_store.in_(store_ids))
            if status and isinstance(status, str):
                query = query.filter(AdjustDB.status == status)
            elif status and isinstance(status, list) and len(status) > 0:
                query = query.filter(AdjustDB.status.in_(status))
            if start_date:
                query = query.filter(AdjustDB.created_at >= start_date)
            if end_date:
                query = query.filter(AdjustDB.created_at < end_date)
            if cost_trans_status is not None:
                query = query.filter(AdjustDB.cost_trans_status == cost_trans_status)
            if id:
                query = query.filter(AdjustDB.id == id)
            return query.all()
        except Exception as e:
            logging.error("** method[%s] error. Error message:%s **" % ('list_adjust', e))
            raise e
        finally:
            db_session.close()

    def update_adjust(self, update_data: dict):
        """只更新报废单adjust
        成本传输批次流转时用
        """
        db_session = session_maker()
        try:
            update_db = db_session.query(AdjustDB).filter(
                AdjustDB.partner_id == update_data.get('partner_id')).filter(
                AdjustDB.id == update_data.get('id')).with_lockmode('update').first()
            if update_db:
                if update_data.get('updated_by'):
                    update_db.updated_by = update_data.get('updated_by')
                if update_data.get('updated_name'):
                    update_db.updated_name = update_data.get('updated_name')
                if update_data.get('cost_trans_status') is not None:
                    update_db.cost_trans_status = update_data.get('cost_trans_status')

                db_session.add(update_db)
                db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Adjust Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                 traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_adjust_log(self, adjust_id, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(AdjustLogDB).filter(AdjustLogDB.adjust_id == adjust_id,
                                                         AdjustLogDB.partner_id == partner_id)
            query = query.order_by(AdjustLogDB.created_at.desc())
            adjust_logs = query.all()
            if not adjust_logs:
                return 0, []
            query_count = db_session.query(func.count(AdjustLogDB.id))
            if partner_id:
                query_count = query_count.filter(AdjustLogDB.partner_id == partner_id)
            if adjust_id:
                query_count = query_count.filter(AdjustLogDB.adjust_id == adjust_id)
            count = query_count.scalar()
            return count, adjust_logs
        except NoResultFound:
            pass
        except Exception as e:
            logging.error(
                "** method[%s] error. Error message:%s **" % ('get_adjust_log', e))
            raise e
        finally:
            db_session.close()
        return None
