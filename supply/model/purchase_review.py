# -*- coding: utf-8 -*-
import logging
import traceback
import sys
import json

from datetime import datetime
from supply.model import DeclarativeB<PERSON>, MethodMixin, TimestampMixin, AsDict
from sqlalchemy import Column, BigInteger, String, DateTime, func, DECIMAL, SMALLINT, TEXT, Boolean, Integer, alias
from supply.model.price_adjustment import price_adjustment_db
from supply.model.supply_doc_code import Supply_doc_code
from supply.utils.helper import get_guid, get_uuids
from supply.driver.mysql import session_maker
from supply.utils.snowflake import gen_snowflake_id


class PurchaseReviewOrder(DeclarativeBase, MethodMixin, TimestampMixin):
    """采购复核订单"""
    __tablename__ = "purchase_review_order"

    class Status(object):
        INITED = 'INITED'         # 待复核
        SUBMITTED = 'SUBMITTED'   # 待确认
        APPROVED = 'APPROVED'     # 已复核
        REJECTED = 'REJECTED'     # 已驳回

    class ProcessStatus(object):
        INITED = 'INITED'      # 待勾兑
        COMPLETED = 'COMPLETED'  # 已勾兑

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 商户id
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)  # 创建人姓名
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)  # 更新人姓名
    batch_id = Column(BigInteger)  # 主单id
    branch_type = Column(String)   # 区分门店仓库WAREHOUSE/STORE
    order_code = Column(String, index=True)  # 订单编号
    origin_code = Column(String)  # 原单编号
    order_type = Column(String, index=True)  # 单据类型（采购收货单,采购退货单,门店收货单,门店退货单）
    status = Column(String, default=Status.INITED, index=True)  # 复核单据业务状态(INITED,COMPLETE)
    process_status = Column(String, default=ProcessStatus.INITED)      # 单据业务状态给发票勾兑使用
    received_date = Column(DateTime, index=True)  # 收货日期
    received_by = Column(BigInteger)  # 门店或仓库id
    received_code = Column(String)    # 门店或仓库code
    received_name = Column(String)  # 门店或仓库名称
    supplier_id = Column(BigInteger)  # 供应商id
    supplier_name = Column(String)    # 供应商名称
    supplier_code = Column(String)    # 供应商code
    review_by = Column(BigInteger)    # 审核人
    is_modify = Column(SMALLINT)  # 是否变更价格(否0,是1)
    company_id = Column(BigInteger)  # 公司id
    adjust_info = Column(TEXT)   # 调价单信息: {adjust_codes: [...]}
    # 单据来源：
    # 订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)
    # 原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)
    source_type = Column(String)
    is_adjust = Column(Boolean, default=0)  # 是否是调整单
    sum_price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 总含税金额
    orig_sum_price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 原始总含税金额
    sum_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 总未税金额
    orig_sum_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 原始未税金额
    generate_adjust = Column(Boolean, default=0)    # 是否生成调价单0/1
    cost_center_id = Column(BigInteger)  # 成本中心id
    reject_reason = Column(String)       # 驳回原因


class PurchaseReviewOrderProduct(DeclarativeBase, MethodMixin, TimestampMixin, AsDict):
    """采购复核订单商品"""
    __tablename__ = "purchase_review_order_product"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)  # 商户id
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)  # 创建人姓名
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)  # 更新人姓名
    order_id = Column(BigInteger)  # 关联的采购复核单id
    product_id = Column(BigInteger)  # 商品id
    product_code = Column(String)  # 商品code
    product_name = Column(String)  # 商品名称
    product_category_id = Column(BigInteger)  # 商品类别id
    product_type = Column(String)  # 商品类型
    unit_id = Column(BigInteger)  # 单位id(门店中为订货单位/仓库为采购单位)
    unit_name = Column(String)  # 单位名称
    unit_spec = Column(String)  # 单位规格
    spec = Column(String)  # 规格
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 订货/退货数量
    actual_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 实收数量
    tax_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 税率
    price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 未税单价
    orig_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 原始未税单价
    sum_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 未税合计
    orig_sum_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 原始未税合计
    price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税单价
    orig_price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 原始含税单价
    prev_price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 上一次调整含税单价
    sum_price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税合计
    orig_sum_price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 原始含税合计
    is_modify = Column(SMALLINT)  # 是否变更价格(否0,是1)


class PurchaseReviewLog(DeclarativeBase, AsDict):
    """采购复核日志"""
    __tablename__ = "purchase_review_log"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    main_id = Column(BigInteger, index=True)  # 关联的复核单id
    action = Column(String(50))               # 操作状态
    created_at = Column(DateTime, default=datetime.utcnow())


class PurchaseReviewPriceAdjustLog(DeclarativeBase, AsDict):
    """采购复核调价记录"""
    __tablename__ = "purchase_review_price_adjust_log"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    main_id = Column(BigInteger, index=True)            # 关联的复核单id
    product_id = Column(BigInteger, index=True)         # 商品ID
    price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)           # 当前含税价
    orig_price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)      # 原始后含税价
    prev_price_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)      # 含税价调整前
    created_at = Column(DateTime, default=datetime.utcnow())


class PurchaseReviewOrderDB(object):
    """采购复核相关数据库操作"""
    def get_purchase_order_by_id(self, review_id=None, batch_id=None, partner_id=None):
        """获取一个采购复核单
        review_id, batch_id,二选一查询
        只能传一个"""
        db_session = session_maker()
        try:
            query = db_session.query(PurchaseReviewOrder).filter(PurchaseReviewOrder.partner_id == partner_id)
            if review_id:
                query = query.filter(PurchaseReviewOrder.id == review_id).first()
                return query
            if batch_id:
                query = query.filter(PurchaseReviewOrder.batch_id == batch_id).first()
                return query
        except Exception as e:
            logging.error("Get Purchase Review Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                      traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_purchase_review_order(self, order_data, product_list):
        """保存采购复核单和商品"""
        db_session = session_maker()
        try:
            order_list = [order_data]
            db_session.bulk_insert_mappings(PurchaseReviewOrder, order_list)
            db_session.bulk_insert_mappings(PurchaseReviewOrderProduct, product_list)
            review_log = PurchaseReviewLog()
            review_log.id = get_guid()
            review_log.partner_id = order_data.get('partner_id')
            review_log.created_by = order_data.get('created_by')
            review_log.created_name = order_data.get('created_name')
            review_log.main_id = order_data.get('id')
            review_log.action = order_data.get('status')
            review_log.created_at = datetime.utcnow()
            db_session.add(review_log)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Purchase Review Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                         traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def list_purchase_review_order(self, partner_id=None, user_id=None, start_date=None, end_date=None, order_code=None,
                                   process_status=None, order_type=None, supplier_ids=None, received_ids=None, limit=-1,
                                   offset=None, include_total=False, order=None, sort=None, branch_type=None,
                                   company_id=None, generate_adjust=None, is_modify=None, status=None, is_adjust=False,
                                   source_type=None, origin_code=None):
        """采购复核列表"""
        db_session = session_maker()
        try:
            query = db_session.query(PurchaseReviewOrder).filter(PurchaseReviewOrder.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(PurchaseReviewOrder.received_date >= start_date)
            if end_date and isinstance(end_date, datetime) and end_date.year != 1970:
                query = query.filter(PurchaseReviewOrder.received_date <= end_date)
            if order_code:
                query = query.filter(PurchaseReviewOrder.order_code == order_code)
            if origin_code:
                query = query.filter(PurchaseReviewOrder.origin_code == origin_code)
            if branch_type:
                query = query.filter(PurchaseReviewOrder.branch_type == branch_type)
            if source_type:
                query = query.filter(PurchaseReviewOrder.source_type == source_type)
            if company_id:
                query = query.filter(PurchaseReviewOrder.company_id == company_id)
            if status:
                query = query.filter(PurchaseReviewOrder.status == status)
            if order_type:
                query = query.filter(PurchaseReviewOrder.order_type == order_type)
            if supplier_ids:
                query = query.filter(PurchaseReviewOrder.supplier_id.in_(supplier_ids))
            if received_ids:
                query = query.filter(PurchaseReviewOrder.received_by.in_(received_ids))
            if generate_adjust in [1, 0, True, False]:
                query = query.filter(PurchaseReviewOrder.generate_adjust == generate_adjust)
            if is_modify in [1, 0]:
                query = query.filter(PurchaseReviewOrder.is_modify == is_modify)
            if process_status:
                query = query.filter(PurchaseReviewOrder.process_status == process_status)
            if is_adjust:
                query = query.filter(PurchaseReviewOrder.is_adjust == is_adjust)
            if sort in ["received_date", "received_code", "supplier_code", "sum_price", "sum_price_tax", "order_code",
                        "orig_sum_price_tax", "orig_sum_price"]:
                if order == "asc":
                    query_str = "PurchaseReviewOrder.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "PurchaseReviewOrder.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(PurchaseReviewOrder.updated_at)
                else:
                    query = query.order_by(PurchaseReviewOrder.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(PurchaseReviewOrder.id)).filter(
                    PurchaseReviewOrder.partner_id == partner_id)
                if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                    query_total = query_total.filter(PurchaseReviewOrder.received_date >= start_date)
                if end_date and isinstance(end_date, datetime) and end_date.year != 1970:
                    query_total = query_total.filter(PurchaseReviewOrder.received_date <= end_date)
                if order_code:
                    query_total = query_total.filter(PurchaseReviewOrder.order_code == order_code)
                if origin_code:
                    query_total = query_total.filter(PurchaseReviewOrder.origin_code == origin_code)
                if branch_type:
                    query_total = query_total.filter(PurchaseReviewOrder.branch_type == branch_type)
                if source_type:
                    query_total = query_total.filter(PurchaseReviewOrder.source_type == source_type)
                if company_id:
                    query_total = query_total.filter(PurchaseReviewOrder.company_id == company_id)
                if status:
                    query_total = query_total.filter(PurchaseReviewOrder.status == status)
                if order_type:
                    query_total = query_total.filter(PurchaseReviewOrder.order_type == order_type)
                if supplier_ids:
                    query_total = query_total.filter(PurchaseReviewOrder.supplier_id.in_(supplier_ids))
                if received_ids:
                    query_total = query_total.filter(PurchaseReviewOrder.received_by.in_(received_ids))
                if generate_adjust in [1, 0, True, False]:
                    query_total = query_total.filter(PurchaseReviewOrder.generate_adjust == generate_adjust)
                if is_modify in [0, 1]:
                    query_total = query_total.filter(PurchaseReviewOrder.is_modify == is_modify)
                if process_status:
                    query_total = query_total.filter(PurchaseReviewOrder.process_status == process_status)
                if is_adjust:
                    query_total = query_total.filter(PurchaseReviewOrder.is_adjust == is_adjust)
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Purchase Review Order Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                             traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def get_review_order_product(self, partner_id=None, user_id=None, order_id=None, limit=None,
                                 offset=None, include_total=False):
        """根据订单id拉取商品信息"""
        db_session = session_maker()
        try:
            query = db_session.query(PurchaseReviewOrderProduct).filter(
                PurchaseReviewOrderProduct.order_id == order_id).filter(
                PurchaseReviewOrderProduct.partner_id == partner_id)
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(PurchaseReviewOrderProduct.id)).filter(
                    PurchaseReviewOrderProduct.order_id == order_id).filter(
                    PurchaseReviewOrderProduct.partner_id == partner_id)
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("Get Review Order Product Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                            traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def list_purchase_review_detail(self, partner_id=None, start_date=None, end_date=None, origin_code=None,
                                    order_code=None, process_status=None, order_type=None, supplier_ids=None,
                                    received_ids=None, limit=-1, offset=None, include_total=False, order=None,
                                    sort=None, branch_type=None, company_id=None, generate_adjust=None, is_modify=None,
                                    status=None, is_adjust=False, source_type=None):
        db_session = session_maker()
        try:
            select_sql = '''
            SELECT 
                pw.id AS order_id, pw.order_code, pw.order_type, pw.status, pw.received_date,
                pw.received_by, pw.received_name, pw.received_code, pw.supplier_id, pw.supplier_name,
                pw.supplier_code, pw.branch_type, pw.company_id, pw.process_status, pw.origin_code,
                pw.source_type, pw.generate_adjust,
                pwp.id, pwp.product_id, pwp.product_code, pwp.product_name, pwp.product_type,
                pwp.product_category_id, pwp.unit_id, pwp.unit_name, pwp.unit_spec, pwp.spec, 
                pwp.quantity, pwp.actual_quantity, pwp.tax_rate, pwp.price, pwp.sum_price,
                pwp.price_tax, pwp.sum_price_tax, pwp.orig_price_tax, pwp.prev_price_tax, 
                pwp.orig_sum_price_tax, pwp.orig_price, pwp.orig_sum_price, pwp.is_modify,
                (pwp.sum_price_tax - pwp.sum_price) AS sum_tax,
                (pwp.orig_sum_price_tax - pwp.orig_sum_price) AS orig_sum_tax, 
                pwp.created_by, pwp.created_name, pwp.created_at, pwp.updated_by, pwp.updated_name,
                pwp.updated_at
            FROM purchase_review_order AS pw
                 LEFT JOIN purchase_review_order_product AS pwp ON pw.id = pwp.order_id
            WHERE {}
            ORDER BY {}
            {};
            '''
            total_sql = '''
            SELECT count(pwp.id)
            FROM purchase_review_order AS pw
                 LEFT JOIN purchase_review_order_product AS pwp ON pw.id = pwp.order_id
            WHERE {};
            '''

            where_sql = "pw.partner_id = {}".format(partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                where_sql += " AND pw.received_date >= '{}'".format(start_date)
            if end_date and isinstance(end_date, datetime) and end_date.year != 1970:
                where_sql += " AND pw.received_date <= '{}'".format(end_date)
            if branch_type:
                where_sql += " AND pw.branch_type = '{}'".format(branch_type)
            if order_code:
                where_sql += " AND pw.order_code = '{}'".format(order_code)
            if origin_code:
                where_sql += " AND pw.origin_code = '{}'".format(origin_code)
            if source_type:
                where_sql += " AND pw.source_type = '{}'".format(source_type)
            if company_id:
                where_sql += " AND pw.company_id = {}".format(company_id)
            if status:
                where_sql += " AND pw.status = '{}'".format(status)
            if order_type:
                where_sql += " AND pw.order_type = '{}'".format(order_type)
            if supplier_ids:
                supplier_ids = ",".join([str(sid) for sid in supplier_ids])
                where_sql += " AND pw.supplier_id in ({})".format(supplier_ids)
            if received_ids:
                received_ids = ",".join([str(rid) for rid in received_ids])
                where_sql += " AND pw.received_by in ({})".format(received_ids)
            if generate_adjust in [1, 0, True, False]:
                where_sql += " AND pw.generate_adjust = {}".format(generate_adjust)
            if is_modify in [1, 0]:
                where_sql += " AND pw.is_modify = {}".format(is_modify)
            if process_status:
                where_sql += " AND pw.process_status = '{}'".format(process_status)
            order_by_sql = 'pw.updated_at DESC'
            limit_sql = ' '
            if limit and limit != -1:
                limit_sql += 'LIMIT {}'.format(limit)
            offset_sql = ' '
            if offset:
                offset_sql += 'OFFSET {}'.format(offset)
            full_sql = select_sql.format(where_sql, order_by_sql, limit_sql+offset_sql)
            query_set = db_session.execute(full_sql).fetchall()
            # print("query: {}".format(full_sql))
            if include_total:
                total = db_session.execute(total_sql.format(where_sql)).fetchone()
                return total[0], query_set
            return query_set
        except Exception as e:
            logging.error("List Purchase Review Order Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                             traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def update_review_order_no_product(self, action=None, is_modify=None, update_data=None, update_logs=None):
        """采购单批量复核
        :param action 更新状态
        :param is_modify 是否调过价 0/1
        :param update_data 待复核的数据列表
        :param update_logs 更新logs
        """
        db_session = session_maker()
        try:
            if isinstance(update_data, list):
                db_session.bulk_update_mappings(PurchaseReviewOrder, update_data)
            if update_logs and isinstance(update_logs, list):
                db_session.bulk_insert_mappings(PurchaseReviewLog, update_logs)
            db_session.commit()
            update_detail = update_data[0]
            partner_id = update_detail.get('partner_id')
            if action == "APPROVED" and is_modify == 1:
                adr, adt = self.pre_create_pad_params(review_id=update_detail.get('id'),
                                                      partner_id=partner_id,
                                                      user_id=update_detail.get('updated_by'),
                                                      username=update_detail.get("updated_name"))
                result = price_adjustment_db.create_price_adjustment(order_data=adr, adjust_detail=adt)
                if result is True:
                    adjust_code = adr.get('code')
                    query_order = db_session.query(PurchaseReviewOrder).filter(
                        PurchaseReviewOrder.id == update_detail.get("id"),
                        PurchaseReviewOrder.partner_id == partner_id).with_lockmode("update").first()
                    if query_order.adjust_info:
                        adjust_info = json.loads(query_order.adjust_info)
                        adjust_codes = adjust_info.get('adjust_codes', [])
                        if isinstance(adjust_codes, list):
                            adjust_codes.append(adjust_code)
                    else:
                        adjust_info = dict(adjust_codes=[adjust_code])
                    query_order.adjust_info = json.dumps(adjust_info)
                    query_order.generate_adjust = 1
                    db_session.add(query_order)
                    db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Review Order Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                       traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def update_review_order_product(self, order_detail=None, product_data_list=None, adjust_logs=None, partner_id=None):
        """复核单更新+提交
        :param order_detail 订单信息
        :param adjust_logs 调价log
        :param product_data_list 订单商品信息
        :param partner_id
        """
        db_session = session_maker()
        try:
            query_order = db_session.query(PurchaseReviewOrder).filter(
                PurchaseReviewOrder.id == order_detail.get("id"),
                PurchaseReviewOrder.partner_id == partner_id).with_lockmode("update").first()
            query_order.updated_name = order_detail.get("updated_name")
            if order_detail.get("status"):
                query_order.status = order_detail.get("status")
            if order_detail.get('process_status'):
                query_order.process_status = order_detail.get("process_status")
            query_order.is_modify = order_detail.get("is_modify")
            if order_detail.get("sum_price_tax") is not None:
                query_order.sum_price_tax = order_detail.get("sum_price_tax")
            if order_detail.get('sum_price') is not None:
                query_order.sum_price = order_detail.get('sum_price')
            db_session.add(query_order)
            if product_data_list:
                db_session.bulk_update_mappings(PurchaseReviewOrderProduct, product_data_list)
            if adjust_logs:
                db_session.bulk_insert_mappings(PurchaseReviewPriceAdjustLog, adjust_logs)
            review_log = PurchaseReviewLog()
            review_log.id = get_guid()
            review_log.partner_id = partner_id
            review_log.created_by = order_detail.get('updated_by')
            review_log.created_name = order_detail.get('updated_name')
            review_log.main_id = order_detail.get('id')
            review_log.action = order_detail.get('status')
            review_log.created_at = datetime.utcnow()
            db_session.add(review_log)
            db_session.commit()
            # modify_product_ids = [p.get('id') for p in product_data_list]
            # adr, adt = self.orig_create_pad_params(review_id=query_order.id,
            #                                       partner_id=partner_id, user_id=user_id,
            #                                       username=order_detail.get("updated_name"),
            #                                       modify_product_ids=modify_product_ids)
            # result = price_adjustment_db.create_price_adjustment(order_data=adr, adjust_detail=adt)
            # if result is True:
            #     return adr.get('code')
            return True
        except Exception as e:
            logging.error(
                "Update Review Order Product Failed:{}-{}-{}".format(e, sys.exc_info(), traceback.format_exc()))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def pre_create_pad_params(self, review_id, partner_id, user_id, username, modify_product_ids=None):
        review_order = purchase_review_order_db.get_purchase_order_by_id(review_id=review_id, partner_id=partner_id)
        if review_order.status != "APPROVED":
            # 如果复核单状态不是已复核状态不往下进行
            raise Exception("Adjustment status invalid!")
        adjust_code = Supply_doc_code.get_code_by_type('PRICE_ADJ', partner_id, user_id)
        adjust_date = datetime.utcnow()  # 取当前单utc时间
        adjust_id = gen_snowflake_id()
        adjust_order = dict(
            id=adjust_id,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            batch_id=review_id,
            batch_code=review_order.order_code,
            branch_type=review_order.branch_type,
            code=adjust_code,
            status="APPROVED",         # 默认生效
            blending_status="INITED",  # 发票勾兑待勾兑
            order_type="REVIEW_ADJUST",  # 此接口暂时只有复核调价单
            adjust_date=adjust_date,
            branch_id=review_order.received_by,
            branch_code=review_order.received_code,
            branch_name=review_order.received_name,
            supplier_id=review_order.supplier_id,
            supplier_name=review_order.supplier_name,
            supplier_code=review_order.supplier_code,
            sum_price_tax=review_order.sum_price_tax,
            pre_sum_price_tax=review_order.orig_sum_price_tax,
            sum_price=review_order.sum_price,
            pre_sum_price=review_order.orig_sum_price,
            cost_trans_status=0,
            cost_update=0,
            cost_center_id=review_order.cost_center_id,  # 不实时拉取成本中心，要拿原单据的
            company_id=review_order.company_id
        )
        # 根据复核单id拿商品详情
        review_products = purchase_review_order_db.get_review_order_product(partner_id=partner_id,
                                                                            user_id=user_id,
                                                                            order_id=review_id)
        adjust_detail = []
        if isinstance(review_products, list):
            ids = get_uuids(len(review_products))
            for index, product in enumerate(review_products):
                # 未修改商品不能生成调价单
                if product.is_modify == 0:
                    continue

                row = dict(
                    id=ids[index],
                    partner_id=partner_id,
                    created_by=user_id,
                    created_name=username,
                    updated_by=user_id,
                    updated_name=username,
                    adjust_id=adjust_id,
                    adjust_code=adjust_code,
                    product_id=product.product_id,
                    product_code=product.product_code,
                    product_name=product.product_name,
                    product_category_id=product.product_category_id,
                    product_type=product.product_type,
                    unit_id=product.unit_id,
                    unit_name=product.unit_name,
                    unit_spec=product.unit_spec,
                    quantity=product.quantity,
                    actual_quantity=product.actual_quantity,
                    tax_rate=product.tax_rate,
                    price=product.price,
                    pre_price=product.orig_price,
                    sum_price=product.sum_price,
                    pre_sum_price=product.orig_sum_price,
                    price_tax=product.price_tax,
                    pre_price_tax=product.orig_price_tax,
                    sum_price_tax=product.sum_price_tax,
                    pre_sum_price_tax=product.orig_sum_price_tax
                )
                adjust_detail.append(row)
        return adjust_order, adjust_detail

    def get_price_adjust_logs(self, review_id, partner_id, product_ids=None, order=None, sort=None):
        """根据复核单id拉取调价logs, 默认按更新时间倒序"""
        db_session = session_maker()
        try:
            query = db_session.query(PurchaseReviewPriceAdjustLog).filter(
                PurchaseReviewPriceAdjustLog.partner_id == partner_id)
            if review_id:
                query = query.filter(PurchaseReviewPriceAdjustLog.main_id == review_id)
            if product_ids and isinstance(product_ids, list):
                query = query.filter(PurchaseReviewPriceAdjustLog.product_id.in_(product_ids))
            if sort:
                if order == "asc":
                    query_str = "PurchaseReviewPriceAdjustLog.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "PurchaseReviewPriceAdjustLog.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(PurchaseReviewPriceAdjustLog.created_at)
                else:
                    query = query.order_by(PurchaseReviewPriceAdjustLog.created_at.desc())
            return query.all()
        except Exception as e:
            logging.error("Get Price Adjust Logs Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                         traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_by_id(self, primary_id):
        db_session = session_maker()
        try:
            q = db_session.query(PurchaseReviewOrder).filter(PurchaseReviewOrder.id==primary_id)
            return q.first()
        except Exception as e:
            raise e
        finally:
            db_session.close()


class SendCostCenterBatch(DeclarativeBase, MethodMixin, TimestampMixin):
    """同步成本中心数据缓存批次控制表"""
    __tablename__ = "send_cost_center_batch"

    class Status(object):
        INITED = 'INITED'  # 待同步
        SUCCESS = 'SUCCESS'  # 已同步

    """
        batch_type->单据类型：
        采购复核调价: PURCHASE_ADJUST
        加工单: PROCESSING
        包装单: PACKING
        物料转换单: MATERIAL_CONVERT
        加工费用分摊表: PROCESSING_COST
    """

    id = Column(BigInteger, primary_key=True, default=get_guid)
    batch_id = Column(BigInteger, unique=True)  # 批次id这里存单据id做唯一限制
    batch_type = Column(String(50))             # 批次单据类型
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.utcnow(), onupdate=datetime.utcnow())
    data = Column(TEXT)    # 待同步数据
    status = Column(String, default=Status.INITED)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)

    @classmethod
    def add_one_record(cls, data):
        """添加一条记录"""
        db_session = session_maker()
        try:
            obj = cls()
            for k, v in data.items():
                if k in obj.__table__.columns.keys() and v is not None:
                    setattr(obj, k, v)
            db_session.add(obj)
            db_session.commit()
        except Exception as e:
            logging.error("Add Batch Record Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                   traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    @classmethod
    def get(cls, batch_id):
        """获取一条记录"""
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.batch_id == batch_id)
            return query.first()
        except Exception as e:
            logging.error("Get Batch Record Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                   traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    @classmethod
    def update(cls, data):
        """更新一条记录"""
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(cls, [data])
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Get Batch Record Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                   traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    @classmethod
    def list_record(cls, batch_ids=None, status=None, partner_id=None):
        """查询批次列表补偿时用"""
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            if batch_ids:
                query = query.filter(cls.batch_id.in_(batch_ids))
            if status:
                query = query.filter(cls.status == status)
            return query.all()
        except Exception as e:
            logging.error("List Batch Record Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                    traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()


purchase_review_order_db = PurchaseReviewOrderDB()
