# -*- coding: utf-8 -*-
from sqlalchemy import text, func, or_, and_

from .inventory import inventory_repository
from ..driver.mysql import DummyTransaction
from ..driver.mysql import session_maker
from datetime import datetime, timedelta
import logging, sys, traceback
from . import DeclarativeBase, TimestampMixin
from sqlalchemy import (Column, BigInteger, Integer, String, Boolean, DateTime, DECIMAL, TEXT)
from ..module import stocktake_service
from sqlalchemy.orm.exc import NoResultFound
from supply.utils.helper import get_guid, set_db, set_model_from_db, update_db, \
    convert_to_int, convert_to_datetime, convert_to_decimal, datetime2str, get_branch_map, get_product_map
import json
from google.protobuf.timestamp_pb2 import Timestamp
from ..error.exception import *
from ..client.metadata_service import metadata_service
from ..client.inventory_service import inventory_service
from ..client.products_manage_service import products_manage_service
# from ..task import public
from ..utils.helper import MessageTopic
from ..client.bom_service import Bom_service
from ..task.message_service_pub import MessageServicePub
from supply import time_cost
from supply.model.metadata import StoreDB
from supply.module.inventory_handle import inventory_handler
from supply.driver.mq import mq_producer
from ..utils.inventory_enum import ACTION
from supply.module.franchisee.helper import *


class AsDict(object):
    def as_dict(self, mapping_args=None):
        if mapping_args:
            dict_data = {}
            for c in self.__table__.columns:
                mapping_name = mapping_args.get(c.name)
                if mapping_name:
                    dict_data[mapping_name] = getattr(self, mapping_name)
        else:
            dict_data = {c.name: getattr(self, c.name) for c in self.__table__.columns}
        if "extends" in dict_data and dict_data['extends'] and isinstance(dict_data['extends'], String):
            # load string to json
            try:
                dict_data['extends'] = json.loads(dict_data['extends'])
            except Exception as e:
                dict_data['extends'] = dict(error=e)
        return dict_data


class SupplySTDocDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_st_doc'
    id = Column(BigInteger, primary_key=True, )
    branch_batch_id = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    schedule_id = Column(BigInteger)
    branch_id = Column(BigInteger)
    batch_id = Column(BigInteger)
    code = Column(String(50))
    type = Column(String(10))
    target_date = Column(DateTime)
    status = Column(String(10))
    process_status = Column(String(10))
    forecasting = Column(Boolean)
    forecasting_time = Column(DateTime)
    calculate_inventory = Column(Boolean, default=False)


class SupplySTDocDetailsDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = 'supply_st_doc_details'
    id = Column(BigInteger, primary_key=True, default=get_guid)
    branch_batch_id = Column(BigInteger)
    doc_id = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    schedule_id = Column(BigInteger)
    branch_id = Column(BigInteger)
    branch_type = Column(String(10))
    batch_id = Column(BigInteger)
    code = Column(String(50))
    schedule_code = Column(String(50))
    type = Column(String(10))
    result_type = Column(String(10))
    target_date = Column(DateTime)
    status = Column(String(10))
    process_status = Column(String(10))
    store_secondary_id = Column(String(50))
    review_by = Column(BigInteger)
    calculate_inventory = Column(Boolean, default=False)
    forecasting = Column(Boolean)
    forecasting_time = Column(DateTime)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    remark = Column(String(255))
    st_diff_flag = Column(Integer)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))
    schedule_name = Column(String(50))
    diff_err_message = Column(TEXT)
    month_err_message = Column(TEXT)
    original_code = Column(String(50))
    original_doc_id = Column(BigInteger)
    is_recreate = Column(Boolean, default=False)
    recreate_code = Column(String(50))
    recreate_doc_id = Column(BigInteger)
    submit_name = Column(String(50))
    approve_name = Column(String(50))
    stocktake_type = Column(String(50))
    attachments = Column(TEXT)  # 附件 "{"attachments": [ats1, ats2,...]}"
    total_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税盘点金额总数
    total_diff_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税盘点差异金额总数
    total_sales_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税盘点零售金额总数
    total_diff_sales_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税盘点零售金额总数


class SupplySTProductDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = "supply_st_product"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    doc_id = Column(BigInteger)
    product_id = Column(BigInteger)
    product_code = Column(String(50))
    product_name = Column(String(50))
    unit_id = Column(BigInteger)
    unit_name = Column(String(50))
    unit_spec = Column(String(50))
    accounting_unit_id = Column(BigInteger)
    accounting_unit_name = Column(String(50))
    accounting_unit_spec = Column(String(50))
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    accounting_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    inventory_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    diff_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    unit_diff_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    material_number = Column(String(25))
    item_number = Column(Integer)
    ignored = Column(Boolean, default=False)
    deleted = Column(Boolean, default=False)
    is_system = Column(Boolean, default=False)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    extends = Column(String(255))
    storage_type = Column(String(10))
    branch_id = Column(BigInteger)
    target_date = Column(DateTime)
    status = Column(String(10))
    is_pda = Column(Boolean)
    is_empty = Column(Boolean)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))
    is_null = Column(Boolean, default=False)
    # 标签聚合盘点数量
    tag_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    # bom折算前核算数量
    convert_accounting_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    # 是否是bom
    is_bom = Column(Boolean, default=False)
    position_id = Column(BigInteger)
    tax_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 税率
    tax_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税单价(从价格中心取)
    cost_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 成本价格
    amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税金额=含税单价*数量
    diff_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 差异金额=含税单价*差异数量
    sales_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税零售价格
    sales_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 含税零售金额=含税零售价格*数量
    diff_sales_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 差异金额=含税单价*差异数量


class SupplySTProductTagNameDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = "supply_st_product_tag_name"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    doc_id = Column(BigInteger)
    stp_id = Column(BigInteger)
    product_id = Column(BigInteger)
    tag_id = Column(BigInteger)
    tag_name = Column(String(50))
    tag_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    accounting_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    unit_id = Column(BigInteger)
    unit_name = Column(String(50))
    unit_spec = Column(String(50))
    unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    accounting_unit_id = Column(BigInteger)
    accounting_unit_name = Column(String(50))
    accounting_unit_spec = Column(String(50))
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))


class SupplySTDocLogDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = "supply_st_doc_log"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    doc_id = Column(BigInteger)
    doc_status = Column(String(25))
    reason = Column(String(255))
    created_by = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)


class StocktakeTagsDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = "supply_st_product_tags"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    name = Column(String(50))
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))
    branch_id = Column(BigInteger)


class StocktakeMonthMessageDB(DeclarativeBase, TimestampMixin, AsDict):
    __tablename__ = "supply_st_month_message"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    doc_id = Column(BigInteger)
    status = Column(String(25))
    target_date = Column(DateTime)
    month_message = Column(TEXT)
    diff_message = Column(TEXT)
    partner_id = Column(BigInteger, index=True)
    user_id = Column(BigInteger)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)


class StocktakeProductImportRecordDB(DeclarativeBase, TimestampMixin, AsDict):
    """盘点单商品导入文件记录"""
    __tablename__ = 'supply_st_product_import_record'

    class STATUS(object):
        INIT = 'INIT'  # 初始状态,待确认
        CANCEL = 'CANCEL'  # 取消
        CONFIRM = 'CONFIRM'  # 确认

    id = Column(BigInteger, primary_key=True, default=get_guid)
    user_id = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)
    doc_id = Column(BigInteger)
    filename = Column(String)
    file_data = Column(TEXT)
    status = Column(String, default=STATUS.INIT)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    created_name = Column(String)
    updated_name = Column(String)


class StockTakeRepository(object):

    def __init__(self):
        pass

    def get_timestamp(self, value: datetime):
        timestamp = Timestamp()
        timestamp.FromDatetime(value)
        return timestamp

    def get_datetime(self, value):
        timestamp = Timestamp()
        timestamp.seconds = value.seconds
        date = timestamp.ToDatetime()
        if date == datetime(1970, 1, 1):
            return None
        return date

    def get_stocktake_doc_business_time(self, doc_id):
        db_session = session_maker()
        try:
            doc_log_db = db_session.query(SupplySTDocLogDB).filter(
                SupplySTDocLogDB.doc_id == doc_id,
                SupplySTDocLogDB.doc_status == 'APPROVED').first()
            return doc_log_db
        except NoResultFound as e:
            pass
        except Exception as e:
            raise e
        finally:
            db_session.close()

    def get_stocktake_doc_by_id(self, doc_id, partner_id=None, is_details=False):
        db_session = session_maker()
        try:
            doc_db = db_session.query(SupplySTDocDetailsDB).filter(
                SupplySTDocDetailsDB.doc_id == doc_id) if is_details else db_session.query(
                SupplySTDocDB).filter(
                SupplySTDocDB.id == doc_id)
            if partner_id:
                doc_db = doc_db.filter(SupplySTDocDetailsDB.partner_id == partner_id) if is_details else doc_db.filter(
                    SupplySTDocDB.partner_id == partner_id)
            doc_db = doc_db.first()
            if doc_db:
                doc_obj = stocktake_service.StockTakeDocDetail() if is_details else stocktake_service.StockTakeDoc()
                set_model_from_db(doc_db.as_dict(), doc_obj)
                return doc_obj
        except NoResultFound as e:
            pass
        except Exception as e:
            raise e
        finally:
            db_session.close()
        return None

    def get_product_ids_by_stocktake_id(self, doc_id, partner_id):
        product_ids = []
        db_session = session_maker()
        try:
            product_db_list = db_session.query(SupplySTProductDB).filter(SupplySTProductDB.doc_id == doc_id).filter(
                SupplySTProductDB.partner_id == partner_id).filter(SupplySTProductDB.deleted == False).all()
            if product_db_list is not None and len(product_db_list) > 0:
                for product_db in product_db_list:
                    if product_db and product_db.product_id and int(product_db.product_id) not in product_ids:
                        product_ids.append(int(product_db.product_id))
        except NoResultFound as e:
            pass
        except Exception as e:
            raise e
        finally:
            db_session.close()
        return product_ids

    def get_stocktake_log(self, doc_id, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(SupplySTDocLogDB).filter(SupplySTDocLogDB.doc_id == doc_id,
                                                              SupplySTDocLogDB.partner_id == partner_id)
            query = query.order_by(SupplySTDocLogDB.created_at.desc())
            adjust_logs = query.all()
            if not adjust_logs:
                return 0, []
            query_count = db_session.query(func.count(SupplySTDocLogDB.id))
            if partner_id:
                query_count = query_count.filter(SupplySTDocLogDB.partner_id == partner_id)
            if doc_id:
                query_count = query_count.filter(SupplySTDocLogDB.doc_id == doc_id)
            count = query_count.scalar()
            return count, adjust_logs
        except NoResultFound:
            pass
        except Exception as e:
            logging.error(
                "** method[%s] error. Error message:%s **" % ('get_stocktake_log', e))
            raise e
        finally:
            db_session.close()
        return None

    def get_create_stocktake_doc_details(self, partner_id=None, target_date=None, branch_ids=None):
        db_session = session_maker()
        try:
            query = db_session.query(SupplySTDocDetailsDB).filter(SupplySTDocDetailsDB.partner_id == partner_id,
                                                                  SupplySTDocDetailsDB.target_date == target_date,
                                                                  SupplySTDocDetailsDB.branch_id.in_(branch_ids))

            list_stocktake_detail_db = query.all()
            stocktake_list = []
            if list_stocktake_detail_db is not None and len(list_stocktake_detail_db) > 0:
                for stocktake_db in list_stocktake_detail_db:
                    stocktake_obj = stocktake_service.StockTakeDocDetail()
                    set_model_from_db(stocktake_db.as_dict(), stocktake_obj)
                    stocktake_obj.id = stocktake_obj.doc_id
                    del stocktake_obj.doc_id
                    stocktake_list.append(stocktake_obj.props())
            return stocktake_list
        except Exception as e:
            raise e
        finally:
            db_session.close()

    def list_stocktake_doc_details(self, partner_id=None, limit=None, offset=None, include_total=False,
                                   target_date=None,
                                   branch_ids=None, branch_type=None, status=None, ids=None, start_date=None,
                                   end_date=None, schedule_code=None, code=None, _type=None, order=None, sort=None,
                                   stocktake_type=None, product_ids=None):
        db_session = session_maker()
        try:
            query = db_session.query(SupplySTDocDetailsDB)
            if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                query = query.outerjoin(SupplySTProductDB, SupplySTDocDetailsDB.doc_id == SupplySTProductDB.doc_id)
                query = query.filter(SupplySTProductDB.product_id.in_(product_ids))
            if partner_id:
                query = query.filter(SupplySTDocDetailsDB.partner_id == partner_id)
            if branch_type:
                query = query.filter(SupplySTDocDetailsDB.branch_type == branch_type)
            if isinstance(ids, list) and len(ids) > 0:
                query = query.filter(SupplySTDocDetailsDB.doc_id.in_(ids))
            if isinstance(target_date, datetime):
                query = query.filter(SupplySTDocDetailsDB.target_date == target_date)
            if isinstance(start_date, datetime) and not target_date and start_date.year != 1970:
                query = query.filter(SupplySTDocDetailsDB.target_date >= start_date)
            if isinstance(end_date, datetime) and not target_date and end_date.year != 1970:
                query = query.filter(SupplySTDocDetailsDB.target_date <= end_date)
            if isinstance(schedule_code, list):
                if len(schedule_code) > 0:
                    query = query.filter(SupplySTDocDetailsDB.schedule_code.in_(schedule_code))
            elif schedule_code:
                query = query.filter(SupplySTDocDetailsDB.schedule_code == schedule_code)
            if branch_ids and isinstance(branch_ids, list):
                query = query.filter(SupplySTDocDetailsDB.branch_id.in_(branch_ids))
            if status:
                query = query.filter(SupplySTDocDetailsDB.status == status) if not isinstance(status,
                                                                                              list) else query.filter(
                    SupplySTDocDetailsDB.status.in_(status))
            if code:
                query = query.filter(SupplySTDocDetailsDB.code == code)
            if stocktake_type:
                query = query.filter(SupplySTDocDetailsDB.stocktake_type == stocktake_type)
            if isinstance(_type, list):
                query = query.filter(SupplySTDocDetailsDB.type.in_(_type))
            if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                query = query.group_by(SupplySTDocDetailsDB.doc_id)
            if sort:
                if order != 'asc':
                    order_sort = 'SupplySTDocDetailsDB.{}.desc()'.format(sort)
                    query = query.order_by(eval(order_sort))
                else:
                    order_sort = 'SupplySTDocDetailsDB.{}'.format(sort)
                    query = query.order_by(eval(order_sort))
            else:
                query = query.order_by(SupplySTDocDetailsDB.updated_at.desc())
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            list_stocktake_detail_db = query.all()
            stocktake_list = []
            if list_stocktake_detail_db is not None and len(list_stocktake_detail_db) > 0:
                for stocktake_db in list_stocktake_detail_db:
                    stocktake_obj = stocktake_service.StockTakeDocDetail()
                    # logging.info("stocktake_db {}".format(stocktake_db.as_dict()))
                    set_model_from_db(stocktake_db.as_dict(), stocktake_obj)
                    # logging.info("stocktake_obj {}".format(stocktake_obj.total_amount))
                    stocktake_obj.id = stocktake_obj.doc_id
                    stocktake_obj.attachments = json.loads(stocktake_obj.attachments).get('attachments') if stocktake_obj.attachments else None
                    # stocktake_obj.total_amount = stocktake_db.as_dict().get('total_amount',0)
                    # stocktake_obj.total_diff_amount = stocktake_db.as_dict().get('total_diff_amount',0)
                    # stocktake_obj.total_sales_amount =  stocktake_db.as_dict().get('total_sales_amount',0)
                    del stocktake_obj.doc_id
                    stocktake_list.append(stocktake_obj.props())
            if include_total:
                query_total = db_session.query(SupplySTDocDetailsDB.id)
                if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                    query_total = query_total.outerjoin(SupplySTProductDB,
                                                        SupplySTDocDetailsDB.doc_id == SupplySTProductDB.doc_id)
                    query_total = query_total.filter(SupplySTProductDB.product_id.in_(product_ids))
                if partner_id:
                    query_total = query_total.filter(SupplySTDocDetailsDB.partner_id == partner_id)
                if branch_type:
                    query_total = query_total.filter(SupplySTDocDetailsDB.branch_type == branch_type)
                if isinstance(ids, list) and len(ids) > 0:
                    query_total = query_total.filter(SupplySTDocDetailsDB.doc_id.in_(ids))
                if isinstance(target_date, datetime):
                    query_total = query_total.filter(SupplySTDocDetailsDB.target_date == target_date)
                if isinstance(start_date, datetime) and end_date.year != 1970:
                    query_total = query_total.filter(SupplySTDocDetailsDB.target_date >= start_date)
                if isinstance(end_date, datetime) and end_date.year != 1970:
                    query_total = query_total.filter(SupplySTDocDetailsDB.target_date <= end_date)
                if isinstance(schedule_code, list):
                    if len(schedule_code) > 0:
                        query_total = query_total.filter(SupplySTDocDetailsDB.schedule_code.in_(schedule_code))
                elif schedule_code:
                    query_total = query_total.filter(SupplySTDocDetailsDB.schedule_code == schedule_code)
                if branch_ids and isinstance(branch_ids, list):
                    query_total = query_total.filter(SupplySTDocDetailsDB.branch_id.in_(branch_ids))
                if status:
                    query_total = query_total.filter(SupplySTDocDetailsDB.status == status) if not isinstance(status,
                                                                                                              list) else query_total.filter(
                        SupplySTDocDetailsDB.status.in_(status))
                if code:
                    query_total = query_total.filter(SupplySTDocDetailsDB.code == code)
                if stocktake_type:
                    query_total = query_total.filter(SupplySTDocDetailsDB.stocktake_type == stocktake_type)
                if isinstance(_type, list):
                    query_total = query_total.filter(SupplySTDocDetailsDB.type.in_(_type))
                if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                    query_total = query_total.group_by(SupplySTDocDetailsDB.doc_id)
                count = query_total.count()
                return count, stocktake_list
            return stocktake_list
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @time_cost
    def list_stocktake_doc_details_by_ids(self, ids, partner_id=None):
        if not partner_id:
            raise DataValidationException("缺失租户信息")
        if not ids:
            return []
        db_session = session_maker()
        try:
            sql_str = '''select 
                            doc_id, 
                            code, 
                            branch_id,
                            status,
                            target_date,
                            remark,
                            branch_type,
                            created_at,
                            updated_at,
                            `type`,
                            stocktake_type,
                            remark,
                            schedule_name,
                            schedule_code,
                            schedule_id,
                            st_diff_flag
                        from supply_st_doc_details 
                        where 
                            partner_id = {}
                        AND
                            doc_id in ({})'''.format(partner_id, ','.join([str(_id) for _id in ids]))
            query_rows = db_session.execute(text(sql_str)).fetchall()
            return query_rows
        except Exception as e:
            raise e
        finally:
            db_session.close()

    def query_st_product_sum_qty(self, doc_ids=None, partner_id=None, ):
        db_session = session_maker()
        try:
            query = db_session.query(SupplySTProductDB.doc_id, func.sum(SupplySTProductDB.quantity)).filter(
                SupplySTProductDB.partner_id == partner_id)
            if doc_ids and isinstance(doc_ids, list):
                query = query.filter(SupplySTProductDB.doc_id.in_(doc_ids))
            query = query.group_by(SupplySTProductDB.doc_id)
            return query.all()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def recalculate_stocktake_total_amount(self, doc_id):
        """重新计算一个盘点单supply_st_doc_details的total_amount/total_sales_amount/total_diff_amount
        """
        if type(doc_id) is int:
            with DummyTransaction(auto_commit=False) as trans:
                sql_str = '''
                UPDATE supply_st_product
                SET diff_amount=IFNULL(tax_price,0) * IFNULL(diff_quantity,0), amount=IFNULL(tax_price,0) * IFNULL(accounting_quantity,0),sales_amount=IFNULL(sales_price,0) * IFNULL(accounting_quantity,0),diff_sales_amount=IFNULL(sales_price,0) * IFNULL(diff_quantity,0)
                
                WHERE doc_id={};
                '''.format(int(doc_id))
                trans.scope_session.execute(text(sql_str))
                sql_str = '''
                UPDATE supply_st_doc_details
                SET total_amount=IFNULL((SELECT SUM(IFNULL(amount,0)) FROM supply_st_product WHERE doc_id=supply_st_doc_details.doc_id AND deleted=0
    ),0), total_diff_amount=IFNULL((SELECT SUM(IFNULL(diff_amount,0)) FROM supply_st_product WHERE doc_id=supply_st_doc_details.doc_id AND deleted=0
    ),0), total_sales_amount=IFNULL((SELECT SUM(IFNULL(sales_amount,0)) FROM supply_st_product WHERE doc_id=supply_st_doc_details.doc_id AND deleted=0
    ),0),total_diff_sales_amount=IFNULL((SELECT SUM(IFNULL(diff_sales_amount,0)) FROM supply_st_product WHERE doc_id=supply_st_doc_details.doc_id AND deleted=0
    ),0)
                WHERE doc_id={};
                '''.format(int(doc_id))
                trans.scope_session.execute(text(sql_str))
                trans.scope_session.commit()
        return True

    def list_stocktake_products_by_doc_id(self, doc_id, partner_id=None, limit=None, offset=None, include_total=False,
                                          record_ids=None, diff_top=False, diff_bottom=False, product_ids=None,
                                          is_diff_count=True, get_obj=False, product_unit_adjust_tax_price_map=None,
                                          product_unit_adjust_tax_ratio_map=None,
                                          product_unit_adjust_sales_price_map=None, **kwargs):
        if doc_id is not None:
            db_session = session_maker()
            try:
                query = db_session.query(SupplySTProductDB).filter(SupplySTProductDB.doc_id == doc_id,
                                                                   SupplySTProductDB.deleted == False)
                if isinstance(record_ids, list) and len(record_ids) > 0:
                    query = query.filter(SupplySTProductDB.id.in_(record_ids))
                if partner_id:
                    query = query.filter(SupplySTProductDB.partner_id == partner_id)
                if product_ids and isinstance(product_ids, list) and len(product_ids) > 0:
                    query = query.filter(SupplySTProductDB.product_id.in_(product_ids))
                if not is_diff_count:
                    query = query.filter(SupplySTProductDB.diff_quantity != 0)
                query = query.order_by(SupplySTProductDB.updated_at.desc())
                if diff_top:
                    query = query.order_by('abs(unit_diff_quantity) desc')
                elif diff_bottom:
                    query = query.order_by('abs(unit_diff_quantity) asc')
                else:
                    query = query.order_by(SupplySTProductDB.quantity.desc())

                if offset:
                    query = query.offset(offset)
                if limit:
                    query = query.limit(limit)
                product_db_list = query.all()
                product_list = []
                if product_db_list is not None and len(product_db_list) > 0:
                    stp_ids = []
                    for product_db in product_db_list:
                        # 有标签的增加商品盘点标签
                        if product_db.is_pda == True:
                            stp_ids.append(product_db.id)
                        # 前端需要使用盘点单上保存的旧价格计算并显示金额
                        if kwargs.get('attach_price') is True and product_db.status not in ["INITED", "REJECTED"]:
                            update_adjust_tax_price_tax_rate(
                                product_unit_adjust_tax_price_map=product_unit_adjust_tax_price_map,
                                product_unit_adjust_tax_ratio_map=product_unit_adjust_tax_ratio_map,
                                product_id=product_db.product_id,
                                tax_price=product_db.tax_price, tax_rate=product_db.tax_rate,
                                product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map,
                                sales_price=product_db.sales_price)

                    product_tags_db_list = []
                    logging.info("stp_ids:{}".format(stp_ids))
                    if len(stp_ids) > 0:
                        product_tags_db_list = db_session.query(SupplySTProductTagNameDB). \
                            filter(SupplySTProductTagNameDB.stp_id.in_(stp_ids))
                    product_tags_map = {}
                    logging.info("product_tags_db_list:{}".format(product_tags_db_list))
                    for product_tags_db in product_tags_db_list:
                        product_tags_data = stocktake_service.StockTakeProductTagName()
                        set_model_from_db(product_tags_db.as_dict(), product_tags_data)
                        product_tags_data_dict = product_tags_data.props()
                        if product_tags_map.get(product_tags_db.stp_id):
                            product_tags_map[product_tags_db.stp_id].append(product_tags_data_dict)
                        else:
                            product_tags_map[product_tags_db.stp_id] = [product_tags_data_dict]
                    for product_db in product_db_list:
                        product_data = stocktake_service.StockTakeProduct()
                        set_model_from_db(product_db.as_dict(), product_data)

                        product_tags_db_list = product_tags_map.get(product_db.id, [])
                        product_tags_list = []
                        if len(product_tags_db_list) > 0:
                            for product_tags in product_tags_db_list:
                                product_tags_list.append(product_tags)
                        product_data.product_tags = product_tags_list
                        if get_obj:
                            product_list.append(product_data)
                        else:
                            product_list.append(product_data.props())
                if include_total:
                    query_total = db_session.query(func.count(SupplySTProductDB.id)).filter(
                        SupplySTProductDB.doc_id == doc_id, SupplySTProductDB.deleted == False)
                    if partner_id:
                        query_total = query_total.filter(SupplySTProductDB.partner_id == partner_id)
                    if isinstance(record_ids, list) and len(record_ids) > 0:
                        query_total = query_total.filter(SupplySTProductDB.id.in_(record_ids))
                    if product_ids and isinstance(product_ids, list) and len(product_ids) > 0:
                        query_total = query_total.filter(SupplySTProductDB.product_id.in_(product_ids))
                    if not is_diff_count:
                        query_total = query_total.filter(SupplySTProductDB.diff_quantity != 0)
                    count = query_total.scalar()
                    return count, product_list
                return product_list
            except Exception as e:
                raise e
            finally:
                db_session.close()
        if include_total:
            return 0, []
        else:
            return []

    def update_stocktake_products_with_price(self, doc_id, products):
        db_session = session_maker()
        try:
            # 更新product
            if len(products) > 0:
                # quantity可更新为null，o，值
                db_session.bulk_update_mappings(SupplySTProductDB, products)
            db_session.commit()
            self.recalculate_stocktake_total_amount(doc_id)
            return True
        except NoResultFound as e:
            pass
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
        return False

    def update_stocktake_products_with_quantity(self, doc_id, products, product_tags_exist=None, product_tags=None,
                                                allow_status=None, partner_id=None, user_id=None, username=None,
                                                update_o_product_tags_ids=None, del_tag_ids=None, attachments=None):
        db_session = session_maker()
        try:
            doc_detail_db = db_session.query(SupplySTDocDetailsDB).filter(
                SupplySTDocDetailsDB.doc_id == doc_id,
                SupplySTDocDetailsDB.partner_id == partner_id).with_lockmode("update").first()
            if not doc_detail_db:
                return False
            if not isinstance(allow_status, list):
                return False
            if isinstance(allow_status, list) and doc_detail_db.status not in allow_status:
                return False
            # doc_db.updated_at = datetime.now()
            # doc_detail_db = db_session.query(SupplySTDocDetailsDB).filter(
            #     SupplySTDocDetailsDB.doc_id == doc_id).with_lockmode(
            #     "update").first()
            doc_detail_db.updated_at = datetime.utcnow()
            if user_id:
                doc_detail_db.updated_by = user_id
            if username:
                doc_detail_db.updated_name = username
            if attachments is not None:
                doc_detail_db.attachments = json.dumps(dict(attachments=attachments))
            # 更新product
            if len(products) > 0:
                # quantity可更新为null，o，值
                db_session.bulk_update_mappings(SupplySTProductDB, products)
            # 查处盘点商品标签,存在更新
            if len(product_tags_exist) > 0:
                db_session.bulk_update_mappings(SupplySTProductTagNameDB, product_tags_exist)
            # 不存在插入
            if len(product_tags) > 0:
                db_session.bulk_insert_mappings(SupplySTProductTagNameDB, product_tags)
            # 新功能如果有需要删除商品下的标签，按id删除
            if len(del_tag_ids) > 0:
                db_session.query(SupplySTProductTagNameDB).filter(
                    SupplySTProductTagNameDB.id.in_(del_tag_ids)).delete(synchronize_session=False)
            # 清除pda切换pc时的pda标签
            if len(update_o_product_tags_ids) > 0:
                db_session.query(SupplySTProductTagNameDB). \
                    filter(SupplySTProductTagNameDB.stp_id.in_(update_o_product_tags_ids)).delete(
                    synchronize_session=False)
            db_session.commit()
            self.recalculate_stocktake_total_amount(doc_id)
            return True
        except NoResultFound as e:
            pass
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
        return False

    def list_doc_product_tags(self, tag_ids=None, doc_id=None, partner_id=None):
        """根据盘点单id/标签列id表查询盘点单中盘点标签记录
        用于盘点单更新，根据每次查出的记录判断是新增还是更新防止重复
        """
        db_session = session_maker()
        try:
            query = db_session.query(SupplySTProductTagNameDB)
            if partner_id:
                query = query.filter(SupplySTProductTagNameDB.partner_id == partner_id)
            if tag_ids:
                query = query.filter(SupplySTProductTagNameDB.tag_id.in_(tag_ids))
            if doc_id:
                query = query.filter(SupplySTProductTagNameDB.doc_id == doc_id)
            return query.all()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def lock_stocktake_status(self, doc_id, current_status, status, user_id=None, partner_id=None, username=None,
                              reason=None, products=None, submit_name=None, approve_name=None):
        db_session = session_maker()
        try:
            q = db_session.query(SupplySTDocDB).filter(SupplySTDocDB.id == doc_id,
                                                       SupplySTDocDB.partner_id == partner_id)
            if current_status:
                if not isinstance(current_status, list):
                    q = q.filter(SupplySTDocDB.status == current_status)
                elif isinstance(current_status, list) and len(current_status) > 0:
                    q = q.filter(SupplySTDocDB.status.in_(current_status))
            doc_db = q.with_lockmode("update").first()
            if doc_db:
                doc_db.status = status
                doc_db.process_status = status
                doc_db.updated_at = datetime.utcnow()
                if user_id:
                    doc_db.updated_by = user_id
                if username:
                    doc_db.updated_name = username
                doc_obj = stocktake_service.StockTakeDoc()
                set_model_from_db(doc_db.as_dict(), doc_obj)
                doc_detail_update = {SupplySTDocDetailsDB.status: status, SupplySTDocDetailsDB.process_status: status,
                                     SupplySTDocDetailsDB.updated_at: datetime.utcnow()}
                if user_id:
                    doc_detail_update.update({SupplySTDocDetailsDB.updated_by: user_id})
                if username:
                    doc_detail_update.update({SupplySTDocDetailsDB.updated_name: username})
                if submit_name:
                    doc_detail_update.update({SupplySTDocDetailsDB.submit_name: submit_name})
                if approve_name:
                    doc_detail_update.update({SupplySTDocDetailsDB.approve_name: approve_name})
                if reason:
                    doc_detail_update.update({SupplySTDocDetailsDB.remark: reason})
                db_session.query(SupplySTDocDetailsDB).filter(SupplySTDocDetailsDB.doc_id == doc_id,
                                                              SupplySTDocDetailsDB.partner_id == partner_id).update(
                    doc_detail_update)
                # if products:
                # 财务驳回的更新
                # doc_product_update={SupplySTProductDB.status:status,SupplySTProductDB.updated_at: datetime.now()}
                # for p in products:
                #     db_session.query(SupplySTProductDB).filter(SupplySTProductDB.id == p).update(
                #         doc_product_update)
                if products is None:
                    doc_product_update = {SupplySTProductDB.status: status,
                                          SupplySTProductDB.updated_at: datetime.utcnow()}
                    db_session.query(SupplySTProductDB).filter(SupplySTProductDB.doc_id == doc_id,
                                                               SupplySTProductDB.partner_id == partner_id).update(
                        doc_product_update)

                doc_log_db = SupplySTDocLogDB()
                doc_log_db.id = get_guid()
                doc_log_db.doc_id = doc_id
                doc_log_db.partner_id = doc_db.partner_id
                doc_log_db.doc_status = status
                doc_log_db.created_at = datetime.utcnow()
                if user_id:
                    doc_log_db.created_by = user_id
                if reason:
                    doc_log_db.reason = reason
                db_session.add(doc_log_db)
                db_session.commit()
                return doc_obj
            else:
                return None
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def check_stocktake_products_number(self, doc_id):
        db_session = session_maker()
        try:
            if doc_id is None:
                return False
            query = db_session.query(func.count(SupplySTProductDB.id)).filter(SupplySTProductDB.doc_id == doc_id,
                                                                              SupplySTProductDB.deleted == False,
                                                                              SupplySTProductDB.quantity < 0)
            count = query.scalar()
            if count > 0:
                return False
            else:
                return True
        except Exception as e:
            raise e
        finally:
            db_session.close()

    def convert_bom_stocktake(self, doc_id, partner_id=None, user_id=None, username=None):
        #######################################################
        # 计算商品的所有核算数量
        # 盘点结算前需要先进行异步结算
        ###异步bom未实现，先循环同步调用拆解
        #######################################################
        stocktake = self.get_stocktake_doc_by_id(doc_id, partner_id)
        logging.debug('------stocktake:%s' % stocktake)
        if not stocktake:
            return
        products = self.list_stocktake_products_by_doc_id(doc_id, partner_id, get_obj=True)
        if products is None or not isinstance(products, list) or len(products) == 0:
            return
        filters = {'allow_stocktake': True}
        # product_filters = {"product_type__in": ["SEMI-FINISHED"]}
        product_fields = ["product_type", "bom_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            return_fields=['id'],
            # product_filters=product_filters,
            include_product_fields=product_fields,
            store_id=stocktake.branch_id,
            filters=filters,
            partner_id=partner_id,
            user_id=user_id
        )
        is_bom_products_list = []
        if ret:
            for data in ret.get('rows'):
                if data['bom_type'] == "MANUFACTURE":
                    is_bom_products_list.append(int(data.get('product_id', 1)))
            # is_bom_products_list = [int(data.get('product_id', 1)) for data in ret.get('rows')]
        print('is_bom_products_list', is_bom_products_list)
        message = {}
        bom_products_map = {}
        for p in products:
            if p.unit_id is None or p.quantity is None:
                p.quantity = 0
            if p.unit_rate is None or p.unit_rate == 0:
                p.unit_rate = 1
            if p.is_pda:
                p.tag_quantity = 0
                p.accounting_quantity = 0
                for product_tag in p.product_tags:
                    p.accounting_quantity += product_tag.get('accounting_quantity', 0)
                p.tag_quantity = p.accounting_quantity / p.unit_rate
            else:
                p.accounting_quantity = p.unit_rate * p.quantity
            # product_dict={}
            # product_dict['product_id']=p.product_id
            # product_dict['product_qty'] = p.accounting_quantity
            # param_products.append((product_dict))
            if p.product_id in is_bom_products_list and p.accounting_quantity:
                param_dict = dict(
                    request_id=get_guid(),
                    store_id=stocktake.branch_id,
                    store_code=None,
                    sales_date=str(stocktake.target_date),
                    biz_code="STOCKTAKE",
                    biz_no=str(stocktake.id),
                    product_id=p.product_id,
                    product_code=None,
                    product_qty=abs(p.accounting_quantity),
                    partner_id=partner_id,
                    user_id=user_id
                )
                ret = Bom_service.get_bom(**param_dict)
                print('get_bom_ret*****', ret)
                if ret:
                    p.is_bom = True
                    if int(ret.get('request_id')) == int(param_dict['request_id']) and ret.get('bom'):
                        boms = ret["bom"]
                        for bom in boms:
                            product_id = str(bom.get('product_id'))
                            qty = bom.get('qty', 0)
                            if bom_products_map.get(product_id):
                                bom_products_map[product_id] += qty
                            else:
                                bom_products_map[product_id] = qty
        # 更新核算和tag数量
        db_session = session_maker()
        try:
            for p in products:
                if p.deleted:
                    continue
                update_products = {SupplySTProductDB.tag_quantity: p.tag_quantity,
                                   SupplySTProductDB.is_bom: p.is_bom,
                                   SupplySTProductDB.quantity: p.quantity, SupplySTProductDB.unit_rate: p.unit_rate,
                                   SupplySTProductDB.accounting_quantity: p.accounting_quantity,
                                   SupplySTProductDB.status: 'CONVERTING',
                                   SupplySTProductDB.updated_name: username,
                                   SupplySTProductDB.updated_at: datetime.utcnow(),
                                   SupplySTProductDB.updated_by: p.updated_by}
                db_session.query(SupplySTProductDB).filter(SupplySTProductDB.id == p.id).update(update_products,
                                                                                                synchronize_session=False)
            update = {SupplySTProductDB.updated_at: datetime.utcnow()}
            if user_id:
                update.update({SupplySTDocDetailsDB.updated_by: user_id})
            if username:
                update.update({SupplySTDocDetailsDB.updated_name: username})
            db_session.query(SupplySTDocDetailsDB).filter(
                SupplySTDocDetailsDB.doc_id == doc_id).update(update, synchronize_session=False)
            db_session.commit()
            # bom折算处理，异步消息
            message['doc_id'] = doc_id
            message['bom_products_map'] = bom_products_map
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            logging.info('bom_products_map***************' + str(bom_products_map))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.STOCKTAKE_BOM_TOPIC,
                                message=message)
            # self.finalize_store_stocktake(doc_id,bom_products_map,partner_id=partner_id,user_id=user_id)
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def finalize_store_stocktake(self, doc_id, bom_products_map, partner_id=None, user_id=None):
        #######################################################
        # 用商品折算后数量进行差异核算，库存扣减，盘点数量发JDE
        # 用商品折算后数量进行差异核算
        # 用商品折算后数量进行差异核算
        #######################################################
        stocktake = self.get_stocktake_doc_by_id(doc_id, partner_id=partner_id, is_details=True)
        # 查提审时间
        log_obj = self.get_stocktake_doc_business_time(doc_id)
        business_time = log_obj.created_at
        logging.debug('------stocktake:%s' % stocktake)
        if not stocktake:
            return
        products = self.list_stocktake_products_by_doc_id(doc_id, get_obj=True)
        if products is None or not isinstance(products, list) or len(products) == 0:
            return self.lock_stocktake_status(doc_id, 'APPROVED', 'CONFIRMED', user_id=user_id, partner_id=partner_id)
        # 取得商品的实时库存map
        dict_product = inventory_service.get_products_inventory_by_branch_id(
            branch_id=stocktake.branch_id,
            partner_id=partner_id,
            user_id=user_id
        )
        logging.debug('------盘点商品补全数据------开始--')
        st_diff_flag = 0
        product_ids = []
        for p in products:
            #######################################################
            # 取bom折算后的数量累加到原商品核算数量，折算商品折算数量为0，剔除不计库存商品不做库存查询和扣减
            # 其中bom_products_dict数据重构成map，取数量进行累加
            #######################################################
            product_ids.append(p.product_id)
            if bom_products_map.get(str(p.product_id)):
                p.convert_accounting_quantity = convert_to_decimal(
                    bom_products_map.get(str(p.product_id))) + p.accounting_quantity
            else:
                p.convert_accounting_quantity = p.accounting_quantity
            if p.is_bom:
                p.convert_accounting_quantity = None
            p_inventory_obj = None
            # 取得商品库存
            if str(p.product_id) in dict_product:
                p_inventory_obj = dict_product[str(p.product_id)]
            if p_inventory_obj is not None:
                p.inventory_quantity = convert_to_decimal(p_inventory_obj.get('quantity_avail'))
            if p.inventory_quantity is None:
                p.inventory_quantity = 0
            # 用商品折算后数量进行差异核算,bom商品无差异
            if p.convert_accounting_quantity:
                p.diff_quantity = p.convert_accounting_quantity - p.inventory_quantity
            else:
                if p.is_bom:
                    p.diff_quantity = 0
                else:
                    p.diff_quantity = p.accounting_quantity - p.inventory_quantity
            p.unit_diff_quantity = convert_to_decimal(p.diff_quantity) / convert_to_decimal(p.unit_rate)
            p.updated = datetime.utcnow()
            if user_id:
                p.updated_by = user_id
            # if st_diff_flag == 0 and p.diff_quantity == 0:
            #     st_diff_flag = 0
            # elif st_diff_flag <= 0 and p.diff_quantity <= 0:
            #     st_diff_flag = -1
            # elif st_diff_flag >= 0 and p.diff_quantity >= 0:
            #     st_diff_flag = 1
            # elif (st_diff_flag <= 0 and p.diff_quantity >= 0) or (st_diff_flag >= 0 and p.diff_quantity <= 0):
            #     st_diff_flag = 2
        logging.debug('------盘点商品补全数据 ------结束--')
        username = metadata_service.get_username_by_pid_uid(partner_id=partner_id, user_id=user_id)
        ACCOUNTS = []
        stocktake_differ_store_code_ret = metadata_service.get_store(store_id=stocktake.branch_id, return_fields='code',
                                                                     partner_id=partner_id, user_id=user_id)
        stocktake_store_code = stocktake_differ_store_code_ret.get('code')

        # 获取半成品
        # product_filters = {"product_type__in": ["SEMI-FINISHED"]}
        product_fields = ["product_type", "bom_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            product_ids=product_ids,
            return_fields=['id'],
            # product_filters=product_filters,
            include_product_fields=product_fields,
            store_id=stocktake.branch_id,
            partner_id=partner_id,
            user_id=user_id
        ).get('rows', [])
        is_bom_products_list = []
        if len(ret) > 0:
            for data in ret:
                if data['bom_type'] == "MANUFACTURE":
                    is_bom_products_list.append(int(data.get('product_id', 1)))
        # 初始化jde消息
        s_type = {'D': 1, 'W': 2, 'M': 3, 'R': 4}
        diff_message = {
            "stocktake_differ_type": s_type.get(stocktake.type),
            "stocktake_differ_date": stocktake.target_date.strftime("%Y-%m-%d"),
            "stocktake_differ_code": stocktake.code,
            "stocktake_differ_store_code": stocktake_store_code,
            "stocktake_differ_products": []
        }
        month_message = {
            "stocktake_code": stocktake.code,
            "stocktake_store_code": stocktake_store_code,
            "stocktake_month_date": stocktake.target_date.strftime("%Y-%m-%d"),
            "stocktake_products": []
        }
        db_session = session_maker()
        try:
            logging.debug('------盘点商品更新折算数据 ------开始--')
            index = 1
            for p in products:
                if p.deleted:
                    continue
                update_products = {
                    SupplySTProductDB.convert_accounting_quantity: p.convert_accounting_quantity,
                    SupplySTProductDB.inventory_quantity: p.inventory_quantity,
                    SupplySTProductDB.diff_quantity: p.diff_quantity,
                    SupplySTProductDB.status: 'CONFIRMED',
                    SupplySTProductDB.unit_diff_quantity: p.unit_diff_quantity,
                    SupplySTProductDB.updated_at: datetime.utcnow(),
                    SupplySTProductDB.updated_by: p.updated_by,
                    SupplySTProductDB.updated_name: username
                }
                db_session.query(SupplySTProductDB).filter(SupplySTProductDB.id == p.id).update(update_products,
                                                                                                synchronize_session=False)
                if stocktake.calculate_inventory:
                    # 加库存
                    # if p.diff_quantity > 0 and (p.product_id not in is_bom_products_list):
                    #     DEPOSIT_ACCOUNTS.append(dict(branch_id=stocktake.branch_id, product_id=p.product_id,
                    #                                  action=2,
                    #                                  amount=str(abs(p.diff_quantity))))
                    acc_data = dict(branch_id=stocktake.branch_id, product_id=p.product_id,
                                    amount=str(float(f'{abs(p.convert_accounting_quantity):.8f}')))
                    if p.convert_accounting_quantity == 0:
                        acc_data['amount'] = '0'
                    if p.position_id != 1 and p.position_id:
                        acc_data['sub_account'] = {"id": p.position_id}
                    ACCOUNTS.append(acc_data)
                    # 减库存
                    # elif p.diff_quantity < 0 and (p.product_id not in is_bom_products_list):
                    #     WITHDRAW_ACCOUNTS.append(dict(branch_id=stocktake.branch_id, product_id=p.product_id,
                    #                                   action=1,
                    #                                   amount=str(abs(p.diff_quantity))))
                diff = abs(p.diff_quantity) if p.diff_quantity else 0
                if p.diff_quantity and diff >= 0.0005 and (p.product_id not in is_bom_products_list):
                    diff_message_product = {}
                    diff_message_product['LITM'] = p.product_code
                    diff_message_product['UOM'] = p.accounting_unit_spec
                    diff_message_product['UORG'] = str(p.diff_quantity)
                    diff_message_product['KTLN'] = str(index)
                    diff_message["stocktake_differ_products"].append(diff_message_product)
                    index += 1
                # 月盘点单推送给JDE
                if stocktake.type == "M" and (p.product_id not in is_bom_products_list):
                    stocktake_product_message = {}
                    stocktake_product_message['LITM'] = p.product_code
                    stocktake_product_message['TQCT'] = str(
                        p.convert_accounting_quantity) if p.convert_accounting_quantity else '0'
                    stocktake_product_message['UOM'] = p.accounting_unit_spec
                    # if p.accounting_quantity != 0 and p.accounting_quantity != None:
                    month_message["stocktake_products"].append(stocktake_product_message)
            logging.debug('------盘点商品更新数据 ------结束--')
            update = {SupplySTDocDetailsDB.st_diff_flag: st_diff_flag, SupplySTProductDB.updated_at: datetime.utcnow()}
            if user_id:
                update.update({SupplySTDocDetailsDB.updated_by: user_id})
            logging.debug('------更新盘点详情----开始')
            db_session.query(SupplySTDocDetailsDB).filter(
                SupplySTDocDetailsDB.doc_id == doc_id).update(update, synchronize_session=False)
            logging.debug('------更新盘点详情----结束')

            if stocktake.calculate_inventory:
                logging.debug('------盘点商品加、减库存----开始')
                # 合并
                # ACCOUNTS = WITHDRAW_ACCOUNTS + DEPOSIT_ACCOUNTS
                if ACCOUNTS and len(ACCOUNTS) > 0:
                    code = 'STOCKTAKE'
                    if stocktake.type == "M":
                        code = 'STOCKTAKE-M'
                    elif stocktake.type == "W":
                        code = 'STOCKTAKE-W'
                    elif stocktake.type == "D":
                        code = 'STOCKTAKE-D'
                    elif stocktake.stocktake_type == "R":
                        code = 'STOCKTAKE-R'
                    elif stocktake.stocktake_type == "MANUAL":
                        code = 'STOCKTAKE-H'
                    # 异步处理库存
                    message = dict(batch_no=str(doc_id),
                                   code=code,
                                   action=13,
                                   business_time=str(business_time),
                                   description=code,
                                   trace_id=stocktake.code,
                                   accounts=ACCOUNTS,
                                   partner_id=partner_id,
                                   user_id=user_id)
                    inventory_dict = dict(batch_no=str(doc_id), code=code, batch_action=13,
                                          action_dec=ACTION[13], status='ERROR')
                    inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                     partner_id=partner_id, user_id=user_id)
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                        message=message)
                    # 实时处理库存
                    # # batch_id = inventory_handler()
                    # if not batch_id:
                    #     raise ActionException('结算盘点单失败')
            # 月盘点单推送给JDE并且滞后推送jde
            # if stocktake.type == "M" and stocktake.calculate_inventory:
            #     dictMerged = month_message.copy()
            #     dictMerged.update(diff_message)
            #     # 月盘点单推送给JDE并且滞后推送jde
            #     stocktake_message_db = StocktakeMonthMessageDB()
            #     stocktake_message_db.id = get_guid()
            #     stocktake_message_db.doc_id = stocktake.doc_id
            #     stocktake_message_db.partner_id = partner_id
            #     stocktake_message_db.target_date = stocktake.target_date
            #     stocktake_message_db.status = 'INITED'
            #     # stocktake_message_db.month_message = month_stocktake_message
            #     # stocktake_message_db.diff_message = differ_stocktake_message
            #     stocktake_message_db.created_at = datetime.utcnow()
            #     stocktake_message_db.updated_at = datetime.utcnow()
            #     db_session.add(stocktake_message_db)
            q = db_session.query(SupplySTDocDB).filter(SupplySTDocDB.id == doc_id,
                                                       SupplySTDocDB.partner_id == partner_id,
                                                       SupplySTDocDB.status == "APPROVED")
            doc_db = q.with_lockmode("update").first()
            status = "CONFIRMED"
            if doc_db:
                doc_db.status = status
                doc_db.process_status = status
                doc_db.updated_at = datetime.utcnow()
                if user_id:
                    doc_db.updated_by = user_id
                if username:
                    doc_db.updated_name = username
                doc_obj = stocktake_service.StockTakeDoc()
                set_model_from_db(doc_db.as_dict(), doc_obj)
                doc_detail_update = {SupplySTDocDetailsDB.status: status,
                                     SupplySTDocDetailsDB.process_status: status,
                                     SupplySTDocDetailsDB.updated_at: datetime.utcnow(),
                                     SupplySTDocDetailsDB.updated_by: user_id,
                                     SupplySTDocDetailsDB.updated_name: username}

                db_session.query(SupplySTDocDetailsDB).filter(SupplySTDocDetailsDB.doc_id == doc_id,
                                                              SupplySTDocDetailsDB.partner_id == partner_id).update(
                    doc_detail_update)
                doc_log_db = SupplySTDocLogDB()
                doc_log_db.id = get_guid()
                doc_log_db.doc_id = doc_id
                doc_log_db.partner_id = doc_db.partner_id
                doc_log_db.doc_status = status
                doc_log_db.created_at = datetime.utcnow()
                doc_log_db.created_by = user_id
                db_session.add(doc_log_db)
                db_session.commit()
                return doc_obj
            else:
                return stocktake
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_stocktake_tags(self, branch_ids=None, tag_name=None, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(StocktakeTagsDB).filter(StocktakeTagsDB.partner_id == partner_id)
            if branch_ids:
                query = query.filter(StocktakeTagsDB.branch_id.in_(branch_ids))
            if tag_name:
                query = query.filter(StocktakeTagsDB.name == tag_name)
            return_obj_list = []
            return_dbs = query.all()
            if return_dbs:
                for return_db in return_dbs:
                    return_obj = stocktake_service.StockTakeProductTags()
                    set_model_from_db(return_db.as_dict(), return_obj)
                    return_obj_list.append(return_obj.props())
            return return_obj_list
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_a_stocktake_tags(self, tag_id=None, partner_id=None, user_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(StocktakeTagsDB)
            if partner_id and tag_id:
                tag_db = query.filter(StocktakeTagsDB.partner_id == partner_id, StocktakeTagsDB.id == tag_id).first()
                if tag_db:
                    return_obj = stocktake_service.StockTakeProductTags()
                    set_model_from_db(tag_db.as_dict(), return_obj)
                    return return_obj
                else:
                    return None
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_a_stocktake_tags(self, obj, partner_id=None, user_id=None):
        with DummyTransaction(auto_commit=False) as trans:
            stocktake_tags_db = trans.scope_session.query(StocktakeTagsDB).filter(
                StocktakeTagsDB.name == obj.name,
                StocktakeTagsDB.branch_id == obj.branch_id,
                StocktakeTagsDB.partner_id == partner_id).first()
            if not stocktake_tags_db:
                stocktake_tag_db = StocktakeTagsDB()
                set_db(stocktake_tag_db, obj.no_timestamp_props())
                stocktake_tag_db.id = get_guid()
                if partner_id:
                    stocktake_tag_db.partner_id = partner_id
                if user_id:
                    stocktake_tag_db.user_id = stocktake_tag_db.created_by = stocktake_tag_db.updated_by = user_id
                stocktake_tag_db.created_at = stocktake_tag_db.updated_at = datetime.utcnow()
                trans.scope_session.add(stocktake_tag_db)
                set_model_from_db(stocktake_tag_db.as_dict(), obj)
                trans.scope_session.commit()
                return obj
            else:
                raise DataValidationException("标签名字不能重复")

    def create_stocktake_tags(self, tag_data_list=None):
        """lelecha批量创建盘点标签
        上边的lelecha不用了
        :param tag_data_list: 已经过滤掉已经存在待更新标签的门店"""
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(StocktakeTagsDB, tag_data_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def delete_a_stocktake_tags(self, tag_ids=None, partner_id=None):
        if not tag_ids:
            return True
        with DummyTransaction(auto_commit=False) as trans:
            trans.scope_session.query(StocktakeTagsDB).filter(
                StocktakeTagsDB.id.in_(tag_ids),
                StocktakeTagsDB.partner_id == partner_id).delete(synchronize_session=False)
            trans.scope_session.commit()
            return True

    def update_a_stocktake_tags(self, obj, partner_id=None, user_id=None):
        with DummyTransaction(auto_commit=False) as trans:
            stocktake_tags_db = trans.scope_session.query(StocktakeTagsDB).filter(
                StocktakeTagsDB.id == obj.id,
                StocktakeTagsDB.partner_id == partner_id).first()
            if stocktake_tags_db:
                if stocktake_tags_db.name == obj.name:
                    raise DataValidationException("标签名字不能重复")
                stocktake_tags_db.user_id = user_id
                stocktake_tags_db.name = obj.name
                trans.scope_session.add(stocktake_tags_db)
                trans.scope_session.commit()
                obj = stocktake_service.StockTakeProductTags()
                set_model_from_db(stocktake_tags_db.as_dict(), obj)
                return obj
            else:
                return None

    def update_stocktake_tags(self, tag_data_list=None):
        """批量更新盘点标签
        上边的lelecha不用了
        :param tag_data_list: 已经过滤掉已经存在待更新标签的门店"""
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(StocktakeTagsDB, tag_data_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def clean_stocktake_product_tags(self, tag_ids, partner_id=None):
        if not tag_ids:
            return False
        if partner_id == None:
            raise DataValidationException("没有租户id,partner_id")
        tag_ids = ','.join([str(tag_id) for tag_id in tag_ids])
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
                        DELETE from supply_st_product_tag_name WHERE id in ({})
                        and partner_id={}
                    '''.format(tag_ids, partner_id)
        trans.scope_session.execute(text(sql_text))
        trans.scope_session.commit()
        return True

    def get_stocktake_balance(self, partner_id, start_date=None, end_date=None, store_ids=None,
                              limit=None, offset=None, target_date=None, status="CONFIRMED", store_secondary_ids=None,
                              include_total=False, schedule_code=None, stocktake_type=None, store_info=None,
                              branch_type=None, cycle_type=None):
        db_session = session_maker()
        try:
            query = db_session.query(SupplySTDocDetailsDB)
            if partner_id:
                query = query.filter(SupplySTDocDetailsDB.partner_id == partner_id)
            if branch_type:
                query = query.filter(SupplySTDocDetailsDB.branch_type == branch_type)
            if isinstance(target_date, datetime):
                query = query.filter(SupplySTDocDetailsDB.target_date == target_date)
            if isinstance(start_date, datetime) and not target_date:
                query = query.filter(SupplySTDocDetailsDB.target_date >= start_date)
            if isinstance(end_date, datetime) and not target_date:
                query = query.filter(SupplySTDocDetailsDB.target_date <= end_date)
            if schedule_code:
                query = query.filter(SupplySTDocDetailsDB.schedule_code == schedule_code)
            if status:
                query = query.filter(SupplySTDocDetailsDB.status == status) if not isinstance(status,
                                                                                              list) else query.filter(
                    SupplySTDocDetailsDB.status.in_(status))
            if stocktake_type:
                query = query.filter(SupplySTDocDetailsDB.stocktake_type == stocktake_type)
            if cycle_type:
                query = query.filter(SupplySTDocDetailsDB.type == cycle_type)
            if store_ids:
                query = query.filter(SupplySTDocDetailsDB.branch_id.in_(store_ids))
            query = query.order_by(SupplySTDocDetailsDB.updated_at.desc())
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            list_stocktake_detail_db = query.all()
            stocktake_list = []
            if list_stocktake_detail_db is not None and len(list_stocktake_detail_db) > 0:
                for stocktake_db in list_stocktake_detail_db:
                    stocktake_obj = stocktake_service.StockTakeDocDetail()
                    set_model_from_db(stocktake_db.as_dict(), stocktake_obj)
                    stocktake_obj.id = stocktake_obj.doc_id
                    stocktake_obj.attachments = json.loads(stocktake_obj.attachments).get(
                        'attachments') if stocktake_obj.attachments else None
                    del stocktake_obj.doc_id
                    props = stocktake_obj.props()
                    store_ = store_info.get(props['branch_id']) if props.get('branch_id') else None
                    props['branch_code'] = store_[0] if store_ else None
                    props['branch_name'] = store_[1] if store_ else None
                    stocktake_list.append(props)
            if include_total:
                query_total = db_session.query(func.count(SupplySTDocDetailsDB.id))
                if partner_id:
                    query_total = query_total.filter(SupplySTDocDetailsDB.partner_id == partner_id)
                if branch_type:
                    query_total = query_total.filter(SupplySTDocDetailsDB.branch_type == branch_type)
                if isinstance(store_secondary_ids, list) and len(store_secondary_ids) > 0:
                    query_total = query_total.filter(SupplySTDocDetailsDB.store_secondary_id.in_(store_secondary_ids))
                if isinstance(target_date, datetime):
                    query_total = query_total.filter(SupplySTDocDetailsDB.target_date == target_date)
                if isinstance(start_date, datetime):
                    query_total = query_total.filter(SupplySTDocDetailsDB.target_date >= start_date)
                if isinstance(end_date, datetime):
                    query_total = query_total.filter(SupplySTDocDetailsDB.target_date <= end_date)
                if schedule_code:
                    query_total = query_total.filter(SupplySTDocDetailsDB.schedule_code == schedule_code)
                if store_ids:
                    query_total = query_total.filter(SupplySTDocDetailsDB.branch_id.in_(store_ids))
                if status:
                    query_total = query_total.filter(SupplySTDocDetailsDB.status == status) if not isinstance(status,
                                                                                                              list) else query_total.filter(
                        SupplySTDocDetailsDB.status.in_(status))
                if stocktake_type:
                    query_total = query_total.filter(SupplySTDocDetailsDB.stocktake_type == stocktake_type)
                if cycle_type:
                    query_total = query_total.filter(SupplySTDocDetailsDB.type == cycle_type)
                count = query_total.scalar()
                return count, stocktake_list
            return stocktake_list
        except Exception as e:
            raise e
        finally:
            db_session.close()

    def stocktake_bi_detailed(self, partner_id=None, store_ids=None, product_ids=None, start_date=None,
                              end_date=None, limit=None, offset=None, include_total=False, cycle_type=None,
                              user_id=None, stocktake_type=None, order=None, sort=None, branch_type=None,
                              with_tag=False):
        tag_rate = '(tag.accounting_quantity / stp.accounting_quantity)'
        select_sql = f"""
                SELECT 
                st.doc_id,
                st.code AS code,
                
                st.type AS type,
                st.target_date AS target_date,
                st.branch_id,
                
                stp.product_id,
                stp.product_code AS product_code,
                stp.product_name AS product_name,
                
                {'stp.quantity' if not with_tag else 'tag.tag_quantity'} AS quantity,
                {'stp.tag_quantity' if not with_tag else 'tag.tag_quantity'} as tag_quantity,
                stp.inventory_quantity {'' if not with_tag else f' * {tag_rate}'} AS inventory_quantity,
                
                {'stp.unit_id' if not with_tag else 'tag.unit_id'} as unit_id,
                {'stp.unit_name' if not with_tag else 'tag.unit_name'} as unit_name,
                stp.accounting_unit_id,
                stp.accounting_unit_name,
                stp.convert_accounting_quantity {'' if not with_tag else f' * {tag_rate}'} AS accounting_quantity,
                
                stp.diff_quantity {'' if not with_tag else f' * {tag_rate}'} AS diff_quantity,
                stp.is_pda,
                st.created_at,
                st.submit_name,
                log_s.created_at,
                st.approve_name,
                stp.unit_rate,
                stp.position_id,
                st.stocktake_type AS stocktake_type,
                st.schedule_code,
                st.schedule_name,
                st.status,
                st.updated_at,
                st.updated_name,
                stp.id
        """
        sub_base_sql = f"""
                {{}}
                from supply_st_doc_details as st 
                INNER JOIN supply_st_product stp ON st.doc_id=stp.doc_id
                LEFT JOIN supply_st_doc_log log_s ON st.doc_id=log_s.doc_id AND log_s.doc_status='SUBMITTED'
                {'LEFT JOIN supply_st_product_tag_name tag on tag.stp_id = stp.id' if with_tag else ''}
                WHERE {{}}
                {{}}
        """
        base_sub_sql = """
                {}
                from supply_st_doc_details as st 
                INNER JOIN supply_st_product stp ON st.doc_id=stp.doc_id
                WHERE {}
                {}
        """
        group_by_sql = f"""
                GROUP BY  st.doc_id,st.code,st.type,st.target_date,st.branch_id,
                stp.product_id,stp.product_code,stp.product_name,
                stp.unit_id,stp.unit_name,stp.accounting_unit_id,stp.accounting_unit_name,stp.is_pda,
                stp.quantity,stp.tag_quantity,stp.convert_accounting_quantity,stp.diff_quantity,stp.inventory_quantity,
                stp.position_id{'' if not with_tag else ',tag.id'}
        """
        total_group_by_sql = """"""
        # GROUP BY stp.is_pda
        total_select_sql = f"""
                SELECT count(*),
                IF(stp.is_pda=1,SUM({'stp.tag_quantity' if not with_tag else 'tag.accounting_quantity'}),
                SUM({'stp.quantity' if not with_tag else 'tag.accounting_quantity'})) AS quantity,
                SUM({'stp.convert_accounting_quantity' if not with_tag else 'tag.accounting_quantity'}) AS convert_accounting_quantity
        """
        total_sub_base_sql = f"""
                {{}}
                from supply_st_doc_details as st 
                INNER JOIN supply_st_product stp ON st.doc_id=stp.doc_id
                {'LEFT JOIN supply_st_product_tag_name tag on tag.stp_id = stp.id' if with_tag else ''}
                WHERE {{}}
                {{}}
        """
        #####
        base_sql = sub_base_sql
        if order and sort:
            if sort == 'target_date':
                sort = 'st.target_date'
            if order != 'asc':
                base_sql += " order by -{}".format(sort)
            else:
                base_sql += " order by {}".format(sort)
        if limit:
            base_sql += " limit {}".format(limit)
            if offset:
                base_sql += " offset {}".format(offset)

        doc_date_sql = "st.target_date>='{}' AND st.target_date<'{}'".format(start_date, end_date)
        doc_type_sql = "st.type='{}'".format(cycle_type) if cycle_type else None
        stocktake_type_sql = "st.stocktake_type='{}'".format(stocktake_type) if stocktake_type else None

        product_sql = "stp.product_id in ({})".format(
            ','.join([str(_id) for _id in product_ids])) if product_ids else None
        store_sql = "st.branch_id in ({})".format(
            ','.join([str(_id) for _id in store_ids])) if store_ids else None
        if branch_type:
            branch_type_sql = "st.branch_type='{}'".format(branch_type)
        else:
            branch_type_sql = ''
        where_sql = ' AND '.join(
            [w for w in [doc_date_sql, doc_type_sql, store_sql, product_sql, stocktake_type_sql, branch_type_sql] if
             w]) + " AND st.status='CONFIRMED' AND st.partner_id={}".format(partner_id)

        base_sql = text(base_sql.format(select_sql, where_sql, group_by_sql))
        print('base_sql', base_sql)
        base_total_sub_sql = text(total_sub_base_sql.format(total_select_sql, where_sql, total_group_by_sql))
        with DummyTransaction(auto_commit=False) as trans:
            resproxy = trans.scope_session.execute(base_sql)
            base_rows = resproxy.fetchall()
            total_row = trans.scope_session.execute(base_total_sub_sql).fetchall()
        total = total_row[0][0] if total_row else 0
        product_list = []
        sum_quantity = total_row[0][1] if total_row else 0
        sum_accounting_quantity = total_row[0][2] if total_row else 0
        # 商品单位规格
        product_ids = []
        branch_ids = []
        position_ids = []
        for r in base_rows:
            if int(r[5]) not in product_ids:
                product_ids.append(int(r[5]))
            if r[4] and r[4] not in branch_ids:
                branch_ids.append(r[4])
            if r[23] and r[23] not in position_ids:
                position_ids.append(r[23])
        branch_map = get_branch_map(branch_ids=branch_ids, branch_type=branch_type,
                                    partner_id=partner_id, user_id=user_id)
        position_map = get_branch_map(branch_ids=position_ids, branch_type="POSITION", partner_id=partner_id,
                                      user_id=user_id, return_fields="id,code,name")
        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,model_name,status,storage_type,category",
                                      partner_id=partner_id, user_id=user_id)
        for row in base_rows:
            result_dict = {}
            product = product_map.get(str(row[5])) if product_map.get(str(row[5])) else {}

            branch = branch_map.get(convert_to_int(row[4]))
            result_dict['id'] = row[30]
            result_dict['doc_id'] = row[0]
            result_dict['code'] = row[1]
            result_dict['type'] = row[2]
            result_dict['target_date'] = self.get_timestamp(row[3])
            result_dict['branch_id'] = row[4]
            result_dict['store_code'] = branch.get('code') if isinstance(branch, dict) else ""
            result_dict['store_name'] = branch.get('name') if isinstance(branch, dict) else ""
            result_dict['product_id'] = row[5]
            result_dict['product_code'] = product.get('code')
            result_dict['product_name'] = product.get('name')
            result_dict['category_name'] = product.get('category_name')
            result_dict['position_id'] = row[23] if row[23] else 1
            position = position_map.get(row[23]) if position_map.get(row[23]) else {}
            result_dict['position_name'] = position.get('name')
            result_dict['position_code'] = position.get('code')
            result_dict['stocktake_type'] = row[24]
            result_dict['schedule_code'] = row[25]
            result_dict['schedule_name'] = row[26]
            result_dict['status'] = row[27]
            result_dict['updated_at'] = self.get_timestamp(row[28]) if row[28] else None
            result_dict['updated_name'] = row[29]

            # 商品补充信息
            result_dict['is_enable'] = True if product.get('status') == 'ENABLED' else False
            result_dict['spec'] = product.get('model_name')
            result_dict['storage_type'] = product.get('storage_type')

            result_dict['unit_id'] = row[11]
            result_dict['unit_name'] = row[12]
            result_dict['accounting_unit_id'] = row[13]
            result_dict['accounting_unit_name'] = row[14]
            result_dict['accounting_quantity'] = row[15]
            result_dict['diff_quantity'] = row[16]
            result_dict['is_pda'] = row[17]
            result_dict['inventory_quantity'] = row[10]
            result_dict['diff_percentage'] = row[16] / row[10] if row[10] else 0
            if row[18]:
                result_dict['created_time'] = self.get_timestamp(row[18])
            if row[19]:
                result_dict['created_user_name'] = row[19] if row[19] else ''
            if row[20]:
                result_dict['submitted_time'] = self.get_timestamp(row[20])
            if row[21]:
                result_dict['submitted_user_name'] = row[21] if row[21] else ''
            if row[17]:
                # pda取数
                result_dict['quantity'] = row[9]
            else:
                result_dict['quantity'] = row[8]
            product_list.append(result_dict)
        return product_list, total, sum_quantity, sum_accounting_quantity

    def get_month_stocktake_messages(self, pub_date, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(StocktakeMonthMessageDB)
            if partner_id:
                query = query.filter(StocktakeMonthMessageDB.partner_id == partner_id)
            if isinstance(pub_date, datetime):
                query = query.filter(StocktakeMonthMessageDB.target_date == pub_date,
                                     StocktakeMonthMessageDB.status == 'INITED')
            list_stocktake_message_db = query.all()
            stocktake_list = []
            if list_stocktake_message_db is not None and len(list_stocktake_message_db) > 0:
                for stocktake_db in list_stocktake_message_db:
                    stocktake_obj = stocktake_service.StocktakeMonthMessage()
                    set_model_from_db(stocktake_db.as_dict(), stocktake_obj)
                    stocktake_list.append(stocktake_obj.props())
            return stocktake_list
        except Exception as e:
            raise e
        finally:
            db_session.close()

    def update_month_stocktake_messages(self, id):
        db_session = session_maker()
        try:
            stocktake_month_update = dict()
            stocktake_month_update.update({StocktakeMonthMessageDB.status: 'SUCCESSED'})
            db_session.query(StocktakeMonthMessageDB).filter(
                StocktakeMonthMessageDB.id == id
            ).update(
                stocktake_month_update)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[%s] error. Error message:%s **" % ('update_transfer_product', e))
            raise e
        finally:
            db_session.close()

    @time_cost
    def get_stocktake_doc_list(self, doc_ids):
        db_session = session_maker()
        try:
            query = db_session.query(SupplySTDocDetailsDB).filter(SupplySTDocDetailsDB.doc_id.in_(doc_ids))
            list_stocktake_detail_db = query.all()
            return list_stocktake_detail_db
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[%s] error. Error message:%s **" % ('update_transfer_product', e))
            raise e
        finally:
            db_session.close()

    @time_cost
    def get_stocktake_product_list(self, doc_ids):
        db_session = session_maker()
        try:
            query = db_session.query(SupplySTProductDB).filter(SupplySTProductDB.doc_id.in_(doc_ids),
                                                               SupplySTProductDB.deleted == False)
            product_db_list = query.all()
            return product_db_list
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[%s] error. Error message:%s **" % ('update_transfer_product', e))
            raise e
        finally:
            db_session.close()

    @time_cost
    def recreate_stocktake_doc(self, stocktake_doc_list, stocktake_doc_details_list, stocktake_product_list,
                               update_stocktake_doc_details_list):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(SupplySTDocDB, stocktake_doc_list)
            db_session.bulk_insert_mappings(SupplySTDocDetailsDB, stocktake_doc_details_list)
            db_session.bulk_insert_mappings(SupplySTProductDB, stocktake_product_list)
            db_session.bulk_update_mappings(SupplySTDocDetailsDB, update_stocktake_doc_details_list)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[%s] error. Error message:%s **" % ('update_transfer_product', e))
            raise e
        finally:
            db_session.close()

    def stocktake_doc_statistics(self, store_ids=None, start_date=None, end_date=None, period_group_by=None,
                                 stocktake_type=None, status=None, limit=None, offset=None, order=None, sort=None,
                                 partner_id=None):
        if start_date:
            start_date = self.get_datetime(start_date)
        if end_date:
            end_date = self.get_datetime(end_date)
        format_str_dict = {
            0: '%Y-%m-%d',
            1: '%Y-%m',
            2: '%Y',
        }
        db_session = session_maker()
        try:
            query = db_session.query(
                func.DATE_FORMAT(SupplySTDocDetailsDB.target_date, format_str_dict[period_group_by]).label('date'),
                StoreDB.code,
                StoreDB.name,
                SupplySTDocDetailsDB.type,
                SupplySTDocDetailsDB.status,
                func.count(SupplySTDocDetailsDB.id)
            ).join(StoreDB, StoreDB.id == SupplySTDocDetailsDB.branch_id).filter(
                SupplySTDocDetailsDB.target_date >= start_date,
                SupplySTDocDetailsDB.target_date <= end_date,
                SupplySTDocDetailsDB.status.notin_(['CONFIRMED', 'REJECTED']))
            if partner_id:
                query = query.filter(SupplySTDocDetailsDB.partner_id == partner_id)
            if store_ids:
                query = query.filter(SupplySTDocDetailsDB.branch_id.in_(store_ids))
            if stocktake_type:
                query = query.filter(SupplySTDocDetailsDB.type.in_(stocktake_type))
            if status:
                query = query.filter(SupplySTDocDetailsDB.status.in_(status))
            query = query.group_by(func.DATE_FORMAT(SupplySTDocDetailsDB.target_date, format_str_dict[period_group_by]),
                                   StoreDB.code,
                                   StoreDB.name,
                                   SupplySTDocDetailsDB.type,
                                   SupplySTDocDetailsDB.status)
            query_total = db_session.query(func.count(query.subquery().c.date)).first()[0]
            if not sort:
                sort = 'store_code'
            sort_dict = {'date': 'func.DATE_FORMAT(SupplySTDocDetailsDB.target_date, format_str_dict[period_group_by])',
                         'store_code': 'StoreDB.code', 'store_name': 'StoreDB.name'}
            sort = sort_dict.get(sort, 'store_code')
            if order != 'asc':
                order_sort = '{}.desc()'.format(sort)
                query = query.order_by(eval(order_sort))
            else:
                order_sort = '{}'.format(sort)
                query = query.order_by(eval(order_sort))
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            stocktake_db_list = query.all()
            total = query_total if query_total else 0
            return total, stocktake_db_list
        except Exception as e:
            db_session.rollback()
            logging.error(
                "** method[%s] error. Error message:%s **" % ('update_transfer_product', e))
            raise e
        finally:
            db_session.close()

    def get_need_rehandle_stocktake(self, bus_date, partner_id):
        db_session = session_maker()
        try:
            now = datetime.utcnow() - timedelta(minutes=5)
            sub_bus_date = bus_date - timedelta(days=1)
            stocktake_db = db_session.query(SupplySTDocDetailsDB).filter(SupplySTDocDetailsDB.target_date <= bus_date,
                                                                         SupplySTDocDetailsDB.target_date >= sub_bus_date,
                                                                         SupplySTDocDetailsDB.status == 'APPROVED',
                                                                         SupplySTDocDetailsDB.updated_at <= now,
                                                                         SupplySTDocDetailsDB.partner_id == partner_id).all()
            return stocktake_db
        except Exception as e:
            raise e
        finally:
            db_session.close()

    def get_need_update_stocktake_diff(self, bus_date):
        db_session = session_maker()
        try:
            now = datetime.utcnow() - timedelta(minutes=5)
            sub_bus_date = bus_date - timedelta(days=2)
            stocktake_db = db_session.query(SupplySTDocDetailsDB).filter(SupplySTDocDetailsDB.target_date <= bus_date,
                                                                         SupplySTDocDetailsDB.target_date >= sub_bus_date,
                                                                         SupplySTDocDetailsDB.status == 'CONFIRMED',
                                                                         SupplySTDocDetailsDB.st_diff_flag == 0,
                                                                         SupplySTDocDetailsDB.updated_at <= now).all()
            return stocktake_db
        except Exception as e:
            raise e
        finally:
            db_session.close()

    def update_store_stocktake_diff(self, doc_id, updata_products):
        db_session = session_maker()
        try:
            st_update = dict()
            st_update.update({SupplySTDocDetailsDB.st_diff_flag: 1})
            db_session.query(SupplySTDocDetailsDB).filter(
                SupplySTDocDetailsDB.doc_id == doc_id
            ).update(st_update)
            db_session.bulk_update_mappings(SupplySTProductDB, updata_products)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def add_st_product_import_record(self, data_list, batch, user_id=None, partner_id=None):
        """保存盘点商品导入记录"""
        db_session = session_maker()
        try:
            record_db = StocktakeProductImportRecordDB()
            record_db.id = batch.get('id')
            record_db.doc_id = batch.get('doc_id')
            record_db.filename = batch.get('filename')
            record_db.user_id = user_id
            record_db.partner_id = partner_id
            record_db.created_by = user_id
            record_db.created_name = batch.get('created_name')
            record_db.updated_name = batch.get('updated_name')
            file_data = dict(rows=data_list)
            file_data = json.dumps(file_data)
            record_db.file_data = file_data
            db_session.add(record_db)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            logging.error("** method{} error. Error message:{} **".format('add_st_product_import_record', e))
        finally:
            db_session.close()

    def get_st_product_import_by_batch_id(self, batch_id=None, partner_id=None):
        """查询一条商品导入记录"""
        db_session = session_maker()
        try:
            query = db_session.query(StocktakeProductImportRecordDB).filter(
                StocktakeProductImportRecordDB.partner_id == partner_id)
            if batch_id:
                query = query.filter(StocktakeProductImportRecordDB.id == batch_id)
                return query.first()
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **".format('get_st_product_import_by_batch_id', e))
        finally:
            db_session.close()

    def update_import_batch_status(self, batch_id=None, status=None, user_id=None, partner_id=None):
        """更新导入商品记录"""
        db_session = session_maker()
        try:
            query = db_session.query(StocktakeProductImportRecordDB).filter(
                StocktakeProductImportRecordDB.id == batch_id,
                StocktakeProductImportRecordDB.partner_id == partner_id).with_lockmode("update").first()
            query.update_by = user_id
            query.status = status
            db_session.add(query)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **".format('update_import_batch_status', e))
            raise e
        finally:
            db_session.close()

    def addProductsToStocktakeForPOS(self, doc_id, products, product_tags_exist=None, product_tags=None,
                                     allow_status=None, partner_id=None, user_id=None, username=None,
                                     update_o_product_tags_ids=None, del_tag_ids=None):
        db_session = session_maker()
        try:
            doc_detail_db = db_session.query(SupplySTDocDetailsDB).filter(
                SupplySTDocDetailsDB.doc_id == doc_id,
                SupplySTDocDetailsDB.partner_id == partner_id).with_lockmode("update").first()
            if not doc_detail_db:
                return False
            if not isinstance(allow_status, list):
                return False
            if isinstance(allow_status, list) and doc_detail_db.status not in allow_status:
                return False
            # doc_db.updated_at = datetime.now()
            # doc_detail_db = db_session.query(SupplySTDocDetailsDB).filter(
            #     SupplySTDocDetailsDB.doc_id == doc_id).with_lockmode(
            #     "update").first()
            doc_detail_db.updated_at = datetime.utcnow()
            if user_id:
                doc_detail_db.updated_by = user_id
            if username:
                doc_detail_db.updated_name = username
            # 更新product
            # logging.info("addProductsToStocktakeForPOS products {}".format(products))
            if len(products) > 0:
                # quantity可更新为null，o，值
                db_session.bulk_insert_mappings(SupplySTProductDB, products)
            # 查处盘点商品标签,存在更新
            if len(product_tags_exist) > 0:
                db_session.bulk_update_mappings(SupplySTProductTagNameDB, product_tags_exist)
            # 不存在插入
            if len(product_tags) > 0:
                db_session.bulk_insert_mappings(SupplySTProductTagNameDB, product_tags)
            # 新功能如果有需要删除商品下的标签，按id删除
            if len(del_tag_ids) > 0:
                db_session.query(SupplySTProductTagNameDB).filter(
                    SupplySTProductTagNameDB.id.in_(del_tag_ids)).delete(synchronize_session=False)
            # 清除pda切换pc时的pda标签
            if len(update_o_product_tags_ids) > 0:
                db_session.query(SupplySTProductTagNameDB). \
                    filter(SupplySTProductTagNameDB.stp_id.in_(update_o_product_tags_ids)).delete(
                    synchronize_session=False)
            db_session.commit()
            self.recalculate_stocktake_total_amount(doc_id)
            return True
        except NoResultFound as e:
            pass
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
        return False

    def update_stocktake_products_with_price_for_pos(self, doc_id):
        self.recalculate_stocktake_total_amount(doc_id)
        return True
