# -*- coding:utf-8 -*-
import logging, sys, traceback
from datetime import datetime, date, timedelta
from sqlalchemy import Index, Column, DECIMAL, BigInteger, Integer, String, Text, DateTime, Boolean, UniqueConstraint, func
from sqlalchemy.dialects.postgresql import JSON
from sqlalchemy.orm.exc import NoResultFound
from decimal import Decimal
from google.protobuf.timestamp_pb2 import Timestamp

from supply.model import DeclarativeBase, MethodMixin, TimestampMixin, AsDict
from ..utils.snowflake import gen_snowflake_id
from ..utils.helper import set_db, set_model_from_db, update_db
from ..driver.mysql import session_maker
from supply.error.exception import DataValidationException




class DailyCutLogModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    ''' 
    每日库存切片log
    '''
    __tablename__ = 'supply_daily_cut_log'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 批次请求编号
    batch_no = Column(String(25))
    # 库存id
    inventory_id = Column(BigInteger)
    # 门店id
    store_id = Column(BigInteger)
    # 商品id列表
    product_ids = Column(Text)
    # 
    end_time = Column(DateTime, default=datetime.now)
    # 发送的detail数据
    send = Column(JSON)
    # 返回的detail数据
    ret = Column(JSON)
    # 提交是否成功
    success = Column(Boolean, default=False)
    # 状态：INITED/ SUCCUSSED
    status = Column(String(10))
    # 商户id
    partner_id = Column(BigInteger)
    created_by = Column(BigInteger)
    # 创建人名称
    created_name = Column(String(50))
    # send数量
    send_product_nums = Column(Integer)
    # 处理成功数量
    success_nums = Column(Integer)
    # 处理失败数量
    fail_nums = Column(Integer)
    # receive数量
    receive_nums = Column(Integer)
    # 重发次数
    retry_nums = Column(Integer)
    # branch类型
    branch_type = Column(String(50))


    @classmethod
    def create_new(cls, **kwargs):
        db_session = session_maker()
        try:
            obj = cls()
            for k, v in kwargs.items():
                if k in obj.__table__.columns.keys() and v is not None:
                    setattr(obj, k, v)
            db_session.add(obj)
            db_session.commit()
            return obj
        except Exception as e:
            # db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_log_by_batchNo(cls, batch_no, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            query = query.filter(cls.batch_no == batch_no).filter(cls.partner_id == partner_id)
            return query.first()
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_log(cls, update_log_list):
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(cls, update_log_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
    
    @classmethod
    def get_unfinish_daily_snapshot(cls, minutes, status, max_retry=None, partner_id=None):
        if not partner_id:
            raise DataValidationException("缺失租户信息")
        db_session = session_maker()
        now_date = datetime.now() - timedelta(minutes=minutes)
        try:
            query = db_session.query(cls)
            query = query.filter(cls.status.notin_(status))
            query = query.filter(cls.updated_at >= now_date)
            if max_retry:
                query = query.filter(cls.retry_nums <= max_retry)
            return query.all()
        except Exception as e:
            raise e
        finally:
            db_session.close()
    
    @classmethod
    def update_log_in_all(cls, update_log_list):

        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(cls, update_log_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def create_log_in_all(cls, new_log_list):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(cls, new_log_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('create_log_in_all', e))
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_log_by_ags(cls, store_id, end_time, partner_id=None):
        if not partner_id:
            raise DataValidationException("缺失租户信息")
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            query = query.filter(cls.store_id == store_id).filter(cls.end_time == end_time).filter(cls.partner_id == partner_id)
            return query.first()
        except Exception as e:
            raise e
        finally:
            db_session.close()


class DailyCutStoreModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    __tablename__ = 'supply_daily_cut_store'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 门店id
    store_id = Column(BigInteger)
    # 门店编号
    store_code = Column(String(25))

    @classmethod
    def get_stores(cls):
        try:
            query = session.query(cls)
            stores_db = query.all()
            result = []
            if stores_db and isinstance(stores_db, list) and len(
                    stores_db) > 0:
                for store_detail in stores_db:
                    store_obj = cls()
                    set_model_from_db(store_detail.as_dict(), store_obj)
                    result.append(store_obj)
            return result
        except Exception as e:
            raise e


class CostTriggerLogModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    ''' 
    成本触发log
    '''
    __tablename__ = 'supply_cost_trigger_log'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 批次请求编号
    batch_no = Column(String(25))
    # 分支id
    branch_id = Column(BigInteger)
    # 分支类型：COSTCENTER/ STORE
    branch_type = Column(String(255))
    # 请求类型: material/ bom
    req_type = Column(String(50))
    # 账期id
    period_id = Column(BigInteger)
    # 
    start_time = Column(DateTime, default=datetime.utcnow)
    # 
    end_time = Column(DateTime, default=datetime.utcnow)
    # 发送的detail数据
    sent = Column(String(255))
    # 提交返回的detail数据
    ret = Column(String(255))
    # 返回是否成功
    success = Column(Boolean, default=False)
    # 返回msg
    msg = Column(String(255))
    # 状态：INITED/ SUCCUSSED
    status = Column(String(10))
    # 商户id
    partner_id = Column(BigInteger)
    created_by = Column(BigInteger)

    @classmethod
    def create_log_in_all(cls, log_list):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(cls, log_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('create_log_in_all', e))
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_log_res(cls, batch_no, success, msg=None, partner_id=None, user_id=None, username=None):
        db_session = session_maker()
        try:
            log_db = db_session.query(cls).filter(cls.batch_no == batch_no).with_lockmode(
                "update").first()
            if success:
                update_db(log_db, {'success': success, 'msg': msg, 'status': 'success'})
            else:
                update_db(log_db, {'success': success, 'msg': msg, 'status': 'finish'})
            log_db.updated_at = datetime.now()
            db_session.commit()
            return log_db
        except Exception as e:
            logging.error('Unexpected error: %s', e)
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_trigger_log(cls, start_time=None, end_time=None, period_ids=None, status=None,
                         limit=None, offset=None, partner_id=None, req_types=None):
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            if partner_id:
                query = query.filter(cls.partner_id == partner_id)
            if period_ids and isinstance(period_ids, list) and len(period_ids)>0:
                query = query.filter(cls.period_id.in_(period_ids))
            
            if isinstance(start_time, Timestamp) and start_time.seconds:
                start_time = datetime.utcfromtimestamp(start_time.seconds)
            if isinstance(end_time, Timestamp) and end_time.seconds:
                end_time = datetime.utcfromtimestamp(end_time.seconds)
            if start_time and isinstance(start_time, datetime):
                query = query.filter(cls.start_time >= start_time)
            if end_time and isinstance(end_time, datetime):
                query = query.filter(cls.end_time <= end_time)

            if status:
                query = query.filter(cls.status.in_(status))
            if req_types and isinstance(req_types, list):
                query = query.filter(cls.req_type.in_(req_types))
            
            count = query.count()
            query = query.order_by(cls.updated_at.desc())
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            
            log_list = query.all()
            return count, log_list
        except Exception as e:
            raise e


class CostTriggerPeriodModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    ''' 
    成本触发账期id
    '''
    __tablename__ = 'supply_cost_trigger_period'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 账期id
    branch_id = Column(BigInteger)
    # 分支类型：COSTCENTER/ STORE
    branch_type = Column(String(255))
    # 请求类型: material/ bom
    req_type = Column(String(50))
    # 账期code
    code = Column(String(50))
    # 账期name
    name = Column(String(50))
    # 
    start_time = Column(DateTime, default=datetime.utcnow)
    # 
    end_time = Column(DateTime, default=datetime.utcnow)
    # 状态：ENABLED/ DISABLED
    status = Column(String(10))
    # 商户id
    partner_id = Column(BigInteger)
    created_by = Column(BigInteger)

    @classmethod
    def create_period_in_all(cls, period_list):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(cls, period_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            logging.error("** method[%s] error. Error message:%s **" % ('create_period_in_all', e))
            raise e
        finally:
            db_session.close()

    @classmethod
    def list_periods(cls, start_time=None, end_time=None, period_ids=None, status=None, 
                                branch_id=None, limit=None, offset=None, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            if partner_id:
                query = query.filter(cls.partner_id == partner_id)
            if branch_id:
                query = query.filter(cls.branch_id == branch_id)
            if period_ids and isinstance(period_ids, list) and len(period_ids)>0:
                query = query.filter(cls.id.in_(period_ids))
            if isinstance(start_time, Timestamp) and start_time.seconds:
                start_time = datetime.utcfromtimestamp(start_time.seconds)
            if isinstance(end_time, Timestamp) and end_time.seconds:
                end_time = datetime.utcfromtimestamp(end_time.seconds)
            if start_time and isinstance(start_time, datetime):
                query = query.filter(cls.start_time <= start_time)
            if end_time and isinstance(end_time, datetime):
                query = query.filter(cls.end_time >= end_time)
            if status:
                query = query.filter(cls.status.in_(status))
            count = query.count()
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            
            period_list = query.all()
            return count, period_list
        except Exception as e:
            raise e
