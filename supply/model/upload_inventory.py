# -*- coding: utf-8 -*-
from sqlalchemy import func
from . import DeclarativeBase, TimestampMixin, MethodMixin, AsDict
from supply.driver.mysql import session_maker
from datetime import datetime, timedelta
from sqlalchemy import Column, BigInteger, Integer, String, DateTime, DECIMAL
from supply.utils.snowflake import gen_snowflake_id
from google.protobuf.timestamp_pb2 import Timestamp



class InventoryUploadBatchDB(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    __tablename__ = 'supply_inventory_upload_batch'
    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    partner_id = Column(BigInteger)
    status = Column(String(10))
    filename = Column(String(200))
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    updated_name = Column(String(100))
    branch_type = Column(String(10))

    @classmethod
    def get_inventory_upload_batch(cls, id, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.id == id)
            obj = query.first()
            return obj
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
    
    @classmethod
    def list_inventory_upload_batch(cls, limit, offset, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.partner_id == partner_id)
            count = query.count()
            if limit:
                query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            return count, query.all()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_upload_batch_status(cls, id, status, partner_id=None, user_id=None):
        db_session = session_maker()
        try:
            batch_db = db_session.query(cls).filter(cls.id == id).with_lockmode(
                "update").first()
            batch_db.status = status
            batch_db.updated_by = user_id
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()


class InventoryUploadDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'supply_inventory_upload_details'
    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    partner_id = Column(BigInteger)
    batch_id = Column(BigInteger)
    branch_id = Column(BigInteger)
    branch_code = Column(String(50))
    branch_name = Column(String(50))
    product_id = Column(BigInteger)
    product_code = Column(String(50))
    product_name = Column(String(50))
    row_num = Column(Integer)
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)

    @classmethod
    def get_inventory_upload_batch_details(cls, batch_id, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.batch_id == batch_id)
            obj = query.all()
            return obj
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()


    @classmethod
    def create_upload_details(cls, batch, detail_list, partner_id, user_id):
        db_session = session_maker()
        try:
            if batch and len(batch)>0:
                db_session.bulk_insert_mappings(InventoryUploadBatchDB, batch)
            if detail_list and len(detail_list) > 0:
                db_session.bulk_insert_mappings(cls, detail_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
    
