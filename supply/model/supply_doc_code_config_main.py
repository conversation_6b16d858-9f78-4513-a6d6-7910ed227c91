# -*-: coding:utf-8 -*-

from sqlalchemy import (Column, BigInteger, Integer, String, Text, DateTime)

from supply.utils.snowflake import gen_snowflake_id
from . import TimestampMixin, MethodMixin, DeclarativeBase
from . import DBSession, db_commit


class Supply_doc_code_config_main(DeclarativeBase, TimestampMixin, MethodMixin):

    __tablename__ = "supply_doc_code_config_main"

    name = Column(String)
    type = Column(String)
    prefix = Column(String)
    value_length = Column(BigInteger)
    partner_id = Column(BigInteger)

    @classmethod
    def test_method(cls):
        pass
