from ..driver.mysql import session_maker
import logging, sys, traceback
from ..utils.helper import get_guid
from datetime import datetime
from . import DeclarativeBase
from sqlalchemy import (Column, BigInteger, String, DateTime)
from ..client.metadata_service import metadata_service


class StoreDB(DeclarativeBase):
    __tablename__ = 'metadata_store'
    id = Column(BigInteger, primary_key=True, default=get_guid())
    name = Column(String(50))
    code = Column(String(50))
    second_code = Column(String(50))
    store_type = Column(String(50))
    status = Column(String(50))
    partner_id = Column(BigInteger)
    branch_region_id = Column(BigInteger)
    geo_region_id = Column(BigInteger)
    created = Column(DateTime, default=datetime.now)
    updated = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class ProductDB(DeclarativeBase):
    __tablename__ = 'metadata_product'
    id = Column(BigInteger, primary_key=True, default=get_guid())
    name = Column(String(50))
    code = Column(String(50))
    bom_type = Column(String(50))
    product_type = Column(String(50))
    status = Column(String(50))
    uom_id = Column(BigInteger)
    bom_uom_id = Column(BigInteger)
    partner_id = Column(BigInteger)
    category_id = Column(BigInteger)
    created = Column(DateTime, default=datetime.now)
    updated = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class MetadataRepository(object):

    @classmethod
    def update_store(cls, partner_id=None, user_id=None):
        store_list = []
        store_list_ret = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id)
        if store_list_ret:
            store_list = store_list_ret['rows']
        if not store_list:
            return True
        db_session = session_maker()
        try:
            store_db_list = []
            if isinstance(store_list, list) and len(store_list) > 0:
                db_session.execute('TRUNCATE TABLE metadata_store')
                for st in store_list:
                    store_dict = dict(
                        id=int(st.get('id')),
                        code=st.get('code'),
                        name=st.get('name'),
                        store_type=st.get('store_type'),
                        status=st.get('status'),
                        branch_region_id=int(st.get('branch_region')[0])
                        if st.get('branch_region') and len(st.get('branch_region')) > 0 else None,
                        partner_id=partner_id
                    )
                    store_db_list.append(store_dict)
                db_session.bulk_insert_mappings(StoreDB, store_db_list)
                db_session.commit()
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_product(self, partner_id=None, user_id=None):
        product_list = []
        product_list_ret = metadata_service.get_product_list(partner_id=partner_id, user_id=user_id,
                                                             return_fields='id,code,name,bom_type,'
                                                                           'product_type,status,'
                                                                           'category')
        if product_list_ret:
            product_list = product_list_ret['rows']
        if not product_list:
            return True
        db_session = session_maker()
        try:
            product_db_list = []
            if isinstance(product_list, list) and len(product_list) > 0:
                db_session.execute('TRUNCATE TABLE metadata_product')
                for pr in product_list:
                    product_dict = dict(
                        id=int(pr.get('id')),
                        code=pr.get('code'),
                        name=pr.get('name'),
                        bom_type=pr.get('bom_type'),
                        product_type=pr.get('product_type'),
                        status=pr.get('status'),
                        partner_id=partner_id
                    )
                    if pr.get('category'):
                        product_dict['category_id'] = int(pr.get('category'))
                    product_db_list.append(product_dict)
                db_session.bulk_insert_mappings(ProductDB, product_db_list)
                db_session.commit()
        except Exception as e:
            logging.error('Unexpected error: %s - %s - %s', e, sys.exc_info()[0], traceback.format_exc())
            db_session.rollback()
            raise e
        finally:
            db_session.close()
