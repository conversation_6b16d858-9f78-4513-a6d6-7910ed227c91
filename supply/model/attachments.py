# -*- coding:utf-8 -*-

from datetime import datetime, date, timedelta
from sqlalchemy import Index, Column, DECIMAL, BigInteger, Integer, String, Text, DateTime, Boolean, UniqueConstraint, func
from sqlalchemy.orm.exc import NoResultFound
from decimal import Decimal

from supply.model import DeclarativeBase, MethodMixin, TimestampMixin, AsDict
from ..utils.snowflake import gen_snowflake_id
from ..utils.helper import set_db, set_model_from_db, update_db
from ..driver.mysql import session, session_maker
from google.protobuf.timestamp_pb2 import Timestamp




class AttachmentsModel(DeclarativeBase, TimestampMixin, MethodMixin, AsDict):
    ''' 
    附件统一管理
    '''
    __tablename__ = 'supply_attachments'

    id = Column(BigInteger, primary_key=True, default=gen_snowflake_id)
    # 单据id
    doc_id  = Column(BigInteger)
    # 单据类型
    doc_type = Column(String(25))
    # 附件
    attachment = Column(Text)
    # 数字签名
    signature = Column(Text)
    # 不签名的理由
    nosign_reason = Column(Text)
    # 
    partner_id = Column(BigInteger)
    # 
    updated_by = Column(BigInteger)
    # 
    created_by = Column(BigInteger)
    

    @classmethod
    def create_attachments(cls, **kwargs):
        obj = cls()
        for k, v in kwargs.items():
            if k in obj.__table__.columns.keys() and v is not None:
                setattr(obj, k, v)
        db_session = session_maker()
        try:
            db_session.add(obj)
            db_session.commit()
            return obj
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_attachments_by_doc_id(cls, doc_id, doc_type):
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            query = query.filter(cls.doc_id == doc_id)
            query = query.filter(cls.doc_type == doc_type)
            count = query.count()
            attachment_list = query.all() 
            return count, attachment_list
        except Exception as e:
            raise e

    @classmethod
    def delete_attachments_by_doc_id(cls, doc_id, doc_type):    
        db_session = session_maker()
        try:
            query = db_session.query(cls)
            query = query.filter(cls.doc_id == doc_id)
            query.filter(cls.doc_type == doc_type).delete()
            db_session.commit()
        except Exception as e:
            raise e
        finally:
            db_session.close()

    @classmethod
    def create_attachment_in_all(cls, attachments):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(cls, attachments)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()