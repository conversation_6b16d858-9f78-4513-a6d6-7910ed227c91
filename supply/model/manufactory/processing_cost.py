import logging
import sys
import traceback

from sqlalchemy import text, func, or_, and_, desc, distinct
from supply.driver.mysql import session_maker
from datetime import datetime
from supply.model import DeclarativeBase, TimestampMixin, AsDict
from sqlalchemy import (Column, BigInteger, String, DateTime, DECIMAL, Boolean)
from supply.utils.helper import get_guid


class ProcessingCostDetail(DeclarativeBase, TimestampMixin, AsDict):
    """加工费用详情表
    根据目前业务暂时先用一张表即可满足需求"""
    __tablename__ = "supply_processing_cost_detail"

    class Status(object):
        INITED = 'INITED'           # 新建
        SUBMITTED = 'SUBMITTED'     # 已提交
        APPROVED = 'APPROVED'       # 已审核
        REJECTED = 'REJECTED'       # 已驳回
        DELETED = 'DELETED'         # 已删除

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.utcnow())
    status = Column(String(20), default=Status.INITED)  # 订单状态
    process_status = Column(String(30), default=Status.INITED)   # 处理状态用来标记是否进行分摊计算：INITED/PROCESSING/SUCCESS
    code = Column(String(50), index=True)  # 编号
    month = Column(String(10))  # 生效月份  yyyy-MM
    machining_center_id = Column(BigInteger, index=True)  # 加工中心ID
    machining_center_name = Column(String(50))  # 加工中心名称
    machining_center_code = Column(String(50))  # 加工中心code
    position_id = Column(BigInteger, index=True)  # 仓位id
    position_name = Column(String(50))  # 仓位名称
    position_code = Column(String(50))  # 仓位编码
    unit_cost = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 当月加工单价
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 计价数量
    unit_id = Column(BigInteger)  # 计价单位ID
    unit_name = Column(String(50))  # 计价单位名称
    unit_spec = Column(String(50))  # 计价单位规格
    type = Column(String(20))  # 加工类型*预留*
    opened_position = Column(Boolean)       # 是否开启多仓位
    remark = Column(String(50))  # 备注
    request_id = Column(BigInteger)  # 幂等性校验请求id
    cost_center_id = Column(BigInteger)  # 成本中心id
    period_id = Column(BigInteger)       # 账期ID


class ProcessingCostLog(DeclarativeBase, AsDict):
    """加工费用操作日志表"""
    __tablename__ = "supply_processing_cost_log"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    main_id = Column(BigInteger, index=True)  # 关联的加工费用id
    action = Column(String(50))  # 操作状态
    created_at = Column(DateTime, default=datetime.utcnow())


class ProcessingCostAllocation(DeclarativeBase, AsDict):
    """加工费用分摊表"""
    __tablename__ = "supply_processing_cost_allocation"

    class Status(object):
        INITED = "INITED"    # 计算成功未传输成本
        FAILED = "FAILED"    # 计算失败
        SUCCESS = "SUCCESS"  # 传输成本成功

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.utcnow())
    status = Column(String(20), default='INITED')  # 单据状态 INITED/SUCCESS/FAILED
    machining_center_id = Column(BigInteger, index=True)  # 加工中心ID
    position_id = Column(BigInteger, index=True)  # 仓位id
    product_id = Column(BigInteger)               # 加工物料ID
    processing_unit_id = Column(BigInteger)       # 加工时单位ID
    processing_unit_name = Column(String(50))     # 加工时单位名称
    processing_quantity = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 加工物料数量
    accounting_unit_id = Column(BigInteger)       # 核算单位ID
    accounting_unit_name = Column(String(50))     # 核算单位名称
    accounting_quantity = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 加工物料核算数量
    amounts = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)   # 总加工费用(核算单位)
    cost_rcpt_id = Column(BigInteger)               # 读取的加工费用单id
    cost_center_id = Column(BigInteger)             # 成本中心ID
    remark = Column(String(255))                    # 备注：分摊处理失败原因
    deleted = Column(Boolean, default=0)             # 软删除标记


class ProcessingCostDB(object):
    """加工费用数据库操作"""

    def get_processing_cost_by_id(self, receipt_id=None, request_id=None, partner_id=None):
        """获取一条加工费用记录
        :param receipt_id 加工费用单ID
        :param request_id 唯一请求ID
        :param partner_id
        """
        db_session = session_maker()
        try:
            query = db_session.query(ProcessingCostDetail).filter(ProcessingCostDetail.partner_id == partner_id)
            if receipt_id:
                query = query.filter(ProcessingCostDetail.id == receipt_id)
                return query.first()
            if request_id:
                query = query.filter(ProcessingCostDetail.request_id == request_id)
                return query.first()
        except Exception as e:
            logging.error("Get Processing Cost Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                      traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_processing_cost(self, receipt_detail):
        """保存加工费用和商品详情
        :param receipt_detail 加工费用详情对应数据 detail
        """
        db_session = session_maker()
        try:
            receipt_details = [receipt_detail]
            db_session.bulk_insert_mappings(ProcessingCostDetail, receipt_details)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Processing Cost Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                         traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_processing_cost_log(self, receipts_log):
        """保存加工费用操作日志
        :param receipts_log 操作日志 dict
        """
        db_session = session_maker()
        try:
            receipts_data = [receipts_log]
            db_session.bulk_insert_mappings(ProcessingCostLog, receipts_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Processing Cost Log Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                             traceback.format_exc().replace('\n',
                                                                                                            ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_processing_cost(self, partner_id=None, period_ids=None, start_month=None, end_month=None, code=None,
                             status=None, machining_centers=None, position_ids=None, order=None, sort=None, limit=-1,
                             offset=None, include_total=False, process_status=None):
        """加工费用列表"""
        db_session = session_maker()
        try:
            query = db_session.query(ProcessingCostDetail).filter(ProcessingCostDetail.partner_id == partner_id)
            if start_month:
                query = query.filter(ProcessingCostDetail.month >= start_month)
            if end_month:
                query = query.filter(ProcessingCostDetail.month <= end_month)
            if period_ids and isinstance(period_ids, list):
                query = query.filter(ProcessingCostDetail.period_id.in_(period_ids))
            if code:
                query = query.filter(ProcessingCostDetail.code == code)
            if status and isinstance(status, list):
                query = query.filter(ProcessingCostDetail.status.in_(status))
            if process_status and isinstance(process_status, list):
                query = query.filter(ProcessingCostDetail.process_status.in_(process_status))
            if machining_centers:
                query = query.filter(ProcessingCostDetail.machining_center_id.in_(machining_centers))
            if position_ids:
                query = query.filter(ProcessingCostDetail.position_id.in_(position_ids))
            if sort in ["month", "code", "unit_cost", "quantity"]:
                if order == "asc":
                    query_str = "ProcessingCostDetail.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "ProcessingCostDetail.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(ProcessingCostDetail.updated_at)
                else:
                    query = query.order_by(ProcessingCostDetail.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(ProcessingCostDetail.id)).filter(
                    ProcessingCostDetail.partner_id == partner_id)
                if period_ids and isinstance(period_ids, list):
                    query_total = query_total.filter(ProcessingCostDetail.period_id.in_(period_ids))
                if start_month:
                    query_total = query_total.filter(ProcessingCostDetail.month >= start_month)
                if end_month:
                    query_total = query_total.filter(ProcessingCostDetail.month <= end_month)
                if code:
                    query_total = query_total.filter(ProcessingCostDetail.code == code)
                if status and isinstance(status, list):
                    query_total = query_total.filter(ProcessingCostDetail.status.in_(status))
                if process_status and isinstance(process_status, list):
                    query_total = query_total.filter(ProcessingCostDetail.process_status.in_(process_status))
                if machining_centers:
                    query_total = query_total.filter(
                        ProcessingCostDetail.machining_center_id.in_(machining_centers))
                if position_ids:
                    query_total = query_total.filter(ProcessingCostDetail.position_id.in_(position_ids))
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Processing Cost Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                       traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_processing_cost(self, receipt_detail: dict, update_detail=False, partner_id=None):
        """加工费用状态变更, <这里没有做状态校验>
        :param receipt_detail -> dict 待更新的加工费用detail数据
        :param update_detail bool 是否更新详情
        :param partner_id
        """
        db_session = session_maker()
        try:
            receipt_db = db_session.query(ProcessingCostDetail).filter(
                ProcessingCostDetail.id == receipt_detail.get('id'),
                ProcessingCostDetail.partner_id == partner_id).with_lockmode('update').first()
            receipt_db.updated_by = receipt_detail.get('updated_by')
            receipt_db.updated_name = receipt_detail.get('updated_name')
            receipt_db.updated_at = datetime.utcnow()
            if update_detail is True:
                if receipt_detail.get('machining_center_id'):
                    receipt_db.machining_center_id = receipt_detail.get('machining_center_id')
                if receipt_detail.get('machining_center_name'):
                    receipt_db.machining_center_name = receipt_detail.get('machining_center_name')
                if receipt_detail.get('machining_center_code'):
                    receipt_db.machining_center_code = receipt_detail.get('machining_center_code')
                if receipt_detail.get('position_id'):
                    receipt_db.position_id = receipt_detail.get('position_id')
                if receipt_detail.get('position_name'):
                    receipt_db.position_name = receipt_detail.get('position_name')
                if receipt_detail.get('position_code'):
                    receipt_db.position_code = receipt_detail.get('position_code')
                if receipt_detail.get('month'):
                    receipt_db.month = receipt_detail.get('month')
                if receipt_detail.get('unit_cost'):
                    receipt_db.unit_cost = receipt_detail.get('unit_cost')
                if receipt_detail.get('quantity'):
                    receipt_db.quantity = receipt_detail.get('quantity')
                if receipt_detail.get('unit_id'):
                    receipt_db.unit_id = receipt_detail.get('unit_id')
                if receipt_detail.get('unit_name'):
                    receipt_db.unit_name = receipt_detail.get('unit_name')
                if receipt_detail.get('unit_spec'):
                    receipt_db.unit_spec = receipt_detail.get('unit_spec')
                if receipt_detail.get('type'):
                    receipt_db.type = receipt_detail.get('type')
                if receipt_detail.get('unit_id'):
                    receipt_db.unit_id = receipt_detail.get('unit_id')
                if receipt_detail.get('remark'):
                    receipt_db.remark = receipt_detail.get('remark')
                if receipt_detail.get('opened_position') is not None:
                    receipt_db.opened_position = receipt_detail.get('opened_position')
                if receipt_detail.get('cost_center_id'):
                    receipt_db.cost_center_id = receipt_detail.get('cost_center_id')
            else:
                if receipt_detail.get('process_status'):
                    receipt_db.process_status = receipt_detail.get('process_status')
                if receipt_detail.get('status'):
                    receipt_db.status = receipt_detail.get('status')
            db_session.add(receipt_db)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Processing Cost Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                          traceback.format_exc().replace('\n',
                                                                                                         ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_processing_cost_allocation(self, receipt_details=None, has_error=False, receipt_id=None):
        """保存加工费用分摊
        :param receipt_details: <list> 加工费用分摊对应数据
        :param has_error: <bool> 计算是否全部成功
        :param receipt_id: <int> 加工费用单ID
        整个批次计算成功或失败更新加工费用单放在一个事务
        """
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(ProcessingCostAllocation, receipt_details)
            receipt_db = db_session.query(ProcessingCostDetail).filter(
                ProcessingCostDetail.id == receipt_id).with_lockmode('update').first()
            if has_error is False:
                # 批次计算成功，更新加工费用单process_status状态为SUCCESS
                receipt_db.process_status = "SUCCESS"
            else:
                # 批次计算失败，更新加工费用单process_status状态为PROCESSING
                receipt_db.process_status = "PROCESSING"
            db_session.add(receipt_db)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Processing Cost Allocation Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                                    traceback.format_exc().replace('\n',
                                                                                                                   ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def upsert_processing_cost_allocation(self, add_details=None, update_details=None):
        """保存/更新加工费用分摊数据
        :param add_details: <list> 增加的加工费用分摊对应数据
        :param update_details: <list> 更新复写加工费用分摊对应数据
        整个批次计算成功或失败更新加工费用单放在一个事务
        """
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(ProcessingCostAllocation, add_details)
            db_session.bulk_update_mappings(ProcessingCostAllocation, update_details)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Upsert Processing Cost Allocation Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                                    traceback.format_exc().replace('\n',
                                                                                                                   ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_processing_cost_allocation(self, partner_id=None, status=None, machining_centers=None, position_ids=None,
                                        cost_rcpt_ids=None, product_ids=None, order=None, sort=None, limit=-1,
                                        offset=None, include_total=False, deleted=0):
        """加工费用分摊列表"""
        db_session = session_maker()
        try:
            query = db_session.query(ProcessingCostAllocation).filter(ProcessingCostAllocation.partner_id == partner_id)
            if status and isinstance(status, list):
                query = query.filter(ProcessingCostAllocation.status.in_(status))
            if machining_centers:
                query = query.filter(ProcessingCostAllocation.machining_center_id.in_(machining_centers))
            if cost_rcpt_ids:
                query = query.filter(ProcessingCostAllocation.cost_rcpt_id.in_(cost_rcpt_ids))
            if product_ids:
                query = query.filter(ProcessingCostAllocation.product_id.in_(product_ids))
            if position_ids:
                query = query.filter(ProcessingCostAllocation.position_id.in_(position_ids))
            if deleted is not None:
                query = query.filter(ProcessingCostAllocation.deleted == deleted)
            if sort in ["amounts", "processing_quantity", "accounting_quantity"]:
                if order == "asc":
                    query_str = "ProcessingCostAllocation.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "ProcessingCostAllocation.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(ProcessingCostAllocation.updated_at)
                else:
                    query = query.order_by(ProcessingCostAllocation.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(ProcessingCostAllocation.id)).filter(
                    ProcessingCostAllocation.partner_id == partner_id)
                if status and isinstance(status, list):
                    query_total = query_total.filter(ProcessingCostAllocation.status.in_(status))
                if machining_centers:
                    query_total = query_total.filter(
                        ProcessingCostAllocation.machining_center_id.in_(machining_centers))
                if cost_rcpt_ids:
                    query_total = query_total.filter(ProcessingCostAllocation.cost_rcpt_id.in_(cost_rcpt_ids))
                if product_ids:
                    query_total = query_total.filter(ProcessingCostAllocation.product_id.in_(product_ids))
                if position_ids:
                    query_total = query_total.filter(ProcessingCostAllocation.position_id.in_(position_ids))
                if deleted is not None:
                    query_total = query_total.filter(ProcessingCostAllocation.deleted == deleted)
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Processing Cost Allocation Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                                  traceback.format_exc().replace('\n',
                                                                                                                 ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_processing_cost_allocation(self, update_data=None, update_detail=False):
        """加工费用分摊更新(状态/明细), 支持批量，这里没有做状态校验
        :param update_data -> dict/list 待更新的数据
        :param update_detail -> bool 是否更新明细，为false时只更新状态
        """
        db_session = session_maker()
        try:
            if not update_detail:
                update_db = db_session.query(ProcessingCostAllocation).filter(
                    ProcessingCostAllocation.partner_id == update_data.get('partner_id')).filter(
                    ProcessingCostAllocation.id == update_data.get('id')).with_lockmode('update').first()
                if update_data.get('updated_by'):
                    update_db.updated_by = update_data.get('updated_by')
                if update_data.get('updated_name'):
                    update_db.updated_name = update_data.get('updated_name')
                if update_data.get('status'):
                    update_db.status = update_data.get('status')
                db_session.add(update_db)
            else:
                if isinstance(update_data, dict):
                    update_data = [update_data]
                db_session.bulk_update_mappings(ProcessingCostAllocation, update_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Processing Cost Allocation Failed!")
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_processing_cost_allocation_by_cid(self, cost_rcpt_id=None, update_data=None):
        """根据加工费用ID一把更新整个批次的数据
        update_data: dict
        """
        db_session = session_maker()
        try:
            update_rows = db_session.query(ProcessingCostAllocation).filter(
                ProcessingCostAllocation.cost_rcpt_id == cost_rcpt_id).update(update_data)
            db_session.commit()
            return update_rows
        except Exception as e:
            logging.error("Update Processing Cost Allocation Failed!")
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def query_not_sync_cost_center_receipts(self, partner_id=None, status=None, process_status=None, month=None):
        """查询已经分摊成功生成分摊表但是未生成同步成本中心批次
        status: 分摊表记录状态
        process_status: 加工费用单状态
        month: 加工费用月份
        """
        db_session = session_maker()
        try:
            cost_rcpt_id = []
            cost_query = db_session.query(ProcessingCostDetail.id).filter(ProcessingCostDetail.partner_id == partner_id)
            if month:
                cost_query = cost_query.filter(ProcessingCostDetail.month == month)
            if process_status:
                cost_query = cost_query.filter(ProcessingCostDetail.process_status == process_status)
            if cost_query.all():
                for c in cost_query.all():
                    cost_rcpt_id.append(int(c[0]))
            if len(cost_rcpt_id) > 0:
                query = db_session.query(distinct(ProcessingCostAllocation.cost_rcpt_id)).filter(
                    ProcessingCostAllocation.partner_id == partner_id).filter(
                    ProcessingCostAllocation.cost_rcpt_id.in_(cost_rcpt_id))
                if status:
                    query = query.filter(ProcessingCostAllocation.status == status)
                return query.all()
            return None
        except Exception as e:
            logging.error("Query Not Sync Cost Center Receipts Error!")
            db_session.rollback()
            raise e
        finally:
            db_session.close()


processing_cost_db = ProcessingCostDB()
