import logging
import sys
import traceback
import json

from sqlalchemy import text, func, or_, and_, desc
from supply.driver.mysql import session_maker
from datetime import datetime, timedelta
from supply.model import DeclarativeBase, TimestampMixin, AsDict
from sqlalchemy import (Column, BigInteger, Integer, String, Boolean, DateTime, DECIMAL)
from supply.utils.helper import get_guid, convert_to_int, convert_to_decimal


class ProcessingReceipts(DeclarativeBase, TimestampMixin, AsDict):
    """加工中心加工单"""
    __tablename__ = "supply_processing_receipts"

    class Status(object):
        INITED = 'INITED'           # 新建
        SUBMITTED = 'SUBMITTED'     # 已提交
        APPROVED = 'APPROVED'       # 已审核
        REJECTED = 'REJECTED'       # 已驳回
        DELETED = 'DELETED'         # 已删除

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.utcnow())
    code = Column(String(50))  # 编号
    status = Column(String(20), default=Status.INITED)  # 状态
    type = Column(String(20))  # 加工类型*预留*
    processing_date = Column(DateTime)  # 加工日期
    machining_center_id = Column(BigInteger)  # 加工中心ID
    position_id = Column(BigInteger)  # 仓位id
    process_status = Column(String(30))  # *(预留)传输成本中心状态(INITED、PROCESSING、SUCCESS)*
    remark = Column(String(50))  # 备注
    processing_rule = Column(BigInteger)  # 加工规则id
    opened_position = Column(Boolean)     # 是否开启多仓位
    request_id = Column(BigInteger)  # 幂等性校验请求id


class ProcessingReceiptsDetail(DeclarativeBase, TimestampMixin, AsDict):
    """加工单详情表"""
    __tablename__ = "supply_processing_receipts_detail"

    class Status(object):
        INITED = 'INITED'           # 新建
        SUBMITTED = 'SUBMITTED'     # 已提交
        APPROVED = 'APPROVED'       # 已审核
        REJECTED = 'REJECTED'       # 已驳回
        DELETED = 'DELETED'         # 已删除

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.utcnow())
    main_id = Column(BigInteger, index=True)  # 主单加工单id
    status = Column(String(20), default=Status.INITED)  # 订单状态
    code = Column(String(50), index=True)  # 编号
    processing_date = Column(DateTime, index=True)  # 加工日期
    type = Column(String(20))  # 加工类型*预留*
    machining_center_id = Column(BigInteger, index=True)  # 加工中心ID
    machining_center_name = Column(String(50))  # 加工中心名称
    machining_center_code = Column(String(50))  # 加工中心code
    position_id = Column(BigInteger, index=True)  # 仓位id
    position_name = Column(String(50))  # 仓位名称
    position_code = Column(String(50))  # 仓位编码
    processing_rule = Column(BigInteger)  # 加工规则id
    target_material = Column(BigInteger, index=True)  # 目标物料ID
    target_material_code = Column(String(50))  # 目标物料code
    target_material_name = Column(String(50))  # 目标物料name
    target_material_unit_id = Column(BigInteger)  # 目标物料单位ID
    target_material_unit_name = Column(String(50))  # 目标物料单位name
    target_material_unit_spec = Column(String(50))  # 目标物料单位规格
    target_material_unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 目标物料单位转换率
    theory_output_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 理论产出率
    actual_output_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 实际产出率
    actual_output_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 实际产出数量
    opened_position = Column(Boolean)     # 是否开启多仓位
    remark = Column(String(50))  # 备注
    request_id = Column(BigInteger)  # 幂等性校验请求id
    cost_center_id = Column(BigInteger)  # 成本中心id


class ProcessingReceiptsProducts(DeclarativeBase, TimestampMixin, AsDict):
    """加工物料明细表"""
    __tablename__ = "supply_processing_receipts_products"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.utcnow())
    main_id = Column(BigInteger, index=True)  # 主单加工单id
    product_id = Column(BigInteger)  # 加工物料ID
    product_code = Column(String(50))  # 加工物料编号
    product_name = Column(String(50))  # 加工物料名称
    unit_id = Column(BigInteger)  # 加工物料单位ID
    unit_name = Column(String(50))  # 加工物料单位名称
    unit_spec = Column(String(50))  # 加工物料单位规格
    unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 加工物料单位转换率
    material_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 物料占比
    actual_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 实际加工物料数量


class ProcessingReceiptsLog(DeclarativeBase, AsDict):
    """加工单操作日志表"""
    __tablename__ = "supply_processing_receipts_log"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    main_id = Column(BigInteger, index=True)  # 关联的加工单id
    action = Column(String(50))  # 操作状态
    created_at = Column(DateTime, default=datetime.utcnow())


class ProcessingReceiptsDB(object):
    """加工单数据库操作"""

    def get_processing_receipts_by_id(self, receipt_id=None, request_id=None, partner_id=None, is_detail=False):
        """获取一条加工单记录"""
        db_session = session_maker()
        try:
            if is_detail is True:
                query = db_session.query(ProcessingReceiptsDetail).filter(
                    ProcessingReceiptsDetail.partner_id == partner_id)
                if receipt_id:
                    query = query.filter(ProcessingReceiptsDetail.main_id == receipt_id)
                    return query.first()
                if request_id:
                    query = query.filter(ProcessingReceiptsDetail.request_id == request_id)
                    return query.first()
            else:
                query = db_session.query(ProcessingReceipts).filter(ProcessingReceiptsDetail.partner_id == partner_id)
                if receipt_id:
                    query = query.filter(ProcessingReceipts.id == receipt_id)
                    return query.first()
                if request_id:
                    query = query.filter(ProcessingReceipts.request_id == request_id)
                    return query.first()
        except Exception as e:
            logging.error("Get Processing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                          traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_processing_receipts(self, receipt_data, receipt_detail, products):
        """保存加工单和商品详情
        :param receipt_data 加工单对应字段数据 dict
        :param receipt_detail 加工单详情对应数据 list[detail,..]
        :param products 加工单物料 list[item,..]
        """
        db_session = session_maker()
        try:
            receipts = [receipt_data]
            receipt_details = [receipt_detail]
            db_session.bulk_insert_mappings(ProcessingReceipts, receipts)
            db_session.bulk_insert_mappings(ProcessingReceiptsDetail, receipt_details)
            db_session.bulk_insert_mappings(ProcessingReceiptsProducts, products)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Processing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                             traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_processing_receipts_log(self, receipts_log):
        """保存加工单操作日志
        :param receipts_log 操作日志 dict
        """
        db_session = session_maker()
        try:
            receipts_data = [receipts_log]
            db_session.bulk_insert_mappings(ProcessingReceiptsLog, receipts_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Processing Receipts Log Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                                 traceback.format_exc().replace('\n',
                                                                                                                ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_processing_receipts_detail(self, partner_id=None, user_id=None, start_date=None, end_date=None, code=None,
                                        status=None, _type=None, machining_centers=None, processing_rules=None,
                                        target_materials=None, position_ids=None, order=None, sort=None, limit=-1,
                                        offset=None, include_total=False):
        """加工单detail列表"""
        db_session = session_maker()
        try:
            query = db_session.query(ProcessingReceiptsDetail).filter(ProcessingReceiptsDetail.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(ProcessingReceiptsDetail.processing_date >= start_date)
            if end_date and isinstance(end_date, datetime) and start_date.year != 1970:
                query = query.filter(ProcessingReceiptsDetail.processing_date <= end_date)
            if code:
                query = query.filter(ProcessingReceiptsDetail.code == code)
            if status and isinstance(status, list):
                query = query.filter(ProcessingReceiptsDetail.status.in_(status))
            if target_materials and isinstance(target_materials, list):
                query = query.filter(ProcessingReceiptsDetail.target_material.in_(target_materials))
            if _type:
                query = query.filter(ProcessingReceiptsDetail.type == _type)
            if machining_centers and isinstance(machining_centers, list):
                query = query.filter(ProcessingReceiptsDetail.machining_center_id.in_(machining_centers))
            if processing_rules and isinstance(processing_rules, list):
                query = query.filter(ProcessingReceiptsDetail.processing_rule.in_(processing_rules))
            if position_ids and isinstance(position_ids, list):
                query = query.filter(ProcessingReceiptsDetail.position_id.in_(position_ids))
            if sort in ["processing_date", "branch_code", "code"]:
                if order == "asc":
                    query_str = "ProcessingReceiptsDetail.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "ProcessingReceiptsDetail.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(ProcessingReceiptsDetail.updated_at)
                else:
                    query = query.order_by(ProcessingReceiptsDetail.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(ProcessingReceiptsDetail.id)).filter(
                    ProcessingReceiptsDetail.partner_id == partner_id)
                if start_date and isinstance(start_date, datetime):
                    query_total = query_total.filter(ProcessingReceiptsDetail.processing_date >= start_date)
                if end_date and isinstance(end_date, datetime):
                    query_total = query_total.filter(ProcessingReceiptsDetail.processing_date <= end_date)
                if code:
                    query_total = query_total.filter(ProcessingReceiptsDetail.code == code)
                if status and isinstance(status, list):
                    query_total = query_total.filter(ProcessingReceiptsDetail.status.in_(status))
                if target_materials and isinstance(target_materials, list):
                    query_total = query_total.filter(ProcessingReceiptsDetail.target_material.in_(target_materials))
                if _type:
                    query_total = query_total.filter(ProcessingReceiptsDetail.type == _type)
                if machining_centers and isinstance(machining_centers, list):
                    query_total = query_total.filter(
                        ProcessingReceiptsDetail.machining_center_id.in_(machining_centers))
                if processing_rules and isinstance(processing_rules, list):
                    query_total = query_total.filter(ProcessingReceiptsDetail.processing_rule.in_(processing_rules))
                if position_ids and isinstance(position_ids, list):
                    query_total = query_total.filter(ProcessingReceiptsDetail.position_id.in_(position_ids))
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Processing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                           traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_processing_receipts(self, partner_id=None, start_date=None, end_date=None, code=None, status=None,
                                 _type=None, machining_centers=None, process_status=None, position_ids=None):
        """加工单列表
        供成本同步补偿查询用
        """
        db_session = session_maker()
        try:
            query = db_session.query(ProcessingReceipts).filter(ProcessingReceipts.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(ProcessingReceipts.processing_date >= start_date)
            if end_date and isinstance(end_date, datetime) and start_date.year != 1970:
                query = query.filter(ProcessingReceipts.processing_date <= end_date)
            if code:
                query = query.filter(ProcessingReceipts.code == code)
            if status and isinstance(status, list):
                query = query.filter(ProcessingReceipts.status.in_(status))
            if process_status and isinstance(process_status, list):
                query = query.filter(ProcessingReceipts.process_status.in_(process_status))
            if _type:
                query = query.filter(ProcessingReceipts.type == _type)
            if machining_centers and isinstance(machining_centers, list):
                query = query.filter(ProcessingReceipts.machining_center_id.in_(machining_centers))
            if position_ids and isinstance(position_ids, list):
                query = query.filter(ProcessingReceipts.position_id.in_(position_ids))
            return query.all()
        except Exception as e:
            logging.error("List Processing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                           traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_processing_receipts_detail(self, partner_id=None, user_id=None, main_id=None):
        """根据id拉取加工单详情信息"""
        db_session = session_maker()
        try:
            query = db_session.query(ProcessingReceiptsProducts).filter(
                ProcessingReceiptsProducts.main_id == main_id).filter(
                ProcessingReceiptsProducts.partner_id == partner_id)
            return query.all()
        except Exception as e:
            logging.error("Get Processing Products Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                           traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_processing_receipts(self, receipt: dict, receipt_detail: dict, product_list: list, update_detail=False,
                                   partner_id=None):
        """加工单状态变更, <这里没有做状态校验>
        :param receipt -> dict 待更新的加工单数据（必须包含加工单主键id）
        :param receipt_detail -> dict 待更新的加工单detail数据
        :param product_list -> list 待更新的物料明细
        :param update_detail bool 是否更新详情
        :param partner_id
        """
        db_session = session_maker()
        try:
            receipt_db = db_session.query(ProcessingReceipts).filter(
                ProcessingReceipts.id == receipt.get('id'),
                ProcessingReceipts.partner_id == partner_id).with_lockmode('update').first()
            receipt_detail_db = db_session.query(ProcessingReceiptsDetail).filter(
                ProcessingReceiptsDetail.main_id == receipt_detail.get('main_id'),
                ProcessingReceiptsDetail.partner_id == partner_id).with_lockmode('update').first()
            if receipt.get('updated_by'):
                receipt_db.updated_by = receipt.get('updated_by')
                receipt_detail_db.updated_by = receipt_detail.get('updated_by')

            if receipt.get('updated_name'):
                receipt_db.updated_name = receipt.get('updated_name')
                receipt_detail_db.updated_name = receipt_detail.get('updated_name')
            receipt_db.updated_at = datetime.utcnow()
            receipt_detail_db.updated_at = datetime.utcnow()
            if update_detail is True:
                if receipt.get('machining_center_id'):
                    receipt_db.machining_center_id = receipt.get('machining_center_id')
                    receipt_detail_db.machining_center_id = receipt_detail.get('machining_center_id')

                if receipt.get('position_id'):
                    receipt_db.position_id = receipt.get('position_id')
                    receipt_detail_db.position_id = receipt_detail.get('position_id')
                if receipt.get('remark'):
                    receipt_db.remark = receipt.get('remark')
                    receipt_detail_db.remark = receipt_detail.get('remark')
                if receipt.get('processing_rule'):
                    receipt_db.processing_rule = receipt.get('processing_rule')
                    receipt_detail_db.processing_rule = receipt_detail.get('processing_rule')
                if receipt_detail.get('machining_center_name'):
                    receipt_detail_db.machining_center_name = receipt_detail.get('machining_center_name')
                if receipt_detail.get('machining_center_code'):
                    receipt_detail_db.machining_center_code = receipt_detail.get('machining_center_code')
                if receipt_detail.get('position_name'):
                    receipt_detail_db.position_name = receipt_detail.get('position_name')
                if receipt_detail.get('position_code'):
                    receipt_detail_db.position_code = receipt_detail.get('position_code')
                if receipt_detail.get('target_material'):
                    receipt_detail_db.target_material = receipt_detail.get('target_material')
                if receipt_detail.get('target_material_code'):
                    receipt_detail_db.target_material_code = receipt_detail.get('target_material_code')
                if receipt_detail.get('target_material_name'):
                    receipt_detail_db.target_material_name = receipt_detail.get('target_material_name')
                if receipt_detail.get('target_material_unit_id'):
                    receipt_detail_db.target_material_unit_id = receipt_detail.get('target_material_unit_id')
                if receipt_detail.get('target_material_unit_name'):
                    receipt_detail_db.target_material_unit_name = receipt_detail.get('target_material_unit_name')
                if receipt_detail.get('target_material_unit_spec'):
                    receipt_detail_db.target_material_unit_spec = receipt_detail.get('target_material_unit_spec')
                if receipt_detail.get('theory_output_rate') is not None:
                    receipt_detail_db.theory_output_rate = receipt_detail.get('theory_output_rate')
                if receipt_detail.get('actual_output_rate') is not None:
                    receipt_detail_db.actual_output_rate = receipt_detail.get('actual_output_rate')
                if receipt_detail.get('actual_output_quantity') is not None:
                    receipt_detail_db.actual_output_quantity = receipt_detail.get('actual_output_quantity')
                if receipt_detail.get('opened_position') is not None:
                    receipt_detail_db.opened_position = receipt_detail.get('opened_position')
                if product_list:
                    db_session.bulk_update_mappings(ProcessingReceiptsProducts, product_list)
                if receipt_detail.get('cost_center_id'):
                    receipt_detail_db.cost_center_id = receipt_detail.get('cost_center_id')

            else:
                if receipt.get('process_status'):
                    receipt_db.process_status = receipt.get('process_status')
                if receipt_detail.get('status'):
                    receipt_detail_db.status = receipt_detail.get('status')
                if receipt.get('status'):
                    receipt_db.status = receipt.get('status')
            db_session.add(receipt_db)
            db_session.add(receipt_detail_db)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Processing Receipts Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                              traceback.format_exc().replace('\n',
                                                                                                             ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    # noinspection PyMethodMayBeStatic
    def query_processing_detail_by_sql(self, start_date=None, end_date=None, machining_center_id=None,
                                       position_id=None, product_ids=None):
        """查询加工单详情，
        根据加工中心/仓位和商品group by
        加工费用月底分摊表固化用
        """
        db_session = session_maker()
        base_sql = """
        SELECT 
            machining_center_id,
            position_id,
            target_material,
            target_material_unit_id,
            target_material_unit_name,
            sum(actual_output_quantity),
            cost_center_id
        FROM supply_processing_receipts_detail
        WHERE status = 'APPROVED'
        {}
        GROUP BY 
        machining_center_id, position_id, target_material, target_material_unit_id, cost_center_id;
        """
        where_sql = ""
        try:
            if start_date and isinstance(start_date, datetime):
                where_sql += " AND processing_date >= '{}'".format(start_date)
            if end_date and isinstance(end_date, datetime):
                where_sql += " AND processing_date < '{}'".format(end_date)
            if machining_center_id:
                where_sql += " AND machining_center_id = {}".format(machining_center_id)
            if position_id:
                where_sql += " AND position_id = {}".format(position_id)
            if product_ids and isinstance(product_ids, list):
                product_ids = ",".join([str(pid) for pid in product_ids])
                where_sql += " AND target_material in ({})".format(product_ids)
            query_set = db_session.execute(base_sql.format(where_sql)).fetchall()
            return query_set
        except Exception as e:
            logging.error("query_processing_detail_by_sql Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                                  traceback.format_exc().replace('\n',
                                                                                                                 ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()


processing_receipts_db = ProcessingReceiptsDB()
