import logging
import sys
import traceback
import json

from sqlalchemy import text, func, or_, and_, desc
from supply.driver.mysql import session_maker
from datetime import datetime, timedelta
from supply.model import DeclarativeBase, TimestampMixin, AsDict
from sqlalchemy import (Column, BigInteger, Integer, String, Boolean, DateTime, DECIMAL)
from sqlalchemy.orm.exc import NoResultFound
from supply.utils.helper import get_guid, convert_to_int, convert_to_decimal


class PackingReceipts(DeclarativeBase, TimestampMixin, AsDict):
    """加工中心包装单业务模型类"""
    __tablename__ = "supply_packing_receipts"

    class Status(object):
        INITED = 'INITED'           # 新建
        SUBMITTED = 'SUBMITTED'     # 已提交
        APPROVED = 'APPROVED'       # 已审核
        REJECTED = 'REJECTED'       # 已驳回
        DELETED = 'DELETED'         # 已删除

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.utcnow(), onupdate=datetime.utcnow())
    code = Column(String(50))                             # 编号
    status = Column(String(20), default=Status.INITED)    # 状态
    type = Column(String(20))                             # 包装类型*预留*
    packing_date = Column(DateTime)                       # 包装日期
    machining_center_id = Column(BigInteger)              # 加工中心ID
    position_id = Column(BigInteger)                      # 仓位id
    process_status = Column(String(30))                   # *(预留)传输成本中心状态(INITED、PROCESSING、SUCCESS)*
    opened_position = Column(Boolean)                     # 是否开启多仓位
    remark = Column(String(50))                           # 备注
    packing_rule = Column(BigInteger)                     # 包装规则id
    request_id = Column(BigInteger)                       # 幂等性校验请求id


class PackingReceiptsDetail(DeclarativeBase, TimestampMixin, AsDict):
    """包装单详情表"""
    __tablename__ = "supply_packing_receipts_detail"

    class Status(object):
        INITED = 'INITED'           # 新建
        SUBMITTED = 'SUBMITTED'     # 已提交
        APPROVED = 'APPROVED'       # 已审核
        REJECTED = 'REJECTED'       # 已驳回
        DELETED = 'DELETED'         # 已删除

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.utcnow(), onupdate=datetime.utcnow())
    main_id = Column(BigInteger, index=True)                # 主单包装单id
    status = Column(String(20), default=Status.INITED)      # 订单状态
    code = Column(String(50), index=True)                   # 编号
    packing_date = Column(DateTime, index=True)             # 包装日期
    type = Column(String(20))                               # 包装类型*预留*
    machining_center_id = Column(BigInteger, index=True)    # 加工中心ID
    machining_center_name = Column(String(50))              # 加工中心名称
    machining_center_code = Column(String(50))              # 加工中心code
    position_id = Column(BigInteger, index=True)        # 仓位id
    position_name = Column(String(50))                  # 仓位名称
    position_code = Column(String(50))                  # 仓位编码
    packing_rule = Column(BigInteger)                   # 包装规则id
    target_material = Column(BigInteger, index=True)    # 目标物料ID
    target_material_code = Column(String(50))           # 目标物料code
    target_material_name = Column(String(50))           # 目标物料name
    target_material_unit_id = Column(BigInteger)        # 目标物料单位ID
    target_material_unit_name = Column(String(50))      # 目标物料单位name
    target_material_unit_spec = Column(String(50))      # 目标物料单位规格
    target_material_unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 目标物料单位转换率
    packed_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 包装数量
    opened_position = Column(Boolean)                   # 是否开启多仓位
    remark = Column(String(50))                         # 备注
    request_id = Column(BigInteger)                     # 幂等性校验请求id
    cost_center_id = Column(BigInteger)  # 成本中心id


class PackingReceiptsItems(DeclarativeBase, TimestampMixin, AsDict):
    """包装物料/耗材明细条目表"""
    __tablename__ = "supply_packing_receipts_items"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    updated_by = Column(BigInteger)
    updated_name = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.utcnow(), onupdate=datetime.utcnow())
    main_id = Column(BigInteger, index=True)            # 主单包装单id
    product_id = Column(BigInteger)                     # 包装物料/耗材ID
    product_code = Column(String(50))                   # 包装物料/耗材编号
    product_name = Column(String(50))                   # 包装物料/耗材名称
    unit_id = Column(BigInteger)                        # 包装物料/耗材单位ID
    unit_name = Column(String(50))                      # 包装物料/耗材单位名称
    unit_spec = Column(String(50))                      # 包装物料/耗材单位规格
    unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 包装物料/耗材单位转换率
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 物料数量
    type = Column(String(20))                           # 区分物料`material`/耗材`consumable`


class PackingReceiptsLog(DeclarativeBase, AsDict):
    """包装单操作日志表"""
    __tablename__ = "supply_packing_receipts_log"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    main_id = Column(BigInteger, index=True)  # 关联的包装单id
    action = Column(String(50))  # 操作状态（确认/作废/物料转换）等
    created_at = Column(DateTime, default=datetime.utcnow())


class PackingReceiptsDB(object):
    """包装单数据库操作"""

    def get_packing_receipts_by_id(self, receipt_id=None, request_id=None, partner_id=None, is_detail=False):
        """获取一条包装单记录"""
        db_session = session_maker()
        try:
            if is_detail is True:
                query = db_session.query(PackingReceiptsDetail).filter(PackingReceiptsDetail.partner_id == partner_id)
                if receipt_id:
                    query = query.filter(PackingReceiptsDetail.main_id == receipt_id)
                    return query.first()
                if request_id:
                    query = query.filter(PackingReceiptsDetail.request_id == request_id)
                    return query.first()
            else:
                query = db_session.query(PackingReceipts).filter(PackingReceipts.partner_id == partner_id)
                if receipt_id:
                    query = query.filter(PackingReceipts.id == receipt_id)
                    return query.first()
                if request_id:
                    query = query.filter(PackingReceipts.request_id == request_id)
                    return query.first()
        except Exception as e:
            logging.error("Get Packing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                       traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_packing_receipts(self, receipt_data, receipt_detail, items):
        """保存包装单和商品详情
        :param receipt_data 包装单对应字段数据 dict
        :param receipt_detail 包装单详情对应数据 list[detail,..]
        :param items 包装单条目明细 list[item,..]
        """
        db_session = session_maker()
        try:
            receipts = [receipt_data]
            receipt_details = [receipt_detail]
            db_session.bulk_insert_mappings(PackingReceipts, receipts)
            db_session.bulk_insert_mappings(PackingReceiptsDetail, receipt_details)
            db_session.bulk_insert_mappings(PackingReceiptsItems, items)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Packing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                          traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_packing_receipts_log(self, receipts_log):
        """保存包装单操作日志
        :param receipts_log 操作日志 dict
        """
        db_session = session_maker()
        try:
            receipts_data = [receipts_log]
            db_session.bulk_insert_mappings(PackingReceiptsLog, receipts_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Packing Receipts Log Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                              traceback.format_exc().replace('\n',
                                                                                                             ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_packing_receipts_detail(self, partner_id=None, user_id=None, start_date=None, end_date=None, code=None,
                                     status=None, _type=None, machining_centers=None, packing_rules=None,
                                     target_materials=None, position_ids=None, order=None, sort=None, limit=-1,
                                     offset=None, include_total=False):
        """包装单列表detail"""
        db_session = session_maker()
        try:
            query = db_session.query(PackingReceiptsDetail).filter(PackingReceiptsDetail.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(PackingReceiptsDetail.packing_date >= start_date)
            if end_date and isinstance(end_date, datetime) and start_date.year != 1970:
                query = query.filter(PackingReceiptsDetail.packing_date <= end_date)
            if code:
                query = query.filter(PackingReceiptsDetail.code == code)
            if status and isinstance(status, list):
                query = query.filter(PackingReceiptsDetail.status.in_(status))
            if target_materials:
                query = query.filter(PackingReceiptsDetail.target_material.in_(target_materials))
            if _type:
                query = query.filter(PackingReceiptsDetail.type == _type)
            if machining_centers:
                query = query.filter(PackingReceiptsDetail.machining_center_id.in_(machining_centers))
            if packing_rules:
                query = query.filter(PackingReceiptsDetail.packing_rule.in_(packing_rules))
            if position_ids:
                query = query.filter(PackingReceiptsDetail.position_id.in_(position_ids))
            if sort in ["packing_date", "code"]:
                if order == "asc":
                    query_str = "PackingReceiptsDetail.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "PackingReceiptsDetail.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(PackingReceiptsDetail.updated_at)
                else:
                    query = query.order_by(PackingReceiptsDetail.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(PackingReceiptsDetail.id)).filter(
                    PackingReceiptsDetail.partner_id == partner_id)
                if start_date and isinstance(start_date, datetime):
                    query_total = query_total.filter(PackingReceiptsDetail.packing_date >= start_date)
                if end_date and isinstance(end_date, datetime):
                    query_total = query_total.filter(PackingReceiptsDetail.packing_date <= end_date)
                if code:
                    query_total = query_total.filter(PackingReceiptsDetail.code == code)
                if status and isinstance(status, list):
                    query_total = query_total.filter(PackingReceiptsDetail.status.in_(status))
                if target_materials:
                    query_total = query_total.filter(PackingReceiptsDetail.target_material.in_(target_materials))
                if _type:
                    query_total = query_total.filter(PackingReceiptsDetail.type == _type)
                if machining_centers:
                    query_total = query_total.filter(PackingReceiptsDetail.machining_center_id.in_(machining_centers))
                if packing_rules:
                    query_total = query_total.filter(PackingReceiptsDetail.packing_rule.in_(packing_rules))
                if position_ids:
                    query_total = query_total.filter(PackingReceiptsDetail.position_id.in_(position_ids))
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Packing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                        traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_packing_receipts(self, partner_id=None, start_date=None, end_date=None, code=None, status=None,
                              _type=None, machining_centers=None, position_ids=None, process_status=None):
        """包装单列表
        供成本同步补偿查询用
        """
        db_session = session_maker()
        try:
            query = db_session.query(PackingReceipts).filter(PackingReceipts.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(PackingReceipts.packing_date >= start_date)
            if end_date and isinstance(end_date, datetime) and start_date.year != 1970:
                query = query.filter(PackingReceipts.packing_date <= end_date)
            if code:
                query = query.filter(PackingReceipts.code == code)
            if status and isinstance(status, list):
                query = query.filter(PackingReceipts.status.in_(status))
            if process_status:
                query = query.filter(PackingReceipts.process_status.in_(process_status))
            if _type:
                query = query.filter(PackingReceipts.type == _type)
            if machining_centers:
                query = query.filter(PackingReceipts.machining_center_id.in_(machining_centers))
            if position_ids:
                query = query.filter(PackingReceipts.position_id.in_(position_ids))
            return query.all()
        except Exception as e:
            logging.error("List Packing Receipts Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                        traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_packing_receipts_detail(self, partner_id=None, user_id=None, main_id=None):
        """根据id拉取包装单详情信息"""
        db_session = session_maker()
        try:
            query = db_session.query(PackingReceiptsItems).filter(
                PackingReceiptsItems.main_id == main_id).filter(
                PackingReceiptsItems.partner_id == partner_id)
            return query.all()
        except Exception as e:
            logging.error("Get Packing Receipts Items Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                               traceback.format_exc().replace('\n',
                                                                                                              ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_packing_receipts(self, receipt: dict, receipt_detail: dict, items_list: list, update_detail=False,
                                partner_id=None):
        """包装单状态变更, <这里没有做状态校验>
        :param receipt -> dict 待更新的包装单数据（必须包含包装单主键id）
        :param receipt_detail -> dict 待更新的包装单detail数据
        :param items_list -> list 待更新的物料明细
        :param update_detail bool 是否更新详情
        :param partner_id
        """
        db_session = session_maker()
        try:
            receipt_db = db_session.query(PackingReceipts).filter(
                PackingReceipts.id == receipt.get('id'),
                PackingReceipts.partner_id == partner_id).with_lockmode('update').first()
            receipt_detail_db = db_session.query(PackingReceiptsDetail).filter(
                PackingReceiptsDetail.main_id == receipt_detail.get('main_id'),
                PackingReceiptsDetail.partner_id == partner_id).with_lockmode('update').first()
            if receipt.get('updated_by'):
                receipt_db.updated_by = receipt.get('updated_by')
                receipt_detail_db.updated_by = receipt_detail.get('updated_by')
            if receipt.get('updated_name'):
                receipt_db.updated_name = receipt.get('updated_name')
                receipt_detail_db.updated_name = receipt_detail.get('updated_name')
            receipt_db.updated_at = datetime.utcnow()
            receipt_detail_db.updated_at = datetime.utcnow()
            if update_detail is True:
                if receipt.get('machining_center_id'):
                    receipt_db.machining_center_id = receipt.get('machining_center_id')
                if receipt.get('position_id'):
                    receipt_db.position_id = receipt.get('position_id')
                if receipt.get('remark'):
                    receipt_db.remark = receipt.get('remark')
                if receipt.get('packing_rule'):
                    receipt_db.packing_rule = receipt.get('packing_rule')

                if receipt_detail.get('machining_center_id'):
                    receipt_detail_db.machining_center_id = receipt_detail.get('machining_center_id')
                if receipt_detail.get('machining_center_name'):
                    receipt_detail_db.machining_center_name = receipt_detail.get('machining_center_name')
                if receipt_detail.get('machining_center_code'):
                    receipt_detail_db.machining_center_code = receipt_detail.get('machining_center_code')
                if receipt_detail.get('position_id'):
                    receipt_detail_db.position_id = receipt_detail.get('position_id')
                if receipt_detail.get('position_name'):
                    receipt_detail_db.position_name = receipt_detail.get('position_name')
                if receipt_detail.get('position_code'):
                    receipt_detail_db.position_code = receipt_detail.get('position_code')
                if receipt_detail.get('packing_rule'):
                    receipt_detail_db.packing_rule = receipt_detail.get('packing_rule')
                if receipt_detail.get('target_material'):
                    receipt_detail_db.target_material = receipt_detail.get('target_material')
                if receipt_detail.get('target_material_code'):
                    receipt_detail_db.target_material_code = receipt_detail.get('target_material_code')
                if receipt_detail.get('target_material_name'):
                    receipt_detail_db.target_material_name = receipt_detail.get('target_material_name')
                if receipt_detail.get('target_material_unit_id'):
                    receipt_detail_db.target_material_unit_id = receipt_detail.get('target_material_unit_id')
                if receipt_detail.get('target_material_unit_name'):
                    receipt_detail_db.target_material_unit_name = receipt_detail.get('target_material_unit_name')
                if receipt_detail.get('target_material_unit_spec'):
                    receipt_detail_db.target_material_unit_spec = receipt_detail.get('target_material_unit_spec')
                if receipt_detail.get('packed_quantity') is not None:
                    receipt_detail_db.packed_quantity = receipt_detail.get('packed_quantity')
                if receipt_detail.get('opened_position') is not None:
                    receipt_detail_db.opened_position = receipt_detail.get('opened_position')
                if receipt_detail.get('remark'):
                    receipt_detail_db.remark = receipt_detail.get('remark')
                if receipt_detail.get('cost_center_id'):
                    receipt_detail_db.cost_center_id = receipt_detail.get('cost_center_id')
                if items_list:
                    db_session.bulk_update_mappings(PackingReceiptsItems, items_list)
            else:
                if receipt.get('status'):
                    receipt_db.status = receipt.get('status')
                if receipt.get('process_status'):
                    receipt_db.process_status = receipt.get('process_status')
                if receipt_detail.get('status'):
                    receipt_detail_db.status = receipt_detail.get('status')
            db_session.add(receipt_detail_db)
            db_session.add(receipt_db)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Packing Receipts Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                           traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()


packing_receipts_db = PackingReceiptsDB()
