from supply.model import DeclarativeBase
from sqlalchemy import Column, BigInteger
from ..driver.mysql import session_maker
from sqlalchemy.orm.query import Query


class ThirdParty(DeclarativeBase):
    __tablename__ = 'third_party'
    id = Column(BigInteger, primary_key=True)
    partner_id = Column(BigInteger)

    @classmethod
    def get_third_party(cls, partner_id):
        db_session = session_maker()
        try:
            q = db_session.query(cls.id)  # type: Query
            q = q.filter(cls.partner_id == partner_id)
            return q.first()
        except Exception as e:
            raise e
        finally:
            db_session.close()
