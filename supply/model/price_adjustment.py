# -*- coding: utf-8 -*-
import logging
import traceback
import sys

from datetime import datetime
from supply.model import DeclarativeBase, MethodMixin, TimestampMixin, AsDict
from sqlalchemy import Column, BigInteger, String, DateTime, func, DECIMAL, Boolean, Integer
from supply.utils.helper import get_guid
from supply.driver.mysql import session_maker


class PriceAdjustmentOrder(DeclarativeBase, MethodMixin, TimestampMixin, AsDict):
    """调价单"""
    __tablename__ = "supply_price_adjustment"

    class Status(object):
        INITED = 'INITED'       # 新建
        APPROVED = 'APPROVED'   # 已审核
        INVALID = 'INVALID'     # 已作废

    class BlendingStatus(object):
        INITED = 'INITED'  # 新建
        COMPLETED = 'COMPLETED'  # 已审核

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 商户id
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)  # 创建人姓名
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)  # 更新人姓名
    batch_id = Column(BigInteger, index=True)  # 主单id - 复核单
    batch_code = Column(String)  # 原单编号 - 复核单
    branch_type = Column(String)   # 区分门店仓库WAREHOUSE/STORE
    code = Column(String, index=True)  # 订单编号
    status = Column(String, default=Status.INITED)  # 订单状态
    blending_status = Column(String, default=BlendingStatus.INITED)    # 发票勾兑状态
    order_type = Column(String)  # 单据类型（复核调价单REVIEW_ADJUST/财务调价单FINANCE_ADJUST）
    adjust_date = Column(DateTime, index=True)  # 调价时间
    branch_id = Column(BigInteger)  # 门店或仓库id
    branch_code = Column(String)    # 门店或仓库code
    branch_name = Column(String)    # 门店或仓库名称
    supplier_id = Column(BigInteger)  # 供应商id
    supplier_name = Column(String)    # 供应商名称
    supplier_code = Column(String)    # 供应商code
    sum_price_tax = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 总含税金额
    pre_sum_price_tax = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 调整前总含税金额
    sum_price = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 总未税金额
    pre_sum_price = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 调整前总未税金额
    cost_trans_status = Column(Boolean, default=0)  # 是否传输成本引擎 0:未传输，1:已传输
    cost_update = Column(Integer)  # 影响成本更新的次数，之后用来校验
    cost_center_id = Column(BigInteger)  # 成本中心id
    company_id = Column(BigInteger)  # 公司id,发票勾兑筛选用


class PriceAdjustmentDetail(DeclarativeBase, MethodMixin, TimestampMixin, AsDict):
    """调价单明细"""
    __tablename__ = "supply_price_adjustment_detail"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 商户id
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)  # 创建人姓名
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)  # 更新人姓名
    adjust_id = Column(BigInteger, index=True)  # 关联的调价单id
    adjust_code = Column(String, index=True)    # 关联的调价单编号
    product_id = Column(BigInteger, index=True)  # 商品id
    product_code = Column(String)  # 商品code
    product_name = Column(String)  # 商品名称
    product_category_id = Column(BigInteger)  # 商品类别id
    product_type = Column(String)  # 商品类型
    unit_id = Column(BigInteger)  # 单位id(门店中为订货单位/仓库为采购单位)
    unit_name = Column(String)  # 单位名称
    unit_spec = Column(String)  # 单位规格
    quantity = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 订货/退货数量
    actual_quantity = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 实收数量
    tax_rate = Column(DECIMAL(precision=16, scale=4, decimal_return_scale=4), default=0.0)  # 税率
    price = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 未税单价
    pre_price = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 调整前未税单价
    sum_price = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 未税合计
    pre_sum_price = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 调整前未税合计
    price_tax = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 含税单价
    pre_price_tax = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 调整前含税单价
    sum_price_tax = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 含税合计
    pre_sum_price_tax = Column(DECIMAL(precision=16, scale=6, decimal_return_scale=6), default=0.0)  # 调整前含税合计


class PriceAdjustmentDB(object):
    def __init__(self):
        pass

    def get_price_adjustment_by_id(self, partner_id, adjust_id=None, adjust_code=None):
        """获取一条调价单单记录
        支持主键id or 调价单code查询"""
        db_session = session_maker()
        try:
            query = db_session.query(PriceAdjustmentOrder).filter(PriceAdjustmentOrder.partner_id == partner_id)
            if adjust_id:
                query = query.filter(PriceAdjustmentOrder.id == adjust_id)
                return query.first()
            if adjust_code:
                query = query.filter(PriceAdjustmentOrder.code == adjust_code)
                return query.first()
        except Exception as e:
            logging.error("Get Price Adjustment Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                       traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_price_adjustment(self, order_data: dict, adjust_detail: list):
        """保存调价单和商品详情
        :param order_data 调价单对应字段数据 dict
        :param adjust_detail 调价单商品详情对应数据 list[detail,..]"""
        db_session = session_maker()
        try:
            if not adjust_detail:
                return False
            order_list = [order_data]
            db_session.bulk_insert_mappings(PriceAdjustmentOrder, order_list)
            db_session.bulk_insert_mappings(PriceAdjustmentDetail, adjust_detail)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Price Adjustment Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                          traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_price_adjustment(self, partner_id=None, user_id=None, start_date=None, end_date=None, order_code=None,
                              status=None, order_type=None, supplier_ids=None, branch_ids=None, limit=-1,
                              offset=None, include_total=False, order=None, sort=None, branch_type=None,
                              cost_trans_status=None, company_id=None, blending_status=None, batch_code=None):
        """调价单列表"""
        db_session = session_maker()
        try:
            query = db_session.query(PriceAdjustmentOrder).filter(PriceAdjustmentOrder.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(PriceAdjustmentOrder.adjust_date >= start_date)
            if end_date and isinstance(end_date, datetime) and start_date.year != 1970:
                query = query.filter(PriceAdjustmentOrder.adjust_date <= end_date)
            if order_code:
                query = query.filter(PriceAdjustmentOrder.code == order_code)
            if batch_code:
                query = query.filter(PriceAdjustmentOrder.batch_code == batch_code)
            if branch_type:
                query = query.filter(PriceAdjustmentOrder.branch_type == branch_type)
            if status:
                query = query.filter(PriceAdjustmentOrder.status == status)
            if order_type:
                query = query.filter(PriceAdjustmentOrder.order_type == order_type)
            if supplier_ids:
                query = query.filter(PriceAdjustmentOrder.supplier_id.in_(supplier_ids))
            if branch_ids:
                query = query.filter(PriceAdjustmentOrder.branch_id.in_(branch_ids))
            if cost_trans_status in [0, False, 1, True]:
                query = query.filter(PriceAdjustmentOrder.cost_trans_status == cost_trans_status)
            if company_id:
                query = query.filter(PriceAdjustmentOrder.company_id == company_id)
            if blending_status:
                query = query.filter(PriceAdjustmentOrder.blending_status == blending_status)
            if sort in ["adjust_date", "branch_code", "supplier_code", "sum_price", "sum_price_tax", "code",
                        "adjust_sum_price_tax", "adjust_sum_price"]:
                if order == "asc":
                    query_str = "PriceAdjustmentOrder.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "PriceAdjustmentOrder.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(PriceAdjustmentOrder.updated_at)
                else:
                    query = query.order_by(PriceAdjustmentOrder.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(func.count(PriceAdjustmentOrder.id)).filter(
                    PriceAdjustmentOrder.partner_id == partner_id)
                if start_date and isinstance(start_date, datetime):
                    query_total = query_total.filter(PriceAdjustmentOrder.adjust_date >= start_date)
                if end_date and isinstance(end_date, datetime):
                    query_total = query_total.filter(PriceAdjustmentOrder.adjust_date <= end_date)
                if order_code:
                    query_total = query_total.filter(PriceAdjustmentOrder.code == order_code)
                if batch_code:
                    query_total = query_total.filter(PriceAdjustmentOrder.batch_code == batch_code)
                if branch_type:
                    query_total = query_total.filter(PriceAdjustmentOrder.branch_type == branch_type)
                if status:
                    query_total = query_total.filter(PriceAdjustmentOrder.status == status)
                if order_type:
                    query_total = query_total.filter(PriceAdjustmentOrder.order_type == order_type)
                if supplier_ids:
                    query_total = query_total.filter(PriceAdjustmentOrder.supplier_id.in_(supplier_ids))
                if branch_ids:
                    query_total = query_total.filter(PriceAdjustmentOrder.branch_id.in_(branch_ids))
                if cost_trans_status:
                    query_total = query_total.filter(PriceAdjustmentOrder.cost_trans_status == cost_trans_status)
                if company_id:
                    query_total = query_total.filter(PriceAdjustmentOrder.company_id == company_id)
                if blending_status:
                    query_total = query_total.filter(PriceAdjustmentOrder.blending_status == blending_status)
                count = query_total.scalar()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Price Adjustment Order Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                              traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_price_adjustment_product(self, partner_id=None, adjust_id=None, adjust_codes=None, product_ids=None,
                                     order=None, sort=None):
        """根据调价单id拉取调价单详情信息, 默认按更新时间倒序"""
        db_session = session_maker()
        try:
            query = db_session.query(PriceAdjustmentDetail).filter(PriceAdjustmentDetail.partner_id == partner_id)
            if adjust_id:
                query = query.filter(PriceAdjustmentDetail.adjust_id == adjust_id)
            if adjust_codes and isinstance(adjust_codes, list):
                query = query.filter(PriceAdjustmentDetail.adjust_code.in_(adjust_codes))
            if product_ids and isinstance(product_ids, list):
                query = query.filter(PriceAdjustmentDetail.product_id.in_(product_ids))
            if sort:
                if order == "asc":
                    query_str = "PriceAdjustmentDetail.{}".format(sort)
                    query = query.order_by(eval(query_str))
                else:
                    query_str = "PriceAdjustmentDetail.{}.desc()".format(sort)
                    query = query.order_by(eval(query_str))
            else:
                if order == "asc":
                    query = query.order_by(PriceAdjustmentDetail.updated_at)
                else:
                    query = query.order_by(PriceAdjustmentDetail.updated_at.desc())
            return query.all()
        except Exception as e:
            logging.error("Get Price Adjustment Detail Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                               traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_price_adjustment(self, update_data=None):
        """调价单审核,支持批量
        :param update_data 待复核的数据列表"""
        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(PriceAdjustmentOrder, update_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Price Adjustment Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                           traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_price_adjustment_detail(self, partner_id=None, user_id=None, start_time=None, end_time=None,
                                     status=None, received_bys=None, receipt_ids=None, cost_trans_status=None,
                                     cost_center_id=None):
        """查询调价单商品和订单详情供成本引擎检查单据使用"""
        db_session = session_maker()
        try:
            query = db_session.query(PriceAdjustmentDetail, PriceAdjustmentOrder.branch_id,
                                     PriceAdjustmentOrder.id,
                                     PriceAdjustmentOrder.status, PriceAdjustmentOrder.adjust_date,
                                     PriceAdjustmentOrder.cost_trans_status, PriceAdjustmentOrder.cost_update,
                                     PriceAdjustmentOrder.branch_type, PriceAdjustmentOrder.supplier_id,
                                     PriceAdjustmentOrder.order_type, PriceAdjustmentOrder.cost_center_id).join(
                PriceAdjustmentOrder, PriceAdjustmentOrder.id == PriceAdjustmentDetail.adjust_id)
            if partner_id:
                query = query.filter(PriceAdjustmentDetail.partner_id == partner_id)
            if receipt_ids and isinstance(receipt_ids, list):
                query = query.filter(PriceAdjustmentOrder.id.in_(receipt_ids))
            if status and isinstance(status, list):
                query = query.filter(PriceAdjustmentOrder.status.in_(status))
            if start_time and isinstance(start_time, datetime):
                query = query.filter(PriceAdjustmentOrder.updated_at >= start_time)
            if end_time and isinstance(end_time, datetime):
                query = query.filter(PriceAdjustmentOrder.updated_at <= end_time)
            if received_bys and isinstance(received_bys, list):
                query = query.filter(PriceAdjustmentOrder.branch_id.in_(received_bys))
            if cost_trans_status:
                query = query.filter(PriceAdjustmentOrder.cost_trans_status == cost_trans_status)
            if cost_center_id:
                query = query.filter(PriceAdjustmentOrder.cost_center_id == cost_center_id)
            result = []
            list_detail_products = query.all()
            if list_detail_products and isinstance(list_detail_products, list):
                for product in list_detail_products:
                    product_dict = product[0].as_dict()
                    product_dict["branch_id"] = product[1]
                    product_dict["adjust_id"] = product[2]
                    product_dict["status"] = product[3]
                    product_dict["adjust_date"] = product[4]
                    product_dict["cost_trans_status"] = product[5]
                    product_dict["cost_update"] = product[6]
                    product_dict["branch_type"] = product[7]
                    product_dict["supplier_id"] = product[8]
                    product_dict["order_type"] = product[9]
                    product_dict["cost_center_id"] = product[10]
                    result.append(product_dict)
            return result
        except Exception as e:
            logging.error("List Price Adjustment Detail Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                               traceback.format_exc().replace('\n',
                                                                                                              ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()


price_adjustment_db = PriceAdjustmentDB()
