from ..driver.mysql import session_maker
from supply.utils.helper import convert_to_decimal
from google.protobuf.timestamp_pb2 import Timestamp
import logging,sys,traceback
from ..utils.helper import get_guid,set_db
from datetime import datetime, timedelta
from . import DeclarativeBase,TimestampMixin, MethodMixin, AsDict
from sqlalchemy import (Column, BigInteger,Integer,String, Boolean, DateTime,DECIMAL,TEXT)
import json
from ..driver.mysql import session
from ..utils.snowflake import gen_snowflake_id

class BreadFactoryModel(DeclarativeBase, MethodMixin, AsDict):
    __tablename__ = 'supply_bread_factory'
    id = Column(BigInteger, primary_key=True,default=gen_snowflake_id)
    code = Column(String(50))
    created_at = Column(DateTime)