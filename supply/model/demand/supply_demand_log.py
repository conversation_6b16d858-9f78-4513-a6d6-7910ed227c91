# -*-: coding:utf-8 -*-
from datetime import timedel<PERSON>, datetime
from sqlalchemy import (DECIMAL, BigInteger, Column, DateTime, Integer, String,
                        Text)
from sqlalchemy.ext.hybrid import hybrid_property

from supply.driver.Redis import Redis_cli
from supply.utils.enums import Demand_type, Demand_enum, Vacancy_enum
from supply.utils.helper import datetime_to_strdate, datetime_to_strdatetime
from supply.utils.snowflake import gen_snowflake_id

from .. import (DBSession, DeclarativeBase, MethodMixin, TimestampMixin,
                db_commit)


class Supply_demand_log(DeclarativeBase, TimestampMixin, MethodMixin):

    __tablename__ = "supply_demand_log"

    partner_id = Column(BigInteger)
    store_id = Column(BigInteger)
    demand_id = Column(BigInteger)
    demand_code = Column(String)
    action_code = Column(String)
    action_name = Column(String)
    updated_name = Column(String)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)


    @classmethod
    def get_by_demand_id(cls, partner_id, demand_id, action_code=None, limit=None, offset=None, user_id=None):
        q = DBSession.query(cls).filter(cls.partner_id==partner_id)
        if demand_id:
            q = q.filter(cls.demand_id==demand_id)
        if action_code:
            q = q.filter(cls.action_code==action_code)
        if user_id:
            q = q.filter(cls.updated_by==user_id)
        if limit:
            q = q.limit(limit).offset(offset)
        total = q.count()
        return q.all(), total