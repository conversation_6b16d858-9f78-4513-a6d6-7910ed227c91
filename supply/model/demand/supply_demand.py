# -*-: coding:utf-8 -*-

from datetime import datetime, timedelta

from sqlalchemy import (BOOLEAN, BigInteger, Boolean, Column, DateTime,
                        Integer, String, Text, and_, func, case, or_, and_,
                        Time)
from sqlalchemy.ext.hybrid import hybrid_property

from supply.error.demand import DemandNotExistError
from supply.utils.enums import Demand_enum
from supply.utils.helper import datetime_to_strdate, datetime_to_strdatetime
from supply.utils.snowflake import gen_snowflake_id
from supply.driver.Redis import Redis_cli
from supply import logger
from supply.driver.mysql import session_maker


from .. import (DBSession, DeclarativeBase, MethodMixin, TimestampMixin,
                db_commit)
from .supply_demand_product import Supply_demand_product
from .supply_demand_log import Supply_demand_log


# 具体表字段含义，请查看数据库表注释
class Supply_demand(DeclarativeBase, TimestampMixin, MethodMixin):

    # 订货单
    __tablename__ = "supply_demand"

    batch_id = Column(BigInteger)  # 批次的id
    code = Column(String)  # 订单编号
    type = Column(String)  # 门店订货单(SD)、门店紧急订货单(HD)、主配类型(MD)
    reason_type = Column(String)  # 原因
    receive_by = Column(BigInteger)  # 收获门店id
    receive_name = Column(String)  # 收获门店名称
    distribute_by = Column(BigInteger)  # 供应商id
    store_secondary_id = Column(String)  # 门店编码
    store_type = Column(String)  # 门店类型
    distribution_type = Column(String)
    demand_date = Column(DateTime)  # 订货日期
    review_by = Column(BigInteger)  # 审核人
    arrival_date = Column(DateTime)  # 到货日期
    description = Column(String)  # 描述
    partner_id = Column(BigInteger)  # 商户id
    sub_type = Column(String)
    remark = Column(String)
    has_product = Column(Integer, default=0)
    is_sys = Column(Integer, default=0)
    created_by = Column(BigInteger)
    created_name = Column(String)
    updated_by = Column(BigInteger)
    updated_name = Column(String)
    status = Column(String)
    process_status = Column(String)
    is_plan = Column(Boolean, default=False)
    is_adjust = Column(Boolean, default=False)
    send_type = Column(String)
    bus_type = Column(String)
    is_auto = Column(BOOLEAN)
    # 期望到货时间
    expect_time = Column(String)

    @hybrid_property
    def demand_date_str(self):
        return datetime_to_strdate(self.demand_date)

    @hybrid_property
    def updated_at_str(self):
        return datetime_to_strdatetime(self.updated_at)
    
    @hybrid_property
    def arrival_date_str(self):
        return datetime_to_strdate(self.arrival_date)

    @classmethod
    def get_demand_list_by_args(cls, partner_id, user_id=None, store_ids=None, has_product=None, start_date=None, end_date=None, s_type=None, sub_type=None,
                                store_type=None, status=None, codes=None, is_plan=None, is_adjust=None, offset=None, limit=None, order=None, sort=None, 
                                plan_ids=None, ids=None, types=None):
        q = DBSession.query(cls).filter(cls.partner_id==partner_id)
        if ids:
            q = q.filter(cls.id.in_(ids))
        if start_date and end_date and end_date.year != 1970:
            q = q.filter(cls.demand_date.between(start_date, end_date))
        if codes:
            q = q.filter(cls.code.in_(codes))
        if status:
            q = q.filter(cls.status.in_(status))
        if store_ids:
            q = q.filter(cls.receive_by.in_(store_ids))
        if plan_ids and len(plan_ids)>0:
            q = q.filter(cls.batch_id.in_(plan_ids))
        if has_product and has_product != '':
            q = q.filter(cls.has_product == int(has_product))
        if s_type:
            q = q.filter(cls.type == s_type)
        if types:
            q = q.filter(cls.type.in_(types))
        if sub_type:
            q = q.filter(cls.sub_type == sub_type)
        if is_plan:
            q = q.filter(cls.is_plan == True)
        # 有点鸡肋，调整单为true时，用type=AD即可
        # if is_adjust:
        #     q = q.filter(cls.is_adjust == True)
        # else:
        #     q = q.filter(cls.is_adjust != True)
        if store_type:
            neq_store_type = []
            eq_store_type = []
            for t in store_type:
                if t.startswith("neq_"):
                    neq_store_type.append(t.replace("neq_", ""))
                else:
                    eq_store_type.append(t)
            if neq_store_type:
                q = q.filter(cls.store_type.notin_(neq_store_type))
            if eq_store_type:
                q = q.filter(cls.store_type.in_(eq_store_type))
        total = q.count()
        
        # 每日订货单先按“新建、已审核、已作废”状态，再按更新时间从早到晚的顺序自上而下显示
        q = q.order_by(case(value=cls.status, whens={'INITED': 1, 'APPROVED':2, 'COMMIT': 3, 'CANCELLED': 4, 'INVALID':5, 'DELETED':6}), cls.updated_at.desc())
        # if order != 'asc':
        #     order_sort = 'cls.{}.desc()'.format(sort)
        #     q = q.order_by(eval(order_sort))
        # else:
        #     order_sort = 'cls.{}'.format(sort)
        #     q = q.order_by(eval(order_sort))
        if limit:
            return q.offset(offset).limit(limit).all(), total
        else:
            return q.all(), total
    
    @classmethod
    def if_demand_exist(cls, partner_id, batch_id=None, code=None):
        db_session = session_maker()
        try:
            q = db_session.query(cls.id).filter(cls.partner_id == partner_id)
            if batch_id:
                q = q.filter(cls.batch_id == int(batch_id))
            if code:
                q = q.filter(cls.code == code)
            if q.count():
                return True
            else:
                return False
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_by_id(cls, partner_id, demand_id):
        db_session = session_maker()
        try:
            q = db_session.query(cls).filter(cls.partner_id == partner_id)
            if demand_id:
                q = q.filter(cls.id == int(demand_id))
            return q.first()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_store_demand_list_by_args(cls, partner_id, user_id=None, store_ids=None, has_product=None, start_date=None,
                                      end_date=None, s_type=None, sub_type=None, store_type=None, status=None,
                                      codes=None, is_plan=None, is_adjust=None, offset=None, limit=None, order=None,
                                      sort=None,plan_ids=None, ids=None, types=None, product_ids=None):
        q = DBSession.query(cls).filter(cls.partner_id==partner_id)
        if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
            q = q.outerjoin(Supply_demand_product, cls.id == Supply_demand_product.demand_id)
            q = q.filter(Supply_demand_product.product_id.in_(product_ids))
        if ids:
            q = q.filter(cls.id.in_(ids))
        if start_date and end_date and end_date.year != 1970:
            q = q.filter(cls.demand_date.between(start_date, end_date))
        if codes:
            q = q.filter(cls.code.in_(codes))
        if status:
            q = q.filter(cls.status.in_(status))
        if store_ids:
            q = q.filter(cls.receive_by.in_(store_ids))
        if plan_ids and len(plan_ids)>0:
            q = q.filter(cls.batch_id.in_(plan_ids))
        if has_product and has_product != '':
            q = q.filter(cls.has_product == int(has_product))
        if types:
            # 门店订货入口处，主配单/库存调整单只显示已审核的
            if 'MD' in types:
                types.remove('MD')
                if 'AD' in types:
                    types.remove('AD')
                    q = q.filter(or_(and_(cls.status == 'APPROVED', cls.type == 'MD'),
                                     and_(cls.status == 'APPROVED', cls.type == 'AD'),
                                          cls.type.in_(types)))
                else:
                    q = q.filter(or_(and_(cls.type == 'MD', cls.status == 'APPROVED'),
                                          cls.type.in_(types)))
            elif 'AD' in types:
                types.remove('AD')
                q = q.filter(or_(and_(cls.status == 'APPROVED', cls.type == 'AD'),
                                      cls.type.in_(types)))
            else:
                q = q.filter(cls.type.in_(types))
        if sub_type:
            q = q.filter(cls.sub_type == sub_type)
        if is_plan:
            q = q.filter(cls.is_plan == True)
        if store_type:
            neq_store_type = []
            eq_store_type = []
            for t in store_type:
                if t.startswith("neq_"):
                    neq_store_type.append(t.replace("neq_", ""))
                else:
                    eq_store_type.append(t)
            if neq_store_type:
                q = q.filter(cls.store_type.notin_(neq_store_type))
            if eq_store_type:
                q = q.filter(cls.store_type.in_(eq_store_type))
        if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
            q = q.group_by(cls.id)
        total = q.count()

        # 每日订货单先按“新建、已审核、已作废”状态，再按更新时间从早到晚的顺序自上而下显示
        q = q.order_by(case(value=cls.status, whens={'INITED': 1, 'APPROVED':2, 'COMMIT': 3, 'CANCELLED': 4, 'INVALID':5, 'DELETED':6}), cls.updated_at.desc())

        if limit:
            return q.offset(offset).limit(limit).all(), total
        else:
            return q.all(), total

    @classmethod
    def list_demand_with_products(cls, partner_id, 
                store_ids=None, has_product=None, start_date=None, end_date=None, \
                status=None, plan_ids=None, types=None, product_ids=None, store_type=None,\
                limit=None, offset=None):
        db_session = session_maker()
        q = db_session.query(cls.id)
        q = q.filter(cls.partner_id==partner_id)
        if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
            q = q.outerjoin(Supply_demand_product, cls.id == Supply_demand_product.demand_id)
            q = q.filter(Supply_demand_product.product_id.in_(product_ids))
        if start_date and end_date and end_date.year != 1970:
            q = q.filter(cls.demand_date.between(start_date, end_date))
        if status:
            q = q.filter(cls.status==status)
        if store_ids:
            q = q.filter(cls.receive_by.in_(store_ids))
        if plan_ids and len(plan_ids)>0:
            q = q.filter(cls.batch_id.in_(plan_ids))
        if has_product and has_product != '':
            q = q.filter(cls.has_product == int(has_product))
        if types:
            # 门店订货入口处，主配单/库存调整单只显示已审核的
            if 'MD' in types:
                types.remove('MD')
                if 'AD' in types:
                    types.remove('AD')
                    q = q.filter(or_(and_(cls.status == 'APPROVED', cls.type == 'MD'),
                                     and_(cls.status == 'APPROVED', cls.type == 'AD'), 
                                          cls.type.in_(types)))
                else:
                    q = q.filter(or_(and_(cls.type == 'MD', cls.status == 'APPROVED'), 
                                          cls.type.in_(types)))
            elif 'AD' in types:
                types.remove('AD')
                q = q.filter(or_(and_(cls.status == 'APPROVED', cls.type == 'AD'), 
                                      cls.type.in_(types)))
            else:
                q = q.filter(cls.type.in_(types))
        
        if store_type:
            neq_store_type = []
            eq_store_type = []
            for t in store_type:
                if t.startswith("neq_"):
                    neq_store_type.append(t.replace("neq_", ""))
                else:
                    eq_store_type.append(t)
            if neq_store_type:
                q = q.filter(cls.store_type.notin_(neq_store_type))
            if eq_store_type:
                q = q.filter(cls.store_type.in_(eq_store_type))
        demand_ids = q.all()
        if demand_ids and len(demand_ids)>0:
            demand_ids = [i[0] for i in demand_ids]
            qp = db_session.query(cls.code, cls.status, cls.type, cls.demand_date, cls.receive_name,\
                cls.store_secondary_id, cls.remark, cls.arrival_date, cls.updated_name, cls.updated_at,\
                Supply_demand_product.product_code, Supply_demand_product.product_name, \
                Supply_demand_product.min_quantity, Supply_demand_product.max_quantity, Supply_demand_product.increment_quantity,\
                Supply_demand_product.distribution_type, Supply_demand_product.quantity, Supply_demand_product.unit_name,\
                Supply_demand_product.spec, Supply_demand_product.arrival_days,\
                Supply_demand_product.product_category_name, Supply_demand_product.sale_type, cls.batch_id
                ).join(Supply_demand_product, Supply_demand_product.demand_id == cls.id)
            qp = qp.filter(cls.id.in_(demand_ids))
            total = qp.count()
            
            # 每日订货单先按“新建、已审核、已作废”状态，再按更新时间从早到晚的顺序自上而下显示
            qp = qp.order_by(case(value=cls.status, whens={'INITED': 1, 'APPROVED':2, 'COMMIT': 3, 'CANCELLED': 4, 'INVALID':5, 'DELETED':6}), cls.updated_at.desc())

            if limit:
                return qp.offset(offset).limit(limit).all(), total
            else:
                return qp.all(), total
        else:
            return [], 0

    @classmethod
    def get_product_count_by_ids(cls, demand_ids:list, partner_id):
        '''
        return:
            {
                136523462364 : 5,
                63246262762364 : 7,
                23462372 : 4
            }
        '''
        products_count = DBSession.query(func.count(Supply_demand_product.id), cls.id).join(cls, cls.id==Supply_demand_product.demand_id).filter(
            cls.id.in_(demand_ids)).filter(cls.partner_id==partner_id).group_by(cls.id).all()
        result = {}
        if products_count:
            result = {i[1]:i[0] for i in products_count}
        return result

    @classmethod
    @db_commit
    def delete_demand_and_product_by_id(cls, demand_id):
        DBSession.query(cls).filter(cls.id==demand_id).delete()
        DBSession.query(Supply_demand_product).filter(Supply_demand_product.demand_id==demand_id).delete()

    @classmethod
    def query_by_order_args(cls, bizdt, store_id, partner_id, status):
        # 查询未处理成要货单的订货单
        return DBSession.query(cls).filter(cls.demand_date==bizdt).filter(cls.receive_by==store_id).filter(cls.partner_id==partner_id).filter(cls.status.in_(status)).all()

    @classmethod
    def get_by_batch_store_id(cls, batch_store_id):
        return DBSession.query(cls).filter(cls.batch_id==batch_store_id).first()

    @classmethod
    def get_not_handle_demand_by_store_id(cls, store_id, partner_id):
        q = DBSession.query(cls).filter(cls.receive_by==store_id).filter(cls.partner_id==partner_id)
        q = q.filter(cls.process_status==Demand_enum.INITED.code)
        
    @classmethod
    def get_by_ids(cls, demand_ids, partner_id):
        q = DBSession.query(cls).filter(cls.id.in_(demand_ids)).filter(cls.partner_id==partner_id)
        return q.all()


    @classmethod
    def get_by_code(cls, demand_code, partner_id):
        q = DBSession.query(cls).filter(cls.code==demand_code).filter(cls.partner_id==partner_id)
        return q.first()

    @classmethod
    def get_overtime_undone_demand(cls, status, process_status, overtime, partner_id):
        db_session = session_maker()
        # 一天之内超过overtime mins未处理
        max_update_time = datetime.now()-timedelta(minutes=overtime)
        min_update_time = max_update_time-timedelta(days=1)
        try:
            query = db_session.query(cls).filter(cls.status.in_(status)).filter(cls.process_status.in_(process_status))
            query = query.filter(cls.updated_at>=min_update_time).filter(cls.updated_at<=max_update_time).filter(cls.partner_id==partner_id)
            return query.all(), query.count()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def update_demands(cls, updated_demands, log_list=None):
        try:
            DBSession.bulk_update_mappings(cls, updated_demands)
            if log_list and len(log_list)>0:
                DBSession.bulk_insert_mappings(Supply_demand_log, log_list)
            DBSession.commit()
        except Exception as e:
            raise e
        finally:
            DBSession.close()
        return True
    
    @classmethod
    def create_demand_and_product(cls, demand_details, demand_products, demand_log=None):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(cls, demand_details)
            db_session.bulk_insert_mappings(Supply_demand_product, demand_products)
            if demand_log and len(demand_log) > 0 and demand_log != [None]:
                db_session.bulk_insert_mappings(Supply_demand_log, demand_log)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_demand_by_code(cls, demand_code, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(
                cls.code == demand_code).filter(cls.partner_id == partner_id)
            return query.first()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
