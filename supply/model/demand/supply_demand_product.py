# -*-: coding:utf-8 -*-

import logging
from sqlalchemy import (DECIMAL, BigInteger, Column, DateTime, Integer, String,
                        Text, Boolean, and_)
from sqlalchemy.sql.expression import bindparam

from supply.client.metadata_service import metadata_service
from supply.error.demand import DemandError, ProductError
from supply.utils.enums import Demand_type, Tag_type
from supply.utils.helper import check_is_distr, get_today_datetime, get_quantity
from supply.utils.snowflake import gen_snowflake_id
from supply.driver.mysql import session_maker

from .. import (DBSession, DeclarativeBase, MethodMixin, TimestampMixin,
                db_commit)


class Supply_demand_product(DeclarativeBase, TimestampMixin, MethodMixin):
    __tablename__ = "supply_demand_product"

    demand_id = Column(BigInteger)
    partner_id = Column(BigInteger, index=True)  # 商户id
    product_id = Column(BigInteger)
    product_code = Column(String)
    product_name = Column(String)
    product_category_id = Column(BigInteger)
    product_category_name = Column(String)
    sale_type = Column(String)
    product_type = Column(String)
    unit_id = Column(BigInteger)
    unit_name = Column(String)
    unit_spec = Column(String)
    spec = Column(String)
    accounting_unit_id = Column(BigInteger)
    accounting_unit_name = Column(String)
    accounting_unit_spec = Column(String)
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    finished_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 成品订货
    tea_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 茶饮订货
    bread_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 热卖订货
    raw_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 原料订货
    suggest_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    confirmed_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    accounting_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    min_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    max_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    increment_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    purchase_tax = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    purchase_price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    arrival_days = Column(Integer)
    distribution_circle = Column(String)
    distribution_type = Column(String)
    distribute_by = Column(BigInteger)
    storage_type = Column(String)
    updated_by = Column(String)
    cycle_extends = Column(String)

    @classmethod
    def get_by_product_id_and_demand_id_and_distr_type(cls, product_id: int, distribution_type: str, demand_id: int):
        return DBSession.query(cls).filter(and_(cls.product_id == product_id, cls.demand_id == demand_id,
                                                cls.distribution_type == distribution_type)).first()

    @classmethod
    def get_product_by_demand_id_and_product_id(cls, demand_id: int, product_id: int):
        return DBSession.query(cls).filter(and_(cls.demand_id == demand_id, cls.product_id == product_id)).first()

    @classmethod
    def get_by_demand_id(cls, demand_id: int):
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.demand_id == demand_id)
            return query.all(), query.count()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_finished_product_by_id(cls, demand_id: int):
        # 获取订单已订货成品数据(排除新零售)
        return DBSession.query(cls).filter(and_(cls.demand_id == demand_id, cls.product_type == "FINISHED",
                                                cls.sale_type.notlike("NEW-RETAIL%"))).all()

    @classmethod
    def get_tea_product_by_id(cls, demand_id: int):
        # 获取订单的茶饮订货商品
        return DBSession.query(cls).filter(and_(cls.demand_id == demand_id, cls.sale_type == "TEA")).all()

    @classmethod
    def get_bread_product_by_id(cls, demand_id: int):
        # 获取订单的面包订货商品
        return DBSession.query(cls).filter(and_(cls.demand_id == demand_id, cls.sale_type == "BREAD")).all()

    @classmethod
    def get_tea_and_bread_by_id(cls, demand_id: int):
        # 获取茶饮和面包的订货商品
        return DBSession.query(cls).filter(and_(cls.demand_id == demand_id, cls.sale_type.in_("TEA", "BREAD"))).all()

    @classmethod
    def get_raw_product_by_id(cls, demand_id: int):
        # 获取原料订货
        return DBSession.query(cls).filter(and_(cls.demand_id == demand_id, cls.product_type == "RAW")).all()

    @classmethod
    @db_commit
    def delete_by_demand_id(cls, demand_id):
        return DBSession.query(cls).filter(cls.demand_id == demand_id).delete()

    @classmethod
    @db_commit
    def reset_quantity(cls, demand_id):
        DBSession.query(cls).filter(cls.demand_id == demand_id).update({cls.quantity: 0.0})

    @classmethod
    @db_commit
    def delete_by_id(cls, id):
        return DBSession.query(cls).filter(cls.id == id).delete()

    @classmethod
    @db_commit
    def delete_by_ids(cls, ids):
        stmt = cls.__table__.delete().where(cls.id.in_(ids))
        DBSession.execute(stmt)

    @classmethod
    def insert_finished_product(cls, product_id, quantity, user_id, demand_id):
        # 成品不会挂在配送或直送区域
        product = metadata_service.get_product(product_id, include_units=True)
        if not product:
            raise ProductError("此成品不存在, product_id:{}".format(product_id))
        units = product.get('units')
        unit_id = 0
        if units:
            for i in units:
                if i.get("order"):
                    unit_id = int(i.get("id", 0))
        unit = metadata_service.get_unit(unit_id)
        demand_product = Supply_demand_product(
            demand_id=demand_id,
            product_id=int(product_id),
            product_code=product.get("code"),
            product_name=product.get("name"),
            product_category_id=int(product.get('category', 0)),
            sale_type=product.get('product_type'),
            product_type=product.get('product_type'),
            unit_id=unit_id,
            unit_name=unit.get('name'),
            quantity=float(quantity),
            storage_type=product.get("storage_type"),
            updated_by=user_id
        )
        return demand_product

    @classmethod
    def insert_by_demand_id_and_receive(cls, demand_id, receive_by, product_id, quanity, partner_id, user_id,
                                        distribution_type=None, type=Demand_type.SD.code, tag_type=Tag_type.RAW.code,
                                        order_date=None, unit_id=None):
        """
        根据订货单，门店，商品插入一个订货商品(默认只能订原料和新零售成品)
        distribution_type:物流模式，如果不传则在配送区域或者采购区域依次获取
        """
        product_resp = {}
        temp_distribution_type = None
        print('distribution_type', distribution_type)

        product_dict = dict()
        product_list = metadata_service.get_product_list(ids=[product_id],
                                                         return_fields="id,code,name,status,allow_order",
                                                         filters={"status": "ENABLED", "allow_order": True},
                                                         partner_id=partner_id,
                                                            user_id=user_id).get('rows', [])
        product_info= None
        for c in product_list:
            product_info = c


        temp_distribution_type = Demand_type.NMD.code
        product_resp = metadata_service.get_list_valid_product_for_distr_by_id(
            int(receive_by),
            product_ids=[int(product_id)],
            partner_id=partner_id, user_id=user_id)

        product = None
        print('product_resp--', product_id,"=====",product_resp)
        if product_resp.get("rows"):
            for p in product_resp.get("rows"):
                if int(p['product_id']) == int(product_id):
                    product = p

        temp_distribution_type = product['logistic_mode']
        units = product_info.get('units')
        unit_rate = 1
        ### 调整单保存单位
        # if type == Demand_type.AD.code and unit_id:
        #     product["unit_id"] = unit_id
        # if units:
        #     order_flag = False
        #     for u in units:
        #         # 区域关联的单位是否存在且是否为可订货状态
        #         if u.get("order"):
        #             unit_rate = float(u.get("rate", 1))
        #             product['unit_id'] = int(u.get('id'))
        #             order_flag = True
        #             break
        #     if not order_flag:
        #         raise ProductError("此商品未设置订货单位，请检查主档设置b, product_id:{}".format(product_id))
        # else:
        #     raise ProductError("此商品未设置订货单位，请检查主档设置b, product_id:{}".format(product_id))

        arrival_days = 0
        if product.get("main_order_arrival_day"):
            arrival_days = int(product.get("main_order_arrival_day"))
        # if type == Demand_type.SD.code:
        #     if not order_date:
        #         order_date = get_today_datetime()
        #     circle_type = product.get("circle_type")
        #     cycles = product.get('cycles')
        #     if not circle_type:
        #         raise DemandError("此订货类型为门市订货，未配置配送周期类型，请检查主档配置")
        #     need_flag, arrival_days = check_is_distr(circle_type, order_date, product.get("interval_days"),
        #                                              product.get("start_date"), cycles, arrival_days)
        #     if not need_flag:
        #         raise DemandError("未到订货周期, product_id:{}".format(product_id))
        # else:
        #     allow_main_order = product.get("allow_main_order")
        #     if not allow_main_order and type == Demand_type.MD.code:
        #         raise DemandError("{}不允许主配订货".format(product.get('name')))

        order_unit = metadata_service.get_unit(int(product.get("order_unit_id")), partner_id=partner_id,
                                               user_id=user_id, )
        # 数量限制去除， for lelecha
        # if type != Demand_type.AD.code:
        #     quanity = get_quantity(quanity, float(product.get("max_number", 0)), float(product.get("min_number", 0)),
        #                            float(product.get("increment_number", 0)))
        demand_product = Supply_demand_product(
            demand_id=demand_id,
            product_id=int(product_id),
            product_code=product_info.get("code"),
            product_name=product_info.get("name"),
            product_category_id=int(product.get('product_category_id', 0)),
            increment_quantity=float(product.get("incr_qty", 0)),
            max_quantity=float(product.get("max_qty", 0)),
            min_quantity=float(product.get("min_qty", 0)),
            arrival_days=arrival_days,
            distribution_circle=product.get('circle_type'),
            sale_type=product.get('sale_type'),
            product_type=product.get('product_type'),
            unit_id=int(order_unit['id']),
            unit_spec=order_unit.get("code", "无"),
            unit_name=order_unit.get('name', "无"),
            unit_rate=unit_rate,
            spec=product.get('spec', "无"),
            quantity=quanity,
            storage_type=product.get("storage_type"),
        )
        if temp_distribution_type == Demand_type.NMD.code:
            demand_product.distribute_by = int(product.get("distribution_center_id", 0))
            demand_product.distribution_type = Demand_type.NMD.code
        elif temp_distribution_type == Demand_type.PUR.code:
            demand_product.distribute_by = int(product.get("distribution_center_id", 0))
            demand_product.distribution_type = Demand_type.PUR.code
            demand_product.purchase_price = float(product.get("purchase_price", 0))
            demand_product.purchase_tax = float(product.get('purchase_tax', 0))
        # 面包
        if distribution_type == 'BREAD':
            demand_product.distribute_by = int(product.get("distribution_center_id", 0))
            demand_product.distribution_type = 'BREAD'
        return demand_product

    @classmethod
    def update_products(cls, updated_products):
        try:
            DBSession.bulk_update_mappings(cls, updated_products)
            DBSession.commit()
        except Exception as e:
            raise e
        finally:
            DBSession.close()
        return True