# -*-: coding:utf-8 -*-

from datetime import datetime

from sqlalchemy import (BOOLEAN, BigInteger, Boolean, Column, DateTime, DECIMAL,
                        Integer, String, Text, and_, extract)
from sqlalchemy.sql.expression import between

from supply.utils.snowflake import gen_snowflake_id

from .. import (DBSession, DeclarativeBase, MethodMixin, TimestampMixin,
                db_commit)


class Supply_first_delivery(DeclarativeBase, TimestampMixin, MethodMixin):

    __tablename__ = "supply_first_delivery"

    partner_id = Column(BigInteger)
    store_id = Column(BigInteger)
    store_code = Column(String)
    store_name = Column(String)
    order_id = Column(BigInteger)
    order_code = Column(String)
    order_date = Column(DateTime)
    product_id = Column(BigInteger)
    product_code = Column(String)
    product_name = Column(String)
    distr_quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)
    unit_id = Column(BigInteger)
    unit_name = Column(String)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)

    @classmethod
    def get_first_delivery_list(cls, store_code, product_code, start_order_date, end_order_date, limit, offset, order, partner_id, user_id):
        q = DBSession.query(cls).filter(cls.partner_id==partner_id).filter(cls.order_date.between(start_order_date, end_order_date))
        if store_code:
            q = q.filter(cls.store_code==store_code)
        if product_code:
            q = q.filter(cls.product_code==product_code)
        # if order_date:
        #     q = q.filter(cls.order_date==order_date)
        if order != 'asc':
            q = q.order_by(cls.updated_at.desc())
        total = q.count()
        if limit:
            return q.offset(offset).limit(limit).all(), total
        else:
            return q.all(), total

    @classmethod
    def get_first_product_by_code(cls, store_code, product_code, partner_id):
        q = DBSession.query(cls).filter(cls.store_code==store_code).filter(cls.product_code==product_code)
        q = q.filter(cls.partner_id==partner_id)
        return q.first()

    @classmethod
    def get_first_delivery_by_store(cls, store_id, partner_id):
        q = DBSession.query(cls).filter(cls.store_id==store_id)
        q = q.filter(cls.partner_id==partner_id)
        return q.all()