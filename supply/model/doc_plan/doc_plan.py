# -*- coding: utf-8 -*-
import sys
import traceback
import logging

from supply.driver.mysql import session_maker
from supply.model import DeclarativeBase, TimestampMixin
from sqlalchemy import (Column, BigInteger, Integer, String, DateTime, DECIMAL, Boolean,TEXT)
from supply.utils.helper import get_guid
from google.protobuf.timestamp_pb2 import Timestamp


class PlanDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'doc_plan'
    id = Column(BigInteger, primary_key=True, default=get_guid())
    partner_id = Column(BigInteger)
    code = Column(String(50))
    name = Column(String(50))
    plan_type = Column(String(10))
    product_method = Column(String(10))
    branch_method = Column(String(10))
    # 循环类型
    method = Column(String(10))

    # 月循环规则 1,2,5
    month_method = Column(String(100))
    # 循环规则对应未来规则间隔
    sub_month_method = Column(String(100))

    # 周循环规则 10101
    week_method = Column(String(100))
    # 循环规则对应未来规则间隔
    sub_week_method = Column(String(100))

    # 日循环规则
    day_method = Column(String(100))
    # 循环规则对应未来规则间隔
    sub_day_method = Column(String(100))
    interval = Column(Integer, default=0)

    status = Column(String(10))
    start = Column(DateTime)
    end = Column(DateTime)
    before = Column(Integer, default=0)
    start_before = Column(DateTime)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    calculate_inventory = Column(Boolean, default=False)
    remark = Column(String(255))
    request_id = Column(BigInteger)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))
    doc_deadline = Column(DateTime)
    doc_cancel = Column(DateTime)
    last_time = Column(DateTime)
    next_time = Column(DateTime)
    is_recreate = Column(Boolean)
    sub_type = Column(String(50))
    extend = Column(TEXT)


class PlanStoreDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'doc_plan_store'
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)
    user_id = Column(BigInteger)
    plan_id = Column(BigInteger)
    store_id = Column(BigInteger)
    store_type = Column(String(10))
    store_name = Column(String(50))
    store_code = Column(String(50))
    deleted = Column(Boolean)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))


class PlanBranchDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'doc_plan_branch'
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)
    user_id = Column(BigInteger)
    plan_id = Column(BigInteger)
    branch_id = Column(BigInteger)
    branch_type = Column(String(10))
    branch_name = Column(String(50))
    branch_code = Column(String(50))
    deleted = Column(Boolean)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))


class PlanCategoryDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'doc_plan_category'
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)
    user_id = Column(BigInteger)
    plan_id = Column(BigInteger)
    category_id = Column(BigInteger)
    category_code = Column(String(50))
    category_name = Column(String(50))
    deleted = Column(Boolean)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))


class PlanProductDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'doc_plan_product'
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)
    user_id = Column(BigInteger)
    plan_id = Column(BigInteger)
    product_id = Column(BigInteger)
    product_code = Column(String(50))
    product_name = Column(String(50))
    deleted = Column(Boolean)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    # 创建人名称
    created_name = Column(String(50))
    # 更新人名称
    updated_name = Column(String(50))


class DocBatchPlanDB(DeclarativeBase, TimestampMixin):
    __tablename__ = "doc_batch_plan"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger)
    user_id = Column(BigInteger)
    batch_id = Column(BigInteger)
    plan_id = Column(BigInteger)
    plan_date = Column(DateTime)
    plan_type = Column(String(10))
    process_status = Column(String(10))
    reason = Column(String(255))
    method = Column(String(10))


class DocBatchPlanBranchDB(DeclarativeBase, TimestampMixin):
    __tablename__ = "doc_batch_plan_branch"
    id = Column(BigInteger, primary_key=True, default=get_guid)
    plan_batch_id = Column(BigInteger)
    partner_id = Column(BigInteger)
    user_id = Column(BigInteger)
    batch_id = Column(BigInteger)
    branch_id = Column(BigInteger)
    plan_id = Column(BigInteger)
    plan_date = Column(DateTime)
    plan_type = Column(String(10))
    process_status = Column(String(10))
    reason = Column(String(255))
    method = Column(String(10))
    retry_count = Column(BigInteger)


class PlanControlDocStatusDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'doc_plan_control_doc_status'
    id = Column(BigInteger, primary_key=True, default=get_guid())
    partner_id = Column(BigInteger, index=True)
    plan_id = Column(BigInteger)
    request_id = Column(BigInteger)
    time = Column(String(50))
    time_around = Column(String(50))
    start_status = Column(String(100))
    end_status = Column(String(100))
    # 单据过滤条件:
    # 空单据: NULL
    # 非空单据: NOT_NULL
    # 不限制: ALL
    doc_filter = Column(String(30))


class DocPlanRepository(object):
    def __init__(self):
        pass

    def get_plan_code_list_by_name(self, plan_name=None, partner_id=None, plan_type=None):
        """根据计划名称和类型，查询计划编号列表"""
        db_session = session_maker()
        try:
            query = db_session.query(PlanDB.code).filter(PlanDB.partner_id == partner_id,
                                                         PlanDB.name == plan_name,
                                                         PlanDB.plan_type == plan_type)
            plans = query.all()
            return [plan.code for plan in plans] if plans else []
        except Exception as e:
            logging.error("Get Plan Code List By Name Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def list_doc_plan(self, plan_name=None, partner_id=None, user_id=None):
        """查询计划列表
        供每日订货根据计划名称模糊查询使用"""
        db_session = session_maker()
        try:
            query = db_session.query(PlanDB).filter(PlanDB.partner_id == partner_id)
            if plan_name:
                query = query.filter(PlanDB.name.like('%' + plan_name + '%'))
            return query.all()
        except Exception as e:
            logging.error("List Doc Plan Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()
    
    def list_doc_plan_by_ids(self, plan_ids, partner_id=None, user_id=None):
        """查询计划列表
        供每日订货根据计划名称模糊查询使用"""
        db_session = session_maker()
        try:
            query = db_session.query(PlanDB).filter(PlanDB.partner_id == partner_id)
            query = query.filter(PlanDB.id.in_(plan_ids))
            return query.all()
        except Exception as e:
            logging.error("List Doc Plan Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
        finally:
            db_session.close()

    def get_by_plan_ids(self, plan_ids, partner_id):
        db_session = session_maker()
        try:
            query = db_session.query(PlanControlDocStatusDB.plan_id,
                                     PlanControlDocStatusDB.time,
                                     PlanControlDocStatusDB.start_status,
                                     PlanControlDocStatusDB.end_status,
                                     PlanControlDocStatusDB.time_around,
                                     PlanControlDocStatusDB.doc_filter)
            return_dbs = query.filter(PlanControlDocStatusDB.plan_id.in_(plan_ids),
                                      PlanControlDocStatusDB.partner_id == partner_id).all()
            return return_dbs
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_valid_control_status(self, partner_id, plan_ids=None):
        db_session = session_maker()
        try:
            query = db_session.query(PlanControlDocStatusDB).filter(
                PlanControlDocStatusDB.partner_id == partner_id)
            if plan_ids and isinstance(plan_ids, list):
                query = query.filter(PlanControlDocStatusDB.plan_id.in_(plan_ids))
            return query.all()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()


doc_plan_repository = DocPlanRepository()
