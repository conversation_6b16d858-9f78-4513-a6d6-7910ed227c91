# -*-: coding:utf-8 -*-

from datetime import datetime

from sqlalchemy import (BigInteger, Column, DateTime, Integer, String, Text,
                        and_)
from sqlalchemy.orm.exc import NoResultFound

from supply.driver.mysql import DummyTransaction
from supply.utils.snowflake import gen_snowflake_id

from . import (DBSession, DeclarativeBase, MethodMixin, TimestampMixin,
               db_commit)
from .supply_doc_code_config_main import Supply_doc_code_config_main
from .supply_doc_code_config_partner import Supply_doc_code_config_partner

class Supply_doc_code(DeclarativeBase, TimestampMixin, MethodMixin):

    __tablename__ = "supply_doc_code"

    config_id = Column(BigInteger)
    value = Column(BigInteger)
    created_by = Column(String)
    updated_by = Column(String)
    partner_id = Column(BigInteger)

    @classmethod
    def get_code_by_type(cls, code_type, partner_id, user_id):
        """
        获得单据编号
        """
        if not code_type:
            raise ValueError('code_type cannot be none')
        if not partner_id:
            raise ValueError('partner_id cannot be none')
        # 如果命中特殊租户的前缀
        q = DBSession.query(Supply_doc_code_config_main, Supply_doc_code_config_partner).outerjoin(
            Supply_doc_code_config_partner,
            and_(Supply_doc_code_config_partner.config_id == Supply_doc_code_config_main.id,
                 Supply_doc_code_config_partner.partner_id == partner_id)).filter(
            Supply_doc_code_config_main.type == code_type).filter(
            Supply_doc_code_config_main.partner_id == partner_id)
        # 如果没有命中特殊租户的前缀，则用通用租户
        if q.count() == 0:
            q = DBSession.query(Supply_doc_code_config_main, Supply_doc_code_config_partner).outerjoin(
                Supply_doc_code_config_partner,
                and_(Supply_doc_code_config_partner.config_id == Supply_doc_code_config_main.id,
                    Supply_doc_code_config_partner.partner_id == partner_id)).filter(
                Supply_doc_code_config_main.type == code_type).filter(
                Supply_doc_code_config_main.partner_id == 0)
        configs = None
        try:
            configs = q.first()
        except NoResultFound as e:
            configs = None
        if not configs:
            return None
        code_config_main, code_config_partner = configs
        if not code_config_main:
            return None
        prefix = code_config_main.prefix
        value_length = code_config_main.value_length
        if code_config_partner:
            if code_config_partner.prefix:
                prefix = code_config_partner.prefix
            if code_config_partner.value_length:
                value_length = code_config_partner.value_length

        if not prefix:
            prefix = ''
        doc_value = 1
        today = datetime.now()

        doc_value_str = cls.__lock_code_for_update(code_config_main.id, partner_id, today, partner_id)

        if len(doc_value_str) < value_length:
            dif_length = value_length - len(doc_value_str)
            while dif_length > 0:
                doc_value_str = '0%s' % doc_value_str
                dif_length -= 1
        # return '%s-%s-%s' % (prefix, today.strftime('%y%m%d'), doc_value_str)
        # return '%s%s' % (today.strftime('%y%m%d'), doc_value_str)
        return '%s%s%s' % (prefix, today.strftime('%y%m%d'), doc_value_str)

    @classmethod
    def __lock_code_for_update(cls, code_config_main_id, partner_id, today, user_id=None) -> str:
        with DummyTransaction(auto_commit=False) as trans:
            DBSession = trans.scope_session
            q = DBSession.query(cls).filter(cls.config_id == code_config_main_id,
                                                cls.partner_id == partner_id)
            doc_code_db = None
            try:
                doc_code_db = q.with_lockmode("update").first()
            except NoResultFound as e:
                doc_code_db = None
            if not doc_code_db:
                doc_code_db = Supply_doc_code()
                doc_code_db.partner_id = partner_id
                doc_code_db.value = 1
                doc_code_db.config_id = code_config_main_id
                if user_id:
                    doc_code_db.created_by = doc_code_db.updated_by = str(user_id)
                doc_code_db.created = doc_code_db.updated = datetime.now()
                DBSession.add(doc_code_db)
            else:

                today_start = datetime(
                    int(today.year), int(today.month), int(today.day))
                if doc_code_db.updated_at < today_start:
                    doc_code_db.value = 1
                else:
                    doc_code_db.value += 1
                doc_code_db.updated_at = datetime.now()
                if user_id:
                    doc_code_db.updated_by = str(user_id)
            DBSession.commit()
            doc_value = doc_code_db.value
            doc_value_str = str(doc_value)
            return doc_value_str
