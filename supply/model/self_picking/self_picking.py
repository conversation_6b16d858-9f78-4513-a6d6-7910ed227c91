# -*- coding: utf-8 -*-
import logging
import traceback
import sys

from datetime import datetime
from .. import DeclarativeBase, MethodMixin
from sqlalchemy import Column, BigInteger, String, DateTime, func, DECIMAL, Text
from supply.utils.helper import get_guid
from supply.driver.mysql import session_maker


class StoreSelfPicking(DeclarativeBase, MethodMixin):
    """门店自采单"""
    __tablename__ = "supply_store_self_picking"

    class OrderStatus(object):
        INITED = 'INITED'        # 新建
        SUBMITTED = 'SUBMITTED'  # 已提交
        REJECTED = 'REJECTED'    # 已驳回
        APPROVED = 'APPROVED'    # 已审核

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 商户id
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)    # 创建人姓名
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)    # 更新人姓名
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.utcnow())
    code = Column(String, index=True)      # 订单编号
    status = Column(String, default=OrderStatus.INITED)  # 订单状态
    process_status = Column(String, default=OrderStatus.INITED)  # 操作状态*预留
    order_date = Column(DateTime, index=True)   # 采购日期
    branch_id = Column(BigInteger)  # 门店id
    branch_code = Column(String)    # 门店编号
    branch_name = Column(String)    # 门店名称
    branch_type = Column(String)     # 区分门店/加工中心-STORE/MACHINING_CENTER
    reason = Column(String, index=True)  # 原因
    remark = Column(String)          # 备注
    attachments = Column(Text)       # 附件 "[ats1, ats2,...]"
    total_amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 合计金额
    request_id = Column(BigInteger, index=True, unique=True)  # 幂等性校验请求id


class StoreSelfPickingProduct(DeclarativeBase, MethodMixin):
    """门店自采单商品明细"""
    __tablename__ = "supply_store_self_picking_product"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)  # 商户id
    created_by = Column(BigInteger)  # 创建人
    created_name = Column(String)  # 创建人姓名
    updated_by = Column(BigInteger)  # 更新人
    updated_name = Column(String)  # 更新人姓名
    created_at = Column(DateTime, default=datetime.utcnow())
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.utcnow())
    main_id = Column(BigInteger, index=True)  # 主单id
    product_id = Column(BigInteger)  # 商品id
    product_code = Column(String)  # 商品code
    product_name = Column(String)  # 商品名称
    product_spec = Column(String)  # 商品规格
    unit_id = Column(BigInteger)  # 单位id
    unit_name = Column(String)  # 单位名称
    unit_spec = Column(String)  # 单位规格
    unit_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 单位转换率
    tax_rate = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 税率
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 采购数量
    price = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 单价
    amount = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=0.0)  # 金额(单价*数量)

    @classmethod
    def add_picking_product(cls, product_data=None):
        """添加商品信息列表"""
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(cls, product_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Add Picking Product Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                       traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    @classmethod
    def get_picking_product_by_id(cls, partner_id=None, main_id=None):
        """根据订单id拉取商品信息"""
        db_session = session_maker()
        try:
            query = db_session.query(cls).filter(cls.main_id == main_id, cls.partner_id == partner_id)
            return query.all()
        except Exception as e:
            logging.error("Get Picking Product Failed:{}".format(e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()


class StoreSelfPickingLog(DeclarativeBase, MethodMixin):
    """门店自采单操作日志表"""
    __tablename__ = "supply_store_self_picking_log"

    id = Column(BigInteger, primary_key=True, default=get_guid)
    partner_id = Column(BigInteger, index=True)
    created_by = Column(BigInteger)
    created_name = Column(String(50))
    main_id = Column(BigInteger, index=True)  # 关联的自采单id
    action = Column(String(50))  # 操作状态
    created_at = Column(DateTime, default=datetime.utcnow())


class StoreSelfPickingDB(object):
    """门店自采单数据库操作"""

    def get_self_picking_by_id(self, receipt_id=None, request_id=None, partner_id=None):
        """获取一条单据记录"""
        db_session = session_maker()
        try:
            query = db_session.query(StoreSelfPicking).filter(
                StoreSelfPicking.partner_id == partner_id)
            if receipt_id:
                query = query.filter(StoreSelfPicking.id == receipt_id)
                return query.first()
            if request_id:
                query = query.filter(StoreSelfPicking.request_id == request_id)
                return query.first()
        except Exception as e:
            logging.error("Get Self Picking Error:{}".format(e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_self_picking(self, receipt_data, product_list, receipts_log):
        """创建自采单"""
        db_session = session_maker()
        try:
            obj = StoreSelfPicking()
            for k, v in receipt_data.items():
                if k in dir(obj) and v is not None:
                    setattr(obj, k, v)
            db_session.add(obj)
            db_session.bulk_insert_mappings(StoreSelfPickingProduct, product_list)
            # 保存操作日志
            self_picking_db.create_self_picking_log(receipts_log=receipts_log)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Self Picking Error:{}".format(e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def create_self_picking_log(self, receipts_log):
        """保存自采单操作日志
        :param receipts_log 操作日志 dict
        """
        db_session = session_maker()
        try:
            receipts_data = [receipts_log]
            db_session.bulk_insert_mappings(StoreSelfPickingLog, receipts_data)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Create Self Picking Receipts Log Error:{}".format(e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def list_self_picking(self, partner_id=None, start_date=None, end_date=None, code=None, status=None,
                          branch_ids=None, branch_type=None, reason=None, limit=-1, offset=None, ids=None,
                          include_total=None, order=None, sort=None, product_ids=None):
        """门店自采单列表查询操作"""
        db_session = session_maker()
        try:
            query = db_session.query(StoreSelfPicking)
            if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                query = query.outerjoin(StoreSelfPickingProduct, StoreSelfPicking.id == StoreSelfPickingProduct.main_id)
                query = query.filter(StoreSelfPickingProduct.product_id.in_(product_ids))
            if partner_id:
                query = query.filter(StoreSelfPicking.partner_id == partner_id)
            if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                query = query.filter(StoreSelfPicking.order_date >= start_date)
            if end_date and isinstance(end_date, datetime) and end_date.year != 1970:
                query = query.filter(StoreSelfPicking.order_date <= end_date)
            if code:
                query = query.filter(StoreSelfPicking.code == code)
            if isinstance(ids, list) and len(ids) > 0:
                query = query.filter(StoreSelfPicking.id.in_(ids))
            if branch_type:
                query = query.filter(StoreSelfPicking.branch_type == branch_type)
            if status and isinstance(status, list):
                query = query.filter(StoreSelfPicking.status.in_(status))
            if branch_ids and isinstance(branch_ids, list):
                query = query.filter(StoreSelfPicking.branch_id.in_(branch_ids))
            if reason:
                query = query.filter(StoreSelfPicking.reason == reason)
            if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                query = query.group_by(StoreSelfPicking.id)
            # 排序方式
            if not sort:
                sort = "updated_at"
            if order == "asc":
                sort_str = "StoreSelfPicking.{}".format(sort)
                query = query.order_by(eval(sort_str))
            else:
                sort_str = "StoreSelfPicking.{}.desc()".format(sort)
                query = query.order_by(eval(sort_str))
            if offset:
                query = query.offset(offset)
            if limit and limit != -1:
                query = query.limit(limit)
            query_set = query.all()
            if include_total:
                query_total = db_session.query(StoreSelfPicking.id)
                if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                    query_total = query_total.outerjoin(StoreSelfPickingProduct,
                                                        StoreSelfPicking.id == StoreSelfPickingProduct.main_id)
                    query_total = query_total.filter(StoreSelfPickingProduct.product_id.in_(product_ids))
                if partner_id:
                    query_total = query_total.filter(StoreSelfPicking.partner_id == partner_id)
                if start_date and isinstance(start_date, datetime) and start_date.year != 1970:
                    query_total = query_total.filter(StoreSelfPicking.order_date >= start_date)
                if end_date and isinstance(end_date, datetime) and end_date.year != 1970:
                    query_total = query_total.filter(StoreSelfPicking.order_date <= end_date)
                if isinstance(ids, list) and len(ids) > 0:
                    query_total = query_total.filter(StoreSelfPicking.id.in_(ids))
                if code:
                    query_total = query_total.filter(StoreSelfPicking.code == code)
                if branch_type:
                    query_total = query_total.filter(StoreSelfPicking.branch_type == branch_type)
                if status and isinstance(status, list):
                    query_total = query_total.filter(StoreSelfPicking.status.in_(status))
                if branch_ids and isinstance(branch_ids, list):
                    query_total = query_total.filter(StoreSelfPicking.branch_id.in_(branch_ids))
                if reason:
                    query_total = query_total.filter(StoreSelfPicking.reason == reason)
                if product_ids and len(product_ids) > 0 and isinstance(product_ids, list):
                    query_total = query_total.group_by(StoreSelfPicking.id)
                count = query_total.count()
                return count, query_set
            return query_set
        except Exception as e:
            logging.error("List Self Picking Error-{}".format(e))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_self_picking(self, update_data: dict, product_list=None, update_detail=False):
        """门店自采单更新状态/更新详情, 这里没有做状态校验
        :param update_data -> dict 待更新的数据
        :param product_list -> list 待更新的商品明细（采取覆盖式，先全部删除之前的商品记录，然后一把插入）
        :param update_detail -> bool 是否更新明细，为false时只更新状态
        """
        db_session = session_maker()
        try:
            if not update_detail:
                update_db = db_session.query(StoreSelfPicking).filter(
                    StoreSelfPicking.partner_id == update_data.get('partner_id')).filter(
                    StoreSelfPicking.id == update_data.get('id')).with_lockmode('update').first()
                if update_db:
                    update_db.updated_by = update_data.get('updated_by')
                    update_db.updated_name = update_data.get('updated_name')
                    update_db.updated_at = datetime.utcnow()
                    update_db.status = update_data.get('status')
                    if update_data.get("remark"):
                        update_db.remark = update_data.get('remark')
                    db_session.add(update_db)
            else:
                db_session.bulk_update_mappings(StoreSelfPicking, [update_data])
                if product_list:
                    db_session.query(StoreSelfPickingProduct).filter(
                        StoreSelfPickingProduct.main_id == update_data.get('id'),
                        StoreSelfPickingProduct.partner_id == update_data.get('partner_id')).delete(
                        synchronize_session=False)
                    db_session.bulk_insert_mappings(StoreSelfPickingProduct, product_list)
            db_session.commit()
            return True
        except Exception as e:
            logging.error("Update Material Convert Failed:{}-{}-{}".format(e, sys.exc_info(),
                                                                           traceback.format_exc().replace('\n', ',')))
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def query_product_name_by_ids(self, main_ids=None, partner_id=None, limit=None):
        """根据单据id查询商品名称，给移动端用"""
        db_session = session_maker()
        try:
            query = db_session.query(StoreSelfPickingProduct.main_id,
                                     StoreSelfPickingProduct.product_name).filter(
                StoreSelfPickingProduct.main_id.in_(main_ids),
                StoreSelfPickingProduct.partner_id == partner_id)
            if limit:
                query = query.limit(limit)
            return query.all()
        except Exception as e:
            logging.error(
                "** method[%s] error. Error message:%s **" % ('query_product_name_by_ids', e))
            raise e
        finally:
            db_session.close()

    def get_self_picking_log(self, receipt_id, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(StoreSelfPickingLog).filter(StoreSelfPickingLog.main_id == receipt_id,
                                                                 StoreSelfPickingLog.partner_id == partner_id)
            query = query.order_by(StoreSelfPickingLog.created_at.desc())
            adjust_logs = query.all()
            if not adjust_logs:
                return 0, []
            query_count = db_session.query(func.count(StoreSelfPickingLog.id))
            if partner_id:
                query_count = query_count.filter(StoreSelfPickingLog.partner_id == partner_id)
            if receipt_id:
                query_count = query_count.filter(StoreSelfPickingLog.main_id == receipt_id)
            count = query_count.scalar()
            return count, adjust_logs
        except Exception as e:
            logging.error(
                "** method[%s] error. Error message:%s **" % ('get_self_picking_log', e))
            raise e
        finally:
            db_session.close()


self_picking_db = StoreSelfPickingDB()



