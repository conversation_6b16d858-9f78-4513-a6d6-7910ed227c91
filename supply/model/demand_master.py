# -*- coding: utf-8 -*-
from sqlalchemy import func
from ..driver.mysql import session_maker
from datetime import datetime, timedelta
from . import DeclarativeBase, TimestampMixin
from sqlalchemy import (Column, BigInteger, Integer, String, DateTime, DECIMAL)
from supply.utils.helper import get_guid
from google.protobuf.timestamp_pb2 import Timestamp
from supply.model.demand.supply_demand import Supply_demand
from supply.model.demand.supply_demand_product import Supply_demand_product
from supply.model.demand.supply_demand_log import Supply_demand_log
from supply.client.metadata_service import metadata_service

class DemandMasterUploadBatchDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'supply_demand_master_upload_batch'
    id = Column(BigInteger, primary_key=True, default=get_guid())
    partner_id = Column(BigInteger)
    status = Column(String(10))
    file_name = Column(String(200))
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    updated_name = Column(String(100))

    file_type = Column(String(10))


class DemandMasterUploadDB(DeclarativeBase, TimestampMixin):
    __tablename__ = 'supply_demand_master_upload'
    id = Column(BigInteger, primary_key=True, default=get_guid())
    partner_id = Column(BigInteger)
    status = Column(String(10))
    batch_id = Column(BigInteger)
    store_id = Column(BigInteger)
    store_code = Column(String(50))
    store_name = Column(String(50))
    product_id = Column(BigInteger)
    product_code = Column(String(50))
    product_name = Column(String(50))
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    row_num = Column(Integer)
    quantity = Column(DECIMAL(precision=18, scale=8, decimal_return_scale=8), default=None)
    created_by = Column(BigInteger)
    updated_by = Column(BigInteger)
    master_id = Column(BigInteger)
    master_code = Column(String(50))
    store_type = Column(String(50))

    centre_id = Column(String(50))
    centre_name = Column(String(50))
    centre_code = Column(String(50))

    remark = Column(String(100))


class DemandMasterRepository(object):

    def __init__(self):
        pass

    def get_timestamp(self, value: datetime):
        timestamp = Timestamp()
        timestamp.FromDatetime(value)
        return timestamp

    def get_datetime(self, value):
        timestamp = Timestamp()
        timestamp.seconds = value.seconds
        date = timestamp.ToDatetime()
        if date == datetime(1970, 1, 1):
            return None
        return date

    def get_demand_master_upload_batch_by_id(self, batch_id):
        db_session = session_maker()
        try:
            query = db_session.query(DemandMasterUploadBatchDB).filter(DemandMasterUploadBatchDB.id == batch_id)
            obj = query.first()
            return obj
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def save_valid_data_for_demand_master(self, batch, detail_list, partner_id,user_id):
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        db_session = session_maker()
        try:
            doc_log_db = DemandMasterUploadBatchDB()
            doc_log_db.id = batch.get('id')
            doc_log_db.file_name = batch.get('file_name')
            doc_log_db.partner_id = partner_id
            doc_log_db.status = batch.get('status')
            doc_log_db.file_type = batch.get('file_type')
            doc_log_db.created_at = datetime.utcnow()
            doc_log_db.updated_at = datetime.utcnow()
            doc_log_db.created_by = user_id
            doc_log_db.updated_by = user_id
            doc_log_db.updated_name = username
            db_session.add(doc_log_db)
            if len(detail_list) > 0:
                db_session.bulk_insert_mappings(DemandMasterUploadDB, detail_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_demand_master_upload_by_batch_id(self, batch_id, offset=None, limit=None, include_total=None,
                                             partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(DemandMasterUploadDB).filter(DemandMasterUploadDB.batch_id == batch_id,
                                                                  DemandMasterUploadDB.partner_id == partner_id)
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            product_db_list = query.all()
            detail_list = []
            for d in product_db_list:
                pr = {}
                pr['store_code'] = d.store_code
                pr['store_name'] = d.store_name
                pr['product_code'] = d.product_code
                pr['product_name'] = d.product_name
                pr['quantity'] = d.quantity
                pr['start_date'] = self.get_timestamp(d.start_date)
                pr['end_date'] = self.get_timestamp(d.end_date)
                pr['row_num'] = d.row_num
                pr['store_id'] = d.store_id
                pr['product_id'] = d.product_id
                pr['status'] = d.status
                pr['batch_id'] = d.batch_id
                pr['master_id'] = d.master_id
                pr['master_code'] = d.master_code
                pr['centre_id'] = d.centre_id
                pr['centre_code'] = d.centre_code
                pr['centre_name'] = d.centre_name
                pr['remark'] = d.remark
                detail_list.append(pr)
            if include_total:
                query_total = db_session.query(func.count(DemandMasterUploadDB.id)).filter(
                    DemandMasterUploadDB.batch_id == batch_id,
                    DemandMasterUploadDB.partner_id == partner_id)
                count = query_total.scalar()
                return count, detail_list
            return detail_list
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_demand_master_upload(self, start_date, end_date, file_name=None,file_type=None, offset=None, limit=None,
                                 include_total=None,
                                 partner_id=None, user_id=None):
        if start_date:
            start_date = self.get_datetime(start_date)
        if end_date:
            end_date = self.get_datetime(end_date)
        db_session = session_maker()
        try:
            query = db_session.query(DemandMasterUploadBatchDB).filter(
                DemandMasterUploadBatchDB.partner_id == partner_id)
            if isinstance(start_date, datetime):
                query = query.filter(DemandMasterUploadBatchDB.created_at >= start_date)
            if isinstance(end_date, datetime):
                query = query.filter(DemandMasterUploadBatchDB.created_at <= end_date)
            if file_name:
                query = query.filter(DemandMasterUploadBatchDB.file_name.like("%" + file_name + "%"))
            if file_type:
                query = query.filter(DemandMasterUploadBatchDB.file_type==file_type)
            query = query.order_by(DemandMasterUploadBatchDB.updated_at.desc())
            if offset:
                query = query.offset(offset)
            if limit:
                query = query.limit(limit)
            product_db_list = query.all()
            detail_list = []
            for d in product_db_list:
                pr = {}
                pr['id'] = d.id
                pr['file_name'] = d.file_name
                pr['upload_date'] = self.get_timestamp(d.created_at)
                pr['created_at'] = self.get_timestamp(d.created_at)
                pr['updated_at'] = self.get_timestamp(d.updated_at)
                pr['status'] = d.status
                pr['updated_name'] = d.updated_name
                detail_list.append(pr)
            if include_total:
                query_total = db_session.query(func.count(DemandMasterUploadBatchDB.id)).filter(
                    DemandMasterUploadBatchDB.partner_id == partner_id)
                if isinstance(start_date, datetime):
                    query_total = query_total.filter(DemandMasterUploadBatchDB.created_at >= start_date)
                if isinstance(end_date, datetime):
                    query_total = query_total.filter(DemandMasterUploadBatchDB.created_at <= end_date)
                if file_name:
                    query_total = query_total.filter(DemandMasterUploadBatchDB.file_name.like("%" + file_name + "%"))
                if file_type:
                    query_total = query_total.filter(DemandMasterUploadBatchDB.file_type == file_type)
                count = query_total.scalar()
                return count, detail_list
            return detail_list
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def get_demand_master_upload_detail(self, batch_id, status=None, partner_id=None):
        db_session = session_maker()
        try:
            query = db_session.query(DemandMasterUploadDB).filter(DemandMasterUploadDB.batch_id == batch_id,
                                                                  DemandMasterUploadDB.partner_id == partner_id)
            if status:
                query = query.filter(DemandMasterUploadDB.status == status)
            detail_list = query.all()
            return detail_list
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_demand_master_upload_detail_more_info(self, demand_master_upload_batch, demand_master_upload_list):

        db_session = session_maker()
        try:
            db_session.bulk_update_mappings(DemandMasterUploadBatchDB, demand_master_upload_batch)
            db_session.bulk_update_mappings(DemandMasterUploadDB, demand_master_upload_list)
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def insert_demand_master_detail_and_product(self, supply_demand_list, supply_demand_product_list, supply_demand_log_list=None):
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(Supply_demand, supply_demand_list)
            db_session.bulk_insert_mappings(Supply_demand_product, supply_demand_product_list)
            if supply_demand_log_list and len(supply_demand_log_list)>0:
                db_session.bulk_insert_mappings(Supply_demand_log, supply_demand_log_list)
            
            db_session.commit()
            return True
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def update_demand_master_upload_batch_status(self, batch_id, status=None,partner_id=None, user_id=None):
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        db_session = session_maker()
        try:
            batch_db = db_session.query(DemandMasterUploadBatchDB). \
                filter(DemandMasterUploadBatchDB.id == batch_id).with_lockmode(
                "update").first()
            batch_db.status = status
            batch_db.updated_name = username
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()


demand_master_repository = DemandMasterRepository()
