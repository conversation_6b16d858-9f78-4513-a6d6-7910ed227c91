# -*- coding: utf8 -*-
import time
from concurrent import futures
import logging

import grpc
from grpc_reflection.v1alpha import reflection

from importlib import import_module
from pathlib import Path
import prometheus_client
from python_grpc_prometheus.prometheus_server_interceptor import PromServerInterceptor
from grpc_healthcheck import new_health_check_server
from grpc_healthcheck import health_pb2_grpc

from supply import APP_CONFIG
from supply import logger
from supply.api.adjust import AdjustService
from supply.api.mobile.mobile_franchisee_transfer_api import MobileFranchiseeTransferApi
from supply.api.receiving_diff import ReceivingDiffAPI
from supply.api.receiving_diff_bi import ReceivingDiffBiAPI
from supply.api.returns import ReturnsAPI
from supply.api.transfer import TransferService
from supply.api.stocktake import StocktakeServicer, tpStocktakeServicer
from supply.api.demand_api import DemandApi
from supply.api.inventory_bi import InventoryBiAPI
from supply.api.inventory_cut import InventoryCutAPI
from supply.api.material_convert import MaterialConvertApi
# from supply.api.cost_engine import CostEngineAPI
from supply.api.material import MaterialServicer
from supply.api.warehouse_api import WarehouseApi
from supply.api.attachments import AttachmentsApi
from supply.api.manufactory.purchase_api import ManufactoryPurchaseApi
from supply.api.manufactory.stocktake import MfyStocktakeApi
from supply.api.manufactory.transfer import MfyTransferApi
from supply.api.manufactory.adjust import MfyAdjustApi
from supply.api.manufactory.material_convert import MfyMaterialConvertApi
from supply.api.manufactory.packing_receipts import PackingReceiptsApi
from supply.api.manufactory.processing_receipts import ProcessingReceiptsApi
from supply.api.manufactory.processing_cost import ProcessingCostApi
from supply.api.mobile.mobile_transfer_api import MobileTransferApi
from supply.api.mobile.mobile_self_picking import MobileStoreSelfPickingApi
from supply.api.mobile.mobile_demand import MobileDemandAPI
from supply.api.mobile.mobile_returns import MobileReturnsAPI
from supply.api.mobile.mobile_inventory_bi import MobileInventoryBiAPI
from supply.api.mobile.mobile_adjust_api import MobileAdjustApi
from supply.api.mobile.mobile_franchisee_adjust_api import MobileFranchiseeAdjustApi
from supply.api.mobile.mobile_receive_diff import MobileReceiveDiffAPI
from supply.api.mobile.mobile_stocktake import MobileStocktakeApi
from supply.api.mobile.mobile_franchisee_stocktake import MobileFranchiseeStocktakeApi
from supply.api.mobile.mobile_common import MobileCommonApi
from supply.api.mobile.mobile_franchisee_demand import MobileFranchiseeDemandAPI
from supply.api.mobile.mobile_franchisee_refund import MobileFranchiseeRefundAPI
from supply.api.mobile.mobile_franchisee_return import MobileFrachiseeReturnsAPI
from supply.api.store.self_picking import StoreSelfPickingApi
from supply.api.store.stocktake import StoreStocktakeApi
from supply.api.store.transfer import StoreTransferApi
from supply.api.store.adjust import StoreAdjustApi
from supply.api.store.material_convert import StoreMaterialConvertApi
from supply.api.store.production_process import StoreProductProcessApi
from supply.api.warehouse.production_process import WarehouseProductProcessApi
from supply.api.manufactory.production_process import ManufactoryProductProcessApi
from supply.api.supply_config.stocktake_tags import StocktakeTagsApi
from supply.api.hd_management.purchase_review import PurchaseReviewApi
from supply.api.hd_management.invoice_blending import InvoiceBlendingApi
from supply.api.hd_management.price_adjustment import PriceAdjustmentAPI
from supply.api.frs_management.franchisee_demand import FranchiseeDemandAPI
from supply.api.frs_management.franchisee_refund import FranchiseeRefundAPI
from supply.api.frs_management.franchisee_hd_assignment import FranchiseeHdAssignmentApi
from supply.api.third_party.demand import TpDemandApi
from supply.api.third_party.receive_diff import TpReceiveDiffApi
from supply.api.third_party.returns import TpReturnsAPI
from supply.api.franchisee.vouchers import VouchersAPI
from supply.proto.third_party import demand_pb2_grpc as tdp_demand_pb2_grpc
from supply.proto.third_party import returns_pb2_grpc as tdp_returns_pb2_grpc
from supply.proto import adjust_pb2_grpc
from supply.proto import transfer_pb2_grpc
from supply.proto import stocktake_pb2_grpc
from supply.proto import receiving_diff_pb2_grpc
from supply.proto import receiving_diff_bi_pb2_grpc
from supply.proto import returns_pb2_grpc
from supply.proto import supply_pb2_grpc
from supply.proto import inventory_bi_pb2_grpc
from supply.proto import inventory_cut_pb2_grpc
from supply.proto import material_pb2_grpc
from supply.proto import warehouse_pb2_grpc
from supply.proto import attachments_pb2_grpc
from supply.proto import material_convert_pb2_grpc
from supply.proto.store import material_convert_pb2_grpc as store_material_convert_grpc
from supply.proto.store import self_picking_pb2_grpc as store_self_picking_grpc
from supply.proto.store import stocktake_pb2_grpc as store_stocktake_grpc
from supply.proto.store import transfer_pb2_grpc as store_transfer_grpc
from supply.proto.store import adjust_pb2_grpc as store_adjust_grpc
from supply.proto.mobile import mobile_demand_pb2_grpc, mobile_return_pb2_grpc, mobile_inventory_bi_pb2_grpc, \
    mobile_transfer_pb2_grpc, mobile_self_picking_pb2_grpc, mobile_adjust_pb2_grpc, mobile_franchisee_adjust_pb2_grpc, mobile_receive_diff_pb2_grpc, \
    mobile_stocktake_pb2_grpc, mobile_common_pb2_grpc, mobile_statements_pb2_grpc, mobile_franchisee_demand_pb2_grpc, mobile_franchisee_stocktake_pb2_grpc, mobile_franchisee_transfer_pb2_grpc
from supply.proto.hd_management import purchase_review_pb2_grpc, invoice_blending_pb2_grpc, price_adjustment_pb2_grpc
from supply.proto.third_party import stocktake_pb2_grpc as tp_stocktake_pb2_grpc
from supply.proto import reduction_augmentation_demand_pb2_grpc

from supply.api.store.demand import StoreDemandApi
from supply.api.store.receive_diff import StoreReceiveDiffAPI
from supply.api.store.returns import StoreReturnsAPI
from supply.api.store_extra.receive_diff import StoreExtraReceiveDiffAPI
from supply.api.store_extra.returns import StoreExtraReturnsAPI
from supply.proto.store_extra import receive_diff_pb2_grpc as store_extra_receive_diff_pb2_grpc,\
    returns_pb2_grpc as store_extra_returns_pb2_grpc
from supply.api.store.inventory_bi import StoreInventoryBiAPI
from supply.proto.store import demand_pb2_grpc as store_demand_pb2_grpc, receive_diff_pb2_grpc as store_receive_diff_pb2_grpc, \
    returns_pb2_grpc as store_returns_pb2_grpc, inventory_bi_pb2_grpc as store_inventory_bi_pb2_grpc
from supply.api.store_bi.receive_diff_bi import StoreReceiveDiffBiAPI
from supply.proto.store_bi import receive_diff_bi_pb2_grpc as store_receive_diff_bi_pb2_grpc
from supply.api.store_management.inventory_adjust import StoreInventoryAdjustApi
from supply.api.store_management.hd_assignment import StoreHdAssignmentApi
from supply.proto.store_management import inventory_adjust_pb2_grpc as store_inventory_adjust_pb2_grpc, \
    hd_assignment_pb2_grpc as store_hd_assignment_pb2_grpc

from supply.api.warehouse.returns import WarehouseReturnsAPI
from supply.api.warehouse.inventory_bi import WarehouseInventoryBiAPI
from supply.api.warehouse.adjust import WarehouseAdjustApi
from supply.api.warehouse.transfer import WarehouseTransferApi
from supply.api.warehouse.purchase_api import WarehousePurchaseApi
from supply.api.warehouse.stocktake import WarehouseStocktakeApi
from supply.api.warehouse.material_convert import WarehouseMaterialConvertApi
from supply.proto.warehouse import returns_pb2_grpc as whs_returns_pb2_grpc, inventory_bi_pb2_grpc as whs_inventory_bi_pb2_grpc
from supply.proto.warehouse import adjust_pb2_grpc as whs_adjust_pb2_grpc
from supply.proto.warehouse import transfer_pb2_grpc as whs_transfer_pb2_grpc
from supply.proto.warehouse import purchase_pb2_grpc as whs_purchase_pb2_grpc
from supply.proto.warehouse import stocktake_pb2_grpc as whs_stocktake_pb2_grpc
from supply.proto.warehouse import material_convert_pb2_grpc as whs_material_convert_pb2_grpc
from supply.api.warehouse_management.inventory_adjust import WarehouseInventoryAdjustApi
from supply.proto.warehouse_management import inventory_adjust_pb2_grpc as whs_inventory_adjust_pb2_grpc
from supply.proto.frs_management import franchisee_demand_pb2_grpc, franchisee_refund_pb2_grpc, \
    franchise_hd_assignment_pb2_grpc
from supply.proto.mobile import mobile_franchisee_refund_pb2_grpc
from supply.proto.mobile import mobile_franchisee_return_pb2_grpc
from supply.api.manufactory.returns import ManufactoryReturnsAPI
from supply.api.manufactory.inventory_bi import ManufactoryInventoryBiAPI
from supply.proto.manufactory import returns_pb2_grpc as mfy_returns_pb2_grpc, inventory_bi_pb2_grpc as mfy_inventory_bi_pb2_grpc
from supply.proto.manufactory import material_convert_pb2_grpc as mfy_material_convert_grpc
from supply.api.manufactory_management.inventory_adjust import ManufactoryInventoryAdjustApi
from supply.proto.manufactory_management import inventory_adjust_pb2_grpc as mfy_inventory_adjust_pb2_grpc
from supply.proto.manufactory import packing_receipts_pb2_grpc
from supply.proto.manufactory import processing_receipts_pb2_grpc
from supply.proto.manufactory import processing_cost_pb2_grpc
from supply.proto.manufactory import purchase_pb2_grpc as mfy_purchase_pb2_grpc
from supply.proto.manufactory import stocktake_pb2_grpc as mfy_stocktake_pb2_grpc
from supply.proto.manufactory import transfer_pb2_grpc as mfy_transfer_pb2_grpc
from supply.proto.manufactory import adjust_pb2_grpc as mfy_adjust_pb2_grpc
from supply.proto.store import production_process_receipts_pb2_grpc
from supply.proto.warehouse import production_process_receipts_pb2_grpc as warehouse_production_process_receipts_pb2_grpc
from supply.proto.manufactory import production_process_receipts_pb2_grpc as manufactory_production_process_receipts_pb2_grpc

from supply.proto.supply_config import stocktake_tags_pb2_grpc

# 加盟商大掌柜
from supply.api.mobile.mobile_franchisee_receive_diff import FranchiseeMobileReceivingDiffAPI
from supply.proto.mobile import mobile_franchisee_receive_diff_pb2_grpc

from supply.proto.franchisee import statements_pb2_grpc, vouchers_pb2_grpc
from supply.api.franchisee.statements import StatementsAPI
from supply.api.mobile.mobile_statements import MobileStatementsAPI

from supply.proto.third_party import demands_pb2_grpc as tp_demands_pb2_grpc
from supply.api.thirdparty.demands import ThirdPartyDemandAPI
from supply.proto.third_party import receive_diff_pb2_grpc as tp_rec_diff_grpc

from supply.proto.vendor_bi import receive_diff_bi_pb2_grpc as v_receive_diff_bi_pb2_grpc
from supply.api.vendor_bi.receive_diff_bi import VendorReceiveDiffBiAPI
from supply.proto.frs_store_extra import receive_diff_pb2_grpc as frs_store_extra_receive_diff_pb2_grpc
from supply.proto.frs_store_extra import return_pb2_grpc as frs_store_extra_return_pb2_grpc
from supply.api.frs_store_extra.receive_diff import FrsStoreExtraReceiveDiffAPI
from supply.api.frs_store_extra.returns import FrsStoreExtraReturnsAPI
from supply.proto.franchisee import inventory_bi_pb2_grpc as frs_inventory_bi_pb2_grpc
from supply.api.franchisee.inventory_bi import FrsStoreInventoryBiAPI
from supply.proto import pos_stocktake_pb2_grpc
from supply.api.pos_stocktake import PosStocktakeApi
from supply.api.reduction_augmentation_demand import ReductionAugmentationDemandApi
from supply.proto.frs_warehouse import inventory_bi_pb2_grpc as frs_warehouse_inventory_bi_pb2_grpc
from supply.api.frs_warehouse.inventory_bi import FrsWarehouseInventoryBiAPI
from supply.proto.frs_store import franchisee_stocktake_pb2_grpc as pc_franchisee_stocktake_pb2_grpc
from supply.proto.frs_store import franchisee_adjust_pb2_grpc as pc_franchisee_adjust_pb2_grpc
from supply.proto.frs_store import franchisee_transfer_pb2_grpc as pc_franchisee_transfer_pb2_grpc
from supply.proto.frs_store import franchisee_receive_diff_pb2_grpc as pc_franchisee_receive_diff_pb2_grpc
from supply.api.frs_store.adjust import FranchiseeAdjustApi
from supply.api.frs_store.stocktake import FranchiseeStocktakeApi
from supply.api.frs_store.transfer import FranchiseeTransferApi
from supply.api.frs_store.receive_diff import FranchiseeReceivingDiffAPI
from supply.message.handle_message import SupplyMessageService
from supply.proto.supply_message import supply_message_pb2_grpc
from supply.proto.frs_store import receive_diff_bi_pb2_grpc as frs_receive_diff_bi_pb2_grpc
from supply.api.frs_store.receive_diff_bi import FranchiseeStoreReceiveDiffBiAPI

from supply.driver.mq import mq_consumer, mq_producer
from supply.utils.get_task_dict import load_mq_task as load_mq_task_grpc
from typing import Dict

_ONE_DAY_IN_SECONDS = 60 * 60 * 24

tracer = None


def load_mq_task():
    task_path = Path(__file__).parent.joinpath('task')
    for f in task_path.iterdir():
        fname = f.name
        if fname == '__init__.py' or fname.startswith('__'):
            continue
        module_name = fname.split('.')[0]
        _ = import_module('supply.task.{}'.format(module_name))


def init_hex_tracer(config: Dict):
    from jaeger_client import Config
    sampler_config = {
        'type': config['sample_type'],
        'param': float(config['sample_param'])
    }
    agent_config = {
        'reporting_host': config['agent_host'],
        'reporting_port': int(config['agent_port']),
    }
    conf = {
        'service_name': config['service_name'],
        'sampler': sampler_config,
        'local_agent': agent_config,
        'enabled': config['enable'],
        'logging': False,
    }
    print("Config:", config)
    jaeger_conf = Config(config=conf, validate=True)
    return jaeger_conf.initialize_tracer()


def task_health_check():
    config = APP_CONFIG.get('task_health_check', {})
    if not config:
        return

    from flask import Flask
    import threading
    app = Flask(__name__)

    @app.route('/health_check')
    def health_check():
        return 'OK'

    config = {'host': config.get('host', '127.0.0.1'), 'port': config.get('port', 9988)}

    t = threading.Thread(target=app.run, kwargs=config)
    t.daemon = True
    t.start()


def run(mode: str, host: str, port: int, max_worker: int, key_path: str = None, cert_path: str = None) -> None:
    """
    Args:
        host (str): hostname
        port (int): port
        max_worker (int): number of workers
        key_path (str): optional absolute path of ssl key file for https setting
        cert_path (str): optional absolute path of certificate file for https setting
    """
    logger.info('supply run!')
    mq_config = APP_CONFIG.get('mq', {}).get('amqp', {})
    mq_config = APP_CONFIG.get('mq', {}).get('rocketmq', {})
    mq_producer.init(mq_type='ROCKETMQ', channel=mq_config.get('channel'),
                     lookup_address=mq_config.get('lookup_address'),
                     access_key=mq_config.get('access_key'), access_secret=mq_config.get('access_secret'),
                     thread_num=mq_config.get('thread_num'), batch_size=mq_config.get('batch_size'))

    mq_producer.start()
    logger.info('mq_producer run!')
    if mode == 'task':
        print('ssss')
        consumer_type = APP_CONFIG.get('mq', {}).get('rocketmq', {}).get('consumer_type', 'GRPC')
        if consumer_type == 'PUSH':
            load_mq_task()
            mq_consumer.init(mq_type='ROCKETMQ', channel=mq_config.get('channel'),
                             consumer_group_name=mq_config.get('consumer_group_name'),
                             lookup_address=mq_config.get('lookup_address'), access_key=mq_config.get('access_key'),
                             access_secret=mq_config.get('access_secret'), thread_num=mq_config.get('thread_num'),
                             batch_size=mq_config.get('batch_size'))
            load_mq_task()
            mq_consumer.start()
            logger.info('supply mq consumer started')
            try:
                while 1:
                    time.sleep(_ONE_DAY_IN_SECONDS)
            except KeyboardInterrupt:
                mq_consumer.stop()
                logger.info('supply GRPC server stopped')
        else:
            load_mq_task_grpc()

            prometheus_interceptor = PromServerInterceptor()
            srv = grpc.server(futures.ThreadPoolExecutor(max_workers=max_worker),
                              interceptors=(prometheus_interceptor,),
                              options=[
                                  ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                                  ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
                              ],
                              )
            # 启动prometheus服务
            prometheus_client.start_http_server(APP_CONFIG['prometheus']['port'], addr=APP_CONFIG['prometheus']['host'])
            health_pb2_grpc.add_HealthCheckServicer_to_server(new_health_check_server(), srv)
            logging.info('prometheus_interceptor run!')
            supply_message_pb2_grpc.add_SupplyMessageServicer_to_server(SupplyMessageService(), srv)
            wording = 'with insecure channel'
            if key_path and cert_path:
                with open(key_path) as key_file:
                    with open(cert_path) as certificate_file:
                        credentials = grpc.ssl_server_credentials(
                            [(key_file.read(), certificate_file.read())])
                srv.add_secure_port('{}:{}'.format(host, port), credentials)
                wording = 'with SSL channel'
            else:
                srv.add_insecure_port('{}:{}'.format(host, port))
            logger.info('srv run!')
            reflection.enable_server_reflection([h.service_name() for h in srv._state.generic_handlers], srv)
            srv.start()
            logger.info('srv done!')
            logger.info('supply GRPC server %s started on %i with %i workers!' % (wording, port, max_worker))
            try:
                while 1:
                    time.sleep(_ONE_DAY_IN_SECONDS)
            except KeyboardInterrupt:
                srv.stop(0)
                logger.info('supply GRPC server stopped')
    else:
        prometheus_interceptor = PromServerInterceptor()
        srv = grpc.server(futures.ThreadPoolExecutor(max_workers=max_worker),
                          interceptors=(prometheus_interceptor,),
                          options=[
                              ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                              ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
                          ],
                          )
        # tracing_interceptor = open_tracing_server_interceptor(opentracing.tracer)
        # srv = intercept_server(srv, tracing_interceptor)
        # 启动prometheus服务
        prometheus_client.start_http_server(APP_CONFIG['prometheus']['port'], addr=APP_CONFIG['prometheus']['host'])
        health_pb2_grpc.add_HealthCheckServicer_to_server(new_health_check_server(), srv)
        logging.info('prometheus_interceptor run!')
        # <原有公共模块服务> 暂时保留以便如果有其他服务内部调用不校验权限
        # 盘点服务
        stocktake_pb2_grpc.add_stocktakeServicer_to_server(StocktakeServicer(), srv)
        tp_stocktake_pb2_grpc.add_TpStocktakeServicer_to_server(tpStocktakeServicer(), srv)
        # 调拨服务
        transfer_pb2_grpc.add_transferServicer_to_server(TransferService(), srv)
        # 每日损耗服务
        adjust_pb2_grpc.add_adjustServicer_to_server(AdjustService(), srv)
        # 收货差异单服务
        receiving_diff_pb2_grpc.add_ReceivingDiffServiceServicer_to_server(ReceivingDiffAPI(), srv)
        # 退货服务
        returns_pb2_grpc.add_ReturnServiceServicer_to_server(ReturnsAPI(), srv)
        # 订货服务
        supply_pb2_grpc.add_supplyServicer_to_server(DemandApi(), srv)
        # 收货差异单报表
        receiving_diff_bi_pb2_grpc.add_ReceivingDiffBiServiceServicer_to_server(ReceivingDiffBiAPI(), srv)
        # 库存报表
        inventory_bi_pb2_grpc.add_InventoryBiServiceServicer_to_server(InventoryBiAPI(), srv)
        # 库存切片
        inventory_cut_pb2_grpc.add_InventoryCutServiceServicer_to_server(InventoryCutAPI(), srv)
        # 物料报表
        material_pb2_grpc.add_materialServicer_to_server(MaterialServicer(), srv)
        # 仓库采购
        warehouse_pb2_grpc.add_warehouseServicer_to_server(WarehouseApi(), srv)
        # 附件管理服务
        attachments_pb2_grpc.add_AttachmentsServiceServicer_to_server((AttachmentsApi()), srv)
        # 物料转换
        material_convert_pb2_grpc.add_MaterialConvertServicer_to_server(MaterialConvertApi(), srv)
        # 成本引擎服务
        # reports_pb2_grpc.add_ReportsServicer_to_server(CostEngineAPI(), srv)

        # <移动端服务>
        # 通用模块服务(首页接口等)
        mobile_common_pb2_grpc.add_MobileCommonServiceServicer_to_server(MobileCommonApi(), srv)
        # 订货
        mobile_demand_pb2_grpc.add_MobileDemandServiceServicer_to_server(MobileDemandAPI(), srv)
        # 加盟商订货
        mobile_franchisee_demand_pb2_grpc.add_MobileFranchiseeDemandServiceServicer_to_server(
            MobileFranchiseeDemandAPI(), srv)
        # 退货
        mobile_return_pb2_grpc.add_MobileReturnServiceServicer_to_server(MobileReturnsAPI(), srv)
        # 调拨
        mobile_transfer_pb2_grpc.add_mobileTransferServicer_to_server(MobileTransferApi(), srv)
        # 门店自采
        mobile_self_picking_pb2_grpc.add_MobileStoreSelfPickingServicer_to_server(MobileStoreSelfPickingApi(), srv)
        # 库存查询
        mobile_inventory_bi_pb2_grpc.add_MobileInventoryBiServiceServicer_to_server(MobileInventoryBiAPI(), srv)
        # 报废单
        mobile_adjust_pb2_grpc.add_mobileAdjustServicer_to_server(MobileAdjustApi(), srv)
        # 加盟商报废单
        mobile_franchisee_adjust_pb2_grpc.add_mobileFranchiseeAdjustServicer_to_server(MobileFranchiseeAdjustApi(), srv)
        # 收货差异单
        mobile_receive_diff_pb2_grpc.add_MobileReceiveDiffServiceServicer_to_server(MobileReceiveDiffAPI(), srv)
        # 盘点单
        mobile_stocktake_pb2_grpc.add_MobileStockTakeServicer_to_server(MobileStocktakeApi(), srv)
        # 加盟商盘点单
        mobile_franchisee_stocktake_pb2_grpc.add_MobileFranchiseeStockTakeServicer_to_server(MobileFranchiseeStocktakeApi(), srv)
        # 加盟商调拨单
        mobile_franchisee_transfer_pb2_grpc.add_mobileFranchiseeTransferServicer_to_server(MobileFranchiseeTransferApi(), srv)

        # <第三方调用输入>
        # 订货单
        tdp_demand_pb2_grpc.add_TpDemandServicer_to_server(TpDemandApi(), srv)
        # 收货差异单
        tp_rec_diff_grpc.add_TpReceiveDiffServiceServicer_to_server(TpReceiveDiffApi(), srv)
        # 退货单
        tdp_returns_pb2_grpc.add_TpReturnServiceServicer_to_server(TpReturnsAPI(), srv)

        # <门店运营>
        store_demand_pb2_grpc.add_StoreDemandServiceServicer_to_server(StoreDemandApi(), srv)
        store_returns_pb2_grpc.add_StoreReturnServiceServicer_to_server(StoreReturnsAPI(), srv)
        store_receive_diff_pb2_grpc.add_StoreReceiveDiffServiceServicer_to_server(StoreReceiveDiffAPI(), srv)
        store_inventory_bi_pb2_grpc.add_StoreInventoryBiServiceServicer_to_server(StoreInventoryBiAPI(), srv)
        store_receive_diff_bi_pb2_grpc.add_StoreReceiveDiffBiServiceServicer_to_server(StoreReceiveDiffBiAPI(), srv)
        store_hd_assignment_pb2_grpc.add_StoreManagementHdAssignmentServicer_to_server(StoreHdAssignmentApi(), srv)
        store_inventory_adjust_pb2_grpc.add_StoreInventoryAdjustServiceServicer_to_server(StoreInventoryAdjustApi(), srv)
        store_stocktake_grpc.add_StoreStockTakeServicer_to_server(StoreStocktakeApi(), srv)
        store_transfer_grpc.add_StoreTransferServicer_to_server(StoreTransferApi(), srv)
        store_adjust_grpc.add_StoreAdjustServicer_to_server(StoreAdjustApi(), srv)
        store_material_convert_grpc.add_StoreMaterialConvertServicer_to_server(StoreMaterialConvertApi(), srv)
        # 门店生产单
        production_process_receipts_pb2_grpc.add_StoreProductionProcessReceiptsServicer_to_server(
            StoreProductProcessApi(), srv)
        # 自采
        store_self_picking_grpc.add_StoreSelfPickingServicer_to_server(StoreSelfPickingApi(), srv)

        # <门店单据审核>
        store_extra_returns_pb2_grpc.add_StoreExtraReturnServiceServicer_to_server(StoreExtraReturnsAPI(), srv)
        store_extra_receive_diff_pb2_grpc.add_StoreExtraReceiveDiffServiceServicer_to_server(StoreExtraReceiveDiffAPI(), srv)

        # <仓库运营>
        whs_returns_pb2_grpc.add_WarehouseReturnServiceServicer_to_server(WarehouseReturnsAPI(), srv)
        whs_inventory_bi_pb2_grpc.add_WarehouseInventoryBiServiceServicer_to_server(WarehouseInventoryBiAPI(), srv)
        whs_inventory_adjust_pb2_grpc.add_WarehouseInventoryAdjustServiceServicer_to_server(WarehouseInventoryAdjustApi(), srv)
        whs_adjust_pb2_grpc.add_WarehouseAdjustServicer_to_server(WarehouseAdjustApi(), srv)
        whs_transfer_pb2_grpc.add_WarehouseTransferServicer_to_server(WarehouseTransferApi(), srv)
        whs_purchase_pb2_grpc.add_WarehousePurchaseServicer_to_server(WarehousePurchaseApi(), srv)
        whs_stocktake_pb2_grpc.add_warehouseStockTakeServicer_to_server(WarehouseStocktakeApi(), srv)
        whs_material_convert_pb2_grpc.add_WarehouseMaterialConvertServicer_to_server(WarehouseMaterialConvertApi(), srv)
        warehouse_production_process_receipts_pb2_grpc.add_WarehouseProductionProcessReceiptsServicer_to_server(WarehouseProductProcessApi(), srv)

        # <加工中心运营>
        mfy_returns_pb2_grpc.add_ManufactoryReturnServiceServicer_to_server(ManufactoryReturnsAPI(), srv)
        mfy_inventory_bi_pb2_grpc.add_ManufactoryInventoryBiServiceServicer_to_server(ManufactoryInventoryBiAPI(), srv)
        mfy_inventory_adjust_pb2_grpc.add_ManufactoryInventoryAdjustServiceServicer_to_server(ManufactoryInventoryAdjustApi(), srv)
        # 物料转换
        mfy_material_convert_grpc.add_ManufactoryMaterialConvertServicer_to_server(MfyMaterialConvertApi(), srv)
        # 包装单
        packing_receipts_pb2_grpc.add_PackingReceiptsServicer_to_server(PackingReceiptsApi(), srv)
        # 加工单
        processing_receipts_pb2_grpc.add_ProcessingReceiptsServicer_to_server(ProcessingReceiptsApi(), srv)
        # 加工费用
        processing_cost_pb2_grpc.add_ProcessingCostServicer_to_server(ProcessingCostApi(), srv)
        # 采购
        mfy_purchase_pb2_grpc.add_ManufactoryPurchaseServicer_to_server(ManufactoryPurchaseApi(), srv)
        # 盘点
        mfy_stocktake_pb2_grpc.add_ManufactoryStockTakeServicer_to_server(MfyStocktakeApi(), srv)
        # 调拨
        mfy_transfer_pb2_grpc.add_ManufactoryTransferServicer_to_server(MfyTransferApi(), srv)
        # 报废
        mfy_adjust_pb2_grpc.add_ManufactoryAdjustServicer_to_server(MfyAdjustApi(), srv)
        # 生产单
        manufactory_production_process_receipts_pb2_grpc.add_ManufactoryProductionProcessReceiptsServicer_to_server(ManufactoryProductProcessApi(), srv)
        # <运营设置>
        stocktake_tags_pb2_grpc.add_StockTakeTagsServicer_to_server(StocktakeTagsApi(), srv)

        # <总部管理>
        # 采购复核
        purchase_review_pb2_grpc.add_PurchaseReviewServicer_to_server(PurchaseReviewApi(), srv)
        # 发票勾兑服务
        invoice_blending_pb2_grpc.add_InvoiceBlendingServicer_to_server(InvoiceBlendingApi(), srv)
        # 调价单
        price_adjustment_pb2_grpc.add_PriceAdjustmentServicer_to_server(PriceAdjustmentAPI(), srv)

        # <加盟商运营模块>
        franchisee_demand_pb2_grpc.add_FranchiseeDemandServiceServicer_to_server(FranchiseeDemandAPI(), srv)
        franchisee_refund_pb2_grpc.add_FranchiseeRefundServiceServicer_to_server(FranchiseeRefundAPI(), srv)
        franchise_hd_assignment_pb2_grpc.add_FranchiseeHdAssignmentServicer_to_server(FranchiseeHdAssignmentApi(), srv)

        # 加盟商代金券功能
        vouchers_pb2_grpc.add_VouchersServiceServicer_to_server(VouchersAPI(), srv)

        mobile_franchisee_refund_pb2_grpc.add_MobileFranchiseeRefundServiceServicer_to_server(MobileFranchiseeRefundAPI(), srv)
        # <供应商平台>
        v_receive_diff_bi_pb2_grpc.add_VendorReceiveDiffBiServiceServicer_to_server(VendorReceiveDiffBiAPI(), srv)
        
        # 第三方报表
        statements_pb2_grpc.add_StatementsServiceServicer_to_server(StatementsAPI(), srv)
        mobile_statements_pb2_grpc.add_MobileStatementsServiceServicer_to_server(MobileStatementsAPI(), srv)
        tp_demands_pb2_grpc.add_ThirdPartyDemandServiceServicer_to_server(ThirdPartyDemandAPI(), srv)
        # 收货差异单
        # 加盟商退货单
        mobile_franchisee_return_pb2_grpc.add_FranchiseeReturnServiceServicer_to_server(MobileFrachiseeReturnsAPI(), srv)
        # 加盟商大掌柜
        mobile_franchisee_receive_diff_pb2_grpc.add_FranchiseeMobileReceivingDiffServiceServicer_to_server(FranchiseeMobileReceivingDiffAPI(), srv)
        # 加盟商门店单据审核
        frs_store_extra_return_pb2_grpc.add_FrsStoreExtraReturnServiceServicer_to_server(FrsStoreExtraReturnsAPI(), srv)
        frs_store_extra_receive_diff_pb2_grpc.add_FrsStoreExtraReceiveDiffServiceServicer_to_server(FrsStoreExtraReceiveDiffAPI(), srv)
        # 加盟商大掌柜盘点称重
        pos_stocktake_pb2_grpc.add_PosStocktakeServicer_to_server(PosStocktakeApi(), srv)

        # 加盟商库存服务
        frs_inventory_bi_pb2_grpc.add_FrsStoreInventoryBiServiceServicer_to_server(FrsStoreInventoryBiAPI(), srv)
        # 减配货
        reduction_augmentation_demand_pb2_grpc.add_ReductionAugmentationDemandServicer_to_server(ReductionAugmentationDemandApi(), srv)
        # 加盟商仓库库存
        frs_warehouse_inventory_bi_pb2_grpc.add_FrsWarehouseInventoryBiServiceServicer_to_server(FrsWarehouseInventoryBiAPI(), srv)
        # PC接口
        pc_franchisee_adjust_pb2_grpc.add_FranchiseeAdjustServicer_to_server(FranchiseeAdjustApi(), srv)
        pc_franchisee_stocktake_pb2_grpc.add_FranchiseeStockTakeServicer_to_server(FranchiseeStocktakeApi(), srv)
        pc_franchisee_transfer_pb2_grpc.add_FranchiseeTransferServicer_to_server(FranchiseeTransferApi(), srv)
        pc_franchisee_receive_diff_pb2_grpc.add_FranchiseeReceivingDiffServiceServicer_to_server(FranchiseeReceivingDiffAPI(), srv)

        # 加盟商收货差异报表
        frs_receive_diff_bi_pb2_grpc.add_FranchiseeStoreReceiveDiffBiServiceServicer_to_server(FranchiseeStoreReceiveDiffBiAPI(), srv)

        wording = 'with insecure channel'
        if key_path and cert_path:
            with open(key_path) as key_file:
                with open(cert_path) as certificate_file:
                    credentials = grpc.ssl_server_credentials(
                        [(key_file.read(), certificate_file.read())])
            srv.add_secure_port('{}:{}'.format(host, port), credentials)
            wording = 'with SSL channel'
        else:
            srv.add_insecure_port('{}:{}'.format(host, port))
        logger.info('srv run!')
        reflection.enable_server_reflection([h.service_name() for h in srv._state.generic_handlers], srv)
        srv.start()
        logger.info('srv done!')
        logger.info('supply GRPC server %s started on %i with %i workers!' % (wording, port, max_worker))
        try:
            while 1:
                time.sleep(_ONE_DAY_IN_SECONDS)
        except KeyboardInterrupt:
            srv.stop(0)
            logger.info('supply GRPC server stopped')
    mq_producer.stop()
