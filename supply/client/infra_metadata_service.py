import grpc
from google.protobuf.json_format import MessageToDict
from google.protobuf.timestamp_pb2 import Timestamp
from supply.proto.metadata.entity import entity_pb2
from supply.proto.metadata.relation import relation_pb2
from supply.proto.metadata.entity.entity_pb2_grpc import EntityServiceStub
from supply.proto.metadata.relation.relation_pb2_grpc import RelationServiceStub

from supply import APP_CONFIG, TIMEOUT
from google.protobuf.struct_pb2 import Struct
from supply import request_info
from hex_exception import UnAuthorized
from ..error.exception import *
from hex_exception import exception_from_str
from supply import logger, time_cost

_METADATA_ADDRESS = str(APP_CONFIG['metadata_center_address'])
_METADATA_CHANNEL = grpc.insecure_channel(_METADATA_ADDRESS,
                                          options=[
                                              ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                                              ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
                                          ]
                                          )


def get_struct(func):
    def inner(*args, **kwargs):
        if kwargs.get('filters'):
            filte_dict = kwargs.get('filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['filters'] = struct
        if kwargs.get('fields'):
            filte_dict = kwargs.get('fields')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['fields'] = struct
        if kwargs.get("store_filters"):
            filte_dict = kwargs.get('store_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['store_filters'] = struct
        if kwargs.get("store_relation_filters"):
            filte_dict = kwargs.get('store_relation_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['store_relation_filters'] = struct
        if kwargs.get("product_relation_filters"):
            filte_dict = kwargs.get('product_relation_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['product_relation_filters'] = struct
        if kwargs.get('product_filters'):
            filte_dict = kwargs.get('product_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['product_filters'] = struct
        if kwargs.get('relation_filters'):
            relation_filters_dict = kwargs.get('relation_filters')
            struct = Struct()
            struct.update(relation_filters_dict)
            kwargs['relation_filters'] = struct
        if kwargs.get('entities'):
            entities = kwargs.get('entities')
            for entity in entities:
                if entity.get('fields'):
                    struct = Struct()
                    struct.update(entity.get('fields'))
                    entity['fields'] = struct
            kwargs['entities'] = entities
        if not kwargs.get("partner_id", None) or not kwargs.get('user_id', None):
            # partner_id, user_id 要传关键字参数
            if hasattr(request_info, "partner_id") and hasattr(request_info, "user_id"):
                kwargs['partner_id'] = request_info.partner_id
                kwargs['user_id'] = request_info.user_id
            else:
                raise UnAuthorized("请传入partner_id和user_id")
        return func(*args, **kwargs)

    return inner


def result_wraps(func):
    def inner(*args, **kwargs):
        entity = func(*args, **kwargs)
        if entity.get('rows'):
            rows = entity['rows']
        else:
            rows = [entity]
        for row in rows:
            if row.get("product"):
                row.update(row['product'])
                del row['product']
        return entity

    return inner


class MetaDataCenterService(object):
    """
        多租户平台主档中心
    """
    TIMEOUT = 60 * 2

    def __init__(self):
        self.entityStub = EntityServiceStub(_METADATA_CHANNEL)
        self.relationStub = RelationServiceStub(_METADATA_CHANNEL)

    @get_struct
    def list_entity(self, schema_name=None, state=None, include_state=False, filters=None, relation=None,
                    return_fields=None, partner_id=None, user_id=None, ids=None, relation_filters=None,
                    include_pending_changes=None, include_parents=None, offset=None, sort=None, order=None,
                    limit=-1, include_total=False, search=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name=schema_name,
                state=state,
                include_state=include_state,
                filters=filters,
                relation=relation,
                ids=ids,
                return_fields=return_fields,
                include_pending_changes=include_pending_changes,
                include_parents=include_parents,
                relation_filters=relation_filters,
                search=search,
                offset=offset,
                sort=sort,
                order=order,
                limit=limit,
                include_total=include_total
            )
            res = self.entityStub.ListEntity(req, metadata=metadata)
            entity = MessageToDict(res, preserving_proto_field_name=True, including_default_value_fields=True)
        except Exception as e:
            raise exception_from_str("调用多租户平台主档失败: {}".format(e))
        return entity

    @get_struct
    def add_entity(self, schema_name=None, auto_enable=False, fields=None, lan="zh-CN", partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.AddEntityRequest(
                schema_name=schema_name,
                auto_enable=auto_enable,
                fields=fields,
                lan=lan
            )
            res = self.entityStub.AddEntity(req, metadata=metadata)
            entity = MessageToDict(res, preserving_proto_field_name=True, including_default_value_fields=True)
        except Exception as e:
            raise exception_from_str("调用多租户平台主档失败: {}".format(e))
        return entity

    @get_struct
    def add_entity_batch(self, entities, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.AddEntityBatchRequest(entitys=entities)
            res = self.entityStub.AddEntityBatch(req, metadata=metadata)
            entity = MessageToDict(res, preserving_proto_field_name=True, including_default_value_fields=True)
        except Exception as e:
            raise exception_from_str("调用多租户平台主档失败: {}".format(e))
        return entity

    @result_wraps
    @get_struct
    def list_region_product_by_store(self, store_id=None, return_fields=None, product_ids=None,
                                     include_product_fields=None, include_product_units=None, limit=None, offset=None,
                                     sort=None, order=None, include_total=False, filters=None, product_filters=None,
                                     product_search=None, product_search_fields=None, can_bom=False, can_order=False,
                                     can_purchase=False, can_stocktake=False, can_sales=False, check_division=False,
                                     product_relation_filters=None, region=None, lan=None, partner_id=None,
                                     user_id=None):
        """拉门店关联区域商品通用接口
        region: 区域schema name"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.relationStub.ListRegionProductByStore(
                relation_pb2.ListRegionProductByStoreRequest(store_id=store_id,
                                                             return_fields=return_fields,
                                                             product_ids=product_ids,
                                                             include_product_fields=include_product_fields,
                                                             include_product_units=include_product_units,
                                                             filters=filters,
                                                             product_filters=product_filters,
                                                             limit=limit,
                                                             offset=offset,
                                                             sort=sort,
                                                             order=order,
                                                             product_search=product_search,
                                                             product_search_fields=product_search_fields,
                                                             include_total=include_total,
                                                             can_bom=can_bom,
                                                             can_order=can_order,
                                                             can_purchase=can_purchase,
                                                             can_stocktake=can_stocktake,
                                                             can_sales=can_sales,
                                                             product_relation_filters=product_relation_filters,
                                                             check_division=check_division,
                                                             region=region,
                                                             lan=lan
                                                             ), timeout=TIMEOUT, metadata=metadata)
        except Exception as e:
            raise exception_from_str("调用多租户平台主档失败: {}".format(e))
        entity = MessageToDict(ret, preserving_proto_field_name=True)
        return entity


metadata_center_service = MetaDataCenterService()
