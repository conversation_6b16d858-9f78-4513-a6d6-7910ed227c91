import traceback

import grpc
import logging
import json
from google.protobuf.struct_pb2 import Struct
from google.protobuf.json_format import MessageToDict
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, date, timedelta

from hex_exception import UnAuthorized, exception_from_str
from supply.utils import pb2dict
from supply.utils.snowflake import gen_snowflake_id
from supply.proto.inventory import inventory_pb2
from supply.proto.inventory.inventory_pb2_grpc import InventoryServiceStub
from supply import APP_CONFIG, TIMEOUT
from .metadata_service import get_struct, result_wraps, metadata_service

from ..model.daily_cut import DailyCutLogModel
from ..driver.mysql import db_commit, session
from supply import time_cost

_HOST = str(APP_CONFIG['inventory_host'])
_PORT = str(APP_CONFIG['inventory_port'])
_CHANNEL = grpc.insecure_channel(_HOST + ':' + _PORT, options=[
    ('grpc.max_send_message_length', 1024 * 1024 * 1024),
    ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
], )


class InventoryService(object):
    """
    所有请求的patner_id和user_id在get_struct会从全局导入，或者自己单独传
    """

    def __init__(self):
        self.inventoryStub = InventoryServiceStub(_CHANNEL)

    # @result_wraps
    # @get_struct
    # 库存批处理请求，可以视为唯一的入口
    def deal_with_inventory(self, batch_no, code=None, action=None, description=None, detail=None, partner_id=None,
                            user_id=None, trace_id=None, pre_trace_id=None):
        '''
        :param batch_no:业务流水号,必须唯一，用于做幂等性检查
        :param code: 业务编码，表示各个具体的业务
        :param action: 业务操作  UNSET = 0; //默认值,未设置
                                WITHDRAW = 1; //减少库存
                                DEPOSIT = 2; //增加库存
                                TRANSFER = 3; //两个库存账户之间的转账
                                TRANSFER_INIT = 4; //准备转账
                                TRANSFER_CANCEL = 5; //取消转账
                                TRANSFER_COMMIT = 6; //确认转账
                                FREEZE = 7; //冻结
                                UNFREEZE = 8; //解冻
                                SNAPSHOT = 9; //快照
                                ACCOUNT_SUB=10; //创建子账号
                                ACCOUNT_SUSPEND=11; //账号锁定
                                ACCOUNT_CANCEL=12;//账号注销
                                MIXED = 100; //混合操作，允许Detail提交不同的操作明细
        :param description: 业务批处理详细说明
        :param detail: 详细业务数据 -list
        :return:
        '''

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = inventory_pb2.BatchRequest(batch_no=batch_no,
                                           code=code,
                                           action=action,
                                           description=description,
                                           detail=detail,
                                           trace_id=trace_id,
                                           pre_trace_id=pre_trace_id)
            # logging.info(req)
            ret = self.inventoryStub.Batch(
                req, timeout=200, metadata=metadata)

            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            if 'hash check error' in str(e):
                return {'id':1, 'status': 'success'}
            else:
                raise exception_from_str(str(e))
        return entity

    # 批次查询库存明细信息
    def query_stock_batch(self, branch_ids, product_id=None, extra=None, aggregate=False, detail=False, snapshot=False,
                          partner_id=None, user_id=None):
        # bool aggregate=1; //是否聚合所有库存，包括子库存、冻结库存、中间库存等
        # bool detail=2; //是否包括相关库存的明细
        # bool snapshot=3; //是否包括快照
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            accounts = []
            account = {}

            for branch_id in branch_ids:
                print('*********', branch_id)
                account['branch_id'] = branch_id
                if product_id:
                    account['product_id'] = product_id
                if extra:
                    account['extra'] = extra
                print(account)
                accounts.append(account)
                print(accounts)

            options = {}
            options['aggregate'] = aggregate
            options['detail'] = detail
            options['snapshot'] = snapshot

            print('*****发送数据******', accounts, options, type(accounts))

            ret = self.inventoryStub.QueryStockBatch(
                inventory_pb2.StockQueryRequest(
                    account=accounts,
                    options=options), timeout=TIMEOUT, metadata=metadata)

            print('ret:', ret)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    def deal_inv_struct(self, detail_dic):
        detail_list = []
        detail = {}
        child_sum_avail_qty  = 0
        child_sum_freeze_qty = 0
        child_sum_broker_qty = 0
        for sku_type, value in detail_dic.items():
            for bd in value:
                detail = {
                    'code': bd.get('account', {}).get('extra', {}).get('code'),
                    'sku_type': sku_type,
                    'sub_account_id': bd.get('account', {}).get('sub_account', {}).get('id'),
                    'product_id': bd.get('account', {}).get('product_id'),
                    'quantity_avail': bd.get('amount', {}).get('qty', 0),
                    'quantity_freeze': bd.get('aggregate', {}).get('freeze', 0), # sku_type仓位汇总在途
                    'quantity_broker': bd.get('aggregate', {}).get('broker', 0), # sku_type仓位汇总在途
                    }
                if bd.get('detail'):
                    extra_detail, child_sum_avail_qty, child_sum_freeze_qty, child_sum_broker_qty = self.deal_inv_struct(bd['detail']) # 账户详情信息
                    detail["extra_detail"] = extra_detail
                detail_list.append(detail)

                child_sum_avail_qty += detail['quantity_avail'] if sku_type == 'child' else 0
                child_sum_freeze_qty += detail['quantity_freeze'] if sku_type == 'child' else 0
                child_sum_broker_qty += detail['quantity_broker'] if sku_type == 'child' else 0

            

        return detail_list, child_sum_avail_qty, child_sum_freeze_qty, child_sum_broker_qty


    # 按照BranchID查询所有的商品实时库存
    # 实时库存报表用
    @time_cost
    def query_realtime_inventory(self, branch_id, sub_account_ids=None,
                                 offset=None, limit=None, page_size=None, total=True,
                                 aggregate=False, detail=False, snapshot=False,
                                 partner_id=None, user_id=None, product_ids=None,
                                 order=None, sort=None, exclude=None, multi_product=None):
        '''
            :param branch_id: 门店id
            :param sub_count_ids 子账户id列表
            :param offset: 分页开始
            :param limit: 分页结束
            :param page_size: 分页大小
            :param total: 是否返回查询总数
            :param aggregate: 是否聚合所有库存，包括子库存、冻结库存、中间库存等
            :param detail: 是否包括相关库存的明细
            :param snapshot: 是否包括快照
            :param partner_id:
            :param user_id:
            :return:{<product_id>:qty}
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        pagination = {"offset": offset, "limit": limit, "page_size": page_size, "total": total}
        options = {"aggregate": aggregate, "detail": detail, "snapshot": snapshot}
        try:
            if not order:
                order = 'DESC'
            elif order == 'desc':
                order = 'DESC'
            elif order == 'asc':
                order = 'ASC'

            if not sort:
                sort = 'updated'

            if not isinstance(branch_id, list):
                if not exclude:
                    ret = self.inventoryStub.QueryBranchInventory(inventory_pb2.InventoryQueryRequest(
                        branch_ids=inventory_pb2.InventoryQueryRequest.BranchIDs(branch_id=[branch_id]),
                        sub_account_ids=sub_account_ids,
                        pagination=pagination,
                        options=options,
                        product_ids=product_ids,
                        orderby_col=[inventory_pb2.OrderBy(
                            relation=order,
                            col_name=sort
                        )]),
                        timeout=TIMEOUT, metadata=metadata)
                else:
                    ret = self.inventoryStub.QueryBranchInventory(inventory_pb2.InventoryQueryRequest(
                        branch_ids=inventory_pb2.InventoryQueryRequest.BranchIDs(branch_id=[branch_id]),
                        sub_account_ids=sub_account_ids,
                        pagination=pagination,
                        options=options,
                        product_ids=product_ids,
                        orderby_col=[inventory_pb2.OrderBy(
                            relation=order,
                            col_name=sort
                        )],
                        extra={"exclude": exclude}
                    ), timeout=TIMEOUT, metadata=metadata)

            else:
                if not exclude:
                    ret = self.inventoryStub.QueryBranchInventory(inventory_pb2.InventoryQueryRequest(
                        branch_ids=inventory_pb2.InventoryQueryRequest.BranchIDs(branch_id=branch_id),
                        sub_account_ids=sub_account_ids,
                        pagination=pagination,
                        options=options,
                        product_ids=product_ids,
                        orderby_col=[inventory_pb2.OrderBy(
                            relation=order,
                            col_name=sort)]
                        ),
                        timeout=TIMEOUT, metadata=metadata)
                else:
                    ret = self.inventoryStub.QueryBranchInventory(inventory_pb2.InventoryQueryRequest(
                        branch_ids=inventory_pb2.InventoryQueryRequest.BranchIDs(branch_id=branch_id),
                        sub_account_ids=sub_account_ids,
                        pagination=pagination,
                        options=options,
                        product_ids=product_ids,
                        orderby_col=[inventory_pb2.OrderBy(
                            relation=order,
                            col_name=sort
                        )],
                        extra={"exclude": exclude}
                    ), timeout=TIMEOUT, metadata=metadata)

            entity = MessageToDict(ret, preserving_proto_field_name=True)
            total = entity.get('total')
            # print('!!!!!!!!!!!!!',json.dumps(entity.get('rows')))
            result = {}
            if entity and entity.get('rows'):
                if multi_product:
                    for row in entity['rows']:
                        child_sum_avail_qty  = 0
                        child_sum_freeze_qty = 0
                        child_sum_broker_qty = 0
                        data = {}
                        data['extra_detail'] = []
                        data['branch_id'] = row['account'].get('branch_id')
                        data['product_id'] = row['account'].get('product_id')
                        # data['quantity_avail'] = row['amount'].get('qty') if row['amount'].get('qty') else 0
                        # data['quantity_freeze'] = row['aggregate'].get('freeze') if row.get('aggregate') else 0
                        # data['quantity_broker'] = row['aggregate'].get('broker') if row.get('aggregate') else 0

                        # 主账户以外账户数据处理，e.g. broker-在途/ freeze-冻结/ child-子账户(子仓位概念)
                        if row.get('detail'):
                            detail_list, child_sum_avail_qty, child_sum_freeze_qty, child_sum_broker_qty = self.deal_inv_struct(row['detail'])
                            data['extra_detail'] = detail_list
                        sku_quantity_avail = row.get('amount', {}).get('qty', 0)
                        sku_quantity_freeze = row.get('aggregate', {}).get('freeze', 0)
                        sku_quantity_broker = row.get('aggregate', {}).get('broker', 0)
                        sku_detail = {
                                        'sku_type': 'sku',
                                        'product_id': row['account'].get('product_id'),
                                        'quantity_avail': sku_quantity_avail,
                                        'quantity_freeze': sku_quantity_freeze,
                                        'quantity_broker': sku_quantity_broker
                                        }
                        data['quantity_avail'] = child_sum_avail_qty+sku_quantity_avail
                        data['quantity_freeze'] = child_sum_freeze_qty+sku_quantity_freeze
                        data['quantity_broker'] = child_sum_broker_qty+sku_quantity_broker
                        if result.get(row['account'].get('product_id')):
                            result[row['account'].get('product_id')].append(data)
                        else:
                            result[row['account'].get('product_id')] = [data]
                
                else:
                    for row in entity['rows']:
                        sum_avail_qty  = 0
                        sum_freeze_qty = 0
                        sum_broker_qty = 0
                        data = {}
                        data['extra_detail'] = []
                        data['sub_detail'] = []
                        data['branch_id'] = row['account'].get('branch_id')
                        data['product_id'] = row['account'].get('product_id')
                        # data['quantity_avail'] = row['amount'].get('qty') if row['amount'].get('qty') else 0
                        # data['quantity_freeze'] = row['aggregate'].get('freeze') if row.get('aggregate') else 0
                        # data['quantity_broker'] = row['aggregate'].get('broker') if row.get('aggregate') else 0
                        if row.get('detail'):
                            for key, value in row.get('detail').items():
                                for bd in value:
                                    extra_detail = {
                                            'code': bd.get('account', {}).get('extra', {}).get('code'),
                                            # 'qty': bd.get('amount', {}).get('qty', 0),
                                            'sku_type': key,
                                            'account_id': bd.get('account', {}).get('sub_account', {}).get('id'),
                                            'product_id': bd.get('account', {}).get('product_id'),
                                            'quantity_avail': bd.get('amount', {}).get('qty', 0),
                                            'quantity_freeze': bd.get('aggregate', {}).get('freeze', 0),
                                            'quantity_broker': bd.get('aggregate', {}).get('broker', 0),
                                        }
                                    data['extra_detail'].append(extra_detail)
                                    sum_avail_qty += extra_detail['quantity_avail'] if key == 'child' else 0
                                    sum_freeze_qty += extra_detail['quantity_freeze'] if key == 'child' else 0
                                    sum_broker_qty += extra_detail['quantity_broker'] if key == 'child' else 0
                        sku_quantity_avail = row['amount'].get('qty', 0)
                        sku_quantity_freeze = row.get('aggregate', {}).get('freeze', 0)
                        sku_quantity_broker = row.get('aggregate', {}).get('broker', 0)
                        sku_detail = {
                                        'sku_type': 'sku',
                                        'product_id': row['account'].get('product_id'),
                                        'quantity_avail': sku_quantity_avail,
                                        'quantity_freeze': sku_quantity_freeze,
                                        'quantity_broker': sku_quantity_broker
                                        }
                        data['extra_detail'].append(sku_detail)
                        data['quantity_avail'] = sum_avail_qty+sku_quantity_avail
                        data['quantity_freeze'] = sum_freeze_qty+sku_quantity_freeze
                        data['quantity_broker'] = sum_broker_qty+sku_quantity_broker
                        result[row['account'].get('product_id')] = data
            return result, total
        except Exception as e:
            raise exception_from_str(str(e))

    # 获取门店下所有商品当前库存######此方法返回product做键，库存信息字典做值的map，方便取数据
    # LIKE：{'4176565203472744449': {'product_id': '4176565203472744449', 'quantity_avail': 84.0},
    #       '4176582377935470593': {'product_id': '4176582377935470593', 'quantity_avail': 69.0},
    # 按照BranchID查询所有的商品实时库存——返回格式包装过
    @get_struct
    def get_products_inventory_by_branch_id(self, branch_id, sub_account_ids=None,
                                            offset=0, limit=-1, page_size=None, total=True,
                                            aggregate=False, detail=False, snapshot=False,
                                            partner_id=None, user_id=None, product_ids=None):
        '''
        :param branch_id: 门店id
        :param sub_account_ids 子账户id列表
        :param offset: 分页开始
        :param limit: 分页结束
        :param page_size: 分页大小
        :param total: 是否返回查询总数
        :param aggregate: 是否聚合所有库存，包括子库存、冻结库存、中间库存等
        :param detail: 是否包括相关库存的明细
        :param snapshot: 是否包括快照
        :param partner_id:
        :param user_id:
        :param product_ids
        :return:{<product_id>:qty}
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        pagination = {"offset": offset, "limit": limit, "page_size": page_size, "total": total}
        options = {"aggregate": aggregate, "detail": detail, "snapshot": snapshot}
        try:
            order = 'DESC'
            sort = 'updated'

            if not isinstance(branch_id, list):
                ret = self.inventoryStub.QueryBranchInventory(inventory_pb2.InventoryQueryRequest(
                    branch_ids=inventory_pb2.InventoryQueryRequest.BranchIDs(branch_id=[branch_id]),
                    sub_account_ids=sub_account_ids,
                    pagination=pagination,
                    options=options,
                    product_ids=product_ids,
                    orderby_col=[inventory_pb2.OrderBy(
                        relation=order,
                        col_name=sort
                    )]
                ), timeout=TIMEOUT, metadata=metadata)

            else:
                ret = self.inventoryStub.QueryBranchInventory(inventory_pb2.InventoryQueryRequest(
                    branch_ids=inventory_pb2.InventoryQueryRequest.BranchIDs(branch_id=branch_id),
                    sub_account_ids=sub_account_ids,
                    pagination=pagination,
                    options=options,
                    product_ids=product_ids,
                    orderby_col=[inventory_pb2.OrderBy(
                        relation=order,
                        col_name=sort
                    )]
                ), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = {}
            if entity and entity.get('rows'):
                for row in entity['rows']:
                    if row.get('account') and row.get('amount'):
                        if row['account'].get('product_id') and row['amount'].get('qty'):
                            data = {}
                            data['product_id'] = row['account'].get('product_id')
                            data['quantity_avail'] = row['amount'].get('qty')
                            result[row['account'].get('product_id')] = data
                    else:
                        # 包装后返回子账户库存格式和主账户一样
                        # {"product_id": {"product_id": '4176565203472744449', "quantity_avail": 84.0}}
                        detail = row.get('detail')
                        if detail and isinstance(detail, dict):
                            childes = detail.get('child') if detail.get('child') else []
                            for bd in childes:
                                sub_data = {}
                                sub_data["sub_account"] = bd.get('account', {}).get('sub_account', {}).get('id')
                                product_id = bd.get('account', {}).get('product_id')
                                sub_data['product_id'] = product_id
                                sub_data.update({
                                    'quantity_avail': bd.get('amount', {}).get('qty', 0),
                                    'quantity_freeze': bd.get('aggregate', {}).get('freeze'),
                                    'quantity_broker': bd.get('aggregate', {}).get('broker')
                                })
                                if sub_data["sub_account"]:
                                    key = str(sub_data["sub_account"]) + str(product_id)
                                else:
                                    key = str(product_id)
                                result[key] = sub_data
            return result
        except Exception as e:
            raise exception_from_str(str(e))

    # 根据操作id等获取操作结果
    @get_struct
    def get_batch_inventory_by_id_batch_no_code(self, id,
                                                partner_id=None, user_id=None):
        '''
        :param id: 批处理id
        :param batch_no: 批次号
        :param code:业务操作编码
        :param offset:
        :param limit:
        :param page_size:
        :param total:
        :param aggregate:
        :param detail:
        :param snapshot:
        :param partner_id:
        :param user_id:
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.inventoryStub.QueryBatch(inventory_pb2.BatchQuery(
                id=id,
            ), metadata=metadata)

            entity = MessageToDict(ret, preserving_proto_field_name=True)
            return entity
        except Exception as e:
            raise exception_from_str(str(e))

    # 查询库存记录情况
    def query_accounting(self, batch_id=None, batch_no=None, code=None, action=None, detail=None, partner_id=None,
                         user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if batch_id:
                ret = self.inventoryStub.QueryAccouting(
                    inventory_pb2.BatchQuery(id=batch_id, detail=detail), timeout=TIMEOUT, metadata=metadata)
            else:
                batch = {}
                batch['batch_no'] = batch_no
                batch['code'] = code
                batch['action'] = action
                ret = self.inventoryStub.QueryAccouting(
                    inventory_pb2.BatchQuery(batch=batch, detail=detail), timeout=TIMEOUT, metadata=metadata)

            print('*************', ret)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            # logging.error('库存批处理请求',e)
            raise exception_from_str(str(e))
        return entity

    # 库存流水请求——门店&商品查询
    def query_log(self, branch_id, start_date, end_date, product_id=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            account = {}
            account['branch_id'] = branch_id
            if product_id:
                account['product_id'] = product_id
            ret = self.inventoryStub.QueryLog(
                inventory_pb2.LogQueryRequest(account=account, start=start_date, end=end_date), timeout=TIMEOUT,
                metadata=metadata)

            # print('*************',ret)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            # logging.error('库存批处理请求',e)
            raise exception_from_str(str(e))
        return entity

    def query_log_list(self, branch_id, start_date, end_date, product_ids=None, code=None, action=None,
                       limit=None, offset=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )

        try:
            accounts = []
            account = {}
            if product_ids:
                for product_id in product_ids:
                    # account['branch_id'] = branch_id
                    account['product_id'] = product_id
                    accounts.append(account)
            # print(start_date, type(start_date))
            # print(accounts)
            ret = self.inventoryStub.QueryLogList(inventory_pb2.ListQueryRequest(
                branch_id=branch_id,
                limit=limit,
                offset=offset,
                # account=accounts,
                start=start_date, end=end_date
            ), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            # logging.error('库存批处理请求',e)
            raise exception_from_str(str(e))
        return entity

    # 根据门店和商品筛选库存流水
    def list_inventory_log(self, branch_id, start_date, end_date, product_ids=None, order_type=None, action=None,
                           limit=None, offset=None, partner_id=None, user_id=None, code=None,
                           account_type=None, sub_account_ids=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if order_type:
            codes = [order_type]
        else:
            codes = None

        try:
            if isinstance(code, list):
                ret = self.inventoryStub.ListInventoryLog(inventory_pb2.ListInventoryLogRequest(
                    branch_id=branch_id,
                    product_ids=product_ids,
                    codes=codes,
                    limit=limit,
                    offset=offset,
                    start_time=start_date,
                    end_time=end_date,
                    trace_ids=code,
                    account_type=account_type,
                    sub_account_ids=sub_account_ids
                ), metadata=metadata)
            else:
                if code:
                    ret = self.inventoryStub.ListInventoryLog(inventory_pb2.ListInventoryLogRequest(
                        branch_id=branch_id,
                        product_ids=product_ids,
                        codes=codes,
                        limit=limit,
                        offset=offset,
                        start_time=start_date,
                        end_time=end_date,
                        trace_ids=[code],
                        account_type=account_type,
                        sub_account_ids=sub_account_ids
                    ), metadata=metadata)
                else:
                    ret = self.inventoryStub.ListInventoryLog(inventory_pb2.ListInventoryLogRequest(
                        branch_id=branch_id,
                        product_ids=product_ids,
                        codes=codes,
                        limit=limit,
                        offset=offset,
                        start_time=start_date,
                        end_time=end_date,
                        account_type=account_type,
                        sub_account_ids=sub_account_ids
                    ), metadata=metadata)
            print('**s**',ret)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            # logging.error('库存批处理请求',e)
            raise exception_from_str(str(e))
        return entity

    # 批量查询库存快照数据
    @time_cost
    def query_snap_list(self, branch_id, start_date, end_date, product_ids=None, code=None, action=None,
                        limit=None, offset=None, partner_id=None, user_id=None, extra=None, sub_account_ids=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            accounts = []

            if product_ids:
                for product_id in product_ids:
                    account = {}
                    account['branch_id'] = branch_id
                    account['product_id'] = product_id
                    accounts.append(account)

            ret = self.inventoryStub.QuerySnapshotList(inventory_pb2.ListQueryRequest(
                branch_id=[branch_id],
                sub_account_ids=sub_account_ids,
                account=accounts,
                code=code,
                action=action,
                extra=extra,
                limit=limit,
                offset=offset,
                start=start_date, end=end_date
            ), metadata=metadata)
            send = inventory_pb2.ListQueryRequest(
                branch_id=[branch_id],
                sub_account_ids=sub_account_ids,
                account=accounts,
                code=code,
                action=action,
                extra=extra,
                limit=limit,
                offset=offset,
                start=start_date, end=end_date
            )
            # logging.info('sendin')
            logging.info("daily request: {}".format(send))
            logging.info("daily resposne: {}".format(ret))
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            logging.info(json.dumps(entity))

        except Exception as e:
            # logging.error('库存批处理请求',e)
            raise exception_from_str(str(e))
        return entity

    # 批量查询库存快照数据(聚合)
    @time_cost
    def query_snapshot_sum_list(self, branch_id, start_date, end_date, product_id=None, limit=-1, offset=0,
                                if_pre=None, if_end=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            product_ids = [int(id) for id in product_id]
            req = inventory_pb2.SumListQueryRequest(
                branch_id=[int(branch_id)],
                start=start_date,
                end=end_date,
                limit=limit,
                offset=offset,
                product_id=product_ids,
                if_pre=if_pre,
                if_end=if_end
            )
            # logging.info("inventory request: {}".format(req))
            # logging.info("inventory request: {}".format(metadata))
            ret = self.inventoryStub.QuerySnapshotSumList(req, metadata=metadata)
            
            # logging.info("inventory response: {}".format(ret))
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            # logging.info(json.dumps(entity))

        except Exception as e:
            # logging.error('库存批处理请求',e)
            raise exception_from_str(str(e))
        return entity


    # 查询单一商品库存快照数据
    def query_snap_by_product_id(self, branch_id, product_id, start_date, end_date,
                                 limit=None, offset=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            account = {}
            account['branch_id'] = branch_id
            account['product_id'] = product_id

            print(account)

            ret = self.inventoryStub.QuerySnapshot(inventory_pb2.SnapshotQueryRequest(
                account=account,
                # limit=limit, offset=offset,
                start=start_date, end=end_date), metadata=metadata)

            print('*************', ret)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            # logging.error('库存批处理请求',e)
            raise exception_from_str(str(e))
        return entity

    # 库存批处理请求，可以视为唯一的入口
    @db_commit
    def cut_daily_snapshot(self, branch_id, product_ids, end_date, partner_id=None, user_id=None, batch_no=None,
                           extra_type=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )

        try:
            if not isinstance(end_date, datetime):
                timestamp = Timestamp()
                timestamp.seconds = end_date.seconds
                date = end_date
                end_date = timestamp.ToDatetime()
                end_date_d = end_date
            else:
                # value=value-timedelta(hours=8)
                timestamp = Timestamp()
                timestamp.FromDatetime(end_date)
                date = timestamp
                end_date_d = end_date

            end_date = end_date_d.strftime('%Y%m%d')
            if not batch_no:
                batch_no = str(gen_snowflake_id())

            # store_code = str(branch_id)[-5:]
            details = []
            count = 0
            if extra_type and extra_type == 5:
                for product_id in product_ids:
                    detail = {}
                    snapshot = {}
                    account = {}
                    count += 1
                    batch_no = batch_no
                    account['extra'] = {
                        'id': str(branch_id),
                        'type': extra_type
                    }
                    account['product_id'] = int(product_id)
                    snapshot['account'] = account
                    snapshot['end_time'] = date
                    detail['sequence_id'] = count
                    detail['snapshot'] = snapshot
                    details.append(detail)
            else:
                for product_id in product_ids:
                    detail = {}
                    snapshot = {}
                    account = {}
                    count += 1
                    batch_no = batch_no
                    account['branch_id'] = int(branch_id)
                    account['product_id'] = int(product_id)
                    snapshot['account'] = account
                    snapshot['end_time'] = date
                    detail['sequence_id'] = count
                    detail['snapshot'] = snapshot
                    # print(detail)
                    details.append(detail)
                    # print(details)
                    # print(count, detail)
                    # details.append(this_detail)
            description = 'DAILY_SNAPSHOT'

            send = inventory_pb2.BatchRequest(batch_no=batch_no,
                                              code='DAILY_SNAPSHOT',
                                              action='SNAPSHOT',
                                              description=description,
                                              detail=details)
            ret = self.inventoryStub.Batch(send, timeout=TIMEOUT, metadata=metadata)
            # print(ret, type(ret))
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            # print(entity, type(entity))
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 
    def list_group_event_log(self, branch_id, start_time, end_time, product_ids=None,
                             limit=None, offset=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )

        try:
            ret = self.inventoryStub.ListGroupEventLog(inventory_pb2.ListGroupEventLogRequest(
                branch_id=branch_id,
                product_ids=product_ids,
                limit=limit,
                offset=offset,
                start_time=start_time,
                end_time=end_time
            ), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            # logging.error('库存批处理请求',e)
            raise exception_from_str(str(e))
        return entity

    # 查询一个月门店商品业务数据聚合
    @time_cost
    def query_month_snap_by_store_id(self, branch_id, start_time, end_time, product_ids=None, code=None,
                                     partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.inventoryStub.ListGroupSnapshot(inventory_pb2.ListGroupSnapshotRequest(
                branch_id=branch_id,
                product_ids=product_ids,
                code=code,
                start_time=start_time,
                end_time=end_time), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 查询昨日销量/ 周平均销量
    @time_cost
    def query_snapshot_for_sales(self, branch_id, start_time=None, end_time=None, code=None,
                                 partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            # print('*******', inventory_pb2.SnapshotStatRequest(
            #     branch_id=branch_id,
            #     code=code,
            #     start=start_time,
            #     end=end_time))
            ret = self.inventoryStub.QuerySnapshotStats(inventory_pb2.SnapshotStatRequest(
                branch_id=branch_id,
                code=code,
                start=start_time,
                end=end_time), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 查询成本中心下的所有商品列表
    @time_cost
    def query_group_product(self, group_id, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.inventoryStub.QueryGroupProduct(
                inventory_pb2.QueryGroupProductRequest(
                    group_id=group_id), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # group的操作
    def deal_with_group(self, action, group_dict, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        group_items = []
        for key, value in group_dict.items():
            group_item = {
                "group_id": int(value),
                "branch_id": int(key),
                "action": action,
                "description": "AddGroup"
            }
            group_items.append(group_item)
        try:
            ret = self.inventoryStub.Group(
                inventory_pb2.GroupRequest(
                    group_item=group_items), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 查询门店盘点单处理结果
    @time_cost
    def query_stocktake(self, batch_nos: list, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.inventoryStub.QueryStocktake(inventory_pb2.QueryStocktakeRequest(
                batch_nos=batch_nos), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    @time_cost
    @get_struct
    def query_account_inventory_list(self, accounts: list, partner_id=None, user_id=None):
        """批量查询多账户实时库存列表
        一个账户对应一个商品返回一个结果
        :param accounts 账户列表
        :param partner_id
        :param user_id
        :returns total, accounts[account]
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.inventoryStub.QueryAccountInventoryList(inventory_pb2.InventoryQueryLsitRequest(
                accounts=accounts), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            total = entity.get('total', 0)
            print("InventoryQueryLsitRequest", inventory_pb2.InventoryQueryLsitRequest(accounts=accounts))
            if entity:
                result = entity.get('rows')
        except Exception as e:
            raise exception_from_str(str(e))
        return total, result

    @time_cost
    def query_branch_inventory_sort(self, partner_id, user_id, branch_id, product_ids=None, query_type=1, limit=-1,
                                    offset=None, sort='desc'):
        """
        商品实时库存查询并排序
        """
        logging.info(f'库存排序参数：{locals()}')
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        page = inventory_pb2.PageOrder(limit=limit, offset=offset, sort=sort)
        ret = self.inventoryStub.QueryBranchInventorySort(inventory_pb2.QueryBranchInventorySortRequest(
            branch_id=branch_id, product_ids=product_ids, query_type=query_type, page=page), metadata=metadata)
        try:
            result = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            logging.info(f'query_branch_inventory_sort_error: {traceback.format_exc()}')
            result = [{'product_id': i, 'flag': '1'} for i in product_ids]

        logging.info(f'库存排序结果: {result}')
        return result


inventory_service = InventoryService()
