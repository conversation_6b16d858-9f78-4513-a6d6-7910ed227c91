import time
import grpc
import datetime
from google.protobuf.json_format import MessageToDict
from google.protobuf.timestamp_pb2 import Timestamp
from supply.proto.metadata import product_pb2 as product_pb2
from supply.proto.metadata import store_pb2 as store_pb2
from supply.proto.metadata import region_pb2 as region_pb2
from supply.proto.metadata import contract_pb2 as contract_pb2
from supply.proto.metadata.entity import entity_pb2
from supply.proto.metadata.relation import relation_pb2
from supply.proto.metadata.contract_pb2_grpc import ContractServiceStub
from supply.proto.metadata.product_pb2_grpc import ProductServiceStub
from supply.proto.metadata.store_pb2_grpc import StoreServiceStub
from supply.proto.metadata.region_pb2_grpc import RegionServiceStub
from supply.proto.metadata.entity.entity_pb2_grpc import EntityServiceStub
from supply.proto.metadata.relation.relation_pb2_grpc import RelationServiceStub

from supply import APP_CONFIG, TIMEOUT
from google.protobuf.struct_pb2 import Struct
from supply import request_info
from hex_exception import UnAuthorized
from supply.client.ianvs_service import ianvs_service
from ..error.exception import *
from hex_exception import exception_from_str
from supply import logger, time_cost
import traceback
from supply.utils import pb2dict
from . import build_grpc_channel_options
from supply.proto.business_rule.business_rule_pb2_grpc import OrderRuleServicesStub
from supply.proto.business_rule import business_rule_pb2

from supply.proto.price_center.price_center_pb2_grpc import PriceCenterServiceStub
from supply.proto.price_center import price_center_pb2

_PRODUCT_CHANNEL_OPTIONS = build_grpc_channel_options(package_services=[
    ("product", "ProductService"),
    ("store", "StoreService"),
    ("region", "RegionService"),
    ("contract", "ContractService")

])
_PRODUCT_AGS_HOST = str(APP_CONFIG['product_ags_host'])
_PRODUCT_AGS_PORT = str(APP_CONFIG['product_ags_port'])
_PRODUCT_CHANNEL = grpc.insecure_channel(_PRODUCT_AGS_HOST + ':' + _PRODUCT_AGS_PORT,
                                         options=_PRODUCT_CHANNEL_OPTIONS
                                         )
# _Oauth_Client_ADDRESS = str(APP_CONFIG['oauth_address'])
_METADATA_CHANNEL_OPTIONS = build_grpc_channel_options(package_services=[
    ("entity", "EntityService"),
    ("relation", "RelationService")

]
)
options = [
    ('grpc.keepalive_time_ms', 10000),
    ('grpc.keepalive_timeout_ms', 5000),
    ('grpc.keepalive_permit_without_calls', True),
    ('grpc.http2.max_pings_without_data', 0),
    ('grpc.http2.min_time_between_pings_ms', 10000),
    ('grpc.http2.min_ping_interval_without_data_ms', 5000),
    ('grpc.max_receive_message_length', 10 * 1024 * 1024)
]
_METADATA_ADDRESS = str(APP_CONFIG['metadata_address'])
_METADATA_CHANNEL = grpc.insecure_channel(_METADATA_ADDRESS,
                                          options=options
                                          )

##### order_rule
_ORDER_RULE_OPTIONS = build_grpc_channel_options(package_services=[
    ("order_rule", "OrderRuleService")

]
)
options = [
    ('grpc.keepalive_time_ms', 10000),
    ('grpc.keepalive_timeout_ms', 5000),
    ('grpc.keepalive_permit_without_calls', True),
    ('grpc.http2.max_pings_without_data', 0),
    ('grpc.http2.min_time_between_pings_ms', 10000),
    ('grpc.http2.min_ping_interval_without_data_ms', 5000),
    ('grpc.max_receive_message_length', 10 * 1024 * 1024)
]
_ORDER_RULE_ADDRESS = str(APP_CONFIG['order_rule_address'])
_ORDER_RULE_CHANNEL = grpc.insecure_channel(_ORDER_RULE_ADDRESS,
                                            options=options
                                            )

##### price
_PRICE_OPTIONS = build_grpc_channel_options(package_services=[
    ("price_center", "PriceCenterService")
]
)
options = [
    ('grpc.keepalive_time_ms', 10000),
    ('grpc.keepalive_timeout_ms', 5000),
    ('grpc.keepalive_permit_without_calls', True),
    ('grpc.http2.max_pings_without_data', 0),
    ('grpc.http2.min_time_between_pings_ms', 10000),
    ('grpc.http2.min_ping_interval_without_data_ms', 5000),
    ('grpc.max_receive_message_length', 10 * 1024 * 1024)
]
_PRICE_ADDRESS = str(APP_CONFIG['price_center_address'])
_PRICE_CHANNEL = grpc.insecure_channel(_PRICE_ADDRESS,
                                            options=options
                                            )


domain_map = {
    "boh.store.diff": "store-supply-config-diff",
    "boh.store.return": "store-supply-config-return",
    "boh.store.self_picking": "store-supply-config-pick",
    "boh.store.transfer": "store-supply-config-transfer",
    "boh.store.stocktake": "store-supply-config-stocktake",
    "boh.store.adjust": "store-supply-config-adjust",
    "boh.store.order": "store-supply-config-order"
}


def get_struct(func):
    def inner(*args, **kwargs):
        if kwargs.get('filters'):
            filte_dict = kwargs.get('filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['filters'] = struct
        if kwargs.get("store_filters"):
            filte_dict = kwargs.get('store_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['store_filters'] = struct
        if kwargs.get("store_relation_filters"):
            filte_dict = kwargs.get('store_relation_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['store_relation_filters'] = struct
        if kwargs.get("product_relation_filters"):
            filte_dict = kwargs.get('product_relation_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['product_relation_filters'] = struct
        if kwargs.get('product_filters'):
            filte_dict = kwargs.get('product_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['product_filters'] = struct
        if kwargs.get('relation_filters'):
            relation_filters_dict = kwargs.get('relation_filters')
            struct = Struct()
            struct.update(relation_filters_dict)
            kwargs['relation_filters'] = struct
        if not kwargs.get("partner_id", None) or not kwargs.get('user_id', None):
            # partner_id, user_id 要传关键字参数
            if hasattr(request_info, "partner_id") and hasattr(request_info, "user_id"):
                kwargs['partner_id'] = request_info.partner_id
                kwargs['user_id'] = request_info.user_id
            else:
                raise UnAuthorized("请传入partner_id和user_id")
        return func(*args, **kwargs)

    return inner


def result_wraps(func):
    def inner(*args, **kwargs):
        entity = func(*args, **kwargs)
        if entity.get('rows'):
            rows = entity['rows']
        else:
            rows = [entity]
        for row in rows:
            if row.get("product"):
                row.update(row['product'])
                del row['product']
        return entity

    return inner


class MetaDataService(object):
    """
    所有请求的patner_id和user_id在get_struct会从全局导入，或者自己单独传
    所有主档信息多物件强制用list方法，避免多次调用影响api响应速度
    """
    TIMEOUT = 60 * 2

    def __init__(self):
        self.productStub = ProductServiceStub(_PRODUCT_CHANNEL)
        self.storeStub = StoreServiceStub(_PRODUCT_CHANNEL)
        self.regionStub = RegionServiceStub(_PRODUCT_CHANNEL)
        # self.oauthClient = OauthClient(_Oauth_Client_ADDRESS)
        self.entityStub = EntityServiceStub(_METADATA_CHANNEL)
        self.contractStub = ContractServiceStub(_PRODUCT_CHANNEL)
        self.relationStub = RelationServiceStub(_METADATA_CHANNEL)

        self.orderRuleStub = OrderRuleServicesStub(_ORDER_RULE_CHANNEL)
        self.priceStub = PriceCenterServiceStub(_PRICE_CHANNEL)

    @result_wraps
    @get_struct
    def get_store(self, store_id, code=None, return_fields=None, partner_id=None, user_id=None):
        '''所有主档信息多物件强制用list方法，避免多次调用影响api响应速度
        :param store_id:
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param relation: 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.storeStub.GetStoreById(
                store_pb2.GetStoreByIdRequest(id=store_id,
                                              return_fields=return_fields,
                                              code=code), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    def get_product(self, product_id, return_fields=None, relation=None, include_units=False, partner_id=None,
                    user_id=None):
        '''所有主档信息多物件强制用list方法，避免多次调用影响api响应速度
        :param product_id:
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param relation: 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.productStub.GetProductById(product_pb2.GetProductByIdRequest(id=product_id,
                                                                                    include_units=include_units,
                                                                                    return_fields=return_fields,
                                                                                    relation=relation),
                                                  timeout=TIMEOUT,
                                                  metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    def get_unit(self, unit_id, return_fields=None, relation=None, partner_id=None, user_id=None):
        '''所有主档信息多物件强制用list方法，避免多次调用影响api响应速度
        :param unit_id:
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param relation: 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.productStub.GetUnitById(product_pb2.GetUnitByIdRequest(id=int(unit_id),
                                                                              return_fields=return_fields),
                                               timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    @time_cost
    def get_store_list(self, filters=None, return_fields=None, ids=None, limit=-1, offset=0,
                       code=None, sort=None, include_total=True, search=None, search_fields=None, relation_filters=None,
                       partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.storeStub.ListStore(store_pb2.ListStoreRequest(limit=limit,
                                                                      offset=offset,
                                                                      return_fields=return_fields,
                                                                      ids=ids,
                                                                      relation_filters=relation_filters,
                                                                      search=search,
                                                                      search_fields=search_fields,
                                                                      filters=filters,
                                                                      include_total=include_total,
                                                                      lan='zh-CN'),
                                           timeout=TIMEOUT,
                                           metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    @time_cost
    def get_product_list(self, filters=None, return_fields=None, ids=None, relation_filters=None, search=None, limit=-1,
                         search_fields=None, include_units=False, include_total=False, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        # logger.info("relation_filters: {}".format(relation_filters))
        try:
            ret = self.productStub.ListProduct(
                product_pb2.ListProductRequest(limit=limit,
                                               filters=filters,
                                               return_fields=return_fields,
                                               ids=ids,
                                               relation_filters=relation_filters,
                                               search=search,
                                               search_fields=search_fields,
                                               include_units=include_units,
                                               include_total=include_total,
                                               lan='zh-CN'),
                timeout=TIMEOUT, metadata=metadata)
            print('ret', ret)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            # raise exception_from_str(str(e))
            pass
        return entity

    def get_product_units_dict(self, product_ids, partner_id, user_id, return_fields=None):
        product_detail_list = self.get_product_list(ids=product_ids, include_units=True,
                                                    return_fields=return_fields,
                                                    partner_id=partner_id, user_id=user_id).get('rows', [])

        product_unit_dict = {}
        if not product_detail_list:
            return {}, []
        for product_detail_single in product_detail_list:
            if product_detail_single.get('units'):
                product_unit_dict[str(product_detail_single['id'])] = {}
                for unit_filter in product_detail_single['units']:
                    if unit_filter.get('order'):
                        product_unit_dict[str(product_detail_single['id'])]['order'] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate')
                        }
                    if unit_filter.get('default'):
                        product_unit_dict[str(product_detail_single['id'])]['default'] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate')
                        }
                    if unit_filter.get('sales'):
                        product_unit_dict[str(product_detail_single['id'])]['sales'] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate')
                        }
                    if unit_filter.get('purchase'):
                        product_unit_dict[str(product_detail_single['id'])]['purchase'] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate')
                        }
                    if unit_filter.get('bom'):
                        product_unit_dict[str(product_detail_single['id'])]['bom'] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate')
                        }
                    if unit_filter.get('stocktake'):
                        product_unit_dict[str(product_detail_single['id'])]['stocktake'] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate')
                        }
        return product_unit_dict, product_detail_list

    @result_wraps
    @get_struct
    def get_product_category_list(self, code=None, return_fields=None, limit=-1, offset=0, sort=None, order=None,
                                  include_total=True, search=None, search_fields=None, filters=None, ids=None,
                                  partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.productStub.ListProductCategory(
                product_pb2.ListProductCategoryRequest(code=code,
                                                       return_fields=return_fields,
                                                       limit=limit,
                                                       offset=offset,
                                                       sort=sort,
                                                       order=order,
                                                       include_total=include_total,
                                                       search=search,
                                                       search_fields=search_fields,
                                                       filters=filters,
                                                       ids=ids), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    def get_unit_list(self, limit=-1, offset=0, sort=None, order=None, include_total=None, search=None,
                      search_fields=None, filters=None, return_fields=None, ids=None, partner_id=None, user_id=None):
        '''
        :param filters: 按字段过滤
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param ids: 按id列表查询
        :param relation: 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.productStub.ListUnit(product_pb2.ListUnitRequest(limit=limit,
                                                                        offset=offset,
                                                                        sort=sort,
                                                                        ids=ids,
                                                                        order=order,
                                                                        include_total=include_total,
                                                                        search=search,
                                                                        search_fields=search_fields,
                                                                        filters=filters,
                                                                        return_fields=return_fields),
                                            timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    def get_unit_map(self, partner_id, user_id):
        """获取单位map"""
        unit_dict = {}
        units_ret = self.get_unit_list(return_fields='id,code,name', partner_id=partner_id, user_id=user_id)
        units = []
        if units_ret:
            units = units_ret['rows']
        if isinstance(units, list):
            for unit in units:
                if isinstance(unit, dict) and 'id' in unit:
                    unit_dict[str(unit['id'])] = unit
        return unit_dict

    @result_wraps
    @get_struct
    def get_branch_region_list(self, filters=None, return_fields=None, ids=None, partner_id=None, user_id=None):
        '''
        :param filters: 按字段过滤
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param ids: 按id列表查询
        :param relation: 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.regionStub.ListRegion(region_pb2.ListRegionRequest(region_type="branch",
                                                                          limit=-1,
                                                                          filters=filters,
                                                                          return_fields=return_fields,
                                                                          ids=ids),
                                             timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    @time_cost
    def get_distrit_region_list(self, filters=None, return_fields=None, ids=None, partner_id=None, user_id=None):
        '''
        :param filters: 按字段过滤
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param ids: 按id列表查询
        :param relation: 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.regionStub.ListRegion(region_pb2.ListRegionRequest(region_type="distribution",
                                                                          limit=-1,
                                                                          filters=filters,
                                                                          return_fields=return_fields,
                                                                          ids=ids),
                                             timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    def get_attribute_region_list(self, filters=None, return_fields=None, ids=None, partner_id=None, user_id=None):
        '''
        :param filters: 按字段过滤
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param ids: 按id列表查询
        :param relation: 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.regionStub.ListRegion(region_pb2.ListRegionRequest(region_type="attribute",
                                                                          limit=-1,
                                                                          filters=filters,
                                                                          return_fields=return_fields,
                                                                          ids=ids),
                                             timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    def get_region_by_id(self, region_id: int, region_type, code=None, return_fields=None, partner_id=None,
                         user_id=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.regionStub.GetRegionById(region_pb2.GetRegionByIdRequest(id=region_id,
                                                                                region_type=region_type,
                                                                                code=code,
                                                                                return_fields=return_fields
                                                                                ),
                                                timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    @time_cost
    # 属性区域
    def get_attribute_products_by_store_id(self, store_id, product_ids=None, filters=None, product_filters=None,
                                           return_fields=None, include_product_fields=None, limit=-1, partner_id=None,
                                           user_id=None, product_search=None, product_search_fields=None,
                                           include_total=True,
                                           offset=None, include_product_units=False, can_bom=False, can_order=False,
                                           can_purchase=False, can_stocktake=False, can_sales=False,
                                           product_relation_filters=None, check_division=False):
        ''' 获取相同属性区域的商品
        :param store_id:
        :param product_ids: 限制的商品id，如果为空则为全部
        :param filters:按字段过滤
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param include_product_fields: 额外返回product的字段
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if isinstance(include_product_fields, list):
            include_product_fields = ",".join(include_product_fields)
        if isinstance(return_fields, list):
            return_fields = ",".join(return_fields)
        try:
            ret = self.productStub.ListAttributeRegionProductByStoreId(
                product_pb2.ListAttributeRegionProductByStoreRequest(store_id=store_id,
                                                                     include_product_fields=include_product_fields,
                                                                     return_fields=return_fields,
                                                                     product_ids=product_ids,
                                                                     filters=filters,
                                                                     product_filters=product_filters,
                                                                     limit=limit,
                                                                     offset=offset,
                                                                     product_search=product_search,
                                                                     product_search_fields=product_search_fields,
                                                                     include_total=include_total,
                                                                     include_product_units=include_product_units,
                                                                     can_bom=can_bom,
                                                                     can_order=can_order,
                                                                     can_purchase=can_purchase,
                                                                     can_stocktake=can_stocktake,
                                                                     can_sales=can_sales,
                                                                     product_relation_filters=product_relation_filters,
                                                                     check_division=check_division
                                                                     ), timeout=TIMEOUT, metadata=metadata)
        except Exception as e:

            raise exception_from_str(str(e))
        entity = MessageToDict(ret, preserving_proto_field_name=True)
        return entity

    # 配送区域
    @result_wraps
    @get_struct
    @time_cost
    def get_distribution_products_by_store_id(self, store_id, product_ids=None, filters=None, return_fields=None,
                                              product_filters=None, check_division=False,
                                              include_product_fields=None, limit=-1, partner_id=None, user_id=None):
        ''' 获取相同属性区域的商品
        :param store_id:
        :param product_ids: 限制的商品id，如果为空则为全部
        :param filters:按字段过滤
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param include_product_fields: 额外返回product的字段
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if isinstance(include_product_fields, list):
            include_product_fields = ",".join(include_product_fields)
        if isinstance(return_fields, list):
            return_fields = ",".join(return_fields)
        if product_filters == False:
            product_filters = Struct()
        try:
            ret = self.productStub.ListDistributionRegionProductByStoreId(
                product_pb2.ListDistributionRegionProductByStoreRequest(store_id=store_id,
                                                                        include_product_fields=include_product_fields,
                                                                        return_fields=return_fields,
                                                                        product_ids=product_ids,
                                                                        include_product_units=True,
                                                                        filters=filters,
                                                                        product_filters=product_filters,
                                                                        limit=limit,
                                                                        check_division=check_division
                                                                        ), timeout=TIMEOUT, metadata=metadata)
        except Exception as e:

            raise exception_from_str(str(e))
        entity = MessageToDict(ret, preserving_proto_field_name=True)
        return entity

    # 采购区域
    @result_wraps
    @get_struct
    @time_cost
    def get_purchase_product_by_store_id(self, store_id, product_ids=None, filters=None, return_fields=None,
                                         product_filters=None, include_product_fields=None, include_product_units=True,
                                         check_division=False, limit=-1, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if isinstance(include_product_fields, list):
            include_product_fields = ",".join(include_product_fields)
        if isinstance(return_fields, list):
            return_fields = ",".join(return_fields)
        # if isinstance(product_filters, Struct):
        #     product_filters.update({"delivery_type__eq": "SUPPLIER"})
        # elif product_filters == None:
        #     product_filters = Struct()
        #     product_filters.update({"delivery_type__eq": "SUPPLIER"})
        if product_filters == False:
            product_filters = Struct()
        try:
            ret = self.productStub.ListPurchaseRegionProductByStoreId(
                product_pb2.ListPurchaseRegionProductByStoreRequest(store_id=store_id,
                                                                    include_product_fields=include_product_fields,
                                                                    return_fields=return_fields,
                                                                    product_ids=product_ids,
                                                                    include_product_units=include_product_units,
                                                                    filters=filters,
                                                                    product_filters=product_filters,
                                                                    limit=limit,
                                                                    check_division=check_division
                                                                    ), timeout=TIMEOUT, metadata=metadata)
        except Exception as e:
            raise GetMetadataException("获取主档信息失败" + str(e))
        entity = MessageToDict(ret, preserving_proto_field_name=True)
        return entity

    def get_product_by_warehouse_id(self, warehouse_id, limit=-1, partner_id=None, user_id=None):
        entity = self.get_entity_by_id(id=warehouse_id, schema_name="distrcenter",
                                       partner_id=partner_id, user_id=user_id)
        if not entity:
            raise DataValidationException("主档未找到该仓库")
        fields = entity.get("fields")
        relation = None
        if isinstance(fields, dict):
            relation = fields.get("relation")
        if not relation or not relation.get("product_category"):
            logger.error("该仓库下未关联商品分类-{}".format(warehouse_id))
            return None

        product_category = relation.get("product_category")

        # 然后根据商品分类拉取商品主档
        relation_filters = dict(product_category=product_category)
        entity = self.get_product_list(relation_filters=relation_filters,
                                       include_units=True,
                                       filters={"status__eq": "ENABLED"},
                                       return_fields="id,code,name,category",
                                       partner_id=partner_id,
                                       user_id=user_id,
                                       include_total=True)
        return entity

    # 获取用户名
    def get_username_by_pid_uid(self, partner_id, user_id):
        try:
            users = ianvs_service.ListUserInfo(partner_id=partner_id, user_id=user_id).get('rows', [])
            user_dict = dict()
            if users:
                for user in users:
                    user_dict[convert_to_int(user.get('id'))] = user.get('nick')
            return user_dict.get(user_id, '')
        except Exception as e:
            logger.error("获取用户名失败: {} \n{}".format(str(e), traceback.format_exc()))
            return None

    # 查询所属公司
    @result_wraps
    @get_struct
    def get_company_by_id(self, company_id: int, code=None, return_fields=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(code, list):
                code = ",".join(code)
            if isinstance(return_fields, list):
                return_fields = ",".join(return_fields)
            ret = self.storeStub.GetCompanyById(store_pb2.GetCompanyByIdRequest(
                id=company_id, code=code, return_fields=return_fields), timeout=TIMEOUT, metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        entity = MessageToDict(ret, preserving_proto_field_name=True)
        return entity

    # 查询配送中心
    @result_wraps
    @get_struct
    def get_distribution_center(self, center_id: int, code=None, return_fields=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(code, list):
                code = ",".join(code)
            if isinstance(return_fields, list):
                return_fields = ",".join(return_fields)
            ret = self.storeStub.GetDistributionCenterById(store_pb2.GetDistributionCenterByIdRequest(
                id=center_id, code=code, return_fields=return_fields), timeout=TIMEOUT, metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        entity = MessageToDict(ret, preserving_proto_field_name=True)
        return entity

    # 查询供应商
    @result_wraps
    @get_struct
    def get_vendor_center(self, center_id: int, code=None, return_fields=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(code, list):
                code = ",".join(code)
            if isinstance(return_fields, list):
                return_fields = ",".join(return_fields)
            ret = self.storeStub.GetVendorById(store_pb2.GetVendorByIdRequest(
                id=center_id, code=code, return_fields=return_fields), timeout=TIMEOUT, metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        entity = MessageToDict(ret, preserving_proto_field_name=True)
        return entity

    # 枚举配送中心
    @result_wraps
    @get_struct
    def get_distribution_center_list(self, code=None, filters=None, return_fields=None, ids=None, relation_filters=None,
                                     search=None,
                                     search_fields=None, include_units=False, include_total=False, partner_id=None,
                                     user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.storeStub.ListDistributionCenter(
                store_pb2.ListDistributionCenterRequest(limit=-1,
                                                        filters=filters,
                                                        code=code,
                                                        return_fields=return_fields,
                                                        ids=ids,
                                                        search=search,
                                                        search_fields=search_fields,
                                                        include_total=include_total), timeout=TIMEOUT,
                metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise GetMetadataException("获取主档信息失败" + str(e))
        return entity

    # 查询商品类
    @result_wraps
    @get_struct
    def get_category(self, category_id, code=None, return_fields=None, partner_id=None, user_id=None):
        '''
        :param category_id:
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param code: 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.productStub.GetProductCategoryById(product_pb2.GetProductCategoryByIdRequest(id=int(category_id),
                                                                                                    return_fields=return_fields),
                                                          timeout=TIMEOUT,
                                                          metadata=metadata)
            # print(ret)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    def get_store_scope(self, donot_transfer_branch_to_store=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.storeStub.GetStoreDataScope(store_pb2.GetStoreDataScopeRequest(
                donot_transfer_branch_to_store=donot_transfer_branch_to_store), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            logger.error(traceback.format_exc())
            raise exception_from_str(str(e))
        return entity

    def get_warehouse_scope(self, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.storeStub.GetWarehouseScope(store_pb2.GetWarehouseScopeRequest(
            ), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            logger.error(traceback.format_exc())
            raise exception_from_str(str(e))
        return entity

    def get_machining_scope(self, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.storeStub.GetMachiningCenterScopeDetail(store_pb2.GetMachiningCenterScopeRequest(
            ), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            rows = entity.get('rows', [])
            machining_ids = []
            if rows:
                for row in rows:
                    machining_ids.append(int(row.get('id', 0)))
        except Exception as e:
            logger.error(traceback.format_exc())
            raise exception_from_str(str(e))
        return machining_ids

    @result_wraps
    @get_struct
    @time_cost
    def get_product_price_by_store_id(self, store_id,item_codes=[],partner_id=None, user_id=None):
        price_type_id = 5014098871228399616
        req = price_center_pb2.GetProductPriceAgentRequest(
            store_id=int(store_id), item_codes=item_codes,price_type_id=price_type_id,page = {"limit":-1,"offset":0}
        )

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            logger.info("GetProductPriceAgent-req---{}".format(req))
            ret = self.priceStub.GetProductPriceAgent(req, metadata=metadata)

            entity = MessageToDict(ret, preserving_proto_field_name=True)
            logger.info("GetProductPriceAgent---{}".format(entity))
        except Exception as e:
            logger.error(traceback.format_exc())
            raise exception_from_str(str(e))
        return entity


    @result_wraps
    @get_struct
    @time_cost
    def get_list_valid_product_for_distr_by_id(self, store_id, return_fields=None, product_ids=None,
                                               include_product_fields=None,
                                               include_product_units=True, limit=-1, offset=0, sort=None, order=None,
                                               include_total=True, filters=None,
                                               product_filters=None, product_search=None, product_search_fields=None,
                                               product_relation_filters=None, distr_type=None, order_date=None,
                                               check_division=False,filter_type=None, partner_id=None, user_id=None):
        if isinstance(order_date, datetime.datetime):
            order_date = Timestamp(seconds=int(order_date.timestamp()))
        req = business_rule_pb2.QueryValidProductByStoreRequest(
            store_id=int(store_id), product_ids=product_ids,filter_type=filter_type,page = {"limit":-1,"offset":0}
        )

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.orderRuleStub.QueryValidProductByStore(req, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            logger.error(traceback.format_exc())
            raise exception_from_str(str(e))
        return entity

        # if isinstance(order_date, datetime.datetime):
        #     order_date = Timestamp(seconds=int(order_date.timestamp()))
        # req = product_pb2.ListValidProductsForDistributionByStoreRequest(
        #     store_id=int(store_id), return_fields=return_fields, product_ids=product_ids,
        #     include_product_fields=include_product_fields,
        #     include_product_units=include_product_units, limit=limit, offset=offset, sort=sort, order=order,
        #     include_total=include_total,
        #     filters=filters, product_filters=product_filters, product_search=product_search,
        #     product_search_fields=product_search_fields,
        #     product_relation_filters=product_relation_filters, distr_type=distr_type, order_date=order_date,
        #     check_division=check_division
        # )
        #
        # metadata = (
        #     ("partner_id", str(partner_id)),
        #     ("user_id", str(user_id))
        # )
        # try:
        #     ret = self.productStub.ListValidProductsForDistributionByStoreId(req, metadata=metadata)
        #     entity = MessageToDict(ret, preserving_proto_field_name=True)
        # except Exception as e:
        #     logger.error(traceback.format_exc())
        #     raise exception_from_str(str(e))
        # return entity

    @result_wraps
    @get_struct
    @time_cost
    def get_list_valid_store_for_distr_by_product_ids(self, product_ids, return_fields=None, filters=None,
                                                      store_ids=None,
                                                      store_filters=None, store_relation_filters=None,
                                                      product_filters=None, product_relation_filters=None,
                                                      product_search=None, product_search_fields=None,
                                                      include_product_fields=None, include_product_units=None,
                                                      limit=None, offset=None, sort=None, order=None,
                                                      include_total=True, distr_type=None, order_date=None,
                                                      check_division=False,partner_id=None, user_id=None):

        if isinstance(order_date, datetime.datetime):
            order_date = Timestamp(seconds=int(order_date.timestamp()))
        req = business_rule_pb2.QueryValidStoresForDistributionByProductRequest(
            product_ids=product_ids, store_ids=store_ids,page = {"limit":-1,"offset":0},filter_type=1

        )

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.orderRuleStub.QueryValidStoresForDistributionByProduct(req, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            logger.error(traceback.format_exc())
            raise exception_from_str(str(e))
        return entity

    @result_wraps
    @get_struct
    def get_company_list(self, limit=-1, offset=0, sort=None, order=None, include_total=None, search=None,
                         search_fields=None, filters=None, return_fields=None, ids=None, partner_id=None, user_id=None):
        '''
        :param filters: 按字段过滤
        :param return_fields: 除code和relation之外需要返回的字段, 多个以逗号隔开
        :param ids: 按id列表查询
        :param relation: 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
        :return:
        '''
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.storeStub.ListCompany(store_pb2.ListCompanyRequest(limit=limit,
                                                                          offset=offset,
                                                                          sort=sort,
                                                                          ids=ids,
                                                                          order=order,
                                                                          include_total=include_total,
                                                                          search=search,
                                                                          search_fields=search_fields,
                                                                          filters=filters,
                                                                          return_fields=return_fields),
                                             timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    # 获取商品单位map
    def get_product_unit_dict(self, partner_id, user_id, product_codes=None, product_ids=None):

        """
        return内容
        {
            "商品id": {

                    "order": {
                        "id": 订货单位id,
                        "unit_rate": 单位转换率,
                        "code": 订货单位编号,
                        "name": 订货单位名称
                    },
                    "default": {
                        "id": 核算单位id,
                        "unit_rate": 单位转换率,
                        "code": 核算单位编号,
                        "name": 核算单位名称
                    }
                }
        }
        """

        # 获取商品详情
        if product_ids:
            product_detail_list = self.get_product_list(ids=product_ids, include_units=True,
                                                        return_fields="id,code,name,category,id,storage_type,sale_type,product_type,bom_type",
                                                        partner_id=partner_id, user_id=user_id).get('rows', [])
        if product_codes:
            product_detail_list = self.get_product_list(filters={'code__in': product_codes}, include_units=True,
                                                        return_fields="id,code,name,category,id,storage_type,sale_type,product_type,bom_type",
                                                        partner_id=partner_id, user_id=user_id).get('rows', [])

        product_detail_dict = {}  # 商品详情map，key为商品id
        unit_ids = []  # 商品单位id列表，用于后续请求主档单位详情

        for product_detail_single in product_detail_list:
            product_detail_dict[str(product_detail_single.get('code', 0))] = product_detail_single
            if product_detail_single.get('units'):
                for unit_filter in product_detail_single['units']:
                    unit_id = int(unit_filter.get('id', 0))
                    unit_ids.append(unit_id)

        # 获取单位详情
        filter_unit_list = self.get_unit_list(ids=unit_ids, partner_id=partner_id, user_id=user_id)
        filter_unit_list = filter_unit_list.get('rows')
        key_filter_unit_dict = {}  # 单位map，key是单位id
        if not filter_unit_list:
            return {}

        for filter_unit in filter_unit_list:
            key_filter_unit_dict[str(filter_unit.get('id', 0))] = filter_unit

        product_unit_dict = {}  # 商品-单位详情map，key1为商品id，key2为单位类型
        if not product_detail_list:
            return {}
        for product_detail_single in product_detail_list:
            if product_detail_single.get('units'):
                product_unit_dict[str(product_detail_single.get('id', 0))] = {}
                for unit_filter in product_detail_single['units']:
                    code = key_filter_unit_dict.get(str(unit_filter['id'])).get('code')
                    if unit_filter.get('order') == True:  # 订货单位
                        product_unit_dict[str(product_detail_single.get('id', 0))]["order"] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate'),
                            'code': code,
                            'name': key_filter_unit_dict.get(str(unit_filter['id'])).get('name')
                        }
                    if unit_filter.get('default') == True:  # 核算单位
                        product_unit_dict[str(product_detail_single.get('id', 0))]["default"] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate'),
                            'code': code,
                            'name': key_filter_unit_dict.get(str(unit_filter['id'])).get('name')
                        }
                    if unit_filter.get('sales') == True:  # 销售单位
                        product_unit_dict[str(product_detail_single.get('id', 0))]["sales"] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate'),
                            'code': code,
                            'name': key_filter_unit_dict.get(str(unit_filter['id'])).get('name')
                        }
                    if unit_filter.get('purchase') == True:  # 采购单位
                        product_unit_dict[str(product_detail_single.get('id', 0))]["purchase"] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate'),
                            'code': code,
                            'name': key_filter_unit_dict.get(str(unit_filter['id'])).get('name')
                        }
                    if unit_filter.get('bom') == True:  # 配方单位
                        product_unit_dict[str(product_detail_single.get('id', 0))]["bom"] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate'),
                            'code': code,
                            'name': key_filter_unit_dict.get(str(unit_filter['id'])).get('name')
                        }
                    if unit_filter.get('stocktake') == True:  # 盘点单位
                        product_unit_dict[str(product_detail_single.get('id', 0))]["stocktake"] = {
                            'id': unit_filter['id'],
                            'rate': unit_filter.get('rate'),
                            'code': code,
                            'name': key_filter_unit_dict.get(str(unit_filter['id'])).get('name')
                        }

        return product_unit_dict

    def list_distribution_region_product_by_store_id(
            self, partner_id, user_id, store_id,
            product_ids=None, limit=-1,
            include_product_fields=None,
            include_product_units=True,
    ):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        req = product_pb2.ListDistributionRegionProductByStoreRequest(
            store_id=store_id,
            product_ids=product_ids,
            limit=limit,
            include_product_fields=include_product_fields,
            include_product_units=include_product_units,
        )
        resp = self.productStub.ListDistributionRegionProductByStoreId(req, metadata=metadata)
        return MessageToDict(resp, preserving_proto_field_name=True).get('rows', [])

    def list_demand_change_limits(self, partner_id, user_id):
        """

        :param partner_id:
        :param user_id:
        :return:
        """
        req = entity_pb2.ListEntityRequest(
            schema_name="advice-order",
            limit=-1,
        )
        resp = self.entityStub.ListEntity(req, metadata=self.get_metadata(partner_id, user_id))
        resp = MessageToDict(resp, preserving_proto_field_name=True, including_default_value_fields=True)
        rows = resp.get('rows', [])
        ret = []
        for row in rows:
            f = row['fields']
            change_rate_upper = convert_to_float(f.get('tolerance')) / 100  # 因为不知道主档配置的数据是否复合规范
            change_rate_lower = convert_to_float(f.get('tolerance_lower')) / 100
            ret.append({
                'sale_type': f['sale_type'],
                'product_type': f['product_type'],
                'bom_type': f['bom_type'],
                'change_rate_upper': change_rate_upper,
                'change_rate_lower': change_rate_lower,
            })
        return ret

    def list_weight_factor(self, partner_id, user_id):
        """

        :param partner_id:
        :param user_id:
        :return:
        """
        req = entity_pb2.ListEntityRequest(
            schema_name="advice-order-factor",
            limit=-1,
        )
        resp = self.entityStub.ListEntity(req, metadata=self.get_metadata(partner_id, user_id))
        resp = MessageToDict(resp, preserving_proto_field_name=True, including_default_value_fields=True)
        rows = resp.get('rows', [])
        ret = []
        now = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        for row in rows:
            f = row['fields']
            # 没设置生效时间，就一直生效
            effective_start_date = entity_date_time_to_python(f.get('effective_start_date'))
            effective_end_date = entity_date_time_to_python(f.get('effective_end_date'))
            if effective_start_date and (now < effective_start_date):  # 不在生效时间
                continue
            if effective_end_date and (now > effective_end_date):  # 不在生效时间
                continue
            weather_factor = convert_to_float(f.get('weather'), default=100.0) / 100  # 因为不知道主档配置的数据是否复合规范
            discount_factor = convert_to_float(f.get('promotion'), default=100.0) / 100
            holiday_factor = convert_to_float(f.get('holiday'), default=100.0) / 100
            geo_region_id = convert_to_int(f.get("region_city"), default=0)
            store_id = convert_to_int(f.get("region_store"), default=0)
            sale_types = f.get('sale_types', [])
            for sale_type in sale_types:
                ret.append({
                    'geo_region_id': geo_region_id,
                    'store_id': store_id,
                    'sale_type': sale_type,
                    'product_type': f['product_type'],
                    'bom_type': f['bom_type'],
                    'weather_factor': weather_factor,
                    'discount_factor': discount_factor,
                    'holiday_factor': holiday_factor,
                })
        return ret

    def list_inventory_safety_factor(self, partner_id, user_id):
        """

        :param partner_id:
        :param user_id:
        :return:
        """
        req = entity_pb2.ListEntityRequest(
            schema_name="advice-order-safety",
            limit=-1,
        )
        resp = self.entityStub.ListEntity(req, metadata=self.get_metadata(partner_id, user_id))
        resp = MessageToDict(resp, preserving_proto_field_name=True, including_default_value_fields=True)
        rows = resp.get('rows', [])
        ret = []
        for row in rows:
            f = row['fields']
            inventory_safety = convert_to_float(f.get('safety'), default=0) / 100
            ret.append({
                'sale_type': f['sale_type'],
                'product_type': f['product_type'],
                'bom_type': f['bom_type'],
                'inventory_safety': inventory_safety,
            })
        return ret

    def list_forecast_days_rate_params(self, partner_id, user_id):
        req = entity_pb2.ListEntityRequest(
            schema_name="advice-order-estimate",
            limit=-1,
        )
        resp = self.entityStub.ListEntity(req, metadata=self.get_metadata(partner_id, user_id))
        resp = MessageToDict(resp, preserving_proto_field_name=True, including_default_value_fields=True)
        rows = resp.get('rows', [])
        ret = []
        for row in rows:
            f = row['fields']
            forecast_before_arrival_days = convert_to_int(f.get('prediction'), default=7)
            forecast_rate = convert_to_float(f.get('percentage'), 80) / 100
            ret.append({
                'sale_type': f['sale_type'],
                'product_type': f['product_type'],
                'bom_type': f['bom_type'],
                'forecast_before_arrival_days': forecast_before_arrival_days,
                'forecast_rate': forecast_rate,
            })
        return ret

    def get_entity_by_id(self, partner_id, user_id, id, schema_name, return_fields=None, include_parents=False):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        req = entity_pb2.GetEntityByIdRequest(
            id=id,
            return_fields=return_fields,
            include_parents=include_parents,
            schema_name=schema_name,
        )
        resp = self.entityStub.GetEntityById(req, metadata=metadata)
        return MessageToDict(resp, preserving_proto_field_name=True, including_default_value_fields=True)

    @staticmethod
    def get_metadata(partner_id, user_id):
        return (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )

    def get_tax_list(self, vendor_id=None, product_id_list=None, warehouse_id=None, store_id=None, machining_id=None,
                     valid_time=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if valid_time and isinstance(valid_time, datetime.datetime):
                valid_time = datetime.datetime.strftime(valid_time, '%Y-%m-%d')
            req = contract_pb2.GetTaxListReq(
                vendor_id=str(vendor_id) if vendor_id else None,
                pro_id_list=product_id_list,
                warehouse_id=str(warehouse_id) if warehouse_id else None,
                store_id=str(store_id) if store_id else None,
                machining_center_id=str(machining_id) if machining_id else None,
                valid_time=valid_time)
            ret = self.contractStub.GetTaxList(
                req, timeout=TIMEOUT, metadata=metadata).list
            logger.info('req:{}, taxPriceRet{}'.format(req, ret))
            product_price_dict = {}
            if ret:
                for detail in ret:
                    # if not detail.get('tax'):
                    # raise DataValidationException("请维护商品合同价：{}".format(detail.product_id))
                    product_price_dict[int(detail.product_id)] = pb2dict(detail)

        except Exception as e:
            raise exception_from_str(str(e))
        return product_price_dict

    def get_tax_rate_map(self, product_ids=None, start_time=None, end_time=None, tz=8, partner_id=None,
                         user_id=None):
        """
        repeated string pro_id_list = 1;
        string start_time = 2;
        string end_time = 3;
        tz : 业务utc时间偏移量
        :return tax_rate_map {product_id: rate}
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if start_time and isinstance(start_time, datetime.datetime):
                start_time = start_time + datetime.timedelta(hours=tz)
                start_time = datetime.datetime.strftime(start_time, '%Y-%m-%d')
            if end_time and isinstance(end_time, datetime.datetime):
                end_time = end_time + datetime.timedelta(hours=tz)
                end_time = datetime.datetime.strftime(end_time, '%Y-%m-%d')
            if product_ids:
                product_ids = [str(pid) for pid in product_ids]
            ret = self.contractStub.GetTaxRateList(
                contract_pb2.GetTaxRateReq(pro_id_list=product_ids, start_time=start_time, end_time=end_time),
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True, including_default_value_fields=True)
            res = dict()
            if ret.get('list') and isinstance(ret.get('list'), list):
                for row in ret.get('list'):
                    res.update(row)
        except Exception as e:
            raise exception_from_str(str(e))
        return res

    @get_struct
    def list_entity(self, schema_name=None, state=None, include_state=False, filters=None, relation=None,
                    return_fields=None, partner_id=None, user_id=None, ids=None, relation_filters=None,
                    sort=None, order=None):
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name=schema_name,
                state=state,
                include_state=include_state,
                filters=filters,
                relation=relation,
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=-1, sort=sort, order=order
            )
            res = self.entityStub.ListEntity(req, metadata=self.get_metadata(partner_id, user_id))
            entity = MessageToDict(res, preserving_proto_field_name=True, including_default_value_fields=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    def get_store_cost_center_map(self, partner_id, user_id):
        # 公司-成本中心dict
        company_costcenter_dict = {}
        entity_list = self.list_entity(
            schema_name='company-info', partner_id=partner_id, user_id=user_id).get('rows')
        for entity in entity_list:
            if entity.get('fields').get('relation'):
                costcenter = entity.get('fields').get('relation').get('cost_center')
                if costcenter:
                    company_costcenter_dict[entity['id']] = costcenter
        # 门店-公司dict
        costcenter_store_dict = {}
        store_list = self.list_entity(
            schema_name='store', partner_id=partner_id, user_id=user_id).get('rows')
        for store in store_list:
            if store.get('fields').get('relation'):
                company_id = store.get('fields').get('relation').get('company_info')
                if costcenter:
                    costcenter_store_dict[store['id']] = company_id
        # 门店-成本中心dict
        store_costcenter_dict = {}
        for key, value in costcenter_store_dict.items():
            if company_costcenter_dict.get(value):
                store_costcenter_dict[key] = company_costcenter_dict.get(value)
        return store_costcenter_dict

    def get_branch_cost_center_map(self, partner_id, user_id, branch_id=None):
        cost_center_details = self.list_entity(partner_id=partner_id, user_id=user_id,
                                               schema_name='cost-center').get('rows')
        branch_costcenter_dict = {}
        for cost_center_detail in cost_center_details:
            fileds = cost_center_detail.get('fields')
            if not fileds:
                continue
            relation = fileds.get('relation')
            if not relation:
                continue
            stores = relation['stores'] if relation.get('stores') else []
            warehouse = relation['warehouse'] if relation.get('warehouse') else []
            machining = relation['machining'] if relation.get('machining') else []
            branch = stores + warehouse + machining
            if branch:
                for b in branch:
                    branch_costcenter_dict[b] = cost_center_detail.get('id')
        if branch_id:
            return branch_costcenter_dict.get(str(branch_id))
        return branch_costcenter_dict

    def get_product_unit(self, ids, partner_id, user_id):
        product_detail_list = self.list_entity(
            ids=ids,
            schema_name='product',
            relation="unit",
            partner_id=partner_id, user_id=user_id).get('rows', [])
        product_unit_dict = dict()
        for product_detail in product_detail_list:
            product_id = int(product_detail.get('id'))
            product_unit_dict[product_id] = dict()
            if product_detail.get('fields') and \
                    product_detail.get('fields').get('relation') and \
                    product_detail.get('fields').get('relation').get('unit'):
                units_list = product_detail['fields']['relation']['unit']
                for unit in units_list:
                    product_unit_dict[product_id][int(unit['id'])] = unit.get('rate') if unit.get('rate') else 1
        return product_unit_dict

    # 获取成本中心下的所有门店
    def get_cost_center_branch_list(self, partner_id, user_id, cost_center_id=None):
        if cost_center_id:
            cost_center_details = self.get_entity_by_id(partner_id=partner_id, user_id=user_id,
                                                        id=int(cost_center_id),
                                                        schema_name='cost-center').get('rows')
        else:
            cost_center_details = self.list_entity(partner_id=partner_id, user_id=user_id,
                                                   schema_name='cost-center').get('rows')
        costcenter_branch_dict = {}
        for cost_center_detail in cost_center_details:
            fileds = cost_center_detail.get('fields')
            relation = fileds.get('relation')
            if not relation:
                continue
            stores = relation['stores'] if relation.get('stores') else []
            warehouse = relation['warehouse'] if relation.get('warehouse') else []
            costcenter_branch_dict[cost_center_detail['id']] = {
                'STORE': stores,
                'WAREHOUSE': warehouse
            }
        return costcenter_branch_dict

    def get_product_detail_with_unit(self, ids, partner_id, user_id):
        product_detail_list = self.list_entity(
            ids=ids,
            schema_name='product',
            partner_id=partner_id, user_id=user_id).get('rows', [])
        unit_list = self.list_entity(
            schema_name='unit',
            partner_id=partner_id, user_id=user_id).get('rows', [])
        unit_dict = dict()
        for u in unit_list:
            unit_id = str(u.get('id'))
            unit_dict[unit_id] = {
                'code': u.get('fields').get('code') if u.get('fields') else None,
                'name': u.get('fields').get('name') if u.get('fields') else None
            }

        product_detail_dict = dict()
        for product_detail in product_detail_list:
            product_id = int(product_detail.get('id'))
            product_detail_dict[product_id] = dict()
            if product_detail.get('fields'):
                product_detail_dict[product_id]['code'] = product_detail.get('fields').get('code')
                product_detail_dict[product_id]['name'] = product_detail.get('fields').get('name')
                product_detail_dict[product_id]['units'] = {}

                if product_detail.get('fields').get('relation') and \
                        product_detail.get('fields').get('relation').get('unit'):
                    units_list = product_detail['fields']['relation']['unit']
                    for unit in units_list:
                        unit_id = unit.get('id')
                        if unit.get('bom') and unit.get('bom') == True:
                            product_detail_dict[product_id]['units']['bom'] = {
                                'id': unit.get('id'),
                                'rate': unit.get('rate'),
                                'code': unit_dict.get(unit_id).get('code') if unit_dict.get(unit_id) else None,
                                'name': unit_dict.get(unit_id).get('name') if unit_dict.get(unit_id) else None
                            }
                        if unit.get('default') and unit.get('default') == True:
                            product_detail_dict[product_id]['units']['default'] = {
                                'id': unit.get('id'),
                                'rate': unit.get('rate'),
                                'code': unit_dict.get(unit_id).get('code') if unit_dict.get(unit_id) else None,
                                'name': unit_dict.get(unit_id).get('name') if unit_dict.get(unit_id) else None
                            }
                        if unit.get('order') and unit.get('order') == True:
                            product_detail_dict[product_id]['units']['order'] = {
                                'id': unit.get('id'),
                                'rate': unit.get('rate'),
                                'code': unit_dict.get(unit_id).get('code') if unit_dict.get(unit_id) else None,
                                'name': unit_dict.get(unit_id).get('name') if unit_dict.get(unit_id) else None
                            }
                        if unit.get('purchase') and unit.get('purchase') == True:
                            product_detail_dict[product_id]['units']['purchase'] = {
                                'id': unit.get('id'),
                                'rate': unit.get('rate'),
                                'code': unit_dict.get(unit_id).get('code') if unit_dict.get(unit_id) else None,
                                'name': unit_dict.get(unit_id).get('name') if unit_dict.get(unit_id) else None
                            }
                        if unit.get('sales') and unit.get('sales') == True:
                            product_detail_dict[product_id]['units']['sales'] = {
                                'id': unit.get('id'),
                                'rate': unit.get('rate'),
                                'code': unit_dict.get(unit_id).get('code') if unit_dict.get(unit_id) else None,
                                'name': unit_dict.get(unit_id).get('name') if unit_dict.get(unit_id) else None
                            }
                        if unit.get('stocktake') and unit.get('stocktake') == True:
                            product_detail_dict[product_id]['units']['stocktake'] = {
                                'id': unit.get('id'),
                                'rate': unit.get('rate'),
                                'code': unit_dict.get(unit_id).get('code') if unit_dict.get(unit_id) else None,
                                'name': unit_dict.get(unit_id).get('name') if unit_dict.get(unit_id) else None
                            }
        return product_detail_dict

    def list_stores_by_region_ids(self, region_ids=None, partner_id=None, user_id=None, region_name=None):
        """根据地理区域id列表获取包含的门店列表
        region_ids [str(地理区域id)]
        :return store_ids[int]"""
        if region_name:
            relation_filters = {region_name: [str(b_id) for b_id in region_ids]}
        else:
            relation_filters = {'geo_region': [str(b_id) for b_id in region_ids]}
        store_ids = []
        branch_list = metadata_service.get_store_list(relation_filters=relation_filters,
                                                      return_fields="id,code,name",
                                                      partner_id=partner_id, user_id=user_id).get('rows', [])
        if branch_list:
            for store in branch_list:
                store_ids.append(convert_to_int(store.get('id')))
        return store_ids

    @time_cost
    @get_struct
    def get_position_list(self, filters=None, return_fields=None, ids=None, limit=-1, offset=0,
                          sort=None, include_total=True, relation=None, relation_filters=None,
                          partner_id=None, user_id=None):
        """查询仓位列表主档"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="POSITION_CONFIG",
                filters=filters,
                relation=relation,
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    @time_cost
    @get_struct
    def get_position_relation_list(self, filters=None, return_fields=None, ids=None, limit=-1, offset=0,
                                   sort=None, include_total=True, relation=None, relation_filters=None,
                                   partner_id=None, user_id=None):
        """查询仓位列表主档"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="POSITION_RELATION_CONFIG",
                filters=filters,
                relation="all",
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    @time_cost
    def get_position_dict(self, filters=None, return_fields=None, ids=None, limit=-1, offset=0,
                          sort=None, include_total=True, relation=None, relation_filters=None,
                          partner_id=None, user_id=None):
        """查询仓位列表主档"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="POSITION_CONFIG",
                filters=filters,
                relation=relation,
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = {}
            if entity and isinstance(entity, dict):
                rows = entity.get('rows')
                if rows:
                    for row in rows:
                        result[row.get('id', 0)] = row.get('fields', {})
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    @time_cost
    @get_struct
    def get_machining_center_list(self, filters=None, return_fields=None, ids=None, limit=-1, offset=0,
                                  sort=None, include_total=True, relation=None, relation_filters=None,
                                  partner_id=None, user_id=None):
        """查询加工中心主档列表"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="MACHINING_CENTER",
                filters=filters,
                relation=relation,
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            if entity and isinstance(entity, dict):
                rows = entity.get('rows', [])
                if rows:
                    for row in rows:
                        fields = row.get('fields', {})
                        fields['id'] = row.get('id')
                        result.append(fields)
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    @time_cost
    @get_struct
    def get_franchisee_list(self, filters=None, return_fields=None, ids=None, limit=-1, offset=0,
                            sort=None, include_total=True, relation=None, relation_filters=None,
                            partner_id=None, user_id=None):
        """查询加盟商列表"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="FRANCHISEE",
                filters=filters,
                relation=relation,
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            if entity and isinstance(entity, dict):
                rows = entity.get('rows', [])
                if rows:
                    for row in rows:
                        fields = row.get('fields', {})
                        fields['id'] = row.get('id')
                        result.append(fields)
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    @time_cost
    def get_business_config(self, filters=None, return_fields=None, ids=None, limit=-1, offset=0,
                            sort=None, include_total=True, relation=None, relation_filters=None,
                            partner_id=None, user_id=None):
        """查询总部管理-运营设置-业务配置(多仓位配置)
        :return {
                "warehouse": [str..],
                "region": [str..],
                "stores": [str..],
                "machining": [str..]
                }
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="BUSINESS-CONFIG",
                filters=filters,
                relation="all",
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = {}
            if entity and isinstance(entity, dict):
                rows = entity.get('rows', [])
                if rows:
                    fields = rows[0].get('fields', {})
                    relation = fields.get('relation')
                    result['store_all'] = True if fields.get('store') == 'all' else False
                    result['warehouse_all'] = True if fields.get('warehouse') == 'all' else False
                    result['machining_all'] = True if fields.get('process') == 'all' else False
                    if relation:
                        result.update(relation)
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    @time_cost
    @get_struct
    def get_position_relation_config(self, filters=None, return_fields=None, branch_ids=None, branch_type=None,
                                     limit=-1, offset=0, sort=None, include_total=True, relation=None,
                                     relation_filters=None, partner_id=None, user_id=None):
        """查询门店/仓库/加工中心运营下-业务配置(仓位相关的业务配置)
        branch_type: -> WAREHOUSE/MACHINING_CENTER/STORE
        branch_ids: 门店/仓库/加工中心id列表 -> [str...]
        :return [
            {
                "adjust": [position_id..],      报废
                "stocktake": [position_id..],   盘点
                "transfer": position_id(str),   外部调拨
                "deliveryReceive": position_id, 配送收货
                "deliveryReturn": position_id,  配送退货
                "directReceive": position_id,   直送收货
                "directReturn": position_id,    直送退货
                "insideTransfer": position_id,  内部调拨
                "sale": position_id,            销售
            },
            ...
        ]
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if not all([branch_type, branch_ids]):
            raise DataValidationException("请传入筛选条件branch_type & branch_ids")
        filters = {}
        if branch_type == 'STORE':
            filters.update({"relation.stores__in": branch_ids})
        if branch_type == 'WAREHOUSE':
            filters.update({"relation.warehouse__in": branch_ids})
        if branch_type == 'MACHINING_CENTER':
            filters.update({"relation.machining__in": branch_ids})
        struct = Struct()
        struct.update(filters)
        filters = struct
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="POSITION_RELATION_CONFIG",
                filters=filters,
                relation=relation,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            if entity and isinstance(entity, dict):
                rows = entity.get('rows', [])
                if rows:
                    for row in rows:
                        fields = row.get('fields', {})
                        result.append(fields)
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    @time_cost
    @get_struct
    def get_machining_center_by_id(self, return_fields=None, _id=None, code=None, relation=None,
                                   include_state=None, partner_id=None, user_id=None):
        """查询加工中心主档,返回单条数据
        :return [
            {"id": id, "code": code, "name": name},
            {...},
        ]
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.GetEntityByIdRequest(
                schema_name="MACHINING_CENTER",
                relation=relation,
                id=_id,
                code=code,
                include_state=include_state,
                return_fields=return_fields,
            )
            ret = self.entityStub.GetEntityById(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = {}
            if entity and isinstance(entity, dict):
                fields = entity.get('fields', {})
                fields['id'] = entity.get('id')
                result = fields
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    @get_struct
    def list_positions(self, filters=None, return_fields=None, ids=None, partner_id=None, user_id=None):
        req = entity_pb2.ListEntityRequest(
            ids=ids,
            schema_name="POSITION_CONFIG",
            limit=-1,
            filters=filters,
            return_fields=return_fields
        )
        resp = self.entityStub.ListEntity(req, metadata=self.get_metadata(partner_id, user_id))
        resp = MessageToDict(resp, preserving_proto_field_name=True, including_default_value_fields=True)
        return resp

    @get_struct
    def get_position_transfer_config(self, return_fields=None, branch_ids=None, branch_type=None, out_position=None,
                                     in_position=None, limit=-1, offset=0, sort=None, include_total=True,
                                     material_ids=None, relation=None, relation_filters=None,
                                     partner_id=None, user_id=None):
        """查询门店/仓库/加工中心运营下-自动调拨规则配置
        branch_type: -> WAREHOUSE/MACHINING_CENTER/STORE
        in_position: -> 调入仓位(str)
        out_position: -> 调出仓位(str)
        material_ids: -> 调拨物料列表[str...]
        branch_ids: 门店/仓库/加工中心id列表 -> [str...]
        :return [
                {code,inWhs,material_id,name,outWhs},{}...
                ]
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if not all([branch_type, branch_ids]):
            raise DataValidationException("请传入筛选条件branch_type & branch_ids")
        filters = {}
        if branch_type == 'STORE':
            filters.update({"relation.stores__in": branch_ids})
        if branch_type == 'WAREHOUSE':
            filters.update({"relation.warehouse__in": branch_ids})
        if branch_type == 'MACHINING_CENTER':
            filters.update({"relation.machining__in": branch_ids})
        if out_position:
            filters.update({"out_whs": str(out_position)})
        if in_position:
            filters.update({"in_whs": str(in_position)})
        if material_ids:
            filters.update({"material_id__in": material_ids})
        struct = Struct()
        struct.update(filters)
        filters = struct
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="POSITION-TRANSFER-CONFIG",
                filters=filters,
                relation=relation,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            if entity and isinstance(entity, dict):
                rows = entity.get('rows', [])
                if rows:
                    for row in rows:
                        fields = row.get('fields', {})
                        result.append(fields)
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    def get_position_material_config(self, return_fields=None, branch_ids=None, branch_type=None,
                                     material_rules=None, limit=-1, offset=0, sort=None,
                                     include_total=True, relation=None, relation_filters=None,
                                     partner_id=None, user_id=None):
        """查询门店/仓库/加工中心运营下-物料转换配置列表
        material_rules -> 物料转换规则id列表
        branch_type: -> WAREHOUSE/MACHINING_CENTER/STORE
        branch_ids: 门店/仓库/加工中心id列表 -> [str...]
        filters: {"ruleSwitch": True}
        :return [
                {"checked": [],
                "material_id": , 物料规则id
                "ruleRange": "store"/"whs",规则生效范围门店/仓位
                },{}...
                ]
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if not all([branch_type, branch_ids]):
            raise DataValidationException("请传入筛选条件branch_type & branch_ids")

        filters = {}
        if material_rules:
            filters.update({"material_id__in": material_rules})
        if branch_type == 'STORE':
            filters.update({"relation.stores__in": branch_ids})
        if branch_type == 'WAREHOUSE':
            filters.update({"relation.warehouse__in": branch_ids})
        if branch_type == 'MACHINING_CENTER':
            filters.update({"relation.machining__in": branch_ids})
        struct = Struct()
        struct.update(filters)
        filters = struct
        print("filters", filters)
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="POSITION-MATERIAL-CONFIG",
                filters=filters,
                relation=relation,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            if entity and isinstance(entity, dict):
                result = entity.get('rows', [])
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    @get_struct
    def get_material_convert_rules(self, filters=None, return_fields=None, ids=None, limit=-1, offset=0,
                                   sort=None, include_total=True, relation=None, relation_filters=None,
                                   partner_id=None, user_id=None):
        """查询总部管理-运营设置-物料转换规则列表
        filters: type: "manual"/"automatic" 区分自动/手动
        :return [...]
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="MATERIAL-TRANSFOR",
                filters=filters,
                relation=relation,
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                offset=offset,
                sort=sort,
                include_total=include_total
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            if entity and isinstance(entity, dict):
                result = entity.get('rows', [])
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    # 校验门店开店状态/ 启用状态 【ID1001973】
    def check_store_status(self, store_id, partner_id, user_id, valid_open_status=None, valid_status=None):
        if not valid_open_status:
            valid_open_status = ['PRE_OPEN', 'OPENED']
        filters = {"open_status__in": valid_open_status}

        if valid_status:
            filters["status__in"] = valid_status

        store_list = self.get_store_list(ids=[store_id], filters=filters, return_fields='id',
                                         partner_id=partner_id, user_id=user_id).get('rows', [])
        if store_list:
            return True

    # 获取商品属性区域
    def get_attribute_info_by_product_id(self, product_id, schema_relation_name, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = relation_pb2.ListRelationRequest(
                source_schema_name="product",
                schema_relation_name=schema_relation_name,
                source_entity_ids=[product_id]
            )
            ret = self.relationStub.ListEntityRelation(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            result = []
            if entity and isinstance(entity, dict):
                result = entity.get('rows', [])
        except Exception as e:
            raise exception_from_str(str(e))
        return result

    #
    def get_delivery_name_dict(self, partner_id, user_id, logistics_type=None, branch_id=None):
        if logistics_type:
            if logistics_type == 'NMD':
                branch_detail = self.get_distribution_center(center_id=branch_id,
                                                             return_fields="id,code,name",
                                                             partner_id=partner_id,
                                                             user_id=user_id)
            if logistics_type == 'PUR':
                branch_detail = self.get_vendor_center(center_id=branch_id,
                                                       return_fields="id,code,name",
                                                       partner_id=partner_id,
                                                       user_id=user_id)
            if logistics_type == 'PAD':
                branch_detail = self.get_machining_center_by_id(_id=branch_id,
                                                                return_fields="id,code,name",
                                                                partner_id=partner_id,
                                                                user_id=user_id)
            return branch_detail.get('name')

        warehouse_list = metadata_service.get_distribution_center_list(
            return_fields="id,code,name",
            partner_id=partner_id,
            user_id=user_id).get("rows", [])
        vendor_list = metadata_service.list_entity(schema_name='vendor',
                                                   return_fields="id,code,name",
                                                   partner_id=partner_id, user_id=user_id).get('rows', [])

        machining_center_list = metadata_service.list_entity(schema_name='machining-center',
                                                             return_fields="id,code,name",
                                                             partner_id=partner_id, user_id=user_id).get('rows', [])

        branch_list = warehouse_list + vendor_list + machining_center_list
        branch_map = {}
        for branch in branch_list:
            branch_map[int(branch.get('id', 0))] = branch.get('name') if branch.get('name') else branch.get('fields',
                                                                                                            {}).get(
                'name')
        return branch_map

    @result_wraps
    @get_struct
    def list_region_product_by_store(self, store_id=None, return_fields=None, product_ids=None,
                                     include_product_fields=None, include_product_units=None, limit=None, offset=None,
                                     sort=None, order=None, include_total=False, filters=None, product_filters=None,
                                     product_search=None, product_search_fields=None, can_bom=False, can_order=False,
                                     can_purchase=False, can_stocktake=False, can_sales=False, check_division=False,
                                     product_relation_filters=None, region=None, lan=None, partner_id=None,
                                     user_id=None):
        """拉门店关联区域商品通用接口
        region: 区域schema name"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.relationStub.ListRegionProductByStore(
                relation_pb2.ListRegionProductByStoreRequest(store_id=store_id,
                                                             return_fields=return_fields,
                                                             product_ids=product_ids,
                                                             include_product_fields=include_product_fields,
                                                             include_product_units=include_product_units,
                                                             filters=filters,
                                                             product_filters=product_filters,
                                                             limit=limit,
                                                             offset=offset,
                                                             sort=sort,
                                                             order=order,
                                                             product_search=product_search,
                                                             product_search_fields=product_search_fields,
                                                             include_total=include_total,
                                                             can_bom=can_bom,
                                                             can_order=can_order,
                                                             can_purchase=can_purchase,
                                                             can_stocktake=can_stocktake,
                                                             can_sales=can_sales,
                                                             product_relation_filters=product_relation_filters,
                                                             check_division=check_division,
                                                             region=region,
                                                             lan=lan
                                                             ), timeout=TIMEOUT, metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        entity = MessageToDict(ret, preserving_proto_field_name=True)
        return entity

    # 获取业务配置
    def get_business_extra_config(self, partner_id, domain, user_id=None):
        # "extra_config": {
        #                 "allow_neg_inv": true // 允许负库存
        #             }
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            auto_configs = {}
            req = entity_pb2.ListEntityRequest(
                schema_name=domain_map.get(domain),
                limit=-1
            )
            entity = self.entityStub.ListEntity(req, metadata=metadata)
            # 公共租户&对应租户
            entity = MessageToDict(entity, preserving_proto_field_name=True).get('rows')
            if entity:
                entity = entity[0]
                auto_configs = entity.get("fields", {}).get("extra_config", {})
        except Exception as e:
            raise exception_from_str(str(e))
        return auto_configs

    # 获取业务配置
    def get_supply_config(self, partner_id, domain, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            supply_config = {}
            req = entity_pb2.ListEntityRequest(
                schema_name=domain_map.get(domain),
                limit=-1
            )
            entity = self.entityStub.ListEntity(req, metadata=metadata)
            entity = MessageToDict(entity, preserving_proto_field_name=True).get('rows')
            if entity:
                entity = entity[0]
                supply_config = entity.get("fields", {})
        except Exception as e:
            raise exception_from_str(str(e))
        return supply_config

    # get_category_parent 获取 category_id 对应的所有父级
    def get_category_parent(self, partner_id, user_id, offset=0, limit=-1):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.productStub.ListProductCategory(
                product_pb2.ListProductCategoryRequest(limit=limit, offset=offset), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            category_rows = entity.get('rows', [])
            category_name_map = {}
            for category in category_rows:
                category_name_map[int(category["id"])] = category.get("parent_id", 0), category["name"]

            category_parent_map = {}
            for _category_id in category_name_map:
                parent_ids = [_category_id]
                parent_id = int(category_name_map[_category_id][0])
                while parent_id != 0:
                    parent_ids.append(parent_id)
                    parent_id = int(category_name_map[parent_id][0])
                parent_ids.reverse()
                category_parent_map[_category_id] = parent_ids

            category_parent_name_map = {}
            for _category_id in category_parent_map:
                _category_names = []
                for _parent_id in category_parent_map[_category_id]:
                    _category_names.append(category_name_map[_parent_id][1])
                category_parent_name_map[_category_id] = _category_names
            return category_parent_name_map
        except Exception as e:
            raise exception_from_str(str(e))
        return {}

    def get_time_config(self, partner_id, user_id, domain, offset=0, limit=-1, store_id=None):
        """获取时间配置"""
        try:
            metadata = (
                ("partner_id", str(partner_id)),
                ("user_id", str(user_id))
            )
            req = entity_pb2.ListEntityRequest(
                schema_name=domain_map.get(domain),
                limit=-1
            )
            entity = self.entityStub.ListEntity(req, metadata=metadata)
            entity = MessageToDict(entity, preserving_proto_field_name=True).get('rows', [])
            time_config_map = {}
            allow_specified_time = False

            # 查询门店主档
            store_req =entity_pb2.ListEntityRequest(
                schema_name="STORE",
                limit=1,
                ids=[int(store_id)],
                return_fields="tz"
            )
            store_entity = self.entityStub.ListEntity(store_req, metadata=metadata)
            store = MessageToDict(store_entity, preserving_proto_field_name=True).get('rows', [{}])[0].get("fields", {})
            logger.info("===============store: {}".format(store))
            tz=store.get("tz", "+08:00")
            if entity:
                entity = entity[-1]
                time_config = entity.get("fields", {}).get("time_config", {})
                allow_specified_time = time_config.get("allow_specified_time", False)
                branch_method = time_config.get("branch_method")
                store_ids = time_config.get("store_ids", [])
                branch_ids = time_config.get("branch_ids", [])
                branch_type = time_config.get("branch_type", '')
                if branch_ids:
                    relation_filters = {}
                    if branch_type == "branch_region":
                        relation_filters = {"branch_region": [str(b) for b in branch_ids]}
                    if branch_type == "geo_region":
                        relation_filters = {"geo_region": [str(b) for b in branch_ids]}
                    if branch_type == "franchisee_region":
                        relation_filters = {"franchisee_region": [str(b) for b in branch_ids]}
                    stores = self.get_store_list(relation_filters=relation_filters,
                                                 partner_id=partner_id,
                                                 return_fields='id',
                                                 user_id=user_id).get("rows", [])
                    for s in stores:
                        store_ids.append(s.get("id"))
                validity_time = time_config.get("validity_time", [])
                if validity_time:
                    #validity_time 存的utc 小时数
                    t1 = datetime.datetime.strptime(validity_time[0], "%Y-%m-%dT%H:%M:%S.000Z").strftime("%H:%M:%S")
                    t2 = datetime.datetime.strptime(validity_time[1], "%Y-%m-%dT%H:%M:%S.000Z").strftime("%H:%M:%S")
                    tz_offset = float(tz.split(":")[0])
                    utc_now = datetime.datetime.now(datetime.timezone.utc)
                    local_date = (utc_now + datetime.timedelta(hours=tz_offset)).date()

                    local_dt1 = datetime.datetime.combine(local_date, datetime.datetime.strptime(t1, "%H:%M:%S").time())
                    local_dt1 = local_dt1.replace(tzinfo=datetime.timezone(datetime.timedelta(hours=tz_offset)))

                    local_dt2 = datetime.datetime.combine(local_date, datetime.datetime.strptime(t2, "%H:%M:%S").time())
                    local_dt2 = local_dt2.replace(tzinfo=datetime.timezone(datetime.timedelta(hours=tz_offset)))

                    d1 = local_dt1.astimezone(datetime.timezone.utc)
                    d2 = local_dt2.astimezone(datetime.timezone.utc)
                    # if t1 > t2:
                    #     d1 = (datetime.datetime.now() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d") + " " + t1
                    #     d2 = datetime.datetime.now().strftime("%Y-%m-%d") + " " + t2
                    # 自定义门店范围, 否则就是全门店生效
                    if branch_method == 'custom_store':
                        if str(store_id) in store_ids:
                            time_config_map[store_id] = [d1, d2]
                    else:
                        time_config_map[store_id] = [d1, d2]
            return time_config_map, allow_specified_time
        except Exception as e:
            raise exception_from_str(str(e))

    def get_stockTake_config(self, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        stock_config = {}
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="STOCKTAKE_SWITCH",
                limit=-1
            )
            entity = self.entityStub.ListEntity(req, metadata=metadata)
            # 公共租户&对应租户
            entity = MessageToDict(entity, preserving_proto_field_name=True).get('rows')
            if entity:
                entity = entity[-1]
                stock_config[int(entity.get("fields", {}).get("brand_id"))] = entity.get("fields", {}).get("is_open",
                                                                                                           False)
                return stock_config
        except Exception as e:
            raise exception_from_str(str(e))
        finally:
            return stock_config

    @get_struct
    def get_transfer_product_list(self, filters=None, return_fields=None, ids=None, relation_filters=None, search=None,
                                  limit=-1,
                                  search_fields=None, include_units=False, include_total=False, partner_id=None,
                                  user_id=None):
        """查询加盟商列表"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.ListEntityRequest(
                schema_name="PRODUCT",
                filters=filters,
                relation="all",
                ids=ids,
                return_fields=return_fields,
                relation_filters=relation_filters,
                limit=limit,
                include_total=include_total,
                search=search,
                search_fields=search_fields,
            )
            ret = self.entityStub.ListEntity(req, timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    def get_neg_inv_config(self, partner_id, user_id, domain, offset=0, limit=-1, store_id=None):
        """获取业务设置单据负库存
           返回结果为一个字典，key为操作类型（submit。approve），value为是否需要校验负库存的结果
        """

        try:
            metadata = (
                ("partner_id", str(partner_id)),
                ("user_id", str(user_id))
            )
            req = entity_pb2.ListEntityRequest(
                schema_name=domain_map.get(domain),
                limit=-1
            )
            entity = self.entityStub.ListEntity(req, metadata=metadata)
            entity = MessageToDict(entity, preserving_proto_field_name=True).get('rows', [])
            need_check_map = {}
            need_check_submit = False
            need_check_approve = False
            if entity:
                entity = entity[-1]
                submit_config = entity.get("fields", {}).get("submit_config", {})

                # 提交时校验负库存
                allow_neg_inv_s = submit_config.get("allow_neg_inv", False)
                if allow_neg_inv_s:
                    branch_method_s = submit_config.get("branch_method", "")
                    store_ids_s = submit_config.get("store_ids", [])
                    branch_ids_s = submit_config.get("branch_ids", [])
                    branch_type_s = submit_config.get("branch_type", '')

                    # 自定义门店范围, 否则就是全门店生效
                    if branch_method_s == 'custom_store':
                        if branch_ids_s:
                            relation_filters = {}
                            if branch_type_s == "branch_region":
                                relation_filters = {"branch_region": [str(b) for b in branch_ids_s]}
                            if branch_type_s == "geo_region":
                                relation_filters = {"geo_region": [str(b) for b in branch_ids_s]}
                            if branch_type_s == "franchisee_region":
                                relation_filters = {"franchisee_region": [str(b) for b in branch_ids_s]}
                            stores = self.get_store_list(relation_filters=relation_filters,
                                                         partner_id=partner_id,
                                                         return_fields='id',
                                                         user_id=user_id).get("rows", [])
                            for s in stores:
                                store_ids_s.append(s.get("id"))
                        if str(store_id) in store_ids_s:
                            need_check_submit = True
                    else:
                        # 全门店生效
                        need_check_submit = True
                need_check_map["submit"] = need_check_submit

                # 审核时校验负库存
                approve_config = entity.get("fields", {}).get("approve_config", {})
                allow_neg_inv_a = approve_config.get("allow_neg_inv", False)

                if allow_neg_inv_a:
                    branch_method_a = approve_config.get("branch_method", "")
                    store_ids_a = approve_config.get("store_ids", [])
                    branch_ids_a = approve_config.get("branch_ids", [])
                    branch_type_a = approve_config.get("branch_type", '')

                    # 自定义门店范围, 否则就是全门店生效
                    if branch_method_a == 'custom_store':
                        if branch_ids_a:
                            relation_filters = {}
                            if branch_type_a == "branch_region":
                                relation_filters = {"branch_region": [str(b) for b in branch_ids_a]}
                            if branch_type_a == "geo_region":
                                relation_filters = {"geo_region": [str(b) for b in branch_ids_a]}
                            if branch_type_a == "franchisee_region":
                                relation_filters = {"franchisee_region": [str(b) for b in branch_ids_a]}
                            stores = self.get_store_list(relation_filters=relation_filters,
                                                         partner_id=partner_id,
                                                         return_fields='id',
                                                         user_id=user_id).get("rows", [])
                            for s in stores:
                                store_ids_a.append(s.get("id"))
                        if str(store_id) in store_ids_a:
                            need_check_approve = True
                    else:
                        # 全门店生效
                        need_check_approve = True
                need_check_map["approve"] = need_check_approve
            return need_check_map

        except Exception as e:
            raise exception_from_str(str(e))

    @get_struct
    def batch_update_entity(self, entities, partner_id, user_id):
        """
        批量更新主档
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            req = entity_pb2.UpdateEntityBatchRequest(
                entitys=entities
            )
            ret = self.entityStub.UpdateEntityBatch(req, timeout=TIMEOUT, metadata=metadata)
            res = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return res


metadata_service = MetaDataService()


def convert_to_float(value, default=0.0):
    try:
        return float(value)
    except Exception as e:
        return default


def convert_to_int(value, default=0):
    try:
        return int(value)
    except Exception as e:
        return default


def entity_date_time_to_python(time_str):
    if time_str:
        time_str = time_str.replace('Z', '').replace('+08:00', '')
        return datetime.datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%S')
    return None
