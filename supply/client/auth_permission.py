from auth_python.auth.auth import Auth
from supply import APP_CONFIG, logger
from google.protobuf.json_format import MessageToDict
from hex_exception import exception_from_str


_AUTH_ADDRESS = str(APP_CONFIG.get('auth_address'))


class AuthPermissionClient(object):
    def __init__(self):
        self.auth_client = Auth(_AUTH_ADDRESS)
        self.schema_mapping = dict(
            branch_region="BRANCH_REGION",          # 管理区域
            warehouse="DISTRCENTER",                # 仓库
            manufactory="MACHINING_CENTER",         # 加工中心
            store="STORE",                          # 门店
            company_info="COMPANY_INFO",            # 公司
            vendor="VENDOR",                        # 供应商
            geo_region="GEO_REGION",                # 地理区域
            stocktake_type="STOCKTAKE_TYPE",        # 盘点单类型
            franchisee_region="FRANCHISEE_REGION"   # 加盟商区域
        )

    def auth(self, subject_type=None, action=None, resource_type='', resource_id=None, partner_id=None,
             user_id=None, domain=None):
        """
        权限验证
        :subject_id: 访问对象id
        :param subject_type: 访问对象类型, 普通用户为user_id
        :param action: 请求action_code
        :param resource_type: 资源对象类型, 即schema, 如门店为STORE
        :param resource_id: 资源对象id
        :param partner_id: 租户id
        :param user_id
        :param domain: 场景
        :return: e.g. {"status": {"code": 1021, "message": "CHECK_AUTH_ERR"}}, code在./codes/codes.proto中定义, 非0即为失败
        """

        if not subject_type:
            subject_type = "user_id"
        try:
            res = self.auth_client.auth(subject_type=subject_type, subject_id=user_id, action=action,
                                        resource_type=resource_type, resource_id=resource_id, partner_id=partner_id,
                                        domain=domain)
            res = MessageToDict(res, preserving_proto_field_name=True)
            status = res.get("status") if res.get("status") else {}
            # logger.info("Request auth status: {}".format(status))
            return status
        except Exception as e:
            raise exception_from_str(str(e))

    def list_data_scope_check(self, partner_id=0, user_id=0, resource_schema='', domain=''):
        if resource_schema not in self.schema_mapping.values():
            resource_schema = self.schema_mapping.get(resource_schema, " ")
        try:
            res = self.auth_client.getSubjectResourceId(subject_id=user_id, partner_id=partner_id,
                                                        resource_schema=resource_schema, domain=domain)
            res = MessageToDict(res, preserving_proto_field_name=True)
            # logger.info("Request data scope res: {}".format(res))
            return res
        except Exception as e:
            raise exception_from_str(str(e))


auth_permission = AuthPermissionClient()
