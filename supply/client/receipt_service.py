import grpc
import datetime
import logging
from google.protobuf.json_format import MessageToDict
from google.protobuf.timestamp_pb2 import Timestamp
from supply.proto.receipt import order_pb2 as order_pb2
from supply.proto.receipt.franchisee import order_pb2 as frs_order_pb2
from supply.proto.receipt.order_pb2_grpc import OrderServiceStub
from supply.proto.receipt.franchisee.order_pb2_grpc import FranchiseeOrderServiceStub
from supply.proto.receipt import receive_pb2 as receive_pb2
from supply.proto.receipt.receive_pb2_grpc import ReceiveServiceStub
from supply.proto.receipt import demand_pb2 as demand_pb2
from supply.proto.receipt.demand_pb2_grpc import DemandServiceStub
from supply.proto.receipt import delivery_pb2 as delivery_pb2
from supply.proto.receipt.delivery_pb2_grpc import DeliveryServiceStub
from supply.proto.receipt import operation_log_pb2
from supply.proto.receipt.operation_log_pb2_grpc import OperationLogServiceStub
from supply.proto.receipt.franchisee.delivery_pb2_grpc import DeliveryServiceStub as FranchiseeDeliveryServiceStub
from supply.proto.receipt.franchisee.delivery_pb2 import ListFranchiseeDeliverysRequest
from supply.proto.receipt.franchisee.receive_pb2_grpc import FranchiseeReceiveServiceStub
from supply.proto.receipt.franchisee.receive_pb2 import ListFranchiseeReceivesRequest

from supply import APP_CONFIG, TIMEOUT
from . import build_grpc_channel_options
from ..error.exception import *
from hex_exception import exception_from_str
from supply import logger, time_cost
import traceback

from ..utils.kit import Kit, KitEnum

_HOST = str(APP_CONFIG['receipt_host'])
_PORT = str(APP_CONFIG['receipt_port'])
# _HOST = '127.0.0.1'
# _PORT = '8686'
_CHANNEL_OPTIONS = build_grpc_channel_options(package_services=[
    ("delivery", "DeliveryService"),
    ("demand", "DemandService"),
    ("operation_log", "OperationLogService"),
    ("order", "OrderService"),
    ("receive", "ReceiveService")

])
_CHANNEL = grpc.insecure_channel(_HOST + ':' + _PORT, options=_CHANNEL_OPTIONS)


class Receiptservice(object):
    """
    所有请求的patner_id和user_id在get_struct会从全局导入，或者自己单独传
    所有主档信息多物件强制用list方法，避免多次调用影响api响应速度
    """
    TIMEOUT = 60 * 2

    def __init__(self):
        self.orderStub = OrderServiceStub(_CHANNEL)
        self.receiveStub = ReceiveServiceStub(_CHANNEL)
        self.demandStub = DemandServiceStub(_CHANNEL)
        self.deliveryStub = DeliveryServiceStub(_CHANNEL)
        self.operationStub = OperationLogServiceStub(_CHANNEL)
        self.frsOrderStub = FranchiseeOrderServiceStub(_CHANNEL)
        self.franchiseeDeliveryStub = FranchiseeDeliveryServiceStub(_CHANNEL)
        self.franchiseeReceiveStub = FranchiseeReceiveServiceStub(_CHANNEL)

    ### --- Receipt.Order --- ###
    # 创建订货单
    def create_orders(self, batch_id=None, batch_code=None, batch_type=None, order_id=None, order_code=None,
                      main_branch_type=None, delivery_id=None, delivery_code=None, receive_by=None, delivery_by=None,
                      storage_type=None, distr_type=None, demand_type=None, demand_date=None, delivery_date=None,
                      expect_date=None, reason=None, remark=None, products=None, partner_id=None, user_id=None,
                      separate_no=None, franchisee_id=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            
            if isinstance(demand_date, datetime.datetime):
                demand_date = Timestamp(seconds=int(demand_date.timestamp()))
            if isinstance(delivery_date, datetime.datetime):
                delivery_date = Timestamp(seconds=int(delivery_date.timestamp()))
            if isinstance(expect_date, datetime.datetime):
                expect_date = Timestamp(seconds=int(expect_date.timestamp()))
            ret = self.orderStub.CreateOrder(
                order_pb2.CreateOrderRequest(batch_id=batch_id, batch_code=batch_code, batch_type=batch_type,
                                             id=order_id, code=order_code, main_branch_type=main_branch_type,
                                             delivery_id=delivery_id, delivery_code=delivery_code,
                                             receive_by=receive_by, reason=reason, expect_date=expect_date,
                                             delivery_by=delivery_by, storage_type=storage_type,
                                             distr_type=distr_type, demand_type=demand_type,
                                             demand_date=demand_date, delivery_date=delivery_date,
                                             products=products, separate_no=separate_no,
                                             remark=remark,
                                             source_remark=remark),
                timeout=200, metadata=metadata)
            
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 更新订货单
    def update_order(self, batch_id=None, remark=None, partner_id=None, user_id=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.orderStub.UpdateOrder(
                order_pb2.UpdateOrderRequest(batch_id=batch_id, source_remark=remark),
                timeout=200, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    def create_franchisee_order(self, batch_id=None, batch_code=None, batch_type=None, order_id=None, order_code=None,
                                main_branch_type=None, delivery_id=None, delivery_code=None, receive_by=None,
                                delivery_by=None, storage_type=None, distr_type=None, demand_type=None, remark=None,
                                delivery_date=None, expect_date=None, reason=None, demand_date=None, products=None,
                                partner_id=None, user_id=None, separate_no=None, franchisee_id=None,
                                order_type_id=None, receive_name=None, bus_type=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(demand_date, datetime.datetime):
                demand_date = Timestamp(seconds=int(demand_date.timestamp()))
            if isinstance(delivery_date, datetime.datetime):
                delivery_date = Timestamp(seconds=int(delivery_date.timestamp()))
            if isinstance(expect_date, datetime.datetime):
                expect_date = Timestamp(seconds=int(expect_date.timestamp()))
            ret = self.frsOrderStub.CreateOrder(
                frs_order_pb2.CreateOrderRequest(batch_id=batch_id, batch_code=batch_code, batch_type=batch_type,
                                                 id=order_id, code=order_code, main_branch_type=main_branch_type,
                                                 delivery_id=delivery_id, delivery_code=delivery_code,
                                                 receive_by=receive_by, reason=reason, expect_date=expect_date,
                                                 delivery_by=delivery_by, storage_type=storage_type,
                                                 distr_type=distr_type, demand_type=demand_type,
                                                 demand_date=demand_date, delivery_date=delivery_date,
                                                 products=products, separate_no=separate_no, remark=remark,
                                                 franchisee_id=franchisee_id, order_type_id=order_type_id,
                                                 bus_type=bus_type),
                timeout=200, metadata=metadata)

            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            Kit.upload(partner_id, batch_code, KitEnum.CREATE_FRS_ORDER.code, KitEnum.CREATE_FRS_ORDER.description,
                       "加盟订货单拆单失败："+str(e), False, receive_by, receive_name)
            raise exception_from_str(str(e))

        Kit.upload(partner_id, batch_code, KitEnum.CREATE_FRS_ORDER.code, KitEnum.CREATE_FRS_ORDER.description,
                   storeId=receive_by, storeName=receive_name,content="加盟订货单拆单成功")
        return entity

    # 查询订货单
    def list_orders(self, start_arrival_date=None, end_arrival_date=None, start_date=None, end_date=None,
                    store_ids=None, batch_id=None, demand_code=None, order_code=None, demand_type=None,
                    storage_type=None, store_type=None, trans_mode=None, offset=None, limit=None, is_received=None,
                    jde_order_id=None, distribution_type=None, distribute_bys=None, is_adjust=None,
                    havi_code=None, order=None, sort=None, partner_id=None, user_id=None, status=None,
                    main_branch_type=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:

            if is_adjust == True:
                batch_type = ["DEMAND_ADJUST", "CHECKING_ADJUST"]
            else:
                batch_type = ["DEMAND_ADJUST", "CHECKING_ADJUST", "DEMAND", "FRS_DEMAND"]
            if not main_branch_type:
                main_branch_type = "S"

            if isinstance(start_arrival_date, datetime.datetime):
                start_arrival_date = Timestamp(seconds=int(start_arrival_date.timestamp()))
            if isinstance(end_arrival_date, datetime.datetime):
                end_arrival_date = Timestamp(seconds=int(end_arrival_date.timestamp()))
            if isinstance(start_date, datetime.datetime):
                start_date = Timestamp(seconds=int(start_date.timestamp()))
            if isinstance(end_date, datetime.datetime):
                end_date = Timestamp(seconds=int(end_date.timestamp()))
            if demand_type:
                for i in demand_type:
                    demand_type = i
            if demand_type == []:
                demand_type = None
            if storage_type:
                for i in storage_type:
                    storage_type = i
            if storage_type == []:
                storage_type = None
            ret = self.orderStub.ListOrder(order_pb2.ListOrderRequest(
                arrival_start_date=start_arrival_date, arrival_end_date=end_arrival_date,
                demand_start_date=start_date, demand_end_date=end_date, receive_bys=store_ids,
                batch_id=batch_id, batch_code=demand_code,
                batch_type=batch_type, main_branch_type=main_branch_type,
                code=order_code,
                demand_type=demand_type, storage_type=storage_type, distr_type=distribution_type,
                status=status, offset=offset, limit=limit, delivery_bys=distribute_bys,
                order=order, sort=sort
            ), timeout=200, metadata=metadata)
            # entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 查询仓库要货单
    def list_receipt_demand(self, partner_id=None, user_id=None, start_date=None, end_date=None, store_ids=None,
                            batch_id=None, batch_code=None, code=None, batch_type=None, offset=None, limit=None,
                            distribution_type=None, distribute_bys=None, order=None, sort=None, status=None,
                            main_branch_type=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime.datetime):
                start_date = Timestamp(seconds=int(start_date.timestamp()))
            if isinstance(end_date, datetime.datetime):
                end_date = Timestamp(seconds=int(end_date.timestamp()))
            ret = self.demandStub.ListDemand(demand_pb2.ListDemandRequest(
                start_date=start_date, end_date=end_date, receive_bys=store_ids,
                batch_id=batch_id, batch_code=batch_code,
                batch_type=batch_type, main_branch_type=main_branch_type,
                code=code, distr_type=distribution_type,
                status=status, offset=offset, limit=limit, delivery_bys=distribute_bys,
                order=order, sort=sort), timeout=200, metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret


    # 查询订货单byId
    def get_orders_by_id(self, order_id, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.orderStub.GetOrderById(order_pb2.GetOrderByIdRequest(
                id=order_id
            ), metadata=metadata)

        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 枚举订货单商品
    def get_order_products_by_order_id(self, order_id, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )

        try:
            ret = self.orderStub.GetOrderProductById(order_pb2.GetOrderProductByIdRequest(
                id=order_id
            ), metadata=metadata)
            # print('&&&&&&&&', ret)
            
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 查询订货单
    def list_orders_by_batch_ids(self, batch_ids, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.orderStub.ListOrderByBatchIds(order_pb2.ListOrderByBatchIdsRequest(
                            batch_ids=batch_ids
                            ), timeout=200, metadata=metadata)
            # entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    ### --- Receipt.Receive --- ###            
    # 创建收货单
    def create_receives(self, batch_id=None, batch_code=None, batch_type=None, id=None, code=None, 
            order_id=None, order_code=None, delivery_id=None, delivery_code=None, receive_by=None, 
            delivery_by=None, storage_type=None, distr_type=None, main_branch_type=None,
            demand_date=None, delivery_date=None, expect_date=None, products=None, 
            partner_id=None, user_id=None, remark=None, reason=None, demand_type=None, franchisee_id=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(demand_date, datetime.datetime):
                demand_date = Timestamp(seconds=int(demand_date.timestamp()))
            if isinstance(delivery_date, datetime.datetime):
                delivery_date = Timestamp(seconds=int(delivery_date.timestamp()))
            if isinstance(expect_date, datetime.datetime):
                expect_date = Timestamp(seconds=int(expect_date.timestamp()))
            ret = self.receiveStub.CreateReceive(
                                receive_pb2.CreateReceiveRequest(
                                            demand_type=demand_type,
                                            batch_id=batch_id, batch_code=batch_code, batch_type=batch_type, 
                                            id=id, code=code, order_id=order_id, order_code=order_code, 
                                            delivery_id=delivery_id, delivery_code=delivery_code, 
                                            receive_by=receive_by, delivery_by=delivery_by, 
                                            storage_type=storage_type, distr_type=distr_type, main_branch_type=main_branch_type,
                                            demand_date=demand_date, delivery_date=delivery_date, expect_date=expect_date, 
                                            remark=remark, reason=reason,
                                            products=products, franchisee_id=franchisee_id), timeout=200, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity
    
    # 查询收货单
    def list_receives(self, code=None, order_code=None, receive_bys=None, batch_id=None, order_id=None, delivery_id=None,
                      delivery_bys=None, start_date=None, end_date=None, status=None, distr_type=None, 
                      batch_type=None, main_branch_type=None, batch_ids=None,
                      limit=None, offset=None, sort=None, order=None, partner_id=None, user_id=None,
                      delivery_start_date=None, delivery_end_date=None, expect_start_date=None, expect_end_date=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime.datetime):
                start_date = Timestamp(seconds=int(start_date.timestamp()))
            if isinstance(end_date, datetime.datetime):
                end_date = Timestamp(seconds=int(end_date.timestamp()))
            if isinstance(delivery_start_date, datetime.datetime):
                delivery_start_date = Timestamp(seconds=int(delivery_start_date.timestamp()))
            if isinstance(delivery_end_date, datetime.datetime):
                delivery_end_date = Timestamp(seconds=int(delivery_end_date.timestamp()))
            if isinstance(expect_start_date, datetime.datetime):
                expect_start_date = Timestamp(seconds=int(expect_start_date.timestamp()))
            if isinstance(expect_end_date, datetime.datetime):
                expect_end_date = Timestamp(seconds=int(expect_end_date.timestamp()))
            
            ret = self.receiveStub.ListReceive(
                receive_pb2.ListReceiveRequest(
                    code=code, order_id=order_id, order_code=order_code,
                    receive_bys=receive_bys, batch_type=batch_type,
                    main_branch_type=main_branch_type,
                    batch_id=batch_id, delivery_bys=delivery_bys,
                    start_date=start_date, end_date=end_date,
                    status=status, distr_type=distr_type,
                    limit=limit, offset=offset, sort=sort, order=order,
                    delivery_id=delivery_id, batch_ids=batch_ids,
                    delivery_start_date=delivery_start_date, 
                    delivery_end_date=delivery_end_date,
                    expect_start_date=expect_start_date,
                    expect_end_date=expect_end_date),
                timeout=200, metadata=metadata)
            # entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 查询收货单byId
    def get_receive_by_id(self, receive_id, partner_id, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.receiveStub.GetReceiveById(receive_pb2.GetReceiveByIdRequest(
                id=receive_id
            ), metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 枚举收货单商品byId
    def get_receive_products_by_receive_id(self, receive_id, limit=None, 
                                                offset=None, sort=None, order=None, 
                                                partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.receiveStub.GetReceiveProductById(receive_pb2.GetReceiveProductByIdRequest(
                id=receive_id
            ), metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 枚举收货单商品byCode
    def get_receive_products_by_receive_code(self, receive_code, 
                                                partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.receiveStub.GetReceiveProductByCode(receive_pb2.GetReceiveProductByCodeRequest(
                code=receive_code
            ), metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 处理收货单
    def deal_receive_by_id(self, receive_id, action, deal_products=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.receiveStub.DealReceiveById(receive_pb2.DealReceiveByIdRequest(
                id=receive_id, 
                action=action,
                products=deal_products
            ), metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 更新收货单商品
    def update_receives_products(self, receive_id=None, batch_id=None, order_id=None, 
                                    deal_products=None, partner_id=None, user_id=None,
                                    remark=None, reason=None, distr_type=None, 
                                    receive_by=None, delivery_by=None, expect_date=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.receiveStub.UpdateReceiveProducts(receive_pb2.UpdateReceiveProductsRequest(
                id=receive_id, 
                batch_id=batch_id,
                order_id=order_id,
                products=deal_products,
                remark=remark,
                reason=reason,
                distr_type=distr_type,
                receive_by=receive_by,
                delivery_by=delivery_by,
                expect_date=expect_date
            ), metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 查询收货单未收货数量
    def get_unifish_receive_products(self, receive_by=None,partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.receiveStub.GetUnfinishReceiveProducts(
                receive_pb2.GetUnfinishReceiveProductsRequest(
                    receive_by=receive_by
            ), metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
            ret = ret.get("unfinish_products_map", {})
        except Exception as e:
            raise exception_from_str(str(e))
        return ret


    ### --- Receipt.Delivery --- ###            
    # 创建发货单
    def create_deliverys(self, batch_id=None, batch_code=None, batch_type=None, id=None, code=None, 
            order_id=None, order_code=None, demand_id=None, demand_code=None, 
            receive_id=None, receive_code=None, receive_by=None, 
            delivery_by=None, storage_type=None, distr_type=None, arrival_date=None,
            demand_date=None, delivery_date=None, expect_date=None, products=None, main_branch_type=None,
            partner_id=None, user_id=None, remark=None, reason=None, demand_type=None, franchisee_id=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(demand_date, datetime.datetime):
                demand_date = Timestamp(seconds=int(demand_date.timestamp()))
            if isinstance(delivery_date, datetime.datetime):
                delivery_date = Timestamp(seconds=int(delivery_date.timestamp()))
            if isinstance(expect_date, datetime.datetime):
                expect_date = Timestamp(seconds=int(expect_date.timestamp()))
            if isinstance(arrival_date, datetime.datetime):
                arrival_date = Timestamp(seconds=int(arrival_date.timestamp()))

            ret = self.deliveryStub.CreateDelivery(
                                delivery_pb2.CreateDeliveryRequest(
                                            batch_id=batch_id, batch_code=batch_code, batch_type=batch_type, 
                                            order_id=order_id, order_code=order_code, 
                                            demand_id=demand_id, demand_code=demand_code,
                                            # receive_id=receive_id, receive_code=receive_code, 
                                            receive_by=receive_by, delivery_by=delivery_by, 
                                            main_branch_type=main_branch_type,
                                            storage_type=storage_type, 
                                            distr_type=distr_type,
                                            demand_date=demand_date, delivery_date=delivery_date, 
                                            expect_date=expect_date, arrival_date=arrival_date,
                                            remark=remark, reason=reason,
                                            products=products, demand_type=demand_type,
                                            franchisee_id=franchisee_id), timeout=200, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 查询发货单
    def list_deliverys(self, code=None, order_code=None, receive_bys=None, batch_id=None, order_id=None, delivery_id=None,
                      delivery_bys=None, start_date=None, end_date=None, status=None, distr_type=None, 
                      batch_type=None, main_branch_type=None,
                      limit=None, offset=None, sort=None, order=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime.datetime):
                start_date = Timestamp(seconds=int(start_date.timestamp()))
            if isinstance(end_date, datetime.datetime):
                end_date = Timestamp(seconds=int(end_date.timestamp()))

            ret = self.deliveryStub.ListDelivery(
                delivery_pb2.ListDeliveryRequest(
                    code=code, order_id=order_id, order_code=order_code,
                    receive_bys=receive_bys, batch_type=batch_type,
                    main_branch_type=main_branch_type,
                    batch_id=batch_id, delivery_bys=delivery_bys,
                    start_date=start_date, end_date=end_date,
                    status=status, distr_type=distr_type,
                    limit=limit, offset=offset, sort=sort, order=order),
                timeout=200, metadata=metadata)
            # entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    # 处理发货单
    def deal_delivery_by_id(self, delivery_id, action, deal_products=None, partner_id=None, user_id=None,remark=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.deliveryStub.DealDeliveryById(delivery_pb2.DealDeliveryByIdRequest(
                id=delivery_id, 
                action=action,
                remark = remark,
                # products=deal_products
            ), metadata=metadata)
        except Exception as e:
            if "OS Error" in str(e):
                logging.info("supplyDealDeliveryById OS Error:{}".format(delivery_id))
                self.deal_delivery_by_id(delivery_id, action, deal_products, partner_id, user_id)
            raise exception_from_str(str(e))
        return ret
    
    # 更新发货单商品
    def update_deliverys_products(self, delivery_id=None, batch_id=None, order_id=None, 
                                    deal_products=None, partner_id=None, user_id=None,
                                    remark=None, reason=None, distr_type=None,
                                    receive_by=None, delivery_by=None, expect_date=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.deliveryStub.UpdateDelivery(delivery_pb2.UpdateDeliveryRequest(
                id=delivery_id, 
                batch_id=batch_id,
                order_id=order_id,
                products=deal_products,
                remark=remark, 
                reason=reason,
                distr_type=distr_type,
                receive_by=receive_by,
                delivery_by=delivery_by,
                expect_date=expect_date
            ), metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    def create_flow_logs(self, doc_id=None, doc_type=None, doc_status=None, sub_doc_type=None,
                         branch_id=None, sub_branch_id=None, operation=None, posterior_operation=None,
                         process_status=None, remark=None, user_id=None, partner_id=None):
        """创建工作流记录"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.operationStub.CreateFlowLogs(operation_log_pb2.CreateFlowLogsRequest(
                doc_id=doc_id,
                doc_type=doc_type,
                doc_status=doc_status,
                sub_doc_type=sub_doc_type,
                branch_id=branch_id,
                sub_branch_id=sub_branch_id,
                operation=operation,
                posterior_operation=posterior_operation,
                process_status=process_status,
                remark=remark
            ), metadata=metadata)
        except Exception as e:
            raise exception_from_str(str(e))
        return MessageToDict(ret, preserving_proto_field_name=True)

    def list_franchisee_deliverys(self, batch_ids, partner_id, user_id, delivery_code=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            rows = self.franchiseeDeliveryStub.ListFranchiseeDeliverys(
                ListFranchiseeDeliverysRequest(batch_ids=batch_ids, delivery_code=delivery_code), metadata=metadata, timeout=200
            )
        except Exception as e:
            raise exception_from_str(str(e))
        return MessageToDict(rows, preserving_proto_field_name=True).get("rows", [])

    def list_franchisee_receives(self, batch_ids, partner_id, user_id, receive_code=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            rows = self.franchiseeReceiveStub.ListFranchiseeReceives(
                ListFranchiseeReceivesRequest(batch_ids=batch_ids, receive_code=receive_code), metadata=metadata, timeout=200
            )
        except Exception as e:
            raise exception_from_str(str(e))
        return MessageToDict(rows, preserving_proto_field_name=True).get("rows", [])


receipt_service = Receiptservice()
