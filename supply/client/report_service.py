import traceback
from datetime import datetime, date
import json

import grpc
from google.protobuf.json_format import MessageToDict
from google.protobuf.timestamp_pb2 import Timestamp
from hex_exception import exception_from_str
from collections import defaultdict
from supply import APP_CONFIG, TIMEOUT
from supply import logger
from supply.utils import type_convert
from ..proto.report import operation_report_pb2
from ..proto.report.operation_report_pb2_grpc import OperationReportStub

_HOST = str(APP_CONFIG['report_host'])
_PORT = str(APP_CONFIG['report_port'])
_CHANNEL = grpc.insecure_channel(_HOST + ':' + _PORT)


class ReportService(object):
    """
    所有请求的patner_id和user_id在get_struct会从全局导入，或者自己单独传
    """

    def __init__(self):
        self.reportStub = OperationReportStub(_CHANNEL)

    # 获取电子小票的trace_id
    def get_eticket_trace_id(self, partner_id, user_id, pos_ticket_number,
                             branch_id, product_ids=None, start_date=None, end_date=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            logger.info("pos_ticket_number:{} branch_id: {} product_ids: {}, start_date: {}, end_date: {} ".format(pos_ticket_number, branch_id, product_ids, start_date, end_date))
            ret = self.reportStub.GetEticketTraceIds(
                operation_report_pb2.GetETicketTraceIdsRequest(
                    pos_ticket_number=pos_ticket_number,
                    branch_id=branch_id,
                    product_ids=product_ids,
                    start_date=start_date,
                    end_date=end_date), timeout=200, metadata=metadata)
            logger.info("return: {}".format(ret))
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 获取门店商品销售量
    def get_amount_of_product_sales(self, start_date, end_date, product_ids, store_id, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime):
                start_date = Timestamp(seconds=int(start_date.timestamp()))
            if isinstance(end_date, datetime):
                end_date = Timestamp(seconds=int(end_date.timestamp()))
            ret = self.reportStub.GetProductSalesOffDemand(
                operation_report_pb2.GetProductSalesOffDemandRequest(
                    start_date=start_date,
                    end_date=end_date,
                    product_ids=product_ids,
                    store_id=store_id), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True, including_default_value_fields=True)
        except Exception as e:
            logger.error("获取商品销售量出错get_amount_of_product_sales_error:" + str(traceback.format_exc()))
            raise exception_from_str(str(e))
        return entity.get("products_sales_info", [])

    def get_store_product_sales(
            self,
            partner_id,
            user_id,
            store_id,
            timestamps,
            product_ids=None,
        ):
        """
        按照查找指定商品的指定时间的销售量
        :param partner_id:
        :param user_id:
        :param store_id:
        :param timestamps:
        :param product_ids:
        :return: {
            <product_id>: {
                <timestamp>: <quantity>,
                ...
            },
            ...
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        data = dict(store_id=store_id, timestamps=timestamps)
        if product_ids:
            data['product_ids'] = product_ids
        req = operation_report_pb2.GetProductSalesOffDemandRequest2(**data)

        pb_response = self.reportStub.GetProductSalesOffDemand2(req, metadata=metadata)
        response = MessageToDict(pb_response, preserving_proto_field_name=True)
        ret = defaultdict(dict)

        for sale_info in response.get('sale_infos', []):
            product_id = type_convert.to_int(sale_info.get('product_id', 0))
            timestamp = type_convert.to_int(sale_info.get('timestamp', 0))
            if (not product_id) or (not timestamp):
                continue
            quantity = type_convert.to_int(sale_info.get('quantity', 0))
            ret[product_id][timestamp] = quantity
        return dict(ret)
    
    # 获取销售分解汇总
    def get_sales_explained_summary_report(self, start_date, end_date, region_type, period_group_by, limit, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if isinstance(start_date, datetime):
            timestamp = Timestamp()
            timestamp.FromDatetime(start_date)
            start_date = timestamp
        if isinstance(end_date, datetime):
            timestamp = Timestamp()
            timestamp.FromDatetime(end_date)
            end_date = timestamp
        req = {
            "period_filter": {
                "start_date": start_date,
                "end_date": end_date
            },
            "region_group_by": {
                "region_type": region_type,
                # "region_level": 1
            },
            "period_group_by": period_group_by,
            "limit": limit
        }

        req = operation_report_pb2.GetSalesExplainedSummaryReportRequest(**req)
        ret = self.reportStub.GetSalesExplainedSummaryReport(req, metadata=metadata)
        ret = MessageToDict(ret, preserving_proto_field_name=True)
        return ret

    # 获取销售分解明细
    def get_sales_explained_report(self, start_date, end_date, limit, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if isinstance(start_date, datetime):
            timestamp = Timestamp()
            timestamp.FromDatetime(start_date)
            start_date = timestamp
        if isinstance(end_date, datetime):
            timestamp = Timestamp()
            timestamp.FromDatetime(end_date)
            end_date = timestamp
        req = {
            "start_date": start_date,
            "end_date": end_date,
            "limit": limit,
            "store_id":[4279909887082758145]
        }

        req = operation_report_pb2.GetSalesExplainedReportRequest(**req)
        ret = self.reportStub.GetSalesExplainedReport(req, metadata=metadata)
        ret = MessageToDict(ret, preserving_proto_field_name=True)
        return ret

    




def datetime_to_pb_timestamp(value):
    if isinstance(value, datetime):
        return Timestamp(seconds=value.timestamp())
    if isinstance(value, date):
        _value = datetime(year=value.year, month=value.month,
                          day=value.day, hour=0)
        return Timestamp(seconds=_value.timestamp())
    raise ValueError("value should be datetime or date type")


report_service = ReportService()
