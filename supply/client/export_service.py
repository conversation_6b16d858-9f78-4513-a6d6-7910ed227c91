import grpc
import logging
import json
from google.protobuf.struct_pb2 import Struct
from google.protobuf.json_format import MessageToDict
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, date, timedelta

from hex_exception import UnAuthorized, exception_from_str, InvalidData

from supply import APP_CONFIG
from supply.utils import pb2dict
from supply.utils.snowflake import gen_snowflake_id
from supply.proto.irons import irons_pb2
from supply.proto.irons.irons_pb2_grpc import DownloadServiceStub


TIMEOUT = 60
_ADDR = str(APP_CONFIG["export_address"])
_CHANNEL = grpc.insecure_channel(_ADDR, options=[
    ("grpc.max_send_message_length", 1024 * 1024 * 1024),
    ("grpc.max_receive_message_length", 1024 * 1024 * 1024),
], )


class IronsService(object):
    """
    所有请求的patner_id和user_id在get_struct会从全局导入，或者自己单独传
    """

    def __init__(self):
        self.ironsStub = DownloadServiceStub(_CHANNEL)
    

    # 导出下载文件
    def download_files(self, task_id=None, title=None, data=None, partner_id=None, user_id=None):

        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )

        def generate_stream_message():
            request = irons_pb2.DownloadTaskRequest(
                task_id = task_id,
                title = json.dumps(title).encode()
            )
            yield request
            for data_detail in data:
                request = irons_pb2.DownloadTaskRequest(
                    task_id = task_id,
                    data = json.dumps([data_detail]).encode()
                )
                yield request

        try:
            # 发送data
            ret = self.ironsStub.ProcessDownload(generate_stream_message(), metadata=metadata)
            
        except Exception as e:
            logging.info("请求下载任务错误 {}".format(str(e)))
            return True
        return ret

export_service = IronsService()