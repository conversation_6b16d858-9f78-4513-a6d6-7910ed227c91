import logging
from datetime import date, datetime, timedelta

import grpc
from google.protobuf.json_format import MessageToDict
from google.protobuf.struct_pb2 import Struct
from google.protobuf.timestamp_pb2 import Timestamp

from supply import APP_CONFIG, TIMEOUT
from supply.proto.bom import bg_pb2, explain_pb2
from supply.proto.bom.bg_pb2_grpc import BigDataServiceStub
from supply.proto.bom.explain_pb2_grpc import BomServiceStub
from supply.utils.helper import datetime_to_strdate, get_today_datetime

from .metadata_service import get_struct, result_wraps

_HOST = str(APP_CONFIG['bom_host'])
_PORT = str(APP_CONFIG['bom_port'])
_CHANNEL = grpc.insecure_channel(_HOST + ':' + _PORT)


class Bom_service:

    bomStub = BomServiceStub(_CHANNEL)
    bomDataStub = BigDataServiceStub(_CHANNEL)

    @classmethod
    @result_wraps
    @get_struct
    def get_bom(cls, request_id: int, product_id: int, product_code: str, store_id: int, store_code: str,
                sales_date: str, biz_code: str, biz_no: str, product_qty, options=None, partner_id=None, user_id=None):
        """
        1.request_id : 请求id,唯一
        2.product_id : 商品id
        3.product_code:商品编码
        4.store_id   : 门店id  
        5.store_code : 门店编码
        6.sales_date : 营业日
        7.biz_code   : 业务编号
        8.biz_no     : 业务单号
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = cls.bomStub.Explain(explain_pb2.ProductBomReqeust(request_id=request_id,
                                                                    product_id=product_id, product_code=product_code,
                                                                    store_id=store_id, store_code=store_code,
                                                                    biz_code=biz_code, biz_no=biz_no,
                                                                    product_qty=product_qty, sales_date=sales_date,
                                                                    options=options),
                                      timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            import traceback
            logging.warning('get_bom请求失败:{}'.format(traceback.format_exc()))
            raise e
        return entity

    @classmethod
    @result_wraps
    @get_struct
    def get_product_sale_amount_in_one_week(cls, store_ids, product_ids, branch_ids=None, limit=10, offset=0,
        region_level=None, region_type=None, product_level=None, date_level=None, partner_id=None, user_id=None):
        """
        获取商品一周销售数量
        -----------------
        区域id:branch_ids
        商品id列表:product_ids
        门店id列表:store_ids
        区域等级:region_level
        区域类型:region_type
        商品等级:product_level
        日期等级:date_level
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        start_date = datetime_to_strdate(get_today_datetime(plus_day=-7))
        end_date = datetime_to_strdate(get_today_datetime())
        try:
            ret = cls.bomDataStub.AggregateTransactionBomIn(bg_pb2.AggregateRequest(
                branch_ids=branch_ids, product_ids=product_ids, store_ids=store_ids,
                start_date=start_date, end_date=end_date, region_level=region_level, region_type=region_type,
                product_level=product_level, date_level=date_level, limit=limit, offset=offset
            ), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            import traceback
            logging.warning('get_product_sale_amount_in_one_week请求失败:{}'.format(traceback.format_exc()))
            raise e
        return entity


    @classmethod
    @result_wraps
    @get_struct
    # 批量分解BOM,nsq异步接收补偿轮询
    def get_products_boms(cls, request_id: int, store_id: int, sales_date: str,
                          biz_code: str, biz_no: str, products, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = cls.bomStub.ExplainBatch(explain_pb2.BatchRequest(request_id=request_id, store_id=store_id,
                                                                    sales_date=sales_date, biz_code=biz_code,
                                                                    biz_no=biz_no, products=products,
                                                                    ), timeout=60*6,
                                      metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True,
                                   including_default_value_fields=True)
        except Exception as e:
            import traceback
            logging.warning('get_products_boms请求失败条件:{}'.format(products))
            logging.warning('get_products_boms请求失败:{}'.format(traceback.format_exc()))
            raise e
        return entity


bom_service = Bom_service()
