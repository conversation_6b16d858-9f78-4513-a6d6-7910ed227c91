# -*- coding: utf8 -*-
import grpc
from typing import Dict
import json

from hex_exception import exception_from_str, UnAuthorized
import opentracing
from grpc_opentracing import open_tracing_client_interceptor
from grpc_opentracing.grpcext import intercept_channel

from pathlib import Path
from grpc._channel import _Rendezvous
from grpc import UnaryUnaryMultiCallable
from typing import Callable
from supply import request_info
from supply import TIMEOUT


def build_grpc_channel_options(package_services: list, max_send_message_length: int = 1024 * 1024 * 1024,
                               max_receive_message_length: int = 1024 * 1024 * 1024):
    """
    构建grpc客户端调用配置, 注入重试机制
    :param package_services: pb里定义的package和service列表 [("package": "service"), ...]
    :param max_send_message_length:
    :param max_receive_message_length:
    :return:
    """
    # see https://github.com/grpc/proposal/blob/master/A6-client-retries.md#retry-policy-capabilities
    name = [{sp[0]: f"<{sp[1]}>.<{sp[0]}>"} for sp in package_services if sp]
    GRPC_SERVICE_DEFAULT_CONFIG = {
        "methodConfig": [
            {
                "name": name,   # [{service: "<package>.<service>"}, ...]
                "retryPolicy": {
                    "maxAttempts": 5,
                    "initialBackoff": "0.1s",
                    "maxBackoff": "3s",
                    "backoffMultiplier": 2,
                    "retryableStatusCodes": [
                        "RESOURCE_EXHAUSTED",
                        "UNAVAILABLE",
                        "INTERNAL"
                    ]
                },
            }
        ],
    }
    GRPC_SERVICE_CONFIG = json.dumps(GRPC_SERVICE_DEFAULT_CONFIG)
    GRPC_CHANNEL_OPTIONS = [
        ('grpc.max_send_message_length', max_send_message_length),
        ('grpc.max_receive_message_length', max_receive_message_length),
        ('grpc.service_config', GRPC_SERVICE_CONFIG)
    ]
    return GRPC_CHANNEL_OPTIONS


def client_method_wrapper(func: Callable) -> Callable:
    """
    把method调用时抛出的异常转换为HexException，重新抛出
    :param method: callable对象
    :return: Callable对象
    """

    def wrapped_func(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
        except _Rendezvous as e:
            new_exception = exception_from_str(e.details())
            raise new_exception
        except Exception as e:
            raise e
        else:
            return result

    return wrapped_func


class GrpcBaseClient(object):
    # client端设计需求，自动拦截所有方法调用，(golang 和python版本）
    # 1.第一个参数接受server的context，能自动把context里的原生user信息传给被调用的服务。
    # 2.自动对接服务端抛出的异常，能把服务端抛出的异常自动转换为HexException
    # 3.实现上述需求的同时，不破坏ide的变量追踪功能

    def __init__(self, name: str, address: str, cert_path: str = None):
        """
        :param name: service name
        :param host: service host
        :param port: service port
        :param cert_path: absolute path for certification file
        """
        self._ssl_mode = False
        self.service_name = name
        if cert_path:
            with Path(cert_path).open() as f:
                self._ssl_cert = grpc.ssl_channel_credentials(f.read())
            self._ssl_mode = True
            self._chan = grpc.secure_channel(address, self._ssl_cert,
                                             options=[('grpc.max_message_length', 8388608),
                                                      ('grpc.max_send_message_length', 8388608),
                                                      ('grpc.max_receive_message_length', 8388608)])
        else:
            self._chan = grpc.insecure_channel(address,
                                               options=[('grpc.max_message_length', 8388608),
                                                        ('grpc.max_send_message_length', 8388608),
                                                        ('grpc.max_receive_message_length', 8388608)])
        tracing_interceptor = open_tracing_client_interceptor(opentracing.tracer)
        self._chan = intercept_channel(self._chan, tracing_interceptor)

        super(GrpcBaseClient, self).__init__(self._chan)

    def __getattribute__(self, item):
        value = super(GrpcBaseClient, self).__getattribute__(item)
        if isinstance(value, UnaryUnaryMultiCallable):
            value = client_method_wrapper(value)
            setattr(self, item, value)
        return value


# class OauthClient:
#     def __init__(self, address):
#         conn = grpc.insecure_channel(address)
#         self._cli = HexOauthStub(conn)
#
#     def introspect_token(self, token: str) -> Dict:
#         req = IntrospectTokenRequest(token=token)
#         try:
#             resp = self._cli.IntrospectToken(req)
#         except _Rendezvous as e:
#             new_exception = exception_from_str(e.details())
#             raise new_exception
#         return pb2dict(resp)
#
#     def get_user_detail(self, partner_id:int, user_id: int, with_roles: bool = True, ) -> Dict:
#         if not (partner_id and user_id):
#             raise UnAuthorized("can't find user info in request_info")
#         metadata = (
#             ('partner_id', str(partner_id)),
#             ('user_id', str(user_id)),
#         )
#         req = GetUserRequest(id=user_id, with_roles=with_roles)
#         try:
#
#             resp = self._cli.GetUserById(req, timeout=TIMEOUT, metadata=metadata)
#         except _Rendezvous as e:
#             new_exception = exception_from_str(e.details())
#             raise new_exception
#         return pb2dict(resp)
