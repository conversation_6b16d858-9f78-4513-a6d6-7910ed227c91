import grpc
from datetime import datetime
from google.protobuf.json_format import MessageToDict
from google.protobuf.timestamp_pb2 import Timestamp

from supply.proto.cost import daily_cost_pb2
from supply.proto.reports import reports_pb2
from supply.proto.cost.daily_cost_pb2_grpc import DailyCostServiceStub
from supply.proto.reports.reports_pb2_grpc import ReportsStub
from supply import APP_CONFIG, TIMEOUT
from hex_exception import exception_from_str
from supply import time_cost

_HOST = str(APP_CONFIG['cost_host'])
_PORT = str(APP_CONFIG['cost_port'])
_CHANNEL = grpc.insecure_channel(_HOST + ':' + _PORT,
                                 options=[
                                     ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                                     ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
                                 ]
                                 )


class CostService(object):
    TIMEOUT = 60 * 2

    def __init__(self):
        self.daily_cost_serviceStub = DailyCostServiceStub(_CHANNEL)
        self.cost_center_serviceStub = ReportsStub(_CHANNEL)

    @time_cost
    def get_product_cost_by_store_code(self, store_code=None, date=None, limit=-1, offset=None, partner_id=None,
                                       user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))

        )
        try:
            ret = self.daily_cost_serviceStub.ListDailyCost(
                daily_cost_pb2.ListDailyCostRequest(store_code=store_code, date=date, limit=limit, offset=offset),
                timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:

            raise exception_from_str(str(e))
        return entity

    @time_cost
    def get_cost_report(self, start_date=None, end_date=None, product_ids=None, center_id=None, count_type=None,
                        category=None, offset=None, limit=None, partner_id=None, user_id=None):
        """成本报表服务
            MPID: 商品id列表 [int]
            CID: 成本中心id int
            countType: 默认"COSTCENTER"
            category: 类别 1:物料，2：成品
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        if start_date and isinstance(start_date, datetime):
            start_date = Timestamp(seconds=int(start_date.timestamp()))
        if end_date and isinstance(end_date, datetime):
            end_date = Timestamp(seconds=int(end_date.timestamp()))
        if not count_type:
            count_type = "COSTCENTER"
        try:
            ret = self.cost_center_serviceStub.GetReports(reports_pb2.GetReportsRequest(
                stime=start_date, etime=end_date, MPID=product_ids, CID=center_id, countType=count_type,
                category=category, offset=offset, limit=limit), timeout=TIMEOUT, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return entity


cost_service = CostService()
