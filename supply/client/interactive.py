from supply import logger
from ..driver.mysql import DummyTransaction
from sqlalchemy import text
from datetime import datetime, timedelta
from supply.client.receipt_service import receipt_service
from supply import time_cost


class InteractiveService(object):

    # 获取门店未确认的收货单
    @time_cost
    def get_list_receiving_details_store(self, store_id=None, end_date=None, partner_id=None, user_id=None, start_date=None):
        """
        :param end_date Timestamp
        :param store_id
        :param partner_id
        :param user_id
        :param start_date Timestamp
        """
        status = ["INITED"]
        entity = receipt_service.list_receives(receive_bys=[store_id], status=status, expect_end_date=end_date,
                                               main_branch_type="S", batch_type=["DEMAND"],
                                               partner_id=partner_id, user_id=user_id, delivery_start_date=start_date)
        rows = entity.rows
        return rows

    # 获取加盟门店未确认的收货单
    @time_cost
    def get_receiving_frs_store(self, store_id=None, end_date=None, partner_id=None, user_id=None):
        """
        :param end_date Timestamp 用于筛选收货单的发货日期小于等于盘点单的盘点日期，状态为“未收货”的收货单
        :param store_id
        :param partner_id
        :param user_id
        """
        status = ["INITED"]
        entity = receipt_service.list_receives(receive_bys=[store_id], status=status, expect_end_date=end_date,
                                               main_branch_type="FS", batch_type=["FRS_DEMAND"],
                                               partner_id=partner_id, user_id=user_id)
        rows = entity.rows
        return rows

    # 获取仓库未确认的收货单
    def get_list_receiving_details_warehouse(self, store_id=None, end_date=None, partner_id=None, user_id=None):
        """:param end_date Timestamp 用于筛选配送日在当日及之前，状态为“未收货”的收货单
            :param store_id
            :param batch_type 单据类型 <list> 'DEMAND'/'RETURN'/'TRANSFER'/'PURCHASE'
            :param partner_id
            :param user_id"""
        entity = receipt_service.list_receives(receive_bys=[store_id], status=["INITED"], delivery_end_date=end_date,
                                               main_branch_type="W", batch_type=["PURCHASE"],
                                               partner_id=partner_id, user_id=user_id)
        rows = entity.rows
        list_receiving = []
        if rows:
            list_receiving = [row for row in rows]
        return list_receiving

    # 获取未确认的收货差异单
    def get_list_receiving_diff_details_neq_status(self, end_date, store_id, partner_id, branch_type):
        """收货差异单的收货日期小于等于盘点单的盘点日期
        “新建”、“已提交”、“已驳回”
        """
        status = "'INITED','SUBMITTED','REJECTED'"
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              SELECT id,code,logistics_type,status FROM supply_receiving_diff
               WHERE received_by={}
                     and branch_type = '{}'
                     and status in ({})
                     and receive_date <= '{}'  
                     and partner_id={} ;
           '''.format(store_id, branch_type, status, end_date, partner_id)
            # print(sql_text)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    # 获取未确认的调拨单、调拨收获单
    def get_list_transfer_detail_neq_status(self, end_date, store_id, partner_id, branch_type, status1=None,
                                            status2=None):
        """
        调拨单的调拨日期小于等于盘点单的盘点日期
            • 调拨日期为当日或之前的调拨单在“新建”状态下会卡调出方门店盘点日期为当日的盘点盘
            • 调拨日期为当日或之前的调拨单在“待收货”状态下会卡调入方门店盘点日期为当日的盘点单
        :param end_date:
        :param store_id:
        :param partner_id:
        :param branch_type:
        :param status1: 调拨单状态
        :param status2: 调拨收货状态
        :return:
        """
        if not status1:
            status1 = "INITED"
        if not status2:
            status2 = 'SUBMITTED'
        sql_text = '''
           SELECT 
               transfer_id,
               code,
               status,
               shipping_store,
               receiving_store
           FROM supply_transfer_detail
           WHERE shipping_store={}
           and status = '{}'
           and transfer_date <= '{}'
           and partner_id={}
           and branch_type='{}';
        '''.format(store_id, status1, end_date, partner_id, branch_type)

        sql_text2 = '''
            SELECT
                transfer_id,
                code,
                status,
                shipping_store,
                receiving_store
            FROM supply_transfer_detail
            WHERE receiving_store={}
            and status='{}'
            and transfer_date <= '{}'
            and partner_id={} 
            and branch_type='{}';
         '''.format(store_id, status2, end_date, partner_id, branch_type)
        with DummyTransaction(auto_commit=False) as trans:
            # print(sql_text)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
            rows2 = trans.scope_session.execute(text(sql_text2)).fetchall()
        return rows + rows2

    # 获取未确认的报废单
    def get_list_store_adjust_neq_status(self, end_date, store_id, partner_id, branch_type, status=None):
        """报废单的报废日期小于等于盘点单的盘点日期"""
        if not status:
            status = "'INITED','SUBMITTED','REJECTED'"
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              SELECT adjust_id,code,status FROM supply_adjust_detail
               WHERE adjust_store={}
                     and status in ({})  
                     and adjust_date < '{}'
                     and partner_id={} 
                     and branch_type='{}';
           '''.format(store_id, status, end_date, partner_id, branch_type)
            # print(sql_text)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    # 获取当日待办报废单
    def get_unfinished_adjust(self, end_date, store_id, partner_id, branch_type, status=None):
        """报废单的创建日期小于等于当日"""
        if not status:
            status = "'INITED','SUBMITTED','REJECTED'"
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              SELECT adjust_id,code,status FROM supply_adjust_detail
               WHERE adjust_store={}
                     and status in ({})  
                     and created_at <= '{}'
                     and partner_id={} 
                     and branch_type='{}';
           '''.format(store_id, status, end_date, partner_id, branch_type)
            # print(sql_text)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    # 获取未确认的退货单
    def get_list_store_return_neq_status(self, end_date, store_id, partner_id, branch_type, status=None):
        """
        “新建”、“已提交”、“已驳回”的退货单，退货单的`退货日期`小于等于盘点单的盘点日期
        “已审核”的退货单，退货单的`预计交货日期`小于等于盘点单的盘点日期
        """
        if not status:
            status = ["INITED", "SUBMITTED", "REJECTED", "APPROVED"]
        if isinstance(status, list):
            status1 = "\',\'".join(status[:3])
            status2 = status[3]
        else:
            status1 = "INITED','SUBMITTED','REJECTED"
            status2 = "APPROVED"
        if branch_type == "WAREHOUSE":
            sub_type = "warehouse"      # 仓库
        elif branch_type == "FRS_STORE":
            sub_type = "fs_store"       # 加盟店
        else:
            sub_type = "store"          # 直营店
        with DummyTransaction(auto_commit=False) as trans:
            sql_text1 = '''
              SELECT id,code,logistics_type,status FROM supply_return
               WHERE  return_by={}
                 and status in ('{}')
                 and return_date <='{}'
                 and partner_id={} 
                 and sub_type='{}';
           '''.format(store_id, status1, end_date, partner_id, sub_type)

            sql_text2 = '''
              SELECT id,code,logistics_type,status FROM supply_return
               WHERE  return_by={}
                 and status in ('{}')
                 and return_delivery_date <= '{}'
                 and partner_id={} 
                 and sub_type='{}';
           '''.format(store_id, status2, end_date, partner_id, sub_type)
            # print(sql_text1)
            rows1 = trans.scope_session.execute(text(sql_text1)).fetchall()
            rows2 = trans.scope_session.execute(text(sql_text2)).fetchall()
            list_return = rows1 + rows2
        return list_return

    # 获取未完成盘点单
    def get_list_store_stocktake_neq_status(self, end_date, store_id, partner_id, status=None, branch_type=None):
        if not status:
            status = "INITED','SUBMITTED"
        if isinstance(status, list):
            status = "\',\'".join(status)
        date = datetime.utcnow()
        if 16 <= date.hour <= 20:
            end_date = str(date - timedelta(days=1))[:10]
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              SELECT doc_id,code,`type`,status,stocktake_type FROM supply_st_doc_details
               WHERE branch_id={}
                 and status in ('{}')  
                 and target_date ='{}'
                 and partner_id={} 
                 and branch_type='{}';
           '''.format(store_id, status, end_date, partner_id, branch_type)
            # print(sql_text)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    # 获取移动端首页未完成盘点单
    @time_cost
    def get_unfinished_stocktake_mobile(self, end_date, store_id, partner_id, status=None, branch_type=None):
        if not status:
            status = "INITED','SUBMITTED"
        if isinstance(status, list):
            status = "\',\'".join(status)
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              SELECT doc_id,code,`type`,status,stocktake_type FROM supply_st_doc_details
               WHERE branch_id={}
                 and status in ('{}')  
                 and created_at <= '{}'
                 and partner_id={} 
                 and branch_type='{}';
           '''.format(store_id, status, end_date, partner_id, branch_type)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    # 获取移动端订货单校验未盘盘点单
    @time_cost
    def get_demand_todo_stocktake(self, end_date, store_id, partner_id, status=None, branch_type=None):
        if not status:
            status = "INITED','SUBMITTED"
        if isinstance(status, list):
            status = "\',\'".join(status)
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              SELECT doc_id,code,`type`,status,stocktake_type FROM supply_st_doc_details
               WHERE branch_id={}
                 and status in ('{}')  
                 and target_date < '{}'
                 and partner_id={} 
                 and branch_type='{}';
           '''.format(store_id, status, end_date, partner_id, branch_type)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    # 获取未完成订货单
    def get_list_store_demand_neq_status(self, end_date, store_id, partner_id):
        status = "'INITED','SUBMITTED','REJECTED'"
        end_date_t = end_date + timedelta(days=1)
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              SELECT id,code,type,status FROM supply_demand
               WHERE receive_by={}
                     and status in ({})  
                     and type in ('SD')
                     and (demand_date ='{}' or demand_date ='{}')
                     and partner_id={} ;
           '''.format(store_id, status, end_date, end_date_t, partner_id)
            # print(sql_text)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    # 获取未完成订货单
    def get_unfinished_demand(self, end_date, store_id, partner_id, branch_type=None):
        if branch_type == "FRS_STORE":
            type_str = "FSD"
            # 对于加盟来说，久久丫的总部分配不需要门店支付，不用纳入“待办”，其他saas则需要纳入
            if partner_id not in [441, 436, 431, 1026]:
                type_str = "FSD','FMD"
            with DummyTransaction(auto_commit=False) as trans:
                sql_text = '''
                    SELECT id,code,`type`,status FROM supply_franchisee_demand
                    WHERE received_by={}
                        and status in ('PREPARE')  
                        and type in ('{}')
                        and created_at <= '{}'
                        and partner_id={};
                '''.format(store_id, type_str, end_date, partner_id)
        else:
            status = "INITED','SUBMITTED','REJECTED"
            with DummyTransaction(auto_commit=False) as trans:
                sql_text = '''
                  SELECT id,code,`type`,status FROM supply_demand
                   WHERE receive_by={}
                         and status in ('{}')  
                         and type in ('SD')
                         and created_at <= '{}'
                         and partner_id={};
               '''.format(store_id, status, end_date, partner_id)
        # print(sql_text)
        rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    def get_unfinished_self_picking(self, end_date, store_id, partner_id, status=None, branch_type=None):
        """
        :param end_date:
        :param store_id:
        :param partner_id:
        :param status:
        :param branch_type:
        :return:
        """
        if not status:
            status = ["INITED", "SUBMITTED","REJECTED"]
        if isinstance(status, list):
            status = "\',\'".join(status)
        with DummyTransaction(auto_commit=False) as trans:
            sql_text = '''
              SELECT id,code,status FROM supply_store_self_picking
               WHERE branch_id = '{}'
                 and status in ('{}')
                 and order_date <= '{}'
                 and partner_id ={} 
                 and branch_type ='{}';
           '''.format(store_id, status, end_date, partner_id, branch_type)
            # print(sql_text)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows


interactive_service = InteractiveService()
