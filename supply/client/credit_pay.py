import grpc
from hex_exception import exception_from_str
from supply import APP_CONFIG, TIMEOUT, time_cost
from supply.proto.credit_pay import credit_pay_pb2
from supply.proto.credit_pay.credit_pay_pb2_grpc import CreditPayStub
from google.protobuf.json_format import MessageToDict


# 信用付服务
_CREDIT_PAY_HOST = str(APP_CONFIG.get('credit_pay_host', '127.0.0.1'))
_CREDIT_PAY_PORT = str(APP_CONFIG.get('credit_pay_port', '8802'))

_CREDIT_PAY_CHANNEL = grpc.insecure_channel(_CREDIT_PAY_HOST + ':' + _CREDIT_PAY_PORT,
                                            options=[
                                                ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                                                ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
                                            ]
                                            )


class CreditPayService(object):
    def __init__(self):
        self.CreditPayClient = CreditPayStub(_CREDIT_PAY_CHANNEL)

    @time_cost
    def Refund(self, refund_ids, partner_id=None, user_id=None):
        """退款接口"""
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.CreditPayClient.Refund(
                credit_pay_pb2.RefundRequest(refund_id=refund_ids), timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret


credit_pay_service = CreditPayService()

