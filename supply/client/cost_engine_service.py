import traceback
from datetime import datetime, date

import grpc
from google.protobuf.json_format import MessageToDict
from google.protobuf.timestamp_pb2 import Timestamp
from hex_exception import exception_from_str
from supply.error.exception import DataValidationException
from collections import defaultdict
from supply import APP_CONFIG, TIMEOUT
from supply import logger
from supply.utils import type_convert
from supply.utils.snowflake import gen_snowflake_id
from supply.proto.reports import reports_pb2
from supply.proto.reports.reports_pb2_grpc import ReportsStub

_HOST = str(APP_CONFIG['cost_host'])
_PORT = str(APP_CONFIG['cost_port'])
_CHANNEL = grpc.insecure_channel(_HOST + ':' + _PORT)
_CALLBALURL = str(APP_CONFIG['cost_callback'])


class CostEngineService(object):
    """
    所有请求的patner_id和user_id在get_struct会从全局导入，或者自己单独传
    """

    def __init__(self):
        self.reportsStub = ReportsStub(_CHANNEL)

    # 触发计算任务 —— 单任务用（弃用）
    def trigger_task(self, partner_id, user_id, batch_no, period_id, pre_period_id, 
                            branch_id, branch_type, task_type, report_type, start_date, end_date):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(start_date)
                start_date = timestamp
            if isinstance(end_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(end_date)
                end_date = timestamp

            req = reports_pb2.CreateTaskRequest(
                                taskId=batch_no,
                                prePeriodId=int(pre_period_id) if pre_period_id else 0, # 上个账期的id
                                periodId=int(period_id) if period_id else 0, 
                                partnerId=partner_id,
                                costCenterId=int(branch_id),
                                operator=user_id,
                                countType=branch_type, # 计算维度类型: 0: 计算单据流水数据 1: 成本中心 2: 门店/仓库/加工中心等
                                taskType=int(task_type), # 1:计算任务(只计算给点时间内的数据)，2：重算任务 （计算给定时间到当天的数据）
                                reportType=int(report_type),# 1:物料计算报表 2：结存计算报表
                                startTime=start_date,
                                endTime=end_date,
                                )


            logger.info("CreateTaskRequest:{}".format(req))
            ret = self.reportsStub.CreateTask(req, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            logger.info("CreateTaskRes:{}".format(entity))
        except Exception as e:
            raise exception_from_str(str(e))
        return entity
    

    # 触发计算任务 —— 多任务用（迁移holo版本）
    def trigger_task_list(self, partner_id, user_id, batch_no, period_id, pre_period_id, 
                            branch_id, branch_type, task_type, report_type, start_date, end_date, extra_config=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(start_date)
                start_date = timestamp
            if isinstance(end_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(end_date)
                end_date = timestamp
            
            req_list = []
            if branch_type and isinstance(branch_type, list) and isinstance(report_type, list):
                for b_type in branch_type:
                    for r_type in report_type:
                        req_details = reports_pb2.CreateTasks(
                                    taskId=gen_snowflake_id(),
                                    prePeriodId=int(pre_period_id) if pre_period_id else 0, # 上个账期的id
                                    periodId=int(period_id) if period_id else 0, 
                                    partnerId=partner_id,
                                    costCenterId=int(branch_id),
                                    operator=user_id,
                                    countType=b_type, # 计算维度类型: 0: 计算单据流水数据 1: 成本中心 2: 门店/仓库/加工中心等
                                    taskType=int(task_type), # 1:计算任务(只计算给点时间内的数据)，2：重算任务 （计算给定时间到当天的数据）
                                    reportType=int(r_type),# 1:物料计算报表 2：结存计算报表
                                    startTime=start_date,
                                    endTime=end_date,
                                    )
                        req_list.append(req_details)
                if extra_config:
                    extra_branch_type = extra_config.get("extra_branch_type")
                    extra_report_type = extra_config.get("extra_report_type")
                    req_details = reports_pb2.CreateTasks(
                                    taskId=gen_snowflake_id(),
                                    prePeriodId=int(pre_period_id) if pre_period_id else 0, # 上个账期的id
                                    periodId=int(period_id) if period_id else 0, 
                                    partnerId=partner_id,
                                    costCenterId=int(branch_id),
                                    operator=user_id,
                                    countType=extra_branch_type, # 计算维度类型: 0: 计算单据流水数据 1: 成本中心 2: 门店/仓库/加工中心等
                                    taskType=int(task_type), # 1:计算任务(只计算给点时间内的数据)，2：重算任务 （计算给定时间到当天的数据）
                                    reportType=int(extra_report_type),# 1:物料计算报表 2：结存计算报表
                                    startTime=start_date,
                                    endTime=end_date,
                                    )
                    req_list.append(req_details)
            
            else:
                req_details = reports_pb2.CreateTasks(
                                    taskId=gen_snowflake_id(),
                                    prePeriodId=int(pre_period_id) if pre_period_id else 0, # 上个账期的id
                                    periodId=int(period_id) if period_id else 0, 
                                    partnerId=partner_id,
                                    costCenterId=int(branch_id),
                                    operator=user_id,
                                    countType=branch_type, # 计算维度类型: 0: 计算单据流水数据 1: 成本中心 2: 门店/仓库/加工中心等
                                    taskType=int(task_type), # 1:计算任务(只计算给点时间内的数据)，2：重算任务 （计算给定时间到当天的数据）
                                    reportType=int(report_type),# 1:物料计算报表 2：结存计算报表
                                    startTime=start_date,
                                    endTime=end_date,
                                    )
                req_list.append(req_details)

            req = reports_pb2.CreateTaskRequest(CreateTasks = req_list)
            logger.info("CreateTaskRequest:{}".format(req))
            ret = self.reportsStub.CreateTask(req, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            logger.info("CreateTaskRes:{}".format(entity))
        except Exception as e:
            raise exception_from_str(str(e))
        return entity
    

    """ saas多租户版本弃用
    # 执行计算 —— 弃用
    def trigger_count_material(self, batch_no, branch_id, branch_type,
                                start_date, end_date, product_ids=None, 
                                callback=None,
                                partner_id=None, user_id=None,
                                period_id=None, task_type=None,
                                report_type=None, category=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(start_date)
                start_date = timestamp
            if isinstance(end_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(end_date)
                end_date = timestamp

            # 计算纬度 1:成本中心，2:门店
            branch_type_dict = {
                'COSTCENTER': 1,
                'STORE': 2
            }

            counts_list = []
            if category:
                print(type(category))
                for ci in category:
                    count = reports_pb2.Count(
                                stime=start_date,
                                etime=end_date,
                                requestID=batch_no,
                                callback=_CALLBALURL,
                                partnerID=partner_id,
                                countType=branch_type_dict.get(branch_type),
                                CID=branch_id,
                                periodID=int(period_id), 
                                taskType=int(task_type), # 1:计算任务(只计算给点时间内的数据)，2：重算任务 （计算给定时间到当天的数据）
                                reportType=int(report_type),# 1:物料计算报表 2：结存计算报表
                                category=int(ci) #计算类别 1 第一类商品(供应商采购的商品) 2 第二类商品(由1初步加工而成的在产品) 3 第三类商品(由2包装工序后的商品) 4 第四类商品(由3物料转换后得到的新的sku) 5 第五类商品(通过一定的BOM配方，由几种其他商品制作而来的)
                                # mids=product_ids,
                                )
                    counts_list.append(count)
                TriggerMaterialReq = reports_pb2.CountRequest(
                        counts = counts_list
                    )
            else:
                logger.info("TriggerMaterialCountFailed:{}".format(str(TriggerMaterialReq)))
                return True
            
            logger.info("TriggerMaterialCount:{}".format(str(TriggerMaterialReq
                )))
            ret = self.reportsStub.Count(TriggerMaterialReq, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            print(entity)

        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 重新执行计算方法 —— 弃用
    def trigger_recount_material(self, batch_no, branch_id, branch_type,
                                start_date, end_date, days=None,
                                product_ids=None, callback=None,
                                partner_id=None, user_id=None, period_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(start_date)
                start_date = timestamp
            if isinstance(end_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(end_date)
                end_date = timestamp
            # 计算纬度 1:成本中心，2:门店
            branch_type_dict = {
                'COSTCENTER': 1,
                'STORE': 2
            }

            logger.info("TriggerMaterialReCount:{}".format(str(reports_pb2.CountRequest(
                                    stime=start_date,
                                    etime=end_date,
                                    requestID=batch_no,
                                    callback=_CALLBALURL,
                                    partnerID=partner_id,
                                    countType=branch_type_dict.get(branch_type),
                                    CID=branch_id,
                                    periodID=period_id, #TODO
                                    taskType=2, # 1:计算任务(只计算给点时间内的数据)，2：重算任务 （计算给定时间到当天的数据）
                                    reportType=1, # 1:物料计算报表 2：结存计算报表
                                    # mids=product_ids,
                                ))))
            req = reports_pb2.CountRequest(
                                    stime=start_date,
                                    etime=end_date,
                                    requestID=batch_no,
                                    callback=_CALLBALURL,
                                    partnerID=partner_id,
                                    countType=branch_type_dict.get(branch_type),
                                    CID=branch_id,
                                    periodID=period_id, #TODO
                                    taskType=2, # 1:计算任务(只计算给点时间内的数据)，2：重算任务 （计算给定时间到当天的数据）
                                    reportType=1, # 1:物料计算报表 2：结存计算报表
                                    # mids=product_ids,
                                )
            ret = self.reportsStub.Count(req, metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 执行bom计算 —— 弃用
    def trigger_count_bom(self, batch_no, branch_id, branch_type,
                                start_date, end_date, product_bom_dict, 
                                callback=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(start_date)
                start_date = timestamp
            if isinstance(end_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(end_date)
                end_date = timestamp
            bp = {}
            for ticket_key, ticket_value in product_bom_dict.items():
                for key, value in ticket_value.items():
                    product_id = int(key)
                    m = {}
                    sale_qty = 0
                    for bom_key, bom_value in value.items():
                        bom_id = int(bom_key)
                        m[bom_id] = bom_value['bom_qty']
                        sale_qty = bom_value['sale_qty']
                    BomProduct = {
                            'PID':  product_id, # 商品id
                            # 'unit': unit_id,
                            'quantity': sale_qty, 
                            'm': m
                        } 
                    bp[product_id] = reports_pb2.BomProduct(**BomProduct)
            
            logger.info("TriggerMaterialBom:{}".format(str(reports_pb2.CountBomRequest(
                stime=start_date,
                etime=end_date,
                CID=branch_id,
                requestID=batch_no,
                # bp=bp,
                callback=_CALLBALURL,
                countType=branch_type
            ))))
            ret = self.reportsStub.CountBom(reports_pb2.CountBomRequest(
                stime=start_date,
                etime=end_date,
                CID=branch_id,
                requestID=batch_no,
                bp=bp,
                callback=_CALLBALURL,
                countType=branch_type
            ), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)
            # print(entity)

        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 重新执行BOM计算方法 —— 弃用
    def trigger_recount_bom(self, batch_no, branch_id, branch_type,
                                start_date, end_date, days,
                                product_bom_dict, callback=None,
                                partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if isinstance(start_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(start_date)
                start_date = timestamp
            if isinstance(end_date, datetime):
                timestamp = Timestamp()
                timestamp.FromDatetime(end_date)
                end_date = timestamp

            bp = {}
            for key, value in product_bom_dict.items():
                product_id = int(key)
                m = {}
                sale_qty = 0
                for bom_key, bom_value in value.items():
                    bom_id = int(bom_key)
                    m[bom_id] = bom_value['bom_qty']
                    sale_qty = bom_value['sale_qty']
                BomProduct = {
                        'PID':  product_id, # 商品id
                        # 'unit': unit_id,
                        'quantity': sale_qty, 
                        'm': m
                    } 
                bp[product_id] = reports_pb2.BomProduct(**BomProduct)
            
            logger.info("TriggerMaterialBomRecount:{}".format(str(reports_pb2.ReCountBomRequest(
                                cbr=reports_pb2.CountBomRequest(
                                        stime=start_date,
                                        etime=end_date,
                                        CID=branch_id,
                                        requestID=batch_no,
                                        bp=bp,
                                        callback=_CALLBALURL,
                                        countType=branch_type
                                    ),
                                days=days
                            ))))
            ret = self.reportsStub.ReCountBom(
                            reports_pb2.ReCountBomRequest(
                                cbr=reports_pb2.CountBomRequest(
                                        stime=start_date,
                                        etime=end_date,
                                        CID=branch_id,
                                        requestID=batch_no,
                                        bp=bp,
                                        callback=_CALLBALURL,
                                        countType=branch_type
                                    ),
                                days=days
                            ), metadata=metadata)
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            raise exception_from_str(str(e))
        return entity

    # 关闭账期
    def close_count_task(self, batch_no, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            logger.info("CloseCountReq:{}".format(str(reports_pb2.CloseTaskRequest(
                                    requestID=batch_no
                                ))))
            req = reports_pb2.CloseTaskRequest(
                                    requestID=batch_no)
            ret = self.reportsStub.CloseTask(req, metadata=metadata)
            logger.info("CloseCountRet:{}".format(str(ret)))
            entity = MessageToDict(ret, preserving_proto_field_name=True)

        except Exception as e:
            raise exception_from_str(str(e))
        return entity
    """

cost_engine_service = CostEngineService()