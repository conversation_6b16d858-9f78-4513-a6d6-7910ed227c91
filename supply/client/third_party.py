import grpc
import logging
from supply.proto.third_party.orderproductprice_pb2_grpc \
     import QuerySupplyOrderProductPriceStub
from supply.proto.third_party.orderproductprice_pb2 \
     import GetOrderProductPriceRequest
from google.protobuf.json_format import MessageToDict
from supply.proto.third_party.finance_reconciliation_pb2_grpc \
     import FinanceReconciliationServiceStub
from supply.proto.third_party.finance_reconciliation_pb2 \
     import FinanceReconciliationReq

from supply import APP_CONFIG
from supply.error.exception import NoResultFoundError

_HOST = str(APP_CONFIG['third_party_host'])
import logging
_PORT = str(APP_CONFIG['third_party_port'])
logging.info("*"*30 + '端口')
logging.info(_HOST)
logging.info(_PORT)
_CHANNEL = grpc.insecure_channel(_HOST + ':' + _PORT, options=[
    ('grpc.max_send_message_length', 1024 * 1024 * 1024),
    ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
], )


class ThirdPartyService(object):
    """
    third_party_host
    third_party_port
    """
    def __init__(self):
        self.third_party_price_stub = QuerySupplyOrderProductPriceStub(_CHANNEL)
        self.third_party_statements_stub = FinanceReconciliationServiceStub(_CHANNEL)

    def get_third_party(self):
        pass

    @staticmethod
    def get_product_tax_price(data):
        result = {}
        for product in data:
            product_id = int(product.get('productId'))
            price = product.get('price')
            result[product_id] = {}
            result[product_id]['tax'] = price
            result[product_id]['no_tax'] = price
            result[product_id]['rate'] = 0
        return result

    def get_product_price(self, storeId=None, productId=None,
                          pageNum=None, pageSize=None, isAll=None):
        """
        :param storeId: 门店编号
        :param productId: 商品编号
        :param pageNum: 分页参数
        :param pageSize: 分页条数
        :param isAll: 是否返回全部
        :return: {
            storeCode,
            storeId,
            productCode,
            unitCode,
            unitId,
            price
        }
        """
        # thir = f"第三方取价格: storeId:{storeId}, productId:{productId}, pageNum:{pageNum}, pageSize:{pageSize}"
        # logging.info(thir)
        res = self.third_party_price_stub.GetOrderProductPrice(
            GetOrderProductPriceRequest(
                productId=productId, storeId=storeId,
                pageNum=pageNum, pageSize=pageSize, isAll=isAll
                )
        )
        result = MessageToDict(res).get('rows')
        if not result:
            return {}
            # raise NoResultFoundError("未找到相关商品!")
        return self.get_product_tax_price(result)

    def get_statements(self, iGjahr, iMonat, iKunnr, iBukrs,
                       limit=None, offset=None, order_type='date', order=None, likeBezei='', likeBelnr=''):
        """
        :param iGjahr: 会计年度
        :param iMonat: 会计期间
        :param iKunnr: 客户编码
        :param iBukrs: 公司代码
        """
        request = FinanceReconciliationReq(
            iGjahr=iGjahr, iMonat=iMonat, iKunnr=iKunnr, iBukrs=iBukrs,
            limit=limit, offset=offset, likeBezei=likeBezei, order=order,
            likeBelnr=likeBelnr
        )
        logging.info(request)
        res = self.third_party_statements_stub.getFinanceReconciliation(
            request)
        result = MessageToDict(res)
        return result


third_party = ThirdPartyService()
