from decimal import Decimal

import grpc
from hex_exception import exception_from_str, UnAuthorized
from supply import APP_CONFIG, request_info, TIMEOUT, logger, time_cost
from supply.proto.products_manage import products_manage_pb2
from supply.proto.products_manage.products_manage_pb2_grpc import ProductManageStub
from google.protobuf.json_format import MessageToDict
from google.protobuf.struct_pb2 import Struct
from google.protobuf.timestamp_pb2 import Timestamp
from ..error.exception import *
from supply.utils import pb2dict

# 商品管理服务
_PRODUCTS_MANAGE_HOST = str(APP_CONFIG.get('products_manage_host', '127.0.0.1'))
_PRODUCTS_MANAGE_PORT = str(APP_CONFIG.get('products_manage_port', '8802'))

_PRODUCTS_MANAGE_CHANNEL = grpc.insecure_channel(_PRODUCTS_MANAGE_HOST + ':' + _PRODUCTS_MANAGE_PORT,
                                                 options=[
                                                     ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                                                     ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
                                                 ]
                                                 )


def get_struct(func):
    def inner(*args, **kwargs):
        if kwargs.get('filters'):
            filte_dict = kwargs.get('filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['filters'] = struct
        if kwargs.get("store_filters"):
            filte_dict = kwargs.get('store_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['store_filters'] = struct
        if kwargs.get("store_relation_filters"):
            filte_dict = kwargs.get('store_relation_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['store_relation_filters'] = struct
        if kwargs.get("product_relation_filters"):
            filte_dict = kwargs.get('product_relation_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['product_relation_filters'] = struct
        if kwargs.get('product_filters'):
            filte_dict = kwargs.get('product_filters')
            struct = Struct()
            struct.update(filte_dict)
            kwargs['product_filters'] = struct
        if kwargs.get('relation_filters'):
            relation_filters_dict = kwargs.get('relation_filters')
            struct = Struct()
            struct.update(relation_filters_dict)
            kwargs['relation_filters'] = struct
        if not kwargs.get("partner_id", None) or not kwargs.get('user_id', None):
            # partner_id, user_id 要传关键字参数
            if hasattr(request_info, "partner_id") and hasattr(request_info, "user_id"):
                kwargs['partner_id'] = request_info.partner_id
                kwargs['user_id'] = request_info.user_id
            else:
                raise UnAuthorized("请传入partner_id和user_id")
        return func(*args, **kwargs)

    return inner


def result_wraps(func):
    def inner(*args, **kwargs):
        entity = func(*args, **kwargs)
        if entity.get('rows'):
            rows = entity['rows']
        else:
            rows = [entity]
        for row in rows:
            if row.get("product"):
                row.update(row['product'])
                del row['product']
        return entity

    return inner


class ProductManageService(object):
    def __init__(self):
        self.ProductManageClient = ProductManageStub(_PRODUCTS_MANAGE_CHANNEL)

    @get_struct
    @time_cost
    def GetAgentProducts(self, store_id=None, category_ids=None, product_ids=None, order_type_id=None, price_type=None,
                         status=-1, allow_order=False, order_rule=None, tax_price_eq_less_zero=None, user_id=None,
                         partner_id=None, search=None, page_order=None, store_ids=None):
        """
        :param store_id:
        :param search: 筛选条件: 商品code, 商品名称
        :param page_order: 分页信息
        :param category_ids: 分类筛选
        :param order_type_id: 订货类型ID（单选）
        :param price_type: 价格类型：订货价 1/零售价 2
        :param status: 过滤的状态 -1返回全部 0过滤估清默认 1过滤置顶
        :param allow_order: 可订货状态 true返回可订货 false全部返回
        :param tax_price_eq_less_zero: 是否过滤小于等于0的价格
        :param order_rule: 订货规则过滤 bool
        :param product_ids:
        :param partner_id:
        :param user_id:
        :return:
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        agent_products_filter = dict()
        if status is not None:
            agent_products_filter['status'] = status
        if allow_order is not None:
            agent_products_filter['allow_order'] = allow_order
        if tax_price_eq_less_zero is not None:
            agent_products_filter['tax_price_eq_less_zero'] = tax_price_eq_less_zero
        if order_rule is not None:
            agent_products_filter['order_rule'] = order_rule
        if page_order is None:
            page_order = {"limit": -1}
        try:
            ret = self.ProductManageClient.GetAgentProductsFilterRules(
                products_manage_pb2.GetAgentProductsRequest(
                    store_id=store_id,
                    type_id=order_type_id,
                    price_type=price_type,
                    agent_products_filter=agent_products_filter,
                    search=search,
                    page=page_order,
                    category_ids=category_ids,
                    product_ids=product_ids,
                    store_ids=store_ids
                ),
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    @get_struct
    @time_cost
    def ListAgentProducts(self, store_id=None, search=None, page_order=None, category_ids=None,
                          order_type_id=None, status=-1, allow_order=False, user_id=None,
                          tax_price_eq_less_zero=None, order_rule=None, product_ids=None, partner_id=None):
        """
        :param store_id:
        :param search: 筛选条件: 商品code, 商品名称
        :param page_order: 分页信息
        :param category_ids: 分类筛选
        :param order_type_id: 订货类型ID（单选）
        :param price_type: 价格类型：订货价 1/零售价 2
        :param status: 过滤的状态 -1返回全部 0过滤估清默认 1过滤置顶
        :param allow_order: 可订货状态 true返回可订货 false全部返回
        :param tax_price_eq_less_zero: 是否过滤小于等于0的价格
        :param order_rule: 订货规则过滤 bool
        :param product_ids:
        :param partner_id:
        :param user_id:
        :return:
        """
        agent_products_filter = dict()
        if status is not None:
            agent_products_filter['status'] = status
        if allow_order is not None:
            agent_products_filter['allow_order'] = allow_order
        if tax_price_eq_less_zero is not None:
            agent_products_filter['tax_price_eq_less_zero'] = tax_price_eq_less_zero
        if order_rule is not None:
            agent_products_filter['order_rule'] = order_rule
        if page_order is None:
            page_order = {"limit": -1}
        try:
            metadata = (
                ("partner_id", str(partner_id)),
                ("user_id", str(user_id))
            )
            ret = self.ProductManageClient.GetAgentProductsFilterRules(
                products_manage_pb2.GetAgentProductsRequest(
                    store_id=store_id,
                    type_id=order_type_id,
                    agent_products_filter=agent_products_filter,
                    search=search,
                    page=page_order,
                    category_ids=category_ids,
                    product_ids=product_ids),
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
            rows = ret.get('rows', {})
            products = rows.get('list', [])
        except Exception as e:
            raise exception_from_str(str(e))
        return products

    @get_struct
    @time_cost
    def GetDistributionRule(self, company_id=None, distrcenter_id=None, store_ids=None, search=None, page_order=None,
                            category_ids=None, product_ids=None, partner_id=None, user_id=None):
        """
        :param company_id 公司
        :param category_ids 分类
        :param product_ids 商品
        :param search: 筛选条件 商品code, 商品名称
        :param distrcenter_id
        :param store_ids 门店id
        :param page_order 分页信息
        :param partner_id
        :param user_id
        """
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            page = {"limit": -1, "offset": 0}
            ret = self.ProductManageClient.GetDistributionRule(
                products_manage_pb2.GetDistributionRuleRequest(
                    company_id=company_id,
                    distrcenter_id=distrcenter_id,
                    store_ids=store_ids,
                    search=search,
                    page=page_order,
                    category_ids=category_ids,
                    product_ids=product_ids),
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    def GetPriceSkuByStore(self, store_id, product_ids=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.ProductManageClient.GetPriceSkuByStore(
                products_manage_pb2.GetPriceSkuByStoreRequest(
                    store_id=store_id,
                    product_ids=product_ids),
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    def GetRangeOfOrder(self, store_ids, time_bucket=None, type_id=None, partner_id=None, user_id=None, limit=10,
                        offset=0):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        # print("store_ids", store_ids)
        if len(store_ids) > 0:
            store_id = store_ids[0]
        else:
            store_id = 0
        try:
            page = products_manage_pb2.PageOrder(limit=limit, offset=offset)
            req = products_manage_pb2.QueryOrderTypeGroupRequest(
                store_id=store_id,
                type_id=type_id,
                page=page,
                visual_angle='store')

            ret = self.ProductManageClient.QueryOrderTypeGroupByStore(
                req,
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
            # print('ret',ret)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    @time_cost
    def get_order_rule_by_store(self, store_id, company_id=None, product_ids=None, category_ids=None, search=None,
                                page=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.ProductManageClient.GetOrderRuleByStore(
                products_manage_pb2.GetOrderRuleByStoreRequest(
                    store_id=store_id,
                    category_ids=category_ids,
                    company_id=company_id,
                    search=search,
                    product_ids=product_ids,
                    page=page or {"limit": -1}),
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))

        return ret

    def query_free_order_limit(self, product_ids, partner_id, user_id, search=None, page=None,
                               accounting_rate_dict=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.ProductManageClient.QueryFreeOrderLimit(
                products_manage_pb2.QueryFreeOrderLimitRequest(
                    search=search,
                    product_ids=product_ids,
                    page=page or {"limit": -1}),
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))

        if accounting_rate_dict:
            return {int(i['product_id']): Decimal(i['limit_qty']) / accounting_rate_dict.get(int(i['product_id']))
                    for i in ret.get('rows', []) if accounting_rate_dict.get(int(i['product_id'])) and 'limit_qty' in i}
        ret: dict
        return ret

    def query_order_type_product(self, store_id, type_id, partner_id, user_id):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.ProductManageClient.QueryOrderTypeGroup(
                products_manage_pb2.QueryOrderTypeGroupRequest(
                    store_id=store_id,
                    type_id=type_id),
                timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret


products_manage_service = ProductManageService()
