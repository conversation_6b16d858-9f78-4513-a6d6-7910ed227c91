import grpc
import logging

from hex_exception import exception_from_str
from supply import APP_CONFIG
from supply.proto.ianvs.user_pb2 import *
from supply.proto.ianvs.user_pb2_grpc import UserStub
from google.protobuf.json_format import MessageToDict


# 用户管理与认证服务
_USER_ADDRESS = str(APP_CONFIG['ianvs_address'])
TIMEOUT = 60
_USER_CHANNEL = grpc.insecure_channel(_USER_ADDRESS,
                                      options=[
                                          ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                                          ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
                                      ]
                                      )


class IanVsService(object):
    def __init__(self):
        self.UserClient = UserStub(_USER_CHANNEL)

    def ListUserInfo(self, ids=None, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            if not ids:
                ids = [user_id]
            ret = self.UserClient.ListUserInfo(ListUserInfoReq(ids=ids), timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret

    def get_user_dict(self,partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        user_dict = {}
        try:
            ret = self.UserClient.ListUser(ListUserReq(), timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
            user_list = ret.get('rows')
            if user_list:
                for user in user_list:
                    user_dict[int(user.get('id', 0))] = user.get('nick', ' ')
            return user_dict
        except Exception:
            logging.info("get_user_dict: 拉取用户信息列表失败")
            return {}

    def ListAllPartner(self, partner_id=None, user_id=None):
        metadata = (
            ("partner_id", str(partner_id)),
            ("user_id", str(user_id))
        )
        try:
            ret = self.UserClient.ListAllPartner(empty(), timeout=TIMEOUT, metadata=metadata)
            ret = MessageToDict(ret, preserving_proto_field_name=True)
        except Exception as e:
            raise exception_from_str(str(e))
        return ret


ianvs_service = IanVsService()


