import logging

from ..proto.supply_message.supply_message_pb2_grpc import SupplyMessageServicer
from ..proto.supply_message.supply_message_pb2 import *
from ..utils import get_user_info
from supply.api import OauthGrpcAPI
from importlib import import_module
from pathlib import Path
import json

from supply.utils.get_task_dict import load_mq_task
from supply.driver.mq import TOPIC


class SupplyMessageService(SupplyMessageServicer, OauthGrpcAPI):

    def HandleMessage(self, request, context):
        tags = request.tags
        logging.info(f'消息: {tags} 开始')
        try:
            body = json.loads(request.body)
        except json.JSONDecodeError as e:
            logging.info(f'body解析错误， {e}')
            return MessageResponse()

        result = True
        if tags in TOPIC:
            result = TOPIC[tags](body)
            logging.info(f'消息 {tags} 结果为 {result}')
        else:
            logging.info(f'消息 {tags} 已丢弃')

        return MessageResponse(result=result if result is not None else True)

