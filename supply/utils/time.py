from datetime import time, datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp


def datetime_slice_to_time(date_time: datetime) -> str:
    time_text = "%H:%M:%S"
    result = datetime.strftime(date_time, time_text)
    return result


def time_convert_to_timedelta(rtime: time) -> timedelta:
    """this function convert datetime.time to timedelta"""
    hour = rtime.hour
    minute = rtime.minute
    second = rtime.second
    result = timedelta(hours=hour, minutes=minute, seconds=second)
    return result


def str_convert_to_timedelta(time: str) -> timedelta:
    """this function convert str to timedelta"""
    time_text = "%H:%M:%S"
    rtime = datetime.strptime(time, time_text)
    hour = rtime.hour
    minute = rtime.minute
    second = rtime.second
    result = timedelta(hours=hour, minutes=minute, seconds=second)
    return result


def get_timestamp(date):
    if isinstance(date, datetime):
        date = Timestamp(seconds=int(date.timestamp()))
    else:
        date = None
    return date
