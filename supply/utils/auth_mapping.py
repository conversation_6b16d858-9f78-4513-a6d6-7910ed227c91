import logging


class AuthCodeMapping(object):
    def __init__(self):
        self.APP_NAME = "boh"
        self.AUTH_DOMAIN_MAP = dict(
            store=dict(
                order="store.order",
                stocktake="store.stocktake",
                loss="store.loss",
                inventory="store.inventory",
                store_bi="store_bi.detail"

            ),
            warehouse=dict(
                order="warehouse.order",
                stocktake="warehouse.stocktake",
                loss="warehouse.loss",
                inventory="warehouse.inventory",
                warehouse_bi="warehouse_bi.detail"
            ),
            manufactory=dict(
                order="manufactory.order",
                stocktake="manufactory.stocktake",
                loss="manufactory.loss",
                inventory="manufactory.inventory",
                manufactory_bi="manufactory_bi.detail"
            )
        )

    def get_action_code(self, branch_type=None, operation=None, action=None):
        """
        :param branch_type 组织类型
        :param operation 功能操作(单据order、盘点stocktake、报废loss、库存inventory等...)
        :param action 查看 view
                      维护 maintain
                      审核 audit
        """
        logging.info(
            "[get_action_code]branch_type: {} - operation: {} - action: {}".format(branch_type, operation, action))
        if not all([branch_type, operation, action]):
            logging.warning("[get_action_code] Lack of params！")
            return ''
        if str(branch_type).lower() == "store":
            branch_type = "store"
        elif str(branch_type).lower() == "warehouse":
            branch_type = "warehouse"
        elif str(branch_type).lower() == "machining_center":
            branch_type = "manufactory"
        else:
            pass
        if branch_type in self.AUTH_DOMAIN_MAP.keys():
            action_code = self.APP_NAME + '.' + self.AUTH_DOMAIN_MAP.get(branch_type).get(operation) + "." + str(action)
        else:
            action_code = ''
        return action_code

    def get_domain(self, branch_type: str):
        if str(branch_type).lower() == "store":
            domain = self.APP_NAME + ".store"
        elif str(branch_type).lower() == "warehouse":
            domain = self.APP_NAME + ".warehouse"
        elif str(branch_type).lower() == "machining_center":
            domain = self.APP_NAME + ".manufactory"
        else:
            domain = ''
        return domain


auth_code_mapping = AuthCodeMapping()
