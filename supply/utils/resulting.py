# -*- coding: utf-8 -*-

from .enum import enum_val


def api_result(status_code=0, code=None, description=None, payload=None):
    result = {
        "status_code": status_code,
    }
    if code is not None:
        result['code'] = code
    if description is not None:
        result['description'] = description
    if payload is not None:
        result["payload"] = payload

    return result


ErrorCode = enum_val(
    Success='SUCCESS',
    Failed='FAILED',
    Unauthorized="UNAUTHORIZED",
    UnknownPartner="UNKNOWN_PARTNER",
    UnknownState="UNKNOWN_STATE",
    AuthorizationError="AUTHORIZATION_ERROR",
    PermissionDenied='PERMISSION_DENIED',
    NotFound="NOT_FOUND",
    DataNotValid='DATA_NOT_VALID',
    ApiError='API_ERROR',
    InvalidRequestData="INVALID_REQUEST_DATA",
    NoPermission='NO_PERMISSION',
    InvalidOrgType="INVALID_ORG_TYPE",
    InvalidParentId="INVALID_PARENT_ID",
    RecordAlreadyUsed="RECORD_ALREADY_USED",
    RecordAlreadyExist="RECORD_ALREADY_EXIST",
    RecordLockedByStatus="RECORD_LOCKED_BY_STATUS",
    RecordStillLinked="RECORD_STILL_LINKED",
    RecordHasChildren="RECORD_HAS_CHILDREN",
    RecordDamaged="RECORD_DAMAGED",
    ParentNotActive="PARENT_NOT_ACTIVE",
    InvalidDataFormat="INVALID_DATA_FORMAT",
    InvalidDataType="INVALID_DATA_TYPE",
    InvalidDataOption="INVALID_DATA_OPTION",
    InvalidId="INVALID_ID",
    InvalidRelationship="INVALID_RELATIONSHIP",
    InvalidRelation="INVALID_RELATION",
    ObjectKeyNotDefined="OBJECT_KEY_NOT_DEFINED",
    ItemAlreadyLinked="ITEM_ALREADY_LINKED",
    ItemAlreadyAssigned="ITEM_ALREADY_ASSIGNED",
    DoNotAllowAssign="DO_NOT_ALLOW_ASSIGN",
    DoNotAllowAssociation="DO_NOT_ALLOW_ASSOCIATION",
    InternalServerError="INTERNAL_SERVER_ERROR",
    UpdateMultiNotId="UPDATE_MULTIPLE_NOT_ID",
    DataValidationError="DATA_VALIDATION_ERROR",
    DataFieldRequired="DATA_FIELD_REQUIRED",
    InputDataError='INPUT_DATA_ERROR',
    BOMDetailsNotFound='BOM_DETAILS_NOT_FOUND',
    DataStateNotRecognized='DATA_STATE_NOT_RECOGNIZED',
    UserAlreadyExist='USER_ALREADY_EXIST',
    ApiAccessDenied='API_ACCESS_DENIED',
    WrongPassword='WRONG_PASSWORD',
    UnknownTaskAction='UNKNOWN_TASK_ACTION',
    InvalidTaskTime='INVALID_TASK_TIME',
    TaskDataValidationError='TASK_DATA_VALIDATION_ERROR',
    InvalidStatus='INVALID_STATUS',
    Timeout='TIME_OUT',
    FormulaConflict='FORMULA_CONFLICT',
    DataIncomplete='DATA_INCOMPLETE',
    StockTakeConflict='STOCKTAKE_CONFLICT',
    KeyboardNotSet='KEYBOARD_NOT_SET',
    UnitNotFound='UNIT_NOT_FOUND',
    InvalidCode='INVALID_CODE',
    InvalidTicketId='INVALID_TICKET_ID',
    InvalidPaymentType='INVALID_PAYMENT_TYPE',
    InvalidPaymentCode='INVALID_PAYMENT_CODE',
    InvalidPaymentItem='INVALID_PAYMENT_ITEM',
    FailedToPay='FAILED_TO_PAY',
    FailedToRefund='FAILED_TO_REFUND',
    FailedToCancel='FAILED_TO_CANCEL',
    DuplicateAuth='DUPLICATE_AUTH',
    PaymentInProcess='PAYMENT_IN_PROCESS',
    NewUserThirdPartyId='New_User_Third_Party_Id',
    InvalidUsername='Invalid_Username'
)


class StatusCode(object):
    @property
    def SUCCESS(self):
        return 0

    @property
    def ERROR(self):
        return 1

    @property
    def WARINING(self):
        return 2


StatusCode = StatusCode()


class OauthInfo(object):
    @property
    def partner_id(self):
        return self.__partner_id

    @partner_id.setter
    def partner_id(self, value):
        self.__partner_id = value

    @property
    def partner_name(self):
        return self.__partner_name

    @partner_name.setter
    def partner_name(self, value):
        self.__partner_name = value

    @property
    def user_name(self):
        return self.__user_name

    @user_name.setter
    def user_name(self, value):
        self.__user_name = value

    @property
    def user_id(self):
        return self.__user_id

    @user_id.setter
    def user_id(self, value):
        self.__user_id = value

    def __init__(self, partner_id=None, partner_name=None, user_id=None, user_name=None):
        self.__partner_id = partner_id
        self.__partner_name = partner_name
        self.__user_id = user_id
        self.__user_name = user_name
