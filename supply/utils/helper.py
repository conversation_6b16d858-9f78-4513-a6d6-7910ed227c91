# coding:utf-8
import functools
from datetime import date, datetime, timezone, timedelta
import time
import calendar
import decimal
from random import choice, seed
from time import mktime
from typing import Union

import pytz
from google.protobuf import struct_pb2

from ..client.ianvs_service import ianvs_service
from ..utils.encode import encodeUTF8, CJsonEncoder
from ..utils.exception import DataValidationException
from supply.utils.snowflake import gen_snowflake_id
from ..error.exception import DataValidationException as DataValidationExceptionNoCode
from supply.error.exception import StoreScopeException
from dateutil import parser
from decimal import Decimal
from collections import Iterable, Iterator, OrderedDict
import json
import math
import copy
import inspect
from supply.utils import snowflake
from supply import APP_CONFIG
from ..driver._nsq import NSQService
from supply.driver.Redis import Redis_cli
from google.protobuf.timestamp_pb2 import Timestamp
from decimal import Decimal
from ..client.metadata_service import metadata_service
import logging
from hex_exception import exception_from_str
from supply.client.auth_permission import auth_permission
from decimal import ROUND_HALF_UP


DEFAULT_STORAGE_TYPE = {'LC': 'LC', 'COLD': 'COLD', 'NORMALTP': 'NORMALTP', 'FRESH': 'FRESH'}


class CJsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.strftime('%Y-%m-%d')
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        else:
            return json.JSONEncoder.default(self, obj)


def get_safe_value(mapping: dict, key=None, default=None):
    """
    Get safe value from dict
    :param mapping:
    :param key:
    :param default: 默认返回值
    :return: value
    """
    value = mapping.get(key)
    if not isinstance(value, type(default)):
        value = default
    return value


def get_guid():
    return snowflake.gen_snowflake_id()


def get_uuids(count):
    return [convert_to_int(_id) for _id in snowflake.get_ids(count)]


def generate_random_code(length):
    seed(datetime.utcnow())
    s_data = 'abcdefghigklmnopqrstuvwxyz1234567890'
    code = ''
    for x in range(length):
        code = code + choice(s_data)
    return code


# def is_bigint(num):
#     assert isinstance(num, (int, long))
#     return len(str(num)) >= 18


def set_db(db_obj, properties, dict_parse=True):
    if db_obj is None:
        raise ValueError('db object cannot be none')
    if properties is None or type(properties) is not dict:
        raise ValueError('properties object must be dict')
    for prop_key in properties:
        if prop_key not in ['extends'] and properties[prop_key] is not None and hasattr(db_obj, prop_key):
            # update the properties to db
            prop_value = properties[prop_key]
            if isinstance(prop_value, dict) and dict_parse:
                prop_value = json.dumps(prop_value, cls=CJsonEncoder)
            setattr(db_obj, prop_key, prop_value)

    if 'extends' in properties and properties['extends'] is not None:
        # parse the extends
        if hasattr(db_obj, 'extends'):
            db_extends = db_obj.extends
            if db_extends is not None:
                try:
                    db_extends = json.loads(db_extends)
                except:
                    db_extends = {}
            else:
                db_extends = {}
            for prop_key in properties['extends']:
                db_extends[prop_key] = properties['extends'][prop_key]
            # update extends to db
            db_obj.extends = json.dumps(db_extends, cls=CJsonEncoder)
    if 'extend' in properties and properties['extend'] is not None:
        # parse the extends
        if hasattr(db_obj, 'extend'):
            db_extend = db_obj.extend
            if db_extend is not None:
                try:
                    db_extend = json.loads(db_extend)
                except:
                    db_extend = {}
            else:
                db_extend = {}
            for prop_key in properties['extend']:
                db_extend[prop_key] = properties['extend'][prop_key]
            # update extends to db
            db_obj.extend = json.dumps(db_extend, cls=CJsonEncoder)


def update_db(db_obj, properties):
    if db_obj is None:
        raise ValueError('db object cannot be none')
    if properties is None or type(properties) is not dict:
        raise ValueError('properties object must be dict')
    if 'id' in properties:
        del properties['id']
    if 'partner_id' in properties:
        del properties['partner_id']
    if 'created_by' in properties:
        del properties['created_by']
    if 'created' in properties:
        del properties['created']
    properties['updated'] = datetime.now()
    set_db(db_obj, properties)


def set_model_from_db(db_props, model):
    if db_props is None:
        raise ValueError('db_props object cannot be none')
    if type(db_props) is not dict:
        raise ValueError('db_props must be dict')
    if model is None:
        raise ValueError('model object cannot be none')
    for prop_key in db_props:
        if db_props[prop_key] is not None and hasattr(model, prop_key):
            # update the properties to db
            setattr(model, prop_key, db_props[prop_key])


def set_model_from_props(props, model):
    if props is None:
        raise ValueError('props object cannot be none')
    if type(props) is not dict:
        raise ValueError('props must be dict')
    if model is None:
        raise ValueError('model object cannot be none')
    for prop_key in props:
        key_str = str(prop_key)
        if props[prop_key] is not None and hasattr(model, key_str):
            # update the properties to db
            setattr(model, key_str, props[prop_key])


def purge_out_data(properties, fields=None, exclude_fields=None):
    if type(properties) is dict:
        if fields is None or not isinstance(fields, list):
            fields = []
        if not isinstance(exclude_fields, list):
            exclude_fields = []
        fields.append('partner_id')
        fields.append('_id')
        if '_id' in properties:
            properties['id'] = properties['_id']
        for field in properties:
            if (field.endswith('_pinyin') or field.endswith('_pinyin_initial')) and field not in exclude_fields:
                fields.append(field)
        for field in fields:
            if field in properties:
                del properties[field]


class DataStringify(object):
    def __init__(self):
        pass

    def do(self, obj, count=0):
        if count > 50:  # 大于50层直接退出
            return None
        count += 1
        if obj is None:
            return None
        if isinstance(obj, list):
            for idx, val in enumerate(obj):
                obj[idx] = self.do(val, count=count)
            # for item in obj:
            #     self.do(item)
            return obj
        elif isinstance(obj, dict):
            for key in obj:
                obj[key] = self.do(obj[key], count=count)
            return obj
        # elif isinstance(obj, (int, long)) and len(str(obj)) > 18:
        #     return str(obj)
        elif isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.strftime('%Y-%m-%d')
        elif isinstance(obj, ObjectId):
            return str(obj)
        else:
            return obj


data_stringify = DataStringify()


class ObjectToJson(object):
    def __init__(self, cls):
        self.cls = cls

    def do(self, obj, level=1, exclude_fields=None, add_has_fields=None):
        """
        1.if "exclude_fields_level" is equal to the current "level", the field names in "exclude_fields" will be removed from "obj",
        2."add_has_fields" is a list of fields needing to be checked and adding a new field with a prefix 'has_' to identify the existing:
            e.g.: for add_had_fields=['password'], if the obj has 'password', it will attached with a new field 'has_password=True'
        :param obj:
        :param level:
        :param exclude_fields:
        :param exclude_fields_level:
        :return:
        """
        exclude_fields = exclude_fields if isinstance(exclude_fields, list) else []
        add_has_fields = add_has_fields if isinstance(add_has_fields, list) else []
        if level > 50:  # 大于50层直接退出
            return None
        if obj is None:
            return None
        if isinstance(obj, list):
            for idx, val in enumerate(obj):
                obj[idx] = self.do(val, level=level + 1, exclude_fields=exclude_fields, add_has_fields=add_has_fields)
            # for item in obj:
            #     self.do(item)
            return obj
        elif isinstance(obj, dict):
            for field in add_has_fields:
                new_field = 'has_%s' % field
                if field in obj and obj[field] is not None and new_field not in obj:
                    obj[new_field] = True
            for field in exclude_fields:
                if field in obj:
                    del obj[field]
            for key in obj:
                obj[key] = self.do(obj[key], level=level + 1, exclude_fields=exclude_fields,
                                   add_has_fields=add_has_fields)
            return obj
        elif isinstance(obj, self.cls):
            return self.do(obj.props, level=level + 1, exclude_fields=exclude_fields, add_has_fields=add_has_fields)
        else:
            return obj


def check_bool_from_str(s):
    if isinstance(s, bool):
        return s
    # if isinstance(s, basestring):
    if isinstance(s, str):
        tmp = encodeUTF8(s)
        if tmp is None:
            return False
        return tmp.strip().upper() == "TRUE"
    return False


def check_boolean_from_querystring(querystring, key):
    if querystring and isinstance(querystring, dict) and key in querystring:
        value = querystring[key]
        if check_bool_from_str(value):
            return True
    return False


def check_stringified_from_querystring(querystring):
    return check_boolean_from_querystring(querystring, 'stringified')


def convert_to_int(s):
    try:
        value = int(s)
    except:
        value = None
    return value


def convert_to_float(s):
    try:
        value = float(s) if not isinstance(s, float) else s
    except:
        value = None
    return value


def convert_to_datetime(s):
    try:
        if isinstance(s, datetime):
            return s
        if isinstance(s, date):  # datetime也是date的一种
            return datetime.strptime(str(s), '%Y-%m-%d')
        value = parser.parse(s)
    except:
        value = None
    return value


def check_bool_from_str(s):
    if isinstance(s, bool):
        return s
    if isinstance(s, str):
        tmp = str(s)
        if tmp is None:
            return False
        return tmp.strip().upper() == "TRUE"
    return False


def convert_to_decimal(s, scale=None):
    """
    :param s: 传入值
    :param scale: 精确值 数字
    :return:
    """
    try:
        value = Decimal(str(s))
    except:
        value = None
    return value


def get_updated_fields(old, new):
    fields_from = dict()
    fields_to = dict()
    filter_fields = ['id', 'partner_id', 'updated_by', 'created_by', 'updated', 'created', 'data_state']
    if isinstance(old, dict) and isinstance(new, dict):
        old_tmp = copy.deepcopy(old)
        new_tmp = copy.deepcopy(new)
        for field in new_tmp:
            if field not in filter_fields:
                if field not in old_tmp:
                    fields_to[field] = new_tmp[field]
                else:
                    if isinstance(old_tmp[field], dict) and isinstance(new_tmp[field], dict):
                        for sub_field in new_tmp[field]:
                            if sub_field not in old[field]:
                                if field not in fields_to:
                                    fields_to[field] = {}
                                fields_to[field][sub_field] = new_tmp[field][sub_field]
                            elif old_tmp[field][sub_field] != new_tmp[field][sub_field]:
                                if field not in fields_from:
                                    fields_from[field] = {}
                                if field not in fields_to:
                                    fields_to[field] = {}
                                fields_from[field][sub_field] = old_tmp[field][sub_field]
                                fields_to[field][sub_field] = new_tmp[field][sub_field]
                    elif old_tmp[field] != new_tmp[field]:
                        fields_from[field] = old_tmp[field]
                        fields_to[field] = new_tmp[field]
    return fields_from, fields_to


def get_merge_properties(from_dict, to_dict):
    props_tmp = {}
    from_dict_tmp = copy.deepcopy(from_dict)
    to_dict_tmp = copy.deepcopy(to_dict)
    for key in to_dict_tmp:
        props_tmp[key] = to_dict_tmp[key]
    for key in from_dict_tmp:
        if key in props_tmp and isinstance(props_tmp[key], dict) and isinstance(from_dict_tmp[key], dict):
            for sub_key in from_dict_tmp[key]:
                props_tmp[key][sub_key] = from_dict_tmp[key][sub_key]
        else:
            props_tmp[key] = from_dict_tmp[key]
    return props_tmp


def check_region(region):
    if region not in [
        'market', 'distribution', 'attribute', 'formula', 'purchase', 'transfer', 'branch', 'geo', 'order',
        'output-tax', 'einvoice-sales'
    ]:
        raise DataValidationException('NOT_FOUND', error_msg='not able to find region %s' % region)
    if region == 'branch':
        return region
    elif region == 'output-tax':
        return 'output_tax'
    elif region == 'einvoice-sales':
        return 'einvoice_sales'
    else:
        return region + '_region'


def read_str_from_sheet(sheet, i, j):
    return str(int(sheet.cell(i, j).value)).encode('utf-8').strip() if isinstance(sheet.cell(i, j).value, float) \
        else sheet.cell(i, j).value.encode('utf-8').strip()


def check_job_status(status):
    status_dict = dict(approve='APPROVED', submit='SUBMITTED', cancel='CANCELLED', reject='REJECTED')
    if status not in status_dict:
        raise DataValidationException('INVALID_STATUS', error_msg='invalid status: %s' % status)
    return status_dict[status]


# nsq_publish
def publish(topic, message, defer=False):
    message_tmp = json.dumps(message, cls=CJsonEncoder) if isinstance(message, dict) else message
    nsq_service = NSQService()
    if isinstance(message_tmp, list):
        message_tmp = [json.dumps(m, cls=CJsonEncoder) if isinstance(m, dict) else m for m in message_tmp]
    if isinstance(message, list):
        return nsq_service.m_publish(topic, message_tmp, defer)
    else:
        return nsq_service.publish(topic, message_tmp, defer)


# nsq_message
class MessageTopic(object):
    SUPPLY_CODE = APP_CONFIG['supply_code']
    SUPPLY_GROUP = 'boh_order'
    STOCKTAKE_GROUP = 'stocktake'
    STOCKTAKE_CHANNEL = '{}.{}'.format(SUPPLY_CODE, STOCKTAKE_GROUP)
    STOCKTAKE_BATCH_INIT_TOPIC = '{}.stocktake.batch.init'.format(SUPPLY_CODE)
    STOCKTAKE_STORE_INIT_TOPIC = '{}.stocktake.store.init'.format(SUPPLY_CODE)
    STOCKTAKE_GENERATE_TOPIC = '{}.stocktake.generate'.format(SUPPLY_CODE)
    # 盘点bom拆解topic
    STOCKTAKE_BOM_TOPIC = '{}.stocktake.bom'.format(SUPPLY_CODE)
    STOCKTAKE_MONTH_JDE_TOPIC = '{}.stocktake.month.jde'.format(SUPPLY_CODE)
    RE_HANDLE_STOCKTAKE_BOM_TOPIC = '{}.rehandle.stocktake.bom'.format(SUPPLY_CODE)
    UPDATE_STOCKTAKE_DIFF_TOPIC = '{}.update.stocktake.diff'.format(SUPPLY_CODE)
    UPDATE_STORE_STOCKTAKE_DIFF_TOPIC = '{}.update.store.stocktake.diff'.format(SUPPLY_CODE)

    ADJUST_GROUP = 'adjust'
    ADJUST_CHANNEL = '{}.adjust'.format(SUPPLY_CODE)
    ADJUST_BATCH_STORE_INIT_TOPIC = '{}.adjust.batch.store.init'.format(SUPPLY_CODE)
    ADJUST_GENERATE_TOPIC = '{}.adjust.generate'.format(SUPPLY_CODE)
    ADJUST_AUTO_CLOSE_TOPIC = '{}.adjust.auto.close'.format(SUPPLY_CODE)
    ADJUST_AUTO_CONFIRM_TOPIC = '{}.adjust.auto.confirm'.format(SUPPLY_CODE)
    ADJUST_BOM_REPORT_TOPIC = '{}.adjust.bom.report'.format(SUPPLY_CODE)

    DEMAND_GROUP = 'demand'
    DEMAND_CHANNEL = '{}.demand'.format(SUPPLY_CODE)
    # 门店订货单生成
    DEMAND_BATCH_INIT_TOPIC = '{}.{}.batch.init'.format(SUPPLY_CODE, DEMAND_GROUP)
    DEMAND_BATCH_STORE_INIT_TOPIC = '{}.{}.store.init'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 检查订货单状态
    DEMAND_CHECK_TOPIC = '{}.{}.check.status'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 门店要货单生成
    DEMAND_STORE_DEMAND_ORDER_TOPIC = '{}.{}.order.generate'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 发送直送单(定时任务)
    HANDLE_SEND_PUR_ORDER_TOPIC = '{}.{}.check_pur_order.send'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 订货单成品原料拆减
    DEMAND_PARSE_BOM_TOPIC = '{}.{}.parse.bom'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 重发订货失败单据
    RESEND_DEMAND_TOPOC = '{}.{}.resend'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 捞取未拆单的订货单
    RECOVER_GENERATE_DEMAND_ORDER = '{}.demand.recover.undone'.format(SUPPLY_CODE)
    # nsq消息回冲库存
    OFFSET_INVENTORY_TOPIC = 'supply.offset.inventory'

    DEMAND_SUGGEST_GROUP = 'demand_suggest'
    DEMAND_SUGGEST_CHANNEL = '{}_demand_suggest'.format(SUPPLY_CODE)
    DEMAND_SUGGEST_FORECAST_CHECK = '{}.{}.check_all_store_forecast'.format(SUPPLY_CODE, DEMAND_SUGGEST_GROUP)
    DEMAND_SUGGEST_FORECAST_STORE_CREATE = '{}.{}.create_store_forecast'.format(SUPPLY_CODE, DEMAND_SUGGEST_GROUP)

    # 获取需要作废的订单
    GET_INVALID_ORDER_TOPIC = '{}.{}.get.invalid.order'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 作废订单
    CANCEL_INVALID_ORDER_TOPIC = '{}.{}.cancel.invalid.order'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 自动审核订货单
    AUTO_APPROVED_ORDER_TOPIC = '{}.{}.auto.approve.order'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 获取需要作废的收货单
    GET_INVALID_REC_TOPIC = '{}.{}.get.invalid.receiving'.format(SUPPLY_CODE, DEMAND_GROUP)
    # 作废收货单
    CANCEL_INVALID_REC_TOPIC = 'supply.cancel.invalid.receiving'
    # 获取需要作废的要货单
    GET_INVALID_DEMAND_TOPIC = 'supply.get.invalid.demand.order'
    # 作废收货单
    CANCEL_INVALID_DEMAND_TOPIC = 'supply.cancel.invalid.demand.order'

    AUTO_RECEIVE_GROUP = 'auto_rec'
    AUTO_RECEIVE_CHANNEL = '{}_auto_rec'.format(SUPPLY_CODE)
    # 获取需要未收货的单子
    GET_UNRECEIVED_REC_TOPIC = '{}.auto_rec.get.rec.unreceived'.format(SUPPLY_CODE)
    # 自动收货
    AUTO_RECEIVE_BY_ID_TOPIC = '{}.auto_rec.by.id'.format(SUPPLY_CODE)
    # 配送差异单自动退机
    GET_UNCONFIRMED_DIFF_TOPIC = '{}.auto_conf.get.diff.unconfirmed'.format(SUPPLY_CODE)
    GET_UNCONFIRMED_DIFF_OVER_DAY_TOPIC = '{}.auto_conf.get.unconfirm.diff.over.day'.format(SUPPLY_CODE)
    GET_AUTO_CONFIRM_DIFF_TOPIC = '{}.auto_conf.get.diff'.format(SUPPLY_CODE)
    AUTO_CONFIRM_DIFFS_TOPIC = '{}.auto.confirm.diffs'.format(SUPPLY_CODE)
    # 退货单自动推进
    GET_UNFINISHED_RETURNS_TOPIC = '{}.auto_conf.get.returns.unifinished'.format(SUPPLY_CODE)
    AUTO_CONFIRM_RETURNS_TOPIC = '{}.auto_conf.confirm.returns'.format(SUPPLY_CODE)
    AUTO_CANCEL_RETURNS_TOPIC = '{}.auto_conf.cancel.returns'.format(SUPPLY_CODE)
    # 报废单自动推进
    GET_UNFINISHED_ADJUST_TOPIC = '{}.auto_conf.get.adjust.unifinished'.format(SUPPLY_CODE)
    AUTO_CANCEL_ADJUST_TOPIC = '{}.auto_conf.cancel.adjust'.format(SUPPLY_CODE)
    # 自采单自动推进
    GET_UNFINISHED_SELF_PICKING_TOPIC = '{}.auto_conf.get.self_picking.unifinished'.format(SUPPLY_CODE)
    AUTO_CHANGE_SELF_PICKING_TOPIC = '{}.auto_conf.change.self_picking'.format(SUPPLY_CODE)
    # 调拨单自动推进
    AUTO_CONFIRM_TRANSFER = '{}.auto_conf.get.transfer'.format(SUPPLY_CODE)
    AUTO_CONFIRM_TRANSFER_BY_ID = '{}.auto_conf.transfer.byid'.format(SUPPLY_CODE)
    AUTO_CANCEL_TRANSFER_BY_ID = '{}.auto_conf.cancel.transfer'.format(SUPPLY_CODE)

    # 获取挂起状态单据
    GET_SUSPEND_REC_TOPIC = '{}.auto.get.suspend.rec'.format(SUPPLY_CODE)
    # 未到店状态重置
    AUTO_RESET_STATUS_BY_ID_TOPIC = '{}.auto.reset.rec.by.id'.format(SUPPLY_CODE)
    # 物料转换
    MATERIAL_CONVERT_CHANNEL = '{}.material_convert'.format(SUPPLY_CODE)
    AUTO_MATERIAL_CONVERT = '{}.auto.material.convert'.format(SUPPLY_CODE)
    # 获取新建状态的盘点单 
    GET_STOCKTAKE_LIST = '{}.get.stocktake'.format(SUPPLY_CODE)

    # 库存channel
    SUPPLY_INVENTORY_GROUP = 'supply_inventory_group'
    INVENTORY_CHANNEL = 'supply_inventory'
    # 库存扣减topic
    INVENTORY_CALCULATE_TOPIC = 'boh.supply.inventory.calculate.topic'
    # 库存扣减补偿topic
    RE_INVENTORY_CALCULATE_TOPIC = 'boh.supply.inventory.re_calculate.topic'
    # 每日库存切片topic
    INVENTORY_DAILY_CUT_TOPIC = 'boh.supply.inventory.daily.cut'
    WAREHOUSE_INVENTORY_DAILY_CUT_TOPIC = 'boh.supply.inventory.daily.cut.warehouse'
    COST_CENTER_INVENTORY_DAILY_CUT_TOPIC = 'boh.supply.inventory.daily.cut.costcenter'
    INVENTORY_GET_PRODUCTS_TOPIC = 'boh.supply.inventory.get.products'
    WAREHOUSE_INVENTORY_GET_PRODUCTS_TOPIC = 'boh.supply.inventory.get.products.warehouse'
    COST_CENTER_INVENTORY_GET_PRODUCTS_TOPIC = 'boh.supply.inventory.get.products.costcenter'
    INVENTORY_DAILY_CUT_BY_STORE_TOPIC = 'boh.supply.inventory.daily.cut.by.store'
    COST_CENTER_STORE_TOPIC = 'boh.supply.inventory.costcenter.store'
    TRIGGER_COST_ALL_TOPIC = 'boh.supply.cost.trigger.all'
    TRIGGER_COST_TASK_TOPIC = 'boh.supply.cost.trigger.one'
    TRIGGER_COST_COUNT_MATERIAL_TOPIC = 'boh.supply.cost.engine.trigger.material'
    PRE_TRIGGER_COST_COUNT_MATERIAL_TOPIC = 'boh.supply.cost.engine.trigger.material.pre'
    TRIGGER_COST_RECOUNT_MATERIAL_TOPIC = 'boh.supply.cost.engine.trigger.material.recount'
    TRIGGER_COST_COUNT_BOM_TOPIC = 'boh.supply.cost.engine.trigger.bom'
    TRIGGER_COST_RECOUNT_BOM_TOPIC = 'boh.supply.cost.engine.trigger.bom.recount'
    TRIGGER_COST_CLOSE_TOPIC = 'boh.supply.cost.close.count.task'
    INVENTORY_BATCH_PROCESS_COMPLETE = 'INVENTORY_BATCH_PROCESS_COMPLETE'
    RECOVER_INVENTORY_DAILY_CUT = 'boh.supply.recover.inventory.daily.cut'

    # 接收单据工作流消息topic
    FLOW_TASK_GROUP = 'boh.supply_flow_task'
    HANDLE_FLOW_TASK = 'boh.supply.handle.flow.task'
    HANDLE_FLOW_TASK_CHANNEL = 'boh.supply.handle_flow_task_channel'

    # 补偿工作流topic
    RETRY_HANDLE_FLOW_TASK = 'boh.supply.retry_handle_flow_task'
    # 轮询补偿工作流批次topic
    RETRY_HANDLE_FLOW_TASK_BATCH = 'boh.supply.retry_handle_flow_task_batch'
    RETRY_HANDLE_FLOW_TASK_CHANNEL = 'boh.supply.retry_handle_flow_task_channel'

    # jde,boh库存比对
    INVENTORY_DIFF_TOPIC = 'boh.supply.inventory.diff.topic'
    INVENTORY_DIFF_STORE_TOPIC = 'boh.supply.inventory.diff.store.topic'

    # 更新主档表topic
    METADATA_CHANNEL = '{}_metadata'.format(SUPPLY_CODE)
    UPDATE_METADATA_TOPIC = '{}.update.metadata.topic'.format(SUPPLY_CODE)

    # 批量BOM拆分topic
    BOM_CHANNEL = '{}.bom'.format(SUPPLY_CODE)
    BOM_VENOM_EXPLAIN = '{}.bom.venom.explain'.format(SUPPLY_CODE)

    # 初始化账期
    INIT_PERIOD_TOPIC = 'boh.supply.init_period.topic'
    # 账期初始化补偿
    RECOVER_PERIOD_BATCH_TOPIC = 'boh.supply.recover_init_period.batch.topic'

    # 清理首页待办单据缓存
    CLEAN_TODO_CACHE_TOPIC = 'boh.supply.clean_todo_cache.topic'

    # 订阅推送消息，PDA等
    MESSAGE_SERVER = 'message_server'

    # 同步采购已复核单据给成本中心
    PURCHASE_REVIEW_CHANNEL = 'purchase_review_channel'
    SEND_TO_COST_CENTER = 'boh.supply.send_to_cost_center.topic'
    SEND_TO_COST_CENTER_CHECK = 'boh.supply.send_to_cost_center_check.topic'
    SYNC_COST_CENTER_CHECK_BATCH = 'boh.supply.sync_cost_center_check_batch.topic'

    # 采购复核创建调价单补偿
    PURCHASE_REVIEW_CREATE_ADJUSTMENT_RECOVER = 'boh.supply.pur_rw_create_adjustment_recover.topic'

    # 加工费用单费用分摊任务处理
    PROCESSING_CENTER_CHANNEL = 'processing_center_channel'
    PROCESSING_COST_ALLOCATION = 'boh.supply.processing_cost_allocation.topic'
    PROCESSING_COST_ALLOCATION_RECOVER = 'boh.supply.processing_cost_allocation_recover.topic'

    # 导出服务
    EXPORT_PURCHASE_DETAILS = 'boh.supply.export.purchase'
    EXPORT_DEMAND_DETAILS = 'boh.supply.export.demand'
    EXPORT_MACHINING_REALTIME = 'boh.supply.export.machining-realtime'  # 加工业务-实时库存导出
    EXPORT_WAREHOUSE_REALTIME = 'boh.supply.export.warehouse-realtime'  # 仓库运营-实时库存导出
    EXPORT_STORE_REALTIME = 'boh.supply.export.store-realtime'          # 门店运营-实时库存导出
    EXPORT_MACHINING_INVENTORY_SUMMARY = 'boh.supply.export.machining-inventory-summary'    # 加工业务-时段汇总库存导出
    EXPORT_WAREHOUSE_INVENTORY_SUMMARY = 'boh.supply.export.warehouse-inventory-summary'    # 仓库运营-时段汇总库存导出
    EXPORT_STORE_INVENTORY_SUMMARY = 'boh.supply.export.store-inventory-summary'            # 门店运营-时段汇总库存导出

    # 同步三方的单子
    SEND_TO_TP = 'boh.receipt.sendToTp'                                                             # 触发处理向第三方发送的单据
    RESEND_TO_TP = 'boh.supply.resendToTp'                                                         # 触发处理向第三方重发的单据
    SYNC_RECEIPT_TO_TP = 'boh.receipt.syncReceiptToTp'                                              # 向第三方同步单据
    ACK_FROM_TP = 'integraion.receipt.ackToBoh'                                                     # 接收第三方的回应

    # 加盟商订货单拆单
    HANDLE_FRANCHISEE_DEMAND = 'boh.supply.handle_franchisee_demand.topic'
    RECOVER_HANDLE_FRANCHISEE_DEMAND = 'boh.supply.recover_handle_franchisee_demand.topic'

    HANDLE_FRANCHISEE_REFUND = 'boh.supply.handle_franchisee_refund.topic'
    RECOVER_HANDLE_FRANCHISEE_REFUND = 'boh.supply.recover_handle_franchisee_refund.topic'

    HANDLE_BATCH_OSS_FRANCHISEE_REFUND = 'boh.supply.handle_batch_oss_frs_refund.topic'
    RECOVER_GENERATE_OSS_FRANCHISEE_REFUND = 'boh.supply.recover_generate_oss_frs_refund.topic'
    HANDLE_FRANCHISEE_DEMAND_DISTRIBUTE = 'boh.supply.handle_frs_demand_distribute.topic'
    RECOVER_FRANCHISEE_DEMAND_DISTRIBUTE = 'boh.supply.recover_frs_demand_distribute.topic'

    # CreditPayTopic boh信用付服务topic
    CREDIT_PAY_TOPIC = "boh_credit_pay"
    # GiveVouchersTag 发放代金券
    GIVE_VOUCHERS_TAG = "boh_credit_pay_give_vouchers"

    # 临时关闭订货到时自动关闭任务
    CLOSE_ORDER_AUTO_TURN_OFF  = 'boh.supply.meta.close_order.auto.turn_off'


def datetime_2_timestamp(date_time):
    if isinstance(date_time, datetime):
        date_time = Timestamp(seconds=int(date_time.timestamp()))
    else:
        date_time = None
    return date_time


def get_today_str() -> str:
    """
    获取营业日期字符串: 2019-01-10 00:00:00
    """
    return date.fromtimestamp(int(datetime.utcnow().timestamp())).strftime('%Y-%m-%d %H:%M:%S')


def get_today_str_2() -> str:
    """
    获取营业日期字符串: 2019-01-10
    """
    return date.fromtimestamp(int(datetime.utcnow().timestamp())).strftime('%Y-%m-%d')


def get_today_datetime(utc=False, plus_day=0) -> datetime:
    """
    获取营业日期datetime: datetime.datetime(2019, 1, 19, 0, 0)
    """
    today = date.today()
    result = None
    if utc:
        result = datetime(year=today.year, month=today.month, day=today.day, tzinfo=timezone.utc)
    else:
        result = datetime(year=today.year, month=today.month, day=today.day)
    if plus_day:
        deltaday = timedelta(days=plus_day)
        result = result + deltaday
    return result


def translate_utc_time(utc_time):
    delta_time = time.strftime("%z", time.gmtime())
    delta_time = int(delta_time)/100
    if utc_time.endswith("Z"):
        utc_format = '%Y-%m-%dT%H:%M:%SZ'
        return datetime.strptime(utc_time, utc_format) + timedelta(hours=delta_time)
    else:
        utc_format = '%Y-%m-%d %H:%M:%S'
        return datetime.strptime(utc_time, utc_format)


def translate_to_datetime(t):
    try:
        t_format = '%Y-%m-%d'
        return datetime.strptime(t, t_format)
    except Exception as e:
        pass

    try:
        t_format = '%Y-%m-%d %H:%M:%S'
        return datetime.strptime(t, t_format)
    except Exception as e:
        pass

    return None


def translate_date(t):
    t_format = '%Y-%m-%d'
    return datetime.strptime(t, t_format)

def entity_date_time_to_python(time_str):
    if time_str:
        time_str = time_str.replace('Z', '').replace('+08:00', '')
        return datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%S')
    return None


def list2dict(lt=None, key='id', include_keys=None):
    '''
    convert list[dict] to dict
    if not include_keys: include all keys
    '''

    if not isinstance(lt, list):
        raise TypeError
    elif len(lt) > 0 and not isinstance(lt[0], dict):
        raise TypeError
    res = {}
    if include_keys:
        for l in lt:
            res[str(l[key])] = {d: l[d] for d in l if d in include_keys}
    else:
        for l in lt:
            res[str(l[key])] = l
    return res


def datetime_to_strdate(d):
    # 返回示例: '2019-02-22 00:00:00'
    if not d:
        return None
    try:
        return datetime.strftime(d, '%Y-%m-%d 00:00:00')
    except Exception as e:
        return None


def datetime_to_strdatetime(d):
    if not d:
        return None
    try:
        return datetime.strftime(d, '%Y-%m-%d %H:%M:%S')
    except Exception as e:
        return None


def datetime_to_strdate2(d):
    # 返回示例: '2019-02-22'
    if not d:
        return None
    try:
        return datetime.strftime(d, '%Y-%m-%d')
    except Exception as e:
        return None


def datetime2str(d, sub='day', offset=0, split=True):
    date_dict = {'day': '%Y-%m-%d', 'month': '%Y-%m', 'year': '%Y', 'second': '%Y-%m-%d %H:%M:%S'}
    try:
        if isinstance(d, datetime):
            value = datetime.strftime(d + timedelta(offset), date_dict.get(sub, '%Y-%m-%d'))
        else:
            value = d

        if not split:
            value = value.replace('-', '').replace(' ', '').replace(':', '')
    except:
        value = ''
    return value


def convert2date(d, offset=0):
    if isinstance(d, date):
        res = d
    elif isinstance(d, datetime):
        res = d.date()
    # elif isinstance(d, str) or isinstance(d, unicode):
    elif isinstance(d, str):
        res = datetime.strptime(d, '%Y-%m-%d').date()
    else:
        res = datetime.utcnow().date()
    return res + timedelta(days=offset)


def get_data_by_type(data, data_type=list):
    if not data:
        res = None
    else:
        if isinstance(data, data_type):
            res = data
        elif isinstance(data, Iterable):
            for k, v in enumerate(data):
                if isinstance(v, data_type):
                    res = v
                    break
        else:
            res = None
    return res


def get_timestamp(d=None, scale=1000):
    if not d:
        d = datetime.utcnow()
    res = int(mktime(convert_to_datetime(d).timetuple()) * scale)
    return res


def singleton(cls, *args, **kw):
    instances = {}

    def _singleton(*args, **kw):
        if cls not in instances:
            instances[cls] = cls(*args, **kw)
        return instances[cls]

    return _singleton


def funcname():
    return inspect.stack()[1][3]


class Singleton(type):
    '''
    Example:
    class Myclass(object):
        __metaclass__ = Singleton

    a = MyClass(1)
    b = MyClass(2)

    a == b # True
    id(a) == id(b) # True
    '''

    def __init__(cls, name, bases, dict):
        super(Singleton, cls).__init__(name, bases, dict)
        cls._instance = None

    def __call__(cls, *args, **kw):
        if cls._instance is None:
            cls._instance = super(Singleton, cls).__call__(*args, **kw)
        return cls._instance


def scale(data, rate=100, default=None, keys=None):
    if isinstance(data, dict) and keys:
        value = copy.deepcopy(data)
        for k in keys:
            if k in value:
                value[k] = scale(value[k], rate=rate, default=default, keys=keys)
    elif isinstance(data, list) and keys:
        value = [scale(d, rate=rate, default=default, keys=keys) for d in data]
    else:
        try:
            value = float(data) / rate
        except:
            value = default if default else data
    return value


def update_data(data, info):
    if not isinstance(data, dict) or not isinstance(info, dict):
        return
    for k, v in info.items():
        if k in data and isinstance(v, dict):
            update_data(data[k], v)
        else:
            data[k] = v


def get_today_with_date():
    return datetime.utcnow().date()


def get_today_with_datetime():
    return datetime(datetime.utcnow().year, datetime.utcnow().month, datetime.utcnow().day)


def get_bus_date() -> datetime:
    """
    获取营业日的时间戳: eg:2022-06-28 16:00:00
    :return:
    """
    tz = pytz.timezone("Asia/Shanghai")
    today = datetime.now(tz=tz)
    return datetime(year=today.year, month=today.month, day=today.day) - timedelta(hours=8)


def load_list(rows, start=0, step=50, stop=None):
    index = start
    stop = stop or len(rows)
    while index < stop:
        yield rows[index:index + step]
        index += step


def check_is_distr(circle_type, today=None, interval_days=None, start_date=None, cycles=None, arrival_days=0):
    """
    判断是否可以门市订货
    Args:
        today: 订货日(任意日期, 默认今天)
        circle_type: 订货周期类型
        interval_days: 按天订货，隔几天可订
        start_date:    按天订货，起始日期
        days_of_week:  按周订货，周订货日
        days_of_month: 按月订货，月订货日
        arrival_days:  按天订货的到货日期
    """
    if not today:
        today = get_today_datetime()

    weekday = today.weekday() + 1
    monthday = today.day

    need_flag = False
    if circle_type == "D":
        # 按天配送
        if interval_days and start_date:
            start_date = translate_utc_time(start_date)
            start_date = datetime(year=start_date.year, month=start_date.month, day=start_date.day)
            gap_day = (today - start_date).days
            gap_day = str(gap_day / (int(interval_days) + 1))
            if str(gap_day).endswith(".0"):
                need_flag = True
            elif gap_day == '0':
                need_flag = True
        else:
            need_flag = True
    elif circle_type == "W":
        # 按周配送
        if cycles:
            for day in cycles:
                if int(day.get("order_date")) == weekday:
                    need_flag = True
                    arrival_days = int(day.get("planned_arrival_days", arrival_days))
                    break
    elif circle_type == "M":
        # 按月配送
        if cycles:
            for day in cycles:
                if int(day.get("order_date")) == monthday:
                    need_flag = True
                    arrival_days = int(day.get("planned_arrival_days", arrival_days))
                    break
                max_day_on_current_month = calendar.monthrange(today.year, today.month)[1]
                temp_day = today.day - max_day_on_current_month - 1
                if temp_day == int(day.get("order_date")):
                    need_flag = True
                    arrival_days = int(day.get("planned_arrival_days", arrival_days))
                    break
    return need_flag, arrival_days


def handle_list_model_to_message(dict_models: list, class_name):
    need_field = [f.name for f in class_name.DESCRIPTOR.fields]
    rows = []
    for obj in dict_models:
        rows.append(handle_model_to_message(obj, class_name, need_field))
    return rows


def dict_to_pb_message(dict_data, message_class) -> object:
    need_fields = set(f.name for f in message_class.DESCRIPTOR.fields)
    kwargs = {}
    for key, value in dict_data.items():
        if key in need_fields:
            if isinstance(value, datetime):
                value = Timestamp(seconds=int(value.timestamp()))
            if isinstance(value, Decimal):
                value = float(value)
            kwargs[key] = value
    return message_class(**kwargs)


def handle_model_to_message(model: dict, class_name, need_field=None):
    """
    model: 数据字典
    class_name: message类型
    need_field: class_name的field列表，如果不传则从class_name获取
    """
    if not need_field:
        need_field = [f.name for f in class_name.DESCRIPTOR.fields]
    ret = {key: value for (key, value) in model.items() if key in need_field}
    return class_name(**ret)


# 根据最大最小递增定量计算数量
def get_quantity(quantity, max_quantity, min_quantity, inc_quantity, mode="down", round_num=3):
    if quantity == 0:
        return 0
    if inc_quantity == 0:
        return round(quantity, round_num)

    quantity = float(quantity)
    max_quantity = float(max_quantity)
    min_quantity = float(min_quantity)
    inc_quantity = float(inc_quantity)
    quantity = round(quantity, round_num)
    max_quantity = round(max_quantity, round_num)
    min_quantity = round(min_quantity, round_num)
    inc_quantity = round(inc_quantity, round_num)
    round_quantity = 10 ** round_num
    if mode == "down":
        # 取小，向下取整
        num = math.floor((quantity * round_quantity - min_quantity * round_quantity) / (inc_quantity * round_quantity))
    else:
        # 取大，向上取整
        num = math.ceil((quantity * round_quantity - min_quantity * round_quantity) / (inc_quantity * round_quantity))
    result = (num * inc_quantity * round_quantity + min_quantity * round_quantity) / round_quantity
    if result > max_quantity:
        result = max_quantity
    if result < min_quantity:
        result = min_quantity
    return round(result, round_num)


def branch_list_scope_check(branch_ids=None, partner_id=None, user_id=None, branch_type=None):

    if (not partner_id) or (not user_id):
        raise StoreScopeException("数据权限校验失败，请传入partner_id:{}, user_id:{}".format(
            partner_id, user_id))

    branch_scope = {}
    if branch_type == 'S':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'store')
    elif branch_type == 'W':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'warehouse')
    elif branch_type == 'M':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'manufactory')
    else:
        logging.info("lose_branch_type")
        return branch_ids

    if branch_scope.get('full_access'):
        branch_list = branch_ids
    else:
        scope_ids = branch_scope.get('ids', [])
        branch_list = list(set(scope_ids))
    if branch_ids:
        branch_list = list(set(branch_list) & set(branch_ids))
        if branch_list == []:
            raise StoreScopeException("数据权限校验失败，权限不足" + str(branch_ids))
    return branch_list


def store_scope_check(store_id, partner_id, user_id):
    if not store_id or not partner_id or not user_id:
        raise StoreScopeException("数据权限校验失败，请传入store_id:{}, partner_id:{}, user_id:{}".format(
            store_id, partner_id, user_id))

    store_scope_ids = Redis_cli.get_store_data_scope(partner_id, user_id)
    if store_scope_ids == "full":
        return True
    if store_id in store_scope_ids:
        return True
    else:
        raise StoreScopeException("数据权限校验失败，权限不足, store_id:" + str(store_id))


def branch_scope_check(branch_type=None, branch_id=None, partner_id=None, user_id=None):
    """各业务校验用户权限
    推荐使用通用方法，校验各个单个组织的权限, 之后业务扩展只需要加一个分支判断即可
    门店/仓库/加工中心
    :param branch_type: STORE/WAREHOUSE/MACHINING_CENTER
    :param branch_id: 各组织id
    :param partner_id
    :param user_id
    """

    if not all([branch_type, branch_id, partner_id, user_id]):
        raise StoreScopeException("数据权限校验失败，请传入branch_type: {}, branch_id:{}, partner_id:{}, user_id:{}".format(
            branch_type, branch_id, partner_id, user_id))
    if branch_type == 'STORE':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'store')
    elif branch_type == 'WAREHOUSE':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'warehouse')
    elif branch_type == 'MACHINING_CENTER':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'manufactory')
    else:
        logging.info("lose_branch_type")
        return True

    if branch_scope.get('full_access'):
        return True
    if int(branch_id) in branch_scope.get('ids'):
        return True
    else:
        raise StoreScopeException("数据权限校验失败，权限不足" + str(branch_id))


def branch_scope_check_list(branch_type=None, branch_ids=None, partner_id=None, user_id=None):
    """各业务推荐使用通用方法，校验各个组织列表的权限, 之后业务扩展只需要加一个分支判断即可
        门店/仓库/加工中心
        :param branch_type: STORE/WAREHOUSE/MACHINING_CENTER
        :param branch_ids: 各组织id列表
        :param partner_id
        :param user_id
    """

    if not partner_id or user_id:
        raise StoreScopeException("数据权限校验失败，请传入partner_id:{}, user_id:{}".format(partner_id, user_id))

    branch_scope = {}
    branch_list = []
    if branch_type == 'STORE':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'store')
    elif branch_type == 'WAREHOUSE':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'warehouse')
    elif branch_type == 'MACHINING_CENTER':
        branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, 'manufactory')
    else:
        logging.info("lose_branch_type")
        return branch_ids

    if branch_scope.get('full_access'):
        branch_list = branch_ids
    else:
        scope_ids = branch_scope.get('ids', [])
        branch_list = list(set(scope_ids))
    if branch_ids:
        branch_list = list(set(branch_list) & set(branch_ids))
        if branch_list == []:
            raise StoreScopeException("数据权限校验失败，权限不足" + str(branch_ids))
    return branch_list


def get_region_and_store_feilds_scope_check(partner_id, user_id, region_id=None):
    # 区域数据权限ids字符,筛选一个region_id
    ret = metadata_service.get_store_scope(donot_transfer_branch_to_store=True,
                                           partner_id=partner_id,
                                           user_id=user_id)
    region_feilds = None
    store_feilds = None
    if ret:
        if ret.get('full_access') == True:
            if region_id:
                region_feilds = str(region_id)
            else:
                region_feilds = None
            store_feilds = None
        else:
            scope_store_ids = ret.get('scope_store_ids')
            scope_branch_ids = ret.get('scope_branch_ids')
            if scope_store_ids and len(scope_store_ids) > 0:
                store_feilds = ','.join(scope_store_ids)
            if scope_branch_ids and len(scope_branch_ids) > 0:
                if region_id:
                    if str(region_id) in scope_branch_ids:
                        region_feilds = str(region_id)
                    else:
                        raise StoreScopeException('没有该区域权限')
                else:
                    region_feilds = ','.join(scope_branch_ids)
        return region_feilds, store_feilds
    else:
        raise StoreScopeException('拉取数据权限失败')


# 调拨权限校验公用方法
def transfer_branch_scope_check(shipping_store, receiving_store, partner_id, user_id, schema, domain=''):
    if not shipping_store or not receiving_store or not partner_id or not user_id:
        raise StoreScopeException("数据权限校验失败，请传入store_id:{}, partner_id:{}, user_id:{}".format(
            shipping_store, receiving_store, partner_id, user_id))
    scope_data = auth_permission.list_data_scope_check(partner_id=partner_id, user_id=user_id, resource_schema=schema,
                                                       domain=domain)
    if scope_data.get('full_access'):
        return True
    scope_ids = scope_data.get('ids', [])
    if str(shipping_store) in scope_ids or str(receiving_store) in scope_ids:
        return True
    else:
        raise StoreScopeException("数据权限校验失败，权限不足")


def transfer_store_list_scope_check(receiving_stores, shipping_stores, partner_id, user_id, schema='', domain=''):
    """
    :param receiving_stores:
    :param shipping_stores:
    :param partner_id:
    :param user_id:
    :param schema: 待校验组织schema["store", "warehouse", "manufactory"]
    :param domain: 权限角色
    :return:
    """
    if (not partner_id) or (not user_id):
        raise StoreScopeException("数据权限校验失败，请传入partner_id:{}, user_id:{}".format(
            partner_id, user_id))
    branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, schema, domain)
    if branch_scope.get('full_access'):
        return receiving_stores, shipping_stores
    else:
        scope_ids = branch_scope.get('ids', [])
        scope_ids = [int(i) for i in scope_ids]
        if not scope_ids:
            raise StoreScopeException("数据权限校验失败，权限不足" + str(receiving_stores + shipping_stores))

    if receiving_stores == [] and shipping_stores == [] and isinstance(scope_ids, list):
        return scope_ids, scope_ids

    receiving_stores_list = []
    shipping_stores_list = []

    if len(receiving_stores) > 0:
        for store_id in receiving_stores:
            if int(store_id) in scope_ids:
                receiving_stores_list.append(store_id)
    if shipping_stores and len(shipping_stores) > 0:
        for store_id in shipping_stores:
            if int(store_id) in scope_ids:
                shipping_stores_list.append(store_id)
    if len(receiving_stores_list) > 0 and len(shipping_stores_list) > 0:
        return receiving_stores_list, shipping_stores_list
    if len(receiving_stores_list) > 0 and len(shipping_stores_list) == 0:
        return receiving_stores_list, []
    if len(receiving_stores_list) == 0 and len(shipping_stores_list) > 0:
        return [], shipping_stores_list
    if len(receiving_stores_list) == 0 and len(shipping_stores_list) == 0:
        raise StoreScopeException("数据权限校验失败，权限不足:" + str(receiving_stores + shipping_stores))


def get_datetime_from_timestamp(value):
    timestamp = Timestamp()
    timestamp.seconds = value.seconds
    date = timestamp.ToDatetime()
    if date == datetime(1970, 1, 1):
        return None
    return date


def get_product_unit_map(product_ids=None, partner_id=None, user_id=None):
    """拉取商品主档获取map
    对应到单位map
    :param product_ids[int]--商品id列表
    :param partner_id
    :param user_id
    :return products_unit_map -- {product_id[int]:
                                        {'default':{id,name,code,rate}},
                                        {'bom': {id,name,code,rate}},
                                        {'order': {id,name,code,rate}},
                                        ....}
    """
    products_unit_map = {}
    unit_dict = {}
    units_ret = metadata_service.get_unit_list(return_fields='id,code,name',
                                               partner_id=partner_id, user_id=user_id)
    units = []
    if units_ret:
        units = units_ret['rows']
    if isinstance(units, list):
        for unit in units:
            if isinstance(unit, dict) and 'id' in unit:
                unit_dict[str(unit['id'])] = unit
    if product_ids:
        product_ret = metadata_service.get_product_list(ids=product_ids,
                                                        include_units=True,
                                                        return_fields='id',
                                                        partner_id=partner_id,
                                                        user_id=user_id).get('rows', [])
        for product in product_ret:
            unit_map = {}
            if product.get('units'):
                for unit in product['units']:
                    if unit.get('default'):
                        unit_map["default"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('order'):
                        unit_map["order"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('sales'):
                        unit_map["sales"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('bom'):
                        unit_map["bom"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('purchase'):
                        unit_map["purchase"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('stocktake'):
                        unit_map["stocktake"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
            if isinstance(product, dict) and 'id' in product:
                products_unit_map[int(product['id'])] = unit_map
    return products_unit_map


def get_utc_now(date_type=None):
    """公用方法:获取当前utc时间
    :param date_type: "date" or "datetime"
    :return: 日期类型
           "date": %Y-%m-%d
           "datetime": %Y-%m-%d %H:%M:%S
    """
    today = datetime.utcnow()
    if date_type == "date":
        result = datetime(year=today.year, month=today.month, day=today.day)
    else:
        result = datetime(year=today.year, month=today.month, day=today.day,
                          hour=today.hour, minute=today.minute, second=today.second)
    return result


def check_stores_costcenter_belonging(store_list, partner_id, user_id):
    store_costcenter_dict = metadata_service.get_branch_cost_center_map(partner_id, user_id)
    costcenter_id = None
    for store in store_list:
        if not costcenter_id:
            if not store_costcenter_dict.get(str(store)):
                store_detail = metadata_service.get_store(int(store), partner_id=partner_id, user_id=user_id)
                raise DataValidationExceptionNoCode("未设置成本中心:%s" % store_detail.get('name', ' '))
            costcenter_id = store_costcenter_dict.get(str(store))
        else:
            if not store_costcenter_dict.get(str(store)):
                store_detail = metadata_service.get_store(int(store), partner_id=partner_id, user_id=user_id)
                raise DataValidationExceptionNoCode("未设置成本中心:%s" % store_detail.get('name', ' '))
            else:
                if costcenter_id != store_costcenter_dict.get(str(store)):
                    return False
    return True


def get_cost_center_map(branch_type=None, branch_id=None, partner_id=None, user_id=None, return_cost=False):
    """获取成本中心-门店/仓库/加工中心关联map
    支持门店/仓库筛选
    :param branch_type 'STORE'/'WAREHOUSE'/'MACHINING_CENTER'
    :param branch_id 门店/仓库id
    :param partner_id
    :param user_id
    :param return_cost  是否返回成本中心id, True的时候返回成本中心id（int）不返回map
    :return {cost_center_id: cost_center{name,code,stores,warehouse,machining...}}
    """
    cost_center_map = {}
    if all([branch_type, branch_id]):
        if branch_type == 'STORE':
            relation_filters = {"stores": [str(branch_id)]}
        elif branch_type == 'WAREHOUSE':
            relation_filters = {"warehouse": [str(branch_id)]}
        elif branch_type == "MACHINING_CENTER":
            relation_filters = {"machining": [str(branch_id)]}
        else:
            relation_filters = None
    else:
        relation_filters = None
    try:
        rows = metadata_service.list_entity(schema_name='COST_CENTER', relation_filters=relation_filters,
                                            partner_id=partner_id, user_id=user_id).get('rows', [])
        if len(rows) < 1:
            logging.warning("没有找到主档成本中心信息")
            return cost_center_map if not return_cost else None
        if return_cost is True:
            cost_center_id = convert_to_int(rows[0].get('id')) if isinstance(rows[0], dict) else None
            return cost_center_id
        for row in rows:
            fields = row.get('fields')
            if fields and isinstance(fields, dict):
                relation = fields.get('relation')
                data = {"name": fields.get('name'),
                        "code": fields.get('code')}
                if relation and isinstance(relation, dict):
                    data["stores"] = relation.get('stores') if relation.get('stores') else []
                    data["warehouse"] = relation.get('warehouse') if relation.get('warehouse') else []
                    data["machining"] = relation.get('machining') if relation.get('machining') else []
                else:
                    data["stores"] = []
                    data["warehouse"] = []
                cost_center_map[str(row.get('id'))] = data
        return cost_center_map
    except Exception as e:
        logging.warning("拉取成本中心主档失败")
        raise exception_from_str(str(e))


def get_store_cost_center_map(partner_id=None, user_id=None):
    """获取门店-成本中心map
    :return {str(store_id): str(cost_center_id)}"""
    store_cost_center_map = {}
    cost_center_map = get_cost_center_map(partner_id=partner_id, user_id=user_id)
    if cost_center_map:
        for key, value in cost_center_map.items():
            stores = []
            if isinstance(value, dict):
                stores = value.get('stores', [])
            if len(stores) > 0:
                for store in stores:
                    if store not in store_cost_center_map.keys():
                        store_cost_center_map[store] = key
    return store_cost_center_map


def get_warehouse_cost_center_map(partner_id=None, user_id=None):
    """获取仓库-成本中心map
    :return {str(warehouse_id): str(cost_center_id)}"""
    warehouse_cost_center_map = {}
    cost_center_map = get_cost_center_map(partner_id=partner_id, user_id=user_id)
    if cost_center_map:
        for key, value in cost_center_map.items():
            warehouses = []
            if isinstance(value, dict):
                warehouses = value.get('warehouse', [])
            if len(warehouses) > 0:
                for warehouse in warehouses:
                    if warehouse not in warehouse_cost_center_map.keys():
                        warehouse_cost_center_map[warehouse] = key
    return warehouse_cost_center_map


def get_machining_cost_center_map(partner_id=None, user_id=None):
    """获取加工中心-成本中心map
    :return {str(machining_center_id): str(cost_center_id)}"""
    machining_cost_center_map = {}
    cost_center_map = get_cost_center_map(partner_id=partner_id, user_id=user_id)
    if cost_center_map:
        for key, value in cost_center_map.items():
            stores = []
            if isinstance(value, dict):
                stores = value.get('machining', [])
            if len(stores) > 0:
                for store in stores:
                    if store not in machining_cost_center_map.keys():
                        machining_cost_center_map[store] = key
    return machining_cost_center_map


def get_branch_ids_by_cost_center(cost_center_id=None, partner_id=None, user_id=None):
    """根据成本中心id获取门店/仓库列表
    :param cost_center_id
    :param partner_id
    :param user_id
    :return [branch_id...]
    如果不传成本中心id返回所有"""
    branch_ids = []
    cost_center_result = get_cost_center_map(partner_id=partner_id, user_id=user_id)
    if cost_center_result:
        if cost_center_id:
            cost_center = cost_center_result.get(str(cost_center_id), {})
            stores = cost_center.get('stores')
            warehouse = cost_center.get('warehouse')
            if isinstance(stores, list):
                branch_ids += [convert_to_int(_id) for _id in stores]
            if isinstance(warehouse, list):
                branch_ids += [convert_to_int(_id) for _id in warehouse]
        else:
            for _, v in cost_center_result.items():
                stores = v.get('stores') if isinstance(v, dict) else []
                warehouse = v.get('warehouse') if isinstance(v, dict) else []
                if isinstance(stores, list):
                    branch_ids += [convert_to_int(_id) for _id in stores]
                if isinstance(warehouse, list):
                    branch_ids += [convert_to_int(_id) for _id in warehouse]
    else:
        logging.warning("【get_branch_ids_by_cost_center】未拉到成本中心主档")
    return list(set(branch_ids))


def get_product_code_unit_map(product_codes=None,  partner_id=None, user_id=None):
    """通过商品code做成商品主档map，id有时候会变
    对应到单位map
    :param product_codes[str] -- 商品编码
    :param partner_id
    :param user_id
    :return products_unit_map -- {product_code[str]:
                                        {'default':{id,name,code,rate}},
                                        {'bom': {id,name,code,rate}},
                                        {'order': {id,name,code,rate}},
                                        ....}
    """
    products_unit_map = {}
    unit_dict = {}
    units_ret = metadata_service.get_unit_list(return_fields='id,code,name',
                                               partner_id=partner_id, user_id=user_id)
    units = []
    if units_ret:
        units = units_ret['rows']
    if isinstance(units, list):
        for unit in units:
            if isinstance(unit, dict) and 'id' in unit:
                unit_dict[str(unit['id'])] = unit
    if product_codes:
        filters = {"code__in": product_codes}
        product_ret = metadata_service.get_product_list(filters=filters,
                                                        include_units=True,
                                                        return_fields='id,code',
                                                        partner_id=partner_id,
                                                        user_id=user_id).get('rows', [])
        for product in product_ret:
            unit_map = {}
            if product.get('units'):
                for unit in product['units']:
                    if unit.get('default'):
                        unit_map["default"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('order'):
                        unit_map["order"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('sales'):
                        unit_map["sales"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('bom'):
                        unit_map["bom"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('purchase'):
                        unit_map["purchase"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
                    if unit.get('stocktake'):
                        unit_map["stocktake"] = dict(
                            id=convert_to_int(unit.get('id')),
                            name=unit_dict.get(unit.get('id', {})).get('name'),
                            code=unit_dict.get(unit.get('id', {})).get('code'),
                            rate=round(unit.get('rate', 1), 5)
                        )
            if isinstance(product, dict) and 'code' in product:
                products_unit_map[product['code']] = unit_map
    return products_unit_map


def get_product_map(product_ids=None, return_fields=None, partner_id=None, user_id=None):
    """拉一把主档返回商品和单位的map
    :param product_ids 商品id列表
    :param return_fields 需要返回的字段
    :param partner_id
    :param user_id
    :returns products_map{product_id(str): product_dict}
    """
    products_map = {}
    if len(product_ids) == 0:
        return products_map
    product_list = metadata_service.get_product_list(ids=product_ids,
                                                     return_fields=return_fields,
                                                     include_units=True,
                                                     partner_id=partner_id, user_id=user_id).get("rows", [])

    if product_list and isinstance(product_list, list):
        for product in product_list:
            products_map[str(product.get('id', ''))] = product
    return products_map


def get_position_list_map_by_branch_id(branch_id=None, branch_type=None, partner_id=None, user_id=None):
    """根据门店/仓库/加工中心拉取其下所配置的仓位列表
    :param branch_id 门店/仓库/加工中心id
    :param branch_type STORE/WAREHOUSE/MACHINING_CENTER
    :param partner_id
    :param user_id
    """
    position_list_map = {}
    if branch_type == "STORE":
        filters = {"relation.stores__in": [str(branch_id)]}
    elif branch_type == "WAREHOUSE":
        filters = {"relation.warehouse__in": [str(branch_id)]}
    elif branch_type == "MACHINING_CENTER":
        filters = {"relation.machining__in": [str(branch_id)]}
    else:
        raise DataValidationException("请传入正经的branch_type-[STORE/WAREHOUSE/MACHINING_CENTER]!")
    position_entity = metadata_service.get_position_list(filters=filters, return_fields="id,code,name",
                                                         partner_id=partner_id, user_id=user_id)
    if position_entity:
        position_list = position_entity.get('rows') if position_entity.get('rows') else []
        for p in position_list:
            fields = p.get('fields') if p.get('fields') else {}
            position_list_map[str(p.get('id'))] = fields
    return position_list_map


def get_product_unit_rate_map(product_ids=None, partner_id=None, user_id=None):
    """拉一把主档返回商品和单位id对应转换率的map
    :param product_ids 商品id列表
    :param partner_id
    :param user_id
    :returns products_map{product_id（int）: {unit_id(key): rate(value), ...}}
    """
    product_list = metadata_service.get_product_list(ids=product_ids,
                                                     return_fields="id",
                                                     include_units=True,
                                                     partner_id=partner_id, user_id=user_id).get("rows", [])

    products_unit_rate_map = {}
    if product_list and isinstance(product_list, list):
        for product in product_list:
            units = product.get('units') if product.get('units') else []
            if units:
                unit_rate = dict()
                for unit in units:
                    unit_rate[convert_to_int(unit.get('id'))] = round(unit.get('rate', 1), 5)
                products_unit_rate_map[convert_to_int(product.get('id'))] = unit_rate
    return products_unit_rate_map


def get_branch_map(branch_ids=None, branch_type=None, partner_id=None, user_id=None, return_fields=None,
                   allow_all=False):
    """获取门店/仓库/加工中心/加盟商/主档map
    :return branch_map{id:
                        {"id": id, "name": name, "code": code}
                      }
    """
    branch_map = {}
    if not branch_ids and not allow_all:
        return branch_map
    if not return_fields:
        return_fields = "id,code,name,company_info"
    if branch_type == "STORE" or branch_type == "":
        branches = metadata_service.get_store_list(ids=branch_ids, return_fields=return_fields,
                                                   partner_id=partner_id, user_id=user_id).get('rows', [])
    elif branch_type == "WAREHOUSE":
        branches = metadata_service.get_distribution_center_list(ids=branch_ids,
                                                                 return_fields=return_fields,
                                                                 partner_id=partner_id, user_id=user_id).get('rows', [])
    elif branch_type == "MACHINING_CENTER":
        branches = metadata_service.get_machining_center_list(return_fields=return_fields, ids=branch_ids,
                                                              partner_id=partner_id, user_id=user_id)
    elif branch_type == "POSITION":
        branches = metadata_service.get_position_list(ids=branch_ids, return_fields=return_fields,
                                                      partner_id=partner_id, user_id=user_id).get('rows', [])
    elif branch_type == "FRANCHISEE":
        branches = metadata_service.get_franchisee_list(ids=branch_ids, return_fields=return_fields,
                                                        partner_id=partner_id, user_id=user_id)
    else:
        branches = []
    if branch_type == "POSITION":
        for p in branches:
            fields = p.get("fields") if p.get("fields") else {}
            branch_map[convert_to_int(p.get('id'))] = fields
    else:
        for b in branches:
            branch_map[convert_to_int(b.get('id'))] = b
    return branch_map


def get_branch_list_map(branch_type=None, branch_ids=None, partner_id=None, user_id=None):
    """根据组织ids一把拉取主单做成map
    :param branch_type 组织类型：store/warehouse/vendor/machining_center/position
    :param branch_ids 组织id列表
    :param partner_id
    :param user_id
    :returns {branch_id: {"id": id, "name": name, "code": code}}"""
    branch_list = []
    if "store" in branch_type:
        branch_list += metadata_service.list_entity(schema_name='store', ids=branch_ids,
                                                   return_fields="id,code,name,tel",
                                                   partner_id=partner_id, user_id=user_id).get('rows', [])
    if "warehouse" in branch_type:
        branch_list += metadata_service.list_entity(schema_name='distrcenter', ids=branch_ids,
                                                   return_fields="id,code,name,tel",
                                                   partner_id=partner_id, user_id=user_id).get('rows', [])
    if "vendor" in branch_type:
        branch_list += metadata_service.list_entity(schema_name='vendor', ids=branch_ids,
                                                   return_fields="id,code,name,tel",
                                                   partner_id=partner_id, user_id=user_id).get('rows', [])

    if "machining_center" in branch_type:
        branch_list += metadata_service.list_entity(schema_name='machining-center', ids=branch_ids,
                                                   return_fields="id,code,name,tel",
                                                   partner_id=partner_id, user_id=user_id).get('rows', [])

    if "position" in branch_type:
        branch_list += metadata_service.list_entity(schema_name="position_config", ids=branch_ids,
                                                   return_fields="id,code,name,tel",
                                                   partner_id=partner_id, user_id=user_id).get('rows', [])
    new_branch_list = []
    for branch in branch_list:
        new_branch_list.append({
                                    'id': branch.get('id'),
                                    'code': branch.get('fields', {}).get('code'),
                                    'name': branch.get('fields', {}).get('name'),
                                    'tel': branch.get('fields', {}).get('tel')
                                    })

    branch_map = {}
    if new_branch_list and len(new_branch_list) > 0:
        for entity in new_branch_list:
            branch_map[int(entity["id"])] = entity
    return branch_map


def get_company_map(company_ids, partner_id, user_id):
    # 查询贸易公司信息
    company_map = dict()
    if len(company_ids) == 0:
        return company_map
    company_info = metadata_service.get_company_list(ids=company_ids, return_fields="id,code,name",
                                                     partner_id=partner_id, user_id=user_id).get('rows', [])
    for c in company_info:
        company_map[convert_to_int(c.get('id'))] = c
    return company_map


def get_category_map(category_ids: Union[list, set], partner_id: int, user_id: int, return_fields=None):
    category_map = {}
    if len(category_ids) == 0:
        return category_map
    if not return_fields:
        return_fields = 'id,code,name'
    # 商品类别
    category_ret = metadata_service.get_product_category_list(ids=category_ids, return_fields=return_fields,
                                                              partner_id=partner_id, user_id=user_id)
    if category_ret and category_ret.get('rows'):
        for c in category_ret.get('rows'):
            category_map[int(c.get('id', 0))] = {
                'code': c.get('code'),
                'name': c.get('name')
            }
    return category_map


def get_unit_map(partner_id: int, user_id: int, unit_ids=None, return_fields=None):
    if not unit_ids:
        unit_ids = None
    unit_list = metadata_service.get_unit_list(ids=unit_ids, partner_id=partner_id, user_id=user_id,
                                               return_fields=return_fields).get('rows', [])

    unit_map = {}
    if unit_list:
        for u in unit_list:
            unit_map[int(u['id'])] = u
    return unit_map


def get_month_first_and_last_date(year: int, month: int, return_utc=False, utc_offset=None):
    """获取指定月份的第一天0点和次月第一天0点日期
    :param year 年份
    :param month 月份
    :param return_utc 是否返回utc时间
    :param utc_offset 时间偏移量
    :return datetime: YYYY-mm-dd HH:MM:SS
    """
    _, month_range = calendar.monthrange(year, month)
    first_day = datetime(year=year, month=month, day=1, hour=0, minute=0, second=0)
    last_day = datetime(year=year, month=month + 1, day=1, hour=0, minute=0, second=0)
    if return_utc is True:
        if not utc_offset:
            ts = time.time()
            utc_offset = int((datetime.fromtimestamp(ts) - datetime.utcfromtimestamp(ts)).total_seconds() / 3600)
        first_day = first_day - timedelta(hours=utc_offset)
        last_day = last_day - timedelta(hours=utc_offset)
    return first_day, last_day


def get_supply_reason_map(codes=None, _type=None, partner_id=None, user_id=None):
    """查询运营原因map
    :return {code: name}
    """
    if not _type:
        raise Exception("reason type is required!")
    filters = dict(
        type=_type
    )
    if codes:
        filters['code__in'] = codes
    res = metadata_service.list_entity(schema_name="SUPPLY_REASON", filters=filters,
                                       return_fields="id,code,name", partner_id=partner_id,
                                       user_id=user_id).get('rows', [])
    reason_map = dict()
    for r in res:
        fields = r.get('fields')
        if isinstance(fields, dict):
            reason_map[fields.get('code')] = fields.get('name')
    return reason_map


def get_username_map(partner_id=None, user_id=None, ids=None):
    users = ianvs_service.ListUserInfo(partner_id=partner_id, user_id=user_id, ids=ids).get('rows', [])
    user_dict = dict()
    if users:
        for user in users:
            user_dict[convert_to_int(user.get('id'))] = user.get('nick')
    return user_dict


def set_response_primary_key(func):

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        response = []
        rows, total = func(*args, **kwargs)
        for row in rows:
            row['primary_id'] = gen_snowflake_id()
            response.append(row)
        return response, total

    return wrapper

# def check_quantity(quantity, max_quantity, min_quantity, inc_quantity, mode="down", round_num=3):
#     if quantity == 0:
#         return 0
#     if inc_quantity == 0:
#         return round(quantity, round_num)
#
#     quantity = float(quantity)
#     max_quantity = float(max_quantity)
#     min_quantity = float(min_quantity)
#     inc_quantity = float(inc_quantity)
#     quantity = round(quantity, round_num)
#     max_quantity = round(max_quantity, round_num)
#     min_quantity = round(min_quantity, round_num)
#     inc_quantity = round(inc_quantity, round_num)
#     # 数量校验, 如果订货数量不满足递增订量, 最小订量, 最大订量的标记为true
#     flag = False
#     # 最大订量配置不对时,需要计算真正的最大订量
#     if (max_quantity - min_quantity ) % inc_quantity:
#         max_quantity = ((max_quantity-min_quantity) // inc_quantity) * inc_quantity + min_quantity
#     # 不满足最小订量
#     if quantity < min_quantity:
#         flag = True
#     if quantity > max_quantity:
#             flag = True
#     # 不满足递增订量
#     if (quantity - min_quantity) % inc_quantity:
#         flag = True
#     return flag


def check_quantity(quantity, min_quantity, increment_quantity, max_quantity):
    if not all(locals().values()):
        return False

    quantity = Decimal(quantity).quantize(Decimal('0.000'), ROUND_HALF_UP) if isinstance(quantity, float) else quantity

    min_quantity, increment_quantity, max_quantity = Decimal(min_quantity), Decimal(increment_quantity),\
                                                     Decimal(max_quantity)

    return min_quantity <= quantity <= max_quantity and not (quantity - min_quantity) % increment_quantity

def dict_to_struct(data):
    """Converts a Python dictionary to a google.protobuf.Struct."""
    struct = struct_pb2.Struct()
    for key, value in data.items():
        if isinstance(value, str):
            struct.fields[key].string_value = value
        elif isinstance(value, bool):
            struct.fields[key].bool_value = value
        elif isinstance(value, int):
            struct.fields[key].number_value = value
        elif isinstance(value, float):
            struct.fields[key].number_value = value

        elif isinstance(value, list):
            if len(value)>0:
                list_value = struct.fields[key].list_value
                for item in value:
                    if isinstance(item, str):
                        list_value.values.add().string_value = item
                    elif isinstance(item, int):
                        list_value.values.add().number_value = item
                    elif isinstance(item, float):
                        list_value.values.add().number_value = item
                    elif isinstance(item, bool):
                        list_value.values.add().bool_value = item
                    elif isinstance(item, dict):
                        list_value.values.add().struct_value.CopyFrom(dict_to_struct(item))
                    else:
                        # Handle other types or raise an error
                        print(f"Unsupported type in list: {type(item)}")
        elif isinstance(value, dict):
            struct.fields[key].struct_value.CopyFrom(dict_to_struct(value))
        elif value is None:
            struct.fields[key].null_value = struct_pb2.NULL_VALUE
        else:
            # Handle other types or raise an error
            print(f"Unsupported type: {type(value)}")
    return struct

def convert_to_RFC3339(dt):
    return dt.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
