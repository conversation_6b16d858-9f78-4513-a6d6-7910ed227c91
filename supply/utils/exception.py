# coding: utf-8
class CodeAndMsg(object):
    def __init__(self, code, error_msg=None):
        self.code = code
        self.error_msg = error_msg
        self.message = error_msg

    def __str__(self):
        return 'Code: %s, Message: %s' % (self.code, self.error_msg)


class AcceptableException(Exception):
    def __init__(self, code, error_msg=None):
        self.value = code
        self.error_msg = error_msg
        self.message = error_msg

    def __str__(self):
        # return repr(self.value)
        return 'Acceptable Exception (code: %s, msg: %s)' % (self.value, self.error_msg)


class DataValidationException(Exception):
    def __init__(self, code, error_msg=None, payload=None):
        self.value = code
        self.error_msg = error_msg
        self.message = error_msg
        self.payload = payload

    def __str__(self):
        # return repr(self.value)
        return 'Data Validation Exception (code: %s, msg: %s)' % (self.value, self.error_msg)


class ErrorCodeClass(object):
    def __init__(self):
        self.NotFound="NOT_FOUND"
        self.DataNotValid="DATA_NOT_VALID"
        self.InvalidDataOption="INVALID_DATA_OPTION"
        self.DataNotValid = "DATA_VALIDATION_ERROR"
ErrorCode=ErrorCodeClass()
class TimeoutError(Exception):
    def __init__(self, code, error_msg=None):
        self.value = code
        self.error_msg = error_msg
        self.message = error_msg


# class AccessDeniedError(StandardError):
#     pass


# class DataValidationError(StandardError):
#     pass


# class DataExistError(StandardError):
#     pass


# class ApiError(StandardError):
#     pass


# class ObjectNotFoundError(StandardError):
#     pass


class TaskError(Exception):
    def __init__(self, code, error_msg=None):
        self.value = code
        self.error_msg = error_msg
        self.message = error_msg

    def __str__(self):
        # return repr(self.value)
        return 'Task Error (code: %s, msg: %s)' % (self.value, self.error_msg)
