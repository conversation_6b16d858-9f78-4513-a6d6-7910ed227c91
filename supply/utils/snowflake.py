# -*- coding: utf8 -*-

from random import randint
import time
from google.protobuf.json_format import MessageToDict

from supply.driver.Redis import redis_cli
from supply import APP_CONFIG
from supply import APP_NAME, logger
from supply.utils import Singleton
from supply.proto.utils.uuid_pb2_grpc import UuidStub
from supply.proto.utils.uuid_pb2 import Null, MutiNewRequest
import grpc


class IdGenerator(object):
    def get_id(self):
        raise NotImplemented("not implement get id")

    def get_ids(self, count):
        raise NotImplemented("not implement get ids")


class CloudIdGenerator(object):
    def __init__(self, addr, timeout):
        self.addr = addr
        self.timeout = timeout
        self.channel = grpc.insecure_channel(addr)
        self.uuid_stub = UuidStub(self.channel)

    def get_id(self):
        resp = self.uuid_stub.New(Null(), timeout=self.timeout)
        return resp.payload

    def get_ids(self, count):
        if count == 0:
            return []
        resp = self.uuid_stub.MutiNew(MutiNewRequest(len=count), timeout=self.timeout)
        return MessageToDict(resp)['payload']


DC_N = APP_CONFIG.get('data_center')
if DC_N is None or int(DC_N) == 0:
    logger.warn('Data center number not set, use 0 as defautl value. It may cause snowflake ID duplication problem.')
    DC_N = 0
DC_N = int(DC_N)
PID_EXPIRE = 60 * 60 * 24   # process ID expire in 1 day
REDIS_KEY_PREFIX = '{}:pid:{}'.format(APP_NAME, DC_N)
EPOCH_TIMESTAMP = ************


def _get_pid():
    """Generate process ID"""
    count = 0
    while 1:
        count += 1
        pid = randint(0, 255)
        k = '{}:{}'.format(REDIS_KEY_PREFIX, pid)
        if redis_cli.setnx(k, 1):
            redis_cli.expire(k, PID_EXPIRE)
            return pid
        if count > 100:
            return pid


class SnowFlakeGenerator(object):
    """Global unique SnowFlake ID Generator"""
    __metaclass__ = Singleton

    def __init__(self, dc, worker):
        self.dc = dc
        self.worker = worker
        self.node_id = ((dc & 0x03)<< 8) | (worker & 0xff)
        self.last_timestamp = EPOCH_TIMESTAMP
        self.sequence = 0
        self.sequence_overload = 0
        self.errors = 0
        self.generated_ids = 0

    def get_id(self):
        curr_time = int(time.time() * 1000)

        if curr_time < self.last_timestamp:
            # stop handling requests til we've caught back up
            self.errors += 1
            logger.error('Clock went backwards! %d < %d, sleep 30ms' % (curr_time, self.last_timestamp))
            time.sleep(0.03)
            return self.get_id()

        if curr_time > self.last_timestamp:
            self.sequence = 0
            self.last_timestamp = curr_time

        self.sequence += 1

        if self.sequence > 4095:
            # the sequence is overload, just wait to next sequence
            logger.warning('The sequence has been overload')
            self.sequence_overload += 1
            time.sleep(0.001)
            return self.get_id()

        generated_id = ((curr_time - EPOCH_TIMESTAMP) << 22) | (self.node_id << 12) | self.sequence

        self.generated_ids += 1
        return generated_id

    def get_ids(self, count):
        ids = []
        for i in range(count):
            ids.append(self.get_id())
        return ids


class RollBackIdGenerator(IdGenerator):

    def __init__(self, addr, rollback_timeout, ):
        self.cloud_generator = CloudIdGenerator(addr, rollback_timeout)
        self.local_generator = SnowFlakeGenerator(DC_N, _get_pid())

    def get_id(self):
        try:
            cloud_id = self.cloud_generator.get_id()
            return cloud_id
        except:
            logger.warning('cloud id generator timeout, use local id generator')
            return self.local_generator.get_id()

    def get_ids(self, count):
        try:
            cloud_ids =  self.cloud_generator.get_ids(count)
            # print("cloud_ids success")
            return cloud_ids
        except:
            logger.warning('cloud id generator timeout, use local id generator')
            return self.local_generator.get_ids(count)


ID_GENERATOR_ADDR = APP_CONFIG.get("id_generator_addr")
if ID_GENERATOR_ADDR:
    __id_generator = RollBackIdGenerator(ID_GENERATOR_ADDR, 3)

else:
    __id_generator = SnowFlakeGenerator(DC_N, _get_pid())


def init_cloud_id_generator(remote_addr):
    global rollback_id_generator
    rollback_id_generator = RollBackIdGenerator(remote_addr, 3)


def gen_snowflake_id() -> int:
    """Generate SnowFlake Unique ID"""
    return __id_generator.get_id()


def get_id():
    return __id_generator.get_id()


def get_ids(count):
    return __id_generator.get_ids(count)
