from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import re
import itertools
from typing import Dict, List, Any, Optional, Union, Tuple
from supply.client.metadata_service import metadata_service
import logging
import copy

# 假设这些常量已经在Python中定义了
DEMAND_STATUS_ENUM = {}
DEMAND_STATUS_TAG = {}
STATUS_TIME_LABEL = {}
ORDER_STATUS_ENUM = {}
DEMAND_TYPE_ENUM = {}

weekDays = ['一', '二', '三', '四', '五', '六', '日']
monthDays = {
    '-1': '倒数第1天',
    '-2': '倒数第2天',
    '-3': '倒数第3天'
}


def get_month_week_day(week_index: int, week: List[Dict[str, Any]], ref_date: datetime = datetime.now()) -> datetime:
    """计算第几周的周几所在的日期"""
    next_month_start = ref_date + relativedelta(months=1, day=1)
    start_week_day = (next_month_start.weekday() + 1) % 7  # Python周一为0，周日为6

    if week_index == 1:
        if week[0]['day'] == start_week_day:
            match_date = next_month_start
        else:
            day_diff = (week[0]['day'] - start_week_day) % 7
            match_date = next_month_start + timedelta(days=day_diff)
    else:
        match_week_day = next_month_start + relativedelta(weeks=week_index - 1)
        if week[0]['day'] == start_week_day:
            match_date = match_week_day
        else:
            day_diff = (week[0]['day'] - start_week_day) % 7
            match_date = match_week_day + timedelta(days=day_diff)

    # 检查是否跨月
    if match_date.month != next_month_start.month:
        return get_month_week_day(week_index, week, ref_date + relativedelta(months=1))
    return match_date


def format_card_data(schedule, demand_date):
    # demand_date = datetime.fromisoformat(item['demand_date'])  # 假设demand_date是ISO格式字符串
    circle = []  # 循环规则描述
    next_day = None  # 下次订货日期

    if schedule.get('method') == 'D':  # 按天订货
        interval = schedule.get('interval', 0)
        circle.append(f"每隔{interval}天订货" if interval else "每天订货")
        next_day = demand_date + timedelta(days=interval+1)

    elif schedule.get('method') == 'W':  # 按周订货

        week_str = schedule.get('week_method', '')

        # 修正1: 直接处理二进制字符串，不再按 - 分割
        week_config = []

        for i, flag in enumerate(week_str):

            if flag == '1':
                # Python的weekday()：周一=0, 周二=1, ..., 周日=6

                # 您的需求：第一位代表周一，最后一位代表周日

                week_config.append({

                    'label': weekDays[i],

                    'day': i  # 直接使用索引0-6

                })

        # 构造周规则描述


        if not week_config:

            circle.append("无订货日配置")


        else:

            # 修正2: 只输出有效订货日

            circle.append(f"每周{''.join([w['label'] for w in week_config])}订货")

        # 计算下次订货日期

        if week_config:

            current_weekday = demand_date.weekday()  # 周一=0, 周日=6

            found = False

            min_diff = float('inf')

            print("sssss", current_weekday)

            # 查找最近的下一个订货日

            for config in week_config:

                target_day = config['day']

                # 计算日期差（考虑跨周）

                diff = (target_day - current_weekday) % 7

                # 如果是当天且需要安排当天，则 diff=0

                # 但通常需要下一个订货日，所以忽略当天

                if diff == 0:
                    diff = 7  # 跳到下周的同一天

                # 找出最小正差值

                if 0 < diff < min_diff:
                    min_diff = diff

                    found = True

            if found:

                next_day = demand_date + timedelta(days=min_diff)


            else:

                # 没有找到有效的未来日期（应不会发生）

                next_day = demand_date + timedelta(days=7)

    elif schedule.get('method') == 'M':  # 按月订货
        month_days = sorted(map(int, schedule.get('month_method', '').split(',')))
        start_days = [d for d in month_days if d > 0]
        end_days = [d for d in month_days if d < 0]

        # 构造月规则描述
        start_desc = '、'.join(map(str, start_days))
        end_desc = '、'.join([monthDays[str(d)] for d in end_days])
        circle.append(f"每月{start_desc}{f'、{end_desc}' if end_days else ''}号订货")

        # 计算下个月第一天和本月第一天
        current_month = demand_date.replace(day=1)
        next_month = current_month + relativedelta(months=1)

        # 生成当前月和下个月的候选日期
        possible_dates = []

        # 生成当前月的所有日期
        for d in month_days:
            if d > 0:  # 正数日期
                try:
                    candidate = current_month.replace(day=d)
                    possible_dates.append(candidate)
                except ValueError:
                    # 处理无效日期（如2月30日）
                    pass
            else:  # 倒数日期
                try:
                    # 计算本月最后一天
                    last_day = (current_month + relativedelta(months=1) - timedelta(days=1)).day
                    # 计算倒数日期
                    candidate = current_month.replace(day=last_day + d + 1)
                    possible_dates.append(candidate)
                except ValueError:
                    pass

        # 生成下个月的候选日期
        next_month_dates = []
        for d in month_days:
            if d > 0:
                try:
                    candidate = next_month.replace(day=d)
                    next_month_dates.append(candidate)
                except ValueError:
                    pass
            else:
                try:
                    # 计算下月最后一天
                    next_month_last_day = (next_month + relativedelta(months=1) - timedelta(days=1)).day
                    candidate = next_month.replace(day=next_month_last_day + d + 1)
                    next_month_dates.append(candidate)
                except ValueError:
                    pass

        # 合并并筛选大于当前日期的日期
        all_dates = sorted(possible_dates + next_month_dates)
        valid_dates = [d for d in all_dates if d > demand_date]

        # 选择最近的日期
        next_day = valid_dates[0] if valid_dates else None
        print("Next order date:", next_day)
    logging.info("format_card_data==={},{},{},{}".format(schedule, demand_date, "next_day====", next_day))
    next_day = next_day + timedelta(days=schedule.get('before', 0))

    # 添加下次订货描述
    # logging.info("format_card_data==={},{},{},{}".format(schedule, demand_date,"next_day====",next_day))
    return next_day


def format_date_to_cn_str(date_obj):
    """
    将日期对象格式化为中文日期字符串
    格式示例：07月16日 周三

    参数:
        date_obj (datetime): 日期对象

    返回:
        str: 格式化的中文日期字符串
    """
    # 定义中文星期缩写映射
    weekdays = ['一', '二', '三', '四', '五', '六', '日']

    # 格式化月份和日期（带前导零）
    month_str = date_obj.strftime("%m月")
    day_str = date_obj.strftime("%d日")

    # 获取星期几（0表示周一，6表示周日）
    weekday_idx = (date_obj.weekday()) % 7  # 将python的周一(0)调整为标准周一(0)
    weekday_str = f"周{weekdays[weekday_idx]}"

    return f"{month_str}{day_str} {weekday_str}"


def get_next_arr_days(store_id, product_ids, partner_id, user_id, order_date):
    arr_days_map = {}
    rows = metadata_service.get_list_valid_product_for_distr_by_id(store_id, product_ids=product_ids,
                                                                   order_date=order_date,
                                                                   include_product_fields='name,code,model_name,model_name,model_code,storage_type,category,product_type,sale_type,barcode',
                                                                   # filter_type=1,
                                                                   partner_id=partner_id, user_id=user_id).get('rows',
                                                                                                               [])
    for i in rows:
        arr_days_map[int(i.get("product_id"))] = int(i.get("arrival_day", 0))

    logging.info("get_list_valid_product_for_distr_by_id=={}==={}".format(order_date, arr_days_map))
    return arr_days_map


def get_all_arr_days_map(plan, product_ids, demand, partner_id, user_id, depth=0):
    # 终止条件：达到31层递归或没有产品ID需要处理
    if depth >= 31 or not product_ids:
        return {}

    all_arr_days_map = {}
    schedule = dict(
        method=plan.method,
        interval=plan.interval,
        week_method=plan.week_method,
        month_method=plan.month_method,
        end=plan.end,
        before=plan.before,
    )

    # 1. 计算当前需求周期
    next_demand_date = format_card_data(schedule, demand.demand_date)

    # 2. 检查是否超过结束日期
    check_date = next_demand_date - timedelta(days=schedule.get('before', 0))
    if check_date > schedule['end']:
        return {}

    # 3. 获取当前周期的到货信息
    in_order_date = next_demand_date

    ### get_list_valid_product_for_distr_by_id==2025-07-18 00:00:00==={5040230127502426112: '3'}
    arr_days_map = get_next_arr_days(demand.receive_by, product_ids, partner_id, user_id, in_order_date)

    # 4. 合并当前结果
    for product_id, arrival_day in arr_days_map.items():
        logging.info("arr_days_map=={}==={}==={}".format(product_id, next_demand_date,arrival_day))
        all_arr_days_map[product_id] = next_demand_date+timedelta(days = (arrival_day))

    # 5. 找出尚未找到的产品ID
    missing_ids = [pid for pid in product_ids if pid not in arr_days_map]

    # 6. 如果有缺失的产品，递归处理
    if missing_ids:
        # 创建新的需求对象，时间点设置为当前计算的next_demand_date
        new_demand = copy.copy(demand)
        new_demand.demand_date = next_demand_date

        # 递归获取后续周期的到货信息
        recursive_results = get_all_arr_days_map(
            plan,
            missing_ids,
            new_demand,
            partner_id,
            user_id,
            depth + 1  # 深度增加
        )

        # 合并递归结果到最终结果中
        for pid, v in recursive_results.items():
            # 只添加缺失ID的结果，避免覆盖已有值
            if pid in missing_ids:
                all_arr_days_map[pid] = v

    return all_arr_days_map
