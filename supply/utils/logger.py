# -*- coding: utf-8 -*-

import os
import logging
import logging.config

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'generic': {
            'format': '[%(levelname)s] [%(asctime)s] [%(pathname)s:%(lineno)d] : %(message)s',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'generic',
        },
    },
    'loggers': {
        '': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
    }
}

DEFAULT_LOGGERS = [
    'requests.packages.urllib3.connectionpool',
    'urllib3.connectionpool',
]

DEFAULT_LEVEL = logging.ERROR

def init_logging() -> None:
    logging.config.dictConfig(LOGGING_CONFIG)
    for logger_name in DEFAULT_LOGGERS:
        logger = logging.getLogger(logger_name)
        logger.setLevel(DEFAULT_LEVEL)

    # graylog handler
    ENABLE_GRAYLOG = os.getenv('ENABLE_GRAYLOG', False)
    if ENABLE_GRAYLOG:
        from pygelf import HexGelfUdpHandler
        logger.addHandler(HexGelfUdpHandler(host='127.0.0.1', port=9402, chunk_size=1350))
