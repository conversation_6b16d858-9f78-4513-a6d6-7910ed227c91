# 按proto文件枚举值顺序
# 盘点计划状态
STS_STATUS = ['INITED', 'CANCELLED', 'CONFIRMED']
# 盘点单类型
S_TYPE = ['W', 'D', 'M', 'R']
# 门店状态
S_STATUS = ['OPENED', 'CLOSED', 'INVALID']
# 盘点单状态
# //创建状态
# INITED = 0;
# // 用户取消状态
# CANCELLED = 1;
# REJECTED = 2;
# // 录入盘点单位确定状态
# CONFIRMED = 3;
#'FINALIZED'=4
# // 财务核算检查后状态
# APPROVED = 5;
# // 最后核算完库存后状态, 一次性扣库存一个字段
ST_STATUS = ['STARTED', 'INITED', 'CANCELLED', 'SUBMITTED', 'REJECTED', 'APPROVED', 'CONFIRMED']
# 盘点商品状态
STP_STATUS = ['INITED', 'STARTED', 'CANCELLED', 'SUBMITTED', 'REJECTED', 'APPROVED', 'CONFIRMED']
