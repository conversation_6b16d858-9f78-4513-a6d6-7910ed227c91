from datetime import date, datetime
import json, decimal
import hashlib


def encodeUTF8(string):
    if string is not None and type(string) is str:
        return string.encode('utf8')
    return string


class CJsonEncoder(json.JSONEncoder):
    
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, date):
            return obj.strftime('%Y-%m-%d')
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        else:
            return json.JSONEncoder.default(self, obj)


def generate_hash_md5(s):
    hash_md5 = hashlib.md5(encodeUTF8(s))
    return hash_md5.hexdigest()
