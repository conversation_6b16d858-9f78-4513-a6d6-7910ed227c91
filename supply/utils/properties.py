# coding: utf-8
import functools
from datetime import datetime, date
from werkzeug.security import generate_password_hash, check_password_hash
import requests, json
from dateutil import parser


# domain property
class domainproperty(property):
    def __init__(self, fget, fset=None, fdel=None, doc=None, *arg, **kw):
        property.__init__(self, fget, fset, fdel, doc, *arg, **kw)
        self.domain_fget = fget
        self.domain_fset = fset
        self.__validate_func = []

    def __get__(self, instance, owner):
        if self.domain_fget.__name__ not in instance.__dict__:
            raise AttributeError('domain model has not property: %s' % self.domain_fget.__name__)
        return instance.__dict__[self.domain_fget.__name__]

    def __set__(self, instance, value):
        if not hasattr(instance, 'props'):
            instance.props = {}
        if not hasattr(instance, 'valid_errors'):
            instance.valid_errors = {}
        if len(self.__validate_func) > 0:
            for f in self.__validate_func:
                is_valid, msg = f(value)
                if not is_valid:
                    instance.valid_errors[self.domain_fget.__name__] = msg
                elif self.domain_fget.__name__ in instance.valid_errors:
                    instance.valid_errors.pop(self.domain_fget.__name__)
        if self.domain_fset:
            value = self.domain_fset(instance, value)
        instance.__dict__[self.domain_fget.__name__] = value
        instance.props[self.domain_fget.__name__] = value

    def validate_func(self, f):
        if callable(f):
            self.__validate_func.append(f)  # TODO 子类验证重写
        return f


# string property
class stringproperty(domainproperty):
    def __init__(self, fget, fset=None, fdel=None, doc=None, min_length=None, max_length=None, *arg, **kw):
        self.min_length = min_length
        self.max_length = max_length
        super(stringproperty, self).__init__(fget, fset, fdel, doc, *arg, **kw)

    def __set__(self, instance, value):
        super(stringproperty, self).__set__(instance, value)
        if value is None: return
        # if not (isinstance(value, basestring) or isinstance(value, str)):
        if not isinstance(value, str):
            try:
                value = str(value)
                super(stringproperty, self).__set__(instance, value)
            except ValueError as e:
                instance.valid_errors[self.domain_fget.__name__] = '%s must be string!' % self.domain_fget.__name__
        if self.domain_fset:
            value = self.domain_fset(instance, value)

        if self.min_length and len(value) < self.min_length:
            instance.valid_errors[self.domain_fget.__name__] = '%s\'s length must be greater than %s!' % (
                self.domain_fget.__name__, self.min_length)
        elif self.max_length and len(value) > self.max_length:
            instance.valid_errors[self.domain_fget.__name__] = '%s\'s length must be less than %s!' % (
                self.domain_fget.__name__, self.max_length)
        elif self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)


# required property
class requiredproperty(domainproperty):
    def __init__(self, fget, fset=None, fdel=None, doc=None, *arg, **kw):
        super(requiredproperty, self).__init__(fget, fset, fdel, doc, *arg, **kw)

    def __set__(self, instance, value):
        super(requiredproperty, self).__set__(instance, value)
        if not hasattr(instance, 'required_props'):
            instance.required_props = []
        if self.domain_fget.__name__ not in instance.required_props:
            instance.required_props.append(self.domain_fget.__name__)

        if value is None or value == '':
            instance.valid_errors[self.domain_fget.__name__] = '%s cannot be empty!' % self.domain_fget.__name__
        elif self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)


# id property
class idproperty(requiredproperty):
    def __init__(self, fget, fset=None, fdel=None, doc=None, *arg, **kw):
        super(idproperty, self).__init__(fget, fset, fdel, doc, *arg, **kw)

    def __set__(self, instance, value):
        c_value = value
        # if isinstance(value, basestring) or isinstance(value, str):  # 处理字符串
        if isinstance(value, str):
            try:
                c_value = int(value)
            except ValueError as e:
                c_value = ''
        super(idproperty, self).__set__(instance, c_value)
        if not isinstance(c_value, int):
            instance.valid_errors[self.domain_fget.__name__] = '%s must be an integer!' % self.domain_fget.__name__
        elif self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)


class integerproperty(domainproperty):
    def __set__(self, instance, value):
        c_value = value
        # if isinstance(value, basestring) or isinstance(value, str) or isinstance(value, long):  # 处理字符串
        if isinstance(value, str):
            try:
                c_value = int(value)
            except ValueError as e:
                c_value = value
        super(integerproperty, self).__set__(instance, c_value)
        if c_value is None: return
        # if not isinstance(c_value, int) and not isinstance(c_value, long):  verver_mod
        if not isinstance(c_value, int):
            instance.valid_errors[self.domain_fget.__name__] = '%s must be a integer!' % self.domain_fget.__name__
        elif self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)


# float property
class floatproperty(domainproperty):
    def __encodeUTF8(self, str):
        # if str is not None and type(str) is unicode:
            # return str.encode('utf8')
        return str

    def __set__(self, instance, value):
        c_value = value
        # if isinstance(value, basestring):
            # c_value = self.__encodeUTF8(value)
        if not isinstance(c_value, float):
            try:
                c_value = float(value)
            except Exception as e:
                c_value = value
        super(floatproperty, self).__set__(instance, c_value)
        if c_value is None: return
        if not isinstance(c_value, float):
            instance.valid_errors[self.domain_fget.__name__] = '%s must be a float!' % self.domain_fget.__name__
        else:
            if self.domain_fget.__name__ in instance.valid_errors:
                instance.valid_errors.pop(self.domain_fget.__name__)


# price property
class priceproperty(domainproperty):
    def __set__(self, instance, value):
        super(priceproperty, self).__set__(instance, value)
        if value is None: return
        if not isinstance(value, float):
            instance.valid_errors[self.domain_fget.__name__] = '%s must be a float!' % self.domain_fget.__name__
        if value < 0.0:
            instance.valid_errors[self.domain_fget.__name__] = '%s must be a positive value' % self.domain_fget.__name__
        else:
            if self.domain_fget.__name__ in instance.valid_errors:
                instance.valid_errors.pop(self.domain_fget.__name__)


# datetime property
class datetimeproperty(domainproperty):
    def __set__(self, instance, value):
        super(datetimeproperty, self).__set__(instance, value)
        if value is None: return
        datetime_object = None
        if not isinstance(value, datetime):
            try:
                # datetime_object = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                datetime_object = parser.parse(value)
                super(datetimeproperty, self).__set__(instance, datetime_object)
            except Exception as e:
                instance.valid_errors[
                    self.domain_fget.__name__] = '%s cannot be converted to a datetime!' % self.domain_fget.__name__
        else:
            datetime_object = value
        if datetime_object and self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)


class dateproperty(domainproperty):
    def __set__(self, instance, value):
        super(dateproperty, self).__set__(instance, value)
        if value is None: return
        datetime_object = None
        if not isinstance(value, datetime) and not isinstance(value, date):
            try:
                # datetime_object = datetime.strptime(value, "%Y-%m-%d")
                datetime_object = parser.parse(value)
                super(dateproperty, self).__set__(instance, datetime_object)
            except Exception as e:
                instance.valid_errors[
                    self.domain_fget.__name__] = '%s cannot be converted to a date!' % self.domain_fget.__name__
        else:
            datetime_object = value
        if datetime_object and self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)


# password property
class passwordproperty(domainproperty):
    def __init__(self, fget, fset=None, fdel=None, doc=None, *arg, **kw):
        super(passwordproperty, self).__init__(fget, fset, fdel, doc, *arg, **kw)
        self.password_value = ''

    def __set__(self, instance, value):
        hash_value = generate_password_hash(value)
        self.password_value = hash_value
        super(passwordproperty, self).__set__(instance, hash_value)

    def check_it(self, password):
        return check_password_hash(self.password_value, password)


# bool property
class boolproperty(domainproperty):
    def __set__(self, instance, value):
        super(boolproperty, self).__set__(instance, value)
        if value is None: return
        # if value in (True, 'true', 'True', 'yes', 'Y', 'y', 'ok',):
        #     value = True
        # if value in (False, 'false', 'False', 'No', 'n', 'N'):
        #     value = False
        if not isinstance(value, (int, bool)):  # bool is a subclass of int
            instance.valid_errors[self.domain_fget.__name__] = '%s must be a boolean!' % self.domain_fget.__name__
        elif self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)


class entityproperty(domainproperty):
    def __init__(self, fget, fset=None, fdel=None, doc=None, cls=None, *arg, **kw):
        super(entityproperty, self).__init__(fget, fset, fdel, doc, *arg, **kw)

        # assert issubclass(cls, DomainModelMixin)

        self.cls = cls

    def __set__(self, instance, value):
        if not isinstance(value, self.cls):
            item = self.cls()
            if hasattr(item, 'load_data'):
                item.load_data(value)
            value = item
        super(entityproperty, self).__set__(instance, value)


class entitiesproperty(domainproperty):
    def __init__(self, fget, fset=None, fdel=None, doc=None, cls=None, *arg, **kw):
        super(entitiesproperty, self).__init__(fget, fset, fdel, doc, *arg, **kw)

        # assert issubclass(cls, DomainModelMixin)

        self.cls = cls

    def __set__(self, instance, value):
        if isinstance(value, list):
            items = []
            for v in value:
                if isinstance(v, self.cls):
                    item = v
                else:
                    item = self.cls()
                    if hasattr(item, 'load_data'):
                        item.load_data(v)
                items.append(item)

            super(entitiesproperty, self).__set__(instance, items)


# data property
class keyvaluesourceproperty(domainproperty):
    def __init__(self, fget=None, fset=None, fdel=None, doc=None, data_set=None, data_url=None, *arg, **kw):
        super(keyvaluesourceproperty, self).__init__(fget, fset, fdel, doc, *arg, **kw)
        self.data_set = data_set
        self.data_url = data_url

    def __set__(self, instance, value):
        super(keyvaluesourceproperty, self).__set__(instance, value)
        # if isinstance(self.data_set, list) and value not in self.datasource:
        #     instance.valid_errors[
        #         self.domain_fget.__name__] = '%s is not valid by datasource' % self.domain_fget.__name__
        if self.data_set and len([item for item in self.data_set if item['value'] == value]) == 0:
            instance.valid_errors[
                self.domain_fget.__name__] = '%s is not valid by datasource' % self.domain_fget.__name__
        elif self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)

            # elif self.data_url:
            #     res = hex_api.commons.invoke_sys(url=self.data_url)
            #     if res['status_code'] == 0:
            #         items = res['payload']
            #         self.data_set = items
            #         if self.data_set and len([item for item in self.data_set if item['value'] == value]) == 0:
            #             instance.valid_errors[
            #                 self.domain_fget.__name__] = '%s is not valid by datasource' % self.domain_fget.__name__


# dict property
class dictproperty(domainproperty):
    def __set__(self, instance, value):
        if value is not None and not isinstance(value, dict):
            try:
                value = json.loads(value)
            except:
                pass
        super(dictproperty, self).__set__(instance, value)
        if value is not None and not isinstance(value, dict):
            instance.valid_errors[self.domain_fget.__name__] = '%s must be a dictionary!' % self.domain_fget.__name__
        elif self.domain_fget.__name__ in instance.valid_errors:
            instance.valid_errors.pop(self.domain_fget.__name__)
