import logging
import traceback
from enum import Enum
from functools import wraps
import requests
from supply import APP_CONFIG
from datetime import datetime

PID_PNAME = {476: "上海华为运营中心",
             411: "乌鸦迁移测试",
             501: "深圳市信号咖啡餐饮有限公司",
             431: "浙江顶誉食品有限公司（玩儿串串）",
             291: "上海合阔茶饮有限公司",
             446: "久久丫私有域测试回归",
             551: "广州味满多餐饮企业管理有限公司",
             246: "湖南半仙豆夫餐饮管理有限公司",
             456: "武汉顽徒科技有限公司",
             571: "上海非同凡想商业管理有限公司",
             461: "久久丫私有化环境压测租户",
             326: "合阔运营平台测试20211118",
             200: "野萃山",
             110: "合阔POS",
             341: "合阔运营平台测试",
             466: "燕姐咖啡-测试产品",
             451: "深圳市乌鸦文化传播有限公司-专有部署",
             417: "集团关联租户测试2",
             481: "基本档优化测试",
             471: "上海合阔餐饮系统管理平台",
             491: "惠东县平山布朵村餐饮店",
             561: "上海鹤见堂餐饮有限公司",
             496: "上海合阔平台运营",
             416: "集团关联租户测试1",
             546: "趁热集合HOTCRUSH",
             241: "长沙三顿半咖啡有限公司",
             486: "燕姐咖啡ll",
             349: "广州山丘餐饮管理有限公司1",
             346: "合阔运营平台测试02",
             347: "合阔运营平台测试03",
             276: "合阔糕点",
             316: "歌帝梵（上海）食品商贸有限公司",
             226: "合阔内部体验租户",
             516: "深圳喝亿杯咖啡餐饮管理有限公司",
             421: "集团关联租户测试3",
             211: "南京茶篱餐饮管理有限公司",
             386: "上海吉茶餐饮管理有限公司",
             206: "桂缘（上海）餐饮管理有限公司",
             356: "合阔运营平台测试05",
             261: "长沙新鲜力企业管理有限公司",
             311: "T9 Tea",
             406: "国潮企业管理（云南）有限公司",
             401: "生产测试登录优化",
             336: "国潮企业管理（云南）有限公司",
             251: "上海山海茶点餐饮管理有限公司",
             351: "合阔运营平台测试04",
             391: "测试-浙江顶誉食品有限公司",
             348: "燕姐咖啡直营",
             376: "浙江嘿糖餐饮管理有限公司",
             426: "集团",
             201: "上海合阔信息技术有限公司",
             436: "浙江优亿食品有限公司（留夫鸭）",
             511: "上海群隆服饰有限公司",
             381: "深圳市乌鸦文化传播有限公司",
             331: "Seesaw Coffee",
             661: "上海大河餐饮有限公司",
             216: "上海艾恰餐饮服务有限公司",
             236: "上海茹愿以茶餐饮管理有限公司",
             281: "自动化专用",
             611: "喜茶租户 id 生产用",
             371: "summing1216",
             256: "柠感（深圳）企业管理有限公司",
             286: "上海合阔运营管理",
             266: "千红餐饮管理（深圳）有限公司",
             271: "上海艾恰餐饮服务有限公司",
             221: "浙江新时沏品牌管理有限公司",
             621: "TESM",
             631: "HYM",
             506: "深圳市豪麟餐饮有限公司",
             646: "看就见咖啡（上海）有限公司",
             441: "久久丫食品集团有限公司",
             231: "不止一杯咖啡（深圳）有限公司",
             100: "合阔",
             306: "合阔运营平台测试20211029",
             396: "久久丫私有化环境测试",
             541: "玩儿串串",
             521: "上海晶尝餐饮管理有限公司",
             531: "上海中域咖烨咖啡有限公司",
             392: "虎头局测试",
             361: "广州山丘餐饮管理有限公司",
             366: "上海万物有样餐饮管理有限公司",
             536: "加盟功能验证",
             616: "TESTME",
             627: "XJ1",
             636: "M82",
             626: "XY",
             641: "演示测试",
             642: "演示商户入驻",
             656: "乐乐茶租户 id",
             651: "深圳臻臻然餐饮管理有限公司",
             292: "小点星咖啡",
             526: "柠感(深圳)企业管理有限责任公司",
             566: "广州小茉茶餐饮管理有限公司"}


class KitEnum(Enum):

    def __init__(self, code, description):
        self.code = code
        self.description = description

    FRS_DEMAND_APPROVE = ("FRS_DEMAND", "加盟商订货单审核")
    MOBILE_FRS_DEMAND_APPROVE = ("MOBILE_FRS_DEMAND", "加盟商订货单移动端审核")
    CREATE_FRS_ORDER = ("FRS_DEMAND", "加盟商订单同步拆单")


class Kit:
    threading_identity = {}
    kit_url = APP_CONFIG['kit_url']
    env = APP_CONFIG.get('env', 'saas-qa')

    def __init__(self):
        pass

    def __call__(self, func):
        @wraps(func)
        def inner(*args, **kwargs):
            return func(*args, **kwargs)

        return inner

    @classmethod
    def upload(cls, partnerId, docNo, docType, actionType, partnerName='saas',
               content=None, actionStatus=True, storeId=None, storeName=None):
        return
        partnerName = cls.env
        try:
            r = requests.post(cls.kit_url,
                              json={**{k: v for k, v in locals().items() if k not in ('cls', 'actionStatus')},
                                    'actionDate': int(datetime.now().timestamp()) * 1000,
                                    'actionStatus': 'NORMAL' if actionStatus else 'ABNORMAL',
                                    'reason': content,
                                    'partnerName': PID_PNAME.get(partnerId)},
                              timeout=0.1)
            return r.status_code
        except Exception as e:
            logging.info("Kit ERROR: ", traceback.format_exc())


if __name__ == '__main__':
    print(KitEnum.CREATE_FRS_ORDER.description)
    print(Kit.upload('123', 'ddd', KitEnum.CREATE_FRS_ORDER.code, KitEnum.CREATE_FRS_ORDER.description))
    # print(datetime.utcnow())
