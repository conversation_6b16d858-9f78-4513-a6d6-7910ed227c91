import logging
from functools import wraps

from supply.utils import get_user_info
from supply.utils.enums import STOCKTAKE_TYPE
from supply.error.exception import AuthScopeException, Unauthorized, StoreScopeException
from supply.client.auth_permission import auth_permission
from supply.client.metadata_service import metadata_service

"""
param:  
        
        permission_code:        权限码              系统.scope.function.action
        scope:                  模块                store 门店运营, warehouse 仓库运营, manufactory 加工中心运营, store_bi 门店报表, warehouse_bi 仓库报表, manufactory_bi 加工中心报表
        function:               功能                order 要货/ receive 收货/ return 退货
        action:                 操作                view 查看/ maintain 维护/ audit 审核

        domain:                 场景(app系统.模块)   boh.store/ boh.warehouse
        action:                 权限码              boh.store.order.view/ ... 
"""
# 功能权限
def authorization_scope(permission_code):

    def deco(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):

            ##### domain截取
            code_list = permission_code.split('.')
            domain = '.'.join((code_list[0], code_list[1]))

            ##### 用户信息获取
            partner_id, user_id = get_user_info(args[2]) #args[2]=context
            if not partner_id or not user_id:
                raise Unauthorized(description='No User Info')
            
            ##### 功能权限校验
            result = auth_permission.auth(domain=domain, action=permission_code, partner_id=partner_id, user_id=user_id)
            if result.get('code'):
                logging.warning(
                    "Authorized failed, partner_id:{}, user_id:{}, domain:{}, action:{}".format(partner_id,
                                                                                                user_id,
                                                                                                domain,
                                                                                                permission_code))
                raise AuthScopeException(description='User Unauthorized', detail=result)
            return f(*args, **kwargs)
        return decorated_function
    return deco

# 功能权限
def authorization_scopes(permission_codes):

    def deco(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):

            if isinstance(permission_codes, list):
                for permission_code in permission_codes:
                    ##### domain截取
                    code_list = permission_code.split('.')
                    domain = '.'.join((code_list[0], code_list[1]))

                    ##### 用户信息获取
                    partner_id, user_id = get_user_info(args[2]) #args[2]=context
                    if not partner_id or not user_id:
                        raise Unauthorized(description='No User Info')
                    
                    ##### 功能权限校验
                    result = auth_permission.auth(domain=domain, action=permission_code, partner_id=partner_id, user_id=user_id)
                    if not result.get('code'):
                        return f(*args, **kwargs)
                logging.warning(
                    "Authorized failed, partner_id:{}, user_id:{}, domain:{}, action:{}".format(partner_id,
                                                                                                user_id,
                                                                                                domain,
                                                                                                permission_code))
                raise AuthScopeException(description='User Unauthorized', detail=result)
                
            else:
                authorization_scope(permission_codes)

        return decorated_function
    return deco

# deal功能权限区分
def authorization_scope_by_action(domain, function):

    def deco(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):


            ##### 用户信息获取
            partner_id, user_id = get_user_info(args[2]) #args[2]=context
            if not partner_id or not user_id:
                raise Unauthorized(description='No User Info')

            ##### action信息获取
            request = args[1] #args[2]=req
            action = str(request.action).upper()
            if action in ('APPROVE','APPROVED', 'REJECT', 'REJECTED'):
                action = 'audit'
            else:
                action = 'maintain'
            
            permission_code = domain+'.'+function+'.'+action
            logging.info('permission_code: {}'.format(permission_code))

            ##### 功能权限校验
            result = auth_permission.auth(domain=domain, action=permission_code, partner_id=partner_id, user_id=user_id)
            if result.get('code'):
                logging.warning(
                    "Authorized failed, partner_id:{}, user_id:{}, domain:{}, action:{}".format(partner_id,
                                                                                                user_id,
                                                                                                domain,
                                                                                                permission_code))
                raise AuthScopeException(description='User Unauthorized', detail=result)
            return f(*args, **kwargs)
        return decorated_function
    return deco


# 数据权限——返回门店id
def branch_list_scope_check(partner_id, user_id, schema, domain, branch_ids=None):

    if (not partner_id) or (not user_id):
        raise StoreScopeException("数据权限校验失败，请传入partner_id:{}, user_id:{}".format(
            partner_id, user_id))

    branch_ids = [int(i) for i in branch_ids]
    
    branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, schema, domain)
    if branch_scope.get('full_access'):
        branch_list = branch_ids
    else:
        scope_ids = branch_scope.get('ids', [])
        if not scope_ids:
            raise StoreScopeException("数据权限校验失败，权限不足" + str(branch_ids))
        branch_list = list(set([int(i) for i in scope_ids]))
    if branch_ids:
        branch_list = [int(j) for j in branch_list]
        branch_list = list(set(branch_list) & set(branch_ids))
        if not branch_list:
            raise StoreScopeException("数据权限校验失败，权限不足" + str(branch_ids))
    return branch_list 


# 数据权限——单门店校验
def branch_scope_check(partner_id, user_id, schema, domain, branch_id):

    if (not partner_id) or (not user_id):
        raise StoreScopeException("数据权限校验失败，请传入partner_id:{}, user_id:{}".format(
            partner_id, user_id))
    
    branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, schema, domain)

    if branch_scope.get('full_access'):
        return True
    if str(branch_id) in branch_scope.get('ids', []):
        return True
    else:
        raise StoreScopeException("数据权限校验失败，权限不足" + str(branch_id))


# 数据权限——可访问盘点资源
def stocktake_resource_check(partner_id, user_id, schema, domain, stoketake_type):
    if (not partner_id) or (not user_id):
        raise StoreScopeException("盘点审批权限校验失败，请传入partner_id:{}, user_id:{}".format(
            partner_id, user_id))

    # 查询业务配置，判断是否需要校验盘点审核权限
    open_audit = metadata_service.get_business_extra_config(partner_id=partner_id, domain='boh.store.stocktake',
                                                            user_id=user_id).get('type_audit_grant')
    if not open_audit:
        pass
    else:
        resource = auth_permission.list_data_scope_check(partner_id, user_id, schema, domain)

        if resource.get('full_access'):
            return True
        type_ids = resource.get('ids', [])
        type_list = []
        for _id in type_ids:
            st = STOCKTAKE_TYPE.get(str(_id))
            if st:
                type_list.append(st.get('code'))
        if stoketake_type in type_list:
            return True
        else:
            raise StoreScopeException("盘点审批权限校验失败，权限不足！")

# 列表字符串元素转换成int
def trans_list_str2int(list):
    if len(list)==0:
        return list

    new_list = []
    for i in list:
        new_list.append(int(i))
    return new_list



# 数据权限——返回单据接收方和发起方权限(不完全通用，慎用)——auth修复版
def two_branch_list_scope_check(partner_id, user_id, schema, domain, receive_by_ids=None, delivery_by_ids=None):

    if (not partner_id) or (not user_id):
        raise StoreScopeException("数据权限校验失败，请传入partner_id:{}, user_id:{}".format(
            partner_id, user_id))

    receive_by_ids = [int(i) for i in receive_by_ids] if receive_by_ids else receive_by_ids
    delivery_by_ids = [int(i) for i in delivery_by_ids] if delivery_by_ids else delivery_by_ids
    
    all_branch_scope = auth_permission.list_data_scope_check(partner_id, user_id," ", domain).get('full_access')
    if all_branch_scope:
        return receive_by_ids, delivery_by_ids
    
    receive_branch_list = auth_permission.list_data_scope_check(partner_id, user_id, "STORE", domain).get('ids', [])
    delivery_branch_list = auth_permission.list_data_scope_check(partner_id, user_id, "VENDOR", domain).get('ids', [])
    delivery_branch_list += auth_permission.list_data_scope_check(partner_id, user_id, "MACHINING_CENTER", domain).get('ids', [])
    delivery_branch_list += auth_permission.list_data_scope_check(partner_id, user_id, "DISTRCENTER", domain).get('ids', [])
    delivery_branch_list += auth_permission.list_data_scope_check(partner_id, user_id, "warehouse", domain).get('ids', [])

    receive_branch_list = trans_list_str2int(receive_branch_list)
    delivery_branch_list = trans_list_str2int(delivery_branch_list)

    if receive_by_ids:
        receive_branch_list = list(set(receive_branch_list) & set(receive_by_ids))
    if delivery_by_ids:
        delivery_branch_list = list(set(delivery_branch_list) & set(delivery_by_ids))
        
    if not receive_branch_list and not delivery_branch_list:
        raise StoreScopeException("数据权限校验失败 {}/{}".format(receive_by_ids, delivery_by_ids))
    if not receive_branch_list: # 有配送方权限，则不再额外校验接收方权限
        receive_branch_list = receive_by_ids
    if not delivery_branch_list: # 有接收方权限，则不再额外校验配送方权限
        delivery_branch_list = delivery_by_ids
    return receive_branch_list,  delivery_branch_list

# 数据权限——返回单据接收方和发起方权限(不完全通用，慎用)——临时版
def two_branch_list_scope_check_temp(partner_id, user_id, schema, domain, receive_by_ids=None, delivery_by_ids=None):

    if (not partner_id) or (not user_id):
        raise StoreScopeException("数据权限校验失败，请传入partner_id:{}, user_id:{}".format(
            partner_id, user_id))

    receive_by_ids = [int(i) for i in receive_by_ids] if receive_by_ids else receive_by_ids
    delivery_by_ids = [int(i) for i in delivery_by_ids] if delivery_by_ids else delivery_by_ids
    
    all_branch_scope = auth_permission.list_data_scope_check(partner_id, user_id, schema, domain).get('full_access')
    if all_branch_scope:
        return receive_by_ids, delivery_by_ids
    
    branch_list = auth_permission.list_data_scope_check(partner_id, user_id, schema, domain).get('ids', [])
    branch_list = [int(i) for i in branch_list]
    meta_res = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id, ids=branch_list).get("rows", [])
    if meta_res:
        receive_branch_list = []
        for meta in meta_res:
            receive_branch_list.append(int(meta.get("id", 0)))
        delivery_branch_list = list(set(branch_list)-set(receive_branch_list))
    else:
        receive_branch_list = []
        delivery_branch_list = branch_list

    if receive_by_ids:
        receive_branch_list = list(set(receive_branch_list) & set(receive_by_ids))
    if delivery_by_ids:
        delivery_branch_list = list(set(delivery_branch_list) & set(delivery_by_ids))
        
    if not receive_branch_list and not delivery_branch_list:
        raise StoreScopeException("数据权限校验失败 {}/{}".format(receive_by_ids, delivery_by_ids))
    if not receive_branch_list: # 有配送方权限，则不再额外校验接收方权限
        receive_branch_list = receive_by_ids
    if not delivery_branch_list: # 有接收方权限，则不再额外校验配送方权限
        delivery_branch_list = delivery_by_ids

    return receive_branch_list,  delivery_branch_list
