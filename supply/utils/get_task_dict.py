from importlib import import_module
from pathlib import Path


def load_mq_task():
    # modules = {}
    task_path = Path(__file__).parent.parent.joinpath('task')
    for f in task_path.iterdir():
        if f.name == '__init__.py' or f.name.startswith('__'):
            continue
        module_name = f.name.split('.')[0]
        module = import_module(f'supply.task.{module_name}')
        for f in dir(module):
            if f.startswith('task_'):
                getattr(module, f)
    # from supply.driver.mq import TOPIC, TTT
    # for k, v in TTT.items():
    #     print(k, '||'.join(v))





