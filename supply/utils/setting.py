import os
import copy
from yaml import load
try:
    from yaml import <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON>
except ImportError:
    from yaml import Loader, <PERSON><PERSON>


def merge_data_with_env(yaml_data, env_prefix=''):
    result = {}
    for key, value in yaml_data.items():
        if env_prefix == '':
            env_key = key.upper()
        else:
            env_key = (env_prefix + '_' + key).upper()
        if isinstance(value, list):
            result[key] = copy.deepcopy(value)
        elif isinstance(value, dict):
            result[key] = merge_data_with_env(value, env_key)
        else:
            if env_key in os.environ:
                env_value = os.environ[env_key]
                value_type = type(value)
                result[key] = value_type(env_value)
            else:
                result[key] = value
    return result


def load_setting(yaml_file, env_prefix=''):
    """加载yaml文件，如果环境变量里有对应的值，则优先使用环境变量中的值"""
    with open(yaml_file, "r", encoding='utf8') as f:
        yaml_data = load(f, Loader=Loader)
    return merge_data_with_env(yaml_data, env_prefix)
