# -*- coding:utf-8 -*-

from enum import Enum

# 自采单状态枚举
SELF_PICKING_STATUS = ['INITED', 'SUBMITTED', 'REJECTED', 'APPROVED', 'CANCELLED']

# 盘点单类型权限枚举
STOCKTAKE_TYPE = {
    "10001": {
        "code": "D",
        "name": "日盘",
    },
    "10002": {
        "code": "W",
        "name": "周盘",
    },
    "10003": {
        "code": "M",
        "name": "月盘",
    },
    "10004": {
        "code": "R",
        "name": "不定期",
    },
    "10005": {
        "code": "MANUAL",
        "name": "手动创建",
    },
}


class BranchType(Enum):
    """supply单据业务组织主体类型"""

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    STORE = ("STORE", "直营门店")
    FRS_STORE = ("FRS_STORE", "加盟门店")
    WAREHOUSE = ("WAREHOUSE", "仓库")
    MACHINING_CENTER = ("MACHINING_CENTER", "加工中心")
    VENDOR = ("VENDOR", "供应商")
    FRANCHISEE = ("FRANCHISEE", "加盟商")


class Platform(Enum):

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    HEX_MOBILE = ("HEX_MOBILE", "合阔智云-大掌柜")
    HEX_WEB = ("HEX_WEB", "合阔智云-WEB端")

    @classmethod
    def get_desc(cls, code: str):
        if hasattr(cls, code):
            return cls.__getattr__(code).desc
        else:
            return None


class PaymentWay(Enum):

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    CreditPay = ("BohCreditPay", "BOH信用付")
    WXMPay = ("WXMPay", "微信小程序")
    Offline = ("BohOfflinePay", "线下支付")
    Voucher = ("BohVoucherPay", "代金券支付")


class RefundType(Enum):
    """退款单类型"""

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    ORDER_REFUND = ("ORDER_REFUND", "订货(仅)退款")
    OOS_REFUND = ("OOS_REFUND", "缺货退款")
    RETURN_REFUND = ("RETURN_REFUND", "退货退款")
    DIFF_REFUND = ("DIFF_REFUND", "差异退款")


class RefundMainType(Enum):
    """退款单关联原单类型"""

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    FRS_DEMAND = ("FRS_DEMAND", "订货单")
    FRS_WHS_DEMAND = ("FRS_WHS_DEMAND", "要货单")
    FRS_RETURN = ("FRS_RETURN", "退货单")
    FRS_REC_DIFF = ("FRS_REC_DIFF", "收货差异单")


class Demand_enum(Enum):

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    @classmethod
    def get_name_by_code(cls, code):
        for i in list(cls):
            if code == i.code:
                return i.desc
        return None

    INITED = ("INITED", "初始化")
    PREPARE = ("PREPARE", "准备处理")
    F_COMMIT = ("F_COMMIT", "成品订货提交")
    CAL_DONE = ("CAL_DONE", "成品原料拆减完成")
    CAL_FAILED = ("CAL_FAILED", "成品原料拆减失败")
    PROCESSING = ("PROCESSING", "处理中")
    SUCCESS = ("SUCCESS", "处理成功")
    FAILED = ("FAILED", "处理失败")
    FINISHED = ("FINISHED", "处理完成,其中有部分失败")
    SENT = ("SENT", "消息发送成功")
    SEND_FAILED = ("SEND_FAILED", "消息发送失败")
    CONFIRMED = ("CONFIRMED", "确认")
    APPROVED = ("APPROVED", "审核")
    REJECTED = ("REJECTED", "驳回")
    CANCELLED = ("CANCELLED", "作废")
    FREEZE = ("FREEZE", "冻结")
    SUBMITTED = ("SUBMITTED", "提交")


class Order_enum(Enum):

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    INITED = ("INITED", "初始化")
    SENT = ("SENT", "消息发送成功")
    SEND_FAILED = ("SEND_FAILED", "消息发送失败")
    CHECKING = ('CHECKING', '正在检核')
    CHECKED = ('CHECKED', '检核完成')
    ONWAY = ("ONWAY", "已出库")
    RECEIVED = ("RECEIVED", "已收货")

    D = ("D", "日配送")
    W = ("W", "周配送")
    M = ("M", "月配送")


class Vacancy_enum(Enum):

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    INITED = ("INITED", "初始化")
    CHECKING = ('CHECKING', '正在检核')
    CHECKED = ('CHECKED', '检核完成')
    EXECUTING = ('EXECUTING', '正在执行')
    SUCCESS = ("SUCCESS", "执行处理成功")
    FAILED = ("FAILED", "执行处理失败")

    VACANCY = ("VACANCY", "缺货")


class Demand_type(Enum):

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    SD = ("SD", "门市订货单")
    HD = ("HD", "门市紧急订货单")
    MD = ("MD", "主配单")
    AD = ("AD", "调整单")
    FSD = ("FSD", "加盟门店订货单")
    FMD = ("FMD", "加盟门店主配单")

    NMD = ("NMD", "总仓配送")
    PUR = ("PUR", "采购(直送)类型")
    PAD = ("PAD", "加工配送")


class Demand_sub_type(Enum):

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    STORE = ("STORE", "单门店多商品")
    PRODUCT = ("PRODUCT", "单商品多门店")
    MASTER = ("MASTER", "多商品多门店")


class Demand_bill_code(Enum):

    def __init__(self, code, description):
        self.code = code
        self.desc = description

    STORE_DO = ("STORE_DO", "门店订货订单编号")
    STORE_DM = ("STORE_DM", "门店要货订单编号")
    VACANCY = ("VACANCY", "缺货检核单号")


class Tag_type(Enum):

    def __init__(self, code, description):
        self.code = code
        self.description = description

    FINISHED = ("FINISHED", "成品订货")
    TEA = ("TEA", "茶饮订货")
    BREAD = ("BREAD", "面包订货")
    TOTAL = ("TOTAL", "合计订货")
    RAW = ("RAW", "原料订货")


class Heytea_type(Enum):

    def __init__(self, code, description):
        self.code = code
        self.description = description

    MIX = ("MIX", "热麦订货")
    TEA = ("TEA", "茶饮订货")
    BOTH = ("BOTH", "共同订货")


class Bom_enum(Enum):

    def __init__(self, code, description):
        self.code = code
        self.description = description

    FINISHED_BOM = ("FINISHED_BOM", "成品物料拆解")


# 消息系统的类型枚举类
class Message_server_source_type(Enum):

    def __init__(self, code, description):
        self.code = code
        self.description = description

    DEMAND = ("DEMAND", "订货单")
    ORDER = ("ORDER", "要货单")
    VACANCY = ("VACANCY", "检核单")
    STOCKTAKE = ("STOCKTAKE", "盘点")
    TRANSFER = ("TRANSFER", "调拨")
    ADJUST = ("ADJUST", "损耗")
    RECEIVING = ("RECEIVING", "收货单")
    RECEIVING_DIFF = ("RECEIVING_DIFF", "收货差异单")
    RETURN = ("RETURN", "退货单")
    FIRST_DLV = ("FIRST_DLV", "首配")


class ReturnStatus(Enum):
    def __init__(self, code, description):
        self.code = code
        self.description = description

    INITED = ("INITED", "新建")
    APPROVING = ("APPROVING", "待审核")
    DELIVERING = ("DELIVERING", "待提货")
    DELIVERED = ("DELIVERED", "已提货")
    INPUTTED = ("LAID", "已入库")
    CANCELED = ("CANCELED", "已取消")
    REJECTED = ("REJECTED", "已驳回")
    CONFIRMED = ("CONFIRMED", "已确认")


class PartnerActionModule(Enum):

    def __init__(self, code, description):
        self.code = code
        self.description = description

    DEMAND = ('DEMAND', '订货')


class ReturnTransType(Enum):
    def __init__(self, code, description):
        self.code = code
        self.description = description
    RefundOnly = ("RefundOnly", "紧退款")
    NeedPickUp = ("NeedPickUp", "需要提货")


class ReturnWareHouseType(Enum):
    def __init__(self, code, description):
        self.code = code
        self.description = description
    Scrap = ("Scrap", "报废仓")
    Normal = ("Normal", "正常仓")