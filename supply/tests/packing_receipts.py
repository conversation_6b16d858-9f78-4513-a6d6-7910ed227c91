import grpc
import sys
import os
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.manufactory import packing_receipts_pb2, packing_receipts_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.utils.helper import get_today_datetime, get_product_unit_map
_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(4371272018445729794)
partner_id = str(4183192445833445399)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
)

def get_timestamp(value: datetime):
    # value = value - timedelta(hours=8)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


start_datetime = datetime.strptime("2020-10-07 00:00:00", "%Y-%m-%d %H:%M:%S")
end_datetime = datetime.strptime("2020-09-18 23:59:59", "%Y-%m-%d %H:%M:%S")
datetime_now = datetime.utcnow()
start_date = get_timestamp(start_datetime)
end_date = get_timestamp(end_datetime)
dt_now = get_timestamp(datetime_now)


def test_packing_receipts(api):
    """测试包装单功能模块
    :param api  —— 传入api名字
    """
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = packing_receipts_pb2_grpc.PackingReceiptsStub(channel=conn)
    print(start_date)
    print(end_date)
    if api == "CreatePackingReceipts":
        request = dict(
            status="INITED",
            packing_date=dt_now,
            machining_center_id=4406790557834149889,
            machining_center_code="CD001",
            machining_center_name="原料加工中心",
            position_id=4394449921815388122,
            position_name="生豆仓",
            position_code="1010",
            packing_rule=1,
            remark="开发测试",
            request_id=4394449921815338442,
            target_material=4380310631497072641,
            target_material_code="5090280712",
            target_material_name="NOA 菠萝百香果慕斯85g",
            target_material_unit_id=4373346093845446657,
            target_material_unit_name="块",
            target_material_unit_spec="",
            packed_quantity=100,
            opened_position=True,
            items=[dict(
                product_id=4374806626750693377,
                product_code="1000",
                product_name="1000",
                product_type="product_type",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=100,
                type="consumable",
            ), dict(
                product_id=4374806626499035137,
                product_code="1001",
                product_name="1001",
                product_type="product_type",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=10,
                type="material",
            )
            ]
        )
        res = client.CreatePackingReceipts(packing_receipts_pb2.CreatePackingReceiptsRequest(**request),
                                           metadata=metadata)
        print(res)

    if api == "ListPackingReceipts":
        request = dict(
            start_date=start_date,
            end_date=dt_now,
            code="",
            machining_centers=[],
            target_materials=[],
            position_ids=[],
            status=["INITED", "REJECTED"],
            limit=-1,
            offset=0,
            include_total=True,
            order=None,
            sort=None
        )
        res = client.ListPackingReceipts(packing_receipts_pb2.ListPackingReceiptsRequest(**request), metadata=metadata)
        print(res)

    if api == "GetPackingReceiptsDetail":
        request = dict(
            receipt_id=4404587767184490496
        )
        res = client.GetPackingReceiptsDetail(packing_receipts_pb2.GetPackingReceiptsDetailRequest(**request),
                                              metadata=metadata)
        print(res)

    if api == "UpdatePackingReceipts":
        request = dict(
            receipt_id=4406371568407543808,
            update_detail=False,
            packing_date=end_date,
            status="APPROVED",
            machining_center_id=101,
            machining_center_code="1001",
            machining_center_name="seesaw加工中心",
            position_id=4394449921815388122,
            position_name="生豆仓",
            position_code="1010",
            packing_rule=22,
            remark="开发测试",
            target_material=4380310631497072641,
            target_material_code="5090280712",
            target_material_name="NOA 菠萝百香果慕斯85g",
            target_material_unit_id=4373346093845446657,
            target_material_unit_name="块",
            target_material_unit_spec="",
            packed_quantity=100,
            opened_position=True,
            items=[dict(
                id=4404587767264182272,
                product_id=4374806626750693377,
                product_code="1000",
                product_name="1000",
                product_type="product_type",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=1000,
                type="consumable",
            ), dict(
                id=4404587767289348096,
                product_id=4374806626499035137,
                product_code="1001",
                product_name="1001",
                product_type="product_type",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=100,
                type="material",
            )
            ]
        )
        res = client.UpdatePackingReceipts(packing_receipts_pb2.UpdatePackingReceiptsRequest(**request),
                                           metadata=metadata)
        print(res)


if __name__ == "__main__":
    # test_packing_receipts("CreatePackingReceipts")
    test_packing_receipts("ListPackingReceipts")
    # test_packing_receipts("GetPackingReceiptsDetail")
    # test_packing_receipts("UpdatePackingReceipts")


