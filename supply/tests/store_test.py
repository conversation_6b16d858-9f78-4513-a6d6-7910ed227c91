import grpc
import sys
import os
from pathlib import Path


base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.store import inventory_bi_pb2, inventory_bi_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.utils.helper import get_today_datetime, get_product_unit_map, get_product_map
from supply.client.inventory_service import metadata_service
from supply.client.cost_service import cost_service
_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(1)
partner_id = str(100)
# metadata = (
#     ("partner_id", partner_id),
#     ("user_id", user_id),
# )


def get_timestamp(value: datetime):
    # value = value - timedelta(hours=8)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


def test_summary_inventory():
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = inventory_bi_pb2_grpc.StoreInventoryBiServiceStub(channel=conn)
    start_datetime = datetime.strptime("2022-01-03 20:00:00", "%Y-%m-%d %H:%M:%S")
    end_datetime = datetime.strptime("2022-01-12 20:00:00", "%Y-%m-%d %H:%M:%S")
    start_date = get_timestamp(start_datetime)
    end_date = get_timestamp(end_datetime)
    request = dict(
        start_date=start_date,
        end_date=end_date,
        branch_id=4462866233712181249,
        product_ids=[4464067105527955457]
    )
    metadata = (
        ("partner_id", '1'),
        ("user_id", '100'),
    )
    response = client.SummaryInventoryByTime(inventory_bi_pb2.SummaryInventoryByTimeRequest(**request),
                                             metadata=metadata)
    return response


if __name__ == "__main__":
    # ret = test_summary_inventory()
    # print(ret)
    print( metadata_service.get_neg_inv_config(1026,1,"boh.store.return",store_id=4679554862445232129))
   # print(ReturnService.submit_return(1026,1,4759017682517393408))
    ret = test_summary_inventory()
    print(ret)

    # ret = test_summary_inventory()
    # print(ret)
    print( metadata_service.get_neg_inv_config(1026,1,"boh.store.adjust",store_id=4679554862445232129))
   # print(ReturnService.submit_return(1026,1,4759017682517393408))