#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import List
import sys

PROTO = 'proto'


def run():
    from pathlib import Path

    current_path = Path(__file__).absolute()
    source_path = current_path.parent.parent
    project_path = source_path.parent
    proto_path = source_path.joinpath(PROTO)
    wait_join_path = [source_path, project_path, proto_path]
    join_path(wait_join_path)

    return True


def join_path(paths: List[str]):
    for path in paths:
        if path not in sys.path:
            sys.path.append(str(path))

    return True


if __name__ == "__main__":
    run()
