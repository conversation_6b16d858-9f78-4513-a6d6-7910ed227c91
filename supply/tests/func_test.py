import grpc
import sys, os, unittest
from datetime import datetime
import json

TEST_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(TEST_DIR)
sys.path.append(TEST_DIR)
sys.path.append(os.path.join(TEST_DIR, "supply", "proto"))

# from ..api.receiving import ReceivingAPI
from supply.proto import receiving_pb2, receiving_pb2_grpc
from supply.proto import receiving_diff_pb2, receiving_diff_pb2_grpc
from supply.proto import returns_pb2, returns_pb2_grpc
from supply.proto import receiving_bi_pb2, receiving_bi_pb2_grpc
from supply.proto import receiving_diff_bi_pb2, receiving_diff_bi_pb2_grpc
from supply.proto import assets_pb2, assets_pb2_grpc
from supply.proto import inventory_bi_pb2, inventory_bi_pb2_grpc
from supply.proto.inventory import inventory_pb2, inventory_pb2_grpc
from supply.proto import inventory_cut_pb2, inventory_cut_pb2_grpc

from supply.proto import supply_pb2, supply_pb2_grpc
# from supply.client import cli
from google.protobuf.timestamp_pb2 import Timestamp
# from ..proto import receiving_pb2, receiving_pb2_grpc


_HOST = '127.0.0.1'
_PORT = '8686'

start_date=datetime(2020, 2, 12, 00, 55, 58, 620600)
start_date = Timestamp(seconds=int(start_date.timestamp()))

end_date=datetime(2020, 2, 15, 8, 00, 00, 000000)
end_date = Timestamp(seconds=int(end_date.timestamp()))

daily_date=datetime(2019, 3, 25, 8, 00, 00, 000000)
daily_date = Timestamp(seconds=int(daily_date.timestamp()))

metadata = (
    ("partner_id", "4183192445833445399"),
    ("user_id", "4201196624908648449"),
    ("sign", "fPqbVZsWaEGFUN59Yt5uuF4pJWzI7aBj+O7tsHAZc3GxmnMajBVc4WsNo8npY/9IAbaEj4xQzmh9bAcPDAL1d4Nr8NwFyg8XnezzOmBG6+084UeUA7K6XAp5UfCR7BxmTgPuA2g1HIixW0thMhQ4M+vrOXRVyHlhWCjMctPNBDQ="),
)

   
from supply.module.inventory_bi import inventory_bi_service
def test_inventory(num):
    if num == 1:
        res = inventory_bi_service.get_snapshot_for_sales(store_id=4219460795467038721, code='SALES', biz_date='2019-08-24 00:00:00', partner_id=2, user_id=4201196624908648449)
    return res


# from supply.module.receiving_diff import receiving_diff_service
# def test_diff(num):
#     if num == 1:
#         res = receiving_diff_service.get_unconfirmed_diffs(end_hours=0, end_mins=0, status=['INITED'], partner_id=2, user_id=4201196624908648449)

#     elif num == 2:
#         res = receiving_diff_service.auto_confirm(diff_id=4299477169997340673, status='INITED', owner='warehouse', partner_id=2, user_id=4201196624908648449)
#     return res

from supply.module.inventory_cut import inventory_cut_service
def test_inventory_service(num):
    if num == 1:
        date = datetime.now()
        end_date = datetime(date.year,date.month,(date.day),20,0,0)
        res = inventory_cut_service.cut_inventory_by_store(store_id=4217527475770490881, partner_id=2, user_id=4201196624908648449)
    elif num == 2:
        res = inventory_cut_service.auto_daily_cut_inventory(partner_id=2, user_id=4201196624908648449)
    elif num == 3:
        res = inventory_cut_service.cut_inventory_by_cost_center(
            cost_center_id=4306106746904018944,  
            partner_id=4183192445833445399, user_id=4201196624908648449,
            end_date='2020-01-01 20:00:00')
    elif num == 4:
        res = inventory_cut_service.trigger_cost_count_material(branch_id=4376292628312883201, 
                    branch_type='COSTCENTER', start_date='2020-12-31 20:00:00', end_date='2021-01-31 20:00:00', 
                    partner_id=4183192445833445399, user_id=4201196624908648449,task_type=4,
                                        report_type=1, category=[1,2,3,4,5])
    elif num == 5:

        end_date=datetime(2020, 2, 15, 8, 00, 00, 000000)
        end_date = Timestamp(seconds=int(end_date.timestamp()))
        res = inventory_cut_service.trigger_cost_count_bom(
                    region_type=0, period_group_by=0,
                    start_date='2020-02-10 20:00:00', end_date='2020-02-15 20:00:00', 
                    branch_type='COSTCENTER',
                    partner_id=4183192445833445399, user_id=4201196624908648449)

    return res

from supply.module.demand import demand_module
def test_demand_2(num):
    partner_id=100
    user_id=1
    if num == 1:
        demand_id = 4464770336644239361
        res = demand_module._generate_order_by_demand(demand_id)
    elif num == 2:
        res = demand_module().list_orders(
                start_arrival_date=start_date,
                end_arrival_date=end_date,
                start_date=start_date, 
                end_date=end_date,
                partner_id=partner_id,
                user_id=user_id
        )
    elif num == 3:
        result =  {4292227729347051520: [{'created_at': '2019-11-13 15:00:36', 'updated_at': '2019-11-13 15:00:36', 'id': 4292227730437570560, 'demand_order_id': 4292227729347051520, 'demand_product_id': 4289955141967183874, 'partner_id': 2, 'product_id': 4217605736626126849, 'category_id': 4217533887963725825, 'product_code': '********', 'product_name': '流沙牛角-大包装', 'line': 1, 'jde_line': 0.0, 'sale_type': 'NEW-RETAIL-NORMAL', 'product_type': 'FINISHED', 'unit_id': 4217533956788060161, 'unit_name': '个', 'unit_spec': 'EA', 'accounting_unit_id': None, 'accounting_unit_name': None, 'accounting_unit_spec': None, 'quantity': 7.0, 'distr_quantity': 7.0, 'accounting_quantity': 0.0, 'received_quantity': 0.0, 'received_accounting_quantity': 0.0, 'purchase_tax': 0.0, 'purchase_price': 0.0, 'distribution_circle': 'D', 'min_quantity': 1.0, 'max_quantity': 999.0, 'increment_quantity': 1.0, 'is_received': 0, 'unit_rate': 1.0, 'order_date': '2019-11-08 00:00:00', 'vacancy_id': None, 'is_vacancy': 0, 'status': None, 'price': 0.0}, {'created_at': '2019-11-13 15:00:36', 'updated_at': '2019-11-13 15:00:36', 'id': 4292227730542428160, 'demand_order_id': 4292227729347051520, 'demand_product_id': 4289955142017515521, 'partner_id': 2, 'product_id': 4217605737632759809, 'category_id': 4217533887963725825, 'product_code': '********', 'product_name': '咸蛋黄千层吐司', 'line': 2, 'jde_line': 0.0, 'sale_type': 'NEW-RETAIL-NORMAL', 'product_type': 'FINISHED', 'unit_id': 4217533956788060161, 'unit_name': '个', 'unit_spec': 'EA', 'accounting_unit_id': None, 'accounting_unit_name': None, 'accounting_unit_spec': None, 'quantity': 7.0, 'distr_quantity': 7.0, 'accounting_quantity': 0.0, 'received_quantity': 0.0, 'received_accounting_quantity': 0.0, 'purchase_tax': 0.0, 'purchase_price': 0.0, 'distribution_circle': 'D', 'min_quantity': 1.0, 'max_quantity': 999.0, 'increment_quantity': 1.0, 'is_received': 0, 'unit_rate': 1.0, 'order_date': '2019-11-08 00:00:00', 'vacancy_id': None, 'is_vacancy': 0, 'status': None, 'price': 0.0}], 4292227730391433216: [{'created_at': None, 'updated_at': None, 'id': None, 'demand_order_id': 4292227730391433216, 'demand_product_id': 4289955141983961088, 'partner_id': 2, 'product_id': 4217605737062334465, 'category_id': 4217533887963725825, 'product_code': '********', 'product_name': '芋头糯米糍', 'line': 1, 'jde_line': None, 'sale_type': 'NEW-RETAIL-NORMAL', 'product_type': 'FINISHED',  'unit_id': 4217533956788060161, 'unit_name': '个', 'unit_spec': 'EA', 'accounting_unit_id': None, 'accounting_unit_name': None, 'accounting_unit_spec': None, 'quantity': 7.0, 'distr_quantity': 7.0, 'accounting_quantity': 0.0, 'received_quantity': None, 'received_accounting_quantity': None, 'purchase_tax': 0.0, 'purchase_price': 0.0, 'distribution_circle': 'D', 'min_quantity': 1.0, 'max_quantity': 9999.0, 'increment_quantity': 1.0, 'is_received': None, 'unit_rate': 1.0, 'order_date': '2019-11-08 00:00:00', 'vacancy_id': None, 'is_vacancy': None, 'status': None, 'price': None}, {'created_at': None, 'updated_at': None, 'id': None, 'demand_order_id': 4292227730391433216, 'demand_product_id': 4289955142000738304, 'partner_id': 2, 'product_id': 4217605737448210433, 'category_id': 4217533887963725825, 'product_code': '********', 'product_name': '紫薯千层吐司', 'line': 2, 'jde_line': None, 'sale_type': 'NEW-RETAIL-NORMAL', 'product_type': 'FINISHED', 'unit_id': 4217533956788060161, 'unit_name': '个', 'unit_spec': 'EA', 'accounting_unit_id': None, 'accounting_unit_name': None, 'accounting_unit_spec': None, 'quantity': 7.0, 'distr_quantity': 7.0, 'accounting_quantity': 0.0, 'received_quantity': None, 'received_accounting_quantity': None, 'purchase_tax': 0.0, 'purchase_price': 0.0, 'distribution_circle': 'D', 'min_quantity': 1.0, 'max_quantity': 99999.0, 'increment_quantity': 1.0, 'is_received': None, 'unit_rate': 1.0, 'order_date': '2019-11-08 00:00:00', 'vacancy_id': None, 'is_vacancy': None, 'status': None, 'price': None}, {'created_at': None, 'updated_at': None, 'id': None, 'demand_order_id': 4292227730391433216, 'demand_product_id': 4289955142046875649, 'partner_id': 2, 'product_id': 4217605737838280705, 'category_id': 4217533887963725825, 'product_code': '********', 'product_name': '黑糖千层吐司', 'line': 3, 'jde_line': None, 'sale_type': 'NEW-RETAIL-NORMAL', 'product_type': 'FINISHED',  'unit_id': 4217533956788060161, 'unit_name': '个', 'unit_spec': 'EA', 'accounting_unit_id': None, 'accounting_unit_name': None, 'accounting_unit_spec': None, 'quantity': 7.0, 'distr_quantity': 7.0, 'accounting_quantity': 0.0, 'received_quantity': None, 'received_accounting_quantity': None, 'purchase_tax': 0.0, 'purchase_price': 0.0, 'distribution_circle': 'D', 'min_quantity': 1.0, 'max_quantity': 99999.0, 'increment_quantity': 1.0, 'is_received': None, 'unit_rate': 1.0, 'order_date': '2019-11-08 00:00:00', 'vacancy_id': None, 'is_vacancy': None, 'status': None, 'price': None}]}
        print('**********&&&&&&&&&&&&*************',type(result))
        res = demand_module.create_receive(order_map=result, partner_id=2, user_id=4201196624908648449)
    elif num == 4:
        demand_id = 4294718867661725697
        status = "APPROVED"
        partner_id = 4183192445833445399
        user_id = 4257138123667931137
        description = None
        res = demand_module().change_demand_status(demand_id, status, description, partner_id, user_id)

    elif num == 5:
        demand_id = 4464770336644239361
        res = demand_module().pre_generate_order_by_demand(demand_id, partner_id, user_id)
    
    elif num == 6:
        res = demand_module().get_valid_demand_product(type='RETURN', distribution_type='NMD',store_id=4273394092244729857)
    elif num == 7:
        product_ids = [4303603876243701761]
        res = demand_module().get_valid_store_product(product_ids, 'MD', 4183192445833445399, 4257138123667931137)

    return res


from supply.module.adjust_service import ads as adjust_schedule_service
def test_ad(num):
    partner_id=4183192445833445399
    user_id=4201196624908648449
    if num == 1:
        result = adjust_schedule_service.list_adjust_store_product(
            store_id=4279872997868699649, 
            target_date=start_date,
            # limit=limit, offset=offset,
            # include_total=include_total,
            # partner_id=partner_id, user_id=user_id,
            # search=search,
            search_fields="code, name", 
            partner_id=partner_id,
            user_id=user_id
            # category_ids=category_ids
        )

    elif num == 2:
        result = adjust_schedule_service.adjust_confirmed(
            adjust_id=4296590960467378176, 
            partner_id=partner_id, user_id=user_id, username='test'
        )
        
    return result

from supply.module.transfer_service import ssm as transfer_service
def test_transfer(num):
    partner_id=4183192445833445399
    user_id=4201196624908648449
    if num == 1:
        store_id = 4273394092244729857
        res = transfer_service.list_transfer_store_product(
                                    store_id=store_id, limit=-1, offset=0,
                                    include_total=False, search=None, 
                                    search_fields=None, 
                                    partner_id=partner_id, user_id=user_id, 
                                    category_ids=None)

    return res

from supply.client.metadata_service import metadata_service
def test_meta(num):
    partner_id=100
    user_id=197
    if num == 1:
        res = metadata_service.get_product_by_warehouse_id(warehouse_id=4303639323644788737, partner_id=partner_id, user_id=user_id)
    elif num == 2:
        # filters = {"status":"ENABLED"}
        res = metadata_service.list_entity(
                    schema_name='distrcenter',partner_id=partner_id, user_id=user_id)
    elif num == 3:
        branch_ids = []
        res = metadata_service.get_store_list(ids=branch_ids, partner_id=partner_id, user_id=user_id)
    elif num == 4:
        store_id = 4489381693400055808
        res = metadata_service.get_time_config(100, 197, "boh.store.return", store_id=store_id)
        print("===res")

    return res

from supply.client.report_service import report_service
def test_report():
    res = report_service.get_sales_explained_summary_report(
        start_date=start_date, end_date=end_date, region_type='STORE', period_group_by='BY_DAY', limit=-1,
        partner_id=4183192445833445399,user_id=4201196624908648449)
    return res

from supply.utils.helper import check_stores_costcenter_belonging
def test_helper():
    print('********')
    res = check_stores_costcenter_belonging(store_list=[4279906600585527297,4279901255846854657,4279908928646545409],
        partner_id=4183192445833445399,user_id=4201196624908648449)
    return res

from supply.utils.auth import two_branch_list_scope_check
def test_auth():
    print('********')
    res = two_branch_list_scope_check(partner_id=100, user_id=215, schema='store_extra', domain='boh.store_extra', receive_by_ids=[4566115032605392896])
    return res

from supply.model.demand.supply_demand_product import Supply_demand_product
def test_demand_main():
    res = Supply_demand_product.insert_by_demand_id_and_receive(
            demand_id=4598507616941998080, 
            receive_by=4598507616941998080,
            product_id=4525991346666143744,
            quanity=1, 
            partner_id=100,
            user_id=197,
            distribution_type="NMD")
    return res

from supply.model.receiving_diff import ReceivingDiffProductModel
def test_receive_diff():
    total, bi_data = ReceivingDiffProductModel.bi_get_receiving_diff_detailed(
        partner_id=100, branch_type="STORE", start_date=datetime(2022,2,28), end_date=datetime(2022,3,31))
    
    return total
def run():
    # response = test_report()
    # response = test_inventory(1)
    # response = test_diff(2)
    # response = test_stock_out(1)
    # response = test_demand(1)
    # response = test_inventory_service(4)
    # response = test_low_cost(3)
    # response = test_demand_2(1)
    # response = test_ad(2)
    # response = test_transfer(1)
    response = test_meta(4)
    # response = test_receive_diff()
    print(response)
if __name__ == '__main__':
    run()
