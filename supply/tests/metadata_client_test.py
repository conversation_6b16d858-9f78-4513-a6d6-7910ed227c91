
import logging
from load_path import load_path
load_path()

import unittest
from supply.client.metadata_service import metadata_service
from supply.client.receipt_service import receipt_service

# prod = [4467665361310842881, 4464067105527955457, 4498076488226045953]
prod = [4467665361310842881]
# prod = [4464067105527955457]  # 饼干
# prod = [4498076488226045953]  # 阿华田1
# prod = [4595943886425194496]
partner_id = 100
user_id = 100


class TestCase(unittest.TestCase):
    def setUp(self):
        self.metadataclient = metadata_service
        self.receipt_service = receipt_service
        self.product_ids = prod

    @unittest.skip
    def test_get_product_unit(self):
        unit = self.metadataclient.get_product_unit_dict(product_ids=self.product_ids, partner_id=100, user_id=100)
        print(unit)
        return unit

    # @unittest.skip
    def test_get_products(self):
        res = metadata_service.get_product_list(ids=self.product_ids, include_units=True,
                                                return_fields='name,code,bom_type', partner_id=100,
                                                user_id=100)
        print(res)
        return res

    # @unittest.skip
    def test_get_relation_products(self):
        product_relation_filters = None
        store_id = 4462866233712181249
        partner_id = 100
        user_id = 100
        search = None
        search_fields = None
        limit = -1
        offset = 0
        include_total = False
        res = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_transfer=True),
            product_filters={
                "status": "ENABLED",
                "bom_type__neq": 'MANUFACTURE'
            },
            product_search=search,
            product_search_fields=search_fields,
            user_id=user_id,
            offset=offset,
            limit=limit,
            include_total=include_total,
            include_product_units=True,
            return_fields="allow_transfer",
            include_product_fields='code,name,model_name,category,category_name,barcode',
            can_order=True,
            product_relation_filters=product_relation_filters,
            region="ATTRIBUTE_REGION"
        )
        products = res.get('rows')
        # print(products)
        res = get_transfer_product(products)
        for product in res:
            if product.get('allow_transfer'):
                print("*" * 80)
                print(product)
                print("-" * 80)
        return res

    @unittest.skip
    def test_get_delivery(self):
        transfer_id = 4622649848959631360
        res = receipt_service.list_deliverys(batch_id=transfer_id, partner_id=partner_id, user_id=user_id)
        print(res)

    @unittest.skip
    def test_warehouse_prod(self):
        res = {}
        branch_id = 4464757362338562049
        return_metadata_products = []
        branch_type = None
        return_all_products = None
        product_category_ids = None
        search_fields = None
        search = None
        try:
            if not return_metadata_products:
                # 先根据仓库拉取仓库关联的商品分类
                if branch_type == "MACHINING_CENTER":
                    entity = metadata_service.get_entity_by_id(id=branch_id, schema_name="MACHINING_CENTER",
                                                               partner_id=partner_id, user_id=user_id)
                else:
                    entity = metadata_service.get_entity_by_id(id=branch_id, schema_name="distrcenter",
                                                               partner_id=partner_id, user_id=user_id)
                if not entity:
                    # raise DataValidationException("主档未找到该组织！")
                    print('主档未找到该组织！')
                fields = entity.get("fields")
                relation = None
                if isinstance(fields, dict):
                    relation = fields.get("relation")
                if not relation or not relation.get("product_category"):
                    logging.info("该组织下未关联商品分类-{}".format(branch_id))
                    res["rows"] = {}
                    res["total"] = 0
                    return res

                product_category = relation.get("product_category")
                if product_category_ids:
                    product_category_ids = [str(category_id) for category_id in product_category_ids]
                    product_category_ids = list(set(product_category_ids) & set(product_category))
                else:
                    product_category_ids = product_category
                relation_filters = dict(product_category=product_category_ids)

            else:
                if product_category_ids:
                    product_category_ids = [str(category_id) for category_id in product_category_ids]
                    relation_filters = dict(product_category=product_category_ids)
                else:
                    relation_filters = None
            # 然后根据商品分类拉取商品主档
            if return_all_products is True:
                filters = None
            else:
                filters = {"status__eq": "ENABLED", "bom_type__neq": 'MANUFACTURE'}
            products_by_category = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                     include_units=True,
                                                                     filters=filters,
                                                                     return_fields="scope_id,name,code,second_code,\
                                                                     sale_type,product_type,bom_type,storage_type,\
                                                                     status,alias,category,updated,model_code,model_name,\
                                                                     default_receiving_deviation_min,\
                                                                     default_receiving_deviation_max,\
                                                                     default_purchase_deviation_min,\
                                                                     default_purchase_deviation_max,\
                                                                     ledger_class",
                                                                     partner_id=partner_id,
                                                                     user_id=user_id,
                                                                     search=search,
                                                                     search_fields=search_fields,
                                                                     include_total=True)
            products = products_by_category.get("rows") if products_by_category.get("rows") else {}
            products = get_transfer_product(products)
            printter_product(products)
            res["rows"] = products_by_category.get("rows") if products_by_category.get("rows") else {}
            res["total"] = products_by_category.get("total") if products_by_category.get("total") else 0

        except Exception as e:
            logging.error("根据仓库拉取商品列表失败:{}-{}-{}".format(branch_id, e))


def get_transfer_product(products):
    result = []
    for product in products:
        units = product.get('units')
        if check_transfer_unit(units):
            result.append(product)
    return result


def check_transfer_unit(units):
    transfer_available = False
    for unit in units:
        if unit.get('transfer'):
            transfer_available = True
            break
    return transfer_available


def printter_product(products):
    for product in products:
        print("*" * 80)
        print(product)
        print("-" * 80)


if __name__ == '__main__':
    unittest.main()
