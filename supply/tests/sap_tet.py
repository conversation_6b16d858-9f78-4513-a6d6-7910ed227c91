# TODO: delete
from itertools import product
import os
import unittest
import sys
file_path = os.path.abspath(__file__)
path = os.path.dirname(os.path.dirname(os.path.dirname(file_path)))

sys.path.append(os.path.join(path, "supply", "proto"))
sys.path.append(path)
print(sys.path)
# TODO

import grpc
from supply.proto.third_party.orderproductprice_pb2_grpc \
     import QuerySupplyOrderProductPriceStub
from supply.proto.third_party.orderproductprice_pb2 \
     import GetOrderProductPriceRequest
from supply.proto.third_party.finance_reconciliation_pb2_grpc \
     import FinanceReconciliationServiceStub
from supply.proto.third_party.finance_reconciliation_pb2 \
     import FinanceReconciliationReq

from supply import APP_CONFIG
from google.protobuf.json_format import MessageToDict
# channel = grpc.insecure_channel("127.0.0.1:8686")

from supply.module.returns import \
    get_tax, get_stub, get_tax_price, get_branch_list_map, \
    get_price_service, get_product_map
from supply.client.third_party import third_party


class ThirdPartyService(object):
    """
    third_party_host
    third_party_port
    """
    def __init__(self):
        self.third_party_stub = QuerySupplyOrderProductPriceStub(_CHANNEL)

    def get_third_party(self):
        pass

    def get_product_price(self, storeId=None, productId=None,
                          pageNum=None, pageSize=None, isAll=None):
        """
        """

        res = self.third_party_stub.GetOrderProductPrice(
            GetOrderProductPriceRequest(
                productId=productId, storeId=storeId,
                pageNum=pageNum, pageSize=pageSize, isAll=isAll
                )
        )
        res = MessageToDict(res)
        print(res)
        print('-'*30)


product_id_list = [4573748668976726015,
                   4573748668976726014,
                   4573748668976726013,
                   4573748668976726012,
                   4573748668976726017
                   ]
store_id = 4575923752583331845
vendor_id = 4575923752583331845
partner_id = 10000
user_id = 1
request = {
    "iGjahr": 2021,
    "iMonat": 3,
    "iKunnr": "200080",
    "iBukrs": "6300"
}
metadata = (
    ("partner_id", "100"),
    ("user_id", "1"),
    ("sign", "fPqbVZsWaEGFUN59Yt5uuF4pJWzI7aBj+O7tsHAZc3GxmnMajBVc4WsNo8npY/9IAbaEj4xQzmh9bAcPDAL1d4Nr8NwFyg8XnezzOmBG6+084UeUA7K6XAp5UfCR7BxmTgPuA2g1HIixW0thMhQ4M+vrOXRVyHlhWCjMctPNBDQ="),
)


class ThirdPartyTest(unittest.TestCase):
    def setUp(self):
        # self.client = FinanceReconciliationServiceStub(channel=channel)
        pass

    def test_statement(self):
        # req = FinanceReconciliationReq(**request)
        res = third_party.get_statements(**request)
        print(res)


if __name__ == "__main__":
    unittest.main()
