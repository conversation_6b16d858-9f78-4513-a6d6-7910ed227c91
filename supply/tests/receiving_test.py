import grpc
import sys, os, unittest
from datetime import datetime
import json

TEST_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(TEST_DIR)
sys.path.append(TEST_DIR)
sys.path.append(os.path.join(TEST_DIR, "supply", "proto"))

# from ..api.receiving import ReceivingAPI
from supply.proto import receiving_pb2, receiving_pb2_grpc
from supply.proto import receiving_diff_pb2, receiving_diff_pb2_grpc
from supply.proto import returns_pb2, returns_pb2_grpc
from supply.proto import receiving_bi_pb2, receiving_bi_pb2_grpc
from supply.proto.store_bi import receive_diff_bi_pb2, receive_diff_bi_pb2_grpc
from supply.proto import assets_pb2, assets_pb2_grpc
from supply.proto import inventory_bi_pb2, inventory_bi_pb2_grpc
from supply.proto.inventory import inventory_pb2, inventory_pb2_grpc
from supply.proto import inventory_cut_pb2, inventory_cut_pb2_grpc
from supply.proto.store import receive_diff_pb2_grpc as store_receive_diff_pb2_grpc, receive_diff_pb2

from supply.proto import supply_pb2, supply_pb2_grpc
# from supply.client import cli
from google.protobuf.timestamp_pb2 import Timestamp
# from ..proto import receiving_pb2, receiving_pb2_grpc


_HOST = '127.0.0.1'
_PORT = '8686'

start_date=datetime(2021, 7, 31, 00, 00, 00, 000000)
start_date = Timestamp(seconds=int(start_date.timestamp()))

end_date=datetime.now()
end_date = Timestamp(seconds=int(end_date.timestamp()))

arrival_start_date=datetime(2019, 12, 12, 00, 00, 00, 0000000)
arrival_start_date = Timestamp(seconds=int(arrival_start_date.timestamp()))

arrival_end_date=datetime(2019, 12, 15, 23, 59, 59, 000000)
arrival_end_date = Timestamp(seconds=int(arrival_end_date.timestamp()))

daily_date=datetime(2019, 3, 25, 8, 00, 00, 000000)
daily_date = Timestamp(seconds=int(daily_date.timestamp()))

metadata = (
    ("partner_id", "100"),
    ("user_id", "1")
    )


def test_receiving(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT, options=[
                ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
            ],)
    client = receiving_pb2_grpc.ReceivingServiceStub(channel=conn)
    if num == 1:
        product = [{'product_id':4175534073151750145, 'product_code':'30990009', 'product_name':'吐司', 'unit_id':4168339425442398209,
                        'unit_name':'测试'}]
        # print(delivery_date)
        res = client.CreateReceiving(receiving_pb2.CreateReceivingRequest(request_id=1111111, 
                demand_order_id=777575757577575775, master_id=1111, delivery_date=start_date, 
                code="190304",products=product, is_adjust=True), metadata=metadata)
    elif num == 2:
        res = client.GetReceivingById(receiving_pb2.GetReceivingByIdRequest(id=4294073338320388096),metadata=metadata)
    elif num == 3:
        res = client.GetReceivingProductById(receiving_pb2.GetReceivingProductByIdRequest(id=4306788715051704321, sort='asc', order='product_code'), metadata=metadata)
    elif num == 4:
        # 非盲，有差异
        products = [{'id': 4295154536502239233,'confirmed_quantity':2}]
        res = client.ConfirmReceiving(receiving_pb2.ConfirmReceivingRequest(id=4295154536467660800, 
                            confirmed_products=products), metadata=metadata)
    elif num == 5:
        # 盲收
        res = client.ConfirmReceiving(receiving_pb2.ConfirmReceivingRequest(id=4205571248696008705, signature='test', nosign_reason='test'), metadata=metadata)
    elif num == 6:
        res = client.ListReceiving(receiving_pb2.ListReceivingRequest(
            start_date=start_date, 
            end_date=end_date,
            status=["INITED"],
            logistics_type='NMD',
            # code='521911280016',
            order_id=121911280041,
            # order_id=4297739369375817729,
            sort='asc',order='code'), metadata=metadata)
    elif num == 7:
        res = client.ListReceivingCold(receiving_pb2.ListReceivingColdRequest(start_date=start_date, end_date=end_date), metadata=metadata)
    return res

def test_receiving_diff(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = receiving_diff_pb2_grpc.ReceivingDiffServiceStub(channel=conn)
    if num == 1:
        res = client.GetReceivingDiffById(receiving_diff_pb2.GetReceivingDiffByIdRequest(id=4186054133255512065))
    elif num == 2:
        res = client.ListReceivingDiff(receiving_diff_pb2.ListReceivingDiffRequest(
            start_date=start_date, end_date=end_date,
            sort='desc',order='code'
            # status=['INITED','CONFIRMED']
            ),metadata=metadata)
    elif num == 3:
        res = client.GetReceivingDiffProductById(receiving_diff_pb2.GetReceivingDiffProductByIdRequest(id=4225571882140061697,sort='desc',order='product_code'), metadata=metadata)
    elif num == 4:
        res = client.SubmitReceivingDiff(receiving_diff_pb2.SubmitReceivingDiffRequest(id=4203094158779056129), metadata=metadata)
    elif num == 5:
        products = [{'id':4196905468339392513, 'reason_type':'01', 's_diff_quantity':5, 'd_diff_quantity':5}]
        res = client.UpdateReceivingDiff(receiving_diff_pb2.UpdateReceivingDiffRequest(id=4196905468121288705,products=products), metadata=metadata)
    elif num == 6:
        # {"receiving_id":"4200134519912710145","confirmed_products":[{"product_id":"4192678497547714561","diff_quantity":99,"reason_type":"货物损坏","remark":"88"}]}
        # products = [{'id': 4205609408657895425, 'diff_quantity': 1, 'reason_type': "hhh", 'remark': "88"}]
        res = client.CreateReceivingDiff(receiving_diff_pb2.CreateReceivingDiffRequest(receiving_id=4295441933978353665), metadata=metadata)
    elif num == 7:
        res = client.RejectReceivingDiff(receiving_diff_pb2.RejectReceivingDiffRequest(id=4188214890547101697))
    elif num == 8:
        res = client.ListReceivingDiffCold(receiving_diff_pb2.ListReceivingDiffColdRequest(
            start_date=start_date, end_date=end_date, receiving_code='141904240022',sort='desc',order='receiving_code',
            status=['INITED','CONFIRMED']), metadata=metadata)
    elif num == 9 :
        products = [{'id':4194079233720901633, 'reason_type':'02', 'remark':'gagaggagaga'}]
        res = client.UpdateReceivingDiffRemark(receiving_diff_pb2.UpdateReceivingDiffRemarkRequest(id=4193649309000404993, products=products), metadata=metadata)
    elif num == 10:
        products = [{'product_id':4192678497547714561, 'remark':'gagaggagaga','diff_quantity':20,'reason_type':'222'}]
        res = client.UpdateReceivingDiffCold(receiving_diff_pb2.UpdateReceivingDiffColdRequest(id=4200180532803919873, products=products),metadata=metadata)
    elif num == 11:
        res = client.ConfirmReceivingDiff(receiving_diff_pb2.ConfirmReceivingDiffRequet(id=4419795957379497984),metadata=metadata)

    return res

def test_returns(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = returns_pb2_grpc.ReturnServiceStub(channel=conn)
    if num == 1:
        # products = [{'product_id':4197653456426631169, 'unit_id':4192944616003076097, 'quantity':2}]
        products = [
                        {
                            "product_id":4303603805901029377,
                            "product_code":"10100102",
                            "product_name":"葱段",
                            "quantity":1,
                            "unit_id":4303604247137615873,
                            "unit_name":"斤",
                            "confirmed_quantity":45.38,
                            "unit_rate":1
                        },
                        {
                            "product_id":4303603806664392705,
                            "product_code":"10100098",
                            "product_name":"蒜米",
                            "quantity":1,
                            "unit_id":4303604247137615873,
                            "unit_name":"斤",
                            "confirmed_quantity":100,
                            "unit_rate":1
                        },
                        {
                            "product_id":4303603807436144641,
                            "product_code":"10100101",
                            "product_name":"葱花",
                            "quantity":1,
                            "unit_id":4303604247137615873,
                            "unit_name":"斤",
                            "confirmed_quantity":100,
                            "unit_rate":1
                        }
                    ]
        res = client.CreateReturn(returns_pb2.CreateReturnRequest(
                    sub_type="store",
                    # is_adjust=True,
                    return_by=4279909169156325377,
                    return_delivery_date=end_date, 
                    products=products, 
                    receiving_id=521912280015,
                    logistics_type='PUR',
                    return_delivery_number='4303613959019167745'
                    ), metadata=metadata)
    elif num == 2:
        res = client.GetReturnById(returns_pb2.GetReturnByIdRequest(id=4189664861540040705))
    elif num == 3:
        res = client.GetReturnProductById(returns_pb2.GetReturnProductByIdRequest(id=4307016521631137792, sort='asc', order='product_code'),metadata=metadata)
    elif num == 4:
        res = client.SubmitReturn(returns_pb2.SubmitReturnRequest(id=4308538261033189376),metadata=metadata)
    elif num == 5:
        res = client.ConfirmReturn(returns_pb2.ConfirmReturnRequet(id=4199127525920337921), metadata=metadata)
    elif num == 6:
        res = client.ListReturn(returns_pb2.ListReturnRequest(
                            start_date=start_date, 
                            end_date=end_date, 
                            # logistic_type='PUR',
                            sub_type='warehouse',
                            type='NBO',
                            store_ids=[4303639323212775425]), metadata=metadata)
    elif num == 7:
        res = client.ListReturnCold(returns_pb2.ListReturnColdRequest(start_date=start_date, 
                            end_date=end_date, 
                             limit=5, sort='desc', order='code'), metadata=metadata)
    elif num == 8:
        res = client.DeleteReturn(returns_pb2.DeleteReturnRequest(id=4189664861540040705))
    elif num == 9:
        product = [
                    {
                        "product_id":4308559778395717632,
                        "product_code":"10100102",
                        "product_name":"葱段",
                        "quantity":2,
                        "unit_id":4303604247137615873,
                        "unit_name":"斤",
                        "confirmed_quantity":45.38,
                        "unit_rate":1
                    },
                    {
                        "product_id":4308561376257769472,
                        "product_code":"10100109",
                        "product_name":"小米椒",
                        "quantity":1,
                        "unit_id":4303604247137615873,
                        "unit_name":"斤",
                        "confirmed_quantity":100,
                        "unit_rate":1
                    },
                    {
                        "product_id":4308559778408300544,
                        "unit_id":4303604247137615873,
                        "quantity":0
                    },
                    {
                        "product_id":4308559778425077760,
                        "unit_id":4303604247137615873,
                        "quantity":0
                    }
                ]
        res = client.UpdateReturn(returns_pb2.UpdateReturnRequest(id=4308559778282471424,
                            products=product,
                            return_reason='xixixi', remark='haaaaaa', return_delivery_date=end_date),metadata=metadata)
    elif num == 10:
        res = client.RejectReturn(returns_pb2.RejectReturnRequest(id=4193368649228996609, reject_reason='hahaha'))
    elif num == 11:
        res = client.ApproveReturn(returns_pb2.ApproveReturnRequest(id=4306803284650979329), metadata=metadata)
    elif num == 12:
        res = client.DeliveryReturn(returns_pb2.DeliveryReturnRequet(id=4302025694836162560), metadata=metadata)
    elif num == 13:
        res = client.CheckReturnAvailableByrec(returns_pb2.CheckReturnAvailableRequest(
            return_id = 4373354522687340544,
            # return_delivery_date="2020-07-05T16:00:00.000Z",
            receiving_id=522006240001,
            products=[
                {
                    "id":4373354524058877952,
                    "product_id":4303603816059633665,
                    "quantity":3}]

        ), metadata=metadata)
    return res

def test_bi(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = receiving_bi_pb2_grpc.ReceivingBiServiceStub(channel=conn)
    if num == 1:
        res = client.GetReceivingCollect(receiving_bi_pb2.GetReceivingCollectRequest(
                st_ids=[4176264529149165569, 4176234574621179905],
                product_name='红糖',
                start_date=start_date, end_date=end_date), metadata=metadata)
    elif num == 2:
        res = client.GetReceivingDetail(receiving_bi_pb2.GetReceivingDetailRequest(
                start_date=start_date, 
                end_date=end_date,
                # category_ids=[4197637974420094977, 4170760698323795969],
                product_name='红糖',
                st_ids=[4176234574621179905]), metadata=metadata)
    return res

def test_diff_bi(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = receive_diff_bi_pb2_grpc.StoreReceiveDiffBiServiceStub(channel=conn)
    if num == 1:
        res = client.GetReceiveDiffCollect(receive_diff_bi_pb2.GetReceiveDiffCollectRequest(
                                # st_ids=[4176264529149165569, 4176234574621179905],
                                # product_name='红糖',
                                branch_type="STORE",
                                start_date=start_date, 
                                end_date=end_date), metadata=metadata)
    elif num == 2:
        res = client.GetReceiveDiffDetail(receive_diff_bi_pb2.GetReceiveDiffDetailRequest(
                                                                start_date=start_date, 
                                                                end_date=end_date, 
                                                                branch_type="STORE"), metadata=metadata)
    return res

def test_assets(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = assets_pb2_grpc.AssetsRecServiceStub(channel=conn)
    if num == 1:
        products= [{'litm':'40100522', 'uom':'TI', 'uorg':2}]
        res = client.CreateAssetsReceiving(assets_pb2.CreateAssetsReceivingRequest(kcoo='01211', doco=1900501, mcu='3103', an8=2000001,products=products), 
                        metadata=metadata)
    elif num == 2:
        products = [{'id':4198019309937459201, 'quantity':2}]
        res = client.ConfirmAssetsReceiving(assets_pb2.ConfirmAssetsReceivingRequest(id=4198019309895516161,products=products),metadata=metadata)
    elif num == 3:
        res = client.ListAssetsReceiving(assets_pb2.ListAssetsReceivingRequest(start_date=start_date, 
                                                                end_date=end_date),metadata=metadata)
    elif num == 4:
        res = client.GetAssetById(assets_pb2.GetAssetByIdRequest(id=4195965579758301185),metadata=metadata)
    elif num == 5:
        res = client.ListAssetProductsById(assets_pb2.ListAssetProductsByIdRequest(id=4195965579758301185),metadata=metadata)
    return res

def test_inventory(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = inventory_bi_pb2_grpc.InventoryBiServiceStub(channel=conn)
    if num == 1:
        branch_ids = [4279873883785396225]
        # category_ids = ['4197653456426631169']
        product_ids =[4483202658273853441]
        # category_ids=category_ids
        res = client.RealtimeInventory(inventory_bi_pb2.RealtimeInventoryRequest(
                # branch_ids=branch_ids, 
                limit=10,
                order='asc',
                offset=20,
                # branch_type='WAREHOUSE',
                product_ids=product_ids,
                # category_ids=category_ids,
                ), metadata=metadata)

    elif num == 2:
        # branch_ids = [4176251636538146817, 4176234574621179905]
        branch_id = 4188259202264727553
        product_ids= [4175525475252174849]
        res = client.DailyInventory(inventory_bi_pb2.DailyInventoryRequest(branch_id=branch_id, 
                        start_date = end_date,
                        end_date = daily_date,
                        product_ids=product_ids
                        ), metadata=metadata)

    elif num == 3:
        batch_id = 4176251636538146817
        res =  client.QueryAccouting(inventory_bi_pb2.QueryAccoutingRequest(batch_id=batch_id), metadata=metadata)

    elif num == 4:
        branch_id = 4303639323212775425
        product_id = [4303603783239204865]
        res = client.QueryInventoryLog(inventory_bi_pb2.QueryInventoryLogRequest(
                        branch_id=branch_id,
                        product_ids=product_id, 
                        start_date=start_date, 
                        end_date=end_date
                        ), metadata=metadata)

    elif num == 5:
        mcu = '3002'
        res = client.QueryMcuRealtimeInventory(inventory_bi_pb2.QueryMcuRealtimeInventoryRequest(mcu=mcu), metadata=metadata)

    elif num == 6:
        res = client.QuerySnapshotForSales(inventory_bi_pb2.QuerySnapshotForSalesRequest(
            store_id = 4217527472486350849,
            biz_date = '2019-06-08 00:00:00',
            code = 'SALES'
        ),metadata=metadata)
    

    return res

def cut_inventory(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = inventory_cut_pb2_grpc.InventoryCutServiceStub(channel=conn)
    if num == 1:
        branch_id = 4175534073151750145
        product_ids = [4175534073151750145]
        res = client.CutDailySnapshot(inventory_cut_pb2.CutDailySnapshotRequest(
                branch_id=branch_id,
                product_ids = product_ids,
                end_date=daily_date), metadata=metadata)
    elif num == 2:
        res = client.TaskCallBack(inventory_cut_pb2.TaskCallBackRequest(
            requestID=4325925895497842688,
        success=1,
        message='yes!', 
        category=1), metadata=metadata)
    
    elif num == 3:
        res = client.ListCostTriggerLog(inventory_cut_pb2.ListCostTriggerLogRequest(
            start_time=start_date, 
            end_time=end_date
            ), metadata=metadata)
    elif num == 4:
        res = client.ListPeriod(inventory_cut_pb2.ListPeriodRequest(
            branch_id=4306106746904018944
            ), metadata=metadata)

    return res

def test_get_valid():
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = supply_pb2_grpc.supplyStub(channel=conn)
    res = client.GetValidProductByStoreId(supply_pb2.GetValidProductByStoreIdRequest(
        id=4176234574621179905,
        # type='HD',
        # distribution_type='PUR',
        # vendor_id=4192974846411407361
    ), metadata=metadata)
    return res

from supply.module.receive_diff import receiving_diff_service

from supply.module.inventory_cut import inventory_cut_service
def test_auto_daily_cut_inventory():
    # partner_id = 2
    res = inventory_cut_service.auto_daily_cut_inventory(partner_id = 2, user_id=4186056888460247152)

from supply.client.metadata_service import metadata_service
def test_meta():
    low_cost_list=['IN01']
    partner_id=2,
    user_id=4186056888460247152
    list_products = metadata_service.get_product_list(
        filters={'ledger_class__in': low_cost_list},
        partner_id=partner_id, user_id=user_id).get('rows')
    return list_products

# from supply.module.demand import demand_module
# def test_produ():
#     partner_id=2
#     user_id=4201196624908648449
#     demand_id=4250581285113434113
#     res = demand_module.get_demand_product(demand_id, partner_id, user_id)
#     return res

def test_demand(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = supply_pb2_grpc.supplyStub(channel=conn)
    if num == 1:
        demand_id=4294038615365619713
        res = client.GetDemandProductDetail(supply_pb2.IdRequest(
            id=4294038615365619713,
            # type='HD',
            # distribution_type='PUR',
            # vendor_id=4192974846411407361
        ), metadata=metadata)
    elif num == 2:
        res = client.ListDemandOrder(supply_pb2.ListDemandOrderRequest(
            start_date=start_date,
            end_date=end_date,
            start_arrival_date=arrival_start_date,
            end_arrival_date=arrival_end_date,
            limit=10,
            offset=0
        ),metadata=metadata)
    
    elif num == 3:
        res = client.GetDemandOrderProductDetail(supply_pb2.IdRequest(
            id=4292523125646557184
        ), metadata=metadata)

    elif num == 4:
        res = client.GetDemandOrderDetail(supply_pb2.IdRequest(
            id=4292523125646557184
        ), metadata=metadata)
    
    elif num == 5:
        res = client.GetDistributionByDemand(supply_pb2.IdRequest(
            id = 4295150303690813441
        ), metadata=metadata)
        
    elif num == 6:
        res = client.CreateProductMain(supply_pb2.ProductMainRequest(
            demand_date=end_date,
            arrival_date=end_date,
            batch_id=1625037970744,
            type="MD",
            sub_type="PRODUCT",
            items=[
                    {
                        "store_id": 4462971415133454336,
                        "product_items": [
                            {
                                "distribution_type": "PAD",
                                "distribution_by": 4464056402230378497,
                                "product_id": 4464067105527955457,
                                "quantity": 2
                            },
                            {
                                "distribution_type": "PUR",
                                "distribution_by": 4487912836625858561,
                                "product_id": 4464067105527955457,
                                "quantity": 3
                            }
                        ]
                    }
                ]
        ), metadata=metadata)
    
    elif num == 7:
        res = client.UpdateDemandProduct(supply_pb2.UpdateDemandProductRequest(
            order_date=end_date,
            demand_id = 4327378455903780865,
            product = [
                                {
                                    "id":4327378456285462529,
                                    "product_id":4303603806664392705,
                                    "quantity":0.000,
                                    "tag_type":"RAW"
                                },
                                {
                                    "id":4327378456285462530,
                                    "product_id":4303603807436144641,
                                    "quantity":0.000,
                                    "tag_type":"RAW"
                                },
                                {
                                    "id":4327378456285462531,
                                    "product_id":4303603808572801025,
                                    "quantity":0.000,
                                    "tag_type":"RAW"
                                }
                            ]
        ), metadata=metadata)

    return res

from supply.proto import low_cost_pb2, low_cost_pb2_grpc
def test_low_cost(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = low_cost_pb2_grpc.LowCostServiceStub(channel=conn)
    if num == 1:
        res = client.ListLowCost(low_cost_pb2.ListLowCostRequest(
            # store_ids=[4217527476508688385],
            product_ids=[4217605218503753729],
            start_date=start_date, 
            end_date=end_date
        ), metadata=metadata)
    return res



from supply.proto import stocktake_pb2, stocktake_pb2_grpc
def test_st(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = stocktake_pb2_grpc.stocktakeStub(channel=conn)
    if num == 1:
        req = stocktake_pb2.GetStocktakeRequest(
            start_date=start_date,
            end_date=end_date,
            status=[0,1,2,3,4,5])
        res = client.GetStocktake(req, metadata=metadata)

    return res

from supply.proto import attachments_pb2, attachments_pb2_grpc
def test_attachments(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = attachments_pb2_grpc.AttachmentsServiceStub(channel=conn)
    if num == 1:
        res = client.CreateAttachments(attachments_pb2.NewAttachmentsList(
            doc_id = 4356360146315640832,
            doc_type = 'PUR',
            attachments = ['https://next-picture.oss-cn-shanghai.aliyuncs.com/heytea/头像-1588918790005.jpg'],
            signature = '111111111111'
        ), metadata=metadata)

    elif num == 2:
        res = client.UpdateAttachmentsByDocId(attachments_pb2.NewAttachmentsList(
            doc_id = 4302328230894882817,
            doc_type = 'PUR_REC'
        ), metadata=metadata)
    
    elif num == 3:
        res = client.GetAttachmentsByDocId(attachments_pb2.DocIdRequest(
            doc_id = 4356052079041503233,
            doc_type = 'PUR'
        ), metadata=metadata)


    return res

from supply.proto.third_party import demand_pb2_grpc as tp_demand_pb2_grpc, demand_pb2 as tp_demand_pb2
def test_tp(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = tp_demand_pb2_grpc.TpDemandStub(channel=conn)
    if num == 1:
        products= [{'litm':'40100522', 'uom':'TI', 'uorg':2}]
        res = client.CreateMainDemand(tp_demand_pb2.CreateMainDemandRequest(
            demand_date = end_date,
            arrival_date = end_date,
            remark = "Hello",
            store_code = "01006",
            products = [{
                            "product_code": "2021091702",
                            "quantity": 1.0,
                            "distribution_type": "NMD"
                            }],
            code = "123456789222"
        ), metadata=metadata)
        return res

def test_store_diff(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = store_receive_diff_pb2_grpc.StoreReceiveDiffServiceStub(channel=conn)
    if num == 0:
        msg = {"receiving_id":4695241982423568385,
               "attachments":'["https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/xlbkdI5EHVDW7fa0343fe46399ec8b40029a3103eb32-1669804389496.jpeg"]',
               "confirmed_products":[{"id":4695241982520037377,"product_id":4568408934582648832,
                                      "product_code":"10086",
                                      "product_name":"养乐多X",
                                      "unit_name":"袋",
                                      "received_quantity":200,
                                      "unit_id":4485022282686693376,
                                      "confirmed_quantity":200,
                                      "d_diff_quantity":1,
                                      "reason_type":"包装破损",
                                      "diff_quantity":1}],
               "remark":"222","lan":"zh-CN"}
        res = client.CreateReceivingDiff(
            receive_diff_pb2.CreateReceivingDiffRequest(**msg), metadata=metadata)
        print("res==", res)
        return res




def run():
    # response = test_attachments(3)
    # response = test_receiving(3)
    # response = test_receiving_diff(11)
    # response = test_receiving_diff(11)
    # response = test_bi(1)
    # response = test_tp(1)
    # response = test_assets(3)
    # response = test_inventory(1)
    # response = test_produ()
    # response = test_low_cost(1)
    # response = test_stock_out(1)
    # response = test_havi(1)
    # response = test_demand(6)
    # response = test_st(1)
    # response = cut_inventory(4)
    response = test_store_diff(0)
    print(response)
if __name__ == '__main__':
    run()
