import os
import sys
import grpc
import unittest

from unittest.case import skip
from datetime import datetime, time
from google.protobuf.timestamp_pb2 import Timestamp

file_path = os.path.abspath(__file__)
path = os.path.dirname(os.path.dirname(os.path.dirname(file_path)))

sys.path.append(os.path.join(path, "supply", "proto"))
sys.path.append(path)

from supply.utils.time import datetime_slice_to_time, time_convert_to_timedelta
from supply.proto.store.demand_pb2 import UpdateDemandInfoRequest
from supply.proto.store.demand_pb2_grpc import StoreDemandServiceStub
from supply.module.demand import demand_module

today = datetime.today()
now = time()
expect_time = Timestamp(seconds=int(today.timestamp()))
metadata = (
    ("partner_id", "100"),
    ("user_id", "1"),
    ("sign", "fPqbVZsWaEGFUN59Yt5uuF4pJWzI7aBj+O7tsHAZc3GxmnMajBVc4WsNo8npY/9IAbaEj4xQzmh9bAcPDAL1d4Nr8NwFyg8XnezzOmBG6+084UeUA7K6XAp5UfCR7BxmTgPuA2g1HIixW0thMhQ4M+vrOXRVyHlhWCjMctPNBDQ="),
)
channel = grpc.insecure_channel("127.0.0.1:8686")


class Test(unittest.TestCase):
    def setUp(self):
        self.client = StoreDemandServiceStub(channel=channel)

    @skip
    def test_get_time(self):
        res = datetime_slice_to_time(today)
        print(res)

    @skip
    def test_get_timedelta(self):
        res = time_convert_to_timedelta(now)
        print(res)

    @skip
    def test_demand_service_stub(self):
        info = {
            "id": 4464770336644239361,
            "expect_time": expect_time
        }
        request = UpdateDemandInfoRequest(**info)
        res = self.client.UpdateDemandInfo(request, metadata=metadata)
        print(res)

    def test_gen_order_by_demand(self):
        demand_id = 4464770336644239361
        demand_module._generate_order_by_demand(demand_id)


if __name__ == "__main__":
    unittest.main()
