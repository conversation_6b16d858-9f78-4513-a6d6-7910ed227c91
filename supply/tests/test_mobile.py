import grpc
import sys
import os
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.mobile import mobile_transfer_pb2, mobile_transfer_pb2_grpc, mobile_self_picking_pb2, \
    mobile_self_picking_pb2_grpc, mobile_adjust_pb2, mobile_adjust_pb2_grpc, mobile_stocktake_pb2, \
    mobile_stocktake_pb2_grpc, mobile_common_pb2, mobile_common_pb2_grpc, mobile_franchisee_demand_pb2,\
    mobile_franchisee_demand_pb2_grpc, mobile_franchisee_refund_pb2_grpc, mobile_franchisee_refund_pb2, \
    mobile_franchisee_receive_diff_pb2_grpc, mobile_franchisee_receive_diff_pb2, \
    mobile_franchisee_return_pb2_grpc, mobile_franchisee_return_pb2
from supply.proto.store import receive_diff_pb2_grpc, receive_diff_pb2
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.client.bom_service import Bom_segrvice
from supply.client.inventory_service import metadata_service
from google.protobuf.json_format import MessageToDict

_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(278)
partner_id = str(1026)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
)


def get_timestamp(value: datetime):
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


# start = datetime.now() - timedelta(days=5)
# end = start + timedelta(days=10)
start = datetime.strptime("2023-01-01 00:00:00", "%Y-%m-%d %H:%M:%S")
end = datetime.strptime("2023-01-10 07:59:59", "%Y-%m-%d %H:%M:%S")
start_date = get_timestamp(start)
end_date = get_timestamp(end)
datetime_now = datetime.utcnow()
dt_now = get_timestamp(datetime_now)


def test_common_service(num):
    ret = None
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_common_pb2_grpc.MobileCommonServiceStub(channel=conn)
    if num == 1:
        request = dict(
            store_id=4589322090032889856,
            end_date=dt_now
        )
        ret = client.GetUnfinishedDoc(mobile_common_pb2.GetUnfinishedDocRequest(**request), metadata=metadata)
    return ret


def test_mobile_transfer_client(num):
    res = ''
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_transfer_pb2_grpc.mobileTransferStub(channel=conn)
    # 查询调拨单1
    if num == 1:
        request = {
            'include_total': True,
            'status': [1, 2, 3, 4],
            'shipping_stores': [4373457990955565057],
            # 'receiving_stores':[4176251636538146817],
            # 'order': 'des',
            # 'offset': 0,
            # 'limit': 10,
            "types": ["AUTO", "MANUAL"],
            "sub_type": "EXTERNAL",
            "branch_type": "STORE",
            'start_date': start_date,
            'end_date': dt_now
        }
        res = client.GetTransfer(mobile_transfer_pb2.GetTransferRequest(**request), metadata=metadata)
    # 查询一个调拨单2
    if num == 2:
        request = {'transfer_id': 4455311079655899136}
        res = client.GetTransferByID(mobile_transfer_pb2.GetTransferByIDRequest(**request), metadata=metadata)
    # 取得门店可调拨商品3
    if num == 3:
        request = {'store_id': 4480742332378939392,
                   'include_total': True
                   }
        res = client.GetTransferProductByBranchID(mobile_transfer_pb2.GetTransferProductByBranchIDRequest(**request),
                                                  metadata=metadata)
    # 查询相同属性区域门店4
    if num == 4:
        request = {'store_id': 4480742332378939392}
        res = client.GetTransferRegionByBranchID(mobile_transfer_pb2.GetTransferRegionByBranchIDRequest(**request),
                                                 metadata=metadata)
    # 获取一个调拨单商品5
    if num == 5:
        request = {'transfer_id': 4485417444675977216}
        res = client.GetTransferProductByTransferID(
            mobile_transfer_pb2.GetTransferProductByTransferIDRequest(**request),
            metadata=metadata)
    # 创建调拨单6
    if num == 6:
        request = {"request_id": 16188210858955469,
                   "shipping_store": 4462866233712181249,
                   "receiving_store": 4480735571215384577,
                   "sub_type": "EXTERNAL",
                   "branch_type": "STORE",
                   "type": "MANUAL",
                   "products": [
                       {"product_id": 4464067105527955457,
                        "unit_id": 4373346031270625281,
                        "quantity": 100.00}, ],
                   "remark": "开发测试外部调拨",
                   "shipping_date": dt_now, "transfer_date": dt_now}
        res = client.CreateTransfer(mobile_transfer_pb2.CreateTransferRequest(**request), metadata=metadata)
    # 修改调拨单7
    if num == 7:
        request = {'transfer_id': 4187507402695839745,
                   "remark": "不要了2",
                   'transfer_date': end,
                   # 'products': [
                   #     {'product_id': 4175525475252174849, 'unit_id': 4168339425442398209, 'quantity': 2256.000000},
                   #    ]}
                   }
        res = client.UpdateTransfer(mobile_transfer_pb2.UpdateTransferRequest(**request), metadata=metadata)
    # 确认调拨单8
    if num == 8:
        request = {'transfer_id': 4236101707128905729

                   }
        res = client.ConfirmTransfer(mobile_transfer_pb2.ConfirmTransferRequest(**request), metadata=metadata)
    # 删除调拨单商品10
    if num == 10:
        request = {
            "ids": [
                4464722067543064576
            ],
            "transfer_id": 4464722067392069632
        }
        res = client.DeleteTransferProduct(mobile_transfer_pb2.DeleteTransferProductRequest(**request),
                                           metadata=metadata)
    # 删除调拨单11
    if num == 11:
        request = {'transfer_id': 4031638116133359617}
        res = client.DeleteTransfer(mobile_transfer_pb2.DeleteTransferRequest(**request))

    # 提交调拨单12
    if num == 12:
        request = {'transfer_id': 4194105194420162561, 'receiving_store': 4176251636538146817,
                   'products': [
                       {'product_id': 4374806621570727937, 'unit_id': 4373346031270625281, 'quantity': 10.000000},
                   ]
                   }
        res = client.SubmitTransfer(mobile_transfer_pb2.SubmitTransferRequest(**request), metadata=metadata)
    # 调拨单报表
    if num == 13:
        print(start, "--", end)
        request = {
            # 'st_ids' :[4176265632020430849],
            'store_ids': [],
            'category_ids': [],
            # 'product_name' :'达芬奇果美经典焦糖风味调味酱',
            'start_date': start,
            'end_date': end,
            'limit': 10,
            # 'offset' : 0,
            'include_total': True,
            # 'is_in': False,
            # 'order': 'sadas',
            # 'sort': 'quantity',
            'branch_type': "MACHINING_CENTER",
            'sub_type': "INTERNAL",
            "type": ""
        }
        res = client.GetTransferCollect(mobile_transfer_pb2.GetTransferCollectRequest(**request), metadata=metadata)
    if num == 14:
        request = {
            # 'st_ids' :[4176265632020430849],
            #       'store_ids':[],
            'category_ids': [],
            # 'product_name' :'达芬奇果美经典焦糖风味调味酱',
            'start_date': start,
            'end_date': end,
            'limit': 10,
            # 'offset' : 0,
            'include_total': True,
            'is_in': False,
            'order': 'sadas',
            'sort': 'quantity',
            'branch_type': "STORE"
        }
        res = client.GetTransferCollectDetailed(mobile_transfer_pb2.GetTransferCollectRequest(**request),
                                                metadata=metadata)
    # 取消调拨单
    if num == 15:
        request = {'transfer_id': 4461849052094922752}
        res = client.CancelTransfer(mobile_transfer_pb2.CancelTransferRequest(**request), metadata=metadata)
    return res


def test_mobile_self_picking(api):
    """测试移动端门店自采功能模块
    :param api  —— 传入api名字
    """
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_self_picking_pb2_grpc.MobileStoreSelfPickingStub(channel=conn)
    print(start_date)
    print(end_date)
    if api == "CreateStoreSelfPicking":
        request = dict(
            status="INITED",
            order_date=dt_now,
            branch_id=4406790557834149889,
            branch_name="CD001",
            branch_code="测试门店",
            reason="门店自采",
            remark="开发测试",
            branch_type="STORE",
            request_id=4394449921815338421,
            items=[dict(
                product_id=4374806626750693377,
                product_code="1000",
                product_name="1000",
                product_spec="product_spec",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=100,
                price=2.34,
            ), dict(
                product_id=4374806626499035137,
                product_code="1001",
                product_name="1001",
                product_spec="product_spec",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=100,
                price=2.66,
            )
            ]
        )
        res = client.CreateStoreSelfPicking(mobile_self_picking_pb2.CreateStoreSelfPickingRequest(**request),
                                            metadata=metadata)
        print(res)

    if api == "ListStoreSelfPicking":
        request = dict(
            start_date=start_date,
            end_date=dt_now,
            code="",
            branch_ids=[],
            # status=["INITED", "REJECTED"],
            branch_type="STORE",
            limit=20,
            offset=0,
            include_total=True,
            order=None,
            sort=None
        )
        res = client.ListStoreSelfPicking(mobile_self_picking_pb2.ListStoreSelfPickingRequest(**request),
                                          metadata=metadata)
        return res

    if api == "GetStoreSelfPickingDetail":
        request = dict(
            receipt_id=4478179024140533760
        )
        res = client.GetStoreSelfPickingDetail(mobile_self_picking_pb2.GetStoreSelfPickingDetailRequest(**request),
                                               metadata=metadata)
        return res

    if api == "UpdateStoreSelfPicking":
        request = dict(
            receipt_id=4462552903064223744,
            update_detail=True,
            status="INITED",
            order_date=dt_now,
            branch_id=4462971415133454336,
            branch_name="CD002",
            branch_code="测试门店2",
            reason="门店自采2",
            remark="开发测试2",
            branch_type="STORE",
            items=[dict(
                id=4462552903190052864,
                product_id=4462867089551523841,
                unit_id=4462867279771598849,
                quantity=1000,
                price=2.34,
            ), dict(
                id=4462552903290716160,
                product_id=4464067105527955457,
                unit_id=4462867279771598849,
                quantity=1000,
                price=2.66,
            )
            ]
        )
        res = client.UpdateStoreSelfPicking(mobile_self_picking_pb2.UpdateStoreSelfPickingRequest(**request),
                                            metadata=metadata)
        return res

    if api == "GetSelfPickingLog":
        request = dict(
            receipt_id=4489377363839705089
        )
        res = client.GetSelfPickingLog(mobile_self_picking_pb2.GetSelfPickingLogRequest(**request),
                                       metadata=metadata)
        return res

    if api == "GetPickingProductByStoreId":
        request = dict(
            store_id=4480742332378939392
        )
        res = client.GetPickingProductByStoreId(mobile_self_picking_pb2.GetPickingProductByStoreIdRequest(**request),
                                                metadata=metadata)
        return res


def test_mobile_adjust(num):
    res = ''
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_adjust_pb2_grpc.mobileAdjustStub(channel=conn)
    # GetAdjust查询每日损耗表9
    if num == 9:
        request = {'include_total': True,
                   # 'code': "352104190001",
                   'ids': [],
                   'start_date': start_date,
                   'end_date': end_date,
                   # 'status': ['CONFIRMED'],
                   # 'reason_type': '',
                   # 'branches': [3850141420400672769, ],
                   'order': 'asc',
                   'offset': 0,
                   'limit': 10,
                   # 'sources': ['MANUAL_CREATED']
                   # 'branch_type': "STORE"
                   }
        res = client.GetAdjust(mobile_adjust_pb2.GetAdjustRequest(**request), metadata=metadata)
    # 每日损耗表商品查询10
    if num == 10:
        request = {'adjust_id': 4474418195051933697}
        res = client.GetAdjustProduct(mobile_adjust_pb2.GetAdjustProductRequest(**request), metadata=metadata)
    # //GetAdjustByID查询一个每日损耗表11
    if num == 11:
        request = {'adjust_id': 4470572848733290496}
        res = client.GetAdjustByID(mobile_adjust_pb2.GetAdjustByIDRequest(**request), metadata=metadata)
    # // ConfirmAdjust确认一个每日损耗表12
    if num == 12:
        import json
        a = json.dumps([])
        print(a)
        d = datetime.now()
        print(d.strftime("%Y-%m-%d"))
        request = {'adjust_id': 4365733119422300161,
                   'branch_type': "STORE"}

        # is_bom_products_list=[4186341114644529153, 4206298044263563265]
        # list_adjust_product=[{'product_id':4186341114644529153,'accounting_quantity':2},
        #                      {'product_id':4206298044263563265,'accounting_quantity':3},
        #                      {'product_id':4186345253294309377,'accounting_quantity':6}]
        # if len(is_bom_products_list)>0:
        #     for product in list_adjust_product:
        #         accounting_quantity=product['accounting_quantity'] if product.get('accounting_quantity') else 0
        #         if int(product.get('product_id')) in is_bom_products_list and accounting_quantity!=None and accounting_quantity!=0:
        #             print('asdasdasdasdasdasdasdasd')

        res = client.ConfirmAdjust(mobile_adjust_pb2.ConfirmAdjustRequest(**request), metadata=metadata)
    # // GetAdjustProductByStoreID查询门店可损耗商品13
    if num == 13:
        # user_id = str(4186056888460247152)
        # partner_id = str(4183192445833445399)
        # metadata = (
        #     ("partner_id", partner_id),
        #     ("user_id", user_id),
        # )
        request = {'store_id': 4480742332378939392,
                   'include_total': True,
                   # 'category_ids':[4203403273681502209,],
                   # 'offset':0,
                   # 'limit':10
                   }
        res = client.GetAdjustProductByStoreID(mobile_adjust_pb2.GetAdjustProductByStoreIDRequest(**request),
                                               metadata=metadata)
    # //CreatedAdjust手动创建一个每日损耗表14
    if num == 14:
        request = {"request_id": 16014368011157213,
                   "adjust_date": end_date,
                   "position_id": None,
                   "products": [{"product_id": 4467282667024416769,
                                 "unit_id": 4462867279771598849, "quantity": 1, "reason_type": "BF001"},
                                {"product_id": 4462867089551523841, "unit_id": 4464067756345524225, "quantity": 10,
                                 "reason_type": "BF001"}],
                   "branch_type": "STORE",
                   "adjust_store": 4462971415133454336,
                   "reason_type": "BF001",
                   "remark": "开发测试2",
                   "source": "MANUAL_CREATED"}
        res = client.CreateAdjust(mobile_adjust_pb2.CreateAdjustRequest(**request), metadata=metadata)
    # //UpdateAdjust更新一个每日损耗表15
    if num == 15:
        request = {'adjust_id': 4468043878662930432,
                   "remark": "开发测试2",
                   "reason_type": 'BF002',
                   "adjust_date": end_date,
                   "products": [{"id": 4468043878738427904, "product_id": 4467282667024416769,
                                 "unit_id": 4462867279771598849, "quantity": 2, "reason_type": "BF002"},
                                {"id": 4468043878776176640, "product_id": 4462867089551523841,
                                 "unit_id": 4464067756345524225, "quantity": 20, "reason_type": "BF002"}],
                   }
        res = client.UpdateAdjust(mobile_adjust_pb2.UpdateAdjustRequest(**request), metadata=metadata)
    # //GetAdjustBiCollect每日损耗表汇总报表18
    if num == 17:
        request = {
            "adjust_ids": [4198298939545772033],
        }
        res = client.CancelAdjust(mobile_adjust_pb2.CancelAdjustRequest(**request), metadata=metadata)
    if num == 18:
        request = {'adjust_id': 4471607991447126016}
        res = client.GetAdjustLog(mobile_adjust_pb2.GetAdjustLogRequest(**request), metadata=metadata)
    return res


def test_mobile_stocktake(num):
    res = None
    global start_date
    global end_date
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_stocktake_pb2_grpc.MobileStockTakeStub(channel=conn)
    global metadata
    if num == 1:
        request = {
            # "store_ids": [4462970109975101440],
            # "target_date": dt_now,
            # "include_total": True,
            # "status": [1, 2, 3, 4],
            # "start_date": start_date,
            # "end_date": end_date,
            "ids": [4589789368492625921,
                    459077130338912256,
                    459160130680064000,
                    459340136546534604,
                    459375826805377433,
                    459376381499984281,
                    459380307633553817,
                    459402807670793420,
                    459410861154775040,
                    4594121462651936769,
                    4532791902383800321,
                    4533094782260740097]
            # "status":[6],
            # "schedule_code":"121706020001",

            # "_type":[2],
            # "store_ids":[3850146051738566657,],
            # "branch_type": "WAREHOUSE"
        }
        # request={
        #     "store_ids":,
        #     "target_date":1559606400,"status":0
        # }
        # request = mobile_stocktake_pb2.GetStocktakeRequest()
        # request.store_ids.extend([4217527472876421121])
        # request.target_date.seconds = 1559606400
        # request.is_create = True
        # ** request
        # request.start_date = start_date
        # request.end_date = end_date
        res = client.GetStocktake(mobile_stocktake_pb2.GetStocktakeRequest(**request), metadata=metadata)
    # //GetStocktakeByDocID 获取一个盘点单
    if num == 2:
        request = {'doc_id': 4412533934116069377}
        res = client.GetStocktakeByDocID(mobile_stocktake_pb2.GetStocktakeByDocIDRequest(**request), metadata=metadata)
    # //GetStocktakeProduct 查询盘点单明细商品
    if num == 3:
        request = {'doc_id': 4490394529897046017,
                   "include_total": True,
                   }
        res = client.GetStocktakeProduct(mobile_stocktake_pb2.GetStocktakeProductRequest(**request), metadata=metadata)

    # //PutStocktakeByDocID 更新盘点单 25
    if num == 4:
        # user_id = str(4257133087260934145)
        # partner_id = str(4183192445833445399)
        metadata = (
            ("partner_id", partner_id),
            ("user_id", user_id),
        )
        request = {"doc_id":4407877753892462593,"products":[{"id":4407877754018291713,"quantity":0,"is_pda":True,
                                                               "tag_products":[{"tag_id":4407905212910391297,
                                                                                "tag_name":"咖啡区域","tag_quantity":5}]}]}

        res = client.PutStocktakeByDocID(mobile_stocktake_pb2.PutStocktakeByDocIDRequest(**request), metadata=metadata)
    # //CheckStocktakeByDocID 检查能否确认盘点单（status=）14
    if num == 14:
        request = {'doc_id': 4491405075297632256,
                   'check': True,
                   'branch_type': "STORE"
                   }
        res = client.CheckStocktakeByDocID(mobile_stocktake_pb2.CheckStocktakeByDocIDRequest(**request), metadata=metadata)

    # //ConfirmStocktakeByDocID 确认盘点单（status=CONFITRMED）15
    if num == 15:
        request = {'doc_id': 4188604090479177729,
                   'check': False}
        res = client.ConfirmStocktakeByDocID(mobile_stocktake_pb2.ConfirmStocktakeByDocIDRequest(**request), metadata=metadata)

    # //ApproveStocktakeByDocID  财务确认盘点单（status=APPROVED最终状态，前置状态status）16
    if num == 16:
        request = {'doc_id': 4220178784501714945,
                   }
        res = client.ApproveStocktakeByDocID(mobile_stocktake_pb2.ApproveStocktakeByDocIDRequest(**request), metadata=metadata)
    # //RejectStocktakeProduct 财务驳回提交后的部分盘点单明细商品（status=REJECTED，部分商品status=REJECTED）17
    if num == 17:
        request = {'doc_id': 4220178784501714945,
                   # 'submit_name':'sss'
                   }
        res = client.SubmitStocktakeByDocID(mobile_stocktake_pb2.SubmitStocktakeByDocIDRequest(**request), metadata=metadata)
    if num == 18:
        request = {'doc_id': 4310804334055264257,
                   'branch_type': 'WAREHOUSE'
                   }
        res = client.CheckedStocktakeByDocID(mobile_stocktake_pb2.CheckedStocktakeByDocIDRequest(**request), metadata=metadata)
    if num == 30:
        req = {
            # "branch_id": 4217527475107790849,
            "tag_name": '开发测试批量更新'
        }
        res = client.GetStocktakeTags(mobile_stocktake_pb2.GetStocktakeTagsRequest(**req), metadata=metadata)
    if num == 31:
        request = {
            # 'tag_id':4194041551821533185,
            'action': 3,
            # 'tag_ids': [4369079606017261573, 4369079606017261572],
            'name': '热带水果',
            'origin_name': '茶饮',
            'add_dimension': 'store',
            'branch_ids': [4273394092244729857]
            # 'region_ids': [4273393458946768897]
        }
        res = client.ActionStocktakeTags(mobile_stocktake_pb2.ActionStocktakeTagsRequest(**request), metadata=metadata)
    if num == 32:
        request = {'id': [4195047809172729857, 4194461348819988482]}
        res = client.DeleteStocktakeProductTags(mobile_stocktake_pb2.DeleteStocktakeProductTagsRequest(**request),
                                                metadata=metadata)

    # 门店盘点损益
    if num == 33:
        request = {
            # "target_date":target_date,
            "start_date": start_date,
            "end_date": end_date,
            # "schedule_code":'213124',
            # "stocktake_type":"D",
            # "include_total":True,
            # "store_ids":[4176265164418449409,4176266229180268545],
            "branch_type": "STORE"
        }
        res = client.GetStocktakeBalance(mobile_stocktake_pb2.GetStocktakeBalanceRequest(**request), metadata=metadata)
    # //GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29
    if num == 34:
        request = {
            "doc_id": 4188604090479177729,
        }
        res = client.GetStocktakeBalanceProductGroup(mobile_stocktake_pb2.GetStocktakeBalanceProductGroupRequest(**request),
                                                     metadata=metadata)
    # //StocktakeBiDetailed盘点单报表
    if num == 35:
        valu = datetime.now() - timedelta(days=10)
        print(valu)
        timestamp = Timestamp()
        timestamp.FromDatetime(valu)
        start_date = timestamp
        valu2 = datetime.now() - timedelta(days=0)
        print(valu2)
        timestamp = Timestamp()
        timestamp.FromDatetime(valu2)
        end_date = timestamp
        request = {
            "start_date": start_date,
            "end_date": end_date,
            "include_total": True,
            "limit": 10,
            "offset": 0,
            'sort': 'quantity',
            "order": 'sada',
            # "product_name":"够",
            # "store_ids":[4217527473354571777,],
            # "category_ids":[]
        }
        res = client.StocktakeBiDetailed(mobile_stocktake_pb2.StocktakeBiDetailedRequest(**request), metadata=metadata)
    if num == 36:
        # include_total = 1;
        # limit = 2;
        # offset = 3;
        # start = 4;
        # end = 5;
        # region_id = 6;
        # product_ids = 7;
        # type = 8;
        request = {
            "start": start_date,
            "end": end_date,
            "include_total": True,
            "branch_type": "STORE"
            # "limit":31,
            #  "offset":0,
            #    "type":"R",
            # "product_name":"轻芝士桂花乌龙",
            # "product_ids":[4176582377935470593,],
            # "category_ids":[]
            # "region_id":4175923876548575233,
        }
        res = client.StocktakeBalanceRegion(mobile_stocktake_pb2.StocktakeBalanceRegionRequest(**request), metadata=metadata)
    if num == 91:
        request = {
            "doc_id": 4419593143576293377
            # "limit":31,
            #  "offset":0,
            #    "type":"R",
            # "product_name":"轻芝士桂花乌龙",
            # "product_ids":[4176582377935470593,],
            # "category_ids":[]
            # "region_id":4175923876548575233,
        }
        res = client.AdvanceStocktakeDiff(mobile_stocktake_pb2.AdvanceStocktakeDiffRequest(**request), metadata=metadata)
    if num == 92:
        request = {
            # "code":'11830000',
            # "stocktake_type":'M',

            # "target_date":start_date

            "limit": 10,
            "offset": 10,
            "order": 's',
            "sort": "type"
            #    "type":"R",
            # "schedule_code": "111904250003",
            # "status": "CONFIRMED",
            # "product_name":"轻芝士桂花乌龙",
            # "store_ids":[4217527473195188225,],
            # "category_ids":[]
            # "region_id":4175923876548575233,
        }
        req = mobile_stocktake_pb2.StocktakeDiffReportRequest(**request)
        req.start_date.seconds = 1561132800
        req.end_date.seconds = 1561392000
        res = client.StocktakeDiffReport(req, metadata=metadata)
    if num == 93:
        request = dict(
            doc_ids=[4218418976915730433, 4218419001636958209],
            calculate_inventory=True,
            schedule_name='重盘计划',
            remark='重盘计划',
            schedule_code='1212412',
            schedule_id=312312312313,
        )
        req = mobile_stocktake_pb2.RecreateStocktakeDocRequest(**request)
        res = client.RecreateStocktakeDoc(req, metadata=metadata)
    if num == 94:
        req = dict(
            period_group_by=2,
            # limit=10,
            stocktake_type=['D'],
            sort='date'
        )
        req = mobile_stocktake_pb2.StocktakeDocStatisticsRequest(**req)
        req.start_date.seconds = 1563359050
        req.end_date.seconds = 1568715850
        res = client.StocktakeDocStatistics(req, metadata=metadata)
    if num == 98:
        req = dict(
            store_id=4279909169156325377
        )
        req = mobile_stocktake_pb2.GetUncompleteDocRequest(**req)
        # req.start_date.seconds = 1563359050
        req.end_date.seconds = 1576684800
        res = client.GetUncompleteDoc(req, metadata=metadata)

    if num == 99:
        req = dict(
            ids=[4483958714467700737,
                 4483958714492866561,
                 4484172464843771905,
                 4484172466202726401,
                 4484354712599646209,
                 4484354715078479873,
                 4484354712578674689,
                 4533094782260740097
                 ]
        )
        res = client.GetStocktakeByIds(mobile_stocktake_pb2.GetStocktakeByIdsRequest(**req), metadata=metadata)
        # // ManualCreateStocktake 手动创建盘点单 125
    if num == 125:
        user_id = str(1)
        partner_id = str(100)
        metadata = (
            ("partner_id", partner_id),
            ("user_id", user_id),
        )
        new_doc_id = client.GetNewStocktakeId(mobile_stocktake_pb2.GetNewStocktakeIdRequest()).new_doc_id
        target_date = get_timestamp(datetime.utcnow())
        request = {"target_date":target_date,"branch_id":4462866233712181249,"new_doc_id":new_doc_id,"calculate_inventory":True,"product_list":[{"product_id":4467665361310842881,"position_id":1},{"product_id":4467665361310842881,"position_id":2}]}

        res = client.ManuallyCreateStocktake(mobile_stocktake_pb2.ManuallyCreateStocktakeRequest(**request), metadata=metadata)

    return res


def test_mobile_franchisee_demand(num):
    ret = None
    # global start_date
    # global end_date
    # global metadata
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_franchisee_demand_pb2_grpc.MobileFranchiseeDemandServiceStub(channel=conn)
    if num == 1:
        ret = client.CreateFDemand(mobile_franchisee_demand_pb2.CreateFDemandRequest(
            batch_id=4462866233712181242,
            demand_date=dt_now,
            received_by=4498417801442164736,
            franchisee_id=4498417801442164736,
            products=[
                {
                    "product_id": 4466525299554648065,
                    "quantity": 10,
                    "unit_id": 4511110399642304513,
                    "category_id": 4482058631692419073,
                    "price_tax": 10,
                    "tax_rate": 8,
                }
            ],
            type='FD'
        ), metadata=metadata)
    if num == 2:
        ret = client.ListFDemand(mobile_franchisee_demand_pb2.ListFDemandRequest(
            start_date=start_date,
            end_date=end_date,
            status=["SUBMITTED", "REJECTED"],
            types=["FD"],
            limit=10,
            order='asc',
            sort=None,
            include_total=True

        ), metadata=metadata)
    if num == 3:
        ret = client.GetFDemandById(mobile_franchisee_demand_pb2.GetFDemandByIdRequest(
            demand_id=4540757285170020353
        ), metadata=metadata)
    if num == 4:
        ret = client.GetProductsById(mobile_franchisee_demand_pb2.GetProductsByIdRequest(
            demand_id=4540757285170020353,
            limit=10,
            order='asc',
            sort=None,
            include_total=True

        ), metadata=metadata)
    if num == 5:
        ret = client.UpdateProducts(mobile_franchisee_demand_pb2.UpdateProductRequest(
            demand_id=4710079427499851776,
            # status="R_APPROVE",
            products=[
                {
                    "id": 4710079427600515072,
                    "product_id": 4690419166786682880,
                    "quantity": 1,
                    "tax_price": 2.98,
                    "tax_rate": 22,
                    "sales_price": 2.82,
                    "approve_quantity": 10,
                }
            ]
        ), metadata=metadata)
    if num == 6:
        ret = client.DealFDemandById(mobile_franchisee_demand_pb2.DealFDemandByIdRequest(
            demand_ids=[4710079427499851776],
            action="R_APPROVE",
            reject_reason=''
        ), metadata=metadata)
    if num == 7:
        ret = client.GetHistoryById(mobile_franchisee_demand_pb2.GetHistoryByIdRequest(
            demand_id=4545889524118519808
        ), metadata=metadata)
    if num == 8:
        req = dict(
            start_date=start_date,
            end_date=end_date,
            limit=10,
            offset=0,
            order='desc',
            sort='amount'
        )
        ret = client.QueryFDemandReport(mobile_franchisee_demand_pb2.QueryFDemandReportRequest(**req),
                                        metadata=metadata)
    if num == 9:
        req = dict(
            demand_id=4709014624488456192,
            products=[
                dict(product_id=4568339807146639360,
                     approve_quantity=10,
                     bind_products=[
                         dict(
                             product_id=4693024369998364672,
                             approve_quantity=10,
                         )
                     ])

            ]
        )
        ret = client.AddFDemandProduct(mobile_franchisee_demand_pb2.AddFDemandProductRequest(**req), metadata=metadata)
    if num == 10:
        req = dict(
            store_id=4498417801442164736,
            order_type_id=4709014624488456192,
            products=[
                dict(product_id=4568339807146639360,
                     quantity=10)
            ]
        )
        ret = client.AddShoppingCartCache(mobile_franchisee_demand_pb2.AddShoppingCartCacheRequest(**req),
                                          metadata=metadata)
    if num == 11:
        ret = client.CheckFDemandTodo(mobile_franchisee_demand_pb2.CheckFDemandTodoRequest(
            store_id=4679554862445232129), metadata=metadata)
    return ret


def test_mobile_franchisee_refund(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_franchisee_refund_pb2_grpc.MobileFranchiseeRefundServiceStub(channel=conn)
    if num == 1:
        ret = client.ListMobileFRefund(mobile_franchisee_refund_pb2.ListMobileFRefundRequest(
            refund_start_date=start_date,
            refund_end_date=end_date,
            status=["INITED", "SUBMITTED", "REJECTED"],
            main_types=["ORDER_REFUND", "RETURN_REFUND"],
            limit=10,
            order='asc',
            sort=None,
            include_total=True

        ), metadata=metadata)
    if num == 2:
        ret = client.GetMobileFRefundById(mobile_franchisee_refund_pb2.GetFRefundByIdRequest(
            refund_id=4668303914343366656
        ), metadata=metadata)

    return ret


def test_mobile_franchisee_receive_diff(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_franchisee_receive_diff_pb2_grpc.FranchiseeMobileReceivingDiffServiceStub(channel=conn)
    if num == 1:
        ret = client.ConfirmReceiveDiff(
            mobile_franchisee_receive_diff_pb2.ConfirmReceiveDiffRequest(
                id=4698034682565033984
            ),metadata=metadata
        )
        return ret
    elif num == 2:
        ret = client.GetReceiveDiffProductById(
            mobile_franchisee_receive_diff_pb2.GetReceiveDiffProductByIdRequest(
                id=4698034682565033984
            ),metadata=metadata
        )
        return ret


def test_mobile_franchisee_return(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = mobile_franchisee_return_pb2_grpc.FranchiseeReturnServiceStub(channel=conn)
    if num == 1:
        msg = {'return_by': 4634349687791517696, 'logistics_type': '', 'type': 'NBO', 'sub_type': 'fs_store', 'return_delivery_date': Timestamp(seconds=1670428800), 'source_id': 0, 'source_code': '', 'return_reason': '包装破损', 'remark': '离谱', 'attachments': ['https:xxx.jpg'], 'request_id': 16703920909181235,
'products': [{'product_id': 4693023984260808704, 'unit_id': 0, 'quantity': 1.0, 'confirmed_quantity': 0.0, 'product_code': '1000000005', 'product_name': 'WEN测试商品5', 'unit_name': '', 'unit_spec': '', 'unit_rate': 0.0, 'tax_rate': 0.0, 'price': 0.0, 'price_tax': 0.0, 'id': 0, 'return_to': 0, 'logistics_type': '', 'refund_amount': 0.0},
             {'product_id': 4589324239550840832, 'unit_id': 0, 'quantity': 1.0, 'confirmed_quantity': 0.0, 'product_code': '306735', 'product_name': '香辣牛蛙', 'unit_name': '', 'unit_spec': '', 'unit_rate': 0.0, 'tax_rate': 0.0, 'price': 0.0, 'price_tax': 0.0, 'id': 0, 'return_to': 0, 'logistics_type': '', 'refund_amount': 0.0}]}
        ret = client.CreateReturn(
            mobile_franchisee_return_pb2.CreateReturnRequest(
                **msg
            ),metadata=metadata
        )
        return ret
    elif num == 2:
        client = mobile_franchisee_return_pb2_grpc.FranchiseeReturnServiceStub(channel=conn)
        ret = client.GetReturnProductById(
            mobile_franchisee_return_pb2.GetReturnProductByIdRequest(
                id=4701061467716255744
            ),metadata=metadata
        )
        return ret
    elif num == 3:
        client = mobile_franchisee_return_pb2_grpc.FranchiseeReturnServiceStub(channel=conn)
        ret = client.GetReturnById(
            mobile_franchisee_return_pb2.GetReturnByIdRequest(
                id=4701059851143413760
            ),metadata=metadata
        )
        return ret


def test_store_receive_diff(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = receive_diff_pb2_grpc.StoreReceiveDiffServiceStub(channel=conn)
    if num == 1:
        msg = {"receiving_id":4701050101728493569,
               "attachments":"['https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/v951HsYSU9Gh7fa0343fe46399ec8b40029a3103eb32-1671530678185.jpeg']",
               "confirmed_products":[{"id":4701050101992734721,"product_id":4701050101992734721,"product_code":"2647324732894","product_name":"仓库苹果xl002",
                                      "unit_name":"克","unit_id":4462867279771598849,
                                      "received_quantity":5,"confirmed_quantity":5,"d_diff_quantity":1,"reason_type":"过期商品","diff_quantity":1}],"remark":""}
        ret = client.CreateReceivingDiff(
            receive_diff_pb2.CreateReceivingDiffRequest(
                **msg
            ),metadata=metadata
        )
        return ret


if __name__ == '__main__':
    # res = test_mobile_transfer_client(5)
    # res = test_mobile_adjust(9)
    # res = test_mobile_self_picking("GetStoreSelfPickingDetail")
    # res = test_mobile_self_picking("ListStoreSelfPicking")
    # res = test_mobile_self_picking("UpdateStoreSelfPicking")
    # res = test_mobile_self_picking("GetSelfPickingLog")
    # res = test_mobile_self_picking("GetPickingProductByStoreId")
    # res = test_mobile_stocktake(1)
    # res = test_common_service(1)
    res = test_mobile_franchisee_demand(11)
    # res = test_mobile_stocktake(125)
    # res = test_mobile_franchisee_refund(2)
    # res = test_mobile_franchisee_receive_diff(2)
    # res = test_mobile_franchisee_return(4)
    # res = test_store_receive_diff(1)
    print(MessageToDict(res))
