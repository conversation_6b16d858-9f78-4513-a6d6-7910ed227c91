from datetime import datetime
from itertools import product
from google.protobuf.timestamp_pb2 import Timestamp
import grpc
import os
import unittest
import sys
file_path = os.path.abspath(__file__)
path = os.path.dirname(os.path.dirname(os.path.dirname(file_path)))

sys.path.append(os.path.join(path, "supply", "proto"))
sys.path.append(path)
from supply.proto.franchisee.statements_pb2 import *
from supply.proto.franchisee.statements_pb2_grpc import StatementsServiceStub

metadata = (
    ("partner_id", '1026'),
    ("user_id", '1'),
)
channel = grpc.insecure_channel("127.0.0.1:8686")
ntime = datetime(2021, 5, 30, 0, 0)

req = {
    "query_dates": Timestamp(seconds=int(ntime.timestamp())),
    "store_code": "200080",
    "fields": "2300000000"
}


class Statements(unittest.TestCase):
    def setUp(self):
        self.client = StatementsServiceStub(channel)

    def test_statement(self):
        request = ListReconciliationRequest(**req)
        res = self.client.ListReconciliation(request, metadata=metadata)
        print(res)


if __name__ == "__main__":
    unittest.main()
