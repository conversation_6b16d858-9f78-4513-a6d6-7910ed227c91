import unittest
import grpc
import sys
import os
file_path = os.path.abspath(__file__)
path = os.path.dirname(os.path.dirname(os.path.dirname(file_path)))

sys.path.append(os.path.join(path, "supply", "proto"))
sys.path.append(path)
from supply.client.receipt_service import receipt_service


class TestClient(unittest.TestCase):
    def setUp(self):
        pass

    def test_list_deliverys(self):
        batch_ids = [4488008250658160640,
                     4487986219338203136
                     ]
        num = 0
        partner_id = 100
        user_id = 1
        rets = receipt_service.list_franchisee_deliverys(batch_ids, partner_id, user_id)
        for ret in rets:
            num += 1
            print(num)
            print(ret)
            print(ret.id)
            print(f"ret.batch_id:{ret.batch_id}")


if __name__ == "__main__":
    unittest.main()
