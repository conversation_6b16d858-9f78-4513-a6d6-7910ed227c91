# -*- coding: utf-8 -*-

import json
import os
import sys
import time
import unittest
from datetime import datetime, timedelta
from pprint import pprint

TEST_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(TEST_DIR)
sys.path.append(TEST_DIR)
sys.path.append(os.path.join(TEST_DIR, "supply", "proto"))

from supply.client.bom_service import Bom_service
from supply.client.inventory_service import inventory_service
from supply.client.metadata_service import metadata_service
from supply.client.bom_service import bom_service

from supply.utils.helper import get_today_datetime
from supply.utils.snowflake import gen_snowflake_id
from supply import time_cost


class service_test(unittest.TestCase):

    @unittest.skip
    def test_bom(self):
        #   "request_id":4192918373812017833,
        #   "product_id": 4192617718509207553,
        #   "store_id": 4176234574621179905,
        #   "biz_code": "SALES",
        #   "biz_no": "1332222223",
        #   "sales_date" :"2019-02-19"
        #
        result = Bom_service.get_bom(gen_snowflake_id(), 4205272555923177473, '30100002', 4196554533553307649,
                                     '2000031',
                                     "2019-04-10", "DEMAND", '111904100013', 5000, partner_id=2,
                                     user_id=4201196624908648450)
        print("test_bom------------------%s" % result)

    @unittest.skip
    def test_product(self):
        return_fields = None
        print("metadata_service.get_product: %s" % metadata_service.get_product(4210276050636111873,
                                                                                return_fields=return_fields,
                                                                                include_units=True,
                                                                                partner_id=4183192445833445399,
                                                                                user_id=4186056888460247153))

    @unittest.skip
    def test_store(self):
        print("metadata_service.get_store: %s" % metadata_service.get_store(4176234574621179905))

    @unittest.skip
    def test_distr(self):
        include_product_fields = ["name", "code", "sale_type", "model_name", "model_code", "storage_type",
                                  "product_type", "category"]
        print(metadata_service.get_distribution_products_by_store_id(4176234574621179905,
                                                                     product_ids=[4176565709339361281],
                                                                     product_filters={
                                                                         "product_type__nin": ["FINISHED"]},
                                                                     include_product_fields=include_product_fields))

    @unittest.skip
    def test_pur(self):
        include_product_fields = ["name", "code", "sale_type", "model_name", "model_code", "storage_type",
                                  "product_type", "category"]
        ret = json.dumps(
            metadata_service.get_purchase_product_by_store_id(4176234574621179905, product_ids=[4176565709339361281],
                                                              product_filters={"product_type__nin": ["FINISHED"]},
                                                              include_product_fields=include_product_fields,
                                                              partner_id=4183192445833445399,
                                                              user_id=4186056888460247154))
        print("purchase=", ret)

    def test_ListValidProductsForDistributionByStoreId(self):
        order_date = get_today_datetime(plus_day=1)
        # order_date=None
        # filters = {"allow_main_order":False}
        # filters = None
        filters = {}
        ret = metadata_service.get_list_valid_product_for_distr_by_id(4205666728631336961,
                                                                      distr_type='NMD',
                                                                      include_product_fields='name,code,model_name,model_name,model_code,storage_type,category,product_type,distr_type',
                                                                      order_date=order_date, filters=filters,
                                                                      product_filters={"status__eq": "ENABLED"},
                                                                      partner_id=4183192445833445399,
                                                                      user_id=4186056888460247154)
        ret = json.dumps(ret)
        print("test_ListValidProductsForDistributionByStoreId=", ret)

    @unittest.skip
    def test_GetValidStoreByProductId(self):
        include_product_fields = 'name,code,model_name,model_name,model_code,storage_type,category,product_type,sale_type,distr_type'
        filters = {"allow_main_order": True}
        print("validStore=" + str(
            json.dumps(metadata_service.get_list_valid_store_for_distr_by_product_ids([4192678680092213249],
                                                                                      include_product_fields=include_product_fields,
                                                                                      filters=filters,
                                                                                      partner_id=4183192445833445399,
                                                                                      user_id=4186056888460247154))))

    @unittest.skip
    def test_store_list(self):
        print("test_store_list:{}".format(
            metadata_service.get_store_list(partner_id=2, limit=10, offset=0, user_id=4208873025392607233)))

    @unittest.skip
    def test_unit(self):
        print(metadata_service.get_unit(4175954254701264897))

    @unittest.skip
    def test_distr_center(self):
        a = [4185984595205816321,
             4185981771231264769,
             4185983421463396353,
             4208928335394242561,
             4185983421463396353]
        partner_id = 2
        user_id = 4201196624908648450
        for i in a:
            center = metadata_service.get_distribution_center(int(i), partner_id=partner_id, user_id=user_id)
            mcu = center.get('code')
            # print("repo_data:{}".format(jde_service.get_quantity(mcu, partner_id=partner_id, user_id=user_id)))

    @unittest.skip
    def test_add_delivery(self):
        #       <res>                    
        #     <AN8>2000021</AN8>                    
        #     <DRQJ>2019-4-28</DRQJ>                    
        #     <EDLN>1</EDLN>                    
        #     <EDTN>19030001</EDTN>                    
        #     <LITM>10100030</LITM>                    
        #     <MCU>3001</MCU>                    
        #     <TRDJ>2019-2-28</TRDJ>                    
        #     <UOM>PG</UOM>                    
        #     <UORG>5</UORG>                    
        #  </res>     
        data = [{"AN8": "2000021", "DRQJ": "2019-4-28", "EDLN": 1, "EDTN": "19030001",
                 "LITM": "10100030", "MCU": "3001", "TRDJ": "2019-2-28", "UOM": "PG", "UORG": 5}]
        # print("test_add_delivery:", jde_service.add_delivery_in(data))

    @unittest.skip
    def test_bom_data(self):
        print("test_bom_data: ", Bom_service.get_product_sale_amount_in_one_week([4176266229180268545],
                                                                                 [4176565709339361281,
                                                                                  4197659226832633857],
                                                                                 partner_id=4183192445833445399,
                                                                                 user_id=4198715562370531329))

    @unittest.skip
    def test_get_products_inventory_by_branch_id(self):
        print("test_get_products_inventory_by_branch_id: ",
              inventory_service.get_products_inventory_by_branch_id(4176266229180268545,
                                                                    partner_id=4183192445833445399,
                                                                    user_id=4198715562370531329))

    @unittest.skip
    def test_get_distribution(self):
        print("test_get_distribution: ",
              metadata_service.get_distribution_center(4170486625060519937, partner_id=4183192445833445399,
                                                       user_id=4198715562370531329))

    @unittest.skip
    def test_access_store(self):
        print("test_access_store: ",
              metadata_service.get_store_scope(partner_id=4183192445833445399, user_id=4186056888460247153))

    @unittest.skip
    @time_cost
    def test_cost(self):
        time.sleep(2)
        print("asfd")


class MetadataService(unittest.TestCase):
    def setUp(self):
        self.service = metadata_service
        self.p_id = 2
        self.u_id = 10
        self.store_id = 4217527472486350849

    def test_demand_change_limits(self):
        resp = self.service.list_demand_change_limits(self.p_id, self.u_id)
        print(resp)

    def test_weight_factor(self):
        resp = self.service.list_weight_factor(self.p_id, self.u_id)
        print(resp)

    def test_list_safe_inventory_factor(self):
        resp = self.service.list_inventory_safety_factor(self.p_id, self.u_id)
        print(resp)

    def test_list_product(self):
        # resp = self.service.get_product_list(
        #     partner_id=self.p_id, user_id=self.u_id, ids=[4217605345394032641,]
        # )
        # print(resp)

        finish_products = metadata_service.get_product_list(
            filters={"product_type": "FINISHED", "status": "ENABLED",
                     "allow_order__eq": True, "sale_type__in": ["BREAD", "TEA"]},
            return_fields='sale_type,product_type,bom_type,storage_type',
            partner_id=self.p_id, user_id=self.u_id,
            include_units=True
        ).get('rows', [])

        print(finish_products, len(finish_products))

    def test_list_store(self):
        resp = metadata_service.get_store_list(
            filters={
                "open_status__in": ['OPENED'],
                "status__in": ["ENABLED"]
            },

            partner_id=self.p_id, user_id=self.u_id, ids=[4217527472486350849, ]
        )
        print(resp)

    def test_list_distribution_region_product_by_store_id(self):
        resp = metadata_service.list_distribution_region_product_by_store_id(
            store_id=self.store_id, partner_id=self.p_id, user_id=self.u_id,

            include_product_fields='name,code,product_type,sale_type,bom_type',
        )
        for i in resp:
            if i.get('circle_type') == 'D':
                pprint(i)

    def test_get_entity_by_id(self):
        id = 4217613150133944321

        store = metadata_service.get_entity_by_id(
            self.p_id, self.u_id, id, 'product', include_parents=True
        )
        print(store, "store ..........")


class TestBomService(unittest.TestCase):

    def setUp(self):
        self.service = bom_service
        self.p_id = 2
        self.u_id = 10
        self.store_id = 4217527479276929025  # 上海正大广场店

    def test_get_products_boms(self):
        now = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=3)
        # products = [
        #     {
        #         'product_id': 4217613190348931073,  # 纯绿妍
        #         'product_qty': 1,
        #     },
        #     {
        #         'product_id': 4217613216118734849,  # 芋薯手撕包
        #         'product_qty': 1,
        #     }
        # ]
        products = [
            {'product_id': 4229554429193355265, 'product_qty': 0},
            {'product_id': 4229554430065770497, 'product_qty': 0}, {
                'product_id': 4229554430392926209, 'product_qty': 0}, {'product_id': 4229554431860932609,
                                                                       'product_qty': 0}, {
                'product_id': 4229554522994769921, 'product_qty': 0}, {'product_id': 4229554524949315585,
                                                                       'product_qty': 0}, {
                'product_id': 4229554525523935233, 'product_qty': 0}, {'product_id': 4238892620002099201,
                                                                       'product_qty': 0}, {
                'product_id': 4229554428895559681, 'product_qty': 0}, {'product_id': 4229554429751197697,
                                                                       'product_qty': 0}, {
                'product_id': 4229554431575719937, 'product_qty': 0}, {'product_id': 4229554524659908609,
                                                                       'product_qty': 0}, {
                'product_id': 4229554527105187841, 'product_qty': 0}, {'product_id': 4229554428438380545,
                                                                       'product_qty': 0}, {
                'product_id': 4229554429470179329, 'product_qty': 0}, {'product_id': 4229554431005294593,
                                                                       'product_qty': 0}, {
                'product_id': 4229554431282118657, 'product_qty': 0}, {'product_id': 4229554523951071233,
                                                                       'product_qty': 0}, {
                'product_id': 4229554524253061121, 'product_qty': 0}, {'product_id': 4229554526459265025,
                                                                       'product_qty': 0}, {
                'product_id': 4229554526807392257, 'product_qty': 0}, {'product_id': 4229554430682333185,
                                                                       'product_qty': 0}, {
                'product_id': 4229554522671808513, 'product_qty': 0}, {'product_id': 4229554523300954113,
                                                                       'product_qty': 0}, {
                'product_id': 4229554523623915521, 'product_qty': 0}, {'product_id': 4229554525834313729,
                                                                       'product_qty': 0}, {
                'product_id': 4229554526119526401, 'product_qty': 0}, {'product_id': 4217613228827475969,
                                                                       'product_qty': 0}, {
                'product_id': 4217613229632782337, 'product_qty': 0}, {'product_id': 4217613229829914625,
                                                                       'product_qty': 0}, {
                'product_id': 4217613151547424769, 'product_qty': 0}, {'product_id': 4217613156769333249,
                                                                       'product_qty': 0}, {
                'product_id': 4217613156958076929, 'product_qty': 0}, {'product_id': 4217613164763676673,
                                                                       'product_qty': 0}, {
                'product_id': 4217613167632580609, 'product_qty': 0}, {'product_id': 4217613168072982529,
                                                                       'product_qty': 0}, {
                'product_id': 4217613172032405505, 'product_qty': 0}, {'product_id': 4217613174494461953,
                                                                       'product_qty': 0}, {
                'product_id': 4217613174800646145, 'product_qty': 0}, {'product_id': 4217613176990072833,
                                                                       'product_qty': 0}, {
                'product_id': 4217613178097369089, 'product_qty': 0}, {'product_id': 4217613178277724161,
                                                                       'product_qty': 0}, {
                'product_id': 4217613182694326273, 'product_qty': 0}, {'product_id': 4217613183663210497,
                                                                       'product_qty': 0}, {
                'product_id': 4217613185819082753, 'product_qty': 0}, {'product_id': 4217613186016215041,
                                                                       'product_qty': 0}, {
                'product_id': 4217613187018653697, 'product_qty': 0}, {'product_id': 4217613187194814465,
                                                                       'product_qty': 0}, {
                'product_id': 4217613189094834177, 'product_qty': 0}, {'product_id': 4217613189283577857,
                                                                       'product_qty': 0}, {
                'product_id': 4217613215703498753, 'product_qty': 0}, {'product_id': 4217613216307478529,
                                                                       'product_qty': 0}, {
                'product_id': 4217613218027143169, 'product_qty': 0}, {'product_id': 4217613218576596993,
                                                                       'product_qty': 0}, {
                'product_id': 4217613220573085697, 'product_qty': 0}, {'product_id': 4217613220950573057,
                                                                       'product_qty': 0}, {
                'product_id': 4217613221298700289, 'product_qty': 0}, {'product_id': 4217613222984810497,
                                                                       'product_qty': 0}, {
                'product_id': 4217613224633171969, 'product_qty': 0}, {'product_id': 4217613225262317569,
                                                                       'product_qty': 0}, {
                'product_id': 4217613226868736001, 'product_qty': 0}, {'product_id': 4217613155590733825,
                                                                       'product_qty': 0}, {
                'product_id': 4217613163811569665, 'product_qty': 0}, {'product_id': 4217613164172279809,
                                                                       'product_qty': 0}, {
                'product_id': 4217613164587515905, 'product_qty': 0}, {'product_id': 4217613166844051457,
                                                                       'product_qty': 0}, {
                'product_id': 4217613167246704641, 'product_qty': 0}, {'product_id': 4217613171650723841,
                                                                       'product_qty': 0}, {
                'product_id': 4217613171814301697, 'product_qty': 0}, {'product_id': 4217613174309912577,
                                                                       'product_qty': 0}, {
                'product_id': 4217613176558059521, 'product_qty': 0}, {'product_id': 4217613176734220289,
                                                                       'product_qty': 0}, {
                'product_id': 4217613177896042497, 'product_qty': 0}, {'product_id': 4217613182295867393,
                                                                       'product_qty': 0}, {
                'product_id': 4217613182518165505, 'product_qty': 0}, {'product_id': 4217613183277334529,
                                                                       'product_qty': 0}, {
                'product_id': 4217613183482855425, 'product_qty': 0}, {'product_id': 4217613185458372609,
                                                                       'product_qty': 0}, {
                'product_id': 4217613185638727681, 'product_qty': 0}, {'product_id': 4217613186775384065,
                                                                       'product_qty': 0}, {
                'product_id': 4217613188650237953, 'product_qty': 0}, {'product_id': 4217613194731978753,
                                                                       'product_qty': 0}, {
                'product_id': 4217613194933305345, 'product_qty': 0}, {'product_id': 4217613216118734849,
                                                                       'product_qty': 0}, {
                'product_id': 4217613217838399489, 'product_qty': 0}, {'product_id': 4217613218215886849,
                                                                       'product_qty': 0}, {
                'product_id': 4217613222804455425, 'product_qty': 0}, {'product_id': 4217613224247296001,
                                                                       'product_qty': 0}, {
                'product_id': 4217613224436039681, 'product_qty': 0}, {'product_id': 4217613225060990977,
                                                                       'product_qty': 0}, {
                'product_id': 4217613226159898625, 'product_qty': 0}, {'product_id': 4217613226684186625,
                                                                       'product_qty': 0}, {
                'product_id': 4217613228198330369, 'product_qty': 0}, {'product_id': 4217613229225934849,
                                                                       'product_qty': 0}, {
                'product_id': 4217613229423067137, 'product_qty': 0}, {'product_id': 4218606481882218497,
                                                                       'product_qty': 0}, {
                'product_id': 4217613150872141825, 'product_qty': 0}, {'product_id': 4217613152562446337,
                                                                       'product_qty': 0}, {
                'product_id': 4217613154848342017, 'product_qty': 0}, {'product_id': 4217613157398478849,
                                                                       'product_qty': 0}, {
                'product_id': 4217613163991924737, 'product_qty': 0}, {'product_id': 4217613166122631169,
                                                                       'product_qty': 0}, {
                'product_id': 4217613166479147009, 'product_qty': 0}, {'product_id': 4217613169520017409,
                                                                       'product_qty': 0}, {
                'product_id': 4217613170845417473, 'product_qty': 0}, {'product_id': 4217613172854489089,
                                                                       'product_qty': 0}, {
                'product_id': 4217613173248753665, 'product_qty': 0}, {'product_id': 4217613173454274561,
                                                                       'product_qty': 0}, {
                'product_id': 4217613175341711361, 'product_qty': 0}, {'product_id': 4217613177648578561,
                                                                       'product_qty': 0}, {
                'product_id': 4217613181683499009, 'product_qty': 0}, {'product_id': 4217613181905797121,
                                                                       'product_qty': 0}, {
                'product_id': 4217613182115512321, 'product_qty': 0}, {'product_id': 4217613183067619329,
                                                                       'product_qty': 0}, {
                'product_id': 4217613185269628929, 'product_qty': 0}, {'product_id': 4217613186385313793,
                                                                       'product_qty': 0}, {
                'product_id': 4217613186561474561, 'product_qty': 0}, {'product_id': 4217613190109855745,
                                                                       'product_qty': 0}, {
                'product_id': 4217613222217252865, 'product_qty': 0}, {'product_id': 4217613222397607937,
                                                                       'product_qty': 0}, {
                'product_id': 4217613223899168769, 'product_qty': 0}, {'product_id': 4217613225975349249,
                                                                       'product_qty': 0}, {
                'product_id': 4217613227590156289, 'product_qty': 0}, {'product_id': 4217613229041385473,
                                                                       'product_qty': 0}, {
                'product_id': 4218606481597005825, 'product_qty': 0}, {'product_id': 4217605213176987649,
                                                                       'product_qty': 0}, {
                'product_id': 4217613150133944321, 'product_qty': 0}, {'product_id': 4217613150616289281,
                                                                       'product_qty': 0}, {
                'product_id': 4217613151761334273, 'product_qty': 0}, {'product_id': 4217613153225146369,
                                                                       'product_qty': 0}, {
                'product_id': 4217613157159403521, 'product_qty': 0}, {'product_id': 4217613165325713409,
                                                                       'product_qty': 0}, {
                'product_id': 4217613168282697729, 'product_qty': 0}, {'product_id': 4217613169335468033,
                                                                       'product_qty': 0}, {
                'product_id': 4217613172242120705, 'product_qty': 0}, {'product_id': 4217613172443447297,
                                                                       'product_qty': 0}, {
                'product_id': 4217613173034844161, 'product_qty': 0}, {'product_id': 4217613174989389825,
                                                                       'product_qty': 0}, {
                'product_id': 4217613177241731073, 'product_qty': 0}, {'product_id': 4217613177426280449,
                                                                       'product_qty': 0}, {
                'product_id': 4217613181440229377, 'product_qty': 0}, {'product_id': 4217613182878875649,
                                                                       'product_qty': 0}, {
                'product_id': 4217613183835176961, 'product_qty': 0}, {'product_id': 4217613184040697857,
                                                                       'product_qty': 0}, {
                'product_id': 4217613185076690945, 'product_qty': 0}, {'product_id': 4217613186196570113,
                                                                       'product_qty': 0}, {
                'product_id': 4217613187404529665, 'product_qty': 0}, {'product_id': 4217613188465688577,
                                                                       'product_qty': 0}, {
                'product_id': 4217613189895946241, 'product_qty': 0}, {'product_id': 4217613219151216641,
                                                                       'product_qty': 0}, {
                'product_id': 4217613220736663553, 'product_qty': 0}, {'product_id': 4217613221814599681,
                                                                       'product_qty': 0}, {
                'product_id': 4217613223337132033, 'product_qty': 0}, {'product_id': 4217613224851275777,
                                                                       'product_qty': 0}, {
                'product_id': 4217613225606250497, 'product_qty': 0}, {'product_id': 4217613225782411265,
                                                                       'product_qty': 0}, {
                'product_id': 4217613227028119553, 'product_qty': 0},
            {'product_id': 4217613227426578433, 'product_qty': 0}]
        resp = self.service.get_products_boms(
            partner_id=self.p_id, user_id=self.u_id,
            request_id=100, store_id=self.store_id,
            sales_date=now.strftime("%Y-%m-%d"), products=products,
            biz_code='None', biz_no='None'
        ).get('response', [])
        for i in resp:
            print(i)
            print("\n")


if __name__ == "__main__":
    unittest.main()
