import sys
import os
import unittest

TEST_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(TEST_DIR)
sys.path.append(TEST_DIR)
sys.path.append(os.path.join(TEST_DIR, "supply", "proto"))
from datetime import timedelta
import unittest

from supply.module.demand_suggest.new_retail_normal import NewRetailNormalSuggest
from supply.module.demand_suggest.tea_bread_material import TeaBreadDemandMaterialSuggest
from supply.module.demand_suggest.utils import list_store_product_detail
from supply.module.demand_suggest import utils
from supply.module.demand_suggest.base import DemandSuggest
from datetime import datetime
from supply.module.sale_forecast import SaleForecastTask

class TestUtils(unittest.TestCase):
    def setUp(self):
        self.store_id = 4217527472486350849  # 江门地王店
        self.products = [
            {"product_id": 4175525475252174849,
             "arrival_day": 3,
             "min_quantity": 2,
             'max_quantity': 550,
             'increment_quantity': 2,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             "unit_rate": 1.2,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
            {"product_id": 4175534073151750145,
             "arrival_day": 2,
             "min_quantity": 4,
             'max_quantity': 410,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             'increment_quantity': 1,
             "unit_rate": 1.3,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
            {"product_id": 4176565203472744449,
             "arrival_day": 1,
             "min_quantity": 6,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             'max_quantity': 234,
             'increment_quantity': 3,
             "unit_rate": 1.4,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
        ]
        self.product_ids = [
            4217605213176987649,
            4217605216196886529,
            4217605216423378945,
            4217605217455177729,
        ]
        self.partner_id = 2
        self.user_id = 4186056888460247154
        self.store_id = 4217527472486350849   # 江门地王店
        self.demand_day = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        self.products = list_store_product_detail(
            self.partner_id, self.user_id, self.store_id, self.product_ids
        )
        self.store_id = 4217527472486350849

    def test_get_inventory_amount(self):
        from supply.module.demand_suggest.utils import get_inventory_amount
        store_id = 4217527472486350849
        ret = get_inventory_amount(self.partner_id, self.user_id, store_id, [])
        print(ret)

    def test_get_weight_factors(self):
        from supply.module.demand_suggest.utils import get_weight_factors
        product = {
            'product_type': 'FINISHED',
            'sale_type': 'TEA',
            'bom_type': 'MANUFACTURE',
            'product_id': 4217605735900512257,
        }
        weight_factor_map = get_weight_factors(
            self.partner_id, self.user_id, self.store_id, [product,]
        )
        print(weight_factor_map)

    def test_list_store_product_detail(self):
        rows = utils.list_store_product_detail(
            self.partner_id, self.user_id, self.store_id,
        product_ids=None,
        )
        print(rows)


class TestDemandSuggest(unittest.TestCase):

    def setUp(self):
        self.store_id = 4217527472486350849  # 江门地王店
        self.products = [
            {"product_id": 4175525475252174849,
             "arrival_day": 3,
             "min_quantity": 2,
             'max_quantity': 550,
             'increment_quantity': 2,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             "unit_rate": 1.2,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
            {"product_id": 4175534073151750145,
             "arrival_day": 2,
             "min_quantity": 4,
             'max_quantity': 410,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             'increment_quantity': 1,
             "unit_rate": 1.3,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
            {"product_id": 4176565203472744449,
             "arrival_day": 1,
             "min_quantity": 6,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             'max_quantity': 234,
             'increment_quantity': 3,
             "unit_rate": 1.4,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
        ]
        self.product_ids = [
            4217605213176987649,
            4217605216196886529,
            4217605216423378945,
            4217605217455177729,
        ]
        self.partner_id = 2
        self.user_id = 4186056888460247154
        self.store_id = 4217527472486350849
        self.demand_day = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        self.products = list_store_product_detail(
            self.partner_id, self.user_id, self.store_id, self.product_ids
        )
    
    def test_new_retail_normal_suggest(self):
        product_id = 4217605736257028097  # 小芋头条-大包装
        store_id = 4217527479000104961  # 广州兴盛路热麦店
        suggests = NewRetailNormalSuggest.get_suggest(
            self.partner_id, self.user_id, self.demand_day, store_id, [product_id,]
        )
        print("suggests: ", suggests)

    def test_tea_and_bread_material_suggest(self):
        store_id = 4217527479000104961  # 广州兴盛路热麦店
        product_ids = [
            4217613216118734849,   # 芋薯手撕包
        ]

        suggests = TeaBreadDemandMaterialSuggest.get_suggest(
            self.partner_id, self.user_id, self.demand_day, store_id, product_ids
        )
        print("suggests: ", suggests)

    def test_tea_and_bread_material_suggest_2(self):
        store_id = 4217527472486350849
        product_ids = [4217613150616289281]
        demand_day = datetime(year=2019, month=7, day=22)
        suggests = TeaBreadDemandMaterialSuggest.get_suggest(
            self.partner_id, self.user_id, demand_day, store_id, product_ids
        )
        print("suggests: ", suggests)

    def test_new_retail_suggest(self):
        partner_id = 2
        user_id = 4186056888460247154
        demand_day = datetime(year=2019, month=7, day=22)
        store_id = 4217527473832722433
        product_ids = [
            4217605736626126849,
        ]
        suggest = DemandSuggest.get_demand_suggest(
            partner_id=partner_id, user_id=user_id, store_id=store_id,
            demand_day=demand_day, product_ids=product_ids,
        )
        print(suggest)

class TestSaleForecast(unittest.TestCase):
    def setUp(self):
        self.products = [
            {"product_id": 4175525475252174849,
             "arrival_day": 3,
             "min_quantity": 2,
             'max_quantity': 550,
             'increment_quantity': 2,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             "unit_rate": 1.2,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
            {"product_id": 4175534073151750145,
             "arrival_day": 2,
             "min_quantity": 4,
             'max_quantity': 410,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             'increment_quantity': 1,
             "unit_rate": 1.3,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
            {"product_id": 4176565203472744449,
             "arrival_day": 1,
             "min_quantity": 6,
             'sale_type': 'NEW-RETAIL-SHORT-TERM',
             'max_quantity': 234,
             'increment_quantity': 3,
             "unit_rate": 1.4,
             'bom_type': 'MANUFACTURE',
             'product_type': 'FINISHED',
             },
        ]
        self.product_ids = [
            4217605213176987649,
            4217605216196886529,
            4217605216423378945,
            4217605217455177729,
        ]
        self.partner_id = 2
        self.user_id = 4186056888460247154
        self.store_id = 4217527479276929025   # 正大广场店
        self.demand_day = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        self.products = list_store_product_detail(
            self.partner_id, self.user_id, self.store_id, self.product_ids
        )

    def test_re_calc_material_sale_forecast(self):
        resp = SaleForecastTask.re_calc_material_sale_forecast(
            partner_id=self.partner_id, user_id=self.user_id,
            store_id=self.store_id, biz_date=self.demand_day,
        )
        print("response: ", resp)

    def test_create_store_sale_forecast(self):
        for store_id in [4217527472486350849]:
            for i in range(7):
                demand_day = self.demand_day + timedelta(days=i)
                response = SaleForecastTask.create_store_sale_forecast(
                    self.partner_id, self.user_id, store_id,
                    biz_date=self.demand_day
                )

