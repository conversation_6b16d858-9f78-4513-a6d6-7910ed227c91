import grpc
import sys
import os
import json
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.frs_management import franchisee_demand_pb2, franchisee_demand_pb2_grpc, franchisee_refund_pb2_grpc,\
    franchisee_refund_pb2, franchise_hd_assignment_pb2, franchise_hd_assignment_pb2_grpc
from supply.proto.franchisee import vouchers_pb2_grpc, vouchers_pb2
from google.protobuf.timestamp_pb2 import Timestamp
from supply.client.metadata_service import metadata_service
from supply.utils.helper import convert_to_int
from datetime import datetime
from google.protobuf.json_format import MessageToDict

_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(1)
partner_id = str(1026)
# partner_id = str(227)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
    ("from_grpc", 'true')
)


def get_timestamp(value: datetime):
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


# start = datetime.now() - timedelta(days=5)
# end = start + timedelta(days=10)
start = datetime.strptime("2023-02-02 16:00:00", "%Y-%m-%d %H:%M:%S")
end = datetime.strptime("2023-02-03 16:00:00", "%Y-%m-%d %H:%M:%S")
start_date = get_timestamp(start)
end_date = get_timestamp(end)
datetime_now = datetime.utcnow()
dt_now = get_timestamp(datetime_now)


def test_franchisee_demand(method):
    ret = None
    # global start_date
    # global end_date
    # global metadata
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = franchisee_demand_pb2_grpc.FranchiseeDemandServiceStub(channel=conn)
    if method == "获取商品可分配仓库":
        ret = client.GetProductDistribution(franchisee_demand_pb2.GetProductDistributionRequest(
            demand_id=4703916579551772672,
            product_ids=[4679592391831715841, 4690418195641729024, 4685064577098973185, 4685002247061372929,
                         4690415869241655296, 4690415978272587776, 4679593461123383297, 4679593143941726209,
                         4679592867738419201, 4690419166786682880, 4690418717094379520, 4690418518808657920,
                         4690417780023951360, 4690417557474181120, 4690417429237530624, 4690417231488679936,
                         4690417120222183424, 4690417010310447104, 4690416890823114752, 4690416505177833472,
                         4690416250134790144]),
            metadata=metadata
        )
    if method == "更新商品详情":
        req = dict(
            demand_id=4719059131053441024,
            # status="R_APPROVE",
            products=[
                {
                    "id": 4720240192038961152,
                    "product_id": 4690415869241655296,
                    "unit_id": 4491998805654274048,
                    "tax_price": 9,
                    "tax_rate": 22,
                    "sales_price": 9,
                    "confirm_quantity": 11
                },
                {
                    "id": 4720241880971935744,
                    "product_id": 4679592391831715841,
                    "unit_id": 4491998805654274048,
                    "tax_price": 15,
                    "tax_rate": 22,
                    "sales_price": 15,
                    "confirm_quantity": 11
                },
                {
                    "id": 4719059131250573312,
                    "product_id": 4690418518808657920,
                    "quantity": 1,
                    "tax_price": 18,
                    "tax_rate": 22,
                    "sales_price": 18,
                    # "approve_quantity": 1
                    "confirm_quantity": 10
                },
                {
                    "id": 4720241880976130048,
                    "product_id": 4690418717094379520,
                    "tax_price": 2.36,
                    "tax_rate": 22,
                    "sales_price": 2.36,
                    "confirm_quantity": 10
                },
                {
                    "id": 4720241880971935745,
                    "product_id": 4567986945032192000,
                    "confirm_quantity": 10
                }
            ]
        )
        ret = client.UpdateProducts(franchisee_demand_pb2.UpdateProductRequest(**req), metadata=metadata)
    if method == "查询订货单":
        ret = client.GetFDemandById(franchisee_demand_pb2.GetFDemandByIdRequest(demand_id=4670956590383697921),
                                    metadata=metadata)
    if method == "变更订货单状态":
        ret = client.DealFDemandById(
            franchisee_demand_pb2.DealFDemandByIdRequest(demand_ids=[4710079427499851776], action="CONFIRMED"),
            metadata=metadata)
    if method == "订单新增商品":
        req = dict(
            demand_id=4678463768023498753,
            product_ids=[4621288835165290497, 4498078181525291009, 4595943886425194496]
        )
        ret = client.AddFDemandProduct(franchisee_demand_pb2.AddFDemandProductRequest(**req), metadata=metadata)
    if method == "导入订单商品":
        req = dict(
            import_type="Overwrite",
            batch_id=4678463768023498753,
            rows=[
                dict(demand_id=4685783769785024513,
                     products=[
                         dict(product_id=4621288835165290497,
                              category_id=4669850835700678657,
                              unit_id=4593663713541718017,
                              accounting_unit_id=4593663713541718017,
                              unit_rate=1,
                              quantity=50,
                              order_tax_price=10,
                              sale_tax_price=20,
                              tax_rate=4,
                              min_quantity=1,
                              max_quantity=100,
                              increment_quantity=1,
                              arrival_days=3
                              )
                     ]),
                dict(
                    demand_id=4685782427670659073,
                    products=[
                        dict(product_id=4621288835165290497,
                             category_id=4669850835700678657,
                             unit_id=4593663713541718017,
                             accounting_unit_id=4593663713541718017,
                             unit_rate=1,
                             quantity=50,
                             order_tax_price=10,
                             sale_tax_price=20,
                             tax_rate=4,
                             min_quantity=1,
                             max_quantity=100,
                             increment_quantity=1,
                             arrival_days=3
                             )
                    ]
                )
            ]
        )
        ret = client.ImportFDemandProducts(franchisee_demand_pb2.ImportFDemandProductsRequest(**req), metadata=metadata)

    if method == "批量确认订单":
        req = dict(
            batch_method="SELF",
            include_ids=[4685783769785024513, 4685782427670659073, 4685781643885264897],
            exclude_ids=[4685781643885264897],
            start_date=start_date,
            end_date=end_date,
            line_doc_id=0
        )
        ret = client.BatchConfirmFDemand(franchisee_demand_pb2.BatchDealFDemandRequest(**req), metadata=metadata)
    if method == "批量还原订单":
        req = dict(
            batch_method="SELF",
            include_ids=[4685783769785024513, 4685782427670659073, 4685781643885264897],
            # exclude_ids=[4685781643885264897],
            # start_date=start_date,
            # end_date=end_date,
            line_doc_id=0
        )
        ret = client.BatchResetFDemand(franchisee_demand_pb2.BatchDealFDemandRequest(**req), metadata=metadata)
    if method == "查询订单日志":
        ret = client.GetHistoryById(franchisee_demand_pb2.GetHistoryByIdRequest(demand_id=4726363358188601344),
                                    metadata=metadata)
    return ret


def test_franchisee_refund(method):
    ret = None
    # global start_date
    # global end_date
    # global metadata
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = franchisee_refund_pb2_grpc.FranchiseeRefundServiceStub(channel=conn)
    if method == "查询退款列表":
        req = dict(
            refund_start_date=start_date,
            refund_end_date=end_date,
            types=["ORDER_REFUND", "OOS_REFUND", "RETURN_REFUND"],
            limit=10,
        )
        ret = client.ListFRefund(franchisee_refund_pb2.ListFRefundRequest(**req), metadata=metadata)
    elif method == "查询退款详情":
        ret = client.GetFRefundById(franchisee_refund_pb2.GetFRefundByIdRequest(
            refund_id=4668303914343366656
        ), metadata=metadata)
    elif method == "修改状态":
        ret = client.DealFRefundById(franchisee_refund_pb2.DealFRefundByIdRequest(
            refund_ids=[4665439179161014273],
            action="APPROVED",
            remark="仅退款"
        ), metadata=metadata)

    return ret


def test_franchisee_hd_demand(method):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = franchise_hd_assignment_pb2_grpc.FranchiseeHdAssignmentStub(channel=conn)
    if method == "CreateHdDemand":
        req = dict(
            batch_id=4665439179161014273,
            demand_date=start_date,
            arrival_date=end_date,
            type="FMD",
            sub_type="PRODUCT",
            by_product_params=dict(
                received_by=4541122673178116096,
                products=[dict(product_id=4498076488226045953, unit_id=4488348217351602177,
                               category_id=4487929436691234817, quantity=10,
                               tax_price=10, tax_rate=5, arrival_days=2)]
            )
        )
        ret = client.CreateHdDemand(franchise_hd_assignment_pb2.CreateHdDemandRequest(**req), metadata=metadata)
    if method == "GetValidStoreByProduct":
        req = dict(
            product_ids=[4626740277858631681]
        )
        ret = client.GetValidStoreByProduct(franchise_hd_assignment_pb2.GetValidStoreByProductRequest(**req),
                                            metadata=metadata)

    if method == "GetValidProductByStore":
        req = dict(
            store_id=4679554862445232129,
            order_type_id=4695867957761343488,
        )
        ret = client.GetValidProductByStore(franchise_hd_assignment_pb2.GetValidProductByStoreRequest(**req),
                                            metadata=metadata)

    if method == "ListHdDemand":
        req = dict(
            start_date=start_date,
            end_date=end_date,
            status=[],
            types=["FMD"],
            bus_types=["HD_ASSIGN"],
            limit=10,
            include_total=True,
            product_ids=[4582170307883728896]
        )
        ret = client.ListHdDemand(franchise_hd_assignment_pb2.ListHdDemandRequest(**req), metadata=metadata)

    return ret


def test_franchisee_vouchers(method):
    ret = None
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = vouchers_pb2_grpc.VouchersServiceStub(channel=conn)
    if method == "CreateVouchers":
        req = dict(
            request_id=4665439179161014274,
            name="月神代金券",
            order_type_ids=[4687231508014956545, 4687231486611423233, 4687231456089473025],
            start_date=start_date,
            end_date=end_date,
            amount=88888,
            remark="三界通用",
            store_ids=[4566497707140841473, 4578835430711033857],
            regions=[dict(
                region_type="branch_region",
                region_id=4590824976172580865,
                region_code="1000",
                region_name="上海久久丫"
            )]
        )
        ret = client.CreateVouchers(vouchers_pb2.CreateVouchersRequest(**req), metadata=metadata)
    if method == "ListVouchers":
        req = dict(
            status=["INITED", "CONFIRMED"],
            order_type_ids=[4687231508014956545],
            offset=0,
            limit=10,
            include_total=True,
        )
        ret = client.ListVouchers(vouchers_pb2.ListVouchersRequest(**req), metadata=metadata)
    if method == "GetVouchersDetail":
        ret = client.GetVouchersDetail(vouchers_pb2.GetVouchersDetailRequest(voucher_id=4688702213358784513),
                                       metadata=metadata)
    if method == "UpdateVouchers":
        req = dict(
            voucher_id=4688705060947783681,
            name="月神代金券",
            order_type_ids=[4687231508014956545, 4687231486611423233],
            amount=888888,
            remark="",
            store_ids=[4566497707140841473, 4578835430711033857, 4589322090032889857]
        )
        ret = client.UpdateVouchers(vouchers_pb2.UpdateVouchersRequest(**req), metadata=metadata)
    if method == "DealVouchersById":
        req = dict(
            voucher_ids=[4688705060947783681],
            action="CONFIRMED"
        )
        ret = client.DealVouchersById(vouchers_pb2.DealVouchersByIdRequest(**req), metadata=metadata)

    return ret


if __name__ == "__main__":
    # res = test_franchisee_demand("更新商品详情")
    # res = test_franchisee_refund("查询退款详情")
    # res = test_franchisee_refund("查询退款列表")
    # res = test_franchisee_demand("获取商品可分配仓库")
    # res = test_franchisee_demand("查询订货单")
    # res = test_franchisee_demand("变更订货单状态")
    # res = test_franchisee_demand("订单新增商品")
    # res = test_franchisee_demand("导入订单商品")
    # res = test_franchisee_demand("批量确认订单")
    # res = test_franchisee_demand("批量还原订单")
    res = test_franchisee_demand("查询订单日志")
    # res = test_franchisee_hd_demand("GetValidStoreByProduct")
    # res = test_franchisee_hd_demand("GetValidProductByStore")
    # res = test_franchisee_vouchers("CreateVouchers")
    # res = test_franchisee_vouchers("ListVouchers")
    # res = test_franchisee_vouchers("UpdateVouchers")
    # res = test_franchisee_vouchers("DealVouchersById")
    print(MessageToDict(res))
