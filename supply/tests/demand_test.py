# -*- coding: utf-8 -*-

import json
import os
import sys
import unittest
from datetime import datetime

TEST_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(TEST_DIR)
sys.path.append(TEST_DIR)
sys.path.append(os.path.join(TEST_DIR, "supply", "proto"))

import grpc
from google.protobuf.timestamp_pb2 import Timestamp

from supply.client.metadata_service import metadata_service   
# from supply.module.demand import demand_module
from supply.module.demand_two import demand_two_module
from supply.proto.supply_pb2 import *
from supply.proto import supply_pb2
from supply.proto.supply_pb2_grpc import supplyStub
from supply.utils import pb2dict
from supply.utils.helper import get_today_datetime
from google.protobuf.json_format import MessageToDict
from supply.module.sale_forecast import list_forecast_store
from google.protobuf.json_format import MessageToDict
from supply.module.demand_suggest.base import DemandSuggest
from google.protobuf.timestamp_pb2 import Timestamp
from supply.module.demand_suggest.tea_bread_material import TeaBreadDemandMaterialSuggest

metadata = (
    ("partner_id", "100"),
    ("user_id", "197"),
)

channel = grpc.insecure_channel("127.0.0.1:8686")
cli = supplyStub(channel)


class demand_test(unittest.TestCase):

    @unittest.skip
    def test_ping(self):
        null = Null()
        cli.Ping(null)

    @unittest.skip
    def test_ActDemandBatchStore(self):
        req = ActDemandBatchRequest(mode="create",
                                    bizdt=Timestamp(seconds=int(get_today_datetime(plus_day=1).timestamp())))
        print(cli.ActDemandBatchStore(req, metadata=metadata))

    # ActOrderBatchStore
    @unittest.skip
    def test_ActOrderBatchStore(self):
        req = Bizdt(bizdt=Timestamp(seconds=int(get_today_datetime(utc=True).timestamp())))
        # req = ActDemandBatchRequest(
        #     mode="create",
        #     bizdt=Timestamp(seconds=int(datetime.strptime("2019-01-19 17:15:04", '%Y-%m-%d %H:%M:%S').timestamp())))
        print(cli.ActOrderBatchStore(req, metadata=metadata))


    @unittest.skip
    def test_UpdateDemandProduct(self):
        req = UpdateDemandProductRequest()
        item = req.product.add()
        item.product_id = 4176565709339361281
        item.quantity = 13.1
        item.tag_type = "FINISHED"
        # item2 = req.product.add()
        # item2.product_id = 4175525475252174849
        # item2.quantity = 23
        # item2.tag_type = "FINISHED"
        req.demand_id = 4187496176905670657
        print(cli.UpdateDemandProduct(req, metadata=metadata))

    def test_ListDemand(self):
        req = QueryDemandRequest()
        # store_ids = req.store_ids.split(",")
        # has_product = req.has_product
        # start_date = datetime.fromtimestamp(req.start_date.seconds)
        # end_date = datetime.fromtimestamp(req.end_date.seconds)
        # status = req.status
        # order = req.order
        # offset = req.offset
        # limit = req.limit
        # req.store_ids.append(4176234574621179905)
        # req.store_ids.append(4176234574621179905)
        # req.has_product = "0"
        req.start_date.CopyFrom(Timestamp(seconds=int(get_today_datetime().timestamp())))
        req.end_date.CopyFrom(Timestamp(seconds=int(get_today_datetime().timestamp())))
        req.status.append("INITED")
        req.status.append("COMMIT")
        req.order = "desc"
        # req.sort = 'store_secondary_id'
        req.offset = 0
        req.limit = 10

        response = cli.ListDemand(req, metadata=metadata)
        print("test_ListDemand-result={}".format(pb2dict(response)))

    @unittest.skip
    def test_GetDemandProductDetail(self):
        # rpc GetDemandProductDetail(IdRequest) returns (QueryDemandProductResponse)
        req = IdRequest()
        req.id = 4191785982166667265
        print("test_GetDemandProductDetail_result:{}".format(cli.GetDemandProductDetail(req, metadata=metadata)))

    # @unittest.skip
    def test_ChangeDemandStatus(self):
        print('ewrweqr')
        req = ChangeDemandStatusRequest()
        demand_date = '2019-09-29'
        arrive_date='2019-09-30'
        demand_date = datetime.strptime(demand_date, '%Y-%m-%d')
        arrive_date = datetime.strptime(arrive_date, '%Y-%m-%d')
        print(int((arrive_date-demand_date).days))
        req.id = 4279469456482111488
        # req.status = "APPROVED"
        req.status = "COMMIT"
        print("test_ConfirmDemandMain_result:{}".format(cli.ChangeDemandStatus(req, metadata=metadata)))

    @unittest.skip
    def test_GetDemandDetail(self):
        req = IdRequest()
        req.id = 4187496176905670657
        print("test_GetDemandDetail:{}".format(cli.GetDemandDetail(req, metadata=metadata)))

    def test_CreateDemandMain(self):
        # {"object_id":"4217527473354571777",
        # "demand_date":"2019-07-21T16:00:00.000Z",
        # "arrival_date":"2019-07-22T16:00:00.000Z",
        # "type":"HD","remark":"2","item_ids":[{"object_by":"4217605218075934721","quantity":1,"distribution_type":"BREAD"}]}
        req = DemandMainRequest()
        req.demand_date.CopyFrom(Timestamp(seconds=int(get_today_datetime().timestamp())))
        req.arrival_date.CopyFrom(Timestamp(seconds=int(get_today_datetime().timestamp())))
        req.remark = ""
        req.type = "HD"
        req.object_id = 4217527473195188225
        item = req.item_ids.add()
        item.object_by = 4217605736823259137
        item.quantity = 12
        item.distribution_type = 'BREAD'

        print("test_CreateDemandMain:{}".format(cli.CreateDemandMain(req, metadata=metadata)))

    @unittest.skip
    def test_ResetDemandMain(self):
        req = IdRequest()
        req.id = 4187201587485913089
        print("test_ResetDemandMain:{}".format(cli.ResetDemandMain(req, metadata=metadata)))

    @unittest.skip
    def test_DeleteDemandMain(self):
        req = IdRequest()
        req.id = 4187201587485913089
        print("test_DeleteDemandMain:{}".format(cli.DeleteDemandMain(req, metadata=metadata)))

    @unittest.skip
    def test_get_valid_store_by_product(self):
        # product_ids=["4210276050636111873","4176602106863550465","4176582377935470593","4188575636941438977","4192678497547714561","4192678680092213249"]
        product_ids = ["4217605216423378945"]
        product_ids = [int(i) for i in product_ids]
        ret = demand_module.get_valid_store_product(product_ids, 'MD', 2, 4186056888460247154)
        print("test_get_valid_store_by_product_new:{}".format(json.dumps(ret)))
        print("------------------------------------------------------------")
        print("------------------------------------------------------------")
        print("------------------------------------------------------------")
        print("------------------------------------------------------------")
        print("------------------------------------------------------------")
        print("------------------------------------------------------------")
        # ret = demand_module.get_valid_store(product_ids, 'HD', 4183192445833445399, 4186056888460247154, branch_ids=["4175911088707076097"])
        print("test_get_valid_store_by_product_old:{}".format(json.dumps(ret)))

    def test_GetValidProductByStoreId(self):
        req = GetValidProductByStoreIdRequest(id=4217527472876421121, type='HD',
                                              # distribution_type="NMD",
                                              order_date=Timestamp(
                                                  seconds=int(get_today_datetime(plus_day=1, utc=True).timestamp())))
        # req.id = 
        ret = MessageToDict(cli.GetValidProductByStoreId(req, metadata=metadata))
        old_rows = ret.get("rows")
        print("test_GetValidProductByStoreId11111:{}".format(json.dumps(ret)))
        # print("---------------------------")
        # print("---------------------------")
        # print("---------------------------")
        # print("---------------------------")
        # print("---------------------------")
        ret = demand_module.get_valid_demand_product(4217527472876421121, 2, 4227715151027388419,
                                                     # distr_type="NMD",
                                                     type="SD",
                                                     order_date=get_today_datetime(plus_day=1))
        # print("ret2222==", len(ret[0]))
        # new_rows = ret[0]

        # for i in old_rows:
        #     flag = False
        #     for j in new_rows:
        #         if str(i.get("productId")) == str(j.get("product_id")):
        #             flag = True
        #     if not flag:
        #         print("product_id=", i.get("productId"))

        print("test_GetValidProductByStoreId22222:{}".format(json.dumps(ret)))
        print("ret_rows=" + str(len(ret[0])))
        # get_valid_demand_product(store_id, partner_id, user_id, distr_type=None, type=Demand_type.SD.code, product_ids=None, 
        # order_date=None, vendor_id=None, limit=None, offset=None):

    @unittest.skip
    def test_get_valid_product(self):
        # print("test_get_valid_product: ", demand_module.get_valid_product(4176234574621179905, 4183192445833445399, 4186056888460247152,
        #             type="HD", product_ids=[4193599925927280641], order_date=None))
        print("1111111", metadata_service.get_store_list(partner_id=4183192445833445399, user_id=4186056888460247152))

    @unittest.skip
    def test_UpdateDemandInfo(self):
        req = UpdateDemandInfoRequest()
        req.id = 4191798038786117633
        req.remark = "test remark"
        print("test_UpdateDemandInfo:{}".format(cli.UpdateDemandInfo(req, metadata=metadata)))

    @unittest.skip
    def test_user_name(self):
        print("test_user_name-:", metadata_service.get_username_by_pid_uid(4183192445833445399, 4186056888460247154))


    @unittest.skip
    def test_get_suggest_quantity(self):
        store_id = 4176234574621179905
        products = [
            {"product_id": 4175525475252174849,
             "arrival_day": 3,
             "min_quantity": 2,
             'max_quantity': 550,
             'increment_quantity': 2,
             'sale_type': 'NEW-RETAIL',
             "unit_rate": 1.2},
            {"product_id": 4175534073151750145,
             "arrival_day": 2,
             "min_quantity": 4,
             'max_quantity': 410,
             'sale_type': 'NEW-RETAIL',
             'increment_quantity': 1,
             "unit_rate": 1.3},
            {"product_id": 4176565203472744449,
             "arrival_day": 1,
             "min_quantity": 6,
             'sale_type': 'NEW-RETAIL',
             'max_quantity': 234,
             'increment_quantity': 3,
             "unit_rate": 1.4},
        ]
        bizdt = get_today_datetime(plus_day=1)
        print("bizdt:" + str(bizdt))
        partner_id = 4183192445833445399
        user_id = 4186056888460247154
        print("get_suggest_quantity_result:" + str(
            demand_two_module.get_suggest_quantity(store_id, products, bizdt, partner_id, user_id)))

    @unittest.skip
    def test_unit_list(self):
        partner_id = 4183192445833445399
        user_id = 4186056888460247154
        print("unit_list:" + str(metadata_service.get_unit_list(filters={"code": "PG"},
                                                                partner_id=4183192445833445399,
                                                                user_id=4186056888460247154)))

    # @unittest.skip
    def test_UploadDemandMaster(self):
        req = UploadDemandMasterRequest()
        req.file_name = 'demand_main(1).xls'
        import base64
        metadata = (
            ("partner_id", "4206763836826861569"),
            ("user_id", "4263973348762976257"),
        )
        with open('demand_main(1).xls', 'rb') as f:
            # req.file = f.read()
            # print(req.file)
            file = f.read()
            file = base64.b64encode(file)
            file = str(file, 'utf-8')
            print('file', file)
            req.file = file
            req.file_type = 'ST'
            print("test_ConfirmDemandMain_result:{}".format(cli.UploadDemandMaster(req, metadata=metadata)))

    def test_UploadDemandMasterby_batch_id(self):
        print('aaaaa')
        req = GetDemandMasterUploadByBatchIdRequest()
        req.batch_id = 4238998909881942016
        req.include_total = True
        print("test_ConfirmDemandMain_result:{}".format(cli.GetDemandMasterUploadByBatchId(req, metadata=metadata)))

    def test_UploadDemandMaster_(self):
        print('aaaaa')
        req = GetDemandMasterUploadRequest()
        # req.batch_id =4238998909881942016
        req.include_total = True
        req.file_name = '我爱我'
        print("test_ConfirmDemandMain_result:{}".format(cli.GetDemandMasterUpload(req, metadata=metadata)))

    def test_ApproveDemandMasterUpload(self):
        print('aaaaa')
        req = ApproveDemandMasterUploadRequest()
        req.batch_id = 4611116661976694784
        ret = cli.ApproveDemandMasterUpload(req, metadata=metadata)
        print("test_ConfirmDemandMain_result:{}".format(ret))

    def test_list_forecast_store(self):
        partner_id = 2
        user_id = 2
        forecast_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        resp, count = list_forecast_store(
            partner_id, user_id, forecast_date,
            None,
            10, 0
        )
        print(resp, count)

    def test_ListForecastStore(self):
        req = supply_pb2.ListForecastStoreRequest(store_ids=[4217527472486350849], store_type='')
        resp = cli.ListForecastStore(req, metadata=metadata)
        print(resp)

    def test_ListProductSaleForecast(self):
        req = supply_pb2.ListProductSaleForecastRequest(store_id=4217527472486350849, product_sale_type='BREAD',
                                                        sort = 'store_id')
        resp = cli.ListProductSaleForecast(req, metadata=metadata)
        print(MessageToDict(resp, including_default_value_fields=True, preserving_proto_field_name=True,
                            ))

    def test_ConfirmProductSaleForecast(self):
        info_class = supply_pb2.ConfirmProductSaleForecastRequest.ForecastInfo
        forecast_infos = [
            info_class(id=4248469212401209345, confirm_amount=100),
            info_class(id=4248469212401209346, confirm_amount=110),
            info_class(id=4248469212401209347, confirm_amount=120),
        ]
        req = supply_pb2.ConfirmProductSaleForecastRequest(forecast_infos=forecast_infos)
        resp = cli.ConfirmProductSaleForecast(req, metadata=metadata)
        print(resp)

    def test_GetProductSaleForecast(self):
        user_id = str(4201196624908648450)
        partner_id = str(2)
        bom_product_ids = []
        # rows = metadata_service.get_product_list(user_id=user_id,partner_id=partner_id).get('rows')
        # 4217527476508688385
        product_filters = {"status__eq": "ENABLED", "allow_order__eq": True,
                           "__or": [{"product_type__eq": "FINISHED",
                                     "sale_type__in": ["NEW-RETAIL-SHORT-TERM", "NEW-RETAIL-NORMAL"]},
                                    {"product_type__neq": "FINISHED"}]}
        store_id=4217527473048387585
        date_ = datetime.now()
        order_date=datetime(date_ .year,date_.month,date_.day)
        # rows = metadata_service.get_list_valid_product_for_distr_by_id(store_id,
        #                                                               include_product_fields='name,code,model_name,model_name,model_code,storage_type,category,product_type,sale_type,distr_type',
        #                                                               distr_type=None, order_date=order_date,
        #                                                               filters={}, limit=-1, offset=0,
        #                                                               product_relation_filters={},
        #                                                               product_filters=product_filters,
        #                                                               partner_id=partner_id, user_id=user_id).get('rows')
        rows = metadata_service.get_product_list(
            filters={"product_type": "FINISHED", "status": "ENABLED",
                     "allow_order__eq": True, "sale_type__nin": ["NEW-RETAIL-SHORT-TERM", "NEW-RETAIL-NORMAL"]},
            return_fields='name,code,second_code,sale_type,product_type,bom_type,storage_type,status,category,model_name',
            include_units=True, partner_id=partner_id, user_id=user_id).get('rows')
        print('rows',rows)
        print('rows', len(rows))
        for r in rows:
            if r.get("sale_type") in (['BREAD', 'TEA']) and r.get("product_type") == "FINISHED":
                bom_product_ids.append(int(r.get('id', 0)))
        print('bom_product_ids',bom_product_ids)
        # for r in [4217613218576596993,4217613220950573057,4217613222397607937]:
        #     bom_product_ids.remove(r)
        req = supply_pb2.GetProductSaleForecastRequest(
            store_id = 4217527487942361089,
            product_ids = bom_product_ids[:10],
            is_tea_and_bread=True

        )
        # 1563926400 --24  1563753600--22 1564012800---25
        req.demand_day.seconds = 1564012800
        # resp = cli.GetProductSaleForecast(req, metadata=metadata)
        # resp = MessageToDict(resp, preserving_proto_field_name=True)
        is_tea_and_bread=True
        from datetime import timedelta
        demand_day = datetime.now()-timedelta(days=1)
        product_ids = bom_product_ids
        if is_tea_and_bread:
            res = TeaBreadDemandMaterialSuggest.get_suggest(
                partner_id, user_id, demand_day, store_id, product_ids
            )
        else:
            res = DemandSuggest.get_demand_suggest(partner_id, user_id, store_id, demand_day, product_ids)
        print('res', res)
        # print(resp)
        ids=[]
        # for id,v in resp['map_field'].items():
        #     ids.append(id)
        # rows = metadata_service.get_product_list(ids=ids,user_id=user_id, partner_id=partner_id).get('rows')
        # name=[]
        # for r in rows:
        #     name.append(r['name'])
        # print(name)

    def test_GetDemandAdjustProductByStoreId(self):
        req = GetDemandAdjustProductByStoreIdRequest()
        req.store_id = 4217527472486350849
        print("test_GetDemandAdjustProductByStoreId_result:{}".format(cli.GetDemandAdjustProductByStoreId(req, metadata=metadata)))


    def test_CreateDemandAdjust(self):
        # {"object_id":"4217527473354571777",
        # "demand_date":"2019-07-21T16:00:00.000Z",
        # "arrival_date":"2019-07-22T16:00:00.000Z",
        # "type":"HD","remark":"2","item_ids":[{"object_by":"4217605218075934721","quantity":1,"distribution_type":"BREAD"}]}
        req = CreateDemandAdjustRequest()

        req.demand_date.CopyFrom(Timestamp(seconds=int(get_today_datetime().timestamp())))
        req.arrival_date.CopyFrom(Timestamp(seconds=int(get_today_datetime().timestamp())))
        req.remark = ""
        req.type = "AD"
        req.store_id = 4217527472876421121
        item = req.products.add()
        item.product_id = 4217605737632759809
        item.unit_id = 4217533956788060161
        item.quantity = 15

        print("test_CreateDemandAdjust:{}".format(cli.CreateDemandAdjust(req, metadata=metadata)))

    def test_GetValidStoreByProductId(self):
        request = dict(
            product_ids=[4374806530898264065, 4374806532819255297],
            type="MD",
            order_date=Timestamp(seconds=int(get_today_datetime().timestamp())),
            branch_ids=[],
            category_ids=[],
        )
        print("test_GetValidStoreByProductId:{}".format(
            cli.GetValidStoreByProductId(GetValidStoreByProductIdRequest(**request), metadata=metadata)))

    def test_GetDistributionByDemand(self):
        request = dict(
            id=4416195787635945473
        )
        print("test_GetDistributionByDemand:{}".format(
            cli.GetDistributionByDemand(IdRequest(**request), metadata=metadata)))



if __name__ == "__main__":
    # unittest.main()
    a = demand_test()
    # print('sssss')
    # a.test_ListProductSaleForecast()
    # a.test_ChangeDemandStatus()
    # a.test_GetValidProductByStoreId()
    # quantity = round(8.87, 3)
    # print(quantity)
    # a.test_UploadDemandMasterby_batch_id()
    a.test_ApproveDemandMasterUpload()
