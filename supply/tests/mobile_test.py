import grpc
import sys, os, unittest
from datetime import datetime
import json

TEST_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(TEST_DIR)
sys.path.append(TEST_DIR)
sys.path.append(os.path.join(TEST_DIR, "supply", "proto"))

# from ..api.receiving import ReceivingAPI
from supply.proto.mobile import mobile_demand_pb2, mobile_demand_pb2_grpc
from supply.proto.mobile import mobile_return_pb2, mobile_return_pb2_grpc
from supply.proto.mobile import mobile_franchisee_demand_pb2, mobile_franchisee_demand_pb2_grpc
from supply.proto.store import returns_pb2, returns_pb2_grpc
from supply.proto.mobile import mobile_inventory_bi_pb2, mobile_inventory_bi_pb2_grpc
from supply.proto.franchisee import inventory_bi_pb2, inventory_bi_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp


_HOST = '0.0.0.0'
_PORT = '8686'

start_date=datetime(2021, 2, 27, 00, 00, 00, 000000)
start_date = Timestamp(seconds=int(start_date.timestamp()))

end_date=datetime(2021, 3, 10, 00, 00, 00, 000000)
end_date = Timestamp(seconds=int(end_date.timestamp()))

arrival_start_date=datetime(2019, 12, 12, 00, 00, 00, 0000000)
arrival_start_date = Timestamp(seconds=int(arrival_start_date.timestamp()))

arrival_end_date=datetime(2019, 12, 15, 23, 59, 59, 000000)
arrival_end_date = Timestamp(seconds=int(arrival_end_date.timestamp()))

daily_date=datetime(2019, 3, 25, 8, 00, 00, 000000)
daily_date = Timestamp(seconds=int(daily_date.timestamp()))

metadata = (
    ("partner_id", "1026"),
    ("user_id", "1")
    )


def test(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT, options=[
                ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
            ],)
    client = mobile_demand_pb2_grpc.MobileDemandServiceStub(channel=conn)
    if num == 1:
        res = client.ListDemand(mobile_demand_pb2.ListDemandRequest(
            start_date=start_date, end_date=end_date, 
            # status=['COMMIT'], 
            has_product='1'
            ), metadata=metadata)
    elif num == 2:
        res = client.GetProductsById(mobile_demand_pb2.GetProductsByIdRequest(
            id=4462852520545652737), metadata=metadata)
    elif num == 3:
        res = client.GetHistoryById(mobile_demand_pb2.GetHistoryByIdRequest(
            id=4377794790492286977), metadata=metadata)
    elif num == 4:
        res = client.GetDemandById(mobile_demand_pb2.GetDemandByIdRequest(
            id=4462852520545652737), metadata=metadata)
    elif num == 5:
        res = client.UpdateProducts(mobile_demand_pb2.UpdateProductRequest(
            id=4464383288013783040,
            remark='test_remark',
            products = [
                {
                    'id': 4464383288294801408,
                    'product_id': 4462867089551523841,
                    'quantity': 12

                }
            ]), metadata=metadata)
    elif num == 6:
        res = client.GetProductsDetailById(mobile_demand_pb2.GetProductsDetailByIdRequest(
            product_id=4460340975729901569), metadata=metadata)
            


    elif num == 7: 
        client = mobile_return_pb2_grpc.MobileReturnServiceStub(channel=conn)
        products = [
                        {
                            "product_id":4462867089551523841,
                            "product_code":"00001",
                            "product_name":"巧克力威化饼干",
                            "quantity":10,
                            "unit_id":4462867279771598849,
                            "unit_name":"克",
                            "unit_rate":1
                        }
                    ]
        res = client.CreateReturn(mobile_return_pb2.CreateReturnRequest(
            sub_type="store",
            return_by=4462866233712181249,
            return_delivery_date=end_date, 
            products=products, 
            # receiving_id=521912280015,
            logistics_type='NMD',
            return_to=4464057421123289089
            ), metadata=metadata)
    
    elif num == 8: 
        client = mobile_return_pb2_grpc.MobileReturnServiceStub(channel=conn)
        res = client.ListReturn(mobile_return_pb2.ListReturnRequest(),metadata=metadata)

    elif num == 9: 
        client = mobile_return_pb2_grpc.MobileReturnServiceStub(channel=conn)
        res = client.GetValidReturnProduct(mobile_return_pb2.GetValidReturnProductRequest(
            store_id=4462866233712181249
        ),metadata=metadata)

    elif num == 10: 
        client = mobile_return_pb2_grpc.MobileReturnServiceStub(channel=conn)
        res = client.GetReturnById(mobile_return_pb2.GetReturnByIdRequest(id=4464741103609872384),metadata=metadata)

    elif num == 11: 
        client = returns_pb2_grpc.StoreReturnServiceStub(channel=conn)
        products = [
                        {
                            "product_id":4462867089551523841,
                            "product_code":"00001",
                            "product_name":"巧克力威化饼干",
                            "quantity":10,
                            "unit_id":4462867279771598849,
                            "unit_name":"克",
                            "unit_rate":1,
                            "return_to":4464057421123289089
                        }
                    ]
        res = client.CreateReturn(returns_pb2.CreateReturnRequest(
            sub_type="store",
            return_by=4462866233712181249,
            return_delivery_date=end_date, 
            products=products, 
            logistics_type='NMD',
            request_id=2021042601
            ), metadata=metadata)
    elif num == 12: 
        client = mobile_inventory_bi_pb2_grpc.MobileInventoryBiServiceStub(channel=conn)
        res = client.FrsRealtimeInventory(mobile_inventory_bi_pb2.RealtimeInventoryRequest(
            branch_ids=[4604918965372813312],
            product_search_fields="咸鸭"
            ), metadata=metadata)
    return res

def test_frs_inventory(num):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT, options=[
                ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
            ],)
    client = inventory_bi_pb2_grpc.FrsStoreInventoryBiServiceStub(channel=conn)
    if num == 1:
        res = client.RealtimeInventory(inventory_bi_pb2.RealtimeInventoryRequest(
            branch_ids=[4634349687791517696],
            return_fields='cost'
        ), metadata=metadata)
        return res



def run():
   # response = test(12)
   response = test_frs_inventory(1)
   print(response)
if __name__ == '__main__':
    run()
