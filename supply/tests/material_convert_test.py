import grpc
import sys
import os
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.manufactory import material_convert_pb2, material_convert_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.utils.helper import get_today_datetime, get_product_unit_map
_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(4371272018445729794)
partner_id = str(4183192445833445399)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
)

def get_timestamp(value: datetime):
    # value = value - timedelta(hours=8)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


start_datetime = datetime.strptime("2020-09-15 00:00:00", "%Y-%m-%d %H:%M:%S")
end_datetime = datetime.strptime("2020-09-21 23:59:59", "%Y-%m-%d %H:%M:%S")
datetime_now = datetime.utcnow()
start_date = get_timestamp(start_datetime)
end_date = get_timestamp(end_datetime)
dt_now = get_timestamp(datetime_now)


def test_material_convert(api):
    """测试物料转换功能模块
    :param api  —— 传入api名字
    """
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = material_convert_pb2_grpc.MaterialConvertStub(channel=conn)
    print(start_date)
    print(end_date)
    if api == "CreateMaterialConvert":
        request = dict(
            branch_type="WAREHOUSE",
            status="INITED",
            convert_type="MANUAL",
            convert_date=dt_now,
            branch_id=103,
            branch_code="1003",
            branch_name="seesaw仓库",
            position_id=4394449921815388122,
            position_name="生豆仓",
            position_code="1010",
            convert_rule=1,
            remark="开发测试",
            request_id=4394449921815388163,
            materials=[dict(
                product_id=4303603808572801025,
                product_code="1000",
                product_name="1000",
                product_type="product_type",
                unit_id=4291789752425775105,
                unit_name="斤",
                unit_spec="500g",
                quantity=100,
                type="origin",
            ), dict(
                product_id=4303603808572801022,
                product_code="1001",
                product_name="1001",
                product_type="product_type",
                unit_id=4291789752425775105,
                unit_name="斤",
                unit_spec="500g",
                quantity=10,
                type="target",
            )
            ]
        )
        res = client.CreateMaterialConvert(material_convert_pb2.CreateMaterialConvertRequest(**request), metadata=metadata)
        print(res)

    if api == "ListMaterialConvert":
        request = dict(
            start_date=start_date,
            end_date=end_date,
            branch_ids=[],
            branch_type="STORE",
            code="",
            convert_rules=[],
            position_ids=[],
            convert_type="",
            status="",
            limit=10,
            offset=0,
            include_total=True,
            order=None,
            sort=None
        )
        res = client.ListMaterialConvert(material_convert_pb2.ListMaterialConvertRequest(**request), metadata=metadata)
        print(res)

    if api == "GetMaterialConvertDetail":
        request = dict(
            convert_id=4403908807832371200
        )
        res = client.GetMaterialConvertDetail(material_convert_pb2.GetMaterialConvertDetailRequest(**request),
                                              metadata=metadata)
        print(res)

    if api == "UpdateMaterialConvert":
        request = dict(
            convert_id=4413998876014411776,
            update_detail=False,
            branch_type="MACHINING_CENTER",
            status="CONFIRMED",
            convert_type="MANUAL",
            convert_date=end_date,
            branch_id=101,
            branch_code="1001",
            branch_name="开发测试",
            position_id=4394449921815388122,
            position_name="生豆仓",
            position_code="1010",
            convert_rule=1,
            remark="",
            materials=[dict(
                id=4403908807861731328,
                product_id=4303603808572801025,
                product_code="1000",
                product_name="1000",
                product_type="product_type",
                unit_id=4291789752425775105,
                unit_name="斤",
                unit_spec="500g",
                quantity=1000,
                type="origin",
            ), dict(
                id=4403908807891091456,
                product_id=4303603808572801022,
                product_code="1001",
                product_name="1001",
                product_type="product_type",
                unit_id=4291789752425775105,
                unit_name="斤",
                unit_spec="500g",
                quantity=100,
                type="target",
            )
            ]
        )
        res = client.UpdateMaterialConvert(material_convert_pb2.UpdateMaterialConvertRequest(**request),
                                           metadata=metadata)
        print(res)


if __name__ == "__main__":
    # test_material_convert("CreateMaterialConvert")
    # test_material_convert("ListMaterialConvert")
    # test_material_convert("GetMaterialConvertDetail")
    test_material_convert("UpdateMaterialConvert")


