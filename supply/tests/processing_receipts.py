import grpc
import sys
import os
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.manufactory import processing_receipts_pb2, processing_receipts_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.utils.helper import get_today_datetime, get_product_unit_map

_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(4371272018445729794)
partner_id = str(4183192445833445399)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
)


def get_timestamp(value: datetime):
    # value = value - timedelta(hours=8)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


start_datetime = datetime.strptime("2020-09-15 00:00:00", "%Y-%m-%d %H:%M:%S")
end_datetime = datetime.strptime("2020-09-20 23:59:59", "%Y-%m-%d %H:%M:%S")
datetime_now = datetime.utcnow()
start_date = get_timestamp(start_datetime)
end_date = get_timestamp(end_datetime)
dt_now = get_timestamp(datetime_now)


def test_processing_receipts(api):
    """测试包装单功能模块
    :param api  —— 传入api名字
    """
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = processing_receipts_pb2_grpc.ProcessingReceiptsStub(channel=conn)
    print(start_date)
    print(end_date)
    if api == "CreateProcessingReceipts":
        request = dict(
            status="INITED",
            processing_date=dt_now,
            machining_center_id=101,
            machining_center_code="1001",
            machining_center_name="seesaw加工中心",
            position_id=4394449921815388122,
            position_name="生豆仓",
            position_code="1010",
            processing_rule=1,
            remark="开发测试",
            request_id=4394449921815388161,
            target_material=4380310631497072641,
            target_material_code="5090280712",
            target_material_name="NOA 菠萝百香果慕斯85g",
            target_material_unit_id=4373346093845446657,
            target_material_unit_name="块",
            target_material_unit_spec="",
            theory_output_rate=0.8,
            actual_output_rate=0.75,
            actual_output_quantity=100,
            items=[dict(
                product_id=4374806626750693377,
                product_code="1000",
                product_name="1000",
                product_type="product_type",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                material_rate=0.2,
                actual_quantity=100,
            ), dict(
                product_id=4374806626499035137,
                product_code="1001",
                product_name="1001",
                product_type="product_type",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                material_rate=0.8,
                actual_quantity=100,
            )
            ]
        )
        res = client.CreateProcessingReceipts(processing_receipts_pb2.CreateProcessingReceiptsRequest(**request),
                                              metadata=metadata)
        print(res)

    if api == "ListProcessingReceipts":
        request = dict(
            start_date=start_date,
            end_date=end_date,
            code="",
            machining_centers=[],
            target_materials=[],
            position_ids=[],
            status="ALL",
            limit=10,
            offset=0,
            include_total=True,
            order=None,
            sort=None
        )
        res = client.ListProcessingReceipts(processing_receipts_pb2.ListProcessingReceiptsRequest(**request),
                                            metadata=metadata)
        print(res)

    if api == "GetProcessingReceiptsDetail":
        request = dict(
            receipt_id=4404878593869479936
        )
        res = client.GetProcessingReceiptsDetail(processing_receipts_pb2.GetProcessingReceiptsDetailRequest(**request),
                                                 metadata=metadata)
        print(res)

    if api == "UpdateProcessingReceipts":
        request = dict(
            receipt_id=4405689580038365185,
            update_detail=False,
            processing_date=end_date,
            status="APPROVED",
            machining_center_id=101,
            machining_center_code="1001",
            machining_center_name="seesaw加工中心",
            position_id=4394449921815388122,
            position_name="生豆仓",
            position_code="1010",
            processing_rule=1,
            remark="开发测试",
            target_material=4380310631497072641,
            target_material_code="5090280712",
            target_material_name="NOA 菠萝百香果慕斯85g",
            target_material_unit_id=4373346093845446657,
            target_material_unit_name="块",
            target_material_unit_spec="",
            actual_output_quantity=100,
            opened_position=True,
            items=[dict(
                id=4404587767264182272,
                product_id=4374806626750693377,
                product_code="1000",
                product_name="1000",
                product_type="product_type",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                actual_quantity=1000,
            ), dict(
                id=4404587767289348096,
                product_id=4374806626499035137,
                product_code="1001",
                product_name="1001",
                product_type="product_type",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                actual_quantity=100,
            )
            ]
        )
        res = client.UpdateProcessingReceipts(processing_receipts_pb2.UpdateProcessingReceiptsRequest(**request),
                                              metadata=metadata)
        print(res)


if __name__ == "__main__":
    # test_processing_receipts("CreateProcessingReceipts")
    # test_processing_receipts("ListProcessingReceipts")
    # test_processing_receipts("GetProcessingReceiptsDetail")
    test_processing_receipts("UpdateProcessingReceipts")
