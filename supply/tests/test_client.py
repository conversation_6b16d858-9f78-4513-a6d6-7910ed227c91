#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import grpc
import unittest
from load_path import run
run()

from supply.client.products_manage_service import products_manage_service
from supply.module.franchisee.franchisee_returns import franchisee_returns_service
from supply.proto.mobile import mobile_franchisee_return_pb2
channel = grpc.insecure_channel("localhost:8802")
metadata = (
    ("partner_id", "100"),
    ("user_id", "197"),
)

partner_id = 100
user_id = 192

class TestClient(object):
    def __init__(self):
        self.client = products_manage_service

    def test_GetAgentProducts(self):
        res = self.client.GetAgentProducts(store_id=4679554862445232129, partner_id=1026, user_id=197)
        print("res=", res)

    def test_GetDistributionRule(self):
        res = self.client.GetDistributionRule(store_ids=[4679554862445232129], partner_id=1026, user_id=197)
        print("res=", res)

class TestReturn(object):
    def test_get_valid_product(self):
        msg = {"store_id": 4679554862445232129}
        request = mobile_franchisee_return_pb2.GetValidProductRequest(**msg)
        res = franchisee_returns_service.get_valid_product(request, partner_id=1026, user_id=197)
        print("===res=", res)

if __name__ == '__main__':
    # TestClient().test_GetAgentProducts()
    # TestClient().test_GetDistributionRule()
    TestReturn().test_get_valid_product()
