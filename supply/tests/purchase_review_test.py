import grpc
import sys
import os
from pathlib import Path
from datetime import datetime

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from google.protobuf.timestamp_pb2 import Timestamp
from supply.proto.hd_management import purchase_review_pb2, purchase_review_pb2_grpc, invoice_blending_pb2_grpc, \
    invoice_blending_pb2, price_adjustment_pb2_grpc, price_adjustment_pb2
from google.protobuf.json_format import MessageToDict
from supply.client.metadata_service import metadata_service


_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(1)
partner_id = str(100)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
)


def get_timestamp(value: datetime):
    # value = value - timedelta(hours=8)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


def test_purchase_review(api):
    """测试采购复核功能模块
       :param api  —— 传入api名字
       """
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = purchase_review_pb2_grpc.PurchaseReviewStub(channel=conn)
    start_datetime = datetime.strptime("2021-11-01 00:00:00", "%Y-%m-%d %H:%M:%S")
    end_datetime = datetime.strptime("2022-01-01 23:59:59", "%Y-%m-%d %H:%M:%S")
    start_date = get_timestamp(start_datetime)
    end_date = get_timestamp(end_datetime)
    now_date = get_timestamp(datetime.utcnow())
    if api == "创建复核":
        request = dict(
            batch_id=4303027132106940418,
            order_type="RETURN",
            received_date=now_date,
            received_by=4462866233712181249,
            received_name="佛山大良店32323",
            received_code="23456",
            supplier_id=4467283686030573569,
            supplier_name="西诺迪斯食品（上海）有限公司",
            supplier_code="GYS001",
            sum_price_tax=66,
            sum_price=50,
            branch_type="STORE",
            company_id=4303612449015201793,
            products=[dict(
                product_id=4464067105527955457,
                product_code="000002",
                product_name="饼干",
                product_category_id=4462866725473353729,
                product_type="RAW",
                unit_id=4482428039866646528,
                unit_name="千克",
                unit_spec="KG",
                spec="500g/斤",
                quantity=2,
                actual_quantity=2,
                tax_rate=0.2,
                price=10,
                price_tax=12,
                sum_price=20,
                sum_price_tax=24,
            ),
                dict(
                    product_id=4292961446226034689,
                    product_name="薄荷叶",
                    product_code="10100105",
                    product_category_id=4292994193271291905,
                    product_type="RAW",
                    unit_id=4291789752425775105,
                    unit_name="千克",
                    unit_spec="KG",
                    spec="500g/斤",
                    quantity=3,
                    actual_quantity=3,
                    tax_rate=0.4,
                    price=10,
                    price_tax=14,
                    sum_price=30,
                    sum_price_tax=42,
                )],
        )
        res = client.CreatePurchaseReviewOrder(purchase_review_pb2.CreatePurchaseReviewOrderRequest(**request),
                                               metadata=metadata)
        return res

    if api == "复核列表":
        request = dict(
            start_date=start_date,
            end_date=end_date,
            order_code="",
            order_status="all",
            order_type="",
            supplier_ids=[],
            received_ids=[],
            limit=10,
            offset=None,
            include_total=True,
            order="asc",
            sort="adjust_sum_price",
            branch_type="STORE",
            company_id=4303612449015201793,
        )
        res = client.ListPurchaseReviewOrder(purchase_review_pb2.ListPurchaseReviewOrderRequest(**request),
                                             metadata=metadata)
    if api == "复核详情":
        request = dict(
            order_id=4512547669052096512,
            include_total=True,
            limit=10
        )
        res = client.GetPurchaseReviewDetail(purchase_review_pb2.GetPurchaseReviewDetailRequest(**request),
                                             metadata=metadata)
    if api == "复核明细":
        request = dict(
            start_date=start_date,
            end_date=end_date,
            order_code="",
            status="all",
            order_type="RECEIVE",
            supplier_ids=[],
            received_ids=[],
            limit=10,
            offset=None,
            include_total=True,
            order="asc",
            sort="adjust_sum_price",
            branch_type="STORE",
            # company_id=4303612449015201793,
        )
        res = client.ListPurchaseReviewDetail(purchase_review_pb2.ListPurchaseReviewOrderRequest(**request),
                                              metadata=metadata)
    if api == "采购复核":
        # request = dict(
        #     order_ids=[4306103481414975488, 4306100777980526592],
        #     include_product=False,
        #     # products=[dict(
        #     #     id=4306103481452724224,
        #     #     adjust_price_tax=15.3,
        #     #     adjust_price=15.0,
        #     #     adjust_sum_price=30,
        #     #     adjust_sum_price_tax=30.6,
        #     #     is_modify=1,
        #     # ),
        #     #     dict(
        #     #         id=4306103481498861568,
        #     #         quantity=3,
        #     #         price_tax=14.0,
        #     #         adjust_price_tax=15.6,
        #     #         price=10.0,
        #     #         adjust_price=15.0,
        #     #         sum_price=30.0,
        #     #         adjust_sum_price=45.0,
        #     #         sum_price_tax=42.0,
        #     #         adjust_sum_price_tax=46.8,
        #     #         # is_modify=0,
        #     #     )],
        #     status="APPROVED"
        # )
        request = {
            "order_ids": [4512547669052096512],
            "products": [
                {
                    "id": 4512547669119205376,
                    "product_id": 4498076487177469953,
                    "product_code": "A0007",
                    "product_name": "草绿园柚子茶",
                    "unit_id": 4487958140750397441,
                    "unit_name": "瓶",
                    "unit_spec": "无",
                    "quantity": 1,
                    "actual_quantity": 1,
                    "price": 40,
                    "adjust_price": 42,
                    "sum_price": 40,
                    "adjust_sum_price": 42,
                    "price_tax": 40,
                    "adjust_price_tax": 42,
                    "sum_price_tax": 40,
                    "adjust_sum_price_tax": 42,
                    "is_modify": 1
                }
            ],
            "include_product": True,
            "status": "COMPLETED"
        }
        res = client.UpdatePurchaseReviewOrder(purchase_review_pb2.UpdatePurchaseReviewOrderRequest(**request),
                                               metadata=metadata)

    return res


def test_price_adjustment(api):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = price_adjustment_pb2_grpc.PriceAdjustmentStub(channel=conn)
    start_datetime = datetime.strptime("2021-09-30 00:00:00", "%Y-%m-%d %H:%M:%S")
    end_datetime = datetime.strptime("2021-10-30 23:59:59", "%Y-%m-%d %H:%M:%S")
    start_date = get_timestamp(start_datetime)
    end_date = get_timestamp(end_datetime)
    now_date = get_timestamp(datetime.utcnow())
    ret = dict()
    if api == "调价单列表":
        req = dict(
            start_date=start_date,
            end_date=end_date,
            limit=10,
            include_total=True
        )
        ret = client.ListPriceAdjustment(price_adjustment_pb2.ListPriceAdjustmentRequest(**req), metadata=metadata)
    if api == "调价单详情":
        req = dict(
            adjust_code="472110110032"
        )
        ret = client.GetPriceAdjustmentDetail(price_adjustment_pb2.GetPriceAdjustmentDetailRequest(**req),
                                              metadata=metadata)
    return ret


def test_invoice_blending(api):
    """测试发票勾兑
     :param api  —— 传入api名字"""
    res = {}
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = invoice_blending_pb2_grpc.InvoiceBlendingStub(channel=conn)
    start_datetime = datetime.strptime("2019-12-5 00:00:00", "%Y-%m-%d %H:%M:%S")
    end_datetime = datetime.strptime("2019-12-20 23:59:59", "%Y-%m-%d %H:%M:%S")
    start_date = get_timestamp(start_datetime)
    end_date = get_timestamp(end_datetime)
    now_date = get_timestamp(datetime.utcnow())
    if api == "创建发票勾兑单":
        request = dict(
            company_id=4303612453905760257,
            supplier_id=4303613942623633409,
            order_ids=[12, 11],
            order_sum_price=122.1,
            order_sum_tax=122.1,
            invoice_ids=[22, 22],
            invoice_sum_price=111.1,
            invoice_sum_tax=111.1,
            diff_price=11.0,
            diff_reason="bx"
        )
        res = client.CreateInvoiceBlendingOrder(
            invoice_blending_pb2.CreateInvoiceBlendingOrderRequest(**request), metadata=metadata)
    if api == "变更状态":
        request = dict(
            ids=[4305326661778276352],
            status="REJECTED"
        )
        res = client.ChangeInvoiceBlendingStatus(invoice_blending_pb2.ChangeInvoiceBlendingStatusRequest(**request),
                                                 metadata=metadata)
    if api == "更新勾兑单":
        request = dict(
            id=4305326661778276352,
            order_data=dict(
                company_id=4303612453905760257,
                supplier_id=4303613942623633409,
                order_ids=[1, 1],
                order_sum_price=122.1,
                order_sum_tax=122.1,
                invoice_ids=[2, 2],
                invoice_sum_price=111.1,
                invoice_sum_tax=111.1,
                diff_price=11.0,
                diff_reason="更新"
            )
        )
        res = client.UpdateInvoiceBlendingOrder(invoice_blending_pb2.UpdateInvoiceBlendingOrderRequest(**request),
                                                metadata=metadata)
    if api == "查询勾兑单":
        request = dict(
            code="",
            status="SUBMITTED",
            company_ids=[],
            supplier_ids=[],
            limit=10,
            offset=0,
            include_total=True,
            sort_type="desc",
            sort="diff_price",
        )
        res = client.ListInvoiceBlending(invoice_blending_pb2.ListInvoiceBlendingRequest(**request), metadata=metadata)

    if api == "勾兑单详情":
        request = dict(
            id=4305671049498853376
        )
        res = client.GetInvoiceBlendingDetail(invoice_blending_pb2.GetInvoiceBlendingDetailRequest(**request),
                                              metadata=metadata)
    return res


if __name__ == "__main__":
    # res = test_purchase_review("创建复核")
    # res = test_purchase_review("复核列表")
    # res = test_purchase_review("复核详情")
    # res = test_purchase_review("采购复核")
    res = test_purchase_review("复核明细")
    # res = test_price_adjustment("调价单列表")
    # res = test_price_adjustment("调价单详情")
    # res = test_invoice_blending("创建发票勾兑单")
    # res = test_invoice_blending("变更状态")
    # res = test_invoice_blending("更新勾兑单")
    # res = test_invoice_blending("查询勾兑单")
    # res = test_invoice_blending("勾兑单详情")
    print(MessageToDict(res))
    # res = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id, id=4303612453905760257,
    #                                         schema_name="company-info")
    # filters = {"relation.company_info__in": ["4303612449015201793"]}
    # res = metadata_service.get_store_list(filters=filters, return_fields="id", partner_id=partner_id, user_id=user_id)
    # res = metadata_service.get_distribution_center_list(filters=filters, return_fields="id,code,name",
    #                                               user_id=user_id)
    # print(res)
