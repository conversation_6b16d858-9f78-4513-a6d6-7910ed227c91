#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def handle_attachments(attachments: str):
    if attachments.startswith('[{'):
        return json.loads(attachments)
    else:
        attachment_list = []
        ats = eval(attachments)
        for at in ats:
            attachment_list.append({"type":"image", "url":at})
        return attachment_list

def check_quantity(quantity, max_quantity, min_quantity, inc_quantity, mode="down", round_num=3):
    if quantity == 0:
        return 0
    if inc_quantity == 0:
        return round(quantity, round_num)

    quantity = float(quantity)
    max_quantity = float(max_quantity)
    min_quantity = float(min_quantity)
    inc_quantity = float(inc_quantity)
    quantity = round(quantity, round_num)
    max_quantity = round(max_quantity, round_num)
    min_quantity = round(min_quantity, round_num)
    inc_quantity = round(inc_quantity, round_num)
    # 数量校验, 如果订货数量不满足递增订量, 最小订量, 最大订量的标记为true
    flag = False
    # 最大订量配置不对时,需要计算真正的最大订量
    if (max_quantity - min_quantity ) % inc_quantity:
        max_quantity = ((max_quantity-min_quantity) // inc_quantity) * inc_quantity + min_quantity
    # 不满足最小订量
    if quantity < min_quantity:
        flag = True
    if quantity > max_quantity:
        flag = True
    # 不满足递增订量
    if (quantity - min_quantity) % inc_quantity:
        flag = True
    return flag

if __name__ == "__main__":
    # handle_attachments('')
    res = check_quantity(10, 10, 1, 2)
    print("res=", res)