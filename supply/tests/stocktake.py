import grpc
import sys
import os
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))
from supply.proto.store import stocktake_pb2, stocktake_pb2_grpc
from supply.proto.store import transfer_pb2, transfer_pb2_grpc
from supply.proto.store import adjust_pb2, adjust_pb2_grpc
from supply.proto import material_pb2, material_pb2_grpc
from supply.proto.metadata import store_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from google.protobuf.json_format import MessageToDict, MessageToJson
from supply.client.inventory_service import inventory_service
from supply.client.metadata_service import metadata_service
from supply.utils.helper import get_guid

_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(15138)
partner_id = str(1170)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
    ("from_grpc", "true")
)


def test_client_stocktake(num):
    user_id = str(1)
    partner_id = str(100)
    metadata = (
        ("partner_id", partner_id),
        ("user_id", user_id),
    )
    res = ''
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)

    client = stocktake_pb2_grpc.StoreStockTakeStub(channel=conn)
    value = datetime.now() - timedelta(days=83)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    start = timestamp
    valu = datetime.now() - timedelta(days=0)
    print(valu)
    timestamp = Timestamp()
    timestamp.FromDatetime(valu)
    target_date = timestamp

    valu = datetime.now() - timedelta(days=5)
    print(valu)
    timestamp = Timestamp()
    timestamp.FromDatetime(valu)
    start_date = timestamp
    valu = datetime.now() + timedelta(days=1)
    print(valu)
    timestamp = Timestamp()
    timestamp.FromDatetime(valu)
    end_date = timestamp

    if num == 20:
        valu = datetime.now() - timedelta(days=10)
        # print(valu)
        timestamp = Timestamp()
        timestamp.FromDatetime(valu)
        start_date = timestamp
        valu_ = datetime.now()
        # print(valu)
        timestamp = Timestamp()
        timestamp.FromDatetime(valu_)
        end_date = timestamp
        request = {
            "store_ids": [4462925783018536960],
            # "target_date": target_date,
            "include_total": True,
            "status": [1, 2, 3, 4, 5, 6],
            # 'target_date': target_date,
            "start_date": start_date,
            "end_date": end_date,
            # "_type":[2],
            # "store_ids":[3850146051738566657,],
            # "branch_type": "WAREHOUSE"
        }
        res = client.GetStocktake(stocktake_pb2.GetStocktakeRequest(**request), metadata=metadata)
    # //GetStocktakeByDocID 获取一个盘点单19
    if num == 19:
        request = {'doc_id': 4412533934116069377}
        res = client.GetStocktakeByDocID(stocktake_pb2.GetStocktakeByDocIDRequest(**request), metadata=metadata)
    # //GetStocktakeProduct 查询盘点单明细商品21
    if num == 21:
        # 4194398491965751297
        # 4188604090479177729

        request = {'doc_id': 4469125261544984577,
                   "include_total": True,
                   }
        res = client.GetStocktakeProduct(stocktake_pb2.GetStocktakeProductRequest(**request), metadata=metadata)

    # //PutStocktakeByDocID 更新盘点单 25
    if num == 25:
        # user_id = str(4257133087260934145)
        # partner_id = str(4183192445833445399)
        metadata = (
            ("partner_id", partner_id),
            ("user_id", user_id),
        )
        request = {"doc_id":4407877753892462593,"products":[{"id":4407877754018291713,"quantity":0,"is_pda":True,
                                                               "tag_products":[{"tag_id":4407905212910391297,
                                                                                "tag_name":"咖啡区域","tag_quantity":5}]}]}

        res = client.PutStocktakeByDocID(stocktake_pb2.PutStocktakeByDocIDRequest(**request), metadata=metadata)
    # //CheckStocktakeByDocID 检查能否确认盘点单（status=）14
    if num == 14:
        request = {'doc_id': 4304685737364799489,
                   'check': True,}
                   # 'branch_type': "WAREHOUSE"}
        res = client.CheckStocktakeByDocID(stocktake_pb2.CheckStocktakeByDocIDRequest(**request), metadata=metadata)

    # //ConfirmStocktakeByDocID 确认盘点单（status=CONFITRMED）15
    if num == 15:
        request = {'doc_id': 4188604090479177729,
                   'check': False}
        res = client.ConfirmStocktakeByDocID(stocktake_pb2.ConfirmStocktakeByDocIDRequest(**request), metadata=metadata)

    # //ApproveStocktakeByDocID  财务确认盘点单（status=APPROVED最终状态，前置状态status）16
    if num == 16:
        request = {'doc_id': 4220178784501714945,
                   }
        res = client.ApproveStocktakeByDocID(stocktake_pb2.ApproveStocktakeByDocIDRequest(**request), metadata=metadata)
    # //RejectStocktakeProduct 财务驳回提交后的部分盘点单明细商品（status=REJECTED，部分商品status=REJECTED）17
    if num == 17:
        request = {'doc_id': 4220178784501714945,
                   # 'submit_name':'sss'
                   }
        res = client.SubmitStocktakeByDocID(stocktake_pb2.SubmitStocktakeByDocIDRequest(**request), metadata=metadata)
    if num == 18:
        request = {'doc_id': 4310804334055264257,
                   'branch_type': 'WAREHOUSE'
                   }
        res = client.CheckedStocktakeByDocID(stocktake_pb2.CheckedStocktakeByDocIDRequest(**request), metadata=metadata)
    if num == 30:
        req = {
            # "branch_id": 4217527475107790849,
            "tag_name": '开发测试批量更新'
        }
        res = client.GetStocktakeTags(stocktake_pb2.GetStocktakeTagsRequest(**req), metadata=metadata)
    if num == 31:
        request = {
            # 'tag_id':4194041551821533185,
            'action': 3,
            # 'tag_ids': [4369079606017261573, 4369079606017261572],
            'name': '热带水果',
            'origin_name': '茶饮',
            'add_dimension': 'store',
            'branch_ids': [4273394092244729857]
            # 'region_ids': [4273393458946768897]
        }
        res = client.ActionStocktakeTags(stocktake_pb2.ActionStocktakeTagsRequest(**request), metadata=metadata)
    if num == 32:
        request = {'id': [4195047809172729857, 4194461348819988482]}
        res = client.DeleteStocktakeProductTags(stocktake_pb2.DeleteStocktakeProductTagsRequest(**request),
                                                metadata=metadata)

    # 门店盘点损益
    if num == 33:
        request = {
            # "target_date":target_date,
            "start_date": start_date,
            "end_date": end_date,
            # "schedule_code":'213124',
            # "stocktake_type":"D",
            # "include_total":True,
            # "store_ids":[4176265164418449409,4176266229180268545],
            "branch_type": "STORE"
        }
        res = client.GetStocktakeBalance(stocktake_pb2.GetStocktakeBalanceRequest(**request), metadata=metadata)
    # //GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29
    if num == 34:
        request = {
            "doc_id": 4188604090479177729,
        }
        res = client.GetStocktakeBalanceProductGroup(stocktake_pb2.GetStocktakeBalanceProductGroupRequest(**request),
                                                     metadata=metadata)
    # //StocktakeBiDetailed盘点单报表
    if num == 35:
        valu = datetime.now() - timedelta(days=10)
        print(valu)
        timestamp = Timestamp()
        timestamp.FromDatetime(valu)
        start_date = timestamp
        valu2 = datetime.now() - timedelta(days=0)
        print(valu2)
        timestamp = Timestamp()
        timestamp.FromDatetime(valu2)
        end_date = timestamp
        request = {
            "start_date": start_date,
            "end_date": end_date,
            "include_total": True,
            "limit": 10,
            "offset": 0,
            'sort': 'quantity',
            "order": 'sada',
            # "product_name":"够",
            # "store_ids":[4217527473354571777,],
            # "category_ids":[]
        }
        res = client.StocktakeBiDetailed(stocktake_pb2.StocktakeBiDetailedRequest(**request), metadata=metadata)
    if num == 36:
        # include_total = 1;
        # limit = 2;
        # offset = 3;
        # start = 4;
        # end = 5;
        # region_id = 6;
        # product_ids = 7;
        # type = 8;
        request = {
            "start": start_date,
            "end": end_date,
            "include_total": True,
            "branch_type": "STORE"
            # "limit":31,
            #  "offset":0,
            #    "type":"R",
            # "product_name":"轻芝士桂花乌龙",
            # "product_ids":[4176582377935470593,],
            # "category_ids":[]
            # "region_id":4175923876548575233,
        }
        res = client.StocktakeBalanceRegion(stocktake_pb2.StocktakeBalanceRegionRequest(**request), metadata=metadata)
    if num == 37:
        import json
        user_id = str(4186056888460247152)
        partner_id = str(4183192445833445399)
        metadata = (
            ("partner_id", partner_id),
            ("user_id", user_id),
        )
        a = {"create_tabe": "month"}
        req = {
            "search": '日',
            "search_fields": "name,code",
            "limit": 10,
            "offset": 10,
            "relation_filters": json.dumps(a)
        }
        res = client.GetStoreScope(stocktake_pb2.StoreDataScopeRequest(**req), metadata=metadata)
    if num == 90:
        res = client.Ping
        print(res)
    if num == 91:
        request = {
            "doc_id": 4419593143576293377
            # "limit":31,
            #  "offset":0,
            #    "type":"R",
            # "product_name":"轻芝士桂花乌龙",
            # "product_ids":[4176582377935470593,],
            # "category_ids":[]
            # "region_id":4175923876548575233,
        }
        res = client.AdvanceStocktakeDiff(stocktake_pb2.AdvanceStocktakeDiffRequest(**request), metadata=metadata)
    if num == 92:
        request = {
            # "code":'11830000',
            # "stocktake_type":'M',

            # "target_date":start_date

            "limit": 10,
            "offset": 10,
            "order": 's',
            "sort": "type"
            #    "type":"R",
            # "schedule_code": "111904250003",
            # "status": "CONFIRMED",
            # "product_name":"轻芝士桂花乌龙",
            # "store_ids":[4217527473195188225,],
            # "category_ids":[]
            # "region_id":4175923876548575233,
        }
        req = stocktake_pb2.StocktakeDiffReportRequest(**request)
        req.start_date.seconds = 1561132800
        req.end_date.seconds = 1561392000
        res = client.StocktakeDiffReport(req, metadata=metadata)
    if num == 93:
        request = dict(
            doc_ids=[4218418976915730433, 4218419001636958209],
            calculate_inventory=True,
            schedule_name='重盘计划',
            remark='重盘计划',
            schedule_code='1212412',
            schedule_id=312312312313,
        )
        req = stocktake_pb2.RecreateStocktakeDocRequest(**request)
        res = client.RecreateStocktakeDoc(req, metadata=metadata)
    if num == 94:
        req = dict(
            period_group_by=2,
            # limit=10,
            stocktake_type=['D'],
            sort='date'
        )
        req = stocktake_pb2.StocktakeDocStatisticsRequest(**req)
        req.start_date.seconds = 1563359050
        req.end_date.seconds = 1568715850
        res = client.StocktakeDocStatistics(req, metadata=metadata)
    if num == 98:
        req = dict(
            store_id=4279909169156325377
        )
        req = stocktake_pb2.GetUncompleteDocRequest(**req)
        # req.start_date.seconds = 1563359050
        req.end_date.seconds = 1576684800
        res = client.GetUncompleteDoc(req, metadata=metadata)

    if num == 100:
        req = stocktake_pb2.StocktakeProductImportRequest()
        req.file_name = '盘点商品导入开发测试.xlsx'
        req.doc_id = 4495847517182787584
        import base64
        with open('/Users/<USER>/Desktop/盘点单明细20210604160947.xlsx', 'rb') as f:
            file = f.read()
            file = base64.b64encode(file)
            file = str(file, 'utf-8')
            # print('file', file)
            req.file_data = file
        res = client.StocktakeProductImport(req, metadata=metadata)

    if num == 101:
        req = stocktake_pb2.UpdateStocktakeImportBatchRequest()
        req.batch_id = 4498459292538503168
        req.status = "CONFIRM"
        res = client.UpdateStocktakeImportBatch(req, metadata=metadata)

    if num == 125:
        user_id = str(1)
        partner_id = str(100)
        metadata = (
            ("partner_id", partner_id),
            ("user_id", user_id),
        )
        new_doc_id = client.GetNewStocktakeId(stocktake_pb2.GetNewStocktakeIdRequest()).new_doc_id
        target_date = Timestamp()
        target_date.FromDatetime(datetime.utcnow())
        request = {"target_date":target_date,"branch_id":4462866233712181249,"new_doc_id":new_doc_id,"calculate_inventory":True,"product_list":[{"product_id":4467665361310842881,"position_id":1},{"product_id":4467665361310842881,"position_id":2}]}

        res = client.ManuallyCreateStocktake(stocktake_pb2.ManuallyCreateStocktakeRequest(**request), metadata=metadata)

    if num == 126:
        user_id = str(1)
        partner_id = str(100)
        metadata = (
            ("partner_id", partner_id),
            ("user_id", user_id),
        )
        request = {'store_id': 4483201431196696576,
                   'include_total': True,
                   # 'category_ids':[4203403273681502209,],
                   # 'offset':0,
                   # 'limit':10
                   }
        res = client.GetStocktakeProductByStoreID(stocktake_pb2.GetStocktakeProductByStoreIDRequest(**request), metadata=metadata)

    return res


def test_client_transfer(num):
    res = ''
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = transfer_pb2_grpc.StoreTransferStub(channel=conn)
    # start = datetime.now() - timedelta(days=5)
    # end = start + timedelta(days=10)
    start = datetime.strptime("2022-02-01 08:00:00", "%Y-%m-%d %H:%M:%S")
    end = datetime.strptime("2022-02-20 07:59:59", "%Y-%m-%d %H:%M:%S")
    s_timestamp = Timestamp()
    s_timestamp.FromDatetime(start)
    start = s_timestamp
    e_timestamp = Timestamp()
    e_timestamp.FromDatetime(end)
    end = e_timestamp
    # 查询调拨单1
    if num == 1:
        request = {
            'include_total': True,
            # 'status': [1, 2],
            # 'shipping_stores':[4373457990955565057],
            # 'receiving_stores':[4176251636538146817],
            # 'order': 'des',
            # 'offset': 0,
            # 'limit': 10,
            "types": ["AUTO", "MANUAL"],
            "sub_type": "EXTERNAL",
            "branch_type":"STORE",
            'start_date': start,
            'end_date': end
        }
        res = client.GetTransfer(transfer_pb2.GetTransferRequest(**request), metadata=metadata)
    # 查询一个调拨单2
    if num == 2:
        request = {'transfer_id': 4590862053761187840}
        res = client.GetTransferByID(transfer_pb2.GetTransferByIDRequest(**request), metadata=metadata)
    # 取得门店可调拨商品3
    if num == 3:
        request = {'store_id': 4462970109975101440,
                   'include_total': True
                   }
        res = client.GetTransferProductByBranchID(transfer_pb2.GetTransferProductByBranchIDRequest(**request),
                                                  metadata=metadata)
    # 查询相同属性区域门店4
    if num == 4:
        request = {'store_id': 4462866233712181249}
        res = client.GetTransferRegionByBranchID(transfer_pb2.GetTransferRegionByBranchIDRequest(**request),
                                                 metadata=metadata)
    # 获取一个调拨单商品5
    if num == 5:
        request = {'transfer_id': 4194105194420162561, }
        res = client.GetTransferProductByTransferID(transfer_pb2.GetTransferProductByTransferIDRequest(**request),
                                                    metadata=metadata)
    # 创建调拨单6
    if num == 6:
        # 4176234574621179905, 4176251636538146817
        # 4180663240348401666, 4180175752915845122, 4176622908455518210
        request = {"request_id": 3256053666531186617, "shipping_store": 4373457990955565057,
                   "receiving_store": 4374827222448472065,
                   "sub_type": "EXTERNAL",
                   "products": [
                       {"product_id": 4374806621570727937, "unit_id": 4373346031270625281, "quantity": 10.00}, ],
                   "remark": "开发测试外部调拨", "shipping_date": start, "transfer_date": start}
        res = client.CreateTransfer(transfer_pb2.CreateTransferRequest(**request), metadata=metadata)
    # 修改调拨单7
    if num == 7:
        request = {'transfer_id': 4187507402695839745,
                   "remark": "不要了2",
                   'transfer_date': end,
                   # 'products': [
                   #     {'product_id': 4175525475252174849, 'unit_id': 4168339425442398209, 'quantity': 2256.000000},
                   #    ]}
                   }
        res = client.UpdateTransfer(transfer_pb2.UpdateTransferRequest(**request), metadata=metadata)
    # 确认调拨单8
    if num == 8:
        request = {'transfer_id': 4236101707128905729

                   }
        res = client.ConfirmTransfer(transfer_pb2.ConfirmTransferRequest(**request), metadata=metadata)
    # 删除调拨单商品10
    if num == 10:
        request = {'transfer_product_id': 4031331857421012994}
        res = client.DeleteTransferProduct(transfer_pb2.DeleteTransferProductRequest(**request))
    # 删除调拨单11
    if num == 11:
        request = {'transfer_id': 4031638116133359617}
        res = client.DeleteTransfer(transfer_pb2.DeleteTransferRequest(**request))

    # 提交调拨单12
    if num == 12:
        request = {'transfer_id': 4194105194420162561, 'receiving_store': 4176251636538146817,
                   'products': [
                       {'product_id': 4176565709339361281, 'unit_id': 4168339425442398209, 'quantity': 345.000000},
                   ]
                   }
        res = client.SubmitTransfer(transfer_pb2.SubmitTransferRequest(**request), metadata=metadata)
    # 调拨单报表
    if num == 13:
        print(start, "--", end)
        request = {
            # 'st_ids' :[4176265632020430849],
            'store_ids': [],
            'category_ids': [],
            # 'product_name' :'达芬奇果美经典焦糖风味调味酱',
            'start_date': start,
            'end_date': end,
            'limit': 10,
            # 'offset' : 0,
            'include_total': True,
            'is_in': False,
            # 'order': 'sadas',
            # 'sort': 'quantity',
            'branch_type': "STORE",
            'sub_type': "EXTERNAL",
            "type": ""
        }
        res = client.GetTransferCollect(transfer_pb2.GetTransferCollectRequest(**request), metadata=metadata)
    if num == 14:
        request = {
            # 'st_ids' :[4176265632020430849],
            #       'store_ids':[],
            'category_ids': [],
            # 'product_name' :'达芬奇果美经典焦糖风味调味酱',
            'start_date': start,
            'end_date': end,
            'limit': 10,
            # 'offset' : 0,
            'include_total': True,
            'is_in': False,
            'order': 'sadas',
            'sort': 'quantity',
            'cross_company': "1",
            'branch_type': "STORE"
        }
        res = client.GetTransferCollectDetailed(transfer_pb2.GetTransferCollectRequest(**request), metadata=metadata)
    if num == 15:
        request = {'transfer_id': 4461849052094922752}
        res = client.CancelTransfer(transfer_pb2.CancelTransferRequest(**request), metadata=metadata)
    return res


def test_client_adjust(num):
    res = ''
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = adjust_pb2_grpc.StoreAdjustStub(channel=conn)
    start = datetime.now() - timedelta(days=13)
    end = start + timedelta(days=4)
    s_timestamp = Timestamp()
    s_timestamp.FromDatetime(start)
    start = s_timestamp
    e_timestamp = Timestamp()
    e_timestamp.FromDatetime(end)
    end = e_timestamp

    valu = datetime.now() - timedelta(days=10)
    print(valu)
    timestamp = Timestamp()
    timestamp.FromDatetime(valu)
    start_date = timestamp
    date_now = datetime.utcnow()
    timestamp = Timestamp()
    timestamp.FromDatetime(date_now)
    end_date = timestamp
    # GetAdjust查询每日损耗表9
    if num == 9:
        request = {'include_total': True, 'start_date': start,
                   'end_date': end,
                   'status': [1, 2, 3, 4, 5],
                   'reason_type': '',
                   'order': 'asc',
                   'offset': 0,
                   'limit': 50,
                   'branch_type': "WAREHOUSE"}
        res = client.GetAdjust(adjust_pb2.GetAdjustRequest(**request), metadata=metadata)
    # 每日损耗表商品查询10
    if num == 10:
        request = {'adjust_id': 4474418195051933697}
        res = client.GetAdjustProduct(adjust_pb2.GetAdjustProductRequest(**request), metadata=metadata)
    # //GetAdjustByID查询一个每日损耗表11
    if num == 11:
        request = {'adjust_id': 4472385551932129280, 'branch_type': 'STORE'}
        res = client.GetAdjustByID(adjust_pb2.GetAdjustByIDRequest(**request), metadata=metadata)
    # // ConfirmAdjust确认一个每日损耗表12
    if num == 12:
        import json
        a = json.dumps([])
        print(a)
        d = datetime.now()
        print(d.strftime("%Y-%m-%d"))
        request = {'adjust_id': 4365733119422300161,
                   'branch_type': "STORE"}

        # is_bom_products_list=[4186341114644529153, 4206298044263563265]
        # list_adjust_product=[{'product_id':4186341114644529153,'accounting_quantity':2},
        #                      {'product_id':4206298044263563265,'accounting_quantity':3},
        #                      {'product_id':4186345253294309377,'accounting_quantity':6}]
        # if len(is_bom_products_list)>0:
        #     for product in list_adjust_product:
        #         accounting_quantity=product['accounting_quantity'] if product.get('accounting_quantity') else 0
        #         if int(product.get('product_id')) in is_bom_products_list and accounting_quantity!=None and accounting_quantity!=0:
        #             print('asdasdasdasdasdasdasdasd')

        res = client.ConfirmAdjust(adjust_pb2.ConfirmAdjustRequest(**request), metadata=metadata)
    # // GetAdjustProductByStoreID查询门店可损耗商品13
    if num == 13:
        # user_id = str(4186056888460247152)
        # partner_id = str(4183192445833445399)
        # metadata = (
        #     ("partner_id", partner_id),
        #     ("user_id", user_id),
        # )
        request = {'store_id': 4728495840539246592,
                   'include_total': True,
                   # 'category_ids':[4203403273681502209,],
                   # 'offset':0,
                   # 'limit':10
                   }
        res = client.GetAdjustProductByStoreID(adjust_pb2.GetAdjustProductByStoreIDRequest(**request),
                                               metadata=metadata)
    # //CreatedAdjust手动创建一个每日损耗表14
    if num == 14:
        request = {"request_id": ****************3,
                   "adjust_date": end_date,
                   "position_id": None,
                   "products": [{"product_id": 4467282667024416769,
                                 "unit_id": 4462867279771598849, "quantity": 1, "reason_type": "BF001"},
                                {"product_id": 4462867089551523841, "unit_id": 4464067756345524225, "quantity": 10,
                                 "reason_type": "BF001"}],
                   "branch_type": "STORE",
                   "adjust_store": 4462971415133454336,
                   "reason_type": "BF001",
                   "remark": "开发测试2",
                   "source": "MANUAL_CREATED"}
        res = client.CreatedAdjust(adjust_pb2.CreatedAdjustRequest(**request), metadata=metadata)
    # //UpdateAdjust更新一个每日损耗表15
    if num == 15:
        request = {'adjust_id': 4468043878662930432,
                   "remark": "开发测试2",
                   "reason_type": 'BF002',
                   "adjust_date": end_date,
                   "products": [{"id": 4468043878738427904, "product_id": 4467282667024416769,
                                 "unit_id": 4462867279771598849, "quantity": 2, "reason_type": "BF002"},
                                {"id": 4468043878776176640, "product_id": 4462867089551523841,
                                 "unit_id": 4464067756345524225, "quantity": 20, "reason_type": "BF002"}],
                   }
        res = client.UpdateAdjust(adjust_pb2.UpdateAdjustRequest(**request), metadata=metadata)
    # //GetAdjustBiCollect每日损耗表汇总报表18
    if num == 16:
        request = {
            "start_date": start_date,
            "end_date": end_date,
            "include_total": True,
            # "period_symbol": 'BY_DAY',
            "order": 'e',
            "sort": 'quantity',
            "limit": 30,
            "branch_type": "STORE",
            "hour_offset": 8
            # "offset":0,
            #  "bom_product_id": [4217605219720101889]
            # "product_name":"布蕾奥利奥奶茶",
            # "store_ids":[4176265461752659969,],
            # "category_ids":[4217533889163296769]
        }
        res = client.GetAdjustBiCollect(adjust_pb2.GetAdjustBiCollectRequest(**request), metadata=metadata)
    if num == 17:
        request = {
            "adjust_ids": [4198298939545772033],
        }
        res = client.CancelAdjust(adjust_pb2.CancelAdjustRequest(**request), metadata=metadata)
    if num == 18:
        request = {
            "start_date": start_date,
            "end_date": end_date,
            "include_total": True,
            "limit": 30,
            "offset": 0,
            "branch_type": "STORE",
            # "bom_product_id": [4217605219720101889]
            # "product_name":"布蕾奥利奥奶茶",
            # "store_ids": [4217527472486350849, ],
            # "order": 'w',
            # "sort": 'jde_code',
            # "category_ids":[4175946169354027009]
        }
        print('收拾收拾')
        res = client.GetAdjustCollectDetailed(adjust_pb2.GetAdjustCollectDetailedRequest(**request), metadata=metadata)
    if num == 19:
        request = {
            'adjust_date': '2019-02-01'
        }
        res = client.AutoCloseCreatedAdjust(adjust_pb2.AutoCloseCreatedAdjustRequest(**request), metadata=metadata)
    if num == 20:
        request = {
            'adjust_id': 4523833855579029504
        }
        res = client.SubmitAdjust(adjust_pb2.SubmitAdjustRequest(**request), metadata=metadata)
    if num == 21:
        request = {
            'adjust_id': 4523833855579029504
        }
        res = client.RejectAdjust(adjust_pb2.RejectAdjustRequest(**request), metadata=metadata)
    if num == 22:
        request = {
            'adjust_id': 4523833855579029504
        }
        res = client.ApproveAdjust(adjust_pb2.ApproveAdjustRequest(**request), metadata=metadata)
    if num == 23:
        request = {

            "adjust_date": "2022-03-01 11:54:03",
            "reason_type": "BF001",
            "adjust_store": "2021052803",
            "request_id": "174842201060654332755002003",
            "products": [
                {
                    "skuRemark": [
                        {
                            "name": {
                                "code": "2022022801",
                                "name": "杯型"
                            },
                            "values": {
                                "code": "81",
                                "name": "大杯"
                            }
                        },
                        {
                            "name": {
                                "code": "2022022802",
                                "name": "冷热"
                            },
                            "values": {
                                "code": "81",
                                "name": "大杯"
                            }
                        },
                        {
                            "name": {
                                "code": "2022022802",
                                "name": "冷热"
                            },
                            "values": {
                                "code": "91",
                                "name": "冷"
                            }
                        }
                    ],
                    "product_code": "8001",
                    "unit_code": "84",
                    "quantity": 3,
                    "reason_type": "BF001"
                },
                {
                    "skuRemark": [
                        {
                            "name": {
                                "code": "2022022801",
                                "name": "杯型"
                            },
                            "values": {
                                "code": "81",
                                "name": "大杯"
                            }
                        },
                        {
                            "name": {
                                "code": "2022022802",
                                "name": "冷热"
                            },
                            "values": {
                                "code": "92",
                                "name": "冷"
                            }
                        }
                    ],
                    "product_code": "9001",
                    "unit_code": "84",
                    "quantity": 2,
                    "reason_type": "BF001"
                }
            ]
        }
        res = client.CreatedAdjustByCode(adjust_pb2.CreatedAdjustByCodeRequest(**request), metadata=metadata)
    return res


def test_client_metadata(num):
    if num == 2:
        # adjust_date=datetime.now()-timedelta(days=1)
        # print(adjust_date)
        # _s_adjust_date=adjust_date.strftime('%Y-%m-%d')
        # _e_adjust_date=(adjust_date-timedelta(days=1)).strftime("%Y-%m-%d")
        # if isinstance(_e_adjust_date,str):
        #     print('asdasda')
        # print( _s_adjust_date,_e_adjust_date)

        # filters = {'allow_stocktake': True
        #            }
        # ret = metadata_service.get_product_list(
        #     ids=[4192679918875705345],
        #     include_units=True,
        #     include_total=True,
        #     limit=10,
        #     partner_id=4183192445833445399,
        #     user_id=4189649538002190337
        # )
        product_filters = {"bom_type__in": ["MANUFACTURE"], "product_type__nin": ["MATERIAL_CONSUME", "LOW_CONSUME"]}
        product_fields = ["product_type", "bom_type"]
        # filters={'inventory_type': 'COUNT'}
        filters = {"allow_transfer": True}
        not_allow_ajdust = []
        allow_adjust_products = []
        adjust_products = [{'product_id': 4217605415954808833}, {'product_id': 4217605416504262657}]
        store_products = metadata_service.get_attribute_products_by_store_id(store_id=4217527487233523713,
                                                                             # return_fields='allow_adjust',
                                                                             product_ids=[4217605415954808833,
                                                                                          4217605416504262657],
                                                                             partner_id=partner_id,
                                                                             user_id=user_id).get('rows', [])

        for store_product in store_products:
            print('ss', store_product)
            if not store_product.get('allow_adjust'):
                not_allow_ajdust.append(int(store_product['product_id']))
        for adjust_product in adjust_products:
            if adjust_product['product_id'] not in not_allow_ajdust:
                allow_adjust_products.append(adjust_product)
        ret = allow_adjust_products
    if num ==3:
        partner_id = 4206763836826861569
        user_id = 4206763836826861571
        store_relation_filters = {}
        product_relation_filters = {}
        product_filters = {"allow_order__eq": True}
        product_filters = {"status__eq": "ENABLED",
                           "__or": [{"bom_type__neq": "MANUFACTURE",
                                     "sale_type__in": ["NEW-RETAIL-SHORT-TERM", "NEW-RETAIL-NORMAL",
                                                       "SOUVENIR"]},
                                    {"product_type__neq": "FINISHED"}]}
        product_filters ={}
        dd= datetime.now()-timedelta(days=5)
        d = datetime(dd.year,dd.month,dd.day)
        print('订单',d)
        ret = metadata_service.get_list_valid_product_for_distr_by_id(4219460796763078657,
                                                                      product_ids=[4241879292578889729],
                                                                      include_product_fields='name,code,model_name,model_name,model_code,storage_type,category,product_type,sale_type',
                                                                      order_date=d,
                                                                      filters={},
                                                                      product_relation_filters=product_relation_filters,
                                                                      product_filters=product_filters,
                                                                      partner_id=partner_id, user_id=user_id).get('rows',[])
        print('',len(ret))
        ret = ret
    if num == 4:
        tea_and_bread_ids=[]
        filters = {"sale_type__in": ['BREAD', 'TEA'], "product_type__eq": "FINISHED"}
        rows = metadata_service.get_product_list(
            filters=filters,
            user_id=user_id,
            partner_id=partner_id
        ).get('rows', [])
        print('rows',rows)
        for r in rows:
            if r.get("sale_type") in (['BREAD', 'TEA']) and r.get("product_type") == "FINISHED":
                tea_and_bread_ids.append(int(r.get('id', 0)))
        print('tea_and_bread_ids',tea_and_bread_ids)
        ret={}
    if num ==6:
        partner_id = 2
        user_id = 4201196624908648450
        ret = metadata_service.get_store_list(
            ids=[4217527472876421121],
                                              return_fields='virtual_cold_warehose,id,code,name,contact,tel',
                                              partner_id=partner_id,user_id=user_id)
        print('ret',ret)
    return ret

def test_materirl(num):
    ret = {}
    if num == 1:
        res = ''
        conn = grpc.insecure_channel(_HOST + ':' + _PORT,
                                     options=[
                                         ('grpc.max_send_message_length', 1024 * 1024 * 1024),
                                         ('grpc.max_receive_message_length', 1024 * 1024 * 1024),
                                     ],
                                     )
        client = material_pb2_grpc.materialStub(channel=conn)
        start = datetime.now() - timedelta(days=1)
        end = start + timedelta(days=180)
        s_timestamp = Timestamp()
        s_timestamp.FromDatetime(start)
        start = s_timestamp
        e_timestamp = Timestamp()
        e_timestamp.FromDatetime(end)
        end = e_timestamp
        if num == 1:
            request = {
                'store_id': 4217527475107790849,
                'sales_date': start,
                'offset': 0,
                'limit': -1,
            }
            res = client.GetMaterialDifference(material_pb2.GetMaterialDifferenceRequest(**request), metadata=metadata)
        # print(res)
    return res


def run():
    res = ''
    test_c = 'a'
    if test_c == 'm':
        res = test_client_metadata(3)
    # 盘点客户端
    elif test_c == 's':
        #res = test_client_stocktake(21)
        #res = MessageToJson(res, preserving_proto_field_name=True)
        res = test_client_stocktake(126)
        res = MessageToJson(res, preserving_proto_field_name=True)
    # 调拨客户端
    elif test_c == 't':

        # res = metadata_service.get_purchase_product_by_store_id(store_id=4217527472486350849,
        #                                                          filters={'circle_type': 'W'},
        #                                                          partner_id=partner_id,
        #                                                          user_id=user_id,
        #                                                          )
        res = test_client_transfer(2)
        # res =  MessageToJson(res, preserving_proto_field_name=True)
    # 每日损耗表客户端
    elif test_c == 'a':
        # res=metadata_service.get_username_by_pid_uid(partner_id=2,user_id=4201196624908648450)
        res = test_client_adjust(13)
        res = MessageToJson(res, preserving_proto_field_name=True)
    #elif test_c == 'n':
    #    res = test_nsq()
    #elif test_c == 'i':
    #    res = test_inventory(2)
    elif test_c == 'me':
        res = test_materirl(1)
    response = res
    print(res)
    # print(len(res['rows']))


if __name__ == '__main__':
    # print(float(100)/float(0.1))
    run()

