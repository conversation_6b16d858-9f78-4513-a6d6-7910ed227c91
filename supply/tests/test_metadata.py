#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import grpc
import unittest
from load_path import run
run()

from supply.module.common import get_store_by_company, get_field_entity_tree_from_entitys
from supply.client.metadata_service import metadata_service
channel = grpc.insecure_channel("localhost:8023")
metadata = (
    ("partner_id", "100"),
    ("user_id", "197"),
)

partner_id = 100
user_id = 192


class TestCase(unittest.TestCase):
    def setUp(self):
        self.client = metadata_service

    @unittest.skip
    def test_get_company(self):
        company_info = ['4462859981472923649']
        stores = get_store_by_company(company_info, partner_id, user_id)
        print(stores)

    @unittest.skip
    def test_get_category(self):
        category_ret = self.client.get_product_category_list(
            return_fields='id,code,name', partner_id=partner_id,
            user_id=user_id)
        categorys = category_ret.get('rows')
        categorys_tree = get_field_entity_tree_from_entitys(categorys, 'id')
        print(categorys)
        print(categorys_tree)

    @unittest.skip
    def test_get_store(self):
        store_id = 4462866233712181249
        partner_id = 100
        partner_id = 1
        res = self.client.get_store(store_id, partner_id=partner_id, user_id=partner_id)
        print(res)

    def test_get_products_list(self):
        product_id = 4498076490264477697
        res = self.client.get_product_list(ids=[product_id], return_fields='name,code,bom_type,category',
                                           partner_id=partner_id, user_id=user_id).get('rows')
        print(type(res))
        print(res)


if __name__ == "__main__":
    unittest.main()
