import grpc
import sys
import os
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.manufactory import processing_cost_pb2, processing_cost_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.utils.helper import get_today_datetime, get_product_unit_map

_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(4371272018445729794)
partner_id = str(4183192445833445399)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
)


def get_timestamp(value: datetime):
    # value = value - timedelta(hours=8)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


start_datetime = datetime.strptime("2020-09-15 00:00:00", "%Y-%m-%d %H:%M:%S")
end_datetime = datetime.strptime("2020-09-20 23:59:59", "%Y-%m-%d %H:%M:%S")
datetime_now = datetime.utcnow()
start_date = get_timestamp(start_datetime)
end_date = get_timestamp(end_datetime)
dt_now = get_timestamp(datetime_now)


def test_processing_cost(api):
    """测试包装单功能模块
    :param api  —— 传入api名字
    """
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = processing_cost_pb2_grpc.ProcessingCostStub(channel=conn)
    print(start_date)
    print(end_date)
    if api == "CreateProcessingCost":
        request = dict(
            month="2020-09",
            machining_center_id=10000000001,
            machining_center_name="seesaw加工中心",
            machining_center_code="2000",
            position_id=4394449921815388122,
            position_name="生豆仓",
            position_code="1010",
            unit_cost=20.1,
            quantity=100,
            unit_id=4373346335126978561,
            unit_name="斤",
            unit_spec="500g",
            type="",
            remark="开发测试",
            request_id=4404587767184490496
        )

        res = client.CreateProcessingCost(processing_cost_pb2.CreateProcessingCostRequest(**request), metadata=metadata)
        print(res)

    if api == "ListProcessingCost":
        request = dict(
            start_month="2020-09",
            end_month="2020-10",
            code="",
            machining_centers=[4400623470577188865],
            position_ids=[],
            status="ALL",
            limit=10,
            offset=0,
            include_total=True,
            order=None,
            sort=None
        )
        res = client.ListProcessingCost(processing_cost_pb2.ListProcessingCostRequest(**request),
                                        metadata=metadata)
        print(res)

    if api == "GetProcessingCostDetail":
        request = dict(
            receipt_id=4404965058855043072
        )
        res = client.GetProcessingCostDetail(processing_cost_pb2.GetProcessingCostDetailRequest(**request),
                                             metadata=metadata)
        print(res)

    if api == "UpdateProcessingCost":
        request = dict(
            receipt_id=4404965058855043072,
            update_detail=False,
            status="REJECTED",
            detail=dict(
                month="2020-09",
                machining_center_id=10000000001,
                machining_center_name="seesaw加工中心",
                machining_center_code="2000",
                position_id=4394449921815388122,
                position_name="生豆仓",
                position_code="1010",
                unit_cost=20.1,
                quantity=100,
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                type="",
                remark="开发测试",
                request_id=4404587767184490496
            )
        )
        res = client.UpdateProcessingCost(processing_cost_pb2.UpdateProcessingCostRequest(**request),
                                          metadata=metadata)
        print(res)


if __name__ == "__main__":
    # test_processing_cost("CreateProcessingCost")
    test_processing_cost("ListProcessingCost")
    # test_processing_cost("GetProcessingCostDetail")
    # test_processing_cost("UpdateProcessingCost")
