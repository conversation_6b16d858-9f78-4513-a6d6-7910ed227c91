import grpc
import sys
import os
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.store import self_picking_pb2, self_picking_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.utils.helper import get_today_datetime, get_product_unit_map
_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(4371272018445729794)
partner_id = str(100)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
)

def get_timestamp(value: datetime):
    # value = value - timedelta(hours=8)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


start_datetime = datetime.strptime("2020-10-07 00:00:00", "%Y-%m-%d %H:%M:%S")
end_datetime = datetime.strptime("2020-09-18 23:59:59", "%Y-%m-%d %H:%M:%S")
datetime_now = datetime.utcnow()
start_date = get_timestamp(start_datetime)
end_date = get_timestamp(end_datetime)
dt_now = get_timestamp(datetime_now)


def test_self_picking(api):
    """测试门店自采单功能模块
    :param api  —— 传入api名字
    """
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = self_picking_pb2_grpc.StoreSelfPickingStub(channel=conn)
    print(start_date)
    print(end_date)
    if api == "CreateStoreSelfPicking":
        request = dict(
            status="INITED",
            order_date=dt_now,
            branch_id=4406790557834149889,
            branch_name="CD001",
            branch_code="测试门店",
            reason="门店自采",
            remark="开发测试",
            branch_type="STORE",
            request_id=4394449921815338422,
            attachments=["http://docs.hexcloud.org/hws-prod/#/%E5%B0%8F%E7%A8%8B%E5%BA%8F"],
            items=[dict(
                product_id=4374806626750693377,
                product_code="1000",
                product_name="1000",
                product_spec="product_spec",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=100,
                price=2.34,
            ), dict(
                product_id=4374806626499035137,
                product_code="1001",
                product_name="1001",
                product_spec="product_spec",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=100,
                price=2.66,
            )
            ]
        )
        res = client.CreateStoreSelfPicking(self_picking_pb2.CreateStoreSelfPickingRequest(**request),
                                            metadata=metadata)
        print(res)

    if api == "ListStoreSelfPicking":
        request = dict(
            start_date=start_date,
            end_date=dt_now,
            code="",
            branch_ids=[],
            # status=["INITED", "REJECTED"],
            branch_type="STORE",
            limit=-1,
            offset=0,
            include_total=True,
            order=None,
            sort=None
        )
        res = client.ListStoreSelfPicking(self_picking_pb2.ListStoreSelfPickingRequest(**request), metadata=metadata)
        print(res)

    if api == "GetStoreSelfPickingDetail":
        request = dict(
            receipt_id=4467212617345204224
        )
        res = client.GetStoreSelfPickingDetail(self_picking_pb2.GetStoreSelfPickingDetailRequest(**request),
                                               metadata=metadata)
        print(res)

    if api == "UpdateStoreSelfPicking":
        request = dict(
            receipt_id=4462552903064223744,
            update_detail=True,
            status="INITED",
            order_date=dt_now,
            branch_id=4406790557834149889,
            branch_name="CD002",
            branch_code="测试门店1",
            reason="门店自采1",
            remark="开发测试1",
            branch_type="STORE",
            items=[dict(
                id=4462552903190052864,
                product_id=4374806626750693377,
                product_code="1000",
                product_name="1000",
                product_spec="product_spec",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=1000,
                price=2.34,
            ), dict(
                id=4462552903290716160,
                product_id=4374806626499035137,
                product_code="1001",
                product_name="1001",
                product_spec="product_spec",
                unit_id=4373346335126978561,
                unit_name="斤",
                unit_spec="500g",
                quantity=1000,
                price=2.66,
            )
            ]
        )
        res = client.UpdateStoreSelfPicking(self_picking_pb2.UpdateStoreSelfPickingRequest(**request),
                                            metadata=metadata)
        print(res)

    if api == "GetPickingProductByStoreId":
        request = dict(
            store_id=4462866233712181249,
            include_total=True,

        )
        res = client.GetPickingProductByStoreId(self_picking_pb2.GetPickingProductByStoreIdRequest(**request),
                                                metadata=metadata)
        print(res)


if __name__ == "__main__":
    # test_self_picking("CreateStoreSelfPicking")
    test_self_picking("ListStoreSelfPicking")
    # test_self_picking("GetStoreSelfPickingDetail")
    # test_self_picking("UpdateStoreSelfPicking")
    # test_self_picking("GetPickingProductByStoreId")



