import grpc
import sys
import os
from pathlib import Path

base_path = Path(__file__).parent.resolve().parent.resolve()
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))

from supply.proto.manufactory import purchase_pb2_grpc, purchase_pb2, \
    inventory_bi_pb2, inventory_bi_pb2_grpc, adjust_pb2, adjust_pb2_grpc
from supply.proto.warehouse import transfer_pb2, transfer_pb2_grpc
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.utils.helper import get_today_datetime, get_product_unit_map, get_product_map
from supply.client.inventory_service import metadata_service
from supply.client.cost_service import cost_service
_HOST = '127.0.0.1'
_PORT = '8686'
user_id = str(1)
partner_id = str(100)
metadata = (
    ("partner_id", partner_id),
    ("user_id", user_id),
)


def get_timestamp(value: datetime):
    # value = value - timedelta(hours=8)
    timestamp = Timestamp()
    timestamp.FromDatetime(value)
    return timestamp


start_datetime = datetime.strptime("2021-07-08 00:00:00", "%Y-%m-%d %H:%M:%S")
end_datetime = datetime.strptime("2021-07-10 23:59:59", "%Y-%m-%d %H:%M:%S")
start_date = get_timestamp(start_datetime)
end_date = get_timestamp(end_datetime)


def test_warehouse_client(api):
    """测试仓库采购功能模块
    :param api  —— 传入api名字
    """
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = purchase_pb2_grpc.ManufactoryPurchaseStub(channel=conn)
    start_datetime = datetime.strptime("2021-06-01 00:00:00", "%Y-%m-%d %H:%M:%S")
    end_datetime = datetime.strptime("2021-06-30 23:59:59", "%Y-%m-%d %H:%M:%S")
    start_date = get_timestamp(start_datetime)
    end_date = get_timestamp(end_datetime)
    print(start_date)
    print(end_date)
    if api == "创建采购":
        order_date = get_today_datetime()
        arrival_date = order_date + timedelta(days=1)
        request = dict(
            order_date=get_timestamp(order_date),
            arrival_date=get_timestamp(arrival_date),
            remark="本地测试",
            received_by=4303639323833532417,
            supplier_id=4303613954401239041,
            order_type="MH",
            purchase_type="DP",
            purchase_reason="没货了呗",
            product_items=[dict(
                product_id=4303603808572801025,
                quantity=3,
                distribution_type="PUR",
                price_tax=12,
                purchase_unit_id=4291789752425775105,
                accounting_unit_id=4291789752425775105,
                unit_rate=10,
                tax_rate=0.2,
                purchase_price=36,
                price=10,
            ),
                dict(
                    product_id=4303603868840755201,
                    quantity=10,
                    distribution_type="PUR",
                    price_tax=14,
                    purchase_unit_id=4292903737313198081,
                    accounting_unit_id=4292903737313198081,
                    unit_rate=10,
                    tax_rate=0.4,
                    purchase_price=140,
                    price=10
                )]
        )
        res = client.CreatePurchaseOrder(purchase_pb2.CreatePurchaseOrderRequest(**request), metadata=metadata)
        print(res)

    if api == "采购列表":
        request = dict(
            start_date=start_date,
            end_date=end_date,
            limit=10,
            include_total=True,
            branch_type="MACHINING_CENTER",
            sort_type="desc"
        )
        res = client.ListPurchaseOrder(purchase_pb2.ListPurchaseOrderRequest(**request), metadata=metadata)
        print(res)

    if api == "修改状态":
        request = dict(
            order_id=4305644284432875520,
            status="APPROVED"
        )
        res = client.ChangeOrderStatus(purchase_pb2.ChangeOrderStatusRequest(**request), metadata=metadata)
        print(res)

    if api == "订单详情":
        request = dict(
            order_id=4302737307784445952
        )
        res = client.GetOrderDetailById(purchase_pb2.GetOrderDetailByIdRequest(**request), metadata=metadata)
        print(res)

    if api == "更新订单":
        order_date = get_today_datetime()
        arrival_date = order_date + timedelta(days=1)
        request = dict(
            order_id=4302737307784445952,
            order_date=get_timestamp(order_date),
            arrival_date=get_timestamp(arrival_date),
            remark="测试更新订单",
            received_by=4292233886995316737,
            supplier_id=4291546589115187201,
            order_type="MH",
            purchase_type="DP",
            purchase_reason="没货了呗",
            product_items=[dict(
                product_id=4292961446007930881,
                quantity=4,
                distribution_type="PUR",
                price_tax=12,
                purchase_unit_id=4291789752425775105,
                accounting_unit_id=4291789752425775105,
                unit_rate=10,
                tax_rate=0.2,
                purchase_price=48,
                price=10,
            ),
                dict(
                    product_id=4292961446226034689,
                    quantity=10,
                    distribution_type="PUR",
                    price_tax=14,
                    purchase_unit_id=4292903737313198081,
                    accounting_unit_id=4292903737313198081,
                    unit_rate=10,
                    tax_rate=0.4,
                    purchase_price=140,
                    price=10
                )]
        )
        res = client.UpdatePurchaseOrder(purchase_pb2.UpdatePurchaseOrderRequest(**request), metadata=metadata)
        print(res)

    if api == "根据仓库拉商品":
        request = dict(
            id=4303639323212775425,
            product_category_ids=[]
        )
        res = client.GetProductListByWHId(purchase_pb2.GetProductListByWHIdRequest(**request), metadata=metadata)
        return res

    if api == "采购单报表":
        request = dict(
            start_date=start_date,
            end_date=end_date,
            limit=10,
            offset=0,
            branch_type="MACHINING_CENTER"
        )
        res = client.GetPurchaseBi(purchase_pb2.GetPurchaseBiRequest(**request), metadata=metadata)
        return res


def test_realtime_inventory(api):
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = inventory_bi_pb2_grpc.ManufactoryInventoryBiServiceStub(channel=conn)
    branch_id_list = [4292233886995316737]
    if api == "实时库存":
        request = dict(
            branch_ids=branch_id_list,
            product_ids=[],
            limit=10,
            order=None,
            sort=None,
            branch_type="WAREHOUSE"
        )
        res = client.RealtimeInventory(inventory_bi_pb2.RealtimeInventoryRequest(**request), metadata=metadata)
        return res
    if api == "库存流水":
        request = dict(
            branch_id=4292233886995316737,
            product_ids=[],
            code=None,
            limit=10,
            # branch_type="WAREHOUSE"
            end_date=end_date
        )
        res = client.QueryInventoryLog(inventory_bi_pb2.QueryInventoryLogRequest(**request), metadata=metadata)
        return res
    if api == "DailyInventory":
        request = dict(
            branch_id=4510052643489710080,
            product_ids=[4485373583517843456],
            code=None,
            limit=10,
            branch_type="MACHINING_CENTER",
            start_date=start_date,
            end_date=end_date,
        )
        res = client.DailyInventory(inventory_bi_pb2.DailyInventoryRequest(**request), metadata=metadata)
        return res
    if api == "RealtimeInventoryByAccounts":
        request = dict(
            accounts=[
                dict(branch_id=4415115443669303297, product_id=4374806541765705729, distribution_type="PAD"),
                dict(branch_id=4400623470577188865, product_id=4374806530898264065, distribution_type="PAD")
            ],
            check_type="deliverGoods"
        )
        res = client.RealtimeInventoryByAccounts(inventory_bi_pb2.RealtimeInventoryByAccountsRequest(**request),
                                                 metadata=metadata)
        return res


def test_get_transfer_list():
    request = dict(
        transfer_ids=[4461849052094922752]
    )
    conn = grpc.insecure_channel(_HOST + ':' + _PORT)
    client = transfer_pb2_grpc.WarehouseTransferStub(channel=conn)
    res = client.GetTransferList(transfer_pb2.GetTransferListRequest(**request), metadata=metadata)
    return res


if __name__ == "__main__":
    # test_warehouse_client("创建采购")
    # test_warehouse_client("采购列表")
    # test_warehouse_client("修改状态")
    # test_warehouse_client("订单详情")
    # test_warehouse_client("更新订单")
    # res = test_warehouse_realtime_inventory("RealtimeInventoryByAccounts")
    # res = test_adjust_client("新建报废单")
    # res = test_warehouse_client("采购单报表")
    # res = test_warehouse_realtime_inventory("每日库存")
    # res = test_warehouse_client("根据仓库拉商品")
    # res = test_realtime_inventory("DailyInventory")
    res = test_get_transfer_list()
    print(res)