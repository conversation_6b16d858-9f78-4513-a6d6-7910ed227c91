# -*- coding: utf8 -*-

from hex_exception import Unknown
from supply.api import GrpcAPI

class SampleAPI(GrpcAPI):

    def Error(self, req, ctx):
        raise Unknown('demo')

    # MUST define firstly in .proto file API name with
    # input and return message types
    # e.g. API named as HelloWorld
    # in .proto file should be:
    # rpc HelloWorld(<message type>) returns (<return message type>);
    # def HelloWorld(self, req, ctx):
    #     return HelloWorldMessage(wording="hello world!")
