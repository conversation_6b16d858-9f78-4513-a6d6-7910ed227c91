# -*- coding:utf-8 -*-
from datetime import datetime, timedelta

from ..error.exception import DataValidationException
from ..module.transfer_service import ssm as transfer_service
from ..proto.transfer_pb2_grpc import transferServicer
from ..proto.transfer_pb2 import *
from ..utils.transfer_enum import STATUS, P_STAUS
from google.protobuf.timestamp_pb2 import Timestamp
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info
from ..client.metadata_service import metadata_service
from supply.utils.helper import store_scope_check, branch_scope_check_list


class TransferService(OauthGrpcAPI, transferServicer):
    # 查询调拨单1
    def GetTransfer(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        transfer_details_list = transfer_service.list_transfer_detail(request, partner_id, user_id)
        total = None
        if isinstance(transfer_details_list, tuple):
            total, transfer_details_list = transfer_details_list
        if total is not None:
            res = dict(total=total, rows=transfer_details_list)
        return GetTransferResponse(**res)

    # 查询一个调拨单2
    def GetTransferByID(self, request, context):
        props = {}
        transfer_id = request.transfer_id
        partner_id, user_id = get_user_info(context)
        detail_obj = transfer_service.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if detail_obj:
            # transfer_service.transfer_scope_check_branch(branch_type=detail_obj.branch_type,
            #                                              sub_type=detail_obj.sub_type,
            #                                              shipping_store=detail_obj.shipping_store,
            #                                              receiving_store=detail_obj.receiving_store,
            #                                              partner_id=partner_id, user_id=user_id)
            props = detail_obj.props()
            if 'transfer_id' in props:
                props['id'] = props['transfer_id']
                del props['transfer_id']
                del props['request_id']
                props['status'] = STATUS.index(props['status'])
                props['process_status'] = P_STAUS.index(props['process_status'])
        return GetTransferByIDResponse(**props)

    # 获取门店可调拨商品3
    def GetTransferProductByBranchID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        limit = request.limit
        if not limit:
            limit = -1
        offset = request.offset
        include_total = request.include_total
        search = request.search
        search_fields = request.search_fields
        store_id = request.store_id
        category_ids = list(request.category_ids)
        list_transfer_store_product = transfer_service.list_transfer_store_product(partner_id=partner_id,
                                                                                   user_id=user_id,
                                                                                   store_id=store_id,
                                                                                   limit=limit, offset=offset,
                                                                                   include_total=include_total,
                                                                                   search=search,
                                                                                   search_fields=search_fields,
                                                                                   category_ids=category_ids,
                                                                                   order_by=request.order_by)
        if include_total:
            if list_transfer_store_product is not None:
                res['total'] = list_transfer_store_product[0]
                res['rows'] = list_transfer_store_product[1]
                res['inventory_unchanged_rows'] = list_transfer_store_product[2]
        else:
            res['rows'] = list_transfer_store_product[0]
            res['inventory_unchanged_rows'] = list_transfer_store_product[1]
        return GetTransferProductByBranchIDResponse(**res)

    # 查询相同调拨区域门店4
    def GetTransferRegionByBranchID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        store_scope_check(store_id, partner_id, user_id)
        same_attribute_region = transfer_service.list_same_attribute_region_store(store_id=store_id,
                                                                                  partner_id=partner_id,
                                                                                  user_id=user_id)
        res['rows'] = same_attribute_region
        return GetTransferRegionByBranchIDResponse(**res)

    # 获取一个调拨单商品5
    def GetTransferProductByTransferID(self, request, context):
        res = {}
        include_total = request.include_total
        order = request.order
        offset = request.offset
        limit = request.limit
        transfer_id = request.transfer_id
        partner_id, user_id = get_user_info(context)
        list_transfer_product = transfer_service.list_transfer_product(partner_id=partner_id, user_id=user_id,
                                                                       transfer_id=transfer_id,
                                                                       include_total=include_total,
                                                                       order=order, offset=offset, limit=limit)
        total = None
        if isinstance(list_transfer_product, tuple):
            total, list_transfer_product = list_transfer_product
        if total is not None:
            res['total'] = total
            res['rows'] = list_transfer_product
        res['rows'] = list_transfer_product
        return GetTransferProductByTransferIDResponse(**res)

    # 创建调拨单6
    def CreateTransfer(self, request, context):
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        transfer_detail = transfer_service.create_update_transfer(request, partner_id, user_id, username,
                                                                  is_create=True)
        if transfer_detail is None:
            transfer_detail = {}
            return Transfer(**transfer_detail)
        props = transfer_detail.props()
        if 'transfer_id' in props:
            props['id'] = props['transfer_id']
            del props['transfer_id']
        return Transfer(**props)

    # 修改调拨单7
    def UpdateTransfer(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        res['result'] = transfer_service.update_transfer_product(request, partner_id, user_id, username)
        return UpdateTransferResponse(**res)

    # 确认调拨单8
    def ConfirmTransfer(self, request, context):
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        transfer_detail = transfer_service.confirmed_transfer(request, partner_id, user_id, username)
        if transfer_detail:
            props = transfer_detail.props()
            props['status'] = STATUS.index(props['status'])
            props['process_status'] = P_STAUS.index(props['process_status'])
        if 'transfer_id' in props:
            props['id'] = props['transfer_id']
            del props['transfer_id']
        return Transfer(**props)

    # 删除调拨单商品10
    def DeleteTransferProduct(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        transfer_product_id = request.transfer_product_id
        res['result'] = transfer_service.delete_transfer_product(transfer_product_id, partner_id, user_id)
        return DeleteTransferProductResponse(**res)

    # 删除调拨单11
    def DeleteTransfer(self, request, context):
        res = {}
        transfer_id = request.transfer_id
        partner_id, user_id = get_user_info(context)
        res['result'] = transfer_service.delete_transfer(transfer_id, partner_id, user_id)
        return DeleteTransferResponse(**res)

    # 提交调拨单12
    def SubmitTransfer(self, request, context):
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        transfer_detail = transfer_service.submit_transfer(request, partner_id, user_id, username)
        if transfer_detail:
            props = transfer_detail.props()
            props['status'] = STATUS.index(props['status'])
            props['process_status'] = P_STAUS.index(props['process_status'])
        if 'transfer_id' in props:
            props['id'] = props['transfer_id']
            del props['transfer_id']
        return Transfer(**props)

    # 取消调拨单13
    def CancelTransfer(self, request, context):
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        transfer_detail = transfer_service.cancel_transfer(request, partner_id, user_id, username)
        if transfer_detail:
            props = transfer_detail.props()
            props['status'] = STATUS.index(props['status'])
            props['process_status'] = P_STAUS.index(props['process_status'])
        if 'transfer_id' in props:
            props['id'] = props['transfer_id']
            del props['transfer_id']
        return Transfer(**props)

    # 调拨创建收获单核算完库存后更新调拨单状态13
    # def FinalizedTransfer(self,request,context):
    #     transfer_id=request.transfer_id
    #     user_id=request.user_id
    #     ret=transfer_service.update_transfer_status(transfer_id=transfer_id,user_id=user_id)
    #     return FinalizedTransferResponse(result=ret)

    def GetTransferCollect(self, request, context):
        """调拨单报表汇总查询"""
        res = {}
        partner_id, user_id = get_user_info(context)
        st_ids = list(request.st_ids)
        store_ids = list(request.store_ids)
        category_ids = list(request.category_ids)
        product_name = request.product_name
        start_date = request.start_date
        end_date = request.end_date
        branch_type = request.branch_type
        sub_type = request.sub_type
        transfer_type = request.type
        # store_ids = branch_scope_check_list(branch_type=branch_type, branch_ids=store_ids, partner_id=partner_id,
        #                                     user_id=user_id)
        cross_company = request.cross_company
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        if start_date.year == 1970:
            raise DataValidationException("请传入查询开始日期")
        if end_date.year == 1970:
            raise DataValidationException("请传入查询结束日期")
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        is_in = request.is_in
        order = request.order
        sort = request.sort
        if not sort:
            sort = 'store_code'
        data, total, sum_quantity, sum_accounting_quantity = transfer_service.bi_get_transfer_collect(partner_id,
                                                                                                      user_id,
                                                                                                      st_ids=st_ids,
                                                                                                      category_ids=category_ids,
                                                                                                      product_name=product_name,
                                                                                                      start_date=start_date,
                                                                                                      end_date=end_date,
                                                                                                      limit=limit,
                                                                                                      offset=offset,
                                                                                                      include_total=include_total,
                                                                                                      is_in=is_in,
                                                                                                      store_ids=store_ids,
                                                                                                      order=order,
                                                                                                      sort=sort,
                                                                                                      branch_type=branch_type,
                                                                                                      sub_type=sub_type,
                                                                                                      transfer_type=transfer_type,
                                                                                                      cross_company=cross_company)
        if include_total:
            if data is None:
                return GetTransferCollectResponse(**res)
            res['total'] = {}
            res['total']['count'] = total
            res['total']['sum_quantity'] = sum_quantity
            res['total']['sum_accounting_quantity'] = sum_accounting_quantity
            res['rows'] = data
            return GetTransferCollectResponse(**res)
        else:
            if data is None:
                return GetTransferCollectResponse(**res)
            res['rows'] = data
            return GetTransferCollectResponse(**res)

    def GetTransferCollectDetailed(self, request, context):
        """调拨单报表明细查询"""
        res = {}
        partner_id, user_id = get_user_info(context)
        st_ids = list(request.st_ids)
        store_ids = list(request.store_ids)
        category_ids = list(request.category_ids)
        product_name = request.product_name
        start_date = request.start_date
        end_date = request.end_date
        branch_type = request.branch_type
        sub_type = request.sub_type
        transfer_type = request.type
        # store_ids = branch_scope_check_list(branch_type=branch_type, branch_ids=store_ids, partner_id=partner_id,
        #                                     user_id=user_id)
        cross_company = request.cross_company
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        if start_date.year == 1970:
            raise DataValidationException("请传入查询开始日期")
        if end_date.year == 1970:
            raise DataValidationException("请传入查询结束日期")
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        is_in = request.is_in
        order = request.order
        sort = request.sort
        if not sort:
            sort = 'store_code'
        data, total, sum_quantity, sum_accounting_quantity = transfer_service.bi_get_transfer_detailed(partner_id,
                                                                                                       user_id,
                                                                                                       st_ids=st_ids,
                                                                                                       category_ids=category_ids,
                                                                                                       product_name=product_name,
                                                                                                       start_date=start_date,
                                                                                                       end_date=end_date,
                                                                                                       limit=limit,
                                                                                                       offset=offset,
                                                                                                       include_total=include_total,
                                                                                                       is_in=is_in,
                                                                                                       store_ids=store_ids,
                                                                                                       order=order,
                                                                                                       sort=sort,
                                                                                                       branch_type=branch_type,
                                                                                                       sub_type=sub_type,
                                                                                                       transfer_type=transfer_type,
                                                                                                       cross_company=cross_company)

        if include_total:
            if data is None:
                return GetTransferCollectDetailedResponse(**res)
            res['total'] = {}
            res['total']['count'] = total
            res['total']['sum_quantity'] = sum_quantity
            res['total']['sum_accounting_quantity'] = sum_accounting_quantity
            res['rows'] = data
            return GetTransferCollectDetailedResponse(**res)
        else:
            if data is None:
                return GetTransferCollectDetailedResponse(**res)
            res['rows'] = data
            return GetTransferCollectDetailedResponse(**res)

    def CreateInnerTransfer(self, request, context):
        """创建自动创建内部调拨单接口(暂时仅内部rpc调用)"""
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        transfer_id = transfer_service.create_inner_transfer(request=request, partner_id=partner_id,
                                                             user_id=user_id, username=username)
        if transfer_id:
            res = dict(transfer_id=transfer_id)
            return CreateInnerTransferResponse(**res)

    def ConfirmTransferByCode(self, request, context):
        """三方确认调拨单"""
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        ret = transfer_service.confirmed_transfer_by_code(request, partner_id, user_id, username)
        return ConfirmTransferByCodeResponse(**ret)