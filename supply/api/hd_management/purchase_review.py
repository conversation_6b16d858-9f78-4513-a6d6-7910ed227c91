# -*- coding: utf-8 -*-
import logging

from supply.error.exception import StatusUnavailable
from supply.proto.hd_management.purchase_review_pb2_grpc import PurchaseReviewServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.proto.hd_management.purchase_review_pb2 import *
from supply.module.purchase_review_service import purchase_review_service


class PurchaseReviewApi(PurchaseReviewServicer, OauthGrpcAPI):
    """采购复核相关API"""
    # 目前只有内部创建采购复核，先不加接口权限
    def CreatePurchaseReviewOrder(self, request, context):
        """创建采购复核单"""
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.create_purchase_review_order(request, partner_id=partner_id, user_id=user_id)
        return CreatePurchaseReviewOrderResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.purchase_review.view")
    def ListPurchaseReviewOrder(self, request, context):
        """查询采购复核单列表"""
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.list_purchase_review_order(request, partner_id=partner_id, user_id=user_id)
        return ListPurchaseReviewOrderResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.purchase_review.view")
    def GetPurchaseReviewDetail(self, request, context):
        """查询采购复核单详情"""
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.get_purchase_review_detail(request, partner_id=partner_id, user_id=user_id)
        return GetPurchaseReviewDetailResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.purchase_review.maintain")
    def UpdatePurchaseReviewOrder(self, request, context):
        """更新采购复核详情"""
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.update_purchase_review_order(request, partner_id=partner_id, user_id=user_id)
        return UpdatePurchaseReviewOrderResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.purchase_review.view")
    def ListPurchaseReviewDetail(self, request, context):
        """查询采购复核明细"""
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.list_purchase_review_detail(request, partner_id=partner_id, user_id=user_id)
        return ListPurchaseReviewDetailResponse(**response)

    def ChangePurchaseReviewOrderStatus(self, request, context):
        """修改采购复核状态
        变更不同状态走不同入口以做权限校验:
            1、维护:
                    SUBMITTED-提交
            2、审批:
                    REJECTED-驳回、APPROVED-审核
            :exception
            勾兑单确认勾兑:
                    COMPLETED-已勾兑
        """
        if request.status == "SUBMITTED":
            return self.SubmitPurchaseReviewOrder(request, context)
        elif request.status == "REJECTED":
            return self.RejectPurchaseReviewOrder(request, context)
        elif request.status == "APPROVED":
            return self.ApprovePurchaseReviewOrder(request, context)
        elif request.status == "COMPLETED":
            return self.BlendingPurchaseReviewOrder(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.status))

    @authorization_scope(permission_code="boh.hd_management.purchase_review.maintain")
    def SubmitPurchaseReviewOrder(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.change_purchase_review_status(partner_id=partner_id,
                                                                         order_ids=request.order_ids,
                                                                         action=request.status,
                                                                         allow_status=["INITED", "REJECTED"],
                                                                         user_id=user_id)
        return ChangePurchaseReviewOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.purchase_review.audit")
    def RejectPurchaseReviewOrder(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.change_purchase_review_status(partner_id=partner_id,
                                                                         order_ids=request.order_ids,
                                                                         action=request.status,
                                                                         reject_reason=request.reject_reason,
                                                                         allow_status=["SUBMITTED"],
                                                                         user_id=user_id)
        return ChangePurchaseReviewOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.purchase_review.audit")
    def ApprovePurchaseReviewOrder(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.change_purchase_review_status(partner_id=partner_id,
                                                                         order_ids=request.order_ids,
                                                                         action=request.status,
                                                                         allow_status=["SUBMITTED"],
                                                                         user_id=user_id)
        return ChangePurchaseReviewOrderStatusResponse(**response)

    # @authorization_scope(permission_code="boh.hd_management.purchase_review.maintain")
    def BlendingPurchaseReviewOrder(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = purchase_review_service.change_purchase_review_status(partner_id=partner_id,
                                                                         order_ids=request.order_ids,
                                                                         action=request.status,
                                                                         allow_status=["APPROVED"],
                                                                         user_id=user_id)
        return ChangePurchaseReviewOrderStatusResponse(**response)

