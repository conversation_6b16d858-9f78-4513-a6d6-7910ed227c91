# -*- coding: utf-8 -*-

from supply.proto.hd_management.price_adjustment_pb2_grpc import PriceAdjustmentServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.proto.hd_management.price_adjustment_pb2 import *
from supply.module.price_adjustment import price_adjustment_service


class PriceAdjustmentAPI(PriceAdjustmentServicer, OauthGrpcAPI):
    """调价单相关api"""
    def CreatePriceAdjustment(self, request, context):
        """创建调价单
        暂时没有外部调用创建调价单的需求先注释掉
        """
        # partner_id, user_id = get_user_info(context)
        # return CreatePurchaseReviewOrderResponse(**response)
        pass

    @authorization_scope(permission_code="boh.hd_management.adjust_price.view")
    def ListPriceAdjustment(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = price_adjustment_service.list_price_adjustment(request, partner_id, user_id)
        return ListPriceAdjustmentResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.adjust_price.view")
    def GetPriceAdjustmentDetail(self, request, context):
        """查询调价单详情两种场景：
        1、调价单页面根据调价单id查询
        2、复核单页面根据调价单code查询"""
        partner_id, user_id = get_user_info(context)
        response = price_adjustment_service.get_price_adjustment_detail(request, partner_id, user_id)
        return GetPriceAdjustmentDetailResponse(**response)
