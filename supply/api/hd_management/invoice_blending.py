# -*- coding: utf-8 -*-

from supply.proto.hd_management.invoice_blending_pb2_grpc import InvoiceBlendingServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.proto.hd_management.invoice_blending_pb2 import *
from supply.module.invoice_blending import invoice_blending_service


class InvoiceBlendingApi(InvoiceBlendingServicer, OauthGrpcAPI):
    """发票勾兑相关api"""
    @authorization_scope(permission_code="boh.hd_management.invoice.maintain")
    def CreateInvoiceBlendingOrder(self, request, context):
        """新建发票勾兑单"""
        partner_id, user_id = get_user_info(context)
        response = invoice_blending_service.create_invoice_blending_order(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return CreateInvoiceBlendingOrderResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.invoice.audit")
    def ChangeInvoiceBlendingStatus(self, request, context):
        """变更勾兑单状态"""
        partner_id, user_id = get_user_info(context)
        response = invoice_blending_service.change_invoice_blending_status(request, user_id=user_id,
                                                                           partner_id=partner_id)
        return ChangeInvoiceBlendingStatusResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.invoice.maintain")
    def UpdateInvoiceBlendingOrder(self, request, context):
        """更新已驳回的勾兑单内容"""
        partner_id, user_id = get_user_info(context)
        response = invoice_blending_service.update_invoice_blending_order(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return UpdateInvoiceBlendingOrderResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.invoice.view")
    def ListInvoiceBlending(self, request, context):
        """查询勾兑单"""
        partner_id, user_id = get_user_info(context)
        response = invoice_blending_service.list_invoice_blending(request, user_id=user_id, partner_id=partner_id)
        return ListInvoiceBlendingResponse(**response)

    @authorization_scope(permission_code="boh.hd_management.invoice.view")
    def GetInvoiceBlendingDetail(self, request, context):
        """查询勾兑单详情"""
        partner_id, user_id = get_user_info(context)
        response = invoice_blending_service.get_invoice_blending_detail(request, partner_id=partner_id, user_id=user_id)
        return GetInvoiceBlendingDetailResponse(**response)

