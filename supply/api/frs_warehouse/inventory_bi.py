# -*- coding: utf-8 -*-

from supply import request_info
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, branch_list_scope_check, branch_scope_check, authorization_scopes

from supply.proto.frs_warehouse.inventory_bi_pb2_grpc import FrsWarehouseInventoryBiServiceServicer
from supply.proto.frs_warehouse.inventory_bi_pb2 import RealtimeInventoryResponse, DailyInventoryResponse, \
        QueryInventoryLogResponse
from supply.module.inventory_bi import inventory_bi_service
from supply.module.upload_inventory import upload_inventory_service


# 加盟商门店运营-库存
# authorization = authorization_scope('boh.frs_store.inventory.view')
class FrsWarehouseInventoryBiAPI(FrsWarehouseInventoryBiServiceServicer, OauthGrpcAPI):

    # 实时库存
    @authorization_scopes(['boh.frs_warehouse.inventory.view'])
    def RealtimeInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.branch_ids[:] = branch_list_scope_check(partner_id, user_id, 'warehouse', 'boh.frs_warehouse', request.branch_ids)
        is_frs = True
        inventory_detail, count = inventory_bi_service.get_realtime_inventory(request, partner_id, user_id, is_frs)
        result = {'rows':inventory_detail, 'total':count}
        return RealtimeInventoryResponse(**result)

    # 流水库存
    @authorization_scopes(['boh.frs_warehouse.inventory.view'])
    def QueryInventoryLog(self, request, context):
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id, user_id, 'warehouse', 'boh.frs_warehouse', request.branch_id)
        is_frs = True
        inventory_log, total, amount_sum = inventory_bi_service.query_inventory_log_list(request, partner_id, user_id, is_frs,schema_name='DISTRCENTER')
        result = {'rows':inventory_log, 'total':total, 'amount_sum':amount_sum}
        return QueryInventoryLogResponse(**result)

    # 每日库存
    @authorization_scopes(['boh.frs_warehouse.inventory.view'])
    def DailyInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id, user_id, 'warehouse', 'boh.frs_warehouse', request.branch_id)
        is_frs = True
        inventory_detail, count = inventory_bi_service.get_daily_inventory(request, partner_id, user_id, is_frs)
        result = {'rows':inventory_detail, 'total':count}
        return DailyInventoryResponse(**result)
