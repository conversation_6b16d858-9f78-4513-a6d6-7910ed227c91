from supply.proto.warehouse.purchase_pb2_grpc import WarehousePurchaseServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.module.purchase_service import purchase_service
from supply.error.exception import StatusUnavailable
from supply.utils.auth import branch_list_scope_check, branch_scope_check
from supply.proto.warehouse.purchase_pb2 import CreatePurchaseOrderResponse, ListPurchaseOrderResponse, \
    ChangeOrderStatusResponse, GetOrderDetailByIdResponse, UpdatePurchaseOrderResponse, \
    GetProductListByWHIdResponse, GetPurchaseBiResponse, ListPurchaseOrderPrintResponse


class WarehousePurchaseApi(WarehousePurchaseServicer, OauthGrpcAPI):
    """仓库模块相关功能API"""

    def ChangeOrderStatus(self, request, context):
        """修改订单状态主接口分发
        变更不同状态走不同入口以做权限校验:
        1、维护:
                INITED新建 SUBMITTED提交 CANCELLED取消 SUCCESS已收货
        2、审批:
                REJECTED驳回 APPROVED审核
        """
        if request.status == "SUBMITTED":
            return self.SubmitPurchaseOrder(request, context)
        elif request.status == "CANCELLED":
            return self.CancelPurchaseOrder(request, context)
        elif request.status == "REJECTED":
            return self.RejectPurchaseOrder(request, context)
        elif request.status == "APPROVED":
            return self.ApprovePurchaseOrder(request, context)
        elif request.status == "SUCCESS":
            return self.CompletePurchaseOrder(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.status))

    @authorization_scope(permission_code="boh.warehouse.order.maintain")
    def CreatePurchaseOrder(self, request, context):
        """新建仓库采购订单"""
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=request.received_by)
        response = purchase_service.create_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return CreatePurchaseOrderResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.view")
    def ListPurchaseOrder(self, request, context):
        """仓库采购订单列表查询"""
        partner_id, user_id = get_user_info(context)
        request.received_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                                                          domain='boh.warehouse', branch_ids=request.received_ids)
        response = purchase_service.list_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return ListPurchaseOrderResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.maintain")
    def SubmitPurchaseOrder(self, request, context):
        """提交仓库采购单"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=record.received_by)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.maintain")
    def CancelPurchaseOrder(self, request, context):
        """取消仓库采购单"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=record.received_by)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.audit")
    def RejectPurchaseOrder(self, request, context):
        """驳回仓库采购单"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=record.received_by)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.audit")
    def ApprovePurchaseOrder(self, request, context):
        """审核仓库采购单"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=record.received_by)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    # @authorization_scope(permission_code="boh.warehouse.order.maintain")
    def CompletePurchaseOrder(self, request, context):
        """完成收货仓库采购单(receipt内部调用)"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.view")
    def GetOrderDetailById(self, request, context):
        """查询采购单详情"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=record.received_by)
        response = purchase_service.get_order_detail_by_id(request, user_id=user_id, partner_id=partner_id)
        return GetOrderDetailByIdResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.maintain")
    def UpdatePurchaseOrder(self, request, context):
        """更新采购单信息"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=record.received_by)
        response = purchase_service.update_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return UpdatePurchaseOrderResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.maintain")
    def GetProductListByWHId(self, request, context):
        """根据仓库id拉商品"""
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=request.id)
        response = purchase_service.get_product_list_by_WHid(request, user_id=user_id, partner_id=partner_id,
                                                             branch_type="WAREHOUSE")
        return GetProductListByWHIdResponse(**response)

    @authorization_scope(permission_code="boh.warehouse_bi.detail.view")
    def GetPurchaseBi(self, request, context):
        """"仓库采购单报表"""
        partner_id, user_id = get_user_info(context)
        request.wh_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                                                    domain='boh.warehouse_bi', branch_ids=request.wh_ids)
        total, response = purchase_service.get_purchase_bi(request, user_id=user_id, partner_id=partner_id)
        bi_detailed = {'rows': response, 'total': total}
        return GetPurchaseBiResponse(**bi_detailed)

    @authorization_scope(permission_code="boh.warehouse.order.view")
    def ListPurchaseOrderPrint(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = purchase_service.list_purchase_order_print(request, user_id=user_id, partner_id=partner_id)
        return ListPurchaseOrderPrintResponse(**response)
