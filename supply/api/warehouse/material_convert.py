# -*- coding: utf-8 -*-
from supply.proto.warehouse.material_convert_pb2_grpc import WarehouseMaterialConvertServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.utils.auth import branch_scope_check, branch_list_scope_check
from supply.module.material_convert import material_convert_service
from supply.proto.warehouse.material_convert_pb2 import CreateMaterialConvertResponse, ListMaterialConvertResponse, \
    GetMaterialConvertDetailResponse, UpdateMaterialConvertResponse


class WarehouseMaterialConvertApi(WarehouseMaterialConvertServicer, OauthGrpcAPI):
    """物料转换模块API"""
    @authorization_scope(permission_code="boh.warehouse.order.maintain")
    def CreateMaterialConvert(self, request, context):
        """创建物料转换单"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=request.branch_id)
        response = material_convert_service.create_material_convert(request, user_id=user_id, partner_id=partner_id)
        return CreateMaterialConvertResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.view")
    def ListMaterialConvert(self, request, context):
        """物料转换单列表查询"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.branch_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                                                        domain='boh.warehouse', branch_ids=request.branch_ids)
        response = material_convert_service.list_material_convert(request, user_id=user_id, partner_id=partner_id)
        return ListMaterialConvertResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.view")
    def GetMaterialConvertDetail(self, request, context):
        """查询物料转换单详情"""
        partner_id, user_id = get_user_info(context)
        convert_record = material_convert_service.get_material_convert_by_id(convert_id=request.convert_id,
                                                                             partner_id=partner_id)
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=convert_record.branch_id)
        response = material_convert_service.get_material_convert_detail(request, user_id=user_id, partner_id=partner_id)
        return GetMaterialConvertDetailResponse(**response)

    @authorization_scope(permission_code="boh.warehouse.order.maintain")
    def UpdateMaterialConvert(self, request, context):
        """更新物料转换单"""
        partner_id, user_id = get_user_info(context)
        convert_record = material_convert_service.get_material_convert_by_id(convert_id=request.convert_id,
                                                                             partner_id=partner_id)
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='warehouse',
                           domain='boh.warehouse', branch_id=convert_record.branch_id)
        response = material_convert_service.update_material_convert(request, user_id=user_id, partner_id=partner_id)
        return UpdateMaterialConvertResponse(**response)


