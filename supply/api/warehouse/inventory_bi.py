# -*- coding: utf-8 -*-

from supply import request_info
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, branch_list_scope_check, branch_scope_check

from supply.proto.warehouse.inventory_bi_pb2_grpc import WarehouseInventoryBiServiceServicer
from supply.proto.warehouse.inventory_bi_pb2 import RealtimeInventoryResponse, DailyInventoryResponse, \
    QueryInventoryLogResponse, RealtimeInventoryByAccountsResponse, SummaryInventoryResponse
from supply.module.inventory_bi import inventory_bi_service


# 仓库运营-库存
authorization = authorization_scope('boh.warehouse.inventory.view')
class WarehouseInventoryBiAPI(WarehouseInventoryBiServiceServicer, OauthGrpcAPI):

    # 实时库存
    @authorization
    def RealtimeInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.branch_ids[:] = branch_list_scope_check(partner_id, user_id, 'warehouse', 'boh.warehouse', request.branch_ids)
        inventory_detail, count = inventory_bi_service.get_realtime_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return RealtimeInventoryResponse(**result)

    # 流水库存
    @authorization
    def QueryInventoryLog(self, request, context):
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id, user_id, 'warehouse', 'boh.warehouse', request.branch_id)
        inventory_log, total, amount_sum = inventory_bi_service.query_inventory_log_list(request, partner_id, user_id,schema_name='DISTRCENTER')
        result = {'rows':inventory_log, 'total':total, 'amount_sum':amount_sum}
        return QueryInventoryLogResponse(**result)

    # 每日库存
    @authorization
    def DailyInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id, user_id, 'warehouse', 'boh.warehouse', request.branch_id)
        inventory_detail, count = inventory_bi_service.get_daily_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return DailyInventoryResponse(**result)

    @authorization
    def RealtimeInventoryByAccounts(self, request, context):
        """按账户和指定商品查询实时库存，包装给前端用
            查询库存前校验当前账户是否有子账户，若有返回子账户的库存
        """
        partner_id, user_id = get_user_info(context)
        ret = inventory_bi_service.get_realtime_inventory_by_accounts(request, partner_id, user_id)
        return RealtimeInventoryByAccountsResponse(**ret)


    @authorization
    def SummaryInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        daily_inventory_list, total = inventory_bi_service.get_summary_inventory_by_summary(request, partner_id, user_id)
        result = {'rows': daily_inventory_list, 'total': total}
        return SummaryInventoryResponse(**result)

