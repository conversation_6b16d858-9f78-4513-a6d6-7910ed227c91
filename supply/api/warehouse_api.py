# -*- coding: utf-8 -*-

from supply.proto.warehouse_pb2_grpc import warehouseServicer
from supply.api import OauthGrpcAPI
from supply.utils import get_user_info
from supply.module.purchase_service import purchase_service
from supply.proto.warehouse_pb2 import CreatePurchaseOrderResponse, ListPurchaseOrderResponse, \
    ChangeOrderStatusResponse, GetOrderDetailByIdResponse, UpdatePurchaseOrderResponse, \
    GetProductListByWHIdResponse, GetPurchaseBiResponse


class WarehouseApi(warehouseServicer, OauthGrpcAPI):
    """仓库模块相关功能API"""

    def CreatePurchaseOrder(self, request, context):
        """新建仓库采购订单"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.create_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return CreatePurchaseOrderResponse(**response)

    def ListPurchaseOrder(self, request, context):
        """仓库采购订单列表查询"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.list_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return ListPurchaseOrderResponse(**response)

    def ChangeOrderStatus(self, request, context):
        """修改订单状态"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    def GetOrderDetailById(self, request, context):
        """查询采购单详情"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.get_order_detail_by_id(request, user_id=user_id, partner_id=partner_id)
        return GetOrderDetailByIdResponse(**response)

    def UpdatePurchaseOrder(self, request, context):
        """更新采购单信息"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.update_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return UpdatePurchaseOrderResponse(**response)

    def GetProductListByWHId(self, request, context):
        """根据仓库id拉商品"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.get_product_list_by_WHid(request, user_id=user_id, partner_id=partner_id)
        return GetProductListByWHIdResponse(**response)

    def GetPurchaseBi(self, request, context):
        """"仓库采购单报表"""
        partner_id, user_id = get_user_info(context)
        total, response = purchase_service.get_purchase_bi(request, user_id=user_id, partner_id=partner_id)
        bi_detailed = {'rows': response, 'total': total}
        return GetPurchaseBiResponse(**bi_detailed)
