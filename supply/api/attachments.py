

from ..utils import pb2dict
from supply import request_info
from ..proto.attachments_pb2_grpc import AttachmentsServiceServicer
from ..proto.attachments_pb2 import Response, AttachmentsList
from ..module.attachments import attachments_service
from ..api import OauthGrpcAP<PERSON>, exclude_oauth
from ..utils import get_user_info


class AttachmentsApi(AttachmentsServiceServicer, OauthGrpcAPI):
    
    def CreateAttachments(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = attachments_service.create_attachments(request, partner_id, user_id)
        return Response(**result)

    def GetAttachmentsByDocId(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = attachments_service.get_attachments_by_doc_id(request, partner_id, user_id)
        return AttachmentsList(**result)

    def UpdateAttachmentsByDocId(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = attachments_service.update_attachments_by_doc_id(request, partner_id, user_id)
        return Response(**result)

    def UpdateAttachmentsByDocIdMobile(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = attachments_service.update_attachments_by_doc_id(request, partner_id, user_id)
        return Response(**result)
