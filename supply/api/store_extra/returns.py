# -*- coding: utf-8 -*-

from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, authorization_scopes, \
    branch_list_scope_check, two_branch_list_scope_check
from datetime import datetime

from supply.proto.store_extra.returns_pb2_grpc import StoreExtraReturnServiceServicer
from supply.proto.store_extra.returns_pb2 import Returns, CreateReturnResponse, \
    ListReturnResponse, GetReturnProductByIdResponse, ReturnCommonResponse
from supply.module.returns import returns_service

# import logging

# 门店运营-退货单
class StoreExtraReturnsAPI(StoreExtraReturnServiceServicer, OauthGrpcAPI):

    @authorization_scopes(['boh.store_extra.return.view', 'boh.supplier.order.view'])
    def ListReturn(self, request, context):
        partner_id, user_id = get_user_info(context)
        if request.code: # 如果指定code就暂时不校验数据权限了，不然在供应商退货单查询的时候会因为没有权限卡
            pass
        else:
            # 接收方、配送方有任一数据权限符合即可
            request.return_by[:], request.return_to[:] = two_branch_list_scope_check(
                        partner_id, user_id, 'store_extra', 'boh.store_extra', request.return_by, request.return_to)
        # logging.info("return_by:{}, return_to:{}".format(request.return_by, request.return_to))
        count, return_list = returns_service.list_returns(partner_id, user_id, request)
        result = {'rows':return_list, 'total':count}
        return ListReturnResponse(**result)
    
    @authorization_scope('boh.store_extra.return.view')
    def GetReturnById(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        return_obj = returns_service.get_return_by_id(return_id, partner_id, user_id)
        return Returns(**return_obj)

    @authorization_scope('boh.store_extra.return.view')
    def GetReturnProductById(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        limit = request.limit
        offset = request.offset
        sort = request.sort
        order = request.order
        count, return_product_list = returns_service.list_return_products_by_return_id(return_id,limit, offset,sort,order,partner_id)
        result = {'rows':return_product_list, 'total':count}
        return GetReturnProductByIdResponse(**result)

    @authorization_scope('boh.store_extra.return.audit')
    def RejectReturn(self, request, context):
        return_id = request.id
        reject_reason = request.reject_reason
        partner_id, user_id = get_user_info(context)
        result = returns_service.reject_return(return_id, partner_id, user_id,reject_reason)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_extra.return.audit')
    def ConfirmReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.confirm_return(return_id, partner_id, user_id)
        result = {'payload': result}
        return ReturnCommonResponse(**result)
    
    @authorization_scope('boh.store_extra.return.audit')
    def UpdateReturn(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        products = request.products
        return_reason = request.return_reason
        remark = request.remark
        return_delivery_date = request.return_delivery_date
        return_to = request.return_to
        attachments = request.attachments
        logistics_type = request.logistics_type
        result = returns_service.update_return_product_quantity(return_id, partner_id, user_id, 
                                                products, return_reason, remark, 
                                                return_delivery_date, return_to,
                                                attachments, logistics_type)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_extra.return.audit')
    def ApproveReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        trans_type = request.trans_type
        result = returns_service.approve_return(return_id=return_id, partner_id=partner_id, \
            user_id=user_id, trans_type=trans_type)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_extra.return.view')
    def CheckReturnAvailableByrec(self, request, context):
        product_detail = request.products
        source_code = request.source_code
        return_id = request.return_id
        partner_id, user_id = get_user_info(context)
        result = returns_service.if_over_return_by_rec(source_code=source_code, product_detail=product_detail, 
                                                    partner_id=partner_id, user_id=user_id, return_id=return_id)
        result = {'payload':result}
        return ReturnCommonResponse(**result)
