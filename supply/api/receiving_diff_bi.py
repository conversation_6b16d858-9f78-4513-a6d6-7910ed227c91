# -*- coding: utf-8 -*-
from supply import request_info
from supply.utils import pb2dict
from supply.utils import get_user_info
from supply.api import OauthGrpcAPI, exclude_oauth

from supply.proto.receiving_diff_bi_pb2_grpc import ReceivingDiffBiServiceServicer
from supply.proto.receiving_diff_bi_pb2 import GetReceivingDiffCollectResponse, GetReceivingDiffDetailResponse
from supply.module.receiving_diff_bi import receiving_diff_bi_service


class ReceivingDiffBiAPI(ReceivingDiffBiServiceServicer, OauthGrpcAPI):

    def GetReceivingDiffCollect(self, request, context):
        """查询收货差异汇总报表"""
        partner_id, user_id = get_user_info(context)
        total, receiving_diff_bi_list = receiving_diff_bi_service.get_receiving_diff_collect(partner_id, user_id,
                                                                                             request)
        # print(total)
        receiving_diff_bi = {'rows': receiving_diff_bi_list, 'total': total}
        # print(receiving_diff_bi)
        return GetReceivingDiffCollectResponse(**receiving_diff_bi)

    def GetReceivingDiffDetail(self, request, context):
        """查询收货差异详情报表"""
        partner_id, user_id = get_user_info(context)
        total, receiving_diff_bi_detailed_list = receiving_diff_bi_service.get_receiving_diff_detailed(partner_id,
                                                                                                       user_id, request)
        # print(total)
        receiving_diff_bi_detailed = {'rows': receiving_diff_bi_detailed_list, 'total': total}
        # print(receiving_diff_bi_detailed)
        return GetReceivingDiffDetailResponse(**receiving_diff_bi_detailed)
