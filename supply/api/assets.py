# -*- coding: utf-8 -*-


from ..utils import pb2dict
from supply import request_info
from ..proto.assets_pb2_grpc import AssetsRecServiceServicer
from ..proto.assets_pb2 import CreateAssetsReceivingResponse, ConfirmAssetsReceivingResponse,\
    Assets, ListAssetProductsByIdResponse, ListAssetsReceivingResponse
from ..module.assets import AssetsService as assets_service
from ..api import OauthGrpcAPI, exclude_oauth
from ..utils import get_user_info


class AssetsAPI(AssetsRecServiceServicer, OauthGrpcAPI):
    
    def CreateAssetsReceiving(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = assets_service.create_assets(self, request, partner_id, user_id)
        result = {'result':result}
        return CreateAssetsReceivingResponse(**result)

    def ConfirmAssetsReceiving(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = assets_service.confirm_assets(self, request, partner_id, user_id)
        result = {'result':result}
        return ConfirmAssetsReceivingResponse(**result)

    def GetAssetById(self, request, context):
        partner_id, user_id = get_user_info(context)
        asset_id = request.id
        result = assets_service.get_asset_by_id(self, asset_id, partner_id, user_id)
        return Assets(**result)

    def ListAssetProductsById(self, request, context):
        partner_id, user_id = get_user_info(context)
        count, result = assets_service.get_asset_products_by_id(self, request, partner_id, user_id)
        result = {'rows':result, 'total':count}
        return ListAssetProductsByIdResponse(**result)

    def ListAssetsReceiving(self, request, context):
        partner_id, user_id = get_user_info(context)
        count, result = assets_service.list_assets(self, request, partner_id, user_id)
        assets = {'rows':result, 'total':count}
        return ListAssetsReceivingResponse(**assets)

        