# -*- coding: utf8 -*-
import traceback
import re
from datetime import datetime, timedelta

from google.protobuf.timestamp_pb2 import Timestamp

from supply import logger
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.driver.Redis import Redis_cli
from supply.error.demand import (DemandBelongError, DemandError,
                                 DemandNotExistError, DemandTypeError,
                                 VacancyError)
from supply.error.exception import DataValidationException
from supply.model.demand.supply_demand import Supply_demand
from supply.model.demand.supply_first_delivery import Supply_first_delivery
from supply.module import sale_forecast
from supply.module.demand import Supply_demand_product, demand_module
from supply.proto import supply_pb2
from supply.proto.supply_pb2 import (<PERSON><PERSON><PERSON><PERSON>,
                                     DemandEntity, DemandOrder,
                                     GetDemandOrderDetailResponse,
                                     GetDemandOrderProductDetailResponse,
                                     GetVacancyOrderProductResponse,
                                     GetVacancyProductResponse,
                                     GetValidStoreByProductIdResponse,
                                     IdRequest, ListDemandOrderResponse,
                                     ListFirstDeliveryResponse,
                                     ListVacancyResponse, OrderProduct,
                                     QueryDemandProductEntity,
                                     QueryDemandProductResponse,
                                     QueryDemandResponse,
                                     QueryOrderBiReportResponse, Response,
                                     Vacancy,
                                     VacancyProduct, Pong,
                                     CancelDemandOrderResponse,
                                     UploadDemandMasterResponse,
                                     GetDemandMasterUploadByBatchIdResponse,
                                     GetDemandMasterUploadResponse,
                                     ApproveDemandMasterUploadResponse,
                                     CancelDemandMasterUploadResponse,
                                     GetProductSaleForecastResponse,
                                     GetDemandAdjustProductByStoreIdResponse,
                                     CreateDemandAdjustResponse,
                                     GetDistributionByDemandResponse
                                     )
from supply.proto.supply_pb2_grpc import supplyServicer
from supply.utils import get_user_info
from supply.utils.enums import Demand_sub_type, Demand_type
from supply.utils.helper import (get_today_datetime,
                                 handle_list_model_to_message,
                                 handle_model_to_message)
from supply.model.doc_plan.doc_plan import doc_plan_repository
from supply.client.receipt_service import receipt_service

class DemandApi(OauthGrpcAPI, supplyServicer):
    @staticmethod
    def parse_time(time_str):
        # 时间格式化接口 2019-07-07 19:00:00 自定义接口速度比datetime.strptime快很多
        year_s, mon_s, day_s, hour_s, min_s, sec_s = re.split(r"[-:\s]\s*", time_str)
        return datetime(int(year_s), int(mon_s), int(day_s), int(hour_s), int(min_s), int(sec_s))

    @exclude_oauth
    def Ping(self, req, ctx):
        logger.info("ping_request")
        return Pong(pong="success")

    def TestMethod(self, req, ctx):
        print("TestMethod: ", req)
        print("vendor: " + str(Redis_cli.get_vendor_center_code(4175956680447623169, partner_id=4183192445833445399,
                                                                user_id=4186056888460247154)))

    def ActDemandBatchStore(self, req, ctx):
        '''
        夜间生成门市订货单批次 (api/v1/supply/demand/store POST)
        '''
        bizdt = datetime.fromtimestamp(req.bizdt.seconds)
        bizdt = datetime(year=bizdt.year, month=bizdt.month, day=bizdt.day)
        partner_id, user_id = get_user_info(ctx)
        if req.mode == "create":
            result = demand_module.create_demand_batch_store(bizdt, partner_id, user_id)
        if req.mode == "recover":
            result = demand_module.recover_demand_batch_store(bizdt, partner_id, user_id)
        return DemandBatch(**result.serialize(conv=True))

    def UpdateDemandProduct(self, req, ctx):
        """
        商品订货接口
        """
        # UpdateDemandProduct(UpdateDemandProductRequest) returns (UpdateDemandProductRequest)
        partner_id, user_id = get_user_info(ctx)
        product_info = req.product
        demand_id = req.demand_id
        order_date = datetime.fromtimestamp(req.order_date.seconds)
        if order_date.year == 1970:
            order_date = get_today_datetime()
        demand_module.update_or_insert_demand_product(product_info, demand_id, partner_id, user_id, order_date)
        return req

    # ListDemand(QueryDemandRequest) returns (QueryDemandResponse){
    def ListDemand(self, req, ctx):
        """
        查询门店订货单
        """
        partner_id, user_id = get_user_info(ctx)
        store_ids = req.store_ids
        has_product = req.has_product
        start_date = datetime.fromtimestamp(req.start_date.seconds)
        end_date = datetime.fromtimestamp(req.end_date.seconds)
        type = req.type
        sub_type = req.sub_type
        store_type = req.store_type
        status = req.status
        offset = req.offset
        limit = req.limit
        codes = req.codes
        is_plan = req.is_plan
        is_adjust = req.is_adjust
        order = req.order
        sort = req.sort
        # 根据计划名称取doc_plan表中查出doc id列表
        plan_name = req.plan_name
        plan_ids = []
        if plan_name:
            plans = doc_plan_repository.list_doc_plan(plan_name=plan_name, partner_id=partner_id, user_id=user_id)
            plan_ids = [int(plan.id) for plan in plans]
        if not sort:
            sort = 'updated_at'
        rows, total = Supply_demand.get_demand_list_by_args(
            partner_id, user_id, store_ids, has_product, start_date, end_date, type, sub_type, store_type, status,
            codes, is_plan, is_adjust, offset, limit, order, sort, plan_ids)
        demand_ids = [i.id for i in rows]
        products_count = Supply_demand.get_product_count_by_ids(demand_ids, partner_id)
        rows = [item.serialize(conv=True) for item in rows]
        result = []
        for i in rows:
            i["product_count"] = products_count.get(i.get("id"), 0)
            result.append(i)
        result = handle_list_model_to_message(result, DemandEntity)
        return QueryDemandResponse(rows=result, total=total)

    # rpc GetDemandProductDetail(IdRequest) returns (QueryDemandProductResponse)
    def GetDemandProductDetail(self, req, ctx):
        """
        获取订货商品
        """
        partner_id, user_id = get_user_info(ctx)
        demand_id = req.id
        products, total = demand_module.get_demand_product(demand_id, partner_id, user_id)
        if not products:
            return QueryDemandProductResponse()
        need_field = [f.name for f in QueryDemandProductEntity.DESCRIPTOR.fields]
        rows = []
        for product in products:
            item = {key: value for (key, value) in product.items() if key in need_field}
            rows.append(QueryDemandProductEntity(**item))
        return QueryDemandProductResponse(rows=rows, total=total)

    # rpc ConfirmDemandMain(IdRequest) returns (Response)
    def ChangeDemandStatus(self, req, ctx):
        """
        修改订货单状态
        """
        demand_id = req.id
        status = req.status
        description = req.description
        partner_id, user_id = get_user_info(ctx)

        demand_module().change_demand_status(demand_id, status, description, partner_id, user_id)
        return Response(description="success")

    # rpc CreateDemandMain (DemandMainRequest) returns (DemandEntity)
    def CreateDemandMain(self, req, ctx):
        """
        创建门店紧急订货单和门店主配单
        """
        partner_id, user_id = get_user_info(ctx)
        object_id = req.object_id
        demand_date = datetime.fromtimestamp(req.demand_date.seconds)
        arrival_date = datetime.fromtimestamp(req.arrival_date.seconds)
        remark = req.remark
        type = req.type
        sub_type = req.sub_type
        bus_type = req.bus_type
        if not demand_date or not arrival_date:
            raise DataValidationException("请填写订货日期或者到货日期")
        if demand_date.year == 1970 or arrival_date.year == 1970:
            raise DataValidationException("请填写订货日期或者到货日期")

        if type == Demand_type.HD.code:
            # 门店紧急订货单，限制每天17:00:00-24:00:00不能新建订货单
            # 获取当前时间
            now_time = datetime.utcnow()
            # 设置偏移量次日凌晨一点
            offset = timedelta(days=1)
            time1 = self.parse_time(str(datetime.utcnow().date()) + ' 15:00:00')
            time2 = self.parse_time(str(datetime.utcnow().date() + offset) + ' 00:00:00')
            # 如果下订单时间在指定时间范围内提示不能订货
            if time1 < now_time < time2:
                print("每天15:00:00-24:00:00不能订货")
                raise DataValidationException("每天15:00:00-24:00:00不能订货")

            product_info = [
                {"product_id": item.object_by, "quantity": item.quantity, "distribution_type": item.distribution_type}
                for item in req.item_ids]
            demand = demand_module.create_urgent_demand(object_id, bus_type, product_info, demand_date, arrival_date,
                                                        remark, user_id, partner_id)
        # elif type == Demand_type.MD.code and sub_type == Demand_sub_type.PRODUCT.code:
        #     # 商品主配
        #     store_ids = [{"receive_by":item.object_by, "quantity":item.quantity, "distribution_type":item.distribution_type} for item in req.item_ids]
        #     demand = Supply_demand(id=1)
        #     demand_module.create_demand_store(demand_date, arrival_date, object_id, store_ids, remark, user_id, partner_id)
        elif type == Demand_type.MD.code and sub_type == Demand_sub_type.STORE.code:
            # 门店主配(门店加急)
            product_info = [
                {"product_id": item.object_by, "quantity": item.quantity, "distribution_type": item.distribution_type}
                for item in req.item_ids]
            demand = demand_module.create_md_store_demand(object_id, product_info, demand_date, arrival_date, remark,
                                                          user_id, partner_id)
        else:
            raise DemandTypeError("订货单类型错误")
        return IdRequest(id=demand.id)

    def CreateProductMain(self, req, ctx):
        """
        创建商品主配单
        """
        partner_id, user_id = get_user_info(ctx)
        demand_date = datetime.fromtimestamp(req.demand_date.seconds)
        arrival_date = datetime.fromtimestamp(req.arrival_date.seconds)
        remark = req.remark
        type = req.type
        sub_type = req.sub_type
        batch_id = req.batch_id
        if not demand_date or not arrival_date:
            raise DataValidationException("请填写订货日期或者到货日期")
        if demand_date.year == 1970 or arrival_date.year == 1970:
            raise DataValidationException("请填写订货日期或者到货日期")
        if type == Demand_type.MD.code and sub_type == Demand_sub_type.PRODUCT.code:
            # 商品主配
            items = [{"store_id": item.store_id,
                      "product_items": [
                          {"product_id": i.product_id, "quantity": i.quantity, "distribution_type": i.distribution_type,
                           "distribution_by": i.distribution_by}
                          for i in item.product_items]}
                     for item in req.items]
            demand_module().create_product_main(items, demand_date, arrival_date, remark, user_id, partner_id, batch_id, type)
        else:
            raise DemandTypeError("订货单类型错误")
        return IdRequest(id=1)

    # rpc GetDemandDetail (IdRequest) returns (DemandEntity)
    def GetDemandDetail(self, req, ctx):
        """
        获取订货单详细
        """
        demand_id = req.id
        partner_id, user_id = get_user_info(ctx)
        demand = demand_module.get_demand_detail(demand_id, partner_id, user_id)
        # return DemandEntity(demand.serialize(conv=True))
        return handle_model_to_message(demand, DemandEntity)

    # ExportDemandMain(ExportDemandMainRequest) returns (ExportDemandMainResponse)
    def ExportDemandMain(self, req, ctx):
        """
        导出订货单明细
        """
        type = req.type
        sub_type = req.sub_type
        start_date = datetime.fromtimestamp(req.start_date.seconds)
        end_date = datetime.fromtimestamp(req.end_date.seconds)
        status = req.start_date
        partner_id, user_id = get_user_info(ctx)
        demand_module.export_demand_product(partner_id, user_id, type, sub_type, start_date, end_date, status)

    # rpc ResetDemandMain(IdRequest) returns (Response){
    def ResetDemandMain(self, req, ctx):
        """
        重置门店订单
        """
        demand_id = req.id
        partner_id, user_id = get_user_info(ctx)
        demand_module.reset_demand(partner_id, user_id, demand_id)
        return Response(description="success")

    # DeleteDemandMain
    def DeleteDemandMain(self, req, ctx):
        """
        删除订货单
        """
        demand_id = req.id
        partner_id, user_id = get_user_info(ctx)
        demand_module.delete_demand(demand_id, partner_id, user_id)
        return Response(description="success")

    # CheckAndHandleDemand
    def CheckAndHandleDemand(self, req, ctx):
        """
        夜间检查订货单，生成要货单
        """
        bizdt = datetime.fromtimestamp(req.bizdt.seconds)
        partner_id, user_id = get_user_info(ctx)
        demand_module.handle_demand_to_order(bizdt, partner_id, user_id)
        return Response(description="success")

    def GetValidProductByStoreId(self, req, ctx):
        """
        根据门店id查询可订货商品
        """
        partner_id, user_id = get_user_info(ctx)
        store_id = req.id
        type = req.type
        distribution_type = req.distribution_type
        vendor_id = req.vendor_id
        order_date = datetime.fromtimestamp(req.order_date.seconds)
        category_ids = list(req.category_ids)
        order_by = req.order_by
        if order_date.year == 1970:
            order_date = get_today_datetime()

        valid_product, total = demand_module.get_valid_demand_product(store_id, partner_id, user_id, distribution_type,
                                                                      type=type, order_date=order_date,
                                                                      vendor_id=vendor_id, category_ids=category_ids,
                                                                      order_by=order_by)
        return QueryDemandProductResponse(rows=handle_list_model_to_message(valid_product, QueryDemandProductEntity),
                                          total=total)

    def UpdateDemandInfo(self, req, ctx):
        """
        更新订单的remark
        """
        demand_id = req.id
        remark = req.remark
        arrival_date = datetime.fromtimestamp(req.arrival_date.seconds)
        partner_id, user_id = get_user_info(ctx)
        demand_module.update_demand_info(demand_id, remark, arrival_date, partner_id, user_id)
        return Response(description="success")


    # DeleteDemandProduct(DeleteDemandProductRequest) returns (Response) 删除订货商品
    def DeleteDemandProduct(self, req, ctx):
        partner_id, user_id = get_user_info(ctx)
        demand_id = req.demand_id
        demand_product_ids = req.demand_product_ids
        demand = Supply_demand.get(demand_id)
        if demand.partner_id != partner_id:
            raise DemandBelongError("此订单不属于该操作员所属商户")
        if demand.type == Demand_type.SD.code:
            raise DemandTypeError("门市订货单不能删除订货商品")

        Supply_demand_product.delete_by_ids(demand_product_ids)
        return Response(description="success")

    def ListFirstDelivery(self, req, ctx):
        partner_id, user_id = get_user_info(ctx)
        store_code = req.store_code
        product_code = req.product_code
        start_order_date = datetime.fromtimestamp(req.start_order_date.seconds)
        end_order_date = datetime.fromtimestamp(req.end_order_date.seconds)
        # if not store_code or not product_code or not order_date:
        #     raise DemandError("查询首配商品参数错误, 请传入: store_code, product_code, order_date")
        if start_order_date.year == 1970 or end_order_date.year == 1970:
            raise DemandError("请传入起始和结束日期")
        limit = req.limit
        offset = req.offset
        order = req.order
        rows, total = Supply_first_delivery.get_first_delivery_list(store_code, product_code, start_order_date,
                                                                    end_order_date, limit, offset, order, partner_id,
                                                                    user_id)
        rows = handle_list_model_to_message([item.serialize(conv=True) for item in rows],
                                            ListFirstDeliveryResponse.FirstDelivery)
        return ListFirstDeliveryResponse(rows=rows, total=total)

    def GetValidStoreByProductId(self, req, ctx):
        partner_id, user_id = get_user_info(ctx)
        product_ids = req.product_ids
        branch_ids = list(req.branch_ids)
        category_ids = list(req.category_ids)
        type = req.type
        order_date = datetime.fromtimestamp(req.order_date.seconds)
        rows, total = demand_module.get_valid_store_product(product_ids, type, partner_id, user_id, order_date,
                                                            branch_ids, category_ids)
        return GetValidStoreByProductIdResponse(rows=rows)


    # 导入总部帮订
    def UploadDemandMaster(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().upload_demand_master(request, partner_id, user_id)
        return UploadDemandMasterResponse(**ret)

    # 获取总部帮订导入记录
    def GetDemandMasterUpload(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().get_demand_master_upload(request, partner_id, user_id)
        return GetDemandMasterUploadResponse(**ret)

    # 获取总部帮订导入记录详情
    def GetDemandMasterUploadByBatchId(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().get_demand_master_upload_by_batch_id(request, partner_id)
        return GetDemandMasterUploadByBatchIdResponse(**ret)

    # 审核总部帮订导入记录开始干活
    def ApproveDemandMasterUpload(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().approve_demand_master_upload_by_batch_id(request, partner_id, user_id)
        return ApproveDemandMasterUploadResponse(**ret)

    # 取消该次导入
    def CancelDemandMasterUpload(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().cancel_demand_master_upload_by_batch_id(request, partner_id, user_id)
        return CancelDemandMasterUploadResponse(**ret)

    def ListForecastStore(self, req, context):
        partner_id, user_id = get_user_info(context)

        if req.forecast_date and req.forecast_date.seconds != 0:
            forecast_date = datetime.fromtimestamp(req.forecast_date.seconds).replace(hour=0, minute=0, second=0,
                                                                                      microsecond=0)
        else:
            forecast_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

        limit = req.limit or 20
        offset = req.offset or 0
        store_ids = req.store_ids
        forecast_stores, count = sale_forecast.list_forecast_store(
            partner_id, user_id, forecast_date, store_ids, limit, offset,
            store_type=req.store_type,
        )

        pb_forecast_stores = []
        for s in forecast_stores:
            pb_forecast_stores.append(supply_pb2.ListForecastStoreResponse.ForecastStore(
                store_id=s['store_id'],
                updated_by=s['updated_by'],
                forecast_date=Timestamp(seconds=int(s['forecast_date'].timestamp())),
                updated_at=Timestamp(seconds=int(s['updated_at'].timestamp())),
            ))
        resp = supply_pb2.ListForecastStoreResponse(total=count, forecast_stores=pb_forecast_stores)
        return resp

    def ListProductSaleForecast(self, req, context):
        partner_id, user_id = get_user_info(context)
        store_id = req.store_id
        order = req.order
        sort = req.sort
        if not sort:
            sort = 'updated_at'
        if req.forecast_date and req.forecast_date.seconds != 0:
            forecast_date = datetime.fromtimestamp(req.forecast_date.seconds).replace(hour=0, minute=0, second=0,
                                                                                      microsecond=0)
        else:
            forecast_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        forecast_data, count = sale_forecast.list_forecast_product(
            partner_id, user_id, forecast_date, store_id, order, sort,
            product_sale_type=req.product_sale_type
        )

        sale_forecasts = []
        for p_id, f in forecast_data.items():
            forecast_infos = []
            for date, data in f.items():
                forecast_infos.append(
                    supply_pb2.ListProductSaleForecastResponse.ForecastInfo(
                        forecast_amount=data['forecast_amount'],
                        confirm_amount=data['confirm_amount'],
                        forecast_date=Timestamp(seconds=int(date.timestamp())),
                        id=data['id']
                    )
                )
            sale_forecasts.append(
                supply_pb2.ListProductSaleForecastResponse.ProductSaleForecast(
                    product_id=p_id,
                    forecast_infos=forecast_infos
                )
            )
        return supply_pb2.ListProductSaleForecastResponse(total=count, sale_forecasts=sale_forecasts)

    def ConfirmProductSaleForecast(self, request, context):
        partner_id, user_id = get_user_info(context)

        confirm_data = {}
        for pb_info in request.forecast_infos:
            confirm_data[pb_info.id] = pb_info.confirm_amount

        if confirm_data:
            try:
                sale_forecast.confirm_product_forecast(partner_id, user_id, confirm_data)
            except Exception as e:
                print(traceback.format_exc())
                return supply_pb2.ConfirmProductSaleForecastResponse(success=False)
        return supply_pb2.ConfirmProductSaleForecastResponse(success=True)

    def GetProductSaleForecast(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().get_product_sale_forecast(request, partner_id, user_id)
        res = GetProductSaleForecastResponse()
        res.map_field.update(ret)
        return res

    # 获取门店可调整商品
    def GetDemandAdjustProductByStoreId(self, request, context):
        """
        根据门店id查询可订货商品
        """
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        type = request.type
        distribution_type = request.distribution_type
        vendor_id = request.vendor_id
        order_date = datetime.fromtimestamp(request.order_date.seconds)
        category_ids = list(request.category_ids)
        if order_date.year == 1970:
            order_date = get_today_datetime()
        rows, total = demand_module().get_demand_adjust_product(store_id, partner_id, user_id,
                                                                distr_type=distribution_type,
                                                                type=type, order_date=order_date,
                                                                vendor_id=vendor_id, category_ids=category_ids)
        return GetDemandAdjustProductByStoreIdResponse(rows=rows, total=total)

    # 创建门店订货调整单
    def CreateDemandAdjust(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = demand_module().create_demand_adjust(request, partner_id, user_id)
        return CreateDemandAdjustResponse(result=result)

    # 获取订货单商品仓库
    def GetDistributionByDemand(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = demand_module().pre_generate_order_by_demand(request.id, partner_id, user_id)
        result = {"products": result}
        return GetDistributionByDemandResponse(**result)