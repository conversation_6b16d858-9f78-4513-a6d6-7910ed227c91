from supply.api import OauthGrpcAPI
from supply.proto.pos_stocktake_pb2_grpc import PosStocktakeServicer
from supply.proto.pos_stocktake_pb2 import UploadPosStocktakeResponse, GetStocktakeQuantityReponse
from supply.module.pos_stocktake import PosStocktake
from google.protobuf.json_format import MessageToDict
from supply.utils import get_user_info


class PosStocktakeApi(PosStocktakeServicer, OauthGrpcAPI):

    def UploadPosStocktake(self, request, context):
        partner_id, user_id = get_user_info(context)
        if request.status not in ['ENABLED', 'DISABLED'] or request.sync_type not in ['STATUS', 'STOCKTAKE']:
            return UploadPosStocktakeResponse()

        result = PosStocktake().upload_pos_data(partner_id, user_id,
                                                **MessageToDict(request, preserving_proto_field_name=True,
                                                                including_default_value_fields=True))

        return UploadPosStocktakeResponse(result=result)

    def GetStocktakeQuantity(self, request, context):
        partner_id, user_id = get_user_info(context)
        return GetStocktakeQuantityReponse(**PosStocktake().get_pos_data(partner_id, request.store_code))