# -*- coding: utf8 -*-
import traceback
import re
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp

from supply import logger
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.module.demand import demand_module
from supply.proto.warehouse_management.inventory_adjust_pb2 import GetDemandAdjustProductByBranchIdResponse,\
    Response
from supply.proto.warehouse_management.inventory_adjust_pb2_grpc import WarehouseInventoryAdjustServiceServicer
from supply.utils import get_user_info
from supply.utils.helper import  get_today_datetime
from supply.utils.auth import authorization_scope, branch_scope_check



# 仓库运营管理-库存调整
class WarehouseInventoryAdjustApi(OauthGrpcAPI, WarehouseInventoryAdjustServiceServicer):

    # 获取门店可调整商品
    @authorization_scope('boh.warehouse_mangement.inventory_adjust.view')
    def GetDemandAdjustProductByBranchId(self, request, context):
        """
        根据门店id查询可订货商品
        """
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        type = request.type
        distribution_type = request.distribution_type
        vendor_id = request.vendor_id
        order_date = datetime.fromtimestamp(request.order_date.seconds)
        category_ids = list(request.category_ids)
        if order_date.year == 1970:
            order_date = get_today_datetime()
        branch_scope_check(partner_id, user_id, 'warehouse', 'boh.warehouse_mangement', store_id)
        rows, total = demand_module().get_demand_adjust_product(store_id, partner_id, user_id,
                                                                distr_type=distribution_type,
                                                                type=type, order_date=order_date,
                                                                vendor_id=vendor_id, category_ids=category_ids)
        return GetDemandAdjustProductByBranchIdResponse(rows=rows, total=total)

    # 创建门店订货调整单
    @authorization_scope('boh.warehouse_mangement.inventory_adjust.maintain')
    def CreateDemandAdjust(self, request, context):
        partner_id, user_id = get_user_info(context)
        demand_module().create_demand_adjust(request, partner_id, user_id)
        return Response(description='success')

    # 审核门店订货调整单
    @authorization_scope('boh.warehouse_mangement.inventory_adjust.audit')
    def DealDemandAdjustById(self, req, ctx):
        """
        修改订货单状态
        """
        demand_id = req.id
        status = req.status
        description = req.description
        partner_id, user_id = get_user_info(ctx)

        demand_module().change_demand_status(demand_id, status, description, partner_id, user_id)
        return Response(description="success")
