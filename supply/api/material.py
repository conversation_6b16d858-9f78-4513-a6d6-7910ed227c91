from ..proto.material_pb2_grpc import materialServicer
from ..proto.material_pb2 import *
from . import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info
from supply.module.material import material_service


class MaterialServicer(materialServicer, OauthGrpcAPI):

    # 查询分差报表
    def GetMaterialDifference(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = material_service.get_material_difference(request, partner_id, user_id)
        return GetMaterialDifferenceResponse(**ret)
