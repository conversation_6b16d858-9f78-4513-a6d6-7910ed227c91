# -*- coding: utf-8 -*-

from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, authorization_scopes, \
    branch_list_scope_check, two_branch_list_scope_check, branch_scope_check
from datetime import datetime

from supply.proto.frs_store_extra.return_pb2_grpc import FrsStoreExtraReturnServiceServicer
from supply.proto.frs_store_extra.return_pb2 import Returns, \
    ListReturnResponse, GetReturnProductByIdResponse, ApproveReturnResponse, \
    RejectReturnResponse, UpdateReturnResponse, HistoryResponse
from supply.module.returns import returns_service
from supply.module.franchisee.franchisee_returns import franchisee_returns_service

# 加盟门店运营-退货单
class FrsStoreExtraReturnsAPI(FrsStoreExtraReturnServiceServicer, OauthGrpcAPI):

    @authorization_scopes(['boh.frs_store_extra.return.view', 'boh.frs_store.return.view'])
    def ListReturn(self, request, context):
        partner_id, user_id = get_user_info(context)
        # request.return_by[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.frs_store_extra', branch_ids=request.return_by)
        # request.return_to[:] = branch_list_scope_check(partner_id, user_id, 'warehouse', 'boh.frs_store_extra', branch_ids=request.return_to)
        request.return_by[:], request.return_to[:] = two_branch_list_scope_check(
            partner_id, user_id, 'frs_store_extra', 'boh.frs_store_extra', request.return_by, request.return_to)
        count, return_list = returns_service.list_returns(partner_id, user_id, request)
        result = {'rows': return_list, 'total': count}
        return ListReturnResponse(**result)

    @authorization_scopes(['boh.frs_store_extra.return.view', 'boh.frs_store.return.view'])
    def GetReturnById(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        return_obj = franchisee_returns_service.get_return_by_id(return_id, partner_id, user_id)
        return Returns(**return_obj)

    @authorization_scopes(['boh.frs_store_extra.return.view', 'boh.frs_store.return.view'])
    def GetReturnProductById(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        limit = request.limit
        offset = request.offset
        sort = request.sort
        order = request.order
        result = franchisee_returns_service.get_return_product_by_id(return_id, partner_id, user_id)
        # result = {'rows': return_product_list, 'total': count}
        return GetReturnProductByIdResponse(**result)

    @authorization_scopes(['boh.frs_store_extra.return.audit'])
    def RejectReturn(self, request, context):
        return_id = request.id
        reject_reason = request.reject_reason
        partner_id, user_id = get_user_info(context)
        result = returns_service.reject_return(return_id, partner_id, user_id, reject_reason)
        result = {'payload': result}
        return RejectReturnResponse(**result)

    @authorization_scopes(['boh.frs_store.return.maintain'])
    def UpdateReturn(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        products = request.products
        return_reason = request.return_reason
        remark = request.remark
        return_delivery_date = request.return_delivery_date
        return_to = request.return_to
        attachments = request.attachments
        logistics_type = request.logistics_type
        result = returns_service.update_return_product_quantity(return_id, partner_id, user_id,
                                                                products, return_reason, remark,
                                                                return_delivery_date, return_to,
                                                                attachments, logistics_type)
        result = {'payload': result}
        return UpdateReturnResponse(**result)

    @authorization_scopes(['boh.frs_store_extra.return.audit', 'boh.frs_store_extra.return.whaudit'])
    def ApproveReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        trans_type = request.trans_type
        warehouse_type = request.warehouse_type
        long_effect = request.long_effect
        result = franchisee_returns_service.approve_return(return_id=return_id, partner_id=partner_id, \
                                                user_id=user_id, trans_type=trans_type, warehouse_type=warehouse_type,
                                                           long_effect=long_effect)
        result = {'payload': result}
        return ApproveReturnResponse(**result)

    @authorization_scopes(['boh.frs_store_extra.return.view', 'boh.frs_store.return.view'])
    def GetHistoryById(self, request, context):
        """查询退货单操作历史"""
        partner_id, user_id = get_user_info(context)
        history, total = returns_service.get_history(partner_id, user_id, request)
        history_res = {'rows':history, 'total':total}
        return HistoryResponse(**history_res)