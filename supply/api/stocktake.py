# -*- coding:utf-8 -*-
import json

from ..module.stocktake_service import ssm as stocktake_service
from ..error.exception import *
from ..proto.stocktake_pb2_grpc import stocktakeServicer
from ..proto.stocktake_pb2 import *
from supply.api import OauthGrpcAPI, get_context_dict
from supply.utils import get_user_info
from ..client.metadata_service import metadata_service
# from ..task import public
from supply.driver.Redis import Redis_cli
from google.protobuf.timestamp_pb2 import Timestamp

from ..proto.third_party.stocktake_pb2_grpc import TpStocktakeServicer
from ..proto.third_party.stocktake_pb2 import GetStocktakeBriefResponse


class StocktakeServicer(OauthGrpcAPI, stocktakeServicer):

    # //CheckStocktakeByDocID 检查能否确认盘点单（status=CHECKED）14
    def CheckStocktakeByDocID(self, request, context):
        doc_id = request.doc_id
        branch_type = request.branch_type
        partner_id, user_id = get_user_info(context)
        res = stocktake_service.check_confirm_stocktake(doc_id=doc_id, partner_id=partner_id,
                                                        user_id=user_id, branch_type=branch_type)
        return CheckStocktakeByDocIDResponse(**res)

    # //ConfirmStocktakeByDocID 确认盘点单（status=CONFITRMED）15 该api已废弃
    def ConfirmStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        check = request.check
        check_data = stocktake_service.check_confirm_stocktake(doc_id, user_id, check)
        if isinstance(check_data, dict) and 'handler' in check_data and check_data['handler']:
            res['result'] = stocktake_service.confirm_stocktake(doc_id=doc_id, user_id=user_id, partner_id=partner_id,
                                                                username=username)
            return ConfirmStocktakeByDocIDResponse(**res)
        else:
            raise DataValidationException('there is no confirmation of the application form')

    # //ApproveStocktakeByDocID  财务确认盘点单（status=APPROVED，核算完库存后status=）16
    def ApproveStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        approve_name = str(request.approve_name).strip()
        # 不要了审核人，后面如果没需要就删掉
        # if not approve_name or approve_name == 'None':
        #     raise StockTakePermissionException("未填写审核人！")
        # type = request.type
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            pass
        else:
            raise DataValidationException('无此单据')
        attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                         domain='boh.store.stocktake',
                                                                         user_id=user_id).get(
            'attachment_required_control')
        attachments = json.loads(doc_obj.attachments).get("attachments") if doc_obj.attachments else None
        ctx = get_context_dict(context)
        if attachment_required and not attachments and not ctx.get('from_grpc'):
            raise DataValidationException("缺少附件信息, 请保存附件信息")
        res = stocktake_service.approve_stocktake(doc_id, partner_id, user_id, username, approve_name)
        return ApproveStocktakeByDocIDResponse(**res)

    # //RejectStocktakeProduct 财务驳回（status=REJECTED，部分商品status=REJECTED）16
    def RejectStocktakeProduct(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        reason = request.reason
        res['result'] = stocktake_service.reject_stocktake(doc_id, partner_id, user_id, username, reason)
        return RejectStocktakeProductResponse(**res)

    # //CancelStocktakeByDocID  作废盘点单（status=CANCELED）18
    def CancelStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        res['result'] = stocktake_service.cancel_stocktake(doc_id, partner_id, user_id)
        return CancelStocktakeByDocIDResponse(**res)

    # //GetStocktakeByDocID 获取一个盘点单19
    def GetStocktakeByDocID(self, request, context):
        props = {}
        doc_id = request.doc_id
        partner_id, user_id = get_user_info(context)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, is_details=True)
        if doc_obj:
            if doc_obj.attachments:
                attachments = json.loads(doc_obj.attachments)
                doc_obj.attachments = attachments.get('attachments', [])
            props = doc_obj.props()
            if 'doc_id' in props:
                props['id'] = props['doc_id']
                del props['doc_id']
        return Stocktake(**props)

    # 查询盘点单20
    def GetStocktake(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_obj_list = stocktake_service.list_stocktake_doc_detail(request, partner_id=partner_id,
                                                                   user_id=user_id)
        total = None
        if isinstance(doc_obj_list, tuple):
            total, doc_obj_list = doc_obj_list
        res['rows'] = doc_obj_list
        res['total'] = total
        return GetStocktakeResponse(**res)

    # //GetStocktakeProduct 查询盘点单明细商品21
    def GetStocktakeProduct(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        include_unit = request.include_unit
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        category_id = request.category_id
        storage_type = request.storage_type
        product_name = request.product_name
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id)
        products = stocktake_service.list_stocktake_product(doc_id=doc_id, include_unit=include_unit,
                                                            limit=limit, offset=offset,
                                                            include_total=include_total,
                                                            category_id=category_id, storage_type=storage_type,
                                                            product_name=product_name,
                                                            partner_id=partner_id, user_id=user_id,
                                                            branch_id=doc_obj.branch_id)
        total, products = products
        res['total'] = total
        res['position_rows'] = products
        return GetStocktakeProductResponse(**res)

    # //PutStocktakeByDocID 更新盘点单 22
    def PutStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        products = request.products
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        attachments = []
        if request.attachments:
            for t in request.attachments:
                attachments.append(dict(
                    type=t.type,
                    url=t.url
                ))
        # attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
        #                                                                  domain='boh.store.stocktake',
        #                                                                  user_id=user_id).get(
        #     'attachment_required_control')
        # if attachment_required and not attachments:
        #     raise DataValidationException("缺少附件信息, 请传入附件信息")
        res['result'] = stocktake_service.update_stocktake_products_with_quantity(doc_id, products,
                                                                                  allow_status=['STARTED',
                                                                                                'REJECTED'],
                                                                                  partner_id=partner_id,
                                                                                  user_id=user_id,
                                                                                  username=username,
                                                                                  branch_id=doc_obj.branch_id,
                                                                                  attachments=attachments)

        return PutStocktakeByDocIDResponse(**res)

    # 检查更新盘点单状态
    def CheckedStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        branch_type = request.branch_type
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        res['result'] = stocktake_service.checked_stocktake(doc_id=doc_id, branch_type=branch_type,
                                                            partner_id=partner_id, user_id=user_id,
                                                            username=username)
        return CancelStocktakeByDocIDResponse(**res)

    # 获取盘点商品标签
    def GetStocktakeTags(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        branch_ids = request.branch_ids
        tag_name = request.tag_name
        res['rows'] = stocktake_service.get_stocktake_tags(branch_ids=branch_ids, tag_name=tag_name,
                                                           partner_id=partner_id, user_id=user_id)
        return GetStocktakeTagsResponse(**res)

    # ActionStocktakeTags增加，删除，更新,获取盘点标签25
    def ActionStocktakeTags(self, request, context):
        '''
        :param request:
        :param context:
        :return:
        action:
         create = 1;
         delete = 2;
         update = 3;
        '''
        partner_id, user_id = get_user_info(context)
        action = request.action
        name = request.name
        origin_name = request.origin_name
        branch_ids = request.branch_ids
        region_ids = request.region_ids
        add_dimension = request.add_dimension
        tag_ids = request.tag_ids
        branch_id = request.branch_id  # 给复制用
        copy_branch = request.copy_branch
        tags = stocktake_service.action_stocktake_tags(name=name, action=action,
                                                       branch_ids=branch_ids, region_ids=region_ids,
                                                       branch_id=branch_id, copy_branch=copy_branch,
                                                       add_dimension=add_dimension, origin_name=origin_name,
                                                       tag_ids=tag_ids, partner_id=partner_id, user_id=user_id)
        ret = {}
        if action in [1, 2, 3, 4]:
            if tags:
                ret['result'] = True
        else:
            if tags:
                ret = tags.props()
                ret['result'] = True
        return ActionStocktakeTagsResponse(**ret)

    # 根据条目ID列表删除盘点商品标签条目
    def DeleteStocktakeProductTags(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        res['result'] = stocktake_service.clean_stocktake_product_tags(request, partner_id)
        return DeleteStocktakeProductTagsResponse(**res)

    # //SubmitStocktakeByDocID 提交盘点单28
    def SubmitStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        submit_name = str(request.submit_name).strip()
        # 提交人不要了，后续如无需要彻底删掉即可
        # if not submit_name or submit_name == 'None':
        #     raise StockTakePermissionException("未填写提交人！")
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            pass
        else:
            raise DataValidationException('无此单据')
        attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                         domain='boh.store.stocktake',
                                                                         user_id=user_id).get(
            'attachment_required_control')
        attachments = json.loads(doc_obj.attachments).get("attachments") if doc_obj.attachments else None
        ctx = get_context_dict(context)
        if attachment_required and not attachments and not ctx.get('from_grpc'):
            raise DataValidationException("缺少附件信息, 请保存附件信息")
        source = request.source
        res['result'] = stocktake_service.submit_stocktake(doc_id=doc_id, user_id=user_id, partner_id=partner_id,
                                                           username=username, branch_type=doc_obj.branch_type,source=source)
        return SubmitStocktakeByDocIDResponse(**res)

    # //GetStocktakeBalance盘点单损益报表27
    def GetStocktakeBalance(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_obj_list = stocktake_service.get_stocktake_balance(request, partner_id, user_id)
        total = None
        if isinstance(doc_obj_list, tuple):
            total, doc_obj_list = doc_obj_list
        res['rows'] = doc_obj_list
        res['total'] = total
        return GetStocktakeBalanceResponse(**res)

    # //GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29
    def GetStocktakeBalanceProductGroup(self, request, context):
        res = {}
        doc_id = request.doc_id
        partner_id, user_id = get_user_info(context)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id=doc_id, is_details=True)
        if doc_obj:
            pass
        else:
            raise DataValidationException('无此单据')
        res['rows'] = stocktake_service.get_st_balance_product_group_by_doc_id(doc_id, partner_id, user_id)
        return GetStocktakeBalanceProductGroupResponse(**res)

    def StocktakeBiDetailed(self, request, context):
        """盘点单报表"""
        res = {}
        include_total = request.include_total
        partner_id, user_id = get_user_info(context)
        data, total, sum_quantity, sum_accounting_quantity = stocktake_service.stocktake_bi_detailed(request,
                                                                                                     partner_id,
                                                                                                     user_id)
        if include_total:
            if data is None:
                return StocktakeBiDetailedResponse(**res)
            res['total'] = {}
            res['total']['count'] = total
            res['total']['sum_quantity'] = sum_quantity
            res['total']['sum_accounting_quantity'] = sum_accounting_quantity
            res['rows'] = data
            return StocktakeBiDetailedResponse(**res)
        else:
            if data is None:
                return StocktakeBiDetailedResponse(**res)
            res['rows'] = data
            return StocktakeBiDetailedResponse(**res)

    # //StocktakeBalanceRegion区域盘点31
    def StocktakeBalanceRegion(self, request, context):
        res = {}
        include_total = request.include_total
        partner_id, user_id = get_user_info(context)
        data = stocktake_service.get_st_balance_product_group(request, partner_id, user_id)
        if include_total:
            if data is None:
                return StocktakeBalanceRegionResponse(**res)
            res['total'] = len(data['rows'])
            res['rows'] = data['rows']
            return StocktakeBalanceRegionResponse(**res)
        else:
            if data is None:
                return StocktakeBalanceRegionResponse(**res)
            res['rows'] = data
            return StocktakeBalanceRegionResponse(**res)

    # 专门给前端用的方法
    def GetStoreScope(self, request, context):
        partner_id, user_id = get_user_info(context)
        search = request.search
        search_fields = request.search_fields
        limit = request.limit
        offset = request.offset
        ret = {}
        store_scope_ids = Redis_cli.get_store_data_scope(partner_id, user_id)

        ids = []
        if request.ids:
            ids = list(request.ids)
        # 防止主档增加冗余字段报错，一定要加return_fields
        return_fields = request.return_fields
        if not return_fields:
            return_fields = 'id,code,name'
        filters = request.filters
        if filters:
            filters = json.loads(filters)
        relation_filters = request.relation_filters
        if relation_filters:
            relation_filters = json.loads(relation_filters)
        # store_scope_ids=metadata_service.get_store_scope(partner_id, user_id)
        # print('store_scope_ids',store_scope_ids)
        if store_scope_ids == "full":
            if ids == []:
                ids = None
            store_ret = metadata_service.get_store_list(search=search,
                                                        search_fields=search_fields,
                                                        return_fields=return_fields,
                                                        ids=ids,
                                                        relation_filters=relation_filters,
                                                        filters=filters,
                                                        include_total=True,
                                                        limit=limit,
                                                        offset=offset,
                                                        partner_id=partner_id,
                                                        user_id=user_id,
                                                        )
        elif not store_scope_ids:
            return StoreDataScope(**ret)
        else:
            store_ids = [int(store_id) for store_id in store_scope_ids]
            final_ids = []
            if len(ids) > 0:
                for id in ids:
                    if int(id) in store_ids:
                        final_ids.append(id)
            else:
                final_ids = store_ids
            if final_ids == []:
                return StoreDataScope(**ret)
            store_ret = metadata_service.get_store_list(
                ids=final_ids,
                search=search,
                search_fields=search_fields,
                return_fields=return_fields,
                relation_filters=relation_filters,
                filters=filters,
                include_total=True,
                limit=limit,
                offset=offset,
                partner_id=partner_id,
                user_id=user_id,
            )
        if store_ret:
            ret['rows'] = store_ret.get('rows')
            ret['total'] = store_ret.get('total')
        return StoreDataScope(**ret)

    def AdvanceStocktakeDiff(self, request, context):
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        ret = stocktake_service.get_advance_stocktake_products_diff(doc_id, partner_id, user_id)
        return AdvanceStocktakeDiffResponse(**ret)

    def StocktakeDiffReport(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.get_stocktake_diff_report(request, partner_id, user_id)
        return StocktakeDiffReportResponse(**ret)

    def GetUncompleteDoc(self, request, context):
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        end_date = request.end_date
        timestamp = Timestamp()
        timestamp.seconds = end_date.seconds
        end_date = timestamp.ToDatetime()
        ret = stocktake_service.check_confirm_stocktake(partner_id=partner_id, user_id=user_id,
                                                        end_date=end_date, store_id=store_id,
                                                        is_home_page=True,
                                                        )
        return GetUncompleteDocResponse(**ret)

    # 重盘接口
    def RecreateStocktakeDoc(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.recreate_stocktake_doc(request, partner_id, user_id)
        return RecreateStocktakeDocResponse(**ret)

    def StocktakeDocStatistics(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.stocktake_doc_statistics(request, partner_id, user_id)
        return StocktakeDocStatisticsResponse(**ret)

    def StocktakeProductImport(self, request, context):
        """仓库/门店盘点单明细商品导入"""
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.stocktake_product_import(request, partner_id, user_id)
        return StocktakeProductImportResponse(**ret)

    def UpdateStocktakeImportBatch(self, request, context):
        """仓库/门店盘点单商品导入文件状态修改"""
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.update_stocktake_import_batch(request, partner_id, user_id)
        return UpdateStocktakeImportBatchResponse(**ret)


class tpStocktakeServicer(OauthGrpcAPI, TpStocktakeServicer):
    def GetStocktakeBrief(self, request, context):
        partner_id, user_id = get_user_info(context)
        res = stocktake_service.get_stocktake_breif(request=request, partner_id=partner_id, user_id=user_id)
    
        return GetStocktakeBriefResponse(**res)