# -*- coding: utf-8 -*-
from supply.proto.material_convert_pb2_grpc import MaterialConvertServicer
from supply.api import OauthGrpcAPI
from supply.utils import get_user_info
from supply.module.material_convert import material_convert_service
from supply.proto.material_convert_pb2 import CreateMaterialConvertResponse, ListMaterialConvertResponse, \
    GetMaterialConvertDetailResponse, UpdateMaterialConvertResponse


class MaterialConvertApi(MaterialConvertServicer, OauthGrpcAPI):
    """物料转换模块API"""
    def CreateMaterialConvert(self, request, context):
        """创建物料转换单"""
        partner_id, user_id = get_user_info(context)
        response = material_convert_service.create_material_convert(request, user_id=user_id, partner_id=partner_id)
        return CreateMaterialConvertResponse(**response)

    def ListMaterialConvert(self, request, context):
        """物料转换单列表查询"""
        partner_id, user_id = get_user_info(context)
        response = material_convert_service.list_material_convert(request, user_id=user_id, partner_id=partner_id)
        return ListMaterialConvertResponse(**response)

    def GetMaterialConvertDetail(self, request, context):
        """查询物料转换单详情"""
        partner_id, user_id = get_user_info(context)
        response = material_convert_service.get_material_convert_detail(request, user_id=user_id, partner_id=partner_id)
        return GetMaterialConvertDetailResponse(**response)

    def UpdateMaterialConvert(self, request, context):
        """更新物料转换单"""
        partner_id, user_id = get_user_info(context)
        response = material_convert_service.update_material_convert(request, user_id=user_id, partner_id=partner_id)
        return UpdateMaterialConvertResponse(**response)


