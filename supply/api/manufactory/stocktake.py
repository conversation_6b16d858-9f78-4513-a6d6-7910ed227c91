# -*- coding:utf-8 -*-
import json

from supply.module.stocktake_service import ssm as stocktake_service
from supply.utils.auth import branch_scope_check, branch_list_scope_check
from supply.error.exception import *
from supply.proto.manufactory.stocktake_pb2_grpc import ManufactoryStockTakeServicer
from supply.proto.manufactory.stocktake_pb2 import *
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.client.metadata_service import metadata_service
from google.protobuf.timestamp_pb2 import Timestamp


class MfyStocktakeApi(OauthGrpcAPI, ManufactoryStockTakeServicer):

    # 检查能否确认盘点单status=CHECKED
    # @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CheckStocktakeByDocID(self, request, context):
        doc_id = request.doc_id
        branch_type = request.branch_type
        partner_id, user_id = get_user_info(context)
        res = stocktake_service.check_confirm_stocktake(doc_id=doc_id, partner_id=partner_id,
                                                        user_id=user_id, branch_type=branch_type)
        return CheckStocktakeByDocIDResponse(**res)

    # 财务确认盘点单（status=APPROVED)
    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def ApproveStocktakeByDocID(self, request, context):
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        approve_name = str(request.approve_name).strip()
        stocktake_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        if stocktake_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory', branch_id=stocktake_obj.branch_id)
            
        res = stocktake_service.approve_stocktake(doc_id, partner_id, user_id, username, approve_name)
        return ApproveStocktakeByDocIDResponse(**res)

    # 财务驳回（status=REJECTED，部分商品status=REJECTED）
    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def RejectStocktakeProduct(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        reason = request.reason
        stocktake_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        if stocktake_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory', branch_id=stocktake_obj.branch_id)

        res['result'] = stocktake_service.reject_stocktake(doc_id, partner_id, user_id, username, reason)
        return RejectStocktakeProductResponse(**res)

    # 作废盘点单（status=CANCELED）
    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CancelStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory', domain='boh.manufactory',
                               branch_id=doc_obj.branch_id)

        res['result'] = stocktake_service.cancel_stocktake(doc_id, partner_id, user_id)
        return CancelStocktakeByDocIDResponse(**res)

    # 获取一个盘点单
    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetStocktakeByDocID(self, request, context):
        props = {}
        doc_id = request.doc_id
        partner_id, user_id = get_user_info(context)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory', branch_id=doc_obj.branch_id)
            if doc_obj.attachments:
                attachments = json.loads(doc_obj.attachments)
                doc_obj.attachments = attachments.get('attachments', [])
            props = doc_obj.props()
            if 'doc_id' in props:
                props['id'] = props['doc_id']
                del props['doc_id']
        return Stocktake(**props)

    # 查询盘点单
    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetStocktake(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                       domain='boh.manufactory', branch_ids=request.store_ids)
        doc_obj_list = stocktake_service.list_stocktake_doc_detail(request, partner_id=partner_id, user_id=user_id)
        total = None
        if isinstance(doc_obj_list, tuple):
            total, doc_obj_list = doc_obj_list
        res['rows'] = doc_obj_list
        res['total'] = total
        return GetStocktakeResponse(**res)

    # 查询盘点单明细商品
    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetStocktakeProduct(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        include_unit = request.include_unit
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        category_id = request.category_id
        storage_type = request.storage_type
        product_name = request.product_name
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory', branch_id=doc_obj.branch_id)
        products = stocktake_service.list_stocktake_product(doc_id=doc_id, include_unit=include_unit,
                                                            limit=limit, offset=offset,
                                                            include_total=include_total,
                                                            category_id=category_id, storage_type=storage_type,
                                                            product_name=product_name,
                                                            partner_id=partner_id, user_id=user_id,
                                                            branch_id=doc_obj.branch_id)
        total, products = products
        res['total'] = total
        res['position_rows'] = products
        return GetStocktakeProductResponse(**res)

    # 更新盘点单
    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def PutStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        products = request.products
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory', branch_id=doc_obj.branch_id)
        res['result'] = stocktake_service.update_stocktake_products_with_quantity(doc_id, products,
                                                                                  allow_status=['INITED',
                                                                                                'REJECTED'],
                                                                                  partner_id=partner_id,
                                                                                  user_id=user_id,
                                                                                  username=username,
                                                                                  branch_id=doc_obj.branch_id)

        return PutStocktakeByDocIDResponse(**res)

    # 检查更新盘点单状态
    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CheckedStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        branch_type = request.branch_type
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory', branch_id=doc_obj.branch_id)
        res['result'] = stocktake_service.checked_stocktake(doc_id=doc_id, branch_type=branch_type,
                                                            partner_id=partner_id, user_id=user_id,
                                                            username=username)
        return CancelStocktakeByDocIDResponse(**res)

    # 获取盘点商品标签
    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetStocktakeTags(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        branch_ids = request.branch_ids
        tag_name = request.tag_name
        branch_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                             domain='boh.manufactory', branch_ids=branch_ids)
        res['rows'] = stocktake_service.get_stocktake_tags(branch_ids=branch_ids, tag_name=tag_name,
                                                           partner_id=partner_id, user_id=user_id)
        return GetStocktakeTagsResponse(**res)

    # 根据条目ID列表删除盘点商品标签条目
    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def DeleteStocktakeProductTags(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        res['result'] = stocktake_service.clean_stocktake_product_tags(request, partner_id)
        return DeleteStocktakeProductTagsResponse(**res)

    # //SubmitStocktakeByDocID 提交盘点单28
    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def SubmitStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory', branch_id=doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        res['result'] = stocktake_service.submit_stocktake(doc_id=doc_id, user_id=user_id, partner_id=partner_id,
                                                           username=username, branch_type=doc_obj.branch_type)
        return SubmitStocktakeByDocIDResponse(**res)

    # //GetStocktakeBalance盘点单损益报表27
    @authorization_scope(permission_code="boh.manufactory_bi.detail.view")
    def GetStocktakeBalance(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                       domain='boh.manufactory_bi', branch_ids=request.store_ids)
        doc_obj_list = stocktake_service.get_stocktake_balance(request, partner_id, user_id)
        total = None
        if isinstance(doc_obj_list, tuple):
            total, doc_obj_list = doc_obj_list
        res['rows'] = doc_obj_list
        res['total'] = total
        return GetStocktakeBalanceResponse(**res)

    # //GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29
    @authorization_scope(permission_code="boh.manufactory_bi.detail.view")
    def GetStocktakeBalanceProductGroup(self, request, context):
        res = {}
        doc_id = request.doc_id
        partner_id, user_id = get_user_info(context)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory_bi', branch_id=doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        res['rows'] = stocktake_service.get_st_balance_product_group_by_doc_id(doc_id, partner_id, user_id)
        return GetStocktakeBalanceProductGroupResponse(**res)

    @authorization_scope(permission_code="boh.manufactory_bi.detail.view")
    def StocktakeBiDetailed(self, request, context):
        """盘点单报表"""
        res = {}
        include_total = request.include_total
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                       domain='boh.manufactory_bi', branch_ids=request.store_ids)
        data, total, sum_quantity, sum_accounting_quantity = stocktake_service.stocktake_bi_detailed(request,
                                                                                                     partner_id,
                                                                                                     user_id)
        if include_total:
            if data is None:
                return StocktakeBiDetailedResponse(**res)
            res['total'] = {}
            res['total']['count'] = total
            res['total']['sum_quantity'] = sum_quantity
            res['total']['sum_accounting_quantity'] = sum_accounting_quantity
            res['rows'] = data
            return StocktakeBiDetailedResponse(**res)
        else:
            if data is None:
                return StocktakeBiDetailedResponse(**res)
            res['rows'] = data
            return StocktakeBiDetailedResponse(**res)

    # //StocktakeBalanceRegion区域盘点31
    @authorization_scope(permission_code="boh.manufactory_bi.detail.view")
    def StocktakeBalanceRegion(self, request, context):
        res = {}
        include_total = request.include_total
        partner_id, user_id = get_user_info(context)
        # todo：区域权限校验
        data = stocktake_service.get_st_balance_product_group(request, partner_id, user_id)
        if include_total:
            if data is None:
                return StocktakeBalanceRegionResponse(**res)
            res['total'] = len(data['rows'])
            res['rows'] = data['rows']
            return StocktakeBalanceRegionResponse(**res)
        else:
            if data is None:
                return StocktakeBalanceRegionResponse(**res)
            res['rows'] = data
            return StocktakeBalanceRegionResponse(**res)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def AdvanceStocktakeDiff(self, request, context):
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                               domain='boh.manufactory', branch_id=doc_obj.branch_id)
        ret = stocktake_service.get_advance_stocktake_products_diff(doc_id, partner_id, user_id)
        return AdvanceStocktakeDiffResponse(**ret)

    def StocktakeDiffReport(self, request, context):
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                       domain='boh.manufactory_bi', branch_ids=request.store_ids)

        ret = stocktake_service.get_stocktake_diff_report(request, partner_id, user_id)
        return StocktakeDiffReportResponse(**ret)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetUncompleteDoc(self, request, context):
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        end_date = request.end_date
        timestamp = Timestamp()
        timestamp.seconds = end_date.seconds
        end_date = timestamp.ToDatetime()
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=store_id)
        ret = stocktake_service.check_confirm_stocktake(partner_id=partner_id, user_id=user_id,
                                                        end_date=end_date, store_id=store_id,
                                                        is_home_page=True,
                                                        )
        return GetUncompleteDocResponse(**ret)

    # 重盘接口
    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def RecreateStocktakeDoc(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.recreate_stocktake_doc(request, partner_id, user_id)
        return RecreateStocktakeDocResponse(**ret)

    @authorization_scope(permission_code="boh.manufactory_bi.detail.view")
    def StocktakeDocStatistics(self, request, context):
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                       domain='boh.manufactory_bi', branch_ids=request.store_ids)
        ret = stocktake_service.stocktake_doc_statistics(request, partner_id, user_id)
        return StocktakeDocStatisticsResponse(**ret)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def StocktakeProductImport(self, request, context):
        """仓库/门店盘点单明细商品导入"""
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.stocktake_product_import(request, partner_id, user_id)
        return StocktakeProductImportResponse(**ret)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def UpdateStocktakeImportBatch(self, request, context):
        """仓库/门店盘点单商品导入文件状态修改"""
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.update_stocktake_import_batch(request, partner_id, user_id)
        return UpdateStocktakeImportBatchResponse(**ret)
