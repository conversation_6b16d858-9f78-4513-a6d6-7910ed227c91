# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.proto.manufactory.processing_receipts_pb2_grpc import ProcessingReceiptsServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.module.manufactory.processing_receipts import processing_receipts_service
from supply.proto.manufactory.processing_receipts_pb2 import *


class ProcessingReceiptsApi(ProcessingReceiptsServicer, OauthGrpcAPI):
    """加工单业务模块API"""

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CreateProcessingReceipts(self, request, context):
        """创建加工单"""
        partner_id, user_id = get_user_info(context)
        response = processing_receipts_service.create_processing_receipts(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return CreateProcessingReceiptsResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def ListProcessingReceipts(self, request, context):
        """加工单列表查询"""
        partner_id, user_id = get_user_info(context)
        response = processing_receipts_service.list_processing_receipts(request, user_id=user_id, partner_id=partner_id)
        return ListProcessingReceiptsResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetProcessingReceiptsDetail(self, request, context):
        """查询加工单详情"""
        partner_id, user_id = get_user_info(context)
        response = processing_receipts_service.get_processing_receipts_detail(request, user_id=user_id,
                                                                              partner_id=partner_id)
        return GetProcessingReceiptsDetailResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def UpdateProcessingReceipts(self, request, context):
        """更新加工单"""
        partner_id, user_id = get_user_info(context)
        response = processing_receipts_service.update_processing_receipts(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return UpdateProcessingReceiptsResponse(**response)

    def ChangeProcessingReceiptsStatus(self, request, context):
        """修改加工费用单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                    SUBMITTED-提交 DELETED-删除
            2、审批:
                    REJECTED-驳回 APPROVED-审核
        """
        if request.status == "SUBMITTED":
            return self.SubmitProcessingReceipts(request, context)
        elif request.status == "REJECTED":
            return self.RejectProcessingReceipts(request, context)
        elif request.status == "APPROVED":
            return self.ApproveProcessingReceipts(request, context)
        elif request.status == "DELETED":
            return self.DeleteProcessingReceipts(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.status))

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def SubmitProcessingReceipts(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = processing_receipts_service.change_processing_receipts_status(request, user_id=user_id,
                                                                                 partner_id=partner_id)
        return ChangeProcessingReceiptsStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def RejectProcessingReceipts(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = processing_receipts_service.change_processing_receipts_status(request, user_id=user_id,
                                                                                 partner_id=partner_id)
        return ChangeProcessingReceiptsStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def ApproveProcessingReceipts(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = processing_receipts_service.change_processing_receipts_status(request, user_id=user_id,
                                                                                 partner_id=partner_id)
        return ChangeProcessingReceiptsStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def DeleteProcessingReceipts(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = processing_receipts_service.change_processing_receipts_status(request, user_id=user_id,
                                                                                 partner_id=partner_id)
        return ChangeProcessingReceiptsStatusResponse(**response)
