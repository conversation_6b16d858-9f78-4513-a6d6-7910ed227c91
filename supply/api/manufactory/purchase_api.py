
from supply.proto.manufactory.purchase_pb2_grpc import ManufactoryPurchaseServicer
from supply.api import OauthGrpcAPI, authorization_scope    
from supply.utils import get_user_info
from supply.module.purchase_service import purchase_service
from supply.error.exception import StatusUnavailable
from supply.utils.auth import branch_list_scope_check, branch_scope_check
from supply.proto.manufactory.purchase_pb2 import CreatePurchaseOrderResponse, ListPurchaseOrderResponse, \
    ChangeOrderStatusResponse, GetOrderDetailByIdResponse, UpdatePurchaseOrderResponse, \
    GetProductListByWHIdResponse, GetPurchaseBiResponse, GetOrdersDetailByIdsResponse


class ManufactoryPurchaseApi(ManufactoryPurchaseServicer, OauthGrpcAPI):
    """加工中心模块相关功能API"""

    def ChangeOrderStatus(self, request, context):
        """修改订单状态主接口分发
        变更不同状态走不同入口以做权限校验:
        1、维护:
                INITED新建 SUBMITTED提交 CANCELLED取消 SUCCESS已收货
        2、审批:
                REJECTED驳回 APPROVED审核
        """
        if request.status == "SUBMITTED":
            return self.SubmitPurchaseOrder(request, context)
        elif request.status == "CANCELLED":
            return self.CancelPurchaseOrder(request, context)
        elif request.status == "REJECTED":
            return self.RejectPurchaseOrder(request, context)
        elif request.status == "APPROVED":
            return self.ApprovePurchaseOrder(request, context)
        elif request.status == "SUCCESS":
            return self.CompletePurchaseOrder(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.status))

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CreatePurchaseOrder(self, request, context):
        """新建加工中心采购订单"""
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=request.received_by)
        response = purchase_service.create_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return CreatePurchaseOrderResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def ListPurchaseOrder(self, request, context):
        """加工中心采购订单列表查询"""
        partner_id, user_id = get_user_info(context)
        request.received_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                          domain='boh.manufactory', branch_ids=request.received_ids)
        response = purchase_service.list_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return ListPurchaseOrderResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def SubmitPurchaseOrder(self, request, context):
        """提交加工中心采购单"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=record.received_by)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CancelPurchaseOrder(self, request, context):
        """取消加工中心采购单"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=record.received_by)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def RejectPurchaseOrder(self, request, context):
        """驳回加工中心采购单"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=record.received_by)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def ApprovePurchaseOrder(self, request, context):
        """审核加工中心采购单"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=record.received_by)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    # @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CompletePurchaseOrder(self, request, context):
        """完成收货加工中心采购单"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.change_order_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeOrderStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetOrderDetailById(self, request, context):
        """查询采购单详情"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=record.received_by)
        response = purchase_service.get_order_detail_by_id(request, user_id=user_id, partner_id=partner_id)
        return GetOrderDetailByIdResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def UpdatePurchaseOrder(self, request, context):
        """更新采购单信息"""
        partner_id, user_id = get_user_info(context)
        record = purchase_service.get_purchase_order_by_id(order_id=request.order_id, partner_id=partner_id)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=record.received_by)
        response = purchase_service.update_purchase_order(request, user_id=user_id, partner_id=partner_id)
        return UpdatePurchaseOrderResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def GetProductListByWHId(self, request, context):
        """根据加工中心id拉商品"""
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=request.id)
        response = purchase_service.get_product_list_by_WHid(request, user_id=user_id, partner_id=partner_id,
                                                              branch_type="MACHINING_CENTER")
        return GetProductListByWHIdResponse(**response)

    @authorization_scope(permission_code="boh.manufactory_bi.detail.view")
    def GetPurchaseBi(self, request, context):
        """"加工中心采购单报表"""
        partner_id, user_id = get_user_info(context)
        request.wh_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                    domain='boh.manufactory_bi', branch_ids=request.wh_ids)
        total, response = purchase_service.get_purchase_bi(request, user_id=user_id, partner_id=partner_id)
        bi_detailed = {'rows': response, 'total': total}
        return GetPurchaseBiResponse(**bi_detailed)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetOrdersDetailByIds(self, request, context):
        """查询订单列表详情"""
        partner_id, user_id = get_user_info(context)
        response = purchase_service.get_orders_detail_by_ids(request, user_id=user_id, partner_id=partner_id)
        return GetOrdersDetailByIdsResponse(**response)
