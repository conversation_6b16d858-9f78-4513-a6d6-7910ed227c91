# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.proto.manufactory.processing_cost_pb2_grpc import ProcessingCostServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.module.manufactory.processing_cost import processing_cost_service
from supply.proto.manufactory.processing_cost_pb2 import *


class ProcessingCostApi(ProcessingCostServicer, OauthGrpcAPI):
    """加工费用业务模块API"""

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CreateProcessingCost(self, request, context):
        """创建加工费用单"""
        partner_id, user_id = get_user_info(context)
        response = processing_cost_service.create_processing_cost(request, user_id=user_id, partner_id=partner_id)
        return CreateProcessingCostResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def ListProcessingCost(self, request, context):
        """加工费用单列表查询"""
        partner_id, user_id = get_user_info(context)
        response = processing_cost_service.list_processing_cost(request, user_id=user_id, partner_id=partner_id)
        return ListProcessingCostResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetProcessingCostDetail(self, request, context):
        """查询加工费用单详情"""
        partner_id, user_id = get_user_info(context)
        response = processing_cost_service.get_processing_cost_detail(request, user_id=user_id, partner_id=partner_id)
        return GetProcessingCostDetailResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def UpdateProcessingCost(self, request, context):
        """更新加工费用单"""
        partner_id, user_id = get_user_info(context)
        response = processing_cost_service.update_processing_cost(request, user_id=user_id, partner_id=partner_id)
        return UpdateProcessingCostResponse(**response)

    def ChangeProcessingCostStatus(self, request, context):
        """修改加工费用单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                    SUBMITTED-提交 DELETED-删除
            2、审批:
                    REJECTED-驳回 APPROVED-审核
        """
        if request.status == "SUBMITTED":
            return self.SubmitProcessingCost(request, context)
        elif request.status == "REJECTED":
            return self.RejectProcessingCost(request, context)
        elif request.status == "APPROVED":
            return self.ApproveProcessingCost(request, context)
        elif request.status == "DELETED":
            return self.DeleteProcessingCost(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.status))

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def SubmitProcessingCost(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = processing_cost_service.change_processing_cost_status(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return ChangeProcessingCostStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def RejectProcessingCost(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = processing_cost_service.change_processing_cost_status(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return ChangeProcessingCostStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def ApproveProcessingCost(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = processing_cost_service.change_processing_cost_status(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return ChangeProcessingCostStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def DeleteProcessingCost(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = processing_cost_service.change_processing_cost_status(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return ChangeProcessingCostStatusResponse(**response)
