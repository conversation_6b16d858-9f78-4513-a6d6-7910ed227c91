import logging
from supply.proto.manufactory.production_process_receipts_pb2_grpc import ManufactoryProductionProcessReceiptsServicer
from .. import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.proto.manufactory.production_process_receipts_pb2 import CreateProductionProcessReceiptsResponse, \
    ListProductionProcessReceiptsResponse, GetProductionProcessReceiptDetailResponse, \
    UpdateProductionProcessReceiptsResponse, ChangeProductionProcessReceiptsStatusResponse, \
    GetMaterialToProductRateResponse, GetRuleByBranchIdResponse
from supply.utils.auth import branch_list_scope_check, branch_scope_check
from supply.module.production_process import product_process_receipts_service


class ManufactoryProductProcessApi(ManufactoryProductionProcessReceiptsServicer, OauthGrpcAPI):

    @authorization_scope(permission_code="boh.manufactory.product_order.maintain")
    def CreateProductionProcessReceipts(self, request, context):
        """创建生产单"""
        partner_id, user_id = get_user_info(context)
        branch_id = request.process_store_id
        # 权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=branch_id)
        response = product_process_receipts_service.create_product_process_receipts(request, user_id=user_id,
                                                                                    partner_id=partner_id,
                                                                                    store_type='manufactory')
        return CreateProductionProcessReceiptsResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.product_order.view")
    def ListProductionProcessReceipts(self, request, context):
        """生产单列表查询"""
        partner_id, user_id = get_user_info(context)
        branch_ids = request.process_store_ids
        branch_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                             domain='boh.manufactory',
                                             branch_ids=branch_ids)
        response = product_process_receipts_service.list_production_process_receipts(request, user_id=user_id,
                                                                                     partner_id=partner_id,
                                                                                     branch_ids=branch_ids,
                                                                                     branch_type='manufactory')
        return ListProductionProcessReceiptsResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.product_order.view")
    def GetProductionProcessReceiptDetail(self, request, context):
        """生产单详情查询"""
        partner_id, user_id = get_user_info(context)
        response = product_process_receipts_service.get_production_process_detail(request, user_id=user_id,
                                                                                  partner_id=partner_id)
        return GetProductionProcessReceiptDetailResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.product_order.maintain")
    def UpdateProductionProcessReceipts(self, request, context):
        """更新生产单"""
        partner_id, user_id = get_user_info(context)
        branch_id = request.process_store_id
        # 权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=branch_id)
        response = product_process_receipts_service.update_production_process_receipts(request, user_id=user_id,
                                                                                       partner_id=partner_id)
        return UpdateProductionProcessReceiptsResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.product_order.maintain")
    def ChangeProductionProcessReceiptsStatus(self, request, context):
        """修改生产单状态"""
        partner_id, user_id = get_user_info(context)
        response = product_process_receipts_service.change_product_process_receipt_status(request, user_id=user_id,
                                                                                          partner_id=partner_id)
        return ChangeProductionProcessReceiptsStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.product_order.view")
    def GetMaterialToProductRate(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                       domain='boh.manufactory', branch_ids=request.store_ids)
        row_list = product_process_receipts_service.get_material_to_product_rate(request, partner_id=partner_id,
                                                                                 user_id=user_id,
                                                                                 branch_type='manufactory')
        total = None
        if isinstance(row_list, tuple):
            total, row_list = row_list
        res['rows'] = row_list
        res['total'] = total
        return GetMaterialToProductRateResponse(**res)

    @authorization_scope(permission_code="boh.manufactory.product_order.view")
    def GetRuleByBranchId(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        branch_id = request.branch_id
        rules_info = product_process_receipts_service.get_rule_by_branch_id(partner_id, user_id, int(branch_id),
                                                                            branch_type='machining')
        res['rows'] = rules_info
        return GetRuleByBranchIdResponse(**res)
