# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.proto.manufactory.packing_receipts_pb2_grpc import PackingReceiptsServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.module.manufactory.packing_receipts import packing_receipts_service
from supply.proto.manufactory.packing_receipts_pb2 import CreatePackingReceiptsResponse, ListPackingReceiptsResponse, \
    GetPackingReceiptsDetailResponse, UpdatePackingReceiptsResponse, ChangePackingStatusResponse


class PackingReceiptsApi(PackingReceiptsServicer, OauthGrpcAPI):
    """包装单业务模块API"""
    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def CreatePackingReceipts(self, request, context):
        """创建包装单"""
        partner_id, user_id = get_user_info(context)
        response = packing_receipts_service.create_packing_receipts(request, user_id=user_id, partner_id=partner_id)
        return CreatePackingReceiptsResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def ListPackingReceipts(self, request, context):
        """包装单列表查询"""
        partner_id, user_id = get_user_info(context)
        response = packing_receipts_service.list_packing_receipts(request, user_id=user_id, partner_id=partner_id)
        return ListPackingReceiptsResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.view")
    def GetPackingReceiptsDetail(self, request, context):
        """查询包装单详情"""
        partner_id, user_id = get_user_info(context)
        response = packing_receipts_service.get_packing_receipts_detail(request, user_id=user_id, partner_id=partner_id)
        return GetPackingReceiptsDetailResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def UpdatePackingReceipts(self, request, context):
        """更新包装单"""
        partner_id, user_id = get_user_info(context)
        response = packing_receipts_service.update_packing_receipts(request, user_id=user_id, partner_id=partner_id)
        return UpdatePackingReceiptsResponse(**response)

    def ChangePackingReceiptsStatus(self, request, context):
        """修改包装单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                    SUBMITTED-提交 DELETED-删除
            2、审批:
                    REJECTED-驳回 APPROVED-审核
        """
        if request.status == "SUBMITTED":
            return self.SubmitPackingReceipts(request, context)
        elif request.status == "REJECTED":
            return self.RejectPackingReceipts(request, context)
        elif request.status == "APPROVED":
            return self.ApprovePackingReceipts(request, context)
        elif request.status == "DELETED":
            return self.DeletePackingReceipts(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.status))

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def SubmitPackingReceipts(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = packing_receipts_service.change_packing_receipt_status(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return ChangePackingStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def RejectPackingReceipts(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = packing_receipts_service.change_packing_receipt_status(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return ChangePackingStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.audit")
    def ApprovePackingReceipts(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = packing_receipts_service.change_packing_receipt_status(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return ChangePackingStatusResponse(**response)

    @authorization_scope(permission_code="boh.manufactory.order.maintain")
    def DeletePackingReceipts(self, request, context):
        partner_id, user_id = get_user_info(context)
        response = packing_receipts_service.change_packing_receipt_status(request, user_id=user_id,
                                                                          partner_id=partner_id)
        return ChangePackingStatusResponse(**response)



