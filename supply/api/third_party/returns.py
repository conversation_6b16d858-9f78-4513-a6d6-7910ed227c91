# -*- coding: utf-8 -*-

from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info
from supply.utils import pb2dict
from datetime import datetime

from supply.proto.third_party.returns_pb2_grpc import TpReturnServiceServicer
from supply.proto.third_party.returns_pb2 import DeliveryReturnByCodeResponse
from supply.module.returns import returns_service


class TpReturnsAPI(TpReturnServiceServicer, OauthGrpcAPI):

    def DeliveryReturnByCode(self, request, context):
        return_code = request.code
        partner_id, user_id = get_user_info(context)
        result = returns_service.delivery_return_by_code(return_code, partner_id, user_id)
        return DeliveryReturnByCodeResponse(**result)

