# -*- coding: utf8 -*-
import traceback
import re
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp

from supply import logger
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.error.exception import DataValidationException
from supply.utils import get_user_info, pb2dict

from supply.module.receive_diff import receiving_diff_service
from supply.proto.third_party.receive_diff_pb2_grpc import TpReceiveDiffServiceServicer
from supply.proto.third_party.receive_diff_pb2 import CommonResponse

# 门店运营管理-收货差异-三方调用
class TpReceiveDiffApi(OauthGrpcAPI, TpReceiveDiffServiceServicer):

    def FinishReceiveDiff(self, req, ctx):
        """
        更新收货差异单并审核
        """
        print(req)
        partner_id, user_id = get_user_info(ctx)
        receive_code = req.receive_code
        diff_products = req.products
        remark = req.remark
        ret = receiving_diff_service.finish_receive_diff_by_tp(\
            receive_code=receive_code, diff_products=diff_products, remark=remark, \
                partner_id=partner_id, user_id=user_id)
        
        return CommonResponse(**ret)

        