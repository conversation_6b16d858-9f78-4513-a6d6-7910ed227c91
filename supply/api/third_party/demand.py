# -*- coding: utf8 -*-
import traceback
import re
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp

from supply import logger
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.error.exception import DataValidationException
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, branch_list_scope_check, authorization_scope_by_action

from supply.module.demand import demand_module
from supply.proto.third_party.demand_pb2_grpc import TpDemandServicer
from supply.proto.third_party.demand_pb2 import CreateMainDemandResponse

# 门店运营管理-总部分配-三方调用
class TpDemandApi(OauthGrpcAPI, TpDemandServicer):

    # 创建商品主配单
    def CreateMainDemand(self, req, ctx):
        """
        创建商品主配单
        """
        logger.info(req)
        partner_id, user_id = get_user_info(ctx)
        demand_date = datetime.fromtimestamp(req.demand_date.seconds)
        arrival_date = datetime.fromtimestamp(req.arrival_date.seconds)
        remark = req.remark
        type = "MD"
        sub_type = "PRODUCT"
        batch_id = req.batch_id
        code = req.code
        if not demand_date or demand_date.year == 1970:
            raise DataValidationException("请填写订货日期或者到货日期")
        store_code = req.store_code
        ret = demand_module().create_product_main_bycode(
            store_code=store_code,
            products=req.products, 
            demand_date=demand_date, 
            arrival_date=arrival_date, 
            remark=remark, 
            partner_id=partner_id, user_id=user_id, 
            batch_id=batch_id, type=type, code=code)
        return CreateMainDemandResponse(**ret)

        