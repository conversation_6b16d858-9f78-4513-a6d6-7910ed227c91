import logging

import redis
from google.protobuf.json_format import MessageToDict

from supply.api import OauthGrpcAPI, exclude_oauth
from supply.model.reduction_augmentation_demand import ReductionAugmentationDemand, ReductionAugmentationDemandProduct, \
    ReductionAugmentationDemandStore, ReductionAugmentationDemandCalculateResult, \
    ReductionAugmentationDemandExcludedStore, ReductionAugmentationDemandCalculateProductAgg
from supply.utils import get_user_info
from supply.proto.reduction_augmentation_demand_pb2_grpc import ReductionAugmentationDemandServicer
from supply.proto import reduction_augmentation_demand_pb2 as rad
from supply.module.reduction_augmentation_demand import ReductionAugmentationDemandModule as Rad_module
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProduct, SupplyFranchiseeDemand
from redis.exceptions import LockError
from supply.error.exception import StatusUnavailable
from supply.utils.auth import authorization_scopes


class ReductionAugmentationDemandApi(ReductionAugmentationDemandServicer, OauthGrpcAPI):

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.view'])
    def ListReductionAugmentationDemand(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemand, rad.ListReductionAugmentationDemandResponse,
                       *get_user_info(context))
        return r.list_reduction_augmentation_demand()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.view'])
    def RetrieveReductionAugmentationDemand(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemand, rad.RetrieveReductionAugmentationDemandResponse,
                       *get_user_info(context))
        return r.retrieve_reduction_augmentation_demand()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.maintain'])
    def CreateReductionAugmentationDemand(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemand, rad.CreateReductionAugmentationDemandResponse,
                       *get_user_info(context))
        return r.create_reduction_augmentation_demand()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.maintain'])
    def UpdateReductionAugmentationDemand(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemand, rad.UpdateReductionAugmentationDemandResponse,
                       *get_user_info(context))
        return r.update_reduction_augmentation_demand()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.view'])
    def ListReductionAugmentationDemandProduct(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemandProduct, rad.ListReductionAugmentationDemandProductResponse,
                       *get_user_info(context))
        return r.list_reduction_augmentation_demand_product()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.view'])
    def ListReductionAugmentationDemandStore(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemandStore, rad.ListReductionAugmentationDemandStoreResponse,
                       *get_user_info(context))
        return r.list_reduction_augmentation_demand_store()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.view'])
    def SumDemandQuantity(self, request, context):
        r = Rad_module(request, SupplyFranchiseeDemand, rad.SumDemandQuantityResponse, *get_user_info(context))
        return r.sum_demand_quantity()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.maintain'])
    def CalculateAllocation(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemandCalculateResult, rad.CalculateAllocationResponse,
                       *get_user_info(context))
        try:
            response = r.calculate_allocation()
        except LockError as e:
            logging.info(e)
            raise StatusUnavailable('正在计算中。。。请等耐心等待')
        return response

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.view'])
    def ListCalculateAllocationResult(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemandCalculateResult, rad.ListCalculateAllocationResultResponse)
        return r.list_calculate_result()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.maintain'])
    def CreateReductionAugmentationDemandExcludedStore(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemandExcludedStore,
                       rad.CreateReductionAugmentationDemandExcludedStoreResponse, *get_user_info(context))
        return r.create_excluded_store()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.view'])
    def ListReductionAugmentationDemandExcludedStore(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemandExcludedStore,
                       rad.ListReductionAugmentationDemandExcludedStoreResponse, *get_user_info(context))
        return r.list_excluded_store()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.maintain'])
    def DestroyReductionAugmentationDemandExcludedStore(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemandExcludedStore,
                       rad.DestroyReductionAugmentationDemandExcludedStoreResponse, *get_user_info(context))
        return r.destroy_excluded_store()

    @authorization_scopes(['boh.frs_hd_management.reduce_allocation.view'])
    def ReductionAugmentationDemandProductAgg(self, request, context):
        r = Rad_module(request, ReductionAugmentationDemandCalculateProductAgg,
                       rad.ReductionAugmentationDemandProductAggResponse, *get_user_info(context))
        return r.list_cal_agg()