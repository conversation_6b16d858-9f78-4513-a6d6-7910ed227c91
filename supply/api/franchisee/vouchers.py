# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.utils import get_user_info
from supply.api import OauthGrpcAPI, authorization_scope
from supply.proto.franchisee.vouchers_pb2_grpc import VouchersServiceServicer
from supply.proto.franchisee.vouchers_pb2 import *
from supply.module.franchisee.vouchers import vouchers_service


# 代金券
class VouchersAPI(VouchersServiceServicer, OauthGrpcAPI):

    # 新建代金券
    @authorization_scope('boh.frs_market_management.vouchers.maintain')
    def CreateVouchers(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = vouchers_service.create_vouchers(request, partner_id, user_id)
        return CommonMsg(**result)

    # 查询代金券详情
    @authorization_scope('boh.frs_market_management.vouchers.view')
    def GetVouchersDetail(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = vouchers_service.get_voucher_detail(request, partner_id, user_id)
        return Vouchers(**result)

    # 查询代金券列表
    @authorization_scope('boh.frs_market_management.vouchers.view')
    def ListVouchers(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = vouchers_service.list_vouchers(request, partner_id, user_id)
        return ListVouchersResponse(**result)

    # 更新代金券
    @authorization_scope('boh.frs_market_management.vouchers.maintain')
    def UpdateVouchers(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = vouchers_service.update_vouchers(request, partner_id, user_id)
        return CommonMsg(**result)

    # 变更状态
    def DealVouchersById(self, request, context):
        """修改状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                CONFIRMED 已确认
                INVALID   已作废
        """
        if request.action == "CONFIRMED":
            return self.ConfirmVouchers(request, context)
        elif request.action == "INVALID":
            return self.InvalidVouchers(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.action))

    # 确认
    @authorization_scope('boh.frs_market_management.vouchers.maintain')
    def ConfirmVouchers(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = vouchers_service.deal_vouchers_by_ids(request, allow_status=["INITED"], partner_id=partner_id,
                                                       user_id=user_id)
        return DealVouchersByIdResponse(**result)

    # 作废
    @authorization_scope('boh.frs_market_management.vouchers.maintain')
    def InvalidVouchers(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = vouchers_service.deal_vouchers_by_ids(request, allow_status=["INITED"], partner_id=partner_id,
                                                       user_id=user_id)
        return DealVouchersByIdResponse(**result)

