# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.utils import get_user_info
from supply.utils.auth import branch_list_scope_check, branch_scope_check
from supply.api import OauthGrpcAPI, authorization_scope

from supply.proto.mobile.mobile_franchisee_demand_pb2_grpc import MobileFranchiseeDemandServiceServicer
from supply.proto.mobile.mobile_franchisee_demand_pb2 import *
from supply.module.mobile.mobile_franchisee_demand import mobile_f_demand_service


# 移动端-加盟商模块-订货单
class MobileFranchiseeDemandAPI(MobileFranchiseeDemandServiceServicer, OauthGrpcAPI):

    @authorization_scope('boh.frs_store.mobile_demand.maintain')
    def CreateFDemand(self, request, context):
        """创建订货单"""
        partner_id, user_id = get_user_info(context)
        result = mobile_f_demand_service.create_franchisee_demand(partner_id, user_id, request)
        return CreateFDemandResponse(**result)

    @authorization_scope('boh.frs_store.mobile_demand.view')
    def ListFDemand(self, request, context):
        """查询订货单列表"""
        partner_id, user_id = get_user_info(context)
        result = mobile_f_demand_service.list_franchisee_demand(request, partner_id, user_id)
        return ListFDemandResponse(**result)

    @authorization_scope('boh.frs_store.mobile_demand.view')
    def GetFDemandById(self, request, context):
        """查询订货单详情"""
        partner_id, user_id = get_user_info(context)
        result = mobile_f_demand_service.get_demand_by_id(partner_id, user_id, request)
        return Demand(**result)

    @authorization_scope('boh.frs_store.mobile_demand.view')
    def GetProductsById(self, request, context):
        """查询订货单商品详情"""
        partner_id, user_id = get_user_info(context)
        result = mobile_f_demand_service.list_product(request, partner_id, user_id)
        return ProductsResponse(**result)

    @authorization_scope('boh.frs_store.mobile_demand.view')
    def GetHistoryById(self, request, context):
        """查询订货单操作历史"""
        partner_id, user_id = get_user_info(context)
        res = mobile_f_demand_service.get_demand_history(request, partner_id, user_id)
        return HistoryResponse(**res)

    @authorization_scope('boh.frs_store.mobile_demand.maintain')
    def UpdateProducts(self, request, context):
        """更新订货单商品详情"""
        partner_id, user_id = get_user_info(context)
        updated_product_res = mobile_f_demand_service.update_products(partner_id, user_id, request)
        return UpdateProductsResponse(**updated_product_res)

    def DealFDemandById(self, request, ctx):
        """修改订单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                    P_SUBMIT付款提交支付凭证 CANCELLED取消 R_APPROVE区经审核
            2、审批:
        """
        if request.action == "P_SUBMIT":
            return self.PSubmitFDemand(request, ctx)
        elif request.action == "CANCELLED":
            return self.CancelFDemand(request, ctx)
        elif request.action == "R_APPROVE":
            return self.RApproveFDemand(request, ctx)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.action))

    @authorization_scope('boh.frs_store.mobile_demand.maintain')
    def PSubmitFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = mobile_f_demand_service.deal_demand_by_ids(request, allow_status=["PREPARE", "REJECTED"],
                                                         partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_store.mobile_demand.maintain')
    def CancelFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = mobile_f_demand_service.deal_demand_by_ids(request,
                                                         allow_status=["PREPARE", "REJECTED", "SUBMITTED", "R_APPROVE"],
                                                         partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_store.mobile_demand.audit')
    def RApproveFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = mobile_f_demand_service.deal_demand_by_ids(request, allow_status=["SUBMITTED"],
                                                         partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_store.mobile_demand.maintain')
    def AddFDemandProduct(self, request, context):
        partner_id, user_id = get_user_info(context)
        # res = mobile_f_demand_service.add_demand_product(partner_id, user_id, request)
        res = mobile_f_demand_service.add_relation_products(partner_id, user_id, request)
        return UpdateProductsResponse(**res)

    @authorization_scope('boh.frs_store.mobile_demand.view')
    def QueryFDemandReport(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = mobile_f_demand_service.query_demand_report(request, partner_id=partner_id, user_id=user_id)
        return QueryFDemandReportResponse(**res)

    def AddShoppingCartCache(self, request, context):
        """添加购物车商品缓存"""
        partner_id, user_id = get_user_info(context)
        res = mobile_f_demand_service.add_shopping_cart_cache(request, partner_id=partner_id, user_id=user_id)
        return AddShoppingCartCacheResponse(**res)

    def GetShoppingCartCache(self, request, context):
        """查询购物车缓存"""
        partner_id, user_id = get_user_info(context)
        res = mobile_f_demand_service.get_shopping_cart_cache(request, partner_id=partner_id, user_id=user_id)
        return GetShoppingCartCacheResponse(**res)

    def CheckFDemandTodo(self, request, context):
        """检查订货待办能否订货"""
        partner_id, user_id = get_user_info(context)
        res = mobile_f_demand_service.check_demand_todo(request, partner_id=partner_id, user_id=user_id)
        return CheckFDemandTodoResponse(**res)

