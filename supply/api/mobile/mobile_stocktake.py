# -*- coding:utf-8 -*-
import json
from datetime import datetime, timedelta

from supply import time_cost
from supply.module.stocktake_service import ssm as stocktake_service
from supply.utils.auth import branch_scope_check, branch_list_scope_check, stocktake_resource_check
from supply.error.exception import *
from supply.proto.mobile.mobile_stocktake_pb2_grpc import MobileStockTakeServicer
from supply.proto.mobile.mobile_stocktake_pb2 import *
from supply.api import OauthGrpcAPI, authorization_scope, get_context_dict
from supply.utils import get_user_info
from supply.client.metadata_service import metadata_service


class MobileStocktakeApi(OauthGrpcAPI, MobileStockTakeServicer):

    @time_cost
    def _deal_params_stocktake(self, st, pro_names_map=None):
        """整理移动端返回盘点单参数
        :param st 查询集 - dict/list
        :param pro_names_map 单据id/商品map
        """
        if isinstance(st, list):
            result = []
            for t in st:
                result.append(self._deal_params_stocktake(t, pro_names_map))
        elif isinstance(st, dict):
            result = dict(
                id=st.get('doc_id') if st.get('doc_id') else st.get('id'),
                code=st.get('code'),
                branch_id=st.get('branch_id'),
                status=st.get('status'),
                target_date=st.get('target_date'),
                remark=st.get('remark'),
                branch_type=st.get('branch_type'),
                created_at=st.get('created_at'),
                updated_at=st.get('updated_at'),
                type=st.get('type'),
                stocktake_type=st.get('stocktake_type'),
                reject_reason=st.get('reject_reason'),
                schedule_name=st.get('schedule_name'),
                schedule_code=st.get('schedule_code'),
                st_diff_flag=st.get('st_diff_flag'),
                auto_approve_time=st.get('auto_approve_time'),
                auto_invalid_time=st.get('auto_invalid_time'),
                is_empty=st.get('is_empty'),
                status_plan=st.get('status_plan'),
                total_amount = st.get('total_amount'),
                total_diff_amount = st.get('total_diff_amount'),
                total_sales_amount = st.get('total_sales_amount'),
                total_diff_sales_amount=st.get('total_diff_sales_amount'),
                attachments=st.get('attachments')
            )
            if pro_names_map:
                result["extends"] = json.dumps(dict(product_names=pro_names_map.get(result['id'], [])),
                                               ensure_ascii=False)
        else:
            result = None
        return result

    # @time_cost
    def _deal_params_stocktake_product(self, stp):
        """整理移动端返回盘点商品参数
        :param stp 查询集 - dict/list
        """
        if isinstance(stp, list):
            result = []
            for t in stp:
                result.append(self._deal_params_stocktake_product(t))
        elif isinstance(stp, dict):
            result = dict(
                id=stp.get('id'),
                doc_id=stp.get('doc_id'),
                product_id=stp.get('product_id'),
                product_code=stp.get('product_code'),
                product_name=stp.get('product_name'),
                unit_id=stp.get('unit_id'),
                unit_name=stp.get('unit_name'),
                accounting_quantity=stp.get('accounting_quantity'),
                accounting_unit_id=stp.get('accounting_unit_id'),
                accounting_unit_name=stp.get('accounting_unit_name'),
                quantity=stp.get('quantity'),
                created_at=stp.get('created_at'),
                storage_type=stp.get('storage_type'),
                updated_at=stp.get('updated_at'),
                units=stp.get('units'),
                product_tags=stp.get('product_tags'),
                spec=stp.get('spec'),
                category_id=stp.get('category_id'),
                category_name=stp.get('category_name'),
                position_id=stp.get('position_id'),
            )
        else:
            result = None
        return result

    def CheckStocktakeByDocID(self, request, context):
        """检查能否确认盘点单"""
        doc_id = request.doc_id
        branch_type = request.branch_type
        partner_id, user_id = get_user_info(context)
        res = stocktake_service.check_confirm_stocktake(doc_id=doc_id, partner_id=partner_id,
                                                        user_id=user_id, branch_type=branch_type)
        return CheckStocktakeByDocIDResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.audit")
    def ApproveStocktakeByDocID(self, request, context):
        """财务确认盘点单（status=APPROVED，核算完库存后status=COMPLETED）"""
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        approve_name = str(request.approve_name).strip()
        source=request.source
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
            # 校验盘点类型审核权限
            stoketake_type = doc_obj.type if doc_obj.type else doc_obj.stocktake_type
            stocktake_resource_check(partner_id=partner_id, user_id=user_id, schema='STOCKTAKE_TYPE',
                                     domain='boh.store', stoketake_type=stoketake_type)
        else:
            raise DataValidationException('无此单据')
        attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                         domain='boh.store.stocktake',
                                                                         user_id=user_id).get(
            'attachment_required_control')
        attachments = json.loads(doc_obj.attachments).get("attachments") if doc_obj.attachments else None
        ctx = get_context_dict(context)
        if attachment_required and not attachments and not ctx.get('from_grpc'):
            raise DataValidationException("缺少附件信息, 请保存附件信息")
        res = stocktake_service.approve_stocktake(doc_id, partner_id, user_id, username, approve_name,source=source)
        return ApproveStocktakeByDocIDResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.audit")
    def RejectStocktakeProduct(self, request, context):
        """财务驳回（status=REJECTED，部分商品status=REJECTED"""
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        reason = request.reason
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id, user_id, 'store', 'boh.store', doc_obj.branch_id)
            # 校验盘点类型审核权限
            stoketake_type = doc_obj.type if doc_obj.type else doc_obj.stocktake_type
            stocktake_resource_check(partner_id=partner_id, user_id=user_id, schema='STOCKTAKE_TYPE',
                                     domain='boh.store', stoketake_type=stoketake_type)
        else:
            raise DataValidationException('无此单据')
        res['result'] = stocktake_service.reject_stocktake(doc_id, partner_id, user_id, username, reason)
        return RejectStocktakeProductResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def CancelStocktakeByDocID(self, request, context):
        """作废盘点单（status=CANCELED）"""
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id)
        if doc_obj:
            branch_scope_check(partner_id, user_id, 'store', 'boh.store', doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        res['result'] = stocktake_service.cancel_stocktake(doc_id, partner_id, user_id)
        return CancelStocktakeByDocIDResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.view")
    def GetStocktakeByDocID(self, request, context):
        """获取一个盘点单"""
        props = {}
        doc_id = request.doc_id
        partner_id, user_id = get_user_info(context)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
            if doc_obj.attachments:
                attachments = json.loads(doc_obj.attachments)
                doc_obj.attachments = attachments.get('attachments', [])
            props = doc_obj.props()
            props = stocktake_service.attach_st_plan_info([props], partner_id)[0]
            props = self._deal_params_stocktake(props)
        return Stocktake(**props)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.view")
    def GetStocktake(self, request, context):
        """查询盘点单"""
        res = {}
        partner_id, user_id = get_user_info(context)
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store', branch_ids=request.store_ids)
        doc_obj_list = stocktake_service.list_stocktake_doc_detail(request, partner_id=partner_id,
                                                                   user_id=user_id)
        total = None
        if isinstance(doc_obj_list, tuple):
            total, doc_obj_list = doc_obj_list
        if len(doc_obj_list) > 0:
            stocktake_service.attach_st_extra_info(doc_obj_list, partner_id)
            doc_obj_list = stocktake_service.attach_st_plan_info(doc_obj_list, partner_id)
        res['rows'] = self._deal_params_stocktake(doc_obj_list)
        res['total'] = total
        return GetStocktakeResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.view")
    def GetStocktakeByIds(self, request, context):
        """根据盘点单ID查询盘点单"""
        partner_id, user_id = get_user_info(context)
        res = stocktake_service.list_stocktake_doc_details_by_ids(request, partner_id=partner_id, user_id=user_id)
        return GetStocktakeByIdsResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.view")
    def GetStocktakeProduct(self, request, context):
        """查询盘点单明细商品"""
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        include_unit = request.include_unit
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        category_id = request.category_id
        storage_type = request.storage_type
        product_name = request.product_name
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id)
        if doc_obj:
            branch_scope_check(partner_id, user_id, 'store', 'boh.store', doc_obj.branch_id)
        products = stocktake_service.list_stocktake_product(doc_id=doc_id, include_unit=include_unit,
                                                            limit=limit, offset=offset,
                                                            include_total=include_total,
                                                            category_id=category_id, storage_type=storage_type,
                                                            product_name=product_name,
                                                            partner_id=partner_id, user_id=user_id,
                                                            branch_id=doc_obj.branch_id)
        total, products = products
        res['total'] = total
        for row in products:
            row["products"] = self._deal_params_stocktake_product(row.get('products'))
        res['position_rows'] = products
        return GetStocktakeProductResponse(**res)

    def GetNewStocktakeId(self, request, context):
        """生成新的盘点单id, 用于手动创建盘点单"""
        res = {"new_doc_id": stocktake_service.getNewStocktakeId()}
        return GetNewStocktakeIdResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def ManuallyCreateStocktake(self, request, context):
        """根据门店、盘点时间和商品，手动创建盘点单"""
        res = {}
        calculate_inventory = request.calculate_inventory
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        new_doc_id = request.new_doc_id
        store_id = request.branch_id
        product_list = request.product_list
        utc_target_datetime = datetime.fromtimestamp(request.target_date.seconds)
        source = request.source
        attachments = []
        if request.attachments:
            for t in request.attachments:
                attachments.append(dict(
                    type=t.type,
                    url=t.url
                ))
        attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                         domain='boh.store.stocktake',
                                                                         user_id=user_id).get(
            'attachment_required_control')
        if attachment_required and not attachments:
            raise DataValidationException("缺少附件信息, 请保存附件信息")
        # 创建手动盘点单
        res["result"] = stocktake_service.manually_create_stocktake(partner_id = partner_id,
                                                                    target_date = utc_target_datetime,
                                                                    branch_id = store_id,
                                                                    new_doc_id = new_doc_id,
                                                                    product_list = product_list,
                                                                    calculate_inventory = calculate_inventory,
                                                                    user_id = user_id,
                                                                    username = username,
                                                                    schema = 'store',
                                                                    domain = 'boh.store',
                                                                    branch_type = 'STORE',source=source,attachments=attachments)

        return ManuallyCreateStocktakeResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def GetStocktakeProductByStoreID(self, request, context):
        """查询门店可盘点商品"""
        res = {}
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        limit = request.limit
        if not limit:
            limit = -1
        offset = request.offset
        include_total = request.include_total
        search = request.search
        search_fields = request.search_fields
        category_ids = list(request.category_ids)
        attach_price = request.attach_price
        exc_codes=request.exc_codes
        in_codes =request.in_codes
        exc_upcs = request.exc_upcs
        in_upcs = request.in_upcs
        # sort = request.sort
        order_by = request.order_by
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=store_id)
        list_stocktake_store_product = stocktake_service.list_stocktake_store_product(
            store_id=store_id,
            limit=limit, offset=offset,
            include_total=include_total,
            partner_id=partner_id, user_id=user_id,
            search=search,
            search_fields=search_fields,
            category_ids=category_ids,
            attach_price=attach_price,
            exc_codes=exc_codes,
            in_codes=in_codes,
            exc_upcs=exc_upcs,
            in_upcs=in_upcs,
            order_by=order_by,
        )
        if include_total:
            if list_stocktake_store_product is not None:
                res['total'] = list_stocktake_store_product[0]
                res['rows'] = list_stocktake_store_product[1]
                res['inventory_unchanged_rows'] = list_stocktake_store_product[2]
        else:
            res['rows'] = list_stocktake_store_product[0]
            res['inventory_unchanged_rows'] = list_stocktake_store_product[1]
        return GetStocktakeProductByStoreIDResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def AddProductsToStocktake(self, request, context):
        """向手动盘点单增加商品"""
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        product_list = request.product_list

        res["result"] = stocktake_service.addProductsToStocktake(partner_id = partner_id,
                                                                 doc_id = doc_id,
                                                                 product_list = product_list,
                                                                 user_id = user_id,
                                                                 username = username,
                                                                 schema = 'store',
                                                                 domain = 'boh.store',
                                                                 branch_type = 'STORE')

        return AddProductsToStocktakeResponse(**res)

    def AddProductsToStocktakeForPOS(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        products = request.products
        if len(products) == 0:
            return PutStocktakeByDocIDResponse(**res)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        if doc_obj.status not in ['STARTED','INITED','REJECTED']:
            raise DataValidationException('新建、驳回允许更新，当前状态盘点单不允许更新！')
        res['result'] = stocktake_service.addProductsToStocktakeForPOS(doc_id, products,
                                                                                  allow_status=['STARTED',
                                                                                                'INITED',
                                                                                                'REJECTED'],
                                                                                  partner_id=partner_id,
                                                                                  user_id=user_id,
                                                                                  username=username,
                                                                                  branch_id=doc_obj.branch_id)

        return PutStocktakeByDocIDResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def DelProductsFromStocktake(self, request, context):
        """从手动盘点单删除商品"""
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        product_list = request.product_list

        res["result"] = stocktake_service.delProductsFromStocktake(partner_id = partner_id,
                                                                   doc_id = doc_id,
                                                                   product_list = product_list,
                                                                   user_id = user_id,
                                                                   username = username,
                                                                   schema = 'store',
                                                                   domain = 'boh.store',
                                                                   branch_type = 'STORE')
        return DelProductsFromStocktakeResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def PutStocktakeByDocID(self, request, context):
        """更新盘点单"""
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        products = request.products
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        attachments = []
        if request.attachments:
            for t in request.attachments:
                attachments.append(dict(
                    type=t.type,
                    url=t.url
                ))
        # attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
        #                                                                  domain='boh.store.stocktake',
        #                                                                  user_id=user_id).get(
        #     'attachment_required_control')
        # if attachment_required and not attachments:
        #     raise DataValidationException("缺少附件信息, 请传入附件信息")
        res['result'] = stocktake_service.update_stocktake_products_with_quantity(doc_id, products,
                                                                                  allow_status=['INITED',
                                                                                                'REJECTED'],
                                                                                  partner_id=partner_id,
                                                                                  user_id=user_id,
                                                                                  username=username,
                                                                                  branch_id=doc_obj.branch_id,
                                                                                  attachments=attachments)

        return PutStocktakeByDocIDResponse(**res)

    # @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def GetStocktakeTags(self, request, context):
        """获取盘点商品标签"""
        res = {}
        partner_id, user_id = get_user_info(context)
        branch_ids = request.branch_ids
        tag_name = request.tag_name
        branch_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                             domain='boh.store', branch_ids=branch_ids)
        res['rows'] = stocktake_service.get_stocktake_tags(branch_ids=branch_ids, tag_name=tag_name,
                                                           partner_id=partner_id, user_id=user_id)
        return GetStocktakeTagsResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def DeleteStocktakeProductTags(self, request, context):
        """根据条目ID列表删除盘点商品标签条目"""
        res = {}
        partner_id, user_id = get_user_info(context)
        res['result'] = stocktake_service.clean_stocktake_product_tags(request, partner_id)
        return DeleteStocktakeProductTagsResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def SubmitStocktakeByDocID(self, request, context):
        """提交盘点单"""
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        source=request.source
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                         domain='boh.store.stocktake',
                                                                         user_id=user_id).get(
            'attachment_required_control')
        attachments = json.loads(doc_obj.attachments).get("attachments") if doc_obj.attachments else None
        ctx = get_context_dict(context)
        if attachment_required and not attachments and not ctx.get('from_grpc'):
            raise DataValidationException("缺少附件信息, 请保存附件信息")
        res['result'] = stocktake_service.submit_stocktake(doc_id=doc_id, user_id=user_id, partner_id=partner_id,
                                                           username=username, branch_type=doc_obj.branch_type,source=source)
        return SubmitStocktakeByDocIDResponse(**res)

    def GetStocktakeBalance(self, request, context):
        """盘点单损益报表"""
        res = {}
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store_bi', branch_ids=request.store_ids)

        doc_obj_list = stocktake_service.get_stocktake_balance(request, partner_id, user_id)
        total = None
        if isinstance(doc_obj_list, tuple):
            total, doc_obj_list = doc_obj_list
        res['rows'] = doc_obj_list
        res['total'] = total
        return GetStocktakeBalanceResponse(**res)

    def GetStocktakeBalanceProductGroup(self, request, context):
        """门店盘点单损益汇总报表"""
        res = {}
        doc_id = request.doc_id
        partner_id, user_id = get_user_info(context)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id=doc_id, partner_id=partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store_bi', branch_id=doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        res['rows'] = stocktake_service.get_st_balance_product_group_by_doc_id(doc_id, partner_id, user_id)
        return GetStocktakeBalanceProductGroupResponse(**res)

    def StocktakeBiDetailed(self, request, context):
        """盘点单报表"""
        res = {}
        include_total = request.include_total
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store_bi', branch_ids=request.store_ids)
        data, total, sum_quantity, sum_accounting_quantity = stocktake_service.stocktake_bi_detailed(request,
                                                                                                     partner_id,
                                                                                                     user_id)
        if include_total:
            if data is None:
                return StocktakeBiDetailedResponse(**res)
            res['total'] = {}
            res['total']['count'] = total
            res['total']['sum_quantity'] = sum_quantity
            res['total']['sum_accounting_quantity'] = sum_accounting_quantity
            res['rows'] = data
            return StocktakeBiDetailedResponse(**res)
        else:
            if data is None:
                return StocktakeBiDetailedResponse(**res)
            res['rows'] = data
            return StocktakeBiDetailedResponse(**res)

    # //StocktakeBalanceRegion区域盘点31
    def StocktakeBalanceRegion(self, request, context):
        res = {}
        include_total = request.include_total
        partner_id, user_id = get_user_info(context)
        # todo：区域权限校验
        data = stocktake_service.get_st_balance_product_group(request, partner_id, user_id)
        if include_total:
            if data is None:
                return StocktakeBalanceRegionResponse(**res)
            res['total'] = len(data['rows'])
            res['rows'] = data['rows']
            return StocktakeBalanceRegionResponse(**res)
        else:
            if data is None:
                return StocktakeBalanceRegionResponse(**res)
            res['rows'] = data
            return StocktakeBalanceRegionResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.view")
    def AdvanceStocktakeDiff(self, request, context):
        """提前查看盘点损益"""
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        ret = stocktake_service.get_advance_stocktake_products_diff(doc_id, partner_id, user_id)
        return AdvanceStocktakeDiffResponse(**ret)

    def StocktakeDiffReport(self, request, context):
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store_bi', branch_ids=request.store_ids)
        ret = stocktake_service.get_stocktake_diff_report(request, partner_id, user_id)
        return StocktakeDiffReportResponse(**ret)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.maintain")
    def RecreateStocktakeDoc(self, request, context):
        """重盘接口"""
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.recreate_stocktake_doc(request, partner_id, user_id)
        return RecreateStocktakeDocResponse(**ret)

    @authorization_scope(permission_code="boh.store.mobile_stocktake.view")
    def GetStocktakeLog(self, request, context):
        """查询盘点单操作log"""
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        res = stocktake_service.get_stocktake_log(doc_id=doc_id, partner_id=partner_id, user_id=user_id)
        return GetStocktakeLogResponse(**res)
