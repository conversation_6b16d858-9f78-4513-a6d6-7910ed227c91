# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.utils import get_user_info
from supply.api import OauthGrpcAPI, authorization_scope

from supply.proto.mobile.mobile_franchisee_refund_pb2_grpc import MobileFranchiseeRefundServiceServicer
from supply.proto.mobile.mobile_franchisee_refund_pb2 import *
from supply.module.mobile.mobile_franchisee_refund import mobile_franchisee_refund_service


# 加盟商退款-移动端
class MobileFranchiseeRefundAPI(MobileFranchiseeRefundServiceServicer, OauthGrpcAPI):

    @authorization_scope('boh.frs_store.mobile_refund.view')
    def ListMobileFRefund(self, request, context):
        """查询退款单列表"""
        partner_id, user_id = get_user_info(context)
        result = mobile_franchisee_refund_service.list_mobile_refund(request, partner_id, user_id)
        return ListMobileFRefundResponse(**result)

    @authorization_scope('boh.frs_store.mobile_refund.view')
    def GetMobileFRefundById(self, request, context):
        """查询退款单详情"""
        partner_id, user_id = get_user_info(context)
        result = mobile_franchisee_refund_service.get_mobile_refund_by_id(request, partner_id, user_id)
        return GetMobileFRefundByIdResponse(**result)

    @authorization_scope('boh.frs_store.mobile_refund.view')
    def GetMobileRefundHistory(self, request, context):
        """查询退款单操作历史"""
        partner_id, user_id = get_user_info(context)
        result = mobile_franchisee_refund_service.get_mobile_refund_log(request, partner_id, user_id)
        return RefundHistoryResponse(**result)

    def DealMobileFRefundById(self, request, ctx):
        """修改订单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                CANCELLED取消
            门店端只支持取消操作
        """
        if request.action == "CANCELLED":
            return self.CancelMobileFRefund(request, ctx)
        else:
            raise StatusUnavailable("门店端仅支持取消操作！")

    # 退款单自动审核功能上线后取消接口弃用
    def CancelMobileFRefund(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = mobile_franchisee_refund_service.deal_mobile_refund_by_ids(request, allow_status=["INITED"],
                                                                         partner_id=partner_id, user_id=user_id)
        return DealMobileFRefundByIdResponse(**res)
