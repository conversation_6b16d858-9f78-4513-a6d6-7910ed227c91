import json
from ...proto.mobile.mobile_adjust_pb2_grpc import mobileAdjustServicer
from ...proto.mobile.mobile_adjust_pb2 import *
from ...module.adjust_service import ads as adjust_service
from .. import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime
from ...utils.adjust_enum import ADS_STATUS
from ...error.exception import *
from ...client.metadata_service import metadata_service
from supply.utils.auth import branch_list_scope_check, branch_scope_check


class MobileAdjustApi(mobileAdjustServicer, OauthGrpcAPI):
    """<移动端>报废单API"""

    def _deal_params_adjust(self, ad, pro_names_map=None):
        """整理移动端返回报废单参数
        :param ad 查询集 - dict/list
        :param pro_names_map 单据id/商品map
        """
        if isinstance(ad, list):
            result = []
            for t in ad:
                result.append(self._deal_params_adjust(t, pro_names_map))
        elif isinstance(ad, dict):
            result = dict(
                id=ad.get('adjust_id') if ad.get('adjust_id') else ad.get('id'),
                code=ad.get('code'),
                adjust_store=ad.get('adjust_store'),
                status=ad.get('status'),
                adjust_date=ad.get('adjust_date'),
                reason_type=ad.get('reason_type'),
                remark=ad.get('remark'),
                branch_type=ad.get('branch_type'),
                created_at=ad.get('created_at'),
                updated_at=ad.get('updated_at'),
                source=ad.get('source'),
                reason_name=ad.get('reason_name'),
                schedule_name=ad.get('schedule_name'),
                is_empty=ad.get('is_empty'),
                status_plan=ad.get('status_plan'),
                reject_reason=ad.get('reject_reason'),
                attachments=ad.get('attachments')
            )
            if pro_names_map:
                result["extends"] = json.dumps(dict(product_names=pro_names_map.get(result['id'], [])),
                                               ensure_ascii=False)
        else:
            result = None
        return result

    def _deal_params_adjust_product(self, adp):
        """整理移动端返回报废商品参数
        :param adp 查询集 - dict/list
        """
        if isinstance(adp, list):
            result = []
            for t in adp:
                result.append(self._deal_params_adjust_product(t))
        elif isinstance(adp, dict):
            result = dict(
                id=adp.get('id'),
                product_id=adp.get('product_id'),
                product_code=adp.get('product_code'),
                product_name=adp.get('product_name'),
                unit_id=adp.get('unit_id'),
                unit_name=adp.get('unit_name'),
                quantity=adp.get('quantity'),
                created_at=adp.get('created_at'),
                updated_at=adp.get('updated_at'),
                model_name=adp.get('model_name')
            )
            if adp.get('units') and isinstance(adp.get('units'), list):
                result['units'] = adp.get('units')
        else:
            result = None
        return result

    @authorization_scope(permission_code="boh.store.mobile_loss.view")
    def GetAdjust(self, request, context):
        """报废单列表查询"""
        partner_id, user_id = get_user_info(context)
        store_ids = request.branches
        start_date = request.start_date
        end_date = request.end_date
        branch_type = request.branch_type
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        code = request.code
        ids = list(request.ids) if request.ids else None
        if not code and not ids:
            if start_date.year == 1970:
                raise DataValidationException("请传入查询开始日期")
            if end_date.year == 1970:
                raise DataValidationException("请传入查询结束日期")
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        status = []
        r_status = request.status
        if r_status:
            for r in r_status:
                status.append(ADS_STATUS[r])
        reason_type = request.reason_type
        sources = list(request.sources)

        product_ids = list(request.product_ids) if request.product_ids else []
        order = request.order
        sort = request.sort
        if not sort:
            sort = 'updated_at'
        # 数据权限校验
        store_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                            domain='boh.store', branch_ids=store_ids)
        res = adjust_service.list_store_adjust(partner_id=partner_id, start_date=start_date,
                                               end_date=end_date, store_ids=store_ids, limit=limit, offset=offset,
                                               include_total=include_total, status=status, reason_type=reason_type,
                                               code=code, order=order, sort=sort, branch_type=branch_type,
                                               user_id=user_id, sources=sources, ids=ids, is_mobile=True,
                                               product_ids=product_ids)
        if len(res.get('rows', [])) > 0:
            res['rows'] = adjust_service.attach_adjust_plan_info(res['rows'], partner_id=partner_id)
            res['rows'] = self._deal_params_adjust(res['rows'], res.get('pro_names_map'))
            # 单据状态排序：按“新建、已提交、已驳回、已审核”
            res['rows'] = sorted(res['rows'], key=lambda e: ADS_STATUS.index(e.__getitem__('status')))
            if 'pro_names_map' in res.keys():
                del res['pro_names_map']
        return GetAdjustResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_loss.view")
    def GetAdjustProduct(self, request, context):
        """查询报废单商品"""
        res = {}
        partner_id, user_id = get_user_info(context)
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        adjust_id = request.adjust_id
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id, user_id, True)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=adjust_obj.adjust_store)
        data = adjust_service.list_store_adjust_product(adjust_id, limit=limit, offset=offset,
                                                        include_total=include_total, partner_id=partner_id,
                                                        user_id=user_id)

        if include_total:
            if data is None:
                return GetAdjustProductResponse(**res)
            res['total'] = data[0]
            res['rows'] = data[1]
        else:
            if data is None:
                return GetAdjustProductResponse(**res)
            res['rows'] = data
        res['rows'] = self._deal_params_adjust_product(res['rows'])
        return GetAdjustProductResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_loss.view")
    def GetAdjustByID(self, request, context):
        """查询一个报废单"""
        res = {}
        adjust_id = request.adjust_id
        partner_id, user_id = get_user_info(context)
        if not adjust_id:
            return Adjust(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id, user_id, True)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=adjust_obj.adjust_store)
            props = adjust_obj.props()
            props['id'] = props['adjust_id']
            del props['adjust_id']
            props = adjust_service.attach_adjust_plan_info([props], partner_id=partner_id)[0]
            res = self._deal_params_adjust(props)
            return Adjust(**res)
        return Adjust(**res)

    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def ConfirmAdjust(self, request, context):
        """确认报废单"""
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        adjust_id = request.adjust_id
        branch_type = request.branch_type
        if not adjust_id:
            return ConfirmAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if not adjust_obj:
            raise DataValidationException('没有该调整单')
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=adjust_obj.adjust_store)
        res['result'] = adjust_service.adjust_confirmed(adjust_id, branch_type, partner_id, user_id, username)
        return ConfirmAdjustResponse(**res)

    # 提交一个报废单
    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def SubmitAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        if not adjust_id:
            return SubmitAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if not adjust_obj:
            raise DataValidationException('没有该报废单')
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=adjust_obj.adjust_store)
        res['result'] = adjust_service.submit_adjust(adjust_id, partner_id, user_id)
        return SubmitAdjustResponse(**res)

    # 审核一个报废单
    @authorization_scope(permission_code="boh.store.mobile_loss.audit")
    def ApproveAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        if not adjust_id:
            return ApproveAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if not adjust_obj:
            raise DataValidationException('没有该报废单')
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=adjust_obj.adjust_store)
        res['result'] = adjust_service.approve_adjust(adjust_id, partner_id, user_id)
        return ApproveAdjustResponse(**res)

    # 驳回一个报废单
    @authorization_scope(permission_code="boh.store.mobile_loss.audit")
    def RejectAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        reject_reason = request.reject_reason
        if not adjust_id:
            return RejectAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if not adjust_obj:
            raise DataValidationException('没有该报废单')
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=adjust_obj.adjust_store)
        res['result'] = adjust_service.reject_adjust(adjust_id, partner_id, user_id, reject_reason=reject_reason)
        return RejectAdjustResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def GetAdjustProductByStoreID(self, request, context):
        """查询门店可报废商品"""
        res = {}
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        limit = request.limit
        if not limit:
            limit = -1
        offset = request.offset
        include_total = request.include_total
        adjust_date = request.adjust_date
        search = request.search
        search_fields = request.search_fields
        category_ids = list(request.category_ids)
        if adjust_date:
            timestamp = Timestamp()
            timestamp.seconds = adjust_date.seconds
            adjust_date = timestamp.ToDatetime()
            adjust_date = datetime(int(adjust_date.year), int(adjust_date.month), int(adjust_date.day))
        else:
            adjust_date = datetime.now()
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=store_id)
        list_adjust_store_product = adjust_service.list_adjust_store_product(
            store_id=store_id, target_date=adjust_date,
            limit=limit, offset=offset,
            include_total=include_total,
            partner_id=partner_id, user_id=user_id,
            search=search,
            search_fields=search_fields,
            category_ids=category_ids,
            order_by=request.order_by
        )
        if include_total:
            if list_adjust_store_product is not None:
                res['total'] = list_adjust_store_product[0]
                res['rows'] = list_adjust_store_product[1]
                res['inventory_unchanged_rows'] = list_adjust_store_product[2]
        else:
            res['rows'] = list_adjust_store_product[0]
            res['inventory_unchanged_rows'] = list_adjust_store_product[1]
        return GetAdjustProductByStoreIDResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def CreateAdjust(self, request, context):
        """创建一个报废单"""
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        request_id = request.request_id
        if not request_id:
            raise NoRequestIDError('没有请求ID')
        adjust_rd = adjust_service.get_adjust_by_request_id(request_id)
        if adjust_rd:
            props = self._deal_params_adjust(adjust_rd.props())
            return Adjust(**props)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=request.adjust_store)
        adjust_detail = adjust_service.create_adjust(request, partner_id=partner_id, user_id=user_id,
                                                     username=username)
        if adjust_detail:
            props = adjust_detail.props()
            if 'adjust_id' in props:
                props['id'] = props['adjust_id']
                del props['adjust_id']
                props = self._deal_params_adjust(props)
        return Adjust(**props)

    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def UpdateAdjust(self, request, context):
        """更新报废单"""
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        adjust_obj = adjust_service.list_store_adjust_by_id(request.adjust_id, partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=adjust_obj.adjust_store)
        else:
            raise DataValidationException('无此单据')
        adjust_detail = adjust_service.update_adjust(request, user_id, partner_id, username=username)
        if adjust_detail:
            props = adjust_detail.props()
            if 'adjust_id' in props:
                props['id'] = props['adjust_id']
                del props['adjust_id']
                props = self._deal_params_adjust(props)
        return Adjust(**props)

    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def DeleteAdjust(self, request, context):
        """删除报废单"""
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        adjust_obj = adjust_service.list_store_adjust_by_id(request.adjust_id, partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=adjust_obj.adjust_store)
        else:
            raise DataValidationException('无此单据')
        res['result'] = adjust_service.delete_adjust(adjust_id, partner_id, user_id)
        return DeleteAdjustResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def DeleteAdjustProduct(self, request, context):
        """删除报废单商品"""
        res = {}
        adjust_id = request.adjust_id
        ids = [int(_id) for _id in list(request.ids)] if request.ids else []
        partner_id, user_id = get_user_info(context)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id=adjust_id, partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=adjust_obj.adjust_store)
        else:
            raise DataValidationException('无此单据')
        res['result'] = adjust_service.delete_adjust_product(adjust_id, ids, partner_id, user_id)
        return DeleteAdjustProductResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def CancelAdjust(self, request, context):
        """取消报废单"""
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        if not adjust_id:
            return CancelAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id=adjust_id, partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=adjust_obj.adjust_store)
        else:
            raise DataValidationException('无此单据')
        res['result'] = adjust_service.adjusts_cancel([adjust_id], partner_id, user_id)
        return CancelAdjustResponse(**res)

    @authorization_scope(permission_code="boh.store.mobile_loss.maintain")
    def CreatedAdjustByCode(self, request, context):
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        request_id = request.request_id
        if not request_id:
            raise NoRequestIDError('没有请求ID')
        adjust_rd = adjust_service.get_adjust_by_code(request_id)
        if adjust_rd:
            props = adjust_rd.props()
            return Adjust(**props)
        adjust_detail = adjust_service.create_adjust_by_code(request, partner_id=partner_id, user_id=user_id,
                                                             username=username, branch_type="STORE")
        if adjust_detail:
            props = adjust_detail.props()
            if 'adjust_id' in props:
                props['id'] = props['adjust_id']
                props['adjust_store_secondary_id'] = request.adjust_store
                del props['adjust_id']
                del props['adjust_store']
        return Adjust(**props)

    @authorization_scope(permission_code="boh.store.mobile_loss.view")
    def GetAdjustLog(self, request, context):
        """查询报废单操作log"""
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        res = adjust_service.get_adjust_log(adjust_id=adjust_id, partner_id=partner_id, user_id=user_id)
        return GetAdjustLogResponse(**res)
