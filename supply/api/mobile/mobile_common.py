# -*- coding:utf-8 -*-
import json

from supply.error.exception import *
from supply.module.mobile.common_sercvice import mobile_common_service
from supply.proto.mobile.mobile_common_pb2_grpc import MobileCommonServiceServicer
from supply.proto.mobile.mobile_common_pb2 import *
from supply.api import OauthGrpcAPI
from supply.utils import get_user_info


class MobileCommonApi(OauthGrpcAPI, MobileCommonServiceServicer):

    def GetUnfinishedDoc(self, request, context):
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        end_date = request.end_date
        branch_type = request.branch_type
        ret = mobile_common_service.get_unfinished_doc(partner_id=partner_id, user_id=user_id,
                                                       end_date=end_date, store_id=store_id,branch_type=branch_type)
        return GetUnfinishedDocResponse(**ret)
