# -*- coding: utf-8 -*-
from supply import request_info
from supply.utils import pb2dict, get_user_info
from supply.utils.auth import authorization_scope, branch_list_scope_check, authorization_scope_by_action
from supply.api import OauthGrpcAPI, exclude_oauth

from supply.proto.mobile.mobile_demand_pb2_grpc import MobileDemandServiceServicer
from supply.proto.mobile.mobile_demand_pb2 import ListDemandResponse, \
    Demand, HistoryResponse, ProductsResponse, UpdateProductsResponse, Response
from supply.module.mobile.mobile_demand import mobile_demand_service


# 移动端-门店运营-订货单
class MobileDemandAPI(MobileDemandServiceServicer, OauthGrpcAPI):
    @authorization_scope('boh.store.mobile_order.view')
    def ListDemand(self, request, context):
        """查询订货单列表"""
        partner_id, user_id = get_user_info(context)
        request.store_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store', request.store_ids)
        demand_list, count = mobile_demand_service.list_demand(partner_id, user_id, request)
        demand_res = {'rows':demand_list, 'total':count}
        return ListDemandResponse(**demand_res)

    @authorization_scope('boh.store.mobile_order.view')
    def GetDemandById(self, request, context):
        """查询订货单详情"""
        partner_id, user_id = get_user_info(context)
        demand = mobile_demand_service.get_demand(partner_id, user_id, request)
        return Demand(**demand)
    
    @authorization_scope('boh.store.mobile_order.view')
    def GetHistoryById(self, request, context):
        """查询订货单操作历史"""
        partner_id, user_id = get_user_info(context)
        demand_history, total = mobile_demand_service.get_demand_history(partner_id, user_id, request)
        demand_history_res = {'rows':demand_history, 'total':total}
        return HistoryResponse(**demand_history_res)

    @authorization_scope('boh.store.mobile_order.view')
    def GetProductsById(self, request, context):
        """查询订货单商品详情"""
        partner_id, user_id = get_user_info(context)
        product_list, total = mobile_demand_service.list_product_by_ids(partner_id, user_id, request)
        product_list_res = {'rows':product_list, 'total':total}
        return ProductsResponse(**product_list_res)

    @authorization_scope('boh.store.mobile_order.maintain')
    def UpdateProducts(self, request, context):
        """更新订货单商品详情"""
        partner_id, user_id = get_user_info(context)
        updated_product_res = mobile_demand_service.update_products(partner_id, user_id, request)
        return UpdateProductsResponse(**updated_product_res)

    @authorization_scope('boh.store.mobile_order.view')
    def GetProductsDetailById(self, request, context):
        """获取商品订货规则等"""
        partner_id, user_id = get_user_info(context)
        product_info = mobile_demand_service.get_product_detail(partner_id, user_id, request)
        product_list_res = {'rows':[product_info], 'total':1}
        return ProductsResponse(**product_list_res)


    @authorization_scope_by_action('boh.store', 'mobile_order')
    def DealDemandById(self, req, ctx):
        """
        修改订货单状态
        """
        demand_id = req.id
        action = req.action
        partner_id, user_id = get_user_info(ctx)

        mobile_demand_service.deal_demand_by_id(demand_id, action, partner_id, user_id)
        return Response(description="success")
   
