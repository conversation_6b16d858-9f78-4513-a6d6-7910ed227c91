# -*- coding: utf-8 -*-
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils.auth import authorization_scope, branch_list_scope_check, authorization_scopes
from supply.utils import get_user_info
from supply.utils import pb2dict

from supply.api.receiving_diff import ReceivingDiffAPI
from supply.module.franchisee.receiving_diff import franchisee_receiving_diff_service
from supply.proto.mobile.mobile_franchisee_receive_diff_pb2 import *
# from supply.proto.franchisee.receiving_diff_pb2_grpc import ReceivingDiffServiceServicer


class FranchiseeMobileReceivingDiffAPI(ReceivingDiffAPI, OauthGrpcAPI):

    @authorization_scopes(['boh.frs_store.mobile_diff.view', 'boh.frs_store_extra.mobile_diff.view'])
    def GetReceiveDiffById(self, request, context):
        partner_id, user_id = get_user_info(context)
        receiving_diff_id = request.id
        receiving_diff_detail = franchisee_receiving_diff_service.get_receiving_diff_by_id(partner_id=partner_id,
                                                                                           user_id=user_id, receiving_diff_id=receiving_diff_id)
        return ReceiveDiff(**receiving_diff_detail)

    @authorization_scopes(['boh.frs_store.mobile_diff.view', 'boh.frs_store_extra.mobile_diff.view'])
    def ListReceiveDiff(self, request, context):
        partner_id, user_id = get_user_info(context)
        received_type = "FRS_DEMAND"
        request.store_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.frs_store', request.store_ids)
        count, receiving_diff_list = franchisee_receiving_diff_service.list_receiving_diffs(
            partner_id, user_id, request, received_type)
        receiving_diff_list = {'rows': receiving_diff_list, 'total': count}
        return ListReceiveDiffResponse(**receiving_diff_list)

    @authorization_scopes(['boh.frs_store.mobile_diff.view', 'boh.frs_store_extra.mobile_diff.view'])
    def GetReceiveDiffProductById(self, request, context):
        partner_id, user_id = get_user_info(context)
        diff_id = request.id
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        sort = request.sort
        order = request.order
        is_frs = True
        count, receiving_diff_products_list, total_d_amount, total_s_amount = franchisee_receiving_diff_service.list_receiving_diff_products_by_diff_id(
            diff_id, limit, offset, include_total, sort, order, partner_id, user_id, is_frs=is_frs)
        receiving_diff_products_list = {
            'rows': receiving_diff_products_list, 'total': count, 'total_d_amount': total_d_amount, 'total_s_amount': total_s_amount}
        return GetReceiveDiffProductByIdResponse(**receiving_diff_products_list)

    @authorization_scopes(['boh.frs_store.mobile_diff.maintain'])
    def SubmitReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = franchisee_receiving_diff_service.submit_receiving_diff(
            receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return SubmitReceiveDiffResponse(**result)

    @authorization_scopes(['boh.frs_store.mobile_diff.maintain', 'boh.frs_store_extra.mobile_diff.audit'])
    def ConfirmReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = franchisee_receiving_diff_service.confirm_receiving_diff(
            receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return ConfirmReceiveDiffResponse(**result)

    @authorization_scopes(['boh.frs_store.mobile_diff.maintain', 'boh.frs_store_extra.mobile_diff.audit'])
    def RejectReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        reject_reason = request.reject_reason
        attachments = request.attachments
        partner_id, user_id = get_user_info(context)
        result = franchisee_receiving_diff_service.reject_receiving_diff(
            receiving_diff_id, partner_id, user_id, reject_reason, attachments)
        result = {'payload': result}
        return RejectReceiveDiffResponse(**result)

    @authorization_scopes(['boh.frs_store.mobile_diff.maintain'])
    def UpdateReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        products = request.products
        attachments = request.attachments
        remark = request.remark
        partner_id, user_id = get_user_info(context)
        result = franchisee_receiving_diff_service.update_receiving_diff(
            receiving_diff_id, partner_id, user_id, products, attachments, remark)
        result = {'payload': result}
        return UpdateReceiveDiffResponse(**result)

    @authorization_scopes(['boh.frs_store.mobile_diff.maintain'])
    def DeleteReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = franchisee_receiving_diff_service.delete_receiving_diff(
            receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return RejectReceiveDiffResponse(**result)

    @authorization_scopes(['boh.frs_store.mobile_diff.view', 'boh.frs_store_extra.mobile_diff.view'])
    def GetHistoryById(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        diff_history, total = franchisee_receiving_diff_service.get_diff_history(
            partner_id, user_id, receiving_diff_id)
        diff_history_res = {'rows': diff_history, 'total': total}
        return HistoryResponse(**diff_history_res)

    @authorization_scopes(['boh.frs_store.mobile_diff.maintain'])
    def CreateReceivingDiff(self, request, context):
        receiving_id = request.receiving_id
        partner_id, user_id = get_user_info(context)
        receiving_diff_id = franchisee_receiving_diff_service.create_receive_diff_by_hand(request, partner_id, user_id,
                                                                               diff_type='HC')
        result = {'receiving_diff_id': receiving_diff_id}
        return CreateReceivingDiffResponse(**result)
