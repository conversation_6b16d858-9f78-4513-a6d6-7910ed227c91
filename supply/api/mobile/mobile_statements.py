from supply.api import OauthGrpcAPI
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, branch_list_scope_check, authorization_scopes
from datetime import datetime
import logging

from supply.proto.mobile.mobile_statements_pb2_grpc import MobileStatementsServiceServicer
from supply.proto.mobile.mobile_statements_pb2 import ListReconciliationResponse
from supply.module.franchisee.statements import mobile_statements_service


# 加盟商手机端报表
class MobileStatementsAPI(MobileStatementsServiceServicer, OauthGrpcAPI):
    def ListReconciliation(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = mobile_statements_service.list_reconciliation(partner_id, user_id, request)
        res = {
            'rows': result.get('collections'),
            'total': result.get('total'),
            'begining_balance': result.get('begining_balance'),
            'ending_balance': result.get('ending_balance'),
            'create_at': result.get('create_at'),
            'msg': result.get('msg'),
            'uid': result.get('uid'),
            'code': result.get('code')
        }

        logging.info(res)
        return ListReconciliationResponse(**res)
