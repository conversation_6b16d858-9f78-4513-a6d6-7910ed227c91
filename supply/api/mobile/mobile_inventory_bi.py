# -*- coding: utf-8 -*-

from supply.utils import pb2dict, get_user_info
from supply import request_info
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils.auth import authorization_scope, branch_list_scope_check

from supply.proto.mobile.mobile_inventory_bi_pb2_grpc import MobileInventoryBiServiceServicer
from supply.proto.mobile.mobile_inventory_bi_pb2 import RealtimeInventoryResponse, \
    QueryInventoryLogResponse, DailyInventoryResponse
from supply.module.mobile.mobile_inventory_bi import mobile_inventory_bi_service


# 移动端-库存
class MobileInventoryBiAPI(MobileInventoryBiServiceServicer, OauthGrpcAPI):

    # 门店实时库存
    @authorization_scope('boh.store.mobile_inventory.view')
    def RealtimeInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.branch_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store', request.branch_ids)
        inventory_detail, count = mobile_inventory_bi_service.get_realtime_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return RealtimeInventoryResponse(**result)

    # 加盟商门店实时库存
    # @authorization_scope('boh.frs_store.mobile_inventory.view')
    def FrsRealtimeInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        # request.branch_ids[:] = branch_list_scope_check(partner_id, user_id, 'frs_store', 'boh.frs_store', request.branch_ids)
        inventory_detail, count = mobile_inventory_bi_service.get_realtime_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return RealtimeInventoryResponse(**result)

    # 加盟商门店库存流水
    @authorization_scope('boh.frs_store.mobile_inventory.view')
    def FrsInventoryLog(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.branch_ids[:] = branch_list_scope_check(partner_id, user_id, 'frs_store', 'boh.frs_store', request.branch_ids)
        inventory_log, total, amount_sum = mobile_inventory_bi_service.query_inventory_log_list(request, partner_id, user_id)
        result = {'rows':inventory_log, 'total':total, 'amount_sum':amount_sum}
        return QueryInventoryLogResponse(**result)

    # 加盟商门店每日库存
    @authorization_scope('boh.frs_store.mobile_inventory.view')
    def FrsDailyInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.branch_ids[:] = branch_list_scope_check(partner_id, user_id, 'frs_store', 'boh.frs_store', request.branch_ids)
        inventory_detail, count = mobile_inventory_bi_service.get_daily_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return DailyInventoryResponse(**result)