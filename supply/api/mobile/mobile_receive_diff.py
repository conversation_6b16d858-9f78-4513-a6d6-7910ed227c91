# -*- coding: utf-8 -*-
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, branch_list_scope_check, authorization_scopes

from supply.proto.mobile.mobile_receive_diff_pb2_grpc import MobileReceiveDiffServiceServicer
from supply.proto.mobile.mobile_receive_diff_pb2 import ReceiveDiff, ListReceiveDiffResponse, \
    GetReceiveDiffProductByIdResponse, SubmitReceiveDiffResponse, UpdateReceiveDiffResponse, \
            RejectReceiveDiffResponse, ConfirmReceiveDiffResponse, DeleteReceiveDiffResponse, \
                HistoryResponse
from supply.module.mobile.mobile_receive_diff import mobile_receiving_diff_service

# 移动端-门店运营-收货差异单
class MobileReceiveDiffAPI(MobileReceiveDiffServiceServicer, OauthGrpcAPI):

    @authorization_scope('boh.store.mobile_diff.view')
    def GetReceiveDiffById(self, request, context):
        partner_id, user_id = get_user_info(context)
        receiving_diff_id = request.id
        receiving_diff_detail = mobile_receiving_diff_service.get_receiving_diff_by_id(partner_id=partner_id,\
            user_id=user_id, receiving_diff_id=receiving_diff_id)
        return ReceiveDiff(**receiving_diff_detail)

    @authorization_scope('boh.store.mobile_diff.view')
    def ListReceiveDiff(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.store_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store', request.store_ids)
        count, receiving_diff_list = mobile_receiving_diff_service.list_receiving_diffs(partner_id, user_id, request)
        receiving_diff_list = {'rows':receiving_diff_list, 'total':count}
        return ListReceiveDiffResponse(**receiving_diff_list)

    @authorization_scope('boh.store.mobile_diff.view')   
    def GetReceiveDiffProductById(self, request, context):
        partner_id, user_id = get_user_info(context)
        diff_id = request.id
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        sort = request.sort
        order = request.order
        count, receiving_diff_products_list, total_d_amount, total_s_amount = mobile_receiving_diff_service.list_receiving_diff_products_by_diff_id(diff_id,limit, offset, include_total, sort, order, partner_id)
        receiving_diff_products_list = {'rows':receiving_diff_products_list, 'total':count, 'total_d_amount': total_d_amount, 'total_s_amount': total_s_amount}
        return GetReceiveDiffProductByIdResponse(**receiving_diff_products_list)

    @authorization_scope('boh.store.mobile_diff.maintain')
    def SubmitReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = mobile_receiving_diff_service.submit_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return SubmitReceiveDiffResponse(**result)

    @authorization_scopes(['boh.store.mobile_diff.audit', 'boh.store_extra.mobile_diff.audit'])
    def ConfirmReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = mobile_receiving_diff_service.confirm_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return ConfirmReceiveDiffResponse(**result)

    @authorization_scopes(['boh.store.mobile_diff.audit', 'boh.store_extra.mobile_diff.audit'])
    def RejectReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        reject_reason = request.reject_reason
        attachments = request.attachments
        partner_id, user_id = get_user_info(context)
        result = mobile_receiving_diff_service.reject_receiving_diff(receiving_diff_id, partner_id, user_id, reject_reason, attachments)
        result = {'payload': result}
        return RejectReceiveDiffResponse(**result)

    @authorization_scopes(['boh.store.mobile_diff.maintain', 'boh.store_extra.mobile_diff.audit'])
    def UpdateReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        products = request.products
        attachments = request.attachments
        remark = request.remark
        partner_id, user_id = get_user_info(context)
        result = mobile_receiving_diff_service.update_receiving_diff(receiving_diff_id, partner_id, user_id, products, attachments, remark)
        result = {'payload': result}
        return UpdateReceiveDiffResponse(**result)

    @authorization_scopes(['boh.store.mobile_diff.audit', 'boh.store_extra.mobile_diff.audit'])
    def DeleteReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = mobile_receiving_diff_service.delete_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return RejectReceiveDiffResponse(**result)

    @authorization_scope('boh.store.mobile_diff.view')
    def GetHistoryById(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        diff_history, total = mobile_receiving_diff_service.get_diff_history(partner_id, user_id, receiving_diff_id)
        diff_history_res = {'rows':diff_history, 'total':total}
        return HistoryResponse(**diff_history_res)

