# -*- coding: utf-8 -*-
import json

from supply.error.exception import StatusUnavailable
from supply.proto.mobile.mobile_self_picking_pb2_grpc import MobileStoreSelfPickingServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.module.self_picking import self_picking_service
from supply.proto.mobile.mobile_self_picking_pb2 import *
from supply.utils.auth import branch_scope_check, branch_list_scope_check


class MobileStoreSelfPickingApi(MobileStoreSelfPickingServicer, OauthGrpcAPI):
    """<移动端>门店自采单业务API"""

    def deal_params_picking(self, sp, pro_names_map=None):
        """整理移动端返回自采单参数
        :param sp 查询集 - dict/list
        :param pro_names_map 单据id/商品map
        """
        if isinstance(sp, list):
            result = []
            for t in sp:
                result.append(self.deal_params_picking(t, pro_names_map))
        elif isinstance(sp, dict):
            result = dict(
                id=sp.get('id'),
                partner_id=sp.get('partner_id'),
                branch_code=sp.get('branch_code'),
                branch_name=sp.get('branch_name'),
                branch_type=sp.get('branch_type'),
                status=sp.get('status'),
                code=sp.get('code'),
                order_date=sp.get('order_date'),
                reason=sp.get('reason'),
                reason_name=sp.get('reason_name'),
                remark=sp.get('remark'),
                attachments=sp.get('attachments'),
                total_amount=sp.get('total_amount'),
                created_at=sp.get('created_at'),
                updated_at=sp.get('updated_at')
            )
            if sp.get('items'):
                result['items'] = self.deal_params_picking_product(sp.get('items'))
            if pro_names_map:
                result["extends"] = json.dumps(dict(product_names=pro_names_map.get(result['id'], [])),
                                               ensure_ascii=False)
        else:
            result = None
        return result

    def deal_params_picking_product(self, spp):
        """整理移动端返回自采商品参数
        :param spp 查询集 - dict/list
        """
        if isinstance(spp, list):
            result = []
            for t in spp:
                result.append(self.deal_params_picking_product(t))
        elif isinstance(spp, dict):
            result = dict(
                id=spp.get('id'),
                product_id=spp.get('product_id'),
                product_code=spp.get('product_code'),
                product_name=spp.get('product_name'),
                unit_id=spp.get('unit_id'),
                unit_name=spp.get('unit_name'),
                quantity=spp.get('quantity'),
                price=spp.get('price'),
                amount=spp.get('amount'),
                model_name=spp.get('model_name')
            )
            if spp.get('units') and isinstance(spp.get('units'), list):
                result['units'] = spp.get('units')
        else:
            result = None
        return result

    @authorization_scope(permission_code="boh.store.mobile_self_picking.maintain")
    def CreateStoreSelfPicking(self, request, context):
        """创建门店自采单"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=request.branch_id)
        response = self_picking_service.create_self_picking(request, user_id=user_id, partner_id=partner_id)
        return CreateStoreSelfPickingResponse(**response)

    @authorization_scope(permission_code="boh.store.mobile_self_picking.view")
    def ListStoreSelfPicking(self, request, context):
        """门店自采单列表查询"""
        partner_id, user_id = get_user_info(context)
        request.branch_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                        domain='boh.store', branch_ids=request.branch_ids)
        response = self_picking_service.list_self_picking(request, user_id=user_id, partner_id=partner_id,
                                                          is_mobile=True)
        response['rows'] = self.deal_params_picking(response.get('rows'), response.get('pro_names_map'))
        del response['pro_names_map']
        return ListStoreSelfPickingResponse(**response)

    @authorization_scope(permission_code="boh.store.mobile_self_picking.view")
    def GetStoreSelfPickingDetail(self, request, context):
        """查询门店自采单详情"""
        partner_id, user_id = get_user_info(context)
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.get_self_picking_detail(request, user_id=user_id, partner_id=partner_id)
        response = self.deal_params_picking(response)
        return GetStoreSelfPickingDetailResponse(**response)

    @authorization_scope(permission_code="boh.store.mobile_self_picking.maintain")
    def UpdateStoreSelfPicking(self, request, context):
        """更新门店自采单"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.update_self_picking(request, user_id=user_id, partner_id=partner_id)
        return UpdateStoreSelfPickingResponse(**response)

    @authorization_scope(permission_code="boh.store.mobile_self_picking.maintain")
    def GetPickingProductByStoreId(self, request, context):
        """取得门店可自采商品（相同属性区域）"""
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                           branch_id=request.store_id)
        response = self_picking_service.get_picking_product_by_store(request, user_id=user_id, partner_id=partner_id)
        return GetPickingProductByStoreIdResponse(**response)

    def ChangeSelfPickingStatus(self, request, context):
        """修改订单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                    INITED新建 SUBMITTED提交 CANCELLED取消 SUCCESS已收货
            2、审批:
                    REJECTED驳回 APPROVED审核
        """
        if request.status == "SUBMITTED":
            return self.SubmitSelfPicking(request, context)
        elif request.status == "CANCELLED":
            return self.CancelSelfPicking(request, context)
        elif request.status == "REJECTED":
            return self.RejectSelfPicking(request, context)
        elif request.status == "APPROVED":
            return self.ApproveSelfPicking(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.status))

    @authorization_scope(permission_code="boh.store.mobile_self_picking.maintain")
    def SubmitSelfPicking(self, request, context):
        """提交门店自采"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.change_self_picking_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeSelfPickingStatusResponse(**response)

    def CancelSelfPicking(self, request, context):
        pass

    @authorization_scope(permission_code="boh.store.mobile_self_picking.audit")
    def RejectSelfPicking(self, request, context):
        """驳回门店自采"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.change_self_picking_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeSelfPickingStatusResponse(**response)

    @authorization_scope(permission_code="boh.store.mobile_self_picking.audit")
    def ApproveSelfPicking(self, request, context):
        """审核门店自采"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.change_self_picking_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeSelfPickingStatusResponse(**response)

    @authorization_scope(permission_code="boh.store.mobile_self_picking.view")
    def GetSelfPickingLog(self, request, context):
        """自采历史记录"""
        partner_id, user_id = get_user_info(context)
        receipt_id = request.receipt_id
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        res = self_picking_service.get_self_picking_log(receipt_id=receipt_id, partner_id=partner_id, user_id=user_id)
        return GetSelfPickingLogResponse(**res)
