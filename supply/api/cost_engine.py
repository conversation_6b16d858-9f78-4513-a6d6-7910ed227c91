# -*- coding: utf-8 -*-

# from supply import request_info
# from ..api import OauthGrpcAPI, exclude_oauth
# from ..utils import get_user_info, pb2dict

# from ..proto.reports.reports_pb2_grpc import ReportsServicer
# from ..proto.reports.reports_pb2 import CommonResponse

# from ..module.cost_engine_service import cost_engine_trigger_service


# class CostEngineAPI(ReportsServicer, OauthGrpcAPI):
#     # 成本引擎回传消息
#     def TaskCallBack(self, request, context):
#         partner_id, user_id = get_user_info(context)
#         batch_no = request.requestID
#         success = request.success
#         msg = request.message
#         category = request.category
#         result = cost_engine_trigger_service.cost_count_callback(batch_no=batch_no,
#                 success=success, category=category, msg=msg, user_id=user_id, partner_id=partner_id)
#         return CommonResponse(**result)
