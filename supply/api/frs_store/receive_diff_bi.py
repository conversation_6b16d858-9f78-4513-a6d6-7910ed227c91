# -*- coding: utf-8 -*-
from supply import request_info
from supply.utils import pb2dict, get_user_info
from supply.utils.auth import authorization_scope, branch_list_scope_check
from supply.api import OauthGrpcAPI, exclude_oauth

from supply.proto.frs_store.receive_diff_bi_pb2_grpc import FranchiseeStoreReceiveDiffBiServiceServicer
from supply.proto.frs_store.receive_diff_bi_pb2 import GetReceiveDiffCollectResponse, GetReceiveDiffDetailResponse
from supply.module.receiving_diff_bi import receiving_diff_bi_service


# 门店报表-收货差异单报表
authorization = authorization_scope('boh.store_bi.detail.view')


class FranchiseeStoreReceiveDiffBiAPI(FranchiseeStoreReceiveDiffBiServiceServicer, OauthGrpcAPI):

    @authorization
    def GetReceiveDiffCollect(self, request, context):
        """查询收货差异汇总报表"""
        partner_id, user_id = get_user_info(context)
        request.st_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.frs_store_bi', request.st_ids)
        total, receiving_diff_bi_list = receiving_diff_bi_service.get_receiving_diff_collect(partner_id, user_id,
                                                                                             request,
                                                                                             is_store=True,
                                                                                             is_franchisee=True)
        receiving_diff_bi = {'rows': receiving_diff_bi_list, 'total': total}
        return GetReceiveDiffCollectResponse(**receiving_diff_bi)

    @authorization
    def GetReceiveDiffDetail(self, request, context):
        """查询收货差异详情报表"""
        partner_id, user_id = get_user_info(context)
        request.st_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.frs_store_bi', request.st_ids)
        total, receiving_diff_bi_detailed_list = receiving_diff_bi_service.\
            get_receiving_diff_detailed(partner_id,user_id, request, is_store=True,
                                        is_franchisee=True)
        receiving_diff_bi_detailed = {'rows': receiving_diff_bi_detailed_list, 'total': total}
        return GetReceiveDiffDetailResponse(**receiving_diff_bi_detailed)
