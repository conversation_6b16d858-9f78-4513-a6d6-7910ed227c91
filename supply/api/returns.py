# -*- coding: utf-8 -*-

from ..api import OauthGrpcAPI, exclude_oauth
from ..utils import get_user_info
from ..utils import pb2dict
from datetime import datetime

from ..proto.returns_pb2_grpc import ReturnServiceServicer
from ..proto.returns_pb2 import Returns, CreateReturnResponse, \
    ListReturnResponse, GetReturnByIdResponse, GetReturnProductByIdResponse, \
        SubmitReturnResponse, RejectReturnResponse, ConfirmReturnResponse, \
            UpdateRemarkResponse, UpdateReturnResponse, DeliveryReturnResponse, \
                ApproveReturnResponse, DeleteReturnResponse, CommonReply
from ..module.returns import returns_service


class ReturnsAPI(ReturnServiceServicer, OauthGrpcAPI):

    def CreateReturn(self, request, context):
        return_by = request.return_by
        logistics_type = request.logistics_type
        type = request.type
        sub_type = request.sub_type
        return_delivery_date = request.return_delivery_date
        source_id = request.source_id
        source_code = request.source_code
        return_reason = request.return_reason
        remark = request.remark
        attachments = request.attachments
        request_id = request.request_id
        products = request.products
        partner_id, user_id = get_user_info(context)
        res = returns_service.create_return_entrance(return_by=return_by, return_delivery_date=return_delivery_date, 
                                    type=type, sub_type=sub_type, logistics_type=logistics_type,
                                    return_reason=return_reason, products=products, request_id=request_id,
                                    partner_id=partner_id, user_id=user_id,
                                    remark=remark, attachments=attachments, source_id=source_id, source_code=source_code)
        return CreateReturnResponse(**res)

    def ListReturn(self, request, context):
        partner_id, user_id = get_user_info(context)
        count, return_list = returns_service.list_returns(partner_id, user_id, request)
        result = {'rows':return_list, 'total':count}
        return ListReturnResponse(**result)
    
    def GetReturnById(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        return_obj = returns_service.get_return_by_id(return_id, partner_id, user_id)
        return Returns(**return_obj)

    def GetReturnProductById(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        limit = request.limit
        offset = request.offset
        sort = request.sort
        order = request.order
        count, return_product_list = returns_service.list_return_products_by_return_id(return_id,limit, offset,sort,order,partner_id)
        result = {'rows':return_product_list, 'total':count}
        return GetReturnProductByIdResponse(**result)

    def SubmitReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.submit_return(return_id,partner_id, user_id)
        result = {'payload': result}
        return SubmitReturnResponse(**result)

    def RejectReturn(self, request, context):
        return_id = request.id
        reject_reason = request.reject_reason
        partner_id, user_id = get_user_info(context)
        result = returns_service.reject_return(return_id, partner_id, user_id,reject_reason)
        result = {'payload': result}
        return RejectReturnResponse(**result)

    def ConfirmReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.confirm_return(return_id, partner_id, user_id)
        result = {'payload': result}
        return ConfirmReturnResponse(**result)
        
    def UpdateReturn(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        products = request.products
        return_reason = request.return_reason
        remark = request.remark
        return_delivery_date = request.return_delivery_date
        return_to = request.return_to
        attachments = request.attachments
        logistics_type = request.logistics_type
        result = returns_service.update_return_product_quantity(return_id, partner_id, user_id, 
                                                products, return_reason, remark, 
                                                return_delivery_date, return_to,
                                                attachments, logistics_type)
        result = {'payload': result}
        return UpdateReturnResponse(**result)

    def DeliveryReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.delivery_return(return_id, partner_id, user_id)
        result = {'payload': result}
        return DeliveryReturnResponse(**result)

    def ApproveReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        trans_type = request.trans_type
        result = returns_service.approve_return(return_id=return_id, partner_id=partner_id, \
            user_id=user_id, trans_type=trans_type)
        result = {'payload': result}
        return ApproveReturnResponse(**result)

    def DeleteReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.delete_return(return_id, partner_id, user_id)
        result = {'payload': result}
        return DeleteReturnResponse(**result)

    def CheckReturnAvailableByrec(self, request, context):
        product_detail = request.products
        receiving_id = request.receiving_id
        return_id = request.return_id
        partner_id, user_id = get_user_info(context)
        success = returns_service.if_over_return_by_rec(receiving_id=receiving_id, product_detail=product_detail, 
                                                    partner_id=partner_id, user_id=user_id, return_id=return_id)
        result = {'success':success}
        return CommonReply(**result)
