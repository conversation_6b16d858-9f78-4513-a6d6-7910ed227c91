# -*- coding: utf-8 -*-
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, branch_list_scope_check,\
    two_branch_list_scope_check


from supply.proto.store.receive_diff_pb2_grpc import StoreReceiveDiffServiceServicer
from supply.proto.store.receive_diff_pb2 import ReceiveDiff, ListReceiveDiffResponse, \
    GetReceiveDiffProductByIdResponse, CommonResponse, ReceiveDiffProduct, CreateReceivingDiffResponse
from supply.module.receive_diff import receiving_diff_service



# 门店运营-收货差异单
class StoreReceiveDiffAPI(StoreReceiveDiffServiceServicer, OauthGrpcAPI):

    @authorization_scope('boh.store.diff.view')
    def GetReceiveDiffById(self, request, context):
        partner_id, user_id = get_user_info(context)
        receiving_diff_id = request.id
        receiving_diff_detail = receiving_diff_service.get_receiving_diff_by_id(partner_id=partner_id,receiving_diff_id=receiving_diff_id)
        return ReceiveDiff(**receiving_diff_detail)

    @authorization_scope('boh.store.diff.view')
    def ListReceiveDiff(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.store_ids[:] = branch_list_scope_check(
                        partner_id, user_id, 'store', 'boh.store', request.store_ids)
        count, receiving_diff_list = receiving_diff_service.list_receiving_diffs(partner_id, user_id, request)
        receiving_diff_list = {'rows':receiving_diff_list, 'total':count}
        return ListReceiveDiffResponse(**receiving_diff_list)
    
    @authorization_scope('boh.store.diff.view')
    def GetReceiveDiffProductById(self, request, context):
        partner_id, user_id = get_user_info(context)
        diff_id = request.id
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        sort = request.sort
        order = request.order
        count, receiving_diff_products_list = receiving_diff_service.list_receiving_diff_products_by_diff_id(diff_id,limit, offset, include_total, sort, order, partner_id)
        receiving_diff_products_list = {'rows':receiving_diff_products_list, 'total':count}
        return GetReceiveDiffProductByIdResponse(**receiving_diff_products_list)

    @authorization_scope('boh.store.diff.maintain')
    def SubmitReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.submit_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return CommonResponse(**result)

    @authorization_scope('boh.store.diff.audit')
    def ConfirmReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.confirm_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return CommonResponse(**result)

    @authorization_scope('boh.store.diff.audit')
    def RejectReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        reject_reason = request.reject_reason
        attachments = request.attachments
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.reject_receiving_diff(receiving_diff_id, partner_id, user_id, reject_reason, attachments)
        result = {'payload': result}
        return CommonResponse(**result)

    @authorization_scope('boh.store.diff.maintain')
    def UpdateReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        products = request.products
        attachments = request.attachments
        remark = request.remark
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.update_receiving_diff(receiving_diff_id, partner_id, user_id, products, attachments, remark)
        result = {'payload': result}
        return CommonResponse(**result)

    @authorization_scope('boh.store.diff.maintain')
    def DeleteReceiveDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.delete_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return CommonResponse(**result)


    @authorization_scope('boh.store.diff.maintain')
    def CreateReceivingDiff(self, request, context):
        receiving_id = request.receiving_id
        partner_id, user_id = get_user_info(context)
        receiving_diff_id = receiving_diff_service.create_receive_diff_by_hand(request, partner_id, user_id,
                                                                               diff_type='HC')
        result = {'receiving_diff_id': receiving_diff_id}
        return CreateReceivingDiffResponse(**result)