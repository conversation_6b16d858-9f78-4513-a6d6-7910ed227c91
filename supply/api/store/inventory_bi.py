# -*- coding: utf-8 -*-

from supply import request_info
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info, pb2dict
from supply.utils.auth import authorization_scope, branch_list_scope_check, branch_scope_check

from supply.proto.store.inventory_bi_pb2_grpc import StoreInventoryBiServiceServicer
from supply.proto.store.inventory_bi_pb2 import RealtimeInventoryResponse, DailyInventoryResponse, \
        QueryInventoryLogResponse, RealtimeInventoryByAccountsResponse, SummaryInventoryByTimeResponse,\
        UploadInventoryResponse, GetInventoryUploadResponse, InventoryUploadDetails, CommonRes,\
        GetInventoryUploadDetailsResponse
from supply.module.inventory_bi import inventory_bi_service
from supply.module.upload_inventory import upload_inventory_service


# 门店运营-库存
authorization = authorization_scope('boh.store.inventory.view')
class StoreInventoryBiAPI(StoreInventoryBiServiceServicer, OauthGrpcAPI):

    # 门店实时库存
    @authorization
    def RealtimeInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.branch_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store', request.branch_ids)
        inventory_detail, count = inventory_bi_service.get_realtime_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return RealtimeInventoryResponse(**result)

    # 门店流水库存
    @authorization
    def QueryInventoryLog(self, request, context):
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id, user_id, 'store', 'boh.store', request.branch_id)
        inventory_log, total, amount_sum = inventory_bi_service.query_inventory_log_list(request, partner_id, user_id)
        result = {'rows':inventory_log, 'total':total, 'amount_sum':amount_sum}
        return QueryInventoryLogResponse(**result)

    # 每日库存
    @authorization
    def DailyInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id, user_id, 'store', 'boh.store', request.branch_id)
        inventory_detail, count = inventory_bi_service.get_daily_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return DailyInventoryResponse(**result)

    # 每日库存
    @authorization
    def RealtimeInventoryByAccounts(self, request, context):
        """按账户和指定商品查询实时库存，包装给前端用
            查询库存前校验当前账户是否有子账户，若有返回子账户的库存
        """
        partner_id, user_id = get_user_info(context)
        ret = inventory_bi_service.get_realtime_inventory_by_accounts(request, partner_id, user_id)
        return RealtimeInventoryByAccountsResponse(**ret)


    @authorization
    def SummaryInventoryByTime(self, request, context):
        """
        时段汇总库存查询
        """
        partner_id, user_id = get_user_info(context)
        daily_inventory_list, total = inventory_bi_service.get_summary_inventory_by_summary(request, partner_id, user_id)
        result = {'rows': daily_inventory_list, 'total': total}
        return SummaryInventoryByTimeResponse(**result)

    @authorization_scope('boh.store_management.upload_inv.maintain')
    def UploadInventory(self, request, context):
        """
        导入初始化库存
        """
        partner_id, user_id = get_user_info(context)
        ret = upload_inventory_service.upload_inventory(request, partner_id, user_id)
        return UploadInventoryResponse(**ret)

    @authorization_scope('boh.store_management.upload_inv.view')
    def GetInventoryUpload(self, request, context):
        """
        获取导入记录
        """
        partner_id, user_id = get_user_info(context)
        ret = upload_inventory_service.get_inventory_upload_list(request, partner_id, user_id)
        return GetInventoryUploadResponse(**ret)

    @authorization_scope('boh.store_management.upload_inv.view')
    def GetInventoryUploadDetails(self, request, context):
        """
        获取导入详情
        """
        partner_id, user_id = get_user_info(context)
        rows = upload_inventory_service.get_inventory_upload_details(request, partner_id, user_id)
        ret = {"total": len(rows), "rows": rows}
        return GetInventoryUploadDetailsResponse(**ret)

    @authorization_scope('boh.store_management.upload_inv.maintain')
    def ApproveInventoryUpload(self, request, context):
        """
        确认导入结果
        """
        partner_id, user_id = get_user_info(context)
        id = request.id
        ret = upload_inventory_service.approve_inventory_upload(id, partner_id, user_id)
        return CommonRes(**ret)

    @authorization_scope('boh.store_management.upload_inv.maintain')
    def CancelInventoryUpload(self, request, context):
        """
        取消导入
        """
        partner_id, user_id = get_user_info(context)
        id = request.id
        ret = upload_inventory_service.cancel_inventory_upload(id, partner_id, user_id)
        return CommonRes(**ret)



