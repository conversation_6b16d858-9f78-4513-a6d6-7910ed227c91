import logging

from supply.proto.store.adjust_pb2_grpc import StoreAdjustServicer
from supply.proto.store.adjust_pb2 import *
from supply.module.adjust_service import ads as adjust_service
from .. import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from google.protobuf.timestamp_pb2 import Timestamp
from datetime import datetime, timedelta
from supply.utils.adjust_enum import ADS_STATUS
from supply.error.exception import *
from supply.client.metadata_service import metadata_service
from supply.utils.auth import branch_scope_check, branch_list_scope_check


class StoreAdjustApi(StoreAdjustServicer, OauthGrpcAPI):

    # 查询报废单
    @authorization_scope(permission_code="boh.store.loss.view")
    def GetAdjust(self, request, context):
        partner_id, user_id = get_user_info(context)
        store_ids = list(request.branches)
        start_date = request.start_date
        end_date = request.end_date
        branch_type = request.branch_type
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        if start_date.year == 1970:
            raise DataValidationException("请传入查询开始日期")
        if end_date.year == 1970:
            raise DataValidationException("请传入查询结束日期")
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        status = []
        r_status = request.status
        if r_status:
            for r in r_status:
                status.append(ADS_STATUS[r])
        reason_type = request.reason_type
        sources = list(request.sources)
        code = request.code

        product_ids = list(request.product_ids) if request.product_ids else []
        order = request.order
        sort = request.sort
        if not sort:
            sort = 'updated_at'
        store_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                            domain='boh.store', branch_ids=store_ids)
        res = adjust_service.list_store_adjust(partner_id=partner_id, start_date=start_date,
                                               end_date=end_date,
                                               store_ids=store_ids, limit=limit, offset=offset,
                                               include_total=include_total, status=status,
                                               reason_type=reason_type,
                                               code=code, order=order, sort=sort, branch_type=branch_type,
                                               user_id=user_id, sources=sources, product_ids=product_ids)
        return GetAdjustResponse(**res)

    # 报废商品查询
    @authorization_scope(permission_code="boh.store.loss.view")
    def GetAdjustProduct(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        adjust_id = request.adjust_id
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id, user_id, True)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=adjust_obj.adjust_store)
        data = adjust_service.list_store_adjust_product(adjust_id, limit=limit, offset=offset,
                                                        include_total=include_total, partner_id=partner_id,
                                                        user_id=user_id)

        if include_total:
            if data is None:
                return GetAdjustProductResponse(**res)
            res['total'] = data[0]
            products = data[1]
        else:
            if data is None:
                return GetAdjustProductResponse(**res)
            products = data
        rows = []
        position_map = {}
        position_ids = []
        for p in products:
            position_id = p.get('position_id', 1)
            if not position_id:
                position_id = 1
            if position_map.get(position_id):
                position_map[position_id].append(p)
            else:
                position_map[position_id] = [p]
                if position_id != 1:
                    position_ids.append(position_id)
        position_list_ret = metadata_service.list_positions(return_fields="id,code,name",
                                                            ids=position_ids,
                                                            partner_id=partner_id, user_id=user_id).get(
            'rows', [])
        position_dict = {}
        if position_list_ret:
            for p in position_list_ret:
                position_dict[int(p['id'])] = [p.get('fields', {}).get('code', ''),
                                               p.get('fields', {}).get('name', '')]
        for position_id, ps in position_map.items():
            data = dict(
                position_id=position_id,
                position_code=position_dict[position_id][0] if position_id in position_dict else '',
                position_name=position_dict[position_id][1] if position_id in position_dict else '',
                products=ps,
                total=len(ps),
            )
            rows.append(data)
        res['position_rows'] = rows
        return GetAdjustProductResponse(**res)

    # 查询一个报废单
    @authorization_scope(permission_code="boh.store.loss.view")
    def GetAdjustByID(self, request, context):
        res = {}
        adjust_id = request.adjust_id
        partner_id, user_id = get_user_info(context)
        if not adjust_id:
            return Adjust(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id, user_id, True)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=adjust_obj.adjust_store)
            props = adjust_obj.props()
            props['id'] = props['adjust_id']
            del props['adjust_id']
            res = props
            return Adjust(**res)
        return Adjust(**res)

    # 提交一个报废单
    @authorization_scope(permission_code="boh.store.loss.maintain")
    def SubmitAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        if not adjust_id:
            return SubmitAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if not adjust_obj:
            raise DataValidationException('没有该报废单')
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=adjust_obj.adjust_store)
        res['result'] = adjust_service.submit_adjust(adjust_id, partner_id, user_id)
        return SubmitAdjustResponse(**res)

    # 审核一个报废单
    @authorization_scope(permission_code="boh.store.loss.audit")
    def ApproveAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        if not adjust_id:
            return ApproveAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if not adjust_obj:
            raise DataValidationException('没有该报废单')
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=adjust_obj.adjust_store)
        res['result'] = adjust_service.approve_adjust(adjust_id, partner_id, user_id)
        return ApproveAdjustResponse(**res)

    # 驳回一个报废单
    @authorization_scope(permission_code="boh.store.loss.audit")
    def RejectAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        reject_reason = request.reject_reason
        if not adjust_id:
            return RejectAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if not adjust_obj:
            raise DataValidationException('没有该报废单')
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=adjust_obj.adjust_store)
        res['result'] = adjust_service.reject_adjust(adjust_id, partner_id, user_id, reject_reason=reject_reason)
        return RejectAdjustResponse(**res)

    # 确认一个报废单
    @authorization_scope(permission_code="boh.store.loss.maintain")
    def ConfirmAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        adjust_id = request.adjust_id
        branch_type = request.branch_type
        if not adjust_id:
            return ConfirmAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if not adjust_obj:
            raise DataValidationException('没有该报废单')
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=adjust_obj.adjust_store)
        res['result'] = adjust_service.adjust_confirmed(adjust_id, branch_type, partner_id, user_id, username)
        return ConfirmAdjustResponse(**res)

    # 查询门店可报废商品
    @authorization_scope(permission_code="boh.store.loss.maintain")
    def GetAdjustProductByStoreID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        limit = request.limit
        if not limit:
            limit = -1
        offset = request.offset
        include_total = request.include_total
        adjust_date = request.adjust_date
        search = request.search
        search_fields = request.search_fields
        category_ids = list(request.category_ids)
        if adjust_date:
            timestamp = Timestamp()
            timestamp.seconds = adjust_date.seconds
            adjust_date = timestamp.ToDatetime()
            adjust_date = datetime(int(adjust_date.year), int(adjust_date.month), int(adjust_date.day))
        else:
            adjust_date = datetime.now()
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=store_id)
        list_adjust_store_product = adjust_service.list_adjust_store_product(
            store_id=store_id, target_date=adjust_date,
            limit=limit, offset=offset,
            include_total=include_total,
            partner_id=partner_id, user_id=user_id,
            search=search,
            search_fields=search_fields,
            category_ids=category_ids,
            order_by=request.order_by,
            combine_result=True
        )
        if include_total:
            if list_adjust_store_product is not None:
                res['total'] = list_adjust_store_product[0]
                res['rows'] = list_adjust_store_product[1]
                res['inventory_unchanged_rows'] = list_adjust_store_product[2]
        else:
            res['rows'] = list_adjust_store_product[0]
            res['inventory_unchanged_rows'] = list_adjust_store_product[1]
        return GetAdjustProductByStoreIDResponse(**res)

    # 手动创建一个报废单
    @authorization_scope(permission_code="boh.store.loss.maintain")
    def CreatedAdjust(self, request, context):
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        request_id = request.request_id
        if not request_id:
            raise NoRequestIDError('没有请求ID')
        adjust_rd = adjust_service.get_adjust_by_request_id(request_id)
        if adjust_rd:
            props = adjust_rd.props()
            return Adjust(**props)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=request.adjust_store)
        adjust_detail = adjust_service.create_adjust(request, partner_id=partner_id, user_id=user_id,
                                                     username=username)
        if adjust_detail:
            props = adjust_detail.props()
            if 'adjust_id' in props:
                props['id'] = props['adjust_id']
                del props['adjust_id']
        return Adjust(**props)

    # 更新一个报废单
    @authorization_scope(permission_code="boh.store.loss.maintain")
    def UpdateAdjust(self, request, context):
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        adjust_obj = adjust_service.list_store_adjust_by_id(request.adjust_id, partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=adjust_obj.adjust_store)
        else:
            raise DataValidationException('无此单据')
        adjust_detail = adjust_service.update_adjust(request, user_id, partner_id, username=username)
        if adjust_detail:
            props = adjust_detail.props()
            if 'adjust_id' in props:
                props['id'] = props['adjust_id']
                del props['adjust_id']
        return Adjust(**props)

    # 删除一个报废单
    @authorization_scope(permission_code="boh.store.loss.maintain")
    def DeleteAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id, partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=adjust_obj.adjust_store)
        else:
            raise DataValidationException('无此单据')
        res['result'] = adjust_service.delete_adjust(adjust_id, partner_id, user_id)
        return DeleteAdjustResponse(**res)

    # 删除报废单的商品
    @authorization_scope(permission_code="boh.store.loss.maintain")
    def DeleteAdjustProduct(self, request, context):
        res = {}
        adjust_id = request.adjust_id
        ids = [int(_id) for _id in list(request.ids)] if request.ids else []
        partner_id, user_id = get_user_info(context)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id=adjust_id, partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=adjust_obj.adjust_store)
        else:
            raise DataValidationException('无此单据')
        res['result'] = adjust_service.delete_adjust_product(adjust_id, ids, partner_id, user_id)
        return DeleteAdjustProductResponse(**res)

    # CancelAdjust取消报废单19
    @authorization_scope(permission_code="boh.store.loss.maintain")
    def CancelAdjust(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        adjust_id = request.adjust_id
        if not adjust_id:
            return CancelAdjustResponse(**res)
        adjust_obj = adjust_service.list_store_adjust_by_id(adjust_id=adjust_id, partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=adjust_obj.adjust_store)
        else:
            raise DataValidationException('无此单据')
        res['result'] = adjust_service.adjusts_cancel([adjust_id], partner_id, user_id)
        return CancelAdjustResponse(**res)

    @authorization_scope(permission_code="boh.store_bi.detail.view")
    def GetAdjustBiCollect(self, request, context):
        """报废单汇总报表"""
        res = {}
        partner_id, user_id = get_user_info(context)
        category_ids = list(request.category_ids)
        product_name = request.product_name
        start_date = request.start_date
        end_date = request.end_date
        branch_type = request.branch_type
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        hour_offset = request.hour_offset
        reason_type = request.reason_type
        if start_date.year == 1970:
            raise DataValidationException("请传入查询开始日期")
        if end_date.year == 1970:
            raise DataValidationException("请传入查询结束日期")
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        store_ids = list(request.store_ids)
        # 权限校验
        store_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                            domain='boh.store_bi', branch_ids=store_ids)
        
        bom_product_id = list(request.bom_product_id)
        period_symbol = request.period_symbol
        order = request.order
        sort = request.sort
        if not sort:
            sort = 'store_code'
        data, total, sum_quantity, sum_accounting_quantity, sum_qty, sum_accounting_qty = adjust_service.bi_get_adjust_collect(
            partner_id,
            user_id,
            category_ids=category_ids,
            product_name=product_name,
            bom_product_id=bom_product_id,
            start_date=start_date,
            end_date=end_date,
            branch_type=branch_type,
            limit=limit,
            offset=offset,
            include_total=include_total,
            store_ids=store_ids,
            period_symbol=period_symbol,
            reason_type=reason_type,
            order=order,
            sort=sort,
            hour_offset=hour_offset)
        if include_total:
            if data is None:
                return GetAdjustBiCollectResponse(**res)
            res['total'] = {}
            res['total']['count'] = total
            res['total']['sum_quantity'] = sum_quantity
            res['total']['sum_accounting_quantity'] = sum_accounting_quantity
            res['total']['sum_qty'] = sum_qty
            res['total']['sum_accounting_qty'] = sum_accounting_qty
            res['rows'] = data
            return GetAdjustBiCollectResponse(**res)
        else:
            if data is None:
                return GetAdjustBiCollectResponse(**res)
            res['rows'] = data
            return GetAdjustBiCollectResponse(**res)

    @authorization_scope(permission_code="boh.store_bi.detail.view")
    def GetAdjustCollectDetailed(self, request, context):
        """报废单明细汇总报表"""
        res = {}
        partner_id, user_id = get_user_info(context)
        category_ids = list(request.category_ids)
        product_name = request.product_name
        start_date = request.start_date
        end_date = request.end_date
        branch_type = request.branch_type
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        reason_type = request.reason_type
        if start_date.year == 1970:
            raise DataValidationException("请传入查询开始日期")
        if end_date.year == 1970:
            raise DataValidationException("请传入查询结束日期")
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        store_ids = list(request.store_ids)
        # 权限校验
        store_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                            domain='boh.store_bi', branch_ids=store_ids)
        bom_product_id = list(request.bom_product_id)
        order = request.order
        sort = request.sort
        if not sort:
            sort = 'store_code'
        data, total, sum_quantity, sum_accounting_quantity, sum_qty, sum_accounting_qty = adjust_service.bi_get_adjust_detailed(
            partner_id,
            user_id,
            category_ids=category_ids,
            product_name=product_name,
            bom_product_id=bom_product_id,
            start_date=start_date,
            end_date=end_date,
            branch_type=branch_type,
            reason_type=reason_type,
            limit=limit,
            offset=offset,
            include_total=include_total,
            store_ids=store_ids,
            order=order,
            sort=sort)
        if include_total:
            if data is None:
                return GetAdjustCollectDetailedResponse(**res)
            res['total'] = {}
            res['total']['count'] = total
            res['total']['sum_quantity'] = sum_quantity
            res['total']['sum_accounting_quantity'] = sum_accounting_quantity
            res['total']['sum_qty'] = sum_qty
            res['total']['sum_accounting_qty'] = sum_accounting_qty
            res['rows'] = data
            return GetAdjustCollectDetailedResponse(**res)
        else:
            if data is None:
                return GetAdjustCollectDetailedResponse(**res)
            res['rows'] = data
            return GetAdjustCollectDetailedResponse(**res)

    def AutoCloseCreatedAdjust(self, request, context):
        """自动关闭前一天未确认的报废表"""
        res = {}
        adjust_date = datetime.strptime(request.adjust_date, "%Y-%m-%d")
        partner_id, user_id = get_user_info(context)
        if not partner_id or not user_id:
            partner_id = request.partner_id
            user_id = request.user_id
        if not adjust_date:
            adjust_date = datetime.now() - timedelta(days=1)
        res['result'] = adjust_service.auto_close_created_adjust(adjust_date, partner_id=partner_id,
                                                                 user_id=user_id)
        return AutoCloseCreatedAdjustResponse(**res)

    # pos端报废 暂时不加权限
    # @authorization_scope(permission_code="boh.store.loss.maintain")
    def CreatedAdjustByCode(self, request, context):
        props = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        request_id = request.request_id
        if not request_id:
            raise NoRequestIDError('没有请求ID')
        adjust_rd = adjust_service.get_adjust_by_code(request_id)
        if adjust_rd:
            props = adjust_rd.props()
            return Adjust(**props)
        adjust_detail = adjust_service.create_adjust_by_code(request, partner_id=partner_id, user_id=user_id,
                                                             username=username, branch_type="STORE")
        if adjust_detail:
            props = adjust_detail.props()
            if 'adjust_id' in props:
                props['id'] = props['adjust_id']
                props['adjust_store_secondary_id'] = request.adjust_store
                del props['adjust_id']
                del props['adjust_store']
        return Adjust(**props)
