# -*- coding:utf-8 -*-
import json
from datetime import datetime, timedelta
import logging

from supply.module.stocktake_service import ssm as stocktake_service
from supply.utils.auth import branch_scope_check, branch_list_scope_check, stocktake_resource_check
from supply.error.exception import *
from supply.proto.store.stocktake_pb2_grpc import StoreStockTakeServicer
from supply.proto.store.stocktake_pb2 import *
from supply.api import OauthGrpcAPI, authorization_scope, get_context_dict
from supply.utils import get_user_info
from supply.client.metadata_service import metadata_service
from supply.driver.Redis import Redis_cli
from google.protobuf.timestamp_pb2 import Timestamp


class StoreStocktakeApi(OauthGrpcAPI, StoreStockTakeServicer):

    # 检查能否确认盘点单status=CHECKED
    # @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def CheckStocktakeByDocID(self, request, context):
        doc_id = request.doc_id
        branch_type = request.branch_type
        partner_id, user_id = get_user_info(context)
        res = stocktake_service.check_confirm_stocktake(doc_id=doc_id, partner_id=partner_id,
                                                        user_id=user_id, branch_type=branch_type)
        return CheckStocktakeByDocIDResponse(**res)

    # 审核盘点单（status=APPROVED)
    @authorization_scope(permission_code="boh.store.stocktake.audit")
    def ApproveStocktakeByDocID(self, request, context):
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        source = request.source
        approve_name = str(request.approve_name).strip() if request.approve_name else username
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
            # 校验盘点类型审核权限
            stoketake_type = doc_obj.type if doc_obj.type else doc_obj.stocktake_type
            stocktake_resource_check(partner_id=partner_id, user_id=user_id, schema='STOCKTAKE_TYPE',
                                     domain='boh.store', stoketake_type=stoketake_type)
        else:
            raise DataValidationException('无此单据')
        attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                         domain='boh.store.stocktake',
                                                                         user_id=user_id).get(
            'attachment_required_control')
        attachments = json.loads(doc_obj.attachments).get("attachments") if doc_obj.attachments else None
        ctx = get_context_dict(context)
        if attachment_required and not attachments and not ctx.get('from_grpc'):
            raise DataValidationException("缺少附件信息, 请保存附件信息")
        res = stocktake_service.approve_stocktake(doc_id, partner_id, user_id, username, approve_name,source=source)
        return ApproveStocktakeByDocIDResponse(**res)

    # 财务驳回（status=REJECTED，部分商品status=REJECTED）
    @authorization_scope(permission_code="boh.store.stocktake.audit")
    def RejectStocktakeProduct(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        reason = request.reason
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
            # 校验盘点类型审核权限
            stoketake_type = doc_obj.type if doc_obj.type else doc_obj.stocktake_type
            stocktake_resource_check(partner_id=partner_id, user_id=user_id, schema='STOCKTAKE_TYPE',
                                     domain='boh.store', stoketake_type=stoketake_type)
        else:
            raise DataValidationException('无此单据')
        res['result'] = stocktake_service.reject_stocktake(doc_id, partner_id, user_id, username, reason)
        return RejectStocktakeProductResponse(**res)

    # 作废盘点单（status=CANCELED）
    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def CancelStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        stocktake_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        if stocktake_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=stocktake_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        res['result'] = stocktake_service.cancel_stocktake(doc_id, partner_id, user_id)
        return CancelStocktakeByDocIDResponse(**res)

    # 获取一个盘点单
    @authorization_scope(permission_code="boh.store.stocktake.view")
    def GetStocktakeByDocID(self, request, context):
        props = {}
        doc_id = request.doc_id
        partner_id, user_id = get_user_info(context)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
            if doc_obj.attachments:
                attachments = json.loads(doc_obj.attachments)
                doc_obj.attachments = attachments.get('attachments', [])
            props = doc_obj.props()
            if 'doc_id' in props:
                props['id'] = props['doc_id']
                del props['doc_id']
        return Stocktake(**props)

    # 查询盘点单
    @authorization_scope(permission_code="boh.store.stocktake.view")
    def GetStocktake(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store', branch_ids=request.store_ids)
        doc_obj_list = stocktake_service.list_stocktake_doc_detail(request, partner_id=partner_id,
                                                                   user_id=user_id)
        total = None
        # logging.info("stocktake_obj {}".format(doc_obj_list))
        if isinstance(doc_obj_list, tuple):
            total, doc_obj_list = doc_obj_list
        res['rows'] = doc_obj_list
        res['total'] = total
        return GetStocktakeResponse(**res)

    # 查询盘点单明细商品
    @authorization_scope(permission_code="boh.store.stocktake.view")
    def GetStocktakeProduct(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        include_unit = request.include_unit
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        category_id = request.category_id
        storage_type = request.storage_type
        product_name = request.product_name
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        products = stocktake_service.list_stocktake_product(doc_id=doc_id, include_unit=include_unit,
                                                            limit=limit, offset=offset,
                                                            include_total=include_total,
                                                            category_id=category_id, storage_type=storage_type,
                                                            product_name=product_name,
                                                            partner_id=partner_id, user_id=user_id,
                                                            branch_id=doc_obj.branch_id)
        total, products = products
        res['total'] = total
        res['position_rows'] = products
        return GetStocktakeProductResponse(**res)

    # 更新盘点单
    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def PutStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        products = request.products
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        attachments = []
        if request.attachments:
            for t in request.attachments:
                attachments.append(dict(
                    type=t.type,
                    url=t.url
                ))
        # attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
        #                                                                  domain='boh.store.stocktake',
        #                                                                  user_id=user_id).get(
        #     'attachment_required_control')
        # if attachment_required and not attachments:
        #     raise DataValidationException("缺少附件信息, 请传入附件信息")
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        res['result'] = stocktake_service.update_stocktake_products_with_quantity(doc_id, products,
                                                                                  allow_status=['INITED',
                                                                                                'REJECTED'],
                                                                                  partner_id=partner_id,
                                                                                  user_id=user_id,
                                                                                  username=username,
                                                                                  branch_id=doc_obj.branch_id,
                                                                                  attachments=attachments)

        return PutStocktakeByDocIDResponse(**res)

    # 获取盘点商品标签
    # @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def GetStocktakeTags(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        branch_ids = request.branch_ids
        tag_name = request.tag_name
        branch_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                             domain='boh.store', branch_ids=branch_ids)
        res['rows'] = stocktake_service.get_stocktake_tags(branch_ids=branch_ids, tag_name=tag_name,
                                                           partner_id=partner_id, user_id=user_id)
        return GetStocktakeTagsResponse(**res)

    # @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def GetStocktakeTagsById(self, request, context):
        ret = dict()
        partner_id, user_id = get_user_info(context)
        tag_id = request.tag_id
        res = stocktake_service.get_stocktake_tags_by_id(tag_id=tag_id, partner_id=partner_id, user_id=user_id)
        if res:
            ret = res.props()
        return StocktakeTags(**ret)

    # 根据条目ID列表删除盘点商品标签条目
    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def DeleteStocktakeProductTags(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        res['result'] = stocktake_service.clean_stocktake_product_tags(request, partner_id)
        return DeleteStocktakeProductTagsResponse(**res)

    # //SubmitStocktakeByDocID 提交盘点单28
    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def SubmitStocktakeByDocID(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        doc_id = request.doc_id
        source = request.source
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                               branch_id=doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                         domain='boh.store.stocktake',
                                                                         user_id=user_id).get(
            'attachment_required_control')
        attachments = json.loads(doc_obj.attachments).get("attachments") if doc_obj.attachments else None
        ctx = get_context_dict(context)
        if attachment_required and not attachments and not ctx.get('from_grpc'):
            raise DataValidationException("缺少附件信息, 请保存附件信息")
        res['result'] = stocktake_service.submit_stocktake(doc_id=doc_id, user_id=user_id, partner_id=partner_id,
                                                           username=username, branch_type=doc_obj.branch_type,source=source)
        return SubmitStocktakeByDocIDResponse(**res)

    # //GetStocktakeBalance盘点单损益报表27
    @authorization_scope(permission_code="boh.store_bi.detail.view")
    def GetStocktakeBalance(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store_bi', branch_ids=request.store_ids)

        doc_obj_list = stocktake_service.get_stocktake_balance(request, partner_id, user_id)
        total = None
        if isinstance(doc_obj_list, tuple):
            total, doc_obj_list = doc_obj_list
        res['rows'] = doc_obj_list
        res['total'] = total
        return GetStocktakeBalanceResponse(**res)

    # //GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29
    @authorization_scope(permission_code="boh.store_bi.detail.view")
    def GetStocktakeBalanceProductGroup(self, request, context):
        res = {}
        doc_id = request.doc_id
        partner_id, user_id = get_user_info(context)
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store_bi', branch_id=doc_obj.branch_id)
        else:
            raise DataValidationException('无此单据')
        res['rows'] = stocktake_service.get_st_balance_product_group_by_doc_id(doc_id, partner_id, user_id)
        return GetStocktakeBalanceProductGroupResponse(**res)

    @authorization_scope(permission_code="boh.store_bi.detail.view")
    def StocktakeBiDetailed(self, request, context):
        """盘点单报表"""
        res = {}
        include_total = request.include_total
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store_bi', branch_ids=request.store_ids)
        data, total, sum_quantity, sum_accounting_quantity = stocktake_service.stocktake_bi_detailed(request,
                                                                                                     partner_id,
                                                                                                     user_id)
        if include_total:
            if data is None:
                return StocktakeBiDetailedResponse(**res)
            res['total'] = {}
            res['total']['count'] = total
            res['total']['sum_quantity'] = sum_quantity
            res['total']['sum_accounting_quantity'] = sum_accounting_quantity
            res['rows'] = data
            return StocktakeBiDetailedResponse(**res)
        else:
            if data is None:
                return StocktakeBiDetailedResponse(**res)
            res['rows'] = data
            return StocktakeBiDetailedResponse(**res)

    # //StocktakeBalanceRegion区域盘点31
    @authorization_scope(permission_code="boh.store_bi.st_balance_region.view")
    def StocktakeBalanceRegion(self, request, context):
        res = {}
        include_total = request.include_total
        partner_id, user_id = get_user_info(context)
        data = stocktake_service.get_st_balance_product_group(request, partner_id, user_id)
        if include_total:
            if data is None:
                return StocktakeBalanceRegionResponse(**res)
            res['total'] = len(data['rows'])
            res['rows'] = data['rows']
            return StocktakeBalanceRegionResponse(**res)
        else:
            if data is None:
                return StocktakeBalanceRegionResponse(**res)
            res['rows'] = data
            return StocktakeBalanceRegionResponse(**res)

    @authorization_scope(permission_code="boh.store.stocktake.view")
    def AdvanceStocktakeDiff(self, request, context):
        partner_id, user_id = get_user_info(context)
        doc_id = request.doc_id
        doc_obj = stocktake_service.get_stocktake_doc_by_id(doc_id, partner_id, is_details=False)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        ret = stocktake_service.get_advance_stocktake_products_diff(doc_id, partner_id, user_id)
        return AdvanceStocktakeDiffResponse(**ret)

    def StocktakeDiffReport(self, request, context):
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store_bi', branch_ids=request.store_ids)
        ret = stocktake_service.get_stocktake_diff_report(request, partner_id, user_id)
        return StocktakeDiffReportResponse(**ret)

    @authorization_scope(permission_code="boh.store.stocktake.view")
    def GetUncompleteDoc(self, request, context):
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        end_date = request.end_date
        timestamp = Timestamp()
        timestamp.seconds = end_date.seconds
        end_date = timestamp.ToDatetime()
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=store_id)
        ret = stocktake_service.check_confirm_stocktake(partner_id=partner_id, user_id=user_id,
                                                        end_date=end_date, store_id=store_id,
                                                        is_home_page=True,
                                                        )
        return GetUncompleteDocResponse(**ret)

    # 重盘接口
    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def RecreateStocktakeDoc(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.recreate_stocktake_doc(request, partner_id, user_id)
        return RecreateStocktakeDocResponse(**ret)

    def GetNewStocktakeId(self, request, context):
        """生成新的盘点单id, 用于手动创建盘点单"""
        res = {"new_doc_id": stocktake_service.getNewStocktakeId()}
        return GetNewStocktakeIdResponse(**res)

    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def ManuallyCreateStocktake(self, request, context):
        """根据门店、盘点时间和商品，手动创建盘点单"""
        res = {}
        calculate_inventory = request.calculate_inventory
        partner_id, user_id = get_user_info(context)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        new_doc_id = request.new_doc_id
        store_id = request.branch_id
        product_list = request.product_list
        utc_target_datetime = datetime.fromtimestamp(request.target_date.seconds)
        source = request.source
        attachments = []
        if request.attachments:
            for t in request.attachments:
                attachments.append(dict(
                    type=t.type,
                    url=t.url
                ))
        attachment_required = metadata_service.get_business_extra_config(partner_id=partner_id,
                                                                         domain='boh.store.stocktake',
                                                                         user_id=user_id).get(
            'attachment_required_control')
        if attachment_required and not attachments:
            raise DataValidationException("缺少附件信息, 请保存附件信息")
        # 创建手动盘点单
        res["result"] = stocktake_service.manually_create_stocktake(partner_id = partner_id,
                                                                    target_date = utc_target_datetime,
                                                                    branch_id = store_id,
                                                                    new_doc_id = new_doc_id,
                                                                    product_list = product_list,
                                                                    calculate_inventory = calculate_inventory,
                                                                    user_id = user_id,
                                                                    username = username,
                                                                    schema = 'store',
                                                                    domain = 'boh.store',
                                                                    branch_type = 'STORE',source=source, attachments=attachments)

        return ManuallyCreateStocktakeResponse(**res)

    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def GetStocktakeProductByStoreID(self, request, context):
        """查询门店可盘点商品"""
        res = {}
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        limit = request.limit
        if not limit:
            limit = -1
        offset = request.offset
        include_total = request.include_total
        search = request.search
        search_fields = request.search_fields
        category_ids = list(request.category_ids)
        attach_price = request.attach_price
        exc_codes  =request.exc_codes
        in_codes = request.in_codes
        exc_upcs = request.exc_upcs
        in_upcs = request.in_upcs
        order_by = request.order_by
        print('request.exc_codes',request.exc_codes)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=store_id)
        list_stocktake_store_product = stocktake_service.list_stocktake_store_product(
            store_id=store_id,
            limit=limit, offset=offset,
            include_total=include_total,
            partner_id=partner_id, user_id=user_id,
            search=search,
            search_fields=search_fields,
            category_ids=category_ids,
            attach_price=attach_price,
            exc_codes=exc_codes,
            in_codes=in_codes,
            exc_upcs=exc_upcs,
            in_upcs=in_upcs,
            order_by=order_by,
            combine_result=True
        )
        if include_total:
            if list_stocktake_store_product is not None:
                res['total'] = list_stocktake_store_product[0]
                res['rows'] = list_stocktake_store_product[1]
                res['inventory_unchanged_rows'] = list_stocktake_store_product[2]
        else:
            res['rows'] = list_stocktake_store_product[0]
            res['inventory_unchanged_rows'] = list_stocktake_store_product[1]
        return GetStocktakeProductByStoreIDResponse(**res)

    @authorization_scope(permission_code="boh.store_bi.detail.view")
    def StocktakeDocStatistics(self, request, context):
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        request.store_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                       domain='boh.store_bi', branch_ids=request.store_ids)
        ret = stocktake_service.stocktake_doc_statistics(request, partner_id, user_id)
        return StocktakeDocStatisticsResponse(**ret)

    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def StocktakeProductImport(self, request, context):
        """仓库/门店盘点单明细商品导入"""
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.stocktake_product_import(request, partner_id, user_id)
        return StocktakeProductImportResponse(**ret)

    @authorization_scope(permission_code="boh.store.stocktake.maintain")
    def UpdateStocktakeImportBatch(self, request, context):
        """仓库/门店盘点单商品导入文件状态修改"""
        partner_id, user_id = get_user_info(context)
        ret = stocktake_service.update_stocktake_import_batch(request, partner_id, user_id)
        return UpdateStocktakeImportBatchResponse(**ret)
