# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.proto.store.self_picking_pb2_grpc import StoreSelfPickingServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info
from supply.module.self_picking import self_picking_service
from supply.proto.store.self_picking_pb2 import *
from supply.utils.auth import branch_scope_check, branch_list_scope_check


class StoreSelfPickingApi(StoreSelfPickingServicer, OauthGrpcAPI):
    """门店自采单业务模块API"""
    @authorization_scope(permission_code="boh.store.self_picking.maintain")
    def CreateStoreSelfPicking(self, request, context):
        """创建门店自采单"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.store', branch_id=request.branch_id)
        response = self_picking_service.create_self_picking(request, user_id=user_id, partner_id=partner_id)
        return CreateStoreSelfPickingResponse(**response)

    @authorization_scope(permission_code="boh.store.self_picking.view")
    def ListStoreSelfPicking(self, request, context):
        """门店自采单列表查询"""
        partner_id, user_id = get_user_info(context)
        request.branch_ids[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                        domain='boh.store', branch_ids=request.branch_ids)
        response = self_picking_service.list_self_picking(request, user_id=user_id, partner_id=partner_id)
        return ListStoreSelfPickingResponse(**response)

    @authorization_scope(permission_code="boh.store.self_picking.view")
    def GetStoreSelfPickingDetail(self, request, context):
        """查询门店自采单详情"""
        partner_id, user_id = get_user_info(context)
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        # 数据权限校验
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.get_self_picking_detail(request, user_id=user_id, partner_id=partner_id)
        return GetStoreSelfPickingDetailResponse(**response)

    @authorization_scope(permission_code="boh.store.self_picking.maintain")
    def UpdateStoreSelfPicking(self, request, context):
        """更新门店自采单"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.update_self_picking(request, user_id=user_id, partner_id=partner_id)
        return UpdateStoreSelfPickingResponse(**response)

    @authorization_scope(permission_code="boh.store.self_picking.maintain")
    def GetPickingProductByStoreId(self, request, context):
        """取得门店可自采商品（相同属性区域）"""
        partner_id, user_id = get_user_info(context)
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store', domain='boh.store',
                           branch_id=request.store_id)
        response = self_picking_service.get_picking_product_by_store(request, user_id=user_id, partner_id=partner_id)
        return GetPickingProductByStoreIdResponse(**response)

    def ChangeSelfPickingStatus(self, request, context):
        """修改订单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                    INITED新建 SUBMITTED提交 CANCELLED取消 SUCCESS已收货
            2、审批:
                    REJECTED驳回 APPROVED审核
        """
        if request.status == "SUBMITTED":
            return self.SubmitSelfPicking(request, context)
        elif request.status == "CANCELLED":
            return self.CancelSelfPicking(request, context)
        elif request.status == "REJECTED":
            return self.RejectSelfPicking(request, context)
        elif request.status == "APPROVED":
            return self.ApproveSelfPicking(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.status))

    @authorization_scope(permission_code="boh.store.self_picking.maintain")
    def SubmitSelfPicking(self, request, context):
        """提交门店自采"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.change_self_picking_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeSelfPickingStatusResponse(**response)

    @authorization_scope(permission_code="boh.store.self_picking.maintain")
    def CancelSelfPicking(self, request, context):
        pass
    
    @authorization_scope(permission_code="boh.store.self_picking.audit")
    def RejectSelfPicking(self, request, context):
        """驳回门店自采"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.change_self_picking_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeSelfPickingStatusResponse(**response)
    
    @authorization_scope(permission_code="boh.store.self_picking.audit")
    def ApproveSelfPicking(self, request, context):
        """审核门店自采"""
        partner_id, user_id = get_user_info(context)
        # 数据权限校验
        doc_obj = self_picking_service.get_self_picking_by_id(receipt_id=request.receipt_id, partner_id=partner_id)
        if doc_obj:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.store', branch_id=doc_obj.branch_id)
        response = self_picking_service.change_self_picking_status(request, user_id=user_id, partner_id=partner_id)
        return ChangeSelfPickingStatusResponse(**response)

