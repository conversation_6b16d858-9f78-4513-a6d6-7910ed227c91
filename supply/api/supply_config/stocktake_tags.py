# -*- coding:utf-8 -*-

from supply.module.stocktake_service import ssm as stocktake_service
from supply.utils.auth import branch_list_scope_check
from supply.proto.supply_config.stocktake_tags_pb2 import *
from supply.proto.supply_config.stocktake_tags_pb2_grpc import StockTakeTagsServicer
from supply.api import OauthGrpcAPI, authorization_scope
from supply.utils import get_user_info


class StocktakeTagsApi(OauthGrpcAPI, StockTakeTagsServicer):
    # 获取盘点商品标签
    @authorization_scope(permission_code="boh.supply_config.stocktake_tag.view")
    def GetStocktakeTags(self, request, context):
        res = {}
        partner_id, user_id = get_user_info(context)
        branch_ids = request.branch_ids
        tag_name = request.tag_name
        branch_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                             domain='boh.store', branch_ids=branch_ids)
        res['rows'] = stocktake_service.get_stocktake_tags(branch_ids=branch_ids, tag_name=tag_name,
                                                           partner_id=partner_id, user_id=user_id)
        return GetStocktakeTagsResponse(**res)

    @authorization_scope(permission_code="boh.supply_config.stocktake_tag.view")
    def GetStocktakeTagsById(self, request, context):
        ret = dict()
        partner_id, user_id = get_user_info(context)
        tag_id = request.tag_id
        res = stocktake_service.get_stocktake_tags_by_id(tag_id=tag_id, partner_id=partner_id, user_id=user_id)
        if res:
            ret = res.props()
        return StocktakeTags(**ret)

    # 增加，删除，更新,获取盘点标签
    @authorization_scope(permission_code="boh.supply_config.stocktake_tag.maintain")
    def ActionStocktakeTags(self, request, context):
        """
        :param request:
        :param context:
        :return:
        action:
            create = 1;
            delete = 2;
            update = 3;
            copy = 4;
        """
        partner_id, user_id = get_user_info(context)
        action = request.action
        name = request.name
        origin_name = request.origin_name
        branch_ids = request.branch_ids
        region_ids = request.region_ids
        add_dimension = request.add_dimension
        tag_ids = request.tag_ids
        branch_id = request.branch_id  # 给复制用
        copy_branch = request.copy_branch
        tags = stocktake_service.action_stocktake_tags(name=name, action=action,
                                                       branch_ids=branch_ids, region_ids=region_ids,
                                                       branch_id=branch_id, copy_branch=copy_branch,
                                                       add_dimension=add_dimension, origin_name=origin_name,
                                                       tag_ids=tag_ids, partner_id=partner_id, user_id=user_id)
        ret = {}
        if action in [1, 2, 3, 4]:
            if tags:
                ret['result'] = True
        return ActionStocktakeTagsResponse(**ret)