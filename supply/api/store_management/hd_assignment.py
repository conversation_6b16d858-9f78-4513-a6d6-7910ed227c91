# -*- coding: utf8 -*-
import traceback
import re
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp

from supply import logger
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.error.exception import DataValidationException 
from supply.module.demand import demand_module, Supply_demand_product
from supply.model.demand.supply_demand import Supply_demand
from supply.proto.store_management.hd_assignment_pb2 import UploadDemandMasterResponse, IdRequest,\
    GetDemandMasterUploadResponse, GetDemandMasterUploadByBatchIdResponse, CommonResponse, \
        ApproveDemandMasterUploadResponse, CancelDemandMasterUploadResponse, GetValidStoreByProductIdResponse,\
            QueryDemandMasterResponse, QueryDemandMasterProductResponse, DemandMasterEntity, QueryDemandMasterProductEntity
from supply.proto.store_management.hd_assignment_pb2_grpc import StoreManagementHdAssignmentServicer
from supply.utils import get_user_info
from supply.utils.helper import handle_list_model_to_message, handle_model_to_message, get_today_datetime
from supply.utils.auth import authorization_scope, branch_list_scope_check, authorization_scope_by_action
from supply.utils.kit import Kit, KitEnum
# 门店运营管理-总部分配
class StoreHdAssignmentApi(OauthGrpcAPI, StoreManagementHdAssignmentServicer):

    # 导入总部帮订
    @authorization_scope('boh.store_management.hd_assignment.maintain')
    def UploadDemandMaster(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().upload_demand_master(request, partner_id, user_id)
        return UploadDemandMasterResponse(**ret)

    # 获取总部帮订导入记录
    @authorization_scope('boh.store_management.hd_assignment.view')
    def GetDemandMasterUpload(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().get_demand_master_upload(request, partner_id, user_id)
        return GetDemandMasterUploadResponse(**ret)

    # 获取总部帮订导入记录详情
    @authorization_scope('boh.store_management.hd_assignment.view')
    def GetDemandMasterUploadByBatchId(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().get_demand_master_upload_by_batch_id(request, partner_id)
        return GetDemandMasterUploadByBatchIdResponse(**ret)

    # 审核总部帮订导入记录开始干活
    @authorization_scope('boh.store_management.hd_assignment.maintain')
    def ApproveDemandMasterUpload(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().approve_demand_master_upload_by_batch_id(request, partner_id, user_id)
        return ApproveDemandMasterUploadResponse(**ret)

    # 取消该次导入
    @authorization_scope('boh.store_management.hd_assignment.maintain')
    def CancelDemandMasterUpload(self, request, context):
        partner_id, user_id = get_user_info(context)
        ret = demand_module().cancel_demand_master_upload_by_batch_id(request, partner_id, user_id)
        return CancelDemandMasterUploadResponse(**ret)
    
    # 创建商品主配单
    @authorization_scope('boh.store_management.hd_assignment.maintain')
    def CreateProductMain(self, req, ctx):
        """
        创建商品主配单
        """
        partner_id, user_id = get_user_info(ctx)
        demand_date = datetime.fromtimestamp(req.demand_date.seconds)
        arrival_date = datetime.fromtimestamp(req.arrival_date.seconds)
        remark = req.remark
        type = req.type
        sub_type = req.sub_type
        batch_id = req.batch_id
        if not demand_date or not arrival_date:
            raise DataValidationException("请填写订货日期或者到货日期")
        if demand_date.year == 1970 or arrival_date.year == 1970:
            raise DataValidationException("请填写订货日期或者到货日期")
        if type == 'MD' and sub_type == 'PRODUCT':
            # 商品主配
            items = [{"store_id": item.store_id,
                      "product_items": [
                          {
                                "product_id": i.product_id, 
                                "quantity": i.quantity, 
                                "distribution_type": i.distribution_type,
                                "distribution_by": i.distribution_by
                            }
                        for i in item.product_items]
                    }
                    for item in req.items]
            demand_module().create_product_main(items, demand_date, arrival_date, remark, user_id, partner_id, batch_id, type)
        else:
            raise DataValidationException("订货单类型错误")
        return IdRequest(id=1)

    # 查询可主配商品
    @authorization_scope('boh.store_management.hd_assignment.view')
    def GetValidStoreByProductId(self, req, ctx):
        partner_id, user_id = get_user_info(ctx)
        product_ids = req.product_ids
        branch_ids = list(req.branch_ids)
        category_ids = list(req.category_ids)
        type = req.type
        order_date = datetime.fromtimestamp(req.order_date.seconds)
        rows, total = demand_module.get_valid_store_product(product_ids, type, partner_id, user_id, order_date,
                                                            branch_ids, category_ids)
        return GetValidStoreByProductIdResponse(rows=rows)

    # 枚举主配单
    @authorization_scope('boh.store_management.hd_assignment.view')
    def ListDemandMaster(self, req, ctx):
        """
        查询门店订货单
        """
        partner_id, user_id = get_user_info(ctx)
        req.store_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store_management', req.store_ids)
        result, total = demand_module().list_demand(req, partner_id, user_id)
        result = handle_list_model_to_message(result, DemandMasterEntity)
        return QueryDemandMasterResponse(rows=result, total=total)

    # 主配单详情
    @authorization_scope('boh.store_management.hd_assignment.view')
    def GetDemandMasterDetail(self, req, ctx):
        """
        获取订货单详细
        """
        demand_id = req.id
        partner_id, user_id = get_user_info(ctx)
        demand = demand_module.get_demand_detail(demand_id, partner_id, user_id)
        return handle_model_to_message(demand, DemandMasterEntity)

    # 枚举主配单商品
    @authorization_scope('boh.store_management.hd_assignment.view')
    def GetDemandMasterProductDetail(self, req, ctx):
        """
        获取订货商品
        """
        partner_id, user_id = get_user_info(ctx)
        demand_id = req.id
        products, total = demand_module.get_demand_product(demand_id, partner_id, user_id)
        if not products:
            return QueryDemandMasterProductResponse()
        need_field = [f.name for f in QueryDemandMasterProductEntity.DESCRIPTOR.fields]
        rows = []
        for product in products:
            item = {key: value for (key, value) in product.items() if key in need_field}
            rows.append(QueryDemandMasterProductEntity(**item))
        return QueryDemandMasterProductResponse(rows=rows, total=total)


    # 处理主配单
    # 做权限划分的时候，从业务上做的理解认为总部分配目前不需要审核（历史问题：后端业务定义成审核字段了，其实是提交权限）
    @authorization_scope('boh.store_management.hd_assignment.maintain')
    def DealDemandMasterById(self, req, ctx):
        """
        修改订货单状态
        """
        demand_id = req.id
        status = req.action
        description = req.description
        partner_id, user_id = get_user_info(ctx)

        demand_module().change_demand_status(demand_id, status, description, partner_id, user_id)
        return CommonResponse(description="success")

    # 更新主配商品
    @authorization_scope('boh.store_management.hd_assignment.maintain')
    def UpdateDemandMasterProduct(self, req, ctx):
        """
        商品订货接口
        """
        # UpdateDemandProduct(UpdateDemandProductRequest) returns (UpdateDemandProductRequest)
        partner_id, user_id = get_user_info(ctx)
        product_info = req.product
        demand_id = req.demand_id
        order_date = datetime.fromtimestamp(req.order_date.seconds)
        if order_date.year == 1970:
            order_date = get_today_datetime()
        demand_module.update_or_insert_demand_product(product_info, demand_id, partner_id, user_id, order_date)
        return req

    # 删除主配商品
    @authorization_scope('boh.store_management.hd_assignment.maintain')
    def DeleteDemandMasterProduct(self, req, ctx):
        partner_id, user_id = get_user_info(ctx)
        demand_id = req.demand_id
        demand_product_ids = req.demand_product_ids
        demand = Supply_demand.get(demand_id)
        if demand.partner_id != partner_id:
            raise DataValidationException("此订单不属于该操作员所属商户")

        Supply_demand_product.delete_by_ids(demand_product_ids)
        return CommonResponse(description="success")

    # 更新主配单
    @authorization_scope('boh.store_management.hd_assignment.maintain')
    def UpdateDemandMasterInfo(self, req, ctx):
        """
        更新订单的remark
        """
        demand_id = req.id
        remark = req.remark
        arrival_date = datetime.fromtimestamp(req.arrival_date.seconds)
        partner_id, user_id = get_user_info(ctx)
        demand_module.update_demand_info(demand_id=demand_id, remark=remark, arrival_date=arrival_date, \
            partner_id=partner_id, user_id=user_id)
        return CommonResponse(description="success")




