# -*- coding: utf8 -*-
import traceback
import re
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp

from supply import logger
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.module.demand import demand_module, Supply_demand_product
from supply.module.returns import returns_service
from supply.model.demand.supply_demand import Supply_demand
from supply.proto.store_management.inventory_adjust_pb2 import GetDemandAdjustProductByStoreIdResponse, \
    Response, QueryDemandAdjustResponse, QueryDemandAdjustProductResponse, DemandAdjustEntity,\
        QueryDemandAdjustProductEntity, Returns, CreateReturnResponse, \
    ListReturnResponse, GetReturnProductByIdResponse, ReturnCommonResponse
from supply.proto.store_management.inventory_adjust_pb2_grpc import StoreInventoryAdjustServiceServicer
from supply.error.exception import DataValidationException 
from supply.utils import get_user_info
from supply.utils.helper import  get_today_datetime, handle_list_model_to_message, handle_model_to_message
from supply.utils.auth import authorization_scope, authorization_scope_by_action, branch_list_scope_check




# 门店运营管理-库存调整
class StoreInventoryAdjustApi(OauthGrpcAPI, StoreInventoryAdjustServiceServicer):

    # 获取门店可调整商品
    @authorization_scope('boh.store_management.inventory_adjust.view')
    def GetDemandAdjustProductByStoreId(self, request, context):
        """
        根据门店id查询可订货商品
        """
        partner_id, user_id = get_user_info(context)
        store_id = request.store_id
        type = request.type
        distribution_type = request.distribution_type
        vendor_id = request.vendor_id
        order_date = datetime.fromtimestamp(request.order_date.seconds)
        category_ids = list(request.category_ids)
        if order_date.year == 1970:
            order_date = get_today_datetime()
        rows, total = demand_module().get_demand_adjust_product(store_id, partner_id, user_id,
                                                                distr_type=distribution_type,
                                                                type=type, order_date=order_date,
                                                                vendor_id=vendor_id, category_ids=category_ids)
        return GetDemandAdjustProductByStoreIdResponse(rows=rows, total=total)

    # 创建门店订货调整单
    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def CreateDemandAdjust(self, request, context):
        partner_id, user_id = get_user_info(context)
        demand_module().create_demand_adjust(request, partner_id, user_id)
        return Response(description='success')

    # 审核门店订货调整单
    @authorization_scope_by_action('boh.store_management', 'inventory_adjust')
    def DealDemandAdjustById(self, req, ctx):
        """
        修改订货单状态
        """
        demand_id = req.id
        status = req.action
        description = req.description
        partner_id, user_id = get_user_info(ctx)

        demand_module().change_demand_status(demand_id, status, description, partner_id, user_id)
        return Response(description="success")

    # 删除订货调整单
    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def DeleteDemandAdjust(self, req, ctx):
        demand_id = req.id
        partner_id, user_id = get_user_info(ctx)
        demand_module.delete_demand(demand_id, partner_id, user_id)
        return Response(description="success")

    # 更新订货调整商品
    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def UpdateDemandAdjustProduct(self, req, ctx):
        """
        商品订货接口
        """
        # UpdateDemandProduct(UpdateDemandProductRequest) returns (UpdateDemandProductRequest)


        partner_id, user_id = get_user_info(ctx)
        product_info = req.product
        demand_id = req.demand_id
        order_date = datetime.fromtimestamp(req.order_date.seconds)
        if order_date.year == 1970:
            order_date = get_today_datetime()
        demand_module.update_or_insert_demand_product(product_info, demand_id, partner_id, user_id, order_date)
        return req

    # 删除订货调整商品
    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def DeleteDemandAdjustProduct(self, req, ctx):
        partner_id, user_id = get_user_info(ctx)
        demand_id = req.demand_id
        demand_product_ids = req.demand_product_ids
        demand = Supply_demand.get(demand_id)
        if demand.partner_id != partner_id:
            raise DataValidationException("此订单不属于该操作员所属商户")

        Supply_demand_product.delete_by_ids(demand_product_ids)
        return Response(description="success")

    # 更新订货调整单
    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def UpdateDemandAdjustInfo(self, req, ctx):
        """
        更新订单的remark
        """
        demand_id = req.id
        remark = req.remark
        arrival_date = datetime.fromtimestamp(req.arrival_date.seconds)
        partner_id, user_id = get_user_info(ctx)
        demand_module.update_demand_info(demand_id, remark, arrival_date, partner_id, user_id)
        return Response(description="success")

    # 枚举订货调整单
    @authorization_scope('boh.store_management.inventory_adjust.view')
    def ListDemandAdjust(self, req, ctx):
        """
        查询门店订货单
        """
        partner_id, user_id = get_user_info(ctx)
        req.store_ids[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store_management', req.store_ids)
        result, total = demand_module().list_demand(req, partner_id, user_id)
        result = handle_list_model_to_message(result, DemandAdjustEntity)
        return QueryDemandAdjustResponse(rows=result, total=total)

    # 订货调整单详情
    @authorization_scope('boh.store_management.inventory_adjust.view')
    def GetDemandAdjustDetail(self, req, ctx):
        """
        获取订货单详细
        """
        demand_id = req.id
        partner_id, user_id = get_user_info(ctx)
        demand = demand_module.get_demand_detail(demand_id, partner_id, user_id)
        return handle_model_to_message(demand, DemandAdjustEntity)

    # 枚举订货调整单商品
    @authorization_scope('boh.store_management.inventory_adjust.view')
    def GetDemandAdjustProductDetail(self, req, ctx):
        """
        获取订货商品
        """
        partner_id, user_id = get_user_info(ctx)
        demand_id = req.id
        products, total = demand_module.get_demand_product(demand_id, partner_id, user_id)
        if not products:
            return QueryDemandAdjustProductResponse()
        need_field = [f.name for f in QueryDemandAdjustProductEntity.DESCRIPTOR.fields]
        rows = []
        for product in products:
            item = {key: value for (key, value) in product.items() if key in need_field}
            rows.append(QueryDemandAdjustProductEntity(**item))
        return QueryDemandAdjustProductResponse(rows=rows, total=total)


    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def CreateReturn(self, request, context):
        return_by = request.return_by
        logistics_type = request.logistics_type
        type = request.type
        sub_type = request.sub_type
        return_delivery_date = request.return_delivery_date
        source_id = request.source_id
        source_code = request.source_code
        return_reason = request.return_reason
        remark = request.remark
        attachments = request.attachments
        request_id = request.request_id
        products = request.products
        partner_id, user_id = get_user_info(context)
        res = returns_service.create_return_entrance(return_by=return_by, return_delivery_date=return_delivery_date, 
                                    type=type, sub_type=sub_type, logistics_type=logistics_type,
                                    return_reason=return_reason, products=products, request_id=request_id,
                                    partner_id=partner_id, user_id=user_id,
                                    remark=remark, attachments=attachments, source_id=source_id, source_code=source_code)
        return CreateReturnResponse(**res)

    @authorization_scope('boh.store_management.inventory_adjust.view')
    def ListReturn(self, request, context):
        partner_id, user_id = get_user_info(context)
        request.return_by[:] = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store_management', request.return_by)
        count, return_list = returns_service.list_adjust_returns(partner_id, user_id, request)
        result = {'rows':return_list, 'total':count}
        return ListReturnResponse(**result)
    
    @authorization_scope('boh.store_management.inventory_adjust.view')
    def GetReturnById(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        return_obj = returns_service.get_return_by_id(return_id, partner_id, user_id)
        return Returns(**return_obj)

    @authorization_scope('boh.store_management.inventory_adjust.view')
    def GetReturnProductById(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        limit = request.limit
        offset = request.offset
        sort = request.sort
        order = request.order
        count, return_product_list = returns_service.list_return_products_by_return_id(return_id,limit, offset,sort,order,partner_id)
        result = {'rows':return_product_list, 'total':count}
        return GetReturnProductByIdResponse(**result)

    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def SubmitReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.submit_return(return_id,partner_id, user_id)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_management.inventory_adjust.audit')
    def RejectReturn(self, request, context):
        return_id = request.id
        reject_reason = request.reject_reason
        partner_id, user_id = get_user_info(context)
        result = returns_service.reject_return(return_id, partner_id, user_id,reject_reason)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_management.inventory_adjust.audit')
    def ConfirmReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.confirm_return(return_id, partner_id, user_id)
        result = {'payload': result}
        return ReturnCommonResponse(**result)
    
    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def UpdateReturn(self, request, context):
        partner_id, user_id = get_user_info(context)
        return_id = request.id
        products = request.products
        return_reason = request.return_reason
        remark = request.remark
        return_delivery_date = request.return_delivery_date
        return_to = request.return_to
        attachments = request.attachments
        logistics_type = request.logistics_type
        result = returns_service.update_return_product_quantity(return_id, partner_id, user_id, 
                                                products, return_reason, remark, 
                                                return_delivery_date, return_to,
                                                attachments, logistics_type)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def DeliveryReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.delivery_return(return_id, partner_id, user_id)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_management.inventory_adjust.audit')
    def ApproveReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        trans_type = request.trans_type
        result = returns_service.approve_return(return_id=return_id, partner_id=partner_id, \
            user_id=user_id, trans_type=trans_type)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def DeleteReturn(self, request, context):
        return_id = request.id
        partner_id, user_id = get_user_info(context)
        result = returns_service.delete_return(return_id, partner_id, user_id)
        result = {'payload': result}
        return ReturnCommonResponse(**result)

    @authorization_scope('boh.store_management.inventory_adjust.maintain')
    def CheckReturnAvailableByrec(self, request, context):
        product_detail = request.products
        source_code = request.source_code
        return_id = request.return_id
        partner_id, user_id = get_user_info(context)
        result = returns_service.if_over_return_by_rec(source_code=source_code, product_detail=product_detail, 
                                                    partner_id=partner_id, user_id=user_id, return_id=return_id)
        result = {'payload':result}
        return ReturnCommonResponse(**result)
