# -*- coding: utf-8 -*-
from supply.api import OauthGrpcAPI, exclude_oauth
from supply.utils import get_user_info
from supply.utils import pb2dict


from supply.proto.receiving_diff_pb2_grpc import ReceivingDiffServiceServicer
from supply.proto.receiving_diff_pb2 import CreateReceivingDiffResponse, ReceivingDiff, ListReceivingDiffResponse, \
    GetReceivingDiffProductByIdResponse,SubmitReceivingDiffResponse, UpdateReceivingDiffResponse, ReceivingDiffProduct, \
            RejectReceivingDiffResponse, ConfirmReceivingDiffResponse, UpdateReceivingDiffRemarkResponse,\
            UpdateReceivingDiffColdResponse, DeleteReceivingDiffResponse
from supply.module.receive_diff import receiving_diff_service


class ReceivingDiffAPI(ReceivingDiffServiceServicer, OauthGrpcAPI):

    def CreateReceivingDiff(self, request, context):
        receiving_id = request.receiving_id
        partner_id, user_id = get_user_info(context)
        receiving_diff_id = receiving_diff_service.create_receive_diff(receiving_id, partner_id, user_id)
        result = {'receiving_diff_id': receiving_diff_id}
        return CreateReceivingDiffResponse(**result)

    def GetReceivingDiffById(self, request, context):
        partner_id, user_id = get_user_info(context)
        receiving_diff_id = request.id
        receiving_diff_detail = receiving_diff_service.get_receiving_diff_by_id(partner_id=partner_id,receiving_diff_id=receiving_diff_id)
        return ReceivingDiff(**receiving_diff_detail)
    
    def GetReceivingDiffByArgs(self, request, context):
        partner_id, user_id = get_user_info(context)
        receiving_diff_list = receiving_diff_service.get_receiving_diff_by_args(partner_id=partner_id,request=request)
        receiving_diff_list = {'rows':receiving_diff_list}
        return ListReceivingDiffResponse(**receiving_diff_list)

    def ListReceivingDiff(self, request, context):
        partner_id, user_id = get_user_info(context)
        received_type = request.received_type
        count, receiving_diff_list = receiving_diff_service.list_receiving_diffs(partner_id, user_id, request, received_type=received_type)
        receiving_diff_list = {'rows':receiving_diff_list, 'total':count}
        return ListReceivingDiffResponse(**receiving_diff_list)

    def GetReceivingDiffProductByArgs(self, request, context):
        partner_id, user_id = get_user_info(context)
        delivery_id = request.delivery_id
        receiving_id = request.receiving_id

        count, receiving_diff_products_list = receiving_diff_service.list_receiving_diff_products_by_args(partner_id=partner_id, 
                                                    user_id=user_id, delivery_id=delivery_id, receiving_id=receiving_id)
        receiving_diff_products_list = {'rows':receiving_diff_products_list, 'total':count}
        return GetReceivingDiffProductByIdResponse(**receiving_diff_products_list)
    
    def GetReceivingDiffProductById(self, request, context):
        partner_id, user_id = get_user_info(context)
        diff_id = request.id
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        sort = request.sort
        order = request.order
        count, receiving_diff_products_list = receiving_diff_service.list_receiving_diff_products_by_diff_id(diff_id,limit, offset, include_total, sort, order, partner_id)
        receiving_diff_products_list = {'rows':receiving_diff_products_list, 'total':count}
        return GetReceivingDiffProductByIdResponse(**receiving_diff_products_list)

    def SubmitReceivingDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.submit_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return SubmitReceivingDiffResponse(**result)

    def ConfirmReceivingDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.confirm_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return ConfirmReceivingDiffResponse(**result)

    def RejectReceivingDiff(self, request, context):
        receiving_diff_id = request.id
        reject_reason = request.reject_reason
        attachments = request.attachments
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.reject_receiving_diff(receiving_diff_id, partner_id, user_id, reject_reason, attachments)
        result = {'payload': result}
        return RejectReceivingDiffResponse(**result)

    def UpdateReceivingDiff(self, request, context):
        receiving_diff_id = request.id
        products = request.products
        attachments = request.attachments
        remark = request.remark
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.update_receiving_diff(receiving_diff_id, partner_id, user_id, products, attachments, remark)
        result = {'payload': result}
        return UpdateReceivingDiffResponse(**result)

    def DeleteReceivingDiff(self, request, context):
        receiving_diff_id = request.id
        partner_id, user_id = get_user_info(context)
        result = receiving_diff_service.delete_receiving_diff(receiving_diff_id, partner_id, user_id)
        result = {'payload': result}
        return RejectReceivingDiffResponse(**result)

