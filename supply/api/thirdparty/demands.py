from supply.proto.third_party.demands_pb2_grpc import ThirdPartyDemandServiceServicer
from supply.proto.third_party.demands_pb2 import GetOrderByCodeResponse
from supply.api import OauthGrpcAPI
from supply.module.third_party import ThirdPartyService


class ThirdPartyDemandAPI(ThirdPartyDemandServiceServicer, OauthGrpcAPI):
    def GetDemandByCode(self, request, context):
        code = request.code
        partner_id = request.partner_id
        demand = ThirdPartyService.get_demand_by_code(code, partner_id)
        return GetOrderByCodeResponse(**demand)
