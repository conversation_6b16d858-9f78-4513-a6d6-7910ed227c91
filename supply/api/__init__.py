# -*- coding: utf8 -*-
import functools
import time
import json
from collections import Callable
import traceback
from typing import Iterable

from supply.error.exception import AuthScopeException
from functools import wraps
from supply.client.auth_permission import auth_permission
from supply.utils.auth_mapping import auth_code_mapping

import grpc
from hex_exception import Hex<PERSON>x<PERSON>, MethodUndefined, UnAuthorized

from supply import APP_CONFIG, logger, request_info, sentry_cli
from supply.proto.supply_pb2 import Null

from supply.utils import pb2dict
from jaeger_client import Config
import logging

exc_info_format = '{}.{}[{:.3f}][{}][{}]'


def get_user_info(context):
    """
    :param context: the grpc server side context
    :return: partner_id, user_id
    """
    meta_dict = {entry.key: entry.value for entry in context.invocation_metadata()}
    partner_id, user_id, scope_id = None, None, None
    if "partner_id" in meta_dict:
        partner_id = int(meta_dict["partner_id"])
    if "user_id" in meta_dict:
        user_id = int(meta_dict["user_id"])
    if "scope_id" in meta_dict:
        scope_id = int(meta_dict["scope_id"])
    return partner_id, user_id, scope_id


def get_context_dict(context):
    """
    :param context: the grpc server side context
    :return: context_dict
    """
    meta_dict = {entry.key: entry.value for entry in context.invocation_metadata()}
    if "partner_id" in meta_dict:
        meta_dict["partner_id"] = int(meta_dict["partner_id"])
    if "user_id" in meta_dict:
        meta_dict["user_id"] = int(meta_dict["user_id"])
    if "scope_id" in meta_dict:
        meta_dict["scope_id"] = int(meta_dict["scope_id"])
    if "from_grpc" in meta_dict:
        if meta_dict['from_grpc'] == "true":
            meta_dict['from_grpc'] = True
        else:
            meta_dict['from_grpc'] = False
    return meta_dict


def get_token(context) -> str:
    """
    :param context: the grpc server side context
    :return: token
    """
    for entry in context.invocation_metadata():
        if entry.key == 'token':
            return entry.value
    return ''


def authorization_scope(permission_code):
    """
    :param: permission_code 权限码eg: "boh.warehouse.order.view"
    :return:
    """
    if not permission_code:
        raise AuthScopeException(description='No Permission Code')

    def deco(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            ctx = get_context_dict(args[2])
            partner_id = ctx.get('partner_id')
            user_id = ctx.get('user_id')
            if not partner_id or not user_id:
                raise AuthScopeException(description='No User Info')
            # rpc内部调用不校验权限
            if ctx.get('from_grpc') is True:
                return f(*args, **kwargs)
            # domain截取
            code_list = permission_code.split('.')
            domain = '.'.join((code_list[0], code_list[1]))

            # action_code = auth_code_mapping.get_action_code(branch_type, operation, action)
            if not domain:
                raise AuthScopeException(description='No domain')
            logger.info("domain: {} - permission_code: - {}".format(domain, permission_code))
            result = auth_permission.auth(domain=domain, action=permission_code, partner_id=partner_id, user_id=user_id)
            if result.get('code'):
                logger.warning(
                    "Authorized failed, partner_id:{}, user_id:{}, domain:{}, action:{}".format(partner_id,
                                                                                                user_id,
                                                                                                domain,
                                                                                                permission_code))
                raise AuthScopeException(description='User Unauthorized', detail=result)
            return f(*args, **kwargs)

        return decorated_function

    return deco


def exclude_oauth(func) -> Callable:
    func.__exclude_oauth__ = True
    return func


def grpc_wrapper(cls, func):
    """Wrap errors and finalize DB sessions"""

    @functools.wraps(func)
    def wrapped_func(self, req, ctx):
        st = time.time()
        # span = tracer_report(func.__name__)
        if hasattr(func, '__exclude_oauth__'):
            pass
        else:
            partner_id, user_id, scope_id = get_user_info(ctx)
            if partner_id and user_id:
                request_info.partner_id = partner_id
                request_info.user_id = user_id
                request_info.scope_id = scope_id
            # else:
            #     token = get_token(ctx)
            #     if not token:
            #         raise UnAuthorized("no token!")
            #     else:
            #         resp = oauth_service.introspect_token(token)
            #         request_info.partner_id = resp['user_id']
            #         request_info.user_id = resp['partner_id']
            #         request_info.scope_id = resp['scope_id']
        try:
            res = func(self, req, ctx)
        except Exception as e:
            # handle exception
            msg = pb2dict(req)
            logger.error("exception: api-{}, request, error-{}".format(func.__name__, traceback.format_exc()))
            et = time.time()
            if isinstance(e, HexException):
                # service raised error
                e_msg = str(e)
                exc_info = exc_info_format.format(cls.__name__, func.__name__, et - st, msg, e_msg)
                request_info.data = exc_info
                logger.error(exc_info)
                if sentry_cli:
                    sentry_cli.captureException()
                ctx.set_code(grpc.StatusCode.INTERNAL)
                ctx.set_details(e_msg)
                return Null()
            elif isinstance(e, NotImplementedError):
                hex_exec = MethodUndefined("%s undefined!" % func.__name__)
                e_msg = str(hex_exec)
                exc_info = exc_info_format.format(cls.__name__, func.__name__, et - st, msg, e_msg)
                request_info.data = exc_info
                logger.error(exc_info)
                if sentry_cli:
                    sentry_cli.captureException()
                ctx.set_code(grpc.StatusCode.INTERNAL)
                ctx.set_details(e_msg)
                return Null()
            else:
                # other error
                exc_info = exc_info_format.format(cls.__name__, func.__name__, et - st, msg, e)
                request_info.data = exc_info
                logger.error(exc_info)
                if sentry_cli:
                    sentry_cli.captureException()
                raise
        else:
            et = time.time()
            # req_msg = pb2dict(req)
            exc_info = exc_info_format.format(cls.__name__, func.__name__, et - st, '', '')
            request_info.data = exc_info
            logger.info(exc_info)
        finally:
            # span.finish()
            if 'mysql' in APP_CONFIG:
                from supply.driver.mysql import session as MySQLSession
                MySQLSession.close()
            if 'postgre' in APP_CONFIG:
                from supply.driver.postgre import session as PostgreSession
                PostgreSession.close()
            if 'sqlite' in APP_CONFIG:
                from supply.driver.sqlite import session as SqliteSession
                SqliteSession.close()
            for local_attr_name in dir(request_info):
                if not (local_attr_name.startswith("__") and local_attr_name.endswith("__")):
                    delattr(request_info, local_attr_name)
        return res

    return wrapped_func
log_level = logging.DEBUG
logging.getLogger('').handlers = []
logging.basicConfig(format='%(asctime)s %(message)s', level=log_level)
config = Config(
    config={  # usually read from some yaml config
        'sampler': {
            'type': 'const',
            'param': 1,
        },
        'local_agent': {
            # 注意这里是指定了JaegerAgent的host和port。
            # 根据官方建议为了保证数据可靠性，JaegerClient和JaegerAgent运行在同一台主机内，因此reporting_host填写为127.0.0.1。
            'reporting_host': '127.0.0.1',
            'reporting_port': 6831,
        },
        'logging': True,
    },
    # 这里填写应用名称
    service_name=APP_CONFIG['tracing']['service_name'],
    validate=True
)
# this call also sets opentracing.tracer
tracer = config.initialize_tracer()
def tracer_report(span_str):
    span = construct_span(tracer,span_str)
    return span
def construct_span(tracer,span_str):
    with tracer.start_span(span_str) as span:
        span.log_kv({'event': 'test message', 'life': 42})
        print("tracer.tages: ", tracer.tags)
        with tracer.start_span('AliyunTestChildSpan', child_of=span) as child_span:
            span.log_kv({'event': 'down below'})
        return span
class GrpcAPIMetaClass(type):

    def __new__(cls, class_name, parent_class_tuple, attribute_dict, **kwargs):
        _kwargs = {}
        for key, value in attribute_dict.items():
            if key[0].isupper() and isinstance(value, Callable):
                _kwargs[key] = grpc_wrapper(cls, value)
            else:
                _kwargs[key] = value
        return super(GrpcAPIMetaClass, cls).__new__(cls, class_name, parent_class_tuple, _kwargs)


class GrpcAPI(metaclass=GrpcAPIMetaClass):
    pass


class HexOauthMixin(object):
    pass


class OauthGrpcAPI(GrpcAPI, HexOauthMixin):
    pass


def handle_attachments(attachments: str):
    if attachments is None or attachments == '':
        return []
    if attachments.startswith('[{'):
        return json.loads(attachments)
    else:
        attachment_list = []
        ats = eval(attachments)
        for at in ats:
            attachment_list.append({"type": "image", "url": at})
        return attachment_list


def handle_request_attachments(attachments):
    if not attachments:
        return ''
    attachment_list = []
    if isinstance(attachments, Iterable):
        for attachment in attachments:
            at = {"type": attachment.type, "url": attachment.url}
            attachment_list.append(at)
        return json.dumps(attachment_list)
    else:
        return json.dumps({"type": attachments.type, "url": attachments.url})



if __name__ == "__main__":
    # handle_attachments('')
    handle_request_attachments('')
