# -*- coding: utf-8 -*-

from ..utils import pb2dict
from supply import request_info
from ..api import OauthGrpcAPI, exclude_oauth
from ..utils import get_user_info

from ..proto.inventory_bi_pb2_grpc import InventoryBiServiceServicer
from ..proto.inventory_bi_pb2 import RealtimeInventoryResponse, DailyInventoryResponse,\
    QueryAccoutingResponse, QueryInventoryLogResponse, QueryMcuRealtimeInventoryResponse,\
    BatchQueryResponse, QueryInventoryLogInTotalResponse, QuerySnapshotForSalesResponse, \
    QueryBohJdeInventoryDiffResponse, RealtimeInventoryByAccountsResponse
from ..module.inventory_bi import inventory_bi_service
# from ..model.receiving import ReceivingModel


class InventoryBiAPI(InventoryBiServiceServicer, OauthGrpcAPI):

    # 门店实时库存
    def RealtimeInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        inventory_detail, count = inventory_bi_service.get_realtime_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return RealtimeInventoryResponse(**result)

    # 门店流水库存
    def QueryInventoryLog(self, request, context):
        partner_id, user_id = get_user_info(context)
        inventory_log, total, amount_sum = inventory_bi_service.query_inventory_log_list(request, partner_id, user_id)
        result = {'rows':inventory_log, 'total':total, 'amount_sum':amount_sum}
        return QueryInventoryLogResponse(**result)

    # 仓库实时库存
    def QueryMcuRealtimeInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        mcu_inventory_detail, count = inventory_bi_service.query_mcu_realtime_inventory(request, partner_id, user_id)
        result = {'rows':mcu_inventory_detail, 'total':count}
        return QueryMcuRealtimeInventoryResponse(**result)

    # 每日库存
    def DailyInventory(self, request, context):
        partner_id, user_id = get_user_info(context)
        inventory_detail, count = inventory_bi_service.get_daily_inventory(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return DailyInventoryResponse(**result)

    # 实时查询库存流水
    def QueryInventoryLogInTotal(self, request, context):
        partner_id, user_id = get_user_info(context)
        inventory_detail, count = inventory_bi_service.get_inventory_log_in_total(request, partner_id, user_id)
        result = {'rows':inventory_detail, 'total':count}
        return QueryInventoryLogInTotalResponse(**result)

    # 根据业务类型&end_time&branch_id来获取库存切片数据
    def QuerySnapshotForSales(self, request, context):
        partner_id, user_id = get_user_info(context)
        code = request.code
        biz_date = request.biz_date
        store_id = request.store_id
        snapshot_res = inventory_bi_service.get_snapshot_for_sales(code, biz_date, store_id, partner_id, user_id)
        result = {'rows':snapshot_res}
        return QuerySnapshotForSalesResponse(**result)


    def BatchQuery(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = inventory_bi_service.bacth_query_by_id(request, partner_id, user_id)
        # result = {'rows':inventory_detail, 'total':count}
        return BatchQueryResponse(**result)


    def QueryAccouting(self, request, context):
        partner_id, user_id = get_user_info(context)
        result, count = inventory_bi_service.query_accounting(request, partner_id, user_id)
        
        return DailyInventoryResponse(**result)

    def RealtimeInventoryByAccounts(self, request, context):
        """按账户和指定商品查询实时库存，包装给前端用
            查询库存前校验当前账户是否有子账户，若有返回子账户的库存
        """
        partner_id, user_id = get_user_info(context)
        ret = inventory_bi_service.get_realtime_inventory_by_accounts(request, partner_id, user_id)
        return RealtimeInventoryByAccountsResponse(**ret)



