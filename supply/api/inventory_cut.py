# -*- coding: utf-8 -*-

from ..utils import pb2dict
from supply import request_info
from ..api import OauthGrpcAPI, exclude_oauth
from ..utils import get_user_info

from ..proto.inventory_cut_pb2_grpc import InventoryCutServiceServicer
from ..proto.inventory_cut_pb2 import CutDailySnapshotResponse, ListCostTriggerLogResponse, \
    ListPeriodResponse, TriggerCostCountResponse

from ..proto.reports.reports_pb2_grpc import ReportsServicer

from ..module.inventory_cut import inventory_cut_service
from ..module.cost_engine_service import cost_engine_trigger_service

class InventoryCutAPI(InventoryCutServiceServicer, OauthGrpcAPI):

    # 每日库存切片
    def CutDailySnapshot(self, request, context):
        partner_id, user_id = get_user_info(context)
        branch_id = request.branch_id
        product_ids = request.product_ids
        end_date = request.end_date
        result = inventory_cut_service.recut_inventory_by_store(store_id=branch_id,end_date=end_date, partner_id=partner_id, user_id=user_id, product_ids=product_ids)
        # result = {'rows':inventory_detail, 'total':count}
        return CutDailySnapshotResponse(**result)

    def ListCostTriggerLog(self, request, context):
        partner_id, user_id = get_user_info(context)
        start_time = request.start_time
        end_time = request.end_time
        period_ids = request.period_ids
        status = request.status
        limit = request.limit
        offset = request.offset
        req_types = list(request.req_types)
        count, log_list = cost_engine_trigger_service.list_cost_trigger_log(start_time=start_time, end_time=end_time,
                                                                            period_ids=period_ids, status=status,
                                                                            limit=limit, offset=offset,
                                                                            partner_id=partner_id, req_types=req_types)
        result = {'rows':log_list, 'total':count}
        return ListCostTriggerLogResponse(**result)

    def ListPeriod(self, request, context):
        partner_id, user_id = get_user_info(context)
        start_time = request.start_time
        end_time = request.end_time
        period_ids = request.period_ids
        status = request.status
        limit = request.limit
        offset = request.offset
        branch_id = request.branch_id
        count, period_list = cost_engine_trigger_service.list_period(start_time=start_time, end_time=end_time, 
                        branch_id=branch_id, period_ids=period_ids, 
                        status=status, limit=limit, offset=offset, partner_id=partner_id)
        result = {'rows':period_list, 'total':count}
        return ListPeriodResponse(**result)

    def TriggerCostCount(self, request, context):
        partner_id, user_id = get_user_info(context)
        start_date = request.start_date
        end_date = request.end_date
        product_ids = request.product_ids
        branch_id = request.branch_id
        branch_type = request.branch_type
        period_id = request.period_id
        task_type = request.task_type
        report_type = request.report_type
        extra_type = request.extra_type
        
        ret = inventory_cut_service.trigger_cost_count_material(branch_id=branch_id, branch_type=branch_type,
            start_date=start_date, end_date=end_date, partner_id=partner_id, user_id=user_id,
            product_ids=product_ids, period_id=period_id, task_type=task_type, report_type=report_type,
            extra_type=extra_type)
        return TriggerCostCountResponse(**ret)


