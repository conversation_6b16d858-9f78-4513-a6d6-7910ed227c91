# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.utils import get_user_info
from supply.utils.auth import branch_list_scope_check
from supply.api import OauthGrpcAPI, authorization_scope, get_context_dict
from supply.proto.frs_management.franchisee_refund_pb2_grpc import FranchiseeRefundServiceServicer
from supply.proto.frs_management.franchisee_refund_pb2 import *
from supply.module.franchisee.franchisee_refund import franchisee_refund_service


# 加盟商运营模块-退款
class FranchiseeRefundAPI(FranchiseeRefundServiceServicer, OauthGrpcAPI):

    def CreateFRefund(self, request, context):
        """创建退款单(内部rpc创建)"""
        partner_id, user_id = get_user_info(context)
        result = franchisee_refund_service.create_refund(request, partner_id, user_id)
        return CreateFRefundResponse(**result)

    @authorization_scope('boh.frs_hd_management.refund.view')
    def ListFRefund(self, request, context):
        """查询退款单列表"""
        ctx = get_context_dict(context)
        partner_id = ctx.get('partner_id')
        user_id = ctx.get('user_id')
        from_grpc = ctx.get('from_grpc')
        if not from_grpc:
            request.received_bys[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                              domain='boh.frs_hd_management',
                                                              branch_ids=request.received_bys)
        result = franchisee_refund_service.list_refund(request, partner_id, user_id)
        return ListFRefundResponse(**result)

    @authorization_scope('boh.frs_hd_management.refund.view')
    def GetFRefundById(self, request, context):
        """查询退款单详情"""
        partner_id, user_id = get_user_info(context)
        result = franchisee_refund_service.get_refund_by_id(request, partner_id, user_id)
        return GetFRefundByIdResponse(**result)

    def GetRefundHistoryById(self, request, context):
        """查询退款单操作历史"""
        pass

    def DealFRefundById(self, request, ctx):
        """修改订单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:

            2、审批:
                    REJECTED驳回 APPROVED审核
        """
        if request.action == "REJECTED":
            return self.RejectFRefund(request, ctx)
        elif request.action == "APPROVED":
            return self.ApproveFRefund(request, ctx)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.action))

    # @authorization_scope('boh.frs_hd_management.refund.audit')
    def RejectFRefund(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        # 退款单不支持驳回了
        # res = franchisee_refund_service.deal_refund_by_ids(request, allow_status=["INITED"],
        #                                                    partner_id=partner_id, user_id=user_id)
        return DealFRefundByIdResponse(**{})

    # @authorization_scope('boh.frs_hd_management.refund.audit')
    def ApproveFRefund(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = franchisee_refund_service.deal_refund_by_ids(request, allow_status=["INITED"],
                                                           partner_id=partner_id, user_id=user_id)
        return DealFRefundByIdResponse(**res)
