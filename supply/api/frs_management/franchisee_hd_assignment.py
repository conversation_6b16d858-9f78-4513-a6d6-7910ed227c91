# -*- coding: utf8 -*-
from google.protobuf.timestamp_pb2 import Timestamp
from supply.api import OauthGrpcAPI, authorization_scope, get_context_dict
from supply.error.exception import StatusUnavailable
from supply.module.franchisee.franchisee_hd_assignment import frs_hd_assign_service
from supply.module.franchisee.franchisee_demand import franchisee_demand_service
from supply.proto.frs_management.franchise_hd_assignment_pb2_grpc import FranchiseeHdAssignmentServicer
from supply.proto.frs_management.franchise_hd_assignment_pb2 import *
from supply.utils import get_user_info
from supply.utils.auth import branch_list_scope_check
from supply import logger
from supply.model.franchisee.franchisee_demand import DemandAction as Action
import logging

# 加盟商运营管理-总部分配
class FranchiseeHdAssignmentApi(OauthGrpcAPI, FranchiseeHdAssignmentServicer):
    @authorization_scope('boh.frs_hd_management.hd_assignment.maintain')
    def CreateHdDemand(self, request, context):
        """创建总部分配"""
        partner_id, user_id = get_user_info(context)
        result = frs_hd_assign_service.create_hd_demand(partner_id, user_id, request)
        return CommonResponse(**result)

    def GetValidStoreByProduct(self, request, context):
        """根据商品id获取可主配门店"""
        partner_id, user_id = get_user_info(context)
        result = frs_hd_assign_service.get_valid_store_by_product(partner_id, user_id, request)
        return GetValidStoreByProductResponse(**result)

    def GetValidProductByStore(self, request, context):
        """根据门店id获取可主配商品"""
        partner_id, user_id = get_user_info(context)
        result = frs_hd_assign_service.get_valid_product_by_store(partner_id, user_id, request)
        return GetValidProductByStoreResponse(**result)

    @authorization_scope('boh.frs_hd_management.hd_assignment.view')
    def ListHdDemand(self, request, context):
        """查询门店主配单"""
        ctx = get_context_dict(context)
        partner_id = ctx.get('partner_id')
        user_id = ctx.get('user_id')
        from_grpc = ctx.get('from_grpc')
        logging.info("ListHdDemand from grpc:{}".format(from_grpc))
        if not from_grpc:
            request.received_bys[:] = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                              domain='boh.frs_hd_management',
                                                              branch_ids=request.received_bys)
        result = frs_hd_assign_service.list_hd_demand(partner_id, user_id, request)
        return ListHdDemandResponse(**result)

    @authorization_scope('boh.frs_hd_management.hd_assignment.view')
    def GetHdDemandDetail(self, request, context):
        """获取主配单"""
        partner_id, user_id = get_user_info(context)
        result = franchisee_demand_service.get_demand_by_id(request, partner_id, user_id)
        return Demand(**result)

    @authorization_scope('boh.frs_hd_management.hd_assignment.view')
    def GetHdDemandProduct(self, request, context):
        """获取主配单商品"""
        partner_id, user_id = get_user_info(context)
        result = franchisee_demand_service.list_product(request, partner_id, user_id)
        return GetHdDemandProductResponse(**result)

    @authorization_scope('boh.frs_hd_management.hd_assignment.maintain')
    def UpdateHdDemandProduct(self, request, context):
        """更新主配单商品"""
        partner_id, user_id = get_user_info(context)
        result = frs_hd_assign_service.update_hd_demand_product(partner_id, user_id, request)
        return UpdateHdDemandProductResponse(**result)

    def DealHdDemandById(self, request, context):
        """更新主配单商品:
            提交： PREPARE (对门店来讲是待付款)
            作废： INVALID
        """
        if request.action == "PREPARE":
            return self.SubmitHdDemand(request, context)
        elif request.action == "INVALID":
            return self.InvalidHdDemand(request, context)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.action))

    @authorization_scope('boh.frs_hd_management.hd_assignment.maintain')
    def SubmitHdDemand(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = frs_hd_assign_service.deal_hd_demand_by_ids(request, allow_status=["INITED"],
                                                             partner_id=partner_id, user_id=user_id)
        return CommonResponse(**result)

    @authorization_scope('boh.frs_hd_management.hd_assignment.maintain')
    def InvalidHdDemand(self, request, context):
        partner_id, user_id = get_user_info(context)
        result = frs_hd_assign_service.deal_hd_demand_by_ids(request, allow_status=["INITED"],
                                                             partner_id=partner_id, user_id=user_id)
        return CommonResponse(**result)

    # BatchConfirmHdDemand
    # @authorization_scope('boh.frs_hd_management.hd_assignment.confirm')
    @authorization_scope('boh.frs_hd_management.hd_assignment.maintain')
    def BatchConfirmHdDemand(self, request, context):
        """批量确认订单"""
        partner_id, user_id = get_user_info(context)
        res = frs_hd_assign_service.batch_deal_hd_demand(request, partner_id, user_id, allow_status=["INITED"],
                                                          action=request.action)
        return BatchDealHdDemandResponse(**res)

