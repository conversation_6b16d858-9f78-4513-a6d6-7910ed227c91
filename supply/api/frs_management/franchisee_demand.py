# -*- coding: utf-8 -*-
from supply.error.exception import StatusUnavailable
from supply.utils import get_user_info
from supply.api import OauthGrpcAPI, authorization_scope, get_context_dict
from supply.proto.frs_management.franchisee_demand_pb2_grpc import FranchiseeDemandServiceServicer
from supply.proto.frs_management.franchisee_demand_pb2 import *
from supply.module.franchisee.franchisee_demand import franchisee_demand_service
from supply.module.franchisee.franchisee_demand_import import import_franchisee_demand_service


# 加盟商运营模块-订货单
class FranchiseeDemandAPI(FranchiseeDemandServiceServicer, OauthGrpcAPI):

    def CreateFDemandBatch(self, request, context):
        """批量创建订货单(总部分配导入使用)"""
        partner_id, user_id = get_user_info(context)
        result = franchisee_demand_service.create_franchisee_demand_batch(partner_id, user_id, request)
        return CreateFDemandBatchResponse(**result)

    @authorization_scope('boh.frs_hd_management.demand.view')
    def ListFDemand(self, request, context):
        """查询订货单列表"""
        ctx = get_context_dict(context)
        partner_id = ctx.get('partner_id')
        user_id = ctx.get('user_id')
        from_grpc = ctx.get('from_grpc')
        result = franchisee_demand_service.list_franchisee_demand(request, partner_id, user_id, from_grpc=from_grpc)
        return ListFDemandResponse(**result)

    @authorization_scope('boh.frs_hd_management.demand.view')
    def GetFDemandById(self, request, context):
        """查询订货单详情"""
        partner_id, user_id = get_user_info(context)
        result = franchisee_demand_service.get_demand_by_id(request, partner_id, user_id)
        return Demand(**result)

    @authorization_scope('boh.frs_hd_management.demand.view')
    def GetProductsById(self, request, context):
        """查询订货单商品详情"""
        partner_id, user_id = get_user_info(context)
        result = franchisee_demand_service.list_product(request, partner_id, user_id)
        return ProductsResponse(**result)

    @authorization_scope('boh.frs_hd_management.demand.view')
    def GetHistoryById(self, request, context):
        """查询订货单操作历史"""
        partner_id, user_id = get_user_info(context)
        res = franchisee_demand_service.get_demand_history(request, partner_id, user_id)
        return HistoryResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.maintain')
    def UpdateProducts(self, request, context):
        """更新订货单商品详情"""
        ctx = get_context_dict(context)
        partner_id = ctx.get('partner_id')
        user_id = ctx.get('user_id')
        from_grpc = ctx.get('from_grpc')
        # print("request====",request)
        res = franchisee_demand_service.update_products(request, partner_id, user_id, from_grpc=from_grpc)
        return UpdateProductsResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.maintain')
    def GetProductDistribution(self, request, context):
        """获取订货商品可分配仓库"""
        partner_id, user_id = get_user_info(context)
        res = franchisee_demand_service.get_product_distribution(request, partner_id, user_id)
        return GetProductDistributionResponse(**res)

    def DealFDemandById(self, request, ctx):
        """修改订单状态主接口分发
            变更不同状态走不同入口以做权限校验:
            1、维护:
                    SUBMITTED确认收款 R_APPROVE区经审核  CONFIRMED确认订单  APPROVED确认分配
            2、审批:
                    REJECTED未付款驳回
        """
        if request.action == "SUBMITTED":
            return self.SubmitFDemand(request, ctx)
        elif request.action == "R_APPROVE":
            return self.RApproveFDemand(request, ctx)
        elif request.action == "REJECTED":
            return self.RejectFDemand(request, ctx)
        elif request.action == "APPROVING":
            return self.ApprovingFDemand(request, ctx)
        elif request.action == "APPROVED":
            return self.ApproveFDemand(request, ctx)
        elif request.action == 'CONFIRMED':
            return self.ConfirmFDemand(request, ctx)
        elif request.action == "CANCELLED":
            return self.CancelFDemand(request, ctx)
        else:
            raise StatusUnavailable("Status Unavailable - {}".format(request.action))

    @authorization_scope('boh.frs_hd_management.demand.maintain')
    def SubmitFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = franchisee_demand_service.deal_demand_by_ids(request, allow_status=["PREPARE", "P_SUBMIT"],
                                                           partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.audit')
    def RApproveFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = franchisee_demand_service.deal_demand_by_ids(request, allow_status=["SUBMITTED"],
                                                           partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.maintain')
    def RejectFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = franchisee_demand_service.deal_demand_by_ids(request, allow_status=["P_SUBMIT"],
                                                           partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.confirm')
    def ConfirmFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = franchisee_demand_service.deal_demand_by_ids(request, allow_status=["R_APPROVE"],
                                                           partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.maintain')
    def CancelFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = franchisee_demand_service.deal_demand_by_ids(request,
                                                           allow_status=["PREPARE", "REJECTED", "SUBMITTED", "R_APPROVE"],
                                                           partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.maintain')
    def ApprovingFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = franchisee_demand_service.deal_demand_by_ids(request, allow_status=["APPROVING", "CONFIRMED"],
                                                           partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.maintain')
    def ApproveFDemand(self, request, ctx):
        partner_id, user_id = get_user_info(ctx)
        res = franchisee_demand_service.deal_demand_by_ids(request, allow_status=["APPROVING", "CONFIRMED"],
                                                           partner_id=partner_id, user_id=user_id)
        return DealFDemandByIdResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.maintain')
    def AddFDemandProduct(self, request, context):
        """订单添加商品"""
        ctx = get_context_dict(context)
        partner_id = ctx.get('partner_id')
        user_id = ctx.get('user_id')
        from_grpc = ctx.get('from_grpc')
        # res = franchisee_demand_service.add_demand_product(partner_id, user_id, request, from_grpc=from_grpc)
        res = franchisee_demand_service.add_relation_products(partner_id, user_id, request)
        return UpdateProductsResponse(**res)

    def ImportFDemandProducts(self, request, context):
        """订单导入商品"""
        partner_id, user_id = get_user_info(context)
        res = import_franchisee_demand_service.import_frs_demand_products(request, partner_id, user_id)
        return ImportFDemandProductsResponse(**res)

    @authorization_scope('boh.frs_hd_management.demand.confirm')
    def BatchConfirmFDemand(self, request, context):
        """批量确认订单"""
        partner_id, user_id = get_user_info(context)
        res = franchisee_demand_service.batch_deal_demand(request, partner_id, user_id, allow_status=["R_APPROVE"],
                                                          action="CONFIRMED")
        return BatchDealFDemandResponse(**res)

    def BatchResetFDemand(self, request, context):
        """批量还原订单导入"""
        partner_id, user_id = get_user_info(context)
        res = franchisee_demand_service.batch_deal_demand(request, partner_id, user_id, allow_status=["R_APPROVE"],
                                                          action="RESET")
        return BatchDealFDemandResponse(**res)

    def ListFDemandToRemind(self, request, context):
        partner_id, user_id = get_user_info(context)
        res = franchisee_demand_service.list_franchisee_demand_to_demind_usejion(partner_id, user_id, request)
        return ListFDemandToRemindResponse(**res)


