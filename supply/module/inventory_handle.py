import logging

from supply.utils.helper import MessageTopic, convert_to_int, convert_to_decimal
from ..client.inventory_service import inventory_service
from ..utils.helper import get_guid
from ..model.inventory import inventory_repository
from ..utils.inventory_enum import ACTION
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp


def inventory_handler(message):
    """
    目前可以处理加、减、调拨库存操作
    """
    batch_no = message['batch_no'] if 'batch_no' in message else str(get_guid())
    code = message['code'] if 'code' in message else None
    total_action = convert_to_int(message['action']) if 'action' in message else 100
    description = message['description'] if 'description' in message else None
    trace_id = message['trace_id'] if 'trace_id' in message else None
    partner_id = message['partner_id'] if 'partner_id' in message else None
    user_id = message['user_id'] if 'user_id' in message else None
    accounts = message['accounts'] if 'accounts' in message else []
    business_time = message.get('business_time')
    transfer_mode = message.get('transfer_mode')  # 调拨模式
    if not business_time:
        value = datetime.utcnow()
        timestamp = Timestamp()
        timestamp.FromDatetime(value)
        business_time = timestamp
        message["business_time"] = value
    else:
        business_time = str(business_time)
        value = datetime.strptime(business_time, "%Y-%m-%d %H:%M:%S")
        timestamp = Timestamp()
        timestamp.FromDatetime(value)
        business_time = timestamp
    detail = []
    for index, account in enumerate(accounts):
        if not isinstance(account, dict):
            logging.error('Unexpected message format: invalid accounts - %s' % str(accounts))
            return True
        data = {}
        # 库存扣减,100为混合值
        if total_action == 100:
            data['sequence_id'] = index + 1
            data['accounting'] = {}
            data['accounting']["account"] = {}
            sub_account = account.get('sub_account')
            if sub_account:
                data['accounting']['account']['sub_account'] = sub_account
            branch_id = account.get('branch_id') if account.get('branch_id') else 0
            product_id = account.get('product_id') if account.get('product_id') else 0
            action = account.get('action') if account.get('action') else 0
            data['accounting']["account"]["branch_id"] = int(branch_id)
            data['accounting']["account"]["product_id"] = int(product_id)
            data['accounting']["amount"] = convert_to_decimal(account.get('amount', 0))
            data['action'] = int(action)
            data['business_time'] = business_time
            detail.append(data)
        # 盘点
        elif total_action == 13:
            data['sequence_id'] = index + 1
            data['stocktake'] = {}
            data['stocktake']["account"] = {}
            if account.get('sub_account'):
                data['stocktake']['account']['sub_account'] = account.get('sub_account')
            branch_id = account.get('branch_id') if account.get('branch_id') else 0
            product_id = account.get('product_id') if account.get('product_id') else 0
            data['stocktake']["account"]["branch_id"] = int(branch_id)
            data['stocktake']["account"]["product_id"] = int(product_id)
            data['stocktake']["amount"] = convert_to_decimal(account.get('amount', 0))
            data['stocktake']["stocktake_id"] = int(trace_id)
            data["business_time"] = business_time
            detail.append(data)
        # 调拨库存
        elif total_action == 3:
            data['sequence_id'] = index + 1
            data['transfer'] = {}
            data['transfer']["from"] = {}
            data['transfer']["to"] = {}
            from_sub_account = account.get('from_sub_account') if account.get('from_sub_account') else 0
            to_sub_account = account.get('to_sub_account') if account.get('to_sub_account') else 0
            if from_sub_account:
                data['transfer']['from']['sub_account'] = {}
                data['transfer']['from']['sub_account']['id'] = from_sub_account
            if to_sub_account:
                data['transfer']['to']['sub_account'] = {}
                data['transfer']['to']['sub_account']['id'] = to_sub_account
            from_branch_id = account.get('from_branch_id') if account.get('from_branch_id') else 0
            product_id = account.get('product_id') if account.get('product_id') else 0
            to_branch_id = account.get('to_branch_id') if account.get('to_branch_id') else 0
            data['transfer']["from"]["branch_id"] = int(from_branch_id)
            data['transfer']["from"]["product_id"] = int(product_id)
            data['transfer']["to"]["branch_id"] = int(to_branch_id)
            data['transfer']["to"]["product_id"] = int(product_id)
            
            # trasfer_account_extra_receive
            if "EXTERNAL_TRANSFER" in code:
                account_extra_code = "Transfer"
                transfer_type = 3
                account_extra_type = transfer_type
                account_extra = {
                                "code": account_extra_code,  # 调拨库存
                                "type": account_extra_type
                                }
                if transfer_mode:
                    data['transfer']['from']['extra'] = account_extra
                else:
                    data['transfer']['to']['extra'] = account_extra

            elif "INTERNAL_TRANSFER" in code:
                account_extra_code = "Transfer"
                transfer_type = 1
                account_extra_type = transfer_type
                account_extra = {
                    "code": account_extra_code,  # 调拨库存
                    "type": account_extra_type
                }
                if transfer_mode:
                    data['transfer']['from']['extra'] = account_extra
                else:
                    data['transfer']['to']['extra'] = account_extra

            data['transfer']["amount"] = convert_to_decimal(account.get('amount', 0))
            data['business_time'] = business_time
            detail.append(data)
    if code is None or total_action is None or partner_id is None or user_id is None:
        logging.error('Unexpected message format: invalid partner_id,batch_id,batch_content - %s' % message)
        return True
    if not isinstance(accounts, list):
        logging.error('Unexpected message format: invalid accounts - %s' % str(accounts))
        return True
    try:
        # 处理库存扣减
        request = dict(batch_no=str(batch_no), code=code, action=total_action, description=description, detail=detail,
                       trace_id=trace_id)
        deal_inventory_obj = inventory_service.deal_with_inventory(**request, partner_id=partner_id, user_id=user_id)
        batch_id = int(deal_inventory_obj.get('id'))
        inventory_dict = dict(batch_no=str(batch_no), code=code, batch_action=total_action,
                              action_dec=ACTION[total_action],
                              batch_id=batch_id,
                              status=deal_inventory_obj.get('status'),
                              )
        inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                         partner_id=partner_id, user_id=user_id)
        return batch_id
    except Exception as e:
        if inventory_repository.check_has_error_calculate_send_message(str(batch_no), partner_id):
            pass
        else:
            inventory_dict = dict(batch_no=str(batch_no), code=code, batch_action=total_action,
                                  action_dec=ACTION[total_action], status='ERROR')
            inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                             partner_id=partner_id, user_id=user_id)
        logging.error('task[inventory_calculated_handler] error.Error message:%s' % (str(e)))
        raise e
