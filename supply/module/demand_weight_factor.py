from supply.client.metadata_service import metadata_service


def get_weight_factor(partner_id, user_id, store_id, products):
    weight_factors = metadata_service.list_weight_factor(
        partner_id, user_id
    )
    factor_map = {
        (c['product_type'], c['bom_type'], c['sale_type']): {
            'weather_factor': c['weather_factor'],
            'discount_factor': c['discount_factor'],
            'holiday_factor': c['holiday_factor'],
        }
        for c in weight_factors
    }
    ret = {}
    for p in products:
        p_id = int(p.get('product_id', 0))
        product_type = p['product_type']
        bom_type = p['bom_type']
        sale_type = p['sale_type']
        key = (product_type, bom_type, sale_type)
        inventory_factor = factor_map.get(key, 0)
        ret[p_id] = inventory_factor
    return ret
