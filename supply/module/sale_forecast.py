import datetime
import json
import traceback
from collections import defaultdict, OrderedDict
from functools import partial

from supply import logger
from supply.client.metadata_service import metadata_service
from supply.client.report_service import report_service
from supply.driver.mysql import DummyTransaction
from supply.driver.mysql import db_commit
from supply.model.sale_forecast import DemandSuggestSaleForecast as SFDB
from supply.model.sale_forecast import DemandSuggestSaleForecastRepo as SFDBRepo
from supply.module.demand_suggest import utils
# from supply.task import public
from supply.utils.helper import MessageTopic
from supply.driver.mysql import session
from supply.driver.mq import mq_producer


def list_forecast_store(partner_id, user_id, forecast_date, store_ids, limit, offset,
                        store_type=None):
    """SELECT store_id, updated_at, max(updated_by) FROM
(SELECT F1.store_id, F1.updated_by, F1.updated_at
FROM supply_demand_suggest_sale_forecast F1 INNER JOIN
  (SELECT store_id, MAX(updated_at) as max_updated
   FROM supply_demand_suggest_sale_forecast where  partner_id = 2 and forecast_date in ('2019-07-24 00:00:00','2019-07-25 00:00:00','2019-07-26 00:00:00','2019-07-27 00:00:00','2019-07-28 00:00:00','2019-07-29 00:00:00')  and store_type = 'MIX'
   GROUP BY store_id) F2
ON F1.store_id=F2.store_id and F1.updated_at=F2.max_updated) F3
GROUP BY F3.store_id, F3.updated_at;
# 找出group后的最大值的那一行
"""
    print(partner_id, user_id, forecast_date, store_ids, limit, offset, store_type)
    all_week_days = [(forecast_date + datetime.timedelta(days=i)).strftime("'%Y-%m-%d %H:%M:%S'") for i in range(1, 7)]
    inner_where_clause = " partner_id = {} and forecast_date in ({}) ".format(partner_id, ','.join(all_week_days))
    if store_ids:
        inner_where_clause += " and store_id in ({}) ".format(','.join(str(id) for id in store_ids))
    if store_type == "MIX":
        inner_where_clause += " and store_type = 'MIX' "

    inner_sql = """select store_id, max(updated_at) as max_updated
            from supply_demand_suggest_sale_forecast where {where_clause}
            group by store_id
    """.format(where_clause=inner_where_clause)

    middle_sql = """select F1.store_id, F1.updated_by, F1.updated_at
from supply_demand_suggest_sale_forecast F1
INNER JOIN ({inner_sql}) F2
ON F1.store_id=F2.store_id AND F1.updated_at = F2.max_updated
    """.format(inner_sql=inner_sql)

    outer_select_sql = """select store_id, updated_at, max(updated_by) from ({middle_sql}) F3
GROUP BY F3.store_id, F3.updated_at
    """.format(middle_sql=middle_sql)

    outer_count_sql = """select count(*) from ({outer_select_sql}) F4""".format(
        outer_select_sql=outer_select_sql
    )
    if limit > 0:
        outer_select_sql += " limit %s, %s" % (offset, limit)
    # print(outer_select_sql)
    # print(outer_count_sql)
    result = sql_execute(outer_select_sql, ['store_id', 'updated_at', 'updated_by'])
    for f in result:
        f['forecast_date'] = forecast_date
    count = sql_execute(outer_count_sql, scalar=True)
    return result, count


def sql_execute(sql, fields=None, scalar=False):
    with DummyTransaction(auto_commit=False) as trans:
        resproxy = trans.scope_session.execute(sql)
        rows = resproxy.fetchall()
    if scalar:
        return rows[0][0]
    result = []
    for row in rows:
        data = {}
        for i in range(len(fields)):
            data[fields[i]] = row[i]
        result.append(data)
    return result


def list_forecast_product(partner_id, user_id, forecast_date, store_id,order,sort,
                          product_sale_type=None):
    """select * from forecast where partner_id = partner_id and forecast_date=forecast_date
    and store_id in (store_ids) and
    select store_id, product_id, forecast_date, forecast_amount, confirm_amount
    order by
    """
    all_week_days = set(forecast_date + datetime.timedelta(days=i) for i in range(1, 7))
    query = session.query(
        SFDB.store_id, SFDB.product_id, SFDB.forecast_date, SFDB.forecast_amount,
        SFDB.confirm_amount, SFDB.id,
    ).filter(
        SFDB.partner_id == partner_id, SFDB.forecast_date.in_(all_week_days),
        SFDB.store_id == store_id,
    )
    if product_sale_type:
        query = query.filter(SFDB.product_sale_type == product_sale_type)
    if order != 'asc':
        order_sort = 'SFDB.{}.desc()'.format(sort)
        query = query.order_by(eval(order_sort))
    else:
        order_sort = 'SFDB.{}'.format(sort)
        query = query.order_by(eval(order_sort))
    # query = query.order_by(SFDB.product_id, SFDB.forecast_amount)
    forecast_data = OrderedDict()
    for row in query.all():
        product_id = row.product_id
        forecast_date = row.forecast_date
        forecast_amount = row.forecast_amount
        confirm_amount = row.confirm_amount
        if product_id not in forecast_data:
            forecast_data[product_id] = {}
        forecast_data[product_id][forecast_date] = {
            'forecast_amount': forecast_amount,
            'confirm_amount': confirm_amount,
            'id': row.id
        }
    return forecast_data, len(forecast_data)


@db_commit
def confirm_product_forecast(partner_id, user_id, confirm_data):
    ids = confirm_data.keys()
    row = None
    for row in session.query(SFDB).filter(SFDB.partner_id == partner_id, SFDB.id.in_(ids), ).all():
        confirm_amount = confirm_data[row.id]
        row.confirm_amount = confirm_amount
        row.updated_by = user_id
        row.status = 'CONFIRM'
    session.commit()


class SaleForecastTask:

    @staticmethod
    @db_commit
    def trigger_store_sale_forecast(partner_id, user_id):
        """select store_id, forecast_date from sale_forecast where forecast_date in () order by store_id"""

        today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        # 从today(包括)开始，连续七天的日期
        all_week_days = set(today + datetime.timedelta(days=i) for i in range(7))
        forecasts = session.query(SFDB.store_id, SFDB.forecast_date).filter(
            SFDB.forecast_date.in_(all_week_days)
        ).group_by(
            SFDB.store_id, SFDB.forecast_date
        ).order_by(
            SFDB.store_id.desc(), SFDB.forecast_date
        ).all()
        result = defaultdict(set)
        for store_id, forecast_date in forecasts:
            result[store_id].add(forecast_date)
        need_forecast_store_info = defaultdict(set)
        all_stores = SaleForecastTask.list_all_open_stores(partner_id, user_id)

        for store in all_stores:
            store_id = int(store.get('id'))
            if store_id not in result:
                need_forecast_store_info[store_id] = all_week_days
            else:
                need_forecast_dates = all_week_days - result[store_id]
                if need_forecast_dates:
                    need_forecast_store_info[store_id] = need_forecast_dates

        for store_id, need_forecast_dates in need_forecast_store_info.items():
            for date in need_forecast_dates:
                msg = {
                    'store_id': store_id,
                    'biz_date': date,
                    'partner_id': partner_id,
                    'user_id': user_id,
                }
                # func = partial(public, MessageTopic.DEMAND_SUGGEST_FORECAST_STORE_CREATE, msg)
                # success = retry(func, times=3)
                # if not success:
                #     logger.error('check_sale_forecast public store(id=%s, date=%s) fail' % (store_id, date))
        logger.info('check_sale_forecast_for_week execute over, total: %s stores' % len(need_forecast_store_info))

    @staticmethod
    def list_all_open_stores(partner_id, user_id):
        filters = {
            "open_status__in": ['OPENED'],
            "status__in": ["ENABLED"]
        }
        resp = metadata_service.get_store_list(
            filters=filters, partner_id=partner_id, user_id=user_id
        )
        return resp.get('rows', [])

    @staticmethod
    @db_commit
    def create_store_sale_forecast(partner_id, user_id, store_id, biz_date):
        """插入门店对应的茶饮和面包的预测量"""
        biz_date = biz_date.replace(hour=0, minute=0, second=0, microsecond=0)
        SaleForecastTask.clear_store_sale_forecast(
            partner_id, user_id, store_id, biz_date, "INIT"
        )
        product_ids = SaleForecastTask.list_need_forecast_products(
            partner_id, user_id, store_id, biz_date,
        )
        store_type = SaleForecastTask.get_store_type(partner_id, user_id, store_id)
        same_week_days = last_month_same_week_date(biz_date)  # 往后倒推四周
        first_week_day = same_week_days[0]
        if product_ids:
            product_history_sale_map = SaleForecastTask.get_sale_history(
                partner_id, user_id, store_id, product_ids=product_ids,
                biz_dates=same_week_days
            )
        else:
            logger.info("no product_id need forecast")
            return
        forecast_data = {}

        products = SaleForecastTask.list_product_detail(partner_id, user_id, product_ids)
        weight_factor_map = utils.get_weight_factors(partner_id, user_id, store_id, products)
        for product in products:
            p_id = product['product_id']
            sale_type = product['sale_type']
            if p_id in product_history_sale_map:
                history_sale_map = product_history_sale_map[p_id]
                first_week_day_sale_qty = history_sale_map.get(first_week_day, 0)
                last_three_sale_qtys = [
                    quantity
                    for date, quantity in history_sale_map.items()
                    if date in same_week_days[1:]
                ]
                if len(last_three_sale_qtys) == 3:  # 前三周的历史料量数据都有
                    last_three_week_day_average_sale_qty = average(last_three_sale_qtys)
                    forecast = 0.5 * first_week_day_sale_qty + 0.5 * last_three_week_day_average_sale_qty
                else:  # 前三周的历史数据缺失, 就使用最后一周的数据作为预测量
                    forecast = first_week_day_sale_qty

                weight_factor_data = weight_factor_map.get(p_id, {})
                weather_factor = weight_factor_data.get('weather_factor', 1)
                discount_factor = weight_factor_data.get('discount_factor', 1)
                holiday_factor = weight_factor_data.get('discount_factor', 1)
                weight_factor = weather_factor * discount_factor * holiday_factor
                forecast = round(forecast * weight_factor)
                history_sale_data = serialize({
                    date.strftime('%Y-%m-%d %H:%M:%S'): quantity
                    for date, quantity in history_sale_map.items()}
                )
            else:
                forecast = 0
                history_sale_data = None
            forecast_data[p_id] = {
                "amount": forecast,
                "history_sale_data": history_sale_data,
                "store_type": store_type,
                "product_sale_type": sale_type,
                'product_id': p_id,
            }

        forecast_objects = [
            SFDB(
                partner_id=partner_id, store_id=store_id, product_id=p_id,
                product_sale_type=data['product_sale_type'], store_type=data['store_type'],
                forecast_amount=data['amount'], forecast_date=biz_date, status='INIT',
                confirm_amount=data['amount'], history_sale_data=data['history_sale_data'],
            )
            for p_id, data in forecast_data.items()
        ]
        session.bulk_save_objects(forecast_objects)
        logger.info("forecast store(id=%s) success, product_count: %s" % (store_id, len(forecast_objects)))

    @staticmethod
    def clear_store_sale_forecast(partner_id, user_id, store_id, biz_date, status):
        objs = session.query(SFDB).filter_by(
            partner_id=partner_id, store_id=store_id, forecast_date=biz_date,
            status=status
        ).all()
        if not objs:
            return
        ids = [obj.id for obj in objs]
        session.query(SFDB).filter(SFDB.id.in_(ids)).delete(synchronize_session=False)

    @staticmethod
    def list_sale_forecast(partner_id, user_id, store_id, biz_date, status):
        db_objs = session.query(SFDB).filter(
            partner_id=partner_id, user_id=user_id, store_id=store_id, forecast_date=biz_date,
            status=status,
        ).all()
        return [obj._asdict() for obj in db_objs]

    @staticmethod
    def list_need_forecast_products(partner_id, user_id, store_id, biz_date):
        response = metadata_service.get_product_list(
            partner_id=partner_id, user_id=user_id,
            filters={"product_type": "FINISHED", "status": "ENABLED",
                     "sale_type__in": ["BREAD", "TEA"]},
        ) or {}
        products = response.get('rows', [])
        exist_forecast_data, _ = SFDBRepo.list(
            # 已经确认的不能再次重建
            partner_id, store_id, start_date=biz_date, end_date=biz_date,
            status='CONFIRM',
            limit=-1,
        )
        exist_product_ids = set(f['product_id'] for f in exist_forecast_data)
        need_forecast_product_ids = []
        for p in products:
            product_id = int(p['id'])
            if product_id not in exist_product_ids:
                need_forecast_product_ids.append(product_id)
        return need_forecast_product_ids

    @classmethod
    def get_store_type(cls, partner_id, user_id, store_id):
        filters = {
            "open_status__in": ['OPENED'],
            "status__in": ["ENABLED"]
        }
        stores = metadata_service.get_store_list(
            filters=filters, partner_id=partner_id,
            user_id=user_id, ids=[store_id, ]
        ).get('rows', [])
        if len(stores) > 0:
            return stores[0].get('type', '')
        return ''

    @staticmethod
    def get_sale_history(partner_id, user_id, store_id, product_ids=None, biz_dates=None):
        """
        :return: {
            <product_id>: {
                <datetime>: <quantity>,
                ...
            },
            ...
        """
        if not biz_dates:
            raise ValueError('biz_dates can not be empty!')
        timestamps = []
        for biz_date in biz_dates:
            timestamps.append(
                int(date_to_datetime(biz_date).timestamp())
            )
        _ret = report_service.get_store_product_sales(partner_id, user_id, store_id, timestamps, product_ids)
        ret = defaultdict(dict)
        for p_id, data in _ret.items():
            for timestamp, quantity in data.items():
                _date = datetime.datetime.fromtimestamp(timestamp)
                ret[p_id][_date] = quantity
        return dict(ret)

    @classmethod
    def list_product_detail(cls, partner_id, user_id, product_ids):
        if not product_ids:
            return []
        products = metadata_service.get_product_list(
            filters={"product_type": "FINISHED", "status": "ENABLED",
                     "sale_type__in": ["BREAD", "TEA"]},
            return_fields='second_code,sale_type,product_type,bom_type,storage_type,status,category,model_name',
            partner_id=partner_id, user_id=user_id).get('rows', [])

        _products = []
        for p in products:
            p_id = int(p['id'])
            sale_type = p['sale_type']
            product_type = p['product_type']
            bom_type = p['bom_type']
            _products.append({
                'product_id': p_id,
                'sale_type': sale_type,
                'product_type': product_type,
                'bom_type': bom_type,
            })
        return _products


def date_to_datetime(_date):
    return datetime.datetime(
        year=_date.year, month=_date.month,
        day=_date.day, hour=0, minute=0
    )


def average(data):
    if len(data) == 0:
        return 0
    return sum(data) / len(data)


def last_month_same_week_date(biz_date):
    return [
        biz_date - datetime.timedelta(days=7 * i)
        for i in range(1, 5)
    ]


def serialize(data):
    return json.dumps(
        data,
        default=lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if isinstance(x, datetime.datetime) else None
    )


def retry(func, times=3):
    for i in range(times):
        try:
            func()
        except Exception as e:
            print(traceback.format_exc())
            continue
        else:
            return True
    return False


def convert_to_int(value, default=0):
    try:
        return int(value)
    except Exception as e:
        return default


def convert_to_float(value, default=0.0):
    try:
        return float(value)
    except Exception as e:
        return default
