# -*- coding: utf-8 -*-
import json
from datetime import datetime, timedelta
from sqlalchemy import func
import logging
from datetime import datetime
from decimal import Decimal
from hex_exception import RecordAlreadyExist
from google.protobuf.timestamp_pb2 import Timestamp
from ..error.exception import NoResultFoundError, StatusUnavailable, DataValidationException

from ..driver.mysql import db_commit, session
from ..utils.helper import set_model_from_db, convert_to_int, convert_to_datetime, set_db, translate_utc_time, MessageTopic
from ..utils.encode import encodeUTF8
from ..utils.resulting import ErrorCode
from ..utils.snowflake import gen_snowflake_id
from ..utils import pb2dict
# from ..task import public
from ..driver.mq import mq_producer

from ..client.metadata_service import metadata_service 
from ..client.inventory_service import inventory_service
from ..client.cost_engine_service import cost_engine_service
from ..client.report_service import report_service

from ..model.daily_cut import DailyCutStoreModel, DailyCutLogModel, \
    CostTriggerLogModel, CostTriggerPeriodModel



class InventoryCutService():
    """
    库存切片服务
    service: 
        - cut_inventory()：库存切片
        - auto_daily_cut_inventory()：全门店进行库存切片任务调用
        - daily_snapshot_by_store()：部分门店进行库存切片任务调用（门店列表——supply_daily_cut_store)
        - cut_inventory_by_store()：按门店进行库存切片
        - recut_inventory_by_store()：指定门店指定商品切片
        
    """

    # 调用库存引擎切片
    def cut_inventory(self, branch_id, product_ids, end_date, partner_id, user_id, batch_no=None, extra_type=None):
        result = inventory_service.cut_daily_snapshot(branch_id=branch_id, 
                                                        product_ids=product_ids, 
                                                        end_date=end_date, 
                                                        partner_id=partner_id, 
                                                        user_id=user_id, 
                                                        batch_no=batch_no,
                                                        extra_type=extra_type)
        return result


    ## ---- 门店切片 ---- ##
    # 供任务每天自动调用——全门店
    def auto_daily_cut_inventory(self, partner_id, user_id, end_date=None):
        store_list = []
        filters = {
                        "open_status__in": ['OPENED', 'PRE_OPEN'],
                        "status__in": ["ENABLED"]
                    }
        
        store_list = metadata_service.get_store_list(
                filters=filters, partner_id=partner_id, user_id=user_id).get('rows')
        
        # 定时任务隔天运行，库存切片起始时间获取
        if not end_date:
            # e.g. 北京时间03-09 4:00运行自动任务 切片至end_date03-09 4:00 - utc 03-08 20:00
            date = datetime.utcnow()
            end_date = datetime(date.year,date.month,(date.day),20,0,0)

        msg_list = []
        log_list = []
        for store in store_list:
            batch_no = gen_snowflake_id()
            msg_detail = {
                'end_date': end_date,
                'store_id': store['id'],
                'batch_no': batch_no,
                'partner_id': partner_id,
                'user_id': user_id
            }
            msg_list.append(msg_detail)

            log_detail = {
                'id': gen_snowflake_id(),
                'batch_no': batch_no,
                'store_id': store['id'],
                'end_time': end_date,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'retry_nums': 0,
                'status': 'INIT',
                'branch_type': 'store'
            }
            log_list.append(log_detail)

        # 切片请求记录写入
        DailyCutLogModel.create_log_in_all(log_list)

        # 分发切片请求
        for msg in msg_list:
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.INVENTORY_GET_PRODUCTS_TOPIC,
                                    message=msg)
            # public(MessageTopic.INVENTORY_GET_PRODUCTS_TOPIC, msg)


    # 供任务每天调用——按数据库设置的门店
    def daily_snapshot_by_store(self, partner_id, user_id):
        stores = DailyCutStoreModel.get_stores()
        # print(stores)
        for store in stores:
            message = {}
            message['store_id'] = store.store_id
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            # public(MessageTopic.INVENTORY_GET_PRODUCTS_TOPIC, message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.INVENTORY_GET_PRODUCTS_TOPIC,
                                    message=message)


    # 指定单门店切片
    def cut_inventory_by_store(self, store_id, partner_id, user_id, end_date=None, batch_no=None):
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 定时任务隔天运行，库存切片起始时间获取
        if not end_date:
            # e.g. 北京时间03-09 4:00运行自动任务 切片至end_date03-09 4:00 - utc 03-08 20:00
            date = datetime.utcnow()
            end_date = datetime(date.year,date.month,(date.day),20,0,0)

        # 主档属性区域商品
        product_list = metadata_service.get_attribute_products_by_store_id(
                                            store_id=int(store_id), 
                                            return_fields=['id'],
                                            partner_id=partner_id, user_id=user_id).get('rows')

        start_date=end_date-timedelta(days=1)
        timestamp = Timestamp()
        timestamp.FromDatetime(start_date)
        start_date = timestamp

        timestamp = Timestamp()
        timestamp.FromDatetime(end_date)
        end_date_d = timestamp

        batch_no = None
        log_id = None
        daily_cut_log_db = DailyCutLogModel.get_log_by_ags(store_id=store_id, end_time=end_date, partner_id=partner_id)
        # 如果没有该门店该日的切片记录, 创建一条
        if not daily_cut_log_db:
            if not batch_no:
                batch_no = gen_snowflake_id() 
            log_id = gen_snowflake_id()
            log = {
                        'id': log_id,
                        'batch_no': batch_no,
                        'store_id': store_id,
                        'send_product_nums': 0,
                        'end_time': end_date,
                        'partner_id': partner_id,
                        'created_by': user_id,
                        'created_at': datetime.utcnow(),
                        'updated_at': datetime.utcnow(),
                        'retry_nums': 0,
                        'status': 'INIT',
                        'branch_type': 'store'
                    }
            DailyCutLogModel.create_new(**log)
        
        else:
            batch_no = daily_cut_log_db.batch_no
            log_id = daily_cut_log_db.id

        # 实时库存的商品id
        product_inventory_list, total = inventory_service.query_realtime_inventory(
            branch_id=int(store_id),
            limit=-1, 
            partner_id=partner_id,
            user_id=user_id)

        product_ids = []
        log_product_ids = []
        total_product_ids = []

        if product_list:
            for product in product_list:
                product_ids.append(int(product['product_id']))
                total_product_ids.append(int(product['product_id']))
        
        if product_inventory_list:
            for i in product_inventory_list:
                product_ids.append(int(i))
                total_product_ids.append(int(i))
                
        if total_product_ids:
            total_product_ids = list(set(total_product_ids))
            result = self.cut_inventory(batch_no=batch_no, 
                                branch_id=store_id, product_ids=total_product_ids, 
                                end_date=end_date, partner_id=partner_id, user_id=user_id)
            
            if result.get('success'):
                update_log_args = {
                    'id': log_id,
                    # 'send':total_product_ids,
                    'send_product_nums': len(total_product_ids),
                    'status': 'SENT'
                }
                DailyCutLogModel.update_log([update_log_args])
            return True

        else:
            update_log_args = {
                    'id': log_id,
                    'send':'Empty',
                    'send_product_nums': 0,
                    'status': 'FINISH'
                }
            DailyCutLogModel.update_log([update_log_args])
            return True


    # 指定门店/门店+商品切片
    # 补库存切片——“/api/v2/supply/inventory/daily/cut”
    def recut_inventory_by_store(self, store_id, end_date, partner_id, user_id, product_ids=None):

        timestamp = Timestamp()
        timestamp.seconds = end_date.seconds
        end_date = timestamp.ToDatetime()
        # end_date = datetime(end_date.year,end_date.month,end_date.day,20,0,0)

        total_product_ids = []
        if not product_ids:
            # 主档属性区域商品
            product_list = metadata_service.get_attribute_products_by_store_id(
                                                store_id=int(store_id), 
                                                return_fields=['id'],
                                                partner_id=partner_id, user_id=user_id).get('rows')
    
            # 实时库存的商品id
            product_inventory_list, total = inventory_service.query_realtime_inventory(
                branch_id=int(store_id),
                limit=-1, 
                partner_id=partner_id,
                user_id=user_id)

            product_ids = []
            log_product_ids = []
            
            if product_list:
                for product in product_list:
                    product_ids.append(int(product['product_id']))
                    total_product_ids.append(int(product['product_id']))
            
            if product_inventory_list:
                for i in product_inventory_list:
                    product_ids.append(int(i))
                    total_product_ids.append(int(i))
                    
            if total_product_ids:
                total_product_ids = list(set(total_product_ids))
                product_ids = total_product_ids
            else:
                raise DataValidationException('门店无商品！')
        batch_no = gen_snowflake_id()
        result = inventory_service.cut_daily_snapshot(batch_no=str(batch_no), 
                                                    branch_id=store_id, 
                                                    product_ids=product_ids, 
                                                    end_date=end_date, 
                                                    partner_id=partner_id, user_id=user_id)
        if result.get('status'):
            log = {
                    'id': gen_snowflake_id(),
                    'batch_no': batch_no,
                    'inventory_id': result.get('id'),
                    'store_id': store_id,
                    'send_product_nums': len(total_product_ids),
                    'ret': json.dumps(result),
                    'success': result.get('success'),
                    'status': result.get('status'),
                    'end_time': end_date,
                    'partner_id': partner_id,
                    'created_by': user_id,
                    'created_at': datetime.now(),
                    'updated_at': datetime.now(),
                    'created_name': metadata_service.get_username_by_pid_uid(partner_id, user_id)
                }
            log_db = DailyCutLogModel.new(**log)
        result.pop('batch_no')
        # print(result, type(result))
        return result



    ## ---- 配送方切片 ---- ##
    # 供任务每天自动调用——全仓库&加工中心
    def auto_daily_cut_warehouse_inventory(self, partner_id, user_id, end_date=None, include_supply=None):
        # 定时任务隔天运行
        if not end_date:
            date = datetime.utcnow()
            end_date = datetime(date.year,date.month,(date.day),20,0,0)
        # filters = {"status__in": ["ENABLED"]}
        entity_list = []
        if 'warehouse' in include_supply:
            distrcenter_entity_list = metadata_service.list_entity(
                        schema_name='distrcenter', partner_id=partner_id, user_id=user_id).get('rows')
            entity_list = entity_list+distrcenter_entity_list
        if 'machining' in include_supply:
            machining_entity_list = metadata_service.list_entity(
                    schema_name='machining-center', partner_id=partner_id, user_id=user_id).get('rows')
            entity_list = entity_list+machining_entity_list
        msg_list = []
        log_list = []
        for entity in entity_list:
            batch_no = gen_snowflake_id()
            msg_detail = {
                'end_date': end_date,
                'warehouse_id': entity['id'],
                'batch_no': batch_no,
                'partner_id': partner_id,
                'user_id': user_id
            }
            msg_list.append(msg_detail)

            log_detail = {
                'id': gen_snowflake_id(),
                'batch_no': batch_no,
                'store_id': entity['id'],
                'end_time': end_date,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'retry_nums': 0,
                'status': 'INIT',
                'branch_type': 'warehouse'
            }
            log_list.append(log_detail)

        # 切片请求记录写入
        DailyCutLogModel.create_log_in_all(log_list)

        # 分发切片请求
        for msg in msg_list:
            # public(MessageTopic.WAREHOUSE_INVENTORY_GET_PRODUCTS_TOPIC, msg)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.WAREHOUSE_INVENTORY_GET_PRODUCTS_TOPIC,
                                    message=msg)


    # 指定单仓库切片
    def cut_inventory_by_warehouse(self, warehouse_id, partner_id, user_id, end_date=None, batch_no=None):
        # username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 定时任务隔天运行
        if not end_date:
            date = datetime.utcnow()
            end_date = datetime(date.year,date.month,(date.day),20,0,0)

        # 拉取仓库下商品
        product_list = metadata_service.get_product_by_warehouse_id(
            warehouse_id=warehouse_id, partner_id=partner_id, user_id=user_id)
        if product_list:
            product_list = product_list.get('rows')
        else:
            return None

        start_date=end_date-timedelta(days=1)
        timestamp = Timestamp()
        timestamp.FromDatetime(start_date)
        start_date = timestamp

        timestamp = Timestamp()
        timestamp.FromDatetime(end_date)
        end_date_d = timestamp
 
        # 实时库存的商品id
        product_inventory_list, total = inventory_service.query_realtime_inventory(
            branch_id=int(warehouse_id),
            limit=-1, 
            partner_id=partner_id,
            user_id=user_id)

        product_ids = []
        total_product_ids = []

        if product_list:
            for product in product_list:
                product_ids.append(int(product.get('id')))
                total_product_ids.append(int(product.get('id')))
        
        if product_inventory_list:
            for i in product_inventory_list:
                product_ids.append(int(i))
                total_product_ids.append(int(i))

        daily_cut_log_db = DailyCutLogModel.get_log_by_ags(store_id=warehouse_id, end_time=end_date, partner_id=partner_id)
        # 如果没有该门店该日的切片记录, 创建一条
        if not daily_cut_log_db:
            if not batch_no:
                batch_no = gen_snowflake_id() 
            log_id = gen_snowflake_id()
            log = {
                        'id': log_id,
                        'batch_no': batch_no,
                        'store_id': warehouse_id,
                        'end_time': end_date,
                        'partner_id': partner_id,
                        'created_by': user_id,
                        'created_at': datetime.utcnow(),
                        'updated_at': datetime.utcnow(),
                        'retry_nums': 0,
                        'status': 'INIT',
                        'branch_type': 'warehouse'
                    }
            DailyCutLogModel.create_new(**log)
        
        else:
            batch_no = daily_cut_log_db.batch_no
            log_id = daily_cut_log_db.id
                
        if total_product_ids:
            total_product_ids = list(set(total_product_ids))
            result = self.cut_inventory(batch_no=batch_no, 
                                branch_id=warehouse_id, product_ids=total_product_ids, 
                                end_date=end_date, partner_id=partner_id, user_id=user_id)

            if result.get('success'):
                update_log_args = {
                    'id': log_id,
                    # 'send':total_product_ids,
                    'send_product_nums': len(total_product_ids),
                    'status': 'SENT'
                }
                DailyCutLogModel.update_log([update_log_args])
            return True

        else:
            update_log_args = {
                    'id': log_id,
                    'send':'Empty',
                    'send_product_nums': 0,
                    'status': 'FINISH'
                }
            DailyCutLogModel.update_log([update_log_args])
            return True



    ## ---- 成本中心切片 ---- ##
    # 供任务每天自动调用——全成本中心
    def auto_daily_cut_costcenter_inventory(self, partner_id, user_id, end_date=None):
        # filters = {"status__in": ["ENABLED"]}
        entity_list = metadata_service.list_entity(
                    schema_name='COST_CENTER', partner_id=partner_id, user_id=user_id).get('rows')

        msg_list = []
        log_list = []
        for entity in entity_list:
            batch_no = gen_snowflake_id()
            msg_detail = {
                'end_date': end_date,
                'cost_center_id': entity['id'],
                'cost_center_code': entity['code'],
                'batch_no': batch_no,
                'partner_id': partner_id,
                'user_id': user_id
            }
            msg_list.append(msg_detail)

            log_detail = {
                'id': gen_snowflake_id(),
                'batch_no': batch_no,
                'store_id': entity['id'],
                'end_time': end_date,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'retry_nums': 0,
                'status': 'INIT',
                'branch_type': 'costcenter'
            }
            log_list.append(log_detail)

        # 切片请求记录写入
        DailyCutLogModel.create_log_in_all(log_list)

        # 分发切片请求
        for msg in msg_list:
            # public(MessageTopic.COST_CENTER_INVENTORY_GET_PRODUCTS_TOPIC, msg)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.COST_CENTER_INVENTORY_GET_PRODUCTS_TOPIC,
                                    message=msg)


    # 指定单成本中心切片
    def cut_inventory_by_cost_center(self, cost_center_id, cost_center_code, partner_id, user_id, end_date=None, batch_no=None):
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 定时任务隔天运行
        if not end_date:
            date = datetime.utcnow()
            end_date = datetime(date.year,date.month,(date.day),20,0,0)
        
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')

        # 拉取成本中心下商品
        product_list = inventory_service.query_group_product(group_id=int(cost_center_id), partner_id=partner_id, user_id=user_id)
        if product_list:
            product_list = product_list.get('product_info')
        else:
            return None
 
        total_product_ids = []

        if product_list:
            for product in product_list:
                total_product_ids.append(int(product.get('product_id')))

        daily_cut_log_db = DailyCutLogModel.get_log_by_ags(store_id=cost_center_id, end_time=end_date, partner_id=partner_id)
        # 如果没有该门店该日的切片记录, 创建一条
        if not daily_cut_log_db:
            if not batch_no:
                batch_no = gen_snowflake_id() 
            log_id = gen_snowflake_id()
            log = {
                        'id': log_id,
                        'batch_no': batch_no,
                        'store_id': cost_center_id,
                        'end_time': end_date,
                        'partner_id': partner_id,
                        'created_by': user_id,
                        'created_at': datetime.utcnow(),
                        'updated_at': datetime.utcnow(),
                        'retry_nums': 0,
                        'status': 'INIT',
                        'branch_type': 'costcenter'
                    }
            DailyCutLogModel.create_new(**log)
        
        else:
            batch_no = daily_cut_log_db.batch_no
            log_id = daily_cut_log_db.id
                
        if total_product_ids:
            total_product_ids = list(set(total_product_ids))
            result = self.cut_inventory(batch_no=batch_no, 
                                branch_id=cost_center_id, product_ids=total_product_ids, 
                                end_date=end_date, partner_id=partner_id, user_id=user_id)

            if result.get('success'):
                update_log_args = {
                    'id': log_id,
                    # 'send':total_product_ids,
                    'send_product_nums': len(total_product_ids),
                    'status': 'SENT'
                }
                DailyCutLogModel.update_log([update_log_args])
            return True

        else:
            update_log_args = {
                    'id': log_id,
                    'send':'Empty',
                    'send_product_nums': 0,
                    'status': 'FINISH'
                }
            DailyCutLogModel.update_log([update_log_args])
            return True


    ## ---- 切片轮询补偿---- ##
    # 切片轮询补偿
    def recover_inventory_batch(self, minutes, status, max_retry=None, partner_id=None):
        daily_cut_log_dbs = DailyCutLogModel.get_unfinish_daily_snapshot(
            minutes=minutes, status=status, max_retry=max_retry, partner_id=partner_id)
        retry_update_record_list = []
        if daily_cut_log_dbs:
            for daily_cut_log_db in daily_cut_log_dbs:
                message = {}
                message['end_date'] = daily_cut_log_db.end_time
                message['store_id'] = daily_cut_log_db.store_id
                message['partner_id'] = daily_cut_log_db.partner_id
                message['user_id'] = daily_cut_log_db.created_by
                message['batch_no'] = daily_cut_log_db.batch_no
                logging.info('DailyCutRecover {}'.format(message))
                # public(MessageTopic.INVENTORY_GET_PRODUCTS_TOPIC, message)
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.INVENTORY_GET_PRODUCTS_TOPIC,
                                    message=message)

                retry_update_record = {}
                retry_update_record['id'] = daily_cut_log_db.id
                retry_update_record['retry_nums'] = int(daily_cut_log_db.retry_nums)+1
                retry_update_record['updated_at'] = datetime.utcnow()
                retry_update_record_list.append(retry_update_record)
                
            DailyCutLogModel.update_log_in_all(retry_update_record_list)
        else:
            logging.info("NoNeedForRecover")
        return True

    # 更新库存成本中心列表
    def update_cost_center_for_inventory(self, partner_id, user_id):
        store_costcenter_dict = metadata_service.get_branch_cost_center_map(partner_id, user_id)
        inventory_service.deal_with_group(action='ADD', group_dict=store_costcenter_dict, partner_id=partner_id, user_id=user_id)
        return True
    
    ## --- 成本引擎计算 --- ##

    # saas多租户最新成本计算触发任务
    def trigger_cost_task(self, branch_id, branch_type, 
                                        start_date=None, end_date=None, 
                                        hour=None, minute=None,
                                        partner_id=None, user_id=None,
                                        product_ids=None, 
                                        pre_period_id=None, period_id=None, 
                                        task_type=None,
                                        report_type=None,
                                        advance_days=None,
                                        extra_config=None
                                        ):
        
        batch_no = gen_snowflake_id()

        # 定时任务隔天运行
        if not end_date:
            date = datetime.utcnow()
            # hour是utc时区下hour
            if not hour:
                hour = 20
                minute = 0
            end_date = datetime(date.year,date.month,(date.day),hour,minute,0)
            start_date = end_date-timedelta(days=advance_days)

        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
            end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
        if isinstance(start_date, Timestamp):
            start_date = datetime.utcfromtimestamp(start_date.seconds)
            end_date = datetime.utcfromtimestamp(end_date.seconds)
       
        period_list = []
        pre_period_id = None
        if not period_id:
            period_list = metadata_service.list_entity(partner_id=partner_id, user_id=user_id, 
                                schema_name='period',relation='all', sort='start_day', order='asc').get('rows')
            
            if period_list and len(period_list)>0:
                for p in period_list:
                    
                    month_start_date = p.get('fields', {}).get('start_datetime', '1970-01-01T00:00:00Z')
                    month_start_date = datetime.strptime(month_start_date, "%Y-%m-%dT%H:%M:%SZ")

                    month_end_date = p.get('fields', {}).get('end_datetime', '1970-01-01T00:00:00Z')
                    month_end_date = datetime.strptime(month_end_date, "%Y-%m-%dT%H:%M:%SZ")

                    if start_date>=month_start_date and end_date<=month_end_date:
                        period_id = p.get('id')
                        if report_type == 0 and branch_type == 0: # 0 0 类型日期传当天
                            pass
                        else:
                            start_date = month_start_date
                            end_date = month_end_date
                        break
                    # 如果不属于当前帐期，记录当前的帐期id
                    # 可以这样操作的前提是，目前帐期查询是顺序返回的（sort='start_day'）
                    else:
                        pre_period_id = p.get('id')

        if not period_id:        
            logging.info('该成本中心未找到对应账期 {}'.format(branch_id))
            return True
        if not pre_period_id:
            logging.info('该成本中心未找到对应前置账期 {}'.format(branch_id))
        
        # 账期任务，开始时间和结束时间为账期时间
        if extra_config and extra_config.get("trigger_type") == "list":
            ret = cost_engine_service.trigger_task_list(partner_id, user_id, batch_no, period_id, pre_period_id, 
                            branch_id, branch_type, task_type, report_type, start_date, end_date, extra_config)
        else:
            ret = cost_engine_service.trigger_task(partner_id, user_id, batch_no, period_id, pre_period_id, 
                            branch_id, branch_type, task_type, report_type, start_date, end_date)

        # 记录请求
        log_list = []
        log_detail = {
                'id': gen_snowflake_id(),
                'batch_no': batch_no,
                'branch_id': branch_id,
                'branch_type': str(branch_type),
                'req_type': str(task_type)+'/'+str(report_type),
                'start_time': start_date,
                'end_time': end_date,
                'partner_id': partner_id,
                'created_by': user_id,
                'ret':str(ret),
                'status': 'failed' if ret.get('code') else 'success',
                'period_id': period_id
            }
        log_list.append(log_detail)
        CostTriggerLogModel.create_log_in_all(log_list)
        return True



#------- 以下在多租户环境下弃用 ---- #

    # 触发成本引擎执行计算—
    def trigger_cost_count_material(self, branch_id, branch_type, 
                                        start_date=None, end_date=None, 
                                        partner_id=None, user_id=None,
                                        product_ids=None, 
                                        hour=None, minute=None,
                                        period_id=None, 
                                        task_type=None,
                                        report_type=None,
                                        extra_type=None,
                                        category=None
                                        ):
        
        batch_no = gen_snowflake_id()

        # 定时任务隔天运行
        if not end_date:
            date = datetime.utcnow()
            # hour是utc时区下hour
            if not hour:
                hour = 20
                minute = 0
            end_date = datetime(date.year,date.month,(date.day),hour,minute,0)
            start_date = end_date-timedelta(days=1)

        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
            end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
        if isinstance(start_date, Timestamp):
            start_date = datetime.utcfromtimestamp(start_date.seconds)
            end_date = datetime.utcfromtimestamp(end_date.seconds)
        
        if extra_type and extra_type == 'DailyPeriodTestRun':
            date = datetime.utcnow()
            start_date = datetime(date.year,date.month,1,hour,minute,0)-timedelta(days=1) # 月初  
        if extra_type and extra_type == 'UnregularTestRun':  
            end_date = start_date+timedelta(days=1)
            task_type = 1
        
        total_product_ids = []
        if product_ids:
            total_product_ids = product_ids
        ### === 成本新版本不需要传输product_ids， 故注释掉不再捞取商品 == ###
        # 拉去branch下的所有商品
        # else:
        #     # 拉取成本中心下商品
        #     if branch_type == 'COSTCENTER':
        #         product_list = inventory_service.query_group_product(group_id=int(branch_id), partner_id=partner_id, user_id=user_id)
        #         if product_list:
        #             product_list = product_list.get('product_info')
        #         else:
        #             return None
        #         if product_list:
        #             for product in product_list:
        #                 total_product_ids.append(int(product.get('product_id')))
            
        #     # 拉取门店下的所有商品（逻辑同库存切片， 保持两边计算一致性）
        #     elif branch_type == 'STORE':
        #         # 主档属性区域商品
        #         product_list = metadata_service.get_attribute_products_by_store_id(
        #                                             store_id=int(branch_id), 
        #                                             return_fields=['id'],
        #                                             partner_id=partner_id, user_id=user_id).get('rows')

        #         # 有实时库存的商品
        #         product_inventory_list, total = inventory_service.query_realtime_inventory(
        #             branch_id=int(branch_id),
        #             limit=-1, 
        #             partner_id=partner_id,
        #             user_id=user_id)

        #         if product_list:
        #             for product in product_list:
        #                 total_product_ids.append(int(product['product_id']))
                
        #         if product_inventory_list:
        #             for i in product_inventory_list:
        #                 total_product_ids.append(int(i))
                        
        #         if total_product_ids:
        #             total_product_ids = list(set(total_product_ids))

        #     # 拉取仓库下的所有商品（逻辑同库存切片， 保持两边计算一致性）
        #     elif branch_type == 'WAREHOUSE':
        #         product_list = metadata_service.get_product_by_warehouse_id(
        #             warehouse_id=branch_id, partner_id=partner_id, user_id=user_id)
        #         if product_list:
        #             total_product_ids = product_list.get('rows')

        # if not total_product_ids:
        #     logging.info('成本计算请求没有拿到商品，branch_id:{}, branch_type:{}'.format(branch_id, branch_type))
        #     return False

        period_list = []
        if not period_id:
            cost_center_detail = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id, 
                                id=int(branch_id),
                                schema_name='cost-center')
            if cost_center_detail:
                fileds = cost_center_detail.get('fields')
                if fileds:
                    feriodList = fileds.get('feriodList')
                    if feriodList:
                        period_list = feriodList.get('list')
            
            if period_list:
                for p in period_list:
                    month_start_date = p.get('start_date')
                    month_start_date = datetime.strptime(month_start_date, "%Y-%m-%dT%H:%M:%S.%fZ")
                    # print('*****', month_start_date)
                    month_end_date = p.get('end_date')
                    month_end_date = datetime.strptime(month_end_date, "%Y-%m-%dT%H:%M:%S.%fZ")
                    print(start_date, month_start_date, end_date, month_end_date)
                    if start_date>=month_start_date and end_date<=month_end_date:
                        period_id = p.get('id')
                        break

        if not period_id:        
            raise NoResultFoundError('未找到对应的账期')
        
        if task_type and task_type==4:
            # 账期试算任务，开始时间和结束时间为账期时间
            ret = cost_engine_service.trigger_count_material(
                                batch_no=batch_no, branch_id=branch_id, branch_type=branch_type,
                                start_date=month_start_date, end_date=month_end_date, product_ids=total_product_ids, 
                                partner_id=partner_id, user_id=user_id, period_id=period_id, 
                                task_type=task_type, report_type=report_type, category=category)
        else:
            ret = cost_engine_service.trigger_count_material(
                                batch_no=batch_no, branch_id=branch_id, branch_type=branch_type,
                                start_date=start_date, end_date=end_date, product_ids=total_product_ids, 
                                partner_id=partner_id, user_id=user_id, period_id=period_id, 
                                task_type=task_type, report_type=report_type, category=category)
        
        # 如果是非常规试算账期任务，会再触发一个月账期试算任务
        if extra_type and extra_type == 'UnregularTestRun':
            month_batch_no = gen_snowflake_id()
            ret_month = cost_engine_service.trigger_count_material(
                                batch_no=month_batch_no, branch_id=branch_id, branch_type=branch_type,
                                start_date=month_start_date, end_date=month_end_date, 
                                product_ids=total_product_ids, 
                                partner_id=partner_id, user_id=user_id, period_id=period_id, 
                                task_type=4, report_type=1, category=category)
            logging.info('自动触发月试算任务结果： {}'.format(ret_month))
            # 记录请求
            log_list = []
            log_detail = {
                    'id': gen_snowflake_id(),
                    'batch_no': month_batch_no,
                    'branch_id': branch_id,
                    'branch_type': branch_type,
                    'req_type': str(4)+'/'+str(1),
                    'sent': str(category),
                    'start_time': start_date,
                    'end_time': end_date,
                    'partner_id': partner_id,
                    'created_by': user_id,
                    'ret':ret,
                    'status':'SENT',
                    'period_id': period_id
                }
            log_list.append(log_detail)
            CostTriggerLogModel.create_log_in_all(log_list)

             
        # 记录请求
        log_list = []
        log_detail = {
                'id': gen_snowflake_id(),
                'batch_no': batch_no,
                'branch_id': branch_id,
                'branch_type': branch_type,
                'req_type': str(task_type)+'/'+str(report_type),
                'start_time': start_date,
                'end_time': end_date,
                'partner_id': partner_id,
                'created_by': user_id,
                'sent': str(category),
                'ret':str(ret),
                'status':'SENT',
                'period_id': period_id
            }
        log_list.append(log_detail)
        CostTriggerLogModel.create_log_in_all(log_list)

        result = {
            'success': True if ret.get('code') == '0' else False
        }
        return result

    # 触发成本引擎执行计BOM算
    def trigger_cost_count_bom(self, region_type, period_group_by, start_date=None, end_date=None, 
                                    branch_id=None, branch_type=None,
                                    partner_id=None, user_id=None):
        filter_branch_id = branch_id
        batch_no = gen_snowflake_id()
        # 定时任务隔天运行，库存切片起始时间需要扣减一天，并转换成UTC时间
        if not end_date:
            date = datetime.utcnow()
            end_date = datetime(date.year,date.month,(date.day),20,0,0)
            start_date = end_date-timedelta(days=1)
        
        if not isinstance(start_date, datetime):
            start_date = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
        if not isinstance(end_date, datetime):
            end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')

        # 门店为key，costcenter为value的字典
        branch_costcenter_dict = metadata_service.get_store_cost_center_map(partner_id, user_id)

        # 拉去所有门店当天的销售物料分解明细
        ret = report_service.get_sales_explained_report(start_date=start_date, end_date=end_date, 
                                        limit=-1, partner_id=partner_id, user_id=user_id).get('rows')
        if not ret:
            logging.info(' trigger_cost_count_bom get No sales_explained_report')
            return None

        # 数据整合
        sales_to_bom_dict = {}
        for row in ret:
            branch_id = str(row['store_id'])
            # 计算纬度判断
            # 1、计算纬度为成本中心
            if branch_type == 'COSTCENTER':
                # 获取门店对应的branch_id作为新的branch_id
                branch_id = branch_costcenter_dict.get(str(branch_id))
                # 如果没有设置成本中心，跳过
                if not branch_id:
                    continue
                # 如果有指定传输某一成本中心，若成本中心与指定不同，则跳过
                elif filter_branch_id and str(branch_id)!=str(filter_branch_id):
                    continue  
            # 2、其余纬度：门店 / 仓库 （仓库无bom） 
            else:
                # 如果有指定传输某一branch，若与指定不同，则跳过
                if filter_branch_id and str(branch_id)!=str(filter_branch_id):
                    continue  
                
            if not sales_to_bom_dict.get(branch_id):
                sales_to_bom_dict[branch_id] = {
                    row['pos_ticket_number']:{
                        row['product_id']:{
                            row['bom_product_id']:{
                                    'sale_qty': row['product_qty'],
                                    'bom_qty': row['qty']
                                }
                        }
                    }
                }
            else:
                if not sales_to_bom_dict[branch_id].get(row['pos_ticket_number']):
                    sales_to_bom_dict[branch_id][row['pos_ticket_number']] = {
                        row['product_id']:{
                            row['bom_product_id']:{
                                    'sale_qty': row['product_qty'],
                                    'bom_qty': row['qty']
                                }
                        }
                    }
                else:
                    if not sales_to_bom_dict[branch_id][row['pos_ticket_number']].get(row['product_id']):
                        sales_to_bom_dict[branch_id][row['pos_ticket_number']][row['product_id']] = {
                            row['bom_product_id']:{
                                                    'sale_qty': row['product_qty'],
                                                    'bom_qty': row['qty']
                                                }
                            }
                    else:
                        if not sales_to_bom_dict[branch_id][row['pos_ticket_number']][row['product_id']].get(row['bom_product_id']):
                            sales_to_bom_dict[branch_id][row['pos_ticket_number']][row['product_id']][row['bom_product_id']] = {
                            'sale_qty': row['product_qty'],
                            'bom_qty': row['qty']
                        }
                        else:
                            # sales_to_bom_dict[branch_id][row['pos_ticket_number']][row['product_id']][row['bom_product_id']]['sale_qty'] = row['product_qty']
                            sales_to_bom_dict[branch_id][row['pos_ticket_number']][row['product_id']][row['bom_product_id']]['bom_qty'] += row['qty']
        

        # # 拉取所有门店当天的销售物料分解汇总（汇总目前有误，所以不采用汇总数据）
        # ret = report_service.get_sales_explained_summary_report(
        #     start_date=start_date, end_date=end_date, region_type=region_type, period_group_by=period_group_by, 
        #     limit=-1, partner_id=partner_id, user_id=user_id).get('rows')
        # if not ret:
        #     logging.info(' trigger_cost_count_bom get No sales_explained_summary_report')
        #     return None
        
        # # 数据整合
        # sales_to_bom_dict = {}
        # for row in ret:
        #     branch_id = str(row['entity_id'])
        #     # 计算纬度判断
        #     # 1、计算纬度为成本中心
        #     if branch_type == 'COSTCENTER':
        #         # 获取门店对应的branch_id作为新的branch_id
        #         branch_id = branch_costcenter_dict.get(str(branch_id))
        #         # 如果没有设置成本中心，跳过
        #         if not branch_id:
        #             continue
        #         # 如果有指定传输某一成本中心，若成本中心与指定不同，则跳过
        #         elif filter_branch_id and str(branch_id)!=str(filter_branch_id):
        #             continue  
        #     # 2、其余纬度：门店 / 仓库 （仓库无bom） 
        #     else:
        #         # 如果有指定传输某一branch，若与指定不同，则跳过
        #         if filter_branch_id and str(branch_id)!=str(filter_branch_id):
        #             continue  
                
        #     if not sales_to_bom_dict.get(branch_id):
        #         sales_to_bom_dict[branch_id] = {
        #             row['product_id']:{
        #                 row['bom_product_id']:{
        #                         'sale_qty': row['product_qty'],
        #                         'bom_qty': row['qty']
        #                       }
        #             }
        #         }
        #     else:
        #         if not sales_to_bom_dict[branch_id].get(row['product_id']):
        #             sales_to_bom_dict[branch_id][row['product_id']] = {
        #                 row['bom_product_id']:{
        #                                         'sale_qty': row['product_qty'],
        #                                         'bom_qty': row['qty']
        #                                     }
        #                 }
        #         else:
        #             if not sales_to_bom_dict[branch_id][row['product_id']].get(row['bom_product_id']):
        #                 sales_to_bom_dict[branch_id][row['product_id']][row['bom_product_id']] = {
        #                 'sale_qty': row['product_qty'],
        #                 'bom_qty': row['qty']
        #             }
        #             else:
        #                 sales_to_bom_dict[branch_id][row['product_id']][row['bom_product_id']]['sale_qty'] += row['product_qty']
        #                 sales_to_bom_dict[branch_id][row['product_id']][row['bom_product_id']]['bom_qty'] += row['qty']
        
        log_list = [] 
        # 请求触发成本计算bom任务
        for key, value in sales_to_bom_dict.items():
            # key是store_id
            batch_no = gen_snowflake_id()
            branch_id = int(key)
            ret = cost_engine_service.trigger_count_bom(
                                batch_no=batch_no, branch_id=branch_id, 
                                branch_type=branch_type,
                                start_date=start_date, end_date=end_date, 
                                product_bom_dict=value, 
                                partner_id=None, user_id=None)
            # 记录请求
            log_detail = {
                'id': gen_snowflake_id(),
                'batch_no': batch_no,
                'branch_id': branch_id,
                'branch_type': branch_type,
                'req_type': 'count_bom',
                'start_time': start_date,
                'end_time': end_date,
                'partner_id': partner_id,
                'created_by': user_id,
                'ret':ret,
                'status':'SENT',
            }
            log_list.append(log_detail)
        
        # 记录写入数据库
        CostTriggerLogModel.create_log_in_all(log_list)

        return True
   
    # 触发重算成本执行计算
    def trigger_cost_recount_material(self,
                                        branch_id, branch_type, 
                                        start_date, end_date, days,
                                        partner_id=None, user_id=None, product_ids=None):
        batch_no = gen_snowflake_id()
        period_id = gen_snowflake_id()
        
        if not isinstance(start_date, datetime):
            start_date = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
        if not isinstance(end_date, datetime):
            end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')

        total_product_ids = []
        if product_ids:
            total_product_ids = product_ids
        else:
            # 拉取成本中心下商品
            if branch_type == 'COSTCENTER':
                product_list = inventory_service.query_group_product(group_id=int(branch_id), partner_id=partner_id, user_id=user_id)
                if product_list:
                    product_list = product_list.get('product_info')
                else:
                    return None
                if product_list:
                    for product in product_list:
                        total_product_ids.append(int(product.get('product_id')))
            
            # 拉取门店下的所有商品（逻辑同库存切片， 保持两边计算一致性）
            elif branch_type == 'STORE':
                # 主档属性区域商品
                product_list = metadata_service.get_attribute_products_by_store_id(
                                                    store_id=int(branch_id), 
                                                    return_fields=['id'],
                                                    partner_id=partner_id, user_id=user_id).get('rows')

                # 有实时库存的商品
                product_inventory_list, total = inventory_service.query_realtime_inventory(
                    branch_id=int(branch_id),
                    limit=-1, 
                    partner_id=partner_id,
                    user_id=user_id)

                if product_list:
                    for product in product_list:
                        total_product_ids.append(int(product['product_id']))
                
                if product_inventory_list:
                    for i in product_inventory_list:
                        total_product_ids.append(int(i))
                        
                if total_product_ids:
                    total_product_ids = list(set(total_product_ids))

            # 拉取仓库下的所有商品（逻辑同库存切片， 保持两边计算一致性）
            elif branch_type == 'WAREHOUSE':
                product_list = metadata_service.get_product_by_warehouse_id(
                    warehouse_id=branch_id, partner_id=partner_id, user_id=user_id)
                if product_list:
                    total_product_ids = product_list.get('rows')

        if not total_product_ids:
            logging.info('成本计算请求没有拿到商品，branch_id:{}, branch_type:{}'.format(branch_id, branch_type))
            return False
        
        ret = cost_engine_service.trigger_recount_material(
                                batch_no=batch_no, branch_id=branch_id, branch_type=branch_type,
                                start_date=start_date, end_date=end_date, days=days,
                                product_ids=total_product_ids, 
                                partner_id=partner_id, user_id=user_id, period_id=period_id)
        # 记录请求
        log_list = []
        log_detail = {
                'id': gen_snowflake_id(),
                'batch_no': batch_no,
                'branch_id': branch_id,
                'branch_type': branch_type,
                'req_type': 'recount_material',
                'start_time': start_date,
                'end_time': end_date,
                'partner_id': partner_id,
                'created_by': user_id,
                'ret':ret,
                'status':'SENT',
                'period_id':period_id
            }
        log_list.append(log_detail)
        CostTriggerLogModel.create_log_in_all(log_list)
        return True
    
    # 触发重算成本执行bom计算
    def trigger_cost_recount_bom(self, region_type, period_group_by, 
                                    start_date, end_date, days, 
                                    branch_id=None, branch_type=None,
                                    partner_id=None, user_id=None):
        filter_branch_id = branch_id
        batch_no = gen_snowflake_id()

        if not isinstance(start_date, datetime):
            start_date = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
        if not isinstance(end_date, datetime):
            end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')

        # 门店为key，costcenter为value的字典
        branch_costcenter_dict = metadata_service.get_store_cost_center_map(partner_id, user_id)

        # 拉取所有门店当天的销售物料分解汇总
        ret = report_service.get_sales_explained_summary_report(
            start_date=start_date, end_date=end_date, region_type=region_type, period_group_by=period_group_by, 
            limit=-1, partner_id=partner_id, user_id=user_id).get('rows')
        if not ret:
            logging.info(' trigger_cost_count_bom get No sales_explained_summary_report')
            return None
        
        # 数据整合
        sales_to_bom_dict = {}
        for row in ret:
            branch_id = str(row['entity_id'])
            # 计算纬度判断
            # 1、计算纬度为成本中心
            if branch_type == 'COSTCENTER':
                # 获取门店对应的branch_id作为新的branch_id
                branch_id = branch_costcenter_dict.get(str(branch_id))
                # 如果没有设置成本中心，跳过
                if not branch_id:
                    continue
                # 如果有指定传输某一成本中心，若成本中心与指定不同，则跳过
                elif filter_branch_id and str(branch_id)!=str(filter_branch_id):
                    continue  
            # 2、其余纬度：门店 / 仓库 （仓库无bom） 
            else:
                # 如果有指定传输某一branch，若与指定不同，则跳过
                if filter_branch_id and str(branch_id)!=str(filter_branch_id):
                    continue  
                
            if not sales_to_bom_dict.get(branch_id):
                sales_to_bom_dict[branch_id] = {
                    row['product_id']:{
                        row['bom_product_id']:{
                                'sale_qty': row['product_qty'],
                                'bom_qty': row['qty']
                              }
                    }
                }
            else:
                if not sales_to_bom_dict[branch_id].get(row['product_id']):
                    sales_to_bom_dict[branch_id][row['product_id']] = {
                        row['bom_product_id']:{
                                                'sale_qty': row['product_qty'],
                                                'bom_qty': row['qty']
                                            }
                        }
                else:
                    if not sales_to_bom_dict[branch_id][row['product_id']].get(row['bom_product_id']):
                        sales_to_bom_dict[branch_id][row['product_id']][row['bom_product_id']] = {
                        'sale_qty': row['product_qty'],
                        'bom_qty': row['qty']
                    }
                    else:
                        sales_to_bom_dict[branch_id][row['product_id']][row['bom_product_id']]['sale_qty'] += row['product_qty']
                        sales_to_bom_dict[branch_id][row['product_id']][row['bom_product_id']]['bom_qty'] += row['qty']
        
        log_list = []
        # 请求触发成本计算bom任务
        for key, value in sales_to_bom_dict.items():
            # key是store_id
            batch_no = gen_snowflake_id()
            branch_id = int(key)
            ret = cost_engine_service.trigger_recount_bom(
                                batch_no=batch_no, branch_id=branch_id, 
                                branch_type=branch_type,
                                start_date=start_date, end_date=end_date, days=days,
                                product_bom_dict=value, 
                                partner_id=None, user_id=None)
            # 记录请求
            log_detail = {
                'id': gen_snowflake_id(),
                'batch_no': batch_no,
                'branch_id': branch_id,
                'branch_type': branch_type,
                'req_type': 'recount_bom',
                'start_time': start_date,
                'end_time': end_date,
                'partner_id': partner_id,
                'created_by': user_id,
                'ret':ret,
                'status':'SENT',
            }
            log_list.append(log_detail)
        
        # 记录写入数据库
        CostTriggerLogModel.create_log_in_all(log_list)
        return True
    
    # 触发成本引擎关闭账期任务
    def trigger_cost_close_count_task(self, batch_no, partner_id, user_id):
    
        ret = cost_engine_service.close_count_task(
                                batch_no=batch_no, partner_id=partner_id, user_id=user_id)
        
        return True


    ## ---- 通用方法 ---- ##
    # 更新切片log
    def update_inventory_batch_process_result(self, partner_id, batch_no, inventory_id, status, total, success, fail):
        
        daily_cut_log_db = DailyCutLogModel.get_log_by_batchNo(batch_no=batch_no, partner_id=partner_id)
        args = {
            'id': daily_cut_log_db.id,
            'status': status,
            'receive_nums': total,
            'success_nums': success,
            'fail_nums': fail,
            'updated_at': datetime.utcnow(),
            'inventory_id': inventory_id,
            'success': 1
        }
        daily_cut_log_db.update_log([args])

        return True
    

    
inventory_cut_service = InventoryCutService()