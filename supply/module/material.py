# -*- coding: utf-8 -*-
from supply.client.metadata_service import metadata_service
from supply.client.inventory_service import inventory_service
from supply.client.cost_service import cost_service
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp
import calendar
import logging


class MaterialService():

    def get_timestamp(self, value):
        value = value
        timestamp = Timestamp()
        timestamp.FromDatetime(value)
        return timestamp

    def get_datetime(self, value):
        timestamp = Timestamp()
        timestamp.seconds = value.seconds
        date = timestamp.ToDatetime()
        if date == datetime(1970, 1, 1):
            return None
        return date

    def get_material_difference(self, request, partner_id, user_id):
        ret = {}
        sales_date = request.sales_date
        close_store = request.close_store
        close_product = request.close_product
        close_start = request.close_start
        close_end = request.close_end
        offset = request.offset
        limit = request.limit
        store_id = request.store_id
        product_ids = list(request.product_ids)
        if sales_date:
            sales_date = self.get_datetime(sales_date)
        year = sales_date.year
        month = sales_date.month
        end = calendar.monthrange(int(year), int(month))[1]
        now_month = int(datetime.now().month)
        now_day = int(datetime.now().day)
        # 当月去昨天
        if now_month == month and now_day != end:
            cost_end = str(now_day - 1).rjust(2, '0')
        else:
            cost_end = end
        # 获取开始，结束日期
        month = str(month).rjust(2, '0')
        start_date_str = '%s-%s-01' % (year, month)
        end_date_str = '%s-%s-%s' % (year, month, end)
        cost_end_date_str = '%s-%s-%s' % (year, month, cost_end)
        cost_date = cost_end_date_str
        start_date = self.get_timestamp(datetime.strptime(start_date_str, '%Y-%m-%d'))
        end_date = self.get_timestamp(datetime.strptime(end_date_str, '%Y-%m-%d'))
        print('start_date', start_date, end_date)
        b_start_date_str = '%s-%s-01 20:00:00' % (year, month)
        b_end_date_str = '%s-%s-%s 20:00:00' % (year, month, end)

        b_start_date = self.get_timestamp(datetime.strptime(b_start_date_str, '%Y-%m-%d %H:%M:%S'))
        b_end_date = self.get_timestamp(datetime.strptime(b_end_date_str, '%Y-%m-%d %H:%M:%S'))

        bb_start_date = self.get_timestamp(datetime.strptime(b_start_date_str, '%Y-%m-%d %H:%M:%S') - timedelta(days=1))
        bb_end_date = self.get_timestamp(datetime.strptime(b_end_date_str, '%Y-%m-%d %H:%M:%S') - timedelta(days=1))
        print(b_start_date, b_end_date, bb_start_date, bb_end_date)
        ret['rows'], ret['total'] = self.get_start_date_inventory_snap_list(store_id, product_ids, start_date, end_date,
                                                                            cost_date, b_start_date, b_end_date,
                                                                            bb_start_date, bb_end_date, partner_id,
                                                                            user_id, limit, offset, close_product,
                                                                            close_start, close_end)
        return ret

    def get_start_date_inventory_snap_list(self, store_id, product_ids, start_date, end_date, cost_date,
                                           b_start_date, b_end_date, bb_start_date, bb_end_date,
                                           partner_id, user_id, limit,
                                           offset, close_product, close_start, close_end):
        limit = limit
        offset = offset
        code = 'DAILY_SNAPSHOT'
        # 获取门店详情
        store_info = metadata_service.get_store(store_id=store_id,
                                                return_fields='id,code,status,name,branch_region,geo_region',
                                                partner_id=partner_id, user_id=user_id)

        branch_region_info = {}
        # 获取管理区域详情
        if store_info.get('branch_region'):
            for branch_region in store_info['branch_region']:
                branch_region_ret = metadata_service.get_region_by_id(region_id=int(branch_region),
                                                                      region_type='branch',
                                                                      return_fields='name', partner_id=partner_id,
                                                                      user_id=user_id)
                branch_region_info['name'] = branch_region_info.get('name', '') + '>' + branch_region_ret.get(
                    'name') if branch_region_info.get('name') else branch_region_ret.get('name')
        # 获取地理区域详情
        geo_region_info = {}
        if store_info.get('geo_region'):
            geo_region_info = metadata_service.get_region_by_id(region_id=int(store_info['geo_region'][0]),
                                                                region_type='geo',
                                                                return_fields='name', partner_id=partner_id,
                                                                user_id=user_id)
        # 获取商品一个月切片
        if product_ids:
            month_snap_product_ids = product_ids
        else:
            month_snap_product_ids = None
        inventory_snapshot_list = inventory_service.query_month_snap_by_store_id(branch_id=store_id,
                                                                                 product_ids=month_snap_product_ids,
                                                                                 start_time=start_date,
                                                                                 end_time=end_date,
                                                                                 code=code,
                                                                                 partner_id=partner_id, user_id=user_id,
                                                                                 ).get('group_snapshots', [])
        product_ids = [int(d.get('account', {}).get('product_id', 0)) for d in inventory_snapshot_list]
        # 获取所有商品期初库存
        previous_info_ret = inventory_service.query_snap_list(branch_id=store_id, start_date=bb_start_date,
                                                              end_date=b_start_date,
                                                              product_ids=product_ids, limit=-1, offset=0,
                                                              partner_id=partner_id, user_id=user_id,
                                                              code=None, action=None, extra=None).get('rows', [])
        start_info = {}
        if len(previous_info_ret) > 0:
            for pi in previous_info_ret:
                product_id = int(pi.get('account').get('product_id'))
                start_info[product_id] = pi.get('previous', {}).get('amount', {}).get('qty', 0)
        # 获取所有商品期末库存
        print('bb_end_date', bb_end_date, b_end_date)
        end_info_ret = inventory_service.query_snap_list(branch_id=store_id, start_date=bb_end_date,
                                                         end_date=b_end_date,
                                                         product_ids=product_ids, limit=-1, offset=0,
                                                         partner_id=partner_id, user_id=user_id,
                                                         code=None, action=None, extra=None).get('rows', [])
        end_info = {}
        if len(end_info_ret) > 0:
            for ei in end_info_ret:
                product_id = int(ei.get('account').get('product_id'))
                end_info[product_id] = ei.get('amount', {}).get('qty')
                # 获取商品单位详情
        unit_info_ret = metadata_service.get_unit_list(return_fields='id,name', partner_id=partner_id,
                                                       user_id=user_id).get('rows', [])
        unit_info = {}
        if len(unit_info_ret) > 0:
            for u in unit_info_ret:
                unit_info[u['id']] = u['name']
        # 获取商品详情
        product_info_ret = metadata_service.get_product_list(ids=product_ids,
                                                             return_fields='id,code,status,name,category,'
                                                                           'product_type,bom_type,sale_type',
                                                             partner_id=partner_id,
                                                             include_units=True,
                                                             user_id=user_id).get('rows', [])
        product_info = {}
        category_ids = []
        if len(product_info_ret) > 0:
            for pr in product_info_ret:
                product_info[int(pr['id'])] = pr
                if pr.get('category'):
                    if int(pr.get('category')) not in category_ids:
                        category_ids.append(int(pr.get('category')))
                uints = pr.get('units', [])
                for u in uints:
                    if u.get('default') == True:
                        pr['accounting_unit_id'] = unit_info.get(u['id'])
        # 获取商品类别
        category_info_ret = metadata_service.get_product_category_list(ids=category_ids, return_fields='id,name',
                                                                       partner_id=partner_id, user_id=user_id).get(
            'rows', [])
        category_info = {}
        if len(category_info_ret) > 0:
            for c in category_info_ret:
                category_info[c['id']] = c.get('name', '')
        # 获取成本
        product_cost_ret = cost_service.get_product_cost_by_store_code(store_code=store_info['code'], date=cost_date,
                                                                       partner_id=partner_id, user_id=user_id).get(
            'rows', [])
        logging.info('分差拉取每日成本,条件{}/{}。'.format(store_info['code'], cost_date))
        product_cost_info = {}
        if len(product_cost_ret) > 0:
            for pc in product_cost_ret:
                product_cost_info[pc['product_id']] = pc.get('uncs', 0)
        daily_inventory_list = []
        for detail in inventory_snapshot_list:
            # {'account': {'branch_id': '4217527472486350849', 'product_id': '4217605316545609729'},
            # 'items': [{'code': 'SALES', 'amount': {'qty': -71.0}}]}
            product_id = detail.get('account').get('product_id')
            # 商品明细
            product_ = product_info.get(int(product_id), {})
            # 过滤添加商品
            start_quantity = start_info.get(int(product_id)) if start_info.get(int(product_id)) else 0
            end_quantity = end_info.get(int(product_id)) if end_info.get(int(product_id)) else 0
            if close_product and product_.get('status') == 'DISABLED':
                continue
            if close_start and start_quantity == 0:
                continue
            if close_end and end_quantity == 0:
                continue
            # 排除成品，半成品展示
            if product_.get('product_type') == "SEMI-FINISHED" and product_.get('product_type') == "FINISHED":
                continue
            # 迭代
            daily_inventory = {}
            # 门店明细
            daily_inventory['store_code'] = store_info['code']
            daily_inventory['store_name'] = store_info['name']
            daily_inventory['branch_region'] = branch_region_info.get('name', '')
            daily_inventory['geo_region'] = geo_region_info.get('name', '')
            daily_inventory['close_store'] = True if store_info.get('status') == 'DISABLED' else False
            daily_inventory['product_code'] = product_['code']
            daily_inventory['product_name'] = product_['name']
            daily_inventory['accounting_unit_name'] = product_.get('accounting_unit_id')
            daily_inventory['bom_type'] = product_.get('bom_type')
            daily_inventory['product_type'] = product_.get('product_type')
            daily_inventory['close_product'] = True if product_.get('status') == 'DISABLED' else False
            daily_inventory['sale_type'] = product_.get('sale_type')
            if product_.get('category'):
                daily_inventory['product_category'] = category_info.get(product_.get('category'), '')
            daily_inventory['product_price'] = product_cost_info.get(product_id, 0)
            # 期初库存
            daily_inventory['start_quantity'] = start_quantity
            daily_inventory['start_price'] = daily_inventory['start_quantity'] * daily_inventory['product_price']
            # 期末库存
            daily_inventory['end_quantity'] = end_quantity
            daily_inventory['end_price'] = daily_inventory['end_quantity'] * daily_inventory['product_price']
            daily_inventory['receiving_quantity'] = 0
            daily_inventory['transfer_quantity'] = 0
            daily_inventory['sales_quantity'] = 0
            daily_inventory['diff_quantity'] = daily_inventory['start_quantity'] - daily_inventory['end_quantity']
            daily_inventory['diff_percentage'] = 0
            if detail.get('items'):
                for stats_detail in detail['items']:
                    if product_['code'] == '10100032':
                        print('s', stats_detail)
                    # 配送收货入库
                    if (stats_detail['code'] == 'ORDER_RECEIVING' or stats_detail['code'] == 'ADJUST_ORDER_RECEIVING'):
                        daily_inventory['receiving_quantity'] += stats_detail.get('amount').get('qty', 0)
                        daily_inventory['diff_quantity'] += stats_detail.get('amount').get('qty', 0)
                    # 直送收货入库
                    elif stats_detail['code'] == 'DIR_RECEIVING':
                        daily_inventory['receiving_quantity'] += stats_detail.get('amount').get('qty', 0)
                        daily_inventory['diff_quantity'] += stats_detail.get('amount').get('qty', 0)
                    # # 收货差异出库
                    # elif stats_detail['code'] == 'PURCHSE_DIFF_RETURN':
                    #     daily_inventory['receiving_quantity'] += stats_detail.get('amount').get('qty', 0)
                    #     daily_inventory['diff_quantity'] += stats_detail.get('amount').get('qty', 0)
                    # # 收货差异入库
                    # elif stats_detail['code'] == 'RECEIVING_DIFF':
                    #     daily_inventory['receiving_quantity'] += stats_detail.get('amount').get('qty', 0)
                    #     daily_inventory['diff_quantity'] += stats_detail.get('amount').get('qty', 0)
                    # 退货出库
                    elif (stats_detail['code'] == 'ORDER_RETURN' or stats_detail['code'] == 'ADJUST_ORDER_RETURN'):
                        daily_inventory['receiving_quantity'] += stats_detail.get('amount').get('qty', 0)
                        daily_inventory['diff_quantity'] += stats_detail.get('amount').get('qty', 0)
                    # 调拨入库+调拨出库
                    elif stats_detail['code'] == 'TRANSFER':
                        daily_inventory['transfer_quantity'] = stats_detail.get('amount').get('qty', 0)
                        daily_inventory['diff_quantity'] += stats_detail.get('amount').get('qty', 0)
                    # 调拨出库
                    # 销售出库
                    elif stats_detail['code'] == 'SALES':
                        daily_inventory['sales_quantity'] = stats_detail.get('amount').get('qty', 0)
                        daily_inventory['diff_quantity'] += stats_detail.get('amount').get('qty', 0)
                # 每次迭代完成重新计算成本
                daily_inventory['diff_percentage'] = 100 * (daily_inventory['diff_quantity'] / daily_inventory[
                    'sales_quantity']) if daily_inventory['sales_quantity'] else 0
                daily_inventory['receiving_price'] = daily_inventory['receiving_quantity'] * daily_inventory[
                    'product_price']
                daily_inventory['transfer_price'] = daily_inventory['transfer_quantity'] * daily_inventory[
                    'product_price']
                daily_inventory['sales_price'] = daily_inventory['sales_quantity'] * daily_inventory[
                    'product_price']
                daily_inventory['diff_price'] = daily_inventory['diff_quantity'] * daily_inventory[
                    'product_price']
            daily_inventory_list.append(daily_inventory)
        total = len(daily_inventory_list)
        return daily_inventory_list, total


material_service = MaterialService()
