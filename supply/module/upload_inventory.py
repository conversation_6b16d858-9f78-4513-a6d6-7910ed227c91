# -*- coding: utf-8 -*-
import json
import logging
import base64
import tempfile
import pyexcel
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp

from supply.error.exception import NoResultFoundError, DataValidationException
from supply.utils.snowflake import gen_snowflake_id

from supply.client.metadata_service import metadata_service
from supply.client.inventory_service import inventory_service

from supply.model.upload_inventory import InventoryUploadDB, InventoryUploadBatchDB


class UploadInventoryService():
    """
    库存期初导入服务
    """
    
    # 请求库存
    def prepare_for_inventory(self, batch_id, details, description, code, action, partner_id, user_id, trace_id=None):
        if action == "DEPOSIT":
            detail_list = []
            sequence = 0
            for detail in details:
                amount = detail.quantity
                account = {
                    "branch_id": detail.branch_id, 
                    "product_id": detail.product_id
                }
                detail = {
                    "sequence_id": sequence,
                    "accounting": 
                        {
                            "account":account,
                            "amount": amount
                        }
                    }
                sequence += 1
                detail_list.append(detail)

            result = inventory_service.deal_with_inventory(
                        batch_no=str(batch_id), code=code, action=action,
                        detail=detail_list, 
                        description=description, trace_id=trace_id,
                        partner_id=partner_id, user_id=user_id)
        
        return result

    # 读取导入文件
    def read_file(self, file_obj, filename):
        extension = filename.split(".")[-1]
        content = file_obj
        if extension.upper() == "XLS":
            sheet = pyexcel.get_sheet(file_type="xls", file_content=content)
        elif extension.upper() == "XLSX":
            sheet = pyexcel.get_sheet(file_type="xlsx", file_content=content)
        else:
            return "文件格式错误，只支持xls或xlsx"
        sheet.name_columns_by_row(0)
        headers = list(sheet.colnames)
        expect_headers = [
                              u"组织编号",
                              u"组织名称",
                              u"商品编号",
                              u"商品名称",
                              u"库存数量"
                              ]
        
        if set(headers) != set(expect_headers):
            return "表格表头错误"
        rows = sheet.to_records()
        if sheet.number_of_rows() > 5000:
            return "文件过大，请导入5000行以内的数据"
        
        header_map = {
            u"组织编号": "branch_code",
            u"组织名称": "branch_name",
            u"商品编号": "product_code",
            u"商品名称": "product_name",
            u"库存数量": "quantity"
        }
        data_list = []
        for row in rows:
            data = {}
            for header, key in header_map.items():
                data[key] = row.get(header)
            if data["branch_code"] == "" and data["product_code"] == "":
                pass
            else:
                data_list.append(data)
        if len(data_list) == 0:
            return "文件为空"
        return data_list

    # 获取主档id的映射
    def get_valid_branch_map(self, data_list, partner_id, user_id, branch_type):
        code_list = list(set([str(d["branch_code"]).strip() for d in data_list]))
        branch_info_list = metadata_service.list_entity(
                                            schema_name=branch_type, 
                                            filters={
                                                "status__eq": "ENABLED",
                                                "code__in": code_list
                                            },
                                            return_fields="id,code,name",
                                            partner_id=partner_id, user_id=user_id).get("rows", [])

        branch_id_map = {}
        if len(branch_info_list) > 0:
            for row in branch_info_list:
                branch_id_map[row.get("fields", {}).get("code", "0")] = [int(row.get("id", 0)), row.get("fields", {}).get("name", "")]
        return branch_id_map

    # 获取主档商品id的映射
    def get_valid_product_map(self, data_list, partner_id, user_id):
        code_list = list(set([str(d["product_code"]).strip() for d in data_list]))
        product_info_list = metadata_service.get_product_list(filters={"status__eq": "ENABLED",
                                                                       "code__in": code_list},
                                                              return_fields="id,code,name",
                                                              partner_id=partner_id,
                                                              user_id=user_id).get("rows", [])
        product_id_map = {}
        if len(product_info_list) > 0:
            for row in product_info_list:
                product_id_map[row["code"]] = [int(row["id"]), row.get("name")]
        return product_id_map

    # 用合法数据重构data_list
    def rebuild_data_list(self, data_list, branch_id_map, product_id_map):
        has_invalid_data = False
        duplicate_dict = {}
        new_data_list = []
        for index, data in enumerate(data_list):
            new_data = {}
            row_num = index + 1
            new_data["row_num"] = row_num
            branch_code = str(data.get("branch_code")).strip()
            product_code = str(data.get("product_code")).strip()

            # 重复数据校验
            duplicate_key = str(branch_code)+str(product_code)
            if duplicate_dict.get(duplicate_key):
                new_data["error"] = u"导入第{}行与第{}行重复，请检查".format(row_num+1, duplicate_dict[duplicate_key])
            else:
                duplicate_dict[duplicate_key] = row_num

            new_data["branch_code"] = branch_code
            new_data["product_code"] = product_code
            new_data["quantity"] = float(data.get("quantity")) if data.get("quantity") else 0
            if branch_code not in branch_id_map:
                new_data["error"] = u"{}门店编号有误".format(branch_code)
                has_invalid_data = True
            elif product_code not in product_id_map:
                new_data["error"] = u"{}商品编号有误".format(product_code)
                has_invalid_data = True
            elif data["quantity"] < 0:
                new_data["error"] = u"{}期初库存数量不能小于0".format(product_code)
                has_invalid_data = True
            else:
                branch_id = branch_id_map.get(branch_code)[0]
                branch_name = branch_id_map.get(branch_code)[1]
                product_id = product_id_map.get(product_code)[0]
                product_name = product_id_map.get(product_code)[1]

                new_data["branch_id"] = branch_id
                new_data["product_id"] = product_id
                new_data["branch_name"] = branch_name
                new_data["product_name"] = product_name
            new_data_list.append(new_data)
        return new_data_list, has_invalid_data

    
    # 导入期初库存
    def upload_inventory(self, request, partner_id, user_id):
        file = base64.b64decode(request.file)
        filename = request.filename
        branch_type = request.branch_type

        temp = tempfile.TemporaryFile()
        temp.write(file)
        temp.seek(0)
        s1 = temp.read()
        temp.close()
        
        ret = {
            "filename": filename,
            "result": True
        }
        data_list = self.read_file(s1, filename)
        if not isinstance(data_list, list):
            ret["result"] = False
            ret["msg"] = data_list
            return ret

        product_id_map = self.get_valid_product_map(data_list, partner_id, user_id)
        branch_id_map = self.get_valid_branch_map(data_list, partner_id, user_id, branch_type)

        check_data_list, has_invalid_data = self.rebuild_data_list(data_list, branch_id_map, product_id_map)
        
        ret["result"] = has_invalid_data
        ret["rows"] = check_data_list

        # 无错误数据，则导入
        if not has_invalid_data:
            batch = {
                "filename": str(filename),
                "status": "INIT",
                "branch_type": branch_type,
                "id": gen_snowflake_id(),
                "created_by": user_id,
                "partner_id": partner_id
            }
            detail_list = []
            for c in check_data_list:
                c["id"] = gen_snowflake_id()
                c["batch_id"] = batch["id"]
                c["partner_id"] = partner_id
                detail_list.append(c)

            InventoryUploadDB.create_upload_details(batch=[batch], detail_list=detail_list,
                                                                       partner_id=partner_id, user_id=user_id)
            ret["msg"] = u"导入成功"
            ret["batch_id"] = batch["id"]
        return ret


    # 审核期初库存
    def approve_inventory_upload(self, id, partner_id, user_id):
        ret = {
            "result": True
        }

        batch_obj = InventoryUploadBatchDB.get_inventory_upload_batch(id, partner_id)

        if batch_obj.status in ("INIT", "FAIL"):
            InventoryUploadBatchDB.update_upload_batch_status(id=id, status="PROCESSING", \
                                                                partner_id=partner_id, user_id=user_id)
            try:

                inventory_upload_list = InventoryUploadDB.get_inventory_upload_batch_details(
                    batch_id=id, partner_id=partner_id)
                inv_ret = self.prepare_for_inventory(batch_id=id, details=inventory_upload_list, \
                        description="INIT_INVENTORY", code="INIT_INVENTORY", \
                        action="DEPOSIT", partner_id=partner_id, user_id=user_id, trace_id=batch_obj.filename)

                ret["msg"] = "{}期初库存导入结束:{}".format(id, inv_ret)
                InventoryUploadBatchDB.update_upload_batch_status(id=id, status="SUCCESS", \
                                                                partner_id=partner_id, user_id=user_id)
            except Exception as e:
                InventoryUploadBatchDB.update_upload_batch_status(id=id, status="FAIL", \
                                                                partner_id=partner_id, user_id=user_id)
                ret["msg"] = "{}期初库存导入失败:{}".format(id, str(e))
                return ret
        else:
            ret["msg"] = "执行batch状态有误"
        return ret


    # 取消期初库存
    def cancel_inventory_upload(self, id, partner_id, user_id):
        ret = {
            "result": True
        }

        batch_obj = InventoryUploadBatchDB.get_inventory_upload_batch(id, partner_id)

        if batch_obj.status in ("INIT"):
            InventoryUploadBatchDB.update_upload_batch_status(id=id, status="CANCELLED", \
                                                                partner_id=partner_id, user_id=user_id)
        else:
            ret["msg"] = "只有新建的单子可以被作废"
        return ret
 

    # 获取导入列表
    def get_inventory_upload_list(self, request, partner_id, user_id):
        offset = request.offset
        limit = request.limit
        
        count, upload_details= InventoryUploadBatchDB.list_inventory_upload_batch(
                                                                    offset=offset,
                                                                    limit=limit,
                                                                    partner_id=partner_id
                                                                    )
        ret = {
            "total": count,
            "rows": []
        }
        if count:
            for upload_detail in upload_details:
                upload_obj = upload_detail.serialize(conv=True)
                ret["rows"].append(upload_obj)
        return ret
    

    # 获取导入详情
    def get_inventory_upload_details(self, request, partner_id, user_id):
        batch_id = request.id
        inventory_upload_detail_db = InventoryUploadDB.get_inventory_upload_batch_details(
                                                                    batch_id=batch_id, 
                                                                    partner_id=partner_id
                                                                    )
        inventory_upload_details = []
        for upload_detail in inventory_upload_detail_db:
            inventory_upload_detail = {
                "branch_code": upload_detail.branch_code,
                "branch_name": upload_detail.branch_name,
                "product_code": upload_detail.product_code,
                "product_name": upload_detail.product_name,
                "quantity": upload_detail.quantity,
                "branch_id": upload_detail.branch_id,
                "product_id": upload_detail.product_id
            }
            inventory_upload_details.append(inventory_upload_detail)
        return inventory_upload_details

upload_inventory_service = UploadInventoryService()