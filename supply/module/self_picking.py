# -*- coding: utf8 -*-
import logging
from datetime import datetime
from decimal import Decimal

from supply.driver.mq import mq_producer
from supply.utils.inventory_enum import ACTION
from supply.utils.enums import SELF_PICKING_STATUS
from supply.model.inventory import inventory_repository
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.helper import convert_to_int, MessageTopic, get_product_unit_rate_map, convert_to_decimal, \
    get_product_map, get_branch_map, get_uuids, get_supply_reason_map, get_username_map
from supply.model.supply_doc_code import Supply_doc_code
from google.protobuf.timestamp_pb2 import Timestamp
from supply.error.exception import DataValidationException, DataDuplicationException, OrderNotExistException
from supply.client.metadata_service import metadata_service
from supply.model.self_picking.self_picking import self_picking_db, StoreSelfPickingProduct
from supply.model.operation_log import TpTransLogModel


class StoreSelfPickingService(object):
    """门店自采单业务处理"""

    def __init__(self):
        pass

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    # noinspection PyMethodMayBeStatic
    def utctimestamp2datetime(self, timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.utcfromtimestamp(timestamp.seconds)
        return date

    def create_self_picking(self, request, user_id=None, partner_id=None):
        """创建门店自采单"""
        response = {}
        request_id = convert_to_int(request.request_id)
        order_date = self.utctimestamp2datetime(request.order_date)
        branch_id = convert_to_int(request.branch_id)
        if not request_id:
            raise DataValidationException("没有请求id")
        # 根据请求id校验该门店自采单是否已经创建
        record = self_picking_db.get_self_picking_by_id(request_id=request_id, partner_id=partner_id)
        if record:
            raise DataDuplicationException("请勿重复创建-{}".format(record.id))
        if not order_date:
            raise DataValidationException("请传入自采日期")
        code = Supply_doc_code.get_code_by_type(code_type='SELF_PICK', partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        main_id = gen_snowflake_id()
        branch_map = get_branch_map(branch_ids=[branch_id], branch_type="STORE", return_fields="id,code,name",
                                    partner_id=partner_id, user_id=user_id)
        branch = branch_map.get(branch_id) if branch_map.get(branch_id) else {}
        receipt = dict(
            id=main_id,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            status='INITED',
            process_status='INITED',
            code=code,
            order_date=order_date,
            branch_id=branch_id,
            branch_name=branch.get("name"),
            branch_code=branch.get("code"),
            branch_type=request.branch_type,
            reason=request.reason,
            remark=request.remark,
            request_id=request_id
        )
        if request.attachments:
            receipt.update(dict(attachments=request.attachments))
        total_amount = Decimal(0)
        items = request.items
        if not items:
            raise DataValidationException("门店自采单必须包含商品")
        product_ids = [convert_to_int(m.product_id) for m in items]
        product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name,model_name",
                                      partner_id=partner_id,
                                      user_id=user_id)
        tax_rate_map = metadata_service.get_tax_rate_map(product_ids=product_ids, start_time=order_date,
                                                         end_time=order_date, partner_id=partner_id, user_id=user_id,
                                                         tz=8)

        product_list = []
        for m in items:
            product = product_map.get(str(m.product_id)) if product_map.get(str(m.product_id)) else {}
            units = product.get('units', [])
            unit_name = ''
            unit_spec = ''
            unit_rate = 0
            for u in units:
                if str(u.get('id')) == str(m.unit_id):
                    unit_name = u.get('name')
                    unit_spec = u.get('code')
                    unit_rate = u.get('rate')
            quantity = convert_to_decimal(m.quantity)
            price = convert_to_decimal(m.price)
            amount = quantity * price
            total_amount += amount
            row = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username,
                main_id=main_id,
                product_id=m.product_id,
                product_code=product.get('code'),
                product_name=product.get('name'),
                product_spec=product.get('model_name'),
                unit_id=m.unit_id,
                unit_name=unit_name,
                unit_spec=unit_spec,
                unit_rate=unit_rate,
                quantity=quantity,
                price=price,
                tax_rate=tax_rate_map.get(str(m.product_id)),
                amount=amount
            )
            product_list.append(row)
        receipt['total_amount'] = total_amount,  # 根据明细自己算得
        receipts_log = dict(
            id=gen_snowflake_id(),
            partner_id=partner_id,
            created_by=user_id,
            created_at=datetime.utcnow(),
            created_name=username,
            main_id=main_id,
            action="INITED",
        )
        res = self_picking_db.create_self_picking(receipt_data=receipt, product_list=product_list,
                                                  receipts_log=receipts_log)
        if res is True:
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=branch_id,
                                             doc_type="self_picking"))
            response["receipt_id"] = main_id
            response["result"] = "success"
        else:
            response["result"] = "failed"
        return response

    def list_self_picking(self, request, partner_id=None, user_id=None, is_mobile=False):
        """查询门店自采单列表"""
        response = {}
        start_date = self.utctimestamp2datetime(request.start_date)
        end_date = self.utctimestamp2datetime(request.end_date)
        branch_ids = [convert_to_int(_id) for _id in request.branch_ids] if request.branch_ids else []
        branch_type = request.branch_type
        code = request.code
        status = list(request.status) if request.status else []
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        order = request.order
        sort = request.sort
        reason = request.reason
        ids = [convert_to_int(_id) for _id in request.ids] if request.ids else None
        product_ids = list(request.product_ids) if request.product_ids else []

        query_set = self_picking_db.list_self_picking(partner_id=partner_id, reason=reason,
                                                      start_date=start_date, end_date=end_date,
                                                      code=code, status=status, ids=ids,
                                                      branch_ids=branch_ids,
                                                      branch_type=branch_type,
                                                      order=order, sort=sort,
                                                      limit=limit, offset=offset,
                                                      include_total=include_total, product_ids=product_ids)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            response["total"] = total
        else:
            query_set = query_set
        result_list = []
        if query_set:
            branch_ids = [q.branch_id for q in query_set]
            branch_map = get_branch_map(branch_ids=branch_ids, branch_type="STORE", partner_id=partner_id,
                                        user_id=user_id)
            reason_map = get_supply_reason_map(_type="SELF_PICKING", partner_id=partner_id, user_id=user_id)
            for row in query_set:
                row = row.serialize(conv=True)
                row['reason_name'] = reason_map.get(row.get('reason'))
                row['attachments'] = eval(row['attachments']) if row.get('attachments') else []
                branch = branch_map.get(row.get('branch_id'))
                if branch and isinstance(branch, dict):
                    row['branch_name'] = branch.get('name')
                result_list.append(row)
        # 给移动端列表拼接商品名称
        if is_mobile is True:
            receipt_ids = [t.get('id') for t in result_list]
            pro_names = self_picking_db.query_product_name_by_ids(receipt_ids, partner_id)
            pro_names_map = {}
            if pro_names:
                for p in pro_names:
                    if p[0] in pro_names_map.keys():
                        if len(pro_names_map[p[0]]) < 5:
                            pro_names_map[p[0]].append(p[1])
                    else:
                        pro_names_map[p[0]] = [p[1]]
            response["pro_names_map"] = pro_names_map
            # 单据状态排序：按“新建、已提交、已驳回、已审核”
            result_list = sorted(result_list, key=lambda e: SELF_PICKING_STATUS.index(e.__getitem__('status')))
        response["rows"] = result_list
        return response

    def get_self_picking_detail(self, request, partner_id=None, user_id=None):
        """查询门店自采单详情"""
        response = {}
        receipt_id = convert_to_int(request.receipt_id)
        receipt_detail = self_picking_db.get_self_picking_by_id(receipt_id=receipt_id,
                                                                partner_id=partner_id)
        if not receipt_detail:
            raise DataValidationException("Self Picking not found！- {}".format(receipt_id))
        reason_map = get_supply_reason_map(_type="SELF_PICKING", partner_id=partner_id, user_id=user_id)
        branch_map = get_branch_map(branch_ids=[receipt_detail.branch_id], branch_type="STORE", partner_id=partner_id,
                                    user_id=user_id)
        branch = branch_map.get(receipt_detail.branch_id)
        response.update(receipt_detail.serialize(conv=True))
        response['reason_name'] = reason_map.get(response.get('reason'))
        response['attachments'] = eval(response['attachments']) if response.get('attachments') else []
        if branch and isinstance(branch, dict):
            response['branch_name'] = branch.get('name')
        items = StoreSelfPickingProduct.get_picking_product_by_id(partner_id=partner_id, main_id=receipt_id)
        response["items"] = []
        if items:
            product_ids = [p.product_id for p in items]
            product_map = get_product_map(product_ids=product_ids, return_fields='id,name,model_name',
                                          partner_id=partner_id, user_id=user_id)
            for row in items:
                row = row.serialize(conv=True)
                product = product_map.get(str(row.get('product_id')))
                if isinstance(product, dict):
                    units = product.get('units', [])
                    for u in units:
                        u['id'] = int(u.get('id'))
                    row['units'] = units
                    row['product_name'] = product.get('name')
                    row['model_name'] = product.get('model_name')
                response["items"].append(row)

        return response

    def update_self_picking(self, request, partner_id=None, user_id=None):
        """更新门店自采单"""
        ret = {}
        receipt_id = convert_to_int(request.receipt_id)
        branch_id = convert_to_int(request.branch_id)
        query_order = self_picking_db.get_self_picking_by_id(receipt_id=receipt_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该门店自采单不存在-{}".format(receipt_id))
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receipt = dict(
            id=receipt_id,
            partner_id=partner_id,
            updated_by=user_id,
            updated_name=username,
            updated_at=datetime.utcnow()
        )
        product_list = []
        if query_order.status in ["SUBMITTED", "APPROVED", "DELETED"]:
            raise DataValidationException("{}状态下不允许更新详情".format(query_order.status))
        if branch_id and branch_id != query_order.branch_id:
            branch_map = get_branch_map(branch_ids=[branch_id], branch_type="STORE", return_fields="id,code,name",
                                        partner_id=partner_id, user_id=user_id)
            branch = branch_map.get(branch_id) if branch_map.get(branch_id) else {}
            receipt.update(
                dict(
                    branch_id=branch_id,
                    branch_name=branch.get("name"),
                    branch_code=branch.get("code")))
        if request.branch_type:
            receipt.update(dict(
                branch_type=request.branch_type,
            ))
        if request.reason:
            receipt.update(dict(
                reason=request.reason
            ))
        if request.remark:
            receipt.update(dict(
                remark=request.remark
            ))
        if request.attachments:
            receipt.update(dict(
                attachments=request.attachments
            ))
        total_amount = Decimal(0)
        items = request.items
        if not items:
            raise DataValidationException("更新门店自采单详情必须包含商品")
        product_ids = [convert_to_int(m.product_id) for m in items]
        p_ids = get_uuids(len(product_ids))
        product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name,model_name",
                                      partner_id=partner_id,
                                      user_id=user_id)
        for inx, m in enumerate(items):
            product = product_map.get(str(m.product_id)) if product_map.get(str(m.product_id)) else {}
            units = product.get('units', [])
            unit_name = ''
            unit_spec = ''
            for u in units:
                if str(u.get('id')) == str(m.unit_id):
                    unit_name = u.get('name')
                    unit_spec = u.get('code')
            quantity = convert_to_decimal(m.quantity)
            price = convert_to_decimal(m.price)
            amount = quantity * price
            total_amount += amount
            row = dict(
                id=p_ids[inx],
                partner_id=partner_id,
                main_id=receipt_id,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username,
                updated_at=datetime.utcnow(),
                product_id=m.product_id,
                product_code=product.get('code'),
                product_name=product.get('name'),
                product_spec=product.get('model_name'),
                unit_id=m.unit_id,
                unit_name=unit_name,
                unit_spec=unit_spec,
                quantity=quantity,
                price=price,
                amount=amount
            )
            product_list.append(row)
        receipt['total_amount'] = total_amount,  # 根据明细自己算得
        res = self_picking_db.update_self_picking(update_data=receipt, product_list=product_list,
                                                  update_detail=True)
        if res is True:
            ret["receipt_id"] = receipt_id
            ret["result"] = "success"
        return ret

    def change_self_picking_status(self, request, partner_id=None, user_id=None):
        """变更门店自采单状态"""
        ret = {}
        receipt_id = convert_to_int(request.receipt_id)
        status = request.status
        query_order = self_picking_db.get_self_picking_by_id(receipt_id=receipt_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该门店自采单不存在-{}".format(receipt_id))
        if status not in ["SUBMITTED", "APPROVED", "REJECTED", "DELETED", "CANCELLED"]:
            raise DataValidationException("更新状态不合法{}".format(status))
        if status == query_order.status:
            raise DataValidationException("状态已变更请勿重复操作")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receipt = dict(
            id=receipt_id,
            partner_id=partner_id,
            updated_by=user_id,
            updated_name=username,
            status=status,
            remark=request.remark,
        )
        product_list = []
        res = self_picking_db.update_self_picking(update_data=receipt, product_list=product_list,
                                                  update_detail=False)
        if res is True:
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=query_order.branch_id,
                                             doc_type="self_picking"))
            ret["receipt_id"] = receipt_id
            ret["result"] = "success"
            receipts_log = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                created_at=datetime.utcnow(),
                created_name=username,
                main_id=receipt_id,
                action=status,
            )
            # 保存操作日志
            self_picking_db.create_self_picking_log(receipts_log=receipts_log)
            if status == "APPROVED":
                """
                单据审核后需要扣减库存处理：
                    扣减库存：WITHDRAW = 1; 
                    增加库存：DEPOSIT = 2; 
                """
                accounts = []
                # 查询自采商品，增加库存
                products = StoreSelfPickingProduct.get_picking_product_by_id(partner_id=partner_id,
                                                                             main_id=receipt_id)
                product_ids = []
                for m in products:
                    product_ids.append(m.product_id)
                # 获取商品单位和转换率map
                product_unit_rate_map = get_product_unit_rate_map(product_ids=product_ids,
                                                                  partner_id=partner_id,
                                                                  user_id=user_id)
                for m in products:
                    # 单位换算为核算数量
                    unit_rate_dict = product_unit_rate_map.get(m.product_id) if product_unit_rate_map.get(
                        m.product_id) else {}
                    quantity = convert_to_decimal(m.quantity)
                    unit_rate = convert_to_decimal(unit_rate_dict.get(m.unit_id))
                    if not unit_rate:
                        raise DataValidationException("该商品单位转换率未拿到-{}".format(m.product_name))
                    accounting_quantity = quantity * unit_rate
                    accounting = dict(
                        branch_id=query_order.branch_id,
                        product_id=m.product_id,
                        amount=str(accounting_quantity),    # 存字符串为了json序列化
                        action=2
                    )
                    accounts.append(accounting)

                # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
                message = dict(batch_no=str(receipt_id),
                               code='SELF_PICKING',
                               action=100,
                               description='STORE_SELF_PICKING',
                               trace_id=query_order.code,
                               accounts=accounts,
                               partner_id=partner_id,
                               user_id=user_id,
                               business_time=datetime.utcnow())
                inventory_dict = dict(batch_no=str(receipt_id), code="SELF_PICKING", batch_action=100,
                                      action_dec=ACTION[100],
                                      batch_id=None,
                                      status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                      )
                inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                 partner_id=partner_id, user_id=user_id)

                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                    message=message)
                
                # vendor单据同步给三方
                message = {
                                'doc_resource': 's_selfpurchase',
                                'doc_id': receipt_id,
                                'partner_id': partner_id,
                                'user_id': user_id,
                                'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
                mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)
                
                # 同步记录落表，用于后续补偿
                tp_trans_log = {
                                'id': receipt_id,
                                'doc_code': query_order.code,
                                'doc_type': 's_selfpurchase',
                                'status': 'inited',
                                'msg': str(message),
                                'partner_id': partner_id,
                                'created_by': user_id,
                                'created_at': datetime.utcnow(),
                                'updated_at': datetime.utcnow()
                            }
                TpTransLogModel.create_logs_list([tp_trans_log])
        return ret

    def get_picking_product_by_store(self, request, partner_id=None, user_id=None):
        """取得门店可自采商品（相同属性区域）"""
        response = {}
        limit = request.limit
        if not limit:
            limit = -1
        offset = request.offset
        sort = request.sort
        include_total = request.include_total
        search = request.search
        search_fields = request.search_fields
        store_id = request.store_id
        category_ids = list(request.category_ids)
        if category_ids:
            product_relation_filters = {"product_category__in": [str(_id) for _id in category_ids]}
        else:
            product_relation_filters = None
        list_store_product_ret = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_self_picking=True),
            product_filters={
                "status": "ENABLED",
            },
            product_search=search,
            product_search_fields=search_fields,
            user_id=user_id,
            offset=offset,
            limit=limit,
            sort=sort,
            include_total=include_total,
            include_product_units=True,
            return_fields="allow_self_picking",
            include_product_fields='code,name,model_name,category,category_name',
            can_order=True,
            product_relation_filters=product_relation_filters,
            region="ATTRIBUTE_REGION"
        )
        total = 0
        store_product_picking = []
        if list_store_product_ret:
            list_store_product = list_store_product_ret['rows']
            total = list_store_product_ret.get('total')
            for product in list_store_product:
                product_dict = dict(
                    product_id=int(product.get('product_id')),
                    product_code=product.get('code'),
                    product_name=product.get('name'),
                    spec=product.get('spec'),
                    category_id=int(product.get('category')) if product.get('category') else 0,
                    category_name=product.get('category_name'),
                    model_name=product.get('model_name')
                )
                units = product.get('units', [])
                for u in units:
                    u['id'] = int(u.get('id'))
                product_dict['units'] = units
                # 过滤自采
                if product.get('allow_self_picking'):
                    store_product_picking.append(product_dict)
        if include_total:
            response["total"] = total
        response["rows"] = store_product_picking
        return response

    def get_self_picking_log(self, receipt_id, partner_id=None, user_id=None):
        """自采历史记录"""
        res = {}
        total, logs = self_picking_db.get_self_picking_log(receipt_id, partner_id=partner_id)
        res["rows"] = []
        res["total"] = total
        user_ids = [log.created_by for log in logs]
        user_dict = get_username_map(partner_id=partner_id, user_id=user_id, ids=user_ids)
        for log in logs:
            row = dict(
                id=log.id,
                status=log.action,
                created_at=self.get_timestamp(log.created_at),
                created_name=user_dict.get(log.created_by),
                created_by=log.created_by
            )
            res["rows"].append(row)
        return res

    def get_self_picking_by_id(self, receipt_id, partner_id=None):
        """根据自采单id获取一个自采单"""
        return self_picking_db.get_self_picking_by_id(receipt_id=receipt_id, partner_id=partner_id)


self_picking_service = StoreSelfPickingService()
