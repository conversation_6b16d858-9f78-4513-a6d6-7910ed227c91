# -*- coding: utf-8 -*-
from datetime import datetime
from sqlalchemy import func
import logging
from datetime import datetime
from decimal import Decimal
import json
from google.protobuf.timestamp_pb2 import Timestamp


from supply.utils.helper import convert_to_int, get_branch_map, get_branch_list_map
from supply.client.metadata_service import metadata_service

from supply.model.receiving_diff import ReceivingDiffModel, ReceivingDiffProductModel


class ReceivingDiffBiService():

    def get_receiving_diff_collect(self, partner_id, user_id, request, return_amount=None, is_franchisee=False,
                                   is_store=False):
        """查询收货差异汇总报表"""
        st_ids = request.st_ids
        category_ids = request.category_ids
        product_name = request.product_name
        start_date = request.start_date
        end_date = request.end_date
        logistics_type = request.logistics_type
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        branch_type = request.branch_type
        delivery_bys = request.delivery_bys
        product_ids = request.product_ids
        received_types = request.received_types if hasattr(request, 'received_types') else []

        if is_store:
            if is_franchisee:
                received_types = ['FRS_DEMAND']
            # else:
            #     received_type = ['DEMAND', 'DEMAND_ADJUST', None]
            # if is_franchisee:
            #     branch_type = ['FRS_STORE']
            # else:
            #     branch_type = ['STORE', None]


        store_list = []
        category_list = []
        list_products = []
        c_product_ids = []
        if category_ids:
            for category_id in category_ids:
                category_list.append(str(category_id))
            relation_filters = {'product_category': category_list}
            list_products = metadata_service.get_product_list(relation_filters=relation_filters,
                                                              partner_id=partner_id, user_id=user_id).get('rows')
            # list_products = list_products
            # print(list_products)

        if product_name:
            search = str(product_name)
            list_products = metadata_service.get_product_list(search=search, search_fields='name',
                                                              partner_id=partner_id, user_id=user_id).get('rows')
            # list_products = list_products.get('rows')

        for st in st_ids:
            store_list.append(st)

        product_ids = [product_id for product_id in product_ids] if product_ids else []

        if list_products:
            for product in list_products:
                c_product_ids.append(int(product['id']))
            if len(product_ids):
                product_ids = list(set(c_product_ids).intersection(set(product_ids)))
            else:
                product_ids = c_product_ids
            if len(product_ids) == 0:
                return None, None

        total, bi_data = ReceivingDiffProductModel.bi_get_return_collect(partner_id, st_ids, product_ids,
                                                                         start_date, end_date, limit,
                                                                         offset, include_total, logistics_type, 
                                                                         branch_type, delivery_bys,
                                                                         is_franchisee=True,
                                                                         received_type=received_types)
        # print(bi_data)
        p_ids = []
        s_ids = []
        position_ids = []
        for d in bi_data:
            p_ids.append(int(d.product_id))
            s_ids.append(int(d.received_by))
            position_ids.append(int(d.sub_receive_by))

        product_map = {}
        products = metadata_service.get_product_list(ids=p_ids, return_fields='id,name,code,category,model_name',
                                                     partner_id=partner_id, user_id=user_id).get('rows', [])
        for p in products:
            product_map[int(p['id'])] = p

        store_map = {}
        list_store = metadata_service.get_store_list(ids=s_ids, return_fields='id,name,code',
                                                     partner_id=partner_id, user_id=user_id).get('rows', [])
        for s in list_store:
            store_map[int(s['id'])] = s
        
        delivery_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center", branch_ids=delivery_bys,
                                             partner_id=partner_id, user_id=user_id)

        category_map = {}
        categorys = metadata_service.get_product_category_list(return_fields="id,code,name",
                                                               partner_id=partner_id, user_id=user_id).get('rows', [])
        for category in categorys:
            category_map[int(category['id'])] = {
                'code': category.get('code'),
                'name': category.get('name')
            }

        position_map = get_branch_map(branch_ids=position_ids, branch_type="POSITION", partner_id=partner_id,
                                      user_id=user_id, return_fields="id,code,name")

        result = []
        for data in bi_data:
            r = dict(
                id=data.id,
                store_id=data.received_by,
                position_id=data.sub_receive_by
            )
            position = position_map.get(data.sub_receive_by)
            if position and isinstance(position, dict):
                r['position_code'] = position.get('code')
                r['position_name'] = position.get('name')
            
            if store_map.get(int(data.received_by)):
                st = store_map.get(int(data.received_by))
                r['store_code'] = st['code']
                r['store_name'] = st['name']
            if product_map.get(int(data.product_id)):
                p = product_map.get(int(data.product_id))
                r['product_code'] = p.get('code')
                r['product_name'] = p.get('name')
                r['product_spec'] = p.get('model_name')
                if p.get('category'):
                    category_id = int(p['category'])
                    r['category_id'] = category_id
                    r['category_code'] = category_map.get(category_id, {}).get('code')
                    r['category_name'] = category_map.get(category_id, {}).get('name')
            r['quantity'] = data.quantity
            r['d_diff_quantity'] = data.d_quantity
            r['s_diff_quantity'] = data.s_quantity
            r['unit_id'] = data.unit_id
            r['unit_name'] = data.unit_name
            r['accounting_quantity'] = data.diff_accounting_quantity
            r['accounting_unit_id'] = data.accounting_unit_id
            r['accounting_unit_name'] = data.accounting_unit_name
            r['logistics_type'] = data.logistics_type
            r['delivery_by'] = data.delivery_by
            r['delivery_by_name'] = delivery_map.get(data.delivery_by, {}).get('name')
            r['delivery_by_code'] = delivery_map.get(data.delivery_by, {}).get('code')
            if return_amount: # 供应商平台收获差异单需要返回金额
                r['amount'] = data.amount
                r['d_diff_amount'] = data.d_diff_amount
                r['s_diff_amount'] = data.s_diff_amount
                r['netwrt'] = data.netwrt
                r['d_diff_netwrt'] = data.d_diff_netwrt
                r['s_diff_netwrt'] = data.s_diff_netwrt
                r['cost_price'] = data.netwrt/data.quantity
                r['tax_price'] = data.amount/data.quantity
            if is_franchisee:
                r['tax_rate'] = data.tax_rate
                r['tax_price'] = data.tax_price
                r['cost_price'] = data.cost_price
                r['tax_amount'] = data.tax_price * data.quantity
                r['cost_amount'] = data.tax_price / (1 + data.tax_rate / 100) * data.quantity
            result.append(r)

        return total, result

    def get_receiving_diff_detailed(self, partner_id, user_id, request, return_amount=None, is_franchisee=False,
                                    is_store=False):
        """查询收货差异详情报表"""
        st_ids = request.st_ids
        category_ids = request.category_ids
        product_name = request.product_name
        start_date = request.start_date
        end_date = request.end_date
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        code = request.code
        branch_type = request.branch_type
        logistics_type = request.logistics_type
        delivery_bys = request.delivery_bys
        product_ids = request.product_ids
        received_types = request.received_types if hasattr(request, 'received_types') else []

        if is_store:
            if is_franchisee:
                received_types = ['FRS_DEMAND']
            # else:
            #     received_type = ['DEMAND', 'DEMAND_ADJUST', None]
            # if is_franchisee:
            #     branch_type = ['FRS_STORE']
            # else:
            #     branch_type = ['STORE', None]

        # include_total = True):


        store_list = []
        c_product_ids = []
        category_list = []
        list_products = []
        if category_ids:
            for category_id in category_ids:
                category_list.append(str(category_id))
            relation_filters = {'product_category': category_list}
            list_products = metadata_service.get_product_list(relation_filters=relation_filters,
                                                              partner_id=partner_id, user_id=user_id).get('rows')
            # list_products = list_products
            # print(list_products)

        if product_name:
            search = str(product_name)
            list_products = metadata_service.get_product_list(search=search, search_fields='name',
                                                              partner_id=partner_id, user_id=user_id).get('rows')
            # list_products = list_products.get('rows')

        for st in st_ids:
            store_list.append(st)

        product_ids = [product_id for product_id in product_ids] if product_ids else []

        if list_products:
            for product in list_products:
                c_product_ids.append(int(product['id']))
            if len(product_ids):
                product_ids = list(set(c_product_ids).intersection(set(product_ids)))
            else:
                product_ids = c_product_ids
            if len(product_ids) == 0:
                return None, None

        total, bi_data = ReceivingDiffProductModel.bi_get_receiving_diff_detailed(partner_id, st_ids=store_list,
                                                                                  product_ids=product_ids,
                                                                                  start_date=start_date,
                                                                                  end_date=end_date, limit=limit,
                                                                                  offset=offset,
                                                                                  include_total=include_total,
                                                                                  logistics_type=logistics_type, code=code,
                                                                                  branch_type=branch_type,
                                                                                  delivery_bys=delivery_bys,
                                                                                  received_type=received_types)

        p_ids = []
        s_ids = []
        diff_ids = []
        position_ids = []
        for d in bi_data:
            p_ids.append(int(d.product_id))
            s_ids.append(int(d.received_by))
            diff_ids.append(d.diff_id)
            position_ids.append(d.sub_receive_by)

        product_map = {}
        products = metadata_service.get_product_list(ids=p_ids, return_fields='id,name,code,category,model_name',
                                                     partner_id=partner_id, user_id=user_id).get('rows', [])
        for p in products:
            product_map[int(p['id'])] = p

        store_map = {}
        list_store = metadata_service.get_store_list(ids=s_ids, return_fields='id,name,code',
                                                     partner_id=partner_id, user_id=user_id).get('rows', [])
        for s in list_store:
            store_map[int(s['id'])] = s
        

        delivery_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center", branch_ids=delivery_bys,
                                             partner_id=partner_id, user_id=user_id)

        category_map = {}
        categorys = metadata_service.get_product_category_list(return_fields="id,code,name",
                                                               partner_id=partner_id, user_id=user_id).get('rows', [])
        for category in categorys:
            category_map[int(category['id'])] = {
                'code': category.get('code'),
                'name': category.get('name')
            }
        
        diff_map = {}
        list_receiving_diff = ReceivingDiffModel.bi_get_receiving_diff(diff_ids, partner_id=partner_id)
        for ad_obj in list_receiving_diff:
            diff_map[ad_obj.id]={
                'diff_code': ad_obj.code,
                'diff_date': ad_obj.demand_date.strftime('%Y-%m-%d') if ad_obj.demand_date else None
            }
            
        position_map = get_branch_map(branch_ids=position_ids, branch_type="POSITION", partner_id=partner_id,
                                      user_id=user_id, return_fields="id,code,name")

        sources = {'AUTO': '自动生成', 'HC': '手动创建'}
        # reasons = []
        # if is_franchisee:
        #     reasons = metadata_service.list_entity('SUPPLY_REASON', filters={'type': 'RECEIVE_DIFF'},
        #                                            partner_id=partner_id, user_id=user_id).get('rows', [])
        #     reasons = {i['fields']['code']: i['fields']['name'] for i in reasons}

        result = []
        for data in bi_data:
            r = dict()
            r['id'] = data.id
            r['store_id'] = data.received_by
            position = position_map.get(data.sub_receive_by)
            if position and isinstance(position, dict):
                r['position_code'] = position.get('code')
                r['position_name'] = position.get('name')
            if store_map.get(int(data.received_by)):
                st = store_map.get(int(data.received_by))
                r['store_code'] = st['code']
                r['store_name'] = st['name']
            if product_map.get(int(data.product_id)):
                p = product_map.get(int(data.product_id))
                r['product_code'] = p.get('code')
                r['product_name'] = p.get('name')
                r['product_spec'] = p.get('model_name')
                if p.get('category'):
                    category_id = int(p['category'])
                    r['category_id'] = category_id
                    r['category_code'] = category_map.get(category_id, {}).get('code')
                    r['category_name'] = category_map.get(category_id, {}).get('name')

            r['quantity'] = data.diff_quantity
            r['s_diff_quantity'] = data.s_diff_quantity
            r['d_diff_quantity'] = data.d_diff_quantity
            r['unit_id'] = data.unit_id
            r['unit_name'] = data.unit_name
            r['accounting_quantity'] = data.diff_accounting_quantity
            r['accounting_unit_id'] = data.accounting_unit_id
            r['accounting_unit_name'] = data.accounting_unit_name
            r['diff_id'] = data.diff_id
            r['diff_code'] = diff_map.get(data.diff_id, {}).get('diff_code')
            r['diff_date'] = diff_map.get(data.diff_id, {}).get('diff_date')
            r['reason_type'] = data.reason_type
            r['logistics_type'] = data.logistics_type
            r['delivery_by'] = data.delivery_by
            r['delivery_by_name'] = delivery_map.get(data.delivery_by, {}).get('name')
            r['delivery_by_code'] = delivery_map.get(data.delivery_by, {}).get('code')
            r['demand_date'] = Timestamp(seconds=int(data.demand_date.timestamp()))
            r['created_at'] = Timestamp(seconds=int(data.created_at.timestamp()))
            r['receiving_code'] = data.receiving_code
            if return_amount: # 供应商平台收获差异单需要返回金额
                r['tax_price'] = data.tax_price
                r['amount'] = data.diff_quantity*data.tax_price if data.diff_quantity and data.tax_price else 0
                r['d_diff_amount'] = data.d_diff_quantity*data.tax_price if data.d_diff_quantity and data.tax_price else 0
                r['s_diff_amount'] = data.s_diff_quantity*data.tax_price if data.s_diff_quantity and data.tax_price else 0
                
                r['netwrt'] = r['amount']/(1+data.tax_rate/100) if data.tax_rate else r['amount'] 
                r['d_diff_netwrt'] = r['d_diff_amount']/(1+data.tax_rate/100) if data.tax_rate else r['d_diff_amount'] 
                r['s_diff_netwrt'] = r['s_diff_amount']/(1+data.tax_rate/100) if data.tax_rate else r['s_diff_amount'] 
                r['cost_price'] = data.tax_price/(1+data.tax_rate/100) if data.tax_price and data.tax_rate else data.cost_price
            if is_franchisee:
                r['tax_rate'] = data.tax_rate
                r['tax_price'] = data.tax_price
                r['cost_price'] = data.cost_price
                r['reason'] = data.reason_type
                r['diff_type_name'] = sources.get(data.diff_type)
                r['tax_amount'] = data.tax_price * data.diff_quantity
                r['cost_amount'] = data.tax_price / (1 + data.tax_rate / 100) * data.diff_quantity
                r['receive_date'] = Timestamp(seconds=int(data.receive_date.timestamp()))
                r['code'] = data.code

            result.append(r)

        return total, result


receiving_diff_bi_service = ReceivingDiffBiService()