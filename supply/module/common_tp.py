# -*- coding: utf-8 -*-
import json
import logging
import hashlib
from datetime import datetime, timedelta
from decimal import Decimal
from google.protobuf.timestamp_pb2 import Timestamp

from supply.driver.mq import register_mq_task, mq_producer
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProduct, SupplyFranchiseeDemand
from supply.module.franchisee.franchisee_demand import franchisee_demand_service
from supply.utils.helper import MessageTopic

from supply.client.metadata_service import metadata_service

from supply.model.operation_log import TpTransLogModel
from supply.model.receiving_diff import ReceivingDiffModel, ReceivingDiffProductModel
from supply.model.self_picking.self_picking import self_picking_db, StoreSelfPickingProduct
from supply.model.adjust import AdjustRepository
from supply.model.stocktake import StockTakeRepository
from supply.model.demand.supply_demand import Supply_demand
from supply.model.demand.supply_demand_product import Supply_demand_product
from supply.model.warehouse.purchase_order import warehouse_purchase_order_db, warehouse_purchase_order_product_db
from supply.model.manufactory.packing_receipts import packing_receipts_db
from supply.model.manufactory.processing_receipts import processing_receipts_db

from supply.module.transfer_service import ssm as transfer_service






class CommonTpService():
    
    # 根据数字拆解出由哪几个数字组成 限定2的n次方的数
    def Total2Number(self, total):
        num_list = []
        n = 1
        for i in range(1, total+1):
            if n > total:
                break
            res = n&total
            if res != 0:
                num_list.append(n)
            n = n*2
        return num_list

    def map_main_branch_type(self, main_branch_type):
        if main_branch_type == 'S':
            return 'STORE'
        elif main_branch_type == 'W':
            return 'WAREHOUSE'
        elif main_branch_type == 'V':
            return 'VENDOR'
        elif main_branch_type == 'M':
            return 'MACHINING_CENTER'
        else:
            return None

    def get_store_info(self, store_id=None, partner_id=None, user_id=None):

        store_info = metadata_service.get_entity_by_id(schema_name='store', id=int(store_id), return_fields="id,code,name",
                                            partner_id=partner_id, user_id=user_id)
        return store_info
    
    def get_vendor_info(self, vendor_id=None, partner_id=None, user_id=None):

        vendor_info = metadata_service.get_entity_by_id(schema_name='vendor', id=int(vendor_id), return_fields="id,code,name",
                                            partner_id=partner_id, user_id=user_id)
        return vendor_info
    
    def get_wh_info(self, warehouse_id=None, partner_id=None, user_id=None):

        wh_info = metadata_service.get_entity_by_id(schema_name='distrcenter', id=int(warehouse_id), return_fields="id,code,name",
                                            partner_id=partner_id, user_id=user_id)
        return wh_info
    
    def get_mc_info(self, mc_id=None, partner_id=None, user_id=None):

        mc_info = metadata_service.get_entity_by_id(schema_name='machining-center', id=int(mc_id), return_fields="id,code,name",
                                            partner_id=partner_id, user_id=user_id)
        return mc_info

    def get_unit_map(self, partner_id, user_id):
        """获取单位map"""
        unit_dict = {}
        units_ret = metadata_service.get_unit_list(return_fields='id,code,name',
                                                   partner_id=partner_id, user_id=user_id)
        units = []
        if units_ret:
            units = units_ret['rows']
        if isinstance(units, list):
            for unit in units:
                if isinstance(unit, dict) and 'id' in unit:
                    unit_dict[str(unit['id'])] = unit
        return unit_dict  

    ### -- 三方消息对接 start -- ###
    # 传输单据数据拼接处理
    def deal_receipt_for_tp(self, doc_id, doc_resource, partner_id, user_id, operate_time=None):
        """
        单据包括：
            - 收货差异单
            - 自采单
            - 盘点单
            - 报废单
            - 调拨单
            - 物料转换单
        """
        doc_code = None
        unit_dict = self.get_unit_map(partner_id, user_id)
        doc_detail = {}
        
        # 收货差异单
        if doc_resource in ('s_demand_rec_diff', 'fs_demand_rec_diff'):
            diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=doc_id)
            count, diff_product_list = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id=doc_id, partner_id=partner_id)
            receipt_product_list = []
            price_map = {}
            order_type_id = 0
            if diff_db.branch_type == 'FRS_STORE':
                products = SupplyFranchiseeDemandProduct.list_demand_product(partner_id=partner_id, demand_id=diff_db.master_id)
                for p in products:
                    price_map[p.product_id] = p.sales_price
                demand_db = SupplyFranchiseeDemand.get(diff_db.master_id)
                order_type_id = demand_db.order_type_id
            for product in diff_product_list:
                p_args = {	
                            "productCode": product.product_code,
                            "productName": product.product_name,
                            "diffQty": '%.3f' % (product.diff_quantity),
                            "d_diffQty": '%.3f' % (product.d_diff_quantity),
                            "s_diffQty": '%.3f' % (product.s_diff_quantity),
                            "reason": product.reason_type,
                            "unitName": product.unit_name,
                            "unitCode": unit_dict.get(str(product.unit_id), {}).get("code"),
                            "acctDiffQty": '%.3f' % (product.diff_quantity * product.unit_rate),
                            "acct_d_diffQty": '%.3f' % (product.d_diff_quantity * product.unit_rate),
                            "acct_s_diffQty": '%.3f' % (product.s_diff_quantity * product.unit_rate),
                            "acctUnitName": product.accounting_unit_name,
                            "acctUnitCode": unit_dict.get(str(product.accounting_unit_id), {}).get("code"),
                            "price": '%.3f' % (product.cost_price), # 不含税价
                            "spec": product.unit_spec,
                            "taxPrice": '%.3f' % (product.tax_price/ product.unit_rate) if product.unit_rate else product.tax_price,
                            "retailPrice": '%.3f' % (price_map.get(product.product_id, 0)/ product.unit_rate) if product.unit_rate else price_map.get(product.product_id, 0),
                            "taxAmount": '%.3f' % (product.tax_price * product.d_diff_quantity),
                            "taxSalesAmount": '%.3f' % (price_map.get(product.product_id, 0) * product.d_diff_quantity)
                        }
                receipt_product_list.append(p_args)
            store_info = self.get_store_info(diff_db.received_by, partner_id, user_id)
            vendor_info = self.get_vendor_info(diff_db.delivery_by, partner_id, user_id)
            # print(store_info, vendor_info, unit_dict)
            doc_code = diff_db.code
            doc_detail = {
                            "partnerId": str(partner_id),
                            "receiptType": doc_resource,
                            "receiptBigType": str(doc_resource+'_'+diff_db.logistics_type).lower(),
                            "code": diff_db.code,
                            "sourceCode": diff_db.receiving_code, # 关联的收货单号
                            "orderDate": diff_db.demand_date.strftime('%Y-%m-%d %H:%M:%S'), # 关联的收货单订货日期
                            "receiveDate": diff_db.receive_date.strftime('%Y-%m-%d %H:%M:%S'), # 关联的收货单收货日期
                            "approveDate": diff_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'), # 差异单被确认时间
                            "distrType": diff_db.logistics_type,
                            "storeCode": store_info.get('fields', {}).get('code'),
                            "storeName": store_info.get('fields', {}).get('name'),
                            "storeType": store_info.get('fields', {}).get('type'),
                            "supplyCode": vendor_info.get('fields', {}).get('code'),
                            "supplyName": vendor_info.get('fields', {}).get('name'),
                            "remark": diff_db.remark if diff_db.remark else "",
                            "products": receipt_product_list,
                            "createdAt": diff_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": diff_db.created_name,
                            "updatedAt": diff_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": diff_db.updated_name,
                            "isFranchisee": '1' if diff_db.branch_type == 'FRS_STORE' else '0',
                            "orderTypeId": str(order_type_id) if order_type_id else '0',
                            "storeId": str(diff_db.received_by)
            }

        # 自采单
        elif doc_resource == 's_selfpurchase':
            receipt_db = self_picking_db.get_self_picking_by_id(partner_id=partner_id, receipt_id=doc_id)
            product_list = StoreSelfPickingProduct.get_picking_product_by_id(main_id=doc_id, partner_id=partner_id)
            receipt_product_list = []
            for product in product_list:
                p_args = {	
                            "productCode": product.product_code,
                            "productName": product.product_name,
                            "orderQty": '%.3f' % (product.quantity),
                            "unitName": unit_dict.get(str(product.unit_id), {}).get("name"),
                            "price": '%.3f' % (product.price), # 不含税价
                            "amount": '%.3f' % (product.amount), 
                            "spec": product.product_spec,
                        }
                receipt_product_list.append(p_args)
            store_info = self.get_store_info(receipt_db.branch_id, partner_id, user_id)

            doc_code = receipt_db.code
            doc_detail = {
                            "partnerId": str(partner_id),
                            "receiptType": doc_resource,
                            "code": receipt_db.code,
                            "orderDate": receipt_db.order_date.strftime('%Y-%m-%d %H:%M:%S'), # 关联的收货单订货日期
                            "storeCode": store_info.get('fields', {}).get('code'),
                            "storeName": store_info.get('fields', {}).get('name'),
                            "storeType": store_info.get('fields', {}).get('type'),
                            "reason": receipt_db.reason,
                            "totalAmout": '%.3f' % (receipt_db.total_amount),
                            "products": receipt_product_list,
                            "createdAt": receipt_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": receipt_db.created_name,
                            "updatedAt": receipt_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": receipt_db.updated_name
                        }
        
        # 报废单
        elif 'adjust' in doc_resource:
            receipt_db = AdjustRepository().get_adjust_by_adjust_id(doc_id, partner_id=partner_id, is_detail=True)
            product_list = AdjustRepository().list_store_adjust_product(adjust_id=doc_id,
                                                                 partner_id=partner_id)
            bom_product_list = AdjustRepository().list_adjust_bom_report_detail(adjust_id=doc_id,
                                                                 partner_id=partner_id)
            meta_products = metadata_service.get_product_list(partner_id=partner_id, user_id=user_id,
                                                         return_fields="id,code,name").get('rows', [])
            meta_p_dict = {}
            for i in meta_products:
                meta_p_dict[int(i.get("id", 0))] = {
                    "code": i.get("code"),
                    "name": i.get("name")
                }

            bom_product_dict = {}
            for bom_product in bom_product_list:
                bom_ags = {
                    "bomProductId": str(bom_product.bom_product_id),
                    "bomProductCode": meta_p_dict.get(bom_product.bom_product_id, {}).get("code"),
                    "bomProductName": meta_p_dict.get(bom_product.bom_product_id, {}).get("name"),
                    "bomUnitId": str(bom_product.bom_unit_id),
                    "unitName": bom_product.bom_unit_name,
                    "unitCode": bom_product.bom_unit_spec,
                    "acctUnitId": bom_product.bom_accounting_unit_id,
                    "acctUnitName": bom_product.bom_accounting_unit_name,
                    "acctUnitCode": bom_product.bom_accounting_unit_spec,
                    "orderQty": '%.3f' % (bom_product.qty) if bom_product.qty else "0.00",
                    "acctOrderQty": '%.3f' % (bom_product.accounting_qty) if bom_product.accounting_qty else "0.00",
                }
                if bom_product_dict.get(bom_product.product_id):
                    bom_product_dict[bom_product.product_id].append(bom_ags)
                else:
                    bom_product_dict[bom_product.product_id] = [bom_ags]

            
            receipt_product_list = []
            for product in product_list:
                unit_rate = product.get('accounting_quantity', 0) / product.get('quantity', 0)
                p_args = {	
                            "productCode": product.get('product_code'),
                            "productName": product.get('product_name'),
                            "orderQty": '%.3f' % (product.get('quantity', 0)),
                            "unitName": product.get('unit_name'),
                            "acctOrderQty": '%.3f' % (product.get('accounting_quantity', 0)),
                            "acctUnitName": product.get('accounting_unit_name'),
                            "spec": product.get('product_spec'),
                            "bomProducts": bom_product_dict.get(product.get("product_id", 0)),
                            "taxPrice": '%.3f' % (product.get("tax_price", 0) / unit_rate),
                            "retailPrice": '%.3f' % (product.get("sales_price", 0) / unit_rate),
                            "taxAmount": '%.3f' % (product.get("accounting_quantity", 0) * (product.get("tax_price", 0) / unit_rate)),
                            "taxSalesAmount": '%.3f' % (product.get("accounting_quantity", 0) * (product.get("sales_price", 0) / unit_rate))
                        }
                receipt_product_list.append(p_args)
            doc_code = receipt_db.code
            doc_detail = {
                            "partnerId": str(partner_id),
                            "receiptType": doc_resource,
                            "code": receipt_db.code,
                            "orderDate": receipt_db.adjust_date.strftime('%Y-%m-%d %H:%M:%S'), 
                            "reason": receipt_db.reason_type,
                            "sourceType": receipt_db.source,
                            "products": receipt_product_list,
                            "createdAt": receipt_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": receipt_db.created_name,
                            "updatedAt": receipt_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": receipt_db.updated_name,
                            "storeId": str(receipt_db.adjust_store)
                        }

            if receipt_db.branch_type in ("STORE", "FRS_STORE"):
                store_info = self.get_store_info(receipt_db.adjust_store, partner_id, user_id)
                doc_detail["storeCode"] = store_info.get('fields', {}).get('code')
                doc_detail["storeName"] = store_info.get('fields', {}).get('name')
                doc_detail["storeType"] = store_info.get('fields', {}).get('type')
            
            if receipt_db.branch_type == "MACHINING_CENTER":
                branch_info = self.get_mc_info(receipt_db.adjust_store, partner_id, user_id)
                doc_detail["branchCode"] = branch_info.get('fields', {}).get('code')
                doc_detail["branchName"] = branch_info.get('fields', {}).get('name')
                doc_detail["branchType"] = branch_info.get('fields', {}).get('type')

            if receipt_db.branch_type == "WAREHOUSE":
                branch_info = self.get_wh_info(receipt_db.adjust_store, partner_id, user_id)
                doc_detail["branchCode"] = branch_info.get('fields', {}).get('code')
                doc_detail["branchName"] = branch_info.get('fields', {}).get('name')
                doc_detail["branchType"] = branch_info.get('fields', {}).get('type')
            
        # 盘点单
        elif doc_resource in ('s_stocktake', 'fs_stocktake'):
            receipt_db = StockTakeRepository().get_stocktake_doc_by_id(doc_id, partner_id=partner_id, is_details=True)
            product_list = StockTakeRepository().list_stocktake_products_by_doc_id(doc_id, partner_id=partner_id)
            
            receipt_product_list = []
            if receipt_db.status != "INITED":
                for product in product_list:
                    p_args = {	
                                "productCode": product.get('product_code'),
                                "productName": product.get('product_name'),
                                "orderQty": '%.3f' % (product.get('quantity', 0)),
                                "diffQty": '%.3f' % (product.get('unit_diff_quantity', 0)), # 盘点单位差异数量——命名规则好搞，注意
                                "unitName": product.get('unit_name'),
                                "acctOrderQty": '%.3f' % (product.get('accounting_quantity', 0)),
                                "acctDiffQty": '%.3f' % (product.get('diff_quantity', 0)), # 核算单位差异数量
                                "acctUnitName": product.get('accounting_unit_name'),
                                "spec": product.get('product_spec'),
                                "taxPrice": '%.3f' % (product.get("tax_price", 0) / product.get("unit_rate", 1)),
                                "retailPrice": '%.3f' % (product.get("sales_price", 0)/ product.get("unit_rate", 1)),
                                # "tag": product.get('product_tags', [])
                                "taxAmount": '%.3f' % (product.get('accounting_quantity', 0) * (product.get("tax_price", 0) / product.get("unit_rate", 1))),
                                "taxSalesAmount": '%.3f' % (product.get('accounting_quantity', 0) * (product.get("sales_price", 0) / product.get("unit_rate", 1)))
                            }
                    receipt_product_list.append(p_args)
            
            store_info = self.get_store_info(receipt_db.branch_id, partner_id, user_id)

            doc_code = receipt_db.code
            doc_detail = {
                            "partnerId": str(partner_id),
                            "status": receipt_db.status,
                            "receiptType": doc_resource,
                            "code": receipt_db.code,
                            "orderDate": receipt_db.target_date.strftime('%Y-%m-%d %H:%M:%S'), 
                            "storeCode": store_info.get('fields', {}).get('code'),
                            "storeName": store_info.get('fields', {}).get('name'),
                            "storeType": store_info.get('fields', {}).get('type'),
                            "sourceType": receipt_db.stocktake_type + receipt_db.type,
                            "products": receipt_product_list,
                            "storeId": str(receipt_db.branch_id),
                            "createdAt": receipt_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": receipt_db.created_name,
                            "updatedAt": receipt_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": receipt_db.updated_name
                        }
        
        # 调拨单
        elif 'transfer' in doc_resource:
            receipt_db = transfer_service.get_transfer_by_id(transfer_id=doc_id, is_details=True, partner_id=partner_id)
            product_list = transfer_service.list_transfer_product(partner_id=partner_id, user_id=user_id,
                                                                       transfer_id=doc_id)

            receipt_product_list = []
            for product in product_list:
                unit_rate = product.get('accounting_quantity', 0) / product.get('quantity', 0)
                p_args = {
                            "productId": str(product.get('product_id', '')),
                            "productCode": product.get('product_code', ''),
                            "productName": product.get('product_name', ''),
                            "orderQty": '%.3f' % (product.get('quantity', 0)),
                            "unitCode": product.get('unit_spec', ''),
                            "unitName": product.get('unit_name', ''),
                            "acctOrderQty": '%.3f' % (product.get('accounting_quantity', 0)),
                            "acctUnitName": product.get('accounting_unit_name', ''),
                            "acctUnitCode": product.get('accounting_unit_spec', ''),
                            "spec": product.get('product_spec', ''),
                            "taxPrice": '%.3f' % (product.get("tax_price", 0)/ unit_rate),
                            "retailPrice": '%.3f' % (product.get("sales_price", 0)/ unit_rate),
                            "taxAmount": '%.3f'% (product.get('accounting_quantity', 0) * (product.get("tax_price", 0)/ unit_rate)),
                            "taxSalesAmount": '%.3f'% (product.get('accounting_quantity', 0) * (product.get("sales_price", 0)/ unit_rate))
                        }
                receipt_product_list.append(p_args)
            if receipt_db.branch_type == 'STORE':
                store_info = self.get_store_info(receipt_db.receiving_store, partner_id, user_id)
                shipping_store_info = self.get_store_info(receipt_db.shipping_store, partner_id, user_id)
            elif receipt_db.branch_type == 'WAREHOUSE':
                store_info = self.get_wh_info(receipt_db.receiving_store, partner_id, user_id)
                shipping_store_info = self.get_wh_info(receipt_db.shipping_store, partner_id, user_id)
            elif receipt_db.branch_type == 'MACHINING_CENTER':
                store_info = self.get_mc_info(receipt_db.receiving_store, partner_id, user_id)
                shipping_store_info = self.get_mc_info(receipt_db.shipping_store, partner_id, user_id)

            doc_code = receipt_db.code
            doc_detail = {
                            "partnerId": str(partner_id),
                            "receiptType": doc_resource,
                            "code": receipt_db.code,
                            "status": receipt_db.status,
                            "orderDate": receipt_db.transfer_date.strftime('%Y-%m-%d %H:%M:%S'), 
                            "storeId": str(receipt_db.receiving_store),
                            "storeCode": store_info.get('fields', {}).get('code', ''),
                            "storeName": store_info.get('fields', {}).get('name'),
                            "receiveId": str(receipt_db.receiving_store),
                            "receiveCode": store_info.get('fields', {}).get('code', ''),
                            "receiveName": store_info.get('fields', {}).get('name', ''),
                            "receiveType": store_info.get('fields', {}).get('type', ''),
                            "supplyId": str(receipt_db.shipping_store), 
                            "supplyCode": shipping_store_info.get('fields', {}).get('code', ''), 
                            "supplyName": shipping_store_info.get('fields', {}).get('name', ''), 
                            "remark": receipt_db.remark,
                            "products": receipt_product_list,
                            "createdAt": receipt_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": receipt_db.created_name,
                            "updatedAt": receipt_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": receipt_db.updated_name
                            
                        }

        # 订货单
        elif doc_resource == 's_demand':
            demand_db = Supply_demand.get_by_id(demand_id=doc_id, partner_id=partner_id)
            products_list, total = Supply_demand_product.get_by_demand_id(demand_id=doc_id)

            receipt_product_list = []
            for product in products_list:
                p_args = {	
                    "productCode": product.product_code, 
                    "productName":  product.product_name,
                    "orderQty": '%.3f' % (product.quantity),
                    "unitName": product.unit_name,
                    "salesType": product.sale_type,
                    "categoryName": product.product_category_name,
                    "distrType": product.distribution_type,
                    "storageType": product.storage_type,
                    "price": '%.3f' %(product.purchase_price) if product.purchase_price else "0.00",
                    "spec": product.unit_spec,
                }
                receipt_product_list.append(p_args)
            store_info = self.get_store_info(demand_db.receive_by, partner_id, user_id)

            doc_code = demand_db.code
            doc_detail = {
                            "partnerId": str(partner_id),
                            "receiptType": doc_resource,
                            "code": demand_db.code,
                            "orderDate": demand_db.demand_date.strftime('%Y-%m-%d %H:%M:%S'), # 关联的收货单订货日期
                            "storeId": str(demand_db.receive_by),
                            "storeCode": store_info.get('fields', {}).get('code'),
                            "storeName": store_info.get('fields', {}).get('name'),
                            "storeType": store_info.get('fields', {}).get('type'),
                            "sourceType": demand_db.type, 
                            "remark": demand_db.remark, 
                            "createdAt": demand_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": demand_db.created_name,
                            "updatedAt": demand_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": demand_db.updated_name,
                            "products": receipt_product_list
            }

        # 采购单
        elif 'purchase' in doc_resource:
            receipt_db = warehouse_purchase_order_db.get_order_by_id(\
                order_id=doc_id, partner_id=partner_id)
            product_list = warehouse_purchase_order_product_db.get_order_product(\
                user_id=user_id, partner_id=partner_id, order_id=doc_id)
            
            receipt_product_list = []
            for product in product_list:
                p_args = {	
                            "productId": str(product.product_id),
                            "productCode": product.product_code,
                            "productName": product.product_name,
                            "productType": product.product_type,
                            "unitId": product.purchase_unit_id,
                            "unitName": product.purchase_unit_name,
                            "unitCode": product.purchase_unit_spec,
                            "saleType": product.sale_type,
                            "spec": product.spec,
                            "acctUnitId": product.accounting_unit_id,
                            "acctUnitName": product.accounting_unit_name,
                            "acctUnitCode": product.accounting_unit_spec,
                            "orderQty": '%.3f' %(product.quantity),
                            "acctOrderQty": '%.3f' %(product.quantity*product.unit_rate),
                            "priceTax": '%.3f' %(product.price_tax),
                            "unitRate": '%.3f' %(product.unit_rate),
                            "price": '%.3f' % (product.price)
                        }
                

                receipt_product_list.append(p_args)
            
            doc_code = receipt_db.order_code

            if receipt_db.branch_type == 'WAREHOUSE':
                receive_info = self.get_wh_info(receipt_db.received_by, partner_id, user_id)
                supply_info = self.get_vendor_info(receipt_db.supplier_id, partner_id, user_id)
            elif receipt_db.branch_type == 'MACHINING_CENTER':
                receive_info = self.get_mc_info(receipt_db.received_by, partner_id, user_id)
                supply_info = self.get_vendor_info(receipt_db.supplier_id, partner_id, user_id)

            doc_detail = {
                            "partnerId": str(partner_id),
                            "receiptType": doc_resource,
                            "code": doc_code,
                            "orderDate": receipt_db.order_date.strftime('%Y-%m-%d %H:%M:%S'), # 关联的收货单订货日期
                            "receiveId": receipt_db.received_by, 
                            "receiveCode": receive_info.get('fields', {}).get('code'),
                            "receiveName": receive_info.get('fields', {}).get('name'),
                            "orderType": receipt_db.order_type,
                            "supplyId": receipt_db.supplier_id,
                            "supplyCode": supply_info.get('fields', {}).get('code'),
                            "supplyName": supply_info.get('fields', {}).get('name'),
                            "reason": receipt_db.purchase_reason,
                            "sumPriceTax": '%.3f' % (receipt_db.sum_price_tax),
                            "sumPrice": '%.3f' % (receipt_db.sum_price),
                            "products": receipt_product_list,
                            "createdAt": receipt_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": receipt_db.created_name,
                            "updatedAt": receipt_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": receipt_db.updated_name,
                            "remark": receipt_db.remark
                        }
        
        # 包装单
        elif 'packing' in doc_resource:
            receipt_db = packing_receipts_db.get_packing_receipts_by_id(receipt_id=doc_id, partner_id=partner_id, is_detail=True)
            product_list = packing_receipts_db.get_packing_receipts_detail(partner_id=partner_id,
                                                                user_id=user_id,
                                                                main_id=doc_id)
            
            material_product_list = []
            for material_product in product_list:
                material_ags = {
                        "materialId": str(material_product.product_id),
                        "materialCode": material_product.product_code,
                        "materialName": material_product.product_name,
                        "materialUnitId": str(material_product.unit_id),
                        "unitName": material_product.unit_name,
                        "unitCode": material_product.unit_spec,
                        "orderQty": '%.3f' % (material_product.quantity) if material_product.quantity else "0.000",
                        "type": material_product.type
                    }
                material_product_list.append(material_ags)
                
            p_args = {	    
                            "productId": str(receipt_db.target_material),
                            "productCode": receipt_db.target_material_code,
                            "productName": receipt_db.target_material_name,
                            "orderQty": '%.3f' % (receipt_db.packed_quantity) if receipt_db.packed_quantity else "0.000",
                            "unitId": str(receipt_db.target_material_unit_id),
                            "unitName": receipt_db.target_material_unit_name,
                            "unitCode": receipt_db.target_material_unit_spec,
                            "materialProducts": material_product_list
                        }
            doc_code = receipt_db.code
            doc_detail = {
                            "partnerId": str(partner_id),
                            "receiptType": doc_resource,
                            "code": receipt_db.code,
                            "orderDate": receipt_db.packing_date.strftime('%Y-%m-%d %H:%M:%S'), 
                            "products": p_args,
                            "remark": receipt_db.remark,
                            "createdAt": receipt_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": receipt_db.created_name,
                            "updatedAt": receipt_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": receipt_db.updated_name,
                            "branchCode": receipt_db.machining_center_code,
                            "branchName": receipt_db.machining_center_name,
                            "branchId": receipt_db.machining_center_id
                        }

        # 加工单
        elif 'processing' in doc_resource:
            receipt_db = processing_receipts_db.get_processing_receipts_by_id(receipt_id=doc_id,
                                                                              partner_id=partner_id,
                                                                              is_detail=True)
            product_list = processing_receipts_db.get_processing_receipts_detail(partner_id=partner_id,
                                                                      user_id=user_id,
                                                                      main_id=doc_id)
            
            material_product_list = []
            for material_product in product_list:
                material_ags = {
                        "materialId": str(material_product.product_id),
                        "materialCode": material_product.product_code,
                        "materialName": material_product.product_name,
                        "materialUnitId": str(material_product.unit_id),
                        "unitName": material_product.unit_name,
                        "unitCode": material_product.unit_spec,
                        "quantity": '%.3f' % (material_product.actual_quantity) if material_product.actual_quantity else "0.000",
                        "materialRate": material_product.material_rate
                        
                    }
                material_product_list.append(material_ags)
                
            p_args = {	    
                            "productId": str(receipt_db.target_material),
                            "productCode": receipt_db.target_material_code,
                            "productName": receipt_db.target_material_name,
                            "quantity": '%.3f' % (receipt_db.actual_output_quantity) if receipt_db.actual_output_quantity else "0.000",
                            "unitId": str(receipt_db.target_material_unit_id),
                            "unitName": receipt_db.target_material_unit_name,
                            "unitCode": receipt_db.target_material_unit_spec,
                            "theoryOutputRate": receipt_db.theory_output_rate,
                            "actualOutputRate": receipt_db.actual_output_rate,
                            "materialProducts": material_product_list
                        }
            doc_code = receipt_db.code
            doc_detail = {
                            "partnerId": str(partner_id),
                            "receiptType": doc_resource,
                            "code": receipt_db.code,
                            "orderDate": receipt_db.processing_date.strftime('%Y-%m-%d %H:%M:%S'), 
                            "products": p_args,
                            "remark": receipt_db.remark,
                            "createdAt": receipt_db.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "createName": receipt_db.created_name,
                            "updatedAt": receipt_db.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                            "updateName": receipt_db.updated_name,
                            "branchCode": receipt_db.machining_center_code,
                            "branchName": receipt_db.machining_center_name,
                            "branchId": receipt_db.machining_center_id
                        }

        return doc_detail
          
    ### -- 三方消息对接 end -- ###



common_tp_service = CommonTpService()