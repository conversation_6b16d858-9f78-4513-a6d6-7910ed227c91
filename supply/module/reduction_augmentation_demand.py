import logging
import pickle
from datetime import datetime, timedelta, date, time as d_t
from decimal import Decimal
from math import ceil, log2
from typing import Callable

from google.protobuf.json_format import MessageToDict
from sqlalchemy import func
from sqlalchemy.orm import make_transient

from supply.client.metadata_service import metadata_service
from supply.driver.Redis import redis_cli, redis_cli_encode
from supply.error.exception import DataValidationException, NoResultFoundError
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProduct, SupplyFranchiseeDemand, \
    SupplyFranchiseeDemandLog, SupplyFranchiseeDemandProductLog, SupplyFranchiseeDemandProductRelation
from supply.model.reduction_augmentation_demand import ReductionAugmentationDemand, \
    ReductionAugmentationDemandProduct, ReductionAugmentationDemandStore, ReductionAugmentationDemandCalculateResult, \
    ReductionAugmentationDemandExcludedStore, ReductionAugmentationDemandCalculateProductAgg, \
    ReductionAugmentationDemandCompany
from supply.model.franchisee.franchisee_demand import DemandAction as Action
from supply.driver.mysql import session
from supply.utils.snowflake import get_id, get_ids
from supply.utils.enums import Platform
import pandas as pd
import numpy as np


class ReductionAugmentationDemandModule:

    def __init__(self, request=None, model=None, response=None, partner_id=None, user_id=None):
        self.request = request
        self.request_dict = MessageToDict(request, preserving_proto_field_name=True) if request else {}
        self.response = response
        self.model = model
        self.turn_fields = {}
        self.partner_id = partner_id
        self.user_id = user_id
        self.identity = {'partner_id': partner_id, 'user_id': user_id}
        self.params = None
        self.limit, self.offset = None, None
        # self.response_fields_dict = {i.name: PROTOBUF_TYPE[i.type] for i in self.response.DESCRIPTOR.fields} \
        #     if response else {}

    def list(self, params=None, fields=None, order_by=None, limit=None, offset=None, need_count=True,
             count_field=None, dynamic=False, dynamic_with_limit=False) -> (list, int):
        self.get_limit_offset(limit, offset)
        self.get_params() if not params else ...
        query_params = params or self.params
        query = session.query(*(fields or [self.model]))
        query = query.filter(*query_params) if query_params else query
        if not dynamic and not dynamic_with_limit:
            query = query.order_by(*(order_by or [self.model.id])).limit(self.limit).offset(self.offset)

        count = None
        if need_count:
            count = session.query(func.count(count_field if count_field else self.model.id).label('c'))
            count = count.filter(*query_params) if query_params else count
            count = count.first() if not dynamic else count

        if dynamic:
            return query, count

        return query.all(), count.c if count is not None else None

    def retrieve(self, params=None, fields=None, filter_by=None, for_update=False):
        self.get_params() if not params else ...
        query_params = params or self.params
        query = session.query(*(fields or [self.model]))
        query = query.filter(*query_params) if query_params else query
        query = query.filter_by(**filter_by) if filter_by else query
        query = query.with_for_update() if for_update else query
        return query.first()

    def create(self, data=None):
        pass

    def create_reduction_augmentation_demand(self):
        return self.save_reduction_augmentation_demand(False)

    def save_reduction_augmentation_demand(self, update=True):
        if not update:
            if not self.request.code:
                raise DataValidationException('required code!')
            code = self.request.code
            instance = None
        else:
            if not self.request.id:
                raise DataValidationException('required id!')

            instance = self.retrieve([self.model.id == self.request.id], for_update=True)
            if not instance:
                raise DataValidationException('Not Found!')
            if instance.status != 'INITED':
                raise DataValidationException('只能修改新建状态的规则')
            code = instance.code

        with redis_cli.lock(f'rad_{code}', timeout=120, blocking_timeout=0.1):
            if self.request.allocation_type not in ['INCREASE', 'REDUCE']:
                raise DataValidationException('allocation_type error!')

            if self.request.order_by_type not in ReductionAugmentationDemand.OrderByType.__members__:
                raise DataValidationException('order_by_type error')

            if not update and self.retrieve([self.model.code == code], [self.model.id]):
                raise DataValidationException('该编号已存在!')

            products = self.request_dict.pop('products', [])
            stores = self.request_dict.pop('store_ids', [])
            products_code, stores_code = {}, {}

            # self.request_dict['start_demand_date'] = self.str_to_datetime(self.request_dict['start_demand_date'])
            # self.request_dict['end_demand_date'] = self.str_to_datetime(self.request_dict['end_demand_date'])
            if self.request.order_by_type != ReductionAugmentationDemand.OrderByType.QUANTITY.value:
                self.request_dict['start_demand_date'] = self.str_to_datetime(self.request_dict['start_demand_date'])
                self.request_dict['end_demand_date'] = self.str_to_datetime(self.request_dict['end_demand_date'])
                if (self.request_dict['end_demand_date'] - self.request_dict['start_demand_date']).days > 7:
                    raise DataValidationException('订货单时间范围不能超过7天!')

            self.request_dict['status'] = instance.status if instance else 'INITED'
            self.request_dict['order_types'] = [int(i) for i in self.request_dict['order_types']]
            self.request_dict['company'] = companies = [int(i) for i in self.request_dict['company']]

            if products:
                products_code = metadata_service.get_product_list(ids=[int(i['product_id']) for i in products],
                                                                  return_fields='id,code',
                                                                  **self.identity).get('rows', [])
                products_code = {i['id']: i['code'] for i in products_code}
                if len(products_code) != len(products):
                    raise DataValidationException('商品错误')

            if stores:
                stores_code = metadata_service.get_store_list(ids=[int(i) for i in stores],
                                                              return_fields='id,code',
                                                              **self.identity).get('rows', [])
                stores_code = {i['id']: i['code'] for i in stores_code}
                if len(stores_code) != len(stores):
                    raise DataValidationException('门店错误')

            rad: ReductionAugmentationDemand = self.set_data(instance=instance, ignore_null=False)
            rad.id, rad.partner_id = rad.id or get_id(), self.partner_id

            products = [ReductionAugmentationDemandProduct(**i, product_code=products_code[i['product_id']],
                                                           rule_id=rad.id, id=get_id()) for i in products]
            stores = [ReductionAugmentationDemandStore(store_id=i, store_code=stores_code[i], rule_id=rad.id,
                                                       id=get_id()) for i in stores]
            companies = [ReductionAugmentationDemandCompany(id=get_id(), rule_id=rad.id, company_id=c)
                         for c in companies]

            new_cal_id = self.request.calculated_rule_id
            redis_key = f'rad_cal_{new_cal_id}'
            old_key = f'rad_cal_{rad.id}'
            rank = redis_cli.get(f'{redis_key}_rank') if new_cal_id else None

            with redis_cli.pipeline() as pipe:
                if rank:
                    cal_details = redis_cli_encode.zrange(f'{redis_key}_details', 0, -1)
                    cal_products = redis_cli_encode.get(f'{redis_key}_products')
                    cal_products = pickle.loads(cal_products) if cal_products else []
                    cal_product_ids = set()
                    for i in cal_products:
                        i.rule_id = rad.id
                        cal_product_ids.add(i.product_id)

                    now_str = datetime.now().__str__()
                    for i, cs in enumerate(cal_details):
                        cs = pickle.loads(cs).to_dict()
                        cs['created_at'] = now_str
                        cs['updated_at'] = now_str
                        cs['rule_id'] = rad.id
                        cal_details[i] = cs

                    pipe.delete(f'{redis_key}_details')
                    pipe.delete(f'{redis_key}_products')

                    if new_cal_id != rad.id:
                        rad.calculation.delete() if update else ...
                        pipe.delete(f'{old_key}_details')
                        pipe.delete(f'{old_key}_products')
                        pipe.delete(f'{old_key}_rank')

                    rag = ReductionAugmentationDemandCalculateProductAgg
                    rad.calculation_agg.filter(rag.product_id.in_(cal_product_ids)).delete(synchronize_session=False) \
                        if update and cal_products else ...
                    session.bulk_save_objects(cal_products)
                    session.bulk_insert_mappings(ReductionAugmentationDemandCalculateResult, cal_details)

                # m = ReductionAugmentationDemand
                # session.query(m).filter()
                # session.query()

                session.add(rad)
                rad.product.delete() if update and products else ...
                rad.store.delete() if update and stores else ...
                rad.companies.delete() if update and companies else ...
                session.bulk_save_objects(companies)
                session.bulk_save_objects(products)
                session.bulk_save_objects(stores)
                session.commit()
                pipe.execute()
                return self.response(**{'result' if update else 'id': True if update else rad.id})

    def change_reduction_augmentation_demand_status(self):
        instance = self.retrieve([self.model.id == self.request.id], for_update=True)
        if not instance:
            raise DataValidationException('Not Found!')
        if instance.status != 'INITED':
            raise DataValidationException('只能修改新建状态的规则')

        if self.request.status == 'CONFIRMED':
            if not instance.calculation.first():
                raise DataValidationException('请先进行计算再执行规则！')
            rcr = ReductionAugmentationDemandCalculateResult
            fdp, fd, fpr = SupplyFranchiseeDemandProduct, SupplyFranchiseeDemand, SupplyFranchiseeDemandProductRelation
            fdgLog = SupplyFranchiseeDemandLog
            fdpLog = SupplyFranchiseeDemandProductLog

            quantity = f'''(if({fdp.is_confirm_qty.expression},{fdp.confirm_quantity.expression},
                            {fdp.approve_quantity.expression}) 
                            {'-' if instance.allocation_type == 'REDUCE' else '+'} 
                            {rcr.allocation_quantity.expression}) '''
            reduce_filter = ''
            if instance.allocation_type == 'REDUCE':
                reduce_filter = f'''and (( {fdp.is_confirm_qty.expression} = 0 
                and {fdp.approve_quantity.expression} >= {rcr.allocation_quantity.expression}) 
                or ( {fdp.is_confirm_qty.expression} = 1 
                and {fdp.confirm_quantity.expression} >= {rcr.allocation_quantity.expression})) '''

            username = metadata_service.get_username_by_pid_uid(self.partner_id, self.user_id)
            demand_ids = session.query(rcr.order_id).filter(rcr.rule_id == instance.id).all()
            # 先查询出要更新的列
            # query_set = session.execute(
            #     f'''SELECT {fdp.id.expression}
            #         FROM  {fdp.__tablename__}
            #         JOIN {rcr.__tablename__} ON {rcr.order_product_id.expression} = {fdp.id.expression}
            #         JOIN {fd.__tablename__} ON {fd.id.expression} = {rcr.order_id.expression}
            #         WHERE {rcr.rule_id.expression} = {instance.id}
            #         AND {fd.status.expression} = 'R_APPROVE' {reduce_filter}''').fetchall()
            # pro_ids = []
            # for q in query_set:
            #     pro_ids.append(str(q[0]))
            # pro_ids = ",".join(pro_ids)

            # 然后再分2步更新商品数量和金额
            # session.execute(f'''UPDATE {fdp.__tablename__} JOIN {rcr.__tablename__}
            #                     ON {rcr.order_product_id.expression} = {fdp.id.expression}
            #                     SET {fdp.confirm_quantity.expression} = IF({fdp.is_confirm_qty.expression},{fdp.confirm_quantity.expression},{fdp.approve_quantity.expression})
            #                     {'-' if instance.allocation_type == 'REDUCE' else '+'} {rcr.allocation_quantity.expression},
            #                     {fdp.updated_by.expression} = {self.user_id},
            #                     {fdp.updated_name.expression} = '{username}',
            #                     {fdp.is_confirm_qty.expression} = 1,
            #                     {rcr.has_effect.expression} = 1
            #                     WHERE {fdp.id.expression} IN ({pro_ids});''')
            # session.execute(f'''UPDATE {fdp.__tablename__}
            #                     SET {fdp.confirm_amount.expression} = round(
            #                                 {fdp.confirm_quantity.expression} * {fdp.tax_price.expression}, 2),
            #                     {fdp.confirm_sales_amount.expression} = round(
            #                                 {fdp.confirm_quantity.expression} * {fdp.sales_price.expression}, 2),
            #                     {fdp.updated_at.expression} = '{datetime.now()}'
            #                     WHERE {fdp.id.expression} IN ({pro_ids});''')

            session.execute(f'''update {fdp.__tablename__} join {rcr.__tablename__} 
                            on {rcr.order_product_id.expression} = {fdp.id.expression}
                            join {fd.__tablename__} on {fd.id.expression} = {rcr.order_id.expression}
                            set {fdp.confirm_quantity.expression} 
                            = if({fdp.is_confirm_qty.expression},{fdp.confirm_quantity.expression},{fdp.approve_quantity.expression}) 
                            {'-' if instance.allocation_type == 'REDUCE' else '+'} 
                            {rcr.allocation_quantity.expression},
                            {rcr.has_effect.expression} = 1,
                            {fdp.confirm_amount.expression} = round({quantity} * {fdp.tax_price.expression}, 2),
                            {fdp.confirm_sales_amount.expression} = round({quantity} * {fdp.sales_price.expression}, 2),
                            {fdp.updated_by.expression} = {self.user_id},
                            {fdp.updated_name.expression} = '{username}',
                            {fdp.updated_at.expression} = '{datetime.now()}',
                            {fdp.is_confirm_qty.expression} = 1
                            where {rcr.rule_id.expression} = {instance.id}
                            and {fd.status.expression} = 'R_APPROVE' {reduce_filter}''')

            # 更新捆绑商品数量和金额
            if demand_ids:
                ur_stmt = f'''update {fpr.__tablename__} 
                                        join {fdp.__tablename__} on {fdp.id.expression} = {fpr.demand_product_id.expression}
                                        set {fpr.confirm_quantity.expression} = 
                                            (case when {fpr.configure.expression} = 1 then ceil({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100) 
                                            when {fpr.configure.expression} = 2 then floor({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100)
                                            else round({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100, 3) end), 
                                        {fpr.confirm_amount.expression} = round(
                                            (case when {fpr.configure.expression} = 1 then ceil({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100) 
                                             when {fpr.configure.expression} = 2 then floor({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100)
                                             else round({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100, 3) end) * {fpr.tax_price.expression}, 2),                   
                                        {fpr.confirm_sales_amount.expression} = round(
                                             (case when {fpr.configure.expression} = 1 then ceil({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100) 
                                             when {fpr.configure.expression} = 2 then floor({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100)
                                             else round({fdp.confirm_quantity.expression} * {fpr.ratio.expression} / 100, 3) end) * {fpr.sales_price.expression}, 2),
                                        {fpr.is_confirm_qty.expression} = 1
                                where {fpr.demand_id.expression} in ({','.join(str(i.order_id) for i in demand_ids)})
                                 '''
                session.execute(ur_stmt)

            # 记录订单日志
            trace_id = get_id()    # 标记日志属于同一个插入动作
            session.execute(f'''INSERT INTO {fdgLog.__tablename__} (partner_id, demand_id, action, action_name, 
                                                            platform, created_by, created_name, created_at, trace_id)
                                       (SELECT {self.partner_id},
                                               order_id,
                                              '{Action.REDUCE.code}',
                                              '{Action.REDUCE.desc}',
                                              '{Platform.HEX_WEB.code}',
                                               {self.user_id},
                                              '{username}',
                                              '{datetime.now()}',
                                               {trace_id}
                                          FROM {rcr.__tablename__}
                                          WHERE rule_id = {instance.id}
                                          AND has_effect = 1 GROUP BY order_id);''')

            session.execute(f'''INSERT INTO {fdpLog.__tablename__} (partner_id, trace_id, demand_id, action,
                                                                      action_name, product_id, product_code,
                                                                      product_name, unit_id, unit_name, tax_price,
                                                                      sales_price, confirm_quantity, confirm_amount,
                                                                      confirm_sales_amount, created_by, created_name,
                                                                      created_at)
                                    (SELECT fdp.partner_id,
                                            {trace_id},
                                            fdp.demand_id,
                                            '{Action.REDUCE.code}',
                                            '{Action.REDUCE.desc}',
                                            fdp.product_id,
                                            fdp.product_code,
                                            fdp.product_name,
                                            fdp.unit_id,
                                            fdp.unit_name,
                                            fdp.tax_price,
                                            fdp.sales_price,
                                            fdp.confirm_quantity,
                                            fdp.confirm_amount,
                                            fdp.confirm_sales_amount,
                                            fdp.updated_by,
                                            fdp.updated_name,
                                            '{datetime.now()}'
                                     FROM {fdp.__tablename__} AS fdp
                                     JOIN {rcr.__tablename__} ON {rcr.order_product_id.expression} = fdp.id
                                     WHERE {rcr.rule_id.expression} = {instance.id} 
                                     AND {rcr.has_effect.expression} = 1);''')
            # session.query(fdp).join(rcr, rcr.order_id == fdp.demand_id).filter(rcr.rule_id == instance.id).\
            #     update({fdp.confirm_quantity: fdp.confirm_quantity - rcr.allocation_quantity},
            #            synchronize_session=False)

        instance.status = self.request.status
        session.commit()

        return self.response(result=True)

    def update_reduction_augmentation_demand(self):
        if self.request.status:
            return self.change_reduction_augmentation_demand_status()

        return self.save_reduction_augmentation_demand()

    def retrieve_reduction_augmentation_demand(self):
        if not self.request.id:
            raise DataValidationException('required id!')
        first = self.retrieve(fields=self.get_query_fields(self.response))
        self.turn_fields = {'start_demand_date': self.datetime_to_str, 'end_demand_date': self.datetime_to_str,
                            'updated_at': self.datetime_to_str}
        return self.response(**(self.map_sa_result(first) if first else {}))

    def list_reduction_augmentation_demand(self):
        self.get_limit_offset()

        company = self.request_dict.pop('company', None)

        self.get_params()
        self.params.append(self.model.partner_id == self.partner_id)
        query, count = self.list(order_by=[self.model.updated_at.desc()],
                                  fields=self.get_query_fields(self.response.Detail),
                                  dynamic=True, dynamic_with_limit=True)
        order_by = self.get_order_by()
        if not company:
            result = query.order_by(*order_by).limit(self.limit).offset(self.offset).all()
            count = count.first().c
        else:
            rad_company = ReductionAugmentationDemandCompany
            query = query.join(rad_company).filter(rad_company.company_id == company).\
                order_by(*order_by).limit(self.limit).offset(self.offset)
            count = query.with_entities(func.count(self.model.id).label('c')).first().c
            result = query.all()

        self.turn_fields = {'start_demand_date': self.datetime_to_str, 'end_demand_date': self.datetime_to_str,
                            'updated_at': self.datetime_to_str}

        return self.response(**{
            'rows': [self.map_sa_result(i) for i in result],
            'count': count
        })

    def list_reduction_augmentation_demand_product(self):
        if not self.request.rule_id:
            raise DataValidationException('required rule_id!')

        result, count = self.list(order_by=[self.model.updated_at.desc()],
                                  fields=self.get_query_fields(self.response.AllocationProduct, ['product_name']))

        if result:
            products_meta = metadata_service.get_product_list(ids={i.product_id for i in result},
                                                              return_fields='id,name',
                                                              **self.identity).get('rows', [])
            products_name = {int(i['id']): i['name'] for i in products_meta}

            for i, v in enumerate(result):
                result[i] = self.map_sa_result(v)
                result[i]['product_name'] = products_name.get(v.product_id)

        return self.response(**{
            'rows': result,
            'count': count
        })

    def list_reduction_augmentation_demand_store(self):
        if not self.request.rule_id:
            raise DataValidationException('required rule_id!')

        result, count = self.list(order_by=[self.model.updated_at.desc()],
                                  fields=self.get_query_fields(self.response.AllocationStore, ['store_name']))

        if result:
            stores_meta = metadata_service.get_store_list(ids={i.store_id for i in result}, return_fields='id,name',
                                                          **self.identity).get('rows', [])
            stores_name = {int(i['id']): i['name'] for i in stores_meta}

            for i, v in enumerate(result):
                result[i] = self.map_sa_result(v)
                result[i]['store_name'] = stores_name.get(v.store_id)

        return self.response(**{
            'rows': result,
            'count': count
        })

    def sum_demand_quantity(self):
        product_ids_len = len(self.request.product_ids)
        store_ids_len = len(self.request.store_ids)
        if 1 not in {product_ids_len, store_ids_len} or 0 in {product_ids_len, store_ids_len}:
            raise DataValidationException('params error!')

        self.request_dict['start_demand_date'] = self.str_to_datetime(self.request_dict['start_demand_date'])
        self.request_dict['end_demand_date'] = self.str_to_datetime(self.request_dict['end_demand_date'])
        if (self.request_dict['end_demand_date'] - self.request_dict['start_demand_date']).days > 7:
            raise DataValidationException('订货单时间范围不能超过7天!')

        product_ids = self.request_dict.pop('product_ids')
        self.request_dict['received_by'] = self.request_dict.pop('store_ids')
        self.request_dict['order_type_id'] = self.request_dict.pop('order_types', [])

        sdp = SupplyFranchiseeDemandProduct
        self.get_limit_offset()
        self.get_params()
        self.model: SupplyFranchiseeDemand
        self.params.append(self.model.partner_id == self.partner_id)
        self.params.append(sdp.product_id.in_(product_ids))
        self.params.append(self.model.status == 'R_APPROVE')
        query = session.query(func.sum(func.IF(sdp.is_confirm_qty, sdp.confirm_quantity, sdp.approve_quantity)).
                              label('quantity'), sdp.product_id).\
            join(self.model, self.model.id == sdp.demand_id).filter(*self.params).\
            group_by(self.model.received_by, sdp.product_id)

        result = query.all()

        if result:
            products_meta = metadata_service.get_product_list(ids={i.product_id for i in result},
                                                              return_fields='id,name', **self.identity).get('rows', [])
            products_name = {int(i['id']): i['name'] for i in products_meta}

            for i, v in enumerate(result):
                result[i] = self.map_sa_result(v)
                result[i]['product_name'] = products_name.get(v.product_id)

        return self.response(**{
            'rows': result,
            # 'count': query.count() if self.limit else None
        })

    def calculate_allocation(self, new_method=True):
        if not self.request.products or not self.request.store_ids:
            raise DataValidationException('required products and stores!')

        if self.request.allocation_type not in ['INCREASE', 'REDUCE']:
            raise DataValidationException('allocation_type error!')

        if not self.request.order_types:
            raise DataValidationException('order_types error!')

        if self.request.order_by_type not in ReductionAugmentationDemand.OrderByType.__members__:
            raise DataValidationException('order_by_type error')

        start_demand_date = self.str_to_datetime(self.request_dict['start_demand_date'])
        end_demand_date = self.str_to_datetime(self.request_dict['end_demand_date'])
        if (start_demand_date - end_demand_date).days > 7:
            raise DataValidationException('订货单时间范围不能超过7天!')

        order_by_sale_price = True
        if self.request.order_by_type != ReductionAugmentationDemand.OrderByType.QUANTITY.value:
            if self.request.order_by_demand_day > 7:
                raise DataValidationException('不能超过7天!')
            order_by_sale_price = False

        if self.request.quantity_per_time not in {'min_quantity', 'increment_quantity'}:
            raise DataValidationException('quantity_per_time error!')

        rule_id, rule, new_rule_id = self.request.rule_id, None, None
        if rule_id:
            pass
            # rule = redis_cli.get(f'rad_cal_{rule_id}_rank') or self.retrieve([ReductionAugmentationDemand.id == rule_id])
            # if not rule:
            #     raise NoResultFoundError('规则不存在')

            # p_agg = ReductionAugmentationDemandCalculateProductAgg
            # calculated_products = rule.calculation_agg.with_entities(p_agg.id).\
            #     filter(p_agg.product_id.notin_([int(i['product_id']) for i in self.request_dict['products']])).first()
            # if calculated_products:
            #     raise DataValidationException('只能选择已经计算过的商品')
            #
            # calculated_stores = rule.calculation.with_entities(self.model.id).\
            #     filter(self.model.store_id.in_([int(i) for i in self.request_dict['store_ids']])).first()
            # if calculated_stores:
            #     raise DataValidationException('不能选择已计算过的门店')
        else:
            new_rule_id = get_id()

        r_id = rule_id or new_rule_id

        with redis_cli.lock(f'rad_cal_lock_{r_id}', timeout=360, blocking_timeout=0.1):
            exs = ReductionAugmentationDemandExcludedStore
            excluded_stores, _ = self.list([exs.partner_id == self.partner_id,
                                            exs.allocation_type == self.request.allocation_type],
                                           [exs.store_id], need_count=False, order_by=[exs.id])
            excluded_stores = {i.store_id for i in excluded_stores}
            stores = {i for i in self.request_dict['store_ids'] if int(i) not in excluded_stores}
            products = {int(i['product_id']): i for i in self.request_dict['products']
                        if all(i.values()) and 0 < i['rate'] <= 100}
            if len(products) != len(self.request_dict['products']):
                raise DataValidationException('products error!')

            base_rate = self.request.rate
            if 0 >= base_rate or base_rate > 100:
                raise DataValidationException('rate must be greater than 0 or less than or equal to 100!')

            today = datetime.combine(date.today(), d_t()) - timedelta(days=1)
            n_days = today - timedelta(days=self.request.order_by_demand_day)
            sd, sdp = SupplyFranchiseeDemand, SupplyFranchiseeDemandProduct

            order_bys = [sdp.product_id]
            if order_by_sale_price:
                order_by = session.query(sd.received_by.label('store_id')).\
                    filter(sd.demand_date.between(n_days, today),
                           sd.received_by.in_(stores)).order_by(sd.sum_price_tax.desc(), sd.id.desc()).all()
                order_bys.append(func.field(sd.received_by, *[i.store_id for i in order_by]).desc()) if order_by else ...

            demands = session.query(func.IF(sdp.is_confirm_qty, sdp.confirm_quantity, sdp.approve_quantity).
                                    label('demand_quantity'),
                                    getattr(sdp, self.request.quantity_per_time).label('step'),
                                    sd.code.label('order_code'), sdp.id.label('order_product_id'),
                                    sd.received_by.label('store_id'), sd.received_code.label('store_code'),
                                    sdp.product_code, sdp.product_id, sd.id.label('order_id'),
                                    sd.bus_type.label('order_source'), sd.order_type_id.label('order_type')). \
                join(sd, sdp.demand_id == sd.id).\
                filter(sd.partner_id == self.partner_id,
                       sdp.product_id.in_(products.keys()),
                       sd.received_by.in_(int(i) for i in stores),
                       sd.status == 'R_APPROVE',
                       getattr(sdp, self.request.quantity_per_time) > 0,
                       sd.order_type_id.in_(int(i) for i in self.request_dict['order_types']),
                       sdp.quantity > 0,
                       sd.demand_date.between(start_demand_date, end_demand_date)).\
                group_by(sd.id, sdp.product_id).order_by(*order_bys,
                                                         sd.created_at.desc(), sdp.id.desc()).all()
            print('dem', len(demands))
            # print(self.map_sa_result(demands[0]))
            if not demands:
                raise DataValidationException('该条件无满足条件的订单')

            self.is_reduce = self.request.allocation_type == 'REDUCE'
            # self.allow_zero = self.request.allow_reduce_to_zero and base_rate == 100 and self.is_reduce
            df = pd.DataFrame([self.map_sa_result(i) for i in demands])
            result_df = None
            # now_str = datetime.now().__str__()
            remain_products = []

            self.model: ReductionAugmentationDemandCalculateResult
            for k, p in products.items():
                if not new_method:
                    # 暴力轮询
                    print('startt', k, p)
                    p['allocation_quantity'] = Decimal(p['allocation_quantity'])
                    self.quantity_sum, self.allocation_quantity = 0,  p['allocation_quantity']
                    self.times = 0
                    df_p = df.loc[df['product_id'] == k].copy()
                    p_rate = Decimal(p['rate'] or base_rate) / 100
                    df_p['rate'] = p_rate
                    self.allow_zero = self.request.allow_reduce_to_zero and p_rate == 1 and self.is_reduce
                    df_p['max_quantity'] = df_p['demand_quantity'] * p_rate
                    df_p['max_times'] = df_p['max_quantity'] // df_p['step']
                    df_p['remain_quantity'] = df_p['max_quantity'] % df_p['step']

                    df_p[['allocation_quantity', 'allocation_times', 'last_times']] = Decimal(0), 0, 0
                    print('/////////////////')

                    df_c = df_p
                    while p['allocation_quantity'] - self.quantity_sum > 0:
                        self.times += 1

                        df_p.loc[df_p['max_times'] >= self.times - 1, ['allocation_quantity', 'allocation_times', 'last_times']] = df_c.apply(self.sum_func, result_type='reduce', axis=1)
                        df_c = df_p.loc[df_p['max_times'] >= self.times]
                        if df_c.empty:
                            remain_products.append({
                                'product_id': k,
                                'remain_quantity': p['allocation_quantity'] - self.quantity_sum
                            })
                            break
                        print('******************')

                        if df_c.apply(lambda x: x['last_times'] == x['allocation_times'], axis=1).all():
                            remain_products.append({
                                'product_id': k,
                                'remain_quantity': p['allocation_quantity'] - self.quantity_sum
                            })
                            break
                elif not new_method:
                    # 二分法
                    p['allocation_quantity'] = Decimal(p['allocation_quantity'])
                    df_p = df.loc[df['product_id'] == k].copy()
                    p_rate = Decimal(p['rate'] or base_rate) / 100
                    df_p['rate'] = p_rate
                    self.allow_zero = self.request.allow_reduce_to_zero and p_rate == 1 and self.is_reduce
                    df_p['max_quantity'] = df_p['demand_quantity'] * p_rate
                    df_p['max_times'] = df_p['max_quantity'] / df_p['step'] if self.allow_zero \
                        else df_p['max_quantity'] // df_p['step']
                    df_p['remain_quantity'] = df_p['max_quantity'] % df_p['step']

                    if not (df_p['max_times'] > 0).any():
                        products[k]['final_quantity'] = 0
                        products[k]['store_count'] = 0
                        continue

                    max_quantity_sum = df_p['max_quantity'].sum() if self.allow_zero else (df_p['max_quantity'] - df_p[
                        'remain_quantity']).sum()

                    if max_quantity_sum <= p['allocation_quantity']:
                        print('kkkk')
                        df_p['allocation_quantity'] = df_p['step'] * df_p['max_times']
                        df_p['allocation_times'] = df_p['max_times']

                        self.quantity_sum = max_quantity_sum
                    else:
                        min_times = p['allocation_quantity'] // df_p['step'].sum()
                        max_times = ceil(p['allocation_quantity'] / df_p['step'].min())

                        range_times = ceil(log2(max_times - min_times or 2)) + 1
                        print('rrrr', range_times, self.allow_zero)
                        df_p['diff'] = 0
                        df_p['float_number'] = df_p.apply(self.modf, axis=1) if self.allow_zero else 0

                        for i in range(range_times):
                            print('iiii', i)
                            mid = ceil((min_times + max_times) / 2)
                            print('mid', mid, max_times, min_times)

                            self.quantity_sum = df_p.apply(
                                lambda x: x['step'] * (mid if mid <= x['max_times'] else x['max_times']),
                                axis=1).sum()
                            print('ssss', self.quantity_sum, p['allocation_quantity'])

                            if self.quantity_sum > p['allocation_quantity']:
                                print('++++')
                                # df_p['mid'] = df_p.apply(
                                #     lambda x: (x['step'] * (1 + x['float_number'])) if mid <= x['max_times'] else 0,
                                #     axis=1)
                                temp_sum = self.quantity_sum

                                df_p['diff'] = 0
                                for di, dv in df_p[::-1].iterrows():
                                    diff = (dv['step'] * (1 + dv['float_number'])) if mid <= dv['max_times'] else 0
                                    if diff:
                                        temp_sum -= diff
                                        df_p.loc[di, 'diff'] = -1

                                    if temp_sum <= p['allocation_quantity']:
                                        print('====')
                                        df_p['allocation_quantity'] = df_p.apply(
                                            lambda x: x['step'] * ((mid + x['diff']) if mid <= x['max_times']
                                                                   else x['max_times']),
                                            axis=1)
                                        df_p['allocation_times'] = df_p.apply(
                                            lambda x: (mid + x['diff']) if mid <= x['max_times'] else x['max_times'],
                                            axis=1)
                                        self.quantity_sum = temp_sum
                                        print('temp_sum', temp_sum, mid, dv['step'])
                                        print(df_p[df_p['allocation_quantity'] > 0])
                                        break

                                max_times = mid
                            else:
                                print('------')
                                # remain_quantity = df_p.apply(
                                #     lambda x: x['remain_quantity'] if mid > x['max_times'] else 0,
                                #     axis=1).sum()
                                # if p['allocation_quantity'] - self.quantity_sum <= remain_quantity:
                                #     df_p['allocation_quantity'] = df_p.apply(
                                #         lambda x: x['step'] * (mid if mid <= x['max_times'] else x['max_times']),
                                #         axis=1)
                                #     df_p['allocation_times'] = df_p.apply(
                                #         lambda x: mid if mid <= x['max_times'] else x['max_times'], axis=1)
                                #     break

                                min_times = mid

                    del df_p['diff'], df_p['float_number']

                    remain_products.append({
                        'product_id': k,
                        'remain_quantity': p['allocation_quantity'] - self.quantity_sum
                    }) if self.quantity_sum != p['allocation_quantity'] else ...

                else:
                    # 以门店为最小维度进行增减
                    p['allocation_quantity'] = round(Decimal(p['allocation_quantity']), 8)
                    print('bbbbb', p)
                    df_p = df.loc[df['product_id'] == k].copy()
                    if df_p.empty:
                        products[k]['final_quantity'] = 0
                        products[k]['store_count'] = 0
                        continue

                    p_rate = Decimal(p['rate'] or base_rate) / 100
                    df_p['rate'] = p_rate
                    self.allow_zero = self.request.allow_reduce_to_zero and p_rate == 1 and self.is_reduce
                    df_p['max_quantity'] = df_p['demand_quantity'] * p_rate
                    df_p['max_times'] = df_p['max_quantity'] / df_p['step'] if self.allow_zero \
                        else df_p['max_quantity'] // df_p['step']
                    # df_p['ceil_max_times'] = df_p['max_times'].apply(lambda x: ceil(x)) if self.allow_zero \
                    #     else df_p['max_times']
                    df_p['remain_quantity'] = df_p['max_quantity'] % df_p['step']
                    df_p['origin_max_times'] = df_p['demand_quantity'] / df_p['step']  \
                        if self.allow_zero else df_p['demand_quantity'] // df_p['step']
                    # df_p['origin_remain_quantity'] = df_p['demand_quantity'] % df_p['step']
                    df_p[['allocation_quantity', 'allocation_times']] = 0, 0
                    # print([i.order_product_id for i in demands])

                    if df_p['step'].min() > p['allocation_quantity']:
                        products[k]['final_quantity'] = 0
                        products[k]['store_count'] = 0
                        continue

                    df_agg = df_p.groupby('store_id', sort=False).agg({'step': 'first', 'demand_quantity': 'sum',
                                                                       'max_times': ['sum', dict, list],
                                                                       # 'ceil_max_times': list,
                                                                       'max_quantity': 'sum',
                                                                       'origin_max_times': dict,
                                                                       'remain_quantity': 'sum'}
                                                                      )
                    # df_agg.columns = [f'{i[0]}_{i[1]}' for i in df_agg.columns]
                    if not (df_agg['max_times']['sum'] > 0).any():
                        products[k]['final_quantity'] = 0
                        products[k]['store_count'] = 0
                        continue

                    if not order_by_sale_price:
                        df_agg = df_agg.sort_values([('demand_quantity', 'sum')], ascending=False)

                    max_quantity_sum = df_agg['max_quantity']['sum'].sum() if self.allow_zero else \
                        (df_agg['max_quantity']['sum'] - df_agg['remain_quantity']['sum']).sum()

                    if round(max_quantity_sum, 8) <= p['allocation_quantity']:
                        self.df_p = df_p
                        df_agg.apply(self.get_allocate_all_demand_quantity, axis=1)
                        self.quantity_sum = max_quantity_sum
                    else:
                        df_agg['diff'] = 0
                        df_agg['float_number'] = df_agg.apply(self.modf_agg, axis=1) if self.allow_zero else 0

                        min_times = p['allocation_quantity'] // df_agg['step']['first'].sum() - 1
                        max_times = ceil(p['allocation_quantity'] / df_agg['step']['first'].min()) \
                                    + (len(df_agg.loc[df_agg.float_number > 0]) if self.allow_zero else 0)
                        # min_times = min_times if not min_times else min_times - 1
                        range_times = ceil(log2(max_times - min_times) or 1)

                        # print('float_number', df_agg['float_number'].sum())
                        # print(df_agg)
                        could_break = False
                        for i in range(range_times):
                            print('iiii', i)
                            mid = self.mid = ceil((min_times + max_times) / 2)
                            print('mid', mid, max_times, min_times)

                            self.quantity_sum = df_agg.apply(self.get_allocate_quantity, axis=1).sum()
                            # print('ssssssss', self.quantity_sum)

                            if round(self.quantity_sum, 8) > p['allocation_quantity']:
                                max_times = mid
                            elif round(self.quantity_sum, 8) < p['allocation_quantity']:
                                temp_sum = self.quantity_sum

                                df_agg['diff'] = 0
                                for di, dv in df_agg.iterrows():
                                    diff = self.get_real_times_quantity(dv)
                                    # print('diff', diff)

                                    if diff:
                                        temp_sum += diff
                                        df_agg.loc[di, 'diff'] = 1
                                    # print('temp_sum', temp_sum)

                                        if round(temp_sum, 8) >= p['allocation_quantity']:
                                            if round(temp_sum, 8) > p['allocation_quantity']:
                                                df_agg.loc[di, 'diff'] = 0
                                                self.quantity_sum = temp_sum - diff
                                            else:
                                                self.quantity_sum = temp_sum
                                            self.df_p = df_p
                                            df_agg.apply(self.get_final_allocation_quantity, axis=1)
                                            could_break = True
                                            break

                                if could_break:
                                    break

                                min_times = mid
                            else:
                                self.df_p = df_p
                                df_agg['diff'] = 0
                                df_agg.apply(self.get_final_allocation_quantity, axis=1)
                                self.quantity_sum = p['allocation_quantity']
                                break

                    final_allocate_quantity = df_p['allocation_quantity'].sum()

                    remain_products.append({
                        'product_id': k,
                        'remain_quantity': p['allocation_quantity'] - final_allocate_quantity
                    }) if final_allocate_quantity != p['allocation_quantity'] else ...

                df_p = df_p.loc[df_p['allocation_times'] > 0].copy()
                products[k]['final_quantity'] = df_p['allocation_quantity'].sum()
                products[k]['store_count'] = len(set(df_p['store_id']))

                result_df = pd.concat([result_df, df_p], ignore_index=True) \
                    if isinstance(result_df, pd.DataFrame) else df_p
                if not new_method:
                    del result_df['last_times']

            if result_df is None or result_df.empty:
                raise DataValidationException('该条件无满足条件的订单')
            # print({i['order_id']:[i['allocation_times'], i['max_times']] for i in result_df.to_dict(orient='records') if i['max_times'] > 10})
            result_df['id'] = get_ids(len(result_df))
            if self.is_reduce:
                result_df['final_quantity'] = result_df['demand_quantity'] - result_df['allocation_quantity']
            else:
                result_df['final_quantity'] = result_df['demand_quantity'] + result_df['allocation_quantity']
            print('ppp_id', products)

            product_meta = self.get_metadata(products.keys(), 'product', ['code', 'name'])
            # product_meta = {}

            with redis_cli.pipeline() as pipe:
                redis_key = f'rad_cal_{r_id}'
                ex = 7200
                p_m = ReductionAugmentationDemandCalculateProductAgg
                if new_rule_id or rule_id: # 目前是每次都全部重算，所以是全部替换这里本来的的判断应该是 if new_rule_id:
                    rank = redis_cli.get(f'{redis_key}_rank') or 0
                    products_data = [p_m(product_id=k, product_code=product_meta.get(k, {}).get('code'),
                                         rate=v['rate'], allocation_quantity=v['final_quantity'],
                                         remain_quantity=v['allocation_quantity'] - v['final_quantity'],
                                         store_count=v['store_count'],
                                         rule_id=r_id)
                                     for k, v in products.items()]

                    pipe.set(f'{redis_key}_products', pickle.dumps(products_data), ex=ex)
                else:
                    db_count = self.retrieve([self.model.rule_id == rule_id], [func.count(self.model.id).label('c')]).c
                    rank = int(redis_cli.get(f'{redis_key}_rank')) or db_count
                    if db_count:
                        origin_data, _ = self.list([p_m.rule_id == rule_id, p_m.product_id.in_(products.keys())],
                                                   [p_m], limit=rank, need_count=False)
                        for i in origin_data:
                            p = products[i.product_id]
                            # i.final_quantity = i.final_quantity + p['final_quantity']
                            i.store_count += i.store_count + p['store_count']
                            i.allocation_quantity += i.allocation_quantity + p['final_quantity']
                            make_transient(i)

                        pipe.set(f'{redis_key}_products', pickle.dumps(origin_data), ex=ex)
                        # pipe.set(f'{redis_key}_products', pickle.dumps({products.values()}), ex=ex)

                pipe.delete(f'{redis_key}_details')
                pipe.delete(f'{redis_key}_rank')
                # pipe.set(f'{redis_key}_rank', rank + len(result_df), ex=ex)
                pipe.set(f'{redis_key}_rank', len(result_df), ex=ex)  # 目前是每次都全部重算, 所以存计算的长度
                # pipe.zadd(f'{redis_key}_details', {pickle.dumps(v): i + rank + 1 for i, v in result_df.iterrows()})
                pipe.zadd(f'{redis_key}_details', {pickle.dumps(v): i + 1 for i, v in result_df.iterrows()})
                pipe.expire(f'{redis_key}_details', ex)
                # pipe.expire(f'{redis_key}_products', ex)
                pipe.execute()

            if remain_products:
                return self.response(**{
                    'remain_products': [{'product_code': product_meta.get(i['product_id'], {}).get('code'),
                                         'product_name': product_meta.get(i['product_id'], {}).get('name'), **i}
                                        for i in remain_products],
                    'rule_id': r_id
                })
            else:
                return self.response(**{
                    'allocation_result': [{'product_id': k,
                                           'product_code': product_meta.get(k, {}).get('code'),
                                           'product_name': product_meta.get(k, {}).get('name'),
                                           'store_count': v['store_count'],
                                           'allocation_quantity': v['final_quantity']}
                                          for k, v in products.items()],
                    'rule_id': r_id
                })

    def sum_func(self, x):
        if self.allow_zero and x['max_times'] == self.times + 1:
            if self.quantity_sum + x['remain_quantity'] <= self.allocation_quantity:
                self.quantity_sum += x['remain_quantity']
                return self.series(x['allocation_quantity'] + x['remain_quantity'],
                                   x['allocation_times'] + Decimal('1.5'), x['allocation_times'])

        if x['max_times'] < self.times:
            return self.series(x['allocation_quantity'], x['allocation_times'], x['allocation_times'])

        if self.quantity_sum + x['step'] <= self.allocation_quantity:
            self.quantity_sum += x['step']
            return self.series(x['allocation_quantity'] + x['step'], x['allocation_times'] + 1, x['allocation_times'])
        else:
            return self.series(x['allocation_quantity'], x['allocation_times'], x['allocation_times'])

    def series(self, allocation_quantity, allocation_times, last_times):
        return pd.Series({'allocation_quantity': allocation_quantity, 'allocation_times': allocation_times,
                          'last_times': last_times})

    def modf(self, x):
        return self.modf_n(x['max_times'])

    def modf_agg(self, x):
        return self.modf_n(x['max_times']['sum'])

    def get_allocate_quantity(self, x):
        if self.allow_zero:
            temp_t, temp_ct = 0, 0
            for i, v in enumerate(x['max_times']['list']):
                c_v = ceil(v)
                if temp_t + c_v > self.mid:
                    return x['step']['first'] * (temp_t + self.mid - temp_ct)
                elif temp_t + c_v == self.mid:
                    return x['step']['first'] * (temp_t + v)

                temp_t += v
                temp_ct += c_v

            return x['step']['first'] * x['max_times']['sum']
        # print('iijjj', x['step']['first'] * (self.mid if self.mid <= x['max_times']['sum'] else x['max_times']['sum']))
        return x['step']['first'] * (self.mid if self.mid <= x['max_times']['sum'] else x['max_times']['sum'])

    def get_real_times_quantity(self, x):
        if self.allow_zero:
            temp_t = 0
            for i, v in enumerate(x['max_times']['list']):
                c_v = ceil(v)
                if temp_t + c_v > self.mid + 1:
                    return x['step']['first']
                elif temp_t + c_v == self.mid + 1:
                    return x['step']['first'] * (self.modf_n(v) or 1)

                temp_t += c_v

            return 0
        # print('gfrdnhjo', self.mid, x['max_times']['sum'])
        return (x['step']['first'] * (1 + x['float_number'][0])) if self.mid < x['max_times']['sum'] else 0

    def get_allocate_all_demand_quantity(self, x):
        step, temp_s = x['step']['first'], 0
        ss = sum(i * step for i in x['max_times']['dict'].values())
        # print('ssss', ss, step)

        for k, v in x['max_times']['dict'].items():
            c_v = ceil(v)
            step_sum = step * v
            if temp_s + step_sum > ss:
                gs = ((ss - temp_s) // step) * step
                self.df_p.loc[k, ['allocation_times', 'allocation_quantity']] = c_v, round(gs, 8)
                break
            elif temp_s + step_sum == ss:
                self.df_p.loc[k, ['allocation_times', 'allocation_quantity']] = c_v, round(step_sum, 8)
                break

            self.df_p.loc[k, ['allocation_times', 'allocation_quantity']] = c_v, round(step_sum, 8)
            temp_s += step_sum

    def get_final_allocation_quantity(self, x):
        temp_ct, temp_t, mid = 0, 0, self.mid + x['diff'][0]
        # print('dddd', x['origin_max_times']['dict'], self.mid, x['diff'])
        for k, v in x['origin_max_times']['dict'].items():
            c_v = ceil(v)
            if temp_ct + c_v > mid:
                c_v = v = mid - temp_ct
                # print('>>', c_v, round(v * x['step']['first'], 8))
                self.df_p.loc[k, ['allocation_times', 'allocation_quantity']] = c_v, round(v * x['step']['first'], 8)
                break
            elif temp_ct + c_v == mid:
                c_v = temp_ct + c_v
                v = temp_t + v
                # print('==', c_v, round(v * x['step']['first'], 8))
                self.df_p.loc[k, ['allocation_times', 'allocation_quantity']] = c_v, round(v * x['step']['first'], 8)
                break
            # print('<<', c_v, round(v * x['step']['first'], 8))
            self.df_p.loc[k, ['allocation_times', 'allocation_quantity']] = c_v, round(v * x['step']['first'], 8)
            temp_t += v
            temp_ct += c_v

    def modf_n(self, x):
        n = str(x).split('.')
        return 0 if len(n) == 1 else Decimal(f'0.{n[1]}')

    def list_calculate_result(self):
        rule_id = self.request.rule_id
        if not rule_id:
            raise DataValidationException('required rule_id!')
        redis_key = f'rad_cal_{rule_id}'
        rank = redis_cli.get(f'{redis_key}_rank')
        rank = int(rank) if rank is not None else 0
        rule_db = self.retrieve([ReductionAugmentationDemand.id == rule_id], [ReductionAugmentationDemand])
        if not rank and not rule_db:
            raise self.response()

        self.get_limit_offset()
        limit = self.limit or 0
        offset = self.offset or 0

        cache = redis_cli_encode.zrangebyscore(f'{redis_key}_details', offset, offset + limit)
        meta_fields = ['product_name', 'store_name']
        need_fields = {i.name: getattr(self.model, i.name) for i in self.response.AllocationDetail.DESCRIPTOR.fields
                       if i.name not in meta_fields}
        rows = [pickle.loads(i)[need_fields].to_dict() for i in cache]
        # print('rows', rows)

        db_rows, dbs, count = [], [], 0
        if (not rows or len(rows) != limit) and rule_db:
            dbs = rule_db.calculation.with_entities(*need_fields.values()).limit(self.limit).offset(self.offset).all()
            count = rank or rule_db.calculation.with_entities(func.count(self.model.id).label('c')).first().c

        if not any([rows, dbs]):
            return self.response()

        order_type_ids, product_ids, store_ids = set(), set(), set()
        for i, v in enumerate(dbs):
            # print(self.map_sa_result(v))
            order_type_ids.add(v.order_type)
            product_ids.add(v.product_id)
            store_ids.add(v.store_id)
            dbs[i] = self.map_sa_result(v)

        for i in rows:
            order_type_ids.add(i['order_type'])
            product_ids.add(i['product_id'])
            store_ids.add(i['store_id'])

        order_types = metadata_service.list_entity(schema_name='order_type', ids=order_type_ids,
                                                   return_fields="id,name", **self.identity).get('rows', [])
        order_types = {int(i['id']): i['fields']['name'] for i in order_types}
        product_meta = self.get_metadata(product_ids, 'product', 'name')
        store_meta = self.get_metadata(store_ids, 'store', 'name')
        order_source = {'MANUAL': '手动创建', 'HD_ASSIGN': '总部分配', 'PLAN': '计划创建'}

        rows = dbs + rows
        for i, v in enumerate(rows):
            rows[i]['order_type'] = order_types.get(v['order_type'])
            rows[i]['product_name'] = product_meta.get(v['product_id'])
            rows[i]['store_name'] = store_meta.get(v['store_id'])
            rows[i]['order_source'] = order_source.get(v['order_source'])
        # print(rows)

        return self.response(**{'rows': rows, 'count': rank or count})

    def list_cal_agg(self):
        rule_id = self.request.rule_id
        if not rule_id:
            raise DataValidationException('required rule_id!')
        # redis_key = f'rad_cal_{rule_id}'
        # cache = redis_cli_encode.get(f'{redis_key}_products')
        # cache_result = pickle.loads(cache) if cache is not None else []
        result, _ = self.list(fields=self.get_query_fields(self.response.AllocationResult, ['product_name']),
                              need_count=False)
        # result = [self.map_sa_result(i) if i.product_id in cache_result else cache_result[i.product_id] for i in result]
        product_meta = self.get_metadata({i.product_id for i in result}, 'product', ['code', 'name'])
        for i, v in enumerate(result):
            v = self.map_sa_result(v)
            # if v.product_id not in cache_result:
            #     v = self.map_sa_result(v)
            # else:
            #     v = cache_result[v.product_id]
            v['product_code'] = product_meta.get(v['product_id'], {}).get('code')
            v['product_name'] = product_meta.get(v['product_id'], {}).get('name')
            result[i] = v

        return self.response(**{'rows': result})

    def create_excluded_store(self):
        if not self.request.stores:
            raise DataValidationException('required stores!')

        if self.request.allocation_type not in ['INCREASE', 'REDUCE']:
            raise DataValidationException('allocation_type error!')

        allocation_type = self.request.allocation_type
        store_ids = {int(i['store_id']) for i in self.request_dict['stores']}

        stores = metadata_service.get_store_list(ids=store_ids, return_fields='id', **self.identity).get('rows', [])
        if len(stores) != len(store_ids):
            raise DataValidationException('门店错误!')

        with redis_cli.lock(f'rad_excluded_store', timeout=60, blocking_timeout=0.1):
            exist_stores, _ = self.list([self.model.store_id.in_(store_ids),
                                         self.model.allocation_type == allocation_type],
                                        [self.model.store_id], limit=-1, offset=-1, need_count=False)
            exist_stores = {str(i.store_id) for i in exist_stores}

            instances = [self.model(**i, allocation_type=allocation_type, partner_id=self.partner_id)
                         for i in self.request_dict['stores'] if i['store_id'] not in exist_stores]

            if not instances:
                return self.response(result=True)

            session.bulk_save_objects(instances)
            session.commit()

        return self.response(result=True)

    def list_excluded_store(self):
        self.get_limit_offset()
        self.get_params()
        self.params.append(self.model.partner_id == self.partner_id)
        result, count = self.list(order_by=[self.model.updated_at.desc()],
                                  fields=self.get_query_fields(self.response.Store, ['store_name']))

        if result:
            stores_meta = metadata_service.get_store_list(ids={i.store_id for i in result}, return_fields='id,name',
                                                          **self.identity).get('rows', [])
            stores_name = {int(i['id']): i['name'] for i in stores_meta}

            for i, v in enumerate(result):
                result[i] = self.map_sa_result(v)
                result[i]['store_name'] = stores_name.get(v.store_id)

        return self.response(**{
            'stores': result,
            'count': count
        })

    def destroy_excluded_store(self):
        m = self.retrieve(filter_by={'id': self.request.id})
        if not m:
            return self.response(result=False)

        session.delete(m)
        session.commit()

        return self.response(result=True)

    def get_limit_offset(self, limit=None, offset=None):
        self.limit, self.offset = limit or self.limit or self.request_dict.pop('limit', None), \
                                  offset or self.offset or self.request_dict.pop('offset', None)
        self.limit = None if self.limit == -1 else self.limit
        self.offset = None if self.offset == -1 else self.offset

    def get_params(self, map_method: Callable = None, refresh=False):
        if not self.params or refresh:
            method = map_method or self.map_params_method
            # self.params = [getattr(getattr(self.model, k), method(v))(v) for k, v in self.request_dict.items() if v]
            self.params = []
            for k, v in self.request_dict.items():
                if v:
                    k, sa_method = method(k, v)
                    self.params.append(getattr(getattr(self.model, k), sa_method)(v))

    def map_params_method(self, k, v):
        if isinstance(v, list):
            return k, 'in_'
        elif isinstance(v, (str, datetime)) and k.startswith('start'):
            return k.replace('start_', '', 1), '__ge__'
        elif isinstance(v, (str, datetime)) and k.startswith('end'):
            return k.replace('end_', '', 1), '__le__'
        else:
            return k, '__eq__'

    def set_data(self, model=None, instance=None, req=None, ignore_null=True, use_self_req=True):
        m = instance or (model or self.model)()
        res = req if req or not use_self_req else self.request
        request_fields = {i.name for i in res.DESCRIPTOR.fields} if res else None

        for k in m.__table__.columns.keys():
            if res and k not in request_fields:
                continue
            if ignore_null and k not in self.request_dict:
                continue

            setattr(m, k, self.request_dict.get(k))
        return m

    def get_query_fields(self, res, pop_name=None):
        return [getattr(self.model, i.name) for i in res.DESCRIPTOR.fields if i.name not in (pop_name or {})]

    def map_sa_result(self, detail):
        return {k: getattr(detail, k) if k not in self.turn_fields else self.turn_fields[k](getattr(detail, k))
                for k in detail.keys()}

    def str_to_datetime(self, s, pattern='%Y-%m-%dT%H:%M:%S.%fZ'):
        try:
            return datetime.strptime(s,  pattern)
        except ValueError as e:
            logging.info(e)
            raise DataValidationException('date format error!')

    def datetime_to_str(self, s, pattern='%Y-%m-%dT%H:%M:%S.%fZ'):
        return datetime.strftime(s,  pattern)

    def get_metadata(self, ids=None, schema='store', return_fields=None):
        rf = return_fields or []
        single = isinstance(rf, str) and rf.find(',') == -1
        schema_dict = {'store': 'get_store_list', 'product': 'get_product_list'}
        if schema not in schema_dict:
            raise Exception('schema error!')
        method = schema_dict[schema]
        meta = getattr(metadata_service, method)(ids=ids, return_fields=','.join(rf) if isinstance(rf, list) else rf,
                                                 **self.identity).get('rows', [])
        return {int(i['id']): i[rf] if single else i for i in meta}

    def get_order_by(self, model=None, allow_sorts=None):
        m = model or self.model
        if not allow_sorts or not hasattr(self.request, 'order') or not hasattr(self.request, 'sort'):
            return [m.id.desc()]

        order, sort = self.request.order, self.request.sort

        order = order if order in ['asc', 'desc'] else 'desc'
        # sorts = {'code', 'id', 'demand_date', 'created_at', 'arrival_date', 'updated_at'}
        return [getattr(getattr(m, sort), order)(), m.id] if sort in allow_sorts else [m.updated_at.desc(), m.id]

