import logging
import sys
import traceback

from google.protobuf.json_format import MessageToDict
from hex_exception import exception_from_str

from ..driver.mq import mq_producer
from ..error.exception import *
from datetime import datetime, timedelta

from copy import deepcopy
from ..model.inventory import inventory_repository
from ..model.operation_log import ReceiptFlowLogModel
from ..utils.inventory_enum import ACTION
from ..utils.transfer_enum import STATUS
from supply.utils.helper import convert_to_int, convert_to_decimal, set_model_from_props, MessageTopic
from ..model.transfer import TransferRepository
from google.protobuf.timestamp_pb2 import Timestamp
from ..client.metadata_service import metadata_service
from supply.model.supply_doc_code import Supply_doc_code
from ..client.inventory_service import inventory_service
from ..client.receipt_service import receipt_service
from supply.utils.helper import check_stores_costcenter_belonging, get_position_list_map_by_branch_id
from ..task.message_service_pub import MessageServicePub
from ..driver.mysql import DummyTransaction
from sqlalchemy import text
from supply import logger
from decimal import Decimal
from supply.utils.snowflake import gen_snowflake_id
from supply.model.operation_log import TpTransLogModel
from supply.module.utils import get_transfer_products, get_transfer_product
from supply.module.franchisee.helper import *


class Dict(dict):
    __setattr__ = dict.__setitem__
    __getattr__ = dict.__getitem__


class TimestampAndMethodMixin(object):
    def __init__(self):
        self.created_at = None
        self.updated_at = None

    # 需要转时间戳的props
    def props(self):
        pr = {}
        for name in dir(self):
            value = getattr(self, name)
            if not name.startswith('__') and not callable(value) and not name.startswith('_'):
                if isinstance(value, datetime):
                    # value = value - timedelta(hours=8)
                    timestamp = Timestamp()
                    timestamp.FromDatetime(value)
                    pr[name] = timestamp
                else:
                    pr[name] = value
        return pr

    # 不需要转时间戳的props
    def no_timestamp_props(self):
        pr = {}
        for name in dir(self):
            value = getattr(self, name)
            if not name.startswith('__') and not callable(value) and not name.startswith('_'):
                pr[name] = value
        return pr


class Transfer(TimestampAndMethodMixin):
    def __init__(self):
        super(Transfer, self).__init__()
        self.id = None
        self.request_id = None
        self.master_id = None
        self.code = None
        self.type = None
        self.sub_type = None
        self.reason_type = None
        self.receiving_store = None
        self.shipping_store = None
        self.transfer_date = None
        self.status = None
        self.process_status = None
        self.description = None
        self.partner_id = None
        self.user_id = None
        self.extends = None
        self.auto_confirm = None
        self.branch_type = None
        self.receiving_position = None
        self.shipping_position = None
        self.sub_account_type = None


class TransferDetail(Transfer):
    def __init__(self):
        super(TransferDetail, self).__init__()
        self.transfer_id = None
        self.transfer_order_number = None
        self.shipper = None
        self.shipping_date = None
        self.shipping_store_name = None
        self.receiver = None
        self.receiving_date = None
        self.receiving_store_name = None
        self.created_by = None
        self.updated_by = None
        self.remark = None
        self.created_name = None
        self.updated_name = None
        self.auto_confirm = None
        self.branch_type = None
        self.receiving_position = None
        self.receiving_position_name = None
        self.shipping_position = None
        self.shipping_position_name = None
        self.type = None
        self.sub_type = None
        self.sub_account_type = None
        self.cross_company = None
        self.total_amount = None
        self.total_sales_amount = None


class TransferProduct(TimestampAndMethodMixin):
    def __init__(self):
        super(TransferProduct, self).__init__()
        self.id = None
        self.transfer_id = None
        self.product_id = None
        self.product_code = None
        self.product_name = None
        self.material_number = None
        self.unit_id = None
        self.unit_name = None
        self.unit_spec = None
        self.quantity = None
        self.accounting_received_quantity = None
        self.accounting_quantity = None
        self.item_number = None
        self.is_confirmed = None
        self.partner_id = None
        self.user_id = None
        self.extends = None
        self.created_by = None
        self.updated_by = None
        self.accounting_unit_id = None
        self.accounting_unit_name = None
        self.accounting_unit_spec = None
        self.description = None
        self.shipping_store = None
        self.receiving_store = None
        self.transfer_date = None
        self.confirmed_received_quantity = None
        self.created_name = None
        self.updated_name = None
        self.tax_rate = None
        self.tax_price = None
        self.cost_price = None
        self.amount = None
        self.sales_price = None
        self.sales_amount = None


class TransferLog(TimestampAndMethodMixin):
    def __init__(self):
        super(TransferLog, self).__init__()
        self.id = None
        self.transfer_id = None
        self.transfer_status = None
        self.reason = None
        self.created_by = None
        self.partner_id = None
        self.user_id = None


class TransferModule(object):
    def __init__(self):
        self.return_repo = TransferRepository()

    def check_transfer_position_config(self, branch_id, branch_type, position_id, transfer_type,
                                       partner_id, user_id):
        """校验组织是否开启多仓位如果开启判断调拨仓位是否在配置中,取到对应的调拨仓位返回
        :param branch_id
        :param branch_type -> warehouse/machining/store
        :param position_id
        :param transfer_type -> 内部`INTERNAL`/外部`EXTERNAL`调拨
        :param partner_id
        :param user_id
        1、先拉取总部业务配置，看该组织是否开启多仓位
        2、再拉取组织下的业务配置获取内部/外部调拨的仓位(调出门店和调入门店都要校验)
        3、区分内部`INTERNAL`/外部`EXTERNAL`调拨
        """
        open_position = False   # 是否开启多仓位
        business_config = metadata_service.get_business_config(partner_id=partner_id, user_id=user_id)
        if not business_config:
            return position_id
        stores = business_config.get('stores', [])
        region = business_config.get('region', [])
        # 通过区域拉取门店与stores合并
        if region:
            region_stores = metadata_service.get_store_list(relation_filters={"geo_region": region}, return_fields="id",
                                                            partner_id=partner_id, user_id=user_id).get('rows', [])
            region_store_ids = []
            if region_stores:
                region_store_ids = [str(s.get('id')) for s in region_stores]
            stores = list(set(stores + region_store_ids))
        warehouse = business_config.get('warehouse', [])
        machining = business_config.get('machining', [])
        if branch_type == 'STORE':
            if str(branch_id) in stores or business_config.get('store_all') is True:
                open_position = True
        if branch_type == 'WAREHOUSE':
            if str(branch_id) in warehouse or business_config.get('warehouse_all') is True:
                open_position = True
        if branch_type == 'MACHINING_CENTER':
            if str(branch_id) in machining or business_config.get('machining_all') is True:
                open_position = True
        if open_position is True:
            position_config = metadata_service.get_position_relation_config(branch_type=branch_type,
                                                                            branch_ids=[str(branch_id)],
                                                                            partner_id=partner_id,
                                                                            user_id=user_id)
            if not position_config:
                return position_id
            position_config = position_config[0]
            if transfer_type == 'INTERNAL':
                inner_positions = position_config.get('insideTransfer')
                if str(position_id) in inner_positions:
                    allow_position = position_id
                else:
                    raise DataValidationException("内部调拨仓位配置校验失败-{}".format(position_id))
            else:
                allow_position = position_config.get('transfer')
            return convert_to_int(allow_position)
        else:
            return position_id

    def check_transfer_branch_company(self, shipping_store, receiving_store, branch_type, partner_id, user_id):
        """校验调拨组织是否跨公司"""
        cross_company = False
        if branch_type == "WAREHOUSE":
            shipping = metadata_service.get_entity_by_id(schema_name='distrcenter', id=shipping_store,
                                                         partner_id=partner_id, user_id=user_id)
            receiving = metadata_service.get_entity_by_id(schema_name='distrcenter', id=receiving_store,
                                                          partner_id=partner_id, user_id=user_id)
            shipping_company = shipping.get('fields', {}).get('relation', {}).get('company_info')
            receiving_company = receiving.get('fields', {}).get('relation', {}).get('company_info')
        elif branch_type == "MACHINING_CENTER":
            shipping = metadata_service.get_machining_center_by_id(_id=shipping_store, return_fields="id",
                                                                   relation='company_info', partner_id=partner_id,
                                                                   user_id=user_id)
            receiving = metadata_service.get_machining_center_by_id(_id=receiving_store, return_fields="id",
                                                                    relation='company_info', partner_id=partner_id,
                                                                    user_id=user_id)
            shipping_company = shipping.get('relation', {}).get('company_info')
            receiving_company = receiving.get('relation', {}).get('company_info')

        else:
            shipping = metadata_service.get_store(store_id=shipping_store, return_fields='company_info',
                                                  partner_id=partner_id, user_id=user_id)
            receiving = metadata_service.get_store(store_id=receiving_store, return_fields='company_info',
                                                   partner_id=partner_id, user_id=user_id)
            shipping_company = shipping.get('company_info', [])[0]
            receiving_company = receiving.get('company_info', [])[0]
        if shipping_company != receiving_company:
            cross_company = True
        return cross_company

    def list_transfer_detail(self, request, partner_id, user_id):
        start_date = request.start_date
        end_date = request.end_date
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        logger.info("start_date: {}  end_date: {}".format(start_date, end_date))
        if start_date.year == 1970:
            raise DataValidationException("请传入查询开始日期")
        if end_date.year == 1970:
            raise DataValidationException("请传入查询结束日期")
        shipping_stores = list(request.shipping_stores)
        receiving_stores = list(request.receiving_stores)
        types = list(request.types) if request.types else []
        sub_type = request.sub_type
        receiving_positions = list(request.receiving_positions) if request.receiving_positions else []
        shipping_positions = list(request.shipping_positions) if request.shipping_positions else []

        product_ids = list(request.product_ids) if request.product_ids else []

        code = request.code
        include_total = request.include_total
        limit = request.limit
        offset = request.offset
        auto_confirm = request.auto_confirm
        order = request.order
        sort = request.sort
        branch_type = request.branch_type
        # if not sub_type:
        #     raise DataValidationException("请传入sub_type来区分内/外调拨")
        if not sort:
            sort = 'updated_at'
        status_list = []
        if request.status:
            for st in list(request.status):
                status = STATUS[st]
                status_list.append(status)
        else:
            status_list = None
        store_ids = None
        return self.return_repo.list_transfer_detail(partner_id=partner_id,
                                                     shipping_stores=shipping_stores,
                                                     receiving_stores=receiving_stores,
                                                     status=status_list, branch_type=branch_type,
                                                     limit=limit, offset=offset, include_total=include_total,
                                                     start_date=start_date, code=code,
                                                     end_date=end_date, store_ids=store_ids,
                                                     auto_confirm=auto_confirm, order=order,
                                                     sort=sort, types=types, sub_type=sub_type,
                                                     shipping_positions=shipping_positions,
                                                     receiving_positions=receiving_positions, product_ids=product_ids)

    def get_transfer_by_id(self, transfer_id, is_details=False, partner_id=None):
        return self.return_repo.get_transfer_by_id(transfer_id, is_details=is_details, partner_id=partner_id)

    def get_transfer_by_ids(self, transfer_ids, is_details=False, partner_id=None):
        return self.return_repo.get_transfer_by_ids(transfer_ids, is_details=is_details, partner_id=partner_id)

    def get_store_detail_by_id(self, store_id, branch_type, partner_id, user_id):
        """根据id拉取主档
        :param store_id 门店或仓库id
        :param branch_type 区分门店/仓库/加工中心
        :param partner_id
        :param user_id
        """
        store_detail = {}
        if not store_id:
            return store_detail
        if branch_type == "WAREHOUSE":
            store_detail = metadata_service.get_distribution_center(
                center_id=int(store_id), return_fields="code,name", partner_id=partner_id, user_id=user_id)
        elif branch_type == "MACHINING_CENTER":
            store_detail = metadata_service.get_machining_center_by_id(_id=store_id, return_fields='code,name',
                                                                       partner_id=partner_id, user_id=user_id)
        elif branch_type == "POSITION":
            position = metadata_service.get_entity_by_id(schema_name="POSITION", id=store_id,
                                                         partner_id=partner_id, user_id=user_id)
            store_detail = position.get('fields', {})
            store_detail["id"] = convert_to_int(position.get('id'))
        else:
            store_detail = metadata_service.get_store(store_id=store_id, return_fields='code,name',
                                                      partner_id=partner_id, user_id=user_id)
        return store_detail

    def iterate_product(self, products_unit, list_transfer_products):
        return_product = []
        for p in products_unit:
            product = {}
            product_id = convert_to_int(p['id']) if 'id' in p else None
            for return_p in list_transfer_products:
                if return_p.product_id == product_id:
                    product['quantity'] = return_p.quantity
                    product['unit_id'] = return_p.unit_id
                    break
            product['id'] = product_id
            if 'name' in p:
                product['product_name'] = p['name']

            if 'spec' in p and p['spec']:
                product['spec'] = p['spec']
            else:
                product['spec'] = ''
            product['product_code'] = p['code'] if 'code' in p else ''
            units = p['units'] if 'units' in p else []
            product_units = []
            for m_unit in units:
                if isinstance(m_unit, dict):
                    if m_unit['order']:
                        unit = {}
                        unit['id'] = m_unit['id'] if 'id' in m_unit else 1.0
                        unit['rate'] = m_unit['rate'] if 'rate' in m_unit else 1.0
                        unit['quantity'] = -1
                        product_units.append(unit)
            product['unit'] = product_units
            return_product.append(product)
        return return_product

    def iterate_inventory_product(self, inventory_products, products_unit, list_transfer_products,
                                  partner_id=None, user_id=None):
        store_product_inventory = []
        unit_dict = {}
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code',
                                                   partner_id=partner_id, user_id=user_id
                                                   )
        units = []
        if units_ret:
            units = units_ret['rows']
        if isinstance(units, list):
            for u in units:
                if isinstance(u, dict) and 'id' in u:
                    unit_dict[str(u['id'])] = dict()
                    if 'name' in u:
                        unit_dict[str(u['id'])]['name'] = u['name']
            quantity_avail = 0
            # 可用数量小于0,不能调拨
            # if inventory.quantity_avail < 0:
            #     quantity_avail = 0
            # else:
        for p in products_unit:
            product = {}
            product_id = convert_to_int(p['id']) if 'id' in p else None
            inventory = inventory_products.get(str(product_id))
            # if not inventory:
            #     continue
            if inventory:
                quantity_avail = inventory['quantity_avail']
            else:
                quantity_avail = 0
            for transfer_p in list_transfer_products:
                if transfer_p.product_id == product_id:
                    product['quantity'] = transfer_p.quantity
                    product['unit_id'] = transfer_p.unit_id
                    break
            product['id'] = product_id
            if 'name' in p:
                product['product_name'] = p['name']
            product['product_code'] = p['code'] if 'code' in p else ''
            product['product_category_id'] = int(p['category']) if 'category' in p else None
            units = p['units'] if isinstance(p, dict) and 'units' in p else []
            product_units = []
            for m_unit in units:
                if isinstance(m_unit, dict):
                    if m_unit.get('order'):
                        unit = {}
                        unit['id'] = int(m_unit['id'])
                        if str(m_unit['id']) in unit_dict:
                            if 'name' in unit_dict[str(m_unit['id'])]:
                                unit['name'] = \
                                    unit_dict[str(m_unit['id'])]['name']
                        rate = convert_to_decimal(m_unit['rate']) if 'rate' in m_unit and m_unit['rate'] and m_unit[
                            'rate'] != 0 else 1.0
                        unit['quantity'] = round(convert_to_decimal(quantity_avail) / convert_to_decimal(rate), 6)
                        product_units.append(unit)
            product['unit'] = product_units
            if len(product_units) == 0:
                continue
            store_product_inventory.append(product)
        return store_product_inventory

    def list_transfer_store_product(self, store_id, limit=-1, offset=0,
                                    include_total=False, search=None, search_fields=None, partner_id=None,
                                    user_id=None, category_ids=None, order_by=None, combine_result=False):
        order_by_inventory = order_by == 'real_inventory'
        if category_ids:
            product_relation_filters = {"product_category__in": [str(id) for id in category_ids]}
        else:
            product_relation_filters = None
        list_store_product_ret = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_transfer=True),
            product_filters={
                "status": "ENABLED",
                "bom_type__neq": 'MANUFACTURE'
            },
            product_search=search,
            product_search_fields=search_fields,
            user_id=user_id,
            offset=offset,
            limit=limit,
            include_total=include_total,
            include_product_units=True,
            return_fields="allow_transfer",
            include_product_fields='code,name,model_name,category,category_name,barcode',
            can_order=True,
            product_relation_filters=product_relation_filters,
            region="ATTRIBUTE_REGION"
        )
        total = 0
        store_product_transfer = []
        final_product_ids = []
        if list_store_product_ret:
            list_store_product = list_store_product_ret.get('rows')
            total = list_store_product_ret.get('total') if list_store_product_ret.get('total') else 0
            for product in list_store_product:
                product_dict = {}
                unit = {}
                product_dict['product_id'] = int(product.get('product_id'))
                product_dict['product_code'] = product.get('code')
                product_dict['product_name'] = product.get('name')
                product_dict['model_name'] = product.get('model_name')
                if product.get('category'):
                    product_dict['product_category_id'] = int(product.get('category'))
                product_dict['barcode'] = product['extends'].get('barcode') if product.get('extends') else []
                product_dict['unit'] = []
                if product.get('units'):
                    for uint in product.get('units'):
                        if uint.get('transfer') == True:
                            unit['id'] = int(uint.get('id'))
                            unit['name'] = uint.get('name')
                            unit['unit_rate'] = uint.get('rate')
                            product_dict['unit'].append(unit)
                # 上层过滤无用，需要根据relation关联，因此在这里加一层过滤
                if product.get('allow_transfer'): 
                    store_product_transfer.append(product_dict)
                    final_product_ids.append(product_dict['product_id'])

        # 新增按照实时库存排序
        inv_unchanged_products, inv_changed_products = [], []
        if order_by_inventory and final_product_ids:
            order_result = inventory_service.query_branch_inventory_sort(partner_id, user_id, store_id,
                                                                         product_ids=final_product_ids).get('rows')
            # logging.info(f'调拨库存排序：{order_result}')
            if order_result:
                big_int = 999999999
                inv_map = {int(v['product_id']): {'index': i, 'qty': v.get('qty'), 'flag': v.get('flag') == '1'}
                           for i, v in enumerate(order_result)}
                store_product_transfer.sort(key=lambda x: inv_map.get(x['product_id'], {}).get('index', big_int))
                for v in store_product_transfer:
                    inv_data = inv_map.get(v['product_id'], {})
                    inv_qty = inv_data.get('qty', 0)
                    v['real_inventory_qty'] = inv_qty/ v['unit'][0].get('unit_rate', 1.0) if v['unit'] else inv_qty
                    inv_changed_products.append(v) if combine_result or inv_data.get('flag') \
                        else inv_unchanged_products.append(v)

                store_product_transfer = inv_changed_products

        store_product_transfer = get_transfer_products(store_product_transfer)
        inv_unchanged_products = get_transfer_products(inv_unchanged_products)
        if include_total:
            return total, store_product_transfer, inv_unchanged_products
        return store_product_transfer, inv_unchanged_products

    def no_allow_minus_inventory(self, store_id, ids_str, total=None, include_total=False, offset=None, limit=None,
                                 search=None, search_fields=None, partner_id=None, user_id=None):
        inventory_products = inventory_service.get_products_inventory_by_branch_id(
            branch_id=int(store_id),
            partner_id=partner_id,
            user_id=user_id,
        )
        # 取得门店商品的单位
        products_unit_ret = metadata_service.get_product_list(ids=ids_str, include_units=True,
                                                              return_fields='name,code,category,bom_type',
                                                              partner_id=partner_id, user_id=user_id,
                                                              )

        products_unit = []
        if products_unit_ret:
            products_unit = products_unit_ret['rows']
        if inventory_products is None or not isinstance(inventory_products, dict) or len(inventory_products) == 0:
            return None
        if products_unit is None or not isinstance(products_unit, list) or len(products_unit) == 0:
            return None
        store_product_inventory = self.iterate_inventory_product(inventory_products, products_unit, [],
                                                                 partner_id=partner_id, user_id=user_id)
        count = total
        if include_total:
            return count, store_product_inventory[offset:offset + limit]
        return store_product_inventory[offset:offset + limit]

    def list_same_attribute_region_store(self, store_id, partner_id, user_id):
        # store_ids = ','.join([str(store_id)])
        list_store = metadata_service.get_store(store_id=store_id, return_fields="id,name,transfer_region",
                                                partner_id=partner_id, user_id=user_id
                                                )
        # list_store = hex_api.get_service('store').list(partner_id, user_id,
        #                                                querystring=dict(ids=store_ids, relation='attribute_region'))
        if list_store and isinstance(list_store, dict) and len(list_store) > 0:
            store = list_store
            if 'transfer_region' in store and store['transfer_region']:
                filters = {"status__eq": "ENABLED", 'open_status': 'OPENED',
                           'relation.transfer_region__in': [str(store['transfer_region'][0])]}
                list_same_store_ret = metadata_service.get_store_list(
                    filters=filters, partner_id=partner_id, user_id=user_id)
                list_same_store = []
                if list_same_store_ret:
                    list_same_store = list_same_store_ret['rows']
                return_content = []
                if list_same_store and isinstance(list_same_store, list) and len(list_same_store) > 0:
                    for s in list_same_store:
                        content = {}
                        if convert_to_int(s['id']) == store_id:
                            continue
                        content['id'] = convert_to_int(s['id'])
                        content['name'] = s['name']
                        content['address'] = s['address'] if 'address' in s else ''
                        return_content.append(content)
                    return return_content
        return None

    def list_transfer_product(self, partner_id=None, user_id=None, limit=None, offset=None, include_total=False,
                              product_ids=None, order=None, transfer_id=None):
        """获取调拨单商品列表，包含当前库存并转换为调拨单位数量

        :param partner_id: 合作伙伴ID
        :param user_id: 用户ID
        :param limit: 分页限制
        :param offset: 分页偏移
        :param include_total: 是否包含总数
        :param product_ids: 商品ID列表
        :param order: 排序
        :param transfer_id: 调拨单ID
        :return: 调拨商品列表，包含库存信息和单位转换
        """
        # 获取调拨单商品列表
        transfer_product_result,total = self.return_repo.list_transfer_product(
            partner_id=partner_id,
            limit=limit,
            offset=offset,
            include_total=include_total,
            transfer_id=transfer_id
        )

        # 处理返回结果（可能包含总数）
        total = None
        transfer_product_list = transfer_product_result
        if include_total and isinstance(transfer_product_result, tuple):
            total, transfer_product_list = transfer_product_result

        if not transfer_product_list:
            if include_total:
                return total or 0, []
            return []

        # 查询调拨单主表信息获取调出门店ID
        transfer = self.return_repo.get_transfer_by_id(transfer_id, is_details=False, partner_id=partner_id)
        if not transfer:
            if include_total:
                return total or 0, []
            return []

        store_id = transfer.shipping_store

        # 收集商品ID列表
        product_ids = []
        for transfer_product in transfer_product_list:
            product_ids.append(transfer_product.product_id)

        # 获取商品的实时库存信息
        inventory_dict = inventory_service.get_products_inventory_by_branch_id(
            branch_id=store_id,
            partner_id=partner_id,
            user_id=user_id,
            product_ids=product_ids
        )

        # 获取商品主档信息（包含单位信息）
        products_unit_ret = metadata_service.get_product_list(
            ids=product_ids,
            include_units=True,
            return_fields='name,code,category,units',
            partner_id=partner_id,
            user_id=user_id
        )

        products_unit = []
        if products_unit_ret:
            products_unit = products_unit_ret.get('rows', [])

        # 获取单位信息字典
        units_ret = metadata_service.get_unit_list(
            return_fields='id,name,code',
            partner_id=partner_id,
            user_id=user_id
        )

        unit_dict = {}
        if units_ret:
            units = units_ret.get('rows', [])
            for unit in units:
                if isinstance(unit, dict) and 'id' in unit:
                    unit_dict[str(unit['id'])] = unit

        # 构建商品主档字典
        product_info_dict = {}
        for product in products_unit:
            if isinstance(product, dict) and 'id' in product:
                product_info_dict[str(product['id'])] = product

        # 处理调拨商品列表，添加库存信息和单位转换
        result_list = []
        for transfer_product in transfer_product_list:
            # 转换为字典格式
            product_dict = transfer_product.props() if hasattr(transfer_product, 'props') else transfer_product

            # 获取当前商品的库存信息
            inventory_info = inventory_dict.get(str(transfer_product.product_id), {})
            quantity_avail = inventory_info.get('quantity_avail', 0)

            # 获取商品主档信息
            product_info = product_info_dict.get(str(transfer_product.product_id), {})
            units = product_info.get('units', [])

            # 计算调拨单位的当前库存数量
            transfer_unit_inventory = 0
            if units and transfer_product.unit_id:
                for unit in units:
                    if isinstance(unit, dict) and convert_to_int(unit.get('id')) == transfer_product.unit_id:
                        unit_rate = convert_to_decimal(unit.get('rate', 1))
                        if unit_rate and unit_rate != 0:
                            # 库存数量 / 单位转换率 = 调拨单位数量
                            transfer_unit_inventory = round(
                                convert_to_decimal(quantity_avail) / unit_rate, 6
                            )
                        break

            # 添加调拨单位的当前库存数量
            product_dict['real_inventory_qty'] = transfer_unit_inventory

            # 添加单位信息
            if transfer_product.unit_id and str(transfer_product.unit_id) in unit_dict:
                unit_info = unit_dict[str(transfer_product.unit_id)]
                product_dict['unit_name'] = unit_info.get('name', '')
                product_dict['unit_code'] = unit_info.get('code', '')

            result_list.append(product_dict)
        return result_list,total



    def get_products_by_transfer_ids(self, partner_id=None, user_id=None, transfer_ids=None):
        return self.return_repo.get_products_by_transfer_ids(partner_id=partner_id, transfer_ids=transfer_ids)

    def valid_transfer_store_product(self, store_id, transfer_date, transfer_products, partner_id, user_id):
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            list_store_product = []
            list_store_product_ret = metadata_service.get_attribute_products_by_store_id(store_id=store_id,
                                                                                         partner_id=partner_id,
                                                                                         user_id=user_id)
            if list_store_product_ret:
                list_store_product = list_store_product_ret['rows']
            if list_store_product and isinstance(list_store_product, list) \
                    and len(list_store_product) > 0:
                set_error_product = []
                not_allow_transfer = []
                product_ids = []
                for store_product in list_store_product:
                    product_ids.append(convert_to_int(store_product['product_id']))

                for store_product in list_store_product:
                    for transfer_product in transfer_products:
                        # 判断产品是否为区域产品
                        if transfer_product.product_id not in product_ids:
                            set_error_product.append(str(transfer_product.product_id))
                        # 门店产品不允许调拨
                        if convert_to_int(store_product['product_id']) == transfer_product.product_id \
                                and not store_product['allow_transfer']:
                            not_allow_transfer.append(str(transfer_product.product_id))
                # 错误的产品和不允许调拨的产品
                set_error_product = list(set(set_error_product))
                not_allow_transfer = list(set(not_allow_transfer))
                if len(set_error_product) > 0 and len(not_allow_transfer) > 0:
                    set_error_product.extend(not_allow_transfer)
                    set_error_product = list(set(set_error_product))
                    all_ids = ','.join(set_error_product)
                    raise DataValidationException(all_ids)
                # 错误的产品
                if len(set_error_product) > 0:
                    err_ids = ','.join(set_error_product)
                    raise DataValidationException(err_ids)
                # 不允许调拨的产品
                if len(not_allow_transfer) > 0:
                    all_ids = ','.join(not_allow_transfer)
                    raise DataValidationException(not_allow_transfer)
        return True

    def update_confirmed_product_unit(self, transfer_products, partner_id, user_id):
        if transfer_products is None or not isinstance(transfer_products, list) or len(transfer_products) == 0:
            return None
        units = []
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code', partner_id=partner_id, user_id=user_id)
        if units_ret:
            units = units_ret['rows']
        if not units or not isinstance(units, list) or len(units) == 0:
            return None
        dict_unit = {}
        for unit in units:
            dict_unit[str(unit['id'])] = unit
        product_ids = []
        for p in transfer_products:
            product_ids.append(p.product_id)
        main_products_ret = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                              return_fields='name,code,bom_type', partner_id=partner_id,
                                                              user_id=user_id)
        main_products = []
        if main_products_ret:
            main_products = main_products_ret['rows']
        if main_products is None or not isinstance(main_products, list) or len(main_products) == 0:
            return None
        list_bom_type = []
        for main_p in main_products:
            bom_type = main_p['bom_type'].upper() if 'bom_type' in main_p and main_p['bom_type'] else None
            for product in transfer_products:
                product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
                product.partner_id = partner_id
                if product_id == product.product_id:
                    if bom_type == 'MANUFACTURE':
                        list_bom_type.append(str(product_id))
                    if 'name' in main_p:
                        product.product_name = main_p['name']
                    if 'code' in main_p:
                        product.product_code = str(main_p['code'])
                        product.material_number = str(main_p['code'])
                    units = main_p['units'] if 'units' in main_p else []
                    unit_current = None
                    # unit_order = None
                    unit_default = None
                    for m_unit in units:
                        if isinstance(m_unit, dict):
                            if convert_to_int(product.unit_id) == convert_to_int(m_unit['id']):
                                unit_current = m_unit
                            # if m_unit['order']:
                            #     unit_order = m_unit
                            if m_unit.get('default'):
                                unit_default = m_unit
                    if unit_current and unit_default:
                        d_unit_current = dict_unit.get(str(unit_current['id']))
                        d_unit_default = dict_unit.get(str(unit_default['id']))
                        if 'name' in d_unit_current:
                            product.unit_name = d_unit_current['name'] if 'name' in d_unit_current else None
                            product.unit_spec = d_unit_current['code'] if 'code' in d_unit_current else None

                        if unit_current['id'] == unit_default['id']:
                            product.accounting_unit_id = convert_to_int(unit_current['id'])
                            if 'name' in d_unit_current and d_unit_current['name']:
                                product.accounting_unit_name = d_unit_current['name']
                            rate = convert_to_decimal(round(unit_current['rate'], 8)) if 'rate' in unit_current else 1.0
                            if product.confirmed_received_quantity != None:
                                product.accounting_received_quantity = (
                                            convert_to_decimal(product.confirmed_received_quantity) * rate).quantize(
                                    Decimal('0.********'))
                            product.accounting_unit_spec = str(
                                d_unit_current['code']) if 'code' in d_unit_current else None
                        else:
                            product.accounting_unit_id = convert_to_int(unit_default['id'])
                            if 'name' in d_unit_default and d_unit_default['name']:
                                product.accounting_unit_name = d_unit_default['name']
                            if product.confirmed_received_quantity != None:
                                product.accounting_received_quantity = (convert_to_decimal(
                                    product.confirmed_received_quantity) * convert_to_decimal(
                                    round(unit_current['rate'], 8))).quantize(Decimal('0.********'))
                            product.accounting_unit_spec = str(
                                d_unit_default['code']) if 'code' in d_unit_default else None
                    else:
                        product.accounting_received_quantity = (
                            convert_to_decimal(product.confirmed_received_quantity)).quantize(Decimal('0.********'))
                        product.accounting_unit_id = product.unit_id
        dict_product = {}
        for p in transfer_products:
            if str(p.product_id) not in dict_product:
                dict_product[str(p.product_id)] = p
            else:
                dict_p = dict_product[str(p.product_id)]
                dict_p.confirmed_received_quantity = p.confirmed_received_quantity + dict_p.confirmed_received_quantity
                dict_p.accounting_received_quantity = p.accounting_received_quantity + dict_p.accounting_received_quantity
                transfer_products.remove(p)
        if list_bom_type and isinstance(list_bom_type, list) and len(list_bom_type) > 0:
            raise DataValidationException("现做商品不允许调拨:" + ','.join(list_bom_type))
        return transfer_products

    def update_product_unit(self, transfer_products, partner_id, user_id):
        if transfer_products is None or not isinstance(transfer_products, list) or len(transfer_products) == 0:
            return None
        units = []
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code', partner_id=partner_id, user_id=user_id)
        if units_ret:
            units = units_ret['rows']
        if not units or not isinstance(units, list) or len(units) == 0:
            return None
        dict_unit = {}
        for unit in units:
            dict_unit[str(unit['id'])] = unit
        product_ids = []
        for p in transfer_products:
            product_ids.append(p.product_id)
        main_products_ret = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                              return_fields='name,code,bom_type', partner_id=partner_id,
                                                              user_id=user_id)
        main_products = []
        if main_products_ret:
            main_products = main_products_ret['rows']
        if main_products is None or not isinstance(main_products, list) or len(main_products) == 0:
            return None
        list_bom_type = []
        for main_p in main_products:
            bom_type = main_p['bom_type'].upper() if 'bom_type' in main_p and main_p['bom_type'] else None
            for product in transfer_products:
                product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
                product.partner_id = partner_id
                if product_id == product.product_id:
                    if bom_type == 'MANUFACTURE':
                        list_bom_type.append(str(product_id))
                    if 'name' in main_p:
                        product.product_name = main_p['name']
                    if 'code' in main_p:
                        product.product_code = str(main_p['code'])
                        product.material_number = str(main_p['code'])
                    units = main_p['units'] if 'units' in main_p else []
                    unit_current = None
                    # unit_order = None
                    unit_default = None
                    for m_unit in units:
                        if isinstance(m_unit, dict):
                            if convert_to_int(product.unit_id) == convert_to_int(m_unit['id']):
                                unit_current = m_unit
                            # if m_unit['order']:
                            #     unit_order = m_unit
                            if m_unit.get('default'):
                                unit_default = m_unit
                    if unit_current and unit_default:
                        d_unit_current = dict_unit.get(str(unit_current['id']))
                        d_unit_default = dict_unit.get(str(unit_default['id']))
                        if 'name' in d_unit_current:
                            product.unit_name = d_unit_current['name'] if 'name' in d_unit_current else None
                            product.unit_spec = d_unit_current['code'] if 'code' in d_unit_current else None

                        if unit_current['id'] == unit_default['id']:
                            product.accounting_unit_id = convert_to_int(unit_current['id'])
                            if 'name' in d_unit_current and d_unit_current['name']:
                                product.accounting_unit_name = d_unit_current['name']
                            rate = convert_to_decimal(round(unit_current['rate'], 8)) if 'rate' in unit_current else 1.0
                            if product.quantity != None:
                                product.accounting_quantity = (convert_to_decimal(product.quantity) * rate).quantize(
                                    Decimal('0.********'))
                            product.accounting_unit_spec = str(
                                d_unit_current['code']) if 'code' in d_unit_current else None
                        else:
                            product.accounting_unit_id = convert_to_int(unit_default['id'])
                            if 'name' in d_unit_default and d_unit_default['name']:
                                product.accounting_unit_name = d_unit_default['name']
                            if product.quantity != None:
                                product.accounting_quantity = (convert_to_decimal(
                                    product.quantity) * convert_to_decimal(
                                    round(unit_current['rate'], 8))).quantize(
                                    Decimal('0.********'))
                            product.accounting_unit_spec = str(
                                d_unit_default['code']) if 'code' in d_unit_default else None
                    else:
                        product.accounting_quantity = (convert_to_decimal(product.quantity)).quantize(
                                    Decimal('0.********'))
                        product.accounting_unit_id = product.unit_id
        dict_product = {}
        for p in transfer_products:
            if str(p.product_id) not in dict_product:
                dict_product[str(p.product_id)] = p
            else:
                dict_p = dict_product[str(p.product_id)]
                dict_p.quantity = p.quantity + dict_p.quantity
                dict_p.accounting_quantity = p.accounting_quantity + dict_p.accounting_quantity
                transfer_products.remove(p)
        if list_bom_type and isinstance(list_bom_type, list) and len(list_bom_type) > 0:
            raise DataValidationException(','.join(list_bom_type))
        return transfer_products

    def check_product_inventory(self, transfer_products=None, store_id=None, position_id=None, detail=False,
                                partner_id=None, user_id=None):
        out_product = []
        if position_id:
            sub_account_ids = [position_id]
        else:
            sub_account_ids = []
        product_ids = [p.product_id for p in transfer_products]
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:

            store_product_inventory_map = inventory_service.get_products_inventory_by_branch_id(branch_id=store_id,
                                                                                                sub_account_ids=sub_account_ids,
                                                                                                detail=detail,
                                                                                                partner_id=partner_id,
                                                                                                user_id=user_id,
                                                                                                product_ids=product_ids)
            # 校验库存
            for transfer_product in transfer_products:
                if position_id:
                    product_inventory = store_product_inventory_map.get(
                        str(position_id) + str(transfer_product.product_id))
                else:
                    product_inventory = store_product_inventory_map.get(str(transfer_product.product_id))
                if product_inventory:
                    if transfer_product.accounting_quantity != None and product_inventory.get('quantity_avail') != None:
                        logger.info("库存校验：调拨数量-{} - 库存数量-{}".format(float(transfer_product.accounting_quantity),
                                                                    float(product_inventory.get('quantity_avail'))))
                        if float(transfer_product.accounting_quantity) > float(product_inventory.get('quantity_avail')):
                            out_product.append(str(transfer_product.product_name))
                else:
                    out_product.append(str(transfer_product.product_name))
        return out_product

    def get_request_products_dict(self, request_product):
        transfer_products = []
        try:
            for p in request_product:
                product_obj = TransferProduct()
                p_data = {}
                for name in dir(product_obj):
                    if not name.startswith('__') and not name.startswith('_'):
                        if hasattr(p, name):
                            r_value = getattr(p, name)
                            if not callable(r_value):
                                p_data[name] = r_value
                set_model_from_props(p_data, product_obj)
                transfer_products.append(product_obj)
            return transfer_products
        except Exception:
            return []

    def valid_same_region_and_direct(self, shipping_store, receiving_store, partner_id, user_id):
        store_ids = [receiving_store, shipping_store]
        list_store_ret = metadata_service.get_store_list(ids=store_ids, partner_id=partner_id, user_id=user_id,
                                                         return_fields="id,transfer_region,open_status")
        list_store = []
        if list_store_ret:
            list_store = list_store_ret['rows']
        if list_store and isinstance(list_store, list) and len(list_store) == 2:
            attribute_region = []
            set_close_store = []
            for s in list_store:
                if 'transfer_region' in s and s['transfer_region'][0] not in attribute_region:
                    attribute_region.append(convert_to_int(s['transfer_region'][0]))
                if 'open_status' in s and str(s['open_status']).upper() != 'OPENED':
                    set_close_store.append(convert_to_int(s['id']))
            attribute_region = list(set(attribute_region))
            is_region = False
            if len(attribute_region) == 1:
                is_region = True
            return is_region, list(set(set_close_store))
        else:
            if list_store and isinstance(list_store, list) and len(list_store) > 0:
                ids = []
                for s in list_store:
                    ids.append(s.get('id'))
                if shipping_store not in ids:
                    raise DataValidationException(str(shipping_store))
                if receiving_store not in ids:
                    raise DataValidationException(str(receiving_store))

    def create_update_transfer(self, request, partner_id, user_id, username=None, is_create=False):
        data = {}
        request_id = request.request_id
        branch_type = request.branch_type
        if not request_id:
            raise NoRequestIDError('没有请求ID')
        # 验证该请求是否已创建
        transfer_d = self.return_repo.get_transfer_by_request_id(request_id)
        if transfer_d:
            return transfer_d
        transfer_detail = TransferDetail()
        for name in dir(transfer_detail):
            if not name.startswith('__') and not name.startswith('_'):
                if hasattr(request, name):
                    r_value = getattr(request, name)
                    if not callable(r_value):
                        if isinstance(r_value, Timestamp):
                            # timestamp = Timestamp()
                            # timestamp.seconds = r_value.seconds
                            data[name] = datetime.fromtimestamp(r_value.seconds)

                        else:
                            data[name] = r_value
        set_model_from_props(data, transfer_detail)
        transfer_products = []
        if request.products:
            transfer_products = self.get_request_products_dict(request.products)
        if transfer_detail.sub_type == "EXTERNAL":
            if not transfer_detail.receiving_store:
                raise DataValidationException('receiving_store is required')
            if not transfer_detail.shipping_store:
                raise DataValidationException('shipping_store is required')
            transfer_detail.cross_company = self.check_transfer_branch_company(
                shipping_store=transfer_detail.shipping_store,
                receiving_store=transfer_detail.receiving_store,
                branch_type=branch_type, partner_id=partner_id,
                user_id=user_id)
        else:
            if not transfer_detail.shipping_store:
                raise DataValidationException('shipping_store is required')
            transfer_detail.cross_company = False
        # receiving_position = transfer_detail.receiving_position
        # shipping_position = transfer_detail.shipping_position
        # if transfer_detail.sub_type == "EXTERNAL":
        #     is_costcenter_id = check_stores_costcenter_belonging(
        #         [transfer_detail.shipping_store, transfer_detail.receiving_store], partner_id, user_id)
        #     if not is_costcenter_id:
        #         if branch_type == "WAREHOUSE":
        #             raise DataValidationException("仓库不属于同一个成本中心")
        #         raise DataValidationException("门店不属于同一个成本中心")
        # 根据组织下的业务配置获取允许内部/外部调拨的仓位和前端传的仓位做下校验，然后拉取主档获取仓位名称编号等
        receiving_position = self.check_transfer_position_config(branch_type=branch_type,
                                                                 branch_id=transfer_detail.receiving_store,
                                                                 position_id=transfer_detail.receiving_position,
                                                                 transfer_type=transfer_detail.sub_type,
                                                                 partner_id=partner_id, user_id=user_id)
        shipping_position = self.check_transfer_position_config(branch_type=branch_type,
                                                                branch_id=transfer_detail.shipping_store,
                                                                position_id=transfer_detail.shipping_position,
                                                                transfer_type=transfer_detail.sub_type,
                                                                partner_id=partner_id, user_id=user_id)
        if receiving_position or shipping_position:
            # 标记开启多仓位子账户功能用于库存扣减判断, 不管是调出门店还是调入门店只要有一个开启多仓位值就为"position"没配置为None
            transfer_detail.sub_account_type = "position"
        if receiving_position:
            receiving_position_detail = self.get_store_detail_by_id(store_id=receiving_position, branch_type="POSITION",
                                                                    partner_id=partner_id, user_id=user_id)
            transfer_detail.receiving_position_name = receiving_position_detail.get('name')
        if shipping_position:
            shipping_position_detail = self.get_store_detail_by_id(store_id=shipping_position, branch_type="POSITION",
                                                                   partner_id=partner_id, user_id=user_id)
            transfer_detail.shipping_position_name = shipping_position_detail.get('name')

        transfer_detail.receiving_position = receiving_position
        transfer_detail.shipping_position = shipping_position
        # 兼容仓库/加工中心处理
        if branch_type == "WAREHOUSE":
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
            receiving_store = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
                                                          branch_type=branch_type,
                                                          partner_id=partner_id, user_id=user_id)
        elif branch_type == "MACHINING_CENTER":
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
            receiving_store = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
                                                          branch_type=branch_type,
                                                          partner_id=partner_id, user_id=user_id)
        else:
            if transfer_detail.sub_type == "EXTERNAL":
                is_region, set_close_store = self.valid_same_region_and_direct(transfer_detail.shipping_store,
                                                                               transfer_detail.receiving_store,
                                                                               partner_id, user_id)
                if not is_region:
                    raise DataValidationException("门店调拨区域不一样")
                if set_close_store and isinstance(set_close_store, list) and len(set_close_store) > 0:
                    raise DataValidationException("门店不是开店状态")
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
            receiving_store = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
                                                          branch_type=branch_type,
                                                          partner_id=partner_id, user_id=user_id)
        if not isinstance(shipping_store, dict):
            raise DataValidationException('invalid shipping_store')
        if not isinstance(receiving_store, dict):
            raise DataValidationException('invalid receiving_store')

        shipping_store_name = shipping_store.get('name')
        receiving_store_name = receiving_store.get('name')
        transfer_detail.shipping_store_name = shipping_store_name
        transfer_detail.receiving_store_name = receiving_store_name
        transfer_detail.partner_id = partner_id
        if not transfer_detail.transfer_date:
            transfer_detail.transfer_date = datetime.utcnow()
        if not transfer_detail.shipper:
            transfer_detail.shipper = user_id
        if transfer_detail.shipping_date:
            transfer_detail.shipping_date = datetime.utcnow()
        if not transfer_detail.transfer_id:
            transfer_detail.created_by = user_id
            transfer_detail.created_at = datetime.utcnow()
            transfer_detail.status = 'INITED'
            transfer_detail.process_status = 'INITED'
        else:
            transfer_detail.updated_by = user_id
            transfer_detail.updated_at = datetime.utcnow()
        if not transfer_detail.code:
            transfer_detail.code = Supply_doc_code.get_code_by_type(
                'STORE_TRS', partner_id, None)
        if transfer_detail.code:
            transfer_detail.transfer_order_number = convert_to_int(transfer_detail.code)
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            # b_store_product = self.valid_transfer_store_product(transfer_detail.shipping_store,
            #                                                     transfer_detail.transfer_date, transfer_products,
            #                                                     partner_id, user_id)

            # if b_store_product:
            # 库存单位转换
            transfer_products = self.update_product_unit(transfer_products, partner_id, user_id)

            # 校验库存数量，多仓位主档配置后需要通过仓位来校验
            if transfer_products:
                # 租户是否校验库存
                allow_neg_inv = metadata_service.get_business_extra_config(
                            partner_id=partner_id, user_id=user_id, domain="boh.store.transfer").get("allow_neg_inv")
                logging.info(allow_neg_inv)
                if allow_neg_inv and allow_neg_inv == True: # 允许负库存
                    pass
                else:
                    # 校验库存
                    # 配置多仓位需要通过仓位来校验库存
                    if transfer_detail.shipping_position:
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                   store_id=transfer_detail.shipping_store,
                                                                   position_id=transfer_detail.shipping_position,
                                                                   detail=True,
                                                                   partner_id=partner_id, user_id=user_id)
                    else:
                        # 未配置多仓位还校验门店库存
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                   store_id=transfer_detail.shipping_store,
                                                                   partner_id=partner_id, user_id=user_id)
                    if len(out_product) > 0:
                        raise TransferInventoryException("商品库存不存在或超出库存:" + '\n' + ','.join(out_product))

            if (transfer_detail.branch_type=="FRS_STORE" and transfer_detail.shipping_store is not None and transfer_products):
                product_id_list = [int(transfer_product.product_id) for transfer_product in transfer_products]
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=transfer_detail.shipping_store, user_id=user_id, partner_id=partner_id, product_ids=product_id_list, include_sales_price=True)
                for product_item in transfer_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity, 'amount', 'sales_amount', product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
            transfer_d = self.return_repo.create_update_transfer(transfer_detail, transfer_products, partner_id,
                                                                 user_id, username)
            # 新建清理调拨待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=transfer_d.shipping_store,
                                             doc_type="transfer"))
            return transfer_d

    def check_transfer_product_division(self, store_id, transfer_products, partner_id, user_id):
        product_ids = [p.product_id for p in transfer_products]
        check_product = []
        list_store_product = metadata_service.get_attribute_products_by_store_id(store_id=store_id,
                                                                                 product_ids=product_ids,
                                                                                 filters={"allow_transfer": True},
                                                                                 partner_id=partner_id,
                                                                                 product_filters={
                                                                                     "__or": [
                                                                                         {"product_type__eq":
                                                                                              "FINISHED",
                                                                                          "bom_type__neq":
                                                                                              "MANUFACTURE",
                                                                                          },
                                                                                         {"product_type__neq":
                                                                                              "FINISHED",
                                                                                          "bom_type__eq":
                                                                                              "MANUFACTURE",
                                                                                          },
                                                                                         {"product_type__neq":
                                                                                              "FINISHED",
                                                                                          "bom_type__neq":
                                                                                              "MANUFACTURE",
                                                                                          }
                                                                                     ],
                                                                                     "product_type__neq":
                                                                                         "SEMI-FINISHED",
                                                                                     "status": "ENABLED",
                                                                                     "allow_transfer": True
                                                                                 },
                                                                                 user_id=user_id,
                                                                                 include_total=True,
                                                                                 can_order=True,
                                                                                 check_division=False
                                                                                 ).get('rows', [])
        check_product_ids = []
        for product in list_store_product:
            product_id = int(product.get('product_id'))
            check_product_ids.append(product_id)
        # 校验
        for transfer_product in transfer_products:
            product_id = transfer_product.product_id
            if product_id not in check_product_ids:
                check_product.append(str(transfer_product.product_name))
        return check_product

    def update_transfer_product(self, request, partner_id, user_id, username=None):
        transfer_id = request.transfer_id
        remark = request.remark
        transfer_date = request.transfer_date
        if transfer_date:
            timestamp = Timestamp()
            timestamp.seconds = transfer_date.seconds
            transfer_date = timestamp.ToDatetime()
            if transfer_date == datetime(1970, 1, 1):
                transfer_date = None
            else:
                transfer_date = transfer_date
        transfer_products = []
        if request.products:
            if request.products:
                transfer_products = self.get_request_products_dict(request.products)
        transfer_detail = self.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if (transfer_detail and transfer_detail.status == 'INITED' and transfer_products and isinstance(
                transfer_products, list) and len(transfer_products) > 0):
            transfer_products = self.update_product_unit(transfer_products, partner_id, user_id)
            # 校验库存数量, 多仓位主档配置后需要通过仓位来校验库存
            if transfer_products:
                # 租户是否校验库存
                allow_neg_inv = metadata_service.get_business_extra_config(
                            partner_id=partner_id, user_id=user_id, domain="boh.store.transfer").get("allow_neg_inv")
                if allow_neg_inv and allow_neg_inv == True: # 允许负库存
                    pass
                else: # 校验库存
                    # 配置多仓位需要通过仓位来校验库存
                    if transfer_detail.shipping_position:
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                store_id=transfer_detail.shipping_store,
                                                                position_id=transfer_detail.shipping_position,
                                                                detail=True,
                                                                partner_id=partner_id, user_id=user_id)
                    else:
                        # 未配置多仓位还校验门店库存
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                store_id=transfer_detail.shipping_store,
                                                                partner_id=partner_id, user_id=user_id)
                    if len(out_product) > 0:
                        raise TransferInventoryException("商品库存不存在或超出库存:" + '\n' + ','.join(out_product))
            if (transfer_products != None and len(transfer_products) != 0):
                if (transfer_detail.branch_type=="FRS_STORE" and transfer_detail.shipping_store is not None and transfer_products):
                    product_id_list = [int(transfer_product.product_id) for transfer_product in transfer_products]
                    product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=transfer_detail.shipping_store, user_id=user_id, partner_id=partner_id, product_ids=product_id_list, include_sales_price=True)
                    for product_item in transfer_products:
                        fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map,
                                                       product_item.product_id, 1, product_item.accounting_quantity, 'amount', 'sales_amount', product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                transfer_d = self.return_repo.update_transfer_product(transfer_detail, transfer_products,
                                                                      remark, transfer_date, user_id, username,
                                                                      partner_id=partner_id)
                if transfer_d:
                    return True
                else:
                    raise ActionException('更新失败')
            else:
                raise ActionException('更新失败')
        elif (remark and transfer_detail and transfer_detail.status == 'INITED') \
                or (transfer_date and transfer_detail and transfer_detail.status == 'INITED'):
            if (transfer_detail.branch_type=="FRS_STORE" and transfer_detail.shipping_store is not None and transfer_products):
                product_id_list = [int(transfer_product.product_id) for transfer_product in transfer_products]
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=transfer_detail.shipping_store, user_id=user_id, partner_id=partner_id, product_ids=product_id_list, include_sales_price=True)
                for product_item in transfer_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity, 'amount', 'sales_amount', product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
            transfer_d = self.return_repo.update_transfer_product(transfer_detail, transfer_products,
                                                                  remark, transfer_date, user_id, username,
                                                                  partner_id=partner_id)
            if transfer_d:
                return True
            else:
                raise ActionException('更新失败')
        else:
            raise ActionException('更新数据不合法')

    def submit_transfer(self, request, partner_id, user_id, username=None):
        transfer_id = request.transfer_id
        branch_type = request.branch_type
        transfer_obj = self.get_transfer_by_id(transfer_id, partner_id=partner_id)
        if not transfer_id:
            return None

        transfer_products= self.return_repo.list_transfer_product(transfer_id=transfer_id)
        transfer_products = [self.dict_to_object(i) for i in transfer_products]

        transfer_detail = self.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            # 库存单位转换
            transfer_products = self.update_product_unit(transfer_products, partner_id, user_id)
            if transfer_products:
                # 租户是否校验库存
                allow_neg_inv = metadata_service.get_business_extra_config(
                            partner_id=partner_id, user_id=user_id, domain="boh.store.transfer").get("allow_neg_inv")
                if allow_neg_inv and allow_neg_inv == True: # 允许负库存
                    pass
                else: # 校验库存
                    # 配置多仓位需要通过仓位来校验库存
                    if transfer_detail.shipping_position:
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                store_id=transfer_detail.shipping_store,
                                                                position_id=transfer_detail.shipping_position,
                                                                detail=True,
                                                                partner_id=partner_id, user_id=user_id)
                    else:
                        # 未配置多仓位还校验门店库存
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                store_id=transfer_detail.shipping_store,
                                                                partner_id=partner_id, user_id=user_id)
                    if len(out_product) > 0:
                        raise TransferInventoryException("商品库存不存在或超出库存:" + '\n' + ','.join(out_product))
        
        if transfer_detail is not None and transfer_detail.status == 'INITED':
            # # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作
            transfer_detail = self.return_repo.submit_transfer(transfer_id=transfer_id,
                                                               transfer_products=transfer_products,
                                                               user_id=user_id, username=username,
                                                               partner_id=partner_id)

            transfer_shipping_store_ret = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                                      branch_type=branch_type,
                                                                      partner_id=partner_id, user_id=user_id)
            transfer_receiving_store_code_ret = deepcopy(transfer_shipping_store_ret)
            transfer_shipping_store_name = transfer_shipping_store_ret.get('name')
            transfer_receiving_store_name = transfer_receiving_store_code_ret.get('name')

            # 调补提货
            TRANSFER_ACCOUNTS = []
            delivery_p_list = []
            rec_p_list = []
            for p in transfer_products:
                delivery_p = {
                    'delivery_by': int(transfer_obj.shipping_store), 
                    'product_id': int(p.product_id),
                    'product_code': p.product_code,
                    'product_name': p.product_name,
                    # 'storage_type': p.storage_type,
                    'unit_id': int(p.unit_id),
                    'unit_name': p.unit_name,
                    'unit_spec': p.unit_spec,
                    'delivery_quantity':p.accounting_quantity
                    # 'category_id':product.category_id
                }
                delivery_p_list.append(delivery_p)

                to_branch_id = transfer_detail.shipping_store
                TRANSFER_ACCOUNTS.append(dict(from_branch_id=transfer_detail.shipping_store,
                                                to_branch_id=to_branch_id,
                                                from_sub_account=transfer_detail.shipping_position,
                                                to_sub_account=transfer_detail.shipping_position,
                                                product_id=p.product_id,
                                                amount=str(abs(
                                                    p.accounting_quantity)) if p.accounting_quantity else 0
                                                )
                                            )

            # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
            # 承接sub_type不为INTERNAL,防止数据空跑
            out_batch_no = transfer_id
            if TRANSFER_ACCOUNTS:
                description = 'EXTERNAL_TRANSFER' if transfer_detail.sub_type == 'EXTERNAL' else 'INTERNAL_TRANSFER'
                code = description
                transfer_mode_deliver = 0  # 调拨发货模式
                message = dict(batch_no=str(out_batch_no),
                            code=code,
                            action=3,
                            description=description,
                            trace_id=transfer_obj.code,
                            accounts=TRANSFER_ACCOUNTS,
                            partner_id=partner_id,
                            user_id=user_id,
                            business_time=datetime.utcnow(),
                            transfer_mode=transfer_mode_deliver)
                inventory_dict = dict(batch_no=str(out_batch_no), code=code, batch_action=3,
                                    action_dec=ACTION[3],
                                    batch_id=None,
                                    status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                    )
                inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                partner_id=partner_id, user_id=user_id, trace_id=str(transfer_id))

                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                    message=message)

            # 状态变更清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=transfer_detail.shipping_store,
                                             doc_type="transfer"))

            # PDA消息推送
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=transfer_detail.shipping_store,
                                                  source_root_id=transfer_id,
                                                  source_id=transfer_id,
                                                  source_type="TRANSFER",
                                                  action="SUBMITTED",
                                                  ref_source_id=transfer_id,
                                                  ref_source_type="TRANSFER",
                                                  ref_action="INITED",
                                                  content={"transfer_date": str(transfer_detail.transfer_date),
                                                           "updated_at": str(datetime.now()),
                                                           "updated_by": str(user_id),
                                                           "remark": transfer_detail.remark,
                                                           "updated_name": username,
                                                           "shipping_store_name": transfer_shipping_store_name,
                                                           "receiving_store_name": transfer_shipping_store_name
                                                           }
                                                  )

            # ---ForReceiptStart---#
            main_branch_type = None
            if transfer_obj.branch_type == 'STORE':
                main_branch_type = 'S'
            elif transfer_obj.branch_type == 'WAREHOUSE':
                main_branch_type = 'W'
            elif transfer_obj.branch_type == 'MACHINING_CENTER':
                main_branch_type = 'M'
            # 同步到receipt里的发货
            receipt_service.create_deliverys(
                batch_id=transfer_id, batch_code=transfer_obj.code, batch_type='TRANSFER', 
                order_id=transfer_id, order_code=transfer_obj.code,
                demand_id=transfer_id, demand_code=transfer_obj.code,  
                receive_by=int(transfer_obj.receiving_store), delivery_by=int(transfer_obj.shipping_store), 
                distr_type='NMD', main_branch_type=main_branch_type,
                delivery_date=transfer_obj.transfer_date, demand_date=transfer_obj.transfer_date, 
                products=delivery_p_list, 
                partner_id=partner_id, user_id=user_id)
        
            # vendor单据同步给三方
            message = {
                                    'doc_resource': main_branch_type.lower() +'_transfer',
                                    'doc_id': transfer_id,
                                    'partner_id': partner_id,
                                    'user_id': user_id,
                                    'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                }
            mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)
                
            # 同步记录落表，用于后续补偿
            tp_trans_log = {
                                'id': out_batch_no,
                                'doc_code': transfer_detail.code,
                                'doc_type': 's_transfer' if transfer_detail.branch_type !='FRS_STORE' else 'fs_transfer',
                                'status': 'inited',
                                'msg': str(message),
                                'partner_id': partner_id,
                                'created_by': user_id,
                                'created_at': datetime.utcnow(),
                                'updated_at': datetime.utcnow()
                            }
            TpTransLogModel.create_logs_list([tp_trans_log])
        return transfer_detail

    def cancel_transfer(self, request, partner_id, user_id, username=None):
        """取消调拨单"""
        transfer_id = request.transfer_id
        if not transfer_id:
            return None
        transfer_detail = self.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if transfer_detail.status != 'INITED':
            raise Exception("只有新建状态单据才能取消！")
        transfer_detail = self.return_repo.cancel_transfer(transfer_id=transfer_id, user_id=user_id,
                                                           partner_id=partner_id, username=username)
        # 取消清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=transfer_detail.shipping_store,
                                         doc_type="transfer"))
        return transfer_detail
    
    def auto_cancel_transfer(self, transfer_id, partner_id, user_id):
        """取消调拨单"""
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        transfer_detail = self.return_repo.cancel_transfer(transfer_id=transfer_id, user_id=user_id,
                                                           partner_id=partner_id, username=username)
        return transfer_detail

    def check_confirmed_product_inventory(self, transfer_products, store_id, partner_id, user_id, position_id=None,
                                          detail=False):
        out_product = []
        if position_id:
            sub_account_ids = [position_id]
        else:
            sub_account_ids = []
        product_ids = [p.product_id for p in transfer_products]
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:

            store_product_inventory_map = inventory_service.get_products_inventory_by_branch_id(branch_id=store_id,
                                                                                                partner_id=partner_id,
                                                                                                user_id=user_id,
                                                                                                detail=detail,
                                                                                                sub_account_ids=sub_account_ids,
                                                                                                product_ids=product_ids)
            # 校验库存
            for transfer_product in transfer_products:
                if position_id:
                    product_inventory = store_product_inventory_map.get(
                        str(position_id) + str(transfer_product.product_id))
                else:
                    product_inventory = store_product_inventory_map.get(str(transfer_product.product_id))
                if product_inventory:
                    # + float(product_inventory.quantity_lock))
                    if transfer_product.accounting_received_quantity != None and product_inventory.get(
                            'quantity_avail') != None:
                        if float(transfer_product.accounting_received_quantity) > float(
                                product_inventory.get('quantity_avail')):
                            out_product.append(str(transfer_product.product_id))
                else:
                    out_product.append(str(transfer_product.product_id))
        return out_product

    def confirmed_transfer(self, request, partner_id, user_id, username=None):
        receiver = request.receiver
        receiving_store = request.receiving_store
        transfer_id = request.transfer_id
        branch_type = request.branch_type
        transfer_obj = self.get_transfer_by_id(transfer_id, partner_id=partner_id)
        transfer_products = []
        # 调拨确认数量默认调出数量
        if request.products:
            transfer_products = self.get_request_products_dict(request.products)
        if receiver is None:
            receiver = user_id
        transfer_detail = self.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            # 库存单位转换
            transfer_products = self.update_confirmed_product_unit(transfer_products, partner_id, user_id)
            if transfer_products:
                # 租户是否校验库存
                allow_neg_inv = metadata_service.get_business_extra_config(
                            partner_id=partner_id, user_id=user_id, domain="boh.store.transfer").get("allow_neg_inv")
                if allow_neg_inv and allow_neg_inv is True:
                    # 允许负库存
                    pass
                else:
                    # 校验库存
                    # 配置多仓位需要通过仓位来校验库存
                    if transfer_detail.shipping_position:
                        out_product = self.check_confirmed_product_inventory(transfer_products=transfer_products,
                                                                             store_id=transfer_detail.shipping_store,
                                                                             position_id=transfer_detail.shipping_position,
                                                                             detail=True,
                                                                             partner_id=partner_id, user_id=user_id)
                    else:
                        # 未配置多仓位还校验门店库存
                        out_product = self.check_confirmed_product_inventory(transfer_products=transfer_products,
                                                                             store_id=transfer_detail.shipping_store,
                                                                             partner_id=partner_id, user_id=user_id)
                    if len(out_product) > 0:
                        pass
                        # raise TransferInventoryException("调出门店的商品库存不存在或超出库存:" +'\n'+ ','.join(out_product))
        if transfer_detail is not None and transfer_detail.status == 'SUBMITTED':
            # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作
            # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作
            # transfer_product_list = self.list_transfer_product(transfer_id=transfer_detail.transfer_id)
            # # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作

            transfer_shipping_store_ret = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                                      branch_type=branch_type,
                                                                      partner_id=partner_id, user_id=user_id)
            transfer_shipping_store_code = transfer_shipping_store_ret.get('code')
            transfer_shipping_store_name = transfer_shipping_store_ret.get('name')
            transfer_receiving_store_code_ret = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
                                                                            branch_type=branch_type,
                                                                            partner_id=partner_id, user_id=user_id)
            transfer_receiving_store_code = transfer_receiving_store_code_ret.get('code')
            transfer_receiving_store_name = transfer_receiving_store_code_ret.get('name')

            TRANSFER_ACCOUNTS = []
            rec_p_list = []
            for p in transfer_products:
                rec_p = {
                    'receive_by': int(transfer_obj.receiving_store), 
                    'product_id': int(p.product_id),
                    'product_code': p.product_code,
                    'product_name': p.product_name,
                    # 'storage_type': product.storage_type,
                    'unit_id': int(p.unit_id),
                    'unit_name': p.unit_name,
                    'unit_spec': p.unit_spec,
                    'delivery_quantity':p.accounting_received_quantity,
                    'receive_quantity':p.accounting_received_quantity
                    # 'category_id':product.category_id
                }
                rec_p_list.append(rec_p)

                to_branch_id = transfer_detail.receiving_store
                TRANSFER_ACCOUNTS.append(dict(from_branch_id=transfer_detail.shipping_store,
                                              to_branch_id=to_branch_id,
                                              from_sub_account=transfer_detail.shipping_position,
                                              to_sub_account=transfer_detail.receiving_position,
                                              product_id=p.product_id,
                                              amount=str(abs(
                                                  p.accounting_received_quantity)) if p.accounting_received_quantity else 0
                                              )
                                         )

            # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
            description = 'EXTERNAL_TRANSFER' if transfer_detail.sub_type == 'EXTERNAL' else 'INTERNAL_TRANSFER'
            code = description
            transfer_mode_deliver = 1  # 调拨收货模式
            in_batch_no = transfer_detail.id
            message = dict(batch_no=str(in_batch_no),
                           code=code,
                           action=3,
                           description=description,
                           trace_id=transfer_obj.code,
                           accounts=TRANSFER_ACCOUNTS,
                           partner_id=partner_id,
                           user_id=user_id,
                           business_time=datetime.utcnow(),
                           transfer_mode=transfer_mode_deliver)
            inventory_dict = dict(batch_no=str(in_batch_no), code=code, batch_action=3,
                                  action_dec=ACTION[3],
                                  batch_id=None,
                                  status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                  )
            inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                             partner_id=partner_id, user_id=user_id, trace_id=str(transfer_id))

            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                message=message)

            transfer_detail = self.return_repo.confirmed_transfer(transfer_id=transfer_id, receiver=receiver,
                                                                  receiving_store=receiving_store,
                                                                  transfer_products=transfer_products,
                                                                  user_id=user_id, username=username,
                                                                  partner_id=partner_id)
            # 状态变更清理接受门店待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=transfer_detail.receiving_store,
                                             doc_type="transfer"))
            # 记录工作流日志
            flow_log_detail = dict(
                doc_id=transfer_detail.transfer_id,
                doc_type="insideTransfer" if transfer_detail.sub_type == "INTERNAL" else "transfer",
                operation="INNER_TRANSFER" if transfer_detail.sub_type == "EXTERNAL" else "MATERIAL_CONVERT",
                branch_id=transfer_detail.receiving_store if transfer_detail.sub_type == "EXTERNAL" else transfer_detail.shipping_store,
                sub_branch_id=transfer_detail.receiving_position,
                sub_doc_type='position' if transfer_detail.receiving_position else 'store',
                posterior_operation='MATERIAL_CONVERT' if transfer_detail.sub_type == "EXTERNAL" else "",
                process_status='INITED',
                partner_id=partner_id,
                created_by=user_id,
                created_at=datetime.utcnow(),
                created_name=username
            )
            flow_log_db = ReceiptFlowLogModel.create_flow_logs(**flow_log_detail)

            message = {
                'flow_id': flow_log_db.id,
                'partner_id': partner_id,
                'user_id': user_id
            }
            logger.info("调拨工作流触发:{}".format(flow_log_db.id))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.HANDLE_FLOW_TASK,
                                message=message)
            
            # vendor单据同步给三方
            message = {
                                'doc_resource': 's_transfer' if transfer_detail.branch_type !='FRS_STORE' else 'fs_transfer',
                                'doc_id': transfer_id,
                                'partner_id': partner_id,
                                'user_id': user_id,
                                'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
            mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)
            
            # 同步记录落表，用于后续补偿
            tp_trans_log = {
                            'id': in_batch_no,
                            'doc_code': transfer_obj.code,
                            'doc_type': 's_transfer' if transfer_obj.branch_type !='FRS_STORE' else 'fs_transfer',
                            'status': 'inited',
                            'msg': str(message),
                            'partner_id': partner_id,
                            'created_by': user_id,
                            'created_at': datetime.utcnow(),
                            'updated_at': datetime.utcnow()
                        }
            TpTransLogModel.create_logs_list([tp_trans_log])


            # ---ForReceiptStart---#
            main_branch_type = None
            if transfer_obj.branch_type == 'STORE':
                main_branch_type = 'S'
            elif transfer_obj.branch_type == 'WAREHOUSE':
                main_branch_type = 'W'
            elif transfer_obj.branch_type == 'MACHINING_CENTER':
                main_branch_type = 'M'

            # 获取delivery_res.id
            delivery_res = receipt_service.list_deliverys(batch_id=transfer_id,partner_id=partner_id, user_id=user_id)
            delivery_res = MessageToDict(delivery_res).get('rows')
            if delivery_res:
                delivery_res = delivery_res[0]
                # 同步到receipt里的收货
                receipt_service.create_receives(
                    batch_id=transfer_id, batch_code=transfer_obj.code, batch_type='TRANSFER',  
                    order_id=transfer_id, order_code=transfer_obj.code,  
                    delivery_id=int(delivery_res['id']), 
                    receive_by=int(transfer_obj.receiving_store), delivery_by=int(transfer_obj.shipping_store),
                    distr_type='NMD', main_branch_type=main_branch_type,
                    delivery_date=transfer_obj.transfer_date, demand_date=transfer_obj.transfer_date, 
                    products=rec_p_list, partner_id=partner_id, user_id=user_id)
            else:
                raise ActionException('确认失败')

            # PDA消息推送
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=transfer_detail.receiving_store,
                                                  source_root_id=transfer_id,
                                                  source_id=transfer_id,
                                                  source_type="TRANSFER",
                                                  action="CONFIRMED",
                                                  ref_source_id=transfer_id,
                                                  ref_source_type="TRANSFER",
                                                  ref_action="SUBMITTED",
                                                  content={"transfer_date": str(transfer_detail.transfer_date),
                                                           "updated_at": str(datetime.now()),
                                                           "updated_by": str(user_id),
                                                           "remark": transfer_detail.remark,
                                                           "updated_name": username,
                                                           "shipping_store_name": transfer_shipping_store_name,
                                                           "receiving_store_name": transfer_receiving_store_name
                                                           }
                                                  )
        else:
            raise ActionException('确认失败')
        return transfer_detail

    def delete_transfer_product(self, transfer_product_id, partner_id, user_id):
        transfer_obj = self.return_repo.get_transfer_by_product_id(transfer_product_id, partner_id=partner_id)
        if transfer_obj:
            if transfer_obj.status == 'INITED':
                ret = self.return_repo.delete_transfer_by_product(ids=transfer_product_id, partner_id=partner_id)
                self.transfer_repo.recalculate_transfer_total_amount(transfer_id=transfer_obj.id)
                return ret
            else:
                raise DataValidationException("status must be inited")
        raise DataValidationException('data not find')

    def delete_transfer(self, transfer_id, partner_id, user_id):
        transfer_obj = self.return_repo.get_transfer_by_id(transfer_id, partner_id=partner_id)
        if transfer_id and transfer_obj:
            if transfer_obj.status == 'INITED':
                return self.return_repo.delete_transfer(transfer_id, partner_id=partner_id)
            else:
                raise DataValidationException("status must be inited")
        raise DataValidationException('data not find')

    def update_transfer_status(self, transfer_id, user_id=None, partner_id=None):
        return self.return_repo.update_transfer_status(transfer_id=transfer_id, user_id=user_id, partner_id=partner_id)

    def bi_get_transfer_collect(self, partner_id, user_id, st_ids=None, category_ids=None, product_name=None,
                                start_date=None, end_date=None, limit=None, offset=None, include_total=False,
                                is_in=False, store_ids=None, order=None, sort=None, branch_type=None, sub_type=None,
                                transfer_type=None, cross_company=None):
        product_ids = []
        if (category_ids and isinstance(category_ids, list) and len(category_ids) > 0) or product_name:
            relation_filters = dict()
            if category_ids and isinstance(category_ids, list) and len(category_ids) > 0:
                relation_filters['product_category'] = [str(category_id) for category_id in category_ids]
            search, search_fields = None, None
            if product_name:
                search = str(product_name)
                search_fields = 'name'
            list_products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                  return_fields='id,code,name',
                                                                  search=search, search_fields=search_fields,
                                                                  include_units=True,
                                                                  partner_id=partner_id, user_id=user_id
                                                                  )
            list_products = []
            if list_products_ret:
                list_products = list_products_ret['rows']
            # list_products = hex_api.get_service('product').list(partner_id, querystring=querystring)
            if not isinstance(list_products, list) or len(list_products) == 0:
                return None, 0, 0, 0, 0
            for product in list_products:
                product_ids.append(convert_to_int(product['id']))
        return self.return_repo.bi_get_transfer_collect(partner_id, user_id=user_id, st_ids=st_ids,
                                                        product_ids=product_ids, start_date=start_date,
                                                        end_date=end_date, is_in=is_in, limit=limit, offset=offset,
                                                        include_total=include_total, store_ids=store_ids,
                                                        order=order, sort=sort, branch_type=branch_type,
                                                        sub_type=sub_type, transfer_type=transfer_type,
                                                        cross_company=cross_company)

    def bi_get_transfer_detailed(self, partner_id, user_id, st_ids=None, category_ids=None, product_name=None,
                                 start_date=None, end_date=None, limit=None, offset=None, include_total=False,
                                 is_in=False, store_ids=None, order=None, sort=None, branch_type=None, sub_type=None,
                                 transfer_type=None, cross_company=None):
        product_ids = []
        if (category_ids and isinstance(category_ids, list) and len(category_ids) > 0) or product_name:
            relation_filters = dict()
            if category_ids and isinstance(category_ids, list) and len(category_ids) > 0:
                relation_filters['product_category'] = [str(category_id) for category_id in category_ids]
            search, search_fields = None, None
            if product_name:
                search = str(product_name)
                search_fields = 'name'
            list_products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                  return_fields='id,code,name',
                                                                  search=search, search_fields=search_fields,
                                                                  include_units=True,
                                                                  partner_id=partner_id, user_id=user_id
                                                                  )
            list_products = []
            if list_products_ret:
                list_products = list_products_ret['rows']
            if not isinstance(list_products, list) or len(list_products) == 0:
                return None, 0, 0, 0, 0
            for product in list_products:
                product_ids.append(convert_to_int(product['id']))
        return self.return_repo.bi_get_transfer_detailed(partner_id, user_id=user_id, st_ids=st_ids,
                                                         product_ids=product_ids, start_date=start_date,
                                                         end_date=end_date, is_in=is_in, limit=limit, offset=offset,
                                                         include_total=include_total, store_ids=store_ids, order=order,
                                                         sort=sort, branch_type=branch_type, sub_type=sub_type,
                                                         transfer_type=transfer_type, cross_company=cross_company)

    def get_un_confirm_transfer_by_transfer_date(self, start_date, end_date, partner_id, status=None, branch_type=None):
        if status and  isinstance(status, list):
            status = ','.join(["\'"+str(s)+"\'" for s in status])
        else:
            status = "SUBMITTED"
        sql_text = '''
            SELECT transfer_id, status FROM supply_transfer_detail
             WHERE status in ({})
                   and transfer_date >='{}'
                   and transfer_date <='{}'
                   and partner_id={}
         '''.format(status, start_date, end_date, partner_id)
        if branch_type:
            sql_text += "and branch_type={}".format(branch_type)
        sql_text += " ;"
        with DummyTransaction(auto_commit=False) as trans:
            print(sql_text)
            rows = trans.scope_session.execute(text(sql_text)).fetchall()
        return rows

    def auto_confirm_transfer(self, transfer_id, partner_id, user_id):

        transfer_obj = self.get_transfer_by_id(transfer_id, partner_id=partner_id)
        transfer_products = []
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        request_product = self.return_repo.list_transfer_product(partner_id=partner_id, transfer_id=transfer_id)
        for p in request_product:
            product_obj = TransferProduct()
            product_obj.confirmed_received_quantity = p.get('accounting_quantity', 0)
            product_obj.product_id = p.get('product_id', 0)
            product_obj.unit_id = p.get('unit_id', 0)
            transfer_products.append(product_obj)
        transfer_detail = self.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            # 库存单位转换
            transfer_products = self.update_confirmed_product_unit(transfer_products, partner_id, user_id)
            # raise TransferInventoryException("调出门店的商品库存不存在或超出库存:" +'\n'+ ','.join(out_product))
        if transfer_detail is not None and transfer_detail.status == 'SUBMITTED':
            transfer_shipping_store_ret = metadata_service.get_store(store_id=transfer_detail.shipping_store,
                                                                     return_fields='code,name',
                                                                     partner_id=partner_id, user_id=user_id
                                                                     )
            transfer_shipping_store_code = transfer_shipping_store_ret.get('code')
            transfer_shipping_store_name = transfer_shipping_store_ret.get('name')
            transfer_receiving_store_code_ret = metadata_service.get_store(store_id=transfer_detail.receiving_store,
                                                                           return_fields='code,name',
                                                                           partner_id=partner_id, user_id=user_id
                                                                           )
            transfer_receiving_store_code = transfer_receiving_store_code_ret.get('code')
            transfer_receiving_store_name = transfer_receiving_store_code_ret.get('name')
            message = {
                "transfer_code": transfer_detail.code,
                "transfer_shipping_store_code": transfer_shipping_store_code,
                "transfer_receiving_store_code": transfer_receiving_store_code,
                "transfer_receiving_date": datetime.now().strftime("%Y-%m-%d"),
                'transfer_products': []
            }
            TRANSFER_ACCOUNTS = []
            rec_p_list = []
            for p in transfer_products:
                rec_p = {
                    'receive_by': int(transfer_obj.receiving_store), 
                    'product_id': int(p.product_id),
                    'product_code': p.product_code,
                    'product_name': p.product_name,
                    # 'storage_type': product.storage_type,
                    'unit_id': int(p.unit_id),
                    'unit_name': p.unit_name,
                    'unit_spec': p.unit_spec,
                    'delivery_quantity':p.accounting_received_quantity,
                    'receive_quantity':p.accounting_received_quantity
                    # 'category_id':product.category_id
                }
                rec_p_list.append(rec_p)
                to_branch_id = transfer_detail.receiving_store
                TRANSFER_ACCOUNTS.append(dict(from_branch_id=transfer_detail.shipping_store,
                                              to_branch_id=to_branch_id,
                                              from_sub_account=transfer_detail.shipping_position,
                                              to_sub_account=transfer_detail.receiving_position,
                                              product_id=p.product_id,
                                              amount=str(abs(
                                                  p.accounting_received_quantity)) if p.accounting_received_quantity else 0
                                              )
                                         )

            # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
            description = 'EXTERNAL_TRANSFER' if transfer_detail.sub_type == 'EXTERNAL' else 'INTERNAL_TRANSFER'
            code = description
            transfer_mode_deliver = 1  # 调拨收货模式
            in_batch_no = transfer_detail.id
            message = dict(batch_no=str(in_batch_no),
                           code=code,
                           action=3,
                           description=description,
                           trace_id=transfer_obj.code,
                           accounts=TRANSFER_ACCOUNTS,
                           partner_id=partner_id,
                           user_id=user_id,
                           business_time=datetime.utcnow(),
                           transfer_mode=transfer_mode_deliver)
            inventory_dict = dict(batch_no=str(in_batch_no), code=code, batch_action=3,
                                  action_dec=ACTION[3],
                                  batch_id=None,
                                  status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                  )
            inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                             partner_id=partner_id, user_id=user_id)

            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                message=message)

            transfer_detail = self.return_repo.confirmed_transfer(transfer_id=transfer_id, receiver=user_id,
                                                                  receiving_store=transfer_obj.receiving_store,
                                                                  transfer_products=transfer_products,
                                                                  user_id=user_id, username=username, auto_confirm=True,
                                                                  partner_id=partner_id)
            # PDA消息推送
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=transfer_detail.shipping_store,
                                                  source_root_id=transfer_id,
                                                  source_id=transfer_id,
                                                  source_type="TRANSFER",
                                                  action="CONFIRMED",
                                                  ref_source_id=transfer_id,
                                                  ref_source_type="TRANSFER",
                                                  ref_action="SUBMITTED",
                                                  content={"transfer_date": str(transfer_detail.transfer_date),
                                                           "updated_at": str(datetime.now()),
                                                           "updated_by": str(user_id),
                                                           "remark": transfer_detail.remark,
                                                           "updated_name": username,
                                                           "shipping_store_name": transfer_shipping_store_name,
                                                           "receiving_store_name": transfer_receiving_store_name
                                                           }
                                                  )
            # PDA消息推送
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=transfer_detail.receiving_store,
                                                  source_root_id=transfer_id,
                                                  source_id=transfer_id,
                                                  source_type="TRANSFER",
                                                  action="CONFIRMED",
                                                  ref_source_id=transfer_id,
                                                  ref_source_type="TRANSFER",
                                                  ref_action="SUBMITTED",
                                                  content={"transfer_date": str(transfer_detail.transfer_date),
                                                           "updated_at": str(datetime.now()),
                                                           "updated_by": str(user_id),
                                                           "remark": transfer_detail.remark,
                                                           "updated_name": username,
                                                           "shipping_store_name": transfer_shipping_store_name,
                                                           "receiving_store_name": transfer_receiving_store_name
                                                           }
                                                  )
        else:
            raise ActionException('确认失败')
        return transfer_detail

    def create_inner_transfer(self, request, partner_id=None, user_id=None, username=None):
        """专门用来创建自动内部调拨单
        type: 自动-AUTO
        sub_type: 内部-INTERNAL
        sub_account_type = "position" # 标记开启多仓位子账户功能用于库存扣减判断
        """
        data = {}
        request_id = request.request_id
        branch_type = request.branch_type
        if not request_id:
            raise NoRequestIDError('没有请求ID')
        # 验证该请求是否已创建
        transfer_d = self.return_repo.get_transfer_by_request_id(request_id)
        if transfer_d:
            return transfer_d.transfer_id
        transfer_detail = TransferDetail()
        for name in dir(transfer_detail):
            if not name.startswith('__') and not name.startswith('_'):
                if hasattr(request, name):
                    r_value = getattr(request, name)
                    if not callable(r_value):
                        if isinstance(r_value, Timestamp):
                            data[name] = datetime.fromtimestamp(r_value.seconds)
                        else:
                            data[name] = r_value
        set_model_from_props(data, transfer_detail)
        transfer_products = []
        if request.products:
            transfer_products = self.get_request_products_dict(request.products)
        if not transfer_detail.shipping_store:
            raise DataValidationException('shipping_store is required')

        # 兼容仓库/加工中心处理
        if branch_type == "WAREHOUSE":
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
            # warehouse_scope_check(transfer_detail.shipping_store, partner_id, user_id)
        elif branch_type == "MACHINING_CENTER":
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
        else:
            # store_scope_check(transfer_detail.shipping_store, partner_id, user_id)
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
        # 拉取仓位主档
        position_map = get_position_list_map_by_branch_id(branch_id=transfer_detail.shipping_store,
                                                          branch_type=transfer_detail.branch_type,
                                                          partner_id=partner_id, user_id=user_id)

        receiving_position_name = position_map.get(str(transfer_detail.receiving_position), {}).get('name')
        shipping_position_name = position_map.get(str(transfer_detail.shipping_position), {}).get('name')
        if not transfer_detail.receiving_position_name:
            transfer_detail.receiving_position_name = receiving_position_name
        if not transfer_detail.shipping_position_name:
            transfer_detail.shipping_position_name = shipping_position_name
        transfer_detail.shipping_store_name = shipping_store.get('name')
        transfer_detail.partner_id = partner_id
        if not transfer_detail.transfer_date:
            transfer_detail.transfer_date = datetime.utcnow()
        if not transfer_detail.shipper:
            transfer_detail.shipper = user_id
        if transfer_detail.shipping_date:
            transfer_detail.shipping_date = datetime.utcnow()

        transfer_detail.created_by = user_id
        transfer_detail.created_at = datetime.utcnow()
        transfer_detail.status = 'CONFIRMED'
        transfer_detail.process_status = 'INITED'
        if not transfer_detail.code:
            transfer_detail.code = Supply_doc_code.get_code_by_type(
                'STORE_TRS', partner_id, None)
        if transfer_detail.code:
            transfer_detail.transfer_order_number = convert_to_int(transfer_detail.code)
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            # 库存单位转换
            transfer_products = self.update_product_unit(transfer_products, partner_id, user_id)
            # 添加调拨收货数量
            for p in transfer_products:
                p.accounting_received_quantity = p.accounting_quantity
            if (transfer_detail.branch_type=="FRS_STORE" and transfer_detail.shipping_store is not None and transfer_products):
                product_id_list = [int(transfer_product.product_id) for transfer_product in transfer_products]
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=transfer_detail.shipping_store, user_id=user_id, partner_id=partner_id, product_ids=product_id_list, include_sales_price=True)
                for product_item in transfer_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity, 'amount', 'sales_amount', product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
            transfer_d = self.return_repo.create_update_transfer(transfer_detail, transfer_products, partner_id,
                                                                 user_id, username)
            transfer_accounts = []
            for p in transfer_products:
                transfer_accounts.append(dict(from_branch_id=transfer_d.shipping_store,
                                              to_branch_id=transfer_d.shipping_store,
                                              from_sub_account=transfer_d.shipping_position,
                                              to_sub_account=transfer_d.receiving_position,
                                              product_id=p.product_id,
                                              amount=str(abs(
                                                  p.accounting_received_quantity)) if p.accounting_received_quantity else 0,
                                              ),
                                         )

            # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
            message = dict(batch_no=str(transfer_d.transfer_id),
                           code='INTERNAL_TRANSFER',
                           action=3,
                           description='INTERNAL_TRANSFER',
                           trace_id=transfer_d.code,
                           accounts=transfer_accounts,
                           partner_id=partner_id,
                           user_id=user_id,
                           business_time=datetime.utcnow())
            inventory_dict = dict(batch_no=str(transfer_d.transfer_id), code="INTERNAL_TRANSFER", batch_action=3,
                                  action_dec=ACTION[3],
                                  batch_id=None,
                                  status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                  )
            inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                             partner_id=partner_id, user_id=user_id)

            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                message=message)

            # 记录工作流日志
            flow_log_detail = dict(
                doc_id=transfer_d.transfer_id,
                doc_type="insideTransfer",
                operation="MATERIAL_CONVERT",   # 内部调拨只能触发自动物料转换
                branch_id=transfer_d.shipping_store,
                sub_branch_id=transfer_d.receiving_position,
                sub_doc_type='position',
                posterior_operation="",
                process_status='INITED',
                partner_id=partner_id,
                created_by=user_id,
                created_at=datetime.utcnow(),
                created_name=username
            )
            flow_log_db = ReceiptFlowLogModel.create_flow_logs(**flow_log_detail)

            message = {
                'flow_id': flow_log_db.id,
                'partner_id': partner_id,
                'user_id': user_id
            }
            logger.info("自动内部调拨工作流触发:{}".format(flow_log_db.id))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.HANDLE_FLOW_TASK,
                                message=message)

            return transfer_d.transfer_id

    def dict_to_object(self, dictObj):
        if not isinstance(dictObj, dict):
            return dictObj
        inst=Dict()
        for k,v in dictObj.items():
            inst[k] = self.dict_to_object(v)
        return inst

    def confirmed_transfer_by_code(self, request, partner_id, user_id, username=None):
        
        transfer_code = request.transfer_code    
        res = {
            "code": transfer_code,
            "status": "FAILED"
        }
        transfer_obj = self.return_repo.get_transfer_by_code(transfer_code, is_details=True, partner_id=partner_id)
        if not transfer_obj:
            res["failed_code"] = "1"
            res["msg"]: "没有对应的调拨单"
            return res
        transfer_id = transfer_obj.transfer_id
        self.auto_confirm_transfer(transfer_id=transfer_id, partner_id=partner_id, user_id=user_id)

        # branch_type = transfer_obj.branch_type
        # receiver = transfer_obj.receiver
        # receiving_store = transfer_obj.receiving_store
        
        # transfer_products = []
        # # 调拨确认数量默认调出数量
        # if request.products:
        #     transfer_products = self.get_request_products_dict(request.products)
        # if receiver is None:
        #     receiver = user_id
        # transfer_detail = self.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        # if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
        #     # 库存单位转换
        #     transfer_products = self.update_confirmed_product_unit(transfer_products, partner_id, user_id)
        #     if transfer_products:
        #         # 租户是否校验库存
        #         allow_neg_inv = metadata_service.get_business_extra_config(
        #                     partner_id=partner_id, user_id=user_id, domain="boh.store.transfer").get("allow_neg_inv")
        #         if allow_neg_inv and allow_neg_inv == True: # 允许负库存
        #             pass
        #         else: # 校验库存
        #             # 配置多仓位需要通过仓位来校验库存
        #             if transfer_detail.shipping_position:
        #                 out_product = self.check_confirmed_product_inventory(transfer_products=transfer_products,
        #                                                                     store_id=transfer_detail.shipping_store,
        #                                                                     position_id=transfer_detail.shipping_position,
        #                                                                     detail=True,
        #                                                                     partner_id=partner_id, user_id=user_id)
        #             else:
        #                 # 未配置多仓位还校验门店库存
        #                 out_product = self.check_confirmed_product_inventory(transfer_products=transfer_products,
        #                                                                     store_id=transfer_detail.shipping_store,
        #                                                                     partner_id=partner_id, user_id=user_id)
        #             if len(out_product) > 0:
        #                 pass
        #                 # raise TransferInventoryException("调出门店的商品库存不存在或超出库存:" +'\n'+ ','.join(out_product))
        # if transfer_detail is not None and transfer_detail.status == 'SUBMITTED':
        #     # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作
        #     # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作
        #     # transfer_product_list = self.list_transfer_product(transfer_id=transfer_detail.transfer_id)
        #     # # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作

        #     transfer_shipping_store_ret = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
        #                                                               branch_type=branch_type,
        #                                                               partner_id=partner_id, user_id=user_id)
        #     transfer_shipping_store_code = transfer_shipping_store_ret.get('code')
        #     transfer_shipping_store_name = transfer_shipping_store_ret.get('name')
        #     transfer_receiving_store_code_ret = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
        #                                                                     branch_type=branch_type,
        #                                                                     partner_id=partner_id, user_id=user_id)
        #     transfer_receiving_store_code = transfer_receiving_store_code_ret.get('code')
        #     transfer_receiving_store_name = transfer_receiving_store_code_ret.get('name')

        #     TRANSFER_ACCOUNTS = []
        #     rec_p_list = []
        #     for p in transfer_products:
        #         rec_p = {
        #             'receive_by': int(transfer_obj.receiving_store), 
        #             'product_id': int(p.product_id),
        #             'product_code': p.product_code,
        #             'product_name': p.product_name,
        #             # 'storage_type': product.storage_type,
        #             'unit_id': int(p.unit_id),
        #             'unit_name': p.unit_name,
        #             'unit_spec': p.unit_spec,
        #             'delivery_quantity':p.accounting_received_quantity,
        #             'receive_quantity':p.accounting_received_quantity
        #             # 'category_id':product.category_id
        #         }
        #         rec_p_list.append(rec_p)

        #         to_branch_id = transfer_detail.receiving_store
        #         TRANSFER_ACCOUNTS.append(dict(from_branch_id=transfer_detail.shipping_store,
        #                                       to_branch_id=to_branch_id,
        #                                       from_sub_account=transfer_detail.shipping_position,
        #                                       to_sub_account=transfer_detail.receiving_position,
        #                                       product_id=p.product_id,
        #                                       amount=str(abs(
        #                                           p.accounting_received_quantity)) if p.accounting_received_quantity else 0
        #                                       )
        #                                  )

        #     # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
        #     description = 'EXTERNAL_TRANSFER' if transfer_detail.sub_type == 'EXTERNAL' else 'INTERNAL_TRANSFER'
        #     code = description
        #     transfer_mode_deliver = 1  # 调拨收货模式
        #     in_batch_no = transfer_detail.id
        #     message = dict(batch_no=str(in_batch_no),
        #                    code=code,
        #                    action=3,
        #                    description=description,
        #                    trace_id=transfer_obj.code,
        #                    accounts=TRANSFER_ACCOUNTS,
        #                    partner_id=partner_id,
        #                    user_id=user_id,
        #                    business_time=datetime.utcnow(),
        #                    transfer_mode=transfer_mode_deliver)
        #     inventory_dict = dict(batch_no=str(in_batch_no), code=code, batch_action=3,
        #                           action_dec=ACTION[3],
        #                           batch_id=None,
        #                           status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
        #                           )
        #     inventory_repository.save_calculate_send_message(inventory_dict, message,
        #                                                      partner_id=partner_id, user_id=user_id, trace_id=str(transfer_id))

        #     mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
        #                         topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
        #                         message=message)

        #     transfer_detail = self.return_repo.confirmed_transfer(transfer_id=transfer_id, receiver=receiver,
        #                                                           receiving_store=receiving_store,
        #                                                           transfer_products=transfer_products,
        #                                                           user_id=user_id, username=username,
        #                                                           partner_id=partner_id)
        #     # 状态变更清理接受门店待办缓存
        #     mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
        #                         topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
        #                         message=dict(partner_id=partner_id, store_id=transfer_detail.receiving_store,
        #                                      doc_type="transfer"))
        #     # 记录工作流日志
        #     flow_log_detail = dict(
        #         doc_id=transfer_detail.transfer_id,
        #         doc_type="insideTransfer" if transfer_detail.sub_type == "INTERNAL" else "transfer",
        #         operation="INNER_TRANSFER" if transfer_detail.sub_type == "EXTERNAL" else "MATERIAL_CONVERT",
        #         branch_id=transfer_detail.receiving_store if transfer_detail.sub_type == "EXTERNAL" else transfer_detail.shipping_store,
        #         sub_branch_id=transfer_detail.receiving_position,
        #         sub_doc_type='position' if transfer_detail.receiving_position else 'store',
        #         posterior_operation='MATERIAL_CONVERT' if transfer_detail.sub_type == "EXTERNAL" else "",
        #         process_status='INITED',
        #         partner_id=partner_id,
        #         created_by=user_id,
        #         created_at=datetime.utcnow(),
        #         created_name=username
        #     )
        #     flow_log_db = ReceiptFlowLogModel.create_flow_logs(**flow_log_detail)

        #     message = {
        #         'flow_id': flow_log_db.id,
        #         'partner_id': partner_id,
        #         'user_id': user_id
        #     }
        #     logger.info("调拨工作流触发:{}".format(flow_log_db.id))
        #     mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
        #                         topic=MessageTopic.HANDLE_FLOW_TASK,
        #                         message=message)
            
        #     # vendor单据同步给三方
        #     message = {
        #                         'doc_resource': 's_transfer',
        #                         'doc_id': transfer_id,
        #                         'partner_id': partner_id,
        #                         'user_id': user_id,
        #                         'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        #                     }
        #     mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)
            
        #     # 同步记录落表，用于后续补偿
        #     tp_trans_log = {
        #                     'id': transfer_id,
        #                     'doc_code': transfer_obj.code,
        #                     'doc_type': 's_transfer',
        #                     'status': 'inited',
        #                     'msg': str(message),
        #                     'partner_id': partner_id,
        #                     'created_by': user_id,
        #                     'created_at': datetime.utcnow()
        #                 }
        #     TpTransLogModel.create_logs_list([tp_trans_log])


        #     # ---ForReceiptStart---#
        #     main_branch_type = None
        #     if transfer_obj.branch_type == 'STORE':
        #         main_branch_type = 'S'
        #     elif transfer_obj.branch_type == 'WAREHOUSE':
        #         main_branch_type = 'W'
        #     elif transfer_obj.branch_type == 'MACHINING_CENTER':
        #         main_branch_type = 'M'

        #     # 获取delivery_res.id
        #     delivery_res = receipt_service.list_deliverys(batch_id=transfer_id,partner_id=partner_id, user_id=user_id)
        #     delivery_res = MessageToDict(delivery_res).get('rows')
        #     if delivery_res:
        #         delivery_res = delivery_res[0]
        #         # 同步到receipt里的收货
        #         receipt_service.create_receives(
        #             batch_id=transfer_id, batch_code=transfer_obj.code, batch_type='TRANSFER',  
        #             order_id=transfer_id, order_code=transfer_obj.code,  
        #             delivery_id=int(delivery_res['id']), 
        #             receive_by=int(transfer_obj.receiving_store), delivery_by=int(transfer_obj.shipping_store),
        #             distr_type='NMD', main_branch_type=main_branch_type,
        #             delivery_date=transfer_obj.transfer_date, demand_date=transfer_obj.transfer_date, 
        #             products=rec_p_list, partner_id=partner_id, user_id=user_id)
        #     else:
        #         if not transfer_obj:
        #             res["failed_code"] = "3"
        #             res["msg"]: "确认失败"
        #             return res

        #     # PDA消息推送
        #     MessageServicePub.pub_message_service(partner_id=partner_id,
        #                                           user_id=user_id,
        #                                           scope_id=1,
        #                                           store_id=transfer_detail.receiving_store,
        #                                           source_root_id=transfer_id,
        #                                           source_id=transfer_id,
        #                                           source_type="TRANSFER",
        #                                           action="CONFIRMED",
        #                                           ref_source_id=transfer_id,
        #                                           ref_source_type="TRANSFER",
        #                                           ref_action="SUBMITTED",
        #                                           content={"transfer_date": str(transfer_detail.transfer_date),
        #                                                    "updated_at": str(datetime.now()),
        #                                                    "updated_by": str(user_id),
        #                                                    "remark": transfer_detail.remark,
        #                                                    "updated_name": username,
        #                                                    "shipping_store_name": transfer_shipping_store_name,
        #                                                    "receiving_store_name": transfer_receiving_store_name
        #                                                    }
        #                                           )
        # else:
        #     if not transfer_obj:
        #         res["failed_code"] = "2"
        #         res["msg"]: "请检查调拨单商品明细以及调拨单状态"
        #         return res
        res["status"] = "SUCCESS"
        return res


class TransferProductModule(object):
    def get_product_list(self, request, user_id=None, partner_id=None, branch_type=None):
        """根据仓库id拉商品列表，仓库下关联了商品分类，
        先拉取主档把该仓库下商品分类id拿到再拉取商品主档"""
        res = {}
        branch_id = convert_to_int(request.id)
        product_category_ids = request.product_category_ids
        return_all_products = request.return_all_products
        return_metadata_products = request.return_metadata_products
        search = request.search
        search_fields = request.search_fields
        # if not warehouse_id:
        #     raise DataValidationException("请传入仓库id")
        try:
            if not return_metadata_products:
                # 先根据仓库拉取仓库关联的商品分类
                if branch_type == "MACHINING_CENTER":
                    entity = metadata_service.get_entity_by_id(id=branch_id, schema_name="MACHINING_CENTER",
                                                               partner_id=partner_id, user_id=user_id)
                else:
                    entity = metadata_service.get_entity_by_id(id=branch_id, schema_name="distrcenter",
                                                               partner_id=partner_id, user_id=user_id)
                if not entity:
                    raise DataValidationException("主档未找到该组织！")
                fields = entity.get("fields")
                relation = None
                if isinstance(fields, dict):
                    relation = fields.get("relation")
                if not relation or not relation.get("product_category"):
                    logging.info("该组织下未关联商品分类-{}".format(branch_id))
                    res["rows"] = {}
                    res["total"] = 0
                    return res

                product_category = relation.get("product_category")
                if product_category_ids:
                    product_category_ids = [str(category_id) for category_id in product_category_ids]
                    product_category_ids = list(set(product_category_ids) & set(product_category))
                else:
                    product_category_ids = product_category
                relation_filters = dict(product_category=product_category_ids)

            else:
                if product_category_ids:
                    product_category_ids = [str(category_id) for category_id in product_category_ids]
                    relation_filters = dict(product_category=product_category_ids)
                else:
                    relation_filters = None
            # 然后根据商品分类拉取商品主档
            if return_all_products is True:
                filters = None
            else:
                filters = {"status__eq": "ENABLED", "bom_type__neq": 'MANUFACTURE'}
            products_by_category = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                     include_units=True,
                                                                     filters=filters,
                                                                     return_fields="scope_id,name,code,second_code,\
                                                                     sale_type,product_type,bom_type,storage_type,\
                                                                     status,alias,category,updated,model_code,model_name,\
                                                                     default_receiving_deviation_min,\
                                                                     default_receiving_deviation_max,\
                                                                     default_purchase_deviation_min,\
                                                                     default_purchase_deviation_max,\
                                                                     ledger_class",
                                                                     partner_id=partner_id,
                                                                     user_id=user_id,
                                                                     search=search,
                                                                     search_fields=search_fields,
                                                                     include_total=True)

            products = products_by_category.get("rows") if products_by_category.get("rows") else {}
            products = get_transfer_product(products)
            logging.info("products: {}".format(products))
            res["rows"] = products
            res["total"] = products_by_category.get("total") if products_by_category.get("total") else 0
        except Exception as e:
            logging.error("根据仓库拉取商品列表失败:{}-{}-{}".format(e, sys.exc_info(), traceback.format_exc()))
        return res


ssm = TransferModule()
ssp = TransferProductModule()
