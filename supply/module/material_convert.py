# -*- coding: utf8 -*-
from datetime import datetime

from supply.driver.mq import mq_producer
from supply.model.inventory import inventory_repository
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.helper import convert_to_int, MessageTopic, convert_to_decimal, get_product_unit_rate_map, \
    get_cost_center_map, get_product_map
from supply.utils.inventory_enum import ACTION
from supply.utils.auth import branch_scope_check
from supply.model.supply_doc_code import Supply_doc_code
from google.protobuf.timestamp_pb2 import Timestamp
from supply.error.exception import DataValidationException, DataDuplicationException, OrderNotExistException
from supply.client.metadata_service import metadata_service
from supply.model.material_convert.material_convert import material_convert_db


class MaterialConvertService(object):
    """物料转换业务处理"""
    def __init__(self):
        pass

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    def utctimestamp2datetime(self, timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.utcfromtimestamp(timestamp.seconds)
        return date

    def update_res_datetime2timestamp(self, keys, res: dict):
        """更新查询集中的datetime to Timestamp
        :param keys: 字段值列表 -> (str)
        :param res: 返回查询集 -> (dict)
        """
        if keys:
            for key in keys:
                if key in res.keys():
                    res.update({key: self.get_timestamp(res[key])})
        return res

    def create_material_convert(self, request, user_id=None, partner_id=None):
        """创建物料转换单"""
        response = {}
        request_id = request.request_id
        # 是否自动确认(用于自动物料转换)
        auto_confirm = request.auto_confirm
        status = request.status
        if not request_id:
            raise DataValidationException("没有请求id")
        # 根据请求id校验该转换单是否已经创建
        convert_record = material_convert_db.get_material_convert_by_id(request_id=request_id, partner_id=partner_id)
        if convert_record:
            raise DataDuplicationException("该转换单已创建{}".format(convert_record.id))
        convert_date = request.convert_date
        if not convert_date:
            raise DataValidationException("请传入转换日期")
        if not status:
            if auto_confirm:
                status = "CONFIRMED"
            else:
                status = "INITED"
        branch_type = request.branch_type
        branch_id = convert_to_int(request.branch_id)
        # 拉取成本中心
        cost_center_id = get_cost_center_map(branch_type=branch_type, branch_id=branch_id,
                                             partner_id=partner_id, user_id=user_id, return_cost=True)
        if not cost_center_id:
            raise DataValidationException("该组织没有配置成本中心-{}".format(request.branch_name))
        convert_date = self.utctimestamp2datetime(convert_date)
        code = Supply_doc_code.get_code_by_type('MATER_CV', partner_id, user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        main_id = gen_snowflake_id()
        order_data = dict(
            id=main_id,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            branch_type=branch_type,
            code=code,
            status=status,
            convert_type=request.convert_type,
            convert_date=convert_date,
            branch_id=branch_id,
            branch_code=request.branch_code,
            branch_name=request.branch_name,
            position_id=request.position_id,
            position_name=request.position_name,
            position_code=request.position_code,
            convert_rule=request.convert_rule,
            remark=request.remark,
            process_status="INITED",
            opened_position=request.opened_position,
            request_id=request_id,
            cost_center_id=cost_center_id
        )
        materials = request.materials
        if not materials:
            raise DataValidationException("转换单必须包含物料")
        # todo: 校验待转换物料的库存
        product_ids = [convert_to_int(m.product_id) for m in materials]
        product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name",
                                      partner_id=partner_id,
                                      user_id=user_id)
        material_detail = []
        for m in materials:
            product = product_map.get(str(m.product_id)) if product_map.get(str(m.product_id)) else {}
            units = product.get('units', [])
            unit_name = ''
            unit_spec = ''
            unit_rate = 0
            for u in units:
                if str(u.get('id')) == str(m.unit_id):
                    unit_name = u.get('name')
                    unit_spec = u.get('code')
                    unit_rate = u.get('rate')
            row = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username,
                main_id=main_id,
                product_id=m.product_id,
                product_code=m.product_code,
                product_name=m.product_name,
                product_type=m.product_type,
                unit_id=m.unit_id,
                unit_name=unit_name,
                unit_spec=unit_spec,
                unit_rate=unit_rate,
                quantity=m.quantity,
                type=m.type,
            )
            material_detail.append(row)
        res = material_convert_db.create_material_convert(order_data=order_data,
                                                          material_detail=material_detail)
        if res is True:
            response["convert_id"] = main_id
            response["result"] = "success"
            if auto_confirm:
                update_log = dict(
                    id=gen_snowflake_id(),
                    partner_id=partner_id,
                    created_by=user_id,
                    created_name=username,
                    main_id=main_id,
                    action="AUTO_CONFIRMED",
                )
                material_convert_db.create_material_convert_log(material_log=update_log)
                convert_record = material_convert_db.get_material_convert_by_id(convert_id=main_id,
                                                                                partner_id=partner_id)
                if convert_record.status == "CONFIRMED":
                    # 自动物料转换需要扣减库存
                    deal_inventory_message(convert_record, partner_id=partner_id, user_id=user_id)
        else:
            response["result"] = "failed"
        return response

    def list_material_convert(self, request, user_id=None, partner_id=None):
        """物料转换单列表查询"""
        response = {}
        start_date = self.utctimestamp2datetime(request.start_date)
        end_date = self.utctimestamp2datetime(request.end_date)
        branch_ids = [convert_to_int(_id) for _id in request.branch_ids] if request.branch_ids else []
        convert_rules = [convert_to_int(_rule) for _rule in request.convert_rules] if request.convert_rules else []
        position_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        branch_type = request.branch_type
        code = request.code
        convert_type = request.convert_type
        status = request.status
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        order = request.order
        sort = request.sort
        if status == 'ALL':
            status = None
        if convert_type == 'ALL':
            convert_type = None
        query_set = material_convert_db.list_material_convert(partner_id=partner_id, user_id=user_id,
                                                              start_date=start_date, end_date=end_date,
                                                              code=code, status=status, convert_type=convert_type,
                                                              branch_ids=branch_ids, limit=limit, offset=offset,
                                                              include_total=include_total, order=order,
                                                              sort=sort, branch_type=branch_type,
                                                              convert_rules=convert_rules, position_ids=position_ids)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            response["total"] = total
        else:
            query_set = query_set
        result_list = []
        if query_set:
            for row in query_set:
                row = row.as_dict()
                # 需要更新datetime的字段
                keys = ["created_at", "updated_at", "convert_date"]
                row = self.update_res_datetime2timestamp(keys=keys, res=row)
                result_list.append(row)
        response["rows"] = result_list
        return response

    def get_material_convert_detail(self, request, user_id=None, partner_id=None):
        """查询物料转换单详情"""
        response = {}
        convert_id = convert_to_int(request.convert_id)
        convert_record = material_convert_db.get_material_convert_by_id(convert_id=convert_id, partner_id=partner_id)
        if not convert_record:
            raise DataValidationException("Material Convert not found！- {}".format(convert_id))
        response.update(convert_record.as_dict())
        # 更新datetime to Timestamp
        keys = ["created_at", "updated_at", "convert_date"]
        response = self.update_res_datetime2timestamp(keys=keys, res=response)
        material_detail = material_convert_db.get_material_convert_detail(partner_id=partner_id,
                                                                          user_id=user_id,
                                                                          main_id=convert_record.id)
        response["materials"] = []
        if material_detail:
            for row in material_detail:
                row = row.as_dict()
                keys = ["created_at", "updated_at"]
                row = self.update_res_datetime2timestamp(keys=keys, res=row)
                response["materials"].append(row)
        return response

    def update_material_convert(self, request, user_id=None, partner_id=None):
        """更新物料转换单"""
        ret = {}
        convert_id = convert_to_int(request.convert_id)
        update_detail = request.update_detail
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        status = request.status
        update_data = dict(
            id=convert_id,
            status=status,
            updated_by=user_id,
            updated_name=username,
            partner_id=partner_id,
        )
        material_detail = []
        query_order = material_convert_db.get_material_convert_by_id(convert_id=convert_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该转换单不存在-{}".format(convert_id))
        if status not in ["INITED", "CONFIRMED", "INVALID"]:
            raise DataValidationException("更新状态不合法{}".format(status))
        if not update_detail and status == query_order.status:
            raise DataValidationException("状态已变更请勿重复操作")
        if update_detail:
            if status in ["CONFIRMED", "INVALID"]:
                raise DataValidationException("只有新建单据可以更新详情操作")
            del update_data['status']
            # 拉取成本中心
            cost_center_id = get_cost_center_map(branch_type=request.branch_type, branch_id=request.branch_id,
                                                 partner_id=partner_id, user_id=user_id, return_cost=True)
            if not cost_center_id:
                raise DataValidationException("该组织没有配置成本中心-{}".format(request.branch_name))
            update_data.update(dict(
                branch_type=request.branch_type,
                convert_type=request.convert_type,
                convert_date=self.utctimestamp2datetime(request.convert_date),
                branch_id=request.branch_id,
                branch_code=request.branch_code,
                branch_name=request.branch_name,
                position_id=request.position_id,
                position_name=request.position_name,
                position_code=request.position_code,
                convert_rule=request.convert_rule,
                opened_position=request.opened_position,
                remark=request.remark,
                cost_center_id=cost_center_id
            ))
            materials = request.materials
            # 需要支持单个更新remark先不校验物料详情
            # if not materials:
            #     raise DataValidationException("转换单必须包含物料")
            if materials:
                product_ids = [convert_to_int(m.product_id) for m in materials]
                product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name",
                                              partner_id=partner_id,
                                              user_id=user_id)
                material_detail = []
                for m in materials:
                    product = product_map.get(str(m.product_id)) if product_map.get(str(m.product_id)) else {}
                    units = product.get('units', [])
                    unit_name = ''
                    unit_spec = ''
                    unit_rate = 0
                    for u in units:
                        if str(u.get('id')) == str(m.unit_id):
                            unit_name = u.get('name')
                            unit_spec = u.get('code')
                            unit_rate = u.get('rate')
                    row = dict(
                        id=m.id,
                        partner_id=partner_id,
                        updated_by=user_id,
                        updated_name=username,
                        main_id=convert_id,
                        product_id=m.product_id,
                        product_code=m.product_code,
                        product_name=m.product_name,
                        product_type=m.product_type,
                        unit_id=m.unit_id,
                        unit_name=unit_name,
                        unit_spec=unit_spec,
                        unit_rate=unit_rate,
                        quantity=m.quantity,
                        type=m.type,
                    )
                    material_detail.append(row)
        res = material_convert_db.update_material_convert(update_data=update_data,
                                                          material_detail=material_detail,
                                                          update_detail=update_detail)
        if res is True:
            ret["convert_id"] = convert_id
            ret["result"] = "success"
            # 保存操作日志
            if not update_detail:
                update_log = dict(
                    id=gen_snowflake_id(),
                    partner_id=partner_id,
                    created_by=user_id,
                    created_name=username,
                    main_id=convert_id,
                    action=status,
                )
                material_convert_db.create_material_convert_log(material_log=update_log)
                if status == "CONFIRMED":
                    """
                    单据确认后需要扣减库存处理：
                        原物料减库存：WITHDRAW = 1; // 减少库存
                        目标物料增加库存：DEPOSIT = 2; //增加库存
                    """
                    deal_inventory_message(query_order=query_order, user_id=user_id, partner_id=partner_id)
        return ret

    def get_material_convert_by_id(self, convert_id=None, partner_id=None):
        record = material_convert_db.get_material_convert_by_id(convert_id=convert_id, partner_id=partner_id)
        return record


def deal_inventory_message(query_order, user_id=None, partner_id=None):
    """
    :param query_order -> 单据查询对象
    :param user_id
    :param partner_id
    单据确认后需要扣减库存处理：
        原物料减库存：WITHDRAW = 1; // 减少库存
        目标物料增加库存：DEPOSIT = 2; //增加库存
    """
    accounts = []
    sub_account = dict(
        id=query_order.position_id,  # 子账户业务ID
        code=query_order.position_code,  # 子账户业务编码
        # level=None                     # 子账户层级
    ) if query_order.opened_position else None
    materials = material_convert_db.get_material_convert_detail(partner_id=partner_id,
                                                                user_id=user_id,
                                                                main_id=query_order.id)
    if materials:
        product_ids = []
        for m in materials:
            product_ids.append(m.product_id)
        # 获取商品单位和转换率map
        product_unit_rate_map = get_product_unit_rate_map(product_ids=product_ids, partner_id=partner_id,
                                                          user_id=user_id)
        for m in materials:
            accounting = dict(sub_account=sub_account)
            # 单位换算为核算数量
            unit_rate_dict = product_unit_rate_map.get(m.product_id) if product_unit_rate_map.get(m.product_id) else {}
            quantity = convert_to_decimal(m.quantity)
            unit_rate = convert_to_decimal(unit_rate_dict.get(m.unit_id, 1))
            accounting_quantity = quantity * unit_rate
            if m.type == 'origin':
                # 原物料减库存：WITHDRAW = 1; //减少库存
                accounting.update(dict(
                    branch_id=query_order.branch_id,
                    product_id=m.product_id,
                    amount=str(accounting_quantity),  # 存字符串为了json序列化
                    action=1
                ))
            elif m.type == 'target':
                # 目标物料增加库存：DEPOSIT = 2; //增加库存
                accounting.update(dict(
                    branch_id=query_order.branch_id,
                    product_id=m.product_id,
                    amount=str(accounting_quantity),
                    action=2
                ))
            else:
                continue
            accounts.append(accounting)
    # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
    message = dict(batch_no=str(query_order.id),
                   code='MATERIAL_CONVERT',
                   action=100,          # todo: 后续可能库存新增一个扣减类型，在一个事务中提交掉
                   description='MATERIAL_CONVERT',
                   trace_id=query_order.code,
                   accounts=accounts,
                   partner_id=partner_id,
                   user_id=user_id,
                   business_time=datetime.utcnow())
    inventory_dict = dict(batch_no=str(query_order.id), code="MATERIAL_CONVERT", batch_action=100,
                          action_dec=ACTION[100],
                          batch_id=None,
                          status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                          )
    inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                     partner_id=partner_id, user_id=user_id)

    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                        topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                        message=message)


material_convert_service = MaterialConvertService()
