import json

from supply.model.supply_doc_code import Supply_doc_code
from supply.utils.helper import convert_to_int
from supply.error.exception import DataValidationException
from supply.model.invoice_blending import invoice_blending_order_db
from supply.client.metadata_service import metadata_service
from supply.utils.snowflake import gen_snowflake_id
from supply.model.invoice_blending import InvoiceBlendingOrder
from supply.module.purchase_service import purchase_service


class InvoiceBlendingService(object):
    """发票勾兑业务逻辑"""

    def __init__(self):
        pass

    def create_invoice_blending_order(self, request, partner_id, user_id):
        """新建发票勾兑单"""
        ret = {}
        company_id = convert_to_int(request.company_id)
        supplier_id = convert_to_int(request.supplier_id)
        order_ids = request.order_ids
        invoice_ids = request.invoice_ids
        if not order_ids:
            raise DataValidationException("单据不能为空，请勾选单据")
        if not invoice_ids:
            raise DataValidationException("发票不能为空，请勾选发票")
        order_ids = [convert_to_int(order_id) for order_id in order_ids]
        invoice_ids = [convert_to_int(invoice_id) for invoice_id in invoice_ids]
        order_sum_price = request.order_sum_price
        order_sum_tax = request.order_sum_tax
        invoice_sum_price = request.invoice_sum_price
        invoice_sum_tax = request.invoice_sum_tax
        diff_price = request.diff_price
        diff_reason = request.diff_reason
        # 拉取供应商
        supplier = metadata_service.get_vendor_center(center_id=supplier_id, partner_id=partner_id,
                                                      user_id=user_id)
        if not supplier:
            raise DataValidationException("未找到该供应商主档信息-{}".format(supplier_id))
        # 拉取公司
        company = metadata_service.get_company_by_id(company_id=company_id, partner_id=partner_id, user_id=user_id)
        if not company:
            raise DataValidationException("未找到该公司主档信息-{}".format(company_id))
        # 生成订单编号，在supply_doc_code_config_main表中新建一条type为IVC_BLD的记录
        code = Supply_doc_code.get_code_by_type('IVC_BLD', partner_id, user_id)
        # 拉取用户名
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        id_ = gen_snowflake_id()
        data = dict(
            id=id_,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            code=code,
            status="SUBMITTED",  # 初始创建状态为已提交
            company_id=company_id,
            company_code=company.get("code"),
            company_name=company.get("name"),
            supplier_id=supplier_id,
            supplier_code=supplier.get("code"),
            supplier_name=supplier.get("name"),
            order_ids_json=json.dumps({"order_ids": order_ids}),
            invoice_ids_json=json.dumps({"invoice_ids": invoice_ids}),
            order_sum_price=order_sum_price,
            order_sum_tax=order_sum_tax,
            invoice_sum_price=invoice_sum_price,
            invoice_sum_tax=invoice_sum_tax,
            diff_price=diff_price,
            diff_reason=diff_reason
        )
        res = invoice_blending_order_db.create_invoice_blending(data=data)
        if res is True:
            ret["result"] = "success"
            ret["id"] = id_
        else:
            ret["result"] = "create failed"
        return ret

    def change_invoice_blending_status(self, request, partner_id, user_id):
        """变更勾兑单状态"""
        ret = {}
        ids = request.ids
        if not ids:
            raise DataValidationException("请传入勾兑单id")
        ids = [convert_to_int(id_) for id_ in ids]
        status = request.status
        if status not in ["SUBMITTED", "REJECTED", "APPROVED"]:
            raise DataValidationException("状态不合法")
        update_data = []
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        for id_ in ids:
            # 判断订单是否存在
            query_order = InvoiceBlendingOrder.get(id_)
            if not query_order:
                raise DataValidationException("该发票勾兑单不存在-{}".format(id_))
            if query_order.status == "REJECTED" and status == "APPROVED":
                raise DataValidationException("已驳回的勾兑单不允许审核-{}".format(id_))
            if query_order.status == "APPROVED" and status == "REJECTED":
                raise DataValidationException("已审核的勾兑单不允许驳回-{}".format(id_))
            row = dict(
                id=id_,
                updated_by=user_id,
                updated_name=username,
                status=status,
            )
            update_data.append(row)
        res = invoice_blending_order_db.change_invoice_blending_status(update_data=update_data)
        if res is True:
            ret["result"] = "success"
            ret["ids"] = ids
        else:
            ret["result"] = "change status failed"
        return ret

    def update_invoice_blending_order(self, request, partner_id, user_id):
        """更新已驳回的勾兑单内容"""
        ret = {}
        id_ = request.id
        order_data = request.order_data
        if not order_data:
            raise DataValidationException("请传入待更新的单据信息")
        query = InvoiceBlendingOrder.get(id_)
        if not query:
            raise DataValidationException("该发票勾兑单不存在-{}".format(id_))
        if query.status != "REJECTED":
            raise DataValidationException("只有已驳回的单据才可以更新")
        company_id = convert_to_int(order_data.company_id)
        supplier_id = convert_to_int(order_data.supplier_id)
        order_ids = order_data.order_ids
        invoice_ids = order_data.invoice_ids
        if not order_ids:
            raise DataValidationException("单据不能为空，请勾选单据")
        if not invoice_ids:
            raise DataValidationException("发票不能为空，请勾选发票")
        order_ids = [convert_to_int(order_id) for order_id in order_ids]
        invoice_ids = [convert_to_int(invoice_id) for invoice_id in invoice_ids]
        order_sum_price = order_data.order_sum_price
        order_sum_tax = order_data.order_sum_tax
        invoice_sum_price = order_data.invoice_sum_price
        invoice_sum_tax = order_data.invoice_sum_tax
        diff_price = order_data.diff_price
        diff_reason = order_data.diff_reason
        # 拉取供应商
        supplier = metadata_service.get_vendor_center(center_id=supplier_id, partner_id=partner_id,
                                                      user_id=user_id)
        if not supplier:
            raise DataValidationException("未找到该供应商主档信息-{}".format(supplier_id))
        # 拉取公司
        company = metadata_service.get_company_by_id(company_id=company_id, partner_id=partner_id, user_id=user_id)
        if not company:
            raise DataValidationException("未找到该公司主档信息-{}".format(company_id))
        # 拉取用户名
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        update_data = dict(
            id=id_,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            status="SUBMITTED",  # 初始创建状态为已提交
            company_id=company_id,
            company_code=company.get("code"),
            company_name=company.get("name"),
            supplier_id=supplier_id,
            supplier_code=supplier.get("code"),
            supplier_name=supplier.get("name"),
            order_ids_json=json.dumps({"order_ids": order_ids}),
            invoice_ids_json=json.dumps({"invoice_ids": invoice_ids}),
            order_sum_price=order_sum_price,
            order_sum_tax=order_sum_tax,
            invoice_sum_price=invoice_sum_price,
            invoice_sum_tax=invoice_sum_tax,
            diff_price=diff_price,
            diff_reason=diff_reason
        )
        update_data_list = [update_data]
        res = invoice_blending_order_db.update_invoice_blending(update_data_list)
        if res is True:
            ret["result"] = "success"
            ret["id"] = id_
        else:
            ret["result"] = "update order failed"
        return ret

    def list_invoice_blending(self, request, partner_id, user_id):
        """查询勾兑单"""
        ret = {}
        code = request.code
        status = request.status
        company_ids = [convert_to_int(company_id) for company_id in request.company_ids] if request.company_ids else []
        supplier_ids = [convert_to_int(supplier_id) for supplier_id in
                        request.supplier_ids] if request.supplier_ids else []
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        sort_type = request.sort_type
        sort = request.sort
        if not sort:
            sort = "updated_at"
        if status == "all":
            status = None
        query_set = invoice_blending_order_db.list_invoice_blending(code=code, status=status, company_ids=company_ids,
                                                                    supplier_ids=supplier_ids, limit=limit,
                                                                    offset=offset, include_total=include_total,
                                                                    sort_type=sort_type, sort=sort,
                                                                    partner_id=partner_id, user_id=user_id)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            ret["total"] = total
        else:
            query_set = query_set
        result_list = []
        if query_set:
            for q in query_set:
                order_ids = list(json.loads(q.order_ids_json).get("order_ids", []))
                invoice_ids = list(json.loads(q.invoice_ids_json).get("invoice_ids", []))
                row = dict(
                    id=q.id,
                    partner_id=q.partner_id,
                    code=q.code,
                    status=q.status,
                    company_id=q.company_id,
                    company_code=q.company_code,
                    company_name=q.company_name,
                    supplier_id=q.supplier_id,
                    supplier_code=q.supplier_code,
                    supplier_name=q.supplier_name,
                    order_ids=order_ids,
                    invoice_ids=invoice_ids,
                    order_sum_price=q.order_sum_price,
                    order_sum_tax=q.order_sum_tax,
                    invoice_sum_price=q.invoice_sum_price,
                    invoice_sum_tax=q.invoice_sum_tax,
                    diff_price=q.diff_price,
                    diff_reason=q.diff_reason,
                    created_by=q.created_by,
                    created_name=q.created_name,
                    created_at=purchase_service.get_timestamp(q.created_at),
                    updated_by=q.updated_by,
                    updated_name=q.updated_name,
                    updated_at=purchase_service.get_timestamp(q.updated_at)
                )
                result_list.append(row)
        ret["rows"] = result_list
        return ret

    def get_invoice_blending_detail(self, request, partner_id, user_id):
        """查询勾兑单详情"""
        id_ = convert_to_int(request.id)
        q = InvoiceBlendingOrder.get(id_)
        if not q:
            raise DataValidationException("该发票勾兑单不存在-{}".format(id_))
        order_ids = list(json.loads(q.order_ids_json).get("order_ids", []))
        invoice_ids = list(json.loads(q.invoice_ids_json).get("invoice_ids", []))
        result = dict(
            id=q.id,
            partner_id=q.partner_id,
            code=q.code,
            status=q.status,
            company_id=q.company_id,
            company_code=q.company_code,
            company_name=q.company_name,
            supplier_id=q.supplier_id,
            supplier_code=q.supplier_code,
            supplier_name=q.supplier_name,
            order_ids=order_ids,
            invoice_ids=invoice_ids,
            order_sum_price=q.order_sum_price,
            order_sum_tax=q.order_sum_tax,
            invoice_sum_price=q.invoice_sum_price,
            invoice_sum_tax=q.invoice_sum_tax,
            diff_price=q.diff_price,
            diff_reason=q.diff_reason,
            created_by=q.created_by,
            created_name=q.created_name,
            created_at=purchase_service.get_timestamp(q.created_at),
            updated_by=q.updated_by,
            updated_name=q.updated_name,
            updated_at=purchase_service.get_timestamp(q.updated_at)
        )
        return result


invoice_blending_service = InvoiceBlendingService()