from supply.client.metadata_service import metadata_service


# if open_position is True:
def get_adjust_stocktake_position_ids(partner_id, user_id, branch_id,branch_type ='', position_type=''):
    """position_type
    {
    "adjust": [position_id..], 报废
    "stocktake": [position_id..], 盘点
    "transfer": position_id(str), 外部调拨
    "deliveryReceive": position_id, 配送收货
    "deliveryReturn": position_id, 配送退货
    "directReceive": position_id, 直送收货
    "directReturn": position_id, 直送退货
    "insideTransfer": position_id, 内部调拨
    "sale": position_id, 销售
    }
    """
    open_position = False  # 是否开启多仓位
    business_config = metadata_service.get_business_config(partner_id=partner_id, user_id=user_id)
    print('business_config',business_config)
    if not business_config:
        return open_position
    stores = business_config.get('stores', [])
    region = business_config.get('region', [])
    # todo: 通过区域拉取门店与stores合并
    warehouse = business_config.get('warehouse', [])
    machining = business_config.get('machining', [])
    if branch_type == 'STORE':
        print('sadsadasdasd',str(branch_id))
        if str(branch_id) in stores:
            open_position = True
    if branch_type == 'WAREHOUSE':
        if str(branch_id) in warehouse:
            open_position = True
    if branch_type == 'MACHINING_CENTER':
        if str(branch_id) in machining:
            open_position = True
    allow_position_ids = []
    if open_position:

        position_config = metadata_service.get_position_relation_config(branch_type=branch_type,
                                                                        branch_ids=[str(branch_id)],
                                                                        partner_id=partner_id,
                                                                        user_id=user_id)
        print('position_config',position_config,branch_type,str(branch_id),partner_id,user_id)
        if position_config:
            allow_position_ret = position_config[0].get(position_type)
            for a in allow_position_ret:
                allow_position_ids.append(int(a))
    return allow_position_ids


def get_transfer_product(products):
    result = []
    for product in products:
        units = product.get('units',[])
        if check_transfer_unit(units):
            result.append(product)
    return result


def check_transfer_unit(units):
    transfer_available = False
    for unit in units:
        if unit.get('transfer'):
            transfer_available = True
            break
    return transfer_available


def get_transfer_products(products):
    result = []
    for product in products:
        if product.get('unit',[]):
            result.append(product)
    return result
