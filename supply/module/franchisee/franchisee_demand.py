# -*- coding: utf-8 -*-
import json
import logging
import traceback
from ast import literal_eval
from collections import namedtuple, deque
from datetime import datetime, timedelta
from decimal import Decimal,ROUND_HALF_UP
from typing import Dict
from sqlalchemy.orm import make_transient

from supply import logger
import operator

from supply.client.credit_pay import credit_pay_service
from supply.driver.mq import mq_producer
from supply.driver.mysql import session
from supply.model.supply_partner_action import SupplyPartnerAction
from supply.utils.auth import branch_scope_check, branch_list_scope_check
from supply.utils.encode import generate_hash_md5
from supply.utils.enums import Demand_type, Demand_sub_type, Platform, BranchType, PartnerActionModule, Demand_enum
from supply.error.exception import StatusUnavailable, DataValidationException, \
    OrderNotExistException
from supply.error.demand import DemandError, ProductError, DemandNotExistError
from supply.utils.helper import get_guid, get_branch_map, get_product_map, get_uuids, convert_to_int, \
    convert_to_decimal, get_username_map, MessageTopic, datetime_2_timestamp, get_category_map, get_unit_map, \
    check_quantity, DEFAULT_STORAGE_TYPE
from supply.client.metadata_service import metadata_service
from supply.client.receipt_service import receipt_service
from supply.client.inventory_service import inventory_service
from supply.module import BaseToolsModule
from supply.model.supply_doc_code import Supply_doc_code
from supply.proto.frs_management.franchisee_demand_pb2 import Product
from supply.model.franchisee.franchisee_demand import DemandAction as Action
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand as sFD
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandLog as FdLogDB
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProductLog as FdpLogDB
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProduct as sFDP
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProductRelation as SFDPR
from supply.client.products_manage_service import products_manage_service
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefund as sFR

from supply.utils.kit import Kit, KitEnum


class FranchiseeDemandService(BaseToolsModule):
    """加盟商订货相关业务操作-PC端"""

    def __init__(self):
        super(FranchiseeDemandService, self).__init__()
        # 加盟商订货模块展示的所有状态
        self.show_status = ["PREPARE", "P_SUBMIT", "SUBMITTED", "R_APPROVE", "REJECTED", "CONFIRMED", "APPROVING",
                            "APPROVED", "CANCELLED"]
        self.batch_type = "FRS_DEMAND"
        self.platform = Platform.HEX_WEB.code

    @staticmethod
    def _attach_product_attr(product: Product, product_map: dict, category_map: dict, unit_map: dict) -> None:
        """attach Request Product extra fields"""
        product_id = product.product_id
        pro_fields = product_map.get(str(product_id), {})
        units = pro_fields.get('units')
        unit_id = None
        accounting_unit_id = None
        unit_rate = 1
        if units:
            order_flag = False
            for u in units:
                if u.get("order"):
                    unit_rate = float(u.get("rate", 1))
                    unit_id = int(u.get('id'))
                    order_flag = True
                if u.get('default'):
                    accounting_unit_id = int(u.get('id'))
            if not order_flag:
                raise ProductError("此商品未设置订货单位，请检查主档设置, product_id:{}".format(product_id))
        else:
            raise ProductError("此商品未设置单位，请检查主档设置, product_id:{}".format(product_id))
        unit = unit_map.get(unit_id, {})
        accounting_unit = unit_map.get(accounting_unit_id, {})
        product.product_code = pro_fields.get('code', '')
        product.product_name = pro_fields.get('name', '')
        product.category_id = convert_to_int(pro_fields.get('category'))
        product.category_name = category_map.get(product.category_id, {}).get('name', '')
        product.unit_id = unit_id
        product.unit_spec = unit.get("code", "")
        product.unit_name = unit.get('name', "")
        product.unit_rate = unit_rate
        product.accounting_unit_id = accounting_unit_id
        product.accounting_unit_name = accounting_unit.get('name')
        product.accounting_unit_spec = accounting_unit.get("code")
        product.storage_type = pro_fields.get('storage_type', '')

    @staticmethod
    def get_distribution_map(distribution: dict, partner_id: int, user_id):
        """distribution: {"distribution_type(配送类型)": [distribution_id(配送方ID), ...]}
        查询配送方主档信息"""
        branch_map = dict()
        for dist_type, distribute_ids in distribution.items():
            if dist_type == Demand_type.NMD.code:
                branch_map.update(get_branch_map(branch_ids=distribute_ids, branch_type=BranchType.WAREHOUSE.code,
                                                 partner_id=partner_id, user_id=user_id))
            if dist_type == Demand_type.PAD.code:
                branch_map.update(
                    get_branch_map(branch_ids=distribute_ids, branch_type=BranchType.MACHINING_CENTER.code,
                                   partner_id=partner_id, user_id=user_id))
        return branch_map

    def create_franchisee_demand_batch(self, partner_id, user_id, request):
        res = dict(result=False)
        batch_id = request.batch_id
        # from google.protobuf.json_format import MessageToJson
        # logging.info("qqqqqqqq")
        # logging.info(MessageToJson(request, preserving_proto_field_name=True))
        if batch_id:
            if_exist = sFD.get_demand_by_id(partner_id=partner_id, request_id=batch_id)
            if if_exist:
                raise DemandError("请勿重复提交批次！")
        else:
            return res
        if not request.batches:
            return res
        demands = []
        demand_products = []
        demand_logs = []
        product_ids = []
        category_ids = set()
        received_bys = []
        all_products, relation_product_ids, demand_relation_products = [], set(), []
        key = namedtuple('key', ['id', 'type', 'relation_type', 'father_id'])
        for index, batch in enumerate(request.batches):
            all_products.append({})
            received_bys.append(batch.received_by)
            p_ids = get_uuids(len(batch.products))
            for i, p in enumerate(batch.products):
                all_products[index][key(p_ids[i], 'main', 'BIND' if p.relation_products else '', None)] = p
                if convert_to_int(p.product_id) not in product_ids:
                    product_ids.append(convert_to_int(p.product_id))
                if convert_to_int(p.category_id) not in category_ids:
                    category_ids.add(convert_to_int(p.category_id))

                rp_ids = get_uuids(len(p.relation_products))
                for ri, rp in enumerate(p.relation_products):
                    all_products[index][key(rp_ids[ri], 'bind', 'BIND', p_ids[i])] = rp
                    relation_product_ids.add(int(rp.product_id))
                    category_ids.add(rp.category_id) if p.category_id else ...

        # 门店
        branch_map = get_branch_map(branch_ids=received_bys, branch_type="STORE", partner_id=partner_id,
                                    user_id=user_id, return_fields="id,code,name,type,chain_type")
        unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
        # 商品类别
        category_map = get_category_map(category_ids, partner_id, user_id)

        product_map = get_product_map(relation_product_ids, 'id,code,name,category,storage_type',
                                      partner_id, user_id)

        action = Demand_enum.INITED.code

        # action = SupplyPartnerAction.get_to_action(partner_id, PartnerActionModule.DEMAND.code, Demand_enum.INITED.code)

        for index, batch in enumerate(request.batches):
            demand_date = self.timestamp2datetime(batch.demand_date)
            arrival_date = self.timestamp2datetime(batch.arrival_date)
            # products = batch.products
            products = all_products[index]
            if demand_date.year == 1970:
                continue
            if not products:
                continue
            store_info = branch_map.get(batch.received_by, {})
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            if not batch.franchisee_id:
                logger.warning(f"门店：{store_info.get('code', '')} 未配置加盟商，跳过！")
                continue
            demand = dict(
                id=get_guid(),
                partner_id=partner_id,
                code=Supply_doc_code.get_code_by_type('FD', partner_id, None),
                received_by=batch.received_by,
                received_name=store_info.get('name'),
                received_code=store_info.get('code'),
                store_type=store_info.get('type'),
                chain_type=store_info.get('chain_type'),
                franchisee_id=batch.franchisee_id,
                distribute_by=batch.distribute_by,
                demand_date=demand_date,
                arrival_date=arrival_date,
                status=action,
                process_status='INITED',
                type=batch.type if batch.type else 'FMD',
                bus_type=batch.bus_type if batch.bus_type else 'HD_ASSIGN',
                sub_type=Demand_sub_type.MASTER.code,
                order_type_id=batch.order_type_id,
                payment_way=batch.payment_way,
                created_by=user_id,
                updated_by=user_id,
                created_name=username,
                updated_name=username,
                batch_id=batch_id,
                remark=batch.remark,
                reason=batch.reason,
                sum_price_tax=Decimal(str(0)),
                sum_tax=Decimal(str(0)),
                sales_amount=Decimal(0),
                extends=batch.extends,
            )

            # ids = get_uuids(len(product_ids))
            for k, p in products.items():
                is_main = k.type == 'main'

                product_id = convert_to_int(p.product_id)
                unit = unit_map.get(p.unit_id)
                accounting_unit = unit_map.get(p.accounting_unit_id, {})
                quantity = convert_to_decimal(p.quantity) if p.quantity else convert_to_decimal(0)
                tax_price = convert_to_decimal(p.tax_price) if p.tax_price else convert_to_decimal(0)
                tax_rate = convert_to_decimal(p.tax_rate) if p.tax_rate else convert_to_decimal(0)
                sales_price = convert_to_decimal(p.sales_price) if p.sales_price else convert_to_decimal(0)
                cost_price = tax_price / convert_to_decimal(1 + tax_rate / 100)
                tax_amount = (tax_price - cost_price) * quantity
                amount = (quantity * tax_price).quantize(Decimal('0.00'), ROUND_HALF_UP)
                demand["sum_price_tax"] += amount
                demand["sum_tax"] += tax_amount
                sales_amount = (quantity * sales_price).quantize(Decimal('0.00'), ROUND_HALF_UP)
                demand["sales_amount"] += sales_amount

                other_data = {}
                if not is_main:
                    ratio = Decimal(p.ratio) if p.ratio else p.ratio
                    # if not ratio or ratio < 0:
                    #     raise ProductError(f"捆绑商品id: {product_id}比率错误")

                    product_info = product_map[str(p.product_id)]
                    if not product_info:
                        continue
                        # raise ProductError(f'商品product_id:{product_id}不存在')
                    units = product_info.get('units')
                    unit_id = None
                    accounting_unit_id = None
                    unit_rate = 1
                    unit = {}
                    accounting_unit = {}
                    if units:
                        order_flag = False
                        for u in units:
                            if u.get("order"):
                                unit_rate = float(u.get("rate", 1))
                                unit_id = int(u.get('id'))
                                order_flag = True
                                unit = unit_map.get(unit_id, {})
                            if u.get('default'):
                                accounting_unit_id = int(u.get('id'))
                                accounting_unit = unit.get(accounting_unit_id, {})
                        if not order_flag:
                            continue
                            # raise ProductError(f"此商品未设置订货单位，请检查主档设置, product_id:{product_id}")
                    # else:
                    #     raise ProductError(f"此商品未设置单位，请检查主档设置, product_id:{product_id}")
                    logging.info(f"unit_id: {unit}, {accounting_unit}, {product_info}")
                    other_data = {
                        'demand_product_id': k.father_id,
                        'ratio': ratio,
                        'configure': p.configure,
                        'unit_rate': unit_rate,
                        'unit_id': unit_id,
                        "unit_spec": unit.get("code", "无"),
                        "unit_name": unit.get('name', "无"),
                        'accounting_unit_id': accounting_unit_id,
                        'accounting_unit_name': accounting_unit.get('name'),
                        'accounting_unit_spec': accounting_unit.get("code"),
                    }

                product_dict = {
                    "id": k.id,
                    "partner_id": partner_id,
                    "demand_id": demand["id"],
                    "status": "INITED",
                    "product_id": int(product_id),
                    "product_code": p.product_code,
                    "product_name": p.product_name,
                    "category_id": int(p.category_id),
                    "category_name": category_map.get(int(p.category_id), {}).get('name', ''),
                    "arrival_days": p.arrival_days,
                    "unit_id": int(p.unit_id),
                    "unit_spec": unit.get("code", "无"),
                    "unit_name": unit.get('name', "无"),
                    "unit_rate": p.unit_rate,
                    'accounting_unit_id': p.accounting_unit_id,
                    'accounting_unit_name': accounting_unit.get('name'),
                    'accounting_unit_spec': accounting_unit.get("code"),
                    "quantity": quantity,
                    "accounting_quantity": convert_to_decimal(p.unit_rate) * quantity,
                    "min_quantity": p.min_quantity,
                    "max_quantity": p.max_quantity,
                    "increment_quantity": p.increment_quantity,
                    "tax_price": tax_price,
                    "cost_price": cost_price,
                    "sales_price": sales_price,
                    "tax_rate": tax_rate,
                    "amount": amount,
                    "sales_amount": sales_amount,
                    "storage_type": p.storage_type or product_info.get('storage_type'),
                    "created_by": user_id,
                    "updated_by": user_id,
                    "created_name": username,
                    "updated_name": username,
                    "extends": p.extends,
                    "approve_quantity": quantity,
                    "confirm_quantity": quantity,
                    "approve_amount": amount,
                    "confirm_amount": amount,
                    "approve_sales_amount": sales_amount,
                    "confirm_sales_amount": sales_amount,
                    'relation_type': k.relation_type
                }

                # if p.arrival_days and p.arrival_days.isdigit():
                #     product_dict["arrival_days"] = int(p.arrival_days)
                # else:
                #     product_dict["arrival_days"] = None
                # print('demand_products======',demand_products)

                if not is_main:
                    product_dict.update(other_data)
                    demand_relation_products.append(product_dict)
                else:
                    demand_products.append(product_dict)

            demands.append(demand)
            demand_logs.append(dict(
                partner_id=partner_id,
                demand_id=demand["id"],
                action=Action.INITED.code,
                action_name=Action.INITED.desc,
                end_status=demand["status"],
                platform=self.platform,
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
                trace_id=batch_id
            ))
        logging.info(f"demand_relation_products: {demand_relation_products}")
        res['result'] = sFD.create_f_demand(demands, demand_products, demand_logs=demand_logs,
                                            demand_relation_products=demand_relation_products)
        # [Kit.upload(partner_id, i['code'], KitEnum.FRS_DEMAND.code, Action.INITED.code)
        #  for i in demands] if demands else ...
        return res

    def list_franchisee_demand(self, request, partner_id, user_id, from_grpc=False):
        res = dict()
        start_date = self.utcTimestamp2datetime(request.start_date)
        end_date = self.utcTimestamp2datetime(request.end_date)
        status = list(request.status) if request.status else []
        received_bys = [convert_to_int(_id) for _id in request.received_bys] if request.received_bys else []
        franchisee_ids = [convert_to_int(_id) for _id in request.franchisee_ids] if request.franchisee_ids else []
        payment_ways = [str(way) for way in request.payment_ways] if request.payment_ways else []
        order_type_ids = [convert_to_int(_id) for _id in request.order_type_ids] if request.order_type_ids else []
        types = [_type for _type in request.types] if request.types else []
        code = request.code
        chain_type = request.chain_type
        bus_types = [bus_type for bus_type in request.bus_types] if request.bus_types else []
        sort = request.sort if request.sort else 'updated_at'
        order = request.order
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        ids = list(request.ids) if request.ids else []
        product_ids = list(request.product_ids) if request.product_ids else []
        if len(ids) == 0:
            ids = None
        if not status:
            status = self.show_status
        # 根据线路过滤出门店
        if request.line_doc_id:
            line_stores = metadata_service.get_store_list(relation_filters={"line_doc": [request.line_doc_id]},
                                                          return_fields="id", partner_id=partner_id,
                                                          user_id=user_id).get('rows', [])
            for store in line_stores:
                if int(store.get('id')) not in received_bys:
                    received_bys.append(int(store.get('id')))
        if not from_grpc:
            received_bys = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                   domain='boh.frs_hd_management',
                                                   branch_ids=received_bys)
        query_set = sFD.list_f_demand(partner_id=partner_id, start_date=start_date, received_bys=received_bys,
                                      franchisee_ids=franchisee_ids, payment_ways=payment_ways, types=types,
                                      order_type_ids=order_type_ids, end_date=end_date, status=status, code=code,
                                      offset=offset, limit=limit, chain_type=chain_type, bus_types=bus_types, ids=ids,
                                      order=order, product_ids=product_ids, include_total=include_total, sort=sort)
        if isinstance(query_set, tuple):
            total, demands = query_set
            res['total'] = total
        else:
            demands = query_set
        franchisee_ids = []
        for demand in demands:
            if demand.franchisee_id and demand.franchisee_id not in franchisee_ids:
                franchisee_ids.append(demand.franchisee_id)
            if demand.order_type_id not in order_type_ids:
                order_type_ids.append(demand.order_type_id)

        branch_map = get_branch_map(branch_ids=franchisee_ids, branch_type="FRANCHISEE", partner_id=partner_id,
                                    user_id=user_id, return_fields="id,code,name")
        order_type_map = self.get_order_type_map(order_type_ids=order_type_ids, partner_id=partner_id,
                                                 user_id=user_id, return_fields="id,code,name,audit_time,time_bucket")
        demand_list = []
        for demand in demands:
            row = demand.serialize(conv=True)
            order_type = order_type_map.get(demand.order_type_id, {})
            franchisee = branch_map.get(demand.franchisee_id, {})
            row['franchisee_name'] = franchisee.get('name')
            row['franchisee_code'] = franchisee.get('code')
            row['order_type_name'] = order_type.get('name')
            row['order_type_code'] = order_type.get('code')
            row['order_type_time'] = dict(order_time=order_type.get('time_bucket', []),
                                          audit_time=order_type.get('audit_time', []))
            row['attachments'] = eval(demand.attachments) if demand.attachments else []
            demand_list.append(row)
        res["rows"] = demand_list
        return res

    def get_demand_by_id(self, request, partner_id, user_id):
        demand_id = request.demand_id
        demand = sFD.get_demand_by_id(demand_id=demand_id, partner_id=partner_id)
        if not demand:
            raise OrderNotExistException("订货单不存在")
        # branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
        #                    domain='boh.frs_hd_management', branch_id=demand.received_by)
        branch_map = get_branch_map(branch_ids=[demand.franchisee_id], branch_type="FRANCHISEE", partner_id=partner_id,
                                    user_id=user_id, return_fields="id,code,name")
        order_type_map = self.get_order_type_map(order_type_ids=[demand.order_type_id], partner_id=partner_id,
                                                 user_id=user_id, return_fields="id,code,name,audit_time,time_bucket")
        franchisee = branch_map.get(demand.franchisee_id, {})
        order_type = order_type_map.get(demand.order_type_id, {})
        result = demand.serialize(conv=True)
        result['franchisee_name'] = franchisee.get('name')
        result['franchisee_code'] = franchisee.get('code')
        result['order_type_name'] = order_type.get('name')
        result['order_type_code'] = order_type.get('code')
        result['order_type_time'] = dict(order_time=order_type.get('time_bucket', []),
                                         audit_time=order_type.get('audit_time', []))
        result['attachments'] = literal_eval(demand.attachments) if demand.attachments else []
        # 查询仓库要货单号
        result['orders'] = []
        orders = receipt_service.list_receipt_demand(batch_id=demand_id, batch_type=[self.batch_type],
                                                     partner_id=partner_id, user_id=user_id)
        if orders:
            rows = orders.rows
            for row in rows:
                result['orders'].append(dict(
                    order_id=row.id,
                    order_code=row.code
                ))
        # 查询退款单
        result['refunds'] = []
        refunds = sFR.query_refund_by_main_ids(partner_id=partner_id, main_ids=[demand_id])
        for rd in refunds:
            result['refunds'].append(dict(
                refund_id=rd[0],
                refund_code=rd[3]
            ))
        return result

    def list_product(self, request, partner_id, user_id):
        res = dict()
        demand_id = request.demand_id
        sort = request.sort if request.sort else 'updated_at'
        order = request.order
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        # demand = sFD.get_demand_by_id(demand_id=demand_id, partner_id=partner_id)
        demand = session.query(sFD).filter_by(id=demand_id).first()
        if not demand:
            raise OrderNotExistException("订货单不存在")
        # branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
        #                    domain='boh.frs_hd_management', branch_id=demand.received_by)
        query_set = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id, limit=limit,
                                             sort=sort, order=order, offset=offset, include_total=include_total)
        if isinstance(query_set, tuple):
            total, demand_products = query_set
            res['total'] = total
        else:
            demand_products = query_set

        relation_products = demand.relation_products.filter(
            SFDPR.demand_product_id.in_(i.id for i in query_set if i.relation_type)).all()
        relation_products_dict = {}
        for p in relation_products:
            if p.demand_product_id in relation_products_dict:
                relation_products_dict[p.demand_product_id].append(p)
                continue
            relation_products_dict[p.demand_product_id] = [p]

        product_ids = []
        distribution = dict()
        demand_products_dict = {}
        expand = request.demand_id and request.expand
        # 临时紧急修改
        for p in demand_products + relation_products:
            if p.org_product_id and p.org_product_id not in product_ids:
                product_ids.append(p.org_product_id)
            if p.product_id and p.product_id not in product_ids:
                product_ids.append(p.product_id)
            if p.distribute_by:
                if p.distribution_type in distribution:
                    distribution[p.distribution_type].append(p.distribute_by)
                else:
                    distribution[p.distribution_type] = [p.distribute_by]

            if expand:
                if p.product_id not in demand_products_dict:
                    make_transient(p)
                    demand_products_dict[p.product_id] = p
                    continue

                demand_products_dict[p.product_id].confirm_quantity += p.confirm_quantity
                demand_products_dict[p.product_id].approve_quantity += p.approve_quantity
                demand_products_dict[p.product_id].quantity += p.quantity
                demand_products_dict[p.product_id].accounting_quantity += p.accounting_quantity

        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,model_name",
                                      partner_id=partner_id, user_id=user_id)
        distribution_map = self.get_distribution_map(distribution=distribution, partner_id=partner_id, user_id=user_id)
        product_list = []

        for product in demand_products_dict.values() or demand_products:
            org_pro = product_map.get(str(product.org_product_id), {})
            pro = product_map.get(str(product.product_id), {})
            dist = distribution_map.get(product.distribute_by)
            product_detail = product.serialize(conv=True)
            product_detail['org_product_id'] = product.org_product_id
            product_detail['org_product_code'] = org_pro.get('code')
            product_detail['org_product_name'] = org_pro.get('name')
            product_detail['product_code'] = pro.get('code') if pro.get('code') else product.product_code
            product_detail['product_name'] = pro.get('name') if pro.get('name') else product.product_name
            product_detail['product_spec'] = pro.get('model_name')
            product_detail['distribute_name'] = dist.get("name") if dist else ""
            if hasattr(product, 'relation_type'):
                del product_detail['relation_type']
            elif expand:
                del product_detail['demand_product_id'], product_detail['product_type'], product_detail['ratio']

            if product.id in relation_products_dict and not request.expand:
                rp_list = []
                for rp in relation_products_dict[product.id]:
                    r_org_pro = product_map.get(str(rp.org_product_id), {})
                    r_pro = product_map.get(str(rp.product_id), {})
                    r_dist = distribution_map.get(rp.distribute_by)
                    rp_detail = rp.serialize(conv=True)
                    rp_detail['org_product_id'] = rp.org_product_id
                    rp_detail['org_product_code'] = r_org_pro.get('code')
                    rp_detail['org_product_name'] = r_org_pro.get('name')
                    rp_detail['product_code'] = r_pro.get('code') if r_pro.get('code') else rp.product_code
                    rp_detail['product_name'] = r_pro.get('name') if r_pro.get('name') else rp.product_name
                    rp_detail['product_spec'] = r_pro.get('model_name')
                    rp_detail['distribute_name'] = r_dist.get("name") if r_dist else ""
                    rp_detail['ratio'] = str(rp_detail['ratio'] or '')
                    del rp_detail['demand_product_id'], rp_detail['product_type']
                    rp_list.append(rp_detail)
                product_detail['relation_products'] = rp_list

            product_list.append(product_detail)
        res["rows"] = product_list
        return res

    def update_products(self, request, partner_id, user_id, from_grpc=False):
        result = {}
        demand_id = request.demand_id
        updated_products = request.products
        action = request.status if request.status else Action.UPDATE.code

        # demand_db = sFD.get_demand_by_id(demand_id=demand_id, partner_id=partner_id)
        demand_db = session.query(sFD).filter_by(id=demand_id).first()
        if not demand_db:
            raise OrderNotExistException("订货单不存在")
        if not from_grpc:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.frs_hd_management', branch_id=demand_db.received_by)

        if action == Action.APPROVED.code:
            return self.allocate_distribute_by(demand_id, partner_id, user_id,)
        # 订单更新状态校验
        if demand_db.status not in ["SUBMITTED", "REJECTED", "CONFIRMED", "APPROVING", "R_APPROVE"]:
            raise DataValidationException("当前状态不允许更新！")
        if not updated_products:
            raise DataValidationException("更新需包含商品详情")
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        product_ids, main_product_dict = set(), {}
        for i in updated_products:
            product_ids.add(i.product_id)
            main_product_dict[int(i.product_id)] = Decimal(i.unit_rate)
            if i.relation_products:
                product_ids |= set(r.product_id for r in i.relation_products)
        # product_ids = [p.product_id for p in updated_products]
        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type,model_name",
                                      partner_id=partner_id, user_id=user_id)
        category_ids = []
        for _, p in product_map.items():
            if p.get('category'):
                category_ids.append(int(p.get('category')))
        category_map = get_category_map(category_ids, partner_id, user_id)

        # 单位
        unit_list = metadata_service.get_unit_list(partner_id=partner_id, user_id=user_id).get('rows', [])
        unit_map = {}
        if unit_list:
            for u in unit_list:
                unit_map[int(u['id'])] = u
        is_completed = True
        product_list = []
        relation_product_list = []
        product_logs = []
        has_distribute_by_products = []
        # 操作ID
        trace_id = get_guid()
        confirm_quantity = Decimal(0)
        confirm_amount = Decimal(0)
        confirm_sales_amount = Decimal(0)
        approve_quantity = Decimal(0)
        approve_amount = Decimal(0)
        approve_sales_amount = Decimal(0)

        key = namedtuple('key', ['type', 'product'])
        products_deque = deque([key('main', updated_products[0])])
        index, product_len = 0, len(updated_products)
        check_messages = []
        valid_products = {}
        free_products = {}
        if action == "CONFIRMED":
            free_products = products_manage_service.query_free_order_limit(main_product_dict.keys(), partner_id,
                                                                           user_id).get('rows', [])
            free_products = {int(i['product_id']): Decimal(i['limit_qty']) for i in free_products if 'limit_qty' in i}
            valid_products = products_manage_service.get_order_rule_by_store(store_id=demand_db.received_by,
                                                                             product_ids=main_product_dict.keys(),
                                                                             partner_id=partner_id,
                                                                             user_id=user_id).get('rows', {})

            valid_products = {int(vp['product_id']): vp['order_rule'] for vp in valid_products
                              if 'order_rule' in vp and 'product_id' in vp}
        # print('valid_products', valid_products)

        go_on_loop = True
        while products_deque:
        # for product in updated_products:
            p = products_deque.pop()
            product, is_main = p.product, p.type == 'main'
            if product.relation_products:
                products_deque.extend(key('bind', i) for i in product.relation_products[::-1])

            self._attach_product_attr(product, product_map, category_map, unit_map)
            product_log = {}
            if is_main:
                index += 1
                products_deque.append(key('main', updated_products[index])) if index < product_len else ...

                # TODO: 校验商品是否符合订货规则(起订量, 递增订量,最大订量)
                if is_main and action == "CONFIRMED":
                    if not product.confirm_quantity:
                        continue

                    vp = valid_products.get(product.product_id, {})
                    pass_check = False
                    if vp or (not vp and not product.tax_price):
                        if product.product_id in free_products:

                            unit_rate = Decimal(product.unit_rate or 0)
                            if unit_rate:
                                free_products[product.product_id] = free_products[product.product_id] / unit_rate

                        max_qty = -Decimal(vp.get('max_qty') or 0)
                        if not product.tax_price:
                            if product.product_id in free_products:
                                free_max_qty = -Decimal(free_products[product.product_id] or 0)
                                max_qty = max(max_qty, free_max_qty) if \
                                    vp.get('max_qty') and product.product_id in free_products else min(max_qty,
                                                                                                       free_max_qty)
                            elif not vp:
                                pass_check = True

                        max_qty = -max_qty
                        check = check_quantity(product.confirm_quantity, vp.get('min_qty'), vp.get('increase_qty'),
                                               max_qty) if vp else (pass_check or product.confirm_quantity <= max_qty)
                        if not check:
                            go_on_loop = False
                            msg = {"product_name": product.product_name,
                                   "product_code": product.product_code,
                                   "spec": product_map.get(str(product.product_id), {}).get("model_name", ""),
                                   "quantity": product.confirm_quantity,
                                   "min_quantity": float(vp.get('min_qty', 0)),
                                   "increment_quantity": float(vp.get('increase_qty', 0)),
                                   "max_quantity": float(max_qty), "unit": product.unit_name,
                                   "tax_price": product.tax_price, "amount": round(
                                    convert_to_decimal(product.tax_price) * convert_to_decimal(
                                        product.confirm_quantity), 2), "storage_type": product.storage_type,
                                   'id': product.id,
                                   'product_id': product.product_id
                                   }
                            check_messages.append(msg)

                if not go_on_loop:
                    continue

                updated_args = {
                    'id': product.id,
                    "partner_id": partner_id,
                    "storage_type": product.storage_type,
                    'product_id': product.product_id,
                    'product_code': product.product_code,
                    'product_name': product.product_name,
                    'category_id': product.category_id,
                    'category_name': product.category_name,
                    "unit_id": product.unit_id,
                    "unit_spec": product.unit_spec,
                    "unit_name": product.unit_name,
                    "unit_rate": product.unit_rate,
                    'accounting_unit_id': product.accounting_unit_id,
                    'accounting_unit_name': product.accounting_unit_name,
                    'accounting_unit_spec': product.accounting_unit_spec,
                    'updated_by': user_id,
                    'updated_name': operator_name,
                }
                product_log = dict(
                    partner_id=partner_id,
                    trace_id=trace_id,
                    demand_id=demand_id,
                    action=action,
                    action_name=Action.get_desc(action),
                    product_id=product.product_id,
                    product_code=product.product_code,
                    product_name=product.product_name,
                    unit_id=product.unit_id,
                    unit_name=product.unit_name,
                    created_by=user_id,
                    created_name=operator_name,
                    created_at=datetime.utcnow()
                )
            else:
                updated_args = {'id': product.id}
            # TODO: 校验商品是否符合订货规则(起订量, 递增订量,最大订量)
            if is_main and action == "CONFIRMED" and (product.min_quantity and product.increment_quantity and product.max_quantity) \
                    and product.confirm_quantity != 0:
                check = check_quantity(product.confirm_quantity,product.max_quantity, product.min_quantity, product.increment_quantity)
                if check:
                    msg = {"product_name": product.product_name,
                           "product_code": product.product_code,
                           "spec": product_map.get(str(product.product_id), {}).get("model_name", ""),
                           "quantity": product.approve_quantity,
                           "min_quantity": product.min_quantity, "incr_quantity": product.increment_quantity,
                           "max_quantity": product.max_quantity, "unit": product.unit_name,
                           "tax_price": product.tax_price, "amount": round(
                            convert_to_decimal(product.tax_price) * convert_to_decimal(
                            product.approve_quantity), 2), "storage_type": product.storage_type}
                    check_messages.append(msg)
            # 待审核状态更新审核数量
            if demand_db.status == "SUBMITTED":
                updated_args['approve_quantity'] = product.approve_quantity
                updated_args['approve_amount'] = (
                    convert_to_decimal(product.tax_price) * convert_to_decimal(
                        product.approve_quantity)).quantize(Decimal('0.00'), ROUND_HALF_UP)
                updated_args['approve_sales_amount'] = (
                    convert_to_decimal(product.sales_price) * convert_to_decimal(
                        product.approve_quantity)).quantize(Decimal('0.00'), ROUND_HALF_UP)

                # 确认数量要随着审核数量联动
                updated_args['confirm_quantity'] = updated_args['approve_quantity']
                updated_args['confirm_amount'] = updated_args['approve_amount']
                updated_args['confirm_sales_amount'] = updated_args['approve_sales_amount']

                approve_quantity += convert_to_decimal(product.approve_quantity)
                approve_amount += updated_args['approve_amount']
                approve_sales_amount += updated_args['approve_sales_amount']
                updated_args['is_approve_qty'] = True
                if is_main:
                    product_log['tax_price'] = str(product.tax_price)
                    product_log['sales_price'] = str(product.sales_price)
                    product_log['approve_quantity'] = str(product.approve_quantity)
                    product_log['approve_amount'] = str(updated_args['approve_amount'])
                    product_log['approve_sales_amount'] = str(updated_args['approve_sales_amount'])
            # 待确认状态更新确认数量
            elif demand_db.status == "R_APPROVE":
                updated_args['tax_price'] = product.tax_price
                updated_args['tax_rate'] = product.tax_rate
                updated_args['quantity'] = product.quantity
                updated_args['confirm_quantity'] = product.confirm_quantity
                updated_args['confirm_amount'] = (
                    convert_to_decimal(product.tax_price) * convert_to_decimal(
                        product.confirm_quantity)).quantize(Decimal('0.00'), ROUND_HALF_UP)
                updated_args['confirm_sales_amount'] = (
                    convert_to_decimal(product.sales_price) * convert_to_decimal(
                        product.confirm_quantity)).quantize(Decimal('0.00'), ROUND_HALF_UP)
                confirm_quantity += convert_to_decimal(product.confirm_quantity)
                confirm_amount += updated_args['confirm_amount']
                confirm_sales_amount += updated_args['confirm_sales_amount']
                updated_args['is_confirm_qty'] = True
                if is_main:
                    product_log['tax_price'] = str(product.tax_price)
                    product_log['sales_price'] = str(product.sales_price)
                    product_log['confirm_quantity'] = str(product.confirm_quantity)
                    product_log['confirm_amount'] = str(updated_args['confirm_amount'])
                    product_log['confirm_sales_amount'] = str(updated_args['confirm_sales_amount'])
            # 分配仓库
            else:
                # 只能更新未分配的商品
                if product.status != "INITED":
                    continue
                updated_args['distribute_by'] = product.distribute_by
                updated_args['distribution_type'] = product.distribution_type
                if product.arrival_days and product.arrival_days.isdigit():
                    updated_args['arrival_days'] = int(product.arrival_days)
                else:
                    updated_args["arrival_days"] = None
                # updated_args['arrival_days'] = product.arrival_days
                if product.org_product_id:
                    updated_args['org_product_id'] = product.org_product_id
                product_log['distribute_by'] = product.distribute_by
                product_log['distribution_type'] = product.distribution_type
                product_log['arrival_days'] = str(product.arrival_days)
            if updated_args.get('distribute_by') and action == "APPROVED":
                updated_args["status"] = "PROCESS"
            if not product.distribute_by:
                is_completed = False
            else:
                has_distribute_by_products.append(product.product_id)
            product_list.append(updated_args) if is_main else relation_product_list.append(updated_args)
            product_logs.append(product_log)
        if check_messages:
            result["msgs"] = check_messages
            return result
        # 订单操作log
        demand_log = dict(
            partner_id=partner_id,
            demand_id=demand_id,
            action=action,
            action_name=Action.get_desc(action),
            platform=self.platform,
            start_status=demand_db.status,
            end_status=action,
            created_by=user_id,
            created_name=operator_name,
            created_at=datetime.utcnow(),
            trace_id=trace_id
        )
        # 分配仓库
        if action == "APPROVED":
            # 判断是否全部分配
            if is_completed is True:
                status = "APPROVED"
            else:
                status = "APPROVING"
            # 全部没有仓库还是待分配has_distribute_by_products
            ps = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id)
            rs = demand_db.relation_products.all()
            had_distribute_by_products = 0
            for p in ps + rs:
                if p.distribution_type and p not in has_distribute_by_products:
                    had_distribute_by_products += 1
            if len(has_distribute_by_products) + had_distribute_by_products == 0:
                status = "CONFIRMED"
            demand = dict(
                id=demand_id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=operator_name,
                status=status,
                process_status='PROCESSING'
            )
            demand_log["end_status"] = status
            res = sFD.update_f_demand(update_data=demand, allow_status=["APPROVING", "CONFIRMED"],
                                      demand_logs=[demand_log], product_list=product_list, product_logs=product_logs,
                                      relation_products=relation_product_list)
            if res is True and is_completed:
                # 全部订单都要生成要货单,部分分配的不生成
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND,
                                    message=dict(demand_id=demand_id, partner_id=partner_id, username=operator_name,
                                                 user_id=user_id))
        # 总部审核订货单
        elif action == "R_APPROVE":
            if approve_quantity == 0:
                raise ProductError("合计审核数量不允许为0！")
            demand = dict(
                id=demand_id,
                partner_id=partner_id,
                approve_amount=approve_amount,
                approve_sales_amount=approve_sales_amount,
                updated_by=user_id,
                updated_name=operator_name,
                status=action
            )
            res = sFD.update_f_demand(update_data=demand, demand_logs=[demand_log], allow_status=["SUBMITTED"],
                                      product_list=product_list, product_logs=product_logs,
                                      relation_products=relation_product_list)
        # 总部确认订单
        elif action == "CONFIRMED":
            if confirm_quantity == 0:
                raise ProductError("合计确认数量不允许为0！")
            demand = dict(
                id=demand_id,
                partner_id=partner_id,
                confirm_amount=confirm_amount,
                confirm_sales_amount=confirm_sales_amount,
                updated_by=user_id,
                updated_name=operator_name,
                status=action
            )
            # 确认订单生成缺货退款单
            from ..franchisee.franchisee_refund import franchisee_refund_service
            refund_detail, refund_products, refund_log = franchisee_refund_service.build_lack_refund_details(
                partner_id=partner_id,
                user_id=user_id,
                demand_db=demand_db,
                demand_products=product_list,
                username=operator_name,
                batch_type=self.batch_type)
            res = sFD.update_f_demand(update_data=demand, demand_logs=[demand_log], allow_status=["R_APPROVE"],
                                      refund_detail=refund_detail, refund_products=refund_products,
                                      refund_log=refund_log, product_list=product_list, product_logs=product_logs,
                                      relation_products=relation_product_list)
            if res is True:
                # 批量分配仓库拆单
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND_DISTRIBUTE,
                                    message=dict(
                                        demand_ids=[demand_id],
                                        partner_id=partner_id,
                                        user_id=user_id
                                    ))

        # 更新商品
        else:
            # 更新商品没有状态变更
            del demand_log["start_status"]
            del demand_log["end_status"]
            if demand_db.status == "SUBMITTED" and approve_quantity == 0:
                raise ProductError("合计审核数量不允许为0！")
            if demand_db.status == "R_APPROVE" and confirm_quantity == 0:
                raise ProductError("合计确认数量不允许为0！")
            res = sFDP.update_products(product_list=product_list, demand_logs=[demand_log], product_logs=product_logs,
                                       relation_products=relation_product_list)
        if res is True:
            result['demand_id'] = demand_id
        else:
            raise StatusUnavailable("更新失败，更新状态不合法：{}".format(action))
        return result

    def build_new_product(self, pro, product_map: dict, price_map: dict, unit_map: dict, category_map: dict,
                          partner_id=None, user_id=None, username=None):
        """构建新增商品参数"""
        product = product_map.get(str(pro.product_id), {})
        if not product:
            raise ProductError(f"商品主档未找到-{pro.product_id}")
        # pro_price = price_map.get(pro.product_id)
        # if not pro_price:
        #     raise ProductError(f"商品未配置价格-{pro.product_id}")
        units = product.get('units')
        unit_id = None
        accounting_unit_id = None
        unit_rate = 1
        if units:
            order_flag = False
            for u in units:
                if u.get("order"):
                    unit_rate = float(u.get("rate", 1))
                    unit_id = int(u.get('id'))
                    order_flag = True
                if u.get('default'):
                    accounting_unit_id = int(u.get('id'))
            if not order_flag:
                raise ProductError("此商品未设置订货单位，请检查主档设置, product_id:{}".format(pro.product_id))
        else:
            raise ProductError("此商品未设置单位，请检查主档设置, product_id:{}".format(pro.product_id))
        unit = unit_map.get(unit_id, {})
        accounting_unit = unit_map.get(accounting_unit_id, {})
        tax_price = convert_to_decimal(pro.tax_price) if pro.tax_price else Decimal(0)
        tax_rate = convert_to_decimal(pro.tax_rate) if pro.tax_rate else Decimal(0)
        cost_price = tax_price / convert_to_decimal(1 + tax_rate / 100)
        sales_price = convert_to_decimal(pro.sales_price) if pro.sales_price else Decimal(0)
        # pro_price_list = pro_price.get('product_price', [])
        # sales_price = Decimal(0)
        # for price in pro_price_list:
        #     if price.get('price_type_id') == "2":
        #         sales_price = convert_to_decimal(price.get('tax_price', 0))
        confirm_quantity = convert_to_decimal(pro.confirm_quantity)
        confirm_amount = (tax_price * confirm_quantity).quantize(Decimal('0.00'), ROUND_HALF_UP)
        confirm_sales_amount = (sales_price * confirm_quantity).quantize(Decimal('0.00'), ROUND_HALF_UP)
        new_pro = {
            "partner_id": partner_id,
            "status": "INITED",
            "product_id": int(pro.product_id),
            "product_code": product.get("code"),
            "product_name": product.get("name"),
            "category_id": int(product.get('category', 0)),
            "category_name": category_map.get(int(product.get('category', 0)), {}).get('name', ''),
            # "arrival_days": pro.arrival_days,
            "unit_id": unit_id,
            "unit_spec": unit.get("code", "无"),
            "unit_name": unit.get('name', "无"),
            "unit_rate": unit_rate,
            'accounting_unit_id': accounting_unit_id,
            'accounting_unit_name': accounting_unit.get('name'),
            'accounting_unit_spec': accounting_unit.get("code"),
            "confirm_quantity": confirm_quantity,
            "is_confirm_qty": True,
            "confirm_amount": confirm_amount,
            "confirm_sales_amount": confirm_sales_amount,
            "min_quantity": pro.min_quantity,
            "max_quantity": pro.max_quantity,
            "increment_quantity": pro.increment_quantity,
            "tax_price": tax_price,
            "sales_price": sales_price,
            "cost_price": cost_price,
            "tax_rate": tax_rate,
            "storage_type": product.get('storage_type'),
            "created_by": user_id,
            "updated_by": user_id,
            "created_name": username,
            "updated_name": username,
            "extends": pro.extends
        }
        if pro.arrival_days and pro.arrival_days.isdigit():
            new_pro["arrival_days"] = int(pro.arrival_days)
        else:
            new_pro["arrival_days"] = None
            # print('demand_products======',demand_products)
        return new_pro

    def add_demand_product(self, partner_id, user_id, request, from_grpc=False):
        demand_db = sFD.get_demand_by_id(demand_id=request.demand_id, partner_id=partner_id)
        if not demand_db:
            raise OrderNotExistException("订货单不存在")
        result = {"demand_id": demand_db.id}
        if not from_grpc:
            branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                               domain='boh.frs_hd_management', branch_id=demand_db.received_by)
        # 状态校验
        if demand_db.status not in ["R_APPROVE"]:
            raise DataValidationException("当前状态不允许新增商品！")
        if len(request.products) == 0:
            raise DataValidationException("添加商品不允许为空")
        bind_products = dict()  # 将捆绑商品的数量合并  这里默认同一个捆绑商品在不同商品组合里价格一致
        product_ids = []
        for pro in request.products:
            if int(pro.product_id) not in product_ids:
                product_ids.append(int(pro.product_id))
            if not pro.bind_products:
                continue
            for p in pro.bind_products:
                if int(p.product_id) in bind_products.keys():
                    bind_products[int(p.product_id)].confirm_quantity += p.confirm_quantity
                else:
                    bind_products[int(p.product_id)] = p
                if int(p.product_id) not in product_ids:
                    product_ids.append(int(p.product_id))

        exist_products = sFDP.query_exist_product_by_demand_id(demand_id=request.demand_id, partner_id=partner_id)
        exist_pro_map = dict()
        for p in exist_products:
            exist_pro_map[p[0]] = dict(
                id=p[1],
                quantity=p[2],
                confirm_quantity=p[5],
                is_confirm_qty=p[6],
                tax_price=p[7],
                sales_price=p[8],
                product_code=p[9],
                product_name=p[10],
                unit_id=p[11],
                unit_name=p[12]
            )
        exist_product_ids = list(exist_pro_map.keys())
        # 剔除订单里存在的商品 再请求主档和价格中心
        product_ids = list(set(product_ids) - set(exist_product_ids))

        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type",
                                      partner_id=partner_id, user_id=user_id)
        category_ids = []
        for _, p in product_map.items():
            if p.get('category'):
                category_ids.append(int(p.get('category')))
        category_map = get_category_map(category_ids, partner_id, user_id)
        unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
        # # 查询商品价格(由于捆绑商品价格和常规商品不一样，后面需要改成让前端传)
        # price_map = self.get_product_price_map(partner_id=partner_id, user_id=user_id, store_id=demand_db.received_by,
        #                                        order_type_id=demand_db.order_type_id, product_ids=product_ids)
        price_map = None
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        ids = get_uuids(len(request.products))
        trace_id = get_guid()
        add_product_map = dict()
        update_products = []
        product_logs = []
        for inx, pro in enumerate(request.products):
            if pro.product_id in exist_product_ids:
                continue
            new_pro = self.build_new_product(pro, product_map, price_map, unit_map, category_map, partner_id=partner_id,
                                             user_id=user_id, username=username)
            new_pro['id'] = ids[inx]
            new_pro['demand_id'] = demand_db.id
            if int(pro.product_id) in add_product_map:
                add_product_map[int(pro.product_id)]['confirm_quantity'] += new_pro.get('confirm_quantity')
            else:
                add_product_map[int(pro.product_id)] = new_pro
        for product_id, pro in bind_products.items():
            # 存在合并
            if exist_pro_map.get(product_id):
                exist_pro = exist_pro_map.get(product_id)
                if exist_pro.get('is_confirm_qty'):
                    confirm_quantity = convert_to_decimal(pro.confirm_quantity) + exist_pro.get('confirm_quantity')
                else:
                    confirm_quantity = convert_to_decimal(pro.confirm_quantity) + exist_pro.get('approve_quantity')
                tax_price = exist_pro.get('tax_price')
                sales_price = exist_pro.get('sales_price')
                confirm_amount = (tax_price * confirm_quantity).quantize(Decimal('0.00'), ROUND_HALF_UP)
                confirm_sales_amount = (sales_price * confirm_quantity).quantize(Decimal('0.00'), ROUND_HALF_UP)
                update_products.append(dict(
                    id=exist_pro.get('id'),
                    confirm_quantity=confirm_quantity,
                    confirm_amount=confirm_amount,
                    confirm_sales_amount=confirm_sales_amount,
                    updated_by=user_id,
                    updated_name=username
                ))
                product_logs.append(dict(
                    partner_id=partner_id,
                    trace_id=trace_id,
                    demand_id=demand_db.id,
                    action=Action.ADD_PRODUCT.code,
                    action_name=Action.ADD_PRODUCT.desc,
                    product_id=product_id,
                    product_code=exist_pro.get('product_code'),
                    product_name=exist_pro.get('product_name'),
                    unit_id=exist_pro.get('unit_id'),
                    unit_name=exist_pro.get('unit_name'),
                    tax_price=str(tax_price),
                    sales_price=str(sales_price),
                    confirm_quantity=str(confirm_quantity),
                    confirm_amount=confirm_amount.quantize(Decimal('0.00'), ROUND_HALF_UP),
                    confirm_sales_amount=confirm_sales_amount.quantize(Decimal('0.00'), ROUND_HALF_UP),
                    created_by=user_id,
                    created_name=username,
                    created_at=datetime.utcnow(),
                ))
            # 不存在插入
            else:
                new_pro = self.build_new_product(pro, product_map, price_map, unit_map, category_map,
                                                 partner_id=partner_id,
                                                 user_id=user_id, username=username)
                new_pro['id'] = get_guid()
                new_pro['demand_id'] = demand_db.id
                if int(pro.product_id) in add_product_map:
                    add_product_map[int(pro.product_id)]['confirm_quantity'] += new_pro.get('confirm_quantity')
                else:
                    add_product_map[int(pro.product_id)] = new_pro
        if len(add_product_map) == 0 and len(update_products) == 0:
            return result
        for _, pro in add_product_map.items():
            product_logs.append(dict(
                partner_id=partner_id,
                trace_id=trace_id,
                demand_id=demand_db.id,
                action=Action.ADD_PRODUCT.code,
                action_name=Action.ADD_PRODUCT.desc,
                product_id=pro.get('product_id'),
                product_code=pro.get('product_code'),
                product_name=pro.get('product_name'),
                unit_id=pro.get('unit_id'),
                unit_name=pro.get('unit_name'),
                tax_price=str(pro.get('tax_price', '')),
                sales_price=str(pro.get('sales_price', '')),
                confirm_quantity=str(pro.get('confirm_quantity', '')),
                confirm_amount=str(pro.get('confirm_amount', '')),
                confirm_sales_amount=str(pro.get('confirm_sales_amount', '')),
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
            ))
        demand_log = dict(
            partner_id=partner_id,
            demand_id=demand_db.id,
            action=Action.ADD_PRODUCT.code,
            action_name=Action.ADD_PRODUCT.desc,
            platform=self.platform,
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow(),
            trace_id=trace_id
        )
        _ = sFDP.add_demand_product(add_products=list(add_product_map.values()), update_products=update_products,
                                    demand_logs=[demand_log], product_logs=product_logs)

        return result

    def deal_demand_by_ids(self, request, allow_status: list, partner_id=None, user_id=None):
        """变更订货单状态, 支持批量修改"""
        ret = {}
        action = request.action
        demand_ids = [int(_id) for _id in request.demand_ids] if request.demand_ids else []
        if len(demand_ids) == 0:
            raise DataValidationException("订单ID不可为空！")
        # 校验订单商品数量是否符合逻辑
        # TODO: PC端商品数量校验
        return_demands, r = {}, None
        if action in ('R_APPROVE', 'CONFIRMED'):
            demand_products_dict = {}
            all_product_ids = set()
            demand_products = sFDP.query_product_name_by_demand_ids(demand_ids=demand_ids, partner_id=partner_id)
            for p in demand_products:
                all_product_ids.add(int(p.product_id))
                if p.demand_id in demand_products_dict:
                    demand_products_dict[p.demand_id][p.product_id] = p
                    continue

                demand_products_dict[p.demand_id] = {p.product_id: p}

            demand_dbs = sFD.list_f_demand(ids=demand_ids)
            f_p = {'R_APPROVE': 'approve_quantity', 'CONFIRMED': 'confirm_quantity'}

            free_products_data = products_manage_service.query_free_order_limit(all_product_ids, partner_id,
                                                                                user_id).get('rows', [])

            franchisee_ids = set()
            for demand in demand_dbs:
                products: Dict = demand_products_dict.get(demand.id)
                valid_products = products_manage_service.get_order_rule_by_store(store_id=demand.received_by,
                                                                                 product_ids=products,
                                                                                 partner_id=partner_id,
                                                                                 user_id=user_id).get('rows', {})

                valid_products = {int(vp['product_id']): vp['order_rule'] for vp in valid_products
                                  if 'order_rule' in vp and 'product_id' in vp}
                print('valid_products', valid_products)

                free_products = {}
                for f in free_products_data:
                    fp_id = int(f['product_id'])
                    if fp_id not in products:
                        continue

                    if 'limit_qty' not in f:
                        continue

                    unit_rate = products[fp_id].unit_rate
                    if not unit_rate:
                        continue

                    free_products[fp_id] = Decimal(f['limit_qty']) / unit_rate

                for k, p in products.items():
                    quantity = getattr(p, f_p.get(action))
                    if not getattr(p, f_p.get(action)):
                        continue

                    vp = valid_products.get(p.product_id, {})
                    print('vp', vp)
                    if not vp and p.tax_price:
                        continue

                    max_qty = -Decimal(vp.get('max_qty') or 0)
                    if not p.tax_price:
                        if p.product_id not in free_products and not vp:
                            continue

                        free_max_qty = -Decimal(free_products.get(p.product_id, 0))
                        max_qty = max(max_qty, free_max_qty) if \
                            vp.get('max_qty') and p.product_id in free_products else min(max_qty, free_max_qty)

                    max_qty = -max_qty
                    check = check_quantity(quantity, vp.get('min_qty'), vp.get('increase_qty'), max_qty) if vp \
                        else quantity <= max_qty
                    if not check:
                        franchisee_ids.add(demand.franchisee_id)
                        return_demands[demand.id] = demand

            if franchisee_ids:
                franchisee_map = get_branch_map(branch_type="FRANCHISEE", partner_id=partner_id, user_id=user_id,
                                                branch_ids=franchisee_ids)
                r = {'msgs': [{"status": demand.status,
                               "store_name": demand.received_name,
                               "code": demand.code,
                               "order_type_id": demand.order_type_id,
                               "amount": demand.sum_price_tax,
                               "demand_date": datetime_2_timestamp(demand.demand_date),
                               "arrival_date": datetime_2_timestamp(demand.arrival_date),
                               "franchisee_name": franchisee_map.get(demand.franchisee_id, {}).get('name'),
                               } for demand in return_demands.values()]}

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        demand_logs = []
        final_demand_ids = []
        refund_detail = None
        refund_products = None
        refund_log = None
        trace_id = get_guid()
        demand_codes = {}

        demand_ids_set = set(demand_ids) - return_demands.keys()
        if not demand_ids_set:
            ret['result'] = "failed"
            return r or ret

        demand_ids = list(demand_ids_set)

        ds = namedtuple('demand', ['received_by', 'received_name'])
        if len(demand_ids) > 1:
            update_data = []
            # 批量只支持·确认收款·和·驳回·
            if action not in ['SUBMITTED', 'REJECTED', 'CONFIRMED']:
                raise StatusUnavailable("批量操作仅支持确认收款/确认订单和驳回!")
            for demand_id in demand_ids:
                demand_db = sFD.get_demand_by_id(partner_id=partner_id, demand_id=demand_id)
                if not demand_db:
                    continue
                if demand_db.status not in allow_status:
                    continue
                row = dict(
                    id=demand_id,
                    partner_id=partner_id,
                    updated_by=user_id,
                    updated_name=username,
                    status=action
                )
                if request.remark:
                    row['remark'] = request.remark
                if request.reject_reason:
                    row['reject_reason'] = request.reject_reason
                if action == "SUBMITTED":
                    action = SupplyPartnerAction.get_to_action(partner_id, PartnerActionModule.DEMAND.code, action)
                    row['pay_amount'] = demand_db.sum_price_tax
                    row['status'] = action
                update_data.append(row)
                final_demand_ids.append(demand_id)
                demand_logs.append(dict(
                    partner_id=partner_id,
                    demand_id=demand_id,
                    action=action,
                    action_name=Action.get_desc(action),
                    platform=Platform.HEX_MOBILE.code if action == "SUBMITTED" else self.platform,
                    start_status=demand_db.status,
                    end_status=action,
                    created_by=user_id,
                    created_name=username,
                    created_at=datetime.utcnow(),
                    trace_id=trace_id
                ))
                demand_codes[demand_db.code] = ds(demand_db.received_by, demand_db.received_name)
        else:
            demand_id = demand_ids[0]
            final_demand_ids.append(str(demand_id))
            demand_db = sFD.get_demand_by_id(partner_id=partner_id, demand_id=demand_id)
            if not demand_db:
                raise OrderNotExistException("订单不存在-{}".format(demand_id))
            update_data = dict(
                id=demand_id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                remark=request.remark,
                reject_reason=request.reject_reason,
                status=action
            )
            if action == "SUBMITTED":
                action = SupplyPartnerAction.get_to_action(partner_id, PartnerActionModule.DEMAND.code, action)
                update_data['pay_amount'] = demand_db.sum_price_tax
                update_data['status'] = action
            # 取消订单，创建单据退款记录
            if action == "CANCELLED" and demand_db.pay_amount:
                from supply.module.mobile.mobile_franchisee_demand import mobile_f_demand_service
                refund_detail, refund_products, refund_log = mobile_f_demand_service.build_refund_details(demand_db,
                                                                                                          partner_id,
                                                                                                          user_id,
                                                                                                          username)
            demand_logs.append(dict(
                partner_id=partner_id,
                demand_id=demand_id,
                action=action,
                action_name=Action.get_desc(action),
                platform=self.platform,
                start_status=demand_db.status,
                end_status=action,
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
                trace_id=trace_id
            ))
            demand_codes[demand_db.code] = ds(demand_db.received_by, demand_db.received_name)
        try:
            sync_confirm = True if action == "CONFIRMED" else False
            sync_approve = True if action == "R_APPROVE" else False
            res = sFD.update_f_demand(update_data=update_data, demand_logs=demand_logs, allow_status=allow_status,
                                      refund_detail=refund_detail, refund_products=refund_products,
                                      refund_log=refund_log,
                                      sync_confirm=sync_confirm, sync_approve=sync_approve, demand_ids=final_demand_ids)
            if res is True:
                ret["result"] = "success"
                if action == "CONFIRMED":
                    msg = dict(
                        demand_ids=final_demand_ids,
                        partner_id=partner_id,
                        user_id=user_id
                    )
                    # 批量生成缺货退款单
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.HANDLE_BATCH_OSS_FRANCHISEE_REFUND,
                                        message=msg)
                    # 批量分配仓库拆单
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND_DISTRIBUTE,
                                        message=msg)
            else:
                ret['result'] = "failed"

            [Kit.upload(partner_id, k, "加盟订货单", KitEnum.FRS_DEMAND_APPROVE.description,
                        actionStatus=res, storeId=v.received_by,
                        storeName=v.received_name,content="加盟订货单拆单成功")
             for k, v in demand_codes.items()] if action == 'R_APPROVE' and demand_codes else ...

        except Exception as e:
            [Kit.upload(partner_id, k, "加盟订货单", KitEnum.FRS_DEMAND_APPROVE.description,
                        actionStatus=False,
                        storeId=v.received_by, storeName=v.received_name,content="加盟订货单拆单失败："+str(e))
             for k, v in demand_codes.items()] if action == 'R_APPROVE' and demand_codes else ...
            raise e

        if refund_detail:
            res = credit_pay_service.Refund(refund_ids=[refund_detail['id']], partner_id=partner_id, user_id=user_id)
            logging.info(f'{refund_detail["id"]}: res->{res}')

        return ret

    def get_demand_history(self, request, partner_id, user_id):
        """订货历史记录
        PC返回商品变更详情"""
        res = {}
        demand_id = request.demand_id
        total, demand_logs = FdLogDB.get_franchisee_demand_log(demand_id=demand_id, partner_id=partner_id)
        product_logs = FdpLogDB.get_franchisee_demand_product_log(demand_id=demand_id, partner_id=partner_id)
        res["rows"] = []
        res["total"] = total
        pro_log_map = dict()
        distribution = dict()
        for log in product_logs:
            if log.distribute_by and log.distribution_type:
                if log.distribution_type in distribution:
                    distribution[log.distribution_type].append(convert_to_int(log.distribute_by))
                else:
                    distribution[log.distribution_type] = [convert_to_int(log.distribute_by)]
        distribution_map = self.get_distribution_map(distribution=distribution, partner_id=partner_id, user_id=user_id)
        for log in product_logs:
            log_row = log.serialize(conv=True)
            if log.distribute_by:
                dist = distribution_map.get(convert_to_int(log.distribute_by))
                log_row["distribute_name"] = dist.get('name') if dist else ''
            if log.trace_id in pro_log_map:
                pro_log_map[log.trace_id].append(log_row)
            else:
                pro_log_map[log.trace_id] = [log_row]
        for demand_log in demand_logs:
            row = demand_log.serialize(conv=True)
            row['platform_name'] = Platform.get_desc(demand_log.platform)
            row['details'] = pro_log_map.get(demand_log.trace_id, [])
            res['rows'].append(row)
        return res

    def get_product_distribution(self, request, partner_id, user_id):
        """
        获取商品可分配仓库/加工中心(并带实时库存)
        加盟商订货通过配送规则读取，若无配送仓库则返回全量仓库 (20221020确认)
        加盟商订货通过配送规则读取，若无配送仓库则返回空      (20221026确认)
        支持同时返回加工中心                              (20221114修改)
        """
        res = {}
        demand_id = request.demand_id
        product_ids = [int(_id) for _id in request.product_ids] if request.product_ids else []
        demand_db = sFD.get_demand_by_id(partner_id=partner_id, demand_id=demand_id)
        if not demand_db:
            raise OrderNotExistException("订单不存在-{}".format(demand_id))
        # 根据加盟商查询关联的贸易公司对应的仓库
        # franchisee = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id,
        #                                                id=demand_db.franchisee_id, schema_name="FRANCHISEE")
        # if not franchisee:
        #     logger.warning("Franchisee Not Found!")
        #     return res
        # fields = franchisee.get('fields') if franchisee.get('fields') else {}
        # company_id = fields.get('relation', {}).get('trading_company')
        # filters = {"relation.company_info": company_id}
        if not product_ids:
            products = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id)
            for p in products:
                product_ids.append(p.product_id)
        distribute_centers = []
        distribute_map = dict()
        # 全部仓库
        distributions = metadata_service.get_distribution_center_list(partner_id=partner_id, user_id=user_id,
                                                                      return_fields="id,code,name").get('rows', [])
        for dist in distributions:
            distribute_map[convert_to_int(dist.get('id'))] = dist
        # 全部加工中心
        machining_centers = metadata_service.get_machining_center_list(return_fields="id,code,name",
                                                                       partner_id=partner_id, user_id=user_id)
        for dist in machining_centers:
            distribute_map[convert_to_int(dist.get('id'))] = dist

        # 配送规则关联仓库
        valid_distribution_dict = dict()
        valid_distributions = products_manage_service.GetDistributionRule(store_ids=[demand_db.received_by],
                                                                          product_ids=product_ids,
                                                                          partner_id=partner_id,
                                                                          page_order=dict(limit=-1),
                                                                          user_id=user_id).get('rows', [])
        for vd in valid_distributions:
            distribute_id = convert_to_int(vd.get('distrcenter_id'))
            distribute_rule = vd.get('rule')
            arrival_days = convert_to_int(distribute_rule.get('planned_arrival_days', 0)) if distribute_rule else 0
            tmp_dist = dict(
                distribute_id=distribute_id,
                distribute_name=distribute_map.get(distribute_id, {}).get('name'),
                distribution_type=vd.get('distr_type')
            )
            distribute_centers.append(tmp_dist)
            valid_distribution_dict[convert_to_int(vd.get('product_id'))] = dict(distribute_id=distribute_id,
                                                                                 arrival_days=arrival_days,
                                                                                 distribution_type=vd.get('distr_type'))
        product_unit_dict,_ = metadata_service.get_product_units_dict(product_ids=product_ids,
                                                                    partner_id=partner_id, user_id=user_id)
        # 查询仓库商品实时库存
        product_inventory_dict, total = inventory_service.query_realtime_inventory(
            branch_id=list(distribute_map.keys()),
            partner_id=partner_id, user_id=user_id, product_ids=product_ids, multi_product=True,
            aggregate=True, detail=True)
        res['rows'] = []
        res['distribute_centers'] = distribute_centers
        for product_id in product_ids:
            row = dict(product_id=convert_to_int(product_id),
                       dists=[])
            product_inv = product_inventory_dict.get(str(product_id), [])
            distribute = valid_distribution_dict.get(product_id)
            if distribute:
                distribute_id = distribute.get('distribute_id')
                dist = dict(
                    distribute_id=distribute_id,
                    distribute_name=distribute_map.get(distribute_id, {}).get('name'),
                    arrival_days=str(distribute.get('arrival_days')),
                    distribution_type=distribute.get('distribution_type')
                )
                for p in product_inv:
                    if convert_to_int(p.get('branch_id')) == distribute_id:
                        order_unit_rate = 1
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get(
                                'order'):
                            order_unit = product_unit_dict.get(str(product_id)).get('order')
                            order_unit_rate = order_unit.get('rate') if order_unit else 1
                        qty = convert_to_decimal(p.get('quantity_avail'))
                        order_qty = qty / convert_to_decimal(order_unit_rate)
                        dist['qty'] = p.get('quantity_avail')  # 核算单位
                        dist['order_qty'] = order_qty  # 订货单位数量
                row['dists'].append(dist)
            # else:
            #     for p in product_inv:
            #         branch_id = convert_to_int(p.get('branch_id'))
            #         order_unit_rate = 1
            #         if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('order'):
            #             order_unit = product_unit_dict.get(str(product_id)).get('order')
            #             order_unit_rate = order_unit.get('rate') if order_unit else 1
            #         qty = convert_to_decimal(p.get('quantity_avail'))
            #         order_qty = qty / convert_to_decimal(order_unit_rate)
            #         row['dists'].append(dict(
            #                 distribute_id=branch_id,
            #                 distribute_name=distribute_map.get(branch_id, {}).get('name'),
            #                 qty=p.get('quantity_avail'),             # 核算单位
            #                 order_qty=order_qty                      # 订货单位数量
            #             ))
            res['rows'].append(row)
        res['total'] = len(product_ids)
        return res

    def handle_frs_demand(self, demand_id: int, partner_id: int, user_id: int, username: str = None):
        """处理订货单拆单"""
        try:
            if not username:
                username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            # demand_db = sFD.get_demand_by_id(partner_id=partner_id, demand_id=demand_id)
            demand_db = session.query(sFD).filter_by(id=demand_id, partner_id=partner_id).with_for_update().first()
            # 状态校验
            # 去掉部分分配状态
            if not demand_db or demand_db.status not in ["APPROVED"] or demand_db.process_status == "SUCCESS":
                return True
            # status=["PROCESS"]
            # demand_products = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id)
            handle_products = dict()
            can_do = True
            # demand_products = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id, status=["PROCESS"])
            demand_products = demand_db.products.filter_by(status='PROCESS').all() + \
                              demand_db.relation_products.filter_by(status='PROCESS').all()

            sp_ids, srp_ids = [], []
            demand_products_dict = {}
            for p in demand_products:
                # if p.confirm_quantity or not p.distribute_by:
                #     continue
                srp_ids.append(p.id) if hasattr(p, 'demand_product_id') else sp_ids.append(p.id)
                if p.product_id not in demand_products_dict:
                    make_transient(p)
                    demand_products_dict[p.product_id] = p
                    continue
                demand_products_dict[p.product_id].confirm_quantity += p.confirm_quantity

            configs = metadata_service.get_supply_config(partner_id=partner_id, domain='boh.store.order',
                                                         user_id=user_id)
            order_config, switch = configs.get('combine_storage_type', {}), configs.get('order_by_storage_type')
            frs_storage = {i: v[0] for v in order_config.get('franchisee', {}) for i in v}
            frs_storage = frs_storage if switch and frs_storage.keys() == DEFAULT_STORAGE_TYPE.keys() else DEFAULT_STORAGE_TYPE

            handle_products = dict()
            for k, p in demand_products_dict.items():
                if p.confirm_quantity == 0:
                    continue
                if not p.distribute_by:
                    can_do = False
                    continue
                if demand_db.type == Demand_type.FMD.code:
                    make_transient(p)
                    key = (p.distribution_type, p.distribute_by, frs_storage[p.storage_type])
                else:
                    key = (p.distribution_type, p.distribute_by, frs_storage[p.storage_type], p.arrival_days)
                if key in handle_products.keys():
                    if p.product_id in handle_products[key]:
                        handle_products[key][p.product_id].confirm_quantity += p.confirm_quantity
                        continue
                    handle_products[key][p.product_id] = p
                else:
                    handle_products[key] = {p.product_id: p}
            if not can_do:
                return True
            for key, products in handle_products.items():
                pro_list = []
                # success_pro = []
                distribution_type = key[0]
                distribute_by = key[1]
                storage_type = key[2]
                order_id = get_guid()
                order_code = Supply_doc_code.get_code_by_type("STORE_DM", demand_db.partner_id, None)
                pro_code_str = ""
                p_ids, rp_ids = [], []
                for p in products.values():
                    pro_list.append(dict(
                        order_id=order_id,
                        order_by=demand_db.received_by,
                        product_id=p.product_id,
                        product_code=p.product_code,
                        product_name=p.product_name,
                        storage_type=storage_type,
                        category_id=p.category_id,
                        category_name=p.category_name,
                        is_confirmed=False,
                        partner_id=partner_id,
                        created_by=user_id,
                        created_name=username,
                        updated_by=user_id,
                        updated_name=username,
                        order_quantity=p.confirm_quantity,
                        unit_id=p.unit_id,
                        unit_name=p.unit_name,
                        unit_spec=p.unit_spec,
                        unit_rate=p.unit_rate,
                        sale_type='',
                        cost_price=p.cost_price,
                        tax_price=p.tax_price,
                        tax_rate=p.tax_rate
                    ))
                    # success_pro.append(dict(
                    #     id=p.id,
                    #     partner_id=partner_id,
                    #     status="SUCCESS"
                    # ))
                    pro_code_str += str(p.product_code)
                    p_ids.append(p.id) if hasattr(p, 'demand_product_id') else rp_ids.append(p.id)
                separate_no = generate_hash_md5(str(demand_db.id) + pro_code_str)
                demand_date = datetime_2_timestamp(demand_db.demand_date)
                if demand_db.type == Demand_type.FMD.code:
                    expect_date = demand_db.arrival_date
                else:
                    arrival_days = key[3] if key[3] else 0
                    expect_date = demand_db.demand_date + timedelta(days=arrival_days)
                entity = receipt_service.create_franchisee_order(batch_id=demand_db.id,
                                                                 batch_code=demand_db.code,
                                                                 order_id=order_id, order_code=order_code,
                                                                 main_branch_type="FS",
                                                                 demand_type=demand_db.type,
                                                                 batch_type=self.batch_type,
                                                                 receive_by=demand_db.received_by,
                                                                 delivery_by=distribute_by,
                                                                 order_type_id=demand_db.order_type_id,
                                                                 storage_type=storage_type, distr_type=distribution_type,
                                                                 demand_date=demand_date,
                                                                 franchisee_id=demand_db.franchisee_id,
                                                                 delivery_date=None, expect_date=expect_date,
                                                                 products=pro_list, partner_id=partner_id,
                                                                 user_id=user_id, separate_no=separate_no,
                                                                 receive_name=demand_db.received_name,
                                                                 bus_type=demand_db.bus_type)
                if entity.get("id"):
                    logger.info("订货单生成要货单-{}-{}".format(entity.get("id"), entity.get("status")))
                    # 更新商品生单状态
                    demand_db.products.filter(sFDP.id.in_(p_ids)).update({
                        sFDP.partner_id: partner_id, sFDP.status: 'SUCCESS'}, synchronize_session=False) if p_ids else ...

                    demand_db.relation_products.filter(SFDPR.id.in_(rp_ids)).update({
                        SFDPR.partner_id: partner_id, SFDPR.status: 'SUCCESS'},
                        synchronize_session=False) if rp_ids else ...
                    session.commit()
                    # sFDP.update_products(product_list=success_pro)
                else:
                    logger.warning("订货单生成要货单失败: {}".format(entity))
            return True
        except Exception as e:
            session.rollback()
            logging.info(f'订单{demand_id} roll back')
        finally:
            session.close()

    def list_franchisee_demand_to_demind(self, partner_id, user_id, request):
        store_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                            domain='boh.store_bi', branch_ids=request.store_ids)
        res = dict()
        demand_date = self.utcTimestamp2datetime(request.demand_date)
        status = request.status
        geo_region_ids = [convert_to_int(_id) for _id in request.geo_region_ids] if request.geo_region_ids else []
        branch_region_ids = [convert_to_int(_id) for _id in
                             request.branch_region_ids] if request.branch_region_ids else []
        franchise_region_ids = [convert_to_int(_id) for _id in
                                request.franchise_region_ids] if request.franchise_region_ids else []
        pre_store_ids = [convert_to_int(_id) for _id in store_ids] if store_ids else []
        request_store_ids = pre_store_ids
        type_ids = [convert_to_int(_id) for _id in request.type_ids] if request.type_ids else []

        limit = request.limit
        offset = request.offset
        ###
        geo_region_store_ids = []
        branch_region_store_ids = []
        franchise_region_store_ids = []
        if len(geo_region_ids) > 0:
            ##schema_name='store'
            relation_filters = {"geo_region": [str(b_id) for b_id in geo_region_ids]}
            ret = metadata_service.list_entity(schema_name='store', relation_filters=relation_filters,
                                               return_fields='id',
                                               partner_id=partner_id,
                                               user_id=user_id, ).get('rows', [])
            ret_store_ids = []
            for r in ret:
                if int(r['id']) not in geo_region_store_ids:
                    geo_region_store_ids.append(int(r['id']))
                ret_store_ids.append(int(r['id']))
            for r_store in request_store_ids:
                if r_store not in ret_store_ids:
                    pre_store_ids.remove(r_store)
        if len(branch_region_ids) > 0:
            relation_filters = {"branch_region": [str(b_id) for b_id in branch_region_ids]}
            ret = metadata_service.list_entity(schema_name='store', relation_filters=relation_filters,
                                               return_fields='id',
                                               partner_id=partner_id,
                                               user_id=user_id, ).get('rows', [])
            ret_store_ids = []

            for r in ret:
                if int(r['id']) not in branch_region_store_ids:
                    branch_region_store_ids.append(int(r['id']))
                ret_store_ids.append(int(r['id']))
            for r_store in request_store_ids:
                if r_store not in ret_store_ids:
                    pre_store_ids.remove(r_store)
        if len(franchise_region_ids) > 0:
            relation_filters = {"franchisee_region": [str(b_id) for b_id in franchise_region_ids]}
            ret = metadata_service.list_entity(schema_name='store', relation_filters=relation_filters,
                                               return_fields='id',
                                               partner_id=partner_id,
                                               user_id=user_id, ).get('rows', [])
            ret_store_ids = []
            for r in ret:
                if int(r['id']) not in franchise_region_store_ids:
                    franchise_region_store_ids.append(int(r['id']))
                ret_store_ids.append(int(r['id']))
            for r_store in request_store_ids:
                if r_store not in ret_store_ids:
                    pre_store_ids.remove(r_store)
        if len(request_store_ids) == 0:
            ##交集
            c = set(geo_region_store_ids) & set(branch_region_store_ids)
            d = set(c) & set(franchise_region_store_ids)
            pre_store_ids = list(d)
        ##先拿订货单
        today = datetime.today()
        start_date = datetime(demand_date.year, demand_date.month, demand_date.day)
        end_date = datetime(demand_date.year, demand_date.month, demand_date.day + 1)
        query_set = sFD.get_no_create_f_demand(partner_id, start_date, end_date, pre_store_ids)
        demand_map = {}
        store_ids = []
        for r in query_set:
            order_type_id = 1
            if r[0]:
                demand_map[r[1]] = r[0]
            else:
                demand_map[r[1]] = order_type_id
        if status == 'ONORDER':
            ##把未订货的门店排除
            for store in pre_store_ids:
                if demand_map.get(store):
                    store_ids.append(store)
            if len(store_ids) == 0:
                return res
        elif status == 'DISORDER':
            for store in pre_store_ids:
                if not demand_map.get(store):
                    store_ids.append(store)
            ##满足订货类型的加入未订货查询
            if len(query_set) > 0:
                for store in pre_store_ids:
                    if demand_map.get(store):
                        ##没有出现的订货类型加入查询
                        pass
        else:
            store_ids = pre_store_ids
        # print('store_ids====', store_ids)
        if len(store_ids) == 0 and (
                len(geo_region_ids) != 0 or len(branch_region_ids) != 0 or len(franchise_region_ids) != 0):
            return res
        ##获取所有可订货数据
        type_id = None
        if len(type_ids) > 0:
            type_id = type_ids[0]
        if len(store_ids) == 0 and (
                len(geo_region_ids) != 0 or len(branch_region_ids) != 0 or len(franchise_region_ids) != 0):
            return res
        ##获取所有可订货数据
        type_id = None
        if len(type_ids) > 0:
            type_id = type_ids[0]
        ret = products_manage_service.GetRangeOfOrder(store_ids=store_ids, type_ids=type_ids,
                                                      time_bucket=[str(demand_date)],
                                                      partner_id=partner_id, user_id=user_id, limit=limit,
                                                      offset=offset)
        # print('ret=====',ret)
        rows = []
        if ret.get('rows'):
            store_ids = []
            for r in ret.get('rows', []):
                if int(r['store_id']) not in store_ids:
                    store_ids.append(int(r['store_id']))

            return_rows = metadata_service.list_entity(schema_name='store', ids=store_ids,
                                                       return_fields="id,code,name",
                                                       relation='all',
                                                       partner_id=partner_id, user_id=user_id).get('rows', [])
            # print('return_rows',return_rows)
            store_relation_map = {}
            branch_region_ids = []
            geo_region_ids = []
            franchise_region_ids = []
            for r in return_rows:
                branch_region = 0
                geo_region = 0
                franchise_region = 0
                if r.get('fields', {}).get('relation'):
                    branch_region = r['fields']['relation'].get('branch_region', 0)
                    if branch_region and branch_region not in branch_region_ids:
                        branch_region_ids.append(int(branch_region))
                    geo_region = r['fields']['relation'].get('geo_region', 0)
                    if geo_region and geo_region not in geo_region_ids:
                        geo_region_ids.append(int(geo_region))
                    franchise_region = r['fields']['relation'].get('franchisee_region', 0)
                    if franchise_region and franchise_region not in franchise_region_ids:
                        franchise_region_ids.append(int(franchise_region))
                store_relation_map[int(r['id'])] = [int(branch_region), int(geo_region), int(franchise_region)]
            branch_region_map = {}
            geo_region_map = {}
            franchise_region_map = {}
            # print('store_relation_map',store_relation_map)
            if len(branch_region_ids) > 0:
                return_rows = metadata_service.list_entity(schema_name='branch_region', ids=branch_region_ids,
                                                           return_fields="id,code,name",
                                                           partner_id=partner_id, user_id=user_id).get('rows', [])
                for r in return_rows:
                    branch_region_map[int(r['id'])] = r.get('fields', {}).get('name', "")
            if len(geo_region_ids) > 0:
                return_rows = metadata_service.list_entity(schema_name='geo_region', ids=geo_region_ids,
                                                           return_fields="id,code,name",
                                                           partner_id=partner_id, user_id=user_id).get('rows', [])
                for r in return_rows:
                    geo_region_map[int(r['id'])] = r.get('fields', {}).get('name', "")
            if len(franchise_region_ids) > 0:
                return_rows = metadata_service.list_entity(schema_name='franchisee_region', ids=franchise_region_ids,
                                                           return_fields="id,code,name",
                                                           partner_id=partner_id, user_id=user_id).get('rows', [])
                for r in return_rows:
                    franchise_region_map[int(r['id'])] = r.get('fields', {}).get('name', "")
            # print('branch_region_map',branch_region_map)
            # print('geo_region_map', geo_region_map)
            if demand_date.year == 1970:
                demand_date = datetime(today.year, today.month, today.day)
            for r in ret.get('rows', []):
                row = {}
                row['store_name'] = r.get('store_name')
                row['store_code'] = r.get('store_code')
                row['type_name'] = r.get('type_name')
                row['demand_date'] = self.get_timestamp(demand_date)
                if store_relation_map.get(int(r.get('store_id'))):
                    # if branch_region_map.get(store_relation_map[int(r.get('store_id'))][0]],''):
                    row['branch_region_name'] = branch_region_map.get(store_relation_map[int(r.get('store_id'))][0], '')
                    row['geo_region_name'] = geo_region_map.get(store_relation_map[int(r.get('store_id'))][1], '')
                    row['franchise_region_name'] = franchise_region_map.get(
                        store_relation_map[int(r.get('store_id'))][2], '')
                row['status'] = "DISORDER"
                if demand_map.get(int(r.get('store_id'))):
                    row['status'] = "ONORDER"
                rows.append(row)
            res["rows"] = rows
            res["total"] = int(ret.get('total', 0))
        return res

    def list_franchisee_demand_to_demind_usejion(self, partner_id, user_id, request):
        logging.info("list_franchisee_demand_to_demind_usejion-request.store_ids:{}".format(request.store_ids))
        store_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                            domain='boh.frs_store_bi', branch_ids=request.store_ids)
        # store_ids = []
        logging.info("list_franchisee_demand_to_demind_usejion-store_ids:{}".format(store_ids))
        res = dict()
        # demand_date = self.utcTimestamp2datetime(request.demand_date)
        start_demand_date = self.utcTimestamp2datetime(request.start_demand_date)
        end_demand_date = self.utcTimestamp2datetime(request.end_demand_date)

        if (end_demand_date - start_demand_date).days > 6:
            raise DataValidationException('时间范围不能超过7天')

        status = request.status
        geo_region_ids = [convert_to_int(_id) for _id in request.geo_region_ids] if request.geo_region_ids else []
        branch_region_ids = [convert_to_int(_id) for _id in
                             request.branch_region_ids] if request.branch_region_ids else []
        franchise_region_ids = [convert_to_int(_id) for _id in
                                request.franchise_region_ids] if request.franchise_region_ids else []
        pre_store_ids = [convert_to_int(_id) for _id in store_ids] if store_ids else []
        type_ids = [convert_to_int(_id) for _id in request.type_ids] if request.type_ids else []

        limit = request.limit
        offset = request.offset
        relation_filters = {}
        if len(geo_region_ids) > 0:
            relation_filters["geo_region"] = [str(b_id) for b_id in geo_region_ids]
        if len(branch_region_ids) > 0:
            relation_filters["branch_region"] = [str(b_id) for b_id in branch_region_ids]
        if len(franchise_region_ids) > 0:
            relation_filters["franchisee_region"] = [str(b_id) for b_id in franchise_region_ids]

        ret = metadata_service.list_entity(schema_name='store', relation_filters=relation_filters,
                                           return_fields='id',
                                           ids=pre_store_ids,
                                           partner_id=partner_id,
                                           user_id=user_id, ).get('rows', [])
        pre_store_ids = []
        for r in ret:
            pre_store_ids.append(int(r['id']))
        if len(pre_store_ids) == 0:
            return res
        print('pre_store_ids', pre_store_ids)
        ##先拿订货单
        today = datetime.today()
        # if demand_date.year == 1970:
        #     demand_date = today
        # logger.info("get_no_create_f_demand_usejoin {}".format(demand_date))
        ret, total = sFD.get_no_create_f_demand_usejoin(partner_id=partner_id, start_demand_date=start_demand_date,
                                                        end_demand_date=end_demand_date,
                                                        store_ids=pre_store_ids,
                                                        limit=limit, offset=offset, status=status, type_ids=type_ids)
        print('ret', ret)
        if len(ret) > 0:
            store_ids = []
            type_ids = []
            for r in ret:
                if int(r['store_id']) not in store_ids:
                    store_ids.append(int(r['store_id']))
                if int(r['type_id']) not in type_ids:
                    type_ids.append(int(r['type_id']))

            return_rows = metadata_service.list_entity(schema_name='store', ids=store_ids,
                                                       return_fields="id,code,name",
                                                       relation='all',
                                                       partner_id=partner_id, user_id=user_id).get('rows', [])
            # print('return_rows',return_rows)
            store_relation_map = {}
            store_name_map = {}
            branch_region_ids = []
            geo_region_ids = []
            franchise_region_ids = []
            for r in return_rows:
                branch_region = 0
                geo_region = 0
                franchise_region = 0
                if r.get('fields', {}).get('relation'):
                    branch_region = r['fields']['relation'].get('branch_region', 0)
                    if branch_region and branch_region not in branch_region_ids:
                        branch_region_ids.append(int(branch_region))
                    geo_region = r['fields']['relation'].get('geo_region', 0)
                    if geo_region and geo_region not in geo_region_ids:
                        geo_region_ids.append(int(geo_region))
                    franchise_region = r['fields']['relation'].get('franchisee_region', 0)
                    if franchise_region and franchise_region not in franchise_region_ids:
                        franchise_region_ids.append(int(franchise_region))
                store_name_map[int(r['id'])] = [r.get('fields', {}).get('name'), r.get('fields', {}).get('code')]
                store_relation_map[int(r['id'])] = [int(branch_region), int(geo_region), int(franchise_region)]
            branch_region_map = {}
            geo_region_map = {}
            franchise_region_map = {}
            type_name_map = {}
            # print('store_relation_map',store_relation_map)
            if len(branch_region_ids) > 0:
                return_rows = metadata_service.list_entity(schema_name='branch_region', ids=branch_region_ids,
                                                           return_fields="id,code,name",
                                                           partner_id=partner_id, user_id=user_id).get('rows', [])
                for r in return_rows:
                    branch_region_map[int(r['id'])] = r.get('fields', {}).get('name', "")
            if len(geo_region_ids) > 0:
                return_rows = metadata_service.list_entity(schema_name='geo_region', ids=geo_region_ids,
                                                           return_fields="id,code,name",
                                                           partner_id=partner_id, user_id=user_id).get('rows', [])
                for r in return_rows:
                    geo_region_map[int(r['id'])] = r.get('fields', {}).get('name', "")
            if len(franchise_region_ids) > 0:
                return_rows = metadata_service.list_entity(schema_name='franchisee_region', ids=franchise_region_ids,
                                                           return_fields="id,code,name",
                                                           partner_id=partner_id, user_id=user_id).get('rows', [])
                for r in return_rows:
                    franchise_region_map[int(r['id'])] = r.get('fields', {}).get('name', "")
            if len(type_ids) > 0:
                return_rows = metadata_service.list_entity(schema_name='order_type', ids=type_ids,
                                                           return_fields="id,code,name",
                                                           partner_id=partner_id, user_id=user_id).get('rows', [])
                for r in return_rows:
                    type_name_map[int(r['id'])] = r.get('fields', {}).get('name', "")
            # print('branch_region_map',branch_region_map)
            # print('geo_region_map', geo_region_map)
            rows = []
            for r in ret:
                row = {}
                # row['type_name'] = r.get('type_name')
                # row['demand_date'] = self.get_timestamp(demand_date)
                row['demand_date'] = self.get_timestamp(r['demand_date'])
                if store_name_map.get(int(r['store_id'])):
                    row['store_name'] = store_name_map.get(int(r['store_id']))[0]
                    row['store_code'] = store_name_map.get(int(r['store_id']))[1]
                if type_name_map.get(int(r['type_id'])):
                    row['type_name'] = type_name_map.get(int(r['type_id']))
                if store_relation_map.get(int(r['store_id'])):
                    # if branch_region_map.get(store_relation_map[int(r.get('store_id'))][0]],''):
                    row['branch_region_name'] = branch_region_map.get(store_relation_map[int(r['store_id'])][0], '')
                    row['geo_region_name'] = geo_region_map.get(store_relation_map[int(r['store_id'])][1], '')
                    row['franchise_region_name'] = franchise_region_map.get(
                        store_relation_map[int(r['store_id'])][2], '')
                # print('id=', r['id'], r['status'])
                row['status'] = 'ONORDER' if r['had_ordered'] else 'DISORDER'
                # if status != "" and status != "ALL":
                #     row['status'] = status
                # else:
                    ##["P_SUBMIT", "SUBMITTED", "CONFIRMED", "REFUNDING", "REFUNDED", "APPROVING","APPROVED", "REJECTED"]
                    # row['status'] = "DISORDER"
                    # if operator.contains(str(r['status']), "PREPARE") or \
                    #         operator.contains(str(r['status']), "INVALID") or \
                    #         operator.contains(str(r['status']), "CANCELLED"):
                    #     row['status'] = "DISORDER"
                    # if r['id'] and (operator.contains(str(r['status']), "P_SUBMIT") or
                    #                 operator.contains(str(r['status']), "SUBMITTED") or
                    #                 operator.contains(str(r['status']), "CONFIRMED") or
                    #                 operator.contains(str(r['status']), "REFUNDING") or
                    #                 operator.contains(str(r['status']), "REFUNDED") or
                    #                 operator.contains(str(r['status']), "APPROVING") or
                    #                 operator.contains(str(r['status']), "APPROVED") or
                    #                 operator.contains(str(r['status']), "REJECTED")):
                    #     row['status'] = "ONORDER"
                rows.append(row)
            res["rows"] = rows
            res["total"] = total
        return res

    def batch_deal_demand(self, request, partner_id, user_id, allow_status=None, action=None):
        """按查询条件批量操作订货单
        // 批量更新方式-batch_method:
        // "ALL"  全量按条件(支持排除部分订单：详见参数exclude_ids)
        // "SELF" 部分按订单(选择部分订单批量提交：详见参数include_ids)
        :param request:
        :param partner_id:
        :param user_id:
        :param allow_status: 别允许操作的状态
        :param action: 确认-CONFIRMED、还原-RESET
        :return:
        """
        start_date = self.timestamp2datetime(request.start_date)
        end_date = self.timestamp2datetime(request.end_date)
        received_bys = [convert_to_int(_id) for _id in request.received_bys] if request.received_bys else []
        order_type_ids = [convert_to_int(_id) for _id in request.order_type_ids] if request.order_type_ids else []
        bus_types = [_type for _type in request.bus_types] if request.bus_types else []
        exclude_ids = list(request.exclude_ids)
        include_ids = list(request.include_ids)
        company_ids = request.company_ids
        ret = dict(result=True)
        if request.batch_method == "ALL":
            if (end_date -  start_date).days > 60:
                raise DataValidationException('时间范围不能超过60天')

            received_bys = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                   domain='boh.frs_hd_management',
                                                   branch_ids=received_bys)

            if len(company_ids)>0:
                company_ids_str = [str(i) for i in company_ids]
                return_rows = metadata_service.list_entity(schema_name='store', ids=received_bys,
                                                           filters = {"relation.company_info__in":company_ids_str},
                                                           return_fields="id,code,name",
                                                           relation='all',
                                                           partner_id=partner_id, user_id=user_id).get('rows', [])
                received_bys = []
                for r in return_rows:
                    received_bys.append(int(r['id']))
            demands = sFD.list_f_demand(partner_id=partner_id, start_date=start_date, received_bys=received_bys,
                                        bus_types=bus_types, order_type_ids=order_type_ids, end_date=end_date,
                                        status=allow_status,exclude_ids=exclude_ids, global_session=True,
                                        for_update=True)
        elif request.batch_method == "SELF":
            if len(include_ids) == 0:
                return ret
            demands = sFD.list_f_demand(partner_id=partner_id, ids=include_ids, status=allow_status,
                                        exclude_ids=exclude_ids, global_session=True, for_update=True)
        else:
            raise DataValidationException("Param `batch_method` is Required!")
        if len(demands) == 0:
            return ret
        demand_dbs = filter(lambda d: d.__getattribute__('id') not in exclude_ids, demands)
        # 批量确认
        if action == "CONFIRMED":
            r = self.batch_confirm_demand(demand_dbs, partner_id=partner_id, user_id=user_id, action=action)
            if not isinstance(r, bool):
                return r
            ret['result'] = r
        # 批量还原
        elif action == "RESET":
            ret['result'] = self.batch_reset_demand(demand_dbs, partner_id=partner_id, user_id=user_id)
        # 批量打回
        else:
            return ret
        if ret.get('result') is True:
            ret['description'] = 'success'
        else:
            ret['description'] = 'failed'
        return ret

    def batch_confirm_demand(self, demand_dbs, partner_id, user_id, action=None):
        """按查询条件批量确认订货单
        1、同步确认数量、金额
        2、批量生成缺货退款单
        3、批量按配送规则分配仓库
        4、批量拆单
        """
        update_data = []
        demand_logs = []
        final_demand_ids = []
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        trace_id = get_guid()
        demand_products_dict = {}
        demand_dbs = list(demand_dbs)
        all_product_ids = set()
        demand_products = sFDP.query_product_name_by_demand_ids(demand_ids={i.id for i in demand_dbs},
                                                                partner_id=partner_id)
        for p in demand_products:
            all_product_ids.add(p.product_id)
            if p.demand_id in demand_products_dict:
                demand_products_dict[p.demand_id][p.product_id] = p
                continue

            demand_products_dict[p.demand_id] = {p.product_id: p}

        msgs, franchisee_ids, return_demands = [], set(), []
        free_products_data = products_manage_service.query_free_order_limit(all_product_ids, partner_id,
                                                                            user_id).get('rows', [])

        for demand in demand_dbs:
            go_on_loop = True
            products: Dict = demand_products_dict.get(demand.id)
            valid_products = products_manage_service.get_order_rule_by_store(store_id=demand.received_by,
                                                                             product_ids=products,
                                                                             partner_id=partner_id,
                                                                             user_id=user_id).get('rows', {})

            valid_products = {int(vp['product_id']): vp['order_rule'] for vp in valid_products
                              if 'order_rule' in vp and 'product_id' in vp}

            free_products = {}
            for f in free_products_data:
                fp_id = int(f['product_id'])
                if fp_id not in products:
                    continue

                if 'limit_qty' not in f:
                    continue

                unit_rate = products[fp_id].unit_rate
                if not unit_rate:
                    continue

                free_products[fp_id] = Decimal(f['limit_qty']) / unit_rate

            for k, p in products.items():
                if not p.confirm_quantity:
                    continue

                vp = valid_products.get(p.product_id, {})
                # print('vp', vp)
                if not vp and p.tax_price:
                    continue

                max_qty = -Decimal(vp.get('max_qty') or 0)
                if not p.tax_price:
                    if p.product_id not in free_products and not vp:
                        continue

                    free_max_qty = -Decimal(free_products.get(p.product_id, 0))
                    max_qty = max(max_qty, free_max_qty) if \
                        vp.get('max_qty') and p.product_id in free_products else min(max_qty, free_max_qty)

                max_qty = -max_qty
                check = check_quantity(p.confirm_quantity, vp.get('min_qty'), vp.get('increase_qty'), max_qty) if vp \
                    else p.confirm_quantity <= max_qty
                if not check:
                    franchisee_ids.add(demand.franchisee_id)
                    return_demands.append(demand)
                    go_on_loop = False
                    break
            print(demand.id, go_on_loop)
            if not go_on_loop:
                continue

            row = dict(
                id=demand.id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                status=action
            )
            update_data.append(row)
            demand_logs.append(dict(
                partner_id=partner_id,
                demand_id=demand.id,
                action=Action.CONFIRMED.code,
                action_name=Action.CONFIRMED.desc,
                platform=self.platform,
                start_status=demand.status,
                end_status=action,
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
                trace_id=trace_id
            ))
            final_demand_ids.append(demand.id)

        print('final_demand_ids', final_demand_ids)

        r = None
        if return_demands:
            franchisee_map = get_branch_map(branch_type="FRANCHISEE", partner_id=partner_id, user_id=user_id,
                                            branch_ids=franchisee_ids)
            r = {'msgs': [{"status": demand.status,
                              "store_name": demand.received_name,
                              "code": demand.code,
                              "order_type_id": demand.order_type_id,
                              "amount": demand.sum_price_tax,
                              "demand_date": datetime_2_timestamp(demand.demand_date),
                              "arrival_date": datetime_2_timestamp(demand.arrival_date),
                              "franchisee_name": franchisee_map.get(demand.franchisee_id, {}).get('name'),
                              } for demand in return_demands]}

        if final_demand_ids:

            res = sFD.update_f_demand(update_data=update_data, demand_logs=demand_logs, sync_confirm=True,
                                      demand_ids=final_demand_ids, global_session=True)
            if res is True:
                msg = dict(
                    demand_ids=final_demand_ids,
                    partner_id=partner_id,
                    user_id=user_id
                )
                # 批量生成缺货退款单
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.HANDLE_BATCH_OSS_FRANCHISEE_REFUND,
                                    message=msg)
                # 批量分配仓库拆单
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND_DISTRIBUTE,
                                    message=msg)
                return r or True
            else:
                return r or False

        return r

    def batch_reset_demand(self, demand_dbs, partner_id, user_id):
        """批量还原订单导入值"""
        demand_logs = []
        demand_ids = []
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        trace_id = get_guid()
        for demand_db in demand_dbs:
            demand_logs.append(dict(
                partner_id=partner_id,
                demand_id=demand_db.id,
                action=Action.RESET.code,
                action_name=Action.RESET.desc,
                platform=self.platform,
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
                trace_id=trace_id
            ))
            demand_ids.append(demand_db.id)
        res = sFD.batch_reset_demand(demand_ids=demand_ids, user_id=user_id, username=username, demand_logs=demand_logs,
                                     trace_id=trace_id, global_session=True)
        return res

    def handle_frs_demand_distribute(self, demand_id: int, partner_id: int, user_id: int):
        """处理自动分配订单仓库"""
        # demand_db = sFD.get_demand_by_id(partner_id=partner_id, demand_id=demand_id)
        demand_db = session.query(sFD).filter_by(id=demand_id, partner_id=partner_id).first()
        if not demand_db:
            return True
        if demand_db.status == "APPROVED":
            return True
        # product_ids = []
        # products = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id)
        products = demand_db.products.all() + demand_db.relation_products.all()
        # for p in products:
        #     product_ids.append(p.product_id)
        # 配送规则关联仓库
        valid_distribution_map = dict()
        valid_distributions = products_manage_service.GetDistributionRule(store_ids=[demand_db.received_by],
                                                                          product_ids={i.product_id for i in products},
                                                                          partner_id=partner_id,
                                                                          page_order=dict(limit=-1),
                                                                          user_id=user_id).get('rows', [])
        for vd in valid_distributions:
            distribute_id = convert_to_int(vd.get('distrcenter_id'))
            distribute_rule = vd.get('rule')
            arrival_days = convert_to_int(distribute_rule.get('planned_arrival_days', 0)) if distribute_rule else 0
            valid_distribution_map[convert_to_int(vd.get('product_id'))] = dict(distribute_id=distribute_id,
                                                                                arrival_days=arrival_days,
                                                                                distribution_type=vd.get('distr_type'))
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        update_products, update_relation_products = [], []
        product_logs = []
        is_completed = True
        trace_id = get_guid()
        has_distribute_by_products = []
        for product in products:
            is_main, product_log = not hasattr(product, 'demand_product_id'), {}
            if product.status != "INITED":
                continue
            updated_args = {
                'id': product.id,
                "partner_id": partner_id,
                'product_id': product.product_id,
                'updated_by': 1,
                'updated_name': "系统机器人",
            }
            if is_main :
                product_log = dict(
                    partner_id=partner_id,
                    trace_id=trace_id,
                    demand_id=demand_id,
                    action=Action.APPROVED.code,
                    action_name=Action.APPROVED.desc,
                    product_id=product.product_id,
                    product_code=product.product_code,
                    product_name=product.product_name,
                    unit_id=product.unit_id,
                    unit_name=product.unit_name,
                    created_by=1,
                    created_name="系统机器人",
                    created_at=datetime.utcnow()
                )
            distribute = valid_distribution_map.get(product.product_id)
            if distribute:
                if not distribute.get('distribute_id'):
                    is_completed = False
                    continue
                updated_args['distribute_by'] = distribute.get('distribute_id')
                updated_args['arrival_days'] = distribute.get('arrival_days')
                updated_args['distribution_type'] = distribute.get('distribution_type')
                updated_args["status"] = "PROCESS"
                has_distribute_by_products.append(product.product_id)
                if is_main:
                    product_log['distribute_by'] = updated_args['distribute_by']
                    product_log['distribution_type'] = updated_args['distribution_type']
                    product_log['arrival_days'] = str(updated_args['arrival_days'])
            else:
                is_completed = False
            if is_main:
                update_products.append(updated_args)
                product_logs.append(product_log)
            else:
                update_relation_products.append(updated_args)
        # 判断是否全部分配
        if is_completed is True:
            status = "APPROVED"
        else:
            status = "APPROVING"
        # 全部没有仓库还是待分配has_distribute_by_products
        ps = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id)
        had_distribute_by_products = 0
        for p in ps:
            if p.distribution_type and p not in has_distribute_by_products:
                had_distribute_by_products += 1
        if len(has_distribute_by_products) + had_distribute_by_products == 0:
            status = "CONFIRMED"
        demand = dict(
            id=demand_id,
            partner_id=partner_id,
            updated_by=1,
            updated_name="系统机器人",
            status=status,
            process_status='PROCESSING'
        )
        demand_log = dict(
            partner_id=partner_id,
            demand_id=demand_id,
            action=Action.APPROVED.code,
            action_name=Action.APPROVED.desc,
            platform=self.platform,
            created_by=1,
            start_status=demand_db.status,
            end_status=status,
            created_name="系统机器人",
            created_at=datetime.utcnow(),
            trace_id=trace_id
        )
        res = sFD.update_f_demand(update_data=demand, product_list=update_products, demand_logs=[demand_log],
                                  allow_status=["APPROVING", "CONFIRMED"], product_logs=product_logs, 
                                  relation_products=update_relation_products)
        # 全部/部分分配的订单都要生成要货单
        # mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND,
        #                     message=dict(demand_id=demand_id, partner_id=partner_id, username=operator_name,
        #                                  user_id=user_id))
        print('is_completed', is_completed)
        # # 全部订单都要生成要货单，部分分配的不生成
        if is_completed:
            logger.info("Publish_HANDLE_FRANCHISEE_DEMAND: {}".format(demand_id))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND,
                                message=dict(demand_id=demand_id, partner_id=partner_id, username=operator_name,
                                             user_id=user_id))
        return res

    def handle_confirm_frs_demand(self, demand_id: int, partner_id: int, user_id: int):
        demand_db = sFD.get_demand_by_id(partner_id=partner_id, demand_id=demand_id)
        if not demand_db:
            return True

    def add_relation_products(self, partner_id, user_id, request):
        if not request.demand_id:
            raise DemandNotExistError(f'Not Found')
        demand = session.query(sFD).filter_by(id=request.demand_id).with_for_update().first()
        if not demand:
            raise DemandNotExistError(f'Not Found')

        if demand.status not in ["R_APPROVE"]:
            raise DataValidationException("当前状态不允许新增商品！")
        if not request.products:
            raise DataValidationException("添加商品不允许为空")

        key = namedtuple('key', ['id', 'type', 'relation_type'])
        all_products, product_ids, ids, relation_id_dict = {}, set(), get_uuids(len(request.products)), {}
        d_ids = set()
        for i, v in enumerate(request.products):
            product_ids.add(int(v.product_id))
            d_ids.add(int(v.product_id))
            if v.relation_products:
                r_ids = get_uuids(len(v.relation_products))
                for r_i, r_v in enumerate(v.relation_products):
                    print(len(v.relation_products))
                    all_products[key(r_ids[r_i], 'bind', '')] = r_v
                    relation_id_dict[r_ids[r_i]] = ids[i]
                    product_ids.add(int(r_v.product_id))
                all_products[key(ids[i], 'main', 'BIND')] = v
                continue
            all_products[key(ids[i], 'main', '')] = v

        duplicated_products = demand.products.filter(sFDP.product_id.in_(d_ids)).with_entities(sFDP.id).first()
        if duplicated_products:
            raise DataValidationException("不能重复添加相同商品！")

        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type",
                                      partner_id=partner_id, user_id=user_id)
        category_ids = [int(p.get('category')) for p in product_map.values() if p.get('category')]
        category_map = get_category_map(category_ids, partner_id, user_id)
        unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        add_products, add_relation_products, add_demand_log, add_demand_product_log = [], [], [], []
        trace_id = get_guid()

        for k, p in all_products.items():
            is_main = k.type == 'main'

            new_product = self.build_new_product(p, product_map, {}, unit_map, category_map, partner_id=partner_id,
                                                 user_id=user_id, username=username)
            new_product['id'] = k.id
            new_product['demand_id'] = demand.id

            if not is_main:
                ratio = Decimal(p.ratio) if p.ratio else p.ratio
                if not ratio or ratio < 0:
                    raise ProductError(f"捆绑商品id: {p.product_id}比率错误")

                new_product.update({'demand_product_id': relation_id_dict[k.id], 'ratio': ratio,
                                    'configure': p.configure})

                add_relation_products.append(new_product)
            else:
                new_product.update({'relation_type': k.relation_type})
                add_products.append(new_product)

                add_demand_product_log.append({'partner_id': partner_id,
                                               'trace_id': trace_id,
                                               'demand_id': demand.id,
                                               'action': Action.ADD_PRODUCT.code,
                                               'action_name': Action.ADD_PRODUCT.desc,
                                               'product_id': new_product.get('product_id'),
                                               'product_code': new_product.get('product_code'),
                                               'product_name': new_product.get('product_name'),
                                               'unit_id': new_product.get('unit_id'),
                                               'unit_name': new_product.get('unit_name'),
                                               'tax_price': str(new_product.get('tax_price', '')),
                                               'sales_price': str(new_product.get('sales_price', '')),
                                               'confirm_quantity': str(new_product.get('confirm_quantity', '')),
                                               'confirm_amount': str(new_product.get('confirm_amount', '')),
                                               'confirm_sales_amount': str(new_product.get('confirm_sales_amount', '')),
                                               'created_by': user_id,
                                               'created_name': username,
                                               'created_at': datetime.utcnow(), })

        add_demand_log.append({'partner_id': partner_id,
                               'demand_id': demand.id,
                               'action': Action.ADD_PRODUCT.code,
                               'action_name': Action.ADD_PRODUCT.desc,
                               'platform': self.platform,
                               'created_by': user_id,
                               'created_name': username,
                               'created_at': datetime.utcnow(),
                               'trace_id': trace_id})

        session.bulk_insert_mappings(sFDP, add_products)
        session.bulk_insert_mappings(FdLogDB, add_demand_log)
        session.bulk_insert_mappings(FdpLogDB, add_demand_product_log)
        session.bulk_insert_mappings(SFDPR, add_relation_products) if add_relation_products else ...
        session.commit()

        return {"demand_id": demand.id}

    def allocate_distribute_by(self, demand_id, partner_id, user_id, operator_name=None, raise_exception=True):
        """
        APPROVED DEMAND
        """
        result = {'demand_id': demand_id}
        logging.info(f'{demand_id} 拿锁之前')
        demand: sFD = session.query(sFD).filter_by(id=demand_id).with_for_update().first()
        logging.info(f'{demand_id} 拿到锁')
        if demand.status not in ('CONFIRMED', 'APPROVING'):
            if raise_exception:
                raise StatusUnavailable('订单状态错误')
            else:
                return False
        fields = ['id', 'product_id', 'status', 'product_code', 'product_name', 'unit_id', 'unit_name']
        demand_products = demand.products.with_entities(*[getattr(sFDP, f) for f in fields],
                                                        sFDP.relation_type).all() + \
                         demand.relation_products.with_entities(*[getattr(SFDPR, f) for f in fields]).all()

        if not demand_products:
            if raise_exception:
                raise DataValidationException('订货单空商品')
            else:
                return False

        product_ids = {i.product_id for i in demand_products}
        valid_distributions = products_manage_service.GetDistributionRule(store_ids=[demand.received_by],
                                                                          product_ids=product_ids,
                                                                          partner_id=partner_id,
                                                                          page_order=dict(limit=-1),
                                                                          user_id=user_id).get('rows', [])

        valid_distributions = {int(i.get('product_id', 0)):
                                   {'distribute_id': int(i.get('distrcenter_id') or 0),
                                    'arrival_days': i.get('rule', {}).get('planned_arrival_days'),
                                    'distribution_type': i.get('distr_type')}
                               for i in valid_distributions}

        products_log, update_products, update_relation_products = [], [], []
        not_allocate_products = 0
        trace_id = get_guid()
        operator_name = metadata_service.get_username_by_pid_uid(partner_id,
                                                                 user_id) if not operator_name else operator_name

        for p in demand_products:
            product_status = 'PROCESS'
            # if p.status != 'INITED':
            #     continue

            distribute = valid_distributions.get(p.product_id, {})
            if not distribute.get('distribute_id'):
                product_status = 'INITED'
                not_allocate_products += 1

            if not hasattr(p, 'relation_type'):
                update_relation_products.append({
                    'id': p.id,
                    'distribute_by': distribute.get('distribute_id'),
                    'arrival_days': distribute.get('arrival_days'),
                    'distribution_type': distribute.get('distribution_type'),
                    'status': product_status
            })
                continue

            update_products.append({
                'id': p.id,
                'distribute_by': distribute.get('distribute_id'),
                'arrival_days': distribute.get('arrival_days'),
                'distribution_type': distribute.get('distribution_type'),
                'status': product_status
            })
            products_log.append({
                'partner_id': partner_id,
                'trace_id': trace_id,
                'demand_id': demand_id,
                'action': Action.APPROVED.code,
                'action_name': Action.APPROVED.desc,
                'product_id': p.product_id,
                'product_code': p.product_code,
                'product_name': p.product_name,
                'unit_id': p.unit_id,
                'unit_name': p.unit_name,
                'created_by': user_id,
                'created_name': operator_name,
                'created_at': datetime.utcnow(),
                'distribute_by': distribute.get('distribute_id'),
                'distribution_type': distribute.get('arrival_days'),
                'arrival_days': str(distribute.get('distribution_type'))
            })

        if not update_products and not update_relation_products:
            return {**result, 'not_allocate_products': not_allocate_products}

        demand_log = FdLogDB(partner_id=partner_id,
                             demand_id=demand_id,
                             action=Action.APPROVED.code,
                             action_name=Action.APPROVED.desc,
                             platform=self.platform,
                             start_status=demand.status,
                             created_by=user_id,
                             created_name=operator_name,
                             created_at=datetime.utcnow(),
                             trace_id=trace_id)

        demand.updated_name = operator_name
        demand.updated_by = user_id
        demand.process_status = 'PROCESSING'
        demand.status = (('APPROVING' if not_allocate_products != len(demand_products) else demand.status)
                         if not_allocate_products else 'APPROVED')
        demand_log.end_status = demand.status

        session.bulk_update_mappings(sFDP, update_products) if update_products else ...
        session.bulk_update_mappings(SFDPR, update_relation_products) if update_relation_products else ...
        session.add(demand_log)
        session.commit()
        logging.info('数据库提交')

        if not not_allocate_products:
            # 全部订单都要生成要货单,部分分配的不生成
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND,
                                message=dict(demand_id=demand_id, partner_id=partner_id, username=operator_name,
                                             user_id=user_id))

            return result

        return {**result, 'not_allocate_products': not_allocate_products}


franchisee_demand_service = FranchiseeDemandService()
