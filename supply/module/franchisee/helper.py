# -*- coding:utf-8 -*-
from decimal import Decimal
from datetime import datetime,date
from google.protobuf.timestamp_pb2 import Timestamp
from google.protobuf.struct_pb2 import Struct
from google.protobuf.empty_pb2 import Empty
from supply.client.products_manage_service import products_manage_service
from supply.driver.mysql import DummyTransaction
from sqlalchemy import text
from supply import logger
import logging


def get_adjust_tax_price_tax_rate(store_id=None, category_ids=None, product_ids=None, partner_id=None, user_id=None, include_sales_price=False,
                                  overwrite=True, product_unit_adjust_tax_price_map=None,product_unit_adjust_tax_ratio_map=None,product_unit_adjust_sales_price_map=None):
    """获取门店商品的订货价格对应的核算价格adjust_tax_price和税率tax_ratio
    """
    if product_unit_adjust_tax_price_map is None:
        product_unit_adjust_tax_price_map = {}
    if product_unit_adjust_tax_ratio_map is None:
        product_unit_adjust_tax_ratio_map = {}
    if product_unit_adjust_sales_price_map is None:
        product_unit_adjust_sales_price_map = {}
    logger.info("ListAgentProducts store_id={},partner_id={},product_ids={},category_ids={}".format(store_id,partner_id,product_ids,category_ids))
    product_unit_price_list = products_manage_service.ListAgentProducts(store_id=int(store_id), user_id=user_id, partner_id=partner_id, product_ids=product_ids, category_ids=category_ids)
    logger.info("ListAgentProducts return product_unit_price_list={}".format(product_unit_price_list))
    if product_unit_price_list:
        for p in product_unit_price_list:
            product_unit_adjust_key = str(p.get('id'))
            if "product_price" in p and p["product_price"]:
                for product_price in p["product_price"]:
                    logging.info("product_price:{}".format(product_price))
                    if int(product_price.get("price_type_id",-1)) == 1: # 1 订货价格 2 零售价格, 这里取订货价格对应的核算价格和税率
                        if overwrite:
                            product_unit_adjust_tax_price_map[product_unit_adjust_key] = (Decimal(product_price.get("tax_price")) if product_price.get("tax_price") else Decimal(0))/(Decimal(product_price.get("unit_rate")) if product_price.get("unit_rate") and Decimal(product_price.get("unit_rate"))>0 else 1)
                            product_unit_adjust_tax_ratio_map[product_unit_adjust_key] = (Decimal(product_price.get("tax_ratio")) if product_price.get("tax_ratio") else Decimal(0))
                        else:
                            if product_unit_adjust_key not in product_unit_adjust_tax_price_map:
                                product_unit_adjust_tax_price_map[product_unit_adjust_key] = (Decimal(product_price.get("tax_price")) if product_price.get("tax_price") else Decimal(0))/(Decimal(product_price.get("unit_rate")) if product_price.get("unit_rate") and Decimal(product_price.get("unit_rate"))>0 else 1)
                            if product_unit_adjust_key not in product_unit_adjust_tax_ratio_map:
                                product_unit_adjust_tax_ratio_map[product_unit_adjust_key] = (Decimal(product_price.get("tax_ratio")) if product_price.get("tax_ratio") else Decimal(0))
                    logging.info("product_unit_adjust_tax_price_map[product_unit_adjust_key]:{}".format(product_unit_adjust_tax_price_map.get(product_unit_adjust_key)))
                    if include_sales_price and int(product_price.get("price_type_id",-1)) == 2: #2 零售价格
                        if overwrite:
                            product_unit_adjust_sales_price_map[product_unit_adjust_key] = (Decimal(product_price.get("tax_price")) if product_price.get("tax_price") else Decimal(0))/(Decimal(product_price.get("unit_rate")) if product_price.get("unit_rate") and Decimal(product_price.get("unit_rate"))>0 else 1)
                        else:
                            if product_unit_adjust_key not in product_unit_adjust_sales_price_map:
                                product_unit_adjust_sales_price_map[product_unit_adjust_key] = (Decimal(product_price.get("tax_price")) if product_price.get("tax_price") else Decimal(0))/(Decimal(product_price.get("unit_rate")) if product_price.get("unit_rate") and Decimal(product_price.get("unit_rate"))>0 else 1)

    del product_unit_price_list
    if include_sales_price:
        return product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map
    else:
        return product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map


def fill_adjust_tax_price_tax_rate(item, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_id, unit_rate, quantity=None, quantityAmountName=None, quantitySalesAmountName=None, product_unit_adjust_sales_price_map=None, quantityTaxAmountName=None):
    if not unit_rate:
        unit_rate = 1
    unit_rate = Decimal(unit_rate)
    quantize_exp = Decimal('0.00000000')
    if (type(item) is dict):
        # logging.info("product_unit_adjust_tax_price_map{}".format(product_unit_adjust_tax_price_map))
        # logging.info("product_unit_adjust_tax_ratio_map{}".format(product_unit_adjust_tax_ratio_map))
        if product_unit_adjust_tax_price_map is not None and product_unit_adjust_tax_ratio_map is not None:
            item["tax_price"] = (unit_rate * Decimal(product_unit_adjust_tax_price_map.get(str(product_id),0))).quantize(quantize_exp)
            item["tax_rate"] = Decimal(product_unit_adjust_tax_ratio_map.get(str(product_id),0)).quantize(quantize_exp)
            item["cost_price"] = (unit_rate * Decimal(product_unit_adjust_tax_price_map.get(str(product_id),0))/Decimal(1+item["tax_rate"])).quantize(quantize_exp)
            if quantity is not None and quantityAmountName is not None:
                item[quantityAmountName] = ((Decimal(quantity) if quantity else 0) * Decimal(item["tax_price"])).quantize(quantize_exp)
            if quantity is not None and quantityTaxAmountName is not None:
                item[quantityTaxAmountName] = ((Decimal(quantity) if quantity else 0) * (Decimal(item["tax_price"])-Decimal(item["cost_price"]))).quantize(quantize_exp)
        # logging.info("product_unit_adjust_sales_price_map{}".format(product_unit_adjust_tax_ratio_map))
        if product_unit_adjust_tax_ratio_map is not None:
            item["sales_price"] = (unit_rate * Decimal(product_unit_adjust_sales_price_map.get(str(product_id),0))).quantize(quantize_exp)
            if quantity is not None and quantitySalesAmountName is not None:
                item[quantitySalesAmountName] = ((Decimal(quantity) if quantity else 0) * Decimal(item["sales_price"])).quantize(quantize_exp)
    else:
        if product_unit_adjust_tax_price_map is not None and product_unit_adjust_tax_ratio_map is not None:
            item.tax_price =  (unit_rate * Decimal(product_unit_adjust_tax_price_map.get(str(product_id),0))).quantize(quantize_exp)
            item.tax_rate = Decimal(product_unit_adjust_tax_ratio_map.get(str(product_id),0)).quantize(quantize_exp)
            item.cost_price =  (unit_rate * Decimal(product_unit_adjust_tax_price_map.get(str(product_id),0))/Decimal(1+item.tax_rate)).quantize(quantize_exp)
            if quantity is not None and quantityAmountName is not None:
                setattr(item, quantityAmountName, ((Decimal(quantity) if quantity else 0) * Decimal(item.tax_price)).quantize(quantize_exp))
            if quantity is not None and quantityTaxAmountName is not None:
                setattr(item, quantityTaxAmountName,((Decimal(quantity) if quantity else 0) * (Decimal(item.tax_price)-Decimal(item.cost_price))).quantize(quantize_exp))
        if product_unit_adjust_sales_price_map is not None:
            item.sales_price =  (unit_rate * Decimal(product_unit_adjust_sales_price_map.get(str(product_id),0))).quantize(quantize_exp)
            if quantity is not None and quantitySalesAmountName is not None:
                setattr(item, quantitySalesAmountName, ((Decimal(quantity) if quantity else 0) * Decimal(item.sales_price)).quantize(quantize_exp))


def update_adjust_tax_price_tax_rate(product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_id, tax_price, tax_rate, product_unit_adjust_sales_price_map=None, sales_price=None, overwrite=True):
    product_unit_adjust_key = str(product_id)
    if product_unit_adjust_tax_price_map is not None and product_unit_adjust_tax_ratio_map is not None and tax_price is not None and tax_rate is not None:
        if overwrite:
            product_unit_adjust_tax_price_map[product_unit_adjust_key] = Decimal(tax_price)
            product_unit_adjust_tax_ratio_map[product_unit_adjust_key] = Decimal(tax_rate)
        else:
            if product_unit_adjust_key not in product_unit_adjust_tax_price_map:
                product_unit_adjust_tax_price_map[product_unit_adjust_key] = Decimal(tax_price)
            if product_unit_adjust_key not in product_unit_adjust_tax_ratio_map:
                product_unit_adjust_tax_ratio_map[product_unit_adjust_key] = Decimal(tax_rate)
    if product_unit_adjust_sales_price_map is not None and sales_price is not None:
        if overwrite:
            product_unit_adjust_sales_price_map[product_unit_adjust_key] = Decimal(sales_price)
        else:
            if product_unit_adjust_key not in product_unit_adjust_sales_price_map:
                product_unit_adjust_sales_price_map[product_unit_adjust_key] = Decimal(sales_price)

def dict_for_message(type_str, props_dict, myeval):
    allowed_props = {}
    if type_str == "Timestamp":
        message_type = Timestamp
    elif type_str == "Struct":
        message_type = Struct
    elif type_str == "Empty":
        message_type = Empty
    else:
        message_type = myeval(type_str)
    if message_type is not None and props_dict:
        allowed_prop_names = [name_item for name_item in dir(message_type) if not name_item.startswith("_") \
            and str(getattr(message_type,name_item)).startswith("<field property ")]
        if isinstance(props_dict, dict):
            for prop_name in props_dict:
                if prop_name in allowed_prop_names:
                    allowed_props[prop_name] = props_dict[prop_name]
                    message_prop_info = getattr(message_type, prop_name)
                    prop_type_str = None
                    if props_dict[prop_name] and hasattr(message_prop_info, "DESCRIPTOR"):
                        message_prop_info = getattr(message_prop_info, "DESCRIPTOR")
                        if message_prop_info and hasattr(message_prop_info, "message_type"):
                            message_prop_info = getattr(message_prop_info, "message_type")
                            if message_prop_info and hasattr(message_prop_info, "full_name"):
                                message_prop_info = getattr(message_prop_info, "full_name")
                                if message_prop_info:
                                    prop_type_str = message_prop_info.split(".")[-1]
                    if prop_type_str:
                        if type(props_dict[prop_name]) is list:
                            allowed_props[prop_name] = [dict_for_message(prop_type_str, list_item, myeval) for list_item in props_dict[prop_name]]
                        else:
                            allowed_props[prop_name] = dict_for_message(prop_type_str, props_dict[prop_name], myeval)
        else:
            if type(props_dict) == message_type:
                return props_dict
            else:
                convert_ok = False
                if  message_type.__name__=="Timestamp":
                    timestamp = Timestamp()
                    if not convert_ok and isinstance(props_dict, datetime):
                        timestamp.FromDatetime(props_dict)
                        props_dict = timestamp
                        convert_ok = True
                    if not convert_ok and isinstance(props_dict, date):
                        props_dict = datetime(year=props_dict.year, month=props_dict.month, day=props_dict.day, hour=0)
                        timestamp.FromDatetime(props_dict)
                        props_dict = timestamp
                        convert_ok = True
                    if not convert_ok and isinstance(props_dict, str): # 2022-12-19T16:00:00Z
                        try:
                            temp_str_props_dict = props_dict[0:props_dict.rfind(":")+3].replace("T", " ")
                            props_dict = datetime.strptime(temp_str_props_dict,"%Y-%m-%d %H:%M:%S")
                            timestamp.FromDatetime(props_dict)
                            props_dict = timestamp
                            convert_ok = True
                        except:
                            pass
                    if not convert_ok and isinstance(props_dict, int): # seconds
                        try:
                            timestamp.FromSeconds(props_dict)
                            props_dict = timestamp
                            convert_ok = True
                        except:
                            pass
                    if not convert_ok and isinstance(props_dict, str): # json string
                        try:
                            timestamp.FromJsonString(props_dict)
                            props_dict = timestamp
                            convert_ok = True
                        except:
                            pass
                else:
                    if not convert_ok:
                        try:
                            props_dict = message_type(props_dict)
                            convert_ok = True
                        except:
                            pass
                    if not convert_ok:
                        try:
                            props_dict = message_type(str(props_dict))
                            convert_ok = True
                        except:
                            pass
                return props_dict
    return allowed_props


def executeSql(sql_text):
    if sql_text:
        with DummyTransaction(auto_commit=False) as trans:
            trans.scope_session.execute(text(sql_text))
            trans.scope_session.commit()
    return True


def get_category_children(categoryid, category_children_map, category_children_ret_map):
    ret = []
    if categoryid and category_children_map:
        if categoryid in category_children_ret_map:
            ret = category_children_ret_map[categoryid]
        elif categoryid in category_children_map:
            children = category_children_map[categoryid]
            ret += children
            for category_child_id in children:
                ret += get_category_children(category_child_id, category_children_map, category_children_ret_map)
            ret = list(set(ret))
            category_children_ret_map[categoryid] = ret
    return ret
