import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal

from supply.api import handle_request_attachments
from supply.client.inventory_service import inventory_service
from supply.model.attachments import AttachmentsModel
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand
from supply.model.returns import ReturnModel
from supply.model.supply_doc_code import Supply_doc_code
from supply.module.common import Refund
from supply.driver.mq import mq_producer
from supply.driver.mysql import session_maker
from supply.module.franchisee.franchisee_demand import franchisee_demand_service
from supply.utils import pb2dict
from supply.utils.helper import get_guid, MessageTopic
from supply.client.receipt_service import receipt_service
from supply.module.mobile.mobile_receive_diff import MobileReceivingDiffService
from supply.task.message_service_pub import MessageServicePub

from supply.client.metadata_service import metadata_service
from supply.error.exception import NoResultFoundError, StatusUnavailable, DataValidationException
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefund
from supply.model.receiving_diff import ReceivingDiffModel, ReceivingDiffProductModel, ReceivingDiffLogModel
from supply.utils.snowflake import gen_snowflake_id
from supply.module.franchisee.franchisee_returns import franchisee_returns_service


class FranchiseeReceivingDiffService(MobileReceivingDiffService):

    def get_receiving_diff_by_id(self, receiving_diff_id, partner_id, user_id):
        receiving_diff = super().get_receiving_diff_by_id(receiving_diff_id, partner_id, user_id)
        # TODO: 这里需要增加差异单的价
        return receiving_diff

    def confirm_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(
            partner_id, user_id)
        count, receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(
            receiving_diff_id=receiving_diff_id, partner_id=partner_id)
        if not count:
            # 无差异商品
            raise NoResultFoundError("no diff products found!")

        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(
            partner_id=partner_id, diff_id=receiving_diff_id)

        # 提交状态
        if receiving_diff_db.status != 'SUBMITTED':
            # 状态不允许提交
            raise StatusUnavailable("只有提交状态的单据可以被审核")

        # 2021-08-10 收货差异单不再生成退货单
        # 根据门店承担部分增加门店库存，仓库承担部分释放锁定的在途库存
        diff_product_list = []
        product_ids = []
        s_diff_product_list = []
        d_diff_product_list = []
        detail_list = []
        sequence = 0
        diff_args = {}
        for receiving_diff_product in receiving_diff_product_db:
            if receiving_diff_product.s_diff_quantity:
                if receiving_diff_product.unit_rate:
                    amount = receiving_diff_product.s_diff_quantity * receiving_diff_product.unit_rate
                else:
                    amount = receiving_diff_product.s_diff_quantity
                parg = {
                    "product_id": receiving_diff_product.product_id,
                    "amount": amount,
                    "from": receiving_diff_db.delivery_by,
                    "to": receiving_diff_db.received_by
                }
                s_diff_product_list.append(parg)

                recall_diff_arg = {
                    "product_id": receiving_diff_product.product_id,
                    "s_diff_quantity": receiving_diff_product.s_diff_quantity
                }
                diff_product_list.append(recall_diff_arg)

            if receiving_diff_product.d_diff_quantity:
                if receiving_diff_product.unit_rate:
                    amount = receiving_diff_product.d_diff_quantity * receiving_diff_product.unit_rate
                else:
                    amount = receiving_diff_product.d_diff_quantity
                d_parg = {
                    "product_id": receiving_diff_product.product_id,
                    "amount": amount,
                    "from": receiving_diff_db.delivery_by,
                    "to": receiving_diff_db.delivery_by
                }
                d_diff_product_list.append(d_parg)

                product_ids.append(receiving_diff_product.product_id)

            if receiving_diff_db.diff_type == 'HC':
                detail = {}
                detail['sequence_id'] = sequence
                accounting = {}
                account = {}
                account['branch_id'] = receiving_diff_db.delivery_by
                account['product_id'] = receiving_diff_product.product_id
                accounting['account'] = account
                accounting['amount'] = receiving_diff_product.d_diff_quantity * Decimal(
                    receiving_diff_product.unit_rate) if receiving_diff_product.unit_rate else receiving_diff_product.d_diff_quantity
                detail['accounting'] = accounting
                sequence += 1

                detail_list.append(detail)
        if receiving_diff_db.diff_type != 'HC':
            self.deal_with_inventory(batch_no=receiving_diff_db.id, s_diff_product_list=s_diff_product_list, \
                            d_diff_product_list=d_diff_product_list, code="RECEIVING_DIFF", action="MIXED", \
                            partner_id=partner_id, user_id=user_id, trace_id=receiving_diff_db.code)
        else:
            code = 'RECEIVING_DIFF'
            description='mcu receiving diff'
            result = inventory_service.deal_with_inventory(batch_no=str(receiving_diff_db.id), code=code, action='DEPOSIT',
                                                           description=description, detail=detail_list,
                                                           partner_id=partner_id,
                                                           user_id=user_id, trace_id=receiving_diff_db.code)
            diff_args = {'inventory_req_id': result['id'], 'inventory_status': result['status']}

        # 按照仓库承担数量生成差异退货单
        if receiving_diff_db.diff_type == 'HC':
            d_diff_pargs = []
            d_flag = False
            for receiving_diff_product in receiving_diff_product_db:

                if receiving_diff_product.d_diff_quantity:
                    d_flag = True
                    parg = {
                                'product_id':receiving_diff_product.product_id,
                                'product_name':receiving_diff_product.product_name,
                                'product_code':receiving_diff_product.product_code,
                                'unit_id':receiving_diff_product.unit_id,
                                'unit_spec':receiving_diff_product.unit_spec,
                                'unit_name':receiving_diff_product.unit_name,
                                'quantity':receiving_diff_product.d_diff_quantity,
                                'reason_type':'收货差异生成',
                                'tax_rate': receiving_diff_product.tax_rate,
                                'price': receiving_diff_product.cost_price,
                                'price_tax': receiving_diff_product.tax_price
                            }
                    d_diff_pargs.append(parg)

            if d_flag:
                order_json = {
                        'demand_order_code': receiving_diff_db.master_code,
                        'rec_code': receiving_diff_db.receiving_code,
                        'diff_code': receiving_diff_db.code
                        }

                _, diff_args = franchisee_returns_service.create_diff_return(return_by=receiving_diff_db.received_by,
                                        return_to=receiving_diff_db.delivery_by,
                                        return_delivery_date=datetime.now(), return_reason='收货差异生成',
                                        product_detail=d_diff_pargs, partner_id=partner_id, user_id=user_id,
                                        source_id=receiving_diff_db.id, source_code=receiving_diff_db.code,
                                        logistics_type=receiving_diff_db.logistics_type, remark=order_json,
                                        franchisee_id=receiving_diff_db.franchisee_id,
                                        diff_type=receiving_diff_db.diff_type, receiving_diff_id=receiving_diff_db.id)

        if len(d_diff_product_list) > 0:
            meta_products = metadata_service.get_product_list(ids=product_ids, return_fields='name,code,bom_type,category',
                                                              partner_id=partner_id, user_id=user_id).get("rows", [])
            meta_products_map = {}
            for meta_product in meta_products:
                meta_products_map[meta_product.get("id")] = meta_product
            demand_db = SupplyFranchiseeDemand.get_demand_by_id(partner_id=partner_id, demand_id=receiving_diff_db.master_id)
            refund_products = generate_refund_products(receiving_diff_product_db, meta_products_map)
            create_refund(detail=receiving_diff_db, products=refund_products, partner_id=partner_id,
                          user_id=user_id, username=operator_name, main_type='FRS_REC_DIFF', refund_type='DIFF_REFUND',
                          franchisee_id=demand_db.franchisee_id)

        # 反写更新收货单数据库
        receipt_service.deal_receive_by_id(receive_id=receiving_diff_db.request_id, action='DIFF_CONFIRM',
                                           partner_id=partner_id, user_id=user_id, deal_products=diff_product_list)

        args = {
            'id': receiving_diff_id,
            'updated_at': datetime.now(),
            'receive_date': datetime.now(),
            'status': 'CONFIRMED',
            'has_checked': True,
            'updated_by': user_id,
            'review_by': user_id,
            'updated_name': operator_name
        }
        # 记录操作日志
        log_detail = {
            'doc_id': receiving_diff_id,
            'operation': 'CONFIRM',
            'success': True,
            'partner_id': partner_id,
            'created_by': user_id,
            'created_at': datetime.now(),
            'created_name': operator_name
        }
        args.update(**diff_args)

        # 单据同步给三方
        message = {
                            'doc_resource': 'fs_demand_rec_diff',
                            'doc_id': receiving_diff_id,
                            'partner_id': partner_id,
                            'user_id': user_id,
                            'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
        mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)

        # 同步记录落表，用于后续补偿
        tp_trans_log = {
                        'id': get_guid(),
                        'doc_code': receiving_diff_db.code,
                        'doc_type': 'fs_demand_rec_diff',
                        'status': 'inited',
                        'msg': json.dumps(message),
                        'partner_id': partner_id,
                        'created_by': user_id,
                        'created_at': datetime.utcnow(),
                        'updated_at': datetime.utcnow()
                    }

        # 一把更新数据库
        ReceivingDiffProductModel.update_receive_diff_in_all([args], [], [log_detail], [tp_trans_log])

        # 状态变更清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=receiving_diff_db.received_by,
                                         doc_type="receiving_diff"))

        # PDA消息推送
        MessageServicePub.pub_message_service(partner_id=partner_id,
                                              user_id=user_id,
                                              scope_id=1,
                                              store_id=receiving_diff_db.received_by,
                                              source_root_id=receiving_diff_db.receiving_id,
                                              source_id=receiving_diff_db.id,
                                              source_type="REC_DIFF",
                                              action="CONFIRMED",
                                              ref_source_id=receiving_diff_db.id,
                                              ref_source_type="REC_COLD",
                                              ref_action="SUBMITTED",
                                              content={
                                                  "store_name": metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                  "receiving_code": str(receiving_diff_db.receiving_code),
                                                  "delivery_date": str(receiving_diff_db.delivery_date),
                                                  "updated_at": str(datetime.now()),
                                                  "updated_by": str(user_id),
                                                  "updated_name": operator_name, }
                                              )

        return True

    def create_receive_diff_by_hand(self, request, partner_id, user_id, diff_type=''):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        diff_dbs = ReceivingDiffModel.list_receiving_diffs_by_receiving_id(int(request.receiving_id), partner_id)
        receiving_db = receipt_service.get_receive_by_id(receive_id=request.receiving_id,
                                                         partner_id=partner_id,
                                                         user_id=user_id)
        return_db = ReturnModel.get_return_by_source_id(request.receiving_id, partner_id)
        if return_db:
            raise DataValidationException("收货单:{},已存在退货原单,不允许新建".format(receiving_db.code))
        if len(diff_dbs) >= 1:
            raise DataValidationException("收货单:{},已存在收货差异单".format(receiving_db.code))
        confirmed_products = request.confirmed_products
        main_branch_type_dict = {
            'W': 'WAREHOUSE',
            'S': 'STORE',
            'M': 'MACHINING',
            'FS': 'FRS_STORE'
        }

        # 新建收货差异单单据明细
        diff_id = gen_snowflake_id()
        args = {
            'id': diff_id,
            'code': Supply_doc_code.get_code_by_type('REC_DIFF', partner_id, None),
            'receiving_id': request.receiving_id,
            'received_by': receiving_db.receive_by,
            'delivery_by': receiving_db.delivery_by,
            'receiving_code': receiving_db.code,
            'master_id': receiving_db.batch_id,
            'master_code': receiving_db.batch_code,
            'status': 'INITED',
            'demand_date': receiving_db.demand_date.ToDatetime() if receiving_db.demand_date else None,
            'delivery_date': receiving_db.delivery_date.ToDatetime() if receiving_db.delivery_date else None,
            'receive_date': receiving_db.arrival_date.ToDatetime() if receiving_db.arrival_date else None,
            'review_by': user_id,  # ?
            'updated_by': user_id,
            'created_by': user_id,
            'updated_name': operator_name,
            'created_name': operator_name,
            'updated_at': datetime.now(),
            'created_at': datetime.now(),
            'partner_id': partner_id,
            'received_type': receiving_db.batch_type,
            'request_id': request.receiving_id,
            'logistics_type': receiving_db.distr_type,
            'branch_type': main_branch_type_dict.get(receiving_db.main_branch_type),
            'sub_receive_by': receiving_db.sub_receive_by,
            'auto_confirm_date': datetime.now() + timedelta(hours=2),
            'diff_type': diff_type,
            'franchisee_id': receiving_db.franchisee_id,
            'remark': request.remark,
            'attachments': handle_request_attachments(request.attachments)
        }
        product_ids = []
        for product in confirmed_products:
            product_ids.append(product.product_id)

        product_unit_dict = metadata_service.get_product_unit_dict(partner_id=partner_id, user_id=user_id,
                                                                   product_ids=product_ids)

        # 新建收货差异单商品明细
        has_diffs = False
        diff_products = []
        for product in confirmed_products:
            product_detail = pb2dict(product)
            # diff_quantity = round((Decimal(
            #     product_detail['delivery_quantity']) if product_detail.get('delivery_quantity') else Decimal(
            #     0.0)) - (Decimal(
            #     product_detail['receive_quantity']) if product_detail.get('receive_quantity') else Decimal(
            #     0.0)), 2)
            # 收货数量>=发货数量，不产生收货差异单，用收货数量记库存
            diff_quantity = product_detail.get("diff_quantity", 0)
            if diff_quantity <= 0:
                pass

            else:
                has_diffs = True
                if product_detail.get('unit_rate'):
                    unit_rate = product_detail.get('unit_rate')
                else:
                    unit_rate = product_unit_dict.get(product.product_id, {}).get("order", {}).get("rate", 1)
                p_args = {
                    'id': gen_snowflake_id(),
                    'receiving_id': receiving_db.id,
                    'received_by': receiving_db.receive_by,
                    'diff_id': diff_id,
                    'product_id': product_detail.get('product_id'),
                    'material_number': product_detail.get('material_number'),
                    'accounting_unit_id': product_unit_dict.get(str(product.product_id), {}).get("default", {}).get(
                        "id", ""),
                    'unit_id': product_detail.get('unit_id'),
                    'product_code': product_detail.get('product_code'),
                    'product_name': product_detail.get('product_name'),
                    'accounting_unit_name': product_unit_dict.get(str(product.product_id), {}).get("default", {}).get(
                        "name", ""),
                    'accounting_unit_spec': product_unit_dict.get(str(product.product_id), {}).get("default", {}).get(
                        "code", ""),
                    'unit_name': product_detail.get('unit_name'),
                    'unit_spec': product_detail.get('unit_spec'),
                    'unit_rate': product_detail.get('unit_rate'),
                    'received_accounting_quantity': float(product_detail.get('received_quantity', 0)) * unit_rate,
                    'received_quantity': float(product_detail.get('received_quantity', 0)),
                    'confirmed_accounting_quantity': float(product_detail.get('confirmed_quantity', 0)) * unit_rate,
                    'confirmed_quantity': float(product_detail.get('confirmed_quantity', 0)),
                    'diff_accounting_quantity': float(diff_quantity) * unit_rate,
                    'diff_quantity': diff_quantity,
                    'd_diff_quantity': diff_quantity,
                    'd_diff_accounting_quantity': diff_quantity * unit_rate,
                    'updated_by': user_id,
                    'created_by': user_id,
                    'updated_name': operator_name,
                    'created_name': operator_name,
                    'updated_at': datetime.now(),
                    'created_at': datetime.now(),
                    'partner_id': partner_id,
                    'sub_receive_by': receiving_db.sub_receive_by,
                    'cost_price': round(float(product_detail.get('cost_price', 0)), 2),
                    'tax_price': round(float(product_detail.get('tax_price', 0)), 2),
                    'tax_rate': round(float(product_detail.get('tax_rate', 0)), 2),
                    'reason_type': product_detail.get("reason_type"),
                    'remark': product_detail.get("remark"),
                }
                diff_products.append(p_args)

        # 整单均无差异：
        if not has_diffs:
            return 0
        # 差异单数据插入数据库
        args['product_nums'] = len(diff_products)
        logging.info('create_receive_diff_in_all {}'.format(args))
        ReceivingDiffProductModel.create_receive_diff_in_all(diff_detail=[args], diff_product_list=diff_products)
        # 附件入库
        attachments_list = []
        if request.attachments:
            for attachment in request.attachments:
                args = {
                    'doc_id': diff_id,
                    'doc_type': 'receive_diff',
                    'attachment': handle_request_attachments(attachment),
                    'signature': '',
                    'nosign_reason': '',
                    'partner_id': partner_id,
                    'created_by': user_id,
                    'updated_by': user_id,
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                }
                attachments_list.append(args)
        AttachmentsModel.create_attachment_in_all(attachments=attachments_list)
        # 记录操作日志
        log_detail = {
            'doc_id': diff_id,
            'operation': 'CREATE',
            'success': True,
            'partner_id': partner_id,
            'created_by': user_id,
            'created_at': datetime.now(),
            'created_name': operator_name
        }
        ReceivingDiffLogModel.create_logs_in_all([log_detail])
        return diff_id


def generate_refund_products(products, meta_products):
    refund_products = []
    for product in products:
        if product.d_diff_quantity <= 0:
            continue
        refund_product = {}
        refund_product["product_id"] = product.product_id
        refund_product["product_code"] = product.product_code
        refund_product["product_name"] = meta_products.get(str(product.product_id), {}).get("name")
        refund_product["category_id"] = meta_products.get(str(product.product_id), {}).get("category")
        refund_product["unit_id"] = product.unit_id
        refund_product["unit_name"] = product.unit_name
        refund_product["unit_rate"] = product.unit_rate
        refund_product["tax_price"] = product.tax_price
        refund_product["cost_price"] = product.cost_price
        refund_product["tax_rate"] = product.tax_rate
        refund_product["quantity"] = product.d_diff_quantity
        refund_products.append(refund_product)
    return refund_products


def create_refund(detail, products, partner_id, user_id, username, main_type='', refund_type='', franchisee_id=0):
    refund_id = get_guid()
    refund = Refund(refund_id, partner_id, user_id, username)
    refund_products, refund_amount = refund.get_refund_products(products=products)
    refund_detail = refund.get_refund_detail(
        detail=detail, refund_amount=refund_amount, main_type=main_type, refund_type=refund_type,
        franchisee_id=franchisee_id)
    refund_log = refund.get_refund_log()
    SupplyFranchiseeRefund.create_franchisee_refund(
        refund_details=[refund_detail], refund_products=refund_products, refund_logs=[refund_log]
    ) if refund_amount else ...


franchisee_receiving_diff_service = FranchiseeReceivingDiffService()
