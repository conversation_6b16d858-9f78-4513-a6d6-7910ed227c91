import json
from supply.utils.helper import convert_to_int, convert_to_datetime, MessageTopic, get_guid, \
    set_model_from_props, convert_to_decimal, get_product_code_unit_map, get_cost_center_map, get_product_unit_map, \
    get_supply_reason_map, get_product_map, get_username_map
from supply.model.adjust import AdjustRepository
from supply.model.inventory import inventory_repository
from supply.client.metadata_service import metadata_service
from supply.module.franchisee.helper import *
from supply.error.exception import *
from supply.client.inventory_service import inventory_service
from supply.client.bom_service import Bom_service
from supply.utils.inventory_enum import ACTION
from supply.driver.mq import mq_producer
from supply.model.operation_log import TpTransLogModel
from supply.utils.kit import Kit
import asyncio

class FranchiseeAdjustService(object):
    """
    报废单业务模块
    """
    def __init__(self):
        self.adjust_repo = AdjustRepository()
        self.allow_minus_inventory = False

    # 取得调整单详细
    def list_store_adjust_by_id(self, adjust_id, partner_id, user_id, is_detail=False):
        adjust_obj = self.adjust_repo.store_adjust_by_id(adjust_id, is_detail, partner_id=partner_id)
        reason_map = get_supply_reason_map(_type="ADJUST", partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            if adjust_obj.attachments:
                attachments = json.loads(adjust_obj.attachments)
                adjust_obj.attachments = attachments.get('attachments', [])
            adjust_obj.reason_name = reason_map.get(adjust_obj.reason_type)
        return adjust_obj

    def list_store_adjust_by_ids(self, adjust_ids, partner_id, user_id):
        adjust_objs = self.adjust_repo.store_adjust_by_ids(adjust_ids, partner_id)
        reason_map = get_supply_reason_map(_type="ADJUST", partner_id=partner_id, user_id=user_id)
        for adjust_obj in adjust_objs:
            if adjust_obj.attachments:
                attachments = json.loads(adjust_obj.attachments)
                adjust_obj.attachments = attachments.get('attachments', [])
            adjust_obj.reason_name = reason_map.get(adjust_obj.reason_type)
        return adjust_objs

    # 确认前获取折算bom数量
    def get_convert_quantity_map(self, adjust_obj, list_adjust_product, partner_id, user_id):
        product_ids = []
        adjust_bom_report_list = []
        for product in list_adjust_product:
            if product.get('quantity'):
                product_ids.append(int(product.get('product_id')))

        product_ids = set(product_ids)
        # product_fields = ["product_type", "bom_type", "sale_type"]
        # ret = metadata_service.get_attribute_products_by_store_id(
        #     return_fields=['id'],
        #     # product_filters=product_filters,
        #     include_product_fields=product_fields,
        #     store_id=adjust_obj.adjust_store,
        #     product_ids=product_ids,
        #     partner_id=partner_id,
        #     user_id=user_id
        # )
        ret = metadata_service.get_product_list(ids=product_ids,
                                                include_units=False,
                                                return_fields='id,code,bom_type',
                                                partner_id=partner_id,
                                                user_id=user_id)

        product_unit_map = get_product_unit_map(product_ids=product_ids, partner_id=partner_id, user_id=user_id)
        is_bom_products_list = []
        not_has_bom_products_list = []
        if ret:
            for p in ret['rows']:
                if p.get('bom_type') == "MANUFACTURE":
                    is_bom_products_list.append(int(p.get('id', 1)))
        bom_products_map = {}

        for product in list_adjust_product:
            line_id = get_guid()
            accounting_quantity = product['accounting_quantity'] if product.get('accounting_quantity') else 0
            quantity = product['quantity'] if product.get('quantity') else 0
            if int(product.get('product_id')) in is_bom_products_list and accounting_quantity:
                # 拆解bom成品需要转为配方单位数量
                bom_unit = product_unit_map.get(int(product.get('product_id')), {}).get('bom')
                bom_quantity = 0
                if not bom_unit:
                    raise DataValidationException("Product not bom unit-{}!".format(product.get('product_code')))
                if isinstance(bom_unit, dict):
                    bom_unit_rate = convert_to_decimal(bom_unit.get('rate', 1))
                    bom_quantity = convert_to_decimal(accounting_quantity) / bom_unit_rate
                options = []
                if product.get('sku_remark'):
                    sku_remark = json.loads(product.get('sku_remark'))
                    if isinstance(sku_remark, dict):
                        sku_remark_list = sku_remark.get('sku_remark', [])
                        for sku in sku_remark_list:
                            option = dict(
                                code=sku.get('name', {}).get('code'),
                                tags=[dict(code=sku.get('values', {}).get('code'))]
                            )
                            options.append(option)
                param_dict = dict(
                    request_id=get_guid(),
                    store_id=adjust_obj.adjust_store,
                    store_code=None,
                    sales_date=str(datetime.now()),
                    biz_code="ADJUST",
                    biz_no=str(adjust_obj.id),
                    product_id=product['product_id'],
                    product_code=None,
                    product_qty=abs(bom_quantity),
                    options=options if options else None,
                    partner_id=partner_id,
                    user_id=user_id
                )
                ret = Bom_service.get_bom(**param_dict)
                if not ret:
                    continue
                logging.info('ret-----------------------' + str(ret))
                logging.info('param_dict-----------------------' + str(param_dict))
                position = product.get('position_id')
                if not position:
                    position_id = 0
                else:
                    position_id = str(position)
                if ret:
                    if int(ret.get('request_id')) == int(param_dict['request_id']) and ret.get('bom'):
                        boms = ret["bom"]
                        # 原料剔除不可报废的商品
                        bom_product_ids = [int(bom_.get('product_id')) for bom_ in boms]
                        list_store_product_ret = metadata_service.get_attribute_products_by_store_id(
                            return_fields='allow_adjust',
                            product_ids=bom_product_ids,
                            store_id=adjust_obj.adjust_store,
                            filters={"allow_adjust": True},
                            partner_id=partner_id,
                            product_filters={"status__in": ["ENABLED"]},
                            user_id=user_id,
                            check_division=False
                        ).get('rows', [])
                        can_adjust_bom_product = []
                        if len(list_store_product_ret) > 0:
                            for pr in list_store_product_ret:
                                # 费用化属性商品不产生报废单
                                if pr.get('allow_adjust'):
                                    can_adjust_bom_product.append(int(pr.get('product_id')))
                        if len(can_adjust_bom_product) == 0:
                            # 全部原料都不可报废，标记该商品
                            not_has_bom_products_list.append(int(product.get('product_id')))

                        # 相同line_id，line_m_qty物料数量只统计一次
                        line_bom_product_flag = {}
                        for bom in boms:
                            product_id = int(bom.get('product_id'))
                            if line_bom_product_flag.get(product_id):
                                line_bom_product_flag[product_id] = 'True'
                            else:
                                line_bom_product_flag[product_id] = 'False'
                            has_same_line_flag = line_bom_product_flag[product_id]
                            # 原料剔除不可报废的商品
                            if product_id not in can_adjust_bom_product:
                                continue
                            qty = bom.get('qty') if bom.get('qty') else 0
                            if accounting_quantity < 0:
                                qty = -qty
                            key = str(position_id) + ',' + str(product_id)
                            if bom_products_map.get(key):
                                bom_products_map[key] += qty
                            else:
                                bom_products_map[key] = qty
                            adjust_bom_report = dict(
                                adjust_id=adjust_obj.id,
                                store_id=adjust_obj.adjust_store,
                                product_id=int(product.get('product_id')),
                                line_id=line_id,
                                line_m_qty='0' if has_same_line_flag == 'True' else str(quantity),
                                line_m_accounting_qty='0' if has_same_line_flag == 'True' else str(accounting_quantity),
                                unit_id=product.get('unit_id'),
                                unit_name=product.get('unit_name'),
                                unit_spec=product.get('unit_spec'),
                                accounting_unit_id=product.get('accounting_unit_id'),
                                accounting_unit_name=product.get('accounting_unit_name'),
                                accounting_unit_spec=product.get('accounting_unit_spec'),

                                bom_product_id=int(bom.get('product_id')),
                                accounting_product_qty=str(accounting_quantity),
                                product_qty=str(quantity),
                                reason_type=product.get('reason_type'),
                                qty=str(qty),
                                position_id=product.get('position_id')
                            )
                            adjust_bom_report_list.append(adjust_bom_report)
                    else:
                        adjust_bom_report = dict(
                            adjust_id=adjust_obj.id,
                            line_id=line_id,
                            line_m_qty=str(quantity),
                            line_m_accounting_qty=str(accounting_quantity),
                            store_id=adjust_obj.adjust_store,
                            product_id=int(product.get('product_id')),
                            accounting_product_qty=str(accounting_quantity),
                            product_qty=str(quantity),
                            unit_id=product.get('unit_id'),
                            unit_name=product.get('unit_name'),
                            unit_spec=product.get('unit_spec'),
                            accounting_unit_id=product.get('accounting_unit_id'),
                            accounting_unit_name=product.get('accounting_unit_name'),
                            accounting_unit_spec=product.get('accounting_unit_spec'),
                            reason_type=product.get('reason_type'),
                            position_id=product.get('position_id')
                        )
                        adjust_bom_report_list.append(adjust_bom_report)
                        not_has_bom_products_list.append(int(product.get('product_id')))
            else:
                adjust_bom_report = dict(
                    line_id=line_id,
                    line_m_qty=str(quantity),
                    line_m_accounting_qty=str(accounting_quantity),
                    adjust_id=adjust_obj.id,
                    store_id=adjust_obj.adjust_store,
                    product_id=int(product.get('product_id')),
                    accounting_product_qty=str(accounting_quantity),
                    product_qty=str(quantity),
                    unit_id=product.get('unit_id'),
                    unit_name=product.get('unit_name'),
                    unit_spec=product.get('unit_spec'),
                    accounting_unit_id=product.get('accounting_unit_id'),
                    accounting_unit_name=product.get('accounting_unit_name'),
                    accounting_unit_spec=product.get('accounting_unit_spec'),
                    reason_type=product.get('reason_type'),
                    position_id=product.get('position_id')
                )
                adjust_bom_report_list.append(adjust_bom_report)
        return bom_products_map, is_bom_products_list, not_has_bom_products_list, adjust_bom_report_list

    # 检查商品调整是否超出库存 - ******** - 增加业务配置项
    def check_adjust_inventory(self, adjust_id, partner_id, user_id, action):
        adjust_products = self.adjust_repo.list_store_adjust_product(adjust_id=adjust_id, partner_id=partner_id)
        list_adjust_products = []
        if isinstance(adjust_products, tuple):
            total = adjust_products[0]
            adjust_products = adjust_products[1]
            store_id = adjust_products[0].get("adjust_store")
        else:
            store_id = adjust_products[0].get("adjust_store")
            total = len(adjust_products)
        if not adjust_products or total == 0:
            logging.info("{}报废单没有商品".format(adjust_id))
            return True
        list_adjust_products = adjust_products
        # 获取单据类型
        adjust = self.adjust_repo.list_adjust(id=adjust_id)
        # 检查业务配置-是否需要进行负库存校验
        need_check_map = metadata_service.get_neg_inv_config(partner_id=partner_id, user_id=user_id,
                                                             domain="boh.store.adjust",
                                                             store_id=store_id)
        if not need_check_map.get(action, False) and adjust and adjust[0].branch_type in ["STORE", "FRS_STORE"]:
            return True
        allow_neg_inv = metadata_service.get_business_extra_config(
            partner_id=partner_id, user_id=user_id,
            domain="boh.store.adjust").get("allow_neg_inv")

        if allow_neg_inv and allow_neg_inv == True and adjust and adjust[0].branch_type not in ["STORE",
                                                                                                "FRS_STORE"]:  # 允许负库存
            return True
        # 配方属性=现做BOM 商品列表获取
        is_bom_products_list = []
        product_ids = []
        for p in adjust_products:
            product_ids.append(p.get('product_id'))
        product_filters = {"bom_type__in": ["MANUFACTURE"]}
        product_fields = ["product_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            return_fields=['id'],
            product_filters=product_filters,
            include_product_fields=product_fields,
            store_id=store_id,
            product_ids=product_ids,
            partner_id=partner_id,
            user_id=user_id
        )
        if ret:
            is_bom_products_list = [int(data.get('product_id', 1)) for data in ret.get('rows')]

        inventory_map = inventory_service.get_products_inventory_by_branch_id(branch_id=store_id,
                                                                              partner_id=partner_id,
                                                                              user_id=user_id)
        list_error = []
        over_product_id_list = []
        for p in list_adjust_products:
            inv = inventory_map.get(str(p.get("product_id")))
            # 现在bom商品不校验库存
            if int(p.get("product_id", 0)) in is_bom_products_list:
                pass
            else:
                accounting_quantity = Decimal(p.get('accounting_quantity', 0)).quantize(Decimal('0.********'))
                if inv:
                    quantity_avail = Decimal(inv.get('quantity_avail', 0)).quantize(Decimal('0.********'))
                    if accounting_quantity > 0 and quantity_avail < accounting_quantity:
                        logging.info(
                            "check_adjust_inventory_err {}inv:{}less_than{}".format(p.get("product_name", ""),
                                                                                    quantity_avail,
                                                                                    accounting_quantity))
                        list_error.append(p.get("product_name", ""))
                        over_product_id_list.append(str(p.get("product_id")))
                else:
                    logging.info("check_adjust_inventory_err {}inv:0 less_than{}".format(p.get("product_name", ""),
                                                                                         accounting_quantity))
                    list_error.append(p.get("product_name", ""))
                    over_product_id_list.append(str(p.get("product_id")))
        if len(list_error) > 0:
            raise AdjustInventoryException("报废数量不能超出实时库存:" + '\n' + ','.join(list_error),
                                           detail=over_product_id_list)

    def reject_adjust(self, adjust_id, partner_id, user_id, reject_reason=None):
        try:
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            adjust_obj = self.adjust_repo.update_adjust_status(adjust_id=adjust_id, allow_status=["SUBMITTED"],
                                                               status="REJECTED", user_id=user_id, partner_id=partner_id,
                                                               username=username, reason=reject_reason)
            if adjust_obj:
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=adjust_obj.adjust_store,
                                                 doc_type="adjust"))
                # Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="驳回",
                #            actionStatus=True, storeId=adjust_obj.adjust_store,
                #            storeName="", content="驳回成功！")
                return {'description': 'success', 'adjust_id': adjust_id, 'adjust_code': adjust_obj.code}
            else:
                # Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="驳回",
                #            actionStatus=False, storeId=adjust_obj.adjust_store,
                #            storeName="", content="当前状态不允许驳回！")
                adjust_obj = self.adjust_repo.get_adjust_by_adjust_id(adjust_id, partner_id=partner_id)
                raise ActionException("当前状态不允许驳回！")
        except Exception as e:
            logging.error(e)
            error_description = e.description if hasattr(e, "description") else "驳回失败！"
            adjust_code = adjust_obj.code if adjust_obj else '0'
            return {'description': error_description, 'adjust_id': adjust_id, 'adjust_code': adjust_code}

    def approve_adjust(self, adjust_id, partner_id, user_id, is_pos=None):
        try:
            if is_pos:
                pass
            else:
                self.check_adjust_inventory(adjust_id, partner_id, user_id, action="approve")

            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            adjust_obj = self.adjust_repo.get_adjust_by_adjust_id(adjust_id, partner_id=partner_id)
            if adjust_obj and adjust_obj.status in ('INITED', 'SUBMITTED'):
                list_adjust_product = self.adjust_repo.list_store_adjust_product(adjust_id=adjust_id, partner_id=partner_id)
                # 获取bom折算
                bom_products_map, is_bom_products_list, not_has_bom_products_list, adjust_bom_report_list = self.get_convert_quantity_map(
                    adjust_obj, list_adjust_product, partner_id, user_id)
                logging.info('is_bom_products_list-{}'.format(is_bom_products_list))
                logging.info('adjust_bom_report_list{}'.format(adjust_bom_report_list))
                # 库存调整
                WITHDRAW_ACCOUNTS = []
                DEPOSIT_ACCOUNTS = []
                index = 1
                for adjust_product in list_adjust_product:
                    if not adjust_product.get('accounting_quantity'):
                        continue
                    product_id = int(adjust_product.get('product_id'))
                    if product_id not in is_bom_products_list:
                        index += 1
                        accounting_quantity = adjust_product['accounting_quantity'] if adjust_product.get(
                            'accounting_quantity') else 0
                        quantity = 0
                        position = adjust_product.get('position_id')
                        if not position:
                            position_id = 0
                        else:
                            position_id = str(position)
                        key = str(position_id) + ',' + str(product_id)
                        if bom_products_map.get(key, 0):
                            convert_accounting_quantity = convert_to_decimal(accounting_quantity) + \
                                                          convert_to_decimal(bom_products_map[key])
                        else:
                            convert_accounting_quantity = accounting_quantity
                        if convert_accounting_quantity:
                            quantity = 0 - convert_accounting_quantity
                        # 加库存
                        if quantity > 0:
                            # 标记已加进订单扣库存
                            bom_products_map[key] = 0

                            acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=adjust_product.get('product_id'),
                                            action=2,
                                            amount=str(abs(quantity)))
                            if adjust_product.get('position_id') and adjust_product.get('position_id') != 1:
                                acc_data['sub_account'] = {"id": adjust_product.get('position_id')}
                            DEPOSIT_ACCOUNTS.append(acc_data)
                        # 减库存
                        elif quantity < 0:
                            # 标记已加进订单扣库存
                            bom_products_map[key] = 0
                            acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=adjust_product.get('product_id'),
                                            action=1,
                                            amount=str(abs(quantity)))
                            if adjust_product.get('position_id') and adjust_product.get('position_id') != 1:
                                acc_data['sub_account'] = {"id": adjust_product.get('position_id')}
                            WITHDRAW_ACCOUNTS.append(acc_data)

                # bom拆解未加进订单的也要进行库存扣减
                mater_products = []
                for k, v in bom_products_map.items():
                    id_list = k.split(',')
                    product_id = id_list[1]
                    position_id = id_list[0]
                    mater_products.append(int(product_id))
                    if v:
                        quantity = 0 - v
                        # 加库存
                        if quantity > 0:
                            acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=product_id,
                                            action=2,
                                            amount=str(abs(quantity)))
                            if int(position_id) and int(position_id) != 1:
                                acc_data['sub_account'] = {"id": int(position_id)}
                            DEPOSIT_ACCOUNTS.append(acc_data)
                        # 减库存
                        elif quantity < 0:
                            acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=product_id,
                                            action=1,
                                            amount=str(abs(quantity)))
                            if int(position_id) and int(position_id) != 1:
                                acc_data['sub_account'] = {"id": int(position_id)}
                            WITHDRAW_ACCOUNTS.append(acc_data)
                ACCOUNTS = WITHDRAW_ACCOUNTS + DEPOSIT_ACCOUNTS
                logging.info("报废商品请求ACCOUNTS：{}".format(ACCOUNTS))
                # bom拆解后的原料也要推送
                # 先拿原料补充信息
                # bom拆解后的原料需要配方单位数量
                mater_products_info_ret = metadata_service.get_product_list(ids=mater_products,
                                                                            include_units=True,
                                                                            return_fields='id,code',
                                                                            partner_id=partner_id,
                                                                            user_id=user_id)
                mater_products_info = []
                if mater_products_info_ret:
                    mater_products_info = mater_products_info_ret.get('rows')

                unit_dict = {}
                units_ret = metadata_service.get_unit_list(return_fields='id,code,rate,name',
                                                           partner_id=partner_id, user_id=user_id
                                                           )
                units = []
                if units_ret:
                    units = units_ret['rows']
                if isinstance(units, list):
                    for u in units:
                        if isinstance(u, dict) and 'id' in u:
                            unit_dict[str(u['id'])] = dict()
                            if 'code' in u:
                                unit_dict[str(u['id'])]['code'] = u['code']
                            else:
                                unit_dict[str(u['id'])]['code'] = None
                            if 'name' in u:
                                unit_dict[str(u['id'])]['name'] = u['name']
                            else:
                                unit_dict[str(u['id'])]['name'] = None
                mater_products_info_map = {}
                for p_i in mater_products_info:
                    mater_products_info_map[p_i.get('id')] = {}
                    units = p_i.get('units')
                    mater_products_info_map[p_i.get('id')]['product_code'] = p_i.get('code')
                    if units:
                        for unit in units:
                            if unit.get('bom'):
                                if unit_dict.get(str(unit.get('id'))):
                                    mater_products_info_map[p_i.get('id')]['unit_id'] = \
                                        unit.get('id')
                                    mater_products_info_map[p_i.get('id')]['unit_spec'] = \
                                        unit_dict.get(str(unit.get('id')))['code']
                                    mater_products_info_map[p_i.get('id')]['rate'] = unit.get('rate') \
                                        if unit.get('rate') else 1
                                    mater_products_info_map[p_i.get('id')]['unit_name'] = \
                                        unit_dict.get(str(unit.get('id')))['name']
                            # 给报表加入核算原料单位
                            if unit.get('default'):
                                if unit_dict.get(str(unit.get('id'))):
                                    mater_products_info_map[p_i.get('id')]['accounting_unit_id'] = \
                                        unit.get('id')
                                    mater_products_info_map[p_i.get('id')]['accounting_unit_spec'] = \
                                        unit_dict.get(str(unit.get('id')))['code']
                                    mater_products_info_map[p_i.get('id')]['accounting_unit_name'] = \
                                        unit_dict.get(str(unit.get('id')))['name']

                if ACCOUNTS and len(ACCOUNTS) > 0:
                    code = 'ADJUST'
                    # 收货差异产生的报废，流水类型做区分
                    if adjust_obj.reason_type == '01':
                        code = 'DIFF_ADJUST'
                    # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
                    message = dict(batch_no=str(adjust_id),
                                   code=code,
                                   action=100,
                                   description='ADJUST',
                                   trace_id=adjust_obj.code,
                                   accounts=ACCOUNTS,
                                   partner_id=partner_id,
                                   user_id=user_id,
                                   business_time=datetime.utcnow())
                    inventory_dict = dict(batch_no=str(adjust_id), code=code, batch_action=100,
                                          action_dec=ACTION[100],
                                          batch_id=None,
                                          status="ERROR")  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                    inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                     partner_id=partner_id, user_id=user_id)
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                        message=message)
                # 写入报废bom报表
                self.adjust_bom_report(adjust_id, partner_id, user_id, adjust_bom_report_list, mater_products_info_map)
                adjust_obj = self.adjust_repo.approve_adjust(adjust_id, user_id=user_id,
                                                             username=username, bom_products_map=bom_products_map,
                                                             is_bom_products_list=is_bom_products_list,
                                                             partner_id=partner_id)
                # vendor单据同步给三方
                message = {
                    'doc_resource': 's_adjust' if adjust_obj.branch_type != 'FRS_STORE' else 'fs_adjust',
                    'doc_id':       adjust_id,
                    'partner_id':   partner_id,
                    'user_id':      user_id,
                    'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)

                # 同步记录落表，用于后续补偿
                tp_trans_log = {
                    'id':         adjust_id,
                    'doc_code':   adjust_obj.code,
                    'doc_type':   's_adjust' if adjust_obj.branch_type != 'FRS_STORE' else 'fs_adjust',
                    'status':     'inited',
                    'msg':        str(message),
                    'partner_id': partner_id,
                    'created_by': user_id,
                    'created_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow()
                }
                TpTransLogModel.create_logs_list([tp_trans_log])

                if adjust_obj:
                    # 清理待办缓存
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                        message=dict(partner_id=partner_id, store_id=adjust_obj.adjust_store,
                                                     doc_type="adjust"))
                    # Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="审核",
                    #            actionStatus=True, storeId=adjust_obj.adjust_store,
                    #            storeName="", content="报废单审核成功")
                    return {'description': 'success', 'adjust_id': adjust_id, 'adjust_code': adjust_obj.code}
                else:
                    # Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="审核",
                    #            actionStatus=False, storeId=adjust_obj.adjust_store,
                    #            storeName="", content="报废单审核失败")
                    raise ActionException('审核失败')
            else:
                raise ActionException('审核失败, 当前状态不允许审核！')
        except Exception as e:
            logging.error(e)
            error_description = e.description if hasattr(e, "description") else "审核失败！"
            adjust_code = adjust_obj.code if adjust_obj else '0'
            return {'description': error_description, 'adjust_id': adjust_id, 'adjust_code': adjust_code}

    def batch_approve_adjust(self, adjust_ids, partner_id, user_id):
        tasks = []
        asyncio.set_event_loop(asyncio.new_event_loop())
        loop = asyncio.get_event_loop()
        for adjust_id in adjust_ids:
            tasks.append(loop.run_in_executor(None, self.approve_adjust, adjust_id, partner_id, user_id))
        res = loop.run_until_complete(asyncio.gather(*tasks))
        return res

    def batch_reject_adjust(self, adjust_ids, partner_id, user_id, reject_reason):
        tasks = []
        asyncio.set_event_loop(asyncio.new_event_loop())
        loop = asyncio.get_event_loop()
        for adjust_id in adjust_ids:
            tasks.append(loop.run_in_executor(None, self.reject_adjust, adjust_id, partner_id, user_id, reject_reason))
        res = loop.run_until_complete(asyncio.gather(*tasks))
        return res

    def adjust_bom_report(self, adjust_id, partner_id, user_id, adjust_bom_report_list, mater_products_info):
        """
        报废确认写入bom拆解日志
        """
        batch_obj = self.get_adjust_bom_report_batch(adjust_id, partner_id=partner_id)
        if batch_obj:
            return True
        else:
            # 没有写入一条记录
            form = {
                "adjust_id":  int(adjust_id),
                "partner_id": partner_id
            }
            adjust_bom_report_batch_obj = self.add_adjust_bom_report_batch(**form)
            if not adjust_bom_report_batch_obj:
                # 重复请求
                return True
            # 展示纬度：门店编码|门店名称|商品名称|商品编码|报废数量|原料名称|原料编码|拆解数量|核算单位
            for adjust_bom_report in adjust_bom_report_list:
                adjust_bom_report['batch_id'] = adjust_bom_report_batch_obj.id
                adjust_bom_report['created_by'] = user_id
                adjust_bom_report['partner_id'] = partner_id
                adjust_bom_report['product_qty'] = convert_to_decimal(adjust_bom_report['product_qty'])
                adjust_bom_report['accounting_product_qty'] = convert_to_decimal(
                    adjust_bom_report.get('accounting_product_qty', 0))
                if adjust_bom_report.get('bom_product_id'):
                    adjust_bom_report['accounting_qty'] = convert_to_decimal(
                        adjust_bom_report['qty']) if adjust_bom_report.get('qty') else 0
                    info = mater_products_info.get(str(adjust_bom_report['bom_product_id']))
                    if info:
                        if adjust_bom_report.get('qty'):
                            adjust_bom_report['qty'] = float(adjust_bom_report['qty']) / info.get('rate') if info.get(
                                'rate') else adjust_bom_report['accounting_qty']
                        else:
                            adjust_bom_report['qty'] = adjust_bom_report['accounting_qty']
                        adjust_bom_report['bom_unit_id'] = convert_to_int(info.get('unit_id'))
                        adjust_bom_report['bom_unit_name'] = info.get('unit_name')
                        adjust_bom_report['bom_unit_spec'] = info.get('unit_spec')
                        adjust_bom_report['bom_accounting_unit_id'] = convert_to_int(info['accounting_unit_id'])
                        adjust_bom_report['bom_accounting_unit_name'] = info.get('accounting_unit_name')
                        adjust_bom_report['bom_accounting_unit_spec'] = info.get('accounting_unit_spec')
                        adjust_bom_report['bom_rate'] = info.get('rate')
            self.add_adjust_bom_report_list(adjust_bom_report_list)
        return True

    def get_adjust_bom_report_batch(self, adjust_id, partner_id=None):
        return self.adjust_repo.get_adjust_bom_report_batch(adjust_id=adjust_id, partner_id=partner_id)

    def add_adjust_bom_report_batch(self, adjust_id, partner_id):
        return self.adjust_repo.add_adjust_bom_report_batch(adjust_id, partner_id)

    def add_adjust_bom_report_list(self, adjust_bom_report_list):
        return self.adjust_repo.add_adjust_bom_report_list(adjust_bom_report_list)

franchisee_adjust_service = FranchiseeAdjustService()
