# -*- coding: utf-8 -*-
import logging
from collections import namedtuple
from decimal import Decimal

from supply import logger
from datetime import datetime

from supply.driver.mysql import session
from supply.error.demand import ProductError
from supply.utils.enums import Platform
from supply.utils.helper import get_product_map, get_uuids, convert_to_int, convert_to_decimal, get_category_map, \
    get_unit_map, get_guid
from supply.client.metadata_service import metadata_service
from supply.module import BaseToolsModule
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProduct as sFDP
# from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProductRelation as sFDPR
# from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand as sFD
# from supply.model.franchisee.franchisee_demand import DemandAction as Action


class ImportFranchiseeDemandService(BaseToolsModule):
    """加盟商订货导入业务操作"""

    def __init__(self):
        super(ImportFranchiseeDemandService, self).__init__()
        self.platform = Platform.HEX_WEB.code

    def import_frs_demand_products(self, request, partner_id, user_id):
        """导入订单修改
        存在重复记录时：
        导入订单（最大值）Overwrite   若导入数量>审核数量 保存"导入数量"为商品的"确认数量"，否则保存"审核数量"
        导入订单（增加值）Increase    将导入数量 + 审核数量保存为确认数量
        不存在重复记录时，在订货单内生成新商品记录，商品订货数量为0，确认数量等于导入数量
        """
        res = dict(result=True)
        if not request.rows:
            return res
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # from google.protobuf.json_format import MessageToJson
        # logging.info("aaaaaaa")
        # logging.info(MessageToJson(request, preserving_proto_field_name=True))
        insert_products, insert_rp, update_rp = self.build_insert_products(request.rows, partner_id, user_id, username)
        if request.import_type == "Overwrite":
            res['result'] = sFDP.import_upsert_products_max(insert_products, partner_id=partner_id, user_id=user_id,
                                                            username=username, platform=self.platform,
                                                            relaiton_products=insert_rp,
                                                            update_relation_products=update_rp)
        if request.import_type == "Increase":
            res['result'] = sFDP.import_upsert_products_increase(insert_products, partner_id=partner_id, user_id=user_id,
                                                                 username=username, platform=self.platform,
                                                                 relaiton_products=insert_rp,
                                                                 update_relation_products=update_rp)
        return res

    def build_insert_products(self, products, partner_id, user_id, username):
        insert_products = []
        product_ids = []
        # unit_ids = []
        category_ids = []
        demand_ids = []
        all_products, demand_relation_products = [], []
        demand_product_key = set()
        key = namedtuple('key', ['demand_id', 'id', 'type', 'relation_type', 'father_id', 'father_product_id'])
        demand_key = namedtuple('demand_key', ['demand_id', 'product_id'])
        for index, row in enumerate(products):
            if not row.demand_id:
                continue
            demand_id = int(row.demand_id)
            demand_ids.append(demand_id)
            all_products.append({})

            ids = get_uuids(len(row.products))
            for i, p in enumerate(row.products):
                product_id = convert_to_int(p.product_id)
                product_ids.append(product_id)
                category_ids.append(convert_to_int(p.category_id))
                # unit_ids.append(convert_to_int(p.unit_id))
                # unit_ids.append(convert_to_int(p.accounting_unit_id))

                all_products[index][key(demand_id, ids[i], 'main', 'BIND' if p.relation_products else '', None,
                                        None)] = p
                demand_product_key.add(demand_key(demand_id, product_id))

                r_ids = get_uuids(len(p.relation_products))
                for ri, rp in enumerate(p.relation_products):
                    product_ids.append(int(rp.product_id))
                    category_ids.append(int(rp.category_id))
                    all_products[index][key(demand_id, r_ids[ri], 'bind', 'BIND', ids[i], product_id)] = rp

        product_ids = list(set(product_ids))
        category_ids = list(set(category_ids))
        # unit_ids = list(set(unit_ids))

        # demands = session.query(sFD).filter(sFD.id.in_(demand_ids)).with_for_update().all()
        exist_products = session.query(sFDP.demand_id, sFDP.product_id).\
            filter(sFDP.demand_id.in_(demand_ids), sFDP.product_id.in_(product_ids)).all()
        exist_products = {demand_key(i.demand_id, i.product_id) for i in exist_products}
        create_products = demand_product_key - exist_products
        update_products = demand_product_key - create_products

        update_relation_products = {}
        for i in update_products:
            if i.demand_id in update_relation_products:
                update_relation_products[i.demand_id].append(i.product_id)
                continue

            update_relation_products[i.demand_id] = [i.product_id]


        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type",
                                      partner_id=partner_id, user_id=user_id)
        # 商品类别
        category_map = get_category_map(category_ids, partner_id, user_id)
        # 单位
        unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
        # demand_logs = []
        # product_logs = []
        # trace_id = get_guid()
        for index, row in enumerate(products):
            if len(row.products) == 0:
                continue
            # demand_ids.append(str(row.demand_id))
            # demand_log = dict(
            #     partner_id=partner_id,
            #     demand_id=row.demand_id,
            #     action=Action.IMPORT.code,
            #     action_name=Action.IMPORT.desc,
            #     platform=self.platform,
            #     created_by=user_id,
            #     created_name=username,
            #     created_at=datetime.utcnow(),
            #     trace_id=trace_id
            # )
            # demand_logs.append(demand_log)
            row_products = all_products[index]
            # ids = get_uuids(len(row.products))
            for k, p in row_products.items():
                is_main = k.type == 'main'
                need_create = False

                product = product_map.get(str(p.product_id), {})
                unit = unit_map.get(p.unit_id, {})
                accounting_unit = unit_map.get(p.accounting_unit_id, {})
                quantity = convert_to_decimal(p.quantity) if p.quantity else convert_to_decimal(0)
                # 订货价
                tax_price = convert_to_decimal(p.order_tax_price) if p.order_tax_price else convert_to_decimal(0)
                tax_rate = convert_to_decimal(p.tax_rate) if p.tax_rate else convert_to_decimal(0)
                cost_price = tax_price / convert_to_decimal(1 + tax_rate / 100)
                # 零售价
                sales_price = convert_to_decimal(p.sale_tax_price)
                confirm_sales_amount = round(sales_price * quantity, 2)
                confirm_amount = round(quantity * tax_price, 2)

                other_data = {}
                # 因为导入改单的状态为待确认，而且目前前端会去更新捆绑商品的数量，加上此次需求时间比较紧张，所以先不处理更新的捆绑商品
                if not is_main and demand_key(k.demand_id, k.father_product_id) in create_products:
                    ratio = Decimal(p.ratio) if p.ratio else p.ratio
                    # if not ratio or ratio < 0:
                    #     raise ProductError(f"捆绑商品id: {p.product_id}比率错误")
                    #
                    # if not product:
                    #     raise ProductError(f'商品product_id:{p.product_id}不存在')
                    units = product.get('units')
                    unit_id = None
                    accounting_unit_id = None
                    unit_rate = 1
                    unit = {}
                    accounting_unit = {}
                    if units:
                        order_flag = False
                        for u in units:
                            if u.get("order"):
                                unit_rate = float(u.get("rate", 1))
                                unit_id = int(u.get('id'))
                                order_flag = True
                                unit = unit_map.get(unit_id, {})
                            if u.get('default'):
                                accounting_unit_id = int(u.get('id'))
                                accounting_unit = unit.get(accounting_unit_id, {})
                        if not order_flag:
                            continue
                            # raise ProductError(f"此商品未设置订货单位，请检查主档设置, product_id:{p.product_id}")
                    # else:
                    #     raise ProductError(f"此商品未设置单位，请检查主档设置, product_id:{p.product_id}")
                    other_data = {
                        'demand_product_id': k.father_id,
                        'ratio': ratio,
                        'configure': p.configure,
                        'unit_rate': unit_rate,
                        'unit_id': unit_id,
                        "unit_spec": unit.get("code", "无"),
                        "unit_name": unit.get('name', "无"),
                        'accounting_unit_id': accounting_unit_id,
                        'accounting_unit_name': accounting_unit.get('name'),
                        'accounting_unit_spec': accounting_unit.get("code"),
                    }
                    need_create = True

                product_dict = {
                    "id": k.id,
                    "partner_id": partner_id,
                    "demand_id": row.demand_id,
                    "status": "INITED",
                    "product_id": int(p.product_id),
                    "product_code": product.get("code"),
                    "product_name": product.get("name"),
                    "category_id": int(p.category_id),
                    "category_name": category_map.get(int(p.category_id), {}).get('name', ''),
                    "arrival_days": p.arrival_days,
                    "unit_id": int(p.unit_id),
                    "unit_spec": unit.get("code", "无"),
                    "unit_name": unit.get('name', "无"),
                    "unit_rate": p.unit_rate,
                    "accounting_unit_id": p.accounting_unit_id,
                    "accounting_unit_name": accounting_unit.get('name'),
                    "accounting_unit_spec": accounting_unit.get("code"),
                    "is_confirm_qty": True,
                    "confirm_quantity": quantity,
                    "min_quantity": p.min_quantity,
                    "max_quantity": p.max_quantity,
                    "increment_quantity": p.increment_quantity,
                    "tax_price": tax_price,
                    "sales_price": sales_price,
                    "cost_price": cost_price,
                    "tax_rate": tax_rate,
                    "confirm_amount": confirm_amount,
                    "confirm_sales_amount": confirm_sales_amount,
                    "storage_type": product.get('storage_type'),
                    "created_by": user_id,
                    "updated_by": user_id,
                    "created_name": username,
                    "updated_name": username,
                    'relation_type': k.relation_type
                }
                if not is_main:
                    product_dict.update(other_data)
                    demand_relation_products.append(product_dict) if need_create else ...
                else:
                    insert_products.append(product_dict)

                # product_logs.append(dict(
                #     partner_id=partner_id,
                #     trace_id=trace_id,
                #     demand_id=demand_log['demand_id'],
                #     action=Action.IMPORT.code,
                #     action_name=Action.IMPORT.desc,
                #     product_id=p.product_id,
                #     product_code=product.get("code"),
                #     product_name=product.get("name"),
                #     unit_id=int(p.unit_id),
                #     unit_name=unit.get('name', "无"),
                #     tax_price=str(tax_price),
                #     sales_price=str(sales_price),
                #     arrival_days=str(p.arrival_days),
                #     confirm_quantity=str(quantity),
                #     confirm_amount=str(confirm_amount),
                #     confirm_sales_amount=str(confirm_sales_amount),
                #     created_by=user_id,
                #     created_name=username,
                # ))
        logging.info(f'insert_products: {insert_products}')
        logging.info(f'demand_relation_products: {demand_relation_products}')
        logging.info(f'update_relation_products: {update_relation_products}')
        return insert_products, demand_relation_products, update_relation_products


import_franchisee_demand_service = ImportFranchiseeDemandService()
