import logging
from datetime import datetime
from supply.utils import pb2dict
from supply.client.third_party import third_party
from google.protobuf.timestamp_pb2 import Timestamp
from supply.utils.snowflake import gen_snowflake_id
from supply.client.metadata_service import metadata_service


class Statements(object):
    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    def list_reconciliation(self, partner_id, user_id, request):
        dates = request.query_dates.seconds
        dates = datetime.fromtimestamp(dates)
        iGjahr = dates.year
        iMonat = dates.month
        iKunnr = request.store_code
        fields = request.fields
        limit = request.limit
        offset = request.offset
        order_type = request.order_type
        likeBezei = fields
        voucher = request.voucher
        likeBelnr = voucher
        desc = request.desc
        order = 1 if desc else 0
        begining_balance = 0
        ending_balance = 0
        collections = []
        msg = ''
        uid = ''
        total = 0
        code = 0
        created_at = self.get_timestamp(datetime.utcnow())
        response = {
            'collections': collections,
            'begining_balance': begining_balance,
            'ending_balance': ending_balance,
            'create_at': created_at,
            'msg': msg,
            'uid': uid,
            'total': total,
            'code': code
        }

        if not iKunnr:
            return response
        else:
            company_code = get_company_code(iKunnr, partner_id, user_id)
            logging.info("company_code%s" % company_code)
            iBukrs = company_code
            result = third_party.get_statements(
                iGjahr, iMonat, iKunnr, iBukrs, limit, offset=offset,
                order=order, likeBezei=likeBezei, likeBelnr=likeBelnr)
            collections = result.get('rows', [])
            msg = result.get('msg')
            total = result.get('total')
            code = result.get('status')
            response['msg'] = msg
            response['code'] = code

            logging.info("code %s" % code)  # code:0/1
            logging.info("total %s" % total)  # 总条数
            if collections:
                # 结果集第一个元素为聚合数据
                begining_item = pb2dict(collections.pop(0))
                begining_balance = self.get_transaction_balance(begining_item.get('dmbtrE'))
                # 结果集最后一个元素的余额用于展示账户余额
                # 220302: 修改结果集第二个元素用于展示账户余额
                ending_item = pb2dict(collections.pop(0))
                ending_balance = self.get_transaction_balance(ending_item.get('dmbtrE'))
                collections = self.handle_reconciliation(collections)
                uid = self.get_uid(collections) if collections else uid
                response['collections'] = collections
                response['begining_balance'] = begining_balance
                response['ending_balance'] = ending_balance
                response['total'] = total
                response['uid'] = uid

            return response

    def get_uid(self, collections):
        FIRST_ROW = 0
        uid = collections[FIRST_ROW].get('uid')
        return uid

    def handle_reconciliation(self, collections):
        res = []
        for collection in collections:
            item = {}
            reconciliation = pb2dict(collection)
            print(reconciliation)
            uid = reconciliation.get('kunnr')
            uname = reconciliation.get('bezei')
            voucher_no = reconciliation.get('belnr')
            dmbtrY = reconciliation.get('dmbtrY', 0)
            dmbtrH = reconciliation.get('dmbtrH')
            transaction_amount = self.get_transaction_amount(dmbtrY, dmbtrH)
            balance = self.get_transaction_balance(reconciliation.get('dmbtrE'))
            account_amount = reconciliation.get('klimk')
            credit = reconciliation.get('budat')
            address = reconciliation.get('address')
            item['uid'] = uid
            item['uname'] = uname
            item['voucher_no'] = voucher_no
            item['transcation_amount'] = transaction_amount
            item['balance'] = balance
            item['account_amount'] = account_amount
            item['credit'] = credit
            item['address'] = address
            item['id'] = gen_snowflake_id()
            res.append(item)
        return res

    def get_transaction_amount(self, dmbtrY, dmbtrH):
        """
        :param dmbtrY: 本期应收 -
        :param dmbtrH: 本期回收 +
        :return: 返回交易金额
        """
        if not dmbtrY and not dmbtrH:
            dmbtrH = 0
        transaction_amount = dmbtrH if dmbtrH else 0 - dmbtrY
        return transaction_amount

    def get_transaction_balance(self, dmbtrE):
        """
        余额要做符号转换
        """
        balance = 0 - dmbtrE if dmbtrE else 0
        return balance


class PcStatements(Statements):
    pass


class MobileStatements(Statements):
    pass


def get_company_code(store_code, partner_id, user_id):
    search_fields = "code"
    collections = metadata_service.get_store_list(
                    search_fields=search_fields, search=store_code,
                    partner_id=partner_id, user_id=user_id).get('rows')
    store = collections[0] if collections else {}
    company_info = store.get('company_info')
    company_id = company_info[0] if company_info else ''
    if company_id:
        company_id = int(company_id)
        company = metadata_service.get_company_by_id(company_id=company_id, partner_id=partner_id, user_id=user_id)
        logging.info("company_id %s" % company_id)
        company_code = company.get('code')
    else:
        company_code = ''
    logging.info("store %s" % store)
    logging.info("company_id %s" % company_id)
    logging.info("store_code %s " % store_code)
    logging.info("partner_id %s " % partner_id)
    logging.info("company_code %s" % company_code)
    return company_code


pc_statements_service = PcStatements()
mobile_statements_service = MobileStatements()
