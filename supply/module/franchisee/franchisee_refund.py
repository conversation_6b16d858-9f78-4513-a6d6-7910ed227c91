from datetime import datetime
from decimal import Decimal
from supply import logger
from supply.error.demand import ProductError
from supply.error.exception import StatusUnavailable, DataValidationException, \
    OrderNotExistException
from supply.model.supply_doc_code import Supply_doc_code
from supply.module import BaseToolsModule
from supply.utils.helper import get_guid, get_branch_map, convert_to_int, get_category_map, get_company_map, get_uuids, \
    convert_to_decimal, get_unit_map, get_product_map, MessageTopic
from supply.client.metadata_service import metadata_service
from supply.utils.enums import RefundType, Demand_type, PaymentWay
from supply.driver.mq import mq_producer

from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefund as sFR, SupplyFranchiseeRefundLog
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefundProduct as sFRP
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand as sFD
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProduct as sFDP


class FranchiseeRefundsService(BaseToolsModule):
    """加盟商退款相关业务操作-PC端"""

    def __init__(self):
        super(FranchiseeRefundsService, self).__init__()

    def create_refund(self, request, partner_id, user_id):
        """创建创建退款单"""
        if request.main_id:
            exist = sFR.get_refund_by_id(partner_id=partner_id, main_id=request.main_id)
            if exist:
                raise Exception("请勿重复提交退款单！")
        else:
            raise Exception("缺少main_id，不允许创建！")
        refund_id = get_guid()
        refund_products = []
        products = request.products
        if not products:
            raise ProductError("退款单须包含商品")
        product_ids = []
        category_ids = []
        unit_ids = []
        for p in products:
            product_ids.append(convert_to_int(p.product_id))
            category_ids.append(convert_to_int(p.category_id))
            unit_ids.append(convert_to_int(p.unit_id))
        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category",
                                      partner_id=partner_id, user_id=user_id)
        unit_map = get_unit_map(unit_ids=unit_ids, partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        refund_detail = dict(
            id=refund_id,
            partner_id=partner_id,
            type=request.type,
            batch_id=request.batch_id,
            main_id=request.main_id,
            main_code=request.main_code,
            main_type=request.main_type,
            code=Supply_doc_code.get_code_by_type('FDR', partner_id, None),
            received_by=request.received_by,
            franchisee_id=request.franchisee_id,
            trade_company=self.get_trade_company_by_franchisee(request.franchisee_id, partner_id, user_id),
            payment_way=request.payment_way,
            refund_date=self.timestamp2datetime(request.refund_date),
            status='INITED',
            process_status='INITED',
            remark=request.remark,
            reason=request.reason,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            refund_amount=Decimal("0"),
            pay_amount=request.pay_amount
        )
        refund_log = dict(
            id=get_guid(),
            partner_id=partner_id,
            refund_id=refund_id,
            action='INITED',
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow()
        )
        ids = get_uuids(len(products))
        for inx, p in enumerate(products):
            product = product_map.get(str(p.product_id), {})
            unit = unit_map.get(int(p.unit_id), {})
            quantity = convert_to_decimal(p.quantity) if p.quantity else Decimal(0)
            tax_price = convert_to_decimal(p.tax_price) if p.tax_price else Decimal(0)
            tax_rate = convert_to_decimal(p.tax_rate) if p.tax_rate else Decimal(0)
            cost_price = tax_price / (Decimal("1") + tax_rate)
            amount = round(quantity * tax_price, 2)
            refund_detail["refund_amount"] += amount
            refund_products.append(dict(
                id=ids[inx],
                refund_id=refund_id,
                partner_id=partner_id,
                product_id=p.product_id,
                product_code=product.get("code"),
                product_name=product.get("name"),
                category_id=int(product.get('category', 0)),
                unit_id=p.unit_id,
                unit_name=unit.get('name', "无"),
                unit_rate=p.unit_rate,
                unit_spec=unit.get("code", "无"),
                quantity=quantity,
                tax_price=tax_price,
                cost_price=cost_price,
                tax_rate=tax_rate,
                amount=amount,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username
            ))
        res = sFR.insert_franchisee_refund(refund_details=[refund_detail], refund_products=refund_products,
                                           refund_logs=[refund_log])
        if res is True:
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FRANCHISEE_REFUND,
                                message=dict(
                                    refund_id=refund_id,
                                    partner_id=partner_id,
                                    user_id=user_id
                                ))
        return {"refund_id": refund_id}

    def list_refund(self, request, partner_id, user_id):
        res = dict()
        refund_start_date = self.utcTimestamp2datetime(request.refund_start_date)
        refund_end_date = self.utcTimestamp2datetime(request.refund_end_date)
        approved_start_date = self.utcTimestamp2datetime(request.approved_start_date)
        approved_end_date = self.utcTimestamp2datetime(request.approved_end_date)
        status = list(request.status) if request.status else []
        franchisee_ids = [convert_to_int(_id) for _id in request.franchisee_ids] if request.franchisee_ids else []
        received_bys = [convert_to_int(_id) for _id in request.received_bys] if request.received_bys else []
        types = [str(tp) for tp in request.types] if request.types else []
        payment_ways = [str(way) for way in request.payment_ways] if request.payment_ways else []
        ids = list(request.ids) if request.ids else []
        sort = request.sort if request.sort else 'updated_at'
        query_set = sFR.list_franchisee_refund(partner_id=partner_id, refund_start_date=refund_start_date, ids=ids,
                                               refund_end_date=refund_end_date, approved_start_date=approved_start_date,
                                               approved_end_date=approved_end_date, franchisee_ids=franchisee_ids,
                                               main_type=request.main_type, payment_ways=payment_ways, types=types,
                                               main_code=request.main_code, received_bys=received_bys, status=status,
                                               code=request.code, offset=request.offset, limit=request.limit,
                                               include_total=request.include_total, order=request.order, sort=sort)
        if isinstance(query_set, tuple):
            total, refunds = query_set
            res['total'] = total
        else:
            refunds = query_set
        franchisee_ids = []
        store_ids = []
        company_ids = []
        refund_ids = []
        for r in refunds:
            refund_ids.append(r.id)
            if r.franchisee_id:
                franchisee_ids.append(r.franchisee_id)
            if r.received_by:
                store_ids.append(r.received_by)
            if r.trade_company:
                company_ids.append(r.trade_company)
        franchisee_map = get_branch_map(branch_ids=franchisee_ids, branch_type="FRANCHISEE", partner_id=partner_id,
                                        user_id=user_id, return_fields="id,code,name")
        store_map = get_branch_map(branch_ids=store_ids, branch_type="STORE", partner_id=partner_id,
                                   user_id=user_id, return_fields="id,code,name")
        # 查询贸易公司信息
        company_map = get_company_map(company_ids=company_ids, partner_id=partner_id, user_id=user_id)

        demand_list = []
        for r in refunds:
            row = r.serialize(conv=True)
            franchisee = franchisee_map.get(r.franchisee_id, {})
            store = store_map.get(r.received_by, {})
            company = company_map.get(r.trade_company, {})
            row['company_name'] = company.get('name')
            row['company_code'] = company.get('code')
            row['franchisee_name'] = franchisee.get('name')
            row['franchisee_code'] = franchisee.get('code')
            row['received_code'] = store.get('code')
            row['received_name'] = store.get('name')
            demand_list.append(row)
        res["rows"] = demand_list
        return res

    def get_refund_by_id(self, request, partner_id, user_id):
        refund_id = request.refund_id
        refund = sFR.get_refund_by_id(partner_id=partner_id, refund_id=refund_id)
        if not refund:
            raise OrderNotExistException("退款单不存在")
        result = refund.serialize(conv=True)
        refund_products = sFRP.list_refund_product(partner_id=partner_id, refund_id=refund_id)
        if refund_products:
            category_ids = []
            product_ids = []
            for p in refund_products:
                if p.category_id and p.category_id not in category_ids:
                    category_ids.append(p.category_id)
                if p.product_id and p.product_id not in product_ids:
                    product_ids.append(p.product_id)
            product_map = get_product_map(product_ids=product_ids,
                                          return_fields="id,code,name,model_name",
                                          partner_id=partner_id, user_id=user_id)
            category_map = get_category_map(category_ids, partner_id, user_id)
            result['products'] = []
            for p in refund_products:
                category = category_map.get(p.category_id, {})
                product = product_map.get(str(p.product_id), {})
                row = p.serialize(conv=True)
                row['category_name'] = category.get('name')
                row['product_code'] = product.get('code')
                row['product_name'] = product.get('name')
                row['product_spec'] = product.get('model_name')
                result['products'].append(row)
        franchisee_map = {}
        store_map = {}
        company_map = {}

        if refund.received_by:
            store_map = get_branch_map(branch_ids=[refund.received_by], branch_type="STORE", partner_id=partner_id,
                                       user_id=user_id, return_fields="id,code,name")
        if refund.franchisee_id:
            franchisee_map = get_branch_map(branch_ids=[refund.franchisee_id], branch_type="FRANCHISEE",
                                            partner_id=partner_id, user_id=user_id, return_fields="id,code,name")
        if refund.trade_company:
            company_map = get_company_map(company_ids=[refund.trade_company], partner_id=partner_id, user_id=user_id)

        franchisee = franchisee_map.get(refund.franchisee_id, {})
        store = store_map.get(refund.received_by, {})
        company = company_map.get(refund.trade_company, {})
        result['franchisee_name'] = franchisee.get('name')
        result['franchisee_code'] = franchisee.get('code')
        result['received_code'] = store.get('code')
        result['received_name'] = store.get('name')
        result['company_name'] = company.get('name')
        result['company_code'] = company.get('code')
        return result

    def deal_refund_by_ids(self, request, allow_status: list, partner_id=None, user_id=None):
        """变更退款单状态, 支持批量修改"""
        ret = {}
        action = request.action
        refund_ids = [int(_id) for _id in request.refund_ids] if request.refund_ids else []
        if len(refund_ids) == 0:
            raise DataValidationException("订单ID不可为空！")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        update_data = []
        refund_logs = []
        demand_detail = []
        demand_logs = []
        update_logs = []
        if len(refund_ids) > 1:
            # 批量支持·审核通过·和·驳回·
            if action not in ['REJECTED', 'APPROVED']:
                raise StatusUnavailable("批量操作仅支持审核和驳回")
        for refund_id in refund_ids:
            refund_db = sFR.get_refund_by_id(partner_id=partner_id, refund_id=refund_id)
            if not refund_db:
                raise OrderNotExistException("退款单不存在-{}".format(refund_id))
            if action == "REJECTED" and refund_db.type != "ORDER_REFUND":
                raise StatusUnavailable("只有仅退款退款单可以驳回操作！")
            row = dict(
                id=refund_id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                status=action
            )
            if request.remark:
                row['remark'] = request.remark
            if request.reject_reason:
                row['reject_reason'] = request.reject_reason
            if request.payment_way:
                row['payment_way'] = request.payment_way
            if action == "APPROVED":
                row['approved_date'] = datetime.now()
            update_data.append(row)
            _, log_db = SupplyFranchiseeRefundLog.get_franchisee_refund_log(refund_db.id, partner_id=partner_id)
            if log_db:
                update_logs.append({"id": log_db[0].id, "action": action})
            refund_logs.append(dict(
                id=get_guid(),
                partner_id=partner_id,
                refund_id=refund_id,
                action=action,
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow())
            )
            # 退款单审核/驳回后同步"仅退款"订货单状态为待确认
            if refund_db.type == RefundType.ORDER_REFUND.code:
                if action == "REJECTED":
                    sync_status = 'SUBMITTED'
                    demand_detail.append(dict(
                        id=refund_db.main_id,
                        partner_id=partner_id,
                        updated_by=user_id,
                        updated_name=username,
                        status=sync_status)
                    )
                    demand_logs.append(dict(
                        id=get_guid(),
                        partner_id=partner_id,
                        demand_id=refund_db.main_id,
                        action=sync_status,
                        created_by=user_id,
                        created_name=username,
                        created_at=datetime.utcnow()
                    ))
        res = sFR.update_franchisee_refund(update_data=update_data, refund_logs=refund_logs, allow_status=allow_status,
                                           demand_detail=demand_detail, demand_logs=demand_logs,
                                           refund_update_logs=update_logs)
        if res is True:
            ret["result"] = "success"
        else:
            ret['result'] = "failed"
        return ret

    def build_lack_refund_details(self, partner_id, user_id, demand_db: sFD, demand_products: list = None,
                                  username=None, batch_type=None):
        """构建缺货退款单数据结构：
        问：什么情况下生成缺货退款单：非总部分配单 + 付款方式为代金券 + 门店确认订单(且商品确认订货数量<商品订货数量)
        :param partner_id
        :param user_id
        :param demand_db 订货单查询对象
        :param demand_products 订货单商品明细(单独确认订单时未落库情况使用) [dict...]
        :param username 用户名
        :param batch_type 主单类型
        :returns refund_detail-`dict`, refund_products-`list`, refund_log-`dict`
        """
        if demand_db.type == Demand_type.FMD.code or demand_db.payment_way != PaymentWay.Voucher.code:
            return None, None, None
        refund_id = get_guid()
        refund_products = []
        refund_detail = dict(
            id=refund_id,
            partner_id=partner_id,
            type=RefundType.OOS_REFUND.code,
            batch_id=demand_db.id,
            main_id=demand_db.id,
            main_code=demand_db.code,
            main_type=batch_type,
            code=Supply_doc_code.get_code_by_type('FDR', partner_id, None),
            received_by=demand_db.received_by,
            franchisee_id=demand_db.franchisee_id,
            trade_company=self.get_trade_company_by_franchisee(demand_db.franchisee_id, partner_id, user_id),
            payment_way=self.get_refund_way(demand_db.payment_way),
            refund_date=datetime.utcnow(),
            status='INITED',
            process_status='INITED',
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            refund_amount=Decimal("0"),
            pay_amount=demand_db.pay_amount

        )
        refund_log = dict(
            id=get_guid(),
            partner_id=partner_id,
            refund_id=refund_id,
            action='INITED',
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow()
        )
        if not demand_products:
            demand_products = sFDP.list_demand_product(demand_id=demand_db.id, partner_id=partner_id)
        ids = get_uuids(len(demand_products))
        for inx, p in enumerate(demand_products):
            if not isinstance(p, dict):
                p = p.serialize(conv=True)
            if p.get('confirm_quantity', 0) < p.get('quantity', 0):
                tax_price = convert_to_decimal(p.get('tax_price', 0))
                tax_rate = convert_to_decimal(p.get('tax_rate', 0))
                quantity = convert_to_decimal(p.get('quantity', 0)) - convert_to_decimal(p.get('confirm_quantity', 0))
                cost_price = tax_price / convert_to_decimal(1 + tax_rate / 100)
                amount = round(quantity * tax_price, 2)     # 2023.1.9业务确认：四舍五入2位
                refund_detail['refund_amount'] += amount
                # 确认数量小于订货数量的部分商品生成缺货退款单
                refund_products.append(dict(
                    id=ids[inx],
                    refund_id=refund_id,
                    partner_id=partner_id,
                    product_id=p.get('product_id'),
                    product_code=p.get('product_code'),
                    product_name=p.get('product_name'),
                    category_id=p.get('category_id'),
                    unit_id=p.get('unit_id'),
                    unit_name=p.get('unit_name'),
                    unit_rate=p.get('unit_rate'),
                    unit_spec=p.get('unit_spec'),
                    quantity=quantity,
                    tax_price=tax_price,
                    cost_price=cost_price,
                    tax_rate=tax_rate,
                    amount=amount,
                    created_by=user_id,
                    created_name=username,
                    updated_by=user_id,
                    updated_name=username
                ))
            del p['quantity']
            del p['tax_rate']
            del p['tax_price']
        if len(refund_products) == 0:
            return None, None, None
        else:
            return refund_detail, refund_products, refund_log

    def generate_oss_refunds(self, demand_ids: list, partner_id: int, user_id: int):
        """处理"确认订单"自动生成缺货退款单
        1、检查订单是否生成过缺货退款单，一个订单只有一个缺货退款单
        2、查询待处理订单，检查订单状态、订单来源、付款方式是否符合生成缺货退款单条件
        3、查询订单商品明细构建缺货退款单明细
        4、批量创建退款单
        """
        refunds = sFR.query_refund_by_main_ids(partner_id=partner_id, main_ids=demand_ids)
        refund_demands = []
        for r in refunds:
            refund_demands.append(r[1])
        demand_ids = list(set(demand_ids) - set(refund_demands))
        demands = sFD.list_f_demand(ids=demand_ids, partner_id=partner_id)
        refund_details = []
        refund_products = []
        refund_logs = []
        for demand_db in demands:
            try:
                refund, refund_pros, refund_log = self.build_lack_refund_details(partner_id=partner_id,
                                                                                 user_id=1,
                                                                                 demand_db=demand_db,
                                                                                 username="系统机器人",
                                                                                 batch_type="FRS_DEMAND")
                if refund:
                    refund_details.append(refund)
                    refund_products.extend(refund_pros)
                    refund_logs.append(refund_log)
            except Exception as e:
                logger.error("build_lack_refund_details Error: {}".format(e))
                continue
        res = sFR.insert_franchisee_refund(refund_details=refund_details, refund_products=refund_products,
                                           refund_logs=refund_logs)
        return res


franchisee_refund_service = FranchiseeRefundsService()
