import json
import logging
from collections import deque, namedtuple
from decimal import Decimal

from datetime import datetime
from typing import Dict

from google.protobuf.timestamp_pb2 import Timestamp

from supply.client.inventory_service import inventory_service
from supply.driver.mysql import session
from supply.model.supply_partner_action import SupplyPartnerAction
from supply.utils import pb2dict
from supply.utils.enums import PaymentWay, Demand_type, Demand_sub_type, Platform, PartnerActionModule
from supply.client.metadata_service import metadata_service
from supply.client.products_manage_service import products_manage_service
from supply.model.supply_doc_code import Supply_doc_code
from supply.model.franchisee.franchisee_demand import DemandAction as Action
from supply.module import BaseToolsModule
from supply.error.exception import DataValidationException, NoRequestIDError, OrderNotExistException, StatusUnavailable
from supply.error.demand import DemandError, ProductError
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand as sFD, SupplyFranchiseeDemandProduct as sFDP
from supply.proto.frs_management.franchise_hd_assignment_pb2 import StoreValidProduct
from supply.utils.helper import convert_to_int, get_branch_map, get_product_map, get_category_map, get_unit_map, \
    get_guid, get_uuids, convert_to_decimal, check_quantity, datetime_2_timestamp
from supply.utils.auth import branch_scope_check, branch_list_scope_check
import logging

class FranchiseeHdAssignmentService(BaseToolsModule):

    def __init__(self):
        super(FranchiseeHdAssignmentService, self).__init__()
        # 加盟商总部分配模块展示的所有状态
        self.show_status = ["INITED", "PREPARE", "INVALID", "P_SUBMIT", "SUBMITTED", "R_APPROVE", "CONFIRMED",
                            "REFUNDING", "REFUNDED", "APPROVING", "APPROVED", "REJECTED", "CANCELLED"]
        self.convert_hd_status = ["P_SUBMIT", "SUBMITTED", "R_APPROVE", "CONFIRMED", "REFUNDING", "REFUNDED",
                                  "APPROVING", "APPROVED", "REJECTED", "CANCELLED"]
        self.platform = Platform.HEX_WEB.code

    def create_hd_demand(self, partner_id, user_id, request):
        demand_date = self.timestamp2datetime(request.demand_date)
        arrival_date = self.timestamp2datetime(request.arrival_date)
        if demand_date.year == 1970 or arrival_date.year == 1970:
            raise DataValidationException("请填写订货日期或者到货日期")
        sub_type = request.sub_type
        batch_id = request.batch_id
        if batch_id:
            if_exist = sFD.get_demand_by_id(partner_id=partner_id, request_id=batch_id)
            if if_exist:
                raise DemandError("请勿重复提交！- {}".format(batch_id))
        else:
            raise NoRequestIDError("缺少唯一请求号，不允许创建！")
        received_bys = []
        product_ids = []
        items = []
        if sub_type == Demand_sub_type.PRODUCT.code:
            by_product_params = request.by_product_params
            if not by_product_params:
                raise ProductError("新建总部分配须包含门店商品")
            products = by_product_params.products
            received_by = by_product_params.received_by
            if not products:
                raise ProductError("新建总部分配须包含商品")
            for p in products:
                product_ids.append(convert_to_int(p.product_id))
                product_ids += [int(i.product_id) for i in p.relation_products if i.product_id]
            if received_by:
                received_bys.append(received_by)
                items.append(dict(received_by=received_by, products=products))
            else:
                # 为了迎合捆绑商品，这样的改动是最方便的，这种情况为多门店单商品
                received_bys += [i.store_id for i in products]
                items += [dict(received_by=i.store_id, products=[i]) for i in products]
            if not received_bys or not items:
                raise ProductError("新建总部分配须包含门店或商品")
        elif sub_type == Demand_sub_type.STORE.code:
            # 弃用，都走上面的判断逻辑了
            by_store_params = request.by_store_params
            if not by_store_params:
                raise ProductError("新建总部分配须包含门店商品")
            if not by_store_params.stores:
                raise DataValidationException("新建总部分配须包含门店信息")
            product_id = convert_to_int(by_store_params.product_id)
            unit_id = convert_to_int(by_store_params.unit_id)
            if not all([product_id, unit_id]):
                raise ProductError("缺失商品信息！")
            product_ids.append(product_id)
            for store in by_store_params.stores:
                store_id = convert_to_int(store.store_id)
                received_bys.append(store_id)
                store = pb2dict(store)
                if 'store_id' in store:
                    del store["store_id"]
                if 'store_code' in store:
                    del store["store_code"]
                if 'store_name' in store:
                    del store["store_name"]
                if 'geo_region' in store:
                    del store["geo_region"]
                store["product_id"] = product_id
                store["unit_id"] = unit_id
                store["quantity"] = by_store_params.quantity
                pro = StoreValidProduct(**store)
                items.append(dict(
                    received_by=convert_to_int(store_id),
                    products=[pro]
                ))
        else:
            raise DataValidationException("主配类型有误-{}".format(sub_type))
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 门店
        branch_map = get_branch_map(branch_ids=received_bys, branch_type="STORE", partner_id=partner_id,
                                    user_id=user_id,
                                    return_fields="id,code,name,biz_type,chain_type,franchisee")
        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type",
                                      partner_id=partner_id, user_id=user_id)
        category_ids = []
        for _, p in product_map.items():
            if p.get('category') and int(p.get('category')) not in category_ids:
                category_ids.append(int(p.get('category')))
        # 商品类别
        category_map = get_category_map(category_ids=category_ids, partner_id=partner_id, user_id=user_id)
        # 单位
        unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
        demands = []
        demand_products = []
        demand_relation_products = []
        demand_logs = []
        for item in items:
            received_by = item.get('received_by')
            store_info = branch_map.get(item.get('received_by'), {})
            if not store_info.get('franchisee'):
                raise DataValidationException(f"门店：{store_info.get('name', '')} 未配置加盟商，无法订货！")
            franchisee_id = convert_to_int(store_info.get('franchisee'))
            supply_demand = dict(
                id=get_guid(),
                partner_id=partner_id,
                code=Supply_doc_code.get_code_by_type('FD', partner_id, None),
                received_by=received_by,
                received_name=store_info.get('name'),
                received_code=store_info.get('code'),
                store_type=store_info.get('biz_type'),
                chain_type=store_info.get('chain_type'),
                franchisee_id=franchisee_id,
                demand_date=demand_date,
                arrival_date=arrival_date,
                status='INITED',
                process_status='INITED',
                type=request.type if request.type else 'FMD',
                bus_type=request.bus_type if request.bus_type else 'HD_ASSIGN',
                sub_type=sub_type,
                order_type_id=request.order_type_id,
                created_by=user_id,
                updated_by=user_id,
                created_name=username,
                updated_name=username,
                batch_id=batch_id,
                remark=request.remark,
                sum_price_tax=Decimal(str(0)),
                sum_tax=Decimal(str(0)),
                sales_amount=Decimal(0),
            )
            demands.append(supply_demand)
            # products_len = len(product_ids)

            products = item.get('products', [])
            if not products:
                continue

            product_len = len(products)
            ids = get_uuids(product_len)

            inx, ratio = 0, 0
            products_deque = deque([{'id': ids[inx], 'type': 'main', 'p': products[inx], 'father_id': None,
                                     'relation_type': ''}])
            while products_deque:
                detail = products_deque.pop()
                p = detail['p']
                is_main = detail['type'] == 'main'
                product_id = convert_to_int(p.product_id)
                product = product_map.get(str(product_id), {})
                if p.relation_products:
                    b_ids = get_uuids(len(p.relation_products))
                    products_deque.extend({'id': b_ids[i], 'type': 'bind', 'p': v, 'father_id': detail['id'],
                                           'relation_type': ''} for i, v in enumerate(p.relation_products[::-1]))
                    detail['relation_type'] = 'BIND'

                units = product.get('units')
                unit_rate = 1
                unit_id = None
                accounting_unit_id = None
                if is_main and not p.quantity:
                    raise ProductError("商品数量不能为空！")
                if units:
                    order_flag = False
                    for u in units:
                        if u.get("order"):
                            unit_rate = float(u.get("rate", 1))
                            unit_id = int(u.get('id'))
                            order_flag = True
                        if u.get('default'):
                            accounting_unit_id = int(u.get('id'))
                    if not order_flag:
                        raise ProductError("此商品未设置订货单位，请检查主档设置, product_id:{}".format(product_id))
                else:
                    raise ProductError("此商品未设置单位，请检查主档设置, product_id:{}".format(product_id))
                unit = unit_map.get(unit_id)
                if not unit:
                    raise ProductError("主档未找到订货单位信息, unit_id:{}".format(unit_id))

                if not is_main:
                    ratio = Decimal(p.ratio) if p.ratio else p.ratio
                    if not ratio or ratio < 0:
                        raise ProductError(f"捆绑商品id: {product_id}比率错误")

                accounting_unit = unit_map.get(accounting_unit_id, {})
                quantity = convert_to_decimal(p.quantity) if p.quantity else convert_to_decimal(0)
                tax_price = convert_to_decimal(p.tax_price) if p.tax_price else convert_to_decimal(0)
                sales_price = convert_to_decimal(p.sales_price) if p.sales_price else convert_to_decimal(0)
                tax_rate = convert_to_decimal(p.tax_rate) if p.tax_rate else convert_to_decimal(0)
                cost_price = tax_price / convert_to_decimal(1 + tax_rate / 100)
                tax_amount = (tax_price - cost_price) * quantity
                amount = round(quantity * tax_price, 2)
                supply_demand["sum_price_tax"] += amount
                supply_demand["sum_tax"] += tax_amount
                sales_amount = round(quantity * sales_price, 2)
                supply_demand["sales_amount"] += sales_amount
                product_dict = {
                    "id": detail['id'],
                    "partner_id": partner_id,
                    "demand_id": supply_demand["id"],
                    "status": "INITED",
                    "product_id": int(product_id),
                    "product_code": product.get("code"),
                    "product_name": product.get("name"),
                    "category_id": int(product.get('category', 0)),
                    "category_name": category_map.get(int(product.get('category', 0)), {}).get('name', ''),
                    # "arrival_days": p.arrival_days,
                    "unit_id": int(p.unit_id),
                    "unit_spec": unit.get("code", "无"),
                    "unit_name": unit.get('name', "无"),
                    "unit_rate": unit_rate,
                    'accounting_unit_id': accounting_unit_id,
                    'accounting_unit_name': accounting_unit.get('name'),
                    'accounting_unit_spec': accounting_unit.get("code"),
                    "quantity": quantity,
                    "accounting_quantity": convert_to_decimal(unit_rate) * quantity,
                    "storage_type": product.get('storage_type'),
                    "min_quantity": p.min_quantity,
                    "max_quantity": p.max_quantity,
                    "increment_quantity": p.increment_quantity,
                    "tax_price": tax_price,
                    "sales_price": sales_price,
                    "cost_price": cost_price,
                    "tax_rate": tax_rate,
                    "amount": amount,
                    "sales_amount": sales_amount,
                    "distribute_by": p.distribute_by,
                    "distribution_type": p.distribution_type if p.distribution_type else Demand_type.NMD.code,  # 暂定配送
                    "created_by": user_id,
                    "updated_by": user_id,
                    "created_name": username,
                    "updated_name": username,
                    "extends": p.extends,
                    "approve_quantity": quantity,
                    "confirm_quantity": quantity,
                    "approve_amount": amount,
                    "confirm_amount": amount,
                    "approve_sales_amount": sales_amount,
                    "confirm_sales_amount": sales_amount,
                    'relation_type': detail['relation_type']
                }
                if p.arrival_days and p.arrival_days.isdigit():
                    product_dict["arrival_days"] = int(p.arrival_days)
                else:
                    product_dict["arrival_days"] = None
                
                # print('dabhfjadds', detail)
                product_dict.update({'demand_product_id': detail['father_id'],
                                     'ratio': ratio,
                                     'configure': p.configure
                                     }) if not is_main else ...

                demand_products.append(product_dict) if is_main else demand_relation_products.append(product_dict)
                inx += is_main

                products_deque.append({'id': ids[inx], 'type': 'main',
                                       'p': products[inx], 'father_id': None,
                                       'relation_type': ''}) if is_main and inx < product_len else ...

            demand_logs.append(dict(
                partner_id=partner_id,
                demand_id=supply_demand["id"],
                action=Action.INITED.code,
                action_name=Action.INITED.desc,
                platform=self.platform,
                end_status=supply_demand['status'],
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
                trace_id=batch_id
            ))

        sFD.create_f_demand(demands, demand_products, demand_logs, demand_relation_products)
        return {"result": True}

    def list_hd_demand(self, partner_id, user_id, request):
        res = dict()
        start_date = self.utcTimestamp2datetime(request.start_date)
        end_date = self.utcTimestamp2datetime(request.end_date)
        status = list(request.status) if request.status else []
        received_bys = [convert_to_int(_id) for _id in request.received_bys] if request.received_bys else []
        types = [_type for _type in request.types] if request.types else []
        code = request.code
        chain_type = request.chain_type
        bus_types = [bus_type for bus_type in request.bus_types] if request.bus_types else []
        franchisee_ids = [convert_to_int(_id) for _id in request.franchisee_ids] if request.franchisee_ids else []
        payment_ways = [str(way) for way in request.payment_ways] if request.payment_ways else []
        order_type_ids = [convert_to_int(_id) for _id in request.order_type_ids] if request.order_type_ids else []
        sort = request.sort if request.sort else 'updated_at'
        order = request.order
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        ids = list(request.ids) if request.ids else []
        product_ids = list(request.product_ids) if request.product_ids else []
        if len(ids) == 0:
            ids = None
        # 总部分配状态查询兼容，
        if "PREPARE" in status:
            status.extend(self.convert_hd_status)
        if not status:
            status = self.show_status
        query_set = sFD.list_f_demand(partner_id=partner_id, start_date=start_date, received_bys=received_bys,
                                      end_date=end_date, status=status, code=code, offset=offset, limit=limit,
                                      franchisee_ids=franchisee_ids, order_type_ids=order_type_ids, ids=ids,
                                      payment_ways=payment_ways, chain_type=chain_type, bus_types=bus_types, sort=sort,
                                      types=types, order=order, product_ids=product_ids, include_total=include_total)
        if isinstance(query_set, tuple):
            total, demands = query_set
            res['total'] = total
        else:
            demands = query_set
        franchisee_ids = []
        for demand in demands:
            if demand.franchisee_id:
                franchisee_ids.append(demand.franchisee_id)
        branch_map = get_branch_map(branch_ids=franchisee_ids, branch_type="FRANCHISEE", partner_id=partner_id,
                                    user_id=user_id, return_fields="id,code,name")
        demand_list = []
        for demand in demands:
            row = demand.serialize(conv=True)
            franchisee = branch_map.get(demand.franchisee_id, {})
            if demand.status in self.convert_hd_status:
                row['status'] = "PREPARE"
            row['franchisee_name'] = franchisee.get('name')
            row['franchisee_code'] = franchisee.get('code')
            row['attachments'] = eval(demand.attachments) if demand.attachments else []
            demand_list.append(row)
        res["rows"] = demand_list
        return res

    def get_valid_store_by_product(self, partner_id, user_id, request):
        """根据商品查询可主配门店及价格"""
        res = dict(rows=[], total=0)
        if not request.product_ids:
            raise DataValidationException("请传入商品ID")
        product_ids = [int(pid) for pid in request.product_ids]
        valid_products = products_manage_service.GetAgentProducts(product_ids=product_ids,
                                                                  order_type_id=request.order_type_id,
                                                                  partner_id=partner_id,
                                                                  user_id=user_id).get('rows')
        if valid_products and isinstance(valid_products, dict):
            products = valid_products.get('list', [])
            valid_product = dict()
            products = sorted(products, key=lambda p: p.__getitem__('id'))
            for product in products:
                product_id = convert_to_int(product.get('id'))
                distribution_rule = product.get('distribution_rule', {})
                order_rule = product.get('order_rule', {})
                product_price = product.get('product_price', [])
                sales_price = 0
                for price in product_price:
                    if price.get('price_type_id') == "2":
                        sales_price = convert_to_decimal(price.get('tax_price', 0))
                store = dict(
                    store_id=convert_to_int(product.get('store_id')),
                    store_code=product.get('store_code'),
                    store_name=product.get('store_name'),
                    tax_price=convert_to_decimal(product.get('tax_price', 0)),
                    tax_rate=convert_to_decimal(product.get('tax_ratio', 0)),
                    sales_price=sales_price,
                    arrival_days=str(distribution_rule.get('planned_arrival_days', 0)),
                    increment_quantity=order_rule.get('increase_qty'),
                    max_quantity=order_rule.get('max_qty'),
                    min_quantity=order_rule.get('min_qty'),
                    extends=json.dumps(dict(product_price=product_price))
                )
                if valid_product.get('product_id') == product_id:
                    valid_product["stores"].append(store)
                else:
                    valid_product = dict(
                        product_id=product_id,
                        product_code=product.get('code'),
                        product_name=product.get('name'),
                        unit_id=convert_to_int(product.get('unit_id')),
                        unit_name=product.get('unit_name'),
                        stores=[store]
                    )
                    res['rows'].append(valid_product)
        res['total'] = len(res['rows'])
        return res

    def get_valid_product_by_store(self, partner_id, user_id, request):
        res = dict(rows=[], total=0)
        if (not request.store_id and (not request.product_id or not request.store_ids)) or not request.order_type_id:
            raise DataValidationException("请传入门店ID和订货类型ID或商品id")
        order_by_inventory = request.order_by == 'real_inventory' and not request.store_ids and request.store_id
        final_product_ids = []
        row_dict={}

        # if request.product_id and not request.store_ids:
        #     raise DataValidationException("不支持多商品与多门店的筛选")
        product_ids = [request.product_id] if request.product_id else None
        valid_products_resp = products_manage_service.GetAgentProducts(store_id=request.store_id,
                                                                  order_type_id=request.order_type_id,
                                                                  partner_id=partner_id,
                                                                  user_id=user_id,
                                                                  product_ids=product_ids,
                                                                  store_ids=request.store_ids
                                                                  )
        valid_products= valid_products_resp.get('rows')
        if valid_products and isinstance(valid_products, dict):
            res['total'] = convert_to_int(valid_products_resp.get('total', 0))
            products = valid_products.get('list', [])
            for product in products:
                # print(product.keys())
                order_rule = product.get('order_rule', {})
                distribution_rule = product.get('distribution_rule', {})
                product_price = product.get('product_price', [])
                sales_price = 0
                for price in product_price:
                    if price.get('price_type_id') == "2":
                        sales_price = convert_to_decimal(price.get('tax_price', 0))
                row=dict(
                    arrival_days=str(distribution_rule.get('planned_arrival_days', 0)),
                    distribute_by=None,  # todo: 等待商品中心返回接入
                    distribution_type=None,
                    increment_quantity=order_rule.get('increase_qty'),
                    max_quantity=order_rule.get('max_qty'),
                    min_quantity=order_rule.get('min_qty'),
                    unit_id=convert_to_int(product.get('unit_id')),
                    unit_name=product.get('unit_name'),
                    unit_spec=None,
                    tax_price=convert_to_decimal(product.get('tax_price', 0)),
                    tax_rate=convert_to_decimal(product.get('tax_ratio', 0)),
                    sales_price=sales_price,
                    extends=json.dumps(dict(product_price=product_price)),
                    product_id=convert_to_int(product.get('id')),
                    product_code=product.get('code'),
                    product_name=product.get('name'),
                    category_id=convert_to_int(product.get('product_category_id')),
                    category_name=product.get('product_category_name'),
                    store_id=int(product.get('store_id', 0)),
                    relation_products=[self.__get_valid_relation_products(i)
                                       for i in product.get('truss_activity_product', '')]
                )
                res['rows'].append(row)
                final_product_ids.append(int(product.get('id', 0)))
                row_dict[int(product.get('id', 0))]=row
            final_rows= res['rows']
            if order_by_inventory and final_product_ids:
                final_rows=[]
                # 新增按照实时库存排序
                inv_unchanged_products, inv_changed_products = [], []
                order_result = inventory_service.query_branch_inventory_sort(partner_id, user_id, request.store_id,
                                                                             product_ids=final_product_ids,limit=request.limit,offset=request.offset).get('rows')
                # logging.info(f'总部分配库存排序：{order_result}')
                if order_result:
                    for i in order_result:
                        tmp_row = row_dict.get(int(i.get('product_id')))
                        if tmp_row:
                            tmp_row['real_inventory_qty'] = i.get('qty')
                            final_rows.append(tmp_row)
            res['rows'] = final_rows

        return res

    def __get_valid_relation_products(self, p):
        product = p.get('product', {})
        r = {'product_name': p.get('product_name'),
             'product_code': p.get('product_code'),
             'product_id': int(p.get('product_id', 0)),
             'ratio': p.get('ratio'),
             'configure': int(p.get('configure', 0))
             }

        if product:
            sales_price = 0
            product_price = product.get('product_price', [])
            for i in product_price:
                if i.get('price_type_id') == "2":
                    sales_price = convert_to_decimal(i.get('tax_price', 0))
                    continue
            r.update({'sales_price': Decimal(sales_price),
                      'tax_price': Decimal(product.get('tax_price', 0)),
                      'tax_rate': Decimal(product.get('tax_ratio', 0)),
                      'unit_id': int(product.get('unit_id', 0)),
                      'unit_name': product.get('unit_name'),
                      'extends': json.dumps({'product_price': product_price}),
                    })
        return r

    def update_hd_demand_product(self, partner_id, user_id, request):
        """更新总部分配订货单商品"""
        result = dict()
        demand_id = request.demand_id
        demand = sFD.get_demand_by_id(partner_id=partner_id, demand_id=demand_id)
        if not demand:
            raise OrderNotExistException("订货单不存在")
        products = request.products
        if not products:
            raise ProductError("更新详情必须包含商品！")

        product_ids = []
        p_ids_len, check_product_ids = 0, {}
        for p in products:
            if p.product_id:
                p_ids_len += 1
                product_ids.append(convert_to_int(p.product_id))
                r_product_ids = [i.product_id for i in p.relation_products if i.product_id]
                check_product_ids[convert_to_int(p.product_id)] = set(r_product_ids)
                product_ids += r_product_ids

        # 调用价格中心接口校验商品价格
        valid_product_ids = []
        agent_products = products_manage_service.GetAgentProducts(store_id=demand.received_by,
                                                                  order_type_id=demand.order_type_id,
                                                                  product_ids=check_product_ids,
                                                                  partner_id=partner_id,
                                                                  user_id=user_id).get('rows')
        valid_product_relations = {}
        if agent_products and isinstance(agent_products, dict):
            valid_products = agent_products.get('list', [])
            result["product_names"] = []
            for p in valid_products:
                p_id = convert_to_int(p.get('id'))
                valid_product_ids.append(p_id)
                valid_product_relations[p_id] = {int(i['product_id'])
                                                 for i in p.get('truss_activity_product', []) if 'product_id' in i}

        invalid_product_ids = list(check_product_ids.keys() - set(valid_product_ids))
        if len(invalid_product_ids) > 0:
            product_map = get_product_map(product_ids=invalid_product_ids,
                                          return_fields="id,code,name",
                                          partner_id=partner_id, user_id=user_id)
            result["product_names"] = []
            result["description"] = "请检查商品价格配置！"
            for _, p in product_map.items():
                result["product_names"].append(p.get('name'))
            return result

        # 为了方便，这段代码就这么写了
        invalid_product_ids = []
        for k, v in check_product_ids.items():
            if v - valid_product_relations.get(k, set()):
                invalid_product_ids.append(k)

        if invalid_product_ids:
            product_map = get_product_map(product_ids=invalid_product_ids,
                                          return_fields="id,code,name",
                                          partner_id=partner_id, user_id=user_id)
            result["product_names"] = []
            result["description"] = "请检查商品价格配置！"
            for _, p in product_map.items():
                result["product_names"].append(p.get('name'))
            return result

        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type",
                                      partner_id=partner_id, user_id=user_id)
        category_ids = []
        for _, p in product_map.items():
            if p.get('category') and int(p.get('category')) not in category_ids:
                category_ids.append(int(p.get('category')))
        # 商品类别
        category_map = get_category_map(category_ids=category_ids, partner_id=partner_id, user_id=user_id)
        # 单位
        unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        demand_products = []
        demand_relation_products = []
        update_demand = dict(
            id=demand_id,
            partner_id=partner_id,
            remark=request.remark,
            updated_by=user_id,
            updated_name=username,
            sum_price_tax=Decimal(str(0)),
            sum_tax=Decimal(str(0)),
            sales_amount=Decimal(str(0))
        )
        products_len = len(product_ids)
        ids = get_uuids(products_len)

        inx, ratio = 0, 0
        products_deque = deque([{'id': ids[inx], 'type': 'main', 'p': products[inx], 'father_id': None,
                                 'relation_type': ''}])
        while products_deque:
            detail = products_deque.pop()
            p = detail['p']
            is_main = detail['type'] == 'main'

            product_id = convert_to_int(p.product_id)
            product = product_map.get(str(product_id), {})
            units = product.get('units')
            unit_rate = 1
            unit_id = None
            accounting_unit_id = None
            if p.relation_products:
                b_ids = get_uuids(len(p.relation_products))
                products_deque.extend({'id': b_ids[i], 'type': 'bind', 'p': v, 'father_id': detail['id'],
                                       'relation_type': 'BIND'} for i, v in enumerate(p.relation_products[::-1]))
                detail['relation_type'] = 'BIND'

            if is_main and not p.quantity:
                raise ProductError("商品数量不能为空！")
            if units:
                order_flag = False
                for u in units:
                    if u.get("order"):
                        unit_rate = float(u.get("rate", 1))
                        unit_id = int(u.get('id'))
                        order_flag = True
                    if u.get('default'):
                        accounting_unit_id = int(u.get('id'))
                if not order_flag:
                    raise ProductError("此商品未设置订货单位，请检查主档设置, product_id:{}".format(product_id))
            else:
                raise ProductError("此商品未设置单位，请检查主档设置, product_id:{}".format(product_id))

            if not is_main:
                ratio = Decimal(p.ratio) if p.ratio else p.ratio
                if not ratio or ratio < 0:
                    raise ProductError(f"捆绑商品id: {product_id}比率错误")

            unit = unit_map.get(unit_id, {})
            accounting_unit = unit_map.get(accounting_unit_id, {})
            quantity = convert_to_decimal(p.quantity) if p.quantity else convert_to_decimal(0)
            tax_price = convert_to_decimal(p.tax_price) if p.tax_price else convert_to_decimal(0)
            sales_price = convert_to_decimal(p.sales_price) if p.sales_price else convert_to_decimal(0)
            tax_rate = convert_to_decimal(p.tax_rate) if p.tax_rate else convert_to_decimal(0)
            cost_price = tax_price / convert_to_decimal(1 + tax_rate / 100)
            tax_amount = (tax_price - cost_price) * quantity
            amount = quantity * tax_price
            sales_amount = round(quantity * sales_price, 2)
            update_demand["sum_price_tax"] += amount
            update_demand["sum_tax"] += tax_amount
            update_demand["sales_amount"] += sales_amount
            product_dict = {
                "id": detail['id'],
                "partner_id": partner_id,
                "demand_id": demand_id,
                "status": "INITED",
                "product_id": int(product_id),
                "product_code": product.get("code"),
                "product_name": product.get("name"),
                "category_id": int(product.get('category', 0)),
                "category_name": category_map.get(int(product.get('category', 0)), {}).get('name', '未分类'),
                # "arrival_days": p.arrival_days,
                "unit_id": unit_id,
                "unit_spec": unit.get("code", "无"),
                "unit_name": unit.get('name', "无"),
                "unit_rate": unit_rate,
                'accounting_unit_id': accounting_unit_id,
                'accounting_unit_name': accounting_unit.get('name'),
                'accounting_unit_spec': accounting_unit.get("code"),
                "quantity": quantity,
                "accounting_quantity": convert_to_decimal(unit_rate) * quantity,
                "min_quantity": p.min_quantity,
                "max_quantity": p.max_quantity,
                "increment_quantity": p.increment_quantity,
                "tax_price": tax_price,
                "sales_price": sales_price,
                "cost_price": cost_price,
                "tax_rate": tax_rate,
                "amount": quantity * tax_price,
                "sales_amount": sales_amount,
                "storage_type": product.get('storage_type'),
                "created_by": user_id,
                "updated_by": user_id,
                "created_name": username,
                "updated_name": username,
                "approve_quantity": quantity,
                "confirm_quantity": quantity,
                "approve_amount": amount,
                "confirm_amount": amount,
                "approve_sales_amount": sales_amount,
                "confirm_sales_amount": sales_amount
            }
            if p.arrival_days and p.arrival_days.decimal():
                product_dict["arrival_days"] = int(p.arrival_days)
            else:
                product_dict["arrival_days"] = None
                # print('demand_products======',demand_products)

            product_dict.update({'demand_product_id': detail['father_id'],
                                 'ratio': ratio,
                                 'configure': p.configure
                                 } if not is_main else {'relation_type': detail['relation_type']})
            demand_products.append(product_dict) if is_main else demand_relation_products.append(product_dict)
            inx += is_main
            products_deque.append({'id': ids[inx], 'type': 'main',
                                   'p': products[inx], 'father_id': None,
                                   'relation_type': ''}) if is_main and inx < p_ids_len else ...
        # demand_log = dict(
        #     id=get_guid(),
        #     partner_id=partner_id,
        #     demand_id=demand_id,
        #     action="UPDATE",
        #     created_by=user_id,
        #     created_name=username,
        #     created_at=datetime.utcnow()
        # )
        res = sFD.update_f_demand(update_data=update_demand, allow_status=["INITED"],
                                  cover_products=demand_products, cover_relation_products=demand_relation_products)
        if res is True:
            result["demand_id"] = demand_id
            result["description"] = "更新成功"
        else:
            raise StatusUnavailable("当前状态不允许更新")
        return result

    def deal_hd_demand_by_ids(self, request, allow_status: list, partner_id=None, user_id=None):
        """变更总部分配单状态, 支持批量修改"""
        ret = {}
        action = Action.HD_SUBMIT.code if request.action == "PREPARE" else request.action
        demand_ids = [int(_id) for _id in request.demand_ids] if request.demand_ids else []
        if len(demand_ids) == 0:
            raise DataValidationException("订单ID不可为空！")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        demand_logs = []
        update_data = []
        if len(demand_ids) > 1:
            # 批量只支持·提交·和·作废·
            if action not in [Action.HD_SUBMIT.code, 'INVALID']:
                raise StatusUnavailable("批量操作仅支持提交和作废")
        print('action11', action)
        change_status = None
        if action == Action.HD_SUBMIT.code:
            # status = "R_APPROVE"  # 总部分配提交后对应门店订单"待确认" 20221206经业务确认
            status = SupplyPartnerAction.get_to_action(partner_id, PartnerActionModule.DEMAND.code,
                                                       Action.R_APPROVE.code)
            if status == Action.PREPARE.code:
                change_status = Action.CONFIRMED.code

            sync_approve = True
        else:
            status = action
            sync_approve = False
        trace_id = get_guid()
        # TODO: 主配单PC端商品数量校验
        # check_map = {}

        demand_ids_map = {i.id: i for i in
                          session.query(sFD).filter(sFD.partner_id == partner_id, sFD.id.in_(demand_ids))
                          if i.status == Action.INITED.code}

        if not demand_ids_map:
            if request.is_detailed:
                raise OrderNotExistException("订单不存在-{}".format(demand_ids[0]))

            return ret

        demand_products_dict, product_ids = {}, set()
        if action == Action.HD_SUBMIT.code:
            demand_products = session.query(sFDP).filter(sFDP.demand_id.in_(demand_ids),
                                                         sFDP.partner_id == partner_id).all()
            for p in demand_products:
                product_ids.add(p.product_id)
                if p.demand_id in demand_products_dict:
                    demand_products_dict[p.demand_id][p.product_id] = p
                    continue

                demand_products_dict[p.demand_id] = {p.product_id: p}
            # print(demand_products_dict)

            # demand_dbs = sFD.list_f_demand(ids=demand_ids)
            # d_map = {d.id: d.serialize(conv=True) for d in demand_dbs}
            # f_ids = {d.id: d.franchisee_id for d in demand_dbs}
            # f_map = {}
            # if f_ids:
            #     f_map = get_branch_map(branch_type="FRANCHISEE", partner_id=partner_id, user_id=user_id)
            # product_ids = {dp.product_id for dp in demand_products}
            # product_map = get_product_map(product_ids=product_ids,
            #                               return_fields="id,code,name,category,storage_type,model_name",
            #                               partner_id=partner_id, user_id=user_id)
            # for dp in demand_products:
            #     if dp.demand_id in check_map:
            #         continue
            #     if all((dp.max_quantity, dp.min_quantity, dp.increment_quantity)) and dp.quantity !=0:
            #         check = check_quantity(dp.quantity, dp.max_quantity, dp.min_quantity, dp.increment_quantity)
            #         if check:
            #             d = d_map.get(dp.demand_id, {})
            #             if request.is_detailed:
            #                 check_map[dp.demand_id] = {"product_name": dp.product_name,
            #                "product_code": dp.product_code,
            #                "spec": product_map.get(str(dp.product_id), {}).get("model_name", ""),
            #                "quantity": dp.approve_quantity,
            #                "min_quantity": dp.min_quantity, "incr_quantity": dp.increment_quantity,
            #                "max_quantity": dp.max_quantity, "unit": dp.unit_name,
            #                "tax_price": dp.tax_price, "amount": round(
            #                 convert_to_decimal(dp.tax_price) * convert_to_decimal(
            #                 dp.approve_quantity), 2), "storage_type": dp.storage_type}
            #             else:
            #                 check_map[dp.demand_id] = {
            #                     "status": d.get("status"),
            #                     "store_name": d.get("received_name"),
            #                     "code": d.get("code"),
            #                     "order_type_id": d.get("order_type_id"),
            #                     "franchisee_name": f_map.get(d.get("franchisee_id"), ""),
            #                     "amount": d.get("sum_price_tax"),
            #                     "demand_date": d.get("demand_date"),
            #                     "arrival_date": d.get("arrival_date")
            #                 }
            # if check_map:
            #     if request.is_detailed:
            #         return {"pmsgs": check_map.values()}
            #     else:
            #         return {"msgs": check_map.values()}
        free_products_data = products_manage_service.query_free_order_limit(
            product_ids, partner_id, user_id).get('rows', []) if action == Action.HD_SUBMIT.code else []

        franchisee_ids, n_product_ids, return_products = set(), set(), {}
        go_on_f = lambda x: False if not request.is_detailed else x
        key = namedtuple('key', ['demand_id', 'product_id'])
        for demand_id, demand_db in demand_ids_map.items():

            dp_ids = None
            if action == Action.HD_SUBMIT.code:
                products: Dict[int: sFDP] = demand_products_dict.get(demand_db.id)
                dp_ids = set(products)
                if products:
                    go_on_for_loop = True

                    valid_products = products_manage_service.get_order_rule_by_store(store_id=demand_db.received_by,
                                                                                     product_ids=product_ids,
                                                                                     partner_id=partner_id,
                                                                                     user_id=user_id).get('rows', {})

                    # print(valid_products)
                    valid_products = {int(vp['product_id']): vp['order_rule'] for vp in valid_products
                                      if 'order_rule' in vp and 'product_id' in vp}

                    free_products = {}
                    for f in free_products_data:
                        fp_id = int(f['product_id'])
                        if fp_id not in products:
                            continue

                        if 'limit_qty' not in f:
                            continue

                        unit_rate = products[fp_id].unit_rate
                        if not unit_rate:
                            continue

                        free_products[fp_id] = Decimal(f['limit_qty']) / unit_rate

                    # print(valid_products)
                    for k, p in products.items():
                        if not request.is_detailed and not go_on_for_loop:
                            break

                        vp = valid_products.get(p.product_id, {})
                        if not vp and p.tax_price:
                            dp_ids.remove(p.product_id)
                            continue

                        max_qty = -Decimal(vp.get('max_qty', 0))
                        if not p.tax_price:
                            if p.product_id not in free_products and not vp:
                                dp_ids.remove(p.product_id)
                                continue

                            free_max_qty = -Decimal(free_products.get(p.product_id, 0))
                            max_qty = max(max_qty, free_max_qty) if \
                                vp.get('max_qty') and p.product_id in free_products else min(max_qty, free_max_qty)

                        max_qty = -max_qty
                        check = check_quantity(p.quantity, vp.get('min_qty'), vp.get('increase_qty'), max_qty) if vp \
                            else p.quantity <= max_qty
                        if check:
                            dp_ids.remove(p.product_id)
                        else:
                            if request.is_detailed:
                                p.min_quantity = float(vp.get('min_qty', 0))
                                p.max_quantity = float(max_qty)
                                p.increment_quantity = float(vp.get('increase_qty', 0))
                                return_products[key(demand_db.id, p.product_id)] = p
                            else:
                                return_products[key(demand_db.id, p.product_id)] = demand_db

                            go_on_for_loop = go_on_f(go_on_for_loop)

                    n_product_ids |= dp_ids
                    franchisee_ids.add(demand_db.franchisee_id) if dp_ids else ...

            if dp_ids:
                continue

            demand = dict(
                id=demand_id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                remark=request.remark,
                status=(change_status or status) if not demand_db.sum_price_tax else status,
                pay_amount=demand_db.sum_price_tax,
                payment_way=PaymentWay.CreditPay.code  # 总部分配默认信用付支付  20221207确认
            )
            if request.attachments:
                demand["attachments"] = str(request.attachments)
            update_data.append(demand)
            demand_logs.append(dict(
                partner_id=partner_id,
                demand_id=demand_id,
                action=action,
                action_name=Action.get_desc(action),
                platform=self.platform,
                start_status=demand_db.status,
                end_status=demand['status'],
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
                trace_id=trace_id
            ))

        if update_data:
            ret['result'] = sFD.update_f_demand(update_data=update_data, demand_logs=demand_logs,
                                                allow_status=allow_status,
                                                sync_approve=sync_approve, demand_ids=demand_ids)

        if n_product_ids:
            franchisee_map = get_branch_map(branch_type="FRANCHISEE", partner_id=partner_id, user_id=user_id,
                                            branch_ids=franchisee_ids) if not request.is_detailed else {}
            product_map = get_product_map(product_ids=n_product_ids,
                                          return_fields="id,code,name,category,storage_type,model_name",
                                          partner_id=partner_id, user_id=user_id) if request.is_detailed else {}

            if request.is_detailed:
                return {"pmsgs": [{"product_name": dp.product_name,
                                   "product_code": dp.product_code,
                                   "spec": product_map.get(str(dp.product_id), {}).get("model_name", ""),
                                   "quantity": dp.approve_quantity,
                                   "min_quantity": dp.min_quantity,
                                   "increment_quantity": dp.increment_quantity,
                                   "max_quantity": dp.max_quantity,
                                   "unit": dp.unit_name,
                                   "tax_price": dp.tax_price,
                                   "amount": round(dp.tax_price * dp.approve_quantity, 2),
                                   "storage_type": dp.storage_type,
                                   'id': dp.id,
                                   'product_id': dp.product_id} for dp in return_products.values()]}

            return {'msgs': [{"status": d.status,
                              "store_name": d.received_name,
                              "code": d.code,
                              "order_type_id": d.order_type_id,
                              "franchisee_name": franchisee_map.get(d.franchisee_id, {}).get('name'),
                              "amount": d.sum_price_tax,
                              "demand_date": datetime_2_timestamp(d.demand_date),
                              "arrival_date": datetime_2_timestamp(d.arrival_date)
                              } for d in return_products.values()]}

        return ret

    def batch_deal_hd_demand(self, request, partner_id, user_id, allow_status=None, action=None):
        action = Action.HD_SUBMIT.code if request.action == "PREPARE" else request.action
        start_date = self.timestamp2datetime(request.start_date)
        end_date = self.timestamp2datetime(request.end_date)
        received_bys = [convert_to_int(_id) for _id in request.received_bys] if request.received_bys else []
        order_type_ids = [convert_to_int(_id) for _id in request.order_type_ids] if request.order_type_ids else []
        bus_types = [_type for _type in request.bus_types] if request.bus_types else []
        exclude_ids = list(request.exclude_ids)
        include_ids = list(request.include_ids)
        ret = dict(result=True)
        is_detailed = False
        if request.batch_method == "ALL":
            if (end_date -  start_date).days > 60:
                raise DataValidationException('时间范围不能超过60天')

            received_bys = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                   domain='boh.frs_hd_management',
                                                   branch_ids=received_bys)
            # received_bys = [4679554862445232129]
            logging.info("received_bys len:{}".format(len(received_bys)))
            demands = sFD.list_f_demand(partner_id=partner_id, start_date=start_date, received_bys=received_bys,
                                        bus_types=bus_types, order_type_ids=order_type_ids, end_date=end_date,
                                        status=allow_status, exclude_ids=exclude_ids, global_session=True,
                                        for_update=True)
        elif request.batch_method == "SELF":
            if len(include_ids) == 0:
                return ret
            demands = sFD.list_f_demand(partner_id=partner_id, ids=include_ids, status=allow_status,
                                        exclude_ids=exclude_ids, global_session=True, for_update=True)
        else:
            raise DataValidationException("Param `batch_method` is Required!")
        if len(demands) == 0:
            return ret
        # for
        demand_ids = [i.id for i in demands if i.id not in exclude_ids]
        # demand_ids = [4791962022491226112]
        if len(demand_ids) > 1:
            # 批量只支持·提交·和·作废·
            if action not in [Action.HD_SUBMIT.code, 'INVALID']:
                raise StatusUnavailable("批量操作仅支持提交和作废")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        demand_logs = []
        update_data = []
        change_status = None
        if action == Action.HD_SUBMIT.code:
            # status = "R_APPROVE"  # 总部分配提交后对应门店订单"待确认" 20221206经业务确认
            status = SupplyPartnerAction.get_to_action(partner_id, PartnerActionModule.DEMAND.code,
                                                       Action.R_APPROVE.code)
            if status == Action.PREPARE.code:
                change_status = Action.CONFIRMED.code

            sync_approve = True
        else:
            status = action
            sync_approve = False
        print('status',status)
        trace_id = get_guid()
        # check_map = {}

        demand_ids_map = {i.id: i for i in
                          session.query(sFD).filter(sFD.partner_id == partner_id, sFD.id.in_(demand_ids))
                          if i.status == Action.INITED.code}

        if not demand_ids_map:
            if is_detailed:
                raise OrderNotExistException("订单不存在-{}".format(demand_ids[0]))

            return ret

        demand_products_dict, product_ids = {}, set()
        if action == Action.HD_SUBMIT.code:
            demand_products = session.query(sFDP).filter(sFDP.demand_id.in_(demand_ids),
                                                         sFDP.partner_id == partner_id).all()
            for p in demand_products:
                product_ids.add(p.product_id)
                if p.demand_id in demand_products_dict:
                    demand_products_dict[p.demand_id][p.product_id] = p
                    continue

                demand_products_dict[p.demand_id] = {p.product_id: p}

        franchisee_ids, n_product_ids, return_products = set(), set(), {}
        go_on_f = lambda x: False if not is_detailed else x
        key = namedtuple('key', ['demand_id', 'product_id'])
        free_products_data = products_manage_service.query_free_order_limit(
            product_ids, partner_id, user_id).get('rows', []) if action == Action.HD_SUBMIT.code else []

        for demand_id, demand_db in demand_ids_map.items():

            dp_ids = None
            if action == Action.HD_SUBMIT.code:
                products: Dict[int: sFDP] = demand_products_dict.get(demand_db.id)
                dp_ids = set(products)
                if products:
                    go_on_for_loop = True

                    valid_products = products_manage_service.get_order_rule_by_store(store_id=demand_db.received_by,
                                                                                     product_ids=product_ids,
                                                                                     partner_id=partner_id,
                                                                                     user_id=user_id).get('rows', {})

                    # print(valid_products)
                    valid_products = {int(vp['product_id']): vp['order_rule'] for vp in valid_products
                                      if 'order_rule' in vp and 'product_id' in vp}
                    free_products = {}
                    for f in free_products_data:
                        fp_id = int(f['product_id'])
                        if fp_id not in products:
                            continue

                        if 'limit_qty' not in f:
                            continue

                        unit_rate = products[fp_id].unit_rate
                        if not unit_rate:
                            continue

                        free_products[fp_id] = Decimal(f['limit_qty']) / unit_rate

                    # print(valid_products)
                    for k, p in products.items():
                        if not is_detailed and not go_on_for_loop:
                            break

                        vp = valid_products.get(p.product_id, {})
                        # print('vp', vp)
                        if not vp and p.tax_price:
                            dp_ids.remove(p.product_id)
                            continue

                        max_qty = -Decimal(vp.get('max_qty') or 0)
                        if not p.tax_price:
                            if p.product_id not in free_products and not vp:
                                dp_ids.remove(p.product_id)
                                continue

                            free_max_qty = -Decimal(free_products.get(p.product_id, 0))
                            max_qty = max(max_qty, free_max_qty) if \
                                vp.get('max_qty') and p.product_id in free_products else min(max_qty, free_max_qty)

                        max_qty = -max_qty
                        check = check_quantity(p.quantity, vp.get('min_qty'), vp.get('increase_qty'), max_qty) if vp \
                            else p.quantity <= max_qty
                        if check:
                            dp_ids.remove(p.product_id)
                        else:
                            if is_detailed:
                                p.min_quantity = float(vp.get('min_qty', 0))
                                p.max_quantity = float(max_qty)
                                p.increment_quantity = float(vp.get('increase_qty', 0))
                                return_products[key(demand_db.id, p.product_id)] = p
                            else:
                                return_products[key(demand_db.id, p.product_id)] = demand_db

                            go_on_for_loop = go_on_f(go_on_for_loop)

                    n_product_ids |= dp_ids
                    franchisee_ids.add(demand_db.franchisee_id) if dp_ids else ...

            if dp_ids:
                print('dp_ids',dp_ids)
                continue

            demand = dict(
                id=demand_id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                # remark=request.remark,
                status=(change_status or status) if not demand_db.sum_price_tax else status,
                pay_amount=demand_db.sum_price_tax,
                payment_way=PaymentWay.CreditPay.code  # 总部分配默认信用付支付  20221207确认
            )
            # if request.attachments:
            #     demand["attachments"] = str(request.attachments)
            update_data.append(demand)
            demand_logs.append(dict(
                partner_id=partner_id,
                demand_id=demand_id,
                action=action,
                action_name=Action.get_desc(action),
                platform=self.platform,
                start_status=demand_db.status,
                end_status=status,
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
                trace_id=trace_id
            ))

        if update_data:
            print('update_data',update_data)
            print('allow_status',allow_status)
            ret['result'] = sFD.update_f_demand(update_data=update_data, demand_logs=demand_logs,
                                                allow_status=allow_status,
                                                sync_approve=sync_approve, demand_ids=demand_ids, global_session=True)

        if n_product_ids:
            franchisee_map = get_branch_map(branch_type="FRANCHISEE", partner_id=partner_id, user_id=user_id,
                                            branch_ids=franchisee_ids) if not is_detailed else {}
            product_map = get_product_map(product_ids=n_product_ids,
                                          return_fields="id,code,name,category,storage_type,model_name",
                                          partner_id=partner_id, user_id=user_id) if is_detailed else {}

            if is_detailed:
                return {"pmsgs": [{"product_name": dp.product_name,
                                   "product_code": dp.product_code,
                                   "spec": product_map.get(str(dp.product_id), {}).get("model_name", ""),
                                   "quantity": dp.approve_quantity,
                                   "min_quantity": dp.min_quantity,
                                   "increment_quantity": dp.increment_quantity,
                                   "max_quantity": dp.max_quantity,
                                   "unit": dp.unit_name,
                                   "tax_price": dp.tax_price,
                                   "amount": round(dp.tax_price * dp.approve_quantity, 2),
                                   "storage_type": dp.storage_type,
                                   'id': dp.id,
                                   'product_id': dp.product_id} for dp in return_products.values()]}

            return {'msgs': [{"status": d.status,
                              "store_name": d.received_name,
                              "code": d.code,
                              "order_type_id": d.order_type_id,
                              "franchisee_name": franchisee_map.get(d.franchisee_id, {}).get('name'),
                              "amount": d.sum_price_tax,
                              "demand_date": datetime_2_timestamp(d.demand_date),
                              "arrival_date": datetime_2_timestamp(d.arrival_date)
                              } for d in return_products.values()]}
        if ret.get('result') is True:
            ret['description'] = 'success'
        else:
            ret['description'] = 'failed'
        return ret


frs_hd_assign_service = FranchiseeHdAssignmentService()
