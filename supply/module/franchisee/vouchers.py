# -*- coding: utf-8 -*-
from datetime import datetime
from supply.driver.mq import mq_producer
from supply.error.exception import *
from supply.utils.helper import get_guid, get_branch_map, get_uuids, convert_to_int, get_username_map, MessageTopic
from supply.client.metadata_service import metadata_service
from supply.module import BaseToolsModule
from supply.model.supply_doc_code import Supply_doc_code
from supply.model.franchisee.vouchers import FranchiseeVouchers as VouchersDB


class VouchersService(BaseToolsModule):
    """加盟商代金券相关业务操作"""

    def __init__(self):
        super(VouchersService, self).__init__()
        self.ALLOW_UPDATE_STATUS = ["INITED"]

    def build_vouchers_relations(self, voucher_id, order_type_ids, store_list, region_list, partner_id, user_id,
                                 username):
        """构建代金券关联数据
        :returns order_types[], stores[], regions[]
        """
        order_types = []
        stores = []
        regions = []
        if order_type_ids:
            ids = get_uuids(len(order_type_ids))
            order_type_map = self.get_order_type_map(order_type_ids=order_type_ids, partner_id=partner_id,
                                                     user_id=user_id)
            for inx, order_type_id in enumerate(order_type_ids):
                order_types.append(dict(
                    id=ids[inx],
                    partner_id=partner_id,
                    voucher_id=voucher_id,
                    order_type_id=order_type_id,
                    order_type_name=order_type_map.get(int(order_type_id), {}).get('name'),
                    order_type_code=order_type_map.get(int(order_type_id), {}).get('code'),
                    created_by=user_id,
                    created_name=username,
                    updated_by=user_id,
                    updated_name=username
                ))

        if store_list:
            store_ids = [store.store_id for store in store_list]
            ids = get_uuids(len(store_ids))
            store_map = get_branch_map(branch_ids=store_ids, branch_type="STORE", partner_id=partner_id,
                                       user_id=user_id)
            for inx, store in enumerate(store_list):
                store_id = store.store_id
                stores.append(dict(
                    id=ids[inx],
                    partner_id=partner_id,
                    voucher_id=voucher_id,
                    store_id=store_id,
                    store_name=store_map.get(store_id, {}).get('name'),
                    store_code=store_map.get(store_id, {}).get('code'),
                    store_type="FRS_STORE",  # 暂时写死 加盟门店
                    amount=store.amount,
                    created_by=user_id,
                    created_name=username,
                    updated_by=user_id,
                    updated_name=username
                ))

        if region_list:
            ids = get_uuids(len(region_list))
            for inx, r in enumerate(region_list):
                regions.append(dict(
                    id=ids[inx],
                    partner_id=partner_id,
                    voucher_id=voucher_id,
                    region_id=r.region_id,
                    region_name=r.region_name,
                    region_code=r.region_code,
                    region_type=r.region_type,
                    created_by=user_id,
                    created_name=username,
                    updated_by=user_id,
                    updated_name=username
                ))
        return order_types, stores, regions

    def create_vouchers(self, request, partner_id, user_id):
        """新建代金券"""
        if request.request_id:
            exist = VouchersDB.get_voucher(partner_id=partner_id, request_id=request.request_id)
            if exist:
                raise Exception("请勿重复提交代金券！")
        else:
            raise NoRequestIDError("缺少request_id，不允许创建！")
        # exist = VouchersDB.get_voucher(partner_id=partner_id, name=request.name)
        # if exist:
        #     raise DataDuplicationException("名称重复-{}".format(request.name), "name")
        voucher_id = get_guid()
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        voucher = dict(
            id=voucher_id,
            partner_id=partner_id,
            request_id=request.request_id,
            name=request.name,
            code=Supply_doc_code.get_code_by_type('VOUCHER', partner_id, None),
            status='INITED',
            process_status='INITED',
            start_date=self.timestamp2datetime(request.start_date),
            end_date=self.timestamp2datetime(request.end_date),
            amount=request.amount,
            remark=request.remark,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username
        )
        voucher_log = dict(
            id=get_guid(),
            partner_id=partner_id,
            voucher_id=voucher_id,
            action='INITED',
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow()
        )
        if not request.order_type_ids:
            raise DataValidationException("请传入订货类型！")
        order_type_ids = [convert_to_int(_id) for _id in request.order_type_ids]
        order_types, stores, regions = self.build_vouchers_relations(voucher_id=voucher_id,
                                                                     order_type_ids=order_type_ids,
                                                                     store_list=request.stores,
                                                                     region_list=request.regions, partner_id=partner_id,
                                                                     user_id=user_id, username=username)

        _ = VouchersDB.create_vouchers(vouchers=[voucher], vouchers_logs=[voucher_log], stores=stores,
                                       regions=regions, order_types=order_types)
        return {"voucher_id": voucher_id}

    def get_voucher_detail(self, request, partner_id, user_id):
        """查询代金券详情"""
        voucher_id = request.voucher_id
        voucher = VouchersDB.get_voucher(partner_id=partner_id, voucher_id=voucher_id)
        if not voucher:
            raise NoResultFoundError("Not found vouchers: {}".format(voucher_id))
        voucher = voucher.serialize(conv=True)
        store_dbs, region_dbs, order_type_dbs = VouchersDB.get_vouchers_relations(voucher_ids=[voucher_id],
                                                                                  partner_id=partner_id, get_store=True,
                                                                                  get_region=True, get_order_type=True)
        if len(store_dbs) > 0:
            voucher['stores'] = []
            for s in store_dbs:
                voucher['stores'].append(dict(
                    store_id=s.store_id,
                    store_code=s.store_code,
                    store_name=s.store_name,
                    store_type=s.store_type,
                    amount=s.amount
                ))
        if len(region_dbs) > 0:
            voucher['regions'] = []
            for r in region_dbs:
                voucher['regions'].append(dict(
                    region_id=r.region_id,
                    region_type=r.region_type,
                    region_code=r.region_code,
                    region_name=r.region_name,
                ))
        if len(order_type_dbs) > 0:
            voucher["order_types"] = []
            for ot in order_type_dbs:
                voucher["order_types"].append(dict(
                    order_type_id=ot.order_type_id,
                    order_type_code=ot.order_type_code,
                    order_type_name=ot.order_type_name,
                ))
        return voucher

    def list_vouchers(self, request, partner_id, user_id):
        """查询代金券列表"""
        res = dict(rows=[], total=0)
        voucher_ids = [convert_to_int(_id) for _id in request.voucher_ids] if request.voucher_ids else []
        status = list(request.status) if request.status else []
        order_type_ids = [convert_to_int(_id) for _id in request.order_type_ids] if request.order_type_ids else []
        query_set = VouchersDB.list_vouchers(partner_id=partner_id, name=request.name, code=request.code,
                                             ids=voucher_ids, status=status, order_type_ids=order_type_ids,
                                             limit=request.limit, offset=request.offset,
                                             include_total=request.include_total, order=request.order,
                                             sort=request.sort)
        if isinstance(query_set, tuple):
            total, voucher_dbs = query_set
            res['total'] = total
        else:
            voucher_dbs = query_set
        voucher_ids = [v.id for v in voucher_dbs]
        store_dbs, _, order_type_dbs = VouchersDB.get_vouchers_relations(voucher_ids=voucher_ids, partner_id=partner_id,
                                                                         get_store=True, get_order_type=True)
        order_type_map = {}
        if order_type_dbs:
            for ot in order_type_dbs:
                row = dict(
                    order_type_id=ot.order_type_id,
                    order_type_name=ot.order_type_name,
                    order_type_code=ot.order_type_code,
                )
                if ot.voucher_id in order_type_map.keys():
                    order_type_map[ot.voucher_id].append(row)
                else:
                    order_type_map[ot.voucher_id] = [row]
        store_map = {}
        if store_dbs:
            for store in store_dbs:
                row = dict(
                    store_id=store.store_id,
                    store_code=store.store_code,
                    store_name=store.store_name,
                    store_type=store.store_type,
                    amount=store.amount,
                )
                if store.voucher_id in store_map.keys():
                    store_map[store.voucher_id].append(row)
                else:
                    store_map[store.voucher_id] = [row]
        for voucher in voucher_dbs:
            row = voucher.serialize(conv=True)
            row['order_types'] = order_type_map.get(voucher.id, [])
            row['stores'] = store_map.get(voucher.id, [])
            res['rows'].append(row)
        return res

    def update_vouchers(self, request, partner_id, user_id):
        voucher_id = request.voucher_id
        if not voucher_id:
            raise DataValidationException("voucher_id is required!")
        voucher = VouchersDB.get_voucher(partner_id=partner_id, voucher_id=voucher_id)
        if not voucher:
            raise NoResultFoundError("Not found vouchers: {}".format(voucher_id))
        # 更新状态校验
        if voucher.status not in self.ALLOW_UPDATE_STATUS:
            raise DataValidationException("当前状态不允许更新！")
        if not request.order_type_ids:
            raise DataValidationException("更新请传入订货类型！")
        # exist = VouchersDB.get_voucher(partner_id=partner_id, name=request.name)
        # if exist and exist.id != voucher_id:
        #     raise DataDuplicationException("名称重复-{}".format(request.name), "name")
        order_type_ids = [convert_to_int(_id) for _id in request.order_type_ids]
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        start_date = self.timestamp2datetime(request.start_date) if request.start_date else None
        end_date = self.timestamp2datetime(request.end_date) if request.end_date else None
        update_data = dict(
            id=voucher_id,
            partner_id=partner_id,
            name=request.name,
            amount=request.amount,  # 注意金额必传的
            remark=request.remark,
            updated_by=user_id,
            updated_name=username
        )
        voucher_log = dict(
            id=get_guid(),
            partner_id=partner_id,
            voucher_id=voucher_id,
            action='UPDATE',
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow()
        )
        if isinstance(start_date, datetime) and start_date.year != 1970:
            update_data['start_date'] = start_date
        if isinstance(end_date, datetime) and end_date.year != 1970:
            update_data['end_date'] = end_date
        update_data['order_types'], update_data['stores'], update_data['regions'] = self.build_vouchers_relations(
            voucher_id=voucher_id,
            order_type_ids=order_type_ids,
            store_list=request.stores, region_list=request.regions,
            partner_id=partner_id, user_id=user_id,
            username=username)
        _ = VouchersDB.update_vouchers(update_data=update_data, voucher_logs=[voucher_log],
                                       allow_status=self.ALLOW_UPDATE_STATUS)

        return {"voucher_id": voucher_id}

    def deal_vouchers_by_ids(self, request, allow_status: list, partner_id=None, user_id=None):
        """变更退款单状态, 支持批量修改"""
        ret = {}
        action = request.action
        voucher_ids = [int(_id) for _id in request.voucher_ids] if request.voucher_ids else []
        if len(voucher_ids) == 0:
            raise DataValidationException("voucher_ids不可为空！")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        update_data = []
        voucher_logs = []
        if len(voucher_ids) > 1:
            # 批量支持·确认·和·作废·
            if action not in ['INVALID', 'CONFIRMED']:
                raise StatusUnavailable("批量操作仅支持审核和驳回")
        for voucher_id in voucher_ids:
            voucher_db = VouchersDB.get_voucher(partner_id=partner_id, voucher_id=voucher_id)
            if not voucher_db:
                raise NoResultFoundError("Not found vouchers: {}".format(voucher_id))
            row = dict(
                id=voucher_id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                status=action
            )
            if request.remark:
                row['remark'] = request.remark
            update_data.append(row)
            voucher_logs.append(dict(
                id=get_guid(),
                partner_id=partner_id,
                voucher_id=voucher_id,
                action=action,
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow())
            )
        res = VouchersDB.update_vouchers(update_data=update_data, voucher_logs=voucher_logs, allow_status=allow_status)
        if res is True:
            ret["result"] = res
            ret["description"] = "success"
            # // CreditPayTopic boh信用付服务topic
            # CreditPayTopic = "boh_credit_pay"
            #
            # // GiveVouchersTag 发放代金券
            # GiveVouchersTag = "boh_credit_pay_give_vouchers"
            mq_producer.publish(topic_group=MessageTopic.CREDIT_PAY_TOPIC, topic=MessageTopic.GIVE_VOUCHERS_TAG,
                                message=dict(voucher_ids=voucher_ids, partner_id=partner_id, user_id=user_id))

        else:
            ret['result'] = res
            ret["description"] = "failed"
        return ret


vouchers_service = VouchersService()
