import json
import logging
from decimal import Decimal
from datetime import datetime,timezone

from google.protobuf.timestamp_pb2 import Timestamp

from supply.api import handle_attachments, handle_request_attachments
from supply.client.inventory_service import inventory_service
from supply.client.products_manage_service import products_manage_service
from supply.model.receiving_diff import ReceivingDiffModel, ReceivingDiffProductModel
from supply.module.franchisee.helper import get_category_children
from supply.task.message_service_pub import MessageServicePub
from supply.utils import pb2dict
from supply.utils.enums import ReturnTransType
from supply.utils.helper import get_guid, MessageTopic, convert_to_int
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.helper import get_branch_list_map, get_branch_map
from supply.driver.mq import mq_producer
from supply.driver.mysql import session_maker, session
from supply.module.common import Refund
import supply.module.common as common
from supply.module.returns import ReturnService, get_receive_prod_price
from supply.client.receipt_service import receipt_service
from supply.client.metadata_service import metadata_service
# from supply.client.products_manage_service import products_manage_service
from supply.error.exception import NoResultFoundError, StatusUnavailable, DataValidationException

from supply.model.supply_doc_code import Supply_doc_code
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefund
from supply.model.returns import ReturnModel, ReturnProductModel, ReturnLogModel
from supply.model.franchisee.franchisee_returns import FranchiseeReturnModel

ORIGINAL_DOCUMENT = "BO"
NON_ORIGINAL_DOCUMENT = "NBO"
DISTRIBUTION_TYPE = "NMD"


class FranchiseeReturn(ReturnService):

    def confirm_return(self, return_id, partner_id, user_id):
        """
        确认退货单
        """
        db_session = session_maker()
        try:
            # 校验价格中心
            # 校验对应的发货仓库
            operator_name = metadata_service.get_username_by_pid_uid(
                partner_id, user_id)
            return_db = common.get_return_by_id(db_session, return_id, partner_id)
            count, return_products_db = common.list_return_products_by_return_id(
                db_session, return_id, partner_id=partner_id)
            if not return_db:
                raise NoResultFoundError("no result found!")

            if return_db.logistics_type in ("NMD", "PAD"):
                if return_db.type in ("IAD", "CAD") and return_db.status != "DELIVERED":
                    raise StatusUnavailable(
                        "only DELIVERED data can be confirmed!")
            else:
                if return_db.status not in ("APPROVED", "DELIVERED"):
                    raise StatusUnavailable(
                        "only APPROVED data can be confirmed!")

            args = {
                "status": "CONFIRMED",
                "updated_at": datetime.utcnow(),
                "updated_by": user_id,
                "updated_name": operator_name,
                "review_by": user_id,
                "inventory_status": "INITED",
            }

            product_update_list = []
            for return_product_db in return_products_db:
                p_args = {
                    "id": return_product_db.id,
                    "status": "CONFIRMED",
                    "updated_at": datetime.utcnow(),
                    "updated_by": user_id,
                    "updated_name": operator_name
                }
                product_update_list.append(p_args)

            if return_db.sub_type == "store":
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                                 doc_type="return"))

            log_detail = {}
            log_detail["doc_id"] = return_id
            log_detail["operation"] = "CONFIRM"
            log_detail["success"] = True
            log_detail["partner_id"] = partner_id
            log_detail["created_by"] = user_id
            log_detail["created_at"] = datetime.utcnow()
            log_detail['updated_at'] = datetime.utcnow()
            log_detail["created_name"] = operator_name

            common.update_returns(db_session, return_id, args, partner_id)
            common.update_products_in_all(db_session, updated_products=product_update_list)
            common.create_returns_log(db_session, **log_detail)

            # 配送方式接入退款
            if return_db.logistics_type == DISTRIBUTION_TYPE and return_db.trans_type == ReturnTransType.NeedPickUp.code:
                batch_id = 0
                refund_id = get_guid()
                if return_db.source_id:
                    rec_db = receipt_service.get_receive_by_id(receive_id=return_db.source_id, partner_id=partner_id,
                                                               user_id=user_id)
                    batch_id = rec_db.batch_id
                refund = Refund(refund_id, partner_id, user_id, operator_name)
                product_list = [p.serialize(conv=True) for p in return_products_db]
                refund_products, total_amount = refund.get_refund_products(products=product_list,
                                                                           main_type='FRS_RETURN')
                refund_detail = refund.get_return_refund_detail(
                    detail=return_db, refund_amount=total_amount, main_type='FRS_RETURN', refund_type='RETURN_REFUND',
                    master_id=batch_id)
                refund_log = refund.get_refund_log()
                db_session = session_maker()
                SupplyFranchiseeRefund.create_franchisee_refund(
                    db_session=db_session, refund_details=[refund_detail], refund_products=refund_products,
                    refund_logs=[refund_log]
                ) if total_amount else ...
            # 直送方式暂不接入退款
            else:
                pass

            db_session.commit()
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()
        return True

    # 门店创建退货单
    def create_return_store(self, return_by, return_to, return_delivery_date,
                            type, sub_type, return_reason,
                            logistics_type, request_id,
                            product_detail, partner_id, user_id,
                            remark=None, attachments=None,
                            source_id=None, source_code=None, refund_type=None,
                            franchisee_id=None):
        """
        加盟商门店创建退款单
        新增了退款金额及退款方式
        """
        if not logistics_type:
            logistics_type = DISTRIBUTION_TYPE
        # operator_name = metadata_service.get_username_by_pid_uid(
        #     partner_id, user_id)
        operator_name = ''
        return_id = gen_snowflake_id()
        return_code = Supply_doc_code.get_code_by_type(
            'RETURN_OD', partner_id, None)
        return_date = datetime.utcnow()
        return_delivery_date = datetime.fromtimestamp(
            return_delivery_date.seconds)

        # 创建退货单商品
        product_nums = 0
        delivery_p_list = []
        rec_p_list = []

        product_id_list = [str(product_d.product_id)
                           for product_d in product_detail]
        product_code_list = [
            product_d.product_code for product_d in product_detail]
        # 原单退货, 校验可退货数量
        diff_map = {}
        return_p_map = {}
        for product_d in product_detail:
            return_p_map[product_d.product_id] = (float(product_d.confirmed_quantity), float(product_d.quantity))
        if type == 'BO':
            # 查询是否有收货差异单
            diff_dbs = ReceivingDiffModel.list_receiving_diffs_by_receiving_id(source_id, partner_id)
            diff_ids = [d.id for d in diff_dbs if d.status == 'CONFIRMED']
            if diff_ids:
                diff_products = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_ids(diff_ids, partner_id)
                for dp in diff_products:
                    diff_map[dp.product_id] = float(dp.d_diff_quantity)
            for key, value in return_p_map.items():
                if round(value[0] - diff_map.get(key), 3) < value[1]:
                    raise DataValidationException("退货数量不能大于可退货数量")

        # 转换单位
        product_unit_dict = {}
        product_spec_dict = {}
        products_info = metadata_service.get_product_list(ids=[int(p) for p in product_id_list],
                                                          include_units=True,
                                                          return_fields='id,code,name,model_name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        # 构造商品规格映射
        for product_detail_info in products_info:
            product_spec_dict[int(product_detail_info['id'])
            ] = product_detail_info.get('model_name')

        # 构造商品单位映射
        unit_map = {}
        for product_detail_info in products_info:
            if product_detail_info.get('units'):
                product_unit_dict[product_detail_info['id']] = {}
                for unit in product_detail_info.get('units'):
                    if unit.get('purchase') and unit['purchase']:
                        product_unit_dict[product_detail_info['id']]['purchase'] = unit['rate'] if unit.get(
                            'rate') else 0
                    if unit.get('order') and unit['order']:
                        product_unit_dict[product_detail_info['id']]['order'] = unit['rate'] if unit.get(
                            'rate') else 0
                        unit_map[int(product_detail_info['id'])] = convert_to_int(unit.get("id", 0))
                    if unit.get('default') and unit['default']:
                        product_unit_dict[product_detail_info['id']]['default'] = unit['rate'] if unit.get(
                            'rate') else 0
            else:
                product_unit_dict[product_detail_info['id']]['default'] = 0

        # 直送单需要拿到价格，获取商品成本
        product_tax_dict = {}
        purchase2order_rate_dict = {}
        products_list_map = {}
        total_amount = Decimal(.0)

        if logistics_type == 'PUR':
            product_tax_dict = metadata_service.get_tax_list(
                vendor_id=return_to, product_id_list=product_id_list,
                store_id=return_by, valid_time=datetime.utcnow(),
                partner_id=partner_id, user_id=user_id)
            # 订货单位*订货rate=采购单位*采购rate
            # 采购单位/订货单位=订货rate/采购rate

            # 订货单价*订货单位=采购单价*采购单位
            # 订货单价 = 采购单价*采购单位/订货单位
            # 订货单价 = 采购单价*订货rate/采购rate
            for key, key_value in product_unit_dict.items():
                purchase_rate = key_value.get(
                    'purchase') if key_value.get('purchase') else 1
                order_rate = key_value.get(
                    'order') if key_value.get('order') else 1
                purchase2order_rate_dict[int(key)] = order_rate / purchase_rate
        elif logistics_type == DISTRIBUTION_TYPE:
            if type == NON_ORIGINAL_DOCUMENT:
                # 价格中心取商品价格
                products_list = products_manage_service.ListAgentProducts(
                    store_id=int(return_by), product_ids=[int(i) for i in product_id_list],
                    page_order={"limit": -1})
                products_list_map = {int(product.get(
                    "id")): product for product in products_list}
                logging.info("获取价格中心:{}".format(products_list_map))
            # 原单退货
            elif type == ORIGINAL_DOCUMENT:
                pass
            else:
                raise DataValidationException(
                    f"退货单据: {return_code} 类型错误")
        # else:
        #     raise DataValidationException(
        #         f"配送类型: {logistics_type} 错误")

        product_insert_list = []
        for product in product_detail:
            product_nums += 1
            attachment = handle_request_attachments(product.attachments) if product.attachments else ['']
            product = pb2dict(product)
            product['attachments'] = attachment
            product['unit_spec'] = product_spec_dict.get(int(product.get('product_id')))
            product['id'] = gen_snowflake_id()
            product['return_id'] = return_id
            product['return_by'] = return_by
            product['return_date'] = return_date
            product['status'] = 'INITED'
            product['partner_id'] = partner_id
            product['created_by'] = user_id
            product['updated_by'] = user_id
            product['updated_name'] = operator_name
            product['created_name'] = operator_name
            product['created_at'] = datetime.utcnow()
            product['updated_at'] = datetime.utcnow()
            product['returned_quantity'] = float(product.get("quantity")) if product.get("quantity") else 0
            product['unit_rate'] = product_unit_dict.get(str(product.get('product_id'))).get(
                'order') if product_unit_dict.get(str(product.get('product_id'))) else 1
            product['unit_id'] = unit_map.get(int(product.get('product_id')))
            # product['attachments'] = json.dumps(list(product.get("attachments"))) if list(product.get("attachments")) else ''
            if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                if not product_tax_dict.get(product.get('product_id')):
                    raise DataValidationException(
                        '{} 没有维护合同'.format(product.get('product_name')))
                product['tax_rate'] = product_tax_dict.get(
                    product.get('product_id')).get('rate', 0)
                product['price'] = product_tax_dict.get(product.get('product_id')).get(
                    'no_tax', 0) * purchase2order_rate_dict.get(product.get('product_id'), 1)
                product['price_tax'] = product_tax_dict.get(product.get('product_id')).get(
                    'tax', 0) * purchase2order_rate_dict.get(product.get('product_id'), 1)

            # 加盟商目前只适配配送类型
            elif logistics_type == DISTRIBUTION_TYPE and type not in ('IAD', 'CAD'):
                if type != ORIGINAL_DOCUMENT:
                    product: ReturnProductModel
                    product_by_manage_service = products_list_map.get(
                        int(product.get("product_id")), {})
                    if not product_by_manage_service.get("tax_price"):
                        raise NoResultFoundError(f"价格中心未找到对应商品价格{product.get('product_name')}")
                    product["price_tax"] = round(float(product_by_manage_service.get(
                        "tax_price", 0)), 2)
                    product["tax_rate"] = float(product_by_manage_service.get(
                        "tax_ratio", 0))
                    product["price"] = round(float(product_by_manage_service.get(
                        "tax_price", 0)) / (1 + product["tax_rate"] / 100), 2)
                    qty = product.get("quantity") if product.get("quantity") else 0
                    tax_price = float(product.get('price_tax')) if product.get('price_tax') else 0
                    product["amount"] = qty * tax_price
                    # if not product.get("price_tax"):
                    #     raise NoResultFoundError(
                    #         f"价格中心未找到对应商品价格{product.get('product_name')}")
                    total_amount += Decimal(product.get('amount'))
                else:
                    product["price_tax"] = round(product.get("price_tax"), 2)
                    product["price"] = round(product.get("price_tax") / (1 + product.get("tax_rate") / 100), 2)
            product_insert_list.append(product)

            # 构造receipt发货单
            delivery_p = {
                'delivery_by': return_by,
                'product_id': product.get('product_id'),
                'product_code': product.get('product_code'),
                'product_name': product.get('product_name'),
                # 'storage_type': product.storage_type,
                'order_quantity': float(product.get('quantity')),
                'delivery_quantity': float(product.get('quantity')),
                'unit_id': product.get('unit_id'),
                'unit_name': product.get('unit_name'),
                'unit_rate': float(product.get('unit_rate')),
                'tax_price': float(product.get("price_tax")),
                "tax_rate": float(product.get("tax_rate"))}
            delivery_p_list.append(delivery_p)

            # 构造receipt收获单
            rec_p = {
                'receive_by': int(return_to),
                'product_id': product.get('product_id'),
                'product_code': product.get('product_code'),
                'product_name': product.get('product_name'),
                # 'storage_type': product.storage_type,
                'order_quantity': float(product.get('quantity')),
                'delivery_quantity': float(product.get('quantity')),
                'unit_id': product.get('unit_id'),
                'unit_name': product.get('unit_name'),
                'unit_rate': float(product.get('unit_rate')),
                'tax_price': float(product.get("price_tax")),
                "tax_rate": float(product.get("tax_rate"))}
            rec_p_list.append(rec_p)
        args = {
            'id': return_id,
            'code': return_code,
            'return_by': return_by,
            'return_delivery_date': return_delivery_date,
            'type': type,
            'sub_type': sub_type,
            'logistics_type': logistics_type,
            'return_reason': return_reason,
            'status': 'INITED',
            'partner_id': partner_id,
            'created_at': datetime.utcnow(),
            'created_by': user_id,
            'updated_at': datetime.utcnow(),
            'updated_name': operator_name,
            'created_name': operator_name,
            'updated_by': user_id,
            'return_to': return_to,
            'source_id': source_id,
            'source_code': source_code,
            'return_date': return_date,
            'product_nums': product_nums,
            'request_id': request_id,
            'refund_amount': total_amount,
            "refund_type": refund_type,
            "franchisee_id": franchisee_id
        }
        if remark:
            args.update(dict(remark=remark))
        if attachments:
            args.update(dict(attachments=handle_request_attachments(attachments)))
        if type in ('CAD'):
            args.update(dict(status='APPROVED'))

        # ---ForReceiptStart---#
        batch_type = 'FRS_RETURN'

        # 退货单创建到发货单
        today = datetime.today()
        delivery_res = receipt_service.create_deliverys(
            batch_id=return_id, batch_code=return_code, batch_type=batch_type,
            demand_type=type,
            # id=None, code=None,
            order_id=return_id, order_code=return_code,
            demand_id=source_id if type == 'BO' else return_id,
            # TODO 原单退货的门店出库单，原单收货单单号暂时塞到demand_id里，处理不优美，后期需要改
            demand_code=source_code if type == 'BO' else return_code,
            # receive_id=None, receive_code=None,
            receive_by=int(return_to), delivery_by=return_by,
            distr_type=logistics_type,
            delivery_date=return_delivery_date, demand_date=return_date,
            # arrival_date=None,
            expect_date=return_delivery_date,
            # storage_type=None,
            products=delivery_p_list, main_branch_type='FS',
            partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason,
            franchisee_id=franchisee_id)

        main_branch_type = None
        if logistics_type == 'PUR':
            main_branch_type = 'V'
        elif logistics_type == 'PAD':
            main_branch_type = 'M'
        else:
            main_branch_type = 'W'

        # 退货单创建到仓库收货单
        # 原单退货 —— batch_code是收货单原单code
        if source_code:
            receipt_service.create_receives(
                demand_type=type,
                # TODO 原单退货的仓库入库单，原单收货单单号暂时塞到batch_code里，处理不优美，后期需要改
                batch_id=return_id, batch_code=str(source_code), batch_type=batch_type,
                order_id=return_id, order_code=return_code,
                delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
                distr_type=logistics_type, main_branch_type='FS',
                delivery_date=return_delivery_date, demand_date=return_date, expect_date=return_delivery_date,
                products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason,
                franchisee_id=franchisee_id)
        # 非原单退货
        else:
            receipt_service.create_receives(
                demand_type=type,
                batch_id=return_id, batch_code=return_code, batch_type=batch_type,
                order_id=return_id, order_code=return_code,
                delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
                distr_type=logistics_type, main_branch_type='FS',
                delivery_date=return_delivery_date, demand_date=return_date, expect_date=return_delivery_date,
                products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason,
                franchisee_id=franchisee_id)

        # ---ForReceiptEnd---#
        ReturnProductModel.create_returns_in_all([args], product_insert_list)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return return_id

    # 门店创建退货单——拆单
    def create_return_store_in_batches(self, return_by, return_delivery_date,
                                       type, sub_type, return_reason,
                                       request_id,
                                       returns_product_dict, partner_id, user_id,
                                       remark=None, attachments=None,
                                       source_id=None, source_code=None, refund_type=None,
                                       franchisee_id=None):
        """
        创建门店退货单
        params: returns_product_dict->key:return_to, logistics_type; value: product
        """
        operator_name = metadata_service.get_username_by_pid_uid(
            partner_id, user_id)

        return_date = datetime.utcnow()
        return_delivery_date = datetime.fromtimestamp(
            return_delivery_date.seconds)

        product_code_list = []
        return_p_map = {}
        for key, value in returns_product_dict.items():
            for product_d in value:
                product_code_list.append(product_d.product_code)
                return_p_map[int(product_d.product_id)] = (
                float(product_d.confirmed_quantity), float(product_d.quantity))
        product_code_list = list(set(product_code_list))
        diff_map = {}
        if type == 'BO':
            # 查询是否有收货差异单
            diff_dbs = ReceivingDiffModel.list_receiving_diffs_by_receiving_id(source_id, partner_id)
            diff_ids = [d.id for d in diff_dbs if d.status == 'CONFIRMED']
            if diff_ids:
                diff_products = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_ids(diff_ids, partner_id)
                for dp in diff_products:
                    diff_map[dp.product_id] = float(dp.d_diff_quantity)
            for key, value in return_p_map.items():
                if round(value[0] - diff_map.get(key, 0), 3) < value[1]:
                    raise DataValidationException("退货数量不能大于可退货数量")
        # 转换单位
        unit_map = {}
        product_unit_dict = {}
        products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                          include_units=True,
                                                          return_fields='id,code,name,model_name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        product_spec_dict = {}
        for product_detail_info in products_info:
            product_spec_dict[int(product_detail_info['id'])
            ] = product_detail_info.get('model_name')
            if product_detail_info.get('units'):
                product_unit_dict[product_detail_info['id']] = {}
                for unit in product_detail_info.get('units'):
                    if unit.get('purchase') and unit['purchase'] == True:
                        product_unit_dict[product_detail_info['id']]['purchase'] = unit['rate'] if unit.get(
                            'rate') else 0
                    if unit.get('order') and unit['order'] == True:
                        product_unit_dict[product_detail_info['id']]['order'] = unit['rate'] if unit.get(
                            'rate') else 0
                        unit_map[int(product_detail_info['id'])] = convert_to_int(unit.get("id", 0))
                    if unit.get('default') and unit['default'] == True:
                        product_unit_dict[product_detail_info['id']]['default'] = unit['rate'] if unit.get(
                            'rate') else 0
            else:
                product_unit_dict[product_detail_info['id']]['default'] = 0

        product_insert_list = []
        return_insert_list = []
        return_ids = []
        for key, value in returns_product_dict.items():
            return_code = Supply_doc_code.get_code_by_type(
                'RETURN_OD', partner_id, None)
            return_id = gen_snowflake_id()
            return_ids.append(return_id)
            return_to = key[0]
            logistics_type = key[1]
            product_id_list = []
            for product_d in value:
                product_id_list.append(str(product_d.product_id))
            product_id_list = list(set(product_id_list))

            # 直送单需要拿到价格，获取商品成本
            product_tax_dict = {}
            purchase2order_rate_dict = {}
            products_list_map = {}
            total_amount = Decimal(.0)

            if logistics_type == 'PUR':
                product_tax_dict = metadata_service.get_tax_list(
                    vendor_id=return_to, product_id_list=product_id_list,
                    store_id=return_by, valid_time=datetime.utcnow(),
                    partner_id=partner_id, user_id=user_id)
                # 订货单位*订货rate=采购单位*采购rate
                # 采购单位/订货单位=订货rate/采购rate

                # 订货单价*订货单位=采购单价*采购单位
                # 订货单价 = 采购单价*采购单位/订货单位
                # 订货单价 = 采购单价*订货rate/采购rate
                for key, key_value in product_unit_dict.items():
                    purchase_rate = key_value.get(
                        'purchase') if key_value.get('purchase') else 1
                    order_rate = key_value.get(
                        'order') if key_value.get('order') else 1
                    purchase2order_rate_dict[int(key)] = order_rate / purchase_rate
            elif logistics_type == DISTRIBUTION_TYPE:
                if type == NON_ORIGINAL_DOCUMENT:
                    products_list = products_manage_service.ListAgentProducts(
                        store_id=return_by, product_ids=[int(i) for i in product_id_list],
                        page_order={"limit": -1})
                    products_list_map = {int(product.get(
                        "id")): product for product in products_list}
                    logging.info("获取价格中心:{}".format(products_list_map))
                # 原单退货
                elif type == ORIGINAL_DOCUMENT:
                    pass
                else:
                    raise DataValidationException(
                        f"退货单据: {return_code} 类型错误")
            # else:
            #     raise DataValidationException(
            #         f"配送类型: {logistics_type} 错误")

            # 创建退货单商品
            product_nums = 0
            delivery_p_list = []
            rec_p_list = []
            for product in value:
                product_nums += 1
                attachment = handle_request_attachments(product.attachments) if product.attachments else ['']
                product = pb2dict(product)
                product['attachments'] = attachment
                product['unit_spec'] = product_spec_dict.get(
                    int(product.get('product_id')))
                product['id'] = gen_snowflake_id()
                product['return_id'] = return_id
                product['return_by'] = return_by
                product['return_date'] = return_date
                product['status'] = 'INITED'
                product['partner_id'] = partner_id
                product['created_by'] = user_id
                product['updated_by'] = user_id
                product['updated_name'] = operator_name
                product['created_name'] = operator_name
                product['created_at'] = datetime.utcnow()
                product['updated_at'] = datetime.utcnow()
                product['returned_quantity'] = float(product.get("quantity")) if product.get("quantity") else 0
                product['unit_rate'] = product_unit_dict.get(str(product.get('product_id'))).get(
                    'order') if product_unit_dict.get(str(product.get('product_id'))) else 1
                product['unit_id'] = unit_map.get(int(product.get('product_id')))
                # product['attachments'] = json.dumps(list(product.get("attachments"))) if list(product.get("attachments")) else ''
                if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                    if not product_tax_dict.get(product.get('product_id')):
                        raise DataValidationException(
                            '{} 没有维护合同'.format(product.get('product_name')))
                    product['tax_rate'] = product_tax_dict.get(
                        int(product.get('product_id'))).get('rate', 0)
                    product['price'] = product_tax_dict.get(product.get('product_id')).get(
                        'no_tax', 0) * purchase2order_rate_dict.get(product.get('product_id'), 1)
                    product['price_tax'] = product_tax_dict.get(product.get('product_id')).get(
                        'tax', 0) * purchase2order_rate_dict.get(product.get('product_id'), 1)

                elif logistics_type == DISTRIBUTION_TYPE and type not in ('IAD', 'CAD'):
                    product: ReturnProductModel
                    if type != ORIGINAL_DOCUMENT:
                        product_by_manage_service = products_list_map.get(
                            int(product.get("product_id")), {})
                        product["price_tax"] = round(float(product_by_manage_service.get(
                            "tax_price", 0)), 2)
                        product["tax_rate"] = float(product_by_manage_service.get(
                            "tax_ratio", 0))
                        product["price"] = round(float(product_by_manage_service.get(
                            "tax_price", 0)) / (1 + product["tax_rate"] / 100), 2)
                        qty = float(product.get("quantity")) if product.get("quantity") else 0
                        tax_price = float(product.get('price_tax')) if product.get('price_tax') else 0
                        product["amount"] = qty * tax_price
                        if not product.get("price_tax"):
                            raise NoResultFoundError(
                                f"价格中心未找到对应商品价格{product.get('product_name')}")
                    else:
                        product["price_tax"] = round(product.get("price_tax"), 2)
                        product["price"] = round(product.get("price_tax") / (1 + product.get("tax_rate") / 100), 2)
                    if product.get("amount"):
                        total_amount += Decimal(product.get("amount"))
                    else:
                        total_amount = 0

                product_insert_list.append(product)

                # 构造receipt发货单
                delivery_p = {
                    'delivery_by': return_by,
                    'product_id': product.get('product_id'),
                    'product_code': product.get('product_code'),
                    'product_name': product.get('product_name'),
                    # 'storage_type': product.storage_type,
                    'order_quantity': float(product.get('quantity')),
                    'delivery_quantity': float(product.get('quantity')),
                    'unit_id': product.get('unit_id'),
                    'unit_name': product.get('unit_name'),
                    'unit_rate': product.get('unit_rate'),
                    'tax_price': float(product.get("price_tax")),
                    "tax_rate": float(product.get("tax_rate"))}
                delivery_p_list.append(delivery_p)

                # 构造receipt收获单
                rec_p = {
                    'receive_by': int(return_to),
                    'product_id': product.get('product_id'),
                    'product_code': product.get('product_code'),
                    'product_name': product.get('product_name'),
                    # 'storage_type': product.storage_type,
                    'order_quantity': float(product.get('quantity')),
                    'delivery_quantity': float(product.get('quantity')),
                    'unit_id': product.get('unit_id'),
                    'unit_name': product.get('unit_name'),
                    'unit_rate': product.get('unit_rate'),
                    'tax_price': float(product.get("price_tax")),
                    "tax_rate": float(product.get("tax_rate"))
                }
                rec_p_list.append(rec_p)
            args = {
                'id': return_id,
                'code': return_code,
                'return_by': return_by,
                'return_delivery_date': return_delivery_date,
                'type': type,
                'sub_type': sub_type,
                'logistics_type': logistics_type,
                'return_reason': return_reason,
                'status': 'INITED',
                'partner_id': partner_id,
                'created_at': datetime.utcnow(),
                'created_by': user_id,
                'updated_at': datetime.utcnow(),
                'updated_name': operator_name,
                'created_name': operator_name,
                'updated_by': user_id,
                'return_to': return_to,
                'source_id': source_id,
                'source_code': source_code,
                'return_date': return_date,
                'product_nums': product_nums,
                'request_id': request_id,
                'refund_amount': total_amount,
                "refund_type": refund_type,
                "franchisee_id": franchisee_id
            }
            if remark:
                args.update(dict(remark=remark))
            if attachments:
                args.update(dict(attachments=handle_request_attachments(attachments)))
            if type in ('CAD'):
                args.update(dict(status='APPROVED'))

            # ---ForReceiptStart---#
            batch_type = 'FRS_RETURN'

            # 退货单创建到发货单
            today = datetime.today()
            delivery_res = receipt_service.create_deliverys(
                batch_id=return_id, batch_code=return_code, batch_type=batch_type,
                demand_type=type,
                # id=None, code=None,
                order_id=return_id, order_code=return_code,
                demand_id=source_id if type == 'BO' else return_id,
                # TODO 原单退货的门店出库单，原单收货单单号暂时塞到demand_id里，处理不优美，后期需要改
                demand_code=source_code if type == 'BO' else return_code,
                # receive_id=None, receive_code=None,
                receive_by=int(return_to), delivery_by=return_by,
                distr_type=logistics_type,
                delivery_date=return_delivery_date, demand_date=return_date,
                # arrival_date=None,
                expect_date=return_delivery_date,
                # storage_type=None,
                products=delivery_p_list, main_branch_type='FS',
                partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason,
                franchisee_id=franchisee_id)

            main_branch_type = None
            if logistics_type == 'PUR':
                main_branch_type = 'V'
            elif logistics_type == 'PAD':
                main_branch_type = 'M'
            else:
                main_branch_type = 'W'

            # 退货单创建到仓库收货单
            # 原单退货 —— batch_code是收货单原单code
            if source_code:
                receipt_service.create_receives(
                    demand_type=type,
                    # TODO 原单退货的仓库入库单，原单收货单单号暂时塞到batch_code里，处理不优美，后期需要改
                    batch_id=return_id, batch_code=str(source_code), batch_type=batch_type,
                    order_id=return_id, order_code=return_code,
                    delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
                    distr_type=logistics_type, main_branch_type='FS',
                    delivery_date=return_delivery_date, demand_date=return_date, expect_date=return_delivery_date,
                    products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason,
                    franchisee_id=franchisee_id)
            # 非原单退货
            else:
                receipt_service.create_receives(
                    demand_type=type,
                    batch_id=return_id, batch_code=return_code, batch_type=batch_type,
                    order_id=return_id, order_code=return_code,
                    delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
                    distr_type=logistics_type, main_branch_type='FS',
                    delivery_date=return_delivery_date, demand_date=return_date, expect_date=return_delivery_date,
                    products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason,
                    franchisee_id=franchisee_id)

            # ---ForReceiptEnd---#

            if type in ('CAD'):
                # 调整单的退货直接更新成终态——已提货，仓库那边也需要判断并直接入库
                self.delivery_return(return_id, partner_id, user_id)

            return_insert_list.append(args)

        log_list = []
        for r_id in return_ids:
            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = r_id
            log_detail['operation'] = 'CREATE'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.utcnow()
            log_detail['updated_at'] = datetime.utcnow()
            log_detail['created_name'] = operator_name
            log_list.append(log_detail)

        ReturnProductModel.create_returns_in_all(return_detail=return_insert_list,
                                                 return_product_list=product_insert_list)
        ReturnLogModel.create_logs_list(log_list)

        return return_ids

    # 创建退货单入口
    def create_return_entrance(self, return_by, return_delivery_date,
                               type, sub_type, logistics_type,
                               return_reason, products, request_id,
                               partner_id, user_id,
                               remark=None, attachments=None, source_id=None, source_code=None, refund_type=None):
        """
        退货单任务分配入口
        根据sub_type判断退货单创建流程
        """
        res = {}
        return_ids = []
        # 判断单据是否重复创建
        exist_return_db = ReturnModel.get_by_request_id(request_id, partner_id)
        if exist_return_db:
            raise DataValidationException('请勿重复提交创建请求！')

        returns_dict = {}
        in_batches_flag = True  # 分批发货，同存储标志，需要拆单
        for product_detail in products:
            return_to = product_detail.return_to
            product_detail_dict = pb2dict(product_detail)
            if not product_detail_dict.get('logistics_type'):
                in_batches_flag = False
            product_detail_dict['logistics_type'] = DISTRIBUTION_TYPE
            key_set = (return_to, product_detail_dict.get('logistics_type'))

            if returns_dict.get(key_set):
                returns_dict[key_set].append(product_detail)
            else:
                returns_dict[key_set] = [product_detail]
        # 门店
        branch_map = get_branch_map(branch_ids=[int(return_by)], branch_type="STORE", partner_id=partner_id,
                                    user_id=user_id,
                                    return_fields="id,franchisee")
        franchisee_id = int(branch_map.get(return_by, {}).get("franchisee", 0))
        # 仓库/加工中心退货
        if sub_type == 'warehouse' or sub_type == 'machining':
            logistics_type = 'PUR'
            for key, value in returns_dict.items():
                return_to = key[0]
                return_id = self.create_return_warehouse(return_by=return_by, return_to=return_to,
                                                         return_delivery_date=return_delivery_date,
                                                         type=type, sub_type=sub_type, return_reason=return_reason,
                                                         logistics_type=logistics_type, request_id=request_id,
                                                         product_detail=value, partner_id=partner_id, user_id=user_id,
                                                         remark=remark, attachments=attachments,
                                                         source_id=source_id, source_code=source_code,
                                                         franchisee_id=franchisee_id)
                return_ids.append(return_id)

        # 门店退货
        else:
            if not logistics_type:
                logistics_type = DISTRIBUTION_TYPE
                # 判断门店状态是否允许退货
            status_check = metadata_service.check_store_status(
                return_by, partner_id, user_id)
            if not status_check:
                raise DataValidationException('请检查门店的开店状态！')

            if in_batches_flag:  # 分批发货，需要拆单
                return_ids = self.create_return_store_in_batches(return_by=return_by,
                                                                 return_delivery_date=return_delivery_date,
                                                                 type=type, sub_type=sub_type,
                                                                 return_reason=return_reason,
                                                                 request_id=request_id,
                                                                 partner_id=partner_id, user_id=user_id,
                                                                 remark=remark, attachments=attachments,
                                                                 source_id=source_id, source_code=source_code,
                                                                 returns_product_dict=returns_dict,
                                                                 refund_type=refund_type,
                                                                 franchisee_id=franchisee_id)
            else:
                for key, value in returns_dict.items():
                    return_to = key[0]
                    return_id = self.create_return_store(return_by=return_by, return_to=return_to,
                                                         return_delivery_date=return_delivery_date,
                                                         type=type, sub_type=sub_type, return_reason=return_reason,
                                                         logistics_type=logistics_type, request_id=request_id,
                                                         product_detail=value, partner_id=partner_id, user_id=user_id,
                                                         remark=remark, attachments=attachments,
                                                         source_id=source_id, source_code=source_code,
                                                         refund_type=refund_type, franchisee_id=franchisee_id)
                    return_ids.append(return_id)
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=return_by,
                                             doc_type="return"))
        # res["payload"] = True
        res["return_id"] = return_ids
        return res

    def list_returns(self, partner_id, user_id, request):
        # TODO: 需要新增receipt单据过滤
        code = request.code  # 门店退货单号
        limit = request.limit
        offset = request.offset
        return_by = request.store_ids
        return_to = request.return_to
        status = request.status
        source_code = request.source_code  # 门店收货单
        source_id = request.source_id
        return_date_from = request.return_date_from
        return_date_to = request.return_date_to
        delivery_date_from = request.delivery_date_from
        delivery_date_to = request.delivery_date_to
        type = request.type
        sub_type = request.sub_type
        logistics_type = request.logistics_type
        sort = request.sort
        order = request.order
        payment_ways = request.payment_ways

        # 加盟商新增的相关过滤
        delivery_code = request.delivery_code  # 发货单号
        receive_code = request.receive_code  # 退货入库单号

        product_ids = list(request.product_ids) if request.product_ids else []
        ids = request.ids

        return_bys = []
        for storeId in return_by:
            return_bys.append(int(storeId))

        return_tos = []
        for rt in return_to:
            return_tos.append(int(rt))

        count, return_db_list = ReturnModel.list_frachisee_returns(partner_id=partner_id, code=code,
                                                                   return_bys=return_bys,
                                                                   status=status, logistics_type=logistics_type,
                                                                   type=type,
                                                                   sub_type=sub_type, source_code=source_code,
                                                                   source_id=source_id,
                                                                   start_date=return_date_from, end_date=return_date_to,
                                                                   delivery_start_date=delivery_date_from,
                                                                   delivery_end_date=delivery_date_to,
                                                                   sort=sort, order=order, limit=limit, offset=offset,
                                                                   return_tos=return_tos, product_ids=product_ids,
                                                                   payment_way=payment_ways, ids=ids)
        franchisee_ids = []
        batch_ids = []
        return_db: ReturnModel
        for return_db in return_db_list:
            if return_db.franchisee_id:
                franchisee_ids.append(return_db.franchisee_id)
            batch_ids.append(return_db.id)

        franchisee_branch_map = get_branch_map(branch_ids=franchisee_ids, branch_type="FRANCHISEE",
                                               partner_id=partner_id,
                                               user_id=user_id, return_fields="id,code,name")

        returns_tos_id = [return_db.return_to for return_db in return_db_list]
        returns_bys_id = [return_db.return_by for return_db in return_db_list]
        returns_tos_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center",
                                              branch_ids=returns_tos_id,
                                              partner_id=partner_id, user_id=user_id)
        returns_bys_map = get_branch_list_map(branch_type="store", branch_ids=returns_bys_id,
                                              partner_id=partner_id, user_id=user_id)

        deliverys = receipt_service.list_franchisee_deliverys(batch_ids=batch_ids, partner_id=partner_id,
                                                              user_id=user_id, delivery_code=delivery_code)
        receives = receipt_service.list_franchisee_receives(batch_ids=batch_ids, partner_id=partner_id, user_id=user_id,
                                                            receive_code=receive_code)
        deliverys_map = {}
        receives_map = {}
        for delivery in deliverys:
            deliverys_map[delivery.get("batch_id")] = delivery

        for receive in receives:
            receives_map[receive.get("batch_id")] = receive
        return_amount_map = {}
        p_map = {}
        if batch_ids:
            amount_dbs, product_dbs = ReturnProductModel.get_return_amount_by_id(batch_ids, partner_id)
            return_amount_map = {amount_db.return_id: amount_db.amount for amount_db in amount_dbs}
            for p in product_dbs:
                if p_map.get(p.return_id):
                    p_map[p.return_id].append(p.product_name)
                else:
                    p_map[p.return_id] = [p.product_name]
        if return_db_list:
            if not delivery_code and not receive_code:
                return_list = []
                for return_db in return_db_list:
                    return_obj = return_db.serialize(conv=True)
                    franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})

                    return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
                    return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
                    return_obj['franchisee_name'] = franchisee.get('name')
                    return_obj['franchisee_code'] = franchisee.get('code')
                    return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                    return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                    return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                    return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                    return_obj['returns_price'] = float(return_amount_map.get(return_db.id, 0.0))
                    return_obj['product_name'] = "、".join(p_map.get(return_db.id)) if p_map.get(return_db.id,
                                                                                                []) else ''
                    del return_obj['attachments']
                    return_list.append(return_obj)
                return count, return_list
            else:
                if delivery_code:
                    return_ids = None
                    for return_id in deliverys_map:
                        return_ids = return_id
                    return_list = []
                    for return_db in return_db_list:
                        if str(return_db.id) == str(return_ids):
                            return_obj = return_db.serialize(conv=True)
                            franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})
                            return_obj['delivery_code'] = deliverys_map.get(str(return_db.id), {}).get('code')
                            return_obj['receive_code'] = receives_map.get(str(return_db.id), {}).get('code')
                            return_obj['franchisee_name'] = franchisee.get('name')
                            return_obj['franchisee_code'] = franchisee.get('code')
                            return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                            return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                            return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                            return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                            return_obj['returns_price'] = float(return_amount_map.get(return_db.id, 0.0))
                            return_obj['product_name'] = "、".join(p_map.get(return_db.id)) if p_map.get(return_db.id,
                                                                                                        []) else ''
                            del return_obj['attachments']
                            return_list.append(return_obj)
                elif receive_code:
                    return_ids = None
                    for return_id in receives_map:
                        return_ids = return_id
                    return_list = []
                    for return_db in return_db_list:
                        if str(return_db.id) == str(return_ids):
                            return_obj = return_db.serialize(conv=True)
                            franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})
                            return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
                            return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
                            return_obj['franchisee_name'] = franchisee.get('name')
                            return_obj['franchisee_code'] = franchisee.get('code')
                            return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                            return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                            return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                            return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                            return_obj['returns_price'] = float(return_amount_map.get(return_db.id, 0.0))
                            return_obj['product_name'] = "、".join(p_map.get(return_db.id)) if p_map.get(return_db.id,
                                                                                                        []) else ''
                            del return_obj['attachments']
                            return_list.append(return_obj)
                return 1, return_list
        return 0, []

    def get_return_by_id(self, return_id, partner_id, user_id):
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        if not return_db:
            return {}
        franchisee_ids = [return_db.franchisee_id]
        batch_ids = [return_db.id]
        deliverys = receipt_service.list_franchisee_deliverys(batch_ids=batch_ids, partner_id=partner_id,
                                                              user_id=user_id)
        receives = receipt_service.list_franchisee_receives(batch_ids=batch_ids, partner_id=partner_id, user_id=user_id)
        franchisee_branch_map = {}
        if return_db.franchisee_id:
            franchisee_branch_map = get_branch_map(branch_ids=franchisee_ids, branch_type="FRANCHISEE",
                                                   partner_id=partner_id,
                                                   user_id=user_id, return_fields="id,code,name")
        franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})
        returns_tos_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center",
                                              branch_ids=[return_db.return_to],
                                              partner_id=partner_id, user_id=user_id)
        returns_bys_map = get_branch_list_map(branch_type="store", branch_ids=[return_db.return_by],
                                              partner_id=partner_id, user_id=user_id)
        refunds = SupplyFranchiseeRefund.query_refund_by_main_ids(partner_id=partner_id, main_ids=[return_db.id])
        product_db, _ = ReturnProductModel.get_return_amount_by_id(return_ids=[return_db.id], partner_id=partner_id)
        deliverys_map = {}
        receives_map = {}
        for delivery in deliverys:
            deliverys_map[delivery.get("batch_id")] = delivery

        for receive in receives:
            receives_map[receive.get("batch_id")] = receive
        return_obj = return_db.serialize(conv=True)
        return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
        return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
        return_obj['franchisee_name'] = franchisee.get('name')
        return_obj['franchisee_code'] = franchisee.get('code')
        return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
        return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
        return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
        return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
        return_obj['refund_id'] = refunds[0].id if refunds else 0
        return_obj['refund_code'] = refunds[0].code if refunds else ''
        return_obj['refund_amount'] = float(product_db[0].amount) if product_db else 0
        return_obj['returns_price'] = float(product_db[0].amount) if product_db else 0
        return_obj['attachments'] = handle_attachments(return_db.attachments)
        return return_obj

    def list_returns_by_store(self, request, partner_id, user_id):
        # TODO: 需要新增receipt单据过滤
        code = request.code  # 门店退货单号
        limit = request.limit
        offset = request.offset
        return_by = request.return_by
        return_to = request.return_to
        status = request.status
        source_code = request.source_code  # 门店收货单
        source_id = request.source_id
        return_date_from = request.return_date_from
        return_date_to = request.return_date_to
        delivery_date_from = request.delivery_date_from
        delivery_date_to = request.delivery_date_to
        type = request.type
        sub_type = request.sub_type
        logistics_type = request.logistics_type
        sort = request.sort
        order = request.order
        payment_ways = request.payment_ways

        # 加盟商新增的相关过滤
        delivery_code = request.delivery_code  # 发货单号
        receive_code = request.receive_code  # 退货入库单号

        product_ids = list(request.product_ids) if request.product_ids else []

        return_bys = []
        for storeId in return_by:
            return_bys.append(int(storeId))

        return_tos = []
        for rt in return_to:
            return_tos.append(int(rt))

        count, return_db_list = FranchiseeReturnModel.list_returns_by_store(partner_id=partner_id, code=code,
                                                                            return_bys=return_bys,
                                                                            status=status,
                                                                            logistics_type=logistics_type, type=type,
                                                                            sub_type=sub_type, source_code=source_code,
                                                                            source_id=source_id,
                                                                            start_date=return_date_from,
                                                                            end_date=return_date_to,
                                                                            delivery_start_date=delivery_date_from,
                                                                            delivery_end_date=delivery_date_to,
                                                                            sort=sort, order=order, limit=limit,
                                                                            offset=offset,
                                                                            return_tos=return_tos,
                                                                            product_ids=product_ids,
                                                                            payment_way=payment_ways, query_type="ST")
        if not return_db_list:
            return 0, []
        franchisee_ids = []
        batch_ids = []
        return_db: ReturnModel
        for return_db in return_db_list:
            if return_db.franchisee_id:
                franchisee_ids.append(return_db.franchisee_id)
            batch_ids.append(return_db.id)

        franchisee_branch_map = get_branch_map(branch_ids=franchisee_ids, branch_type="FRANCHISEE",
                                               partner_id=partner_id,
                                               user_id=user_id, return_fields="id,code,name")

        returns_tos_id = [return_db.return_to for return_db in return_db_list]
        returns_bys_id = [return_db.return_by for return_db in return_db_list]
        returns_tos_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center",
                                              branch_ids=returns_tos_id,
                                              partner_id=partner_id, user_id=user_id)
        returns_bys_map = get_branch_list_map(branch_type="store", branch_ids=returns_bys_id,
                                              partner_id=partner_id, user_id=user_id)

        deliverys = receipt_service.list_franchisee_deliverys(batch_ids=batch_ids, partner_id=partner_id,
                                                              user_id=user_id, delivery_code=delivery_code)
        receives = receipt_service.list_franchisee_receives(batch_ids=batch_ids, partner_id=partner_id, user_id=user_id,
                                                            receive_code=receive_code)
        deliverys_map = {}
        receives_map = {}
        for delivery in deliverys:
            deliverys_map[delivery.get("batch_id")] = delivery

        for receive in receives:
            receives_map[receive.get("batch_id")] = receive

        return_list = []
        if return_db_list:
            if not delivery_code and not receive_code:
                for return_db in return_db_list:
                    return_obj = {}
                    franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})

                    # return_obj['id'] = return_db.id
                    # return_obj['franchisee_id'] = return_db.franchisee_id
                    return_obj['product_code'] = return_db.product_code
                    return_obj['product_id'] = return_db.product_id
                    return_obj['product_name'] = return_db.product_name
                    return_obj['unit_spec'] = return_db.unit_spec
                    return_obj['unit_name'] = return_db.unit_name
                    return_obj['price_tax'] = return_db.price_tax
                    return_obj['quantity'] = return_db.quantity
                    return_obj['returns_price'] = return_db.returns_price
                    # return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
                    # return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
                    return_obj['franchisee_name'] = franchisee.get('name')
                    return_obj['franchisee_code'] = franchisee.get('code')
                    return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                    return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                    return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                    return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                    # return_obj['attachments'] = eval(return_obj['attachments']) if return_obj.get('attachments') else []
                    return_list.append(return_obj)
                return count, return_list
            else:
                if delivery_code:
                    return_ids = None
                    for return_id in deliverys_map:
                        return_ids = return_id
                    for return_db in return_db_list:
                        if str(return_db.id) == str(return_ids):
                            return_obj = {}
                            # franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})

                            # return_obj['id'] = return_db.id
                            # return_obj['franchisee_id'] = return_db.franchisee_id
                            return_obj['product_code'] = return_db.product_code
                            return_obj['product_name'] = return_db.product_name
                            return_obj['product_id'] = return_db.product_id
                            return_obj['unit_spec'] = return_db.unit_spec
                            return_obj['unit_name'] = return_db.unit_name
                            return_obj['price_tax'] = return_db.price_tax
                            return_obj['quantity'] = return_db.quantity
                            return_obj['returns_price'] = return_db.returns_price
                            # return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
                            # return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
                            # return_obj['franchisee_name'] = franchisee.get('name')
                            # return_obj['franchisee_code'] = franchisee.get('code')
                            return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                            return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                            return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                            return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                            # return_obj['attachments'] = eval(return_obj['attachments']) if return_obj.get(
                            #     'attachments') else []
                            return_list.append(return_obj)
                elif receive_code:
                    return_ids = None
                    for return_id in receives_map:
                        return_ids = return_id
                    for return_db in return_db_list:
                        if str(return_db.id) == str(return_ids):
                            return_obj = {}
                            # franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})

                            return_obj['id'] = return_db.id
                            # return_obj['franchisee_id'] = return_db.franchisee_id
                            return_obj['product_code'] = return_db.product_code
                            return_obj['product_name'] = return_db.product_name
                            return_obj['product_id'] = return_db.product_id
                            return_obj['unit_spec'] = return_db.unit_spec
                            return_obj['unit_name'] = return_db.unit_name
                            return_obj['price_tax'] = return_db.price_tax
                            return_obj['quantity'] = return_db.quantity
                            return_obj['returns_price'] = return_db.returns_price
                            # return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
                            # return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
                            # return_obj['franchisee_name'] = franchisee.get('name')
                            # return_obj['franchisee_code'] = franchisee.get('code')
                            return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                            return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                            return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                            return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                            # return_obj['attachments'] = eval(return_obj['attachments']) if return_obj.get(
                            #     'attachments') else []
                            return_list.append(return_obj)
                return count, return_list
        return 0, None

    def list_returns_by_product(self, request, partner_id, user_id):
        # TODO: 需要新增receipt单据过滤
        code = request.code  # 门店退货单号
        limit = request.limit
        offset = request.offset
        return_by = request.return_by
        return_to = request.return_to
        status = request.status
        source_code = request.source_code  # 门店收货单
        source_id = request.source_id
        return_date_from = request.return_date_from
        return_date_to = request.return_date_to
        delivery_date_from = request.delivery_date_from
        delivery_date_to = request.delivery_date_to
        type = request.type
        sub_type = request.sub_type
        logistics_type = request.logistics_type
        sort = request.sort
        order = request.order
        payment_ways = request.payment_ways

        # 加盟商新增的相关过滤
        delivery_code = request.delivery_code  # 发货单号
        receive_code = request.receive_code  # 退货入库单号

        product_ids = list(request.product_ids) if request.product_ids else []

        return_bys = []
        for storeId in return_by:
            return_bys.append(int(storeId))

        return_tos = []
        for rt in return_to:
            return_tos.append(int(rt))

        count, return_db_list = FranchiseeReturnModel.list_returns_by_product(partner_id=partner_id, code=code,
                                                                              return_bys=return_bys,
                                                                              status=status,
                                                                              logistics_type=logistics_type, type=type,
                                                                              sub_type=sub_type,
                                                                              source_code=source_code,
                                                                              source_id=source_id,
                                                                              start_date=return_date_from,
                                                                              end_date=return_date_to,
                                                                              delivery_start_date=delivery_date_from,
                                                                              delivery_end_date=delivery_date_to,
                                                                              sort=sort, order=order, limit=limit,
                                                                              offset=offset,
                                                                              return_tos=return_tos,
                                                                              product_ids=product_ids,
                                                                              payment_way=payment_ways, query_type="PD")
        if not return_db_list:
            return 0, []
        franchisee_ids = []
        batch_ids = []
        return_db: ReturnModel
        for return_db in return_db_list:
            if return_db.franchisee_id:
                franchisee_ids.append(return_db.franchisee_id)
            batch_ids.append(return_db.id)

        # franchisee_branch_map = get_branch_map(branch_ids=franchisee_ids, branch_type="FRANCHISEE",
        #                                        partner_id=partner_id,
        #                                        user_id=user_id, return_fields="id,code,name")

        returns_tos_id = [return_db.return_to for return_db in return_db_list]
        # returns_bys_id = [return_db.return_by for return_db in return_db_list]
        returns_tos_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center",
                                              branch_ids=returns_tos_id,
                                              partner_id=partner_id, user_id=user_id)
        # returns_bys_map = get_branch_list_map(branch_type="store", branch_ids=returns_bys_id,
        #                                       partner_id=partner_id, user_id=user_id)

        deliverys = receipt_service.list_franchisee_deliverys(batch_ids=batch_ids, partner_id=partner_id,
                                                              user_id=user_id, delivery_code=delivery_code)
        receives = receipt_service.list_franchisee_receives(batch_ids=batch_ids, partner_id=partner_id, user_id=user_id,
                                                            receive_code=receive_code)
        deliverys_map = {}
        receives_map = {}
        for delivery in deliverys:
            deliverys_map[delivery.get("batch_id")] = delivery

        for receive in receives:
            receives_map[receive.get("batch_id")] = receive
        return_list = []
        if return_db_list:
            if not delivery_code and not receive_code:
                for return_db in return_db_list:
                    return_obj = {}
                    # franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})

                    # return_obj['id'] = return_db.id
                    # return_obj['franchisee_id'] = return_db.franchisee_id
                    return_obj['product_code'] = return_db.product_code
                    return_obj['product_name'] = return_db.product_name
                    return_obj['product_id'] = return_db.product_id
                    return_obj['unit_spec'] = return_db.unit_spec
                    return_obj['unit_name'] = return_db.unit_name
                    return_obj['price_tax'] = return_db.price_tax
                    return_obj['quantity'] = return_db.quantity
                    return_obj['returns_price'] = return_db.returns_price
                    # return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
                    # return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
                    # return_obj['franchisee_name'] = franchisee.get('name')
                    # return_obj['franchisee_code'] = franchisee.get('code')
                    return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                    return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                    # return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                    # return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                    # return_obj['attachments'] = eval(return_obj['attachments']) if return_obj.get('attachments') else []
                    # return_list.append(return_obj)
                return count, return_list
            else:
                if delivery_code:
                    return_ids = None
                    for return_id in deliverys_map:
                        return_ids = return_id
                    for return_db in return_db_list:
                        if str(return_db.id) == str(return_ids):
                            return_obj = {}
                            # franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})

                            # return_obj['id'] = return_db.id
                            # return_obj['franchisee_id'] = return_db.franchisee_id
                            return_obj['product_code'] = return_db.product_code
                            return_obj['product_name'] = return_db.product_name
                            return_obj['product_id'] = return_db.product_id
                            return_obj['unit_spec'] = return_db.unit_spec
                            return_obj['unit_name'] = return_db.unit_name
                            return_obj['price_tax'] = return_db.price_tax
                            return_obj['quantity'] = return_db.quantity
                            return_obj['returns_price'] = return_db.returns_price
                            # return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
                            # return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
                            # return_obj['franchisee_name'] = franchisee.get('name')
                            # return_obj['franchisee_code'] = franchisee.get('code')
                            return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                            return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                            # return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                            # return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                            # return_obj['attachments'] = eval(return_obj['attachments']) if return_obj.get(
                            #     'attachments') else []
                            return_list.append(return_obj)
                    return 1, return_list
                elif receive_code:
                    return_ids = None
                    for return_id in receives_map:
                        return_ids = return_id
                    for return_db in return_db_list:
                        if str(return_db.id) == str(return_ids):
                            return_obj = {}
                            # franchisee = franchisee_branch_map.get(return_db.franchisee_id, {})

                            # return_obj['id'] = return_db.id
                            # return_obj['franchisee_id'] = return_db.franchisee_id
                            return_obj['product_code'] = return_db.product_code
                            return_obj['product_name'] = return_db.product_name
                            return_obj['unit_spec'] = return_db.unit_spec
                            return_obj['price_tax'] = return_db.price_tax
                            return_obj['quantity'] = return_db.quantity
                            return_obj['returns_price'] = return_db.returns_price
                            # return_obj['delivery_code'] = deliverys_map.get(return_db.id, {}).get('code')
                            # return_obj['receive_code'] = receives_map.get(return_db.id, {}).get('code')
                            # return_obj['franchisee_name'] = franchisee.get('name')
                            # return_obj['franchisee_code'] = franchisee.get('code')
                            return_obj['return_to_code'] = returns_tos_map.get(return_db.return_to, {}).get('code')
                            return_obj['return_to_name'] = returns_tos_map.get(return_db.return_to, {}).get('name')
                            # return_obj['return_by_code'] = returns_bys_map.get(return_db.return_by, {}).get('code')
                            # return_obj['return_by_name'] = returns_bys_map.get(return_db.return_by, {}).get('name')
                            # return_obj['attachments'] = eval(return_obj['attachments']) if return_obj.get(
                            #     'attachments') else []
                            return_list.append(return_obj)
                    return 1, return_list
        return 0, None

    def get_return_product_by_id(self, return_id, partner_id, user_id):
        count = 0
        res = {}
        count, return_products_db_list = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                              partner_id=partner_id)
        product_ids = [rp.product_id for rp in return_products_db_list]
        price_map = {}
        if len(return_products_db_list):
            products = products_manage_service.GetAgentProducts(return_products_db_list[0].return_by,
                                                                product_ids=product_ids, partner_id=partner_id,
                                                                user_id=user_id).get("rows", {}).get("list", [])
            for p in products:
                price_map[int(p.get("id"))] = {}
                for pr in p.get("product_price", []):
                    # 含税零售价
                    if pr.get("price_type_id", '0') == '2':
                        price_map[int(p.get("id"))]['retail_price'] = float(pr.get("tax_price", 0))
        return_products_list = []
        total_amount = 0
        if return_products_db_list is not None and len(return_products_db_list) > 0:
            for return_product_db in return_products_db_list:
                return_product_obj = return_product_db.serialize(conv=True)
                return_product_obj["sum_price"] = float(return_product_obj.get("price_tax", 0) * return_product_obj.get(
                    "quantity", 0))
                return_product_obj["return_price"] = float(
                    return_product_obj.get("price_tax", 0) * return_product_obj.get(
                        "quantity", 0))
                total_amount += return_product_obj.get("return_price", 0)
                return_product_obj['attachments'] = handle_attachments(return_product_db.attachments)
                return_product_obj['retail_price'] = price_map.get(return_product_db.product_id, {}).get("retail_price",
                                                                                                         0)
                return_products_list.append(return_product_obj)

        res["total"] = count
        res["rows"] = return_products_list
        res["total_amount"] = total_amount
        return res

    def get_valid_product(self, request, partner_id, user_id):
        # 校验商品是否有配置对应的发货仓库
        order_by_inventory = request.order_by == 'real_inventory'
        category_ids = [int(i) for i in request.category_ids]
        center_rows = products_manage_service.GetDistributionRule(store_ids=[request.store_id],
                                                                  partner_id=partner_id, user_id=user_id,
                                                                  category_ids=category_ids,
                                                                  page_order={"limit": -1}).get("rows", [])
        # logging.info("从订货规则获取到的商品:{}-{}".format(len(center_rows),center_rows))
        # 价格中心商品
        product_ids = [int(cr.get("product_id")) for cr in center_rows if cr.get("distrcenter_id")]

        if request.search:
            search_fields = request.search_fields or 'code,name'
            search_ids = metadata_service.get_product_list(ids=product_ids, search=request.search,
                                                           search_fields=search_fields,
                                                           return_fields='id', partner_id=partner_id,
                                                           user_id=user_id).get('rows', [])
            product_ids = [int(i['id']) for i in search_ids]

        if not product_ids:
            return {}

        valid_product_map = {}
        agent_products = products_manage_service.GetAgentProducts(store_id=request.store_id, product_ids=product_ids,
                                                                  partner_id=partner_id, user_id=user_id,
                                                                  page_order={"limit": -1}).get('rows', [])
        if not agent_products:
            return {}
        # logging.info(f'agent_products: {agent_products}')
        invalid_products = []
        if agent_products and isinstance(agent_products, dict):
            valid_products = agent_products.get('list', [])
            # logging.info("从价格中心获取到的商品:{}-{}".format(len(valid_products), valid_products))
            price_map = {}
            for p in valid_products:
                product_price = p.get('product_price', [])
                tax_price = 0
                for price in product_price:
                    if price.get('price_type_id') == "1":
                        tax_price = float(price.get('tax_price', 0))
                    valid_product_map[convert_to_int(p.get('id'))] = tax_price
                    if price_map.get(convert_to_int(p.get('id'))):
                        price_map[convert_to_int(p.get('id'))].append(price.get('price_type_id'))
                    else:
                        price_map[convert_to_int(p.get('id'))] = [price.get('price_type_id')]
            invalid_products = [k for k, v in price_map.items() if "1" not in v]
        distr_map = {int(c.get("product_id")): int(c.get("distrcenter_id", 0)) for c in center_rows}
        # liumou说加盟模块未维护订货价的商品不允许退货
        valid_products = []
        final_product_ids = []
        valid_products_dict = {}
        for cr in agent_products.get("list", []):
            product_id = int(cr.get("id"))
            if not cr.get("unit_id"):
                continue
            if product_id in invalid_products:
                continue
            vp = dict(
                product_id=product_id,
                product_code=cr.get("code"),
                product_name=cr.get("name"),
                spec=cr.get("spec"),
                tax_price=round(float(valid_product_map.get(product_id, 0)), 2),
                unit={
                    "id": int(cr.get("unit_id", 0)),
                    "name": cr.get("unit_name")
                },
                distr_by=distr_map.get(product_id, 0)
            )
            valid_products.append(vp)
            final_product_ids.append(vp['product_id'])
            valid_products_dict[vp['product_id']] = vp

        # logging.info(f'退货final_product_ids: {final_product_ids}')
        total = 0
        if order_by_inventory and final_product_ids:
            total = len(final_product_ids)
            # 新增按照实时库存排序
            inv_unchanged_products, inv_changed_products = [], []
            order_result = inventory_service.query_branch_inventory_sort(partner_id, user_id, request.store_id,
                                                                         product_ids=final_product_ids,
                                                                         offset=request.offset,
                                                                         limit=request.limit).get('rows')
            # logging.info(f'退货库存排序：{order_result}')
            if order_result:
                for v in order_result:
                    tmp_record = valid_products_dict.get(int(v['product_id']))
                    if tmp_record:
                        tmp_record['real_inventory_qty'] = v.get('qty')
                        inv_changed_products.append(tmp_record)
                return {"rows": inv_changed_products, 'inventory_unchanged_rows': inv_unchanged_products,
                        "total": total}

        return {"rows": valid_products}

    def get_history(self, partner_id, user_id, request):
        return_id = request.id
        return_logs, total = ReturnLogModel.get_by_doc_id(partner_id=partner_id, doc_id=return_id)
        log_list = []
        for log in return_logs:
            log_detail = {
                'id': log.id,
                'status': log.operation,
                'updated_by': log.created_by,
                'updated_by_name': log.created_name,
                'updated_at': Timestamp(seconds=int(log.updated_at.timestamp())),
            }
            log_list.append(log_detail)
        return log_list, total

    def approve_return(self, return_id, partner_id, user_id, trans_type=None, warehouse_type=None, long_effect=False):
        """
        审核退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_products_db = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                         partner_id=partner_id)
        if not count:
            raise NoResultFoundError("没有对应退货单!")

        if return_db.status != 'SUBMITTED':
            if return_db.logistics_type == 'NMD':
                raise StatusUnavailable("只有已提交的退货单可以审核!")

        # 增加库存校验：是否允许审核退货
        product_detail_list = []
        for return_product_db in return_products_db:
            product_detail = {}
            product_detail['product_id'] = return_product_db.product_id
            product_detail['quantity'] = return_product_db.quantity
            product_detail['unit_rate'] = return_product_db.unit_rate
            product_detail_list.append(product_detail)
        self.check_return_available(store_id=return_db.return_by, product_detail_list=product_detail_list,
                                    partner_id=partner_id, user_id=user_id,
                                    sub_type=return_db.sub_type, logistics_type=return_db.logistics_type,
                                    action="approve")

        # 需要提货
        if trans_type == ReturnTransType.NeedPickUp.code and (long_effect=="Y" or long_effect=="N"):
            args = {
                'updated_at': datetime.utcnow(),
                'updated_name': operator_name,
                'updated_by': user_id,
                'review_by': user_id,
                'inventory_status': 'INITED',
                'trans_type': trans_type,
                'long_effect': long_effect
            }
            return_db.update_returns(return_id, args, partner_id)
            # vendor单据同步给三方
            long_effect_req = long_effect
            if not long_effect_req:
                long_effect_req = return_db.long_effect
            remark_dict = dict(
                trans_type=trans_type,
                long_effect=long_effect_req
            )
            logging.info("ApproveReturn_remark_dict:{}".format(remark_dict))
            remark = json.dumps(remark_dict)
            entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                delivery_detail = rows[0]
                receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='SUBMITTED-JJY',
                                                  partner_id=partner_id, user_id=user_id, remark=remark)
            if long_effect=="Y":
                # 记录操作日志
                log_detail = {}
                log_detail['doc_id'] = return_id
                log_detail['operation'] = 'REVIEWED'
                log_detail['success'] = True
                log_detail['partner_id'] = partner_id
                log_detail['created_by'] = user_id
                log_detail['created_at'] = datetime.utcnow()
                log_detail['updated_at'] = datetime.utcnow()
                log_detail['created_name'] = operator_name
                ReturnLogModel.create_returns_log(**log_detail)
                return True
        args = {
            'status': 'APPROVED' if return_db.type != "IAD" else 'CONFIRMED',
            'updated_at': datetime.utcnow(),
            'updated_name': operator_name,
            'updated_by': user_id,
            'review_by': user_id,
            'inventory_status': 'INITED',
            'trans_type': trans_type,
            'warehouse_type': warehouse_type
        }
        if long_effect:
            args['long_effect'] = long_effect
        return_db.update_returns(return_id, args, partner_id)

        product_update_list = []
        for return_product_db in return_products_db:
            p_args = {
                'id': return_product_db.id,
                'status': 'APPROVED' if return_db.type != "IAD" else 'CONFIRMED',
                # 'status':'CONFIRMED',
                'updated_at': datetime.utcnow(),
                'updated_name': operator_name,
                'updated_by': user_id,
                'review_by': user_id,
                'inventory_status': 'INITED',
            }
            if trans_type == ReturnTransType.NeedPickUp.code and not long_effect:
                p_args.update({"status": "SUBMITTED"})
            product_update_list.append(p_args)
        ReturnProductModel.update_products_in_all(updated_products=product_update_list)

        # 门店退货
        try:
            if return_db.sub_type in ('store', 'fs_store'):
                # source_type = 'RETURN'
                today = datetime.today()
                # 状态同步receipt单据
                ####会存在数据库没更新，消息已经发集成了，直接带信息
                long_effect_req = long_effect
                if not long_effect_req:
                    long_effect_req = return_db.long_effect
                remark_dict = dict(
                    trans_type=trans_type,
                    long_effect=long_effect_req
                )
                logging.info("ApproveReturn_remark_dict:{}".format(remark_dict))
                remark = json.dumps(remark_dict)
                entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if rows:
                    delivery_detail = rows[0]
                    if return_db.type == 'IAD':  # 2021-07-27 库存调整退货单不自动推进，改由审核退货单触发
                        receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='AUTO_DELIVERY',
                                                            partner_id=partner_id, user_id=user_id)
                    else:
                        receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='APPROVED',
                                                            partner_id=partner_id, user_id=user_id,remark=remark)

                entity = receipt_service.list_receives(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if not rows:
                    raise NoResultFoundError('NoResultFound!')
                receive_detail = rows[0]
                if return_db.type == 'IAD':  # 2021-07-27 库存调整退货单不自动推进，改由审核退货单触发
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='AUTO_RECEIVE',
                                                       partner_id=partner_id, user_id=user_id)
                    self.confirm_return(return_id=return_id, partner_id=partner_id, user_id=user_id)
                else:
                    # 审核之后，仓库退货收货单被激活，状态同步给仓库
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='ACTIVATE',
                                                       partner_id=partner_id, user_id=user_id)
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                                 doc_type="return"))
            # 仓库/加工中心采购退货
            else:
                # 状态同步receipt单据
                entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if rows:
                    delivery_detail = rows[0]
                    ####会存在数据库没更新，消息已经发集成了，直接带信息
                    # long_effect_req = long_effect
                    # if not long_effect_req:
                    #     long_effect_req = return_db.long_effect
                    # remark_dict = dict(
                    #     trans_type=trans_type,
                    #     long_effect=long_effect_req
                    # )
                    # logging.info("ApproveReturn_remark_dict:{}".format(remark_dict))
                    # remark = json.dumps(remark_dict)
                    receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='APPROVED',
                                                        partner_id=partner_id, user_id=user_id)
        except Exception as e:
            raise DataValidationException("审核退货单, 同步状态失败, 请重试！")
        batch_id = 0
        if return_db.source_id:
            rec_db = receipt_service.get_receive_by_id(receive_id=return_db.source_id, partner_id=partner_id,
                                                       user_id=user_id)
            batch_id = rec_db.batch_id
        # 接入退款单
        refund_id = get_guid()

        # 配送方式接入退款
        if (return_db.logistics_type == DISTRIBUTION_TYPE and trans_type != ReturnTransType.NeedPickUp.code
                and (trans_type == ReturnTransType.NeedPickUp.code and long_effect == 'N')):
            refund = Refund(refund_id, partner_id, user_id, operator_name)
            product_list = [p.serialize(conv=True) for p in return_products_db]
            refund_products, total_amount = refund.get_refund_products(products=product_list, main_type='FRS_RETURN')
            refund_detail = refund.get_return_refund_detail(
                detail=return_db, refund_amount=total_amount, main_type='FRS_RETURN', refund_type='RETURN_REFUND',
                master_id=batch_id)
            refund_log = refund.get_refund_log()
            db_session = session_maker()
            SupplyFranchiseeRefund.create_franchisee_refund(
                db_session=db_session, refund_details=[refund_detail], refund_products=refund_products,
                refund_logs=[refund_log]
            ) if total_amount else ...
        # 直送方式暂不接入退款
        else:
            pass

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'REVIEWED'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['updated_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        if trans_type == ReturnTransType.NeedPickUp.code and return_db.long_effect == "Y":
            log_detail['operation'] = 'APPROVE'
        ReturnLogModel.create_returns_log(**log_detail)

        if trans_type and trans_type == ReturnTransType.RefundOnly.code and return_db.sub_type in ('store', 'fs_store'):
            self.delivery_return(return_id=return_id, partner_id=partner_id, \
                                 user_id=user_id, trans_type=trans_type)

        return True

    def submit_return(self, return_id, partner_id, user_id):
        """
        提交退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_products_db = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                         partner_id=partner_id)
        if not count:
            raise NoResultFoundError("no result found!")
        # 时间校验
        now = datetime.now(timezone.utc)
        time_config_map, allow_specified_time = metadata_service.get_time_config(partner_id, user_id,
                                                                                 "boh.store.return",
                                                                                 store_id=return_db.return_by)
        if return_db.type in ('BO', 'NBO') and return_db.sub_type in (
        'store', 'fs_store') and allow_specified_time and time_config_map.get(return_db.return_by):
            start = time_config_map.get(return_db.return_by)[0]
            end = time_config_map.get(return_db.return_by)[1]
            # if start <= now <= end:
            if now < start or now > end:
                raise DataValidationException("时间限制, 不允许提交退货单")

        # 增加库存校验
        product_detail_list = []
        for return_product_db in return_products_db:
            product_detail = {}
            product_detail['product_id'] = return_product_db.product_id
            product_detail['product_name'] = return_product_db.product_name
            product_detail['quantity'] = return_product_db.quantity
            product_detail['unit_rate'] = return_product_db.unit_rate
            product_detail_list.append(product_detail)
        # 如果是原单退货，校验是否超过收货数量
        if return_db.type == 'BO':
            self.if_over_return_by_rec(source_code=return_db.source_code,
                                       product_detail=product_detail_list, partner_id=partner_id,
                                       user_id=user_id, return_id=return_db.id)

        # 实时库存是否允许退货
        self.check_return_available(store_id=return_db.return_by, product_detail_list=product_detail_list,
                                    partner_id=partner_id, user_id=user_id,
                                    sub_type=return_db.sub_type, logistics_type=return_db.logistics_type,
                                    action="submit")

        # 状态同步receipt单据
        today = datetime.today()
        try:
            entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                delivery_detail = rows[0]
                receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='SUBMITTED',
                                                    partner_id=partner_id, user_id=user_id)

            entity = receipt_service.list_receives(batch_id=return_id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                receive_detail = rows[0]
                receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='SUBMITTED',
                                                   partner_id=partner_id, user_id=user_id)
        except Exception as e:
            raise DataValidationException("同步单据状态出错, 请重试！")

        if return_db.status == 'INITED' or return_db.status == 'REJECTED':
            args = {
                'status': 'SUBMITTED',
                'updated_at': datetime.utcnow(),
                'updated_by': user_id,
                'updated_name': operator_name,
                'review_by': user_id,
            }
            return_db.update_returns(return_id, args, partner_id)

            product_update_list = []
            for return_product_db in return_products_db:
                p_args = {
                    'id': return_product_db.id,
                    'status': 'SUBMITTED',
                    'updated_at': datetime.utcnow(),
                    'updated_by': user_id,
                    'updated_name': operator_name
                }
                product_update_list.append(p_args)
            ReturnProductModel.update_products_in_all(updated_products=product_update_list)

            if return_db.logistics_type == 'NMD':
                # PDA消息推送
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                      user_id=user_id,
                                                      scope_id=1,
                                                      store_id=return_db.return_by,
                                                      source_root_id=return_db.id,
                                                      source_id=return_db.id,
                                                      source_type="RETURN",
                                                      action="SUBMITTED",
                                                      ref_source_id=return_db.id,
                                                      ref_source_type="RETURN",
                                                      ref_action="INITED",
                                                      content={
                                                          "store_name": metadata_service.get_store(return_db.return_by,
                                                                                                   partner_id=partner_id,
                                                                                                   user_id=user_id).get(
                                                              'name'),
                                                          "return_delivery_date": str(return_db.return_delivery_date),
                                                          "return_reason": return_db.return_reason,
                                                          "remark": return_db.remark,
                                                          "updated_at": str(datetime.utcnow()),
                                                          "updated_by": str(user_id),
                                                          "updated_name": operator_name}
                                                      )
            else:
                # PDA消息推送
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                      user_id=user_id,
                                                      scope_id=1,
                                                      store_id=return_db.return_by,
                                                      source_root_id=return_db.id,
                                                      source_id=return_db.id,
                                                      source_type="RETURN_C",
                                                      action="SUBMITTED",
                                                      ref_source_id=return_db.id,
                                                      ref_source_type="RETURN_C",
                                                      ref_action="INITED",
                                                      content={
                                                          "store_name": metadata_service.get_store(return_db.return_by,
                                                                                                   partner_id=partner_id,
                                                                                                   user_id=user_id).get(
                                                              'name'),
                                                          "return_delivery_date": str(return_db.return_delivery_date),
                                                          "return_reason": return_db.return_reason,
                                                          "remark": return_db.remark,
                                                          "updated_at": str(datetime.utcnow()),
                                                          "updated_by": str(user_id),
                                                          "updated_name": operator_name}
                                                      )

            if return_db.sub_type in ('store', 'fs_store'):
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                                 doc_type="return"))

            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = return_id
            log_detail['operation'] = 'SUBMIT'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.utcnow()
            log_detail['updated_at'] = datetime.utcnow()
            log_detail['created_name'] = operator_name
            ReturnLogModel.create_returns_log(**log_detail)

            return True
        else:
            raise StatusUnavailable("only INITED/ REJECTED data can be submitted!")

    # 创建配送差异仓库承担产生的退单
    def create_diff_return(self, return_by, return_to,
                           return_delivery_date, return_reason,
                           product_detail, partner_id, user_id,
                           source_id, source_code, logistics_type,
                           remark=None, franchisee_id=None, diff_type=None,
                           receiving_diff_id=None):
        '''
        配送差异仓库承担部分自动退单
        '''
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        return_date = datetime.utcnow()
        diff_ret_id = gen_snowflake_id()
        diff_ret_code = Supply_doc_code.get_code_by_type('RETURN_OD', partner_id, None)

        # 创建退货单商品
        product_nums = 0
        product_code_list = []
        for product in product_detail:
            product_code_list.append(product['product_code'])

        # 转换单位
        product_unit_dict = {}
        products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                          include_units=True,
                                                          return_fields='id,code,name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        for product_detail_info in products_info:
            if product_detail_info.get('units'):
                for i in product_detail_info['units']:
                    if i.get('order') and i['order'] == True:
                        product_unit_dict[product_detail_info['id']] = i
                    else:
                        logging.warning("没有设置订货单位, product_id:{}".format(product_detail_info['id']))
            else:
                logging.warning("没有订货单位, product_id:{}".format(product_detail_info['id']))

        delivery_p_list = []
        rec_p_list = []
        product_insert_list = []
        for product in product_detail:
            product_nums += 1
            product['id'] = gen_snowflake_id()
            product['return_id'] = diff_ret_id
            product['return_by'] = return_by
            product['return_date'] = return_date
            product['status'] = 'CONFIRMED'
            product['partner_id'] = partner_id
            product['created_by'] = user_id
            product['updated_by'] = user_id
            product['updated_name'] = operator_name
            product['created_name'] = operator_name
            product['created_at'] = datetime.utcnow()
            product['updated_at'] = datetime.utcnow()

            unit = product_unit_dict[str(product['product_id'])]
            if unit:
                product['unit_id'] = unit['id']
                # product['unit_name'] = unit['name']
                # product['unit_spec'] = unit['code']
                product['unit_rate'] = unit['rate']
            product_insert_list.append(product)

            delivery_p = {
                'delivery_by': int(return_by),
                'product_id': int(product.get('product_id')),
                'product_code': product.get('product_code'),
                'product_name': product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': float(product.get('quantity')),
                'order_quantity': float(product.get("quantity")),
                'unit_id': int(product.get('unit_id')),
                'unit_name': product.get('unit_name'),
                'unit_rate': float(product.get('unit_rate')),
                'cost_price': product.get('price'),
                'tax_price': product.get('price_tax'),
                'tax_rate': product.get('tax_rate'),
                'unit_spec': product.get('unit_spec'),
                # 'category_id':product.category_id
            }
            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by': int(return_to),
                'product_id': int(product.get('product_id')),
                'product_code': product.get('product_code'),
                'product_name': product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': float(product.get('quantity')),
                'order_quantity': float(product.get("quantity")),
                'unit_id': int(product.get('unit_id')),
                'unit_name': product.get('unit_name'),
                'unit_rate': float(product.get('unit_rate')),
                'cost_price': product.get('price'),
                'tax_price': product.get('price_tax'),
                'tax_rate': product.get('tax_rate'),
                'unit_spec': product.get('unit_spec'),
                # 'category_id':product.category_id
            }
            rec_p_list.append(rec_p)
        # ---ForReceiptStart---#

        ReturnProductModel.create_returns_products(product_insert_list)

        batch_type = 'DIFF_RETURN'
        # 退货单创建到发货单
        today = datetime.today()
        receipt_service.create_deliverys(
            demand_type='BD',
            batch_id=source_id, batch_code=source_code, batch_type=batch_type,
            # id=None, code=None,
            order_id=diff_ret_id, order_code=diff_ret_code,
            demand_id=diff_ret_id, demand_code=diff_ret_code,
            # receive_id=None, receive_code=None,
            receive_by=int(return_to), delivery_by=return_by,
            distr_type=logistics_type,
            delivery_date=return_delivery_date, demand_date=return_date,
            # arrival_date=None,  expect_date=None,
            # storage_type=None,
            products=delivery_p_list, main_branch_type='FS',
            partner_id=partner_id, user_id=user_id,
            franchisee_id=franchisee_id)

        # ---ForReceiptEnd---#

        args = {
            'id': diff_ret_id,
            'code': diff_ret_code,
            'return_by': return_by,
            'return_delivery_date': return_delivery_date,
            'return_reason': return_reason,
            'status': 'CONFIRMED',  # 直接设置为最终状态
            'type': 'BD',  # 收货差异产生
            'sub_type': 'fs_store',
            'source_id': source_id,
            'source_code': source_code,
            'partner_id': partner_id,
            'created_at': datetime.utcnow(),
            'created_by': user_id,
            'updated_at': datetime.utcnow(),
            'updated_name': operator_name,
            'created_name': operator_name,
            'updated_by': user_id,
            'return_to': return_to,
            'logistics_type': logistics_type,
            'product_nums': product_nums
        }
        if remark:
            args.update(dict(remark=json.dumps(remark)))
        # 创建退货单
        ReturnModel.create_returns(**args)

        # 调用库存引擎，按照仓库承担数量减少门店库存 —— 创建前已经扣了，所以不重复扣
        diff_args = {}
        if diff_type == 'HC':
            description = 'mcu receiving diff'
            # receiving_diff_db = ReceivingDiffModel.get(receiving_diff_id)
            result = self.withdraw_inventory(diff_ret_id, description, partner_id, user_id, code='DIFF_RETURN')
            if result and result.get('id'):
                diff_args = {'inventory_req_id': result['id'], 'inventory_status': result['status']}
                # receiving_diff_db.update(**args)
        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = diff_ret_id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['updated_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return diff_ret_id, diff_args

    # 更新
    def update_return_product_quantity(self, return_id, partner_id, user_id,
                                       products=None, return_reason=None, remark=None,
                                       return_delivery_date=None, return_to=None,
                                       attachments=None, logistics_type=None):
        """
        更新退货单及退货商品详情
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        product_id_list = []
        str_product_id_list = []
        returns_db = ReturnModel.get_return_by_id(return_id, partner_id)
        if not return_to:
            return_to = returns_db.return_to
        if products and len(products) > 0:
            for product in products:
                product_id_list.append(int(product.product_id))
                str_product_id_list.append(str(product.product_id))

            product_dict = {}
            product_unit_dict = {}
            product_meta_details = metadata_service.get_product_list(ids=product_id_list,
                                                                     include_units=True,
                                                                     return_fields='id,code,name,model_name',
                                                                     partner_id=partner_id, user_id=user_id).get('rows',
                                                                                                                 [])
            product_spec_dict = {}
            for product_detail_info in product_meta_details:
                product_spec_dict[int(product_detail_info['id'])] = product_detail_info.get('model_name')
                product_dict[int(product_detail_info['id'])] = product_detail_info
                if product_detail_info.get('units'):
                    product_unit_dict[product_detail_info['id']] = {}
                    for unit in product_detail_info.get('units'):
                        if unit.get('purchase') and unit['purchase'] == True:
                            product_unit_dict[product_detail_info['id']]['purchase'] = unit['rate'] if unit.get(
                                'rate') else 0
                        if unit.get('order') and unit['order'] == True:
                            product_unit_dict[product_detail_info['id']]['order'] = unit['rate'] if unit.get(
                                'rate') else 0
                        if unit.get('default') and unit['default'] == True:
                            product_unit_dict[product_detail_info['id']]['default'] = unit['rate'] if unit.get(
                                'rate') else 0
                else:
                    product_unit_dict[product_detail_info['id']]['default'] = 0

            unit_dict = {}
            unit_details = metadata_service.get_unit_list(partner_id=partner_id, user_id=user_id).get('rows', [])
            for unit_detail in unit_details:
                unit_dict[int(unit_detail['id'])] = unit_detail
            # 直送单需要拿到价格，获取商品成本
            products_list_map = {}
            if returns_db.type == 'NBO':
                products_list = products_manage_service.ListAgentProducts(
                    store_id=returns_db.return_by, product_ids=[int(i) for i in product_id_list])
                products_list_map = {int(product.get(
                    "id")): product for product in products_list}

            new_product_list = []
            update_product_list = []
            delete_product_list = []

            count = 0
            for product in products:
                count += 1
                attachment = handle_request_attachments(product.attachments) if product.attachments else ['']
                product = pb2dict(product)
                product['attachments'] = attachment
                product_id = product.get('product_id', 0)
                if not product.get('id'):
                    product['id'] = gen_snowflake_id()
                    product['return_id'] = return_id
                    product['return_by'] = returns_db.return_by
                    product['return_to'] = return_to
                    product['return_date'] = returns_db.return_date
                    product['status'] = 'INITED'
                    product['partner_id'] = partner_id
                    product['created_by'] = user_id
                    product['updated_name'] = operator_name
                    product['created_name'] = operator_name
                    product['created_at'] = datetime.utcnow()
                    product['updated_at'] = datetime.utcnow()
                    product['returned_quantity'] = product['quantity']
                    product_meta_detail = product_dict.get(int(product_id))
                    if not product_meta_detail:
                        raise NoResultFoundError("未找到商品主档-product_id-{}".format(convert_to_int(product_id)))
                    product['product_name'] = product_meta_detail['name']
                    product['product_code'] = product_meta_detail['code']
                    unit_detail = unit_dict.get(int(product['unit_id']))
                    if not unit_detail:
                        raise NoResultFoundError(
                            "未找到商品单位-unit_id-{}".format(convert_to_int(product['unit_id'])))
                    product['unit_name'] = unit_detail['name']
                    product['unit_spec'] = product_spec_dict.get(int(product_id))  # 用作商品规格
                    if returns_db.type == 'BO':  # 原单价格从收货单里取，不必做转换
                        product['price'] = float(product['price_tax'] / (1 + product['tax_rate'] / 100))
                    else:
                        pp = products_list_map.get(int(product_id), {})
                        product['tax_rate'] = float(pp.get("tax_ratio", 0))
                        product['price_tax'] = float(pp.get("tax_price", 0))
                        product['price'] = float(product['price_tax'] / (1 + product['tax_rate'] / 100))

                    self.check_product_attachments(product)
                    new_product_list.append(product)
                else:
                    if product['quantity'] == 0:
                        delete_product_list.append(product['id'])
                    else:
                        product_db = ReturnProductModel.get(product['id'])
                        product['unit_spec'] = product_spec_dict.get(int(product['product_id']))  # 用作商品规格
                        product['unit_rate'] = product_db.unit_rate
                        product['updated_by'] = user_id
                        product['updated_at'] = datetime.utcnow()
                        product['tax_rate'] = product_db.tax_rate
                        product['price'] = product_db.price
                        product['price_tax'] = product_db.price_tax
                        product['returned_quantity'] = product['quantity']
                        product.pop('product_id')
                        self.check_product_attachments(product)
                        update_product_list.append(product)

            ReturnProductModel.create_returns_products(
                return_product_list=new_product_list,
                updated_product_list=update_product_list,
                delete_product_list=delete_product_list)

        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        p_total, p_db = ReturnProductModel.list_return_products_by_return_id(return_id, partner_id=partner_id)
        args = {
            'product_nums': p_total,
            'updated_at': datetime.utcnow(),
            'updated_by': user_id,
            'updated_name': operator_name
        }
        if return_reason:
            args['return_reason'] = return_reason
        if remark:
            args['remark'] = remark
        expect_date = return_delivery_date
        if return_delivery_date and isinstance(return_delivery_date, Timestamp) and return_delivery_date.seconds:
            timestamp = Timestamp()
            timestamp.seconds = return_delivery_date.seconds
            return_delivery_date = timestamp.ToDatetime()
            args['return_delivery_date'] = return_delivery_date
        if return_to:
            args['return_to'] = return_to
        if attachments:
            args['attachments'] = handle_request_attachments(attachments)
        if logistics_type:
            args['logistics_type'] = logistics_type
        if args:
            return_db.update_returns(return_id, args, partner_id)

        delivery_p_list = []
        rec_p_list = []
        for product in p_db:
            # ---ForReceiptStart---#
            # ForReceiptDelivery
            delivery_p = {
                'delivery_by': return_db.return_by,
                'product_id': product.product_id,
                'product_code': product.product_code,
                'product_name': product.product_name,
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.quantity,
                'order_quantity': product.quantity,
                'unit_id': product.unit_id,
                'unit_name': product.unit_name,
                'unit_rate': product.unit_rate,
                'cost_price': product.price,
                'tax_price': product.price_tax,
                'tax_rate': product.tax_rate,
                'unit_spec': product.unit_spec,
                # 'category_id':product.category_id
            }
            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by': int(return_db.return_to),
                'product_id': product.product_id,
                'product_code': product.product_code,
                'product_name': product.product_name,
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.quantity,
                'order_quantity': product.quantity,
                'unit_id': product.unit_id,
                'unit_name': product.unit_name,
                'unit_rate': product.unit_rate,
                'cost_price': product.price,
                'tax_price': product.price_tax,
                'tax_rate': product.tax_rate,
                'unit_spec': product.unit_spec,
                # 'category_id':product.category_id
            }
            rec_p_list.append(rec_p)
        logging.info('Start Update Receipt.Receive')
        try:
            receipt_service.update_receives_products(
                batch_id=return_db.id, order_id=return_db.id,
                deal_products=rec_p_list, partner_id=partner_id, user_id=user_id,
                reason=return_reason, remark=remark, distr_type=logistics_type, receive_by=return_to,
                expect_date=expect_date)
            receipt_service.update_deliverys_products(
                batch_id=return_db.id, order_id=return_db.id,
                deal_products=delivery_p_list, partner_id=partner_id, user_id=user_id,
                reason=return_reason, remark=remark, distr_type=logistics_type, receive_by=return_to,
                expect_date=expect_date)
        except Exception as e:
            raise DataValidationException("同步更新退货收货单数据失败, 请重试！")

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'UPDATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['updated_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return True

    def get_return_category_by_branch_id(self, store_id, partner_id=None, user_id=None, is_franchisee=False,
                                         distr_type=None):
        """以下的代码是copy的调拨的，只是去除一些筛选"""
        if is_franchisee:
            # 商品分类同 可退货商品查询的过滤条件一致
            center_rows = products_manage_service.GetDistributionRule(store_ids=[store_id],
                                                                      partner_id=partner_id, user_id=user_id,
                                                                      page_order={"limit": -1}).get("rows", [])
            product_ids = [int(cr.get("product_id")) for cr in center_rows if cr.get("distrcenter_id")]
            data = products_manage_service.GetAgentProducts(store_id=store_id,
                                                            partner_id=partner_id, user_id=user_id,
                                                            product_ids=product_ids,
                                                            page_order={"limit": -1}).get('rows', {}).get('list', [])

            if not data:
                return []

            data = [{'category': i['product_category_id'], 'category_name': i.get('product_category_name')}
                    for i in data if 'product_category_id' in i]

        else:
            # 门店退货商品
            # 先查分类
            category_list = metadata_service.get_product_category_list(filters={"status__in": ["ENABLED"]},
                                                                       partner_id=partner_id,
                                                                       user_id=user_id).get('rows', [])

            category_dict = {i['id']: i.get('name') for i in category_list}
            product_relation_filters = {"product_category": [str(i) for i in category_dict]}

            product_filters = {"status__eq": "ENABLED",
                               "bom_type__neq": "MANUFACTURE",
                               "allow_order__eq": True}

            data = metadata_service.get_list_valid_product_for_distr_by_id(store_id,
                                                                           product_relation_filters=product_relation_filters,
                                                                           include_product_fields='category',
                                                                           product_filters=product_filters,
                                                                           partner_id=partner_id,
                                                                           user_id=user_id).get('rows', [])

            if not data:
                return []

            data = [{'category': i['category'], 'category_name': category_dict.get(i['category'])} for i in data
                    if 'category' in i]

        # list_store_product_ret = metadata_service.list_region_product_by_store(
        #     store_id=store_id,
        #     partner_id=partner_id,
        #     product_filters={
        #         "status": "ENABLED",
        #         "bom_type__neq": 'MANUFACTURE'
        #     },
        #     user_id=user_id,
        #     include_product_units=False,
        #     include_product_fields='category,category_name',
        #     can_order=True,
        #     region="ATTRIBUTE_REGION"
        # )
        store_product_category_ids = []
        store_product_category_names = {}
        store_product_category_product_count = {}
        for product in data:
            if product.get('category'):
                category_id = int(product.get('category'))
                if category_id not in store_product_category_product_count:
                    store_product_category_ids.append(category_id)
                    store_product_category_names[category_id] = product.get('category_name')
                    store_product_category_product_count[category_id] = 1
                else:
                    store_product_category_product_count[category_id] = store_product_category_product_count[
                                                                            category_id] + 1

        category_list = metadata_service.get_product_category_list(
            ids=store_product_category_ids if len(store_product_category_ids) == 1 else None,
            return_fields="id,parent_id", partner_id=partner_id,
            user_id=user_id).get('rows', []) if store_product_category_ids else []
        category_children_map = {}
        for category in category_list:
            category_id = int(category["id"])
            category_parent_id = int(category.get("parent_id", 0))
            if category_parent_id:
                if category_parent_id in category_children_map:
                    if category_id not in category_children_map[category_parent_id]:
                        category_children_map[category_parent_id].append(category_id)
                else:
                    category_children_map[category_parent_id] = [category_id]
        store_product_category_product_count_sum = {}
        category_children_ret_map = {}
        for category_id in store_product_category_ids:
            store_product_category_product_count_sum[category_id] = store_product_category_product_count[category_id]
            for category_child_id in get_category_children(category_id, category_children_map,
                                                           category_children_ret_map):
                store_product_category_product_count_sum[category_id] += store_product_category_product_count.get(
                    category_child_id, 0)
        return [{'category_id': category_id,
                 'category_name': store_product_category_names[category_id],
                 'product_count': store_product_category_product_count_sum[category_id]}
                for category_id in store_product_category_ids]


franchisee_returns_service = FranchiseeReturn()
