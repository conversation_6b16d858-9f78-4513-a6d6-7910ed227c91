from supply.client.third_party import third_party
from supply.model.demand.supply_demand import Supply_demand



class ThirdPartyService(object):
    """第三方相关服务"""
    __third_party = third_party

    @classmethod
    def get_product_price(self, product_id_list=None,
                          partner_id=None, user_id=None, store_id=None,
                          vendor_id=None
                          ):
        """
        第三方获取价格
        """
        products_info = {}
        if product_id_list:
            products_info = self.__third_party.get_product_price(
                store_id, productId=product_id_list)
        return products_info

    @classmethod
    def get_statements(self, iGjahr, iMonat, iKunnr, iBukrs):
        statements = self.__third_party.get_statements(
            iGjahr, iMonat, iKunnr, iBukrs)
        return statements

    @staticmethod
    def get_demand_by_code(code, partner_id):
        demand = {}
        collection = Supply_demand.get_demand_by_code(demand_code=code, partner_id=partner_id)
        if collection:
            demand['code'] = collection.code
            demand['id'] = collection.id
            demand['type'] = collection.type
            demand['expect_date'] = collection.expect_time
            demand['remark'] = collection.remark
        return demand
