# -*- coding: utf8 -*-
import json
import logging

from datetime import datetime
from supply.utils.snowflake import gen_snowflake_id
from supply.model.supply_doc_code import Supply_doc_code
from supply.error.exception import DataValidationException, OrderNotExistException, DataDuplicationException, \
    StatusUnavailable
from supply.client.metadata_service import metadata_service
from supply.model.purchase_review import purchase_review_order_db, PurchaseReviewOrder
from google.protobuf.timestamp_pb2 import Timestamp
from supply.utils.helper import convert_to_int, get_uuids, convert_to_decimal, get_cost_center_map, get_guid
from supply.module.price_adjustment import price_adjustment_service
from ..model.price_adjustment import price_adjustment_db
from ..utils.encode import CJsonEncoder


class PurchaseReviewService(object):
    """采购复核相关业务逻辑处理"""

    def __init__(self):
        pass

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    def create_purchase_review_order(self, request, partner_id=None, user_id=None):
        """创建采购复核单"""
        result = {}
        received_date = datetime.fromtimestamp(request.received_date.seconds)
        if received_date == 1970:
            raise DataValidationException("请传入收货日期")
        query_order = purchase_review_order_db.get_purchase_order_by_id(batch_id=request.batch_id,
                                                                        partner_id=partner_id)
        if query_order:
            raise DataDuplicationException("重复请求！")

        products = request.products
        if not products:
            raise DataValidationException("订单必须包含商品")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 在supply_doc_code_config_main表中新建一条type为PRC_RW的记录
        order_code = Supply_doc_code.get_code_by_type('PRC_RW', partner_id, user_id)
        order_id = gen_snowflake_id()
        # 拉取成本中心
        cost_center_id = get_cost_center_map(branch_type=request.branch_type, branch_id=request.received_by,
                                             partner_id=partner_id, user_id=user_id, return_cost=True)
        if not cost_center_id:
            raise Exception("该组织没有配置成本中心-{}".format(request.received_name))
        order_data = dict(
            id=order_id,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            batch_id=request.batch_id,
            order_code=order_code,
            order_type=request.order_type,
            status="INITED",
            received_date=received_date,
            received_by=request.received_by,
            received_name=request.received_name,
            received_code=request.received_code,
            supplier_id=request.supplier_id,
            supplier_name=request.supplier_name,
            supplier_code=request.supplier_code,
            is_modify=0,  # 默认未作修改
            branch_type=request.branch_type,
            company_id=request.company_id,
            origin_code=request.origin_code,
            is_adjust=request.is_adjust,
            source_type=request.source_type,
            cost_center_id=cost_center_id
        )
        sum_price_tax = convert_to_decimal(0)
        sum_price = convert_to_decimal(0)
        product_list = []
        ids = get_uuids(len(products))
        for index, product in enumerate(products):
            price = convert_to_decimal(product.price_tax) / convert_to_decimal(1 + product.tax_rate / 100)
            sum_price_tax_tmp = convert_to_decimal(product.price_tax) * convert_to_decimal(product.actual_quantity)
            # 未税合计 = 含税合计 /（1 + 税率）
            sum_price_tmp = sum_price_tax_tmp / convert_to_decimal(1 + product.tax_rate / 100)
            sum_price += sum_price_tmp
            sum_price_tax += sum_price_tax_tmp
            row = dict(
                id=ids[index],
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username,
                order_id=order_id,
                product_id=product.product_id,
                product_code=product.product_code,
                product_name=product.product_name,
                product_category_id=product.product_category_id,
                product_type=product.product_type,
                unit_id=product.unit_id,
                unit_name=product.unit_name,
                unit_spec=product.unit_spec,
                spec=product.spec,
                quantity=product.quantity,
                actual_quantity=product.actual_quantity,
                tax_rate=product.tax_rate,
                price=price,
                orig_price=price,
                sum_price=sum_price_tmp,
                orig_sum_price=sum_price_tmp,
                price_tax=product.price_tax,
                orig_price_tax=product.price_tax,
                prev_price_tax=product.price_tax,
                sum_price_tax=sum_price_tax_tmp,
                orig_sum_price_tax=sum_price_tax_tmp,
                is_modify=0   # 默认未作修改
            )
            product_list.append(row)
        # 采购复核创建时计算总价改为通过单价计算
        order_data['sum_price_tax'] = sum_price_tax
        order_data['orig_sum_price_tax'] = sum_price_tax
        order_data['sum_price'] = sum_price
        order_data['orig_sum_price'] = sum_price
        res = purchase_review_order_db.create_purchase_review_order(order_data=order_data, product_list=product_list)
        if res is True:
            result["order_id"] = order_id
            result["result"] = "success"
        else:
            raise Exception("Create Failed!")
        return result

    def list_purchase_review_order(self, request, partner_id=None, user_id=None):
        """查询采购复核单列表"""
        ret = {}
        start_date = request.start_date
        end_date = request.end_date
        order_code = request.order_code
        status = request.status
        company_id = convert_to_int(request.company_id)
        branch_type = request.branch_type
        process_status = request.process_status
        is_adjust = request.is_adjust
        if status == 'all':
            status = None
        order_type = request.order_type
        supplier_ids = list(request.supplier_ids) if request.supplier_ids else []
        received_ids = list(request.received_ids) if request.received_ids else []
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        order = request.order
        sort = request.sort
        source_type = request.source_type
        origin_code = request.origin_code
        if not start_date or not end_date:
            raise DataValidationException("请填写采购日期")
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        query_set = purchase_review_order_db.list_purchase_review_order(partner_id=partner_id, user_id=user_id,
                                                                        start_date=start_date, end_date=end_date,
                                                                        order_code=order_code, origin_code=origin_code,
                                                                        status=status,
                                                                        order_type=order_type,
                                                                        supplier_ids=supplier_ids,
                                                                        received_ids=received_ids, limit=limit,
                                                                        offset=offset, include_total=include_total,
                                                                        order=order, sort=sort, source_type=source_type,
                                                                        branch_type=branch_type, company_id=company_id,
                                                                        process_status=process_status,
                                                                        is_adjust=is_adjust)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            ret["total"] = total
        else:
            query_set = query_set
        result_list = []
        if query_set and len(query_set) > 0:
            for q in query_set:
                row = dict(
                    order_id=q.id,
                    order_code=q.order_code,
                    order_type=q.order_type,
                    status=q.status,
                    received_date=self.get_timestamp(q.received_date),
                    received_by=q.received_by,
                    received_name=q.received_name,
                    received_code=q.received_code,
                    supplier_id=q.supplier_id,
                    supplier_name=q.supplier_name,
                    supplier_code=q.supplier_code,
                    sum_price_tax=q.sum_price_tax,
                    orig_sum_price_tax=q.orig_sum_price_tax,
                    sum_price=q.sum_price,
                    orig_sum_price=q.orig_sum_price,
                    review_by=q.review_by,
                    created_by=q.created_by,
                    created_name=q.created_name,
                    created_at=self.get_timestamp(q.created_at),
                    updated_by=q.updated_by,
                    updated_name=q.updated_name,
                    updated_at=self.get_timestamp(q.updated_at),
                    is_modify=q.is_modify,
                    branch_type=q.branch_type,
                    company_id=q.company_id,
                    process_status=q.process_status,
                    origin_code=q.origin_code,
                    source_type=q.source_type,
                    generate_adjust=q.generate_adjust,
                    adjust_info=q.adjust_info
                )
                result_list.append(row)
            ret["rows"] = result_list
        else:
            ret["rows"] = {}
        return ret

    def get_purchase_review_detail(self, request, partner_id=None, user_id=None):
        """查询采购复核详情"""
        order_id = convert_to_int(request.order_id)
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        # 校验该订单是否存在
        query_order = purchase_review_order_db.get_purchase_order_by_id(review_id=order_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该订单不存在！")
        ret = dict(
            order_id=query_order.id,
            order_code=query_order.order_code,
            order_type=query_order.order_type,
            status=query_order.status,
            received_date=self.get_timestamp(query_order.received_date),
            received_by=query_order.received_by,
            received_name=query_order.received_name,
            received_code=query_order.received_code,
            supplier_id=query_order.supplier_id,
            supplier_name=query_order.supplier_name,
            supplier_code=query_order.supplier_code,
            sum_price_tax=query_order.sum_price_tax,
            orig_sum_price_tax=query_order.orig_sum_price_tax,
            sum_price=query_order.sum_price,
            orig_sum_price=query_order.orig_sum_price,
            review_by=query_order.review_by,
            created_by=query_order.created_by,
            created_name=query_order.created_name,
            created_at=self.get_timestamp(query_order.created_at),
            updated_by=query_order.updated_by,
            updated_name=query_order.updated_name,
            updated_at=self.get_timestamp(query_order.updated_at),
            is_modify=query_order.is_modify,
            branch_type=query_order.branch_type,
            company_id=query_order.company_id,
            process_status=query_order.process_status,
            origin_code=query_order.origin_code,
            is_adjust=query_order.is_adjust,
            source_type=query_order.source_type,
            generate_adjust=query_order.generate_adjust,
            reject_reason=query_order.reject_reason
        )
        if query_order.adjust_info:
            adjust_info = json.loads(query_order.adjust_info)
            adjust_codes = adjust_info.get('adjust_codes', [])
            ret["adjust_codes"] = adjust_codes
        # 根据订单id拿商品详情
        product_detail = purchase_review_order_db.get_review_order_product(partner_id=partner_id, user_id=user_id,
                                                                           order_id=order_id, limit=limit,
                                                                           offset=offset, include_total=include_total)
        product_list = []
        if isinstance(product_detail, tuple):
            total, product_detail = product_detail
            ret["total_product"] = total
        else:
            product_detail = product_detail
        if not product_detail:
            product_list = []
        adjust_map = dict()
        # 根据该复核单生成的调价单查询调价明细
        # if query_order.adjust_info:
        #     pro_ids = [p.product_id for p in product_detail]
        #     adjust_info = json.loads(query_order.adjust_info)
        #     adjust_codes = adjust_info.get('adjust_codes', [])
        #     ret["adjust_codes"] = adjust_codes
        #     adjust_detail = price_adjustment_db.get_price_adjustment_product(partner_id=partner_id,
        #                                                                      adjust_codes=adjust_codes,
        #                                                                      product_ids=pro_ids)
        #     if adjust_detail:
        #         for ad in adjust_detail:
        #             row = dict(
        #                 pre_price_tax=ad.pre_price_tax,
        #                 price_tax=ad.price_tax,
        #                 updated_by=ad.updated_by,
        #                 updated_name=ad.updated_name,
        #                 updated_at=self.get_timestamp(ad.updated_at)
        #             )
        #             if ad.product_id in adjust_map.keys():
        #                 adjust_map[ad.product_id].append(row)
        #             else:
        #                 adjust_map[ad.product_id] = [row]
        adjust_logs = purchase_review_order_db.get_price_adjust_logs(review_id=order_id, partner_id=partner_id)
        if adjust_logs:
            for ad in adjust_logs:
                row = dict(
                    orig_price_tax=ad.orig_price_tax,
                    price_tax=ad.price_tax,
                    prev_price_tax=ad.prev_price_tax,
                    updated_by=ad.created_by,
                    updated_name=ad.created_name,
                    updated_at=self.get_timestamp(ad.created_at)
                )
                if ad.product_id in adjust_map.keys():
                    adjust_map[ad.product_id].append(row)
                else:
                    adjust_map[ad.product_id] = [row]

        for product in product_detail:
            adjust_logs = adjust_map.get(product.product_id, [])
            row = dict(
                id=product.id,
                product_id=product.product_id,
                product_code=product.product_code,
                product_name=product.product_name,
                product_category_id=product.product_category_id,
                product_type=product.product_type,
                unit_id=product.unit_id,
                unit_name=product.unit_name,
                unit_spec=product.unit_spec,
                spec=product.spec,
                quantity=product.quantity,
                actual_quantity=product.actual_quantity,
                tax_rate=product.tax_rate,
                price=product.price,
                sum_price=product.sum_price,
                price_tax=product.price_tax,
                sum_price_tax=product.sum_price_tax,
                orig_price_tax=product.orig_price_tax,
                prev_price_tax=product.prev_price_tax,
                orig_sum_price_tax=product.orig_sum_price_tax,
                orig_price=product.orig_price,
                orig_sum_price=product.orig_sum_price,
                is_modify=product.is_modify,
                sum_tax=product.sum_price_tax - product.sum_price,
                orig_sum_tax=product.orig_sum_price_tax - product.orig_sum_price,
                adjust_logs=adjust_logs
            )
            product_list.append(row)
        ret["products"] = product_list
        # ret["total_product"] = len(product_list)
        return ret

    def update_purchase_review_order(self, request, partner_id=None, user_id=None):
        """采购复核更新详情"""
        ret = {}
        order_id = convert_to_int(request.order_id)
        if not order_id:
            raise DataValidationException("请传入复核单ID")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        status = request.status
        products = request.products
        query_order = purchase_review_order_db.get_purchase_order_by_id(review_id=order_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该订单不存在-{}".format(order_id))
        order_detail = dict(
            id=order_id,
            updated_by=user_id,
            updated_name=username,
        )
        if status == "SUBMITTED":
            order_detail["status"] = status
        sum_price_tax = convert_to_decimal(0)
        sum_price = convert_to_decimal(0)
        product_data_list = []
        adjust_logs = []
        if len(products) > 0:
            for product in products:
                # 调整后
                price = convert_to_decimal(product.price_tax) / convert_to_decimal(1 + product.tax_rate / 100)
                sum_price_tax_tmp = convert_to_decimal(product.price_tax) * convert_to_decimal(
                    product.actual_quantity)
                # 未税合计 = 含税合计 /（1 + 税率）
                sum_price_tmp = sum_price_tax_tmp / convert_to_decimal(1 + product.tax_rate / 100)
                if product.price_tax != product.orig_price_tax:
                    is_modify = 1
                    order_detail['is_modify'] = 1
                else:
                    is_modify = 0
                if status == "SUBMITTED" and product.price_tax != product.prev_price_tax:
                    adjust_logs.append(dict(
                        id=get_guid(),
                        partner_id=partner_id,
                        created_by=user_id,
                        created_name=username,
                        main_id=order_id,
                        product_id=product.product_id,
                        price_tax=product.price_tax,
                        orig_price_tax=product.orig_price_tax,
                        prev_price_tax=product.prev_price_tax,
                        created_at=datetime.utcnow()
                    ))
                row = dict(
                    id=convert_to_int(product.id),
                    updated_by=user_id,
                    updated_name=username,
                    is_modify=is_modify,
                    price=price,
                    price_tax=product.price_tax,
                    prev_price_tax=product.prev_price_tax,
                    sum_price=sum_price_tmp,
                    sum_price_tax=sum_price_tax_tmp,
                    # orig_price=orig_price,
                    # orig_price_tax=product.orig_price_tax,
                    # orig_sum_price=orig_sum_price_tmp,
                    # orig_sum_price_tax=orig_sum_price_tax_tmp,
                )
                product_data_list.append(row)
                # orig_sum_price_tax += orig_sum_price_tax_tmp
                # orig_sum_price += orig_sum_price_tmp
                sum_price += sum_price_tmp
                sum_price_tax += sum_price_tax_tmp
            order_detail["sum_price_tax"] = sum_price_tax
            order_detail["sum_price"] = sum_price

        res = purchase_review_order_db.update_review_order_product(order_detail=order_detail,
                                                                   product_data_list=product_data_list,
                                                                   adjust_logs=adjust_logs,
                                                                   partner_id=partner_id)
        if res:
            ret["order_id"] = order_id
            ret["result"] = "success"
        return ret

    def list_purchase_review_detail(self, request, partner_id=None, user_id=None):
        """查询采购复核单列表"""
        ret = {}
        start_date = request.start_date
        end_date = request.end_date
        order_code = request.order_code
        status = request.status
        company_id = convert_to_int(request.company_id)
        branch_type = request.branch_type
        process_status = request.process_status
        is_adjust = request.is_adjust
        if status == 'all':
            status = None
        order_type = request.order_type
        supplier_ids = list(request.supplier_ids) if request.supplier_ids else []
        received_ids = list(request.received_ids) if request.received_ids else []
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        order = request.order
        sort = request.sort
        source_type = request.source_type
        origin_code = request.origin_code
        if not start_date or not end_date:
            raise DataValidationException("请填写采购日期")
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        query_set = purchase_review_order_db.list_purchase_review_detail(partner_id=partner_id, start_date=start_date,
                                                                         end_date=end_date, order_code=order_code,
                                                                         origin_code=origin_code, status=status,
                                                                         order_type=order_type,
                                                                         supplier_ids=supplier_ids,
                                                                         received_ids=received_ids, limit=limit,
                                                                         offset=offset, include_total=include_total,
                                                                         order=order, sort=sort,
                                                                         source_type=source_type,
                                                                         branch_type=branch_type, company_id=company_id,
                                                                         process_status=process_status,
                                                                         is_adjust=is_adjust)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            ret["total"] = total
        else:
            query_set = query_set
        result_list = []
        if query_set and len(query_set) > 0:
            for q in query_set:
                row = dict(
                    order_id=q[0],
                    order_code=q[1],
                    order_type=q[2],
                    status=q[3],
                    received_date=self.get_timestamp(q[4]),
                    received_by=q[5],
                    received_name=q[6],
                    received_code=q[7],
                    supplier_id=q[8],
                    supplier_name=q[9],
                    supplier_code=q[10],
                    branch_type=q[11],
                    company_id=q[12],
                    process_status=q[13],
                    origin_code=q[14],
                    source_type=q[15],
                    generate_adjust=q[16],
                    id=q[17],
                    product_id=q[18],
                    product_code=q[19],
                    product_name=q[20],
                    product_type=q[21],
                    product_category_id=q[22],
                    unit_id=q[23],
                    unit_name=q[24],
                    unit_spec=q[25],
                    spec=q[26],
                    quantity=q[27],
                    actual_quantity=q[28],
                    tax_rate=q[29],
                    price=q[30],
                    sum_price=q[31],
                    price_tax=q[32],
                    sum_price_tax=q[33],
                    orig_price_tax=q[34],
                    prev_price_tax=q[35],
                    orig_sum_price_tax=q[36],
                    orig_price=q[37],
                    orig_sum_price=q[38],
                    is_modify=q[39],
                    sum_tax=q[40],
                    orig_sum_tax=q[41],
                    created_by=q[42],
                    created_name=q[43],
                    created_at=self.get_timestamp(q[44]),
                    updated_by=q[45],
                    updated_name=q[46],
                    updated_at=self.get_timestamp(q[47]),
                )
                result_list.append(row)
            ret["rows"] = result_list
        else:
            ret["rows"] = {}
        return ret

    def change_purchase_review_status(self, partner_id, order_ids, action, allow_status: list,
                                      reject_reason=None, user_id=None):
        ret = {}
        order_ids = [convert_to_int(order_id) for order_id in order_ids] if order_ids else []
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 支持批量复核, 该接口也支持发票勾兑页面对复核单修改状态
        update_data = []
        update_logs = []
        log_ids = get_uuids(len(order_ids))
        if not order_ids:
            raise DataValidationException("请传入单据ID!")
        if len(order_ids) > 1:
            raise DataValidationException("暂不支持批量操作")
            # if action not in ["APPROVED", "SUBMITTED", "COMPLETED"]:
            #     raise DataValidationException("批量操作仅支持提交、复核和勾兑")
        else:
            order_id = order_ids[0]
            # 判断订单是否存在
            query_order = purchase_review_order_db.get_purchase_order_by_id(review_id=order_id,
                                                                            partner_id=partner_id)
            if not query_order:
                raise OrderNotExistException("该单据不存在-{}".format(order_id))

            if query_order.status not in allow_status:
                raise StatusUnavailable("当前状态不允许操作")

            if query_order.process_status == "COMPLETED" and action == "APPROVED":
                raise DataValidationException("已经勾兑的单据不允许再次复核-{}".format(order_id))
            row = dict(
                id=order_id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                status=action,
            )
            if action == "COMPLETED":
                row['process_status'] = action
                del row['status']
            elif action == "APPROVED":
                row['review_by'] = user_id
                row['process_status'] = "INITED"
            elif action == "REJECTED":
                row["reject_reason"] = reject_reason

            update_data.append(row)
            update_logs.append(dict(
                id=log_ids[0],
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                main_id=order_id,
                action=action,
                created_at=datetime.utcnow()
            ))
            res = purchase_review_order_db.update_review_order_no_product(action=action,
                                                                          is_modify=query_order.is_modify,
                                                                          update_data=update_data,
                                                                          update_logs=update_logs)
            if res is True:
                ret["order_ids"] = order_ids
                ret["result"] = "success"
        return ret


purchase_review_service = PurchaseReviewService()
