# -*- coding: utf8 -*-
from datetime import datetime
import logging

from supply.driver.mq import mq_producer
from supply.utils.inventory_enum import ACTION
from supply.model.inventory import inventory_repository
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.helper import convert_to_int, MessageTopic, get_product_unit_rate_map, convert_to_decimal, \
    get_cost_center_map, get_product_unit_map, get_product_map
from supply.model.supply_doc_code import Supply_doc_code
from google.protobuf.timestamp_pb2 import Timestamp
from supply.error.exception import DataValidationException, DataDuplicationException, OrderNotExistException, \
    StatusUnavailable
from supply.client.metadata_service import metadata_service
from supply.model.manufactory.packing_receipts import packing_receipts_db
from supply.model.operation_log import ReceiptFlowLogModel, TpTransLogModel
from supply.utils.auth import branch_list_scope_check, branch_scope_check


class PackingReceiptsService(object):
    """包装单业务处理"""
    def __init__(self):
        pass

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    # noinspection PyMethodMayBeStatic
    def utctimestamp2datetime(self, timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.utcfromtimestamp(timestamp.seconds)
        return date

    def update_res_datetime2timestamp(self, keys, res: dict):
        """更新查询集中的datetime to Timestamp
        :param keys: 字段值列表 -> (str)
        :param res: 返回查询集 -> (dict)
        """
        if keys:
            for key in keys:
                if key in res.keys():
                    res.update({key: self.get_timestamp(res[key])})
        return res

    def create_packing_receipts(self, request, user_id=None, partner_id=None):
        """创建包装单"""
        response = {}
        request_id = convert_to_int(request.request_id)
        packing_date = self.utctimestamp2datetime(request.packing_date)
        machining_center_id = convert_to_int(request.machining_center_id)
        position_id = convert_to_int(request.position_id)
        packing_rule = convert_to_int(request.packing_rule)
        target_material = request.target_material
        target_material_code = request.target_material_code
        target_material_name = request.target_material_name
        target_material_unit_id = convert_to_int(request.target_material_unit_id)
        if not request_id:
            raise DataValidationException("没有请求id")
        # 权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=machining_center_id)
        # 根据请求id校验该包装单是否已经创建
        record = packing_receipts_db.get_packing_receipts_by_id(request_id=request_id, partner_id=partner_id)
        if record:
            raise DataDuplicationException("该包装单已创建{}".format(record.id))
        if not packing_date:
            raise DataValidationException("请传入包装日期")
        code = Supply_doc_code.get_code_by_type(code_type='PAC_REC', partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 拉取成本中心
        cost_center_id = get_cost_center_map(branch_type="MACHINING_CENTER", branch_id=machining_center_id,
                                             partner_id=partner_id, user_id=user_id, return_cost=True)
        if not cost_center_id:
            raise DataValidationException("该组织没有配置成本中心-{}".format(request.machining_center_name))
        main_id = gen_snowflake_id()
        receipt = dict(
            id=main_id,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            code=code,
            status="INITED",
            packing_date=packing_date,
            machining_center_id=machining_center_id,
            position_id=position_id,
            process_status='INITED',
            remark=request.remark,
            packing_rule=packing_rule,
            opened_position=request.opened_position,
            request_id=request_id
        )
        receipt_detail = dict(
            id=gen_snowflake_id(),
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            main_id=main_id,
            status='INITED',
            code=code,
            packing_date=packing_date,
            machining_center_id=machining_center_id,
            machining_center_name=request.machining_center_name,
            machining_center_code=request.machining_center_code,
            position_id=position_id,
            position_name=request.position_name,
            position_code=request.position_code,
            packing_rule=packing_rule,
            target_material=target_material,
            target_material_code=target_material_code,
            target_material_name=target_material_name,
            target_material_unit_id=target_material_unit_id,
            target_material_unit_name=request.target_material_unit_name,
            target_material_unit_spec=request.target_material_unit_spec,
            packed_quantity=request.packed_quantity,
            remark=request.remark,
            opened_position=request.opened_position,
            request_id=request_id,
            cost_center_id=cost_center_id
        )
        items = request.items
        if not items:
            raise DataValidationException("包装单必须包含商品")
        product_ids = [convert_to_int(m.product_id) for m in items]
        product_ids.append(target_material)
        product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name",
                                      partner_id=partner_id,
                                      user_id=user_id)
        product = product_map.get(str(target_material)) if product_map.get(str(target_material)) else {}
        units = product.get('units', [])
        target_material_unit_rate = 0
        for u in units:
            if str(u.get('id')) == str(target_material_unit_id):
                target_material_unit_rate = u.get('rate')
        receipt_detail['target_material_unit_rate'] = target_material_unit_rate
        items_list = []
        for m in items:
            product = product_map.get(str(m.product_id)) if product_map.get(str(m.product_id)) else {}
            units = product.get('units', [])
            unit_name = ''
            unit_spec = ''
            unit_rate = 0
            for u in units:
                if str(u.get('id')) == str(m.unit_id):
                    unit_name = u.get('name')
                    unit_spec = u.get('code')
                    unit_rate = u.get('rate')
            row = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username,
                main_id=main_id,
                product_id=m.product_id,
                product_code=m.product_code,
                product_name=m.product_name,
                product_type=m.product_type,
                unit_id=m.unit_id,
                unit_name=unit_name,
                unit_spec=unit_spec,
                unit_rate=unit_rate,
                quantity=m.quantity,
                type=m.type,
            )
            items_list.append(row)
        res = packing_receipts_db.create_packing_receipts(receipt_data=receipt, receipt_detail=receipt_detail,
                                                          items=items_list)
        if res is True:
            response["receipt_id"] = main_id
            response["result"] = "success"
        else:
            response["result"] = "failed"
        return response

    def list_packing_receipts(self, request, partner_id=None, user_id=None):
        """查询包装单列表"""
        response = {}
        start_date = self.utctimestamp2datetime(request.start_date)
        end_date = self.utctimestamp2datetime(request.end_date)
        machining_centers = [convert_to_int(_id) for _id in
                             request.machining_centers] if request.machining_centers else []
        target_materials = [convert_to_int(_rule) for _rule in
                            request.target_materials] if request.target_materials else []
        position_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        code = request.code
        status = list(request.status) if request.status else []
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        order = request.order
        sort = request.sort
        # 权限校验
        machining_centers = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                    domain='boh.manufactory', branch_ids=machining_centers)
        query_set = packing_receipts_db.list_packing_receipts_detail(partner_id=partner_id, user_id=user_id,
                                                                     start_date=start_date, end_date=end_date,
                                                                     code=code, status=status, _type=None,
                                                                     machining_centers=machining_centers,
                                                                     packing_rules=None,
                                                                     target_materials=target_materials,
                                                                     position_ids=position_ids,
                                                                     order=order, sort=sort,
                                                                     limit=limit, offset=offset,
                                                                     include_total=include_total)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            response["total"] = total
        else:
            query_set = query_set
        result_list = []
        if query_set:
            for row in query_set:
                row = row.as_dict()
                # 需要更新datetime的字段
                keys = ["created_at", "updated_at", "packing_date"]
                row = self.update_res_datetime2timestamp(keys=keys, res=row)
                result_list.append(row)
        response["rows"] = result_list
        return response

    def get_packing_receipts_detail(self, request, partner_id=None, user_id=None):
        """查询包装单详情"""
        response = {}
        receipt_id = convert_to_int(request.receipt_id)
        receipt_detail = packing_receipts_db.get_packing_receipts_by_id(receipt_id=receipt_id, partner_id=partner_id,
                                                                        is_detail=True)
        if not receipt_detail:
            raise DataValidationException("Packing Receipt not found！- {}".format(receipt_id))
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=receipt_detail.machining_center_id)
        response.update(receipt_detail.as_dict())
        # 更新datetime to Timestamp
        keys = ["created_at", "updated_at", "packing_date"]
        response = self.update_res_datetime2timestamp(keys=keys, res=response)
        items = packing_receipts_db.get_packing_receipts_detail(partner_id=partner_id,
                                                                user_id=user_id,
                                                                main_id=receipt_id)
        response["items"] = []
        if items:
            for row in items:
                row = row.as_dict()
                keys = ["created_at", "updated_at"]
                row = self.update_res_datetime2timestamp(keys=keys, res=row)
                response["items"].append(row)
        return response

    def update_packing_receipts(self, request, partner_id=None, user_id=None):
        """更新包装单"""
        ret = {}
        receipt_id = convert_to_int(request.receipt_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        packing_date = self.utctimestamp2datetime(timestamp=request.packing_date)
        machining_center_id = convert_to_int(request.machining_center_id)
        position_id = convert_to_int(request.position_id)
        packing_rule = convert_to_int(request.packing_rule)
        query_order = packing_receipts_db.get_packing_receipts_by_id(receipt_id=receipt_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该包装单不存在-{}".format(receipt_id))
        # 权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=query_order.machining_center_id)
        items_list = []
        if query_order.status in ["SUBMITTED", "APPROVED"]:
            raise DataValidationException("{}状态下不允许更新详情".format(query_order.status))
        # 拉取成本中心
        cost_center_id = get_cost_center_map(branch_type="MACHINING_CENTER", branch_id=machining_center_id,
                                             partner_id=partner_id, user_id=user_id, return_cost=True)
        if not cost_center_id:
            raise DataValidationException("该组织没有配置成本中心-{}".format(request.machining_center_name))
        target_material = convert_to_int(request.target_material)
        target_material_unit_id = convert_to_int(request.target_material_unit_id)
        receipt = dict(
            id=receipt_id,
            updated_by=user_id,
            updated_name=username,
            packing_date=packing_date,
            machining_center_id=machining_center_id,
            position_id=position_id,
            remark=request.remark,
            packing_rule=packing_rule,
            opened_position=request.opened_position,
        )
        receipt_detail = dict(
            main_id=receipt_id,
            updated_by=user_id,
            updated_name=username,
            packing_date=packing_date,
            machining_center_id=machining_center_id,
            machining_center_name=request.machining_center_name,
            machining_center_code=request.machining_center_code,
            position_id=position_id,
            position_name=request.position_name,
            position_code=request.position_code,
            packing_rule=packing_rule,
            target_material=target_material,
            target_material_code=request.target_material_code,
            target_material_name=request.target_material_name,
            target_material_unit_id=target_material_unit_id,
            target_material_unit_name=request.target_material_unit_name,
            target_material_unit_spec=request.target_material_unit_spec,
            packed_quantity=request.packed_quantity,
            remark=request.remark,
            opened_position=request.opened_position,
        )
        items = request.items
        if not items:
            raise DataValidationException("更新包装单详情必须包含物料")
        product_ids = [convert_to_int(m.product_id) for m in items]
        product_ids.append(target_material)
        product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name",
                                      partner_id=partner_id,
                                      user_id=user_id)
        product = product_map.get(str(target_material)) if product_map.get(str(target_material)) else {}
        units = product.get('units', [])
        target_material_unit_rate = 0
        for u in units:
            if str(u.get('id')) == str(target_material_unit_id):
                target_material_unit_rate = u.get('rate')
        receipt_detail['target_material_unit_rate'] = target_material_unit_rate
        for m in items:
            product = product_map.get(str(m.product_id)) if product_map.get(str(m.product_id)) else {}
            units = product.get('units', [])
            unit_name = ''
            unit_spec = ''
            unit_rate = 0
            for u in units:
                if str(u.get('id')) == str(m.unit_id):
                    unit_name = u.get('name')
                    unit_spec = u.get('code')
                    unit_rate = u.get('rate')
            row = dict(
                id=m.id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                main_id=receipt_id,
                product_id=m.product_id,
                product_code=m.product_code,
                product_name=m.product_name,
                product_type=m.product_type,
                unit_id=m.unit_id,
                unit_name=unit_name,
                unit_spec=unit_spec,
                unit_rate=unit_rate,
                quantity=m.quantity,
                type=m.type,
            )
            items_list.append(row)
        res = packing_receipts_db.update_packing_receipts(receipt=receipt, receipt_detail=receipt_detail,
                                                          items_list=items_list, update_detail=True,
                                                          partner_id=partner_id)
        if res is True:
            ret["receipt_id"] = receipt_id
            ret["result"] = "success"
        return ret

    def change_packing_receipt_status(self, request, partner_id=None, user_id=None):
        """包装单状态变更"""
        ret = {}
        receipt_id = convert_to_int(request.receipt_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        status = request.status
        query_order = packing_receipts_db.get_packing_receipts_by_id(receipt_id=receipt_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该包装单不存在-{}".format(receipt_id))
        if status and status not in ["INITED", "SUBMITTED", "APPROVED", "REJECTED", "DELETED"]:
            raise DataValidationException("更新状态不合法{}".format(status))
        if status == query_order.status:
            raise DataValidationException("状态已变更请勿重复操作")
        if status == "REJECTED" and query_order.status != "SUBMITTED":
            raise StatusUnavailable("只有已提交的包装单可以驳回!")
        if status == "APPROVED" and query_order.status != "SUBMITTED":
            raise StatusUnavailable("只有已提交的包装单可以审核!")
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=query_order.machining_center_id)
        receipt = dict(
            id=receipt_id,
            updated_by=user_id,
            updated_name=username,
            status=status,
        )
        receipt_detail = dict(
            updated_by=user_id,
            updated_name=username,
            main_id=receipt_id,
            status=status,
        )
        items_list = []

        res = packing_receipts_db.update_packing_receipts(receipt=receipt, receipt_detail=receipt_detail,
                                                          items_list=items_list, update_detail=False,
                                                          partner_id=partner_id)
        if res is True:
            ret["receipt_id"] = receipt_id
            ret["result"] = "success"
            update_log = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                main_id=receipt_id,
                action=status,
            )
            # 保存操作日志
            packing_receipts_db.create_packing_receipts_log(receipts_log=update_log)

            if status == "APPROVED":
                """
                单据审核后需要扣减库存处理：
                    加工物料扣减库存：WITHDRAW = 1; // 减少库存
                    目标物料增加库存：DEPOSIT = 2; //增加库存
                """
                accounts = []
                sub_account = dict(
                    id=query_order.position_id,  # 子账户业务ID
                ) if query_order.opened_position else None
                # 查询包装消耗物料，扣减库存：WITHDRAW = 1; // 减少库存
                materials = packing_receipts_db.get_packing_receipts_detail(partner_id=partner_id,
                                                                            user_id=user_id,
                                                                            main_id=receipt_id)
                # 查询目标物料，增加库存：DEPOSIT = 2; //增加库存
                order_detail = packing_receipts_db.get_packing_receipts_by_id(receipt_id=receipt_id,
                                                                              partner_id=partner_id,
                                                                              is_detail=True)

                product_ids = []
                for m in materials:
                    product_ids.append(m.product_id)
                if order_detail:
                    product_ids.append(order_detail.target_material)
                # 获取商品单位和转换率map
                product_unit_rate_map = get_product_unit_rate_map(product_ids=product_ids,
                                                                  partner_id=partner_id,
                                                                  user_id=user_id)
                if materials:
                    for m in materials:
                        accounting = dict(sub_account=sub_account)
                        # 单位换算为核算数量
                        unit_rate_dict = product_unit_rate_map.get(m.product_id) if product_unit_rate_map.get(
                            m.product_id) else {}
                        quantity = convert_to_decimal(m.quantity)
                        unit_rate = convert_to_decimal(unit_rate_dict.get(m.unit_id, 1))
                        accounting_quantity = quantity * unit_rate
                        accounting.update(dict(
                            branch_id=query_order.machining_center_id,
                            product_id=m.product_id,
                            amount=str(accounting_quantity),    # 存字符串为了json序列化
                            action=1
                        ))
                        accounts.append(accounting)

                if order_detail:
                    accounting = dict(sub_account=sub_account)
                    # 单位换算为核算数量
                    unit_rate_dict = product_unit_rate_map.get(
                        order_detail.target_material) if product_unit_rate_map.get(
                        order_detail.target_material) else {}
                    quantity = convert_to_decimal(order_detail.packed_quantity)
                    unit_rate = convert_to_decimal(unit_rate_dict.get(order_detail.target_material_unit_id, 1))
                    accounting_quantity = quantity * unit_rate
                    accounting.update(dict(
                        branch_id=order_detail.machining_center_id,
                        product_id=order_detail.target_material,
                        amount=str(accounting_quantity),
                        action=2
                    ))
                    accounts.append(accounting)
                # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
                message = dict(batch_no=str(receipt_id),
                               code='PACKING',
                               action=100,
                               description='PACKING',
                               trace_id=query_order.code,
                               accounts=accounts,
                               partner_id=partner_id,
                               user_id=user_id,
                               business_time=datetime.utcnow())
                inventory_dict = dict(batch_no=str(receipt_id), code="PACKING", batch_action=100,
                                      action_dec=ACTION[100],
                                      batch_id=None,
                                      status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                      )
                inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                 partner_id=partner_id, user_id=user_id)
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                    message=message)

                # 记录工作流日志
                flow_log_detail = dict(
                    doc_id=receipt_id,
                    doc_type="packingOrder",
                    operation="INNER_TRANSFER",
                    branch_id=order_detail.machining_center_id,
                    sub_branch_id=order_detail.position_id,
                    sub_doc_type='position' if order_detail.position_id else 'store',
                    posterior_operation='MATERIAL_CONVERT',
                    process_status='INITED',
                    partner_id=partner_id,
                    created_by=user_id,
                    created_at=datetime.utcnow(),
                    created_name=username
                )
                flow_log_db = ReceiptFlowLogModel.create_flow_logs(**flow_log_detail)

                message = {
                    'flow_id': flow_log_db.id,
                    'partner_id': partner_id,
                    'user_id': user_id
                }
                logging.info("包装单工作流触发:{}".format(flow_log_db.id))
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.HANDLE_FLOW_TASK,
                                    message=message)
                
                # vendor单据同步给三方
                message = {
                                        'doc_resource': 'm_packing',
                                        'doc_id': receipt_id,
                                        'partner_id': partner_id,
                                        'user_id': user_id,
                                        'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    }
                mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)
                    
                # 同步记录落表，用于后续补偿
                tp_trans_log = {
                                    'id': receipt_id,
                                    'doc_code': query_order.code,
                                    'doc_type': 'm_packing',
                                    'status': 'inited',
                                    'msg': str(message),
                                    'partner_id': partner_id,
                                    'created_by': user_id,
                                    'created_at': datetime.utcnow(),
                                    'updated_at': datetime.utcnow()
                                }

                # 一把更新数据库
                TpTransLogModel.create_logs_list([tp_trans_log])

        return ret


packing_receipts_service = PackingReceiptsService()
