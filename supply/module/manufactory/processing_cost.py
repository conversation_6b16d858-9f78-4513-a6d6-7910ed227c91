# -*- coding: utf8 -*-
from datetime import datetime

from supply.client.infra_metadata_service import metadata_center_service
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.helper import convert_to_int, get_month_first_and_last_date, get_cost_center_map, MessageTopic
from supply.model.supply_doc_code import Supply_doc_code
from google.protobuf.timestamp_pb2 import Timestamp
from supply.error.exception import DataValidationException, DataDuplicationException, OrderNotExistException, \
    StatusUnavailable
from supply.client.metadata_service import metadata_service
from supply.model.manufactory.processing_cost import processing_cost_db
from supply.utils.auth import branch_list_scope_check, branch_scope_check
from supply.driver.mq import mq_producer


class ProcessingCostService(object):
    """加工费用单业务处理"""

    def __init__(self):
        pass

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    # noinspection PyMethodMayBeStatic
    def utctimestamp2datetime(self, timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.utcfromtimestamp(timestamp.seconds)
        return date

    def update_res_datetime2timestamp(self, keys, res: dict):
        """更新查询集中的datetime to Timestamp
        :param keys: 字段值列表 -> (str)
        :param res: 返回查询集 -> (dict)
        """
        if keys:
            for key in keys:
                if key in res.keys():
                    res.update({key: self.get_timestamp(res[key])})
        return res

    def get_period_map(self, period_ids: list, partner_id=None, user_id=None):
        """
        :return {period_id: period_name}
        """
        period_map = dict()
        period_list = metadata_center_service.list_entity(schema_name="PERIOD", ids=period_ids,
                                                          return_fields="id,name",
                                                          partner_id=partner_id,
                                                          user_id=user_id).get('rows', [])
        for period in period_list:
            fields = period.get('fields') if period.get('fields') else {}
            period_map[convert_to_int(period.get('id'))] = fields.get('name')
        return period_map

    def create_processing_cost(self, request, user_id=None, partner_id=None):
        """创建加工费用单"""
        response = {}
        request_id = convert_to_int(request.request_id)
        machining_center_id = convert_to_int(request.machining_center_id)
        position_id = convert_to_int(request.position_id)
        unit_id = convert_to_int(request.unit_id)
        month = request.month
        period_id = convert_to_int(request.period_id)
        if not request_id:
            raise DataValidationException("没有请求id")
        # 权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=machining_center_id)
        # 同一个月份内，同一个加工中心的同一个仓位，只能有一个生效的加工费用
        query_set = processing_cost_db.list_processing_cost(partner_id=partner_id, period_ids=[period_id],
                                                            machining_centers=[machining_center_id],
                                                            position_ids=[position_id], status=['APPROVED'])
        # query_set = processing_cost_db.list_processing_cost(partner_id=partner_id, start_month=month,
        #                                                     end_month=month,
        #                                                     machining_centers=[machining_center_id],
        #                                                     position_ids=[position_id], status=['APPROVED'])
        if len(query_set) > 0:
            raise DataDuplicationException("系统中已存在{}账期<{}>下<{}>的加工费用！".format(period_id,
                                                                              request.machining_center_name,
                                                                              request.position_name))
        # 根据请求id校验该加工费用单是否已经创建
        record = processing_cost_db.get_processing_cost_by_id(request_id=request_id, partner_id=partner_id)
        if record:
            raise DataDuplicationException("该加工费用单已创建{}".format(record.id))
        # 拉取成本中心
        cost_center_id = get_cost_center_map(branch_type="MACHINING_CENTER", branch_id=machining_center_id,
                                             partner_id=partner_id, user_id=user_id, return_cost=True)
        if not cost_center_id:
            raise DataValidationException("该组织没有配置成本中心-{}".format(request.machining_center_name))
        code = Supply_doc_code.get_code_by_type(code_type='PRO_COST', partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        main_id = gen_snowflake_id()
        receipt_detail = dict(
            id=main_id,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            status='INITED',
            code=code,
            month=month,
            period_id=period_id,
            machining_center_id=machining_center_id,
            machining_center_name=request.machining_center_name,
            machining_center_code=request.machining_center_code,
            position_id=position_id,
            position_name=request.position_name,
            position_code=request.position_code,
            unit_cost=request.unit_cost,
            quantity=request.quantity,
            unit_id=unit_id,
            unit_name=request.unit_name,
            unit_spec=request.unit_spec,
            type=request.type,
            remark=request.remark,
            opened_position=request.opened_position,
            request_id=request_id,
            cost_center_id=cost_center_id
        )
        res = processing_cost_db.create_processing_cost(receipt_detail=receipt_detail)
        if res is True:
            response["receipt_id"] = main_id
            response["result"] = "success"
        else:
            response["result"] = "failed"
        return response

    def list_processing_cost(self, request, partner_id=None, user_id=None):
        """查询加工费用单列表"""
        response = {}
        start_month = request.start_month
        end_month = request.end_month

        machining_centers = [convert_to_int(_id) for _id in
                             request.machining_centers] if request.machining_centers else []
        position_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        code = request.code
        status = list(request.status) if request.status else []
        period_ids = [convert_to_int(_id) for _id in request.period_ids] if request.period_ids else []
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        order = request.order
        sort = request.sort
        # 权限校验
        machining_centers = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                                                    domain='boh.manufactory', branch_ids=machining_centers)
        query_set = processing_cost_db.list_processing_cost(partner_id=partner_id,
                                                            period_ids=period_ids,
                                                            start_month=start_month,
                                                            end_month=end_month,
                                                            code=code, status=status,
                                                            machining_centers=machining_centers,
                                                            position_ids=position_ids,
                                                            order=order, sort=sort,
                                                            limit=limit, offset=offset,
                                                            include_total=include_total)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            response["total"] = total
        else:
            query_set = query_set
        period_dict = self.get_period_map(period_ids=period_ids, partner_id=partner_id, user_id=user_id)
        result_list = []
        if query_set:
            for row in query_set:
                row = row.as_dict()
                # 需要更新datetime的字段
                keys = ["created_at", "updated_at"]
                row = self.update_res_datetime2timestamp(keys=keys, res=row)
                row['period_name'] = period_dict.get(row.get('period_id'))
                result_list.append(row)
        response["rows"] = result_list
        return response

    def get_processing_cost_detail(self, request, partner_id=None, user_id=None):
        """查询加工费用单详情"""
        detail = {}
        response = {}
        receipt_id = convert_to_int(request.receipt_id)
        receipt_detail = processing_cost_db.get_processing_cost_by_id(receipt_id=receipt_id,
                                                                      partner_id=partner_id)
        if not receipt_detail:
            raise DataValidationException("Processing Receipt not found！- {}".format(receipt_id))
        # 权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=receipt_detail.machining_center_id)
        period_dict = self.get_period_map(period_ids=[receipt_detail.period_id], partner_id=partner_id, user_id=user_id)
        detail.update(receipt_detail.as_dict())
        detail['period_name'] = period_dict.get(receipt_detail.period_id)
        # 更新datetime to Timestamp
        keys = ["created_at", "updated_at"]
        response["detail"] = self.update_res_datetime2timestamp(keys=keys, res=detail)
        return response

    def update_processing_cost(self, request, partner_id=None, user_id=None):
        """更新加工费用单"""
        ret = {}
        receipt_id = convert_to_int(request.receipt_id)

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        query_order = processing_cost_db.get_processing_cost_by_id(receipt_id=receipt_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该加工费用单不存在-{}".format(receipt_id))
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=query_order.machining_center_id)
        receipt_detail = dict(
            updated_by=user_id,
            updated_name=username,
            id=receipt_id,
        )
        detail = request.detail
        if not detail:
            raise DataValidationException("保存操作必须传明细信息detail")
        machining_center_id = convert_to_int(detail.machining_center_id)
        position_id = convert_to_int(detail.position_id)
        unit_id = convert_to_int(detail.unit_id)
        if query_order.status in ["SUBMITTED", "APPROVED"]:
            raise DataValidationException("{}状态下不允许更新操作".format(query_order.status))
        # 拉取成本中心
        cost_center_id = get_cost_center_map(branch_type="MACHINING_CENTER", branch_id=machining_center_id,
                                             partner_id=partner_id, user_id=user_id, return_cost=True)
        if not cost_center_id:
            raise DataValidationException("该组织没有配置成本中心-{}".format(request.machining_center_name))
        receipt_detail.update(dict(
                    month=detail.month,
                    period_id=detail.period_id,
                    machining_center_id=machining_center_id,
                    machining_center_name=detail.machining_center_name,
                    machining_center_code=detail.machining_center_code,
                    position_id=position_id,
                    position_name=detail.position_name,
                    position_code=detail.position_code,
                    unit_cost=detail.unit_cost,
                    quantity=detail.quantity,
                    unit_id=unit_id,
                    unit_name=detail.unit_name,
                    unit_spec=detail.unit_spec,
                    type=detail.type,
                    remark=detail.remark,
                    opened_position=detail.opened_position,
                    cost_center_id=cost_center_id
        ))
        res = processing_cost_db.update_processing_cost(receipt_detail=receipt_detail, update_detail=True,
                                                        partner_id=partner_id)
        if res is True:
            ret["receipt_id"] = receipt_id
            ret["result"] = "success"
        return ret

    def change_processing_cost_status(self, request, partner_id=None, user_id=None):
        ret = {}
        receipt_id = convert_to_int(request.receipt_id)
        status = request.status

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        query_order = processing_cost_db.get_processing_cost_by_id(receipt_id=receipt_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该加工费用单不存在-{}".format(receipt_id))
        if status and status not in ["INITED", "SUBMITTED", "APPROVED", "REJECTED", "DELETED"]:
            raise DataValidationException("更新状态不合法{}".format(status))
        if status == query_order.status:
            raise DataValidationException("状态已变更请勿重复操作")
        if status == "REJECTED" and query_order.status != "SUBMITTED":
            raise StatusUnavailable("只有已提交的加工费用单可以驳回!")
        if status == "APPROVED" and query_order.status != "SUBMITTED":
            raise StatusUnavailable("只有已提交的加工费用单可以审核!")
        # 数据权限校验
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='manufactory',
                           domain='boh.manufactory', branch_id=query_order.machining_center_id)
        receipt_detail = dict(
            updated_by=user_id,
            updated_name=username,
            id=receipt_id,
            status=status,
        )
        res = processing_cost_db.update_processing_cost(receipt_detail=receipt_detail, update_detail=False,
                                                        partner_id=partner_id)
        if res is True:
            ret["receipt_id"] = receipt_id
            ret["result"] = "success"
            update_log = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                main_id=receipt_id,
                action=status,
            )
            processing_cost_db.create_processing_cost_log(receipts_log=update_log)
            if status == "APPROVED":
                # 触发加工费用分摊任务处理
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.PROCESSING_COST_ALLOCATION,
                                    message=dict(partner_id=partner_id,
                                                 user_id=user_id,
                                                 receipt_id=receipt_id))

        return ret


processing_cost_service = ProcessingCostService()
