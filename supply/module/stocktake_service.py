# -*- coding: utf-8 -*-
import base64
from decimal import Decimal
import tempfile
import logging
import json
import pyexcel
import copy

from ..model import stocktake
from ..error.exception import *
from datetime import datetime, timedelta
from supply.utils.helper import convert_to_int, MessageTopic, get_guid, datetime2str, convert_to_decimal, get_uuids, \
    get_branch_map, get_username_map, get_product_map,dict_to_struct,convert_to_float
from ..driver.mysql import session_maker
from ..driver.mysql import DummyTransaction
from sqlalchemy import text
from ..model.stocktake import SupplySTDocDB, \
    SupplySTDocDetailsDB, SupplySTProductDB
from collections import Iterable
from google.protobuf.timestamp_pb2 import Timestamp

from ..utils.auth import branch_list_scope_check, branch_scope_check
from ..utils.stocktake_enum import STS_STATUS, S_TYPE, S_STATUS, ST_STATUS
from ..client.metadata_service import metadata_service
from supply.module.franchisee.helper import *
from supply.driver.mq import mq_producer
from supply.model.supply_doc_code import Supply_doc_code
from supply.model.stocktake_month_code import Stocktake_month_code
from supply.model.doc_plan.doc_plan import doc_plan_repository
from ..client.interactive import interactive_service
from supply.utils.helper import get_region_and_store_feilds_scope_check, get_guid
from ..task.message_service_pub import MessageServicePub
from ..client.inventory_service import inventory_service
from ..client.bom_service import Bom_service
from supply.module.purchase_service import purchase_service
from supply import time_cost
from supply.model.operation_log import TpTransLogModel
from supply import logger


class TimestampAndMethodMixin(object):
    def __init__(self):
        self.created_at = None
        self.updated_at = None

    # 需要转时间戳的props
    def props(self):
        pr = {}
        for name in dir(self):
            value = getattr(self, name)
            if not name.startswith('__') and not callable(value) and not name.startswith('_'):
                if isinstance(value, datetime):
                    # value = value - timedelta(hours=8)
                    timestamp = Timestamp(seconds=int(value.timestamp()))
                    # 下边这个方法有坑，加了8小时
                    # timestamp.FromDatetime(value)
                    pr[name] = timestamp
                else:
                    pr[name] = value
        return pr

    # 不需要转时间戳的props
    def no_timestamp_props(self):
        pr = {}
        for name in dir(self):
            value = getattr(self, name)
            if not name.startswith('__') and not callable(value) and not name.startswith('_'):
                pr[name] = value
        return pr


class StockTakeDoc(TimestampAndMethodMixin):
    def __init__(self):
        super(StockTakeDoc, self).__init__()
        self.id = None
        self.partner_id = None
        self.user_id = None
        self.schedule_id = None
        self.branch_id = None
        self.branch_batch_id = None
        self.type = None
        self.code = None
        self.status = None
        self.process_status = None
        self.target_date = None
        self.calculate_inventory = None
        self.forecasting = None
        self.forecasting_time = None


class StockTakeDocDetail(StockTakeDoc, TimestampAndMethodMixin):
    def __init__(self):
        super(StockTakeDocDetail, self).__init__()
        self.doc_id = None
        self.result_type = None
        self.branch_type = None
        self.review_by = None
        self.store_secondary_id = None
        self.created_by = None
        self.updated_by = None
        self.schedule_code = None
        self.remark = None
        self.st_diff_flag = None
        self.created_name = None
        self.updated_name = None
        self.schedule_name = None
        self.diff_err_message = None
        self.month_err_message = None
        self.original_code = None
        self.original_doc_id = None
        self.is_recreate = None
        self.recreate_code = None
        self.recreate_doc_id = None
        self.submit_name = None
        self.approve_name = None
        self.stocktake_type = None
        self.total_amount = None
        self.total_diff_amount = None
        self.total_sales_amount = None
        self.total_diff_sales_amount = None
        self.attachments = None


class StockTakeProduct(TimestampAndMethodMixin):
    def __init__(self):
        super(StockTakeProduct, self).__init__()
        self.id = None
        self.doc_id = None
        self.partner_id = None
        self.user_id = None
        self.extends = None
        self.product_code = None
        self.product_id = None
        self.product_name = None
        self.quantity = None
        self.accounting_quantity = None
        self.unit_id = None
        self.unit_spec = None
        self.unit_name = None
        self.accounting_unit_id = None
        self.accounting_unit_name = None
        self.accounting_unit_spec = None
        self.ignored = False
        self.deleted = False
        self.is_system = False
        self.material_number = None
        self.item_number = None
        self.unit_rate = None
        self.created_by = None
        self.updated_by = None
        self.units = None
        self.inventory_quantity = None
        self.diff_quantity = None
        self.unit_diff_quantity = None
        self.storage_type = None
        self.branch_id = None
        self.target_date = None
        self.code = None
        self.st_type = None
        self.display_order = None
        self.status = None
        self.is_pda = None
        self.is_empty = None
        self.created_name = None
        self.updated_name = None
        self.is_null = None
        self.tag_quantity = None
        self.convert_accounting_quantity = None
        self.is_bom = None
        self.position_id = None
        self.tax_rate = None
        self.tax_price = None
        self.cost_price = None
        self.amount = None
        self.diff_amount = None
        self.sales_price = None
        self.sales_amount = None
        self.diff_sales_amount = None


class StockTakeProductTagName(TimestampAndMethodMixin):
    def __init__(self):
        super(StockTakeProductTagName, self).__init__()
        self.id = None
        self.doc_id = None
        self.stp_id = None
        self.product_id = None
        self.tag_id = None
        self.tag_name = None
        self.tag_quantity = None
        self.accounting_quantity = None
        self.unit_id = None
        self.unit_spec = None
        self.unit_name = None
        self.unit_rate = None
        self.accounting_unit_id = None
        self.accounting_unit_name = None
        self.accounting_unit_spec = None
        self.partner_id = None
        self.user_id = None
        self.created_by = None
        self.updated_by = None
        self.created_name = None
        self.updated_name = None


class StockTakeProductTags(TimestampAndMethodMixin):
    def __init__(self):
        super(StockTakeProductTags, self).__init__()
        self.id = None
        self.name = None
        self.partner_id = None
        self.user_id = None
        self.created_by = None
        self.updated_by = None
        self.created_name = None
        self.updated_name = None
        self.branch_id = None


class StocktakeMonthMessage(TimestampAndMethodMixin):
    def __init__(self):
        super(StocktakeMonthMessage, self).__init__()
        self.id = None
        self.doc_id = None
        self.status = None
        self.target_date = None
        self.month_message = None
        self.diff_message = None
        self.partner_id = None
        self.user_id = None
        self.created_by = None
        self.updated_by = None


class StocktakeScheduleModule(object):
    """
    盘点业务模块
    """

    def __init__(self):
        self.stocktake_repo = stocktake.StockTakeRepository()
        self.__data_types = {}

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    def get_unit_map(self, partner_id, user_id):
        """query metadata unit
        :return unit_map {id: {code: "", name: "", rate: }}
        """
        units = metadata_service.get_unit_list(return_fields='id,name,code,rate',
                                               partner_id=partner_id, user_id=user_id).get("rows", [])
        unit_map = dict()
        for u in units:
            if isinstance(u, dict) and 'id' in u:
                unit_map[str(u['id'])] = dict()
                unit_map[str(u['id'])]['name'] = u.get('name')
                unit_map[str(u['id'])]['code'] = u.get('code')
                unit_map[str(u['id'])]['rate'] = round(u.get('rate', 1), 5)
        return unit_map

    def list_stores_by_region_ids(self, region_ids=None, partner_id=None, user_id=None):
        """根据管理区域id列表获取包含的门店列表
        region_ids [str(管理区域id)]
        :return store_ids[int]"""
        # 获取该区域下所有子级区域关联的门店要用relation_filters
        relation_filters = {'branch_region': [str(b_id) for b_id in region_ids]}
        store_ids = []
        branch_list = metadata_service.get_store_list(relation_filters=relation_filters,
                                                      return_fields="id",
                                                      partner_id=partner_id, user_id=user_id).get('rows', [])
        if branch_list:
            for store in branch_list:
                store_ids.append(convert_to_int(store.get('id')))
        return store_ids

    def get_request_products_dict(self, request_products):
        products = []
        try:
            for p in request_products:
                product_obj = {}
                product_obj['product_id'] = p.product_id
                products.append(product_obj)
            return products
        except Exception:
            return []

    def get_request_branches_dict(self, request_branches):
        branches = []
        try:
            for b in request_branches:
                branche_obj = {}
                branche_obj['branch_id'] = b.branch_id
                branches.append(branche_obj)
            return branches
        except Exception:
            return []

    def get_request_categories_dict(self, request_categories):
        categories = []
        try:
            for c in request_categories:
                category_obj = {}
                category_obj['category_id'] = c.category_id
                categories.append(category_obj)
            return categories
        except Exception:
            return []

    def get_stocktake_doc_by_id(self, doc_id, partner_id=None, is_details=False):
        return self.stocktake_repo.get_stocktake_doc_by_id(doc_id, partner_id=partner_id, is_details=is_details)

    def list_stocktake_doc_detail(self, request, partner_id=None, user_id=None):
        limit = request.limit
        offset = request.offset
        branch_type = request.branch_type
        include_total = request.include_total
        # 区域ID
        branch_ids = list(request.branch_ids)
        # 门店id
        store_ids = list(request.store_ids)
        start_date = request.start_date
        end_date = request.end_date
        schedule_code = request.schedule_code
        schedule_name = request.schedule_name
        schedule_code_name_conflict = False
        if schedule_name:
            plan_code_list = doc_plan_repository.get_plan_code_list_by_name(plan_name=schedule_name,
                                                                            partner_id=partner_id,
                                                                            plan_type='STOCKTAKE')
            if schedule_code:
                if schedule_code in plan_code_list:
                    pass
                else:
                    schedule_code_name_conflict = True
            else:
                if len(plan_code_list) > 0:
                    schedule_code = plan_code_list
                else:
                    schedule_code_name_conflict = True

        target_date = request.target_date
        stocktake_type = request.stocktake_type
        _type = request._type
        status = request.status
        code = request.code
        is_create = request.is_create
        order = request.order
        sort = request.sort
        ids = list(request.ids) if request.ids else None

        product_ids = list(request.product_ids) if request.product_ids else []
        if not sort:
            sort = 'updated_at'
        if _type:
            _type = [S_TYPE[_typ] for _typ in _type]
        store_status = request.store_status
        if store_status:
            store_status = [S_STATUS[store_statu] for store_statu in store_status]
        if start_date and not isinstance(start_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = start_date.seconds
            start_date = timestamp.ToDatetime()
            start_date = start_date
        if end_date and not isinstance(end_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = end_date.seconds
            end_date = timestamp.ToDatetime()
            end_date = end_date
        if target_date and not isinstance(target_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = target_date.seconds
            target_date = timestamp.ToDatetime()
            target_date = target_date
            # target_date = datetime(int(target_date.year), int(target_date.month),
            #                        int(target_date.day))
            if target_date == datetime(1970, 1, 1):
                target_date = None
        if status:
            status = [ST_STATUS[statu] for statu in status]
        if branch_ids and not store_ids:
            if len(branch_ids) > 0:
                filters = {'relation.branch_region__in': [str(b_id) for b_id in branch_ids]}
                if store_status:
                    filters.update({'open_status__in': store_status})
                store_info_ret = metadata_service.get_store_list(filters=filters, partner_id=partner_id,
                                                                 user_id=user_id)
                store_info = []
                if store_info_ret:
                    store_info = store_info_ret['rows']
                store_ids = [store['id'] for store in store_info if store.get('id')]
        if is_create:
            stocktake_list = self.stocktake_repo.get_create_stocktake_doc_details(partner_id=partner_id,
                                                                                  branch_ids=store_ids,
                                                                                  target_date=target_date)

            if not stocktake_list:
                return stocktake_list
            final_stocktake_obj_list = []
            # 把新不定期也拿出来 乐乐茶无规则
            for stocktake in stocktake_list:
                if stocktake['status'] == 'INITED':
                    final_stocktake_obj_list.append(stocktake)
            return final_stocktake_obj_list
        if schedule_code_name_conflict:
            return []
        return self.stocktake_repo.list_stocktake_doc_details(partner_id=partner_id, limit=limit, offset=offset,
                                                              include_total=include_total,
                                                              branch_ids=store_ids, branch_type=branch_type,
                                                              status=status, start_date=start_date, end_date=end_date,
                                                              schedule_code=schedule_code, _type=_type,
                                                              target_date=target_date, code=code, ids=ids,
                                                              order=order, sort=sort, stocktake_type=stocktake_type,
                                                              product_ids=product_ids)

    def list_stocktake_doc_details(self, partner_id, status, start_date):
        return self.stocktake_repo.list_stocktake_doc_details(partner_id=partner_id,
                                                              status=status, start_date=start_date)

    def list_stocktake_doc_details_by_ids(self, request, partner_id=None, user_id=None):
        """
        :param request:
        :param partner_id:
        :param user_id:
        :return:
        """
        res = dict(rows=[], total=0)
        ids = list(request.ids) if request.ids else []
        print('ids', ids)
        stocktake_list = self.stocktake_repo.list_stocktake_doc_details_by_ids(ids=ids, partner_id=partner_id)
        for st in stocktake_list:
            res['rows'].append(dict(
                id=st[0],
                code=st[1],
                branch_id=st[2],
                status=st[3],
                target_date=self.get_timestamp(st[4]),
                remark=st[5],
                branch_type=st[6],
                created_at=self.get_timestamp(st[7]),
                updated_at=self.get_timestamp(st[8]),
                type=st[9],
                stocktake_type=st[10],
                reject_reason=st[11],
                schedule_name=st[12],
                schedule_code=st[13],
                schedule_id=st[14],
                st_diff_flag=st[15]
            ))
            res['total'] += 1
        if res['total'] > 0:
            self.attach_st_extra_info(res['rows'], partner_id)
            self.attach_st_plan_info(res['rows'], partner_id)
        return res

    @time_cost
    def attach_st_extra_info(self, query_rows: list, partner_id=None):
        doc_ids = [q.get('id') for q in query_rows]
        st_qty_set = self.stocktake_repo.query_st_product_sum_qty(doc_ids=doc_ids, partner_id=partner_id)
        st_qty_set_map = {}
        if st_qty_set:
            for s in st_qty_set:
                st_qty_set_map[s[0]] = False if s[1] else True
        for row in query_rows:
            row['is_empty'] = st_qty_set_map.get(row.get('id'), True)

    @time_cost
    def attach_st_plan_info(self, query_rows: list, partner_id=None):
        """给盘点列表查询结果构建计划相关的信息"""
        plan_ids = [row.get('schedule_id') for row in query_rows if row.get('schedule_id')]
        if len(plan_ids) == 0:
            return query_rows
        control_status_list = doc_plan_repository.list_valid_control_status(partner_id=partner_id, plan_ids=plan_ids)
        plan_status_map = dict()
        for c in control_status_list:
            row = dict(
                start_status=str(c.start_status).split(',') if c.start_status else [],
                end_status=c.end_status,
                time=c.time,
                time_around=c.time_around,
                doc_filter=c.doc_filter
            )
            if c.plan_id in plan_status_map.keys():
                plan_status_map[c.plan_id].append(row)
            else:
                plan_status_map[c.plan_id] = [row]
        for row in query_rows:
            plan_id = row.get('schedule_id')
            target_date = row.get("target_date")
            if isinstance(target_date, Timestamp):
                target_date = datetime.fromtimestamp(target_date.seconds)
                target_date = target_date + timedelta(hours=8)  # 盘点日期要按实际业务日期来算，数据库存的是utc

            plan_data = plan_status_map.get(plan_id)
            if plan_data:
                status_plan = copy.deepcopy(plan_data)
                for plan in status_plan:
                    time_around = plan.get("time_around")
                    if time_around == 'Yesterday':
                        end_date = target_date - timedelta(days=1)
                    elif time_around == "Today":
                        end_date = target_date
                    elif time_around == 'Tomorrow':
                        end_date = target_date + timedelta(days=1)
                    else:
                        end_date = target_date
                    time = plan.get('time')
                    if isinstance(time, str):
                        datetime_local = datetime.strptime(str(end_date)[:10] + " " + time,
                                                           "%Y-%m-%d %H:%M:%S") + timedelta(hours=8)
                        end_time = datetime.strptime(str(end_date)[:10] + " " + str(datetime_local)[10:],
                                                     "%Y-%m-%d %H:%M:%S") - timedelta(hours=8)
                        end_time = self.get_timestamp(end_time)
                    else:
                        end_time = time
                    plan['time'] = end_time
                    # if time and row.get('status') in plan.get('start_status', []):
                    #     if plan.get('end_status') == "APPROVED":
                    #         auto_approve_time = datetime.strptime(str(end_date)[:10] + " " + time, "%Y-%m-%d %H:%M:%S")
                    #         row['auto_approve_time'] = self.get_timestamp(auto_approve_time)
                    #     if plan.get('end_status') == "CANCELLED":
                    #         auto_invalid_time = datetime.strptime(str(end_date)[:10] + " " + time, "%Y-%m-%d %H:%M:%S")
                    #         row['auto_invalid_time'] = self.get_timestamp(auto_invalid_time)
                row['status_plan'] = status_plan
        return query_rows

    @time_cost
    def manually_create_stocktake(self, partner_id, target_date, branch_id, new_doc_id, product_list,
                                  calculate_inventory, user_id, username, schema, domain, branch_type, source=None,attachments=None):
        """门店手动创建盘点单"""
        # if target_date.hour != 16 or target_date.minute != 0 or target_date.second != 0:
        #     target_date = datetime(target_date.year, target_date.month, target_date.day) - timedelta(hours=8)
        target_date = target_date+timedelta(hours=8)
        target_date = datetime(target_date.year, target_date.month, target_date.day)
        # 对传入的门店进行验证
        branch_scope_check(partner_id=partner_id,
                           user_id=user_id,
                           schema=schema,
                           domain=domain,
                           branch_id=branch_id)
        store_info = metadata_service.get_store(store_id=branch_id,
                                                partner_id=partner_id,
                                                user_id=user_id,
                                                return_fields='status')
        if not store_info or store_info.get('status') != 'ENABLED':
            raise DataValidationException("门店输入有误，或者门店不是启用状态。")

        if abs(target_date.year - datetime.now().year) > 10:
            raise DataValidationException("请检查输入的盘点日期。")

        # 根据门店对产品列表进行验证
        product_id_list = list({p.product_id for p in product_list})
        if len(product_id_list) <= 0 and source != "POS":
            raise DataValidationException("未输入要盘点的产品。")
        if len(product_id_list) > 0:
            list_store_product_ret = metadata_service.list_region_product_by_store(store_id=branch_id,
                                                                                   partner_id=partner_id,
                                                                                   product_ids=product_id_list,
                                                                                   filters=dict(allow_stocktake=True),
                                                                                   product_filters={"status": "ENABLED",
                                                                                                    "bom_type__neq": "MANUFACTURE"},
                                                                                   user_id=user_id,
                                                                                   include_total=True,
                                                                                   include_product_units=False,
                                                                                   return_fields="id",
                                                                                   include_product_fields='code,name',
                                                                                   product_relation_filters={},
                                                                                   region="ATTRIBUTE_REGION")
            store_product_id_set = set()
            list_store_product = list_store_product_ret.get('rows',
                                                            []) if list_store_product_ret and 'rows' in list_store_product_ret else []
            if len(list_store_product) < len(product_id_list):
                for store_product_row in list_store_product:
                    store_product_id_set.add(store_product_row.get('id'))
                remain_product_id_set = set(product_id_list).difference(store_product_id_set)
                remain_product_ret = metadata_service.get_product_list(ids=list(remain_product_id_set),
                                                                       include_units=False,
                                                                       return_fields='code,name',
                                                                       partner_id=partner_id,
                                                                       user_id=user_id)
                errorMessage = "有些产品不可盘点。"
                remain_product_list = remain_product_ret.get('rows',
                                                             []) if remain_product_ret and 'rows' in remain_product_ret else []
                for remain_p in remain_product_list:
                    if isinstance(remain_p, dict):
                        errorMessage += " " + remain_p["name"] + "[" + remain_p["code"] + "]"
                raise DataValidationException(errorMessage)

            final_product_ids = product_id_list
            main_products_ret = metadata_service.get_product_list(ids=final_product_ids,
                                                                  include_units=True,
                                                                  return_fields='id,code,name,storage_type,status',
                                                                  partner_id=partner_id,
                                                                  user_id=user_id)
            main_products = []
            unit_ids = []
            final_product_info_dict = {}
            if main_products_ret:
                main_products = main_products_ret['rows']
            check_product_ids = []
            for main_p in main_products:
                if isinstance(main_p, dict):
                    product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
                    final_product = dict()
                    units = main_p['units'] if 'units' in main_p else []
                    has_unit = False
                    has_default_unit = False
                    if product_id and product_id in final_product_ids and product_id not in check_product_ids:
                        final_product['id'] = product_id
                        check_product_ids.append(product_id)
                        if 'code' in main_p and main_p['code']:
                            final_product['code'] = main_p['code']
                        if 'name' in main_p and main_p['name']:
                            final_product['name'] = main_p['name']
                        if 'storage_type' in main_p and main_p['storage_type']:
                            final_product['storage_type'] = main_p['storage_type']
                        # 获取核算单位和盘点单位
                        if isinstance(units, list) and len(units) > 0:
                            for unit in units:
                                if isinstance(unit, dict) and 'id' in unit and unit['id']:
                                    if not has_default_unit and 'default' in unit and unit['default']:
                                        final_product['default_unit'] = convert_to_int(unit['id'])
                                        has_default_unit = True
                                    if unit.get('default_stocktake'):
                                        final_product['unit'] = convert_to_int(unit['id'])
                                        final_product['unit_rate'] = round(unit.get('rate'), 7) if unit.get(
                                            'rate') else None
                                        has_unit = True
                                    if not has_unit and unit.get('stocktake'):
                                        final_product['unit'] = convert_to_int(unit['id'])
                                        final_product['unit_rate'] = round(unit.get('rate'), 7) if unit.get(
                                            'rate') else None
                                        has_unit = True
                                    # if has_unit and has_default_unit:
                                    #     break
                    # 商品必须要有核算单位和盘点单位
                    if len(final_product) > 0 and has_default_unit and has_unit:
                        final_product_info_dict[product_id] = final_product
                        if str(final_product['unit']) not in unit_ids:
                            unit_ids.append(str(final_product['unit']))
                        if str(final_product['default_unit']) not in unit_ids:
                            unit_ids.append(str(final_product['default_unit']))
            unit_dict = {}
            if len(unit_ids) > 0:
                units_ret = metadata_service.get_unit_list(return_fields='id,name,code',
                                                           ids=[int(p) for p in unit_ids],
                                                           partner_id=partner_id,
                                                           user_id=user_id)
                units = []
                if units_ret:
                    units = units_ret['rows']
                if isinstance(units, list):
                    for u in units:
                        if isinstance(u, dict) and 'id' in u and u['id'] in unit_ids:
                            unit_dict[str(u['id'])] = dict()
                            if 'name' in u:
                                unit_dict[str(u['id'])]['name'] = u['name']
                            if 'code' in u:
                                unit_dict[str(u['id'])]['code'] = u['code']

            if (len(main_products) < 1):
                return False
            # 商品的含税单价和税率
            product_unit_adjust_tax_price_map = {}
            product_unit_adjust_tax_ratio_map = {}
            product_unit_adjust_sales_price_map = {}
            if branch_type == "FRS_STORE":
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                    store_id=branch_id, user_id=user_id, partner_id=partner_id, product_ids=product_id_list,
                    include_sales_price=True)

        db_session = session_maker()
        try:
            stocktake_doc = dict(
                id=new_doc_id,
                batch_id=None,
                branch_batch_id=new_doc_id,
                branch_id=branch_id,
                type='',
                target_date=target_date,
                schedule_id=None,
                status='INITED',
                process_status='INITED',
                calculate_inventory=calculate_inventory,
                partner_id=partner_id,
                user_id=user_id,
                created_name=username,
                updated_name=username,
                created_by=user_id,
                updated_by=user_id,
            )
            stocktake_doc['code'] = Supply_doc_code.get_code_by_type('STORE_ST', partner_id, None)
            stocktake_doc_detail = dict(
                doc_id=stocktake_doc['id'],
                batch_id=stocktake_doc['batch_id'],
                # 盘点属性
                stocktake_type='MANUAL',
                branch_batch_id=stocktake_doc['branch_batch_id'],
                branch_id=stocktake_doc['branch_id'],
                code=stocktake_doc['code'],
                schedule_id=None,
                schedule_code=None,
                schedule_name=None,
                target_date=stocktake_doc['target_date'],
                type='',
                status='INITED',
                process_status='INITED',
                branch_type=branch_type,
                calculate_inventory=calculate_inventory,
                partner_id=partner_id,
                user_id=user_id,
                created_name=username,
                updated_name=username,
                created_by=user_id,
                updated_by=user_id,
                attachments=json.dumps(dict(attachments=attachments)) if attachments else None
            )

            stocktake_products = []
            processed_product_list = set()
            for list_item in product_list:
                product_id = list_item.product_id
                position_id = list_item.position_id
                quantity = list_item.quantity
                # 暂时不考虑仓位，设置为1
                position_id = 1
                product_position_key = f"{product_id}-{position_id}"
                if product_position_key in processed_product_list:
                    continue
                processed_product_list.add(product_position_key)
                p = final_product_info_dict.get(product_id)
                if not p:
                    continue
                stocktake_product = dict(
                    id=get_guid(),
                    doc_id=stocktake_doc['id'],
                    product_id=p['id'],
                    product_code=p.get('code'),
                    product_name=p.get('name'),
                    storage_type=p.get('storage_type'),
                    unit_rate=p.get('unit_rate'),
                    accounting_quantity=Decimal(p.get('unit_rate')) * Decimal(quantity),
                    accounting_unit_id=p.get('default_unit'),
                    unit_id=p.get('unit'),
                    is_system=False,
                    partner_id=partner_id,
                    user_id=user_id,
                    created_name=username,
                    updated_name=username,
                    branch_id=stocktake_doc_detail['branch_id'],
                    target_date=stocktake_doc_detail['target_date'],
                    status='INITED',
                    created_by=user_id,
                    updated_by=user_id,
                    position_id=position_id,
                    quantity=quantity,
                    is_null=True
                )
                if str(stocktake_product['unit_id']) in unit_dict:
                    stocktake_product['unit_name'] = \
                        unit_dict[str(stocktake_product['unit_id'])].get('name', '')
                    stocktake_product['unit_spec'] = \
                        unit_dict[str(stocktake_product['unit_id'])].get('code')
                if str(stocktake_product['accounting_unit_id']) in unit_dict:
                    stocktake_product['accounting_unit_name'] = \
                        unit_dict[str(stocktake_product['accounting_unit_id'])].get('name')
                    stocktake_product['accounting_unit_spec'] = \
                        unit_dict[str(stocktake_product['accounting_unit_id'])].get('code')
                if branch_type == "FRS_STORE":
                    fill_adjust_tax_price_tax_rate(stocktake_product, product_unit_adjust_tax_price_map,
                                                   product_unit_adjust_tax_ratio_map,
                                                   stocktake_product['product_id'], 1, 0, 'amount', 'sales_amount',
                                                   product_unit_adjust_sales_price_map)
                stocktake_products.append(stocktake_product)
            doc_log_db = stocktake.SupplySTDocLogDB()
            doc_log_db.id = get_guid()
            doc_log_db.doc_id = stocktake_doc['id']
            doc_log_db.partner_id = stocktake_doc['partner_id']
            doc_log_db.doc_status = stocktake_doc['status']
            doc_log_db.created_at = datetime.utcnow()
            doc_log_db.created_by = user_id
            doc_log_db.reason = stocktake_doc_detail['stocktake_type']
            db_session.add(doc_log_db)
            if (source != "POS") or (source == "POS" and len(product_id_list) == 0):
                db_session.bulk_insert_mappings(stocktake.SupplySTDocDB, [stocktake_doc])
                db_session.bulk_insert_mappings(stocktake.SupplySTDocDetailsDB, [stocktake_doc_detail])
            db_session.bulk_insert_mappings(stocktake.SupplySTProductDB, stocktake_products)
            db_session.commit()
            self.stocktake_repo.recalculate_stocktake_total_amount(new_doc_id)
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

        # 清理待办缓存
        mq_producer.publish(topic_group="boh_order",
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=branch_id,
                                         doc_type="stocktake"))
        return True

    def getNewStocktakeId(self):
        return get_guid()

    @time_cost
    def list_stocktake_store_product(self, store_id, limit=-1, offset=0, include_total=None, search=None,
                                     category_ids=None,
                                     search_fields=None, partner_id=None, user_id=None, attach_price=False,
                                     exc_codes=None, in_codes=None, exc_upcs=None, in_upcs=None,
                                     order_by=None, combine_result=False):
        order_by_inventory = order_by == 'real_inventory'
        # if order_by_inventory and (not category_ids or not search_fields):
        #     raise DataValidationException('影响库存排序必传分类或者搜索参数')
        # 分页参数处理
        real_limit = limit
        real_offset = offset
        if order_by_inventory:
            real_limit=-1
            real_offset=0
        if category_ids:
            product_relation_filters = {"product_category__in": [str(id) for id in category_ids]}
        else:
            product_relation_filters = None
        product_filters = {"status": "ENABLED", "bom_type__neq": "MANUFACTURE"}
        print('in_upcs', in_upcs)
        if exc_codes:
            if len(exc_codes) > 0:
                codes = []
                for e in exc_codes:
                    codes.append(e)
                product_filters['code__nin'] = codes
        if in_codes:
            if len(in_codes) > 0:
                codes = []
                for e in in_codes:
                    codes.append(e)
                product_filters['code__in'] = codes
        if exc_upcs:
            if len(exc_upcs) > 0:
                codes = []
                for e in exc_upcs:
                    codes.append(e)
                product_filters['upc__nin'] = codes
        if in_upcs:
            if len(in_upcs) > 0:
                codes = []
                for e in in_upcs:
                    codes.append(e)
                product_filters['upc__in'] = codes
        logging.info("product_filters:{}".format(product_filters))
        list_store_product_ret = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_stocktake=True),
            product_filters=product_filters,
            product_search=search,
            product_search_fields=search_fields,
            user_id=user_id,
            offset=real_offset,
            limit=real_limit,
            include_total=include_total,
            include_product_units=True,
            return_fields="allow_stocktake",
            include_product_fields='code,name,category,category_name,model_name,barcode',
            product_relation_filters=product_relation_filters,
            region="ATTRIBUTE_REGION"
        )
        print('list_store_product_ret', list_store_product_ret)
        # 商品的含税单价和税率
        product_id_list = []
        if list_store_product_ret:
            list_store_product = list_store_product_ret.get('rows', [])
            category_ids = [int(p.get('category')) for p in list_store_product if p.get('category')]
            for product in list_store_product:
                has_stocktake_unit = False
                has_account_unit = False
                units = product.get('units', [])
                for unit in units:
                    unit['id'] = int(unit.get('id'))
                    if 'stocktake' in unit and unit['stocktake']:
                        has_stocktake_unit = True
                    elif 'default_stocktake' in unit and unit['default_stocktake']:
                        has_stocktake_unit = True
                    if 'default' in unit and unit['default']:
                        has_account_unit = True
                if not has_stocktake_unit or not has_account_unit:
                    continue
                product_id_list.append(int(product.get('product_id')))
        product_unit_adjust_tax_price_map = None
        product_unit_adjust_tax_ratio_map = None
        product_unit_adjust_sales_price_map = None
        if attach_price is True:
            product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                store_id=store_id, user_id=user_id, partner_id=partner_id, product_ids=product_id_list,
                include_sales_price=True)

        store_product_stocktake = []
        final_product_ids = []
        total = 0
        store_product_stocktake_dict = {}
        if list_store_product_ret:
            list_store_product = list_store_product_ret.get('rows', [])
            total = list_store_product_ret.get('total') if include_total and list_store_product_ret.get('total') else 0
            category_ids = [int(p.get('category')) for p in list_store_product if p.get('category')]
            cate_dict = dict()
            category_list = metadata_service.get_product_category_list(ids=category_ids, return_fields="id,code,name",
                                                                       partner_id=partner_id,
                                                                       user_id=user_id).get('rows', [])
            for c in category_list:
                cate_dict[convert_to_int(c.get('id'))] = c
            for product in list_store_product:
                product_dict = dict(
                    product_id=int(product.get('product_id')),
                    product_code=product.get('code'),
                    product_name=product.get('name'),
                    model_name=product.get('model_name')
                )
                if product.get('category'):
                    category_id = int(product.get('category'))
                    product_dict['category_id'] = category_id
                    if isinstance(cate_dict.get(category_id), dict):
                        product_dict["category_name"] = cate_dict.get(category_id).get('name')
                has_stocktake_unit = False
                has_account_unit = False
                units = product.get('units', [])
                for unit in units:
                    unit['id'] = int(unit.get('id'))
                    if 'stocktake' in unit and unit['stocktake']:
                        has_stocktake_unit = True
                    elif 'default_stocktake' in unit and unit['default_stocktake']:
                        has_stocktake_unit = True
                    if 'default' in unit and unit['default']:
                        has_account_unit = True
                    fill_adjust_tax_price_tax_rate(unit, product_unit_adjust_tax_price_map,
                                                   product_unit_adjust_tax_ratio_map,
                                                   product.get('product_id'), unit['rate'],
                                                   product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                if not has_stocktake_unit or not has_account_unit or not product.get('allow_stocktake'):
                    # if attach_price is not True and (len(exc_upcs) == 0 and len(in_upcs) == 0):
                    #     # print('ssssssss')
                    #     if total > 0:
                    #         total -= 1
                    #     continue
                    # else:
                    units = []
                product_dict['units'] = units
                product_dict['barcode'] = product['extends'].get('barcode') if product.get('extends') else []
                store_product_stocktake.append(product_dict)
                final_product_ids.append(product_dict['product_id'])
                store_product_stocktake_dict[product_dict['product_id']] = product_dict

        # 新增按照实时库存排序
        inv_unchanged_products, inv_changed_products = [], []
        final_result = store_product_stocktake
        if order_by_inventory and final_product_ids:
            total = len(final_product_ids)
            final_result = []
            order_result = inventory_service.query_branch_inventory_sort(partner_id, user_id, store_id,
                                                                         product_ids=final_product_ids,offset=offset,limit=limit).get('rows')
            # logging.info(f'库存排序：{order_result}')
            if order_result:
                for i in order_result:
                    tmp_record = store_product_stocktake_dict.get(int(i.get('product_id')))
                    if tmp_record:
                        tmp_record['real_inventory_qty'] = i.get('qty')
                        final_result.append(tmp_record)

        if include_total:
            return total, final_result, inv_unchanged_products
        return final_result, inv_unchanged_products

    @time_cost
    def list_stocktake_store_product_category(self, store_id, partner_id=None, user_id=None):
        list_store_product_ret = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_stocktake=True),
            product_filters={"status": "ENABLED", "bom_type__neq": "MANUFACTURE"},
            user_id=user_id,
            include_product_units=False,
            return_fields="allow_stocktake",
            include_product_fields='category,category_name',
            region="ATTRIBUTE_REGION"
        )
        store_product_category_ids = []
        store_product_category_names = {}
        store_product_category_product_count = {}
        if list_store_product_ret:
            list_store_product = list_store_product_ret.get('rows', [])
            for product in list_store_product:
                if product.get('category') and product.get('allow_stocktake'):
                    category_id = int(product.get('category'))
                    if category_id not in store_product_category_product_count:
                        store_product_category_ids.append(category_id)
                        store_product_category_names[category_id] = product.get('category_name')
                        store_product_category_product_count[category_id] = 1
                    else:
                        store_product_category_product_count[category_id] = store_product_category_product_count[
                                                                                category_id] + 1
        category_list = metadata_service.get_product_category_list(
            ids=store_product_category_ids if len(store_product_category_ids) == 1 else None,
            return_fields="id,parent_id", partner_id=partner_id, user_id=user_id).get('rows',
                                                                                      []) if store_product_category_ids else []
        category_children_map = {}
        for category in category_list:
            category_id = int(category["id"])
            category_parent_id = int(category.get("parent_id", 0))
            if category_parent_id:
                if category_parent_id in category_children_map:
                    if category_id not in category_children_map[category_parent_id]:
                        category_children_map[category_parent_id].append(category_id)
                else:
                    category_children_map[category_parent_id] = [category_id]
        store_product_category_product_count_sum = {}
        category_children_ret_map = {}
        for category_id in store_product_category_ids:
            store_product_category_product_count_sum[category_id] = store_product_category_product_count[category_id]
            for category_child_id in get_category_children(category_id, category_children_map,
                                                           category_children_ret_map):
                store_product_category_product_count_sum[category_id] += store_product_category_product_count.get(
                    category_child_id, 0)
        return [{'category_id': category_id, 'category_name': store_product_category_names[category_id],
                 'product_count': store_product_category_product_count_sum[category_id]} for category_id in
                store_product_category_ids]

    @time_cost
    def update_stocktake_products_with_quantity(self, doc_id, update_products, imp_tags_pro_map=None,
                                                allow_status=None, partner_id=None, user_id=None, username=None,
                                                branch_id=None, attach_price=False, attachments=None):
        """
        更新盘点单的商品
        :param doc_id:
        :param update_products:
        :param allow_status:
        :param user_id:
        :param partner_id
        :param username
        :param imp_tags_pro_map: {product_code: [tag_dict, ...]} 盘点导入
        :param branch_id
        :param attach_price 是否计算价格
        :return:
        """

        record_ids = [p.id for p in update_products]
        products_exist = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id,
                                                                               record_ids=record_ids,
                                                                               partner_id=partner_id)
        products_map = {}
        product_ids = []
        if len(products_exist) < 1:
            return True
        if isinstance(products_exist, list):
            for p in products_exist:
                products_map[p.get('id')] = p.get('product_id')
                if p.get('product_id') not in product_ids:
                    product_ids.append(p.get('product_id'))
        unit_dict = self.get_unit_map(partner_id=partner_id, user_id=user_id)
        units_rate_dict = {}
        main_products_ret = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                              return_fields='id,code,name',
                                                              partner_id=partner_id, user_id=user_id
                                                              )
        products_info_map = {}
        main_products = []
        if main_products_ret:
            main_products = main_products_ret['rows']
        for main_p in main_products:
            product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
            products_info_map[product_id] = {}
            products_info_map[product_id]['product_name'] = main_p['name'] if 'name' in main_p and main_p[
                'name'] else None
            products_info_map[product_id]['product_code'] = main_p['code'] if 'code' in main_p and main_p[
                'code'] else None
            if main_p.get('units'):
                for unit in main_p['units']:
                    if unit.get('default'):
                        products_info_map[product_id]['accounting_unit_id'] = unit['id'] if 'id' in unit and unit[
                            'id'] else None
                        products_info_map[product_id]['accounting_unit_spec'] = unit_dict[str(unit['id'])]['code']
                        products_info_map[product_id]['accounting_unit_name'] = unit_dict[str(unit['id'])]['name']
                    key = str(product_id) + str(unit['id'])
                    units_rate_dict[key] = round(unit.get('rate'), 7) if unit.get('rate') else None
        # 获取商品的含税单价和税率
        product_unit_adjust_tax_price_map = None
        product_unit_adjust_tax_ratio_map = None
        product_unit_adjust_sales_price_map = None
        if attach_price is True:
            product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                store_id=branch_id, user_id=user_id, partner_id=partner_id, product_ids=product_ids,
                include_sales_price=True)
            # 商品实时库存, 保存时还是要取实时库存, 需要计算差异数量, 差异金额
            products_inventory = inventory_service.get_products_inventory_by_branch_id(
                branch_id=branch_id,
                partner_id=partner_id,
                user_id=user_id,
                product_ids=product_ids,
                detail=True
            )
        products = []
        product_tags = []
        product_tags_exist = []
        del_tag_ids = []
        # pc与pda交换
        # 切换pc，清楚pda数据，切换pda，pc数量变为0
        update_o_product_tags_ids = []
        if update_products:
            # doc_product_tags_map 来获取存在的标签记录的主键id列表，多单位下一个商品下的标签可能会重复
            doc_product_tags_map = {}  # {product_id: {str(tag_id+unit_id): query_tag}}
            # 通过盘点单id捞出该盘点单对应的商品标签记录
            doc_product_tags = self.stocktake_repo.list_doc_product_tags(doc_id=doc_id, partner_id=partner_id)
            if doc_product_tags:
                # 盘点导入，清空之前存在的标签记录，再重新覆盖插入
                if imp_tags_pro_map:
                    del_tag_ids.extend([tag.id for tag in doc_product_tags])
                # 页面更新需要根据标签和单位判断该条数据是否存在
                for tag in doc_product_tags:
                    tag_id = tag.tag_id
                    product_id = tag.product_id
                    unit_id = tag.unit_id
                    key = str(tag_id) + str(unit_id)
                    if product_id not in doc_product_tags_map.keys():
                        doc_product_tags_map[product_id] = {key: tag}
                    else:
                        if key not in doc_product_tags_map[product_id].keys():
                            doc_product_tags_map[product_id][key] = tag
            # if imp_tags_pro_map:
            #     # 通过盘点单id捞出该盘点单对应的商品标签记录
            #     doc_product_tags = self.stocktake_repo.list_doc_product_tags(doc_id=doc_id, partner_id=partner_id)
            #     if doc_product_tags:
            #         for tag in doc_product_tags:
            #             del_tag_ids.append(tag.id)
            for p in update_products:
                product_dict = dict(id=p.id)
                product_id = products_map.get(p.id)
                if not product_id:
                    continue
                product_dict['product_id'] = product_id
                product_info = products_info_map.get(int(product_id))
                if not product_info:
                    continue
                product_dict['product_code'] = product_dict['material_number'] = product_info.get('product_code')
                product_dict['product_name'] = product_info.get('product_name')
                product_dict['accounting_unit_id'] = product_info.get('accounting_unit_id')
                product_dict['accounting_unit_spec'] = product_info.get('accounting_unit_spec')
                product_dict['accounting_unit_name'] = product_info.get('accounting_unit_name')
                product_dict['is_pda'] = p.is_pda
                product_dict['is_empty'] = p.is_empty
                # if not p.unit_id:
                #     raise StocktakeUnitException(
                #         "存在无盘点单位商品，请配好盘点单位%s" % (
                #             p.id))
                if p.unit_id:
                    product_dict['unit_id'] = p.unit_id
                    product_dict['unit_name'] = unit_dict[str(p.unit_id)]['name']
                    product_dict['unit_spec'] = unit_dict[str(p.unit_id)]['code']
                    key = str(product_dict['product_id']) + str(p.unit_id)
                    product_dict['unit_rate'] = units_rate_dict[key] if units_rate_dict.get(key) else 1
                product_dict['is_null'] = p.is_null
                # PC盘点数量置空操作
                if p.is_null:
                    product_dict['quantity'] = None
                    product_dict['accounting_quantity'] = None
                    product_dict['convert_accounting_quantity'] = None
                else:
                    product_dict['is_null'] = False
                    product_dict['quantity'] = p.quantity
                    product_dict['accounting_quantity'] = convert_to_decimal(p.quantity) * convert_to_decimal(
                        product_dict.get('unit_rate')) if p.quantity else 0
                    product_dict['convert_accounting_quantity'] = convert_to_decimal(p.quantity) * convert_to_decimal(
                        product_dict.get('unit_rate')) if p.quantity else 0
                if imp_tags_pro_map or (p.is_pda and p.tag_products):
                    if imp_tags_pro_map:
                        product_code = product_dict['product_code']
                        imp_tags = imp_tags_pro_map.get(str(product_code), [])
                        for tag in imp_tags:
                            if tag.get('unit_id') and p.id:
                                product_tag_dict = dict(
                                    id=get_guid(),
                                    tag_id=tag.get('tag_id', 2),
                                    tag_name=tag.get('tag_name', '默认标签'),
                                    stp_id=p.id,
                                    doc_id=doc_id,
                                    created_at=datetime.now(),
                                    product_id=product_id,
                                    partner_id=partner_id,
                                    user_id=user_id,
                                    created_by=user_id,
                                    unit_rate=1,  # 给个默认值
                                )
                                unit_id = tag.get('unit_id')
                                tag_quantity = tag.get('tag_quantity', 0)
                                product_tag_dict['unit_id'] = unit_id
                                product_tag_dict['tag_quantity'] = tag_quantity
                                if unit_id:
                                    key = str(product_id) + str(unit_id)
                                    product_tag_dict['unit_rate'] = units_rate_dict[key] if units_rate_dict.get(
                                        key) else 1
                                    product_tag_dict['unit_name'] = unit_dict[str(unit_id)]['name']
                                    product_tag_dict['unit_spec'] = unit_dict[str(unit_id)]['code']
                                product_tag_dict['accounting_unit_id'] = product_dict['accounting_unit_id']
                                product_tag_dict['accounting_unit_spec'] = product_dict['accounting_unit_spec']
                                product_tag_dict['accounting_unit_name'] = product_dict['accounting_unit_name']
                                product_tag_dict['accounting_quantity'] = convert_to_decimal(
                                    tag_quantity) * convert_to_decimal(
                                    product_tag_dict['unit_rate']) if tag_quantity else 0
                                product_dict['quantity'] += tag_quantity  # 同步更新商品盘点数量
                                product_dict['accounting_quantity'] += product_tag_dict['accounting_quantity']
                                product_dict['convert_accounting_quantity'] += product_tag_dict['accounting_quantity']
                                product_tags.append(product_tag_dict)

                    elif p.tag_products:
                        tag_product_map = doc_product_tags_map.get(product_id, {})
                        update_tag_keys = [str(tag.tag_id) + str(tag.unit_id) for tag in p.tag_products]
                        for tag_key, tag in tag_product_map.items():
                            if tag_key not in update_tag_keys:
                                tag_quantity = tag.tag_quantity if tag.tag_quantity else 0
                                accounting_quantity = tag.accounting_quantity if tag.accounting_quantity else 0
                                product_dict['quantity'] += float(tag_quantity)  # 将盘点单中原来的标签数量和这次更新的标签数量相加
                                product_dict['accounting_quantity'] += accounting_quantity
                                product_dict['convert_accounting_quantity'] += accounting_quantity
                        for tag in p.tag_products:
                            if tag.unit_id and p.id:
                                product_tag_dict = dict(
                                    tag_id=tag.tag_id,
                                    tag_name=tag.tag_name,
                                    unit_id=tag.unit_id
                                )
                                tag_quantity = tag.tag_quantity if tag.tag_quantity else 0
                                product_tag_dict['tag_quantity'] = tag_quantity
                                product_dict['quantity'] += tag_quantity  # 同步更新商品盘点数量
                                product_tag_dict['stp_id'] = p.id
                                product_tag_dict['unit_rate'] = 1  # 给个默认值
                                if tag.unit_id:
                                    key = str(product_dict['product_id']) + str(tag.unit_id)
                                    product_tag_dict['unit_rate'] = units_rate_dict[key] if units_rate_dict.get(
                                        key) else 1
                                    product_tag_dict['unit_name'] = unit_dict[str(tag.unit_id)]['name']
                                    product_tag_dict['unit_spec'] = unit_dict[str(tag.unit_id)]['code']
                                product_tag_dict['accounting_unit_id'] = product_dict['accounting_unit_id']
                                product_tag_dict['accounting_unit_spec'] = product_dict['accounting_unit_spec']
                                product_tag_dict['accounting_unit_name'] = product_dict['accounting_unit_name']
                                product_tag_dict['accounting_quantity'] = convert_to_decimal(
                                    tag.tag_quantity) * convert_to_decimal(
                                    product_tag_dict['unit_rate']) if tag.tag_quantity else 0
                                # 根据标签id取原盘点单中的标签记录
                                key = str(tag.tag_id) + str(tag.unit_id)
                                tag_product = tag_product_map.get(key)
                                # 存在更新
                                if tag_product:
                                    product_tag_dict['id'] = tag_product.id
                                    product_tags_exist.append(product_tag_dict)
                                else:
                                    product_tag_dict['created_at'] = datetime.now()
                                    product_tag_dict['id'] = get_guid()
                                    product_tag_dict['doc_id'] = doc_id
                                    product_tag_dict['product_id'] = product_dict['product_id']
                                    product_tag_dict['partner_id'] = partner_id
                                    product_tag_dict['user_id'] = user_id
                                    product_tag_dict['created_by'] = user_id
                                    product_tags.append(product_tag_dict)
                                product_dict['accounting_quantity'] += product_tag_dict['accounting_quantity']
                                product_dict['convert_accounting_quantity'] += product_tag_dict['accounting_quantity']
                        del_tag_ids = [convert_to_int(_id) for _id in p.del_tag_ids] if p.del_tag_ids else []
                else:
                    update_o_product_tags_ids.append(p.id)
                if attach_price:
                    fill_adjust_tax_price_tax_rate(product_dict, product_unit_adjust_tax_price_map,
                                                   product_unit_adjust_tax_ratio_map,
                                                   product_dict['product_id'], 1, product_dict['accounting_quantity'],
                                                   'amount', 'sales_amount',
                                                   product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                    quantity_avail = Decimal(0)
                    if products_inventory and str(product_id) in products_inventory and products_inventory[
                        str(product_id)]:
                        inventory_obj = products_inventory[str(product_id)]
                        if inventory_obj:
                            quantity_avail += convert_to_decimal(
                                inventory_obj.get('quantity_avail') if inventory_obj.get('quantity_avail') else 0)
                    product_dict["diff_quantity"] = (Decimal(product_dict['accounting_quantity']) if product_dict[
                        'accounting_quantity'] else Decimal(0)) - Decimal(quantity_avail)
                    product_dict["diff_amount"] = product_dict["diff_quantity"] * Decimal(
                        product_dict.get("tax_price", 0))
                products.append(product_dict)
        if len(products) == 0 and attachments is None:
            return True
        elif attach_price:
            self.stocktake_repo.update_stocktake_products_with_price(doc_id, products)
        return self.stocktake_repo.update_stocktake_products_with_quantity(doc_id, products,
                                                                           product_tags_exist=product_tags_exist,
                                                                           product_tags=product_tags,
                                                                           allow_status=allow_status,
                                                                           partner_id=partner_id,
                                                                           update_o_product_tags_ids=update_o_product_tags_ids,
                                                                           del_tag_ids=del_tag_ids,
                                                                           user_id=user_id, username=username,
                                                                           attachments=attachments)

    def list_stocktake_product(self, doc_id, partner_id=None, limit=None, offset=None, include_total=False,
                               include_unit=False, diff_top=False, diff_bottom=False, category_id=None,
                               storage_type=None, product_name=None, is_diff_count=None, user_id=None, branch_id=None,
                               **kwargs):

        if is_diff_count and str(is_diff_count).upper() == 'TRUE':
            is_diff_count = True
        elif is_diff_count and str(is_diff_count).upper() == 'FALSE':
            is_diff_count = False
        else:
            is_diff_count = True
        p_ids = []
        if category_id or product_name or storage_type:
            search = search_fields = None
            if product_name:
                search = product_name
                search_fields = 'name,code'
            products = []
            filters = {'storage_type': storage_type}
            relation_filters = {'product_category': [str(category_id)]}
            products_ret = metadata_service.get_product_list(filters=filters,
                                                             relation_filters=relation_filters,
                                                             search=search,
                                                             search_fields=search_fields,
                                                             return_fields='id,name,code',
                                                             partner_id=partner_id, user_id=user_id
                                                             )
            if products_ret:
                products = products_ret['rows']
            if products and isinstance(products, list) and len(products) > 0:
                for product in products:
                    p_ids.append(convert_to_int(product['id']))
            else:
                if include_total:
                    return 0, []
                return []
        product_unit_adjust_tax_price_map = {}
        product_unit_adjust_tax_ratio_map = {}
        product_unit_adjust_sales_price_map = {}
        products = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id, partner_id=partner_id, limit=limit,
                                                                         offset=offset, include_total=True,
                                                                         diff_top=diff_top, diff_bottom=diff_bottom,
                                                                         product_ids=p_ids, is_diff_count=is_diff_count,
                                                                         product_unit_adjust_tax_price_map=product_unit_adjust_tax_price_map,
                                                                         product_unit_adjust_tax_ratio_map=product_unit_adjust_tax_ratio_map,
                                                                         product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map,
                                                                         **kwargs)
        count, products_tmp = products
        # logging.info("products_tmp:{}".format(products_tmp))
        if isinstance(products_tmp, list):
            product_ids = []
            unit_dict = {}
            units_ret = metadata_service.get_unit_list(return_fields='id,name,code',
                                                       partner_id=partner_id,
                                                       user_id=user_id)
            units = []
            if units_ret:
                units = units_ret['rows']
            if isinstance(units, list):
                for u in units:
                    if isinstance(u, dict) and 'id' in u:
                        unit_dict[str(u['id'])] = dict()
                        if 'name' in u:
                            unit_dict[str(u['id'])]['name'] = u['name']
            for p in products_tmp:
                product_ids.append(p.get('product_id'))
            if kwargs.get('attach_price') is True:
                # 商品的含税单价和税率
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                    store_id=branch_id, user_id=user_id, partner_id=partner_id, product_ids=product_ids,
                    include_sales_price=True, overwrite=False,
                    product_unit_adjust_tax_price_map=product_unit_adjust_tax_price_map,
                    product_unit_adjust_tax_ratio_map=product_unit_adjust_tax_ratio_map,
                    product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)

                for product_data in products_tmp:
                    # logging.info("products_tmp-product_data-----:{}".format(product_data))
                    # logging.info("product_data-----:{}".format(product_data.get("product_tags")))
                    for product_tags_data_dict in product_data.get("product_tags", []):
                        # logging.info("product_tags_data_dict-----:{}".format(product_tags_data_dict))
                        fill_adjust_tax_price_tax_rate(product_tags_data_dict, product_unit_adjust_tax_price_map,
                                                       product_unit_adjust_tax_ratio_map,
                                                       product_tags_data_dict.get("product_id", 0),
                                                       product_tags_data_dict.get("unit_rate", 1),
                                                       product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                        # logging.info("product_tags_data_dicto===={}".format(product_tags_data_dict))

            main_products_ret = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                                  return_fields='id,model_name,barcode,category',
                                                                  partner_id=partner_id, user_id=user_id
                                                                  )
            main_products = {}
            if main_products_ret:
                main_products = main_products_ret['rows']
            main_products_info_map = {}
            for main_p in main_products:
                units = main_p['units'] if 'units' in main_p else []
                product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
                product_units = []
                if isinstance(units, list) and len(units) > 0:
                    for unit in units:
                        if isinstance(unit, dict) and unit.get('stocktake') and 'id' in unit:
                            unit_data = dict(
                                unit_id=convert_to_int(unit.get('id')),
                                default=unit.get('default'),
                                stocktake=True
                            )
                            # 获取单位名
                            if str(unit['id']) in unit_dict:
                                if 'name' in unit_dict[str(unit['id'])]:
                                    unit_data['unit_name'] = \
                                        unit_dict[str(unit['id'])]['name']
                            unit_data['unit_rate'] = round(unit.get('rate'), 7) if unit.get('rate') else None
                            if kwargs.get('attach_price') is True:
                                fill_adjust_tax_price_tax_rate(unit_data, product_unit_adjust_tax_price_map,
                                                               product_unit_adjust_tax_ratio_map, product_id,
                                                               unit_data['unit_rate'],
                                                               product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                            product_units.append(unit_data)
                barcode = []
                extends = main_p.get('extends')
                if extends and isinstance(extends, dict):
                    barcode = extends.get('barcode')
                info = dict(
                    units=product_units,
                    spec=main_p.get('model_name'),
                    category_id=convert_to_int(main_p.get('category')),
                    category_name=main_p.get('category_name'),
                    barcode=barcode
                )
                main_products_info_map[product_id] = info
            for st_product in products[1]:
                info = main_products_info_map.get(st_product.get('product_id'))
                st_product['category_id'] = 0
                if info:
                    st_product['units'] = info['units']
                    st_product['spec'] = info['spec']
                    st_product['category_id'] = info['category_id']
                    st_product['category_name'] = info['category_name']
                    st_product['barcode'] = info['barcode']
        rows = []
        position_map = {}
        position_ids = []
        for p in products[1]:
            position_id = p.get('position_id', 1)
            if not position_id:
                position_id = 1
            if position_map.get(position_id):
                position_map[position_id].append(p)
            else:
                position_map[position_id] = [p]
                if position_id != 1:
                    position_ids.append(position_id)
        position_list_ret = metadata_service.list_positions(return_fields="id,code,name",
                                                            ids=position_ids,
                                                            partner_id=partner_id, user_id=user_id).get(
            'rows', [])
        position_dict = {}
        if position_list_ret:
            for p in position_list_ret:
                position_dict[int(p['id'])] = [p.get('fields', {}).get('code', ''),
                                               p.get('fields', {}).get('name', '')]
        for position_id, ps in position_map.items():
            data = dict(
                position_id=position_id,
                position_code=position_dict[position_id][0] if position_id in position_dict else '',
                position_name=position_dict[position_id][1] if position_id in position_dict else '',
                products=sorted(ps, key=lambda e: e.__getitem__('category_id')),
                total=len(ps),
            )
            rows.append(data)
        return count, rows

    def cancel_stocktake(self, doc_id, partner_id, user_id):
        username = metadata_service.get_username_by_pid_uid(partner_id=partner_id, user_id=user_id)
        # 只允许新建、已驳回的盘点单取消
        st_obj = self.stocktake_repo.lock_stocktake_status(doc_id, ['INITED', 'REJECTED'], 'CANCELLED',
                                                           user_id=user_id, partner_id=partner_id, username=username)
        if st_obj:
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=st_obj.branch_id,
                                             doc_type="stocktake"))
            return True
        else:
            raise ActionException('取消失败')

    def approve_stocktake(self, doc_id, partner_id, user_id, username, approve_name, source=''):
        stocktake_obj = self.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        if not stocktake_obj:
            raise DataValidationException('无此单据')
        # 4 点后不能盘点审核
        # target_date = stocktake_obj.target_date
        # can_approve_date = target_date + timedelta(hours=28)
        # if datetime.now() > can_approve_date:
        #     raise StockTakePermissionException("超过盘点日4点不能审核！")
        # if stocktake_obj.type != type:
        #     s_type = {'D': '日盘', 'W': '周盘', 'M': '月盘', 'R': '不定期盘'}
        #     raise StockTakePermissionException('审核盘点单类型权限不正确！\n用户权限：{}；盘点类型：{}'.format(s_type[type],
        #                                                                    s_type[stocktake_obj.type]))
        result = {}
        if stocktake_obj.branch_type in ['STORE', 'FRS_STORE'] and source != "POS":  # 加盟商盘点单,也需验证卡单逻辑
            result = self.check_confirm_stocktake(doc_id=doc_id, partner_id=partner_id, user_id=user_id,
                                                  branch_type=stocktake_obj.branch_type)
            if result.get('handler') is True:
                # SUBMITTED
                st_obj = self.stocktake_repo.lock_stocktake_status(doc_id, ['INITED', 'SUBMITTED'], 'APPROVED',
                                                                   user_id=user_id, partner_id=partner_id,
                                                                   username=username, approve_name=approve_name)
                if st_obj:
                    logging.debug('------convert_stocktake:%s' % True)
                    # 折算盘点单
                    self.stocktake_repo.convert_bom_stocktake(st_obj.id, partner_id, user_id, username)
                    result['result'] = True
                else:
                    logging.debug('------convert_stocktake:%s' % False)
                    raise ActionException('折算盘点单失败')
            else:
                result['result'] = False
        else:
            st_obj = self.stocktake_repo.lock_stocktake_status(doc_id, ['INITED', 'SUBMITTED'], 'APPROVED',
                                                               user_id=user_id, username=username,
                                                               partner_id=partner_id, approve_name=approve_name)
            if st_obj:
                logging.debug('------convert_stocktake:%s' % True)
                # 折算盘点单
                self.stocktake_repo.convert_bom_stocktake(st_obj.id, partner_id, user_id, username)
                result['result'] = True
            else:
                logging.debug('------convert_stocktake:%s' % False)
                raise ActionException('折算盘点单失败')

        if result.get('result') is True:
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=stocktake_obj.branch_id,
                                             doc_type="stocktake"))
        return result

    # 异步结算盘点单
    def finalize_store_stocktake(self, doc_id, products, partner_id, user_id):

        stocktake_obj = self.stocktake_repo.finalize_store_stocktake(doc_id, products, partner_id, user_id)
        stocktake = self.get_stocktake_doc_by_id(doc_id, partner_id=partner_id, is_details=True)
        # vendor单据同步给三方
        message = {
            'doc_resource': 's_stocktake' if stocktake.branch_type != 'FRS_STORE' else 'fs_stocktake',
            'doc_id': doc_id,
            'partner_id': partner_id,
            'user_id': user_id,
            'operate_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
        }
        mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)

        # 同步记录落表，用于后续补偿
        tp_trans_log = {
            'id': doc_id,
            'doc_code': stocktake_obj.code,
            'doc_type': 's_stocktake' if stocktake.branch_type != 'FRS_STORE' else 'fs_stocktake',
            'status': 'inited',
            'msg': str(message),
            'partner_id': partner_id,
            'created_by': user_id,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        TpTransLogModel.create_logs_list([tp_trans_log])

        return stocktake_obj

    def check_confirm_stocktake(self, doc_id=None, partner_id=None, user_id=None, branch_type=None,
                                # 以下参数给首页用
                                end_date=None, store_id=None, is_home_page=False):
        # 首页用
        if is_home_page:
            day_end = end_date
        else:
            stock_config = metadata_service.get_stockTake_config(partner_id, user_id)
            # 配置了开启卡盘点开关的, 不对单据做校验
            if stock_config.get(partner_id, False):
                return {"handler": True}
            stocktake_obj = self.get_stocktake_doc_by_id(doc_id, partner_id)
            if stocktake_obj is None:
                raise DataValidationException('data not find')
            if stocktake_obj.target_date is None:
                raise DataValidationException("stocktake target date is empty!")
            # 校验所有盘点单商品是否为0
            flag = self.stocktake_repo.check_stocktake_products_number(doc_id)
            if not flag:
                raise DataValidationException("stocktake product's quantity less than zero!")
            day_end = stocktake_obj.target_date + timedelta(days=1)
            # day_end = datetime(int(end_date.year), int(end_date.month), int(end_date.day))
            partner_id = stocktake_obj.partner_id
            store_id = stocktake_obj.branch_id
        end_date = purchase_service.get_timestamp(day_end)
        if branch_type == "WAREHOUSE":
            # 拉取仓库未处理收货单（batch_type="RETURN"-退货单，batch_type="PURCHASE"-采购收货batch_type="TRANSFER"-调拨收货）
            list_receiving = interactive_service.get_list_receiving_details_warehouse(store_id=store_id,
                                                                                      end_date=end_date,
                                                                                      partner_id=partner_id,
                                                                                      user_id=user_id)
        elif branch_type == "FRS_STORE":
            list_receiving = interactive_service.get_receiving_frs_store(store_id=store_id,
                                                                         end_date=end_date,
                                                                         partner_id=partner_id,
                                                                         user_id=user_id)
        else:  # branch_type = "STORE"
            list_receiving = interactive_service.get_list_receiving_details_store(
                store_id=store_id,
                end_date=end_date,
                partner_id=partner_id,
                user_id=user_id)
        is_receiving = True
        receiving_list = []
        if list_receiving and isinstance(list_receiving, Iterable):
            for receiving_obj in list_receiving:
                receiving_list.append(dict(
                    id=receiving_obj.id,
                    code=receiving_obj.code,
                    status=receiving_obj.status
                ))
            is_receiving = False

        list_receiving_diff = interactive_service.get_list_receiving_diff_details_neq_status(
            partner_id=partner_id,
            store_id=store_id,
            end_date=day_end,
            branch_type=branch_type
        )
        is_receiving_diff = True
        receiving_diff_list = []
        if list_receiving_diff and isinstance(list_receiving_diff, list) and len(list_receiving_diff) > 0:
            for receiving_diff_obj in list_receiving_diff:
                receiving_diff_list.append(dict(
                    id=receiving_diff_obj[0],
                    code=receiving_diff_obj[1],
                    status=receiving_diff_obj[3]
                ))
            is_receiving_diff = False

        list_transfer = interactive_service.get_list_transfer_detail_neq_status(partner_id=partner_id,
                                                                                store_id=store_id,
                                                                                end_date=day_end,
                                                                                branch_type=branch_type)
        is_transfer = True
        transfer_list = []
        if list_transfer and isinstance(list_transfer, list) and len(list_transfer) > 0:
            for transfer_obj in list_transfer:
                transfer_list.append(dict(
                    id=transfer_obj[0],
                    code=transfer_obj[1],
                    status=transfer_obj[2]
                ))
            is_transfer = False

        list_adjust = interactive_service.get_list_store_adjust_neq_status(partner_id=partner_id,
                                                                           store_id=store_id,
                                                                           end_date=day_end,
                                                                           branch_type=branch_type)
        is_adjust = True
        adjust_list = []
        if list_adjust and isinstance(list_adjust, list) and len(list_adjust) > 0:
            for adjust_obj in list_adjust:
                adjust_list.append(dict(
                    id=adjust_obj[0],
                    code=adjust_obj[1],
                    status=adjust_obj[2]
                ))
            is_adjust = False

        list_return = interactive_service.get_list_store_return_neq_status(partner_id=partner_id, store_id=store_id,
                                                                           end_date=day_end, branch_type=branch_type)
        is_return = True
        return_list = []
        if list_return and isinstance(list_return, list) and len(list_return) > 0:
            for return_obj in list_return:
                return_list.append(dict(
                    id=return_obj[0],
                    code=return_obj[1],
                    status=return_obj[3]
                ))
            is_return = False

        # 首页新加盘点订货单
        target_date = day_end - timedelta(days=1)
        list_stocktake = interactive_service.get_list_store_stocktake_neq_status(partner_id=partner_id,
                                                                                 store_id=store_id,
                                                                                 end_date=target_date,
                                                                                 branch_type=branch_type)
        stocktake_list = []
        if list_stocktake and isinstance(list_stocktake, list) and len(list_stocktake) > 0:
            for stocktake_obj in list_stocktake:
                stocktake_list.append(dict(
                    id=stocktake_obj[0],
                    code=stocktake_obj[1],
                    status=stocktake_obj[3]
                ))

        list_demand = interactive_service.get_list_store_demand_neq_status(partner_id=partner_id,
                                                                           store_id=store_id,
                                                                           end_date=target_date)
        demand_list = []
        if list_demand and isinstance(list_demand, list) and len(list_demand) > 0:
            for demand_obj in list_demand:
                demand_list.append(dict(
                    id=demand_obj[0],
                    code=demand_obj[1],
                    type=demand_obj[2],
                    status=demand_obj[3]
                ))

        # 查询未完成的门店自采单(新建，已提交，已驳回)
        list_self_picking = interactive_service.get_unfinished_self_picking(end_date=day_end,
                                                                            store_id=store_id,
                                                                            partner_id=partner_id,
                                                                            status=["INITED", "SUBMITTED",
                                                                                    "REJECTED"],
                                                                            branch_type=branch_type)
        is_self_picking = True
        self_picking_list = []
        if isinstance(list_self_picking, list) and len(list_self_picking) > 0:
            for pick in list_self_picking:
                self_picking_list.append(dict(
                    id=pick[0],
                    code=pick[1],
                    status=pick[2]
                ))
            is_self_picking = False

        result = dict()
        result['receiving'] = receiving_list
        result['return'] = return_list
        # 盘点不校验订货单，盘点单
        if is_home_page:
            result['stocktake'] = stocktake_list
            result['demand'] = demand_list
        result['receiving_diff'] = receiving_diff_list
        result['transfer'] = transfer_list
        result['adjust'] = adjust_list
        result['self_picking'] = self_picking_list
        if is_receiving and is_receiving_diff and is_transfer and is_adjust and is_return and is_self_picking:
            check = True
        else:
            check = False
        result['handler'] = check
        return result

    def checked_stocktake(self, doc_id, branch_type, partner_id, user_id, username):
        stocktake_obj = self.get_stocktake_doc_by_id(doc_id, partner_id)
        if not stocktake_obj:
            raise DataValidationException('无此单据')

        target_date = stocktake_obj.target_date
        can_checke_date = target_date + timedelta(hours=26)
        if datetime.now() > can_checke_date:
            raise StockTakePermissionException("超过盘点日2点不能新建！")
        stocktake = self.stocktake_repo.lock_stocktake_status(doc_id, 'INITED', 'INITED',
                                                              user_id=user_id, partner_id=partner_id, username=username)
        if stocktake:
            if branch_type == "WAREHOUSE":
                stocktake_store_name_ret = metadata_service.get_distribution_center(center_id=stocktake.branch_id,
                                                                                    return_fields='name',
                                                                                    partner_id=partner_id,
                                                                                    user_id=user_id)
            else:
                stocktake_store_name_ret = metadata_service.get_store(store_id=stocktake.branch_id,
                                                                      return_fields='name',
                                                                      partner_id=stocktake.partner_id, user_id=user_id)
            stocktake_store_name = stocktake_store_name_ret.get('name')
            MessageServicePub.pub_message_service(partner_id=stocktake.partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=stocktake.branch_id,
                                                  source_root_id=doc_id,
                                                  source_id=doc_id,
                                                  source_type="STOCKTAKE",
                                                  action="INITED",
                                                  ref_source_id=doc_id,
                                                  ref_source_type="STOCKTAKE",
                                                  ref_action="INITED",
                                                  content={"target_date": str(stocktake.target_date),
                                                           "type": stocktake.type,
                                                           "updated_at": str(datetime.now()),
                                                           "updated_by": str(user_id),
                                                           "updated_name": username,
                                                           "stocktake_store_name": stocktake_store_name
                                                           }
                                                  )
            return True
        else:
            raise ActionException('检核失败')

    def confirm_stocktake(self, doc_id, user_id, partner_id, username):
        stocktake = self.stocktake_repo.lock_stocktake_status(doc_id, ['INITED', 'REJECTED'], 'CONFIRMED',
                                                              user_id=user_id, partner_id=partner_id,
                                                              username=username)
        logging.debug('------confirm_stocktake_stocktake:%s' % stocktake)

        if stocktake:
            logging.debug('------confirm_stocktake:%s' % True)
            # 结算盘点单
            self.stocktake_repo.finalize_store_stocktake(stocktake.id, user_id, partner_id=partner_id)
            return True
        else:
            logging.debug('------confirm_stocktake:%s' % False)
            return False

    def reject_stocktake(self, doc_id, partner_id, user_id, username=None, reason=None):
        stocktake_obj = self.get_stocktake_doc_by_id(doc_id, partner_id)
        if not stocktake_obj:
            raise DataValidationException('无此单据')
        res = self.stocktake_repo.lock_stocktake_status(doc_id, 'SUBMITTED', 'REJECTED',
                                                        user_id=user_id, partner_id=partner_id,
                                                        username=username, reason=reason)
        logging.info("reject_stocktake res: {}".format(res))
        if res:
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=stocktake_obj.branch_id,
                                             doc_type="stocktake"))
            # PDA消息推送
            stocktake_store_name_ret = metadata_service.get_store(store_id=res.branch_id,
                                                                  return_fields='name',
                                                                  partner_id=partner_id, user_id=user_id)

            stocktake_store_name = stocktake_store_name_ret.get('name')
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=res.branch_id,
                                                  source_root_id=doc_id,
                                                  source_id=doc_id,
                                                  source_type="STOCKTAKE",
                                                  action="REJECTED",
                                                  ref_source_id=doc_id,
                                                  ref_source_type="STOCKTAKE",
                                                  ref_action="SUBMITTED",
                                                  content={"target_date": str(res.target_date),
                                                           "type": res.type,
                                                           "updated_at": str(datetime.now()),
                                                           "updated_by": str(user_id),
                                                           "updated_name": username,
                                                           "reason": reason,
                                                           "stocktake_store_name": stocktake_store_name}
                                                  )
            return True
        else:
            raise ActionException('驳回失败')

    def submit_stocktake(self, doc_id, user_id, partner_id, username=None, branch_type=None, source=''):
        logging.info("source:{}".format(source))
        if (branch_type == "STORE" or branch_type == "FRS_STORE") and source != "POS":  # 加盟商盘点单,也需验证卡单逻辑
            result = self.check_confirm_stocktake(doc_id=doc_id, partner_id=partner_id, user_id=user_id,
                                                  branch_type=branch_type)
            handler = result.get('handler')
        else:
            handler = True
        if handler is True:
            st_doc = self.stocktake_repo.lock_stocktake_status(doc_id, ['INITED', 'REJECTED'], 'SUBMITTED',
                                                               user_id=user_id, username=username,
                                                               submit_name=username, partner_id=partner_id)

            if st_doc:
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=st_doc.branch_id,
                                                 doc_type="stocktake"))
                # stocktake_store_name_ret = metadata_service.get_store(store_id=st_doc.branch_id,
                #                                                       return_fields='name',
                #                                                       partner_id=st_doc.partner_id, user_id=user_id)
                # stocktake_store_name = stocktake_store_name_ret.get('name')
                # MessageServicePub.pub_message_service(partner_id=st_doc.partner_id,
                #                                       user_id=user_id,
                #                                       scope_id=1,
                #                                       store_id=st_doc.branch_id,
                #                                       source_root_id=doc_id,
                #                                       source_id=doc_id,
                #                                       source_type="STOCKTAKE",
                #                                       action="SUBMITTED",
                #                                       ref_source_id=doc_id,
                #                                       ref_source_type="STOCKTAKE",
                #                                       ref_action="INITED",
                #                                       content={"target_date": str(st_doc.target_date),
                #                                                "type": st_doc.type,
                #                                                "updated_at": str(datetime.now()),
                #                                                "updated_by": str(user_id),
                #                                                "updated_name": username,
                #                                                "stocktake_store_name": stocktake_store_name
                #                                                }
                #                                       )
                return True
            else:
                raise ActionException('提交失败')
        else:
            logging.warning("提交失败，有卡盘点单据！")
            return False

    # 检查商品盘点库存
    def check_inventory(self, store_id, stocktake_products, partner_id, user_id):
        if stocktake_products is None or not isinstance(stocktake_products, list) or len(stocktake_products) == 0:
            return stocktake_products
        product_ids = [product['id'] for product in stocktake_products]
        product_fields = ["product_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            return_fields=['id'],
            include_product_fields=product_fields,
            store_id=store_id,
            product_ids=product_ids,
            partner_id=partner_id,
            user_id=user_id
        )
        is_bom_products_list = []
        if ret:
            is_bom_products_list = [int(data.get('product_id', 1)) for data in ret.get('rows')]
        inventory_map = inventory_service.get_products_inventory_by_branch_id(branch_id=store_id,
                                                                              partner_id=partner_id,
                                                                              user_id=user_id)
        final_products = []
        for p in stocktake_products:
            inv = inventory_map.get(str(p.get('id')))
            if inv:
                if (inv.get('quantity_avail') != 0 and inv.get('quantity_avail') != None) or \
                        int(p.get('id')) in is_bom_products_list:
                    final_products.append(p)
                    # 半成品不验证
            else:
                # 半成品不验证
                if int(p.get('id')) in is_bom_products_list:
                    final_products.append(p)
        return final_products

    def get_stocktake_tags(self, branch_ids=None, tag_name=None, partner_id=None, user_id=None):
        # store_scope_check(branch_id, partner_id, user_id)
        branch_ids = [convert_to_int(s) for s in branch_ids]
        tag_list = self.stocktake_repo.get_stocktake_tags(branch_ids=branch_ids, tag_name=tag_name,
                                                          partner_id=partner_id)
        store_list = metadata_service.get_store_list(ids=branch_ids,
                                                     return_fields="id,name,code",
                                                     partner_id=partner_id,
                                                     user_id=user_id).get('rows', [])
        store_map = {}
        for store in store_list:
            store_map[convert_to_int(store.get('id'))] = store

        for tag in tag_list:
            store = store_map.get(tag.get('branch_id'), {})
            tag["branch_name"] = store.get('name')

        return tag_list

    def get_stocktake_tags_by_id(self, tag_id, partner_id=None, user_id=None):
        """
        :param tag_id: 标签id
        :param partner_id:
        :param user_id:
        :return: tag
        """
        return self.stocktake_repo.get_a_stocktake_tags(tag_id=tag_id, partner_id=partner_id, user_id=user_id)

    def action_stocktake_tags(self, name=None, action=0, branch_ids=None, region_ids=None,
                              branch_id=None, copy_branch=None, add_dimension=None, origin_name=None,
                              tag_ids=None, partner_id=None, user_id=None):
        """
        :param name: 标签名称 同一个门店不允许重复
        :param action: 动作
        :param branch_ids: 门店/仓库id列表
        :param region_ids: 管理区域id列表
        :param branch_id: 待复制原门店id
        :param add_dimension: 添加维度：全市场/管理区域/门店->all/region/store
        :param origin_name 批量修改中原标签名称
        :param copy_branch copy的目的门店
        :param tag_ids:
        :param partner_id:
        :param user_id:
        :return:
        """
        # 批量创建标签
        if action == 1:
            return self.create_stocktake_tags(name=name, add_dimension=add_dimension, region_ids=region_ids,
                                              branch_ids=branch_ids, partner_id=partner_id, user_id=user_id)
        # 批量删除
        elif action == 2:
            return self.stocktake_repo.delete_a_stocktake_tags(tag_ids=tag_ids, partner_id=partner_id)
        # 批量更新
        elif action == 3:
            return self.update_stocktake_tags(name=name, origin_name=origin_name, add_dimension=add_dimension,
                                              region_ids=region_ids, branch_ids=branch_ids, partner_id=partner_id,
                                              user_id=user_id)
        # 复制新增
        elif action == 4:
            return self.copy_stocktake_tags(branch_id=branch_id, copy_branch=copy_branch,
                                            partner_id=partner_id, user_id=user_id)
        else:
            return None

    def clean_stocktake_product_tags(self, request, partner_id=None):
        tag_ids = request.id
        return self.stocktake_repo.clean_stocktake_product_tags(tag_ids, partner_id)

    def create_stocktake_tags(self, name=None, add_dimension=None, region_ids=None, branch_ids=None,
                              partner_id=None, user_id=None):
        """批量创建盘点标签数据整理
        对已经存在的标签名称对应的门店要过滤掉
        """
        tags_data_list = []
        if add_dimension == 'all':
            # 拉取全市场所有门店列表
            store_list = get_all_store_list(partner_id=partner_id, user_id=user_id)
        elif add_dimension == 'region':
            # 根据管理区域拉取门店列表
            if not region_ids:
                raise DataValidationException("请传入管理区域")
            store_list = list_stores_by_region_id(region_ids=region_ids, partner_id=partner_id, user_id=user_id)
        elif add_dimension == 'store':
            store_list = [convert_to_int(s) for s in branch_ids]
        else:
            raise DataValidationException(f"添加维度不合法-{add_dimension}")
        # 查询一把标签列表将该标签下的门店捞出
        tag_list = self.stocktake_repo.get_stocktake_tags(branch_ids=store_list, tag_name=name, partner_id=partner_id)
        if tag_list:
            exist_branch = []
            for tag in tag_list:
                exist_branch.append(tag.get('branch_id'))
            store_list = list(set(store_list) - set(exist_branch))
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        tag_counts = len(store_list)
        tag_ids = get_uuids(tag_counts)
        for i in range(tag_counts):
            row = dict(
                id=tag_ids[i],
                name=name,
                partner_id=partner_id,
                user_id=user_id,
                created_by=user_id,
                updated_by=user_id,
                created_name=username,
                updated_name=username,
                branch_id=store_list[i]
            )
            tags_data_list.append(row)
        res = self.stocktake_repo.create_stocktake_tags(tags_data_list)
        return res

    def update_stocktake_tags(self, name=None, origin_name=None, add_dimension=None, region_ids=None,
                              branch_ids=None, partner_id=None, user_id=None):
        """批量更新盘点标签
        根据前端传的盘点标签名称和门店/管理区域查询出存在的标签记录拿到id再更新
        对已经存在的标签名称对应的门店要过滤掉
        """
        if add_dimension == 'all':
            # 拉取全市场所有门店
            store_list = get_all_store_list(partner_id=partner_id, user_id=user_id)
        elif add_dimension == 'region':
            # 根据管理区域拉取门店列表
            if not region_ids:
                raise DataValidationException("请传入管理区域")
            store_list = list_stores_by_region_id(region_ids=region_ids, partner_id=partner_id, user_id=user_id)
        elif add_dimension == 'store':
            store_list = [convert_to_int(s) for s in branch_ids]
        else:
            raise DataValidationException(f"选择的维度不合法-{add_dimension}")
        # 查询一把标签列表将该更新目的标签下的门店捞出过滤掉
        tag_list = self.stocktake_repo.get_stocktake_tags(tag_name=name,
                                                          partner_id=partner_id)
        if tag_list:
            exist_branch = []
            for tag in tag_list:
                exist_branch.append(tag.get('branch_id'))
            store_list = list(set(store_list) - set(exist_branch))
        if not store_list:
            return True

        tag_list = self.stocktake_repo.get_stocktake_tags(branch_ids=store_list, tag_name=origin_name,
                                                          partner_id=partner_id)
        exist_tag_ids = []
        if tag_list:
            for tag in tag_list:
                exist_tag_ids.append(tag.get('id'))
        tags_data_list = []
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        for i in range(len(exist_tag_ids)):
            row = dict(
                id=exist_tag_ids[i],
                name=name,
                partner_id=partner_id,
                user_id=user_id,
                created_by=user_id,
                updated_by=user_id,
                created_name=username,
                updated_name=username,
            )
            tags_data_list.append(row)
        res = self.stocktake_repo.update_stocktake_tags(tags_data_list)
        return res

    def copy_stocktake_tags(self, branch_id=None, copy_branch=None, partner_id=None, user_id=None):
        """复制门店标签
        branch_id 原门店
        copy_branch 待复制门店
        1、先取出待复制的门店标签名称
        2、将名称列表和目标复制的门店匹配
        """
        copy_tag_list = self.stocktake_repo.get_stocktake_tags(branch_ids=[convert_to_int(branch_id)],
                                                               partner_id=partner_id)
        if not copy_tag_list:
            raise DataValidationException(f"原门店没有设置标签-{branch_id}")
        tags_data_list = []
        tag_counts = len(copy_tag_list)
        tag_ids = get_uuids(tag_counts)
        for i, tag in enumerate(copy_tag_list):
            tag_name = tag.get('name')
            # 查询一把标签列表将该标签下的门店捞出
            tag_list = self.stocktake_repo.get_stocktake_tags(branch_ids=[copy_branch], tag_name=tag_name,
                                                              partner_id=partner_id)
            if tag_list:
                continue
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            row = dict(
                id=tag_ids[i],
                name=tag_name,
                partner_id=partner_id,
                user_id=user_id,
                created_by=user_id,
                updated_by=user_id,
                created_name=username,
                updated_name=username,
                branch_id=copy_branch
            )
            tags_data_list.append(row)
        res = self.stocktake_repo.create_stocktake_tags(tags_data_list)
        return res

    def get_stocktake_balance(self, request, partner_id=None, user_id=None):
        start_date = request.start_date
        end_date = request.end_date
        target_date = request.target_date
        branch_type = request.branch_type
        if start_date and not isinstance(start_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = start_date.seconds
            start_date = timestamp.ToDatetime()
            start_date = start_date
        if end_date and not isinstance(end_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = end_date.seconds
            end_date = timestamp.ToDatetime()
            end_date = end_date
        if target_date and not isinstance(target_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = target_date.seconds
            target_date = timestamp.ToDatetime()
            target_date = target_date
            # target_date = datetime(int(target_date.year), int(target_date.month),
            #                        int(target_date.day))
            if target_date == datetime(1970, 1, 1):
                target_date = None
        store_ids = request.store_ids
        if branch_type == "WAREHOUSE":
            store_ids = [convert_to_int(store_id) for store_id in store_ids]
            store_info_list = metadata_service.get_distribution_center_list(ids=store_ids,
                                                                            return_fields='id,code,name',
                                                                            partner_id=partner_id,
                                                                            user_id=user_id).get('rows', [])
        elif branch_type == "MACHINING_CENTER":
            store_ids = [convert_to_int(store_id) for store_id in store_ids]
            store_info_list = metadata_service.get_machining_center_list(ids=store_ids,
                                                                         return_fields='id,code,name',
                                                                         partner_id=partner_id,
                                                                         user_id=user_id)
        else:
            # 导出服务用
            store_ids = [convert_to_int(store_id) for store_id in store_ids]
            store_info_list = metadata_service.get_store_list(ids=store_ids,
                                                              return_fields='id,code,name',
                                                              partner_id=partner_id,
                                                              user_id=user_id).get('rows', [])
        limit = request.limit
        offset = request.offset
        # status = list(request.status)
        store_info = {}
        if len(store_info_list) > 0:
            for s in store_info_list:
                store_info[int(s['id'])] = [s.get('code'), s.get('name')]
        include_total = request.include_total
        schedule_code = request.schedule_code
        stocktake_type = request.stocktake_type
        cycle_type = request.type
        return self.stocktake_repo.get_stocktake_balance(partner_id, start_date=start_date, end_date=end_date,
                                                         store_ids=store_ids, limit=limit, offset=offset,
                                                         target_date=target_date, cycle_type=cycle_type,
                                                         include_total=include_total, schedule_code=schedule_code,
                                                         stocktake_type=stocktake_type, store_info=store_info,
                                                         branch_type=branch_type)

    def get_st_balance_product_group_by_doc_id(self, doc_id, partner_id=None, user_id=None):
        # pda盘点取tag_quantity
        pre_sql = '''
            SELECT stp.product_id, 
            stp.product_code,
            stp.product_name,
            
            stp.unit_id,
            stp.unit_name,
            stp.unit_spec,
            stp.accounting_unit_id,
            stp.accounting_unit_name,
            stp.accounting_unit_spec,
            IF(stp.is_pda=1,stp.tag_quantity,stp.quantity) AS quantity,
            stp.accounting_quantity,
            stp.inventory_quantity,
            stp.diff_quantity,
            stp.unit_diff_quantity,
            stp.unit_rate,
            stp.material_number,
            stp.is_system,
            stp.storage_type,
            
            stn.tag_name,
            stn.tag_quantity,
            stn.unit_name as tag_unit_name, 
            stn.unit_rate as tag_uint_rate,
            stp.position_id
            
            FROM supply_st_product AS stp 
            LEFT JOIN supply_st_product_tag_name stn ON stp.id=stn.stp_id
            WHERE stp.doc_id={} AND stp.deleted=0 AND stp.partner_id={};
        '''.format(doc_id, partner_id)
        bind_sql = text(pre_sql)
        logging.debug('bind_sql' + str(bind_sql))
        product_map = {}
        with DummyTransaction(auto_commit=False) as trans:
            rows = trans.scope_session.execute(bind_sql).fetchall()
        position_ids = [row[22] for row in rows if row[22]]
        position_map = get_branch_map(branch_ids=position_ids, branch_type='POSITION',
                                      partner_id=partner_id, user_id=user_id)
        for row in rows:
            position = position_map.get(row[22]) if position_map.get(row[22]) else {}
            product_item = dict(product_id=row[0],
                                product_code=row[1],
                                product_name=row[2],
                                unit_id=row[3],
                                unit_name=row[4],
                                unit_spec=row[5],
                                accounting_unit_id=row[6],
                                accounting_unit_name=row[7],
                                accounting_unit_spec=row[8],
                                quantity=row[9],
                                accounting_quantity=row[10],
                                accounting_inventory_quantity=row[11],
                                accounting_diff_quantity=row[12],
                                unit_diff_quantity=row[13],
                                unit_rate=row[14],
                                material_number=row[15],
                                is_system=row[16],
                                storage_type=row[17],
                                inventory_quantity=row[11] / row[14] if row[11] and row[14] else 0,
                                diff_quantity=row[12] / row[14] if row[12] and row[14] else 0,
                                position_id=row[22],
                                position_code=position.get("code"),
                                position_name=position.get("name"),
                                tag_details=[])
            tag_dict = dict(
                tag_name=row[18],
                tag_quantity=row[19],
                tag_unit_name=row[20],
                tag_uint_rate=row[21],
            )
            if product_map.get(row[0]):
                product_map.get(row[0])['tag_details'].append(tag_dict)
            else:
                product_item['tag_details'] = [tag_dict]
                product_map[row[0]] = product_item
        return product_map.values()

    def stocktake_bi_detailed(self, request, partner_id=None, user_id=None):
        """盘点单报表"""
        start_date = request.start_date
        end_date = request.end_date
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        if start_date.year == 1970:
            raise DataValidationException("请传入查询开始日期")
        if end_date.year == 1970:
            raise DataValidationException("请传入查询结束日期")
        category_ids = list(request.category_ids)
        store_ids = list(request.store_ids)
        branch_type = request.branch_type
        cycle_type = request.type
        stocktake_type = request.stocktake_type
        order = request.order
        sort = request.sort
        offset = request.offset
        limit = request.limit
        include_total = request.include_total
        product_name = request.product_name
        product_ids = []
        if (category_ids and isinstance(category_ids, list) and len(category_ids) > 0) or product_name:
            relation_filters = search = search_fields = None
            if category_ids and isinstance(category_ids, list) and len(category_ids) > 0:
                relation_filters = {"product_category": [str(category_id) for category_id in category_ids]}
            if product_name:
                search = str(product_name)
                search_fields = 'name'
            list_products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                  search=search,
                                                                  search_fields=search_fields,
                                                                  return_fields="id,code,name",
                                                                  include_units=True,
                                                                  partner_id=partner_id, user_id=user_id
                                                                  )
            list_products = []
            if list_products_ret:
                list_products = list_products_ret['rows']
            if not isinstance(list_products, list) or len(list_products) == 0:
                return [], 0, 0, 0, 0
            for product in list_products:
                product_ids.append(convert_to_int(product['id']))
        return self.stocktake_repo.stocktake_bi_detailed(partner_id=partner_id, store_ids=store_ids,
                                                         product_ids=product_ids, start_date=start_date,
                                                         end_date=end_date, limit=limit, offset=offset,
                                                         include_total=include_total, cycle_type=cycle_type,
                                                         user_id=user_id, order=order, sort=sort,
                                                         branch_type=branch_type, stocktake_type=stocktake_type,
                                                         with_tag=request.with_tag if hasattr(request, 'with_tag') else False)

    def get_st_balance_product_group(self, request, partner_id, user_id):
        include_total = request.include_total
        limit = request.limit
        offset = request.offset
        start = request.start
        end = request.end
        region_id = request.region_id
        product_ids = list(request.product_ids)
        cycle_type = request.type  # 盘点循环类型 D/W/M
        stocktake_type = request.stocktake_type  # 盘点属性(不定期-R、计划生成-PLAN)
        branch_type = request.branch_type
        start = datetime.fromtimestamp(start.seconds)
        end = datetime.fromtimestamp(end.seconds)
        if start.year == 1970:
            raise DataValidationException("请传入查询开始日期")
        if end.year == 1970:
            raise DataValidationException("请传入查询结束日期")
        # pda盘点取tag_quantity
        base_sql = (
            ' SELECT '
            '  stp.product_id'
            ', stp.unit_name'
            ', stp.accounting_unit_name'

            ', IF(stp.is_pda=1,SUM(stp.tag_quantity),SUM(stp.quantity)) AS quantity'
            ', SUM(stp.inventory_quantity) AS inventory_quantity'
            ', SUM(stp.diff_quantity) AS diff_quantity'
            ', SUM(stp.accounting_quantity) AS accounting_quantity'
            ', SUM(stp.unit_diff_quantity) AS unit_diff_quantity'

            ', stp.unit_rate AS unit_rate'
            ', doc.type AS doc_type'

            ', doc.branch_id AS store_id'
            ', doc.stocktake_type AS stocktake_type'

            ' FROM supply_st_doc_details AS doc'
            ' INNER JOIN supply_st_product stp ON doc.doc_id=stp.doc_id'
            ' WHERE {}'
            ' GROUP BY stp.product_id,unit_name, accounting_unit_name,stp.is_pda,unit_rate,'
            'doc_type,store_id'
        )
        if limit:
            base_sql += " limit {}".format(limit)
            if offset:
                base_sql += " offset {}".format(offset)
        doc_date_sql = "doc.target_date>='{}' AND doc.target_date<'{}' ".format(datetime2str(start), datetime2str(end))
        doc_type_sql = "doc.type='{}'".format(cycle_type) if cycle_type else None
        stocktake_type_sql = "doc.stocktake_type='{}'".format(stocktake_type) if stocktake_type else None

        # 先不需要校验区域数据权限  直接把区域下的门店拉出来做校验
        # region_fields, store_fields = get_region_and_store_feilds_scope_check(partner_id=partner_id,
        #                                                                       user_id=user_id, region_id=region_id)
        # region_fields = str(region_id) if region_id else None
        store_ids = self.list_stores_by_region_ids(region_ids=[region_id], partner_id=partner_id, user_id=user_id)
        if branch_type == "STORE" or branch_type == "FRS_STORE":
            store_ids = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                                domain='boh.store_bi', branch_ids=store_ids)
        if branch_type:
            branch_type_sql = "doc.branch_type = '{}'".format(branch_type)
        else:
            branch_type_sql = ''
        # if region_fields:
        #     branch_sql = "{} in ({}) ".format(branch_id_field, region_fields)
        # else:
        #     branch_sql = ''
        # if store_ids:
        #
        # else:
        #     store_sql = ''
        store_sql = "doc.branch_id in ({}) ".format(','.join([str(_id) for _id in store_ids])) if store_ids else None
        product_sql = "stp.product_id in ({})".format(
            ','.join([str(_id) for _id in product_ids])) if product_ids else None
        where_sql = " AND ".join(
            [w for w in [doc_date_sql, doc_type_sql, store_sql, product_sql, branch_type_sql,
                         stocktake_type_sql] if w]) + " AND doc.status='CONFIRMED' AND doc.partner_id={}".format(
            partner_id)
        base_sql = base_sql.format(where_sql)
        bind_base_sql = text(base_sql)
        logging.info('bind_base_sql--------------------------' + str(bind_base_sql))
        product_group_map = {}
        with DummyTransaction(auto_commit=False) as trans:
            resproxy = trans.scope_session.execute(bind_base_sql)
            base_rows = resproxy.fetchall()

        branch_ids = []
        product_ids = []
        for s in base_rows:
            if s[10]:
                branch_ids.append(s[10])
            if s[0] and s[0] not in product_ids:
                product_ids.append(s[0])

        branch_map = get_branch_map(branch_ids=branch_ids, branch_type=branch_type,
                                    partner_id=partner_id, user_id=user_id, return_fields="id,code,name")
        product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name", partner_id=partner_id,
                                      user_id=user_id)
        for row in base_rows:
            product = product_map.get(str(row[0])) if product_map.get(str(row[0])) else {}
            branch = branch_map.get(row[10]) if branch_map.get(row[10]) else {}
            base_product_item = dict(
                product_id=row[0],
                product_code=product.get('code'),
                product_name=product.get('name'),
                unit_name=row[1],
                accounting_unit_name=row[2],
                quantity=row[3] if row[3] else 0,
                inventory_quantity=row[4] / row[8] if row[4] and row[8] else 0,
                accounting_inventory_quantity=row[4] if row[4] else 0,
                accounting_diff_quantity=row[5] if row[5] else 0,
                accounting_quantity=row[6] if row[6] else 0,
                unit_diff_quantity=row[7] if row[7] else 0,
                unit_rate=row[8],
                type=row[9],
                store_id=row[10],
                store_code=branch.get('code'),
                store_name=branch.get('name'),
                stocktake_type=row[11],
                diff_quantity=row[5] / row[8] if row[5] and row[8] else 0
            )
            map_key = str(row[0]) + str(row[1]) + str(row[8]) + str(row[9]) + str(row[10])
            if product_group_map.get(map_key):
                product_group_map[map_key]['quantity'] += base_product_item['quantity']
                product_group_map[map_key]['inventory_quantity'] += base_product_item['inventory_quantity']
                product_group_map[map_key]['accounting_inventory_quantity'] += base_product_item[
                    'accounting_inventory_quantity']
                product_group_map[map_key]['accounting_diff_quantity'] += base_product_item['accounting_diff_quantity']
                product_group_map[map_key]['accounting_quantity'] += base_product_item['accounting_quantity']
                product_group_map[map_key]['unit_diff_quantity'] += base_product_item['unit_diff_quantity']
                product_group_map[map_key]['diff_quantity'] += base_product_item['diff_quantity']
            else:
                product_group_map[map_key] = base_product_item
        if include_total:
            return dict(rows=product_group_map.values())
        return product_group_map.values()

    def advance_convert_bom_stocktake(self, doc_id, stocktake, partner_id=None, user_id=None, username=None):
        #######################################################
        # 计算商品的所有核算数量
        # 盘点结算前需要先进行异步结算
        ###异步bom未实现，先循环同步调用拆解
        #######################################################
        products = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id, get_obj=True)
        if products is None or not isinstance(products, list) or len(products) == 0:
            return
        filters = {'allow_stocktake': True
                   }
        product_fields = ["product_type", "bom_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            return_fields=['id'],
            include_product_fields=product_fields,
            store_id=stocktake.branch_id,
            filters=filters,
            partner_id=partner_id,
            user_id=user_id
        )
        is_bom_products_list = []
        if ret:
            for data in ret.get('rows'):
                if data['bom_type'] == "MANUFACTURE":
                    is_bom_products_list.append(int(data.get('product_id', 1)))
        print('is_bom_products_list', is_bom_products_list)
        message = {}
        bom_products_map = {}
        accounting_quantity_products_map = {}
        for p in products:
            if p.unit_id is None or p.quantity is None:
                p.quantity = 0
            if p.unit_rate is None or p.unit_rate == 0:
                p.unit_rate = 1
            if p.is_pda:
                p.tag_quantity = 0
                p.accounting_quantity = 0
                for product_tag in p.product_tags:
                    p.accounting_quantity += product_tag.get('accounting_quantity', 0)
                p.tag_quantity = p.accounting_quantity / p.unit_rate
            else:
                p.accounting_quantity = p.unit_rate * p.quantity
            accounting_quantity_products_map[str(p.id)] = p.accounting_quantity
            if p.product_id in is_bom_products_list and p.accounting_quantity != None and p.accounting_quantity != 0:
                param_dict = dict(
                    request_id=get_guid(),
                    store_id=stocktake.branch_id,
                    store_code=None,
                    sales_date=str(stocktake.target_date),
                    biz_code="STOCKTAKE",
                    biz_no=str(stocktake.id),
                    product_id=p.product_id,
                    product_code=None,
                    product_qty=abs(p.accounting_quantity),
                    partner_id=partner_id,
                    user_id=user_id
                )
                ret = Bom_service.get_bom(**param_dict)
                print('get_bom_ret*****', ret)
                if ret:
                    p.is_bom = True
                    if int(ret.get('request_id')) == int(param_dict['request_id']) and ret.get('bom'):
                        boms = ret["bom"]
                        for bom in boms:
                            product_id = str(bom.get('product_id'))
                            qty = bom.get('qty', 0)
                            if bom_products_map.get(product_id):
                                bom_products_map[product_id] += qty
                            else:
                                bom_products_map[product_id] = qty
        logging.info('bom_products_map***************' + str(bom_products_map))
        return bom_products_map, accounting_quantity_products_map

    def get_advance_stocktake_products_diff(self, doc_id, partner_id, user_id):
        ret = {}
        stocktake = self.get_stocktake_doc_by_id(doc_id, partner_id)
        if not stocktake:
            return {}
        stocktake_detail = self.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        products = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id, partner_id=partner_id)
        if products is None or not isinstance(products, list) or len(products) == 0:
            return {}
        # 取得商品的每日库存
        product_ids = []
        for p in products:
            product_ids.append(int(p.get('product_id')))
        product_ids = list(set(product_ids))
        # 取得商品的实时库存map
        products_inventory = inventory_service.get_products_inventory_by_branch_id(
            branch_id=stocktake.branch_id,
            partner_id=partner_id,
            user_id=user_id,
            product_ids=product_ids,
            detail=True
        )
        bom_products_map, accounting_quantity_products_map = self.advance_convert_bom_stocktake(doc_id, stocktake,
                                                                                                partner_id, user_id)

        # bom_products_map={}
        # accounting_quantity_products_map={}
        # dict_product={}
        for p in products:
            p['accounting_quantity'] = accounting_quantity_products_map.get(str(p.get('id')), 0)
            #######################################################
            # 取bom折算后的数量累加到原商品核算数量，折算商品折算数量为0，剔除不计库存商品不做库存查询和扣减
            # 其中bom_products_dict数据重构成map，取数量进行累加
            #######################################################
            if bom_products_map.get(str(p.get('product_id'))):
                p['convert_accounting_quantity'] = convert_to_decimal(
                    bom_products_map.get(str(p.get('product_id')))) + p.get('accounting_quantity')
            else:
                p['convert_accounting_quantity'] = p.get('accounting_quantity')
            if p.get('is_bom'):
                p['convert_accounting_quantity'] = None
            p_inventory_obj = None
            # 每日库存为空时，取得商品库存
            position_id = p.get('position_id')
            if position_id and position_id != 1:
                key = str(position_id) + str(p.get('product_id'))
            else:
                key = str(p.get('product_id'))
            if key in products_inventory:
                p_inventory_obj = products_inventory[key]
            if p_inventory_obj is not None:
                p['inventory_quantity'] = convert_to_decimal(p_inventory_obj.get('quantity_avail'))
            if p['inventory_quantity'] is None:
                p['inventory_quantity'] = 0
            # 用商品折算后数量进行差异核算,bom商品无差异
            if p['convert_accounting_quantity']:
                p['diff_quantity'] = p['convert_accounting_quantity'] - p['inventory_quantity']
            else:
                if p['is_bom']:
                    p['diff_quantity'] = 0
                else:
                    p['diff_quantity'] = p.get('accounting_quantity') - p['inventory_quantity']
            p["diff_amount"] = Decimal(p['diff_quantity']) * Decimal(p.get("tax_price", 0))
            # 转为盘点数
            unit_rate = p.get("unit_rate", 1)
            p['diff_quantity'] = p['diff_quantity'] / unit_rate

        rows = []
        position_map = {}
        position_ids = []
        for p in products:
            position_id = p.get('position_id', 1)
            if not position_id:
                position_id = 1
            if position_map.get(position_id):
                position_map[position_id].append(p)
            else:
                position_map[position_id] = [p]
                if position_id != 1:
                    position_ids.append(position_id)
        position_list_ret = metadata_service.list_positions(return_fields="id,code,name",
                                                            ids=position_ids,
                                                            partner_id=partner_id, user_id=user_id).get(
            'rows', [])
        print('position_list_ret', position_list_ret)
        position_dict = {}
        if position_list_ret:
            for p in position_list_ret:
                position_dict[int(p['id'])] = [p.get('fields', {}).get('code', ''),
                                               p.get('fields', {}).get('name', '')]
        for position_id, ps in position_map.items():
            data = dict(
                position_id=position_id,
                position_code=position_dict[position_id][0] if position_id in position_dict else '',
                position_name=position_dict[position_id][1] if position_id in position_dict else '',
                products=ps,
                total=len(ps),
            )
            rows.append(data)
        ret['position_rows'] = rows
        ret['total'] = len(rows)
        return ret

    def get_stocktake_diff_report(self, request, partner_id, user_id):
        ret = {}
        start_date = request.start_date
        end_date = request.end_date
        target_date = request.target_date
        status = request.status
        schedule_code = request.schedule_code
        if target_date and not isinstance(target_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = target_date.seconds
            target_date = timestamp.ToDatetime()
            if target_date == datetime(1970, 1, 1):
                target_date = None
            else:
                target_date = target_date
        if start_date and not isinstance(start_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = start_date.seconds
            start_date = timestamp.ToDatetime()
            if start_date == datetime(1970, 1, 1):
                start_date = None
            else:
                start_date = start_date
        if end_date and not isinstance(end_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = end_date.seconds
            end_date = timestamp.ToDatetime()
            if end_date == datetime(1970, 1, 1):
                end_date = None
            else:
                end_date = end_date + timedelta(hours=32)
        store_ids = list(request.store_ids)
        store_ret = metadata_service.get_store_list(ids=store_ids,
                                                    # return_fields='id,company_info',
                                                    partner_id=partner_id,
                                                    user_id=user_id).get('rows', [])
        store_company_dict = {}
        company_ids = []
        if len(store_ret) > 0:
            for st in store_ret:
                store_company_dict[int(st['id'])] = {'id': int(st.get('company_info', [0])[0])}
                company_ids.append(int(st.get('company_info', [0])[0]))
        company_ret = metadata_service.get_company_list(ids=company_ids,
                                                        partner_id=partner_id,
                                                        user_id=user_id).get('rows', [])

        company_info_dict = {}
        if len(company_ret) > 0:
            for c in company_ret:
                company_info_dict[int(c['id'])] = [c.get('code'), c.get('name')]
        for k, data in store_company_dict.items():
            data['code'] = company_info_dict[data['id']][0] if company_info_dict.get(data['id']) else None
            data['name'] = company_info_dict[data['id']][1] if company_info_dict.get(data['id']) else None
        product_ids = list(request.product_ids)
        type = request.stocktake_type
        code = request.code
        offset = request.offset
        limit = request.limit
        order = request.order
        sort = request.sort
        diff_sub_sql = '''
                SELECT 
                st.code AS code,
                st.schedule_code AS schedule_code,
                st.status AS status,
                st.type AS type,
                st.target_date AS target_date,
                stp.branch_id,
                m_s.name AS branch_name,
                m_s.code AS branch_code,
                stp.product_id, 
                stp.product_code AS product_code,
                stp.product_name AS product_name,
                stp.unit_id,
                stp.unit_name,
                stp.unit_spec,
                stp.unit_rate,
                stp.accounting_unit_id,
                stp.accounting_unit_name,
                stp.accounting_unit_spec,
                IF(stp.is_pda=1,stp.tag_quantity,stp.quantity) AS quantity,
                stp.accounting_quantity AS accounting_quantity,
                stp.inventory_quantity AS inventory_quantity,
                stp.diff_quantity AS diff_quantity
                FROM supply_st_doc_details AS st
                INNER JOIN supply_st_product stp ON st.doc_id=stp.doc_id
                LEFT JOIN metadata_store m_s ON stp.branch_id=m_s.id
                WHERE {}
        '''
        if order and sort:
            if order != 'asc':
                diff_sub_sql += " order by -{}".format(sort)
            else:
                diff_sub_sql += " order by {}".format(sort)
        if limit:
            diff_sql = diff_sub_sql + " limit {}".format(limit)
            if offset:
                diff_sql += " offset {}".format(offset)
        else:
            diff_sql = diff_sub_sql
        print('diff_sql', diff_sql)
        if not target_date and (start_date and end_date):
            doc_date_sql = "st.target_date>='{}' AND st.target_date<'{}'".format(datetime2str(start_date),
                                                                                 datetime2str(end_date))
        elif target_date:
            doc_date_sql = "st.target_date='{}'".format(datetime2str(target_date))
        else:
            doc_date_sql = ""
        doc_type_sql = "st.type='{}'".format(type) if type else None

        doc_code_sql = "st.code='{}'".format(code) if code else None
        doc_schedule_code_sql = "st.schedule_code='{}'".format(schedule_code) if schedule_code else None
        doc_status_sql = "st.status='{}'".format(status) if status else None
        product_sql = "stp.product_id in ({})".format(
            ','.join([str(_id) for _id in product_ids])) if product_ids else None
        store_sql = "st.branch_id in ({})".format(
            ','.join([str(_id) for _id in store_ids])) if store_ids else None
        where_sql = ' AND '.join(
            [w for w in
             [doc_code_sql, doc_schedule_code_sql, doc_date_sql, doc_status_sql, doc_type_sql, store_sql, product_sql]
             if w]) + " AND st.status='CONFIRMED' AND st.partner_id={}".format(partner_id)
        total_sql_sub = diff_sub_sql.format(where_sql).replace(';', '')
        diff_sql = text(diff_sql.format(where_sql))
        total_sql = """
                    select count(*) from 
                    ({})
                    as tmp;
         """.format(total_sql_sub)
        with DummyTransaction(auto_commit=False) as trans:
            resproxy = trans.scope_session.execute(diff_sql)
            diff_rows = resproxy.fetchall()
            total_row = trans.scope_session.execute(total_sql).fetchall()
        ret['total'] = total_row[0][0]
        product_list = []
        product_ids = []
        for row in diff_rows:
            value = row[4]
            timestamp = Timestamp()
            timestamp.FromDatetime(value)
            company_more_info = store_company_dict.get(row[5], {})
            product_item = dict(
                code=row[0],
                schedule_code=row[1],
                status=row[2],
                type=row[3],
                target_date=timestamp,
                store_id=row[5],
                store_name=row[6],
                store_code=row[7],
                product_id=row[8],
                product_code=row[9],
                product_name=row[10],
                unit_id=row[11],
                unit_name=row[12],
                unit_code=row[13],
                unit_rate=row[14],
                accounting_unit_id=row[15],
                accounting_unit_name=row[16],
                accounting_unit_code=row[17],
                quantity=row[18],
                accounting_quantity=row[19],

                accounting_inventory_quantity=row[20],
                accounting_diff_quantity=row[21],

                inventory_quantity=row[20] / row[14] if (row[14] and row[20]) else row[20],
                diff_quantity=row[21] / row[14] if (row[14] and row[21]) else row[21],

                diff_quantity_percentage=row[21] / row[20] if (row[20] and row[21]) else 0,
                company_code=company_more_info.get('code'),
                company_name=company_more_info.get('name')
            )
            if row[8] not in product_ids and limit:
                product_ids.append(row[8])
            product_list.append(product_item)
        # 单位规格
        if limit:
            spec_ret = metadata_service.get_product_list(ids=product_ids,
                                                         return_fields='id,model_name',
                                                         partner_id=partner_id,
                                                         user_id=user_id).get('rows', [])
            spec_dict = {}
            if len(spec_ret) > 0:
                for s in spec_ret:
                    spec_dict[int(s['id'])] = s.get('model_name')
            for p in product_list:
                p['unit_spec'] = spec_dict.get(p['product_id'])
        ret['rows'] = product_list
        return ret

    def get_month_stocktake_messages(self, pub_date, partner_id):

        return self.stocktake_repo.get_month_stocktake_messages(pub_date, partner_id)

    def update_month_stocktake_messages(self, id):

        return self.stocktake_repo.update_month_stocktake_messages(id)

    def recreate_stocktake_doc(self, request, partner_id, user_id):
        doc_ids = list(request.doc_ids)
        stocktake_doc_list = []
        stocktake_doc_details_list = []
        update_stocktake_doc_details_list = []
        stocktake_product_list = []
        target_date = datetime(int(datetime.now().year), int(datetime.now().month),
                               int(datetime.now().day)) - timedelta(days=8)
        calculate_inventory = request.calculate_inventory
        schedule_name = request.schedule_name
        schedule_id = request.schedule_id
        schedule_code = request.schedule_code
        remark = request.remark
        # username = None
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        stocktake_doc_objs = self.stocktake_repo.get_stocktake_doc_list(doc_ids)
        # 已生成重盘的未审核不进行重盘
        # recreate_doc_id
        # 判断状态不为CONFIRMED剔除
        has_recreate_doc_id_no_confirm = []
        recreate_doc_ids = []
        for st in stocktake_doc_objs:
            if st.recreate_doc_id:
                recreate_doc_ids.append(st.recreate_doc_id)
        if len(recreate_doc_ids) > 0:
            recreate_stocktake_doc_objs = self.stocktake_repo.get_stocktake_doc_list(recreate_doc_ids)
            for re in recreate_stocktake_doc_objs:
                if re.status != 'CONFIRMED' and re.status != 'CANCELLED':
                    has_recreate_doc_id_no_confirm.append(re.original_doc_id)
        # 捞出所有原单据信息，商品信息，生成新单据单号，组装好，一把全部插入
        restocktake_doc_ids = []
        # 更新原单据重盘号
        # 边迭代边记录原单号和单号关系
        original_doc_id_map = {}
        for obj in stocktake_doc_objs:
            if obj.doc_id in has_recreate_doc_id_no_confirm:
                continue
            # 月盘需要8位
            if obj.type == 'M':
                code = Stocktake_month_code.get_stocktake_code(partner_id)
            else:
                code = Supply_doc_code.get_code_by_type('STORE_ST', partner_id, None)
            stocktake_doc = dict(
                id=get_guid(),
                partner_id=partner_id,
                user_id=user_id,
                schedule_id=schedule_id,
                branch_id=obj.branch_id,
                code=code,
                type=obj.type,
                target_date=target_date,
                status='INITED',
                process_status='INITED',
                # 默认原单的
                calculate_inventory=obj.calculate_inventory
            )
            stocktake_doc_details = dict(
                id=get_guid(),
                doc_id=stocktake_doc['id'],
                partner_id=partner_id,
                user_id=user_id,
                branch_id=obj.branch_id,
                branch_type=obj.branch_type,
                # 记录原单号
                original_code=obj.code,
                original_doc_id=obj.doc_id,
                # 记录重盘
                is_recreate=True,
                stocktake_type='R',
                code=stocktake_doc['code'],
                schedule_id=schedule_id,
                schedule_code=schedule_code,
                schedule_name=schedule_name,
                type=obj.type,
                target_date=target_date,
                status='INITED',
                process_status='INITED',
                calculate_inventory=obj.calculate_inventory,
                created_by=user_id,
                updated_by=user_id,
                remark=remark,
                created_name=username,
                updated_name=username
            )
            # 更新原单据重盘单号
            update_stocktake_details_doc = dict(
                id=obj.id,
                recreate_code=stocktake_doc['code'],
                recreate_doc_id=stocktake_doc['id'],
            )
            original_doc_id_map[obj.doc_id] = stocktake_doc['id']
            update_stocktake_doc_details_list.append(update_stocktake_details_doc)
            restocktake_doc_ids.append(stocktake_doc['id'])
            stocktake_doc_list.append(stocktake_doc)
            stocktake_doc_details_list.append(stocktake_doc_details)
        print('has_recreate_doc_id_no_confirm', has_recreate_doc_id_no_confirm, doc_ids)
        for x in has_recreate_doc_id_no_confirm:
            if x in doc_ids:
                doc_ids.remove(x)
        stocktake_product_objs = self.stocktake_repo.get_stocktake_product_list(doc_ids)
        print('time', datetime.now())
        for pr_obj in stocktake_product_objs:
            doc_id = original_doc_id_map.get(pr_obj.doc_id)
            product = dict(
                id=get_guid(),
                product_id=pr_obj.product_id,
                product_code=pr_obj.product_code,
                product_name=pr_obj.product_name,
                unit_rate=pr_obj.unit_rate,
                accounting_unit_id=pr_obj.accounting_unit_id,
                accounting_unit_name=pr_obj.accounting_unit_name,
                accounting_unit_spec=pr_obj.accounting_unit_spec,
                unit_id=pr_obj.unit_id,
                unit_name=pr_obj.unit_name,
                unit_spec=pr_obj.unit_spec,
                # 匹配新的单据doc_id
                doc_id=doc_id,
                is_system=False,
                partner_id=partner_id,
                user_id=user_id,
                branch_id=pr_obj.branch_id,
                target_date=target_date,
                status='INITED',
                created_by=user_id
            )
            stocktake_product_list.append(product)
        print('time----', datetime.now())
        self.stocktake_repo.recreate_stocktake_doc(stocktake_doc_list, stocktake_doc_details_list,
                                                   stocktake_product_list, update_stocktake_doc_details_list)
        ret = {'result': True,
               'restocktake_doc_ids': restocktake_doc_ids,
               'has_recreate_doc_id_no_confirm': has_recreate_doc_id_no_confirm}
        return ret

    def stocktake_doc_statistics(self, request, partner_id, user_id):
        ret = {}
        store_ids = list(request.store_ids)
        start_date = request.start_date
        end_date = request.end_date
        period_group_by = request.period_group_by
        stocktake_type = list(request.stocktake_type)
        status = list(request.status)
        limit = request.limit
        offset = request.offset
        order = request.order
        sort = request.sort
        rows = []
        total, data = self.stocktake_repo.stocktake_doc_statistics(store_ids=store_ids, start_date=start_date,
                                                                   end_date=end_date, period_group_by=period_group_by,
                                                                   stocktake_type=stocktake_type, status=status,
                                                                   limit=limit, offset=offset, order=order, sort=sort,
                                                                   partner_id=partner_id)
        print('len', len(data))
        if len(data) > 0:
            for d in data:
                r = dict(
                    date=d[0],
                    store_code=d[1] if d[1] else '',
                    store_name=d[2] if d[2] else '',
                    stocktake_type=d[3] if d[3] else '',
                    status=d[4] if d[4] else '',
                    count=d[5]
                )
                rows.append(r)
        ret['total'] = total
        ret['rows'] = rows
        return ret

    def get_need_rehandle_stocktake(self, bus_date, partner_id):

        return self.stocktake_repo.get_need_rehandle_stocktake(bus_date, partner_id)

    def get_need_update_stocktake_diff(self, bus_date):

        return self.stocktake_repo.get_need_update_stocktake_diff(bus_date)

    def update_store_stocktake_diff(self, doc_id, partner_id, user_id):
        print('[doc_id]', [doc_id])
        inv_ret = inventory_service.query_stocktake([doc_id], partner_id, user_id).get('stocktake_item', [])
        # {
        #     "total": 1,
        #     "stocktake_item": [
        #         {
        #             "batch_no": "***********",
        #             "account": {
        #                 "branch_id": "*********",
        #                 "product_id": "*********"
        #             },
        #             "stocktake_id": ***********,
        #             "qty": 1000,
        #             "real_qty": 990,
        #             "diff_qty": 10,
        #             "business_time": "2020-02-03T20:00:00Z"
        #         }
        #     ]
        # }
        print('inv_ret', inv_ret)
        diff_qty_dict = {}
        for i in inv_ret:
            product_id = i['account']['product_id']
            diff_qty = i.get('diff_qty', 0)
            real_qty = i.get('real_qty', 0)
            diff_qty_dict[int(product_id)] = [diff_qty, real_qty]
        updata_products = []
        products = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id, get_obj=True)
        for p in products:
            product_id = p.product_id
            if not diff_qty_dict.get(product_id):
                continue
            diff_qty = diff_qty_dict.get(product_id)[0]
            real_qty = diff_qty_dict.get(product_id)[1]
            unit_diff_quantity = diff_qty / float(p.unit_rate) if p.unit_rate else diff_qty
            update_data = dict(
                id=p.id,
                diff_quantity=diff_qty,
                inventory_quantity=real_qty,
                updated_at=datetime.utcnow(),
                unit_diff_quantity=unit_diff_quantity
            )
            updata_products.append(update_data)
        self.stocktake_repo.update_store_stocktake_diff(doc_id, updata_products)
        return

    def update_store_stocktake_diff_by_inventory(self, doc_id):
        # print('[doc_id]', [doc_id])
        st_obj = self.stocktake_repo.get_stocktake_doc_by_id(doc_id, partner_id=None, is_details=True)
        if not st_obj:
            return True
        inv_ret = inventory_service.query_stocktake([doc_id], st_obj.partner_id, st_obj.user_id).get('stocktake_item',
                                                                                                     [])
        # print('inv_ret', inv_ret)
        diff_qty_dict = {}
        for i in inv_ret:
            if i.get("sub_account_id"):
                key = str(i.get("sub_account_id")) + str(i['account']['product_id'])
            else:
                key = str(i['account']['product_id'])
            diff_qty = i.get('diff_qty', 0)
            real_qty = i.get('real_qty', 0)
            diff_qty_dict[key] = [diff_qty, real_qty]
        update_products = []
        products = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id, get_obj=True)
        for p in products:
            if p.position_id != 1 and p.position_id:
                key = str(p.position_id) + str(p.product_id)
            else:
                key = str(p.product_id)
            if not diff_qty_dict.get(key):
                continue
            diff_qty = diff_qty_dict.get(key)[0]
            real_qty = diff_qty_dict.get(key)[1]
            unit_diff_quantity = diff_qty / float(p.unit_rate) if p.unit_rate else diff_qty
            update_data = dict(
                id=p.id,
                diff_quantity=diff_qty,
                inventory_quantity=real_qty,
                updated_at=datetime.utcnow(),
                unit_diff_quantity=unit_diff_quantity
            )
            update_products.append(update_data)
        self.stocktake_repo.update_store_stocktake_diff(doc_id, update_products)
        return

    def rehandle_stocktake(self, doc_id, partner_id=None, user_id=None, username=None):
        #######################################################
        stocktake = self.get_stocktake_doc_by_id(doc_id, partner_id)
        products = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id, get_obj=True)
        if products is None or not isinstance(products, list) or len(products) == 0:
            return self.stocktake_repo.lock_stocktake_status(doc_id, 'APPROVED', 'CONFIRMED', user_id=user_id,
                                                             partner_id=partner_id)
        filters = {'allow_stocktake': True}
        product_fields = ["product_type", "bom_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            return_fields=['id'],
            # product_filters=product_filters,
            include_product_fields=product_fields,
            store_id=stocktake.branch_id,
            filters=filters,
            partner_id=partner_id,
            user_id=user_id
        )
        is_bom_products_list = []
        if ret:
            for data in ret.get('rows'):
                if data['bom_type'] == "MANUFACTURE":
                    is_bom_products_list.append(int(data.get('product_id', 1)))
        message = {}
        bom_products_map = {}
        for p in products:
            if p.unit_id is None or p.quantity is None:
                p.quantity = 0
            if p.unit_rate is None or p.unit_rate == 0:
                p.unit_rate = 1
            if p.is_pda:
                p.tag_quantity = 0
                p.accounting_quantity = 0
                for product_tag in p.product_tags:
                    p.accounting_quantity += product_tag.get('accounting_quantity', 0)
                p.tag_quantity = p.accounting_quantity / p.unit_rate
            else:
                p.accounting_quantity = p.unit_rate * p.quantity
            if p.product_id in is_bom_products_list and p.accounting_quantity != None and p.accounting_quantity != 0:
                param_dict = dict(
                    request_id=get_guid(),
                    store_id=stocktake.branch_id,
                    store_code=None,
                    sales_date=str(stocktake.target_date),
                    biz_code="STOCKTAKE",
                    biz_no=str(stocktake.id),
                    product_id=p.product_id,
                    product_code=None,
                    product_qty=abs(p.accounting_quantity),
                    partner_id=partner_id,
                    user_id=user_id
                )
                ret = Bom_service.get_bom(**param_dict)
                print('get_bom_ret*****', ret)
                if ret:
                    p.is_bom = True
                    if int(ret.get('request_id')) == int(param_dict['request_id']) and ret.get('bom'):
                        boms = ret["bom"]
                        for bom in boms:
                            product_id = str(bom.get('product_id'))
                            qty = bom.get('qty', 0)
                            if bom_products_map.get(product_id):
                                bom_products_map[product_id] += qty
                            else:
                                bom_products_map[product_id] = qty
        # 更新核算和tag数量
        db_session = session_maker()
        try:
            for p in products:
                if p.deleted:
                    continue
                update_products = {SupplySTProductDB.tag_quantity: p.tag_quantity,
                                   SupplySTProductDB.is_bom: p.is_bom,
                                   SupplySTProductDB.quantity: p.quantity, SupplySTProductDB.unit_rate: p.unit_rate,
                                   SupplySTProductDB.accounting_quantity: p.accounting_quantity,
                                   SupplySTProductDB.status: 'CONVERTING',
                                   SupplySTProductDB.updated_name: username,
                                   SupplySTProductDB.updated_at: datetime.now(),
                                   SupplySTProductDB.updated_by: p.updated_by}
                db_session.query(SupplySTProductDB).filter(SupplySTProductDB.id == p.id).update(update_products,
                                                                                                synchronize_session=False)
            update = {SupplySTProductDB.updated_at: datetime.now()}
            if user_id:
                update.update({SupplySTDocDetailsDB.updated_by: user_id})
            if username:
                update.update({SupplySTDocDetailsDB.updated_name: username})
            db_session.query(SupplySTDocDetailsDB).filter(
                SupplySTDocDetailsDB.doc_id == doc_id).update(update, synchronize_session=False)
            db_session.commit()
            # bom折算处理，异步消息
            message['doc_id'] = doc_id
            message['bom_products_map'] = bom_products_map
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            logging.debug('bom_products_map***************' + str(bom_products_map))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.STOCKTAKE_BOM_TOPIC,
                                message=message)

            # self.finalize_store_stocktake(doc_id,bom_products_map,partner_id=partner_id,user_id=user_id)
        except Exception as e:
            db_session.rollback()
            raise e
        finally:
            db_session.close()

    def read_excel_file(self, file_obj, filename):
        """读取excel文件"""
        extension = filename.split('.')[-1]
        logging.info("Read Excel filename {}-{}".format(filename, extension))
        content = file_obj
        if extension.upper() == 'XLS':
            sheet = pyexcel.get_sheet(file_type='xls', file_content=content)
        elif extension.upper() == 'XLSX':
            sheet = pyexcel.get_sheet(file_type='xlsx', file_content=content)
        else:
            raise FileFormatIncorrectError('文件格式错误，请导入Excel格式文件！')
        sheet.name_columns_by_row(0)
        headers = list(sheet.colnames)
        expect_headers = [
            u"商品类别",
            u"商品名称",
            u"商品编号",
            u"储藏类型",
            u"规格",
            u"盘点数量",
            u"盘点单位",
            u"仓位ID",
            u"仓位编号",
            u"仓位名称"
        ]
        # if set(headers) != set(expect_headers):
        #     raise FileFormatIncorrectError('表格表头格式错误！')
        if sheet.number_of_rows() > 5000:
            raise FileFormatIncorrectError('文件过大，请导入000行以内的数据！')
        # 获取表格内容数组
        rows = sheet.to_records()
        data_list = []
        # 用来判断同一个仓位的商品编号是否重复
        position_product_code = {}
        # 完成表格标题中文到英文的映射
        for row in rows:
            data = {}
            get_header_map = {
                u"商品名称": 'product_name',
                u"商品编号": 'product_code',
                u"储藏类型": 'storage_type',
                u"规格": 'spec',
                u"盘点数量": 'quantity',
                u"盘点单位": 'unit',
                u"盘点数量1": 'quantity1',
                u"盘点单位1": 'unit1',
                u"盘点数量2": 'quantity2',
                u"盘点单位2": 'unit2',
                u"盘点数量3": 'quantity3',
                u"盘点单位3": 'unit3',
                u"盘点数量4": 'quantity4',
                u"盘点单位4": 'unit4',
                u"仓位ID": 'position_id',
                u"仓位编号": 'position_code',
                u"仓位名称": 'position_name'
            }
            for header, key in get_header_map.items():
                data[key] = str(row.get(header)).strip() if row.get(header) is not None else None
            data_list.append(data)
            product_code = str(row.get('商品编号')).strip()
            position_id = data.get('position_id')
            position_name = data.get('position_name')
            if position_id not in position_product_code.keys():
                position_product_code[position_id] = [product_code]
            else:
                if product_code in position_product_code[position_id]:
                    raise DataDuplicationException(f"同一个仓位:{position_name}下商品编号重复-{product_code}")
                else:
                    position_product_code[position_id].append(product_code)
        if len(data_list) == 0:
            raise FileFormatIncorrectError("文件为空！")
        return data_list

    def convert_str_to_float(self, qty, is_necessary=False):
        """
        is_necessary:不可为空 - 可以为0 但是不可为空
        """
        if not qty or qty == "-" or qty == '':
            if is_necessary == True:
                return "主盘点数量不能为空；"
            return 0
        try:
            qty = float(qty)
            if qty < 0:
                return "盘点数量不可小于零；"
            return qty
        except Exception:
            return "商品数量类型不合法；"

    def checked_data_list(self, data_list, product_code_list, doc_product_code_list):
        """按行校验数据"""
        has_invalid_row = False
        for index, data in enumerate(data_list):
            row_num = index + 1
            data["row_num"] = row_num
            product_code = data.get('product_code')
            data["error_msg"] = ""
            quantity = self.convert_str_to_float(data.get('quantity'), is_necessary=True)
            if isinstance(quantity, str):
                data["error_msg"] += quantity
                data["quantity"] = 0
                has_invalid_row = True
            else:
                data["quantity"] = quantity

            quantity1 = self.convert_str_to_float(data.get('quantity1'))
            if isinstance(quantity1, str):
                data["error_msg"] += quantity1
                data["quantity1"] = 0
                has_invalid_row = True
            else:
                data["quantity1"] = quantity1

            quantity2 = self.convert_str_to_float(data.get('quantity2'))
            if isinstance(quantity2, str):
                data["error_msg"] += quantity2
                data["quantity2"] = 0
                has_invalid_row = True
            else:
                data["quantity2"] = quantity2

            quantity3 = self.convert_str_to_float(data.get('quantity3'))
            if isinstance(quantity3, str):
                data["error_msg"] += quantity3
                data["quantity3"] = 0
                has_invalid_row = True
            else:
                data["quantity3"] = quantity3

            quantity4 = self.convert_str_to_float(data.get('quantity4'))
            if isinstance(quantity4, str):
                data["error_msg"] += quantity4
                data["quantity4"] = 0
                has_invalid_row = True
            else:
                data["quantity4"] = quantity4

            if str(product_code) not in product_code_list:
                data["error_msg"] += "商品编码未维护主档；"
                has_invalid_row = True

            if str(product_code) not in doc_product_code_list:
                data["error_msg"] += "该商品不在当前盘点单中；"
                has_invalid_row = True

        return data_list, has_invalid_row

    def stocktake_product_import(self, request, partner_id=None, user_id=None):
        """仓库/门店盘点单明细商品导入
        1、文件读取，格式校验，将数据序列化
            -系统文件格式：文件格式是否与下载模版的文件格式一致（Excel）
            -文件大小限制
        2、根据盘点单号取得盘点商品信息
            -商品编号：商品编号是否合法/是否在新建盘点单中
            -盘点数量：不能为负数；
            -盘点数量：不能为空；
        3、校验有错误，返回所有导入数据，按行提示给前端，直至用户更改正确为止
        4、校验成功, 无错误将数据存储在数据库中, 同时返回给前端结果和数据
        6、用户点击"确认"按钮，根据保存的文件id取出导入商品列表
        5、将取得商品信息与导入的商品信息合并
        6、更新盘点单
        """
        ret = {}
        # 将grpc传输过来的文件转换为可以读取的文件对象
        file_data = base64.b64decode(request.file_data)
        filename = request.file_name
        doc_id = request.doc_id
        doc_obj = self.stocktake_repo.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
        # 确认一下导入是否需要权限校验
        if doc_obj:
            pass
        else:
            raise DataValidationException('无此盘点单据')
        temp = tempfile.TemporaryFile()
        temp.write(file_data)
        temp.seek(0)
        file_obj = temp.read()
        # 读取表格文件获取数据列表
        data_list = self.read_excel_file(file_obj, filename)
        # 获取主档商品编码列表
        metadata_products = metadata_service.get_product_list(return_fields="code", partner_id=partner_id,
                                                              user_id=user_id).get('rows', [])
        product_code_list = [str(p.get('code')) for p in metadata_products] if metadata_products else []

        # 通过盘点单号取出盘点单中商品信息
        doc_product_list = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id=doc_id,
                                                                                 partner_id=partner_id)
        if not doc_product_list or len(doc_product_list) < 1:
            raise DataValidationException("该盘点单中无商品记录")
        doc_product_code_list = [str(st_p.get('product_code', "")) for st_p in doc_product_list]

        if len(data_list) <= 0:
            return ret

        # 按行校验
        checked_data_list, has_invalid_row = self.checked_data_list(data_list, product_code_list, doc_product_code_list)
        temp.close()

        # 有错误直接返回
        ret['result'] = not has_invalid_row
        ret['rows'] = checked_data_list
        ret['file_name'] = filename
        ret['rows_num'] = len(data_list)
        if has_invalid_row:
            return ret
        else:
            # 无错误将导入记录存入一张表中
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            batch = {
                "filename": str(filename),
                "status": 'INIT',
                'id': get_guid(),
                'doc_id': doc_id,
                'created_name': username,
                'updated_name': username,
            }
            ret['batch_id'] = batch.get('id')
            # 把无用的字段删除只保留商品编码和数量还有row_num行号
            data_list = []
            for data in checked_data_list:
                row = dict(
                    product_code=data.get('product_code'),
                    quantity=data.get('quantity') if data.get('quantity') else 0,
                    position_id=data.get('position_id') if data.get('position_id') else 1,
                    units=[dict(
                        unit=data.get('unit'),
                        quantity=data.get('quantity') if data.get('quantity') else 0
                    )]
                )
                if data.get('unit1') and data.get('unit1') != "-":
                    row['units'].append(dict(
                        unit=data.get('unit1'),
                        quantity=data.get('quantity1') if data.get('quantity1') else 0
                    ))
                if data.get('unit2') and data.get('unit2') != "-":
                    row['units'].append(dict(
                        unit=data.get('unit2'),
                        quantity=data.get('quantity2') if data.get('quantity2') else 0
                    ))
                if data.get('unit3') and data.get('unit3') != "-":
                    row['units'].append(dict(
                        unit=data.get('unit3'),
                        quantity=data.get('quantity3') if data.get('quantity3') else 0
                    ))
                if data.get('unit4') and data.get('unit4') != "-":
                    row['units'].append(dict(
                        unit=data.get('unit4'),
                        quantity=data.get('quantity4') if data.get('quantity4') else 0
                    ))
                data_list.append(row)
            self.stocktake_repo.add_st_product_import_record(data_list=data_list, batch=batch,
                                                             user_id=user_id)
            return ret

    def update_stocktake_import_batch(self, request, partner_id, user_id):
        """仓库/门店盘点单商品导入文件状态修改业务逻辑
            1、判断该导入记录是否存在
            2、存在取出商品导入信息、盘点单号
            3、通过盘点单号取出盘点单中商品信息
            4、将取得商品信息与导入的商品信息合并
            5、更新盘点单商品信息
        """
        res = {}
        batch_id = convert_to_int(request.batch_id)
        status = request.status
        batch = self.stocktake_repo.get_st_product_import_by_batch_id(batch_id=batch_id)
        if not batch:
            raise DataValidationException("该导入记录不存在")
        # 更新导入记录状态CANCEL/CONFIRM
        if status not in ["INIT", "CONFIRM", "CANCEL"]:
            raise DataValidationException("状态{}不合法".format(status))
        if status == "CONFIRM":
            # 确认导入, 取出商品导入信息、盘点单号
            doc_id = batch.doc_id
            file_data = batch.file_data
            file_data = json.loads(file_data)
            import_product_list = file_data.get('rows')
            doc_obj = self.stocktake_repo.get_stocktake_doc_by_id(doc_id, partner_id, is_details=True)
            # 通过盘点单号取出盘点单中商品信息
            doc_product_list = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id=doc_id,
                                                                                     partner_id=partner_id)
            if not doc_product_list or len(doc_product_list) < 1:
                raise DataValidationException("该盘点单中无商品记录")
            pro_ids = [p.get('product_id') for p in doc_product_list]
            product_map = get_product_map(product_ids=pro_ids, return_fields="id,code,name", partner_id=partner_id,
                                          user_id=user_id)
            # 将取得商品信息与导入的商品信息合并
            update_st_products = []
            tag_products_map = {}
            if import_product_list and len(import_product_list) > 0:
                doc_product_map = dict()
                for st_p in doc_product_list:
                    key = str(st_p.get('product_code')) + str(st_p.get('position_id'))
                    product = product_map.get(str(st_p.get('product_id')), {})
                    units = product.get('units', [])
                    units_dict = dict()
                    for unit in units:
                        if unit.get('stocktake') is True:
                            units_dict[unit.get('name')] = convert_to_int(unit.get('id'))

                    doc_product_map[key] = dict(id=st_p.get('id'), units_dict=units_dict, unit_id=st_p.get('unit_id'))

                for imp in import_product_list:
                    key = str(imp.get('product_code')) + str(imp.get('position_id'))
                    quantity = convert_to_decimal(imp.get('quantity')) if imp.get('quantity') else 0
                    doc_product = doc_product_map.get(key)
                    imp_units = imp.get('units', [])
                    if doc_product:
                        product_obj = StockTakeProduct()
                        product_obj.id = doc_product.get('id')
                        units_dict = doc_product.get('units_dict')

                        if doc_obj.branch_type == "STORE" or doc_obj.branch_type == "FRS_STORE":
                            # 门店盘点单构造 tag_products_map 去更新标签商品
                            p_key = str(imp.get('product_code'))
                            tag_products_map[p_key] = []
                            product_obj.is_pda = True
                            product_obj.quantity = 0
                            for u in imp_units:
                                if u.get('unit') in units_dict:
                                    tag_products_dict = dict()
                                    tag_products_dict['tag_id'] = 2
                                    tag_products_dict['tag_name'] = '默认标签'
                                    tag_products_dict['unit_id'] = units_dict[u.get('unit')]
                                    tag_products_dict['tag_quantity'] = u.get('quantity')
                                    tag_products_map[p_key].append(tag_products_dict)
                        else:
                            if quantity == 0:
                                product_obj.is_null = True
                            product_obj.unit_id = doc_product.get('unit_id')
                            product_obj.quantity = quantity
                        update_st_products.append(product_obj)
                attach_price = True if doc_obj.branch_type == "FRS_STORE" else False
                update_res = self.update_stocktake_products_with_quantity(doc_id=doc_id,
                                                                          update_products=update_st_products,
                                                                          imp_tags_pro_map=tag_products_map,
                                                                          allow_status=['INITED',
                                                                                        'REJECTED'],
                                                                          partner_id=partner_id, user_id=user_id,
                                                                          branch_id=doc_obj.branch_id,
                                                                          attach_price=attach_price)
                if update_res is False:
                    raise Exception("导入失败")
                res['doc_id'] = doc_id
        update_res = self.stocktake_repo.update_import_batch_status(batch_id=batch_id, status=status,
                                                                    user_id=user_id)
        res["result"] = update_res
        return res

    def get_stocktake_log(self, doc_id, partner_id, user_id):
        """查询盘点操作log"""
        res = {}
        total, logs = self.stocktake_repo.get_stocktake_log(doc_id, partner_id=partner_id)
        res["rows"] = []
        res["total"] = total
        user_ids = [log.created_by for log in logs]
        user_dict = get_username_map(partner_id=partner_id, user_id=user_id, ids=user_ids)
        for log in logs:
            row = dict(
                id=log.id,
                status=log.doc_status,
                created_at=self.get_timestamp(log.created_at),
                created_by=log.created_by,
                created_name=user_dict.get(log.created_by),
                reason=log.reason
            )
            res["rows"].append(row)
        return res

    def get_stocktake_breif(self, request, partner_id=None, user_id=None):

        # 门店编号
        store_codes = list(request.store_code)
        start_date = request.start_date
        end_date = request.end_date

        if start_date and not isinstance(start_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = start_date.seconds
            start_date = timestamp.ToDatetime()
            start_date = start_date
        if end_date and not isinstance(end_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = end_date.seconds
            end_date = timestamp.ToDatetime()
            end_date = end_date

        branch_list = metadata_service.get_store_list(filters={"code__in": store_codes},
                                                      return_fields="id,code",
                                                      partner_id=partner_id, user_id=user_id).get('rows', [])
        store_ids = []
        store_map = {}
        if branch_list:
            for store in branch_list:
                store_ids.append(convert_to_int(store.get('id')))
                store_map[int(store.get('id', 0))] = store.get('code')

        total, doc_obj_list = self.stocktake_repo.list_stocktake_doc_details(partner_id=partner_id,
                                                                             include_total=True,
                                                                             branch_ids=store_ids,
                                                                             start_date=start_date, end_date=end_date)

        res = {
            "rows": [],
            "total": total
        }
        if total:
            for obj in doc_obj_list:
                doc_obj = {
                    'status': obj.get("status"),
                    'code': obj.get("code"),
                    'store_code': store_map.get(obj.get("branch_id", 0)),
                    'stocktake_date': obj.get("target_date")
                }
                if obj.get('stocktake_type') == 'PLAN':
                    doc_obj["type"] = obj.get("type")
                else:
                    doc_obj["type"] = "R"

                res['rows'].append(doc_obj)
        return res

    @time_cost
    def addProductsToStocktakeForPOS(self, doc_id, update_products, imp_tags_pro_map=None,
                                     allow_status=None, partner_id=None, user_id=None, username=None,
                                     branch_id=None, attach_price=False):
        """
        更新盘点单的商品
        :param doc_id:
        :param update_products:
        :param allow_status:
        :param user_id:
        :param partner_id
        :param username
        :param imp_tags_pro_map: {product_code: [tag_dict, ...]} 盘点导入
        :param branch_id
        :param attach_price 是否计算价格
        :return:
        """

        product_ids = [p.product_id for p in update_products]
        attach_price = True
        logging.info('list_stocktake_products_by_doc_id-------{},{},{}'.format(doc_id, product_ids, partner_id))
        products_exist = self.stocktake_repo.list_stocktake_products_by_doc_id(doc_id,
                                                                               product_ids=product_ids,
                                                                               partner_id=partner_id)

        # logging.info('sssss-------{}'.format(product_ids))
        logging.info('products_exist-------{}'.format(products_exist))
        # if isinstance(products_exist, list):
        for p in products_exist:
            # logging.info('sssss',p.get('product_id'))
            if p.get('product_id') in product_ids:
                return True

        unit_dict = self.get_unit_map(partner_id=partner_id, user_id=user_id)
        units_rate_dict = {}
        main_products_ret = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                              return_fields='id,code,name',
                                                              partner_id=partner_id, user_id=user_id
                                                              )
        products_info_map = {}
        main_products = []

        logging.info("product_ids {}".format(product_ids))
        logging.info("main_products_ret {}".format(main_products_ret))
        if main_products_ret:
            main_products = main_products_ret['rows']
        for main_p in main_products:
            product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
            products_info_map[product_id] = {}
            products_info_map[product_id]['product_name'] = main_p['name'] if 'name' in main_p and main_p[
                'name'] else None
            products_info_map[product_id]['product_code'] = main_p['code'] if 'code' in main_p and main_p[
                'code'] else None
            if main_p.get('units'):
                for unit in main_p['units']:
                    if unit.get('default'):
                        products_info_map[product_id]['accounting_unit_id'] = unit['id'] if 'id' in unit and unit[
                            'id'] else None
                        products_info_map[product_id]['accounting_unit_spec'] = unit_dict[str(unit['id'])]['code']
                        products_info_map[product_id]['accounting_unit_name'] = unit_dict[str(unit['id'])]['name']
                    key = str(product_id) + str(unit['id'])
                    units_rate_dict[key] = round(unit.get('rate'), 7) if unit.get('rate') else None
        # 获取商品的含税单价和税率
        product_unit_adjust_tax_price_map = None
        product_unit_adjust_tax_ratio_map = None
        product_unit_adjust_sales_price_map = None
        if attach_price is True:
            product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                store_id=branch_id, user_id=user_id, partner_id=partner_id, product_ids=product_ids,
                include_sales_price=True)
            # 商品实时库存, 保存时还是要取实时库存, 需要计算差异数量, 差异金额
            products_inventory = inventory_service.get_products_inventory_by_branch_id(
                branch_id=branch_id,
                partner_id=partner_id,
                user_id=user_id,
                product_ids=product_ids,
                detail=True
            )
        products = []
        product_tags = []
        product_tags_exist = []
        del_tag_ids = []
        # pc与pda交换
        # 切换pc，清楚pda数据，切换pda，pc数量变为0
        update_o_product_tags_ids = []
        if update_products:
            # doc_product_tags_map 来获取存在的标签记录的主键id列表，多单位下一个商品下的标签可能会重复
            doc_product_tags_map = {}  # {product_id: {str(tag_id+unit_id): query_tag}}
            # 通过盘点单id捞出该盘点单对应的商品标签记录
            doc_product_tags = self.stocktake_repo.list_doc_product_tags(doc_id=doc_id, partner_id=partner_id)
            if doc_product_tags:
                # 盘点导入，清空之前存在的标签记录，再重新覆盖插入
                if imp_tags_pro_map:
                    del_tag_ids.extend([tag.id for tag in doc_product_tags])
                # 页面更新需要根据标签和单位判断该条数据是否存在
                for tag in doc_product_tags:
                    tag_id = tag.tag_id
                    product_id = tag.product_id
                    unit_id = tag.unit_id
                    key = str(tag_id) + str(unit_id)
                    if product_id not in doc_product_tags_map.keys():
                        doc_product_tags_map[product_id] = {key: tag}
                    else:
                        if key not in doc_product_tags_map[product_id].keys():
                            doc_product_tags_map[product_id][key] = tag
            # if imp_tags_pro_map:
            #     # 通过盘点单id捞出该盘点单对应的商品标签记录
            #     doc_product_tags = self.stocktake_repo.list_doc_product_tags(doc_id=doc_id, partner_id=partner_id)
            #     if doc_product_tags:
            #         for tag in doc_product_tags:
            #             del_tag_ids.append(tag.id)
            for p in update_products:
                logging.info("update_products {}".format(p))
                product_dict = dict(id=get_guid(), doc_id=doc_id, partner_id=partner_id)
                product_id = p.product_id
                if not product_id:
                    continue
                product_dict['product_id'] = product_id
                product_dict['branch_id'] = branch_id
                product_info = products_info_map.get(int(product_id))
                logging.info("product_info {}".format(product_info))
                if not product_info:
                    continue
                product_dict['product_code'] = product_dict['material_number'] = product_info.get('product_code')
                product_dict['product_name'] = product_info.get('product_name')
                product_dict['accounting_unit_id'] = product_info.get('accounting_unit_id')
                product_dict['accounting_unit_spec'] = product_info.get('accounting_unit_spec')
                product_dict['accounting_unit_name'] = product_info.get('accounting_unit_name')
                product_dict['is_pda'] = p.is_pda
                product_dict['is_empty'] = p.is_empty
                # if not p.unit_id:
                #     raise StocktakeUnitException(
                #         "存在无盘点单位商品，请配好盘点单位%s" % (
                #             p.id))
                if p.unit_id:
                    product_dict['unit_id'] = p.unit_id
                    product_dict['unit_name'] = unit_dict[str(p.unit_id)]['name']
                    product_dict['unit_spec'] = unit_dict[str(p.unit_id)]['code']
                    key = str(product_dict['product_id']) + str(p.unit_id)
                    product_dict['unit_rate'] = units_rate_dict[key] if units_rate_dict.get(key) else 1
                else:

                    product_dict['unit_id'] = product_info['accounting_unit_id']
                    product_dict['unit_name'] = product_info['accounting_unit_name']
                    product_dict['unit_spec'] = product_info['accounting_unit_spec']
                    key = str(product_dict['product_id']) + str(product_dict['unit_id'])
                    product_dict['unit_rate'] = units_rate_dict[key] if units_rate_dict.get(key) else 1

                product_dict['is_null'] = p.is_null
                # PC盘点数量置空操作
                if p.is_null:
                    product_dict['quantity'] = None
                    product_dict['accounting_quantity'] = None
                    product_dict['convert_accounting_quantity'] = None
                else:
                    product_dict['is_null'] = False
                    product_dict['quantity'] = p.quantity
                    product_dict['accounting_quantity'] = convert_to_decimal(p.quantity) * convert_to_decimal(
                        product_dict.get('unit_rate')) if p.quantity else 0
                    product_dict['convert_accounting_quantity'] = convert_to_decimal(p.quantity) * convert_to_decimal(
                        product_dict.get('unit_rate')) if p.quantity else 0
                if imp_tags_pro_map or (p.is_pda and p.tag_products):
                    if imp_tags_pro_map:
                        product_code = product_dict['product_code']
                        imp_tags = imp_tags_pro_map.get(str(product_code), [])
                        for tag in imp_tags:
                            if tag.get('unit_id') and p.id:
                                product_tag_dict = dict(
                                    id=get_guid(),
                                    tag_id=tag.get('tag_id', 2),
                                    tag_name=tag.get('tag_name', '默认标签'),
                                    stp_id=p.id,
                                    doc_id=doc_id,
                                    created_at=datetime.now(),
                                    product_id=product_id,
                                    partner_id=partner_id,
                                    user_id=user_id,
                                    created_by=user_id,
                                    unit_rate=1,  # 给个默认值
                                )
                                unit_id = tag.get('unit_id')
                                tag_quantity = tag.get('tag_quantity', 0)
                                product_tag_dict['unit_id'] = unit_id
                                product_tag_dict['tag_quantity'] = tag_quantity
                                if unit_id:
                                    key = str(product_id) + str(unit_id)
                                    product_tag_dict['unit_rate'] = units_rate_dict[key] if units_rate_dict.get(
                                        key) else 1
                                    product_tag_dict['unit_name'] = unit_dict[str(unit_id)]['name']
                                    product_tag_dict['unit_spec'] = unit_dict[str(unit_id)]['code']
                                product_tag_dict['accounting_unit_id'] = product_dict['accounting_unit_id']
                                product_tag_dict['accounting_unit_spec'] = product_dict['accounting_unit_spec']
                                product_tag_dict['accounting_unit_name'] = product_dict['accounting_unit_name']
                                product_tag_dict['accounting_quantity'] = convert_to_decimal(
                                    tag_quantity) * convert_to_decimal(
                                    product_tag_dict['unit_rate']) if tag_quantity else 0
                                product_dict['quantity'] += tag_quantity  # 同步更新商品盘点数量
                                product_dict['accounting_quantity'] += product_tag_dict['accounting_quantity']
                                product_dict['convert_accounting_quantity'] += product_tag_dict['accounting_quantity']
                                product_tags.append(product_tag_dict)

                    elif p.tag_products:
                        tag_product_map = doc_product_tags_map.get(product_id, {})
                        update_tag_keys = [str(tag.tag_id) + str(tag.unit_id) for tag in p.tag_products]
                        for tag_key, tag in tag_product_map.items():
                            if tag_key not in update_tag_keys:
                                tag_quantity = tag.tag_quantity if tag.tag_quantity else 0
                                accounting_quantity = tag.accounting_quantity if tag.accounting_quantity else 0
                                product_dict['quantity'] += float(tag_quantity)  # 将盘点单中原来的标签数量和这次更新的标签数量相加
                                product_dict['accounting_quantity'] += accounting_quantity
                                product_dict['convert_accounting_quantity'] += accounting_quantity
                        for tag in p.tag_products:
                            if tag.unit_id and p.product_id:
                                product_tag_dict = dict(
                                    tag_id=tag.tag_id,
                                    tag_name=tag.tag_name,
                                    unit_id=tag.unit_id
                                )
                                tag_quantity = tag.tag_quantity if tag.tag_quantity else 0
                                product_tag_dict['tag_quantity'] = tag_quantity
                                product_dict['quantity'] += tag_quantity  # 同步更新商品盘点数量
                                product_tag_dict['stp_id'] = product_dict['id']
                                product_tag_dict['unit_rate'] = 1  # 给个默认值
                                if tag.unit_id:
                                    key = str(product_dict['product_id']) + str(tag.unit_id)
                                    product_tag_dict['unit_rate'] = units_rate_dict[key] if units_rate_dict.get(
                                        key) else 1
                                    product_tag_dict['unit_name'] = unit_dict[str(tag.unit_id)]['name']
                                    product_tag_dict['unit_spec'] = unit_dict[str(tag.unit_id)]['code']
                                product_tag_dict['accounting_unit_id'] = product_dict['accounting_unit_id']
                                product_tag_dict['accounting_unit_spec'] = product_dict['accounting_unit_spec']
                                product_tag_dict['accounting_unit_name'] = product_dict['accounting_unit_name']
                                product_tag_dict['accounting_quantity'] = convert_to_decimal(
                                    tag.tag_quantity) * convert_to_decimal(
                                    product_tag_dict['unit_rate']) if tag.tag_quantity else 0
                                # 根据标签id取原盘点单中的标签记录
                                key = str(tag.tag_id) + str(tag.unit_id)
                                tag_product = tag_product_map.get(key)
                                # 存在更新
                                if tag_product:
                                    product_tag_dict['id'] = tag_product.id
                                    product_tags_exist.append(product_tag_dict)
                                else:
                                    product_tag_dict['created_at'] = datetime.now()
                                    product_tag_dict['id'] = get_guid()
                                    product_tag_dict['doc_id'] = doc_id
                                    product_tag_dict['product_id'] = product_dict['product_id']
                                    product_tag_dict['partner_id'] = partner_id
                                    product_tag_dict['user_id'] = user_id
                                    product_tag_dict['created_by'] = user_id
                                    product_tags.append(product_tag_dict)
                                product_dict['accounting_quantity'] += product_tag_dict['accounting_quantity']
                                product_dict['convert_accounting_quantity'] += product_tag_dict['accounting_quantity']
                        del_tag_ids = [convert_to_int(_id) for _id in p.del_tag_ids] if p.del_tag_ids else []
                else:
                    update_o_product_tags_ids.append(p.id)
                if attach_price:
                    fill_adjust_tax_price_tax_rate(product_dict, product_unit_adjust_tax_price_map,
                                                   product_unit_adjust_tax_ratio_map,
                                                   product_dict['product_id'], 1, product_dict['accounting_quantity'],
                                                   'amount', 'sales_amount',
                                                   product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                    quantity_avail = Decimal(0)
                    if products_inventory and str(product_id) in products_inventory and products_inventory[
                        str(product_id)]:
                        inventory_obj = products_inventory[str(product_id)]
                        if inventory_obj:
                            quantity_avail += convert_to_decimal(
                                inventory_obj.get('quantity_avail') if inventory_obj.get('quantity_avail') else 0)
                    product_dict["diff_quantity"] = (Decimal(product_dict['accounting_quantity']) if product_dict[
                        'accounting_quantity'] else Decimal(0)) - Decimal(quantity_avail)
                    product_dict["diff_amount"] = product_dict["diff_quantity"] * Decimal(
                        product_dict.get("tax_price", 0))
                    product_dict["diff_sales_amount"] = product_dict["diff_quantity"] * Decimal(
                        product_dict.get("sales_price", 0))
                products.append(product_dict)
        logging.info("product_tags {}".format(product_tags))

        if len(products) == 0:
            return True
        elif attach_price:
            self.stocktake_repo.update_stocktake_products_with_price_for_pos(doc_id)

        return self.stocktake_repo.addProductsToStocktakeForPOS(doc_id, products,
                                                                product_tags_exist=product_tags_exist,
                                                                product_tags=product_tags,
                                                                allow_status=allow_status,
                                                                partner_id=partner_id,
                                                                update_o_product_tags_ids=update_o_product_tags_ids,
                                                                del_tag_ids=del_tag_ids,
                                                                user_id=user_id, username=username)

    def export_stocktake_doc_by_id(self,partner_id, doc_id, limit=None, offset=None, include_total=False,
                               include_unit=False, diff_top=False, diff_bottom=False, category_id=None,
                               storage_type=None, product_name=None, is_diff_count=None, user_id=None, branch_id=None,
                               **kwargs):
        # 先查询主表信息
        doc_obj = self.stocktake_repo.get_stocktake_doc_by_id(doc_id, partner_id=partner_id)
        if not doc_obj:
            raise DataValidationException('无此盘点单据')
        # 再查询商品信息
        total,position_rows = self.list_stocktake_product(doc_id=doc_id,partner_id=partner_id, limit=limit, offset=offset, include_total=include_total,
                                 include_unit=include_unit, diff_top=diff_top, diff_bottom=diff_bottom,
                                    category_id=category_id, storage_type=storage_type, product_name=product_name,
                                    is_diff_count=is_diff_count, user_id=user_id, branch_id=branch_id, **kwargs)
        res = {}
        res['doc_code'] = doc_obj.code
        res['branch_id'] = doc_obj.branch_id
        res['target_date'] = self.get_timestamp(doc_obj.target_date)

        # 查询门店信息
        store_info = metadata_service.get_store( doc_obj.branch_id, partner_id=partner_id, user_id=user_id)
        res['branch_name'] = store_info.get('name') if store_info else ''
        res['branch_code'] = store_info.get('code') if store_info else ''
        res['total']= total
        rows = []
        #  // 商品ID
    # uint64  product_id = 1;
    # // 商品编码
    # string  product_code = 2;
    # // 商品名称
    # string  product_name = 3;
    # // 储藏类型
    # string storage_type = 4;
    # // 商品类别
    # uint64 category_id =5;
    # // 商品类别编码
    # string  category_code = 6;
    # // 商品类别名称
    # string category_name = 7;
    # // 规格
    # string spec =8;
    # // 单位数量信息{单位名称，数量}
   # google.protobuf.Struct   product_tag=9;

        if not position_rows:
            return res

        for position_row in position_rows:
            products = position_row.get('products',[])
            for product in products:
                row = {}
                row['product_id'] = product.get('product_id', 0)
                row['product_code'] = product.get('product_code', '')
                row['product_name'] = product.get('product_name', '')
                row['storage_type'] = product.get('storage_type', '')
                row['category_id'] = product.get('category_id', 0)
                row['category_code'] = product.get('category_code', '')
                row['category_name'] = product.get('category_name', '')
                row['spec'] = product.get('spec', '')
                tag_info ={}
                for product_tag in product.get('product_tags', []):
                    qty = convert_to_float(product_tag.get("tag_quantity", 0))
                    tag_info[product_tag.get("unit_name")]=qty
                logger.info("tag_info=====:{}".format(tag_info))
                row['product_tag'] = dict_to_struct(tag_info)
                rows.append(row)
        res['rows'] = rows
        return res









def list_stores_by_region_id(region_ids=None, partner_id=None, user_id=None):
    """
    公用方法
    根据管理区域id列表获取包含的门店列表
    region_ids [str(管理区域id)]
    :return store_ids[int]"""
    # 获取该区域下所有子级区域关联的门店要用relation_filters
    relation_filters = {'branch_region': [str(b_id) for b_id in region_ids]}
    store_ids = []
    branch_list = metadata_service.get_store_list(relation_filters=relation_filters,
                                                  return_fields="id",
                                                  partner_id=partner_id, user_id=user_id).get('rows', [])
    if branch_list:
        for store in branch_list:
            store_ids.append(convert_to_int(store.get('id')))
    return list(set(store_ids))


def get_all_store_list(partner_id=None, user_id=None):
    """拉取主档所有启用门店id列表"""
    store_list = []
    res = metadata_service.get_store_list(filters={"open_status__in": ["OPENED"],
                                                   "status__in": ["ENABLED"]},
                                          return_fields="id",
                                          partner_id=partner_id, user_id=user_id).get('rows', [])
    for store in res:
        store_list.append(convert_to_int(store.get('id')))
    return store_list



ssm = StocktakeScheduleModule()
