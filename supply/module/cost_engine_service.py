# -*- coding: utf-8 -*-
import json
from datetime import datetime, timedelta
from sqlalchemy import func
import logging
from datetime import datetime
from decimal import Decimal
from hex_exception import RecordAlreadyExist
from google.protobuf.timestamp_pb2 import Timestamp

from ..error.exception import NoResultFoundError, StatusUnavailable, DataValidationException
from ..driver.mysql import db_commit, session
from ..utils.helper import set_model_from_db, convert_to_int, convert_to_datetime, set_db, translate_utc_time, MessageTopic
from ..utils.encode import encodeUTF8
from ..utils.resulting import ErrorCode
from ..utils.snowflake import gen_snowflake_id
from ..utils import pb2dict
# from ..task import public

from ..client.metadata_service import metadata_service 
from ..client.inventory_service import inventory_service
from ..client.cost_engine_service import cost_engine_service
from ..client.report_service import report_service
from ..client.receipt_service import receipt_service

from ..model.daily_cut import DailyCutStoreModel, DailyCutLogModel, \
    CostTriggerLogModel, CostTriggerPeriodModel




class CostEngineTriggerService():

    # 接收成本引擎回传内容
    def cost_count_callback(self, batch_no, category, success=None, msg=None, partner_id=None, user_id=None):
        logging.info('接收到成本引擎回传内容{}:{}'.format(batch_no, success))
        if not success:
            success = 0
        log_db = CostTriggerLogModel.update_log_res(batch_no, success, msg)
        # if category == 1: # 物料统计任务
        #     msg = {
        #             "partner_id": partner_id,
        #             "user_id": user_id,
        #             "start_date": log_db.start_time.strftime('%Y-%m-%d %H:%M:%S'),
        #             "end_date": log_db.end_time.strftime('%Y-%m-%d %H:%M:%S'),
        #             "branch_type":log_db.branch_type,
        #             "branch_id":log_db.branch_id
        #         }
        #     logging.info('自动触发成本计算BOM {}'.format(msg))
        #     public(MessageTopic.TRIGGER_COST_COUNT_BOM_TOPIC, msg)

        return {'code':'100'}
    
    def list_cost_trigger_log(self, start_time, end_time, period_ids=None, 
                                    status=None, limit=None, offset=None, partner_id=None, req_types=None):
        period_id_list = []
        if period_ids:
            for period_id in period_ids:
                period_id_list.append(int(period_id))

        count, log_dbs = CostTriggerLogModel.list_trigger_log(start_time=start_time,
                                                              end_time=end_time, period_ids=period_id_list,
                                                              status=status, limit=limit, offset=offset,
                                                              partner_id=partner_id,
                                                              req_types=req_types)
        if log_dbs:
            log_list = []
            for log_db in log_dbs:
                log_obj = log_db.serialize(conv=True)
                log_list.append(log_obj)
            return count, log_list
        return 0, None

    def list_period(self, start_time=None, end_time=None, 
                        branch_id=None, period_ids=None, 
                        status=None, limit=None, offset=None, partner_id=None):
        period_id_list = []
        if period_ids:
            for period_id in period_ids:
                period_id_list.append(int(period_id))
        count, period_dbs = CostTriggerPeriodModel.list_periods(start_time=start_time, 
                                                        end_time=end_time, period_ids=period_id_list, 
                                                        status=status,  branch_id=branch_id,
                                                        limit=limit, offset=offset, 
                                                        partner_id=partner_id)
        if period_dbs:
            period_list = []
            for period_db in period_dbs:
                period_obj = period_db.serialize(conv=True)
                period_list.append(period_obj)
            return count, period_list
        return 0, None


    
        

cost_engine_trigger_service = CostEngineTriggerService()