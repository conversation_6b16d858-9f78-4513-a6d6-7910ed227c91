# -*- coding: utf-8 -*-
from datetime import datetime
from sqlalchemy import func
import logging
from datetime import datetime
from decimal import Decimal
from hex_exception import RecordAlreadyExist
from ..error.exception import NoResultFoundError, StatusUnavailable
import logging

from ..driver.mysql import db_commit, session_maker, DummyTransaction, session
from ..utils.helper import set_model_from_db, convert_to_int, convert_to_datetime, set_db
from ..utils.encode import encodeUTF8
from ..utils.exception import DataValidationException
from ..utils.resulting import ErrorCode
from ..utils.snowflake import gen_snowflake_id
from ..utils import pb2dict
from ..client.metadata_service import metadata_service 
from ..task.message_service_pub import MessageServicePub

from ..model.attachments import AttachmentsModel





class AttachmentsService():
    '''
        附件管理相关服务
    '''

    # 创建
    def create_attachments(self, request, partner_id, user_id):
        doc_id = request.doc_id
        doc_type = request.doc_type
        attachments = request.attachments
        signature = request.signature
        nosign_reason = request.nosign_reason
        res = {'id': doc_id, 'status': 'FAILED'}

        attachments_list = []
        if attachments:
            for attachment in attachments:
                args = {
                    'doc_id': doc_id,
                    'doc_type': doc_type,
                    'attachment': attachment,
                    'signature': signature,
                    'nosign_reason': nosign_reason,
                    'partner_id': partner_id,
                    'created_by': user_id,
                    'updated_by': user_id,
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                }
                attachments_list.append(args)
        else:
            attachments_list = [{
                'doc_id': doc_id,
                'doc_type': doc_type,
                # 'attachment': attachments,
                'signature': signature,
                'nosign_reason': nosign_reason,
                'partner_id': partner_id,
                'created_by': user_id,
                'updated_by': user_id,
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }]
        
        db_session = session_maker()
        try:
            db_session.bulk_insert_mappings(AttachmentsModel, attachments_list)
            db_session.commit()
            res['status'] = 'SUCCESS'
        except Exception as e:
            logging.error('Unexpected mysql error: %s', e)
            db_session.rollback()
            raise e
        finally:
            db_session.close()
        return res

    # 查询
    def get_attachments_by_doc_id(self, request, partner_id, user_id):
        doc_id = request.doc_id
        doc_type = request.doc_type
        
        count, attachments_db_list = AttachmentsModel.get_attachments_by_doc_id(doc_id=doc_id, doc_type=doc_type)
        attachments = []
        signature = None
        nosign_reason = None
        if count:
            for attachment_db in attachments_db_list:
                if attachment_db.attachment:
                    attachments.append(attachment_db.attachment)
                signature = attachment_db.signature
                nosign_reason = attachment_db.nosign_reason
        return {'doc_id':doc_id,'attachments':attachments, 'signature': signature, 'nosign_reason': nosign_reason}

    # 更新
    def update_attachments_by_doc_id(self, request, partner_id, user_id):
        
        doc_id = request.doc_id
        doc_type = request.doc_type

        res = {'id': doc_id, 'status': 'FAILED'}
        
        AttachmentsModel.delete_attachments_by_doc_id(doc_id=doc_id, doc_type=doc_type)
        self.create_attachments(request, partner_id, user_id)
        res['status'] = 'SUCCESS'
        return res


            
attachments_service = AttachmentsService()