# -*- coding: utf-8 -*-
from sqlalchemy import func
import logging
from datetime import datetime, timedelta, date,timezone
from decimal import Decimal
import json
from google.protobuf.timestamp_pb2 import Timestamp
from google.protobuf.message import Message as PbMessage
from hex_exception import RecordAlreadyExist

from supply import logger, time_cost
from .common import Refund
from ..api import handle_attachments, handle_request_attachments
from ..driver.mq import mq_producer
from ..driver.mysql import session_maker
from ..error.exception import NoResultFoundError, StatusUnavailable, DataValidationException, DealInventoryException, \
    ReturnInventoryException
from supply.module.third_party import ThirdPartyService
from ..model.franchisee.franchisee_refund import SupplyFranchiseeRefund
from ..utils.enums import ReturnTransType

from ..utils.helper import set_model_from_db, convert_to_int, convert_to_datetime, set_db, \
    get_product_map, MessageTopic, get_branch_list_map, get_guid
from ..utils.encode import encodeUTF8
from ..utils.resulting import ErrorCode
from ..utils.snowflake import gen_snowflake_id
from ..utils import pb2dict

from ..client.metadata_service import metadata_service
from ..client.inventory_service import inventory_service
from ..client.receipt_service import receipt_service
from ..task.message_service_pub import MessageServicePub

from ..model.returns import ReturnModel, ReturnProductModel, ReturnLogModel
from ..model.receiving_diff import ReceivingDiffModel, ReceivingDiffProductModel
from ..model.supply_doc_code import Supply_doc_code
from ..model.third_party import ThirdParty


class ReturnService():
    '''收货单相关服务
    service:
        - create_diff_return()：按照配送收货差异单的仓库承担生成退货单
        - get_return_by_id(): 通过receiving_id查询退货单信息
        - list_returns(): 枚举所有退货单
        - list_return_products_by_return_id(): return_id 枚举该退货单上所有商品明细
        - submit_return()：提交退货单
        - reject_return()：驳回退货单
        - confirm_return()：确认退货单
        - update_return_product_quantity()：更新退货单商品数量
        - delivery_return()：确认派送退货单商品
        - approve_return()：审核退货单
        - delete_return()：删除退货单
    '''

    # def __init__(self, alias=None):
    #     super(ReceivingService, self).__init__(alias=alias)

    def withdraw_inventory(self, return_id, description, partner_id, user_id, code=''):
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_product_db_list = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                             partner_id=partner_id)

        detail_list = []
        sequence = 0
        for product_db in return_product_db_list:
            detail = {}
            detail['sequence_id'] = sequence
            accounting = {}
            account = {}
            account['branch_id'] = product_db.return_by
            account['product_id'] = product_db.product_id
            accounting['account'] = account
            accounting['amount'] = product_db.quantity * Decimal(
                product_db.unit_rate) if product_db.unit_rate else product_db.quantity
            detail['accounting'] = accounting
            sequence += 1

            detail_list.append(detail)

        # print('************库存调用detail*************')
        # print(detail_list)
        # 增加库存
        code = 'ORDER_RETURN' if not code else code
        result = inventory_service.deal_with_inventory(batch_no=str(return_id), code=code, action='WITHDRAW',
                                                       description=description, detail=detail_list,
                                                       partner_id=partner_id,
                                                       user_id=user_id, trace_id=return_db.code)

        # print('***********库存返回***********')
        # print(result)

        time = 0
        if result.get('success') and result['success'] == True:
            return result
        else:
            time += 1
            # 超过三次没成功：
            if time == 3:
                raise DealInventoryException("failed to desposit inventory:{}".format(return_id))
            else:
                self.withdraw_inventory(return_id, description, partner_id, user_id)

    def check_return_available(self, store_id, product_detail_list, partner_id, user_id, sub_type=None,
                               logistics_type=None, action=None):
        # 检查业务配置-是否需要进行负库存校验
        need_check_map = metadata_service.get_neg_inv_config(partner_id=partner_id, user_id=user_id,
                                                             domain="boh.store.return",
                                                             store_id=store_id)
        if not need_check_map.get(action, False):
            logger.info(f"门店：{store_id}不需要进行负库存校验")
            return True
        logger.info(f"门店：{store_id}需要进行负库存校验")
        product_ids = []
        for product_detail in product_detail_list:
            product_ids.append(product_detail['product_id'])

        # 获取对应仓位
        if sub_type in ('store', 'fs_store'):
            relation_branch_type = 'stores'
            if logistics_type == 'PUR':
                position_relation_type = 'directReturn'  # 直送退货
            else:
                position_relation_type = 'deliveryReturn'  # 配送退货
        if sub_type == 'warehouse':
            relation_branch_type = 'warehouse'
            position_relation_type = 'purchaseReturn'
        if sub_type == 'machining':
            relation_branch_type = 'machining'
            position_relation_type = 'purchaseReturn'
        if sub_type:
            relation_filters = {relation_branch_type: [str(store_id)]}
            sub_accounts = metadata_service.get_position_relation_list(relation_filters=relation_filters,
                                                                       partner_id=partner_id, user_id=user_id).get(
                'rows', [])
            if len(sub_accounts) > 0:
                sub_accounts = sub_accounts[0]
            else:
                sub_accounts = {}
            position_id = sub_accounts.get('fields', {}).get(position_relation_type) if (
                        sub_accounts and position_relation_type) else None
        sub_account_ids = []
        if position_id:
            sub_account_ids = [int(position_id)]

        product_inventory_detail = inventory_service.get_products_inventory_by_branch_id(
            branch_id=store_id, product_ids=product_ids, sub_account_ids=sub_account_ids, partner_id=partner_id,
            user_id=user_id)
        over_product_id_list = []
        over_product_name_list = []
        for product_detail in product_detail_list:
            if not product_detail.get('unit_rate'):
                product_detail['unit_rate'] = 1  # 防止历史数据报错
            if sub_account_ids:
                key = str(sub_account_ids[0]) + str(product_detail['product_id'])
            else:
                key = str(product_detail['product_id'])
            if product_inventory_detail.get(key):
                inventory_qty = product_inventory_detail.get(key).get('quantity_avail', 0)
                if Decimal(inventory_qty).quantize(Decimal('0.********')) - Decimal(
                        product_detail['quantity']) * Decimal(product_detail['unit_rate']) < 0:
                    product_info = metadata_service.get_product(product_id=int(product_detail['product_id']),
                                                                partner_id=partner_id, user_id=user_id)
                    logging.info('{} return_qty_is_larger_than_inventory'.format(product_detail['product_id']))
                    #     raise DataValidationException('{}库存不足，不允许退货!'.format(product_info['name']))
                    # else:
                    #     continue
                    # 一次检查完毕后整体返回
                    over_product_id_list.append(str(product_detail['product_id']))
                    over_product_name_list.append(product_info['name'])
            else:
                product_info = metadata_service.get_product(product_id=int(product_detail['product_id']),
                                                            partner_id=partner_id, user_id=user_id)
                logging.info('{} return_qty_is_larger_than_inventory'.format(product_detail['product_id']))
                # raise DataValidationException('{}库存不足，不允许退货!'.format(product_info['name']))
                over_product_id_list.append(str(product_detail['product_id']))
                over_product_name_list.append(product_info['name'])
        if over_product_name_list:
            raise ReturnInventoryException('退货数量不能超出实时库存：{}'.format(over_product_name_list),
                                           detail=over_product_id_list)
        return True

    def get_return_by_id(self, return_id, partner_id, user_id):
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        if not return_db:
            return {}
        delivery_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center",
                                           branch_ids=[return_db.return_to],
                                           partner_id=partner_id, user_id=user_id)
        return_by_map = get_branch_list_map(branch_type="store", branch_ids=[return_db.return_by],
                                            partner_id=partner_id, user_id=user_id)
        receive_code_map = {}
        if return_db:
            receives = receipt_service.list_franchisee_receives(batch_ids=[return_db.id], partner_id=partner_id,
                                                                user_id=user_id)
            for receive in receives:
                receive_code_map[int(receive.get("batch_id"))] = receive.get("code", '')
        if return_db:
            return_obj = return_db.serialize(conv=True)
            return_obj['return_to_code'] = delivery_map.get(return_obj.get('return_to'), {}).get('code')
            return_obj['return_to_name'] = delivery_map.get(return_obj.get('return_to'), {}).get('name')
            return_obj['return_by_code'] = return_by_map.get(return_obj.get('return_by'), {}).get('code')
            return_obj['return_by_name'] = return_by_map.get(return_obj.get('return_by'), {}).get('name')
            return_obj['attachments'] = handle_attachments(return_db.attachments)
            return_obj['receive_code'] = receive_code_map.get(return_db.id, '')
            return return_obj
        return None

    def list_return_products_by_return_id(self, return_id, limit=None, offset=None, sort=None, order=None,
                                          partner_id=None, user_id=None):
        count = None
        count, return_products_db_list = ReturnProductModel.list_return_products_by_return_id(return_id, limit, offset,
                                                                                              sort, order, partner_id)
        product_ids = [return_product_db.product_id for return_product_db in return_products_db_list]
        product_dict = {}
        products = metadata_service.get_product_list(return_fields='id,name,code,category,model_name',
                                                     partner_id=partner_id, user_id=user_id,
                                                     ids=product_ids)
        products = products.get("rows", [])
        for product in products:
            product_dict[int(product.get('id', 0))] = product
        return_products_list = []
        if return_products_db_list is not None and len(return_products_db_list) > 0:
            for return_product_db in return_products_db_list:
                return_product_obj = return_product_db.serialize(conv=True)
                return_product_obj['unit_spec'] = product_dict.get(return_product_db.product_id, {}).get("model_name",
                                                                                                         '')
                return_product_obj["sum_price"] = return_product_obj.get("price_tax", 0) * return_product_obj.get(
                    "quantity", 0)
                return_product_obj['attachments'] = handle_attachments(return_product_db.attachments)
                return_products_list.append(return_product_obj)
            return count, return_products_list
        return 0, None

    def list_returns(self, partner_id, user_id, request):
        code = request.code
        limit = request.limit
        offset = request.offset
        return_by = request.return_by
        return_to = request.return_to
        status = request.status
        source_code = request.source_code
        source_id = request.source_id
        return_date_from = request.return_date_from
        return_date_to = request.return_date_to
        delivery_date_from = request.delivery_date_from
        delivery_date_to = request.delivery_date_to
        type = request.type
        sub_type = request.sub_type
        logistics_type = request.logistics_type
        sort = request.sort
        order = request.order

        product_ids = list(request.product_ids) if request.product_ids else []

        return_bys = []
        for storeId in return_by:
            return_bys.append(int(storeId))

        return_tos = []
        for rt in return_to:
            return_tos.append(int(rt))

        count, return_db_list = ReturnModel.list_returns(partner_id=partner_id, code=code, return_bys=return_bys,
                                                         status=status, logistics_type=logistics_type, type=type,
                                                         sub_type=sub_type, source_code=source_code,
                                                         source_id=source_id,
                                                         start_date=return_date_from, end_date=return_date_to,
                                                         delivery_start_date=delivery_date_from,
                                                         delivery_end_date=delivery_date_to,
                                                         sort=sort, order=order, limit=limit, offset=offset,
                                                         return_tos=return_tos, product_ids=product_ids)
        return_tos = [return_db.return_to for return_db in return_db_list]
        return_bys = [return_db.return_by for return_db in return_db_list]
        delivery_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center", branch_ids=return_tos,
                                           partner_id=partner_id, user_id=user_id)
        return_by_map = get_branch_list_map(branch_type="store", branch_ids=return_bys,
                                            partner_id=partner_id, user_id=user_id)
        # 请求receipt 获取发货单号和退货入库单号
        batch_ids = [return_db.id for return_db in return_db_list]
        receive_code_map = {}
        delivery_code_map = {}
        if batch_ids:
            deliverys = receipt_service.list_franchisee_deliverys(batch_ids=batch_ids, partner_id=partner_id,
                                                                  user_id=user_id)
            receives = receipt_service.list_franchisee_receives(batch_ids=batch_ids, partner_id=partner_id,
                                                                user_id=user_id)
            for delivery in deliverys:
                delivery_code_map[int(delivery.get("batch_id"))] = delivery.get("code")
            for receive in receives:
                receive_code_map[int(receive.get("batch_id"))] = receive.get("code")
        if return_db_list:
            return_list = []
            for return_db in return_db_list:
                return_obj = return_db.serialize(conv=True)
                return_obj['return_by_code'] = return_by_map.get(return_db.return_by, {}).get("code", '')
                return_obj['return_by_name'] = return_by_map.get(return_db.return_by, {}).get("name", '')
                return_obj['return_to_code'] = delivery_map.get(return_obj.get('return_to'), {}).get('code')
                return_obj['return_to_name'] = delivery_map.get(return_obj.get('return_to'), {}).get('name')
                return_obj['attachments'] = handle_attachments(return_db.attachments)
                return_obj['receive_code'] = receive_code_map.get(return_db.id, '')
                return_obj['delivery_code'] = delivery_code_map.get(return_db.id, '')
                return_list.append(return_obj)
            return count, return_list
        return 0, None

    def list_adjust_returns(self, partner_id, user_id, request):
        code = request.code
        limit = request.limit
        offset = request.offset
        return_by = request.return_by
        return_to = request.return_to
        status = request.status
        source_code = request.source_code
        source_id = request.source_id
        return_date_from = request.return_date_from
        return_date_to = request.return_date_to
        delivery_date_from = request.delivery_date_from
        delivery_date_to = request.delivery_date_to
        type = request.type
        sub_type = request.sub_type
        logistics_type = request.logistics_type
        sort = request.sort
        order = request.order

        return_bys = []
        for storeId in return_by:
            return_bys.append(int(storeId))

        return_tos = []
        for rt in return_to:
            return_tos.append(int(rt))

        count, return_db_list = ReturnModel.list_adjust_returns(partner_id=partner_id,
                                                                code=code, return_bys=return_bys,
                                                                status=status, logistics_type=logistics_type,
                                                                type=type, sub_type=sub_type, source_code=source_code,
                                                                source_id=source_id,
                                                                start_date=return_date_from, end_date=return_date_to,
                                                                delivery_start_date=delivery_date_from,
                                                                delivery_end_date=delivery_date_to,
                                                                sort=sort, order=order, limit=limit, offset=offset,
                                                                return_tos=return_tos)


        if return_db_list:
            return_list = []
            for return_db in return_db_list:
                return_obj = return_db.serialize(conv=True)
                return_obj['attachments'] = eval(return_obj['attachments']) if return_obj.get('attachments') else []
                return_list.append(return_obj)
            return count, return_list
        return 0, None

    # 退货单任务分配入口
    def create_return_entrance(self, return_by, return_delivery_date,
                               type, sub_type, logistics_type,
                               return_reason, products, request_id,
                               partner_id, user_id,
                               remark=None, attachments=None, source_id=None, source_code=None):
        """
        退货单任务分配入口
        根据sub_type判断退货单创建流程
        """
        res = {"payload": False}
        return_ids = []
        # 判断单据是否重复创建
        exist_return_db = ReturnModel.get_by_request_id(request_id, partner_id)
        if exist_return_db:
            raise DataValidationException('请勿重复提交创建请求！')

        returns_dict = {}
        in_batches_flag = True  # 分批发货，同存储标志，需要拆单
        for product_detail in products:
            return_to = product_detail.return_to
            product_detail_dict = pb2dict(product_detail)
            if not product_detail_dict.get('logistics_type'):
                in_batches_flag = False
            key_set = (return_to, product_detail_dict.get('logistics_type'))

            if returns_dict.get(key_set):
                returns_dict[key_set].append(product_detail)
            else:
                returns_dict[key_set] = [product_detail]

        # 仓库/加工中心退货
        if sub_type == 'warehouse' or sub_type == 'machining':
            logistics_type = 'PUR'
            for key, value in returns_dict.items():
                return_to = key[0]
                return_id = self.create_return_warehouse(return_by=return_by, return_to=return_to,
                                                         return_delivery_date=return_delivery_date,
                                                         type=type, sub_type=sub_type, return_reason=return_reason,
                                                         logistics_type=logistics_type, request_id=request_id,
                                                         product_detail=value, partner_id=partner_id, user_id=user_id,
                                                         remark=remark, attachments=attachments,
                                                         source_id=source_id, source_code=source_code)
                return_ids.append(return_id)

        # 门店退货
        else:
            # 判断门店状态是否允许退货
            status_check = metadata_service.check_store_status(return_by, partner_id, user_id)
            if not status_check:
                raise DataValidationException('请检查门店的开店状态！')

            if in_batches_flag:  # 分批发货，需要拆单
                return_ids = self.create_return_store_in_batches(return_by=return_by,
                                                                 return_delivery_date=return_delivery_date,
                                                                 type=type, sub_type=sub_type,
                                                                 return_reason=return_reason,
                                                                 request_id=request_id,
                                                                 partner_id=partner_id, user_id=user_id,
                                                                 remark=remark, attachments=attachments,
                                                                 source_id=source_id, source_code=source_code,
                                                                 returns_product_dict=returns_dict)
            else:
                for key, value in returns_dict.items():
                    return_to = key[0]
                    return_id = self.create_return_store(return_by=return_by, return_to=return_to,
                                                         return_delivery_date=return_delivery_date,
                                                         type=type, sub_type=sub_type, return_reason=return_reason,
                                                         logistics_type=logistics_type, request_id=request_id,
                                                         product_detail=value, partner_id=partner_id, user_id=user_id,
                                                         remark=remark, attachments=attachments,
                                                         source_id=source_id, source_code=source_code)
                    return_ids.append(return_id)
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=return_by,
                                             doc_type="return"))
        res["payload"] = True
        res["return_id"] = return_ids
        return res

    # 门店退货单——拆单
    def create_return_store_in_batches(self, return_by, return_delivery_date,
                                       type, sub_type, return_reason,
                                       request_id,
                                       returns_product_dict, partner_id, user_id,
                                       remark=None, attachments=None,
                                       source_id=None, source_code=None):
        """
        创建门店退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        return_date = datetime.utcnow()
        return_delivery_date = datetime.fromtimestamp(return_delivery_date.seconds)

        product_code_list = []
        return_p_map = {}
        for key, value in returns_product_dict.items():
            for product_d in value:
                product_code_list.append(product_d.product_code)
                return_p_map[int(product_d.product_id)] = (product_d.confirmed_quantity, product_d.quantity)
        product_code_list = list(set(product_code_list))
        # 原单退货, 校验可退货数量
        diff_map = {}
        if type == 'BO':
            # 查询是否有收货差异单
            diff_dbs = ReceivingDiffModel.list_receiving_diffs_by_receiving_id(source_id, partner_id)
            diff_ids = [d.id for d in diff_dbs if d.status == 'CONFIRMED']
            if diff_ids:
                diff_products = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_ids(diff_ids, partner_id)
                for dp in diff_products:
                    diff_map[dp.product_id] = float(dp.d_diff_quantity)
            for key, value in return_p_map.items():
                if round(value[0] - diff_map.get(key, 0), 6) < value[1]:
                    raise DataValidationException("退货数量不能大于可退货数量")
        # 转换单位
        product_unit_dict = {}
        products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                          include_units=True,
                                                          return_fields='id,code,name,model_name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        product_spec_dict = {}
        for product_detail_info in products_info:
            product_spec_dict[int(product_detail_info['id'])] = product_detail_info.get('model_name')
            if product_detail_info.get('units'):
                product_unit_dict[product_detail_info['id']] = {}
                for unit in product_detail_info.get('units'):
                    if unit.get('purchase') and unit['purchase'] == True:
                        product_unit_dict[product_detail_info['id']]['purchase'] = unit['rate'] if unit.get(
                            'rate') else 0
                    if unit.get('order') and unit['order'] == True:
                        product_unit_dict[product_detail_info['id']]['order'] = unit['rate'] if unit.get('rate') else 0
                    if unit.get('default') and unit['default'] == True:
                        product_unit_dict[product_detail_info['id']]['default'] = unit['rate'] if unit.get(
                            'rate') else 0
            else:
                product_unit_dict[product_detail_info['id']]['default'] = 0

        product_insert_list = []
        return_insert_list = []
        return_ids = []
        for key, value in returns_product_dict.items():
            return_code = Supply_doc_code.get_code_by_type('RETURN_OD', partner_id, None)
            return_id = gen_snowflake_id()
            return_ids.append(return_id)
            return_to = key[0]
            logistics_type = key[1]
            product_id_list = []
            for product_d in value:
                product_id_list.append(int(product_d.product_id))
            product_id_list = list(set(product_id_list))

            # 直送单需要拿到价格，获取商品成本
            if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                if type == "BO":  # 原单退货价格从收货单里取
                    product_tax_dict = get_receive_prod_price(source_code, partner_id, user_id)
                    stub = 'metadata'
                else:
                    stub = get_stub(partner_id=partner_id, is_original_receipt=True)
                    product_tax_dict = get_tax(
                        stub=stub, vendor_id=return_to, product_id_list=product_id_list,
                        partner_id=partner_id, user_id=user_id, store_id=return_by
                    )

                # 订货单位*订货rate=采购单位*采购rate
                # 采购单位/订货单位=订货rate/采购rate

                # 订货单价*订货单位=采购单价*采购单位
                # 订货单价 = 采购单价*采购单位/订货单位
                # 订货单价 = 采购单价*订货rate/采购rate
                purchase2order_rate_dict = get_tax_price(stub, product_unit_dict)
            product_nums = 0
            # 创建退货单商品
            delivery_p_list = []
            rec_p_list = []
            for product in value:
                product_nums += 1
                product = pb2dict(product)
                product['attachments'] = handle_request_attachments(product.get("attachments"))
                product['unit_spec'] = product_spec_dict.get(int(product.get('product_id', 0)))
                product['id'] = gen_snowflake_id()
                product['return_id'] = return_id
                product['return_by'] = return_by
                product['return_date'] = return_date
                product['status'] = 'INITED'
                product['partner_id'] = partner_id
                product['created_by'] = user_id
                product['updated_by'] = user_id
                product['updated_name'] = operator_name
                product['created_name'] = operator_name
                product['created_at'] = datetime.utcnow()
                product['updated_at'] = datetime.utcnow()
                price = 0
                price_tax = 0
                tax_rate = 0
                if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                    product_id = product.get('product_id')
                    product_tax_detail = product_tax_dict.get(product_id)
                    if not product_tax_detail:
                        raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))

                    tax_rate = product_tax_detail.get('rate', 0)
                    if type == 'BO':  # 原单价格从收货单里取，不必做转换
                        price = product_tax_detail.get('no_tax')
                        price_tax = product_tax_detail.get('tax')
                    else:
                        price = product_tax_detail.get('no_tax', 0) * purchase2order_rate_dict.get(product_id, 1)
                        price_tax = product_tax_detail.get('tax', 0) * purchase2order_rate_dict.get(product_id, 1)

                    product['tax_rate'] = tax_rate
                    product['price'] = price
                    product['price_tax'] = price_tax

                self.check_product_attachments(product)
                product_insert_list.append(product)

                # ---ForReceiptStart---#
                # ForReceiptDelivery
                delivery_p = {
                    'delivery_by':       return_by,
                    'product_id':        product.get('product_id'),
                    'product_code':      product.get('product_code'),
                    'product_name':      product.get('product_name'),
                    # 'storage_type': product.storage_type,
                    'order_quantity':    product.get('quantity'),
                    'delivery_quantity': product.get('quantity'),
                    'unit_id':           product.get('unit_id'),
                    'unit_name':         product.get('unit_name'),
                    'unit_rate':         product.get('unit_rate')
                }
                if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                    delivery_p['cost_price'] = price
                    delivery_p['tax_price'] = price_tax
                    delivery_p['tax_rate'] = tax_rate
                delivery_p_list.append(delivery_p)

                rec_p = {
                    'receive_by':        int(return_to),
                    'product_id':        product.get('product_id'),
                    'product_code':      product.get('product_code'),
                    'product_name':      product.get('product_name'),
                    # 'storage_type': product.storage_type,
                    'order_quantity':    product.get('quantity'),
                    'delivery_quantity': product.get('quantity'),
                    'unit_id':           product.get('unit_id'),
                    'unit_name':         product.get('unit_name'),
                    'unit_rate':         product.get('unit_rate')
                }
                if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                    rec_p['cost_price'] = price
                    rec_p['tax_price'] = price_tax
                    rec_p['tax_rate'] = tax_rate
                rec_p_list.append(rec_p)

            args = {
                'id':                   return_id,
                'code':                 return_code,
                'return_by':            return_by,
                'return_delivery_date': return_delivery_date,
                'type':                 type,
                'sub_type':             sub_type,
                'logistics_type':       logistics_type,
                'return_reason':        return_reason,
                'status':               'INITED',
                'partner_id':           partner_id,
                'created_at':           datetime.utcnow(),
                'created_by':           user_id,
                'updated_at':           datetime.utcnow(),
                'updated_name':         operator_name,
                'created_name':         operator_name,
                'updated_by':           user_id,
                'return_to':            return_to,
                'source_id':            source_id,
                'source_code':          source_code,
                'return_date':          return_date,
                'product_nums':         product_nums,
                'request_id':           request_id
            }
            if remark:
                args.update(dict(remark=remark))
            if attachments:
                args.update(dict(attachments=handle_request_attachments(attachments)))
            if type in ('CAD'):
                args.update(dict(status='APPROVED'))
            return_insert_list.append(args)

            # ---ForReceiptStart---#
            batch_type = 'RETURN'
            if type == 'IAD':
                batch_type = 'RETURN_ADJUST'
            elif type == 'CAD':
                batch_type = 'CHECKING_ADJUST'

            # 退货单创建到发货单
            today = datetime.today()
            delivery_res = receipt_service.create_deliverys(
                batch_id=return_id, batch_code=return_code, batch_type=batch_type,
                demand_type=type,
                # id=None, code=None,
                order_id=return_id, order_code=return_code,
                demand_id=source_id if type == 'BO' else return_id,
                demand_code=source_code if type == 'BO' else return_code,
                # TODO 原单退货的门店出库单，原单收货单单号暂时塞到demand_id里，处理不优美，后期需要改
                # receive_id=None, receive_code=None,
                receive_by=int(return_to), delivery_by=return_by,
                distr_type=logistics_type,
                delivery_date=return_delivery_date, demand_date=return_date,
                # arrival_date=None,  expect_date=None,
                # storage_type=None,
                products=delivery_p_list, main_branch_type='S',
                partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)
            logging.info("create_deliverys_res:{}".format(delivery_res))
            if not delivery_res.get('id'):
                raise DataValidationException("保存失败，请重新尝试")
            main_branch_type = None
            if logistics_type == 'PUR':
                main_branch_type = 'V'
            elif logistics_type == 'PAD':
                main_branch_type = 'M'
            else:
                main_branch_type = 'W'

            # 退货单创建到仓库收货单
            # 原单退货 —— batch_code是收货单原单code
            if source_code:
                receipt_service.create_receives(
                    demand_type=type,
                    batch_id=return_id, batch_code=str(source_code), batch_type=batch_type,
                    # TODO 原单退货的仓库入库单，原单收货单单号暂时塞到batch_code里，处理不优美，后期需要改
                    order_id=return_id, order_code=return_code,
                    delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
                    distr_type=logistics_type, main_branch_type=main_branch_type,
                    delivery_date=return_delivery_date, demand_date=return_date,
                    products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)
            # 非原单退货
            else:
                receipt_service.create_receives(
                    demand_type=type,
                    batch_id=return_id, batch_code=return_code, batch_type=batch_type,
                    order_id=return_id, order_code=return_code,
                    delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
                    distr_type=logistics_type, main_branch_type=main_branch_type,
                    delivery_date=return_delivery_date, demand_date=return_date,
                    products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)

            # ---ForReceiptEnd---#

        # 创建退货单
        ReturnProductModel.create_returns_in_all(return_detail=return_insert_list,
                                                 return_product_list=product_insert_list)

        if type in ('CAD'):
            for r_id in return_ids:
                # 调整单的退货直接更新成终态——已提货，仓库那边也需要判断并直接入库
                self.delivery_return(r_id, partner_id, user_id)
        log_list = []
        for r_id in return_ids:
            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = r_id
            log_detail['operation'] = 'CREATE'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.utcnow()
            log_detail['created_name'] = operator_name
            log_list.append(log_detail)
        ReturnLogModel.create_logs_list(log_list)

        return return_ids

    # 门店退货单
    def create_return_store(self, return_by, return_to, return_delivery_date,
                            type, sub_type, return_reason,
                            logistics_type, request_id,
                            product_detail, partner_id, user_id,
                            remark=None, attachments=None,
                            source_id=None, source_code=None):
        """
        创建门店退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        return_id = gen_snowflake_id()
        return_code = Supply_doc_code.get_code_by_type('RETURN_OD', partner_id, None)
        return_date = datetime.utcnow()
        return_delivery_date = datetime.fromtimestamp(return_delivery_date.seconds)

        # 创建退货单商品
        product_nums = 0
        delivery_p_list = []
        rec_p_list = []

        product_id_list = []
        product_code_list = []
        return_p_map = {}
        for product_d in product_detail:
            product_code_list.append(product_d.product_code)
            product_id_list.append(str(product_d.product_id))
            return_p_map[int(product_d.product_id)] = (product_d.confirmed_quantity, product_d.quantity)
        product_code_list = list(set(product_code_list))
        # 原单退货, 校验可退货数量
        diff_map = {}
        if type == 'BO':
            # 查询是否有收货差异单
            diff_dbs = ReceivingDiffModel.list_receiving_diffs_by_receiving_id(source_id, partner_id)
            diff_ids = [d.id for d in diff_dbs if d.status == 'CONFIRMED']
            if diff_ids:
                diff_products = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_ids(diff_ids, partner_id)
                for dp in diff_products:
                    diff_map[dp.product_id] = float(dp.d_diff_quantity)
            for key, value in return_p_map.items():
                if round(value[0] - diff_map.get(key, 0), 6) < value[1]:
                    raise DataValidationException("退货数量不能大于可退货数量")

        # 转换单位
        product_unit_dict = {}
        products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                          include_units=True,
                                                          return_fields='id,code,name,model_name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        product_spec_dict = {}
        for product_detail_info in products_info:
            product_spec_dict[int(product_detail_info['id'])] = product_detail_info.get('model_name')
        for product_detail_info in products_info:
            if product_detail_info.get('units'):
                product_unit_dict[product_detail_info['id']] = {}
                for unit in product_detail_info.get('units'):
                    if unit.get('purchase') and unit['purchase'] == True:
                        product_unit_dict[product_detail_info['id']]['purchase'] = unit['rate'] if unit.get(
                            'rate') else 0
                    if unit.get('order') and unit['order'] == True:
                        product_unit_dict[product_detail_info['id']]['order'] = unit['rate'] if unit.get('rate') else 0
                    if unit.get('default') and unit['default'] == True:
                        product_unit_dict[product_detail_info['id']]['default'] = unit['rate'] if unit.get(
                            'rate') else 0
            else:
                product_unit_dict[product_detail_info['id']]['default'] = 0
        # 直送单需要拿到价格，获取商品成本
        product_tax_dict = {}
        purchase2order_rate_dict = {}
        if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
            if type == "BO":  # 原单退货价格从收货单里取
                product_tax_dict = get_receive_prod_price(source_code, partner_id, user_id)
                stub = 'metadata'
            else:
                stub = get_stub(partner_id=partner_id, is_original_receipt=True)
                product_tax_dict = get_tax(
                    stub=stub, vendor_id=return_to, product_id_list=product_id_list,
                    partner_id=partner_id, user_id=user_id, store_id=return_by
                )
            # 订货单位*订货rate=采购单位*采购rate
            # 采购单位/订货单位=订货rate/采购rate

            # 订货单价*订货单位=采购单价*采购单位
            # 订货单价 = 采购单价*采购单位/订货单位
            # 订货单价 = 采购单价*订货rate/采购rate
            for key, key_value in product_unit_dict.items():
                purchase_rate = key_value.get('purchase') if key_value.get('purchase') else 1
                order_rate = key_value.get('order') if key_value.get('order') else 1
                purchase2order_rate_dict[int(key)] = order_rate / purchase_rate

        product_insert_list = []
        for product in product_detail:
            product_nums += 1
            product = pb2dict(product)
            product['unit_spec'] = product_spec_dict.get(int(product.get('product_id', 0)))
            product['id'] = gen_snowflake_id()
            product['return_id'] = return_id
            product['return_by'] = return_by
            product['return_date'] = return_date
            product['status'] = 'INITED'
            product['partner_id'] = partner_id
            product['created_by'] = user_id
            product['updated_by'] = user_id
            product['updated_name'] = operator_name
            product['created_name'] = operator_name
            product['created_at'] = datetime.utcnow()
            product['updated_at'] = datetime.utcnow()
            if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                product_id = product.get('product_id')
                prod_dict = product_tax_dict.get(product_id)
                if not prod_dict:
                    raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))

                tax_rate = prod_dict.get('rate', 0)
                if type == 'BO':  # 原单价格从收货单里取，不必做转换
                    price = prod_dict.get('no_tax')
                    price_tax = prod_dict.get('tax')
                else:
                    price = prod_dict.get('no_tax', 0) * purchase2order_rate_dict.get(product_id, 1)
                    price_tax = prod_dict.get('tax', 0) * purchase2order_rate_dict.get(product_id, 1)

                product['tax_rate'] = tax_rate
                product['price'] = price
                product['price_tax'] = price_tax
            self.check_product_attachments(product)
            product_insert_list.append(product)

            # ---ForReceiptStart---#
            # ForReceiptDelivery
            delivery_p = {
                'delivery_by':       return_by,
                'product_id':        product.get('product_id'),
                'product_code':      product.get('product_code'),
                'product_name':      product.get('product_name'),
                # 'storage_type': product.storage_type,
                'order_quantity':    product.get('quantity'),
                'delivery_quantity': product.get('quantity'),
                'unit_id':           product.get('unit_id'),
                'unit_name':         product.get('unit_name'),
                'unit_rate':         product.get('unit_rate'),
                'unit_spec':         product.get('unit_spec')
            }
            if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                delivery_p['cost_price'] = price
                delivery_p['tax_price'] = price_tax
                delivery_p['tax_rate'] = tax_rate

            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by':        int(return_to),
                'product_id':        product.get('product_id'),
                'product_code':      product.get('product_code'),
                'product_name':      product.get('product_name'),
                # 'storage_type': product.storage_type,
                'order_quantity':    product.get('quantity'),
                'delivery_quantity': product.get('quantity'),
                'unit_id':           product.get('unit_id'),
                'unit_name':         product.get('unit_name'),
                'unit_rate':         product.get('unit_rate'),
                'unit_spec':         product.get('unit_spec')
            }
            if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                rec_p['cost_price'] = price
                rec_p['tax_price'] = price_tax
                rec_p['tax_rate'] = tax_rate

            rec_p_list.append(rec_p)

        ReturnProductModel.create_returns_products(product_insert_list)

        args = {
            'id':                   return_id,
            'code':                 return_code,
            'return_by':            return_by,
            'return_delivery_date': return_delivery_date,
            'type':                 type,
            'sub_type':             sub_type,
            'logistics_type':       logistics_type,
            'return_reason':        return_reason,
            'status':               'INITED',
            'partner_id':           partner_id,
            'created_at':           datetime.utcnow(),
            'created_by':           user_id,
            'updated_at':           datetime.utcnow(),
            'updated_name':         operator_name,
            'created_name':         operator_name,
            'updated_by':           user_id,
            'return_to':            return_to,
            'source_id':            source_id,
            'source_code':          source_code,
            'return_date':          return_date,
            'product_nums':         product_nums,
            'request_id':           request_id
        }
        if remark:
            args.update(dict(remark=remark))
        if attachments:
            args.update(dict(attachments=handle_request_attachments(attachments)))
        if type in ('CAD'):
            args.update(dict(status='APPROVED'))

        # ---ForReceiptStart---#
        batch_type = 'RETURN'
        if type == 'IAD':
            batch_type = 'RETURN_ADJUST'
        elif type == 'CAD':
            batch_type = 'CHECKING_ADJUST'

        # 退货单创建到发货单
        today = datetime.today()
        delivery_res = receipt_service.create_deliverys(
            batch_id=return_id, batch_code=return_code, batch_type=batch_type,
            demand_type=type,
            # id=None, code=None,
            order_id=return_id, order_code=return_code,
            demand_id=source_id if type == 'BO' else return_id,
            demand_code=source_code if type == 'BO' else return_code,
            # TODO 原单退货的门店出库单，原单收货单单号暂时塞到demand_id里，处理不优美，后期需要改
            # receive_id=None, receive_code=None,
            receive_by=int(return_to), delivery_by=return_by,
            distr_type=logistics_type,
            delivery_date=return_delivery_date, demand_date=return_date,
            # arrival_date=None,  expect_date=None,
            # storage_type=None,
            products=delivery_p_list, main_branch_type='S',
            partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)

        main_branch_type = None
        if logistics_type == 'PUR':
            main_branch_type = 'V'
        elif logistics_type == 'PAD':
            main_branch_type = 'M'
        else:
            main_branch_type = 'W'

        # 退货单创建到仓库收货单
        # 原单退货 —— batch_code是收货单原单code
        if source_code:
            receipt_service.create_receives(
                demand_type=type,
                batch_id=return_id, batch_code=str(source_code), batch_type=batch_type,
                # TODO 原单退货的仓库入库单，原单收货单单号暂时塞到batch_code里，处理不优美，后期需要改
                order_id=return_id, order_code=return_code,
                delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
                distr_type=logistics_type, main_branch_type=main_branch_type,
                delivery_date=return_delivery_date, demand_date=return_date,
                products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)
        # 非原单退货
        else:
            receipt_service.create_receives(
                demand_type=type,
                batch_id=return_id, batch_code=return_code, batch_type=batch_type,
                order_id=return_id, order_code=return_code,
                delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
                distr_type=logistics_type, main_branch_type=main_branch_type,
                delivery_date=return_delivery_date, demand_date=return_date,
                products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)

        # ---ForReceiptEnd---#

        # 创建退货单
        ReturnModel.create_returns(**args)
        if type in ('CAD'):
            # 调整单的退货直接更新成终态——已提货，仓库那边也需要判断并直接入库
            self.delivery_return(return_id, partner_id, user_id)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return return_id

    # 仓库退货单
    def create_return_warehouse(self, return_by, return_to, return_delivery_date,
                                type, sub_type, return_reason,
                                logistics_type, request_id,
                                product_detail, partner_id, user_id,
                                remark=None, attachments=None,
                                source_id=None, source_code=None, franchisee_id=None):
        """
        创建仓库/加工中心退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        return_id = gen_snowflake_id()
        return_code = Supply_doc_code.get_code_by_type('RETURN_OD', partner_id, None)
        return_date = datetime.utcnow()
        return_delivery_date = datetime.fromtimestamp(return_delivery_date.seconds)

        # 创建退货单商品
        product_nums = 0
        delivery_p_list = []
        rec_p_list = []

        # 直送单需要拿到价格，获取商品成本
        product_id_list = []
        product_id_list_int = []
        for product in product_detail:
            product_id_list.append(str(product.product_id))
            product_id_list_int.append(int(product.product_id))
        if type == "BO":  # 原单退货价格从收货单里取
            product_tax_dict = get_receive_prod_price(source_code, partner_id, user_id)
            stub = 'metadata'
        else:
            if sub_type == 'warehouse':
                product_tax_dict = metadata_service.get_tax_list(
                    vendor_id=return_to, product_id_list=product_id_list,
                    warehouse_id=return_by, valid_time=datetime.utcnow(),
                    partner_id=partner_id, user_id=user_id)
            if sub_type == 'machining':
                product_tax_dict = metadata_service.get_tax_list(
                    vendor_id=return_to, product_id_list=product_id_list,
                    machining_id=return_by, valid_time=datetime.utcnow(),
                    partner_id=partner_id, user_id=user_id)
        product_unit_dict = metadata_service.get_product_unit(ids=product_id_list_int, partner_id=partner_id,
                                                              user_id=user_id)

        products_info = metadata_service.get_product_list(ids=product_id_list_int,
                                                          return_fields='id,model_name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        product_spec_dict = {}
        for product_detail_info in products_info:
            product_spec_dict[int(product_detail_info['id'])] = product_detail_info.get('model_name')

        product_insert_list = []
        for product in product_detail:
            product_nums += 1
            product = pb2dict(product)
            product['unit_spec'] = product_spec_dict.get(int(product.get('product_id', 0)))
            product['id'] = gen_snowflake_id()
            product['return_id'] = return_id
            product['return_by'] = return_by
            product['return_date'] = return_date
            product['status'] = 'INITED'
            product['partner_id'] = partner_id
            product['created_by'] = user_id
            product['updated_by'] = user_id
            product['updated_name'] = operator_name
            product['created_name'] = operator_name
            product['created_at'] = datetime.utcnow()
            product['updated_at'] = datetime.utcnow()
            product_id = product.get('product_id')
            product['unit_rate'] = product_unit_dict.get(product_id).get(
                int(product.get('unit_id'))) if product.get('unit_id') else 1

            product_tax_detail = product_tax_dict.get(product_id)
            if not product_tax_detail:
                raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))

            if not product.get('tax_rate'):
                product['tax_rate'] = product_tax_detail.get('rate', 0)
            if not product.get('price'):
                product['price'] = product_tax_detail.get('no_tax', 0)
            if not product.get('price_tax'):
                product['price_tax'] = product_tax_detail.get('tax', 0)
            self.check_product_attachments(product)
            product_insert_list.append(product)

            # ---ForReceiptStart---#
            # ForReceiptDelivery
            delivery_p = {
                'delivery_by':       return_by,
                'product_id':        product.get('product_id'),
                'product_code':      product.get('product_code'),
                'product_name':      product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.get('quantity'),
                'unit_id':           product.get('unit_id'),
                'unit_name':         product.get('unit_name'),
                'unit_rate':         product.get('unit_rate'),
                'unit_spec':         product.get('unit_spec'),
                'cost_price':        product_tax_dict.get(product.get('product_id')).get(
                    'no_tax') if product_tax_dict.get(product.get('product_id')) else 0,
                'tax_price':         product_tax_dict.get(product.get('product_id')).get('tax') if product_tax_dict.get(
                    product.get('product_id')) else 0,
                'tax_rate':          product_tax_dict.get(product.get('product_id')).get(
                    'rate') if product_tax_dict.get(product.get('product_id')) else 0

                # 'unit_spec':product.unit_spec,
                # 'category_id':product.category_id
            }
            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by':        int(return_to),
                'product_id':        product.get('product_id'),
                'product_code':      product.get('product_code'),
                'product_name':      product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.get('quantity'),
                'unit_id':           product.get('unit_id'),
                'unit_name':         product.get('unit_name'),
                'unit_rate':         product.get('unit_rate'),
                'unit_spec':         product.get('unit_spec'),
                'cost_price':        product_tax_dict.get(product.get('product_id')).get(
                    'no_tax') if product_tax_dict.get(product.get('product_id')) else 0,
                'tax_price':         product_tax_dict.get(product.get('product_id')).get('tax') if product_tax_dict.get(
                    product.get('product_id')) else 0,
                'tax_rate':          product_tax_dict.get(product.get('product_id')).get(
                    'rate') if product_tax_dict.get(product.get('product_id')) else 0
                # 'unit_spec':product.unit_spec,
                # 'category_id':product.category_id
            }
            rec_p_list.append(rec_p)

        ReturnProductModel.create_returns_products(product_insert_list)

        args = {
            'id':                   return_id,
            'code':                 return_code,
            'return_by':            return_by,
            'return_delivery_date': return_delivery_date,
            'type':                 type,
            'sub_type':             sub_type,
            'return_reason':        return_reason,
            'status':               'INITED',
            'partner_id':           partner_id,
            'created_at':           datetime.utcnow(),
            'created_by':           user_id,
            'updated_at':           datetime.utcnow(),
            'updated_name':         operator_name,
            'created_name':         operator_name,
            'updated_by':           user_id,
            'return_to':            return_to,
            'source_id':            source_id,
            'source_code':          source_code,
            'logistics_type':       logistics_type,
            'product_nums':         product_nums,
            'request_id':           request_id,
            'franchisee_id':        franchisee_id
        }
        if remark:
            args.update(dict(remark=remark))
        if attachments:
            args.update(dict(attachments=attachments))

        batch_type = 'PUR_RETURN'
        if type == 'IAD':
            batch_type = 'PUR_RETURN_ADJUST'
        elif type == 'CAD':
            batch_type = 'CHECKING_ADJUST'
            # 调整单的退货直接更新成终态
            args.update(dict(status='SUBMITTED'))
        if sub_type == 'fs_store':
            batch_type = 'FRS_RETURN'
        # ---ForReceiptStart---#
        main_branch_type = 'W'
        if sub_type == 'machining':
            main_branch_type = 'M'
        # 退货单创建到发货单
        today = datetime.today()
        delivery_res = receipt_service.create_deliverys(
            demand_type=type,
            batch_id=return_id, batch_code=return_code, batch_type=batch_type,
            # id=None, code=None,
            order_id=return_id, order_code=return_code,
            demand_id=return_id, demand_code=return_code,
            # receive_id=None, receive_code=None,
            receive_by=int(return_to), delivery_by=return_by,
            distr_type=logistics_type,
            delivery_date=return_delivery_date,
            demand_date=return_date,
            arrival_date=return_delivery_date,
            # expect_date=None,
            # storage_type=None,
            products=delivery_p_list, main_branch_type=main_branch_type,
            remark=remark, reason=return_reason,
            partner_id=partner_id, user_id=user_id,
            franchisee_id=franchisee_id)

        # 退货单创建到供应商收货单
        receive_res = receipt_service.create_receives(
            demand_type=type,
            batch_id=return_id, batch_code=return_code, batch_type=batch_type,
            order_id=return_id, order_code=return_code,
            delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by,
            distr_type=logistics_type, main_branch_type='V',
            delivery_date=return_delivery_date, demand_date=return_date,
            products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason,
            franchisee_id=franchisee_id)
        # ---ForReceiptEnd---#

        # 创建退货单
        ReturnModel.create_returns(**args)
        # 调整单的退货直接更新成终态
        if type in ('CAD'):
            self.approve_return(return_id, partner_id, user_id)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return return_id

    # 创建配送差异仓库承担产生的退单
    def create_diff_return(self, return_by, return_to,
                           return_delivery_date, return_reason,
                           product_detail, partner_id, user_id,
                           source_id, source_code, logistics_type,
                           remark=None, franchisee_id=None,
                           diff_type=None, receiving_diff_id=None):
        '''
        配送差异仓库承担部分自动退单
        '''
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        return_date = datetime.utcnow()
        diff_ret_id = gen_snowflake_id()
        diff_ret_code = Supply_doc_code.get_code_by_type('RETURN_OD', partner_id, None)

        # 创建退货单商品
        product_nums = 0
        product_code_list = []
        for product in product_detail:
            product_code_list.append(product['product_code'])

        # 转换单位
        product_unit_dict = {}
        products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                          include_units=True,
                                                          return_fields='id,code,name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        for product_detail_info in products_info:
            if product_detail_info.get('units'):
                for i in product_detail_info['units']:
                    if i.get('order') and i['order'] == True:
                        product_unit_dict[product_detail_info['id']] = i
                    else:
                        logger.warning("没有设置订货单位, product_id:{}".format(product_detail_info['id']))
            else:
                logger.warning("没有订货单位, product_id:{}".format(product_detail_info['id']))

        delivery_p_list = []
        rec_p_list = []
        product_insert_list = []
        for product in product_detail:
            product_nums += 1
            product['id'] = gen_snowflake_id()
            product['return_id'] = diff_ret_id
            product['return_by'] = return_by
            product['return_date'] = return_date
            product['status'] = 'CONFIRMED'
            product['partner_id'] = partner_id
            product['created_by'] = user_id
            product['updated_by'] = user_id
            product['updated_name'] = operator_name
            product['created_name'] = operator_name
            product['created_at'] = datetime.utcnow()
            product['updated_at'] = datetime.utcnow()

            unit = product_unit_dict[str(product['product_id'])]
            if unit:
                product['unit_id'] = unit['id']
                # product['unit_name'] = unit['name']
                # product['unit_spec'] = unit['code']
                product['unit_rate'] = unit['rate']
            product_insert_list.append(product)

            delivery_p = {
                'delivery_by':       int(return_by),
                'product_id':        int(product.get('product_id')),
                'product_code':      product.get('product_code'),
                'product_name':      product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': float(product.get('quantity')),
                'unit_id':           int(product.get('unit_id')),
                'unit_name':         product.get('unit_name'),
                'unit_rate':         float(product.get('unit_rate')),
                'cost_price':        product.get('price'),
                'tax_price':         product.get('price_tax'),
                'tax_rate':          product.get('tax_rate'),
                'unit_spec':         product.get('unit_spec'),
                # 'category_id':product.category_id
            }
            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by':        int(return_to),
                'product_id':        int(product.get('product_id')),
                'product_code':      product.get('product_code'),
                'product_name':      product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': float(product.get('quantity')),
                'unit_id':           int(product.get('unit_id')),
                'unit_name':         product.get('unit_name'),
                'unit_rate':         float(product.get('unit_rate')),
                'cost_price':        product.get('price'),
                'tax_price':         product.get('price_tax'),
                'tax_rate':          product.get('tax_rate'),
                'unit_spec':         product.get('unit_spec'),
                # 'category_id':product.category_id
            }
            rec_p_list.append(rec_p)
        # ---ForReceiptStart---#

        ReturnProductModel.create_returns_products(product_insert_list)

        batch_type = 'DIFF_RETURN'
        # 退货单创建到发货单
        today = datetime.today()
        receipt_service.create_deliverys(
            demand_type='BD',
            batch_id=source_id, batch_code=source_code, batch_type=batch_type,
            # id=None, code=None,
            order_id=diff_ret_id, order_code=diff_ret_code,
            demand_id=diff_ret_id, demand_code=diff_ret_code,
            # receive_id=None, receive_code=None,
            receive_by=int(return_to), delivery_by=return_by,
            distr_type=logistics_type,
            delivery_date=return_delivery_date, demand_date=return_date,
            # arrival_date=None,  expect_date=None,
            # storage_type=None,
            products=delivery_p_list, main_branch_type='FS' if franchisee_id else 'S',
            partner_id=partner_id, user_id=user_id)

        # ---ForReceiptEnd---#

        args = {
            'id':                   diff_ret_id,
            'code':                 diff_ret_code,
            'return_by':            return_by,
            'return_delivery_date': return_delivery_date,
            'return_reason':        return_reason,
            'status':               'CONFIRMED',  # 直接设置为最终状态
            'type':                 'BD',  # 收货差异产生
            'sub_type':             'store',
            'source_id':            source_id,
            'source_code':          source_code,
            'partner_id':           partner_id,
            'created_at':           datetime.utcnow(),
            'created_by':           user_id,
            'updated_at':           datetime.utcnow(),
            'updated_name':         operator_name,
            'created_name':         operator_name,
            'updated_by':           user_id,
            'return_to':            return_to,
            'logistics_type':       logistics_type,
            'product_nums':         product_nums
        }
        if remark:
            args.update(dict(remark=json.dumps(remark)))
        # 创建退货单
        ReturnModel.create_returns(**args)

        # 调用库存引擎，按照仓库承担数量减少门店库存 —— 创建前已经扣了，所以不重复扣
        diff_args = {}
        if diff_type == 'HC':
            description = 'mcu receiving diff'
            # receiving_diff_db = ReceivingDiffModel.get(receiving_diff_id)
            result = self.withdraw_inventory(diff_ret_id, description, partner_id, user_id, code='DIFF_RETURN')
            if result and result.get('id'):
                diff_args = {'inventory_req_id': result['id'], 'inventory_status': result['status']}
                # receiving_diff_db.update(**args)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = diff_ret_id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return diff_ret_id, diff_args

    # 提交
    def submit_return(self, return_id, partner_id, user_id):
        """
        提交退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_products_db = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                         partner_id=partner_id)
        if not count:
            raise NoResultFoundError("no result found!")
        # 时间校验
        now = datetime.now(timezone.utc)
        time_config_map, allow_specified_time = metadata_service.get_time_config(partner_id, user_id,
                                                                                 "boh.store.return",
                                                                                 store_id=return_db.return_by)
        if return_db.type in ('BO', 'NBO') and return_db.sub_type in (
        'store', 'fs_store') and allow_specified_time and time_config_map.get(return_db.return_by):
            start = time_config_map.get(return_db.return_by)[0]
            end = time_config_map.get(return_db.return_by)[1]
            # if start <= now <= end:
            if now < start or now > end:
                raise DataValidationException("时间限制, 不允许提交退货单")
        # 增加库存校验
        product_detail_list = []
        for return_product_db in return_products_db:
            product_detail = {}
            product_detail['product_id'] = return_product_db.product_id
            product_detail['product_name'] = return_product_db.product_name
            product_detail['quantity'] = return_product_db.quantity
            product_detail['unit_rate'] = return_product_db.unit_rate
            product_detail_list.append(product_detail)
        # 如果是原单退货，校验是否超过收货数量
        if return_db.type == 'BO':
            self.if_over_return_by_rec(source_code=return_db.source_code,
                                       product_detail=product_detail_list, partner_id=partner_id,
                                       user_id=user_id, return_id=return_db.id)

        # 实时库存是否允许退货
        self.check_return_available(store_id=return_db.return_by, product_detail_list=product_detail_list,
                                    partner_id=partner_id, user_id=user_id,
                                    sub_type=return_db.sub_type, logistics_type=return_db.logistics_type,
                                    action="submit")

        # 状态同步receipt单据
        today = datetime.today()
        try:
            entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                delivery_detail = rows[0]
                receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='SUBMITTED',
                                                    partner_id=partner_id, user_id=user_id)

            entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                receive_detail = rows[0]
                receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='SUBMITTED',
                                                   partner_id=partner_id, user_id=user_id)
        except Exception as e:
            raise DataValidationException("同步单据状态出错, 请重试！")

        if return_db.status == 'INITED' or return_db.status == 'REJECTED':
            args = {
                'status':       'SUBMITTED',
                'updated_at':   datetime.utcnow(),
                'updated_by':   user_id,
                'updated_name': operator_name,
                'review_by':    user_id,
            }
            return_db.update_returns(return_id, args, partner_id)

            product_update_list = []
            for return_product_db in return_products_db:
                p_args = {
                    'id':           return_product_db.id,
                    'status':       'SUBMITTED',
                    'updated_at':   datetime.utcnow(),
                    'updated_by':   user_id,
                    'updated_name': operator_name
                }
                product_update_list.append(p_args)
            ReturnProductModel.update_products_in_all(updated_products=product_update_list)

            if return_db.logistics_type == 'NMD':
                # PDA消息推送
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                      user_id=user_id,
                                                      scope_id=1,
                                                      store_id=return_db.return_by,
                                                      source_root_id=return_db.id,
                                                      source_id=return_db.id,
                                                      source_type="RETURN",
                                                      action="SUBMITTED",
                                                      ref_source_id=return_db.id,
                                                      ref_source_type="RETURN",
                                                      ref_action="INITED",
                                                      content={
                                                          "store_name":           metadata_service.get_store(
                                                              return_db.return_by, partner_id=partner_id,
                                                              user_id=user_id).get('name'),
                                                          "return_delivery_date": str(return_db.return_delivery_date),
                                                          "return_reason":        return_db.return_reason,
                                                          "remark":               return_db.remark,
                                                          "updated_at":           str(datetime.utcnow()),
                                                          "updated_by":           str(user_id),
                                                          "updated_name":         operator_name
                                                      }
                                                      )
            else:
                # PDA消息推送
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                      user_id=user_id,
                                                      scope_id=1,
                                                      store_id=return_db.return_by,
                                                      source_root_id=return_db.id,
                                                      source_id=return_db.id,
                                                      source_type="RETURN_C",
                                                      action="SUBMITTED",
                                                      ref_source_id=return_db.id,
                                                      ref_source_type="RETURN_C",
                                                      ref_action="INITED",
                                                      content={
                                                          "store_name":           metadata_service.get_store(
                                                              return_db.return_by, partner_id=partner_id,
                                                              user_id=user_id).get('name'),
                                                          "return_delivery_date": str(return_db.return_delivery_date),
                                                          "return_reason":        return_db.return_reason,
                                                          "remark":               return_db.remark,
                                                          "updated_at":           str(datetime.utcnow()),
                                                          "updated_by":           str(user_id),
                                                          "updated_name":         operator_name
                                                      }
                                                      )

            if return_db.sub_type in ('store', 'fs_store'):
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                                 doc_type="return"))

            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = return_id
            log_detail['operation'] = 'SUBMIT'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.utcnow()
            log_detail['created_name'] = operator_name
            ReturnLogModel.create_returns_log(**log_detail)

            return True
        else:
            raise StatusUnavailable("only INITED/ REJECTED data can be submitted!")

    # 驳回
    def reject_return(self, return_id, partner_id, user_id, reject_reason=None):
        """
        驳回退货单
        提交状态下的退货单才可以驳回
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_product_dbs = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                         partner_id=partner_id)
        if not count:
            raise NoResultFoundError("no result found!")
        if return_db.status != 'SUBMITTED':
            raise StatusUnavailable("only SUBMITTED data can be rejected!")

        # 状态同步receipt单据
        today = datetime.today()
        entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
        rows = entity.rows
        if rows:
            delivery_detail = rows[0]
            receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='REJECTED',
                                                partner_id=partner_id, user_id=user_id)

        entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
        rows = entity.rows
        if rows:
            receive_detail = rows[0]
            receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='REJECTED',
                                               partner_id=partner_id, user_id=user_id)

        args = {
            'status':        'REJECTED',
            'updated_at':    datetime.utcnow(),
            'updated_by':    user_id,
            'updated_name':  operator_name,
            'review_by':     user_id,
            'reject_reason': reject_reason
        }
        return_db.update_returns(return_id, args, partner_id)

        product_update_list = []
        for return_product_db in return_product_dbs:
            p_args = {
                'id':           return_product_db.id,
                'status':       'REJECTED',
                'updated_at':   datetime.utcnow(),
                'updated_by':   user_id,
                'updated_name': operator_name,
                'review_by':    user_id
            }
            product_update_list.append(p_args)
        ReturnProductModel.update_products_in_all(updated_products=product_update_list)
        if return_db.sub_type in ('store', 'fs_store'):
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                             doc_type="return"))
        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'REJECT'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['updated_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return True

    # 审核
    def approve_return(self, return_id, partner_id, user_id, trans_type=None):
        """
        审核退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_products_db = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                         partner_id=partner_id)
        if not count:
            raise NoResultFoundError("没有对应退货单!")

        if return_db.status != 'SUBMITTED':
            if return_db.logistics_type == 'NMD':
                raise StatusUnavailable("只有已提交的退货单可以审核!")

        # 增加库存校验：是否允许审核退货
        product_detail_list = []
        for return_product_db in return_products_db:
            product_detail = {}
            product_detail['product_id'] = return_product_db.product_id
            product_detail['quantity'] = return_product_db.quantity
            product_detail['unit_rate'] = return_product_db.unit_rate
            product_detail_list.append(product_detail)
        self.check_return_available(store_id=return_db.return_by, product_detail_list=product_detail_list,
                                    partner_id=partner_id, user_id=user_id,
                                    sub_type=return_db.sub_type, logistics_type=return_db.logistics_type,
                                    action="approve")

        args = {
            'status':           'APPROVED' if return_db.type != "IAD" else 'CONFIRMED',
            'updated_at':       datetime.utcnow(),
            'updated_name':     operator_name,
            'updated_by':       user_id,
            'review_by':        user_id,
            'inventory_status': 'INITED',
            'trans_type':       trans_type
        }
        return_db.update_returns(return_id, args, partner_id)

        product_update_list = []
        for return_product_db in return_products_db:
            p_args = {
                'id':               return_product_db.id,
                'status':           'APPROVED' if return_db.type != "IAD" else 'CONFIRMED',
                # 'status':'CONFIRMED',
                'updated_at':       datetime.utcnow(),
                'updated_name':     operator_name,
                'updated_by':       user_id,
                'review_by':        user_id,
                'inventory_status': 'INITED',
            }
            product_update_list.append(p_args)
        ReturnProductModel.update_products_in_all(updated_products=product_update_list)

        # 门店退货
        try:
            if return_db.sub_type in ('store', 'fs_store'):
                # source_type = 'RETURN'
                today = datetime.today()
                # 状态同步receipt单据
                entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if rows:
                    delivery_detail = rows[0]
                    if return_db.type == 'IAD':  # 2021-07-27 库存调整退货单不自动推进，改由审核退货单触发
                        receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='AUTO_DELIVERY',
                                                            partner_id=partner_id, user_id=user_id)
                    else:
                        receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='APPROVED',
                                                            partner_id=partner_id, user_id=user_id)

                entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if not rows:
                    raise NoResultFoundError('NoResultFound!')
                receive_detail = rows[0]
                if return_db.type == 'IAD':  # 2021-07-27 库存调整退货单不自动推进，改由审核退货单触发
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='AUTO_RECEIVE',
                                                       partner_id=partner_id, user_id=user_id)
                    self.confirm_return(return_id=return_id, partner_id=partner_id, user_id=user_id)
                else:
                    # 审核之后，仓库退货收货单被激活，状态同步给仓库
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='ACTIVATE',
                                                       partner_id=partner_id, user_id=user_id)
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                                 doc_type="return"))
            # 仓库/加工中心采购退货
            else:
                # 状态同步receipt单据
                entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if rows:
                    delivery_detail = rows[0]
                    receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='APPROVED',
                                                        partner_id=partner_id, user_id=user_id)
        except Exception as e:
            raise DataValidationException("审核退货单, 同步状态失败, 请重试！")

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'APPROVE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        if trans_type and trans_type == "RefundOnly" and return_db.sub_type == 'store':
            self.delivery_return(return_id=return_id, partner_id=partner_id, \
                                 user_id=user_id, trans_type=trans_type)

        return True

    # 提货
    def delivery_return(self, return_id, partner_id, user_id, is_auto=False, trans_type=None):
        """
        退货单提货
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_products_db = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                         partner_id=partner_id)
        if not count:
            raise NoResultFoundError("NoResultFound!")

        if return_db.status != 'APPROVED' and not is_auto:
            raise StatusUnavailable("只有审核后的退货单可以提货!")

        args = {
            'id':                   return_id,
            'status':               'DELIVERED',
            'return_delivery_date': datetime.utcnow(),
            'updated_at':           datetime.utcnow(),
            'updated_by':           user_id,
            'review_by':            user_id,
            'inventory_status':     'INITED',
            'updated_name':         operator_name
        }

        if return_db.type in ('CAD', 'IAD'):  # 对账调整单在创建时，receipt就会自动推进
            args['status'] = 'CONFIRMED'
        else:
            try:
                ## receipt.delivery发货，扣库存
                entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if not rows:
                    raise NoResultFoundError('NoResultFound!')
                delivery_detail = rows[0]
                receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='AUTO_DELIVERY',
                                                    partner_id=partner_id, user_id=user_id)

                ## receipt.receive提货/入库
                today = datetime.today()
                entity = receipt_service.list_receives(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if not rows:
                    raise NoResultFoundError('NoResultFound!')
                receive_detail = rows[0]

                # 仓库/加工中心采购退货单 & 门店直送退货单，直接推进入库，更新为终态
                if return_db.logistics_type == "PUR" or is_auto:
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='CONFIRM',
                                                       partner_id=partner_id, user_id=user_id)
                    args['status'] = 'CONFIRMED'

                # 退货收货单做提货操作，更新已提货状态
                else:
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='DELIVERY_OUT',
                                                       partner_id=partner_id, user_id=user_id)
            except Exception as e:
                raise DataValidationException("退货提货同步状态失败, 请重试！")

        product_update_list = []
        for return_product_db in return_products_db:
            p_args = {
                'id':           return_product_db.id,
                'updated_at':   datetime.utcnow(),
                'updated_by':   user_id,
                'review_by':    user_id,
                'updated_name': operator_name
            }
            product_update_list.append(p_args)
        ReturnProductModel.update_products_in_all(updated_products=product_update_list, update_return_details=args)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'DELIVERY' if not is_auto else 'AUTO_DELIVEY'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return True

    def delivery_return_by_code(self, return_code, partner_id, user_id):
        ret = {
            'code':   return_code,
            'status': 'FAILED'
        }
        return_db = ReturnModel.get_return_by_code(return_code, partner_id)
        if not return_db:
            ret['msg'] = '没找到对应单据'
            ret['failed_code'] = 'NotFound'
            return ret
        try:
            self.delivery_return(return_id=return_db.id, partner_id=partner_id, user_id=user_id)
        except Exception as e:
            ret["msg"] = str(e)
        ret['status'] = 'SUCCESS'
        return ret

    # 确认
    def confirm_return(self, return_id, partner_id, user_id):
        """
        确认退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_products_db = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                         partner_id=partner_id)
        if not return_db:
            raise NoResultFoundError("no result found!")

        # if return_db.logistics_type in ('NMD', 'PAD'):
        #     if return_db.type not in ('IAD', 'CAD') and return_db.status != 'DELIVERED':
        #         raise StatusUnavailable("only DELIVERED data can be confirmed!")
        # else:
        #     if return_db.type not in ('IAD', 'CAD') and return_db.status not in ('APPROVED', 'DELIVERED'):
        #         raise StatusUnavailable("only APPROVED data can be confirmed!")
        if return_db.type not in ('IAD', 'CAD') and return_db.status not in ('APPROVED', 'DELIVERED'):
            raise StatusUnavailable("只有审核/提货状态的退货单可确认!")

        args = {
            'status':           'CONFIRMED',
            'updated_at':       datetime.utcnow(),
            'updated_by':       user_id,
            'updated_name':     operator_name,
            'review_by':        user_id,
            'inventory_status': 'INITED',
        }

        product_update_list = []
        for return_product_db in return_products_db:
            p_args = {
                'id':           return_product_db.id,
                'status':       'CONFIRMED',
                'updated_at':   datetime.utcnow(),
                'updated_by':   user_id,
                'updated_name': operator_name
            }
            product_update_list.append(p_args)

        # 配送方式接入退款
        if return_db.logistics_type == "NMD" and return_db.trans_type == ReturnTransType.NeedPickUp.code:
            batch_id = 0
            refund_id = get_guid()
            if return_db.source_id:
                rec_db = receipt_service.get_receive_by_id(receive_id=return_db.source_id, partner_id=partner_id,
                                                           user_id=user_id)
                batch_id = rec_db.batch_id
            refund = Refund(refund_id, partner_id, user_id, operator_name)
            product_list = [p.serialize(conv=True) for p in return_products_db]
            refund_products, total_amount = refund.get_refund_products(products=product_list,
                                                                       main_type='FRS_RETURN')
            refund_detail = refund.get_return_refund_detail(
                detail=return_db, refund_amount=total_amount, main_type='FRS_RETURN', refund_type='RETURN_REFUND',
                master_id=batch_id)
            refund_log = refund.get_refund_log()
            db_session = session_maker()
            SupplyFranchiseeRefund.create_franchisee_refund(
                db_session=db_session, refund_details=[refund_detail], refund_products=refund_products,
                refund_logs=[refund_log]
            ) if total_amount else ...

        return_db.update_returns(return_id, args, partner_id)
        ReturnProductModel.update_products_in_all(updated_products=product_update_list)

        if return_db.sub_type == "store":
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                             doc_type="return"))
        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'CONFIRM'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return True

    # 更新
    def update_return_product_quantity(self, return_id, partner_id, user_id,
                                       products=None, return_reason=None, remark=None,
                                       return_delivery_date=None, return_to=None,
                                       attachments=None, logistics_type=None):
        """
        更新退货单及退货商品详情
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        product_id_list = []
        str_product_id_list = []
        returns_db = ReturnModel.get_return_by_id(return_id, partner_id)
        if not return_to:
            return_to = returns_db.return_to
        if products and len(products) > 0:
            for product in products:
                product_id_list.append(int(product.product_id))
                str_product_id_list.append(str(product.product_id))

            product_dict = {}
            product_unit_dict = {}
            product_meta_details = metadata_service.get_product_list(ids=product_id_list,
                                                                     include_units=True,
                                                                     return_fields='id,code,name,model_name',
                                                                     partner_id=partner_id, user_id=user_id).get('rows',
                                                                                                                 [])
            product_spec_dict = {}
            for product_detail_info in product_meta_details:
                product_spec_dict[int(product_detail_info['id'])] = product_detail_info.get('model_name')
                product_dict[int(product_detail_info['id'])] = product_detail_info
                if product_detail_info.get('units'):
                    product_unit_dict[product_detail_info['id']] = {}
                    for unit in product_detail_info.get('units'):
                        if unit.get('purchase') and unit['purchase'] == True:
                            product_unit_dict[product_detail_info['id']]['purchase'] = unit['rate'] if unit.get(
                                'rate') else 0
                        if unit.get('order') and unit['order'] == True:
                            product_unit_dict[product_detail_info['id']]['order'] = unit['rate'] if unit.get(
                                'rate') else 0
                        if unit.get('default') and unit['default'] == True:
                            product_unit_dict[product_detail_info['id']]['default'] = unit['rate'] if unit.get(
                                'rate') else 0
                else:
                    product_unit_dict[product_detail_info['id']]['default'] = 0

            unit_dict = {}
            unit_details = metadata_service.get_unit_list(partner_id=partner_id, user_id=user_id).get('rows', [])
            for unit_detail in unit_details:
                unit_dict[int(unit_detail['id'])] = unit_detail

            # 直送单需要拿到价格，获取商品成本
            product_tax_dict = {}
            if returns_db.logistics_type == 'PUR' and returns_db.type not in ('IAD', 'CAD'):
                if returns_db.type == 'BO':  # 原单退货价格从收货单里取
                    product_tax_dict = get_receive_prod_price(returns_db.source_code, partner_id, user_id)
                else:
                    if returns_db.sub_type == 'warehouse':
                        product_tax_dict = metadata_service.get_tax_list(
                            vendor_id=return_to, product_id_list=str_product_id_list,
                            warehouse_id=returns_db.return_by, valid_time=datetime.utcnow(),
                            partner_id=partner_id, user_id=user_id)
                    elif returns_db.sub_type == 'machining':
                        product_tax_dict = metadata_service.get_tax_list(
                            vendor_id=return_to, product_id_list=str_product_id_list,
                            machining_id=returns_db.return_by, valid_time=datetime.utcnow(),
                            partner_id=partner_id, user_id=user_id)
                    else:
                        product_tax_dict = metadata_service.get_tax_list(
                            vendor_id=return_to, product_id_list=str_product_id_list,
                            store_id=returns_db.return_by, valid_time=datetime.utcnow(),
                            partner_id=partner_id, user_id=user_id)
                # 订货单位*订货rate=采购单位*采购rate
                # 采购单位/订货单位=订货rate/采购rate

                # 订货单价*订货单位=采购单价*采购单位
                # 订货单价 = 采购单价*采购单位/订货单位
                # 订货单价 = 采购单价*订货rate/采购rate
                purchase2order_rate_dict = {}
                if returns_db.sub_type in ('store', 'fs_store'):  # 只有门店直送需要转换单位价格，仓库/加工中心采购退货本身业务单位就是采购单位
                    for key, key_value in product_unit_dict.items():
                        purchase_rate = key_value.get('purchase') if key_value.get('purchase') else 1
                        order_rate = key_value.get('order') if key_value.get('order') else 1
                        purchase2order_rate_dict[int(key)] = order_rate / purchase_rate

            new_product_list = []
            update_product_list = []
            delete_product_list = []

            count = 0
            for product in products:
                count += 1
                product = pb2dict(product)
                product_id = product.get('product_id', 0)
                if not product.get('id'):
                    product['id'] = gen_snowflake_id()
                    product['return_id'] = return_id
                    product['return_by'] = returns_db.return_by
                    product['return_to'] = return_to
                    product['return_date'] = returns_db.return_date
                    product['status'] = 'INITED'
                    product['partner_id'] = partner_id
                    product['created_by'] = user_id
                    product['updated_name'] = operator_name
                    product['created_name'] = operator_name
                    product['created_at'] = datetime.utcnow()
                    product['updated_at'] = datetime.utcnow()
                    product['returned_quantity'] = product['quantity']
                    product_meta_detail = product_dict.get(int(product_id))
                    if not product_meta_detail:
                        raise NoResultFoundError("未找到商品主档-product_id-{}".format(convert_to_int(product_id)))
                    product['product_name'] = product_meta_detail['name']
                    product['product_code'] = product_meta_detail['code']
                    unit_detail = unit_dict.get(int(product['unit_id']))
                    if not unit_detail:
                        raise NoResultFoundError("未找到商品单位-unit_id-{}".format(convert_to_int(product['unit_id'])))
                    product['unit_name'] = unit_detail['name']
                    product['unit_spec'] = product_spec_dict.get(int(product_id))  # 用作商品规格
                    if returns_db.logistics_type == 'PUR' and returns_db.type not in ('IAD', 'CAD'):
                        product_tax_detail = product_tax_dict.get(product_id)
                        if not product_tax_detail:
                            raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))

                        tax_rate = product_tax_detail.get('rate', 0)
                        if returns_db.type == 'BO':  # 原单价格从收货单里取，不必做转换
                            price = product_tax_detail.get('no_tax')
                            price_tax = product_tax_detail.get('tax')
                        else:
                            price = product_tax_detail.get('no_tax', 0) * purchase2order_rate_dict.get(product_id, 1)
                            price_tax = product_tax_detail.get('tax', 0) * purchase2order_rate_dict.get(product_id, 1)

                        product['tax_rate'] = tax_rate
                        product['price'] = price
                        product['price_tax'] = price_tax

                    self.check_product_attachments(product)
                    new_product_list.append(product)

                else:
                    if product['quantity'] == 0:
                        delete_product_list.append(product['id'])
                    else:
                        product_db = ReturnProductModel.get(product['id'])
                        product['unit_spec'] = product_spec_dict.get(int(product['product_id']))  # 用作商品规格
                        product['unit_rate'] = product_db.unit_rate
                        product['updated_by'] = user_id
                        product['updated_at'] = datetime.utcnow()
                        product['returned_quantity'] = product['quantity']
                        if returns_db.logistics_type == 'PUR' and returns_db.type not in ('IAD', 'CAD'):
                            product_tax_detail = product_tax_dict.get(product_id)
                            if not product_tax_detail:
                                raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))

                            tax_rate = product_tax_detail.get('rate', 0)
                            if returns_db.type == 'BO':  # 原单价格从收货单里取，不必做转换
                                price = product_tax_detail.get('no_tax')
                                price_tax = product_tax_detail.get('tax')
                            else:
                                price = product_tax_detail.get('no_tax', 0) * purchase2order_rate_dict.get(product_id,
                                                                                                           1)
                                price_tax = product_tax_detail.get('tax', 0) * purchase2order_rate_dict.get(product_id,
                                                                                                            1)

                            product['tax_rate'] = tax_rate
                            product['price'] = price
                            product['price_tax'] = price_tax

                        product.pop('product_id')
                        self.check_product_attachments(product)
                        update_product_list.append(product)

            ReturnProductModel.create_returns_products(
                return_product_list=new_product_list,
                updated_product_list=update_product_list,
                delete_product_list=delete_product_list)

        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        p_total, p_db = ReturnProductModel.list_return_products_by_return_id(return_id, partner_id=partner_id)
        args = {
            'product_nums': p_total,
            'updated_at':   datetime.utcnow(),
            'updated_by':   user_id,
            'updated_name': operator_name
        }
        if return_reason:
            args['return_reason'] = return_reason
        if remark:
            args['remark'] = remark
        if return_delivery_date and isinstance(return_delivery_date, Timestamp) and return_delivery_date.seconds:
            timestamp = Timestamp()
            timestamp.seconds = return_delivery_date.seconds
            return_delivery_date = timestamp.ToDatetime()
            args['return_delivery_date'] = return_delivery_date
        if return_to:
            args['return_to'] = return_to
        if attachments:
            args['attachments'] = handle_request_attachments(attachments)
        if logistics_type:
            args['logistics_type'] = logistics_type
        if args:
            return_db.update_returns(return_id, args, partner_id)

        delivery_p_list = []
        rec_p_list = []
        for product in p_db:
            # ---ForReceiptStart---#
            # ForReceiptDelivery
            delivery_p = {
                'delivery_by':       return_db.return_by,
                'product_id':        product.product_id,
                'product_code':      product.product_code,
                'product_name':      product.product_name,
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.quantity,
                'order_quantity':    product.quantity,
                'unit_id':           product.unit_id,
                'unit_name':         product.unit_name,
                'unit_rate':         product.unit_rate,
                'cost_price':        product.price,
                'tax_price':         product.price_tax,
                'tax_rate':          product.tax_rate,
                'unit_spec':         product.unit_spec,
                # 'category_id':product.category_id
            }
            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by':        int(return_db.return_to),
                'product_id':        product.product_id,
                'product_code':      product.product_code,
                'product_name':      product.product_name,
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.quantity,
                'order_quantity':    product.quantity,
                'unit_id':           product.unit_id,
                'unit_name':         product.unit_name,
                'unit_rate':         product.unit_rate,
                'cost_price':        product.price,
                'tax_price':         product.price_tax,
                'tax_rate':          product.tax_rate,
                'unit_spec':         product.unit_spec,
                # 'category_id':product.category_id
            }
            rec_p_list.append(rec_p)
        logging.info('Start Update Receipt.Receive')
        try:
            receipt_service.update_receives_products(
                batch_id=return_db.id, order_id=return_db.id,
                deal_products=rec_p_list, partner_id=partner_id, user_id=user_id,
                reason=return_reason, remark=remark, distr_type=logistics_type, receive_by=return_to)
            receipt_service.update_deliverys_products(
                batch_id=return_db.id, order_id=return_db.id,
                deal_products=delivery_p_list, partner_id=partner_id, user_id=user_id,
                reason=return_reason, remark=remark, distr_type=logistics_type, receive_by=return_to)
        except Exception as e:
            raise DataValidationException("同步更新退货收货单数据失败, 请重试！")

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'UPDATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return True

    # 删除
    def delete_return(self, return_id, partner_id, user_id, is_auto=False):
        """
        删除退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        count, return_product_dbs = ReturnProductModel.list_return_products_by_return_id(return_id,
                                                                                         partner_id=partner_id)
        if not count:
            raise NoResultFoundError("no result found!")

        if return_db.status not in ('INITED', 'REJECTED') and not is_auto:
            raise StatusUnavailable("只有新建/驳回的单子可以被作废")

        # 状态同步receipt单据
        today = datetime.today()
        entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
        rows = entity.rows
        if rows:
            delivery_detail = rows[0]
            receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='DELETED',
                                                partner_id=partner_id, user_id=user_id)

        entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
        rows = entity.rows
        if rows:
            receive_detail = rows[0]
            receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='DELETED',
                                               partner_id=partner_id, user_id=user_id)

        args = {
            'status':       'DELETED',
            'updated_at':   datetime.utcnow(),
            'updated_by':   user_id,
            'updated_name': operator_name,
            'review_by':    user_id
        }
        return_db.update_returns(return_id, args, partner_id)

        product_update_list = []
        for return_product_db in return_product_dbs:
            p_args = {
                'id':           return_product_db.id,
                'status':       'DELETED',
                'updated_at':   datetime.utcnow(),
                'updated_by':   user_id,
                'updated_name': operator_name,
                'review_by':    user_id
            }
            product_update_list.append(p_args)
        ReturnProductModel.update_products_in_all(updated_products=product_update_list)

        if return_db.sub_type in ('store', 'fs_store'):
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                             doc_type="return"))
        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'DELETE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return True

    def check_product_attachments(self, product):
        if 'attachments' in product.keys():
            if not product.get('attachments'):
                product.pop('attachments')
        return product

    def if_over_return_by_rec(self, source_code, product_detail, partner_id, user_id, return_id=None):
        """
        检查原单退货的是否超退,
        更新单据时，必须传入已有的return_id
        """

        # 新建时的校验
        if not return_id:
            # 1、如果收货单存在新建/提交/驳回状态的退货单，则不允许建新退单
            count= ReturnModel.get_return_by_source_code(source_code, partner_id, ['INITED', 'SUBMITTED', 'REJECTED'])
            if count:
                raise DataValidationException('存在新建/提交/驳回状态的退货单，不允许重复创建:{}-{}'.format(source_code, partner_id))
        # 对于驳回和新建的单子进行校验
        else:
            return_db = ReturnModel.get_return_by_id(int(return_id), partner_id)
            count, return_dbs = ReturnModel.list_returns(partner_id=partner_id, source_code=source_code,
                                                         status=['INITED', 'SUBMITTED'])
            for renturn_db in return_dbs:
                if return_db.status in ('INITED', 'REJECTED', 'SUBMITTED') and renturn_db.id != return_id:
                    raise DataValidationException('存在新建/提交/驳回状态的退货单，请先确认！')

        # 2、校验数量
        # a。获取该收货单收货数量, key:int(product_id), value:实际收货数量
        actual_rec_p_dict = {}
        rec_product_list = receipt_service.get_receive_products_by_receive_code(receive_code=str(source_code),
                                                                                partner_id=partner_id, user_id=user_id)
        rec_product_list = rec_product_list.rows
        for rec_p in rec_product_list:
            actual_rec_p_dict[int(rec_p.product_id)] = rec_p.receive_quantity

        # b。捞取该收货单已创建的退货单, key:int(product_id), value:已退总数
        exist_ret_p_dict = ReturnProductModel.get_sum_return_quantity_by_source_code(source_code, partner_id=partner_id)
        # c。校验：已退数量+此次退货数量 > 实际收货数 False
        for product in product_detail:
            if isinstance(product, PbMessage):
                product = pb2dict(product)
            product_id = int(product.get('product_id', 0))

            # 此次退货数量
            return_qty = product.get('quantity', 0)
            return_qty = Decimal(return_qty).quantize(Decimal('0.********'))
            # 实际收货数
            receive_qty = actual_rec_p_dict.get(product_id) if actual_rec_p_dict.get(product_id) else 0
            receive_qty = Decimal(receive_qty).quantize(Decimal('0.********'))
            # 已退数量
            exist_return_qty = exist_ret_p_dict.get(product_id) if exist_ret_p_dict.get(product_id) else 0
            exist_return_qty = Decimal(exist_return_qty).quantize(Decimal('0.********'))

            if exist_return_qty + return_qty > receive_qty:
                # print(exist_return_qty, return_qty, receive_qty, exist_return_qty+return_qty)
                raise DataValidationException('{}超出退货限制{}'.format(product.get('product_name'), Decimal(
                    exist_return_qty + return_qty - receive_qty).quantize(Decimal('0.000'))))

        return True

    def list_unfinished_returns(self, return_date_from, return_date_to, delivery_date_from, delivery_date_to,
                                partner_id, user_id, sub_type=None, trans_type=None):

        # 新建、已提交、已驳回状态的自动作废
        count, return_db_list = ReturnModel.list_returns(partner_id=partner_id,
                                                         status=["INITED", "SUBMITTED", "REJECTED"],
                                                         start_date=return_date_from, end_date=return_date_to,
                                                         sub_type=sub_type, trans_type=trans_type
                                                         )

        # 已审核状态的自动入库
        count, a_return_db_list = ReturnModel.list_returns(partner_id=partner_id,
                                                           status=["APPROVED"],
                                                           delivery_start_date=delivery_date_from,
                                                           delivery_end_date=delivery_date_to,
                                                           sub_type=sub_type,
                                                           trans_type=trans_type
                                                           )

        return return_db_list, a_return_db_list

    def get_history(self, partner_id, user_id, request):
        return_id = request.id
        return_logs, total = ReturnLogModel.get_by_doc_id(partner_id=partner_id, doc_id=return_id)
        log_list = []
        for log in return_logs:
            log_detail = {
                'id':              log.id,
                'status':          log.operation,
                'updated_by':      log.created_by,
                'updated_by_name': log.created_name,
                'updated_at':      Timestamp(seconds=int(log.updated_at.timestamp())),
            }
            log_list.append(log_detail)
        return log_list, total

    """
    def create_return(self, return_by, return_delivery_date, type, sub_type, return_reason,
                                    product_detail,partner_id,user_id, return_to, 
                                    remark=None, attachments=None, is_adjust=None):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 创建退货单
        if type not in ['01', '02', '03']:
            # 01:统退，02:直退，03:空容器退货
            raise DataValidationException("type must be one of ['01','02', '03']!")
        if sub_type not in ['01', '02']:
            # 01:自主退货，02:召回
            raise DataValidationException("type must be one of ['01','02']!")
        return_delivery_date = date.fromtimestamp(return_delivery_date.seconds)

        return_id = gen_snowflake_id()
        return_code = Supply_doc_code.get_code_by_type('RETURN_OD',partner_id, None)
        return_date = datetime.utcnow()
        
        # return_to = metadata_service.get_store(return_by,partner_id=partner_id, user_id=user_id).get('distribution_region')
        # if return_to:
        #     return_to = return_to[0]

        product_id_list = []
        product_code_list = []
        product_id_list_int = []
        for product_d in product_detail:
            product_code_list.append(product_d.product_code)
            product_id_list.append(str(product_d.product_id))
            product_id_list_int.append(int(product_d.product_id))
        
        products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                          include_units=True,
                                                          return_fields='id,code,name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        for product_detail_info in products_info:
            product_info_dict[product_detail_info['id']] = product_detail_info
        

        product_info_dict = {}
        product_unit_dict = metadata_service.get_product_unit(ids=product_id_list_int, partner_id=partner_id, user_id=user_id)

        # 创建退货单商品
        product_nums = 0
        product_insert_list = []
        for product in product_detail:
            # TODO 库存&主档：验证商品是否可退以及可退的库存数量
            product_nums += 1
            product = pb2dict(product)
            product['id'] = gen_snowflake_id()
            product['return_id'] = return_id
            product['return_by'] = return_by
            product['return_date'] = return_date
            product['status'] = 'INITED'
            product['is_returned'] = False
            product['partner_id'] = partner_id
            product['created_by'] = user_id
            product['updated_by'] = user_id
            product['updated_name'] = operator_name
            product['created_name'] = operator_name
            product['created_at'] = datetime.utcnow()
            product['updated_at'] = datetime.utcnow()

            product_meta_detail = product_info_dict.get(product['product_id'])
            product['product_name'] = product_meta_detail.get('name') if product_meta_detail else None
            product['product_code'] = product_meta_detail.get('code') if product_meta_detail else None
            units_list = product_meta_detail.get('units')
            
            # 配送退货需要校验订货单位
            if is_direct == False:
                for unit in units_list:
                    if int(unit['id']) == product['unit_id']:
                        if not unit.get('order'):
                            raise DataValidationException("\"{}\"未配置订货单位".format(product['product_name']))

            unit_id = product['unit_id']
            product['unit_id'] = unit_id
            # product['unit_name'] = unit_detail['name']
            # product['unit_spec'] = unit_detail['code']
            product_insert_list.append(product)
        ReturnProductModel.create_returns_products(product_insert_list)
        args = {
            'id':return_id,
            'code':return_code,
            'return_by':return_by,
            'return_delivery_date':return_delivery_date,
            'type':type,
            'sub_type':sub_type,
            'return_reason':return_reason,
            'status':'INITED',
            'is_returned':False,
            'partner_id':partner_id,
            'created_at':datetime.utcnow(),
            'created_by':user_id,
            'updated_at':datetime.utcnow(),
            'updated_name':operator_name,
            'created_name':operator_name,
            'updated_by':user_id,
            'return_to':return_to,
            'return_date':return_date
        }
        if remark:
            args.update(dict(remark=remark))
        if attachments:
            args.update(dict(attachments=attachments))
        if is_adjust:
            args.update(dict(is_adjust=is_adjust))
        
        # 创建退货单
        ReturnModel.create_returns(**args)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.utcnow()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)
        
        return return_id
    """


returns_service = ReturnService()


def get_tax(stub, product_id_list, partner_id, user_id, store_id, vendor_id):
    """获取tax_price"""
    price_service = get_price_service(stub)
    return price_service.get_product_price(
        product_id_list=product_id_list,
        partner_id=partner_id, user_id=user_id,
        store_id=store_id, vendor_id=vendor_id)


def get_price_service(stub='metadata'):
    collections = {
        'metadata':    MetaDataService,
        'third_party': ThirdPartyService
    }
    return collections[stub]


def get_stub(partner_id, is_original_receipt):
    res = ThirdParty.get_third_party(partner_id=partner_id)
    if res and not is_original_receipt:
        stub = 'third_party'
    else:
        stub = 'metadata'
    return stub


def get_tax_price(stub, product_unit_dict):
    purchase2order_rate_dict = {}
    for key, key_value in product_unit_dict.items():
        purchase_rate = key_value.get('purchase') if key_value.get('purchase') else 1
        order_rate = key_value.get('order') if key_value.get('order') else 1
        default_rate = key_value.get('default') if key_value.get('default') else 1
        if stub == 'metadata':
            purchase2order_rate_dict[int(key)] = order_rate / purchase_rate
        else:
            purchase2order_rate_dict[int(key)] = order_rate / default_rate
    return purchase2order_rate_dict


def get_receive_prod_price(receive_code, partner_id, user_id):
    if not receive_code:
        return {}
    res = receipt_service.get_receive_products_by_receive_code(receive_code, partner_id, user_id)
    res = pb2dict(res)
    # logger.info(res)
    products = res.get('rows')
    collections = {}
    for product in products:
        prod = {}
        product_id = product.get('product_id')
        prod['rate'] = product.get('tax_rate')
        prod['no_tax'] = product.get('cost_price')
        prod['tax'] = product.get('tax_price')
        collections[product_id] = prod
    # logger.info(collections)
    return collections


class MetaDataService(object):
    _metadata_service = metadata_service

    @classmethod
    def get_product_price(self, product_id_list=None,
                          partner_id=None, user_id=None, store_id=None,
                          vendor_id=None
                          ):
        product_id_list = [str(product_id) for product_id in product_id_list]
        products_info = {}
        if product_id_list:
            products_info = self._metadata_service.get_tax_list(
                vendor_id=vendor_id, product_id_list=product_id_list,
                store_id=store_id, valid_time=datetime.utcnow(),
                partner_id=partner_id, user_id=user_id)
        return products_info
