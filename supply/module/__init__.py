import pytz

from supply import logger
from datetime import datetime, timedelta
from decimal import Decimal

from supply.client.products_manage_service import products_manage_service
from supply.utils.enums import PaymentWay
from google.protobuf.timestamp_pb2 import Timestamp
from supply.client.metadata_service import metadata_service
from supply.utils.helper import convert_to_int, convert_to_decimal


class BaseToolsModule(object):
    """公共抽象工具类"""

    def __init__(self):
        pass

    @staticmethod
    def round_up_num(num, pec=2):
        """不舍位向上保留指定位数小数，一般为处理金额时使用
        eg: num=3.333 保留2位 -> 3.34
        :param num 待处理数据 float/Decimal
        :param pec 保留小数位数 int
        """
        if isinstance(num, float):
            num = Decimal(str(num))
        elif isinstance(num, Decimal):
            pass
        else:
            return num
        if not isinstance(pec, int):
            raise TypeError(f"'{type(pec)}' object cannot be interpreted as an integer")
        if pec < 0:
            return num
        elif pec == 0:
            return num.quantize(Decimal(1), rounding="ROUND_UP")
        else:
            return num.quantize(Decimal(str(5 / 10 ** pec)), rounding="ROUND_UP")

    def get_bus_date(self) -> datetime:
        """
        获取营业日的时间戳: eg:2022-06-28 16:00:00
        :return:
        """
        tz = pytz.timezone("Asia/Shanghai")
        today = datetime.now(tz=tz)
        return datetime(year=today.year, month=today.month, day=today.day) - timedelta(hours=8)

    @staticmethod
    def get_timestamp(date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    @staticmethod
    def utcTimestamp2datetime(timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.utcfromtimestamp(timestamp.seconds)
        return date

    @staticmethod
    def timestamp2datetime(timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.fromtimestamp(timestamp.seconds)
        return date

    @staticmethod
    def get_trade_company_by_franchisee(franchisee_id, partner_id, user_id):
        # 根据加盟商查询关联的贸易公司对应的仓库
        franchisee = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id,
                                                       id=franchisee_id, schema_name="FRANCHISEE")
        if not franchisee:
            logger.warning("Franchisee Not Found!")
            return None
        fields = franchisee.get('fields') if franchisee.get('fields') else {}
        company_id = fields.get('relation', {}).get('trading_company')
        return company_id

    @staticmethod
    def get_refund_way(payment_way: str = None):
        """获取退款支付方式，目前(20221108)产品给的逻辑是:
        原单支付方式="代金券" -> 退款方式="代金券"
        其余情况: 退款方式 = "信用付"
        :param payment_way: 原单支付方式
        """
        if payment_way == PaymentWay.Voucher.code:
            refund_way = PaymentWay.Voucher.code
        else:
            refund_way = PaymentWay.CreditPay.code
        return refund_way

    @staticmethod
    def get_product_price_map(partner_id: int, user_id: int, store_id: int, order_type_id: int = None,
                              product_ids: list = None, allow_order: bool = None):
        """
        获取商品价格map
        :param partner_id:
        :param user_id:
        :param store_id:
        :param order_type_id:
        :param product_ids:
        :param allow_order:
        :return:
            {
                product_id: {xxx},
            }
        """

        price_map = dict()
        valid_products = products_manage_service.GetAgentProducts(store_id=store_id,
                                                                  order_type_id=order_type_id,
                                                                  product_ids=product_ids,
                                                                  allow_order=allow_order,
                                                                  partner_id=partner_id,
                                                                  user_id=user_id).get('rows')
        if valid_products and isinstance(valid_products, dict):
            products = valid_products.get('list', [])
            for product in products:
                order_rule = product.get('order_rule', {})
                distribution_rule = product.get('distribution_rule', {})
                product_id = convert_to_int(product.get('id'))
                price_map[product_id] = dict(
                    arrival_days=convert_to_int(distribution_rule.get('planned_arrival_days', 0)),
                    increment_quantity=convert_to_decimal(order_rule.get('increase_qty', 0)),
                    expire_quantity=convert_to_decimal(product.get('expire_quantity')),
                    max_quantity=convert_to_decimal(order_rule.get('max_qty', 0)),
                    min_quantity=convert_to_decimal(order_rule.get('min_qty', 0)),
                    product_price=product.get('product_price', []),
                    tax_price=convert_to_decimal(product.get('tax_price', 0)),  # 订货类型配置对应的价格
                    tax_rate=convert_to_decimal(product.get('tax_ratio', 0)),   # 订货类型配置对应的税率
                )
        return price_map

    @staticmethod
    def get_order_type_map(order_type_ids: list, partner_id: int, user_id: int, return_fields=None):
        if not return_fields:
            return_fields = "id,code,name"
        order_type_map = dict()
        # 查询订货类型主档
        order_types = metadata_service.list_entity(schema_name="ORDER_TYPE", ids=order_type_ids,
                                                   return_fields=return_fields, partner_id=partner_id,
                                                   user_id=user_id).get('rows', [])
        for ot in order_types:
            fields = ot.get('fields') if ot.get('fields') else {}
            order_type_map[int(ot.get('id'))] = fields
        return order_type_map
