# -*- coding: utf-8 -*-
from sqlalchemy import func
import logging
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp
from decimal import Decimal
from supply import logger
from supply.error.exception import NoResultFoundError, StatusUnavailable, DataValidationException, DealInventoryException

from supply.driver.mysql import session
from supply.utils.encode import encodeUTF8
from supply.utils.resulting import ErrorCode
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.helper import MessageTopic, convert_to_int, get_product_unit_map
from supply.utils import pb2dict
from supply.client.metadata_service import metadata_service 
from supply.client.inventory_service import inventory_service
from supply.client.receipt_service import receipt_service
from supply.task.message_service_pub import MessageServicePub
# from supply.task import public
from supply.driver.mq import mq_producer

from supply.model.supply_doc_code import Supply_doc_code
from supply.model.receiving_diff import ReceivingDiffModel, ReceivingDiffProductModel, ReceivingDiffLogModel
from supply.model.returns import ReturnModel, ReturnProductModel
from supply.module.returns import returns_service



class ReceivingDiffService():
    '''收货差异单相关服务
    service: 
        - create_receiving_diff()：新建收货差异单
        - get_receiving_diff_by_id(): 通过diff_id查询收货差异单信息
        - list_receiving_diff_products_by_diff_id():根据diff_id查询收货差异单商品明细
        - list_receiving_diffs(): 枚举所有收货差异单
        - update_receiving_diff(): 更新收货差异单（差异数量两边承担数量、差异原因、备注）
        - submit_receiving_diff(): 提交收货差异单
        - confirm_receiving_diff(): 确认收货差异单
        - reject_receiving_diff(): 驳回收货差异单
        - delete_receiving_diff(): 删除收货差异单
    '''

    def get_receiving_diff_by_id(self, receiving_diff_id, partner_id):
        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)
        if receiving_diff_db:
            receiving_diff_obj = receiving_diff_db.serialize(conv=True)
            receiving_diff_obj['attachments'] = eval(receiving_diff_obj['attachments']) if receiving_diff_obj.get('attachments') else []
            return receiving_diff_obj
        return None

    def get_receiving_diff_by_args(self, request, partner_id):
        request_id = request.request_id
        receiving_diff_dbs = ReceivingDiffModel.get_diff_by_args(partner_id=partner_id, request_id=request_id)
        receiving_diff_list = []
        if receiving_diff_dbs:
            for receiving_diff_db in receiving_diff_dbs:
                diff_obj = {
                    'receiving_id': receiving_diff_db.receiving_id,
                    'id': receiving_diff_db.id,
                    'code': receiving_diff_db.code,
                    'status': receiving_diff_db.status

                }
                receiving_diff_list.append(diff_obj)
            return receiving_diff_list
        return []

    def list_receiving_diff_products_by_args(self, partner_id, user_id, delivery_id=None, receiving_id=None):
        ''' 根据发货单/收货单查询 枚举差异单所有商品明细 '''
        if delivery_id:
            entity = receipt_service.list_receives(delivery_id=delivery_id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            receiving_id = rows[0].id
        count, receiving_diff_products_db_list = ReceivingDiffProductModel.list_receiving_diff_products_by_args(partner_id=partner_id,request_id=receiving_id)
        receiving_diff_products_list = []
        if receiving_diff_products_db_list:
            for receiving_diff_product_db in receiving_diff_products_db_list:
                receiving_diff_product_obj = receiving_diff_product_db.serialize(conv=True)
                receiving_diff_products_list.append(receiving_diff_product_obj)
            return count, receiving_diff_products_list
        return 0, None

    def list_receiving_diff_products_by_diff_id(self, diff_id,limit=None,offset=None,include_total=False, 
                                                    sort=None, order=None, partner_id=None):
        ''' 根据diff_id 枚举该收货单上所有商品明细 '''
        count = None
        count, receiving_diff_products_db_list = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(diff_id,limit,offset,include_total, sort, order, partner_id)
        receiving_diff_products_list = []
        if receiving_diff_products_db_list:
            for receiving_diff_product_db in receiving_diff_products_db_list:
                receiving_diff_product_obj = receiving_diff_product_db.serialize(conv=True)
                receiving_diff_products_list.append(receiving_diff_product_obj)
            return count, receiving_diff_products_list
        return 0, None

    def list_receiving_diffs(self,partner_id, user_id, request):
        '''枚举所有收货单'''

        limit=request.limit
        offset=request.offset
        include_total=False
        store_ids=request.store_ids
        status=request.status
        start_date=request.start_date
        end_date=request.end_date
        receiving_ids=request.receiving_ids
        receiving_code=request.receiving_code
        code=request.code
        logistics_type=request.logistics_type
        sort = request.sort
        order = request.order


        storeIdList = []
        for storeId in store_ids:
            storeIdList.append(int(storeId))
        store_ids = storeIdList

        count, receiving_diff_db = ReceivingDiffModel.list_receiving_diffs(partner_id, user_id, 
                                        limit, offset, include_total, store_ids,
                                        status, start_date, end_date, receiving_ids, receiving_code, 
                                        code, logistics_type, sort, order)
        if receiving_diff_db:
            receiving_diff_list = []
            for receiving_diff in receiving_diff_db:
                receiving_diff_obj = receiving_diff.serialize(conv=True)
                receiving_diff_obj['attachments'] = eval(receiving_diff_obj['attachments']) if receiving_diff_obj.get('attachments') else []
                receiving_diff_list.append(receiving_diff_obj)
            return count, receiving_diff_list
        return 0, None

    def update_receiving_diff(self, receiving_diff_id, partner_id, user_id, products=None, attachments=None, remark=None):
        # 配送收货差异单商品明细更新
        # 修改差异单商品明细（差异原因、门店承担数量、配送方承担数量、备注）
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)
        diff_args = {
            'id': receiving_diff_id,
            'updated_at': datetime.now(),
            'updated_by': user_id,
            'updated_name': operator_name
        }
        if attachments:
            diff_args.update(dict(attachments=attachments))
        if remark:
            diff_args.update(dict(remark=remark))

        updated_products = []
        if products:
            for product in products:
                p_args = {
                    'id': product.id,
                    'updated_at':datetime.now(),
                    'updated_by':user_id,
                    'updated_name':operator_name,
                }
                product = pb2dict(product)

                if product.get('remark'):
                    p_args['remark'] = product['remark']
                if product.get('reason_type'):
                    p_args['reason_type'] = product['reason_type']
                if product.get('s_diff_quantity'):
                    p_args['s_diff_quantity'] = product['s_diff_quantity']
                    p_args['s_diff_accounting_quantity'] = float(product['s_diff_quantity'])*float(product['unit_rate']) if product.get('unit_rate') else float(product['s_diff_quantity'])
                else:
                    p_args['s_diff_quantity'] = 0
                if product.get('d_diff_quantity'):
                    p_args['d_diff_quantity'] = product['d_diff_quantity']
                    p_args['d_diff_accounting_quantity'] = float(product['d_diff_quantity'])*float(product['unit_rate']) if product.get('unit_rate') else float(product['d_diff_quantity'])
                else:
                    p_args['d_diff_quantity'] = 0

                updated_products.append(p_args)
            
        # 一把更新数据库
        ReceivingDiffProductModel.update_receive_diff_in_all(diff_detail=[diff_args], diff_product_list=updated_products)

        # 记录操作日志
        log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'UPDATE',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
            }
        ReceivingDiffLogModel.create_logs_in_all([log_detail])
        return True
     
    def submit_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)

        if receiving_diff_db.status not in ('INITED','REJECTED'):
            # 状态不允许提交
            raise StatusUnavailable("only INITED/ REJECTED data can be submitted!")

        args = {
                'id':receiving_diff_id,
                'updated_at':datetime.now(),
                'status':'SUBMITTED',
                'updated_by':user_id,
                'review_by':user_id,
                'updated_name':operator_name,
                'auto_confirm_date': datetime.now()+timedelta(hours=2),
                }
        # 一把更新数据库
        ReceivingDiffProductModel.update_receive_diff_in_all([args], [])

        # 记录操作日志
        log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'SUBMIT',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
            }
        ReceivingDiffLogModel.create_logs_in_all([log_detail])
        # 状态变更清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=receiving_diff_db.received_by,
                                         doc_type="receiving_diff"))
        # PDA消息推送
        MessageServicePub.pub_message_service(partner_id=partner_id,
                                                    user_id=user_id,
                                                    scope_id=1,
                                                    store_id=receiving_diff_db.received_by,
                                                    source_root_id=receiving_diff_db.receiving_id,
                                                    source_id=receiving_diff_db.id,
                                                    source_type="REC_DIFF",
                                                    action="SUBMITTED",
                                                    ref_source_id=receiving_diff_db.receiving_id,
                                                    ref_source_type="RECEIVING",
                                                    ref_action="INITED",
                                                    content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name}
                                                    )  
        return True

    def confirm_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        count, receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id=receiving_diff_id, partner_id=partner_id)
        if not receiving_diff_product_db:
            # 无差异商品
            raise NoResultFoundError("no diff products found!")

        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)

        # 提交状态
        if receiving_diff_db.status != 'SUBMITTED':
            # 状态不允许提交
            raise StatusUnavailable("only SUBMIITED data can be confirmed!")   
        
        args = {            'id':receiving_diff_id,
                            'updated_at':datetime.now(),
                            'status':'CONFIRMED',
                            'has_checked':True,
                            'updated_by':user_id,
                            'review_by':user_id,
                            'updated_name':operator_name
                        }
        # 反写更新收货单数据库
        receipt_service.deal_receive_by_id(receive_id=receiving_diff_db.request_id, action='DIFF_CONFIRM', 
                                                    partner_id=partner_id, user_id=user_id)
        # 一把更新数据库
        ReceivingDiffProductModel.update_receive_diff_in_all([args], [])

        
        # 按照仓库承担数量生成差异退货单
        d_diff_pargs = []
        d_flag = False
        for receiving_diff_product in receiving_diff_product_db:

            if receiving_diff_product.d_diff_quantity:
                d_flag = True
                parg = {
                            'product_id':receiving_diff_product.product_id,
                            'product_name':receiving_diff_product.product_name,
                            'product_code':receiving_diff_product.product_code,
                            'unit_id':receiving_diff_product.unit_id,
                            'unit_spec':receiving_diff_product.unit_spec,
                            'unit_name':receiving_diff_product.unit_name,
                            'quantity':receiving_diff_product.d_diff_quantity,
                            'reason_type':'收货差异生成',
                            'tax_rate': receiving_diff_product.tax_rate,
                            'price': receiving_diff_product.cost_price,
                            'price_tax': receiving_diff_product.tax_price
                        }
                d_diff_pargs.append(parg)

        if d_flag:
            order_json = {
                    'demand_order_code': receiving_diff_db.master_code,
                    'rec_code': receiving_diff_db.receiving_code,
                    'diff_code': receiving_diff_db.code
                    }

            returns_service.create_diff_return(return_by=receiving_diff_db.received_by, 
                                    return_to=receiving_diff_db.delivery_by, 
                                    return_delivery_date=datetime.now(), return_reason='收货差异生成',
                                    product_detail=d_diff_pargs, partner_id=partner_id, user_id=user_id, 
                                    source_id=receiving_diff_db.id, source_code=receiving_diff_db.code,
                                    logistics_type=receiving_diff_db.logistics_type, remark=order_json)
        # 状态变更清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=receiving_diff_db.received_by,
                                         doc_type="receiving_diff"))
        # PDA消息推送
        MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=receiving_diff_db.received_by,
                                                  source_root_id=receiving_diff_db.receiving_id,
                                                  source_id=receiving_diff_db.id,
                                                  source_type="REC_DIFF",
                                                  action="CONFIRMED",
                                                  ref_source_id=receiving_diff_db.id,
                                                  ref_source_type="REC_COLD",
                                                  ref_action="SUBMITTED",
                                                  content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name,}
                                                  )
                
        # 记录操作日志
        log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'CONFIRM',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
                }
        ReceivingDiffLogModel.create_logs_in_all([log_detail])

        return True
        
    def reject_receiving_diff(self, receiving_diff_id, partner_id, user_id, reject_reason=None, attachments=None):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)
        receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id=receiving_diff_id, partner_id=partner_id)
        if not receiving_diff_product_db:
            # 无差异商品
            raise NoResultFoundError("no diff products found!")

        if receiving_diff_db.status != 'SUBMITTED':
            # 状态不允许提交
            raise StatusUnavailable("only SUBMIITED data can be rejected!")  
            
        args = {
                'id':receiving_diff_id,
                'updated_at':datetime.now(),
                'status':'REJECTED',
                'updated_by':user_id,
                'updated_name':operator_name,
                'review_by':user_id,
                'reject_reason':reject_reason,
                'attachments':attachments,
                'auto_confirm_date': datetime.now()+timedelta(hours=2),
            }
        # 一把更新数据库
        ReceivingDiffProductModel.update_receive_diff_in_all([args], [])

        # 记录操作日志
        log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'REJECT',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
            }
        ReceivingDiffLogModel.create_logs_in_all([log_detail])
        # 状态变更清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=receiving_diff_db.received_by,
                                         doc_type="receiving_diff"))
        # PDA消息推送
        MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=receiving_diff_db.received_by,
                                                  source_root_id=receiving_diff_db.receiving_id,
                                                  source_id=receiving_diff_db.id,
                                                  source_type="REC_DIFF",
                                                  action="REJECTED",
                                                  ref_source_id=receiving_diff_db.id,
                                                  ref_source_type="REC_COLD",
                                                  ref_action="SUBMITTED",
                                                  content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name,}
                                                  )
             
        return True
        
    def delete_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)

        if receiving_diff_db.status == 'INITED' or receiving_diff_db.status == 'REJECTED':
            args = {
                'id': receiving_diff_id,
                'updated_at':datetime.now(),
                'status':'DELETED',
                'updated_by':user_id,
                'review_by':user_id,
                'updated_name':operator_name
            }
            # 一把更新数据库
            ReceivingDiffProductModel.update_receive_diff_in_all([args], [])

            # 记录操作日志
            log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'DELETE',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
            }
            ReceivingDiffLogModel.create_logs_in_all([log_detail])
            return True
        
        else:
            # 状态不允许提交
            raise StatusUnavailable("only INITED data can be deleted!")  
    
    def get_unconfirmed_diffs(self, end_hours, end_mins, status, partner_id, user_id):
        # 月底月盘日，在当日12:00PM前自动作业配送差异单
        today = datetime.today()
        start_time = datetime(today.year, today.month, 1, 0, 0)
        end_time = datetime(today.year, today.month, today.day, end_hours, end_mins)

        # 新建\提交状态的配送差异单，自动审核完成
        is_direct=False
        status=['INITED', 'REJECTED']
        diff_dbs = ReceivingDiffModel.get_diffs_by_status(start_time=start_time, end_time=end_time, status=status, 
                                                            is_direct=is_direct, partner_id=partner_id, user_id=user_id)

        for diff_db in diff_dbs:
            message = {}
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            message['diff_id'] = diff_db.id
            message['status'] = diff_db.status
            # public(MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC, message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC,
                                    message=message)
            
    def get_unconfirmed_diffs_over1day(self, overhours, status, partner_id, user_id, max_overdays=None):
        # 收货后24小时，门店没有填写的差异单
        if not max_overdays:
            max_overdays = 30
        start_time = datetime.now()-timedelta(days=max_overdays)
        end_time = datetime.now()-timedelta(hours=overhours)

        # 新建\提交状态的配送差异单，自动审核完成
        is_direct=False
        diff_dbs = ReceivingDiffModel.get_diffs_by_status(start_time=start_time, end_time=end_time, 
                            status=status, is_direct=is_direct, partner_id=partner_id, user_id=user_id)

        for diff_db in diff_dbs:
            message = {}
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            message['diff_id'] = diff_db.id
            message['status'] = diff_db.status
            message['owner'] = 'warehouse'
            logging.info('StartAutoConfirmDiffs: {}'.format(message))
            # public(MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC, message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC,
                                    message=message)

    def get_auto_confirm_data(self, partner_id=None, user_id=None,status=None):
        # 新建\提交状态的配送差异单，自动审核完成
        diff_dbs = ReceivingDiffModel.get_diffs_by_status(status=status, partner_id=partner_id, user_id=user_id, auto_confirm_date=datetime.utcnow())

        for diff_db in diff_dbs:
            message = {}
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            message['diff_id'] = diff_db.id
            message['status'] = diff_db.status
            message['owner'] = 'store'
            logging.info('StartAutoConfirmDiffs: {}'.format(message))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC,
                                    message=message)
        
    def auto_confirm(self, diff_id, status=None, owner=None, partner_id=None, user_id=None):
        if not status:
            raise DataValidationException('状态异常！')
        
        # 新建/驳回状态的配送差异单，默认门店承担全部数量
        # 如果owner='warehouse',仓库承担数量
        if status == 'INITED' or status == 'REJECTED':
            updated_products = []
            count, diff_products = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id=diff_id, partner_id=partner_id)
            for diff_product_db in diff_products:
                pargs = {
                    'id':diff_product_db.id,
                    's_diff_quantity':0 if owner=='warehouse' else diff_product_db.diff_quantity,
                    'd_diff_quantity':diff_product_db.diff_quantity if owner=='warehouse' else 0,
                    'updated_at':datetime.now(),
                    'updated_by':user_id
                }
                updated_products.append(pargs)
            diff_args = {'id':diff_id, 'status':'SUBMITTED'}
            # 一把更新数据库
            ReceivingDiffProductModel.update_receive_diff_in_all(diff_detail=[diff_args], diff_product_list=updated_products)
            res = self.confirm_receiving_diff(diff_id, partner_id, user_id)
            return res

        elif status == 'SUBMITTED':
            res = self.confirm_receiving_diff(diff_id, partner_id, user_id)
            return res


    #---ForReceiptStart---#
    # 创建收货差异单
    def create_receive_diff(self, receiving_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_db = receipt_service.get_receive_by_id(receive_id=receiving_id, partner_id=partner_id, user_id=user_id)
        entity = receipt_service.get_receive_products_by_receive_id(receive_id=receiving_id, partner_id=partner_id, user_id=user_id)
        confirmed_products = entity.rows

        main_branch_type_dict = {
            'W': 'WAREHOUSE',
            'S': 'STORE',
            'M': 'MACHINING'
        }

        # 新建收货差异单单据明细
        diff_id = gen_snowflake_id()
        args = {
                'id': diff_id,
                'code':Supply_doc_code.get_code_by_type('REC_DIFF',partner_id, None),
                'receiving_id':receiving_id,
                'received_by':receiving_db.receive_by,
                'delivery_by':receiving_db.delivery_by,
                'receiving_code':receiving_db.code,
                'master_id':receiving_db.batch_id,
                'master_code':receiving_db.batch_code,
                'status':'INITED',
                'demand_date':receiving_db.demand_date.ToDatetime() if receiving_db.demand_date else None,
                'delivery_date':receiving_db.delivery_date.ToDatetime() if receiving_db.delivery_date else None,
                'receive_date':receiving_db.arrival_date.ToDatetime() if receiving_db.arrival_date else None,
                'review_by':user_id, # ?
                'updated_by':user_id,
                'created_by':user_id,
                'updated_name':operator_name,
                'created_name':operator_name,
                'updated_at':datetime.now(),
                'created_at':datetime.now(),
                'partner_id':partner_id,
                'received_type':receiving_db.batch_type,
                'request_id':receiving_id,
                'logistics_type':receiving_db.distr_type,
                'branch_type': main_branch_type_dict.get(receiving_db.main_branch_type),
                'sub_receive_by': receiving_db.sub_receive_by,
                'auto_confirm_date': datetime.now()+timedelta(hours=2),
            }

        # 新建收货差异单商品明细
        has_diffs = False
        diff_products = []
        for product in confirmed_products:
            product_detail = pb2dict(product) 
            diff_quantity = round((Decimal(
                            product_detail['delivery_quantity']) if product_detail.get('delivery_quantity') else Decimal(
                            0.0)) - (Decimal(
                            product_detail['receive_quantity']) if product_detail.get('receive_quantity') else Decimal(
                            0.0)), 2)
            # 商品无差异   
            if diff_quantity == 0:
                pass
                
            else:
                has_diffs = True
                unit_rate = product_detail.get('unit_rate', 1)
                p_args = {
                    'id': gen_snowflake_id(),
                    'receiving_id': receiving_db.id,
                    'received_by': receiving_db.receive_by,
                    'diff_id': diff_id,
                    'product_id': product_detail.get('product_id'),
                    'material_number': product_detail.get('material_number'),
                    'accounting_unit_id': product_detail.get('accounting_unit_id'),
                    'unit_id': product_detail.get('unit_id'),
                    'product_code': product_detail.get('product_code'),
                    'product_name': product_detail.get('product_name'),
                    'accounting_unit_name': product_detail.get('accounting_unit_name'),
                    'accounting_unit_spec': product_detail.get('accounting_unit_spec'),
                    'unit_name': product_detail.get('unit_name'),
                    'unit_spec': product_detail.get('unit_spec'),
                    'unit_rate': product_detail.get('unit_rate'),
                    'received_accounting_quantity': float(product_detail.get('delivery_quantity', 0)) * unit_rate,
                    'received_quantity': float(product_detail.get('delivery_quantity', 0)),
                    'confirmed_accounting_quantity': float(product_detail.get('receive_quantity', 0)) * unit_rate,
                    'confirmed_quantity': float(product_detail.get('receive_quantity', 0)),
                    'diff_accounting_quantity': float(diff_quantity) * unit_rate,
                    'diff_quantity': diff_quantity,
                    'updated_by': user_id,
                    'created_by': user_id,
                    'updated_name': operator_name,
                    'created_name': operator_name,
                    'updated_at': datetime.now(),
                    'created_at': datetime.now(),
                    'partner_id': partner_id,
                    'sub_receive_by': receiving_db.sub_receive_by,
                    'cost_price': float(product_detail.get('cost_price', 0)),
                    'tax_price': float(product_detail.get('tax_price', 0)),
                    'tax_rate': float(product_detail.get('tax_rate', 0))
                }
                diff_products.append(p_args)
       
        # 整单均无差异：
        if not has_diffs:
            return None

        # 差异单数据插入数据库
        args['product_nums'] = len(diff_products)
        logging.info('create_receive_diff_in_all {}'.format(args))
        ReceivingDiffProductModel.create_receive_diff_in_all(diff_detail=[args], diff_product_list=diff_products)

        # 记录操作日志
        log_detail = {
            'doc_id': diff_id,
            'operation': 'CREATE',
            'success': True,
            'partner_id': partner_id,
            'created_by': user_id,
            'created_at': datetime.now(),
            'created_name': operator_name
        }
        ReceivingDiffLogModel.create_logs_in_all([log_detail])
        return diff_id

    #---ForReceiptEnd---#

receiving_diff_service = ReceivingDiffService()