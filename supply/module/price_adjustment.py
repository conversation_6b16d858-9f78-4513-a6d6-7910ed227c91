# -*- coding: utf8 -*-
import json

from datetime import datetime
from supply import logger
from supply.utils.snowflake import gen_snowflake_id
from supply.model.supply_doc_code import Supply_doc_code
from supply.error.exception import DataValidationException, OrderNotExistException
from supply.client.metadata_service import metadata_service
from supply.model.price_adjustment import price_adjustment_db, PriceAdjustmentOrder
from supply.model.purchase_review import purchase_review_order_db, SendCostCenterBatch
from google.protobuf.timestamp_pb2 import Timestamp
from supply.utils.helper import convert_to_int, MessageTopic, get_product_code_unit_map, get_uuids, convert_to_decimal
from ..utils.encode import CJsonEncoder


class PriceAdjustmentService(object):
    """调价单相关业务逻辑"""
    def __init__(self):
        pass

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    def utctimestamp2datetime(self, timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.utcfromtimestamp(timestamp.seconds)
        return date

    def update_res_datetime2timestamp(self, keys, res: dict):
        """更新查询集中的datetime to Timestamp
        :param keys: 字段值列表 -> (str)
        :param res: 返回查询集 -> (dict)
        """
        if keys:
            for key in keys:
                if key in res.keys():
                    res.update({key: self.get_timestamp(res[key])})
        return res

    def create_price_adjustment(self, review_id, partner_id, user_id):
        """采购复核创建调价单
        review_id 复核单id
        通过复核单查询详情组织参数创建调价单"""
        result = {}
        review_order = purchase_review_order_db.get_purchase_order_by_id(review_id=review_id, partner_id=partner_id)
        if not review_order:
            logger.warning("生成调价单时采购复核单未找到-{}".format(review_id))
            result["result"] = False
            result["msg"] = "Adjustment not found!"
            return result
        if review_order.status != "COMPLETED":
            # 如果复核单状态不是已复核状态不往下进行
            result["result"] = False
            result["msg"] = "Adjustment status invalid!"
            return result
        # 校验一下该复核单是否已经生成调价单
        # adjust_record = price_adjustment_db.get_price_adjustment_by_id(review_id=review_id)
        # if adjust_record:
        #     result["result"] = False
        #     result["msg"] = "Adjustment already created!"
        #     return result
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        adjust_code = Supply_doc_code.get_code_by_type('PRICE_ADJ', partner_id, user_id)
        adjust_date = datetime.utcnow()   # 取当前单utc时间
        adjust_id = gen_snowflake_id()
        adjust_order = dict(
            id=adjust_id,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            batch_id=convert_to_int(review_id),
            batch_code=review_order.order_code,
            branch_type=review_order.branch_type,
            code=adjust_code,
            status="INITED",  # 新建状态
            blending_status="INITED",   # 发票勾兑待勾兑
            order_type="REVIEW_ADJUST",  # 此接口暂时只有复核调价单
            adjust_date=adjust_date,
            branch_id=review_order.received_by,
            branch_code=review_order.received_code,
            branch_name=review_order.received_name,
            supplier_id=review_order.supplier_id,
            supplier_name=review_order.supplier_name,
            supplier_code=review_order.supplier_code,
            sum_price_tax=review_order.sum_price_tax,
            pre_sum_price_tax=review_order.pre_sum_price_tax,
            sum_price=review_order.sum_price,
            pre_sum_price=review_order.pre_sum_price,
            cost_trans_status=0,
            cost_update=0,
            cost_center_id=review_order.cost_center_id,     # 不实时拉取成本中心，要拿原单据的
            company_id=review_order.company_id
        )
        # 根据复核单id拿商品详情
        review_products = purchase_review_order_db.get_review_order_product(partner_id=partner_id,
                                                                            user_id=user_id,
                                                                            order_id=review_id)
        adjust_detail = []
        if isinstance(review_products, list):
            ids = get_uuids(len(review_products))
            for index, product in enumerate(review_products):
                # 未修改商品不能生成调价单
                if product.is_modify != 1:
                    continue
                row = dict(
                    id=ids[index],
                    partner_id=partner_id,
                    created_by=user_id,
                    created_name=username,
                    updated_by=user_id,
                    updated_name=username,
                    adjust_id=adjust_id,
                    adjust_code=adjust_code,
                    product_id=product.product_id,
                    product_code=product.product_code,
                    product_name=product.product_name,
                    product_category_id=product.product_category_id,
                    product_type=product.product_type,
                    unit_id=product.unit_id,
                    unit_name=product.unit_name,
                    unit_spec=product.unit_spec,
                    quantity=product.quantity,
                    actual_quantity=product.actual_quantity,
                    tax_rate=product.tax_rate,
                    price=product.price,
                    pre_price=product.pre_price,
                    sum_price=product.sum_price,
                    pre_sum_price=product.pre_sum_price,
                    price_tax=product.price_tax,
                    pre_price_tax=product.pre_price_tax,
                    sum_price_tax=product.sum_price_tax,
                    pre_sum_price_tax=product.pre_sum_price_tax
                )
                adjust_detail.append(row)
        res = price_adjustment_db.create_price_adjustment(order_data=adjust_order,
                                                          adjust_detail=adjust_detail)

        if res is True:
            result["adjust_code"] = adjust_code
            result["result"] = True
            result["msg"] = "success"
            # 目前生成调价单没有审核过程直接传输成本中心
            message = dict(
                batch_id=adjust_id,
                batch_type="PURCHASE_ADJUST",
                partner_id=partner_id,
                user_id=user_id
            )
            # public(topic=MessageTopic.SEND_TO_COST_CENTER, data=message)
            return result
        else:
            result["result"] = False
            result["msg"] = "Operate DB Failed"
            return result

    def list_price_adjustment(self, request, partner_id, user_id):
        """查询调价单列表"""
        result = {}
        start_date = self.utctimestamp2datetime(request.start_date)
        end_date = self.utctimestamp2datetime(request.end_date)
        order_code = request.order_code
        batch_code = request.batch_code
        status = request.status
        order_type = request.order_type
        branch_type = request.branch_type
        supplier_ids = list(request.supplier_ids) if request.supplier_ids else []
        branch_ids = list(request.branch_ids) if request.branch_ids else []
        supplier_ids = [convert_to_int(s) for s in supplier_ids]
        branch_ids = [convert_to_int(b) for b in branch_ids]
        company_id = convert_to_int(request.company_id)
        blending_status = request.blending_status
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        order = request.order
        sort = request.sort
        query_set = price_adjustment_db.list_price_adjustment(partner_id=partner_id, user_id=user_id,
                                                              start_date=start_date, end_date=end_date,
                                                              order_code=order_code, status=status,
                                                              order_type=order_type, batch_code=batch_code,
                                                              supplier_ids=supplier_ids,
                                                              branch_ids=branch_ids, limit=limit,
                                                              offset=offset, include_total=include_total,
                                                              order=order, sort=sort,
                                                              branch_type=branch_type, company_id=company_id,
                                                              blending_status=blending_status)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            result["total"] = total
        else:
            query_set = query_set
        result_list = []
        if query_set:
            for row in query_set:
                row = row.as_dict()
                # 需要更新datetime的字段
                keys = ["created_at", "updated_at", "adjust_date"]
                row = self.update_res_datetime2timestamp(keys=keys, res=row)
                result_list.append(row)
        result["rows"] = result_list
        return result

    def get_price_adjustment_detail(self, request, partner_id, user_id):
        """
        查询调价单详情两种场景二选一：
        1、调价单页面根据调价单id查询
        2、复核单页面根据调价单code查询
        """
        result = {}
        adjust_id = request.adjust_id
        # review_id = request.review_id
        adjust_code = request.adjust_code
        adjust_order = price_adjustment_db.get_price_adjustment_by_id(partner_id=partner_id,
                                                                      adjust_id=convert_to_int(adjust_id),
                                                                      adjust_code=adjust_code)
        if not adjust_order:
            raise DataValidationException("Adjustment not found！")
        result.update(adjust_order.as_dict())
        # 更新datetime to Timestamp
        keys = ["created_at", "updated_at", "adjust_date"]
        result = self.update_res_datetime2timestamp(keys=keys, res=result)
        adjust_id = adjust_order.id
        adjust_detail = price_adjustment_db.get_price_adjustment_product(partner_id=partner_id,
                                                                         adjust_id=adjust_id)
        result["products"] = []
        if adjust_detail:
            for row in adjust_detail:
                row = row.as_dict()
                keys = ["created_at", "updated_at"]
                row = self.update_res_datetime2timestamp(keys=keys, res=row)
                row["tax"] = convert_to_decimal(row.get('sum_price_tax', 0)) - convert_to_decimal(
                    row.get('sum_price', 0))
                row["pre_tax"] = convert_to_decimal(row.get('pre_sum_price_tax', 0)) - convert_to_decimal(
                    row.get('pre_sum_price', 0))
                result["products"].append(row)
        return result


def deal_to_cost_center_pur_adjust_data(partner_id=None, user_id=None, adjust_id=None):
    """整理给成本中心的复核调价单单据数据"""
    receipt_list = []
    # 校验该订单是否存在
    query_order = price_adjustment_db.get_price_adjustment_by_id(partner_id=partner_id, adjust_id=adjust_id)
    if not query_order:
        return receipt_list
    # 2020-05-15最新修改 rtime传单据调价单调价时间
    rtime = query_order.adjust_date
    from pytz import utc
    rtime = rtime.replace(tzinfo=utc)  # 强制指定utc时间
    # 根据门店/仓库拉成本中心id
    cost_center_id = query_order.cost_center_id
    if not cost_center_id:
        # 门店未关联成本中心就不传给成本中心
        logger.warning("【deal_to_cost_center_pur_adjust_data】该调价单的门店/仓库未关联成本中心 - {}".format(adjust_id))
        return receipt_list
    # 拉取单据对应的商品明细
    product_detail = price_adjustment_db.get_price_adjustment_product(partner_id=partner_id, adjust_id=adjust_id)
    if not product_detail:
        return receipt_list
    product_codes = [p.product_code for p in product_detail]
    # 拉取商品对应的单位信息(使用封装好的接口)
    product_unit_map = get_product_code_unit_map(product_codes=product_codes, partner_id=partner_id,
                                                 user_id=user_id)

    for product in product_detail:
        product_id = convert_to_int(product.product_id)
        product_code = product.product_code
        # 这里取"default"核算单位
        unit_detail = product_unit_map.get(product_code, {})
        if unit_detail.get('default'):
            accounting_unit_id = unit_detail.get("default", {}).get('id')
        else:
            logger.warning("【deal_to_cost_center_pur_adjust_data】该商品没有设置核算单位-{}".format(product_id))
            accounting_unit_id = ""
        adjust_amount = product.pre_sum_price - product.sum_price
        args = {
            # 'requestID': gen_snowflake_id(),  # 唯一请求id
            'MID': product_id,
            'MQuantity': 0,  # 复核单调价格不需要传数量
            'costCenterID': convert_to_int(cost_center_id),   # 成本中心id
            'storeID': 0,
            'partnerID': partner_id,  # partner_id
            'rtime': rtime,           # 这里为了存json未转换Timestamp类型需要后边转换
            'price': adjust_amount,     # 商品调整总价差额(有正负)
            'RID': query_order.id,      # 单据id
            'Status': query_order.status,  # 单据终态
            'category': 7,              # 单据类型 7：采购复核调整金额调价单
            'unit': str(accounting_unit_id),
            'supplyID': query_order.supplier_id,   # 供应商id
            'costUpdate': 0,
            'warehouseID': 0,
            'branchId': query_order.branch_id,      # 组织id
            'branchType': query_order.branch_type   # 组织类型
        }
        receipt_list.append(args)
    # 将待同步的数据缓存在一张表中
    data = dict(
        id=gen_snowflake_id(),
        partner_id=partner_id,
        created_by=user_id,
        updated_by=user_id,
        batch_id=query_order.id,  # 批次id这里取订单id做唯一限制
        batch_type="PURCHASE_ADJUST",
        data=json.dumps({"data": receipt_list}, cls=CJsonEncoder),
        status='INITED'
    )
    SendCostCenterBatch.add_one_record(data=data)
    return receipt_list


price_adjustment_service = PriceAdjustmentService()