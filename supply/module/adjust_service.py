import json
import copy
from datetime import datetime, timedelta
from decimal import Decimal

from supply.utils.helper import convert_to_int, convert_to_datetime, MessageTopic, get_guid, \
    set_model_from_props, convert_to_decimal, get_product_code_unit_map, get_cost_center_map, get_product_unit_map, \
    get_supply_reason_map, get_product_map, get_username_map
from ..model.adjust import AdjustRepository
import logging
from google.protobuf.timestamp_pb2 import Timestamp

from ..model.doc_plan.doc_plan import doc_plan_repository
from ..model.inventory import inventory_repository
from ..utils.adjust_enum import SOURCE_ENUM
from ..client.metadata_service import metadata_service
from supply.module.franchisee.helper import *
from supply.model.supply_doc_code import Supply_doc_code
from ..error.exception import *
from ..client.inventory_service import inventory_service
from ..task.message_service_pub import MessageServicePub
from ..client.bom_service import Bom_service
from ..utils.inventory_enum import ACTION
from supply.utils import pb2dict
from supply.driver.mq import mq_producer
from supply.model.operation_log import TpTransLogModel
from supply import APP_CONFIG
from supply.utils.kit import Kit, KitEnum

class TimestampAndMethodMixin(object):
    def __init__(self):
        self.created_at = None
        self.updated_at = None

    # 需要转时间戳的props
    def props(self):
        pr = {}
        for name in dir(self):
            value = getattr(self, name)
            if not name.startswith('__') and not callable(value) and not name.startswith('_'):
                if isinstance(value, datetime):
                    # value = value - timedelta(hours=8)
                    timestamp = Timestamp()
                    timestamp.FromDatetime(value)
                    pr[name] = timestamp
                else:
                    pr[name] = value
        return pr

    # 不需要转时间戳的props
    def no_timestamp_props(self):
        pr = {}
        for name in dir(self):
            value = getattr(self, name)
            if not name.startswith('__') and not callable(value) and not name.startswith('_'):
                pr[name] = value
        return pr


class Adjust(TimestampAndMethodMixin):
    def __init__(self):
        super(Adjust, self).__init__()
        self.id = None
        self.branch_batch_id = None
        self.schedule_id = None
        self.partner_id = None
        self.user_id = None
        self.adjust_store = None
        self.code = None
        self.reason_type = None
        self.adjust_date = None
        self.status = None
        self.adjust_date = None
        self.status = None
        self.process_status = None
        self.remark = None
        self.request_id = None
        self.receive_id = None
        self.receive_code = None
        self.source = None


class AdjustDetails(TimestampAndMethodMixin):
    def __init__(self):
        super(AdjustDetails, self).__init__()
        self.id = None
        self.branch_batch_id = None
        self.schedule_id = None
        self.adjust_id = None
        self.partner_id = None
        self.user_id = None
        self.adjust_order_number = None
        self.adjust_store = None
        self.adjust_store_secondary_id = None
        self.reason_type = None
        self.code = None
        self.schedule_code = None
        self.adjust_date = None
        self.status = None
        self.process_status = None
        self.created_by = None
        self.updated_by = None
        self.remark = None
        self.request_id = None
        self.created_name = None
        self.updated_name = None
        self.receive_id = None
        self.receive_code = None
        self.schedule_name = None
        self.branch_type = None
        self.source = None
        self.reason_name = None
        self.reject_reason = None
        self.attachments = None
        self.total_amount = None
        self.total_sales_amount = None


class AdjustProduct(TimestampAndMethodMixin):
    def __init__(self):
        super(AdjustProduct, self).__init__()
        self.id = None
        self.partner_id = None
        self.user_id = None
        self.adjust_id = None
        self.product_id = None
        self.product_code = None
        self.product_name = None
        self.unit_id = None
        self.unit_name = None
        self.unit_spec = None
        self.accounting_unit_id = None
        self.accounting_unit_name = None
        self.accounting_unit_spec = None
        self.quantity = None
        self.accounting_quantity = None
        self.confirmed_quantity = None
        self.is_confirmed = None
        self.item_number = None
        self.material_number = None
        self.adjust_store = None
        self.adjust_date = None
        self.reason_type = None
        self.convert_accounting_quantity = None
        self.is_bom = None
        self.position_id = None
        self.sku_remark = None
        self.tax_rate = None
        self.tax_price = None
        self.cost_price = None
        self.amount = None
        self.tax_amount = None
        self.sales_price = None
        self.sales_amount = None


class AdjustService(object):
    """
    报废单业务模块
    """

    def __init__(self):
        self.adjust_repo = AdjustRepository()
        self.allow_minus_inventory = False

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    def get_adjust_by_request_id(self, request_id):
        return self.adjust_repo.get_adjust_by_request_id(request_id)

    def create_adjust_products(self, allow_adjust_products, partner_id=None):
        allow_create_adjust_products = []
        if allow_adjust_products:
            for allow_adjust_product in allow_adjust_products:
                adjust_product_obj = AdjustProduct()
                adjust_product_obj.product_id = allow_adjust_product.product_id
                adjust_product_obj.product_code = allow_adjust_product.product_code
                adjust_product_obj.product_name = allow_adjust_product.product_name
                adjust_product_obj.partner_id = partner_id
                allow_create_adjust_products.append(adjust_product_obj)
        return allow_create_adjust_products

    def auto_create_adjust_products(self, allow_adjust_products, partner_id=None):
        allow_create_adjust_products = []
        if allow_adjust_products:
            for allow_adjust_product in allow_adjust_products:
                adjust_product_obj = AdjustProduct()
                adjust_product_obj.product_id = allow_adjust_product['product_id']
                adjust_product_obj.product_code = allow_adjust_product.get('product_code')
                adjust_product_obj.product_name = allow_adjust_product.get('product_name')
                adjust_product_obj.partner_id = partner_id
                allow_create_adjust_products.append(adjust_product_obj)
        return allow_create_adjust_products

    def auto_valid_adjust_store_product(self, partner_id, user_id, store_id, adjust_date, adjust_products):
        if not adjust_products or not isinstance(adjust_products, list) or len(adjust_products) == 0:
            return []
        store_products_ret = metadata_service.get_attribute_products_by_store_id(store_id=store_id,
                                                                                 partner_id=partner_id,
                                                                                 user_id=user_id)
        store_products = []
        if store_products_ret:
            store_products = store_products_ret['rows']
        if store_products and isinstance(store_products, list) and len(store_products) > 0:
            product_ids = []
            not_allow_ajdust = []
            allow_adjust_products = []
            for store_product in store_products:
                product_ids.append(convert_to_int(store_product['product_id']))

            # 取得所有的商品信息
            for store_product in store_products:
                for adjust_product in adjust_products:
                    # 判断产品是否为区域产品
                    if adjust_product['product_id'] not in (product_ids):
                        not_allow_ajdust.append(adjust_product['product_id'])
                    if adjust_product['product_id'] == convert_to_int(store_product['product_id']):
                        # 门店产品不允许调拨
                        if not store_product.get(
                                'allow_adjust'):
                            not_allow_ajdust.append(adjust_product['product_id'])
            for adjust_product in adjust_products:
                if convert_to_int(adjust_product['product_id']) not in not_allow_ajdust:
                    allow_adjust_products.append(adjust_product)
            # 不允许调整的产品
            if len(not_allow_ajdust) > 0:
                logging.info('不允许调整的产品' + str(not_allow_ajdust))
            return allow_adjust_products
        else:
            return []

    # 收获报废不可报废非报废商品
    def valid_receive_adjust_store_product(self, store_id, adjust_date, adjust_products, partner_id=None, user_id=None):
        if not adjust_products or not isinstance(adjust_products, list) or len(adjust_products) == 0:
            raise ActionException('没有可损耗商品')

        product_ids = [ad.product_id for ad in adjust_products]
        store_products_ret = metadata_service.get_attribute_products_by_store_id(store_id=store_id,
                                                                                 # return_fields='allow_adjust',
                                                                                 product_ids=product_ids,
                                                                                 partner_id=partner_id,
                                                                                 user_id=user_id)
        store_products = []
        if store_products_ret:
            store_products = store_products_ret['rows']
        if store_products and isinstance(store_products, list) and len(store_products) > 0:
            not_allow_ajdust = []
            allow_adjust_products = []
            # 取得所有的商品信息
            for store_product in store_products:
                if not store_product.get('allow_adjust'):
                    not_allow_ajdust.append(int(store_product['product_id']))
            for adjust_product in adjust_products:
                if adjust_product.product_id not in not_allow_ajdust:
                    allow_adjust_products.append(adjust_product)
            # 不允许调整的产品
            if len(not_allow_ajdust) > 0:
                logging.info('不允许调整的产品' + str(not_allow_ajdust))
            return allow_adjust_products
        else:
            return []

    # 检查产品是否为可以调整的商品
    def valid_adjust_store_product(self, store_id, adjust_date, adjust_products, partner_id=None, user_id=None):
        if not adjust_products or not isinstance(adjust_products, list) or len(adjust_products) == 0:
            raise ActionException('没有可损耗商品')

        store_products_ret = metadata_service.get_attribute_products_by_store_id(store_id=store_id,
                                                                                 partner_id=partner_id, user_id=user_id)
        store_products = []
        if store_products_ret:
            store_products = store_products_ret['rows']
        if store_products and isinstance(store_products, list) and len(store_products) > 0:
            product_ids = []
            not_allow_ajdust = []
            allow_adjust_products = []
            for store_product in store_products:
                product_ids.append(convert_to_int(store_product['product_id']))

            # 取得所有的商品信息
            for store_product in store_products:
                for adjust_product in adjust_products:
                    # 判断产品是否为区域产品
                    if adjust_product.product_id not in (product_ids):
                        not_allow_ajdust.append(adjust_product.product_id)
                    if adjust_product.product_id == convert_to_int(store_product['product_id']):
                        # 门店产品不允许调拨
                        if not store_product.get('allow_adjust'):
                            not_allow_ajdust.append(adjust_product.product_id)
            for adjust_product in adjust_products:
                if convert_to_int(adjust_product.product_id) not in not_allow_ajdust:
                    allow_adjust_products.append(adjust_product)
            # 不允许调整的产品
            if len(not_allow_ajdust) > 0:
                logging.info('不允许调整的产品' + str(not_allow_ajdust))
            return allow_adjust_products
        else:
            return []

    # 更新商品单位
    def update_product_unit(self, adjust_products, partner_id=None, user_id=None, is_receive=False,
                            is_pos_adjust=False):
        if not adjust_products or not isinstance(adjust_products, list) or len(adjust_products) == 0:
            return None
        product_ids = []
        for p in adjust_products:
            product_ids.append(int(p.product_id))
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code',
                                                   partner_id=partner_id,
                                                   user_id=user_id)
        units = []
        if units_ret:
            units = units_ret['rows']
        if not units or not isinstance(units, list) or len(units) == 0:
            return None
        dict_unit = {}
        for unit in units:
            dict_unit[str(unit['id'])] = unit
        main_products = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                          return_fields='name,code,bom_type',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        if not main_products:
            return None
        main_products_map = {}
        for main_p in main_products:
            main_products_map[str(main_p.get('id', 0))] = main_p
        for product in adjust_products:
            main_p = main_products_map.get(str(product.product_id))
            if not main_p:
                continue
            product.partner_id = partner_id
            if 'name' in main_p:
                product.product_name = main_p['name']
            if 'code' in main_p:
                product.product_code = str(main_p['code'])
                product.material_number = str(main_p['code'])
            units = main_p['units'] if 'units' in main_p else []
            unit_rate = dict()
            unit_default = {}
            has_bom_unit = False
            for m_unit in units:
                if isinstance(m_unit, dict):
                    if m_unit.get('default'):
                        unit_default = m_unit
                    if m_unit.get('bom'):
                        has_bom_unit = True
                    unit_rate[int(m_unit.get('id'))] = convert_to_decimal(round(m_unit.get('rate', 0), 6))
            # bom商品必须配置配方单位
            if main_p.get('bom_type') == "MANUFACTURE" and has_bom_unit is False:
                raise DataValidationException("bom商品必须有配方单位-{}！".format(product.product_code))

            if str(product.unit_id) in dict_unit:
                if 'name' in dict_unit[str(product.unit_id)]:
                    product.unit_name = \
                        dict_unit[str(product.unit_id)]['name']
                if 'code' in dict_unit[str(product.unit_id)]:
                    product.unit_spec = \
                        dict_unit[str(product.unit_id)]['code']

            product.accounting_unit_id = convert_to_int(unit_default.get('id'))
            accounting_unit = dict_unit.get(str(product.accounting_unit_id))
            if accounting_unit and isinstance(accounting_unit, dict):
                product.accounting_unit_name = accounting_unit.get('name')
                product.accounting_unit_spec = accounting_unit.get('spec')
            # 核算数量转换
            account_rate = unit_rate.get(int(product.unit_id))
            product.accounting_quantity = (convert_to_decimal(product.quantity) * account_rate).quantize(
                Decimal('0.********')) if account_rate else Decimal(0)

        dict_product = {}
        if is_pos_adjust is True:
            # Pos端报废按商品 + 报废原因 + sku合并
            for p in adjust_products:
                map_key = hash(str(p.product_id) + str(p.reason_type) + str(p.sku_remark))
                current_product = dict_product.get(map_key)
                if current_product:
                    current_product.quantity += p.quantity
                    current_product.accounting_quantity += p.accounting_quantity
                    if p.id:
                        current_product.id = p.id
                else:
                    dict_product[map_key] = p
        else:
            for p in adjust_products:
                map_key = str(p.product_id) + str(p.reason_type)
                current_product = dict_product.get(map_key)
                if current_product:
                    current_product.quantity += p.quantity
                    current_product.accounting_quantity += p.accounting_quantity
                    if p.id:
                        current_product.id = p.id
                else:
                    dict_product[map_key] = p
        return list(dict_product.values())

    # 更新仓库使用的商品单位(订货单位)
    def update_product_order_unit(self, adjust_products, partner_id=None, user_id=None, is_receive=False,
                                  is_pos_adjust=False):
        if not adjust_products or not isinstance(adjust_products, list) or len(adjust_products) == 0:
            return None
        product_ids = []
        for p in adjust_products:
            product_ids.append(int(p.product_id))
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code',
                                                   partner_id=partner_id,
                                                   user_id=user_id)
        units = []
        if units_ret:
            units = units_ret['rows']
        if not units or not isinstance(units, list) or len(units) == 0:
            return None
        dict_unit = {}
        for unit in units:
            dict_unit[str(unit['id'])] = unit
        main_products_ret = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                              return_fields='name,code,bom_type',
                                                              partner_id=partner_id, user_id=user_id)
        main_products = []
        if main_products_ret:
            main_products = main_products_ret['rows']
        if not main_products or not isinstance(main_products, list) or len(main_products) == 0:
            return None
        main_products_map = {}
        for main_p in main_products:
            main_products_map[str(main_p.get('id', 0))] = main_p
        # for main_p in main_products:
        for product in adjust_products:
            main_p = main_products_map.get(str(product.product_id))
            if not main_p:
                continue
            product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
            product.partner_id = partner_id
            if product_id == product.product_id:
                if 'name' in main_p:
                    product.product_name = main_p['name']
                if 'code' in main_p:
                    product.product_code = str(main_p['code'])
                    product.material_number = str(main_p['code'])
                units = main_p['units'] if 'units' in main_p else []
                unit_current = {}
                unit_order = {}
                unit_default = {}
                for m_unit in units:
                    if isinstance(m_unit, dict):
                        if m_unit.get('order'):
                            unit_order = m_unit
                            product.unit_id = int(m_unit.get('id'))
                            unit_current = m_unit
                        if m_unit.get('default'):
                            unit_default = m_unit
                if product.unit_id:
                    if str(product.unit_id) in dict_unit:
                        if 'name' in dict_unit[str(product.unit_id)]:
                            product.unit_name = \
                                dict_unit[str(product.unit_id)]['name']
                        if 'code' in dict_unit[str(product.unit_id)]:
                            product.unit_spec = \
                                dict_unit[str(product.unit_id)]['code']
                    if str(product.unit_id) == unit_default.get('id'):
                        # 自动接口没有数量
                        if product.quantity:
                            product.accounting_quantity = (convert_to_decimal(product.quantity)).quantize(
                                Decimal('0.********'))  # 最终数量记得保留6位小数
                        product.accounting_unit_id = convert_to_int(unit_default['id'])
                        if str(product.unit_id) in dict_unit:
                            if 'name' in dict_unit[str(product.unit_id)]:
                                product.accounting_unit_name = \
                                    dict_unit[str(product.unit_id)]['name']
                            if 'code' in dict_unit[str(product.unit_id)]:
                                product.accounting_unit_spec = \
                                    dict_unit[str(product.unit_id)]['code']
                    elif 'rate' in unit_current and unit_current['rate'] and not is_receive:
                        # 自动接口没有数量
                        if product.quantity:
                            product.accounting_quantity = (convert_to_decimal(product.quantity) * convert_to_decimal(
                                round(unit_current['rate'], 8))).quantize(Decimal('0.********'))
                        product.accounting_unit_id = convert_to_int(unit_default['id'])

                        if str(unit_default['id']) in dict_unit:
                            if 'name' in dict_unit[str(unit_default['id'])]:
                                product.accounting_unit_name = \
                                    dict_unit[str(unit_default['id'])]['name']
                            if 'code' in dict_unit[str(unit_default['id'])]:
                                product.accounting_unit_spec = \
                                    dict_unit[str(unit_default['id'])]['code']
                    else:
                        product.accounting_quantity = convert_to_decimal(product.quantity).quantize(
                            Decimal('0.********'))
                        product.accounting_unit_id = product.unit_id

                    # 收获差异的报废单由订货单位转为bom单位数量
                    if is_receive:
                        if product.quantity:
                            # 先算出核算数量
                            product.accounting_quantity = (convert_to_decimal(
                                product.quantity) * convert_to_decimal(round(unit_order['rate'], 8))).quantize(
                                Decimal('0.********')) if unit_order.get('rate') else product.quantity
                            # # 转为配方单位数量
                            # product.quantity = round(float(
                            #     product.accounting_quantity) / float(
                            #     unit_bom['rate']), 3) if unit_bom.get('rate') else product.accounting_quantity

                            product.accounting_unit_id = convert_to_int(unit_default['id'])
                            if str(unit_default['id']) in dict_unit:
                                if 'name' in dict_unit[str(unit_default['id'])]:
                                    product.accounting_unit_name = \
                                        dict_unit[str(unit_default['id'])]['name']
                                if 'code' in dict_unit[str(unit_default['id'])]:
                                    product.accounting_unit_spec = \
                                        dict_unit[str(unit_default['id'])]['code']

        dict_product = {}
        if is_pos_adjust is True:
            # Pos端报废按商品 + 报废原因 + sku合并
            for p in adjust_products:
                map_key = hash(str(p.product_id) + str(p.reason_type) + str(p.sku_remark))
                current_product = dict_product.get(map_key)
                if current_product:
                    current_product.quantity += p.quantity
                    current_product.accounting_quantity += p.accounting_quantity
                    if p.id:
                        current_product.id = p.id
                else:
                    dict_product[map_key] = p

        else:
            for p in adjust_products:
                map_key = str(p.product_id) + str(p.reason_type)
                current_product = dict_product.get(map_key)
                if current_product:
                    current_product.quantity += p.quantity
                    current_product.accounting_quantity += p.accounting_quantity
                    if p.id:
                        current_product.id = p.id
                else:
                    dict_product[map_key] = p
        return list(dict_product.values())

    # 检查商品调整是否超出库存 - ******** - 增加业务配置项
    def check_adjust_inventory(self, adjust_id, partner_id, user_id, action):
        adjust_products = self.adjust_repo.list_store_adjust_product(adjust_id=adjust_id, partner_id=partner_id)
        list_adjust_products = []
        if isinstance(adjust_products, tuple):
            total = adjust_products[0]
            adjust_products = adjust_products[1]
            store_id = adjust_products[0].get("adjust_store")
        else:
            store_id = adjust_products[0].get("adjust_store")
            total = len(adjust_products)
        if not adjust_products or total == 0:
            logging.info("{}报废单没有商品".format(adjust_id))
            return True
        list_adjust_products = adjust_products
        # 获取单据类型
        adjust = self.adjust_repo.list_adjust(id=adjust_id)
        # 检查业务配置-是否需要进行负库存校验
        need_check_map = metadata_service.get_neg_inv_config(partner_id=partner_id, user_id=user_id,
                                                             domain="boh.store.adjust",
                                                             store_id=store_id)
        if not need_check_map.get(action, False) and adjust and adjust[0].branch_type in ["STORE","FRS_STORE"]:
            return True
        allow_neg_inv = metadata_service.get_business_extra_config(
            partner_id=partner_id, user_id=user_id,
            domain="boh.store.adjust").get("allow_neg_inv")

        if allow_neg_inv and allow_neg_inv == True and adjust and adjust[0].branch_type not in ["STORE",
                                                                                                "FRS_STORE"]:  # 允许负库存
            return True
        # 配方属性=现做BOM 商品列表获取
        is_bom_products_list = []
        product_ids = []
        for p in adjust_products:
            product_ids.append(p.get('product_id'))
        product_filters = {"bom_type__in": ["MANUFACTURE"]}
        product_fields = ["product_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            return_fields=['id'],
            product_filters=product_filters,
            include_product_fields=product_fields,
            store_id=store_id,
            product_ids=product_ids,
            partner_id=partner_id,
            user_id=user_id
        )
        if ret:
            is_bom_products_list = [int(data.get('product_id', 1)) for data in ret.get('rows')]

        inventory_map = inventory_service.get_products_inventory_by_branch_id(branch_id=store_id,
                                                                              partner_id=partner_id,
                                                                              user_id=user_id)
        list_error = []
        over_product_id_list = []
        for p in list_adjust_products:
            inv = inventory_map.get(str(p.get("product_id")))
            # 现在bom商品不校验库存
            if int(p.get("product_id", 0)) in is_bom_products_list:
                pass
            else:
                accounting_quantity = Decimal(p.get('accounting_quantity', 0)).quantize(Decimal('0.********'))
                if inv:
                    quantity_avail = Decimal(inv.get('quantity_avail', 0)).quantize(Decimal('0.********'))
                    if accounting_quantity > 0 and quantity_avail < accounting_quantity:
                        logging.info("check_adjust_inventory_err {}inv:{}less_than{}".format(p.get("product_name", ""),
                                                                                             quantity_avail,
                                                                                             accounting_quantity))
                        list_error.append(p.get("product_name", ""))
                        over_product_id_list.append(str(p.get("product_id")))
                else:
                    logging.info("check_adjust_inventory_err {}inv:0 less_than{}".format(p.get("product_name", ""),
                                                                                         accounting_quantity))
                    list_error.append(p.get("product_name", ""))
                    over_product_id_list.append(str(p.get("product_id")))
        if len(list_error) > 0:
            raise AdjustInventoryException("报废数量不能超出实时库存:" + '\n' + ','.join(list_error),
                                           detail=over_product_id_list)

    # 检查商品调整是否超出库存
    def check_inventory(self, store_id, adjust_products, partner_id, user_id):
        if adjust_products is None or not isinstance(adjust_products, list) or len(adjust_products) == 0:
            return
        product_ids = set([product.product_id for product in adjust_products])
        # product_filters = {"product_type__in": ["SEMI-FINISHED", "FINISHED"]}
        product_fields = ["product_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            return_fields=['id'],
            # product_filters=product_filters,
            include_product_fields=product_fields,
            store_id=store_id,
            product_ids=product_ids,
            partner_id=partner_id,
            user_id=user_id
        )
        is_bom_products_list = []
        if ret:
            is_bom_products_list = [int(data.get('product_id', 1)) for data in ret.get('rows')]
        print('is_bom_products_list', is_bom_products_list)

        inventory_map = inventory_service.get_products_inventory_by_branch_id(branch_id=store_id,
                                                                              partner_id=partner_id,
                                                                              user_id=user_id)
        list_error = []
        for p in adjust_products:
            is_exsit = False
            inv = inventory_map.get(str(p.product_id))
            if inv:
                is_exsit = True
                if p.accounting_quantity > 0 and inv.get('quantity_avail') < p.accounting_quantity:
                    # 半成品不验证
                    if p.product_id not in is_bom_products_list:
                        list_error.append(str(p.product_name))
            # 半成品不验证
            if p.product_id in is_bom_products_list:
                is_exsit = True
            if not is_exsit and p.quantity > 0:
                list_error.append(str(p.product_name))
        if len(list_error) > 0:
            raise AdjustInventoryException("商品报废超出库存:" + '\n' + ','.join(list_error))

    def list_store_adjust(self, partner_id, store_ids=None, start_date=None, end_date=None, limit=None, offset=None,
                          include_total=False, status=None, reason_type=None, code=None, order=None, sort=None,
                          branch_type=None, user_id=None, sources=None, ids=None, is_mobile=False, product_ids=None):
        res = dict()
        if partner_id and not isinstance(partner_id, int):
            partner_id = convert_to_int(partner_id)
        if store_ids and isinstance(store_ids, list) and len(store_ids) > 0:
            store_ids = [convert_to_int(_id) for _id in store_ids]
        else:
            store_ids = None
        if isinstance(ids, list) and len(ids) > 0:
            ids = [convert_to_int(_id) for _id in ids]
        else:
            ids = None
        if start_date and not isinstance(start_date, datetime):
            start_date = convert_to_datetime(start_date)
        if end_date and not isinstance(end_date, datetime):
            end_date = convert_to_datetime(end_date)
        if limit and not isinstance(limit, int):
            limit = convert_to_int(limit)
        if offset and not isinstance(offset, int):
            offset = convert_to_int(offset)

        adjust_detail_list = self.adjust_repo.list_store_adjust(partner_id=partner_id, store_ids=store_ids,
                                                                status=status,
                                                                reason_type=reason_type, start_date=start_date,
                                                                end_date=end_date, limit=limit, offset=offset,
                                                                include_total=include_total, code=code, order=order,
                                                                sort=sort, ids=ids, branch_type=branch_type,
                                                                sources=sources, product_ids=product_ids)

        if include_total:
            res['total'] = adjust_detail_list[0]
            res['rows'] = adjust_detail_list[1]
        else:
            res['rows'] = adjust_detail_list
        if not res['rows']:
            return res
        # 给移动端列表拼接商品名称
        if is_mobile is True:
            adjust_ids = [t.get('id') for t in res['rows']]
            pro_names = self.adjust_repo.query_product_name_by_adjust_ids(adjust_ids, partner_id)
            pro_names_map = {}
            if pro_names:
                for p in pro_names:
                    if p[0] in pro_names_map.keys():
                        if len(pro_names_map[p[0]]) < 5:
                            pro_names_map[p[0]].append(p[1])
                    else:
                        pro_names_map[p[0]] = [p[1]]
            res["pro_names_map"] = pro_names_map
            # 查询报废单明细看是否为空
            adjust_qty_set = self.adjust_repo.query_adjust_product_sum_qty(adjust_ids=adjust_ids, partner_id=partner_id)
            adjust_qty_map = {}
            if adjust_qty_set:
                for s in adjust_qty_set:
                    adjust_qty_map[s[0]] = False if s[1] else True
            for row in res['rows']:
                row['is_empty'] = adjust_qty_map.get(row.get('id'), True)
        reason_map = get_supply_reason_map(_type="ADJUST", partner_id=partner_id, user_id=user_id)


        distrcenter_ids = []
        for q in res['rows']:
            distrcenter_ids.append(int(q['adjust_store']))
        currencys = dict()
        distrcenterRet = metadata_service.list_entity(schema_name="distrcenter", ids=distrcenter_ids,
                                                      return_fields="",
                                                      relation="all",
                                                      partner_id=partner_id,
                                                      user_id=user_id).get('rows', [])
        logging.info("list_entity-distrcenter----{}".format(distrcenterRet))
        distrcenter_company = dict()
        if len(distrcenterRet) > 0:
            company_ids = []
            for distrcenter in distrcenterRet:
                company_id = distrcenter['fields'].get('relation', {}).get('company_info')
                company_ids.append(int(company_id))

                distrcenter_company[int(distrcenter['id'])] = int(company_id)
            if company_ids:
                companyRet = metadata_service.list_entity(schema_name="company-info",
                                                          ids=company_ids,
                                                          return_fields="currency",
                                                          relation="",
                                                          partner_id=partner_id,
                                                          user_id=user_id).get('rows', [])
                logging.info("list_entity-company----{}".format(companyRet))
                if len(companyRet) > 0:
                    for company in companyRet:
                        currencys[int(company['id'])] = company['fields'].get('currency', '')

        for row in res['rows']:
            company_id = distrcenter_company.get(int(q['adjust_store']))
            if currencys.get(company_id):
                row['currency'] = currencys.get(company_id)
            if row.get('attachments'):
                attachments = json.loads(row.get('attachments'))
                row['attachments'] = attachments.get('attachments', [])
            row['reason_name'] = reason_map.get(row.get('reason_type'))
        return res

    def attach_adjust_plan_info(self, query_rows: list, partner_id=None, ):
        """给报废列表查询结果构建计划相关的信息"""
        plan_ids = [row.get('schedule_id') for row in query_rows if row.get('schedule_id')]
        control_status_list = doc_plan_repository.list_valid_control_status(partner_id=partner_id, plan_ids=plan_ids)
        plan_status_map = dict()
        # 一个计划不只有一条计划变更记录
        for c in control_status_list:
            row = dict(
                start_status=str(c.start_status).split(',') if c.start_status else [],
                end_status=c.end_status,
                time=c.time,
                time_around=c.time_around,
                doc_filter=c.doc_filter
            )
            if c.plan_id in plan_status_map.keys():
                plan_status_map[c.plan_id].append(row)
            else:
                plan_status_map[c.plan_id] = [row]
        for row in query_rows:
            plan_id = row.get('schedule_id')
            if not plan_id:
                continue
            target_date = row.get("adjust_date")
            if isinstance(target_date, Timestamp):
                target_date = datetime.fromtimestamp(target_date.seconds)
                target_date = target_date + timedelta(hours=8)  # 报废日期要按实际业务日期来算，数据库存的是utc

            plan_data = plan_status_map.get(plan_id)
            # 需要确认多条计划变更记录截止时间显示哪个
            if plan_data:
                for plan in plan_data:
                    time_around = plan.get("time_around")
                    if time_around == 'Yesterday':
                        end_date = target_date - timedelta(days=1)
                    elif time_around == "Today":
                        end_date = target_date
                    elif time_around == 'Tomorrow':
                        end_date = target_date + timedelta(days=1)
                    else:
                        end_date = target_date
                    time = plan.get('time')
                    if isinstance(time, str):
                        datetime_local = datetime.strptime(str(end_date)[:10] + " " + time,
                                                           "%Y-%m-%d %H:%M:%S") + timedelta(hours=8)
                        end_time = datetime.strptime(str(end_date)[:10] + " " + str(datetime_local)[10:],
                                                     "%Y-%m-%d %H:%M:%S") - timedelta(hours=8)
                        end_time = self.get_timestamp(end_time)
                    else:
                        end_time = time
                    plan['time'] = end_time
            row['status_plan'] = plan_data
        return query_rows

    # 查询调整单所有的商品
    def list_store_adjust_product(self, adjust_id, product_ids=None, limit=-1, offset=0,
                                  include_total=False, partner_id=None, user_id=None, branch_type=None, branch_id=None,
                                  status=None):
        if not adjust_id:
            return None
        else:
            adjust_id = convert_to_int(adjust_id)
        list_adjust = self.adjust_repo.list_store_adjust_product(adjust_id=adjust_id,
                                                                 limit=limit, partner_id=partner_id,
                                                                 offset=offset, include_total=include_total)
        if isinstance(list_adjust, tuple):
            total = list_adjust[0]
            list_product = list_adjust[1]
        else:
            list_product = list_adjust
            total = 0
        if not list_product or len(list_product) == 0:
            return None
        product_ids = [p.get('product_id') for p in list_product]
        product_map = get_product_map(product_ids=product_ids, return_fields='id,model_name',
                                      partner_id=partner_id,
                                      user_id=user_id)
        if (branch_type == "FRS_STORE" and branch_id is not None):
            product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                store_id=branch_id, user_id=user_id, partner_id=partner_id, product_ids=product_ids,
                include_sales_price=True)

        for row in list_product:
            product = product_map.get(str(row.get('product_id')))
            if (branch_type == "FRS_STORE" and branch_id is not None and status is not None and status not in ["INITED",
                                                                                                               "REJECTED"]):
                # 前端需要使用报废单上保存的旧价格计算并显示金额
                update_adjust_tax_price_tax_rate(product_unit_adjust_tax_price_map=product_unit_adjust_tax_price_map,
                                                 product_unit_adjust_tax_ratio_map=product_unit_adjust_tax_ratio_map,
                                                 product_id=row.get('product_id'),
                                                 tax_price=row.get('tax_price'), tax_rate=row.get('tax_rate'),
                                                 product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map,
                                                 sales_price=row.get('sales_price'))

            if isinstance(product, dict):
                units = product.get('units', [])
                for u in units:
                    u['id'] = int(u.get('id'))
                    if (branch_type == "FRS_STORE" and branch_id is not None):
                        fill_adjust_tax_price_tax_rate(u, product_unit_adjust_tax_price_map,
                                                       product_unit_adjust_tax_ratio_map, row.get('product_id'),
                                                       Decimal(u.get("unit_rate", 1)),
                                                       product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                row['units'] = units
                row['model_name'] = product.get('model_name')
        if include_total:
            return total, list_product
        return list_product

    # 取得调整单详细
    def list_store_adjust_by_id(self, adjust_id, partner_id, user_id, is_detail=False):
        adjust_obj = self.adjust_repo.store_adjust_by_id(adjust_id, is_detail, partner_id=partner_id)
        reason_map = get_supply_reason_map(_type="ADJUST", partner_id=partner_id, user_id=user_id)
        if adjust_obj:
            if adjust_obj.attachments:
                attachments = json.loads(adjust_obj.attachments)
                adjust_obj.attachments = attachments.get('attachments', [])
            adjust_obj.reason_name = reason_map.get(adjust_obj.reason_type)
        return adjust_obj

    # 确认前获取折算bom数量
    def get_convert_quantity_map(self, adjust_obj, list_adjust_product, partner_id, user_id):
        product_ids = []
        adjust_bom_report_list = []
        for product in list_adjust_product:
            if product.get('quantity'):
                product_ids.append(int(product.get('product_id')))

        product_ids = set(product_ids)
        product_fields = ["product_type", "bom_type", "sale_type"]
        ret = metadata_service.get_attribute_products_by_store_id(
            return_fields=['id'],
            # product_filters=product_filters,
            include_product_fields=product_fields,
            store_id=adjust_obj.adjust_store,
            product_ids=product_ids,
            partner_id=partner_id,
            user_id=user_id
        )
        product_unit_map = get_product_unit_map(product_ids=product_ids, partner_id=partner_id, user_id=user_id)
        is_bom_products_list = []
        not_has_bom_products_list = []
        if ret:
            for p in ret['rows']:
                if p.get('bom_type') == "MANUFACTURE":
                    is_bom_products_list.append(int(p.get('product_id', 1)))
        bom_products_map = {}

        for product in list_adjust_product:
            line_id = get_guid()
            accounting_quantity = product['accounting_quantity'] if product.get('accounting_quantity') else 0
            quantity = product['quantity'] if product.get('quantity') else 0
            if int(product.get('product_id')) in is_bom_products_list and accounting_quantity:
                # 拆解bom成品需要转为配方单位数量
                bom_unit = product_unit_map.get(int(product.get('product_id')), {}).get('bom')
                bom_quantity = 0
                if not bom_unit:
                    raise DataValidationException("Product not bom unit-{}!".format(product.get('product_code')))
                if isinstance(bom_unit, dict):
                    bom_unit_rate = convert_to_decimal(bom_unit.get('rate', 1))
                    bom_quantity = convert_to_decimal(accounting_quantity) / bom_unit_rate
                options = []
                if product.get('sku_remark'):
                    sku_remark = json.loads(product.get('sku_remark'))
                    if isinstance(sku_remark, dict):
                        sku_remark_list = sku_remark.get('sku_remark', [])
                        for sku in sku_remark_list:
                            option = dict(
                                code=sku.get('name', {}).get('code'),
                                tags=[dict(code=sku.get('values', {}).get('code'))]
                            )
                            options.append(option)
                param_dict = dict(
                    request_id=get_guid(),
                    store_id=adjust_obj.adjust_store,
                    store_code=None,
                    sales_date=str(datetime.now()),
                    biz_code="ADJUST",
                    biz_no=str(adjust_obj.id),
                    product_id=product['product_id'],
                    product_code=None,
                    product_qty=abs(bom_quantity),
                    options=options if options else None,
                    partner_id=partner_id,
                    user_id=user_id
                )
                ret = Bom_service.get_bom(**param_dict)
                if not ret:
                    continue
                logging.info('ret-----------------------' + str(ret))
                logging.info('param_dict-----------------------' + str(param_dict))
                position = product.get('position_id')
                if not position:
                    position_id = 0
                else:
                    position_id = str(position)
                if ret:
                    if int(ret.get('request_id')) == int(param_dict['request_id']) and ret.get('bom'):
                        boms = ret["bom"]
                        # 原料剔除不可报废的商品
                        bom_product_ids = [int(bom_.get('product_id')) for bom_ in boms]
                        list_store_product_ret = metadata_service.get_attribute_products_by_store_id(
                            return_fields='allow_adjust',
                            product_ids=bom_product_ids,
                            store_id=adjust_obj.adjust_store,
                            filters={"allow_adjust": True},
                            partner_id=partner_id,
                            product_filters={"status__in": ["ENABLED"]},
                            user_id=user_id,
                            check_division=False
                        ).get('rows', [])
                        can_adjust_bom_product = []
                        if len(list_store_product_ret) > 0:
                            for pr in list_store_product_ret:
                                # 费用化属性商品不产生报废单
                                if pr.get('allow_adjust'):
                                    can_adjust_bom_product.append(int(pr.get('product_id')))
                        if len(can_adjust_bom_product) == 0:
                            # 全部原料都不可报废，标记该商品
                            not_has_bom_products_list.append(int(product.get('product_id')))

                        # 相同line_id，line_m_qty物料数量只统计一次
                        line_bom_product_flag = {}
                        for bom in boms:
                            product_id = int(bom.get('product_id'))
                            if line_bom_product_flag.get(product_id):
                                line_bom_product_flag[product_id] = 'True'
                            else:
                                line_bom_product_flag[product_id] = 'False'
                            has_same_line_flag = line_bom_product_flag[product_id]
                            # 原料剔除不可报废的商品
                            if product_id not in can_adjust_bom_product:
                                continue
                            qty = bom.get('qty') if bom.get('qty') else 0
                            if accounting_quantity < 0:
                                qty = -qty
                            key = str(position_id) + ',' + str(product_id)
                            if bom_products_map.get(key):
                                bom_products_map[key] += qty
                            else:
                                bom_products_map[key] = qty
                            adjust_bom_report = dict(
                                adjust_id=adjust_obj.id,
                                store_id=adjust_obj.adjust_store,
                                product_id=int(product.get('product_id')),
                                line_id=line_id,
                                line_m_qty='0' if has_same_line_flag == 'True' else str(quantity),
                                line_m_accounting_qty='0' if has_same_line_flag == 'True' else str(accounting_quantity),
                                unit_id=product.get('unit_id'),
                                unit_name=product.get('unit_name'),
                                unit_spec=product.get('unit_spec'),
                                accounting_unit_id=product.get('accounting_unit_id'),
                                accounting_unit_name=product.get('accounting_unit_name'),
                                accounting_unit_spec=product.get('accounting_unit_spec'),

                                bom_product_id=int(bom.get('product_id')),
                                accounting_product_qty=str(accounting_quantity),
                                product_qty=str(quantity),
                                reason_type=product.get('reason_type'),
                                qty=str(qty),
                                position_id=product.get('position_id')
                            )
                            adjust_bom_report_list.append(adjust_bom_report)
                    else:
                        adjust_bom_report = dict(
                            adjust_id=adjust_obj.id,
                            line_id=line_id,
                            line_m_qty=str(quantity),
                            line_m_accounting_qty=str(accounting_quantity),
                            store_id=adjust_obj.adjust_store,
                            product_id=int(product.get('product_id')),
                            accounting_product_qty=str(accounting_quantity),
                            product_qty=str(quantity),
                            unit_id=product.get('unit_id'),
                            unit_name=product.get('unit_name'),
                            unit_spec=product.get('unit_spec'),
                            accounting_unit_id=product.get('accounting_unit_id'),
                            accounting_unit_name=product.get('accounting_unit_name'),
                            accounting_unit_spec=product.get('accounting_unit_spec'),
                            reason_type=product.get('reason_type'),
                            position_id=product.get('position_id')
                        )
                        adjust_bom_report_list.append(adjust_bom_report)
                        not_has_bom_products_list.append(int(product.get('product_id')))
            else:
                adjust_bom_report = dict(
                    line_id=line_id,
                    line_m_qty=str(quantity),
                    line_m_accounting_qty=str(accounting_quantity),
                    adjust_id=adjust_obj.id,
                    store_id=adjust_obj.adjust_store,
                    product_id=int(product.get('product_id')),
                    accounting_product_qty=str(accounting_quantity),
                    product_qty=str(quantity),
                    unit_id=product.get('unit_id'),
                    unit_name=product.get('unit_name'),
                    unit_spec=product.get('unit_spec'),
                    accounting_unit_id=product.get('accounting_unit_id'),
                    accounting_unit_name=product.get('accounting_unit_name'),
                    accounting_unit_spec=product.get('accounting_unit_spec'),
                    reason_type=product.get('reason_type'),
                    position_id=product.get('position_id')
                )
                adjust_bom_report_list.append(adjust_bom_report)
        return bom_products_map, is_bom_products_list, not_has_bom_products_list, adjust_bom_report_list

    def submit_adjust(self, adjust_id, partner_id, user_id):
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        self.check_adjust_inventory(adjust_id, partner_id, user_id, action="submit")
        adjust_obj = self.adjust_repo.update_adjust_status(adjust_id=adjust_id, allow_status=["INITED", "REJECTED"],
                                                           status="SUBMITTED", user_id=user_id, partner_id=partner_id,
                                                           username=username)
        if adjust_obj:
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=adjust_obj.adjust_store,
                                             doc_type="adjust"))
            Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="提交",
                       actionStatus=True, storeId=adjust_obj.adjust_store,
                       storeName="", content="报废单提交成功")
            return True
        else:
            Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="提交",
                       actionStatus=False, storeId=adjust_obj.adjust_store,
                       storeName="", content="当前状态不允许提交！")
            raise ActionException("当前状态不允许提交！")

    def reject_adjust(self, adjust_id, partner_id, user_id, reject_reason=None):
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        adjust_obj = self.adjust_repo.update_adjust_status(adjust_id=adjust_id, allow_status=["SUBMITTED"],
                                                           status="REJECTED", user_id=user_id, partner_id=partner_id,
                                                           username=username, reason=reject_reason)
        if adjust_obj:
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=adjust_obj.adjust_store,
                                             doc_type="adjust"))
            Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="驳回",
                       actionStatus=True, storeId=adjust_obj.adjust_store,
                       storeName="", content="驳回成功！")
            return True
        else:
            Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="驳回",
                       actionStatus=False, storeId=adjust_obj.adjust_store,
                       storeName="", content="当前状态不允许驳回！")
            raise ActionException("当前状态不允许驳回！")

    def approve_adjust(self, adjust_id, partner_id, user_id, is_pos=None):
        if is_pos:
            pass
        else:
            self.check_adjust_inventory(adjust_id, partner_id, user_id, action="approve")

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        adjust_obj = self.adjust_repo.get_adjust_by_adjust_id(adjust_id, partner_id=partner_id)
        if adjust_obj and adjust_obj.status in ('INITED', 'SUBMITTED'):
            list_adjust_product = self.adjust_repo.list_store_adjust_product(adjust_id=adjust_id, partner_id=partner_id)
            # 获取bom折算
            bom_products_map, is_bom_products_list, not_has_bom_products_list, adjust_bom_report_list = self.get_convert_quantity_map(
                adjust_obj, list_adjust_product, partner_id, user_id)
            logging.info('is_bom_products_list-{}'.format(is_bom_products_list))
            logging.info('adjust_bom_report_list{}'.format(adjust_bom_report_list))
            # 库存调整
            WITHDRAW_ACCOUNTS = []
            DEPOSIT_ACCOUNTS = []
            index = 1
            for adjust_product in list_adjust_product:
                if not adjust_product.get('accounting_quantity'):
                    continue
                product_id = int(adjust_product.get('product_id'))
                if product_id not in is_bom_products_list:
                    index += 1
                    accounting_quantity = adjust_product['accounting_quantity'] if adjust_product.get(
                        'accounting_quantity') else 0
                    quantity = 0
                    position = adjust_product.get('position_id')
                    if not position:
                        position_id = 0
                    else:
                        position_id = str(position)
                    key = str(position_id) + ',' + str(product_id)
                    if bom_products_map.get(key, 0):
                        convert_accounting_quantity = convert_to_decimal(accounting_quantity) + \
                                                      convert_to_decimal(bom_products_map[key])
                    else:
                        convert_accounting_quantity = accounting_quantity
                    if convert_accounting_quantity:
                        quantity = 0 - convert_accounting_quantity
                    # 加库存
                    if quantity > 0:
                        # 标记已加进订单扣库存
                        bom_products_map[key] = 0

                        acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=adjust_product.get('product_id'),
                                        action=2,
                                        amount=str(abs(quantity)))
                        if adjust_product.get('position_id') and adjust_product.get('position_id') != 1:
                            acc_data['sub_account'] = {"id": adjust_product.get('position_id')}
                        DEPOSIT_ACCOUNTS.append(acc_data)
                    # 减库存
                    elif quantity < 0:
                        # 标记已加进订单扣库存
                        bom_products_map[key] = 0
                        acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=adjust_product.get('product_id'),
                                        action=1,
                                        amount=str(abs(quantity)))
                        if adjust_product.get('position_id') and adjust_product.get('position_id') != 1:
                            acc_data['sub_account'] = {"id": adjust_product.get('position_id')}
                        WITHDRAW_ACCOUNTS.append(acc_data)

            # bom拆解未加进订单的也要进行库存扣减
            mater_products = []
            for k, v in bom_products_map.items():
                id_list = k.split(',')
                product_id = id_list[1]
                position_id = id_list[0]
                mater_products.append(int(product_id))
                if v:
                    quantity = 0 - v
                    # 加库存
                    if quantity > 0:
                        acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=product_id,
                                        action=2,
                                        amount=str(abs(quantity)))
                        if int(position_id) and int(position_id) != 1:
                            acc_data['sub_account'] = {"id": int(position_id)}
                        DEPOSIT_ACCOUNTS.append(acc_data)
                    # 减库存
                    elif quantity < 0:
                        acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=product_id,
                                        action=1,
                                        amount=str(abs(quantity)))
                        if int(position_id) and int(position_id) != 1:
                            acc_data['sub_account'] = {"id": int(position_id)}
                        WITHDRAW_ACCOUNTS.append(acc_data)
            ACCOUNTS = WITHDRAW_ACCOUNTS + DEPOSIT_ACCOUNTS
            logging.info("报废商品请求ACCOUNTS：{}".format(ACCOUNTS))
            # bom拆解后的原料也要推送
            # 先拿原料补充信息
            # bom拆解后的原料需要配方单位数量
            mater_products_info_ret = metadata_service.get_product_list(ids=mater_products,
                                                                        include_units=True,
                                                                        return_fields='id,code',
                                                                        partner_id=partner_id,
                                                                        user_id=user_id)
            mater_products_info = []
            if mater_products_info_ret:
                mater_products_info = mater_products_info_ret.get('rows')

            unit_dict = {}
            units_ret = metadata_service.get_unit_list(return_fields='id,code,rate,name',
                                                       partner_id=partner_id, user_id=user_id
                                                       )
            units = []
            if units_ret:
                units = units_ret['rows']
            if isinstance(units, list):
                for u in units:
                    if isinstance(u, dict) and 'id' in u:
                        unit_dict[str(u['id'])] = dict()
                        if 'code' in u:
                            unit_dict[str(u['id'])]['code'] = u['code']
                        else:
                            unit_dict[str(u['id'])]['code'] = None
                        if 'name' in u:
                            unit_dict[str(u['id'])]['name'] = u['name']
                        else:
                            unit_dict[str(u['id'])]['name'] = None
            mater_products_info_map = {}
            for p_i in mater_products_info:
                mater_products_info_map[p_i.get('id')] = {}
                units = p_i.get('units')
                mater_products_info_map[p_i.get('id')]['product_code'] = p_i.get('code')
                if units:
                    for unit in units:
                        if unit.get('bom'):
                            if unit_dict.get(str(unit.get('id'))):
                                mater_products_info_map[p_i.get('id')]['unit_id'] = \
                                    unit.get('id')
                                mater_products_info_map[p_i.get('id')]['unit_spec'] = \
                                    unit_dict.get(str(unit.get('id')))['code']
                                mater_products_info_map[p_i.get('id')]['rate'] = unit.get('rate') \
                                    if unit.get('rate') else 1
                                mater_products_info_map[p_i.get('id')]['unit_name'] = \
                                    unit_dict.get(str(unit.get('id')))['name']
                        # 给报表加入核算原料单位
                        if unit.get('default'):
                            if unit_dict.get(str(unit.get('id'))):
                                mater_products_info_map[p_i.get('id')]['accounting_unit_id'] = \
                                    unit.get('id')
                                mater_products_info_map[p_i.get('id')]['accounting_unit_spec'] = \
                                    unit_dict.get(str(unit.get('id')))['code']
                                mater_products_info_map[p_i.get('id')]['accounting_unit_name'] = \
                                    unit_dict.get(str(unit.get('id')))['name']

            if ACCOUNTS and len(ACCOUNTS) > 0:
                code = 'ADJUST'
                # 收货差异产生的报废，流水类型做区分
                if adjust_obj.reason_type == '01':
                    code = 'DIFF_ADJUST'
                # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
                message = dict(batch_no=str(adjust_id),
                               code=code,
                               action=100,
                               description='ADJUST',
                               trace_id=adjust_obj.code,
                               accounts=ACCOUNTS,
                               partner_id=partner_id,
                               user_id=user_id,
                               business_time=datetime.utcnow())
                inventory_dict = dict(batch_no=str(adjust_id), code=code, batch_action=100,
                                      action_dec=ACTION[100],
                                      batch_id=None,
                                      status="ERROR")  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                 partner_id=partner_id, user_id=user_id)
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                    message=message)
            # 写入报废bom报表
            self.adjust_bom_report(adjust_id, partner_id, user_id, adjust_bom_report_list, mater_products_info_map)
            adjust_obj = self.adjust_repo.approve_adjust(adjust_id, user_id=user_id,
                                                         username=username, bom_products_map=bom_products_map,
                                                         is_bom_products_list=is_bom_products_list,
                                                         partner_id=partner_id)
            # vendor单据同步给三方
            message = {
                'doc_resource': 's_adjust' if adjust_obj.branch_type != 'FRS_STORE' else 'fs_adjust',
                'doc_id':       adjust_id,
                'partner_id':   partner_id,
                'user_id':      user_id,
                'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)

            # 同步记录落表，用于后续补偿
            tp_trans_log = {
                'id':         adjust_id,
                'doc_code':   adjust_obj.code,
                'doc_type':   's_adjust' if adjust_obj.branch_type != 'FRS_STORE' else 'fs_adjust',
                'status':     'inited',
                'msg':        str(message),
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            }
            TpTransLogModel.create_logs_list([tp_trans_log])

            if adjust_obj:
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=adjust_obj.adjust_store,
                                                 doc_type="adjust"))
                Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="审核",
                           actionStatus=True, storeId=adjust_obj.adjust_store,
                           storeName="", content="报废单审核成功")
                return True
            else:
                Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="审核",
                           actionStatus=False, storeId=adjust_obj.adjust_store,
                           storeName="", content="报废单审核失败")
                raise ActionException('审核失败')
        else:
            raise ActionException('审核失败, 当前状态不允许审核！')

    # 确认调整单
    def adjust_confirmed(self, adjust_id, branch_type, partner_id, user_id, username, is_auto=False):
        adjust_obj = self.adjust_repo.get_adjust_by_adjust_id(adjust_id, partner_id=partner_id)
        if adjust_obj and (adjust_obj.status in ('INITED', 'CREATED') or is_auto):
            # 区分门店还是仓库
            list_adjust_product = self.adjust_repo.list_store_adjust_product(adjust_id=adjust_id, partner_id=partner_id)
            if branch_type == "WAREHOUSE":
                adjust_store_ret = metadata_service.get_distribution_center(center_id=adjust_obj.adjust_store,
                                                                            partner_id=partner_id, user_id=user_id)
            elif branch_type == "MACHINING_CENTER":
                adjust_store_ret = metadata_service.get_machining_center_by_id(_id=adjust_obj.adjust_store,
                                                                               return_fields='code,name',
                                                                               partner_id=partner_id, user_id=user_id)
            else:
                adjust_store_ret = metadata_service.get_store(store_id=adjust_obj.adjust_store,
                                                              return_fields='code,name',
                                                              partner_id=partner_id, user_id=user_id)
            adjust_store_code = adjust_store_ret.get('code')
            adjust_store_name = adjust_store_ret.get('name')
            total_message = []
            # 获取bom折算
            bom_products_map, is_bom_products_list, not_has_bom_products_list, adjust_bom_report_list = self.get_convert_quantity_map(
                adjust_obj, list_adjust_product, partner_id, user_id)
            logging.info('is_bom_products_list-{}'.format(is_bom_products_list))
            logging.info('adjust_bom_report_list{}'.format(adjust_bom_report_list))
            # 库存调整
            WITHDRAW_ACCOUNTS = []
            DEPOSIT_ACCOUNTS = []
            index = 1
            pid_rt_map = {}
            for adjust_product in list_adjust_product:
                if not adjust_product.get('accounting_quantity'):
                    continue
                product_id = int(adjust_product.get('product_id'))
                if product_id not in is_bom_products_list:
                    # bom商品不推送，先维护一个pid和vre的map
                    message = {
                        "URDT": adjust_obj.adjust_date.strftime("%Y-%m-%d"),
                        "DL03": adjust_obj.code,
                        "MCU":  adjust_store_code,
                    }
                    # HWS行号
                    message["KTLN"] = str(index)

                    message['LITM'] = adjust_product.get('product_code')
                    # bom单位         HWS传入
                    message['UOM'] = adjust_product.get('unit_spec')
                    # 数量           HWS传入
                    message['UORG'] = str(adjust_product.get('quantity') * -1)
                    # 报废原因码      HWS传入
                    message['ATT3'] = adjust_product.get('reason_type')

                    pid_rt_map[message['LITM'] + message['ATT3']] = message["KTLN"]

                    total_message.append(message)
                    index += 1
                    accounting_quantity = adjust_product['accounting_quantity'] if adjust_product.get(
                        'accounting_quantity') else 0
                    quantity = 0
                    position = adjust_product.get('position_id')
                    if not position or position == None:
                        position_id = 0
                    else:
                        position_id = str(position)
                    key = str(position_id) + ',' + str(product_id)
                    if bom_products_map.get(key, 0):
                        convert_accounting_quantity = convert_to_decimal(accounting_quantity) + \
                                                      convert_to_decimal(bom_products_map[key])
                    else:
                        convert_accounting_quantity = accounting_quantity
                    if convert_accounting_quantity:
                        quantity = 0 - convert_accounting_quantity
                    # 加库存
                    if quantity > 0:
                        # 标记已加进订单扣库存
                        bom_products_map[key] = 0

                        acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=adjust_product.get('product_id'),
                                        action=2,
                                        amount=str(abs(quantity)))
                        if adjust_product.get('position_id') and adjust_product.get('position_id') != 1:
                            acc_data['sub_account'] = {"id": adjust_product.get('position_id')}
                        DEPOSIT_ACCOUNTS.append(acc_data)
                    # 减库存
                    elif quantity < 0:
                        # 标记已加进订单扣库存
                        bom_products_map[key] = 0
                        acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=adjust_product.get('product_id'),
                                        action=1,
                                        amount=str(abs(quantity)))
                        if adjust_product.get('position_id') and adjust_product.get('position_id') != 1:
                            acc_data['sub_account'] = {"id": adjust_product.get('position_id')}
                        WITHDRAW_ACCOUNTS.append(acc_data)

            # bom拆解未加进订单的也要进行库存扣减
            mater_products = []
            for k, v in bom_products_map.items():
                id_list = k.split(',')
                product_id = id_list[1]
                position_id = id_list[0]
                mater_products.append(int(product_id))
                if v:
                    quantity = 0 - v
                    # 加库存
                    if quantity > 0:
                        acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=product_id,
                                        action=2,
                                        amount=str(abs(quantity)))
                        if int(position_id) and int(position_id) != 1:
                            acc_data['sub_account'] = {"id": int(position_id)}
                        DEPOSIT_ACCOUNTS.append(acc_data)
                    # 减库存
                    elif quantity < 0:
                        acc_data = dict(branch_id=adjust_obj.adjust_store, product_id=product_id,
                                        action=1,
                                        amount=str(abs(quantity)))
                        if int(position_id) and int(position_id) != 1:
                            acc_data['sub_account'] = {"id": int(position_id)}
                        WITHDRAW_ACCOUNTS.append(acc_data)
            ACCOUNTS = WITHDRAW_ACCOUNTS + DEPOSIT_ACCOUNTS
            logging.info("报废商品请求ACCOUNTS：{}".format(ACCOUNTS))
            # bom拆解后的原料也要推送
            # 先拿原料补充信息
            # bom拆解后的原料需要配方单位数量
            mater_products_info_ret = metadata_service.get_product_list(ids=mater_products,
                                                                        include_units=True,
                                                                        return_fields='id,code',
                                                                        partner_id=partner_id,
                                                                        user_id=user_id)
            mater_products_info = []
            if mater_products_info_ret:
                mater_products_info = mater_products_info_ret.get('rows')

            unit_dict = {}
            units_ret = metadata_service.get_unit_list(return_fields='id,code,rate,name',
                                                       partner_id=partner_id, user_id=user_id
                                                       )
            units = []
            if units_ret:
                units = units_ret['rows']
            if isinstance(units, list):
                for u in units:
                    if isinstance(u, dict) and 'id' in u:
                        unit_dict[str(u['id'])] = dict()
                        if 'code' in u:
                            unit_dict[str(u['id'])]['code'] = u['code']
                        else:
                            unit_dict[str(u['id'])]['code'] = None
                        if 'name' in u:
                            unit_dict[str(u['id'])]['name'] = u['name']
                        else:
                            unit_dict[str(u['id'])]['name'] = None
            mater_products_info_map = {}
            for p_i in mater_products_info:
                mater_products_info_map[p_i.get('id')] = {}
                units = p_i.get('units')
                mater_products_info_map[p_i.get('id')]['product_code'] = p_i.get('code')
                if units:
                    for unit in units:
                        if unit.get('bom'):
                            if unit_dict.get(str(unit.get('id'))):
                                mater_products_info_map[p_i.get('id')]['unit_id'] = \
                                    unit.get('id')
                                mater_products_info_map[p_i.get('id')]['unit_spec'] = \
                                    unit_dict.get(str(unit.get('id')))['code']
                                mater_products_info_map[p_i.get('id')]['rate'] = unit.get('rate') \
                                    if unit.get('rate') else 1
                                mater_products_info_map[p_i.get('id')]['unit_name'] = \
                                    unit_dict.get(str(unit.get('id')))['name']
                        # 给报表加入核算原料单位
                        if unit.get('default'):
                            if unit_dict.get(str(unit.get('id'))):
                                mater_products_info_map[p_i.get('id')]['accounting_unit_id'] = \
                                    unit.get('id')
                                mater_products_info_map[p_i.get('id')]['accounting_unit_spec'] = \
                                    unit_dict.get(str(unit.get('id')))['code']
                                mater_products_info_map[p_i.get('id')]['accounting_unit_name'] = \
                                    unit_dict.get(str(unit.get('id')))['name']

            if ACCOUNTS and len(ACCOUNTS) > 0:
                code = 'ADJUST'
                # 收货差异产生的报废，流水类型做区分
                if adjust_obj.reason_type == '01':
                    code = 'DIFF_ADJUST'
                # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
                message = dict(batch_no=str(adjust_id),
                               code=code,
                               action=100,
                               description='ADJUST',
                               trace_id=adjust_obj.code,
                               accounts=ACCOUNTS,
                               partner_id=partner_id,
                               user_id=user_id,
                               business_time=datetime.utcnow())
                inventory_dict = dict(batch_no=str(adjust_id), code=code, batch_action=100,
                                      action_dec=ACTION[100],
                                      batch_id=None,
                                      status="ERROR")  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                 partner_id=partner_id, user_id=user_id)
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                    message=message)
            # 写入报废bom报表
            self.adjust_bom_report(adjust_id, partner_id, user_id, adjust_bom_report_list, mater_products_info_map)
            # PDA消息推送
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=adjust_obj.adjust_store,
                                                  source_root_id=adjust_id,
                                                  source_id=adjust_id,
                                                  source_type="ADJUST",
                                                  action="CONFIRMED",
                                                  ref_source_id=adjust_id,
                                                  ref_source_type="ADJUST",
                                                  ref_action="INITED",
                                                  content={
                                                      "adjust_date":       str(adjust_obj.adjust_date),
                                                      "updated_at":        str(datetime.now()),
                                                      "updated_by":        str(user_id),
                                                      "reason_type":       adjust_obj.reason_type,
                                                      "remark":            adjust_obj.remark,
                                                      "updated_name":      username,
                                                      "adjust_store_name": adjust_store_name
                                                  }
                                                  )
            adjust_obj = self.adjust_repo.confirm_adjust_status(adjust_id, user_id=user_id,
                                                                username=username, bom_products_map=bom_products_map,
                                                                is_bom_products_list=is_bom_products_list,
                                                                partner_id=partner_id)
            if adjust_obj:
                # 清理待办缓存
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=adjust_obj.adjust_store,
                                                 doc_type="adjust"))
                Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="审核",
                           actionStatus=True, storeId=adjust_obj.adjust_store,
                           storeName="", content="报废单确认成功")
                return True
            else:
                Kit.upload(partnerId=partner_id, docNo=adjust_obj.code, docType='报废单', actionType="审核",
                           actionStatus=False, storeId=adjust_obj.adjust_store,
                           storeName="", content="报废单确认失败")
                raise ActionException('确认失败')
        else:
            raise ActionException('确认失败,损耗表状态不对')

    def adjust_bom_report(self, adjust_id, partner_id, user_id, adjust_bom_report_list, mater_products_info):
        """
        报废确认写入bom拆解日志
        """
        batch_obj = self.get_adjust_bom_report_batch(adjust_id, partner_id=partner_id)
        if batch_obj:
            return True
        else:
            # 没有写入一条记录
            form = {
                "adjust_id":  int(adjust_id),
                "partner_id": partner_id
            }
            adjust_bom_report_batch_obj = self.add_adjust_bom_report_batch(**form)
            if not adjust_bom_report_batch_obj:
                # 重复请求
                return True
            # 展示纬度：门店编码|门店名称|商品名称|商品编码|报废数量|原料名称|原料编码|拆解数量|核算单位
            for adjust_bom_report in adjust_bom_report_list:
                adjust_bom_report['batch_id'] = adjust_bom_report_batch_obj.id
                adjust_bom_report['created_by'] = user_id
                adjust_bom_report['partner_id'] = partner_id
                adjust_bom_report['product_qty'] = convert_to_decimal(adjust_bom_report['product_qty'])
                adjust_bom_report['accounting_product_qty'] = convert_to_decimal(
                    adjust_bom_report.get('accounting_product_qty', 0))
                if adjust_bom_report.get('bom_product_id'):
                    adjust_bom_report['accounting_qty'] = convert_to_decimal(
                        adjust_bom_report['qty']) if adjust_bom_report.get('qty') else 0
                    info = mater_products_info.get(str(adjust_bom_report['bom_product_id']))
                    if info:
                        if adjust_bom_report.get('qty'):
                            adjust_bom_report['qty'] = float(adjust_bom_report['qty']) / info.get('rate') if info.get(
                                'rate') else adjust_bom_report['accounting_qty']
                        else:
                            adjust_bom_report['qty'] = adjust_bom_report['accounting_qty']
                        adjust_bom_report['bom_unit_id'] = convert_to_int(info.get('unit_id'))
                        adjust_bom_report['bom_unit_name'] = info.get('unit_name')
                        adjust_bom_report['bom_unit_spec'] = info.get('unit_spec')
                        adjust_bom_report['bom_accounting_unit_id'] = convert_to_int(info['accounting_unit_id'])
                        adjust_bom_report['bom_accounting_unit_name'] = info.get('accounting_unit_name')
                        adjust_bom_report['bom_accounting_unit_spec'] = info.get('accounting_unit_spec')
                        adjust_bom_report['bom_rate'] = info.get('rate')
            self.add_adjust_bom_report_list(adjust_bom_report_list)
        return True

    # 获取bom报表信息
    def get_adjust_bom_report_info(self, adjust_id, partner_id, user_id):
        adjust_obj = self.adjust_repo.get_adjust_by_adjust_id(adjust_id, partner_id=partner_id)
        list_adjust_product = self.adjust_repo.list_store_adjust_product(adjust_id=adjust_id, partner_id=partner_id)
        # 获取bom折算
        bom_products_map, is_bom_products_list, not_has_bom_products_list, \
        adjust_bom_report_list = self.get_convert_quantity_map(adjust_obj, list_adjust_product, partner_id, user_id)
        # 库存调整
        # bom拆解未加进订单的也要进行库存扣减
        mater_products = []
        for k, v in bom_products_map.items():
            mater_products.append(int(k))
        # bom拆解后的原料也要推送
        # 先拿原料补充信息
        # bom拆解后的原料需要配方单位数量
        mater_products_info_ret = metadata_service.get_product_list(ids=mater_products,
                                                                    include_units=True,
                                                                    return_fields='id,code',
                                                                    partner_id=partner_id,
                                                                    user_id=user_id)
        mater_products_info = []
        if mater_products_info_ret:
            mater_products_info = mater_products_info_ret.get('rows')
        unit_dict = {}
        units_ret = metadata_service.get_unit_list(return_fields='id,code,rate,name',
                                                   partner_id=partner_id, user_id=user_id
                                                   )
        units = []
        if units_ret:
            units = units_ret['rows']
        if isinstance(units, list):
            for u in units:
                if isinstance(u, dict) and 'id' in u:
                    unit_dict[str(u['id'])] = dict()
                    if 'code' in u:
                        unit_dict[str(u['id'])]['code'] = u['code']
                    else:
                        unit_dict[str(u['id'])]['code'] = None
                    if 'name' in u:
                        unit_dict[str(u['id'])]['name'] = u['name']
                    else:
                        unit_dict[str(u['id'])]['name'] = None
        mater_products_info_map = {}
        for p_i in mater_products_info:
            mater_products_info_map[p_i.get('id')] = {}
            units = p_i.get('units')
            mater_products_info_map[p_i.get('id')]['product_code'] = p_i.get('code')
            if units:
                for unit in units:
                    if unit.get('bom'):
                        if unit_dict.get(str(unit.get('id'))):
                            mater_products_info_map[p_i.get('id')]['unit_id'] = \
                                unit.get('id')
                            mater_products_info_map[p_i.get('id')]['unit_spec'] = \
                                unit_dict.get(str(unit.get('id')))['code']
                            mater_products_info_map[p_i.get('id')]['rate'] = unit.get('rate') \
                                if unit.get('rate') else 1
                            mater_products_info_map[p_i.get('id')]['unit_name'] = \
                                unit_dict.get(str(unit.get('id')))['name']
                    # 给报表加入核算原料单位
                    if unit.get('default'):
                        if unit_dict.get(str(unit.get('id'))):
                            mater_products_info_map[p_i.get('id')]['accounting_unit_id'] = \
                                unit.get('id')
                            mater_products_info_map[p_i.get('id')]['accounting_unit_spec'] = \
                                unit_dict.get(str(unit.get('id')))['code']
                            mater_products_info_map[p_i.get('id')]['accounting_unit_name'] = \
                                unit_dict.get(str(unit.get('id')))['name']
        # 写入报废bom报表
        info = dict(adjust_bom_report_list=adjust_bom_report_list,
                    # 带入单位补充信息
                    mater_products_info_map=mater_products_info_map)
        return info

    def list_adjust_store_product(self, store_id, target_date=None, is_add=False, limit=-1, offset=0,
                                  include_total=None, search=None, category_ids=None,
                                  search_fields=None, storage_type=None, partner_id=None,
                                  user_id=None, branch_type=None, order_by=None, combine_result=False):
        order_by_inventory = order_by == 'real_inventory'
        if category_ids:
            product_relation_filters = {
                "product_category__in": [str(id) for id in category_ids],
            }
        else:
            product_relation_filters = None

        # 按库存排序时，分页在库存服务完成
        real_limit = limit
        real_offset = offset
        if order_by_inventory:
            real_limit = -1
            real_offset = 0
        list_store_product_ret = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_adjust=True),
            product_filters={"status": "ENABLED"},
            product_search=search,
            product_search_fields=search_fields,
            user_id=user_id,
            offset=real_offset,
            limit=real_limit,
            include_total=include_total,
            include_product_units=True,
            return_fields="allow_adjust",
            include_product_fields='code,name,category,category_name,barcode',
            product_relation_filters=product_relation_filters,
            region="ATTRIBUTE_REGION"
        )

        # 商品的含税单价和税率
        product_unit_adjust_tax_price_map = product_unit_adjust_tax_ratio_map = product_unit_adjust_sales_price_map = None
        if branch_type == "FRS_STORE":
            product_id_list = []
            if list_store_product_ret:
                list_store_product = list_store_product_ret.get('rows', [])
                for product in list_store_product:
                    if not product.get('allow_adjust'):
                        continue
                    product_id_list.append(int(product.get('product_id')))
            product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                store_id=store_id, user_id=user_id, partner_id=partner_id, product_ids=product_id_list,
                include_sales_price=True)

        store_product_adjust = []
        store_product_adjust_dict= {}
        final_product_ids = []
        total = 0

        if list_store_product_ret:
            list_store_product = list_store_product_ret.get('rows', [])
            total = list_store_product_ret.get('total') if list_store_product_ret.get('total') else 0
            category_ids = [int(p.get('category')) for p in list_store_product if p.get('category')]
            cate_dict = dict()
            category_list = metadata_service.get_product_category_list(ids=category_ids, return_fields="id,code,name",
                                                                       partner_id=partner_id).get('rows', [])
            for c in category_list:
                cate_dict[convert_to_int(c.get('id'))] = c


            unit_dict = dict()
            unit_list = metadata_service.list_entity(schema_name="unit", return_fields="id,code,name",
                                                                       partner_id=partner_id).get('rows', [])
            logger.info("unit_list===:{}".format(unit_list))
            for c in unit_list:
                unit_dict[convert_to_int(c.get('id'))] = c
            for product in list_store_product:
                product_dict = dict(
                    product_id=int(product.get('product_id')),
                    product_code=product.get('code'),
                    product_name=product.get('name'),
                  #  model_name=product.get('model_name')
                )
                if product.get('category'):
                    category_id = int(product.get('category'))
                    product_dict['category_id'] = category_id
                    if isinstance(cate_dict.get(category_id), dict):
                        product_dict["category_name"] = cate_dict.get(category_id).get('name')
                units = product.get('units', [])
                for unit in units:
                    unit['id'] = int(unit.get('id'))
                    unit_info = unit_dict.get(int(unit.get('id')))
                    if unit_info:
                        fields = unit_info['fields']
                        unit['code'] = fields.get('code')
                        unit['name'] = fields.get('name')
                    if branch_type == "FRS_STORE":
                        fill_adjust_tax_price_tax_rate(unit, product_unit_adjust_tax_price_map,
                                                       product_unit_adjust_tax_ratio_map, product.get('product_id'),
                                                       Decimal(unit.get("unit_rate", 1)),
                                                       product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                if not product.get('allow_adjust'):
                    if branch_type != "FRS_STORE":
                        if total > 0:
                            total -= 1
                        continue
                    else:
                        units = []
                product_dict['units'] = units
                product_dict['barcode'] = product['extends'].get('barcode') if product.get('extends') else []
                store_product_adjust.append(product_dict)
                store_product_adjust_dict[product_dict['product_id']] = product_dict
                final_product_ids.append(product_dict['product_id'])

        # 新增按照实时库存,分页
        inv_unchanged_products, inv_changed_products = [], []
        final_result=store_product_adjust
        if order_by_inventory and final_product_ids:
            total = len(final_product_ids)
            final_result = []
            order_result = inventory_service.query_branch_inventory_sort(partner_id, user_id, store_id,
                                                                        product_ids=final_product_ids,limit=limit,offset=offset).get('rows')
            # logging.info(f'报废库存排序：{order_result}')

            if order_result:
                for o in order_result:
                    tmp_record =store_product_adjust_dict.get(int(o.get('product_id')))
                    if tmp_record:
                        tmp_record['real_inventory_qty'] = o.get('qty')
                        final_result.append(tmp_record)
        if branch_type == "FRS_STORE":
            del product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map
        if include_total:
            return total, final_result, inv_unchanged_products
        return final_result, inv_unchanged_products

    def list_adjust_store_product_category(self, store_id, partner_id=None, user_id=None):
        list_store_product_ret = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_adjust=True),
            product_filters={"status": "ENABLED"},
            user_id=user_id,
            include_product_units=False,
            return_fields="allow_adjust",
            include_product_fields='category,category_name',
            region="ATTRIBUTE_REGION"
        )
        store_product_category_ids = []
        store_product_category_names = {}
        store_product_category_product_count = {}
        if list_store_product_ret:
            list_store_product = list_store_product_ret.get('rows', [])
            for product in list_store_product:
                if product.get('category') and product.get('allow_adjust'):
                    category_id = int(product.get('category'))
                    if category_id not in store_product_category_product_count:
                        store_product_category_ids.append(category_id)
                        store_product_category_names[category_id] = product.get('category_name')
                        store_product_category_product_count[category_id] = 1
                    else:
                        store_product_category_product_count[category_id] = store_product_category_product_count[
                                                                                category_id] + 1
        category_list = metadata_service.get_product_category_list(
            ids=store_product_category_ids if len(store_product_category_ids) == 1 else None,
            return_fields="id,parent_id", partner_id=partner_id, user_id=user_id).get('rows',
                                                                                      []) if store_product_category_ids else []
        category_children_map = {}
        for category in category_list:
            category_id = int(category["id"])
            category_parent_id = int(category.get("parent_id", 0))
            if category_parent_id:
                if category_parent_id in category_children_map:
                    if category_id not in category_children_map[category_parent_id]:
                        category_children_map[category_parent_id].append(category_id)
                else:
                    category_children_map[category_parent_id] = [category_id]
        store_product_category_product_count_sum = {}
        category_children_ret_map = {}
        for category_id in store_product_category_ids:
            store_product_category_product_count_sum[category_id] = store_product_category_product_count[category_id]
            for category_child_id in get_category_children(category_id, category_children_map,
                                                           category_children_ret_map):
                store_product_category_product_count_sum[category_id] += store_product_category_product_count.get(
                    category_child_id, 0)
        return [{
                    'category_id':   category_id, 'category_name': store_product_category_names[category_id],
                    'product_count': store_product_category_product_count_sum[category_id]
                } for category_id in store_product_category_ids]

    def iterate_inventory_product(self, inventory_products, products_unit, list_adjust, partner_id, user_id):
        store_product_inventory = []
        if inventory_products is None or not isinstance(inventory_products, dict) or len(
                inventory_products) == 0 or products_unit is None or not isinstance(products_unit, list) or len(
            products_unit) == 0:
            return store_product_inventory
        unit_dict = {}
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code',
                                                   partner_id=partner_id,
                                                   user_id=user_id)
        units = []
        if units_ret:
            units = units_ret['rows']
        if isinstance(units, list):
            for u in units:
                if isinstance(u, dict) and 'id' in u:
                    unit_dict[str(u['id'])] = dict()
                    if 'name' in u:
                        unit_dict[str(u['id'])]['name'] = u['name']
                    if 'code' in u:
                        unit_dict[str(u['id'])]['code'] = u['code']
        for p in products_unit:
            product_id = convert_to_int(p['id']) if 'id' in p else None
            inventory = inventory_products.get(str(product_id))
            # 半成品，成品也要取出来
            # product_type = p.get('product_type')
            # if (not inventory) and (product_type not in ["SEMI-FINISHED", "FINISHED"]):
            #     continue
            product = {}
            product['id'] = product_id
            product['product_name'] = p['name'] if 'name' in p else ''
            product['product_code'] = p['code'] if 'code' in p else ''
            product['spec'] = p['spec'] if 'spec' in p else ''
            product['product_category_id'] = int(p['category']) if 'category' in p else None
            product['storage_type'] = p['storage_type'] if 'storage_type' in p else ''
            for ajdust_product in list_adjust:
                if ajdust_product.product_id == product_id:
                    product['quantity'] = ajdust_product.quantity
                    product['unit_id'] = ajdust_product.unit_id
                    break
            units = p['units'] if 'units' in p else []
            product_units = []
            for m_unit in units:
                if isinstance(m_unit, dict):
                    if m_unit.get('bom'):
                        unit = {}
                        unit['id'] = int(m_unit['id'])
                        if 'rate' in m_unit:
                            unit['rate'] = m_unit.get('rate')
                            if not inventory:
                                pass
                            else:
                                unit['quantity'] = convert_to_decimal(
                                    inventory['quantity_avail']) / convert_to_decimal(
                                    m_unit.get('rate', 1))

                            if str(m_unit['id']) in unit_dict:
                                if 'name' in unit_dict[str(m_unit['id'])]:
                                    unit['unit_name'] = \
                                        unit_dict[str(m_unit['id'])]['name']
                        else:
                            if not inventory:
                                pass
                            else:
                                unit['quantity'] = convert_to_decimal(inventory['quantity_avail'])
                        product_units.append(unit)
            if len(product_units) == 0:
                continue
            product['unit'] = product_units
            store_product_inventory.append(product)
        return store_product_inventory

    def iterate_product(self, products_unit, list_adjust):
        store_product_inventory = []
        if products_unit is None or not isinstance(products_unit, list) or len(products_unit) == 0:
            return store_product_inventory
        for p in products_unit:
            product_id = convert_to_int(p['id']) if 'id' in p else None
            if product_id is None:
                continue
            product = {}
            for product_obj in list_adjust:
                if product_obj.product_id == product_id:
                    product['quantity'] = product_obj.quantity
                    product['unit_id'] = product_obj.unit_id
                    break
            product['id'] = product_id
            product['product_name'] = p['name'] if 'name' in p else ''
            product['product_code'] = p['code'] if 'code' in p else ''
            product['spec'] = p['spec'] if 'spec' in p else ''
            product['loss_report_order'] = p['loss_report_order'] if 'loss_report_order' in p else ''
            product['storage_type'] = p['storage_type'] if 'storage_type' in p else ''
            units = p['units'] if 'units' in p else []

            product_units = []
            for m_unit in units:
                if isinstance(m_unit, dict):
                    if m_unit['stocktake']:
                        unit = {}
                        unit['id'] = m_unit['id']
                        unit['rate'] = m_unit['rate'] if 'rate' in m_unit and m_unit['rate'] else 1.0
                        unit['quantity'] = -1
                        product_units.append(unit)
            if len(product_units) == 0:
                continue
            product['unit'] = product_units
            store_product_inventory.append(product)
        return store_product_inventory

    def create_adjust_from_receive(self, adjust_detail, partner_id=None, user_id=None):
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        list_adjust_products = []

        # 检验该收货差异单是否生成过报废单
        request_id = adjust_detail.get('request_id')
        adjust_db = self.adjust_repo.get_adjust_by_request_id(request_id=request_id)
        if adjust_db:
            raise DataValidationException('DuplicateRequestIdForAdjust')

        if adjust_detail.get('products'):
            for p in adjust_detail['products']:
                if p:
                    if p.get('quantity') and p.get('quantity') != 0:
                        product_obj = AdjustProduct()
                        if not p.get('reason_type'):
                            raise DataValidationException('reason_type required')
                        p = {
                            'product_id':  p.get('product_id'), 'unit_id': p.get('unit_id'),
                            'quantity':    p.get('quantity'),
                            'partner_id':  partner_id, 'user_id': user_id,
                            'reason_type': p.get('reason_type'),
                            'position_id': p.get('position_id')
                        }
                        set_model_from_props(p, product_obj)
                        list_adjust_products.append(product_obj)
        adjust_detail_obj = AdjustDetails()
        del adjust_detail['products']
        data = adjust_detail
        set_model_from_props(data, adjust_detail_obj)
        adjust_detail_obj.partner_id = partner_id
        adjust_detail_obj.user_id = user_id
        if not adjust_detail_obj.reason_type or not adjust_detail_obj.adjust_store:
            raise DataValidationException('reason_type and adjust_store are required')
        shipping_store = metadata_service.get_store(store_id=adjust_detail_obj.adjust_store, return_fields='id',
                                                    partner_id=partner_id, user_id=user_id)
        if not isinstance(shipping_store, dict):
            raise DataValidationException('invalid shipping_store')
        if not adjust_detail_obj.code:
            adjust_detail_obj.code = Supply_doc_code.get_code_by_type(
                'S_INV_AD', partner_id, None)
        if adjust_detail_obj.adjust_date:
            adjust_detail_obj.adjust_date = convert_to_datetime(adjust_detail_obj.adjust_date)
        if not adjust_detail_obj.adjust_date or adjust_detail_obj.adjust_date == datetime(1970, 1, 1, 8):
            adjust_detail_obj.adjust_date = datetime.now()

        if adjust_detail_obj.code:
            adjust_detail_obj.adjust_order_number = int(adjust_detail_obj.code)
        if not adjust_detail_obj.adjust_id:
            if user_id:
                adjust_detail_obj.created_by = user_id
            adjust_detail_obj.created = datetime.now()
            adjust_detail_obj.status = 'INITED'
            adjust_detail_obj.process_status = 'INITED'
        else:
            if user_id:
                adjust_detail_obj.updated_by = user_id
            adjust_detail_obj.updated = datetime.now()
        adjust_detail_obj.partner_id = partner_id
        if list_adjust_products and isinstance(list_adjust_products, list) and len(list_adjust_products) > 0:
            # 验证收获产品是否属于门店，不验证是否可报废
            b_store_product = self.valid_receive_adjust_store_product(adjust_detail_obj.adjust_store,
                                                                      adjust_detail_obj.adjust_date,
                                                                      list_adjust_products,
                                                                      partner_id=partner_id, user_id=user_id)
            if b_store_product:
                list_adjust_products = self.update_product_unit(b_store_product, partner_id=partner_id,
                                                                user_id=user_id, is_receive=True)
                # self.check_inventory(adjust_detail_obj.adjust_store, list_adjust_products, partner_id, user_id)
            else:
                return None
            if adjust_detail_obj.branch_type == "FRS_STORE":
                product_id_list = [int(product_item.product_id) for product_item in list_adjust_products]
                # 商品的含税单价和税率
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                    store_id=adjust_detail_obj.adjust_store, user_id=user_id, partner_id=partner_id,
                    product_ids=product_id_list, include_sales_price=True)
                for product_item in list_adjust_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map,
                                                   product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity,
                                                   'amount', 'sales_amount',
                                                   product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map,
                                                   quantityTaxAmountName="tax_amount")
        adjust_obj = self.adjust_repo.create_update_adjust(adjust_detail_obj, list_adjust_products, user_id,
                                                           username=username, partner_id=partner_id)
        self.adjust_repo.recalc_adjust_detail_total_amount(adjust_id=adjust_obj.adjust_id)
        return adjust_obj

    def create_adjust(self, request, partner_id=None, user_id=None, username=None):
        adjust_detail_obj = AdjustDetails()
        small_list = ['products', 'attachments']
        data = {}
        for name in dir(adjust_detail_obj):
            if not name.startswith('__') and not name.startswith('_') and name not in small_list:
                if hasattr(request, name):
                    r_value = getattr(request, name)
                    if not callable(r_value):
                        if isinstance(r_value, Timestamp):
                            timestamp = Timestamp()
                            timestamp.seconds = r_value.seconds
                            data[name] = timestamp.ToDatetime()
                        else:
                            data[name] = r_value
        set_model_from_props(data, adjust_detail_obj)
        adjust_detail_obj.partner_id = partner_id
        adjust_detail_obj.user_id = user_id
        adjust_detail_obj.branch_type = request.branch_type
        list_adjust_products = []
        position_id = request.position_id
        attachments = []
        if request.attachments:
            for t in request.attachments:
                attachments.append(dict(
                    type=t.type,
                    url=t.url
                ))
        adjust_detail_obj.attachments = json.dumps(dict(attachments=attachments))
        if not request.source:
            raise DataValidationException('Adjust source is required!')
        products = request.products
        if not request.products:
            raise DataValidationException('报废必须包含商品')
        for p in products:
            if p.quantity == 0:
                if request.source == "MANUAL_CREATED":
                    raise DataValidationException('商品报废数量不能为空或者为0，请检查单据')
            if not p.unit_id:
                raise DataValidationException('报废商品单位不能为空！')
            if not position_id:
                position_id = p.position_id if p.position_id else 1
            product_obj = AdjustProduct()
            # if not p.reason_type:
            #     raise DataValidationException('reason_type required')
            p = {
                'product_id':  p.product_id, 'unit_id': p.unit_id, 'quantity': p.quantity,
                'partner_id':  partner_id, 'user_id': user_id, 'reason_type': p.reason_type,
                'position_id': position_id
            }
            set_model_from_props(p, product_obj)
            list_adjust_products.append(product_obj)
        if not adjust_detail_obj.reason_type or not adjust_detail_obj.adjust_store:
            raise DataValidationException('reason_type and adjust_store are required')
        branch_type = request.branch_type
        if branch_type == "STORE" or branch_type == "FRS_STORE":
            # 判断门店状态是否允许报废
            status_check = metadata_service.check_store_status(adjust_detail_obj.adjust_store, partner_id, user_id)
            if not status_check:
                raise DataValidationException('请检查门店的开店状态!')

        if not adjust_detail_obj.code:
            adjust_detail_obj.code = Supply_doc_code.get_code_by_type(
                'S_INV_AD', partner_id, None)
        if adjust_detail_obj.adjust_date:
            adjust_detail_obj.adjust_date = convert_to_datetime(adjust_detail_obj.adjust_date)
        if not adjust_detail_obj.adjust_date or adjust_detail_obj.adjust_date == datetime(1970, 1, 1, 0):
            adjust_detail_obj.adjust_date = datetime.now()

        if adjust_detail_obj.code:
            adjust_detail_obj.adjust_order_number = int(adjust_detail_obj.code)
        if not adjust_detail_obj.adjust_id:
            if user_id:
                adjust_detail_obj.created_by = user_id
            adjust_detail_obj.created = datetime.now()
            adjust_detail_obj.status = 'INITED'
            adjust_detail_obj.process_status = 'INITED'
        else:
            if user_id:
                adjust_detail_obj.updated_by = user_id
            adjust_detail_obj.updated = datetime.now()
        adjust_detail_obj.partner_id = partner_id
        if list_adjust_products and isinstance(list_adjust_products, list) and len(list_adjust_products) > 0:
            # 验证产品是否属于门店，并且是否允许调整
            # b_store_product = self.valid_adjust_store_product(adjust_detail_obj.adjust_store,
            #                                                   adjust_detail_obj.adjust_date, list_adjust_products,
            #                                                   partner_id=partner_id, user_id=user_id)
            # self.check_inventory(adjust_detail_obj.adjust_store, list_adjust_products, partner_id,user_id)

            # 报废单位可选，支持所有单位报废，要注意单位转换(bom)
            list_adjust_products = self.update_product_unit(list_adjust_products, partner_id=partner_id,
                                                            user_id=user_id)
            if adjust_detail_obj.branch_type == "FRS_STORE":
                product_id_list = [int(product_item.product_id) for product_item in list_adjust_products]
                # 商品的含税单价和税率
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                    store_id=adjust_detail_obj.adjust_store, user_id=user_id, partner_id=partner_id,
                    product_ids=product_id_list, include_sales_price=True)
                for product_item in list_adjust_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map,
                                                   product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity,
                                                   'amount', 'sales_amount',
                                                   product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map,
                                                   quantityTaxAmountName="tax_amount")

        adjust_obj = self.adjust_repo.create_update_adjust(adjust_detail_obj, list_adjust_products, user_id,
                                                           username=username, partner_id=partner_id)
        self.adjust_repo.recalc_adjust_detail_total_amount(adjust_id=adjust_obj.adjust_id)
        if adjust_obj.attachments:
            attachments = json.loads(adjust_obj.attachments)
            adjust_obj.attachments = attachments.get('attachments', [])
        # 清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=adjust_obj.adjust_store,
                                         doc_type="adjust"))
        return adjust_obj

    def update_adjust(self, request, user_id, partner_id, username=None):
        if not request.adjust_id:
            return None
        adjust_detail_obj = AdjustDetails()
        adjust_detail_obj.adjust_id = request.adjust_id
        adjust_detail_obj.branch_type = request.branch_type
        adjust_date = request.adjust_date
        if adjust_date:
            timestamp = Timestamp()
            timestamp.seconds = adjust_date.seconds
            adjust_date = timestamp.ToDatetime()
            if adjust_date == datetime(1970, 1, 1):
                adjust_detail_obj.adjust_date = None
            else:
                adjust_detail_obj.adjust_date = adjust_date
        remark = request.remark
        adjust_detail_obj.remark = remark
        if request.reason_type:
            adjust_detail_obj.reason_type = request.reason_type
        attachments = []
        if request.attachments:
            for t in request.attachments:
                attachments.append(dict(
                    type=t.type,
                    url=t.url
                ))
        adjust_detail_obj.attachments = json.dumps(dict(attachments=attachments))
        position_id = request.position_id
        products = request.products
        list_adjust_products = []
        if products:
            for p in products:
                # if p.quantity == 0:
                #     raise DataValidationException('报废商品数量不能为0!-{}'.format(p.product_id))
                if not p.unit_id:
                    raise DataValidationException('报废商品单位不能为空！')

                if not position_id:
                    position_id = p.position_id if p.position_id else 1
                product_obj = AdjustProduct()
                p = {
                    'id':          p.id, 'product_id': p.product_id, 'unit_id': p.unit_id, 'quantity': p.quantity,
                    'partner_id':  partner_id, 'user_id': user_id, 'reason_type': p.reason_type,
                    'position_id': position_id
                }
                set_model_from_props(p, product_obj)
                list_adjust_products.append(product_obj)
        adjust_obj = self.adjust_repo.store_adjust_by_id(adjust_id=adjust_detail_obj.adjust_id,
                                                         partner_id=partner_id)
        if adjust_obj is None:
            return None
        if adjust_obj.status not in ['INITED', 'REJECTED']:
            raise StatusUnavailable("当前状态不允许更新")
        result = None
        if len(list_adjust_products) > 0:
            # 验证产品是否属于门店，并且是否允许调整
            # b_store_product = self.valid_adjust_store_product(adjust_obj.adjust_store,
            #                                                   adjust_detail_obj.adjust_date, list_adjust_products,
            #                                                   partner_id=partner_id, user_id=user_id)
            # self.check_inventory(adjust_detail_obj.adjust_store, list_adjust_products, partner_id, user_id)

            list_adjust_products = self.update_product_unit(list_adjust_products, partner_id=partner_id,
                                                            user_id=user_id)
        # 可单独更新以下字段
        elif (
                remark or adjust_detail_obj.adjust_date or adjust_detail_obj.reason_type or adjust_detail_obj.attachments) and len(
            list_adjust_products) <= 0:

            result = self.adjust_repo.update_adjust_detail(adjust_detail_obj, user_id=user_id, username=username,
                                                           partner_id=partner_id)
        # else:
        #     raise ActionException('更新数据不合法')
        if adjust_detail_obj.branch_type == "FRS_STORE":
            product_id_list = [int(product_item.product_id) for product_item in list_adjust_products]
            # 商品的含税单价和税率
            product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                store_id=adjust_obj.adjust_store, user_id=user_id, partner_id=partner_id, product_ids=product_id_list,
                include_sales_price=True)
            for product_item in list_adjust_products:
                fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map,
                                               product_unit_adjust_tax_ratio_map,
                                               product_item.product_id, 1, product_item.accounting_quantity, 'amount',
                                               'sales_amount',
                                               product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map,
                                               quantityTaxAmountName="tax_amount")

        result = self.adjust_repo.create_update_adjust(adjust_detail_obj, list_adjust_products, user_id=user_id,
                                                       username=username, partner_id=partner_id)
        self.adjust_repo.recalc_adjust_detail_total_amount(adjust_id=result.adjust_id)
        if result.attachments:
            attachments = json.loads(result.attachments)
            result.attachments = attachments.get('attachments', [])
        return result

    # 删除调整单
    def delete_adjust(self, adjust_id, partner_id, user_id):
        adjust_obj = self.adjust_repo.store_adjust_by_id(adjust_id=adjust_id, partner_id=partner_id)
        if adjust_id and adjust_obj:
            if adjust_obj.status in ["INITED", "REJECTED"]:
                return self.adjust_repo.delete_adjust(adjust_id, partner_id=partner_id)
            else:
                raise DataValidationException("status must be inited")
        raise DataValidationException("data not find")

    def delete_adjust_product(self, adjust_id, ids=None, partner_id=None, user_id=None):
        adjust_obj = self.adjust_repo.get_adjust_by_adjust_id(adjust_id, partner_id=partner_id)
        if adjust_obj and adjust_obj.status in ["INITED", "REJECTED"]:
            result = self.adjust_repo.delete_adjust_product(ids=ids, partner_id=partner_id)
            self.adjust_repo.recalc_adjust_detail_total_amount(adjust_id=adjust_id)
            return result
        raise DataValidationException("data not find")

    def bi_get_adjust_collect(self, partner_id, user_id, category_ids=None, product_name=None, bom_product_id=None,
                              start_date=None, end_date=None, branch_type=None, limit=None, offset=None,
                              include_total=False, store_ids=None, period_symbol=None, reason_type=None, order=None,
                              sort=None, hour_offset=None):
        product_ids = []
        if (category_ids and isinstance(category_ids, list) and len(category_ids) > 0) or product_name:
            relation_filters = search = search_fields = None
            if category_ids and isinstance(category_ids, list) and len(category_ids) > 0:
                category_ids = [str(category_id) for category_id in category_ids]
                relation_filters = {"product_category": category_ids}
            if product_name:
                search = str(product_name)
                search_fields = 'name'
            list_products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                  search=search,
                                                                  search_fields=search_fields,
                                                                  include_units=True,
                                                                  return_fields="id,code,name",
                                                                  partner_id=partner_id, user_id=user_id)
            list_products = []
            if list_products_ret:
                list_products = list_products_ret['rows']
            if not isinstance(list_products, list) or len(list_products) == 0:
                return None, 0, 0, 0, 0, 0, 0
            for product in list_products:
                product_ids.append(convert_to_int(product['id']))
        return self.adjust_repo.bi_get_adjust_collect(partner_id, user_id=user_id, product_ids=product_ids,
                                                      bom_product_id=bom_product_id,
                                                      start_date=start_date, end_date=end_date, branch_type=branch_type,
                                                      limit=limit, offset=offset, include_total=include_total,
                                                      store_ids=store_ids, period_symbol=period_symbol,
                                                      reason_type=reason_type, order=order, sort=sort,
                                                      hour_offset=hour_offset)

    def adjusts_cancel(self, adjust_ids, partner_id, user_id,allow_status=None):
        # for adjust_id in adjust_ids:
        #     adjust_obj = self.adjust_repo.get_adjust_by_adjust_id(adjust_id, partner_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        return self.adjust_repo.adjusts_cancel(adjust_ids, partner_id, user_id, allow_status=allow_status,username=username)

    def bi_get_adjust_detailed(self, partner_id, user_id, st_ids=None, category_ids=None, product_name=None,
                               reason_type=None, bom_product_id=None, start_date=None, end_date=None, branch_type=None,
                               limit=None, offset=None, include_total=False, store_ids=None, order=None, sort=None):
        product_ids = []
        if (category_ids and isinstance(category_ids, list) and len(category_ids) > 0) or product_name:
            relation_filters = search = search_fields = None
            if category_ids and isinstance(category_ids, list) and len(category_ids) > 0:
                category_ids = [str(category_id) for category_id in category_ids]
                relation_filters = {"product_category": category_ids}
            if product_name:
                search = str(product_name)
                search_fields = 'name'
            list_products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                  search=search,
                                                                  search_fields=search_fields,
                                                                  include_units=True,
                                                                  return_fields="id,code,name",
                                                                  partner_id=partner_id, user_id=user_id)
            list_products = []
            if list_products_ret:
                list_products = list_products_ret['rows']
            if not isinstance(list_products, list) or len(list_products) == 0:
                return None, 0, 0, 0, 0, 0, 0
            for product in list_products:
                product_ids.append(convert_to_int(product['id']))

        return self.adjust_repo.bi_get_adjust_detailed(partner_id, user_id=user_id, st_ids=st_ids,
                                                       product_ids=product_ids, bom_product_id=bom_product_id,
                                                       start_date=start_date, end_date=end_date,
                                                       branch_type=branch_type, reason_type=reason_type, limit=limit,
                                                       offset=offset, include_total=include_total,
                                                       store_ids=store_ids, order=order, sort=sort)

    def auto_close_created_adjust(self, adjust_date, partner_id=None, user_id=None):

        return self.adjust_repo.auto_close_created_adjust(adjust_date, partner_id=partner_id, user_id=user_id)

    def get_adjust_bom_report_batch(self, adjust_id, partner_id=None):
        return self.adjust_repo.get_adjust_bom_report_batch(adjust_id=adjust_id, partner_id=partner_id)

    def add_adjust_bom_report_batch(self, adjust_id, partner_id):
        return self.adjust_repo.add_adjust_bom_report_batch(adjust_id, partner_id)

    def add_adjust_bom_report_list(self, adjust_bom_report_list):
        return self.adjust_repo.add_adjust_bom_report_list(adjust_bom_report_list)

    def create_adjust_by_code(self, request, partner_id=None, user_id=None, username=None, branch_type="STORE"):
        store_code = request.adjust_store
        adjust_store = metadata_service.get_store_list(filters={'code__in': [store_code]},
                                                       return_fields='id,code,name,chain_type',
                                                       partner_id=partner_id, user_id=user_id).get('rows', [])
        if not adjust_store:
            raise DataValidationException('invalid adjust_store')
        elif branch_type in ['STORE', 'FRS_STORE'] and len(adjust_store) == 1 and adjust_store[0] and adjust_store[
            0].get('chain_type'):
            chain_type = adjust_store[0].get('chain_type')
            if chain_type == 'DRS':
                branch_type = 'STORE'
            elif chain_type == 'FRS':
                branch_type = 'FRS_STORE'

        p_codes = []
        unit_codes = []
        new_codes_map = {}
        if str(partner_id) in (APP_CONFIG['new_adjust_partner']).split(','):
            req_pro_codes = []
            for p in request.products:
                unit_codes.append(p.unit_code)
                req_pro_codes.append(p.product_code)
                cup_type, hot_and_cold = '', ''
                if p.skuRemark:
                    for remark in p.skuRemark:
                        code, name = '', ''
                        if remark.values:
                            code = remark.values.code if remark.values.code else ''
                        if remark.name:
                            name = remark.name.name if remark.name.name else ''
                        if name == '杯型' and (code in ['81', '82']):
                            cup_type = code
                        elif name == '冷热' and (code in ['91', '92']):
                            hot_and_cold = code
                new_codes_map[p.product_code] = '{}{}{}'.format(p.product_code, cup_type, hot_and_cold)
            p_ret = metadata_service.get_product_list(filters={'code__in': req_pro_codes},
                                                      return_fields='id,code,name',
                                                      partner_id=partner_id, user_id=user_id).get('rows', [])
            for p in p_ret:
                p_codes.append(p['code'])

            sku_codes = [new_codes_map[i] for i in new_codes_map.keys() if i not in p_codes]
            p_ret = metadata_service.get_product_list(filters={'code__in': sku_codes},
                                                      return_fields='id,code,name',
                                                      partner_id=partner_id, user_id=user_id).get('rows', [])
            for p in p_ret:
                p_codes.append(p['code'])

            # if len(p_codes) != len(req_pro_codes):
            unknown_codes = [code for code in sku_codes if code not in p_codes]
            if unknown_codes:
                raise DataValidationException('该sku编码:{}不存在!'.format(unknown_codes))
        else:
            for p in request.products:
                p_codes.append(p.product_code)
                unit_codes.append(p.unit_code)
        p_ret = metadata_service.get_attribute_products_by_store_id(
            return_fields='allow_adjust',
            store_id=convert_to_int(adjust_store[0].get('id')),
            partner_id=partner_id,
            product_filters={"status__in": ["ENABLED"], "code__in": p_codes},
            include_product_fields='code,name',
            user_id=user_id
        ).get('rows', [])

        p_infos = {}
        for p in p_ret:
            p_infos[p['code']] = p
        product_unit_map = get_product_code_unit_map(product_codes=p_codes, partner_id=partner_id, user_id=user_id)

        adjust_detail_obj = AdjustDetails()

        adjust_detail_obj.reason_type = request.reason_type
        adjust_detail_obj.adjust_store = adjust_store[0]['id']
        adjust_detail_obj.adjust_date = datetime.utcnow()
        adjust_detail_obj.partner_id = partner_id
        adjust_detail_obj.user_id = user_id
        adjust_detail_obj.created_by = user_id
        adjust_detail_obj.created = datetime.utcnow()
        adjust_detail_obj.status = 'INITED'
        adjust_detail_obj.process_status = 'INITED'
        adjust_detail_obj.request_id = get_guid()
        adjust_detail_obj.branch_type = branch_type
        adjust_detail_obj.source = SOURCE_ENUM.get('POS_ADJUST')
        # 三方接口
        adjust_detail_obj.code = request.request_id
        # 拉取成本中心
        cost_center_id = get_cost_center_map(branch_type="STORE", branch_id=adjust_detail_obj.adjust_store,
                                             partner_id=partner_id, user_id=user_id, return_cost=True)
        adjust_detail_obj.cost_center_id = cost_center_id
        list_adjust_products = []
        for p in request.products:
            if not p.quantity:
                continue
            product_obj = AdjustProduct()
            if not p.reason_type:
                raise DataValidationException('reason_type required')
            is_new_sku = False
            if p_infos.get(p.product_code):
                product = p_infos.get(p.product_code)
                product_code = p.product_code
            else:
                product = p_infos.get(new_codes_map.get(p.product_code))
                product_code = new_codes_map.get(p.product_code)
                is_new_sku = True
            if not product:
                raise DataValidationException('商品不存在或未配置属性区域-{}'.format(product_code))
            allow_adjust = product.get('allow_adjust')
            if not allow_adjust:
                raise DataValidationException('商品属性配置不可报废-{}'.format(product.get('code')))
            p_id = convert_to_int(product.get('product_id'))
            # 默认取销售单位
            unit_dict = product_unit_map.get(str(product_code)) if product_unit_map.get(str(product_code)) else {}
            sales_unit = unit_dict.get('sales', {})
            if not sales_unit:
                raise DataValidationException("该商品没有配置销售单位-{}".format(product_code))

            # unit_dict = unit_map.get(p.unit_code)
            # if not unit_dict:
            #     raise DataValidationException('product unit_code Not Found!')
            product_obj.product_id = p_id
            product_obj.unit_id = sales_unit.get('id', 0)
            product_obj.unit_spec = sales_unit.get('code', '')
            product_obj.unit_name = sales_unit.get('name', '')
            product_obj.quantity = p.quantity
            product_obj.reason_type = p.reason_type
            sku_remark = []
            if p.skuRemark and is_new_sku is False:
                for sku in list(p.skuRemark):
                    sku_remark.append(pb2dict(sku))
            if sku_remark:
                product_obj.sku_remark = json.dumps(dict(sku_remark=sku_remark))
                product_obj.is_bom = True
            list_adjust_products.append(product_obj)
        if not adjust_detail_obj.reason_type:
            raise DataValidationException('reason_type are required')
        else:
            if user_id:
                adjust_detail_obj.updated_by = user_id
            adjust_detail_obj.updated = datetime.utcnow()
        adjust_detail_obj.partner_id = partner_id
        if list_adjust_products and isinstance(list_adjust_products, list) and len(list_adjust_products) > 0:
            list_adjust_products = self.update_product_unit(list_adjust_products, partner_id=partner_id,
                                                            user_id=user_id, is_pos_adjust=True)
            if adjust_detail_obj.branch_type == "FRS_STORE":
                product_id_list = [int(product_item.product_id) for product_item in list_adjust_products]
                # 商品的含税单价和税率
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                    store_id=adjust_detail_obj.adjust_store, user_id=user_id, partner_id=partner_id,
                    product_ids=product_id_list, include_sales_price=True)
                for product_item in list_adjust_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map,
                                                   product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity,
                                                   'amount', 'sales_amount',
                                                   product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map,
                                                   quantityTaxAmountName="tax_amount")

        adjust_obj = self.adjust_repo.create_update_adjust(adjust_detail_obj, list_adjust_products, user_id,
                                                           username=username, partner_id=partner_id)
        self.adjust_repo.recalc_adjust_detail_total_amount(adjust_id=adjust_obj.adjust_id)
        # pos端报废直接确认掉
        self.approve_adjust(adjust_id=adjust_obj.adjust_id, partner_id=partner_id, user_id=user_id, is_pos=True)
        return adjust_obj

    def create_manufactoru_adjust_by_code(self, request, partner_id=None, user_id=None, username=None):
        store_code = request.adjust_store
        adjust_store = metadata_service.list_entity(schema_name='machining-center',
                                                    filters={'code__in': [store_code]},
                                                    return_fields="id,code,name",
                                                    partner_id=partner_id, user_id=user_id).get('rows', [])

        if not adjust_store:
            raise DataValidationException('加工中心编码不存在')

        unit_codes = []
        req_pro_codes = []
        for p in request.products:
            unit_codes.append(p.unit_code)
            req_pro_codes.append(p.product_code)

        p_ret = metadata_service.get_product_list(filters={'code__in': req_pro_codes},
                                                  return_fields='id,code,name',
                                                  partner_id=partner_id, user_id=user_id).get('rows', [])
        p_infos = {}
        for p in p_ret:
            p_infos[p['code']] = p
        product_unit_map = get_product_code_unit_map(product_codes=req_pro_codes, partner_id=partner_id,
                                                     user_id=user_id)

        adjust_detail_obj = AdjustDetails()
        adjust_detail_obj.reason_type = request.reason_type
        adjust_detail_obj.adjust_store = adjust_store[0]['id']
        adjust_detail_obj.adjust_date = datetime.utcnow()
        adjust_detail_obj.partner_id = partner_id
        adjust_detail_obj.user_id = user_id
        adjust_detail_obj.created_by = user_id
        adjust_detail_obj.created = datetime.utcnow()
        adjust_detail_obj.status = 'INITED'
        adjust_detail_obj.process_status = 'INITED'
        adjust_detail_obj.request_id = get_guid()
        adjust_detail_obj.branch_type = 'MACHINING_CENTER'
        adjust_detail_obj.source = 'MANUAL_CREATED'
        adjust_detail_obj.code = request.request_id
        print
        # 拉取成本中心
        cost_center_id = get_cost_center_map(branch_type="MACHINING_CENTER", branch_id=adjust_detail_obj.adjust_store,
                                             partner_id=partner_id, user_id=user_id, return_cost=True)
        # print("!!!!", cost_center_id)
        adjust_detail_obj.cost_center_id = cost_center_id
        list_adjust_products = []
        for p in request.products:
            if not p.quantity:
                continue
            product_obj = AdjustProduct()
            if not p.reason_type:
                raise DataValidationException('缺少商品行报废原因')

            product = p_infos.get(p.product_code)
            product_code = p.product_code
            if not product:
                raise DataValidationException('商品主档不存在-{}'.format(product_code))

            p_id = convert_to_int(product.get('id'))
            # 默认取订货单位
            unit_dict = product_unit_map.get(str(product_code)) if product_unit_map.get(str(product_code)) else {}
            order_unit = unit_dict.get('order', {})
            if not order_unit:
                raise DataValidationException("该商品没有配置订货单位-{}".format(product_code))

            print(product)
            product_obj.product_id = p_id
            product_obj.product_name = product.get("name")
            product_obj.product_code = product.get("code")
            product_obj.partner_id = partner_id
            product_obj.unit_id = order_unit.get('id', 0)
            product_obj.unit_spec = order_unit.get('code', '')
            product_obj.unit_name = order_unit.get('name', '')
            product_obj.quantity = p.quantity
            product_obj.reason_type = p.reason_type
            list_adjust_products.append(product_obj)
        if not adjust_detail_obj.reason_type:
            raise DataValidationException('缺少报废原因')
        else:
            if user_id:
                adjust_detail_obj.updated_by = user_id
            adjust_detail_obj.updated = datetime.utcnow()
        adjust_detail_obj.partner_id = partner_id
        if list_adjust_products and isinstance(list_adjust_products, list) and len(list_adjust_products) > 0:
            list_adjust_products = self.update_product_unit(list_adjust_products, partner_id=partner_id,
                                                            user_id=user_id, is_pos_adjust=True)
            if adjust_detail_obj.branch_type == "FRS_STORE":
                product_id_list = [int(product_item.product_id) for product_item in list_adjust_products]
                # 商品的含税单价和税率
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(
                    store_id=adjust_detail_obj.adjust_store, user_id=user_id, partner_id=partner_id,
                    product_ids=product_id_list, include_sales_price=True)
                for product_item in list_adjust_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map,
                                                   product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity,
                                                   'amount', 'sales_amount',
                                                   product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map,
                                                   quantityTaxAmountName="tax_amount")

        adjust_obj = self.adjust_repo.create_update_adjust(adjust_detail_obj, list_adjust_products, user_id,
                                                           username=username, partner_id=partner_id)
        self.adjust_repo.recalc_adjust_detail_total_amount(adjust_id=adjust_obj.adjust_id)
        # 直接确认掉
        self.approve_adjust(adjust_id=adjust_obj.adjust_id, partner_id=partner_id, user_id=user_id, is_pos=True)
        return adjust_obj

    def get_adjust_by_code(self, code):
        return self.adjust_repo.get_adjust_by_code(code)

    def get_adjust_log(self, adjust_id=None, partner_id=None, user_id=None):
        """查询报废操作log"""
        res = {}
        total, adjust_logs = self.adjust_repo.get_adjust_log(adjust_id, partner_id=partner_id)
        res["rows"] = []
        res["total"] = total
        user_ids = [log.created_by for log in adjust_logs]
        user_dict = get_username_map(partner_id=partner_id, user_id=user_id, ids=user_ids)
        for log in adjust_logs:
            row = dict(
                id=log.id,
                status=log.adjust_status,
                created_at=self.get_timestamp(log.created_at),
                created_by=log.created_by,
                created_name=user_dict.get(log.created_by),
                reason=log.reason
            )
            res["rows"].append(row)
        return res


ads = AdjustService()
