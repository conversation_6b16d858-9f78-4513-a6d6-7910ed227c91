from supply.client.metadata_service import metadata_service
from supply.driver.Redis import redis_cli


class PosStocktake:

    def upload_pos_data(self, partner_id, user_id, store_code, status, quantity, sync_type):
        name = f'supply:pos_stocktake:{partner_id}:{store_code}'
        value = redis_cli.hgetall(name)

        if not value and \
                not metadata_service.get_store_list(filters={'code': store_code}, return_fields='id',
                                                    partner_id=partner_id, user_id=user_id).get('rows'):
            return False

        redis_cli.hmset(name, {'status': status,
                               'quantity': quantity if sync_type == 'STOCKTAKE' else (value.get('quantity') or '')})
        redis_cli.expire(name, 60 * 60)
        return True

    def get_pos_data(self, partner_id, store_code):
        return redis_cli.hgetall(f'supply:pos_stocktake:{partner_id}:{store_code}')