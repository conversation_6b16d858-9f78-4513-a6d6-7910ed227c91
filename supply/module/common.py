from decimal import Decimal
from datetime import datetime

from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand
from supply.model.returns import ReturnModel, ReturnProductModel, ReturnLogModel
from supply.model.supply_doc_code import Supply_doc_code
from supply.utils.enums import PaymentWay

from supply.utils.helper import update_db
from supply.error.exception import DataValidationException

from supply.utils.constants import EMPTY_ROWS
from supply.client.metadata_service import metadata_service
from google.protobuf.timestamp_pb2 import Timestamp

from supply.module import BaseToolsModule


class Refund:
    def __init__(self, refund_id, partner_id, user_id, username):
        self.refund_id = refund_id
        self.partner_id = partner_id
        self.user_id = user_id
        self.username = username

    def get_refund_detail(self, detail, refund_amount, main_type='', refund_type='', franchisee_id=0):
        demand_db = SupplyFranchiseeDemand.get(detail.master_id)
        franchisee_id =franchisee_id if franchisee_id else detail.franchisee_id
        return dict(
            id=self.refund_id,
            partner_id=self.partner_id,
            main_id=detail.id,
            main_code=detail.code,
            main_type=main_type,
            code=Supply_doc_code.get_code_by_type('FDR', self.partner_id, None),
            received_by=detail.received_by,
            franchisee_id=franchisee_id,
            refund_date=datetime.utcnow(),
            status='INITED',
            process_status='INITED',
            created_by=self.user_id,
            created_name=self.username,
            updated_by=self.user_id,
            updated_name=self.username,
            refund_amount=refund_amount,
            type=refund_type,
            batch_id=detail.master_id,
            payment_way=BaseToolsModule.get_refund_way(demand_db.payment_way),
            trade_company=BaseToolsModule.get_trade_company_by_franchisee(franchisee_id, self.partner_id, self.user_id)
        )

    def get_refund_products(self, products, main_type=''):
        refund_products = []
        total_amount = Decimal(0.0)
        for i, p in enumerate(products):
            quantity = p.get("quantity", 0)
            tax_price = p.get("tax_price", 0) if not main_type else p.get("price_tax", 0)
            amount = round(Decimal(quantity * tax_price), 2)
            refund_products.append(dict(
                refund_id=self.refund_id,
                partner_id=self.partner_id,
                quantity=quantity,
                tax_price=tax_price,
                amount=amount,
                created_by=self.user_id,
                created_name=self.username,
                updated_by=self.user_id,
                updated_name=self.username,
                product_id=p.get("product_id"),
                product_code=p.get("product_code"),
                product_name=p.get("product_name"),
                category_id=p.get("category_id"),
                unit_id=p.get("unit_id"),
                unit_name=p.get("unit_name"),
                unit_rate=p.get("unit_rate"),
                unit_spec=p.get("unit_spec"),
                cost_price=p.get("cost_price", 0),
                tax_rate=p.get("tax_rate", 0),
            ))
            total_amount += amount
        return refund_products, total_amount

    def get_refund_log(self):
        return dict(
            partner_id=self.partner_id,
            refund_id=self.refund_id,
            action='INITED',
            created_by=self.user_id,
            created_name=self.username,
            created_at=datetime.utcnow()
        )

    def get_return_refund_detail(self, detail, refund_amount, main_type='', refund_type='', master_id=0):
        return dict(
            id=self.refund_id,
            partner_id=self.partner_id,
            main_id=detail.id,
            main_code=detail.code,
            main_type=main_type,
            code=Supply_doc_code.get_code_by_type('FDR', self.partner_id, None),
            received_by=detail.return_by,
            franchisee_id=detail.franchisee_id,
            refund_date=datetime.utcnow(),
            status='INITED',
            process_status='INITED',
            created_by=self.user_id,
            created_name=self.username,
            updated_by=self.user_id,
            updated_name=self.username,
            refund_amount=refund_amount,
            type=refund_type,
            batch_id=master_id,
            payment_way=PaymentWay.CreditPay.code,
            trade_company=BaseToolsModule.get_trade_company_by_franchisee(detail.franchisee_id, self.partner_id,
                                                                          self.user_id)
        )


def get_return_by_id(session, primary_id, partner_id):
    q = session.query(ReturnModel).filter(ReturnModel.id==primary_id).filter(ReturnModel.partner_id==partner_id)
    return q.first()


def list_return_products_by_return_id(session, return_id, limit=None, offset=None, sort=None, order=None, partner_id=None):
    if not partner_id:
        raise DataValidationException("请输入租户信息")

    query = session.query(ReturnProductModel)
    query = query.filter(ReturnProductModel.return_id == return_id)
    query = query.filter(ReturnProductModel.partner_id == partner_id)
    count = query.count()
    if limit:
        query = query.limit(limit)
    if offset:
        query = query.offset(offset)
    if order:
        if order == "asc":
            if sort == "product_code":
                query = query.order_by(ReturnProductModel.product_code)
        elif order == "desc":
            if sort == "product_code":
                query = query.order_by(ReturnProductModel.product_code.desc())
    return_products_db_list = query.all()
    return count, return_products_db_list


def create_returns_log(session, **kwargs):
    obj = ReturnLogModel()
    for k, v in kwargs.items():
        if k in obj.__table__.columns.keys() and v is not None:
            setattr(obj, k, v)
    session.add(obj)
    session.commit()
    return obj


def update_returns(session, id, update_obj,
                   partner_id, user_id=None):
    return_db = session.query(ReturnModel).filter(ReturnModel.id == id).filter(ReturnModel.partner_id == partner_id).with_lockmode(
        "update").first()
    update_db(return_db, update_obj)
    session.commit()
    return True


def update_products_in_all(session, updated_products, update_return_details=None):
    session.bulk_update_mappings(ReturnProductModel, updated_products)
    if update_return_details:
        session.bulk_update_mappings(ReturnModel, [update_return_details])
    session.commit()
    return True


def get_field_set_from_entitys(entitys, field):
    return {get_entity_field(entity, field) for entity in entitys}


def get_field_entity_tree_from_entitys(entitys, field):
    return {get_entity_field(entity, field): entity for entity in entitys}


def get_field_entity_collection_from_entitys(entitys, field):
    return [{get_entity_field(entity, field): entity} for entity in entitys]


def get_entity_field(entity, query):
    field = entity.get(query) if isinstance(entity, dict) else get_entity_attribute(entity, query)
    if not field:
        raise DataValidationException(f'实体{entity.__class__.__name__}, 没有{query}属性')
    return field


def get_entity_attribute(entity, query):
    return None if not hasattr(entity, query) else getattr(entity, query)


def get_store_by_company(company_info, partner_id, user_id, client=metadata_service) -> set:
    relation_filters = {"company_info": company_info}
    request = {
        "limit": -1,
        "partner_id": partner_id,
        "user_id": user_id,
        "relation_filters": relation_filters
    }
    response = client.get_store_list(**request)
    stores = response.get("rows", [])
    store_ids = {int(store_id) for store_id in get_field_set_from_entitys(stores, 'id')}

    return store_ids


def add_store_ids_to_receive_bys(receive_bys: list, store_ids: list, is_requred=False):
    """通过store_ids与store_ids联动

    Args:
        receive_bys (list): origin 请求
        store_ids (list): 根据用户行为查询获得
        is_requred (bool, optional): 根据用户行为是否操作store_ids更新;操作即真. Defaults to False.

    Returns:
        list OR -1: EMPTY_ROWS:自定义空行标识-1;
    """
    if not is_requred:
        receive_bys.extend(list(store_ids))
        return receive_bys
    else:
        if receive_bys:
            receive_bys = list(set(receive_bys).intersection(set(store_ids)))
            return EMPTY_ROWS if not receive_bys else receive_bys
        return EMPTY_ROWS if not store_ids else store_ids


def deal_timestamp(date):
    if date and isinstance(date, Timestamp):
        if date.seconds:
            date = datetime.utcfromtimestamp(date.seconds)
        else:
            date = None
    return date
