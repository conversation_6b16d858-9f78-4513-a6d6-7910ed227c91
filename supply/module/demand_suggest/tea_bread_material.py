import calendar
from collections import OrderedDict, defaultdict
from datetime import timedelta
from . import utils
import dateutil

from supply import logger
from supply.client.bom_service import bom_service
from supply.driver.mysql import session
from supply.model.sale_forecast import DemandSuggestSaleForecast as SFDB
from supply.utils import type_convert
from supply.utils.snowflake import get_id
from . import utils


class TeaBreadDemandMaterialSuggest:
    """面包茶饮类材料商品的建议订货量"""

    @classmethod
    def get_suggest(cls, partner_id, user_id, demand_day, store_id, finished_product_ids):
        """
        :return: {
            <int:product_id>: <float: suggest_amount>,
            ...
        }
        """
        demand_day = utils.clear_time(demand_day)

        suggest_data = {}
        suggest_params = cls.get_suggest_params(partner_id, user_id, demand_day, store_id, finished_product_ids)
        for material_p_id, params in suggest_params.items():
            product = params['product']
            min_quantity = product['min_quantity']
            max_quantity = product['max_quantity']
            increment_quantity = product['increment_quantity']

            inventory_safety_factor = params.get('inventory_safety_factor', 0)
            inventory_amount = params.get('inventory_amount', 0)
            forecast_amount_sum = params.get('forecast_amount_sum', 0)
            this_arrival_day = params['this_arrival_day']
            next_arrival_day = params['next_arrival_day']
            intervals = (next_arrival_day - this_arrival_day).days
            if intervals:
                inventory_safety_amount = inventory_safety_factor * forecast_amount_sum / intervals
            else:
                inventory_safety_amount = 0
            suggest_amount = forecast_amount_sum - inventory_amount + inventory_safety_amount
            suggest_amount = utils.regular_quantity(min_quantity, max_quantity, increment_quantity, suggest_amount)


            suggest_data[material_p_id] = suggest_amount
        return suggest_data

    @classmethod
    def get_suggest_params(cls, partner_id, user_id, demand_day, store_id, finished_product_ids):
        """
        获取建议原料的建议订货量
        :return: {
            <material_product_id>: {
                'this_arrival_day': <datetime>,
                'next_arrival_day': <datetime>,
                'forecast_amount_sum': <int>,  # [this_arrival_day, next_arrival_day) 时间段的预测量汇总
                'inventory_safety_factor': inventory_factor,
                'inventory_amount': inventory_amount,
                'product': {},
            },
            ...
        }
        """
        all_dates = [demand_day + timedelta(days=i) for i in range(1, 8)]

        material_sale_forecasts = cls.list_material_sale_forecast(
            partner_id, user_id, store_id, finished_product_ids, all_dates
        )
        if not material_sale_forecasts:
            return {}
        material_product_ids = list(material_sale_forecasts.keys())
        material_products = utils.list_store_product_detail(partner_id, user_id, store_id, material_product_ids)

        inventory_factor_map = utils.get_inventory_safety_factor(partner_id, user_id, store_id, material_products)
        inventory_map = utils.get_inventory_amount(partner_id, user_id, store_id, material_product_ids)

        ret = defaultdict(dict)
        change_limit_map = utils.get_change_limit_setting(
            partner_id, user_id, store_id, material_products
        )
        arrival_infos = cls.get_product_arrival_infos(demand_day, material_products)
        for product in material_products:
            p_id = int(product['product_id'])
            sale_forecast = material_sale_forecasts.get(p_id, {})
            arrival_info = arrival_infos.get(p_id)
            if not arrival_info:
                continue
            this_arrival_day = arrival_info['this_arrival_day']
            next_arrival_day = arrival_info['next_arrival_day']

            forecast_amount_sum = 0
            for date, forecast_amount in sale_forecast.items():
                if next_arrival_day > date >= this_arrival_day:
                    forecast_amount_sum += forecast_amount
            inventory_factor = inventory_factor_map.get(p_id, 0)
            inventory_amount = inventory_map.get(p_id, 0)

            change_limit = change_limit_map.get(p_id, {})
            ret[p_id]['change_rate_upper'] = change_limit.get('change_rate_upper')
            ret[p_id]['change_rate_lower'] = change_limit.get('change_rate_lower')

            ret[p_id] = {
                'this_arrival_day': this_arrival_day,
                'next_arrival_day': next_arrival_day,
                'forecast_amount_sum': forecast_amount_sum,
                'inventory_safety_factor': inventory_factor,
                'inventory_amount': inventory_amount,
                'product': product,
            }

        return dict(ret)

    @classmethod
    def get_product_arrival_infos(cls, demand_day, products):
        arrival_infos = {}
        for p in products:
            p_id = int(p['product_id'])
            circle_type = p['circle_type']
            planned_arrival_days = int(p['planned_arrival_days'])
            if circle_type == 'M':
                cycles = utils.format_cycles(p['cycles'], planned_arrival_days)
                try:
                    info = cls.calc_month_arrival_day_info(demand_day, cycles)
                except ValueError as e:
                    logger.error("invalid demand_day(%s) for  product(id:%s) (month cycle)" % (demand_day, p_id))
                    continue
            elif circle_type == 'W':
                cycles = utils.format_cycles(p['cycles'], planned_arrival_days)
                try:
                    info = cls.calc_week_arrival_day_info(demand_day, cycles)
                except ValueError as e:
                    logger.error("invalid demand_day(%s) for  product(id:%s) (week cycle)" % (demand_day, p_id))
                    continue

            elif circle_type == 'D':
                demand_interval_days = p['demand_interval_days']
                info = cls.calc_day_arrival_day_info(demand_day, demand_interval_days, planned_arrival_days)
            else:
                continue
            arrival_infos[p_id] = info

        return arrival_infos

    @classmethod
    def calc_day_arrival_day_info(cls, demand_day, demand_interval_days, arrival_days):
        """按日配送 的 到货日计算
        :return: {
            'this_demand_day': <datetime>,
            'next_arrival_day': <datetime>,
        }
        """
        this_arrival_day = demand_day + timedelta(days=arrival_days)

        next_demand_day = demand_day + timedelta(days=demand_interval_days)
        next_arrival_day = next_demand_day + timedelta(days=arrival_days)

        return {
            'this_arrival_day': this_arrival_day,
            'next_arrival_day': next_arrival_day,
        }

    @classmethod
    def calc_month_arrival_day_info(cls, demand_day, cycles):
        """按月配送 的 到货日计算
        :return: {
            'this_arrival_day': <datetime>,
            'next_arrival_day': <datetime>,
        }
        """
        _, max_month_day = calendar.monthrange(demand_day.year, demand_day.month)

        this_month_order_detail = []
        for cycle in cycles:
            if cycle['order_date'] < 0:
                order_day = max_month_day - cycle['order_date'] + 1
            else:
                order_day = cycle['order_date']
            if order_day < max_month_day:
                this_month_order_detail.append({
                    'date': demand_day.replace(day=order_day),
                    'planned_arrival_days': cycle['planned_arrival_days'],
                })

        next_month_first_day = demand_day.replace(day=1) - dateutil.relativedelta.relativedelta(months=1)
        _, max_month_day = calendar.monthrange(next_month_first_day.year, next_month_first_day.month)
        next_month_order_detail = []
        for cycle in cycles:
            if cycle['order_date'] < 0:
                order_day = max_month_day - cycle['order_date'] + 1
            else:
                order_day = cycle['order_date']
            if order_day < max_month_day:
                next_month_order_detail.append({
                    'date': next_month_first_day.replace(day=order_day),
                    'planned_arrival_days': cycle['planned_arrival_days'],
                })

        if demand_day not in set(i['order_date'] for i in this_month_order_detail):
            raise ValueError("invalid demand day")

        index = utils.find_index(this_month_order_detail, demand_day, key=lambda x: x['order_date'])
        this_order_detail = this_month_order_detail[index]
        next_order_detail = (this_month_order_detail + next_month_order_detail)[index + 1]

        this_arrival_day = this_order_detail['date'] + timedelta(days=this_order_detail['planned_arrival_days'])
        next_arrival_day = next_order_detail['date'] + timedelta(days=next_order_detail['planned_arrival_days'])

        return {
            'this_arrival_day': this_arrival_day,
            'next_arrival_day': next_arrival_day,
        }

    @classmethod
    def calc_week_arrival_day_info(cls, demand_day, cycles):
        """按周配送 的 到货日计算
        :return order_date_details: {
            'this_arrival_day': <datetime>,
            'next_arrival_day': <datetime>,
        }
        """
        week_number = demand_day.isoweekday()
        cycles.sort(key=lambda x: x['order_date'])
        demand_date_valid = week_number in [i['order_date'] for i in cycles]
        if not demand_date_valid:
            raise ValueError("demand_date not valid")

        monday = demand_day - timedelta(days=week_number - 1)
        order_date_details = []
        for cycle in cycles:
            order_date_number = cycle['order_date']
            order_date = monday + timedelta(days=order_date_number - 1)
            planned_arrival_days = int(cycle.get('planned_arrival_days'))
            order_date_details.append({
                'date': order_date,
                'planned_arrival_days': planned_arrival_days,
            })
        index = utils.find_index(cycles, week_number, key=lambda x: x['order_date'])
        this_order_detail = order_date_details[index]
        next_index = index + 1
        if next_index <= len(cycles) - 1:
            next_order_detail = order_date_details[next_index]
        else:
            first_order_detail = order_date_details[0]
            next_order_detail = {
                'date': first_order_detail['date'] + timedelta(days=7),
                'planned_arrival_days': first_order_detail['planned_arrival_days'],
            }

        this_arrival_day = this_order_detail['date'] + timedelta(days=this_order_detail['planned_arrival_days'])
        next_arrival_day = next_order_detail['date'] + timedelta(days=next_order_detail['planned_arrival_days'])

        return {
            'this_arrival_day': this_arrival_day,
            'next_arrival_day': next_arrival_day,
        }

    @classmethod
    def list_sale_forecast(cls, partner_id, user_id, store_id, product_ids, dates):
        """
        :return: {
            <product_id>: {
                <date>: amount,
                <date>: amount,
            }
        """
        query = session.query(
            SFDB.store_id, SFDB.product_id, SFDB.forecast_date, SFDB.forecast_amount,
            SFDB.confirm_amount, SFDB.id,
        ).filter(
            SFDB.partner_id == partner_id, SFDB.forecast_date.in_(dates),
            SFDB.store_id == store_id,
        ).order_by(SFDB.product_id, SFDB.forecast_amount)

        forecast_data = OrderedDict()
        for row in query.all():
            product_id = row.product_id
            forecast_date = row.forecast_date
            confirm_amount = row.confirm_amount
            if product_id not in forecast_data:
                forecast_data[product_id] = {}
            forecast_data[product_id][forecast_date] = confirm_amount
        return forecast_data

    @classmethod
    def list_material_sale_forecast(cls, partner_id, user_id, store_id, finished_product_ids, dates):
        """
        :return: {
            <material_product_id>: {
                <date>: amount,
                <date>: amount,
            }
        """
        if (not dates) or (not finished_product_ids):
            return {}
        material_product_forecast_data = defaultdict(dict)
        for date in dates:
            material_data = cls.list_bom_sale_forecast(
                partner_id, user_id, store_id, finished_product_ids, date
            )
            for material_id, forecast_amount in material_data.items():
                if date in material_product_forecast_data[material_id]:
                    material_product_forecast_data[material_id][date] += forecast_amount
                else:
                    material_product_forecast_data[material_id][date] = forecast_amount
        return material_product_forecast_data

    @classmethod
    def list_bom_sale_forecast(cls, partner_id, user_id, store_id, finished_product_ids, biz_date):
        """
        从成品的预测量，得出原料的预测量
        :return: {
            'material_product_id': <float>,
            ...
        }
        """
        if not finished_product_ids:
            return {}
        query = session.query(
            SFDB.store_id, SFDB.product_id, SFDB.forecast_date, SFDB.forecast_amount,
            SFDB.confirm_amount, SFDB.id,
        ).filter(
            SFDB.partner_id == partner_id, SFDB.forecast_date == biz_date,
            SFDB.store_id == store_id, SFDB.product_id.in_(finished_product_ids),
        ).order_by(SFDB.product_id, SFDB.forecast_amount)
        finish_product_forecasts = [
            row._asdict()
            for row in query.all()
        ]
        if not finish_product_forecasts:
            return {}
        bom_products_params = []
        for p in finish_product_forecasts:
            temp = {
                'product_id': int(p['product_id']),
                'product_qty': p['confirm_amount'],
            }
            if temp['product_qty'] != 0:
                bom_products_params.append(temp)

        if bom_products_params:
            bom_infos = bom_service.get_products_boms(
                partner_id=partner_id, user_id=user_id,
                request_id=get_id(), store_id=store_id,
                sales_date=biz_date.strftime("%Y-%m-%d"),
                products=bom_products_params, biz_code="SALE_FORECAST",
                biz_no=str(get_id()),
            ).get('response', [])
        else:
            bom_infos = []
        material_products = {}
        for info in bom_infos:
            p_id = type_convert.to_int(info.get('product_id'))
            boms = info.get('bom') or []
            for b in boms:
                material_product_id = type_convert.to_int(b.get('product_id'))
                qty = type_convert.to_float(b.get('qty'))
                if material_product_id in material_products:
                    material_products[material_product_id]['forecast_amount'] += qty
                    material_products[material_product_id]['finished_product_ids'].append(p_id)
                else:
                    material_products[material_product_id] = {
                        'forecast_amount': qty,
                        'finished_product_ids': [p_id, ],
                    }

        _material_products = {
            p_id: data['forecast_amount']
            for p_id, data in material_products.items()
        }
        return _material_products

    @staticmethod
    def list_between_dates(start_date, end_date):
        dates = [start_date, ]
        for i in range(10):
            if start_date >= end_date:
                return dates
            else:
                start_date = start_date + timedelta(days=1)
                dates.append(start_date)
