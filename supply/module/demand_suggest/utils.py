import traceback

from supply.client.inventory_service import inventory_service
from supply.client.metadata_service import metadata_service
from supply.client.report_service import report_service
import datetime


def find_index(data_list, value, key=lambda x: x):
    for i, data in enumerate(data_list):
        _value = key(data)
        if _value == value:
            return i
    raise ValueError("%s is not in list" % value)


def regular_quantity(min_value, max_value, increment_value, origin_value):
    """
    按照最大值，最小值，允许的递增量 规整数据。
    """

    if origin_value <= 0:
        return 0
    min_value = min_value or 0
    max_value = max_value or 0
    increment_value = increment_value or 1
    mod = (origin_value - min_value) % increment_value
    ceiling_standardize = lambda x: x + (increment_value - (mod or increment_value))
    if origin_value <= min_value:
        return min_value
    standardize_value = ceiling_standardize(origin_value)
    if not max_value:
        return standardize_value
    else:
        if standardize_value <= max_value:
            return standardize_value
        else:
            return max_value


def format_cycles(cycles, default_arrival_days):
    _cycles = []
    for cycle in cycles:
        planned_arrival_days = cycle.get('planned_arrival_days', 0)
        try:
            order_date = int(cycle['order_date'])
        except Exception as e:
            continue
        if not planned_arrival_days:
            planned_arrival_days = default_arrival_days
        _cycles.append({
            'planned_arrival_days': planned_arrival_days,
            'order_date': order_date,
        })
    return _cycles


def get_inventory_amount(partner_id, user_id, store_id, product_ids):
    inventory_amounts, _ = inventory_service.query_realtime_inventory(
        partner_id=partner_id, user_id=user_id, branch_id=store_id,
        limit=-1, product_ids=product_ids
    )
    ret = {}
    for product_id, data in inventory_amounts.items():
        ret[int(product_id)] = data['quantity_avail']
    return ret


def get_inventory_safety_factor(partner_id, user_id, store_id, products):
    """
    获取安全库存系数
    :return: {
        <product_id>: <float:inventory_factor>,
        ...
    }
    """
    factors = metadata_service.list_inventory_safety_factor(partner_id, user_id)
    factor_map = {
        (f['product_type'], f['bom_type'], f['sale_type']): f['inventory_safety']
        for f in factors
    }
    ret = {}
    for p in products:
        p_id = int(p.get('product_id', 0))
        product_type = p['product_type']
        bom_type = p['bom_type']
        sale_type = p['sale_type']
        key = (product_type, bom_type, sale_type)
        inventory_factor = factor_map.get(key, 0)
        ret[p_id] = inventory_factor
    return ret


def get_store_product_sales(partner_id, user_id, store_id, product_ids, start_date, end_date):
    """
    获取指定时间段内的平均销售量
    """
    product_sales = report_service.get_amount_of_product_sales(
        start_date, end_date, product_ids, store_id, partner_id, user_id
    )
    ret = {}
    for sale_info in product_sales:
        product_id = int(sale_info['product_id'])
        ret[product_id] = sale_info.get('average_quantity', 0)
    return ret


def get_change_limit_setting(partner_id, user_id, store_id, products):
    change_settings = metadata_service.list_demand_change_limits(
        partner_id, user_id,
    )
    change_setting_map = {
        (c['product_type'], c['bom_type'], c['sale_type']): {
            'change_rate_upper': c['change_rate_upper'],
            'change_rate_lower': c['change_rate_lower'],
        }
        for c in change_settings
    }

    ret = {}
    for product in products:
        p_id = int(product.get('product_id', 0))
        product_type = product['product_type']
        bom_type = product['bom_type']
        sale_type = product['sale_type']
        key = (product_type, bom_type, sale_type)
        change_setting = change_setting_map.get(key, {
            'change_rate_upper': None,
            'change_rate_lower': None,
        })
        ret[p_id] = change_setting
    return ret


def get_weight_factors(partner_id, user_id, store_id, products):
    region_ids = get_store_id_geo_region_ids(partner_id, user_id, store_id)
    weight_factors = metadata_service.list_weight_factor(
        partner_id, user_id
    )
    weight_factor_map = {}
    for w in weight_factors:
        data = {
            'weather_factor': w['weather_factor'],
            'discount_factor': w['discount_factor'],
            'holiday_factor': w['holiday_factor'],
        }
        if w['store_id'] != 0:
            key = (w['product_type'], w['bom_type'], w['sale_type'], w['store_id'])
            weight_factor_map[key] = data
        if w['geo_region_id'] != 0:
            key = (w['product_type'], w['bom_type'], w['sale_type'], w['geo_region_id'])
            weight_factor_map[key] = data
    ret = {}
    for p in products:
        p_id = int(p.get('product_id', 0))
        product_type = p['product_type']
        bom_type = p['bom_type']
        sale_type = p['sale_type']
        key = (product_type, bom_type, sale_type, store_id)
        if key in weight_factor_map:
            ret[p_id] = weight_factor_map.get(key)
        else:
            for region_id in region_ids:
                key = (product_type, bom_type, sale_type, region_id)
                if key in weight_factor_map:
                    ret[p_id] = weight_factor_map.get(key)

    return ret


def get_store_id_geo_region_ids(partner_id, user_id, store_id):
    try:
        store = metadata_service.get_entity_by_id(
            partner_id, user_id, store_id, 'store', include_parents=True
        )
    except Exception as e:
        return []
    if not store:
        return []
    else:
        fields = store.get('fields') or {}
        relation = fields.get('fields') or {}
        geo_region_id = convert_to_int(relation.get('geo_region'))
    if not geo_region_id:
        return []

    region = metadata_service.get_entity_by_id(
        partner_id, user_id, geo_region_id, 'geo-region', include_parents=True
    )
    ids = [geo_region_id, ]
    for i in range(10):
        if 'parent' in region:
            parent = region.get('parent') or {}
            parent_id = int(parent.get('id') or 0)
            if parent_id:
                ids.append(parent_id)
                region = parent
            else:
                break
    ids = set(i for i in ids if i)
    return ids


def list_store_product_detail(partner_id, user_id, store_id, product_ids):
    if not product_ids:
        return []
    product_map_from_purchase = list_store_product_detail_from_purchase(
        partner_id, user_id, store_id, product_ids
    )

    product_map_from_distribution = list_store_product_detail_from_distribution(
        partner_id, user_id, store_id, product_ids
    )
    product_map_from_distribution.update(product_map_from_purchase)
    return product_map_from_distribution.values()


def list_store_product_detail_from_purchase(partner_id, user_id, store_id, product_ids):
    product_infos = metadata_service.get_purchase_product_by_store_id(
        store_id=store_id, partner_id=partner_id, user_id=user_id,
        product_ids=product_ids,
        include_product_fields='product_type,sale_type,bom_type,name,code',
    ) or {}
    product_infos = product_infos.get('rows', [])
    product_map = {}
    for p in product_infos:
        p_id = int(p['product_id'])
        sale_type = p['sale_type']
        bom_type = p['bom_type']
        product_type = p['product_type']
        cycles = p.get('cycles', [])
        name = p.get('name')
        code = p.get('code')
        circle_type = p.get('circle_type')
        if circle_type not in ('D', 'W', 'M'):
            continue
        planned_arrival_days = p.get('planned_arrival_days') or 0
        cycles = format_cycles(cycles, planned_arrival_days)
        if circle_type == "D":
            demand_interval_days = convert_to_int(p.get('interval_days')) + 1

        unit_id = int(p['unit_id'])
        unit_rate = 1
        for unit in p.get('units', []):
            if convert_to_int(unit.get('id')) == unit_id:
                unit_rate = float(unit.get('rate', 1))
        temp = {
            'sale_type': sale_type,
            'product_id': p_id,
            'bom_type': bom_type,
            'name': name,
            'code': code,
            'product_type': product_type,
            'cycles': cycles,
            'circle_type': circle_type,
            'min_quantity': convert_to_int(p['min_number']),
            'max_quantity': convert_to_int(p['max_number']),
            'increment_quantity': convert_to_int(p['increment_number']),
            'planned_arrival_days': planned_arrival_days,
            'unit_rate': unit_rate,
            'unit_id': unit_id,
        }
        if circle_type == "D":
            temp['demand_interval_days'] = demand_interval_days
        product_map[p_id] = temp
    return product_map


def list_store_product_detail_from_distribution(partner_id, user_id, store_id, product_ids):
    product_infos = metadata_service.list_distribution_region_product_by_store_id(
        store_id=store_id, partner_id=partner_id, user_id=user_id,
        product_ids=product_ids,
        include_product_fields='product_type,sale_type,bom_type,name,code',
    )
    product_map = {}
    for p_info in product_infos:
        p_id = int(p_info['product_id'])
        p = p_info['product']
        sale_type = p['sale_type']
        bom_type = p['bom_type']
        product_type = p['product_type']
        cycles = p_info.get('cycles', [])
        name = p.get('name')
        code = p.get('code')
        circle_type = p_info.get('circle_type')
        if circle_type not in ('D', 'W', 'M'):
            continue
        planned_arrival_days = p_info.get('planned_arrival_days') or 0
        cycles = format_cycles(cycles, planned_arrival_days)
        if circle_type == "D":
            demand_interval_days = convert_to_int(p_info.get('interval_days')) + 1

        unit_id = int(p_info['unit_id'])
        unit_rate = 1
        for unit in p.get('units', []):
            if convert_to_int(unit.get('id')) == unit_id:
                unit_rate = float(unit.get('rate', 1))
        temp = {
            'sale_type': sale_type,
            'product_id': p_id,
            'bom_type': bom_type,
            'name': name,
            'code': code,
            'product_type': product_type,
            'cycles': cycles,
            'circle_type': circle_type,
            'min_quantity': convert_to_int(p_info['min_number']),
            'max_quantity': convert_to_int(p_info['max_number']),
            'increment_quantity': convert_to_int(p_info['increment_number']),
            'planned_arrival_days': planned_arrival_days,
            'unit_rate': unit_rate,
            'unit_id': unit_id,
        }
        if circle_type == "D":
            temp['demand_interval_days'] = demand_interval_days

        product_map[p_id] = temp
    return product_map


def list_tea_and_bread(partner_id, user_id, product_ids):
    metadata_service.get_product_list()


def clear_time(_datetime):
    return _datetime.replace(hour=0, minute=0, second=0, microsecond=0)


def get_product_ids(products):
    return [int(p['product_id']) for p in products]


def convert_to_int(value, default=0):
    try:
        return int(value)
    except Exception as e:
        return default


def str_to_datetime(time_str):
    if time_str:
        time_str = time_str.replace('Z', '').replace('+08:00', '')
        try:
            t = datetime.datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%S')
        except Exception as e:
            print(traceback.format_exc())
            return None
        else:
            return t
    return None
