import calendar
from datetime import timedelta

import dateutil

from supply import logger
from . import utils


class NewRetailNormalSuggest(object):
    """新零售-零售类建议订货量"""

    @classmethod
    def get_suggest(cls, partner_id, user_id, demand_day, store_id, product_ids):
        demand_day = utils.clear_time(demand_day)
        products = utils.list_store_product_detail(partner_id, user_id, store_id, product_ids)
        suggest_params = cls.get_suggest_params(
            partner_id, user_id, demand_day, store_id, products
        )
        ret = {}
        for p in products:
            p_id = int(p['product_id'])
            if p_id not in suggest_params:
                ret[p_id] = 0
            params = suggest_params.get(p_id)

            if params:
                demand_interval_days = params['demand_interval_days']
                month_average_sale_amount = params['month_average_sale_amount']
                arrival_day = params['arrival_days']
                inventory_amount = params['inventory_amount']

                suggest_amount = month_average_sale_amount * demand_interval_days - \
                                 inventory_amount + arrival_day * month_average_sale_amount

                min_quantity = params['min_quantity']
                max_quantity = params['max_quantity']
                increment_quantity = params['increment_quantity']
                suggest_amount = utils.regular_quantity(min_quantity, max_quantity, increment_quantity, suggest_amount)
            else:
                suggest_amount = 0
            ret[p_id] = suggest_amount
        return ret

    @classmethod
    def get_suggest_params(cls, partner_id, user_id, demand_day, store_id, products):
        """
        获取 新零售零售类商品 的建议订货量的计算参数
        :return: {
            <product_id>: {
                'demand_interval_days': 1.0,  # 配送间隔时间。
                'month_average_sale_amount': 100,  # 月平均日销量(从订货日上一日算起，倒退30天为一个月)，来源于Report项目
                'inventory_amount': 50,  # 库存量
                'arrival_days': 10,  # 预计到货天数,
                'max_quantity': <int>,
                'min_quantity': <int>,
                'increment_quantity': <int>,
            },
            ...
        }
        """
        product_ids = utils.get_product_ids(products)

        product_circle_map = {}
        for product in products:
            product_id = int(product['product_id'])
            cycle_type = product['circle_type']
            default_planned_arrival_days = int(product['planned_arrival_days'])
            cycles = utils.format_cycles(product.get('cycles', []), default_planned_arrival_days)
            if cycle_type == 'D':
                arrival_days = default_planned_arrival_days
                demand_interval_days = product['demand_interval_days']
            elif cycle_type == 'W':
                try:
                    demand_interval_days, arrival_days = cls.calc_week_cycle_interval_days(demand_day, cycles)
                except ValueError as e:
                    logger.error("invalid demand_day(%s) for product(id:%s) (week cycle)" % (demand_day, product_id))
                    continue
            elif cycle_type == 'M':
                try:
                    demand_interval_days, arrival_days = cls.calc_month_cycle_interval_days(demand_day, cycles)
                except ValueError as e:
                    logger.error("invalid demand_day(%s) for  product(id:%s) (month cycle)" % (demand_day, product_id))
                    continue
            else:
                logger.info("wrong product cycle type (product_id:%s)" % product_id)
                continue
            product_circle_map[product_id] = {
                'arrival_days': arrival_days,
                'demand_interval_days': demand_interval_days,
            }

        product_inventory_map = utils.get_inventory_amount(partner_id, user_id, store_id, product_ids)

        sale_end_date = demand_day
        sale_start_date = demand_day - timedelta(days=30)
        product_sales_map = utils.get_store_product_sales(
            partner_id, user_id, store_id, product_ids, sale_start_date, sale_end_date
        )
        change_limit_map = utils.get_change_limit_setting(
            partner_id, user_id, store_id, products
        )
        demand_suggest_params = {}
        for product in products:
            p_id = int(product['product_id'])

            params = {}
            if p_id in product_circle_map:
                circle_info = product_circle_map[p_id]
                params['arrival_days'] = circle_info['arrival_days']
                params['demand_interval_days'] = circle_info['demand_interval_days']
            else:
                continue
            params['inventory_amount'] = product_inventory_map.get(p_id, 0.0)
            params['month_average_sale_amount'] = product_sales_map.get(p_id, 0.0)

            max_quantity = int(product['max_quantity'] or 0)
            min_quantity = int(product['min_quantity'] or 0)

            increment_quantity = int(product['increment_quantity'] or 0)
            params['max_quantity'] = max_quantity
            params['min_quantity'] = min_quantity
            params['increment_quantity'] = increment_quantity

            change_limit = change_limit_map.get(p_id, {})
            params['change_rate_upper'] = change_limit.get('change_rate_upper')
            params['change_rate_lower'] = change_limit.get('change_rate_lower')

            demand_suggest_params[p_id] = params
        return demand_suggest_params

    @classmethod
    def calc_week_cycle_interval_days(cls, demand_day, cycles):
        """按周的配送间隔时间
        :param order_date_details: [
            { 'this_arrival_day': <datetime>, <next_>: <int>},
            ...
        ]
        """
        week_number = demand_day.isoweekday()
        cycles.sort(key=lambda x: x['order_date'])
        demand_date_valid = week_number in [i['order_date'] for i in cycles]
        if not demand_date_valid:
            raise ValueError("demand_date not valid")

        monday = demand_day - timedelta(days=week_number - 1)
        order_date_details = []
        for cycle in cycles:
            order_date_number = cycle['order_date']
            order_date = monday + timedelta(days=order_date_number - 1)
            planned_arrival_days = int(cycle.get('planned_arrival_days'))
            order_date_details.append({
                'date': order_date,
                'planned_arrival_days': planned_arrival_days,
            })

        index = utils.find_index(cycles, week_number, key=lambda x: x['order_date'])
        this_order_info = cycles[index]
        arrival_days = this_order_info['planned_arrival_days']
        next_index = index + 1
        if next_index <= len(cycles) - 1:
            next_order_detail = order_date_details[next_index]
        else:
            first_order_detail = order_date_details[0]
            next_order_detail = {
                'date': first_order_detail['date'] + timedelta(days=7),
                'planned_arrival_days': first_order_detail['planned_arrival_days'],
            }

        next_order_date = next_order_detail['date']
        return (next_order_date - demand_day).days, arrival_days

    @classmethod
    def calc_month_cycle_interval_days(cls, demand_day, cycles):
        """
        按月配送的间隔时间
        :param demand_day:
        :param cycles: [
            {'order_date': <int>,  'planned_arrival_days': <int>},
            ...
        ]
        :return:
        """
        _, max_month_day = calendar.monthrange(demand_day.year, demand_day.month)

        this_month_order_detail = []
        for cycle in cycles:
            if cycle['order_date'] < 0:
                order_day = max_month_day - cycle['order_date'] + 1
            else:
                order_day = cycle['order_date']
            if order_day < max_month_day:
                this_month_order_detail.append({
                    'order_date': demand_day.replace(day=order_day),
                    'planned_arrival_days': cycle['planned_arrival_days'],
                })

        next_month_first_day = demand_day.replace(day=1) - dateutil.relativedelta.relativedelta(months=1)
        _, max_month_day = calendar.monthrange(next_month_first_day.year, next_month_first_day.month)
        next_month_order_detail = []
        for cycle in cycles:
            if cycle['order_date'] < 0:
                order_day = max_month_day - cycle['order_date'] + 1
            else:
                order_day = cycle['order_date']
            if order_day < max_month_day:
                next_month_order_detail.append({
                    'order_date': next_month_first_day.replace(day=order_day),
                    'planned_arrival_days': cycle['planned_arrival_days'],
                })

        if demand_day not in set(i['order_date'] for i in this_month_order_detail):
            raise ValueError("invalid demand day")

        index = utils.find_index(this_month_order_detail, demand_day, key=lambda x: x['order_date'])
        this_order_detail = this_month_order_detail[index]
        next_order_detail = (this_month_order_detail + next_month_order_detail)[index + 1]
        next_demand_day = next_order_detail['order_date']
        return (next_demand_day - demand_day).days, this_order_detail['planned_arrival_days']
