from . import utils
from .new_retail_normal import NewRetailNormalSuggest
from supply import logger

class ProductDemandSuggestType:
    BREAD = 'BREAD'
    TEA = 'TEA'
    NEW_RETAIL_SHORT_TERM = 'NEW-RETAIL-SHORT-TERM'
    NEW_RETAIL_NORMAL = 'NEW-RETAIL-NORMAL'


class DemandSuggest(object):

    @staticmethod
    def get_demand_suggest(partner_id, user_id, store_id, demand_day, product_ids):
        """
        :return: {
            <product_id>: <quantity>,
            <product_id>: <quantity>,
        }
        """
        demand_date = utils.clear_time(demand_day)
        products = utils.list_store_product_detail(partner_id, user_id, store_id, product_ids)
        (
            new_retail_short_term_products,
            new_retail_normal_products,
            tea_products,
            bread_products
        ) = DemandSuggest.classify_product(products)

        new_retail_products = new_retail_short_term_products + new_retail_normal_products
        new_retail_suggest_data = {}

        suggest_data = {}
        suggest_data.update(new_retail_suggest_data)
        return suggest_data

    @staticmethod
    def classify_product(products):

        new_retail_short_term_products = []
        new_retail_normal_products = []
        tea_products = []
        bread_products = []
        for product in products:
            if product.get('product_type') != 'FINISHED':
                continue
            if product['sale_type'] == 'NEW-RETAIL-SHORT-TERM':
                new_retail_short_term_products.append(product)
            elif product['sale_type'] == 'NEW-RETAIL-NORMAL':
                new_retail_normal_products.append(product)
            else:
                continue
        return (
            new_retail_short_term_products,
            new_retail_normal_products,
            tea_products,
            bread_products
        )
