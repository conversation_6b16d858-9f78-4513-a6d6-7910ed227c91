# -*- coding: utf8 -*-
import logging
import sys
import traceback

from google.protobuf.timestamp_pb2 import Timestamp
from supply.error.exception import DataValidationException, DataDuplicationException
from datetime import datetime
from supply.utils.helper import convert_to_int, get_branch_map, \
    convert_to_decimal, get_supply_reason_map, get_uuids, MessageTopic
from supply.utils.snowflake import gen_snowflake_id
from supply.module.utils import get_transfer_product
from supply.driver.mq import mq_producer
from supply.client.metadata_service import metadata_service
from supply.client.receipt_service import receipt_service
from supply.model.warehouse.purchase_order import warehouse_purchase_order_db, WarehousePurchaseOrder, \
    warehouse_purchase_order_product_db
from supply.model.supply_doc_code import Supply_doc_code
from supply.model.operation_log import TpTransLogModel
from supply.error.exception import OrderNotExistException
from supply.error.demand import ProductError
from datetime import timedelta
from supply.model.price_chain_document_products.price_chain_document_products import PriceChainDocumentProduct,price_chain_document_products_db
from supply.utils.helper import get_guid
import logging



class PurchaseService(object):
    """采购模块业务逻辑"""

    def __init__(self):
        pass

    # noinspection PyMethodMayBeStatic
    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    # noinspection PyMethodMayBeStatic
    def get_unit_map(self, unit_ids, partner_id=None, user_id=None):
        """获取商品单位map
        :param unit_ids 单位id列表
        :return unit_map{unit_id: unit_dict}
        """
        unit_list = metadata_service.get_unit_list(ids=unit_ids, partner_id=partner_id,
                                                   user_id=user_id).get("rows", [])
        unit_map = {}
        if unit_list and len(unit_list) > 0:
            for unit in unit_list:
                unit_map[int(unit["id"])] = unit
        return unit_map

    # noinspection PyMethodMayBeStatic
    def get_product_map(self, product_ids, partner_id=None, user_id=None):
        """拉一把主档返回商品和单位的map
        :param product_ids 商品id列表
        :returns products_map{product_id: product_dict}
        """
        product_list = metadata_service.get_product_list(ids=product_ids,
                                                         return_fields="id,code,name,category,product_type,model_name,\
                                                         storage_type,sale_type",
                                                         include_units=True,
                                                         partner_id=partner_id, user_id=user_id).get("rows", [])

        products_map = {}
        if product_list and len(product_list) > 0:
            for product in product_list:
                products_map[int(product["id"])] = product
        return products_map

    def create_purchase_order(self, request, user_id=None, partner_id=None):
        """创建仓库采购单"""
        # 接受参数并校验
        order_date = datetime.fromtimestamp(request.order_date.seconds)
        arrival_date = datetime.fromtimestamp(request.arrival_date.seconds)
        logging.info("接收到创建仓库采购请求：{}-{}-{}".format(order_date, partner_id, user_id))

        if order_date.year == 1970:
            raise DataValidationException("请填写采购日期")

        query_order = warehouse_purchase_order_db.get_order_by_id(request_id=request.request_id, partner_id=partner_id)
        if query_order:
            raise DataDuplicationException("重复请求！")

        # 拉取仓库主档获取仓库名称,根据供应商id拉取供应商code
        distribution_center = metadata_service.get_distribution_center(int(request.received_by), partner_id=partner_id,
                                                                       user_id=user_id)
        received_name = distribution_center.get("name", "--")
        supplier = metadata_service.get_vendor_center(center_id=int(request.supplier_id), partner_id=partner_id,
                                                      user_id=user_id)
        supplier_name = supplier.get("name", "--")
        # 在supply_doc_code_config_main表中新建一条type为WH_PRC的记录
        order_code = Supply_doc_code.get_code_by_type('WH_PRC', partner_id, user_id)
        # 拉取用户名
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        purchase_order_data = dict(
            id=gen_snowflake_id(),
            partner_id=partner_id,
            created_by=user_id,
            updated_by=user_id,
            order_status="INITED",
        )
        purchase_order_data["remark"] = request.remark
        purchase_order_data["order_type"] = request.order_type
        purchase_order_data["received_by"] = request.received_by
        purchase_order_data["order_code"] = order_code
        purchase_order_data["received_name"] = received_name
        purchase_order_data["order_date"] = order_date
        purchase_order_data["arrival_date"] = arrival_date
        purchase_order_data["created_name"] = username
        purchase_order_data["updated_name"] = username
        purchase_order_data["supplier_id"] = request.supplier_id
        purchase_order_data["supplier_name"] = supplier_name
        purchase_order_data["purchase_type"] = request.purchase_type
        purchase_order_data["purchase_reason"] = request.purchase_reason
        purchase_order_data["request_id"] = request.request_id
        purchase_order_data["branch_type"] = request.branch_type if request.branch_type else ""
        # 遍历前端传过来的商品信息
        # 合计含税金额
        sum_price_tax = convert_to_decimal(0)
        # 合计采购金额(不含税)
        sum_price = convert_to_decimal(0)
        # 订单必须包含商品
        products = request.product_items
        if not products:
            raise ProductError("订单必须包含商品")
        # 避免循环拉主档，整理参数一把拉出来
        product_ids = []
        purchase_unit_ids = []
        accounting_unit_ids = []
        for p in products:
            product_ids.append(int(p.product_id))
            purchase_unit_ids.append(int(p.purchase_unit_id))
            accounting_unit_ids.append(int(p.accounting_unit_id))

        products_map = self.get_product_map(product_ids=product_ids, partner_id=partner_id, user_id=user_id)
        purchase_unit_map = self.get_unit_map(unit_ids=purchase_unit_ids, partner_id=partner_id, user_id=user_id)
        accounting_unit_map = self.get_unit_map(unit_ids=accounting_unit_ids, partner_id=partner_id, user_id=user_id)
        # 整理商品信息
        product_list = []
        ids = get_uuids(len(product_ids))
        for inx, product in enumerate(products):
            product_data = dict(
                id=ids[inx],
                partner_id=partner_id,
                created_by=user_id,
                updated_by=user_id,
                created_name=username,
                updated_name=username,
                unit_rate=product.unit_rate,
            )
            product_id = int(product.product_id)
            quantity = product.quantity if product.quantity else 0
            purchase_unit_id = int(product.purchase_unit_id)
            tax_rate = product.tax_rate if product.tax_rate else 0
            accounting_unit_id = int(product.accounting_unit_id)
            price_tax = convert_to_decimal(product.price_tax)
            product = products_map.get(product_id, {})
            if not product:
                raise ProductError("商品:{}，不存在".format(product_id))
            if not purchase_unit_id:
                raise ProductError("商品没有配置采购单位-{}".format(product.get("name")))
            # if not price_tax:
            #     raise ProductError("商品没有合同价-{}".format(product.get('name')))
            # if not tax_rate:
            #     raise ProductError("商品没有税率-{}".format(product.get('name')))
            # 未税价 = 含税价 / (1 + 税率)
            # 含税价总价：未税价 * 数量，不用前端传的，后端计算
            price = price_tax / convert_to_decimal(1 + tax_rate / 100)
            purchase_price = convert_to_decimal(quantity) * price_tax
            # 算总金额存采购订单表
            # 未税合计 = 含税合计 /（1 + 税率）
            # 税额 = 含税合计 - 未税合计
            sum_price_tax += purchase_price
            no_tax_price = purchase_price / convert_to_decimal(1 + tax_rate / 100)  # 未税合计
            sum_price += no_tax_price
            purchase_unit = purchase_unit_map.get(purchase_unit_id, {})
            accounting_unit = accounting_unit_map.get(accounting_unit_id, {})
            if not accounting_unit:
                raise ProductError("商品没有配置核算单位-{}".format(product.get('name')))
            if not purchase_unit:
                raise ProductError("商品没有配置采购单位-{}".format(product.get("name")))
            # 整理采购订单商品信息
            product_data["product_id"] = product_id
            product_data["quantity"] = quantity
            product_data["price_tax"] = price_tax
            product_data["purchase_unit_id"] = purchase_unit_id
            product_data["purchase_price"] = round(purchase_price, 8)
            product_data["price"] = round(price, 8)
            product_data["tax_rate"] = tax_rate
            product_data["product_name"] = product.get("name")
            product_data["product_code"] = product.get("code")
            product_data["product_type"] = product.get("product_type")
            product_data["order_id"] = purchase_order_data.get("id")
            product_data["sale_type"] = product.get("sale_type")
            product_data["product_category_id"] = convert_to_int(product.get("category", 0))
            product_data["storage_type"] = product.get("storage_type")
            product_data["purchase_unit_id"] = purchase_unit_id
            product_data["purchase_unit_name"] = purchase_unit.get('name')
            product_data["purchase_unit_spec"] = purchase_unit.get("code")
            product_data["spec"] = product.get("model_name")
            product_data["accounting_unit_id"] = accounting_unit_id
            product_data["accounting_unit_name"] = accounting_unit.get("name")
            product_data["accounting_unit_spec"] = accounting_unit.get("code")
            product_list.append(product_data)
        purchase_order_data["sum_price_tax"] = round(sum_price_tax, 8)
        purchase_order_data["sum_price"] = round(sum_price, 8)
        purchase_order_data["sum_tax"] = round(sum_price_tax - sum_price, 8)
        # 保存采购订单信息
        create_order_result = warehouse_purchase_order_db.create_purchase_order(purchase_order_data, product_list)
        if create_order_result is True:
            response = {"order_id": purchase_order_data.get("id")}
        else:
            raise DataValidationException("新建采购单失败，请检查订单信息")
        return response

    def list_purchase_order(self, request, user_id=None, partner_id=None):
        """仓库采购订单列表查询"""
        start_date = request.start_date
        end_date = request.end_date
        order_code = request.order_code
        order_status = request.order_status
        order_type = request.order_type
        purchase_type = request.purchase_type
        supplier_ids = request.supplier_ids
        received_ids = request.received_ids
        purchase_reason = request.purchase_reason
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        sort_type = request.sort_type
        sort = request.sort
        branch_type = request.branch_type

        product_ids = list(request.product_ids) if request.product_ids else []
        if not start_date or not end_date:
            raise DataValidationException("请填写采购日期")
        if order_status == 'all':
            order_status = None
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        ret = {}

        order_list = warehouse_purchase_order_db.list_purchase_order(user_id=user_id, partner_id=partner_id,
                                                                     start_date=start_date, end_date=end_date,
                                                                     order_code=order_code, order_status=order_status,
                                                                     order_type=order_type, purchase_type=purchase_type,
                                                                     supplier_ids=list(supplier_ids),
                                                                     received_ids=list(received_ids),
                                                                     branch_type=branch_type,
                                                                     purchase_reason=purchase_reason, limit=limit,
                                                                     offset=offset, include_total=include_total,
                                                                     sort_type=sort_type, sort=sort,
                                                                     product_ids=product_ids)
        if isinstance(order_list, tuple):
            total, query_set = order_list
            ret["total"] = total
        else:
            query_set = order_list
        result_list = []
        reason_map = get_supply_reason_map(_type="PURCHASE", partner_id=partner_id, user_id=user_id)
        if query_set:
            distrcenter_ids = []
            for q in query_set:
                distrcenter_ids.append(int(q.received_by))
            currencys = dict()
            distrcenterRet = metadata_service.list_entity(schema_name="distrcenter", ids=distrcenter_ids,
                                                          return_fields="",
                                                          relation="all",
                                                          partner_id=partner_id,
                                                          user_id=user_id).get('rows', [])
            logging.info("list_entity-distrcenter----{}".format(distrcenterRet))
            distrcenter_company = dict()
            if len(distrcenterRet) > 0:
                company_ids = []
                for distrcenter in distrcenterRet:
                    company_id = distrcenter['fields'].get('relation', {}).get('company_info')
                    company_ids.append(int(company_id))

                    distrcenter_company[int(distrcenter['id'])] = int(company_id)
                if company_ids:
                    companyRet = metadata_service.list_entity(schema_name="company-info",
                                                              ids=company_ids,
                                                              return_fields="currency",
                                                              relation="",
                                                              partner_id=partner_id,
                                                              user_id=user_id).get('rows', [])
                    logging.info("list_entity-company----{}".format(companyRet))
                    if len(companyRet) > 0:
                        for company in companyRet:
                            currencys[int(company['id'])] = company['fields'].get('currency','')

            for q in query_set:
                row = dict(
                    order_id=q.id,
                    order_code=q.order_code,
                    order_status=q.order_status,
                    order_date=self.get_timestamp(q.order_date),
                    received_by=q.received_by,
                    received_name=q.received_name,
                    order_type=q.order_type,
                    supplier_id=q.supplier_id,
                    branch_type=q.branch_type,
                    supplier_name=q.supplier_name,
                    sum_price_tax=q.sum_price_tax,
                    sum_price=q.sum_price,
                    sum_tax=q.sum_tax,
                    created_by=q.created_by,
                    created_name=q.created_name,
                    created_at=self.get_timestamp(q.created_at),
                    updated_by=q.updated_by,
                    updated_name=q.updated_name,
                    updated_at=self.get_timestamp(q.updated_at),
                    purchase_reason=reason_map.get(q.purchase_reason, ''),
                    purchase_type=q.purchase_type
                )
                company_id = distrcenter_company.get(q.received_by)
                if company_id:
                    if currencys.get(company_id):
                        row['currency'] = currencys.get(company_id)
                result_list.append(row)
        ret["rows"] = result_list
        return ret

    def list_purchase_order_print(self, request, user_id=None, partner_id=None):
        """仓库采购订单打印列表查询"""
        order_ids = request.order_ids
        logging.info("接收到参数: {}-{}-{}".format(order_ids, partner_id, user_id))
        rows = []
        row_info = {}
        ret = {}
        if order_ids != None and len(order_ids) > 0:
            ids = [int(id) for id in order_ids]
            query_orders = warehouse_purchase_order_db.get_orders_by_ids(order_ids=ids, partner_id=partner_id)
            if not query_orders:
                raise OrderNotExistException("查询不到订单！")
            logging.info("query_orders: {}".format(query_orders))
            # 根据订单id获取商品信息
            products = warehouse_purchase_order_product_db.get_orders_product(user_id=user_id, partner_id=partner_id,
                                                                              order_ids=ids)
            logging.info("query products: {}".format(products))
            product_info = {}
            for product in products:
                if product.order_id not in product_info:
                    product_info[product.order_id] = []
                row = dict(
                    id=product.id,
                    created_by=product.created_by,
                    created_name=product.created_name,
                    updated_by=product.updated_by,
                    updated_name=product.updated_name,
                    product_id=product.product_id,
                    product_code=product.product_code,
                    product_name=product.product_name,
                    product_type=product.product_type,
                    product_category_id=product.product_category_id,
                    sale_type=product.sale_type,
                    spec=product.spec,
                    quantity=product.quantity,
                    purchase_unit_id=product.purchase_unit_id,
                    purchase_unit_name=product.purchase_unit_name,
                    purchase_unit_spec=product.purchase_unit_spec,
                    accounting_unit_id=product.accounting_unit_id,
                    accounting_unit_name=product.accounting_unit_name,
                    accounting_unit_spec=product.accounting_unit_spec,
                    unit_rate=product.unit_rate,
                    tax_rate=product.tax_rate,
                    purchase_price=product.purchase_price,
                    price_tax=product.price_tax,
                    price=product.price
                )
                product_info[product.order_id].append(row)
            logging.info("product_info: {}".format(product_info))
            for query_order in query_orders:
                row = dict(
                    order_id=query_order.id,
                    created_by=query_order.created_by,
                    created_name=query_order.created_name,
                    updated_by=query_order.updated_by,
                    updated_name=query_order.updated_name,
                    order_code=query_order.order_code,
                    order_status=query_order.order_status,
                    order_date=self.get_timestamp(query_order.order_date),
                    received_by=query_order.received_by,
                    received_name=query_order.received_name,
                    order_type=query_order.order_type,
                    supplier_id=query_order.supplier_id,
                    supplier_name=query_order.supplier_name,
                    purchase_type=query_order.purchase_type,
                    purchase_reason=query_order.purchase_reason,
                    remark=query_order.remark,
                    branch_type=query_order.branch_type,
                    created_at=self.get_timestamp(query_order.created_at),
                    updated_at=self.get_timestamp(query_order.updated_at),
                    products=None
                )
                if query_order.id in product_info:
                    row["products"] = product_info[query_order.id]
                row_info[query_order.id] = row
            for id in ids:
                if id in row_info:
                    rows.append(row_info[id])
            logging.info("rows: {}".format(rows))
        ret["rows"] = rows
        return ret

    # noinspection PyMethodMayBeStatic
    def change_order_status(self, request, user_id=None, partner_id=None):
        """修改订单状态"""
        order_id = convert_to_int(request.order_id)
        status = request.status
        status_list = ["SUBMITTED", "REJECTED", "APPROVED", "CANCELLED", "SUCCESS"]
        ret = {}
        # 校验该订单是否存在
        query_order = WarehousePurchaseOrder.get(order_id)
        if not query_order:
            raise OrderNotExistException("该订单不存在！")
        if query_order.order_status == "APPROVED" and status in ["REJECTED", "CANCELLED"]:
            raise DataValidationException("已审核的订单不允许驳回和取消！")
        # 拉取用户名
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        if status in status_list:
            # 采购单审核生成要货单优化，先掉用receipt服务再修改状态，这样可以服务失败可以重试，防止单据未流转
            if status == "APPROVED":
                # 拉取商品信息
                products_query = warehouse_purchase_order_product_db.get_order_product(user_id=user_id,
                                                                                       partner_id=partner_id,
                                                                                       order_id=order_id)
                products = []
                for product in products_query:
                    row = dict(
                        product_id=product.product_id,
                        product_code=product.product_code,
                        product_name=product.product_name,
                        storage_type=product.storage_type,
                        order_quantity=product.quantity,  # 采购数量
                        unit_id=product.purchase_unit_id,
                        unit_name=product.purchase_unit_name,
                        unit_spec=product.spec,
                        category_id=product.product_category_id,
                        cost_price=product.price,
                        tax_price=product.price_tax,
                        tax_rate=product.tax_rate,
                        unit_rate=product.unit_rate
                    )
                    products.append(row)
                purchase_demand_code = Supply_doc_code.get_code_by_type('WH_PRC_DM', partner_id, user_id)
                # purchase_receipt_code = Supply_doc_code.get_code_by_type('WH_PRC_RC', partner_id, user_id)
                # 采购单审核调用receipt服务生成要货单和收货单
                demand_date = query_order.order_date
                expect_date = query_order.arrival_date
                if isinstance(demand_date, datetime):
                    demand_date = Timestamp(seconds=int(demand_date.timestamp()))
                if isinstance(expect_date, datetime):
                    expect_date = Timestamp(seconds=int(expect_date.timestamp()))
                # 判断是否生成要货单
                now_datetime = datetime.now()
                end_date = Timestamp(seconds=int(now_datetime.timestamp()))
                try:
                    orders_entity = receipt_service.list_orders(batch_id=order_id, end_date=end_date,
                                                                end_arrival_date=end_date, partner_id=partner_id,
                                                                user_id=user_id)
                    total = orders_entity.total
                    rows = orders_entity.rows
                    if total > 0 or rows:
                        logging.info("该采购单已经生成订货单-{}".format(order_id))
                        pass
                    else:
                        order_id = gen_snowflake_id()
                        main_branch_type = "W" if query_order.branch_type == "WAREHOUSE" else "M"
                        entity = receipt_service.create_orders(batch_id=query_order.id,
                                                               batch_code=query_order.order_code,
                                                               order_id=order_id, order_code=purchase_demand_code,
                                                               main_branch_type=main_branch_type,
                                                               demand_type=query_order.purchase_reason,
                                                               batch_type="PURCHASE",
                                                               receive_by=query_order.received_by,
                                                               delivery_by=query_order.supplier_id,
                                                               storage_type=None, distr_type="PUR",
                                                               demand_date=demand_date,
                                                               delivery_date=None, expect_date=expect_date,
                                                               products=products, partner_id=partner_id,
                                                               user_id=user_id)
                        logging.info("采购单生成订货单-{}".format(entity))
                        logging.info(entity)
                        if entity:
                            logging.info("采购单生成订货单-{}-{}".format(entity.get("id"), entity.get("status")))
                        else:
                            logging.info("Purchase create_orders entity: {}".format(entity))
                            ret["description"] = "Purchase create order failed! Please try again:{}".format(entity)
                            ret["order_id"] = None
                            return ret
                except Exception as e:
                    logging.error(str(e))
                    ret["description"] = "Purchase create order failed! Please try again {}".format(e)
                    ret["order_id"] = None
                    return ret

            query = warehouse_purchase_order_db.change_order_status(user_id=user_id, partner_id=partner_id,
                                                                    username=username, order_id=query_order.id,
                                                                    status=status)

            if status == "APPROVED":
                # vendor单据同步给三方
                message = {
                    'doc_resource': 'w_purchase' if query_order.branch_type == "WAREHOUSE" else 'm_purchase',
                    'doc_id': query_order.id,
                    'partner_id': partner_id,
                    'user_id': user_id,
                    'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)

                # 同步记录落表，用于后续补偿
                tp_trans_log = {
                    'id': query_order.id,
                    'doc_code': query_order.order_code,
                    'doc_type': 'w_purchase' if query_order.branch_type == "WAREHOUSE" else 'm_purchase',
                    'status': 'inited',
                    'msg': str(message),
                    'partner_id': partner_id,
                    'created_by': user_id,
                    'created_at': datetime.utcnow(),
                    'updated_at': datetime.utcnow()
                }

                # 一把更新数据库
                TpTransLogModel.create_logs_list([tp_trans_log])

            if status == "SUBMITTED":
                demand_products = warehouse_purchase_order_product_db.get_order_product(user_id=user_id,
                                                                                        partner_id=partner_id,
                                                                                        order_id=order_id)
                item_codes = []
                for product in demand_products:
                    item_codes.append(product.product_code)
                currency = ""
                distrcenterRet = metadata_service.list_entity(schema_name="distrcenter", ids=[int(query_order.received_by)],
                                                          return_fields="",
                                                          relation="all",
                                                          partner_id=partner_id,
                                                          user_id=user_id).get('rows', [])

                if len(distrcenterRet)>0:
                    company_id = distrcenterRet[0]['fields'].get('relation',{}).get('company_info')
                    if company_id:
                        companyRet = metadata_service.list_entity(schema_name="company-info",
                                                                      ids=[int(company_id)],
                                                                      return_fields="currency",
                                                                      relation="all",
                                                                      partner_id=partner_id,
                                                                      user_id=user_id).get('rows', [])
                        if len(companyRet)>0:
                            currency = companyRet[0]['fields']['currency']
                product_data = []
                for product in demand_products:
                    price = dict()
                    price['id'] = get_guid()
                    price['partner_id'] = partner_id
                    price['doc_id'] = order_id
                    price['product_id'] = product.product_id
                    price['doc_type'] = "demand"
                    price['currency'] = currency
                    price['source_id'] = order_id
                    price['unit_rate'] = product.unit_rate
                    price['price'] = product.price

                    product_data.append(price)
                price_chain_document_products_db.create_price_chain_document_products(product_data)
            ret["description"] = "success"
            ret["order_id"] = query.id
        else:
            ret["description"] = "Status is illegal"
            ret["order_id"] = None
        return ret

    def update_purchase_order(self, request, user_id=None, partner_id=None):
        """更新采购单信息"""
        # 保存返回结果
        ret = {}
        # 接受参数并校验
        order_id = convert_to_int(request.order_id)
        order_date = request.order_date
        if not order_date:
            raise DataValidationException("请填写订货日期")
        order_date = datetime.fromtimestamp(request.order_date.seconds)
        received_by = convert_to_int(request.received_by)
        supplier_id = convert_to_int(request.supplier_id)
        order_type = request.order_type
        purchase_type = request.purchase_type
        purchase_reason = request.purchase_reason
        product_items = request.product_items
        # 校验该订单是否存在
        query_order = warehouse_purchase_order_db.get_order_by_id(order_id=order_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该订单不存在！")
        if query_order.order_status in ["SUBMITTED", "APPROVED"]:
            raise DataValidationException("已提交或已审核的订单不允许修改订单信息")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 拉取仓库主档获取仓库名称,根据供应商id拉取供应商code
        distribution_center = metadata_service.get_distribution_center(int(request.received_by),
                                                                       partner_id=partner_id,
                                                                       user_id=user_id)
        received_name = distribution_center.get("name")
        supplier = metadata_service.get_vendor_center(center_id=int(request.supplier_id), partner_id=partner_id,
                                                      user_id=user_id)
        supplier_name = supplier.get("name")
        purchase_order_data = dict(
            created_by=user_id,
            created_name=username,
            updated_name=username,
            updated_by=user_id,
            remark=request.remark,
            order_date=order_date,
            received_by=received_by,
            supplier_id=supplier_id,
            order_type=order_type,
            purchase_type=purchase_type,
            purchase_reason=purchase_reason,
            received_name=received_name,
            supplier_name=supplier_name,
        )
        # 合计含税金额
        sum_price_tax = convert_to_decimal(0)
        # 合计采购金额(不含税)
        sum_price = convert_to_decimal(0)
        if not product_items:
            raise ProductError("订单必须包含商品")
        product_ids = []
        purchase_unit_ids = []
        accounting_unit_ids = []

        for p in product_items:
            product_ids.append(int(p.product_id))
            purchase_unit_ids.append(int(p.purchase_unit_id))
            accounting_unit_ids.append(int(p.accounting_unit_id))
        products_map = self.get_product_map(product_ids=product_ids, partner_id=partner_id, user_id=user_id)
        purchase_unit_map = self.get_unit_map(unit_ids=purchase_unit_ids, partner_id=partner_id, user_id=user_id)
        accounting_unit_map = self.get_unit_map(unit_ids=accounting_unit_ids, partner_id=partner_id,
                                                user_id=user_id)
        # 整理商品信息
        product_list = []
        for product in product_items:
            product_data = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                updated_by=user_id,
                created_name=username,
                updated_name=username,
                unit_rate=product.unit_rate,
            )
            product_id = int(product.product_id)
            quantity = product.quantity if product.quantity else 0
            purchase_unit_id = int(product.purchase_unit_id)
            tax_rate = product.tax_rate if product.tax_rate else 0
            accounting_unit_id = int(product.accounting_unit_id)
            price_tax = convert_to_decimal(product.price_tax)
            product = products_map.get(product_id, {})
            if not product:
                raise ProductError("商品:{}，不存在".format(product_id))
            # if not price_tax:
            #     raise ProductError("商品没有合同价-{}".format(product.get('name')))
            # if not tax_rate:
            #     raise ProductError("商品没有税率-{}".format(product.get('name')))
            # 未税价 = 含税价 / (1 + 税率)
            # 含税价总价：未税价 * 数量，不用前端传的，后端计算
            price = price_tax / convert_to_decimal(1 + tax_rate / 100)
            purchase_price = convert_to_decimal(quantity) * price_tax
            # 算总金额存采购订单表
            sum_price_tax += purchase_price
            no_tax_price = purchase_price / convert_to_decimal(1 + tax_rate / 100)  # 未税合计
            sum_price += no_tax_price
            purchase_unit = purchase_unit_map.get(purchase_unit_id, {})
            accounting_unit = accounting_unit_map.get(accounting_unit_id, {})
            if not accounting_unit:
                raise ProductError("商品没有配置核算单位-{}".format(product.get('name')))
            if not purchase_unit:
                raise ProductError("商品没有配置采购单位-{}".format(product.get("name")))
            # 整理采购订单商品信息
            product_data["product_id"] = product_id
            product_data["quantity"] = quantity
            product_data["price_tax"] = price_tax
            product_data["price"] = round(price, 8)
            product_data["purchase_unit_id"] = purchase_unit_id
            product_data["purchase_price"] = round(purchase_price, 8)
            product_data["tax_rate"] = tax_rate
            product_data["product_name"] = product.get("name")
            product_data["product_code"] = product.get("code")
            product_data["product_type"] = product.get("product_type")
            product_data["order_id"] = order_id
            product_data["sale_type"] = product.get("sale_type")
            product_data["product_category_id"] = product.get("product_category_id")
            product_data["storage_type"] = product.get("storage_type")
            product_data["purchase_unit_id"] = purchase_unit_id
            product_data["purchase_unit_name"] = purchase_unit.get('name')
            product_data["purchase_unit_spec"] = purchase_unit.get("code")
            product_data["spec"] = product.get("model_name")
            product_data["accounting_unit_id"] = accounting_unit_id
            product_data["accounting_unit_name"] = accounting_unit.get("name")
            product_data["accounting_unit_spec"] = accounting_unit.get("code")
            product_list.append(product_data)
        purchase_order_data["sum_price_tax"] = round(sum_price_tax, 8)
        purchase_order_data["sum_price"] = round(sum_price, 8)
        purchase_order_data["sum_tax"] = round(sum_price_tax - sum_price, 8)
        # 更新采购订单信息
        update_order = warehouse_purchase_order_db.update_purchase_order(order_id=order_id,
                                                                         order_data=purchase_order_data,
                                                                         product_data=product_list,
                                                                         partner_id=partner_id)
        if update_order is True:
            ret["description"] = "success"
            ret["order_id"] = order_id
        else:
            ret["description"] = "update order failure"
        return ret

    def get_order_detail_by_id(self, request, user_id=None, partner_id=None):
        """查询采购单详情"""
        order_id = request.order_id
        # 校验该订单是否存在
        query_order = warehouse_purchase_order_db.get_order_by_id(order_id=order_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该订单不存在！")
        ret = dict(
            order_id=query_order.id,
            created_by=query_order.created_by,
            created_name=query_order.created_name,
            updated_by=query_order.updated_by,
            updated_name=query_order.updated_name,
            order_code=query_order.order_code,
            order_status=query_order.order_status,
            order_date=self.get_timestamp(query_order.order_date),
            received_by=query_order.received_by,
            received_name=query_order.received_name,
            order_type=query_order.order_type,
            supplier_id=query_order.supplier_id,
            supplier_name=query_order.supplier_name,
            purchase_type=query_order.purchase_type,
            purchase_reason=query_order.purchase_reason,
            remark=query_order.remark,
            branch_type=query_order.branch_type,
            created_at=self.get_timestamp(query_order.created_at),
            updated_at=self.get_timestamp(query_order.updated_at),
            sum_price_tax=query_order.sum_price_tax
        )

        currency = ""
        distrcenterRet = metadata_service.list_entity(schema_name="distrcenter", ids=[int(query_order.received_by)],
                                                      return_fields="",
                                                      relation="all",
                                                      partner_id=partner_id,
                                                      user_id=user_id).get('rows', [])

        if len(distrcenterRet) > 0:
            company_id = distrcenterRet[0]['fields'].get('relation', {}).get('company_info')
            if company_id:
                companyRet = metadata_service.list_entity(schema_name="company-info",
                                                          ids=[int(company_id)],
                                                          return_fields="currency",
                                                          relation="all",
                                                          partner_id=partner_id,
                                                          user_id=user_id).get('rows', [])
                if len(companyRet) > 0:
                    currency = companyRet[0]['fields']['currency']

        # 根据订单id拿商品信息
        products = warehouse_purchase_order_product_db.get_order_product(user_id=user_id, partner_id=partner_id,
                                                                         order_id=order_id)
        product_list = []
        total_quantity = 0
        for product in products:
            row = dict(
                id=product.id,
                created_by=product.created_by,
                created_name=product.created_name,
                updated_by=product.updated_by,
                updated_name=product.updated_name,
                product_id=product.product_id,
                product_code=product.product_code,
                product_name=product.product_name,
                product_type=product.product_type,
                product_category_id=product.product_category_id,
                sale_type=product.sale_type,
                spec=product.spec,
                quantity=product.quantity,
                purchase_unit_id=product.purchase_unit_id,
                purchase_unit_name=product.purchase_unit_name,
                purchase_unit_spec=product.purchase_unit_spec,
                accounting_unit_id=product.accounting_unit_id,
                accounting_unit_name=product.accounting_unit_name,
                accounting_unit_spec=product.accounting_unit_spec,
                unit_rate=product.unit_rate,
                tax_rate=product.tax_rate,
                purchase_price=product.purchase_price,
                price_tax=product.price_tax,
                price=product.price,
                currency=currency
            )
            product_list.append(row)
            total_quantity += product.quantity
        ret["products"] = product_list
        ret["total_product"] = len(product_list)
        ret["total_quantity"] = total_quantity
        return ret

    def get_orders_detail_by_ids(self, request, user_id=None, partner_id=None):
        """查询采购单详情"""
        order_ids = request.order_ids
        rows = []
        row_info = {}
        ret = {}
        # 校验该订单是否存在
        if order_ids != None and len(order_ids) > 0:
            ids = [int(id) for id in order_ids]
            query_orders = warehouse_purchase_order_db.get_orders_by_ids(order_ids=ids, partner_id=partner_id)
            if not query_orders:
                raise OrderNotExistException("订单不存在！")
            # 根据订单ids拿商品信息
            products = warehouse_purchase_order_product_db.get_orders_product(user_id=user_id, partner_id=partner_id,
                                                                              order_ids=ids)
            logging.info("query products: {}".format(products))
            product_info = {}
            for product in products:
                if product.order_id not in product_info:
                    product_info[product.order_id] = []
                row = dict(
                    id=product.id,
                    created_by=product.created_by,
                    created_name=product.created_name,
                    updated_by=product.updated_by,
                    updated_name=product.updated_name,
                    product_id=product.product_id,
                    product_code=product.product_code,
                    product_name=product.product_name,
                    product_type=product.product_type,
                    product_category_id=product.product_category_id,
                    sale_type=product.sale_type,
                    spec=product.spec,
                    quantity=product.quantity,
                    purchase_unit_id=product.purchase_unit_id,
                    purchase_unit_name=product.purchase_unit_name,
                    purchase_unit_spec=product.purchase_unit_spec,
                    accounting_unit_id=product.accounting_unit_id,
                    accounting_unit_name=product.accounting_unit_name,
                    accounting_unit_spec=product.accounting_unit_spec,
                    unit_rate=product.unit_rate,
                    tax_rate=product.tax_rate,
                    purchase_price=product.purchase_price,
                    price_tax=product.price_tax,
                    price=product.price
                )
                product_info[product.order_id].append(row)
            logging.info("product_info: {}".format(product_info))
            for query_order in query_orders:
                row = dict(
                    order_id=query_order.id,
                    created_by=query_order.created_by,
                    created_name=query_order.created_name,
                    updated_by=query_order.updated_by,
                    updated_name=query_order.updated_name,
                    order_code=query_order.order_code,
                    order_status=query_order.order_status,
                    order_date=self.get_timestamp(query_order.order_date),
                    received_by=query_order.received_by,
                    received_name=query_order.received_name,
                    order_type=query_order.order_type,
                    supplier_id=query_order.supplier_id,
                    supplier_name=query_order.supplier_name,
                    purchase_type=query_order.purchase_type,
                    purchase_reason=query_order.purchase_reason,
                    remark=query_order.remark,
                    branch_type=query_order.branch_type,
                    created_at=self.get_timestamp(query_order.created_at),
                    updated_at=self.get_timestamp(query_order.updated_at)
                )
                if query_order.id in product_info:
                    row["products"] = product_info[query_order.id]
                row_info[query_order.id] = row
            for order_id in ids:
                if order_id in row_info:
                    rows.append(row_info[order_id])
            logging.info("rows: {}".format(rows))
        ret["rows"] = rows
        return ret

    def get_purchase_order_by_id(self, order_id, partner_id=None):
        query_order = warehouse_purchase_order_db.get_order_by_id(order_id=order_id, partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该订单不存在！")
        return query_order

    def get_product_list_by_WHid(self, request, user_id=None, partner_id=None, branch_type=None):
        """根据仓库id拉商品列表，仓库下关联了商品分类，
        先拉取主档把该仓库下商品分类id拿到再拉取商品主档"""
        res = {}
        branch_id = convert_to_int(request.id)
        product_category_ids = request.product_category_ids
        return_all_products = request.return_all_products
        return_metadata_products = request.return_metadata_products
        search = request.search
        search_fields = request.search_fields
        # if not warehouse_id:
        #     raise DataValidationException("请传入仓库id")
        try:
            if not return_metadata_products:
                # 先根据仓库拉取仓库关联的商品分类
                if branch_type == "MACHINING_CENTER":
                    entity = metadata_service.get_entity_by_id(id=branch_id, schema_name="MACHINING_CENTER",
                                                               partner_id=partner_id, user_id=user_id)
                else:
                    entity = metadata_service.get_entity_by_id(id=branch_id, schema_name="distrcenter",
                                                               partner_id=partner_id, user_id=user_id)
                if not entity:
                    raise DataValidationException("主档未找到该组织！")
                fields = entity.get("fields")
                relation = None
                if isinstance(fields, dict):
                    relation = fields.get("relation")
                if not relation or not relation.get("product_category"):
                    logging.info("该组织下未关联商品分类-{}".format(branch_id))
                    res["rows"] = {}
                    res["total"] = 0
                    return res

                product_category = relation.get("product_category")
                if product_category_ids:
                    product_category_ids = [str(category_id) for category_id in product_category_ids]
                    product_category_ids = list(set(product_category_ids) & set(product_category))
                else:
                    product_category_ids = product_category
                relation_filters = dict(product_category=product_category_ids)

            else:
                if product_category_ids:
                    product_category_ids = [str(category_id) for category_id in product_category_ids]
                    relation_filters = dict(product_category=product_category_ids)
                else:
                    relation_filters = None
            # 然后根据商品分类拉取商品主档
            if return_all_products is True:
                filters = None
            else:
                filters = {"status__eq": "ENABLED", "bom_type__neq": 'MANUFACTURE'}
            products_by_category = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                     include_units=True,
                                                                     filters=filters,
                                                                     return_fields="scope_id,name,code,second_code,\
                                                                     sale_type,product_type,bom_type,storage_type,\
                                                                     status,alias,category,updated,model_code,model_name,\
                                                                     default_receiving_deviation_min,\
                                                                     default_receiving_deviation_max,\
                                                                     default_purchase_deviation_min,\
                                                                     default_purchase_deviation_max,\
                                                                     ledger_class",
                                                                     partner_id=partner_id,
                                                                     user_id=user_id,
                                                                     search=search,
                                                                     search_fields=search_fields,
                                                                     include_total=True)
            res["rows"] = products_by_category.get("rows") if products_by_category.get("rows") else {}
            res["total"] = products_by_category.get("total") if products_by_category.get("total") else 0
        except Exception as e:
            logging.error("根据仓库拉取商品列表失败:{}-{}-{}".format(e, sys.exc_info(), traceback.format_exc()))
        return res

    # noinspection PyMethodMayBeStatic
    def get_purchase_bi(self, request, user_id=None, partner_id=None):
        """查询采购单详情报表"""
        s_ids = [convert_to_int(s_id) for s_id in
                 request.s_ids] if request.s_ids else None
        wh_ids = [convert_to_int(wh_id) for wh_id in
                  request.wh_ids] if request.wh_ids else None
        category_ids = [convert_to_int(category_id) for category_id in
                        request.category_ids] if request.category_ids else None
        product_ids = [convert_to_int(product_id) for product_id in
                       request.product_ids] if request.product_ids else None
        start_date = request.start_date
        end_date = request.end_date
        limit = request.limit
        offset = request.offset
        branch_type = request.branch_type

        if not isinstance(start_date, datetime):
            start_date = datetime.fromtimestamp(start_date.seconds)
        if not isinstance(end_date, datetime):
            end_date = datetime.fromtimestamp(end_date.seconds)
        category_list = []
        list_products = []

        if category_ids:
            for category_id in category_ids:
                category_list.append(str(category_id))
            relation_filters = {'product_category': category_list}
            list_products = metadata_service.get_product_list(relation_filters=relation_filters,
                                                              partner_id=partner_id, user_id=user_id).get('rows')

        bi_data, total = warehouse_purchase_order_product_db.bi_get_purchase_detailed(partner_id=partner_id,
                                                                                      s_ids=s_ids, wh_ids=wh_ids,
                                                                                      product_ids=product_ids,
                                                                                      branch_type=branch_type,
                                                                                      start_date=start_date,
                                                                                      end_date=end_date,
                                                                                      limit=limit, offset=offset)
        p_ids = []
        s_ids = []
        w_ids = []
        order_ids = []
        if not bi_data:
            return None, None
        for d in bi_data:
            p_ids.append(int(d.product_id))
            w_ids.append(int(d.received_by))
            s_ids.append(int(d.supplier_id))
            order_ids.append(d.id)
        order_ids = list(set(order_ids))

        products = metadata_service.get_product_list(ids=p_ids, return_fields="id,name,code,category",
                                                     partner_id=partner_id, user_id=user_id)
        products = products['rows']

        branch_map = get_branch_map(branch_ids=w_ids, branch_type=branch_type, partner_id=partner_id,
                                    user_id=user_id, return_fields="id,code,name")

        categorys = metadata_service.get_product_category_list(return_fields="id,code,name",
                                                               partner_id=partner_id, user_id=user_id)
        categorys = categorys['rows']

        list_vendor = metadata_service.list_entity(schema_name='vendor',
                                                   return_fields="id,code,name",
                                                   partner_id=partner_id, user_id=user_id).get('rows', [])
        new_branch_list = []
        for branch in list_vendor:
            new_branch_list.append({
                'id': branch['id'],
                'code': branch['fields'].get('code'),
                'name': branch['fields'].get('name')
            })
        list_vendor = new_branch_list

        result = []
        for data in bi_data:
            r = dict()
            r['id'] = data.id
            r['order_id'] = data.id
            r['wh_id'] = data.received_by
            r['vendor_id'] = data.supplier_id
            branch = branch_map.get(data.received_by) if branch_map.get(data.received_by) else {}
            r['wh_code'] = branch.get('code')
            r['wh_name'] = branch.get('name')
            for vd in list_vendor:
                if int(vd['id']) == data.supplier_id:
                    r['vendor_code'] = vd.get('code')
                    r['vendor_name'] = vd.get('name')

            r['product_id'] = data.product_id
            for p in products:
                if int(p['id']) == data.product_id:
                    r['product_code'] = p.get('code')
                    r['product_name'] = p.get('name')
                    if p.get('category'):
                        category_id = int(p['category'])
                        r['category_id'] = category_id
                        for category in categorys:
                            if int(category['id']) == category_id:
                                r['category_code'] = category.get('code')
                                r['category_name'] = category.get('name')

            r['order_code'] = data.order_code
            r['order_date'] = (data.order_date + timedelta(hours=8)).strftime('%Y-%m-%d')
            r['arrival_date'] = data.arrival_date.strftime('%Y-%m-%d') if data.arrival_date else None
            r['arrival_date'] = data.arrival_date.strftime('%Y-%m-%d') if data.arrival_date else None
            r['unit_id'] = data.purchase_unit_id
            r['unit_name'] = data.purchase_unit_name
            r['purchase_quantity'] = data.quantity
            r['price_tax'] = float(data.price_tax) * float(data.quantity)
            r['price'] = float(data.price) * float(data.quantity)
            r['tax_rate'] = data.tax_rate
            r['tax'] = r['price_tax'] - r['price']
            r['remark'] = data.remark

            result.append(r)
            # print(result)
        total = dict(count=total)
        return total, result


purchase_service = PurchaseService()
