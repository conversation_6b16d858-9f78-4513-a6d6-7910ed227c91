# -*- coding: utf-8 -*-
from datetime import datetime
from sqlalchemy import func
import logging
from datetime import datetime
from decimal import Decimal
from hex_exception import RecordAlreadyExist
from ..error.exception import NoResultFoundError, StatusUnavailable
import logging

from ..driver.mysql import db_commit, session_maker, DummyTransaction, session
from ..utils.helper import set_model_from_db, convert_to_int, convert_to_datetime, set_db
from ..utils.encode import encodeUTF8
from ..utils.exception import DataValidationException
from ..utils.resulting import ErrorCode
from ..utils.snowflake import gen_snowflake_id
from ..utils import pb2dict
from ..client.metadata_service import metadata_service 
from ..task.message_service_pub import MessageServicePub

from ..model.supply_doc_code import Supply_doc_code

from ..model.assets import AssetsModel, AssetsProductModel




class AssetsService():
    '''固定资产相关服务
    service: 
        - create_assets()：新建固定资产单
        - confirm_assets()：确认固定资产单
        - get_asset_by_id()：根据id获取固定资产单
        - get_asset_products_by_id()：根据id获取固定资产商品详情
        - list_assets()：枚举固定资产单
        - create_assets_from_jde()：JDE发送请求创建固定资产单
    '''

    # 未在实际场景使用
    @db_commit
    def create_assets(self, request, partner_id, user_id):
        if request.doco:
            # 已生成固定资产收货单
            if_assets_exists = AssetsModel.get_assets_by_doco(request.doco)
        else:
            raise DataValidationException("lose required data:DOCO!")

        # 未生成对应收货单,初始化收货单
        if not if_assets_exists:
            assets_detail = pb2dict(request)
            assets_detail['id'] = gen_snowflake_id()
            assets_detail['code'] = Supply_doc_code.get_code_by_type('REC_OD',partner_id=4183192445833445399, user_id=4186056888460247152)
            assets_detail['status'] = 'INITED'
            assets_detail['partner_id'] = 4183192445833445399
            # assets_detail['created_by'] = user_id
            assets_detail['created_at'] = datetime.utcnow()
            assets_detail['created_name'] = 'JDE'

            mcu_code = assets_detail['mcu']
            mcu_detail = metadata_service.get_distribution_center_list(search=mcu_code, search_fields='code', return_fields="name, id").get('rows')
            mcu_detail = mcu_detail[0]
            assets_detail['mcu_id'] = mcu_detail['id']
            assets_detail['mcu_name'] = mcu_detail['name']

            store_code = assets_detail['an8']
            store_detail = metadata_service.get_store_list(filters={"code__in":[store_code]}, partner_id=4183192445833445399, user_id=4186056888460247152)
            store_detail = store_detail.get("rows")[0]
            assets_detail['store_id'] = store_detail['id']
            assets_detail['store_name'] = store_detail['name']

            assets_db = AssetsModel.new(**assets_detail)
            session.commit()

            product_codes = []
            for product_detail in assets_detail['products']:
                product_codes.append(product_detail.get('litm'))
            product_list = metadata_service.get_product_list(filters={"code__in":product_codes}, partner_id=4183192445833445399, user_id=4186056888460247152).get('rows')

            product_nums = 0
            result_details = []
            for product_detail in assets_detail['products']:
                for product_d in product_list:
                    if product_detail.get('litm') == product_d['code']:
                        product = {}
                        result_detail = {}
                        product['litm'] = product_detail.get('litm')
                        product['uom'] = product_detail.get('uom')
                        product['uorg'] = product_detail.get('uorg')
                        product['id'] = gen_snowflake_id()
                        product['asset_id'] = assets_detail['id']
                        product['created_at'] = datetime.utcnow()
                        product['created_name'] = 'JDE'
                        product['partner_id'] = 4183192445833445399
                        product['product_name'] = product_d['name']
                        product['product_id'] = product_d['id']
                        # product['created_by'] = user_id
                        product_nums += 1
                        AssetsProductModel.new(**product)
                        result_detail['edtn'] = assets_detail['code']
                        result_detail['status'] = 'SUCCESS'
                        result_detail['edln'] = product_nums
                        result_details.append(result_detail)
               
            # 更新收货单商品数量
            args = {'product_nums':product_nums}
            assets_db.update(**args)
            session.commit()
            
            # session.close()
            # session.flush()

        # 已生成对应要货单
        else:
            raise RecordAlreadyExist("data already exists: %s" % if_assets_exists)
            # return if_assets_exists
        return True

    @db_commit
    def confirm_assets(self, request, partner_id, user_id):
        asset_id = request.id
        products = request.products
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        asset_db = AssetsModel.get(asset_id)
        if not asset_db:
            raise NoResultFoundError("no result found!")

        if asset_db.status != 'INITED':
            raise StatusUnavailable("only INITED data can be confirmed!")

        # 更新收货单状态
        args = {
            'status':'CONFIRMED',
            'updated_at':datetime.utcnow(),
            # 'inventory_status':'INITED',
            'updated_by':user_id,
            'updated_name':operator_name
            }
        asset_db.update(**args)
        session.commit()
        # session.close()

        # 更新收货单商品表的confirmed数量：
        for product in products:
            product = pb2dict(product)
            print(product['quantity'])
            args = {
                'quantity':product['quantity'],
                'updated_at':datetime.utcnow(),
                'updated_by':user_id,
                'updated_name':operator_name
                }

            # store_id = receiving_db.AN8
            product_db = AssetsProductModel.get(product['id'])
            # product_meta_detail = metadata_service.get_distribution_products_by_store_id(
            #                         store_id,
            #                         product_ids=[product_db.product_id],
            #                         return_fields=["code","name","receiving_deviation_min","receiving_deviation_max"],
            #                         include_product_fields=["name", "code"]
            #                         )
            # product_meta_detail = product_meta_detail.get("rows")
            # if not product_meta_detail:
            #     raise NoResultFoundError("此门店配送区域无此商品, store_id:{}, product_id:{}".format(store_id, product['id']))
            
            product_db.update(**args)
            session.commit()

            # 推送单据
            # TODO：通知仓库收货
            # 发送pda推送
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                    user_id=user_id,
                                                    scope_id=1,
                                                    store_id=asset_db.store_id,
                                                    source_root_id=asset_db.id,
                                                    source_id=asset_db.id,
                                                    source_type="ASSET",
                                                    action="CONFIRMED",
                                                    ref_source_id=asset_db.id,
                                                    ref_source_type="ASSET",
                                                    ref_action="INITED",
                                                    content={
                                                            "store_name":asset_db.store_name,
                                                            "asset_code":str(asset_db.code),
                                                            "delivery_date":str(asset_db.trdj),
                                                            "updated_at":str(datetime.utcnow()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": asset_db.updated_name})
            # session.close()
            return True

    def get_asset_by_id(self, asset_id, partner_id, user_id):
        asset_db = AssetsModel.get(asset_id)
        if asset_db:
            asset_obj = asset_db.serialize(conv=True)
            return asset_obj
        return None

    def get_asset_products_by_id(self, request, partner_id, user_id):
        count = None
        asset_id = request.id
        limit = request.limit
        offset = request.offset
        count,asset_products_db_list = AssetsProductModel.list_products_by_asset_id(asset_id,limit,offset)
        asset_products_list = []
        if asset_products_db_list:
            for asset_product_db in asset_products_db_list:
                asset_product_obj = asset_product_db.serialize(conv=True)
                asset_products_list.append(asset_product_obj)
            return count, asset_products_list
        return 0, None

    def list_assets(self, request, partner_id, user_id):
        asset_id = request.asset_id
        store_ids = request.store_ids
        status = request.status
        start_date = request.start_date
        end_date = request.end_date
        limit = request.limit
        offset = request.offset
        doco = request.jde_order_id
        code = request.code
        storeIdList = []
        for storeId in store_ids:
            storeIdList.append(int(storeId))
        store_ids = storeIdList

        count, asset_dbs = AssetsModel.list_assets(partner_id, user_id, limit, offset, store_ids,
                               status, start_date, end_date, asset_id, doco, code)
        if asset_dbs:
            asset_list = []
            for asset_db in asset_dbs:
                asset_obj = asset_db.serialize(conv=True)
                asset_list.append(asset_obj)
            return count, asset_list
        return 0, None

    @db_commit
    def create_assets_from_jde(self, details, partner_id, user_id):
        if details[0].get('doco'):
            # 已生成固定资产收货单
            if_assets_exists = AssetsModel.get_assets_by_doco(details[0]['doco'])
        else:
            # raise DataValidationException("lose required data:DOCO!")
            result = {'status': 'FAILED'}
            return result
        # 未生成对应收货单,初始化收货单
        # if not if_assets_exists:
        assets_detail = details[0]
        assets_detail['id'] = gen_snowflake_id()
        assets_detail['code'] = Supply_doc_code.get_code_by_type('ASSET_OD',partner_id=partner_id, user_id=user_id)
        assets_detail['status'] = 'INITED'
        assets_detail['partner_id'] = partner_id
        # assets_detail['created_by'] = user_id
        assets_detail['created_at'] = datetime.utcnow()
        assets_detail['created_name'] = 'JDE'

        mcu_code = assets_detail['mcu']
        mcu_detail = metadata_service.get_distribution_center_list(search=mcu_code, search_fields='code', return_fields="name, id",  partner_id=partner_id, user_id=user_id).get('rows')
        mcu_detail = mcu_detail[0]
        assets_detail['mcu_id'] = mcu_detail['id']
        assets_detail['mcu_name'] = mcu_detail['name']

        store_code = assets_detail['an8']
        store_detail = metadata_service.get_store_list(filters={"code__in":[store_code]}, partner_id=partner_id, user_id=user_id)
        store_detail = store_detail.get("rows")[0]
        assets_detail['store_id'] = store_detail['id']
        assets_detail['store_name'] = store_detail['name']

        assets_db = AssetsModel.new(**assets_detail)
        session.commit()

        product_codes = []
        for product_detail in details:
            product_codes.append(product_detail.get('litm'))
        product_list = metadata_service.get_product_list(filters={"code__in":product_codes}, partner_id=partner_id, user_id=user_id).get('rows')

        product_nums = 0
        result_details = []
        for product_detail in details:
            for product_d in product_list:
                if product_detail.get('litm') == product_d['code']:
                    product = {}
                    result_detail = {}
                    product['litm'] = product_detail.get('litm')
                    product['uom'] = product_detail.get('uom')
                    product['uorg'] = product_detail.get('uorg')
                    product['status'] = product_detail.get('lnid')
                    product['id'] = gen_snowflake_id()
                    product['asset_id'] = assets_detail['id']
                    product['created_at'] = datetime.utcnow()
                    product['created_name'] = 'JDE'
                    product['partner_id'] = partner_id
                    product['product_name'] = product_d['name']
                    product['product_id'] = product_d['id']
                    # product['created_by'] = user_id
                    product_nums += 1
                    AssetsProductModel.new(**product)
                    result_detail['edtn'] = assets_detail['code']
                    result_detail['status'] = 'Y'
                    result_detail['edln'] = product_nums
                    result_details.append(result_detail)
               
        # 更新收货单商品数量
        args = {'products_num':product_nums}
        assets_db.update(**args)
        session.commit()
        # 已生成对应要货单
        # else:
        #     result_details = [{
        #                         'edtn':details[0].get('doco'), 
        #                         'status':'FAILED', 
        #                         'edln':1}]
        
        logging.info('jdestatus')
        logging.info(details)
        return result_details
            
asset_service = AssetsService()