# -*- coding:utf-8 -*-

import base64
import functools
import logging
import re
import tempfile
import time
from decimal import Decimal
from datetime import datetime, timedelta

from google.protobuf import struct_pb2
from google.protobuf.struct_pb2 import Struct, Value

from supply.utils.time import datetime_slice_to_time, str_convert_to_timedelta
import traceback
import json

import pyexcel
from google.protobuf.timestamp_pb2 import Timestamp
from hex_exception import WrongPartner
from supply import logger, time_cost
from supply.client.bom_service import Bom_service
from supply.client.inventory_service import inventory_service
from supply.client.metadata_service import metadata_service
from supply.client.report_service import report_service
from supply.driver.mysql import session_maker
from supply.error.demand import (DemandBelongError, DemandInvalidError,
                                 DemandNotExistError, Demand<PERSON>tatusError,
                                 DemandTypeError, ProductError, StoreNotExist,
                                 VacancyError)
from supply.model import DBSession, db_commit
from supply.model.demand.supply_demand import Supply_demand
from supply.model.demand.supply_demand_log import Supply_demand_log
from supply.model.demand.supply_demand_product import Supply_demand_product
from supply.model.demand.supply_first_delivery import Supply_first_delivery
from supply.model.demand_master import demand_master_repository
from supply.model.operation_log import TpTransLogModel
from supply.model.supply_doc_code import Supply_doc_code
from supply.model.business_config import BusinessConfigModel
from supply.module.demand_suggest.base import DemandSuggest
from supply.module.demand_suggest.tea_bread_material import TeaBreadDemandMaterialSuggest
from supply.module.demand_two import demand_two_module
from supply.module.demand_suggest.base import DemandSuggest
from supply.proto.receiving_pb2 import CreateReceivingRequest, Product
# from supply.task import public
from supply.task.message_service_pub import MessageServicePub
from supply.utils.enums import (Demand_bill_code, Demand_enum,
                                Demand_sub_type, Demand_type,
                                Message_server_source_type, Order_enum,
                                Tag_type, Vacancy_enum)
from supply.utils.exception import DataValidationException
from supply.utils.helper import (MessageTopic, datetime_to_strdate2, get_quantity,
                                 get_today_datetime, DEFAULT_STORAGE_TYPE, dict_to_struct, convert_to_RFC3339)
from supply.utils.helper import get_guid
from supply.utils.snowflake import gen_snowflake_id
from supply.utils import pb2dict
from ..error.exception import *
from supply.error.demand import DemandError, ProductError
from supply.client.receipt_service import receipt_service
from supply.driver.mq import mq_producer
from supply.model.doc_plan.doc_plan import doc_plan_repository
from supply.utils.kit import Kit, KitEnum
from supply.model.price_chain_document_products.price_chain_document_products import PriceChainDocumentProduct,price_chain_document_products_db

"""
| 修改日期       | 修改人       | 修改记录                              |
---------------------------------------------------------------------
| 2021-06-02      Mia           只对配方属性=现做bom的无法订货和主配做限制 |
---------------------------------------------------------------------
|
---------------------------------------------------------------------

"""


class demand_module(object):

    def list_demand(self, req, partner_id, user_id):
        store_ids = req.store_ids
        has_product = req.has_product
        start_date = datetime.fromtimestamp(req.start_date.seconds)
        end_date = datetime.fromtimestamp(req.end_date.seconds)
        type = req.type
        types = req.types
        sub_type = req.sub_type
        store_type = req.store_type
        status = req.status
        offset = req.offset
        limit = req.limit
        codes = req.codes
        is_plan = req.is_plan
        is_adjust = req.is_adjust
        order = req.order
        sort = req.sort
        # 根据计划名称取doc_plan表中查出doc id列表
        plan_name = req.plan_name
        plan_ids = []
        if plan_name:
            plans = doc_plan_repository.list_doc_plan(plan_name=plan_name, partner_id=partner_id, user_id=user_id)
            plan_ids = [int(plan.id) for plan in plans]
            if not plan_ids or len(plan_ids) == 0:
                return [], 0
        if not sort:
            sort = 'updated_at'
        rows, total = Supply_demand.get_demand_list_by_args(
            partner_id=partner_id, user_id=user_id, store_ids=store_ids, has_product=has_product,
            start_date=start_date, end_date=end_date, s_type=type, sub_type=sub_type, store_type=store_type,
            status=status,
            codes=codes, is_plan=is_plan, is_adjust=is_adjust, offset=offset, limit=limit, order=order, sort=sort,
            plan_ids=plan_ids, types=types)

        # 拼接plan相关信息
        plan_dict = {}
        for demand in rows:
            if demand.batch_id:
                plan_ids.append(demand.batch_id)
        if plan_ids and len(plan_ids) > 0:
            plans = doc_plan_repository.list_doc_plan_by_ids(plan_ids=plan_ids, partner_id=partner_id, user_id=user_id)
            if plans:
                for plan in plans:
                    plan_dict[int(plan.id)] = {
                        'name': plan.name,
                        'method': plan.method
                    }

        demand_ids = [i.id for i in rows]

        demand_ids_str = [str(i.id) for i in rows]
        doc_id_amount_map = price_chain_document_products_db.get_sum_amount_by_doc_ids(demand_ids_str)

        products_count = Supply_demand.get_product_count_by_ids(demand_ids, partner_id)
        rows = [item.serialize(conv=True) for item in rows]
        result = []
        for i in rows:
            i["product_count"] = products_count.get(i['id'])
            i["plan_method"] = plan_dict.get(i.get('batch_id', 0), {}).get('method')
            i["plan_name"] = plan_dict.get(i.get('batch_id', 0), {}).get('name')

            info = doc_id_amount_map.get(str(i['id']))
            if info:
                i['amount'] = str(info['total_amount'])
                i['currency'] = str(info['currency'])
            result.append(i)
        return result, total

    def list_store_demand(self, req, partner_id, user_id):
        store_ids = req.store_ids
        has_product = req.has_product
        start_date = datetime.fromtimestamp(req.start_date.seconds)
        end_date = datetime.fromtimestamp(req.end_date.seconds)
        type = req.type
        types = req.types
        sub_type = req.sub_type
        store_type = req.store_type
        status = req.status
        offset = req.offset
        limit = req.limit
        codes = req.codes
        is_plan = req.is_plan
        is_adjust = req.is_adjust
        tag_id = req.tag_id
        order = req.order
        sort = req.sort
        # 根据计划名称取doc_plan表中查出doc id列表
        plan_name = req.plan_name
        plan_ids = []

        product_ids = list(req.product_ids) if req.product_ids else []
        if plan_name:
            plans = doc_plan_repository.list_doc_plan(plan_name=plan_name, partner_id=partner_id, user_id=user_id)
            plan_ids = [int(plan.id) for plan in plans]
            if not plan_ids or len(plan_ids) == 0:
                return [], 0
        if not sort:
            sort = 'updated_at'
        rows, total = Supply_demand.get_store_demand_list_by_args(
            partner_id=partner_id, user_id=user_id, store_ids=store_ids, has_product=has_product,
            start_date=start_date, end_date=end_date, s_type=type, sub_type=sub_type, store_type=store_type,
            status=status,
            codes=codes, is_plan=is_plan, is_adjust=is_adjust, offset=offset, limit=limit, order=order, sort=sort,
            plan_ids=plan_ids, types=types, product_ids=product_ids,tag_id=tag_id)

        # 拼接plan相关信息
        plan_dict = {}
        for demand in rows:
            if demand.batch_id:
                plan_ids.append(demand.batch_id)
        if plan_ids and len(plan_ids) > 0:
            plans = doc_plan_repository.list_doc_plan_by_ids(plan_ids=plan_ids, partner_id=partner_id, user_id=user_id)
            if plans:
                for plan in plans:
                    plan_dict[int(plan.id)] = {
                        'name': plan.name,
                        'method': plan.method
                    }

        demand_ids = [i.id for i in rows]

        ## amount
        demand_ids_str = [str(i.id) for i in rows]
        doc_id_amount_map = price_chain_document_products_db.get_sum_amount_by_doc_ids(demand_ids_str)
        # logging.info("doc_id_amount_map ids==={}".format(demand_ids))
        logging.info("doc_id_amount_map==={}".format(doc_id_amount_map))
        products_count = Supply_demand.get_product_count_by_ids(demand_ids, partner_id)
        rows = [item.serialize(conv=True) for item in rows]
        result = []
        for i in rows:
            i["product_count"] = products_count.get(i['id'])
            i["plan_method"] = plan_dict.get(i.get('batch_id', 0), {}).get('method')
            i["plan_name"] = plan_dict.get(i.get('batch_id', 0), {}).get('name')
            logging.info("doc_id_amount_map id==={}".format(str(i['id'])))
            info = doc_id_amount_map.get(str(i['id']))
            if info:
                logging.info("doc_id_amount_map ids==={}".format(doc_id_amount_map.get(str(i['id']),"")))
                i['amount'] = str(info['total_amount'])
                i['currency'] = str(info['currency'])
            result.append(i)
        return result, total

    def list_demand_with_products(self, partner_id, user_id, \
                                  start_date=None, end_date=None, \
                                  status=None, store_ids=None, has_product=None, types=None, \
                                  plan_name=None, product_ids=None):
        if start_date and not isinstance(start_date, datetime):
            start_date = datetime.fromtimestamp(start_date.seconds)
        if end_date and not isinstance(end_date, datetime):
            end_date = datetime.fromtimestamp(end_date.seconds)

        demands = []
        plans = []
        # 根据计划名称取doc_plan表中查出doc_id列表
        plan_ids = []
        if plan_name:
            plans = doc_plan_repository.list_doc_plan(plan_name=plan_name, partner_id=partner_id, user_id=user_id)
            if plans:
                for plan in plans:
                    plan_ids.append(int(plan.id))
                if len(plan_ids) == 0:
                    return [], 0
            else:
                return [], 0

        demands, total = Supply_demand.list_demand_with_products(
            partner_id=partner_id, store_ids=store_ids, \
            has_product=has_product, start_date=start_date, end_date=end_date, \
            status=status, plan_ids=plan_ids, types=types, product_ids=product_ids)

        for demand in demands:
            if demand[22]:
                plan_ids.append(demand[22])
        # 拼接plan相关信息
        plan_dict = {}
        if plan_ids and len(plan_ids) > 0:
            plans = doc_plan_repository.list_doc_plan_by_ids(plan_ids=plan_ids, partner_id=partner_id, user_id=user_id)
            if plans:
                for plan in plans:
                    plan_dict[int(plan.id)] = {
                        'name': plan.name,
                        'method': plan.method
                    }

        type_map = {
            "SD": "订货计划",
            "AD": "库存调整",
            "MD": "总部分配"
        }

        status_map = {
            "INITED": "新建",
            "SUBMITTED": "已提交",
            "APPROVED": "已审核",
            "REJECTED": "已驳回",
            "CANCELLED": "已作废",
            "INVALID": "已作废"
        }

        plan_method_map = {
            "D": "日",
            "M": "月",
            "W": "周"
        }

        distr_type_map = {
            "PUR": "直送",
            "NMD": "总仓配送",
            "PAD": "加工配送"
        }

        sale_type_map = {
            "NORMAL": "普通商品",
            "ADDITION": "加料商品"
        }

        if total:
            result = []
            for i in demands:
                details = {
                    "code": i[0],
                    "status": status_map.get(i[1]),
                    "type": type_map.get(i[2]),
                    "demand_date": (i[3] + timedelta(hours=8)).strftime("%Y-%m-%d") if i[3] and isinstance(i[3],
                                                                                                           datetime) else "",
                    "receive_name": i[4],
                    "store_secondary_id": i[5],
                    "remark": i[6],
                    "arrival_date": (i[7] + timedelta(hours=8)).strftime("%Y-%m-%d") if i[7] and isinstance(i[7],
                                                                                                            datetime) else "",
                    "updated_name": i[8],
                    "updated_at": (i[9] + timedelta(hours=8)).strftime("%Y-%m-%d %H:%M:%S") if i[9] and isinstance(i[9],
                                                                                                                   datetime) else "",
                    "product_code": i[10],
                    "product_name": i[11],
                    "min_quantity": float(i[12]) if i[12] else 0,
                    "max_quantity": float(i[13]) if i[13] else 0,
                    "increment_quantity": float(i[14]) if i[14] else 0,
                    "distribution_type": distr_type_map.get(i[15]),
                    "quantity": float(i[16]) if i[16] else 0,
                    "unit_name": i[17],
                    "spec": i[18],
                    "arrival_days": i[19],
                    "product_category_name": i[20],
                    "sale_type": sale_type_map.get(i[21]),
                    "plan_method": plan_method_map.get(plan_dict.get(int(i[22]), {}).get('method')) if i[22] else "",
                    "plan_name": plan_dict.get(int(i[22]), {}).get('name') if i[22] else ""
                }
                result.append(details)
            return result
        else:
            return []

    @staticmethod
    @db_commit
    def update_or_insert_demand_product(product_info: list, demand_id: int, partner_id: int, user_id: int,
                                        order_date=None) -> list:
        demand = Supply_demand.get(demand_id)
        if demand == None:
            raise DemandNotExistError("not find demand")
        if demand.partner_id != partner_id:
            raise DemandBelongError("租户不匹配")

        # 作废/准备拆单时会锁定处理状态为正在处理中，此时不再允许更新订货单信息
        if demand.process_status == 'PROCESSING':
            raise DemandInvalidError("订货单正在处理中，不允许更新")

        # 只有刚初始化和被驳回的订货单才能订货
        if demand.status not in (Demand_enum.INITED.code, Demand_enum.CAL_DONE.code, Demand_enum.REJECTED.code):
            raise DemandInvalidError("订货单状态不正确:{}".format(demand.status))
        if not order_date:
            order_date = get_today_datetime()
        result = []
        total_quantity = 0
        for item in product_info:
            s_id = item.id
            product_id = item.product_id
            quantity = item.quantity if item.quantity else 0
            distribution_type = item.distribution_type
            tag_type = item.tag_type
            unit_id = item.unit_id
            unit_rate = item.unit_rate
            if not tag_type:
                continue
            if tag_type not in [i.code for i in list(Tag_type)]:
                continue

            total_quantity += float(quantity)
            demand_product = None
            if s_id:
                demand_product = Supply_demand_product.get(s_id)
            if (not demand_product) and (demand.type == Demand_type.AD.code):
                demand_product = Supply_demand_product.get_by_product_id_and_demand_id_and_distr_type(product_id,
                                                                                                      distribution_type,
                                                                                                      demand_id)

            if demand_product == None:
                # 需要新插入
                if tag_type == Tag_type.FINISHED.code:
                    if demand.type != Demand_type.SD.code:
                        raise DemandTypeError("订货单类型不正确:{}, 只有门市订货单才能订成品".format(demand.type))
                    demand_product = Supply_demand_product.insert_finished_product(product_id, quantity, user_id,
                                                                                   demand_id)
                else:
                    demand_product = Supply_demand_product.insert_by_demand_id_and_receive(demand_id, demand.receive_by,
                                                                                           product_id,
                                                                                           quantity, partner_id,
                                                                                           user_id,
                                                                                           distribution_type=distribution_type,
                                                                                           type=demand.type,
                                                                                           tag_type=tag_type,
                                                                                           order_date=order_date,
                                                                                           unit_id=unit_id)
            else:
                # 需要更新
                # get_quantity(quantity, max_quantity, min_quantity, inc_quantity, mode="down", round_num=3):
                # if demand.type != Demand_type.AD.code:
                #     quantity = get_quantity(quantity, demand_product.max_quantity, demand_product.min_quantity,
                # demand_product.increment_quantity)

                ### 调整单更新单位
                if demand.type == Demand_type.AD.code and unit_id:
                    demand_product.unit_id = unit_id
                    order_unit = metadata_service.get_unit(unit_id, partner_id=partner_id, return_fields='id,code,name',
                                                           user_id=user_id, )
                    demand_product.unit_code = order_unit.get('code', '无')
                    demand_product.unit_name = order_unit.get('name', '无')
                    demand_product.unit_rate = unit_rate
                demand_product.quantity = float(quantity)
            if item.distribution_by and demand_product is not None:
                demand_product.distribute_by = item.distribution_by
                demand_product.distribution_type = item.distribution_type

            if demand_product:
                DBSession.add(demand_product)
            result.append(item)

        # 是否含商品更新s
        demand.has_product = 1
        if total_quantity == 0:
            demand.has_product = 0
        demand.updated_by = user_id
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        demand.updated_name = username

        demand_log = Supply_demand_log()
        demand_log.partner_id = partner_id
        demand_log.store_id = demand.receive_by
        demand_log.demand_id = demand.id
        demand_log.demand_code = demand.code
        demand_log.action_code = 'UPDATE'
        demand_log.action_name = '更新'
        demand_log.updated_name = username
        demand_log.created_by = user_id
        demand_log.created_at = datetime.now()
        demand_log.updated_by = user_id
        demand_log.updated_at = datetime.now()
        DBSession.add(demand_log)
        DBSession.add(demand)
        return result

    @staticmethod
    def get_demand_product(demand_id, partner_id=None, user_id=None):
        demand = Supply_demand.get(demand_id)
        if not demand:
            raise DemandNotExistError("can not find demand")
        if demand.partner_id != partner_id:
            raise WrongPartner("订单和商户不匹配")

        products, total = Supply_demand_product.query(**{"demand_id": demand_id})
        demand_products = products.all()

        unfinish_products_map = receipt_service.get_unifish_receive_products(demand.receive_by, partner_id=partner_id,
                                                                             user_id=user_id)
        product_list = []
        for product in demand_products:
            product_detail = product.serialize(conv=True)
            product_detail["store_unfinish_qty"] = unfinish_products_map.get(int(product_detail.get("product_id", 0)),
                                                                             0)
            product_list.append(product_detail)

        return product_list, total

    @staticmethod
    def list_demand_product(demand_id, partner_id=None, user_id=None, include_unfinish=None, include_inv=None):

        demand = Supply_demand.get_by_id(partner_id=partner_id, demand_id=demand_id)
        if not demand:
            raise DemandNotExistError("DemandNotExist")
        demand_products, total = Supply_demand_product.get_by_demand_id(demand_id=demand_id)
        if include_unfinish:
            unfinish_products_map = receipt_service.get_unifish_receive_products(demand.receive_by,
                                                                                 partner_id=partner_id, user_id=user_id)
        product_ids = []
        for product in demand_products:
            product_ids.append(int(product.product_id))
        if include_inv:

            product_unit_dict, _ = metadata_service.get_product_units_dict(product_ids=product_ids,
                                                                           partner_id=partner_id, user_id=user_id)

            inv_products_map, total = inventory_service.query_realtime_inventory(branch_id=demand.receive_by, limit=-1,
                                                                                 partner_id=partner_id, user_id=user_id,
                                                                                 product_ids=product_ids)
        tag_id_map = {}
        product_ret = metadata_service.list_entity(schema_name = "product",ids = product_ids,return_fields="tag_id",partner_id=partner_id,user_id=user_id).get('rows',[])
        for p in product_ret:
            tag_id_map[int(p['id'])] = int(p['fields'].get('tag_id',1))
        logging.info("tag_id_map:{}".format(tag_id_map))
        product_list = []
        for product in demand_products:
            product_detail = product.serialize(conv=True)
            if include_unfinish:
                product_detail["store_unfinish_qty"] = unfinish_products_map.get(
                    int(product_detail.get("product_id", 0)), 0)
            if include_inv:
                p = inv_products_map.get(str(product_detail.get("product_id", 0)))
                demand_unit_rate = product_unit_dict.get(str(product_detail.get("product_id", 0)), {}).get('order',
                                                                                                           {}).get(
                    'rate', 1)
                if p:
                    # 核算数量=订货数量*订货单位比率
                    product_detail["store_inv_qty"] = (Decimal(p['quantity_avail']) / Decimal(
                        demand_unit_rate) if demand_unit_rate else p[
                        'quantity_avail']).quantize(Decimal('0.00000000'))

            rule_extends_ = product.rule_extends
            if rule_extends_ != "" and rule_extends_ != None:
                rule_extends = json.loads(rule_extends_)
                ##    string next_plan_arrival_date = 46;
                # string plan_arrive_date_text = 47;
                # string next_plan_arrive_date_text = 48;
                product_detail["next_plan_arrival_date"]=rule_extends.get("next_plan_arrival_date")
                product_detail["plan_arrive_date_text"] =rule_extends.get("plan_arrive_date_text")
                product_detail["next_plan_arrive_date_text"] =rule_extends.get("next_plan_arrive_date_text")
            if tag_id_map.get(int(product.product_id)):
                product_detail['tag_id'] = tag_id_map.get(int(product.product_id))
            product_list.append(product_detail)

        return product_list, total

    def check_change_demand_status(self, status, demand):
        if status == Demand_enum.F_COMMIT.code:
            if demand.type != Demand_type.SD.code:
                raise DemandTypeError("只有门市订货单才能有成品提交")
            if demand.status != Demand_enum.INITED.code:
                raise DemandStatusError("门市订货单成品提交必须在初始化状态")
        if status == Demand_enum.SUBMITTED.code:
            if demand.type == Demand_type.SD.code:
                # 如果是门市订货单，则必须要先成品订货完成且原料拆减完成
                #         去掉 Demand_enum.CAL_DONE.code,
                if demand.status not in (Demand_enum.REJECTED.code, Demand_enum.INITED.code):
                    raise DemandStatusError("新建或驳回的门市订货单才能提交:{}-{}".format(demand.id, demand.status))
            else:
                if demand.status not in (Demand_enum.INITED.code, Demand_enum.REJECTED.code):
                    raise DemandStatusError("非门市订货单确认要在初始化或者驳回状态才能执行")
        if status == Demand_enum.REJECTED.code:
            if demand.status not in (Demand_enum.SUBMITTED.code, Demand_enum.CONFIRMED.code):
                raise DemandStatusError("订货单驳回要在确认状态")

        if status == Demand_enum.APPROVED.code:
            pass
            # if demand.status != Demand_enum.SUBMITTED.code:
            #     raise DemandStatusError("订货单要在提交状态才能审核")
        if status == 'delete':
            if demand.status != Demand_enum.INITED.code:
                raise DemandStatusError("订货单状态错误")
            if demand.type == Demand_type.SD.code:
                # 门市订货单不能删除
                raise DemandTypeError("订货单类型错误")

    def pub_pda_message(self, demand, status, old_status, partner_id, user_id, username):
        if demand.type == Demand_type.SD.code:
            # 门市订货单消息不推送到pda
            push_rule = []
        else:
            push_rule = None
        MessageServicePub.pub_message_service(partner_id=partner_id,
                                              user_id=user_id,
                                              scope_id=1,
                                              store_id=demand.receive_by,
                                              source_root_id=demand.id,
                                              source_id=demand.id,
                                              source_type=Message_server_source_type.DEMAND.code + "_" + demand.type,
                                              action=status,
                                              ref_source_id=demand.id,
                                              ref_source_type=Message_server_source_type.DEMAND.code + "_" + demand.type,
                                              ref_action=old_status,
                                              content={
                                                  "code": demand.code,
                                                  "store_name": demand.receive_name,
                                                  "user_name": username,
                                                  "demand_date": demand.demand_date_str,
                                                  "arrival_date": demand.arrival_date_str,
                                                  "updated_at": demand.updated_at_str,
                                                  "type": demand.type,
                                                  "sub_type": demand.sub_type,
                                                  "description": demand.description,
                                                  "remark": demand.remark
                                              },
                                              push_rule=push_rule
                                              )

    @db_commit
    def change_demand_status(self, demand_id, status, description, partner_id=None, user_id=None, is_auto=False):
        demand = Supply_demand.get(demand_id)
        if not demand:
            raise DemandNotExistError("demand not exist")
        if demand.partner_id != partner_id:
            raise DemandBelongError("订单不属于该租户")

        if demand.status == status:
            logger.info("提交重复请求, {}-{}-{}".format(demand.id, demand.status, status))
            return True

        if status == "reset":
            if demand.status not in (Demand_enum.INITED.code, Demand_enum.REJECTED.code):
                raise DemandTypeError("订货单类型错误")
            demand.has_product = 0
            demand.status = "INITED"
            DBSession.add(demand)
            return Supply_demand_product.reset_quantity(demand_id)

        elif status == "delete":
            if demand.status != Demand_enum.INITED.code:
                raise DemandStatusError("订货单状态错误")
            if demand.type == Demand_type.SD.code:
                # 门市订货单不能删除
                raise DemandTypeError("订货单类型错误")
            Supply_demand.delete_demand_and_product_by_id(demand_id)
            return True

        elif status == "CANCELLED":
            if demand.status not in (Demand_enum.INITED.code, Demand_enum.REJECTED.code):
                raise DemandStatusError("订货单状态错误")

        elif status == Demand_enum.SUBMITTED.code:
            if demand.type == Demand_type.SD.code:
                # 如果是门市订货单，则必须要先成品订货完成且原料拆减完成
                if demand.status not in (Demand_enum.REJECTED.code, Demand_enum.INITED.code):
                    raise DemandStatusError("新建或驳回的门市订货单才能提交:{}-{}".format(demand.id, demand.status))
            else:
                if demand.status not in (Demand_enum.INITED.code, Demand_enum.REJECTED.code):
                    raise DemandStatusError("非门市订货单确认要在初始化或者驳回状态才能执行")
        elif status == Demand_enum.REJECTED.code:
            if demand.status not in (Demand_enum.SUBMITTED.code, Demand_enum.CONFIRMED.code):
                raise DemandStatusError("订货单驳回要在确认状态")

        ### 根据send_type 选择不同物流系统  ### 不改原逻辑
        message = {}
        message['partner_id'] = partner_id
        message['user_id'] = user_id
        message['demand_ids'] = [demand.id]

        # if status == Demand_enum.APPROVED.code and demand.process_status in (
        #         Demand_enum.INITED.code, Demand_enum.PROCESSING.code, Demand_enum.FAILED.code):
        #     # 单子审核后需要发送消息去拆减成要货单
        #     # public(MessageTopic.DEMAND_STORE_DEMAND_ORDER_TOPIC, message)
        #     mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
        #                         topic=MessageTopic.DEMAND_STORE_DEMAND_ORDER_TOPIC,
        #                         message=message)
        #     logger.info("发送主配拆单消息, demand.id:{}".format(demand.id))
        #     # 单据同步给三方
        #     message = {
        #         'doc_resource': 's_demand',
        #         'doc_id': demand.id,
        #         'partner_id': partner_id,
        #         'user_id': user_id,
        #         'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        #     }
        #     mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)
        #
        #     # 同步记录落表，用于后续补偿
        #     tp_trans_log = {
        #         'id': demand.id,
        #         'doc_code': demand.code,
        #         'doc_type': 's_demand',
        #         'status': 'inited',
        #         'msg': str(message),
        #         'partner_id': partner_id,
        #         'created_by': user_id,
        #         'created_at': datetime.utcnow(),
        #         'updated_at': datetime.utcnow()
        #     }
        #     TpTransLogModel.create_logs_list([tp_trans_log])
        #
        # if status == Demand_enum.F_COMMIT.code and demand.type == Demand_type.SD.code:
        #     # 原门市订货成品原料拆解的消息
        #     # public(MessageTopic.DEMAND_PARSE_BOM_TOPIC, message)
        #     mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
        #                         topic=MessageTopic.DEMAND_PARSE_BOM_TOPIC,
        #                         message=message)
        #     logger.info("发送原门市订货成品原料拆解消息, demand.id:{}".format(demand.id))

        old_status = demand.status
        demand.status = status
        demand.process_status = "INITED"
        demand.is_auto = is_auto
        demand.description = description
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        demand.updated_by = user_id
        demand.updated_name = username
        DBSession.add(demand)
        DBSession.commit()

        # 发送pda消息(紧急和主配才给pda显示，门市订货不显示)
        status_desc = ""
        if status in (Demand_enum.SUBMITTED.code, Demand_enum.APPROVED.code, Demand_enum.REJECTED.code):
            # logger.info("订货单状态变更发送pda消息, demand.id:{}, old_status:{}, status:{}".format(demand.id, old_status, status))
            self.pub_pda_message(demand, status, old_status, partner_id, user_id, username)
            # 清理订货单待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=demand.receive_by,
                                             doc_type="demand"))
            if status == Demand_enum.SUBMITTED.code:
                status_desc = Demand_enum.SUBMITTED.desc
            if status == Demand_enum.APPROVED.code:
                status_desc = Demand_enum.APPROVED.desc
            if status == Demand_enum.REJECTED.code:
                status_desc = Demand_enum.REJECTED.desc

        if status in (Demand_enum.SUBMITTED.code, Demand_enum.APPROVED.code):
            demand_products, total = Supply_demand_product.get_by_demand_id(demand_id=demand_id)
            item_codes = []
            for product in demand_products:
                item_codes.append(product.product_code)
            price_dict = dict()
            price_list = metadata_service.get_product_price_by_store_id(store_id=demand.receive_by,
                                                                        item_codes=item_codes,
                                                                        partner_id=partner_id,
                                                                        user_id=user_id).get('rows', [])
            for c in price_list:
                price_dict[c.get('item_code')] = [c['price'], c.get('currency','')]
            for product in demand_products:
                price = PriceChainDocumentProduct()
                price.id = get_guid()
                price.partner_id = partner_id
                price.doc_id = demand.id
                price.product_id = product.product_id
                price.doc_type = "demand"
                price.quantity = product.quantity  # 添加这行，确保有数量值
                price.accounting_quantity = product.accounting_quantity  # 如果也需要设置核算数量
                price.created = datetime.utcnow()
                price.updated = datetime.utcnow()
                priceInfo = price_dict.get(product.product_code)
                if priceInfo:
                    price.price = priceInfo[0]
                    price.currency = priceInfo[1]
                price.source_id = demand.id
                price.unit_rate = product.unit_rate
                # 查询记录是否存在
                existing_price = DBSession.query(PriceChainDocumentProduct).filter_by(doc_id=demand.id,product_id=product.product_id).first()
                if not existing_price:
                    DBSession.add(price)
        demand_log = Supply_demand_log()
        demand_log.partner_id = partner_id
        demand_log.store_id = demand.receive_by
        demand_log.demand_id = demand.id
        demand_log.demand_code = demand.code
        demand_log.action_code = demand.status
        demand_log.action_name = Demand_enum.get_name_by_code(demand.status)
        demand_log.updated_name = username
        demand_log.created_by = user_id
        demand_log.created_at = username
        demand_log.updated_by = user_id
        demand_log.updated_at = datetime.utcnow()
        DBSession.add(demand_log)
        DBSession.add(demand)
        # Kit.upload(partnerId=partner_id, docNo=demand.code, docType='订货单', actionType=status_desc,
        #            actionStatus=True, storeId=demand.receive_by,
        #            storeName=demand.receive_name, content="订货单{}成功".format(status_desc))
        if status == Demand_enum.APPROVED.code and demand.process_status in (
                Demand_enum.INITED.code, Demand_enum.PROCESSING.code, Demand_enum.FAILED.code):
            # 单子审核后需要发送消息去拆减成要货单
            # public(MessageTopic.DEMAND_STORE_DEMAND_ORDER_TOPIC, message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.DEMAND_STORE_DEMAND_ORDER_TOPIC,
                                message=message)
            logger.info("发送主配拆单消息, demand.id:{}".format(demand.id))
            # 单据同步给三方
            message = {
                'doc_resource': 's_demand',
                'doc_id': demand.id,
                'partner_id': partner_id,
                'user_id': user_id,
                'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)

            # 同步记录落表，用于后续补偿
            tp_trans_log = {
                'id': demand.id,
                'doc_code': demand.code,
                'doc_type': 's_demand',
                'status': 'inited',
                'msg': str(message),
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            }
            TpTransLogModel.create_logs_list([tp_trans_log])

        if status == Demand_enum.F_COMMIT.code and demand.type == Demand_type.SD.code:
            # 原门市订货成品原料拆解的消息
            # public(MessageTopic.DEMAND_PARSE_BOM_TOPIC, message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.DEMAND_PARSE_BOM_TOPIC,
                                message=message)
            logger.info("发送原门市订货成品原料拆解消息, demand.id:{}".format(demand.id))

    @staticmethod
    @db_commit
    def create_md_store_demand(receive_by: int, product_info: list, demand_date, arrival_date, remark, user_id,
                               partner_id):
        """
        创建门市主配订货单(门店加急)
        receive_by: 门店id
        product_info: 商品id和数量, 例如:[{"product_id":3416723, "quantity":12.3}]
        """
        store = metadata_service.get_store(receive_by, partner_id=partner_id, user_id=user_id)
        if not store:
            raise StoreNotExist("门店:{},不存在".format(receive_by))
        if not isinstance(product_info, list) or len(product_info) == 0:
            raise ProductError("配送商品不能为空")

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        supply_demand = Supply_demand(
            id=gen_snowflake_id(),
            send_type=send_type,
            code=Supply_doc_code.get_code_by_type(Demand_bill_code.STORE_DO.code, partner_id, user_id),
            store_secondary_id=store.get("code"), store_type=store.get("type"), type=Demand_type.MD.code,
            sub_type=Demand_sub_type.STORE.code,
            receive_by=receive_by, receive_name=store.get("name"), demand_date=demand_date,
            partner_id=partner_id, created_by=user_id, updated_by=user_id,
            status=Demand_enum.INITED.code, process_status=Demand_enum.INITED.code,
            remark=remark, arrival_date=arrival_date, has_product=1,
            created_name=username, updated_name=username
        )
        DBSession.add(supply_demand)
        for product in product_info:
            demand_product = Supply_demand_product()
            product_id = product['product_id']
            quantity = product['quantity']
            distribution_type = product['distribution_type']
            product = metadata_service.get_product(product_id, partner_id=partner_id, user_id=user_id)
            if not product:
                raise ProductError("商品:{}，不存在".format(product_id))
            demand_product = Supply_demand_product.insert_by_demand_id_and_receive(supply_demand.id, receive_by,
                                                                                   product_id,
                                                                                   quantity, partner_id, user_id,
                                                                                   distribution_type=distribution_type,
                                                                                   type=supply_demand.type)
            DBSession.add(demand_product)
        return supply_demand

    @staticmethod
    @db_commit
    def create_urgent_demand(receive_by: int, bus_type, product_info: list, demand_date, arrival_date, remark, user_id,
                             partner_id):
        """
        创建门市紧急订货单(支持订新零售成品)
        receive_by: 门店id
        product_info: 商品id和数量, 例如:[{"product_id":3416723, "quantity":12.3, "distribution_type":"NMD"}]
        """
        store = metadata_service.get_store(receive_by, partner_id=partner_id, user_id=user_id)
        if not store:
            raise StoreNotExist("门店:{},不存在".format(receive_by))
        if not isinstance(product_info, list) or len(product_info) == 0:
            raise ProductError("配送商品不能为空")

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        supply_demand = Supply_demand(
            id=gen_snowflake_id(),
            send_type=send_type,
            bus_type=bus_type,
            code=Supply_doc_code.get_code_by_type(Demand_bill_code.STORE_DO.code, partner_id, user_id),
            store_secondary_id=store.get("code"), store_type=store.get("type"), type=Demand_type.HD.code,
            receive_by=receive_by, receive_name=store.get("name"), demand_date=demand_date,
            partner_id=partner_id, created_by=user_id, updated_by=user_id, arrival_date=arrival_date,
            status=Demand_enum.INITED.code, process_status=Demand_enum.INITED.code, has_product=1,
            remark=remark, created_name=username, updated_name=username
        )
        DBSession.add(supply_demand)
        for product in product_info:
            demand_product = Supply_demand_product()
            product_id = product['product_id']
            quantity = product['quantity']
            distribution_type = product['distribution_type']
            product = metadata_service.get_product(product_id, include_units=True, partner_id=partner_id,
                                                   user_id=user_id)
            if not product:
                raise ProductError("商品:{}，不存在".format(product_id))
            if not product.get('product_type'):
                continue
            if product.get("product_type") == Tag_type.FINISHED.code:
                if product.get("sale_type") and product.get('sale_type').startswith("NEW-RETAIL"):
                    demand_product = Supply_demand_product.insert_by_demand_id_and_receive(supply_demand.id, receive_by,
                                                                                           product_id, quantity,
                                                                                           partner_id, user_id,
                                                                                           distribution_type=distribution_type,
                                                                                           type=supply_demand.type,
                                                                                           tag_type=Tag_type.RAW.code)
            else:
                demand_product = Supply_demand_product.insert_by_demand_id_and_receive(supply_demand.id, receive_by,
                                                                                       product_id, quantity,
                                                                                       partner_id, user_id,
                                                                                       distribution_type=distribution_type,
                                                                                       type=supply_demand.type)

            DBSession.add(demand_product)
        return supply_demand

    @staticmethod
    @db_commit
    def old_create_product_main(items, demand_date, arrival_date, remark, user_id, partner_id, batch_id=None):
        """
        商品主配
        [
            {"store_id":1245235,
                "product_items":[
                    {"distribution_type": "NMD",
                    "product_id": 6355989871,
                    "quantity": 5.2},
                    {"distribution_type": "PUR",
                    "product_id": 225151245,
                    "quantity": 124.5},
                ]
            },
            {"store_id":836583,
                "product_items":[
                    {"distribution_type": "NMD",
                    "product_id": 6355989871,
                    "quantity": 5.2},
                    {"distribution_type": "PUR",
                    "product_id": 225151245,
                    "quantity": 124.5},
                ]
            }
        ]
        """
        # 主配batch_id 为唯一的创建请求id
        # 先判断batch_id是否已经创建过主配单
        if batch_id:
            supply_demand_db = DBSession.query(Supply_demand).filter(Supply_demand.batch_id == int(batch_id)).first()
            if supply_demand_db:
                raise DemandError("请勿重复提交主配单！")
        else:
            raise DemandError("缺少唯一请求号，不允许创建！")

        if items and isinstance(items, list):
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            # 单位
            unit_list = metadata_service.get_unit_list(partner_id=partner_id, user_id=user_id).get('rows', [])
            unit_dict = {}
            if unit_list:
                for u in unit_list:
                    unit_dict[int(u['id'])] = u

            # 门店
            store_list = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id,
                                                         return_fields="id,code,name,type").get('rows', [])
            store_dict = {}
            if store_list:
                for s in store_list:
                    store_dict[int(s['id'])] = s

            # 商品类别
            category_ret = metadata_service.get_product_category_list(return_fields='id,code,name',
                                                                      partner_id=partner_id,
                                                                      user_id=user_id)
            category_map = {}
            if category_ret and category_ret.get('rows'):
                for c in category_ret.get('rows'):
                    category_map[int(c.get('id', 0))] = {
                        'code': c.get('code'),
                        'name': c.get('name')
                    }

            for item in items:
                store = store_dict.get(int(item['store_id']))
                if not store:
                    raise StoreNotExist("门店:{},不存在".format(item.get("store_id")))
                # 非预开店/已开店的门店不允许订货
                status_check = metadata_service.check_store_status(int(item['store_id']), partner_id, user_id)
                if not status_check:
                    raise DataValidationException('请检查门店的开店状态！')
                supply_demand = Supply_demand(
                    id=gen_snowflake_id(),
                    code=Supply_doc_code.get_code_by_type(Demand_bill_code.STORE_DO.code, partner_id, user_id),
                    store_secondary_id=store.get("code"), store_type=store.get("type"), type=Demand_type.MD.code,
                    receive_by=int(item.get("store_id")), receive_name=store.get("name"), demand_date=demand_date,
                    partner_id=partner_id, created_by=user_id, updated_by=user_id,
                    sub_type=Demand_sub_type.PRODUCT.code,
                    status=Demand_enum.INITED.code, process_status=Demand_enum.INITED.code,
                    remark=remark, arrival_date=arrival_date,
                    has_product=1, created_name=username, updated_name=username,
                    batch_id=batch_id
                )

                receive_by = int(item.get("store_id"))
                type = Demand_type.MD.code

                product_id_list = []
                product_items = item.get('product_items')
                if product_items and isinstance(product_items, list):
                    for pi in product_items:
                        product_id_list.append(int(pi['product_id']))
                product_nmd_list = metadata_service.get_distribution_products_by_store_id(
                    receive_by,
                    product_ids=product_id_list,
                    product_filters=False,
                    include_product_fields=["name", "code", "sale_type", "model_name", "model_code", "storage_type",
                                            "product_type", "category"],
                    partner_id=partner_id, user_id=user_id).get('rows', [])

                product_pur_list = metadata_service.get_purchase_product_by_store_id(
                    receive_by,
                    product_ids=product_id_list,
                    product_filters=False,
                    include_product_fields=["name", "code", "sale_type", "model_name", "model_code", "storage_type",
                                            "product_type", "category"],
                    partner_id=partner_id, user_id=user_id).get('rows', [])
                product_list = product_nmd_list + product_pur_list
                product_dict = {}
                if product_list:
                    for p in product_list:
                        product_dict[int(p['product_id'])] = p

                    for product_detail in product_items:
                        # 根据订货单，门店，商品插入一个订货商品(默认只能订原料和新零售成品)
                        # distribution_type:物流模式，如果不传则在配送区域或者采购区域依次获取
                        distribution_type = None
                        temp_distribution_type = product_detail.get("distribution_type")
                        product_id = int(product_detail.get("product_id"))
                        quantity = float(product_detail.get("quantity"))

                        product = product_dict.get(product_id)
                        if not product:
                            if distribution_type and distribution_type == Demand_type.NMD.code:
                                raise ProductError(
                                    "此门店总仓配送区域无此商品, store_id:{}, product_id:{}".format(receive_by, product_id))
                            elif distribution_type and distribution_type == 'PAD':
                                raise ProductError(
                                    "此门店加工配送区域无此商品, store_id:{}, product_id:{}".format(receive_by, product_id))
                            elif distribution_type and distribution_type == Demand_type.PUR.code:
                                raise ProductError(
                                    "此门店配送区域无此商品, store_id:{}, product_id:{}".format(receive_by, product_id))
                            else:
                                raise ProductError(
                                    "此门店配送或采购区域无此商品, store_id:{}, product_id:{}".format(receive_by, product_id))

                        units = product.get('units')
                        unit_rate = 1
                        if units:
                            order_flag = False
                            for u in units:
                                # 区域关联的单位是否存在且是否为可订货状态
                                if u.get("order"):
                                    unit_rate = float(u.get("rate", 1))
                                    product['unit_id'] = int(u.get('id'))
                                    order_flag = True
                                    break
                            if not order_flag:
                                raise ProductError("此商品未设置订货单位，请检查主档设置b, product_id:{}".format(product_id))
                        else:
                            raise ProductError("此商品未设置订货单位，请检查主档设置b, product_id:{}".format(product_id))

                        arrival_days = 0
                        if product.get("planned_arrival_days"):
                            arrival_days = product.get("planned_arrival_days")

                        allow_main_order = product.get("allow_main_order")
                        if not allow_main_order and type == Demand_type.MD.code:
                            raise DemandError("{}不允许主配订货".format(product.get('name')))

                        distribution_type = product.get('distr_type', temp_distribution_type)
                        tag_type = product.get('product_type')
                        order_unit = unit_dict.get(int(product['unit_id']))
                        rule_extends = ""
                        if product.get("next_plan_arrival_date"):
                            rule_extends_ = dict(
                                next_plan_arrival_date=product.get("next_plan_arrival_date"),
                                plan_arrive_date_text=product.get("plan_arrive_date_text"),
                                next_plan_arrive_date_text=product.get("next_plan_arrive_date_text"),
                            )
                            rule_extends = str(rule_extends_)
                        demand_product = Supply_demand_product(
                            demand_id=supply_demand.id,
                            product_id=int(product_id),
                            product_code=product.get("code"),
                            product_name=product.get("name"),
                            product_category_id=int(product.get('category', 0)),
                            product_category_name=category_map.get(int(product.get('category', 0)), {}).get('name',
                                                                                                            '未分类'),
                            increment_quantity=float(product.get("increment_number", 0)),
                            max_quantity=float(product.get("max_number", 0)),
                            min_quantity=float(product.get("min_number", 0)),
                            arrival_days=arrival_days,
                            distribution_circle=product.get('circle_type'),
                            sale_type=product.get('sale_type'),
                            product_type=product.get('product_type'),
                            unit_id=int(order_unit['id']),
                            unit_spec=order_unit.get("code", "无"),
                            unit_name=order_unit.get('name', "无"),
                            unit_rate=unit_rate,
                            spec=product.get('model_name', "无"),
                            storage_type=product.get("storage_type"),
                            quantity=float(quantity),
                            rule_extends=rule_extends
                        )
                        if distribution_type == Demand_type.NMD.code:
                            demand_product.distribute_by = int(product.get("distribution_center_id", 0))
                            demand_product.distribution_type = Demand_type.NMD.code
                        elif distribution_type == 'PAD':
                            demand_product.distribute_by = int(product.get("distribution_center_id", 0))
                            demand_product.distribution_type = 'PAD'
                        elif distribution_type == Demand_type.PUR.code:
                            demand_product.distribute_by = int(product.get("vendor_id", 0))
                            demand_product.distribution_type = Demand_type.PUR.code
                            demand_product.purchase_price = float(product.get("purchase_price", 0))
                            demand_product.purchase_tax = float(product.get('purchase_tax', 0))
                        if product.get("distribution_by"):
                            demand_product.distribute_by = int(product.get("distribution_by", 0))
                        DBSession.add(demand_product)
                DBSession.add(supply_demand)

    def create_product_main(self, items, demand_date, arrival_date, remark, user_id, partner_id, batch_id=None,
                            type=None):

        if batch_id:
            if_exist = Supply_demand.if_demand_exist(partner_id=partner_id, batch_id=batch_id)
            if if_exist:
                raise DemandError("请勿重复提交主配单！")
        else:
            raise DemandError("缺少唯一请求号，不允许创建！")

        if items and isinstance(items, list):
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)

            # 单位
            unit_list = metadata_service.get_unit_list(partner_id=partner_id, user_id=user_id).get('rows', [])
            unit_dict = {}
            if unit_list:
                for u in unit_list:
                    unit_dict[int(u['id'])] = u

            # 门店
            store_list = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id,
                                                         return_fields="id,code,name,type").get('rows', [])
            store_dict = {}
            if store_list:
                for s in store_list:
                    store_dict[int(s['id'])] = s

            # 商品类别
            category_ret = metadata_service.get_product_category_list(return_fields='id,code,name',
                                                                      partner_id=partner_id,
                                                                      user_id=user_id)
            category_map = {}
            if category_ret and category_ret.get('rows'):
                for c in category_ret.get('rows'):
                    category_map[int(c.get('id', 0))] = {
                        'code': c.get('code'),
                        'name': c.get('name')
                    }

            # 处理主配单
            for item in items:
                store = store_dict.get(int(item['store_id']))
                if not store:
                    raise StoreNotExist("门店:{},不存在".format(item.get("store_id")))
                # 非预开店/已开店的门店不允许订货
                status_check = metadata_service.check_store_status(int(item['store_id']), partner_id, user_id)
                if not status_check:
                    raise DataValidationException('请检查门店的开店状态！')
                supply_demand = {
                    "id": gen_snowflake_id(),
                    "code": Supply_doc_code.get_code_by_type(Demand_bill_code.STORE_DO.code, partner_id, user_id),
                    "store_secondary_id": store.get("code"),
                    "store_type": store.get("type"),
                    "type": Demand_type.MD.code,
                    "receive_by": int(item.get("store_id")),
                    "receive_name": store.get("name"),
                    "demand_date": demand_date,
                    "partner_id": partner_id,
                    "created_by": user_id,
                    "updated_by": user_id,
                    "sub_type": Demand_sub_type.PRODUCT.code,
                    "status": Demand_enum.INITED.code,
                    "process_status": Demand_enum.INITED.code,
                    "remark": remark,
                    "arrival_date": arrival_date,
                    "has_product": 1,
                    "created_name": username,
                    "updated_name": username,
                    "batch_id": batch_id
                }

                receive_by = int(item.get("store_id"))

                product_items = item.get('product_items')
                if not item.get('product_items'):
                    continue
                product_id_list = []
                for pi in product_items:
                    product_id_list.append(int(pi['product_id']))

                product_info_dict = dict()
                product_info_list = metadata_service.get_product_list(ids=product_id_list, include_units=True,
                                                                      return_fields="id,code,name,status,allow_order",
                                                                      filters={"status": "ENABLED",
                                                                               "allow_order": True},
                                                                      partner_id=partner_id,
                                                                      user_id=user_id).get('rows', [])
                for c in product_info_list:
                    product_info_dict[int(c.get('id'))] = c

                product_id_list = list(set(product_id_list))
                product_list = metadata_service.get_list_valid_product_for_distr_by_id(
                    receive_by,
                    product_ids=product_id_list,
                    filter_type=1,
                    partner_id=partner_id, user_id=user_id).get('rows', [])
                logging.info("get_list_valid_product_for_distr_by_id====:{}".format(product_list))
                product_dict = {}
                if product_list:
                    for p in product_list:
                        product_dict[int(p['product_id'])] = p

                supply_demand_list = []
                for product_detail in product_items:
                    # 根据订货单，门店，商品插入一个订货商品(默认只能订原料和新零售成品)
                    distribution_type = product_detail.get('distribution_type', 0)
                    distribution_by = product_detail.get('distribution_by')
                    product_id = int(product_detail.get("product_id"))
                    quantity = product_detail.get("quantity", 0)

                    product = product_dict.get(product_id)
                    if not product:
                        logging.info("此门店无此商品, store_id:{}, product_id:{}".format(receive_by, product_id))
                        continue
                    elif product.get('distribution_center_id') and int(product['distribution_center_id']) != int(
                            distribution_by):
                        logging.info("此门店{}{}区域不可订该商品{}".format(receive_by, distribution_type, product_id))
                        continue

                    product_info = product_info_dict.get(product_id)
                    if not product_info:
                        continue

                    units = product_info.get('units')
                    unit_rate = 1
                    if units:
                        order_flag = False
                        for u in units:
                            # 区域关联的单位是否存在且是否为可订货状态
                            if u.get("order"):
                                unit_rate = float(u.get("rate", 1))
                                product['unit_id'] = int(u.get('id'))
                                order_flag = True
                                break
                        if not order_flag:
                            raise ProductError("此商品未设置订货单位，请检查主档设置, product_id:{}".format(product_id))
                    else:
                        raise ProductError("此商品未设置单位，请检查主档设置, product_id:{}".format(product_id))

                    arrival_days = int(product.get("main_order_arrival_day", 0))
                    # if not product.get("allow_main_order") and type == Demand_type.MD.code:
                    #     raise DemandError("{}不允许主配订货".format(product.get('name')))

                    order_unit = unit_dict.get(int(product['unit_id']))
                    if not order_unit:
                        raise ProductError("此商品单位异常，请检查主档设置:{}".format(product.get("name")))
                    demand_product = {
                        "demand_id": supply_demand["id"],
                        "product_id": int(product_id),
                        "product_code": product_info.get("code"),
                        "product_name": product_info.get("name"),
                        "product_category_id": int(product.get('product_category_id', 0)),
                        "product_category_name": category_map.get(int(product.get('product_category_id', 0)), {}).get(
                            'name', '未分类'),
                        "increment_quantity": float(product.get("incr_qty", 0)),
                        "max_quantity": float(product.get("max_qty", 0)),
                        "min_quantity": float(product.get("min_qty", 0)),
                        "arrival_days": arrival_days,
                        "distribution_circle": product.get('circle_type'),
                        "sale_type": product.get('sale_type'),
                        "product_type": product.get('product_type'),
                        "unit_id": product.get('order_unit_id', "无"),
                        ###       'unit_name': i.get('order_unit_name', "未设置"),
                    # 'unit_spec': i.get("order_unit_code", "无"),
                        "unit_spec": product.get("order_unit_code", "无"),
                        "unit_name": product.get('order_unit_name', "无"),
                        "unit_rate": product.get('order_unit_rate', 1),
                        "spec": product.get('spec', "无"),
                        "storage_type": product.get("storage_type"),
                        "quantity": quantity,
                        "distribute_by": distribution_by,
                        "distribution_type": distribution_type}
                    if distribution_type == Demand_type.PUR.code:
                        demand_product['purchase_price'] = float(product.get("purchase_price", 0))
                        demand_product['purchase_tax'] = float(product.get('purchase_tax', 0))
                    supply_demand_list.append(demand_product)

                # demand_log = {
                #     "partner_id": partner_id,
                #     "store_id": receive_by,
                #     "demand_id": supply_demand["id"],
                #     "demand_code": supply_demand["code"],
                #     "action_code": "CREATE",
                #     "updated_name": username,
                #     "updated_by": user_id,
                #     "updated_at": datetime.utcnow()
                # }
                if len(supply_demand_list)>0:
                    Supply_demand.create_demand_and_product([supply_demand], supply_demand_list)
                # Kit.upload(partnerId=partner_id, docNo=supply_demand["code"], docType="总部分配单", actionType="总部分配创建",
                #            actionStatus=True, storeId=receive_by,
                #            storeName=supply_demand["receive_name"], content="总部分配单创建成功")

    def create_product_main_bycode(self, store_code=None, products=None,
                                   demand_date=None, arrival_date=None, remark=None,
                                   partner_id=None, user_id=None, batch_id=None, type=None, code=None):
        """
            商品主配 —— 三方调用接口
        """

        res = {
            "status": "failed",
            "batch_id": batch_id,
            "code": code,
            "msg": ""
        }

        # 主配batch_id 为唯一的创建请求id
        # 先判断batch_id是否已经创建过主配单
        if batch_id:
            if_exist = Supply_demand.if_demand_exist(partner_id=partner_id, batch_id=batch_id)
            if if_exist:
                res["msg"] = "请勿重复提交主配单"
                res["failed_code"] = "1"
                return res
        elif code:
            if_exist = Supply_demand.if_demand_exist(partner_id=partner_id, code=code)
            if if_exist:
                res["msg"] = "请勿重复提交主配单"
                res["failed_code"] = "1"
                return res
            batch_id = code
        else:
            res["msg"] = "缺少唯一请求号，不允许创建"
            res["failed_code"] = "1"
            return res

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        # 门店
        valid_open_status = ['PRE_OPEN', 'OPENED']
        filters = {"open_status__in": valid_open_status, "code__in": [store_code]}
        store_list = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id,
                                                     return_fields="id,code,name,type",
                                                     filters=filters).get('rows', [])
        if not store_list or len(store_list) == 0:
            res["msg"] = "请检查门店编号，该门店不存在"
            res["failed_code"] = "2"
            return res
        store = store_list[0]
        receive_by = int(store.get("id", "0"))  # 门店id

        #
        supply_demand = {
            "id": gen_snowflake_id(),
            "code": batch_id,  # 三方用batch_id作为code
            "store_secondary_id": store.get("code"),
            "store_type": store.get("type"),
            "type": "MD",
            "receive_by": int(store.get("id", "0")),
            "receive_name": store.get("name"),
            "demand_date": demand_date,
            "arrival_date": arrival_date,
            "partner_id": partner_id,
            "created_by": user_id,
            "updated_by": user_id,
            "sub_type": Demand_sub_type.PRODUCT.code,
            "status": Demand_enum.INITED.code,
            "process_status": Demand_enum.INITED.code,
            "remark": remark,
            "created_name": username,
            "updated_name": username,
            "batch_id": batch_id
        }
        supply_demand_product_list = []
        # 处理主配单商品
        if products:
            # 单位
            unit_list = metadata_service.get_unit_list(partner_id=partner_id, user_id=user_id).get('rows', [])
            unit_dict = {}
            if unit_list:
                for u in unit_list:
                    unit_dict[int(u['id'])] = u

            # 商品类别
            category_ret = metadata_service.get_product_category_list(return_fields='id,code,name',
                                                                      partner_id=partner_id,
                                                                      user_id=user_id)
            category_map = {}
            if category_ret and category_ret.get('rows'):
                for c in category_ret.get('rows'):
                    category_map[int(c.get('id', 0))] = {
                        'code': c.get('code'),
                        'name': c.get('name')
                    }

            product_code_list = []
            for pi in products:
                product_code_list.append(pi.product_code)
            product_list = metadata_service.get_product_list(partner_id=partner_id, user_id=user_id,
                                                             return_fields="id,code,name",
                                                             filters={"code__in": product_code_list}).get('rows', [])
            product_id_list = []
            for p in product_list:
                product_id_list.append(int(p.get("id", 0)))
            product_id_list = list(set(product_id_list))
            product_nmd_list = metadata_service.get_distribution_products_by_store_id(
                receive_by,
                product_ids=product_id_list,
                product_filters=False,
                include_product_fields=["name", "code", "sale_type", "model_name", "model_code", "storage_type",
                                        "product_type", "category"],
                partner_id=partner_id, user_id=user_id).get('rows', [])

            product_pur_list = metadata_service.get_purchase_product_by_store_id(
                receive_by,
                product_ids=product_id_list,
                product_filters=False,
                include_product_fields=["name", "code", "sale_type", "model_name", "model_code", "storage_type",
                                        "product_type", "category"],
                partner_id=partner_id, user_id=user_id).get('rows', [])
            product_list = product_nmd_list + product_pur_list
            if len(product_list) == 0:
                res["msg"] = "请检查商品配送/直送区域配置"
                res["failed_code"] = "2"
                return res
            product_dict = {}
            if product_list:
                for p in product_list:
                    product_dict[p['code']] = p

            for product_detail in products:
                product_detail = pb2dict(product_detail)
                product_code = product_detail.get("product_code")
                product = product_dict.get(product_code)
                if not product:
                    res["msg"] = "门店{}无此商品{}".format(store_code, product_code)
                    res["failed_code"] = "2"
                    return res
                product_id = product.get("product_id")

                # 根据订货单，门店，商品插入一个订货商品(默认只能订原料和新零售成品)
                distribution_type = product_detail.get('distribution_type', 0)
                distribution_by = product_detail.get('distribution_by')
                quantity = float(product_detail.get("quantity"))

                if product.get('distribution_by') and int(product['distribution_by']) != int(distribution_by):
                    res["msg"] = "此门店{}配送/采购区域不可订该商品{}".format(store_code, product_code)
                    res["failed_code"] = "2"
                    return res

                units = product.get('units')
                unit_rate = 1
                if units:
                    order_flag = False
                    for u in units:
                        # 区域关联的单位是否存在且是否为可订货状态
                        if u.get("order"):
                            unit_rate = float(u.get("rate", 1))
                            product['unit_id'] = int(u.get('id'))
                            order_flag = True
                            break
                    if not order_flag:
                        res["msg"] = "{}未设置订货单位".format(product.get('name'))
                        res["failed_code"] = "2"
                        return res
                else:
                    res["msg"] = "{}未设置单位".format(product.get('name'))
                    res["failed_code"] = "2"
                    return res

                arrival_days = product.get("planned_arrival_days")
                if not product.get("allow_main_order") and type == Demand_type.MD.code:
                    res["msg"] = "{}不允许主配订货".format(product.get('name'))
                    res["failed_code"] = "2"
                    return res

                order_unit = unit_dict.get(int(product['unit_id']))
                if not order_unit.get("id"):
                    raise ProductError("此商品单位异常，请检查主档设置:{}".format(product.get("name")))
                demand_product = {
                    "demand_id": supply_demand["id"],
                    "product_id": int(product_id),
                    "product_code": product.get("code"),
                    "product_name": product.get("name"),
                    "product_category_id": int(product.get('category', 0)),
                    "product_category_name": category_map.get(int(product.get('category', 0)), {}).get('name', '未分类'),
                    "increment_quantity": float(product.get("increment_number", 0)),
                    "max_quantity": float(product.get("max_number", 0)),
                    "min_quantity": float(product.get("min_number", 0)),
                    # "arrival_days": arrival_days,
                    "distribution_circle": product.get('circle_type'),
                    "sale_type": product.get('sale_type'),
                    "product_type": product.get('product_type'),
                    "unit_id": int(order_unit['id']),
                    "unit_spec": order_unit.get("code", "无"),
                    "unit_name": order_unit.get('name', "无"),
                    "unit_rate": unit_rate,
                    "spec": product.get('model_name', "无"),
                    "storage_type": product.get("storage_type"),
                    "quantity": float(quantity),
                    "distribute_by": distribution_by,
                    "distribution_type": distribution_type}
                if arrival_days and arrival_days.isdigit():
                    demand_product["arrival_days"] = int(arrival_days)
                else:
                    demand_product["arrival_days"] = None
                if distribution_type == Demand_type.PUR.code:
                    demand_product['purchase_price'] = float(product.get("purchase_price", 0))
                    demand_product['purchase_tax'] = float(product.get('purchase_tax', 0))
                supply_demand_product_list.append(demand_product)
            Supply_demand.create_demand_and_product([supply_demand], supply_demand_product_list)
            res["status"] = "success"

        else:
            res["msg"] = "缺少商品信息"
            res["failed_code"] = "2"
            return res
        return res

    @staticmethod
    def get_demand_detail(demand_id, partner_id, user_id):
        """
        获取门店订货单详细
        """
        demand = Supply_demand.get(demand_id)
        if not demand:
            raise DemandNotExistError("订货单不存在")

        demand = demand.serialize(conv=True)
        # 拼接plan相关信息
        if demand.get('batch_id'):
            plans = doc_plan_repository.list_doc_plan_by_ids(plan_ids=[demand['batch_id']], partner_id=partner_id,
                                                             user_id=user_id)
            if plans:
                plan = plans[0]
                demand["plan_method"] = plan.method
                demand["plan_name"] = plan.name

        return demand

    @staticmethod
    def export_demand_product(partner_id, user_id, type, sub_type, start_date, end_date, status):
        """
        根据租户和查询条件返回订货商品
        """
        pass

    @staticmethod
    @db_commit
    def reset_demand(partner_id, user_id, demand_id):
        """
        重置门店订单
        """
        demand = Supply_demand.get(demand_id)
        if not demand:
            raise DemandNotExistError("订货单不存在")
        if demand.status not in (Demand_enum.INITED.code, Demand_enum.REJECTED.code):
            raise DemandTypeError("订货单类型错误")
        demand.has_product = 0
        DBSession.add(demand)
        return Supply_demand_product.reset_quantity(demand_id)

    @staticmethod
    def delete_demand(demand_id, partner_id, user_id):
        """
        删除紧急订货单或者主配订单
        """
        demand = Supply_demand.get(demand_id)
        if not demand:
            raise DemandNotExistError("订货单不存在")
        if demand.status != Demand_enum.INITED.code:
            raise DemandStatusError("订货单状态错误")
        if demand.type == Demand_type.SD.code:
            # 门市订货单不能删除
            raise DemandTypeError("订货单类型错误")
        if demand.partner_id != partner_id:
            raise DemandBelongError("订货单不属于改商户")
        Supply_demand.delete_demand_and_product_by_id(demand_id)

    @staticmethod
    @db_commit
    def handle_demand_to_order(bizdt, partner_id, user_id):
        store_list = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id)
        if store_list.get("rows"):
            for store in store_list.get("rows"):
                try:
                    store_id = store.get('id')
                    status_list = [Demand_enum.CAL_DONE.code, Demand_enum.SUBMITTED.code, Demand_enum.APPROVED.code]
                    q = DBSession.query(Supply_demand.id).filter(Supply_demand.receive_by == int(store_id)).filter(
                        Supply_demand.partner_id == int(partner_id)).filter(
                        Supply_demand.process_status != Demand_enum.SUCCESS.code).filter(
                        Supply_demand.status.in_(status_list)).filter(Supply_demand.demand_date == bizdt).filter(
                        Supply_demand.type == Demand_type.SD.code)
                    # 以上状态外的门市订货单直接作废，不能发去拆单
                    DBSession.query(Supply_demand).filter(Supply_demand.receive_by == int(store_id)).filter(
                        Supply_demand.partner_id == int(partner_id)).filter(
                        Supply_demand.status.notin_(status_list)).filter(
                        Supply_demand.demand_date == bizdt).filter(Supply_demand.type == Demand_type.SD.code).update(
                        {Supply_demand.status: Demand_enum.CANCELLED.code, Supply_demand.updated_at: datetime.utcnow()},
                        synchronize_session=False)
                    demand_ids = q.with_for_update().all()
                    if demand_ids:
                        demand_ids = [demand[0] for demand in demand_ids]
                        DBSession.query(Supply_demand).filter(Supply_demand.id.in_(demand_ids)).update(
                            {Supply_demand.process_status: Demand_enum.PROCESSING.code,
                             Supply_demand.updated_at: datetime.utcnow()},
                            synchronize_session=False)
                        DBSession.commit()
                    else:
                        DBSession.commit()
                        continue
                    message = {}
                    message['partner_id'] = partner_id
                    message['user_id'] = user_id
                    message['demand_ids'] = demand_ids
                    logger.info("检查需要处理的订单id:{}".format(demand_ids))
                    # public(MessageTopic.DEMAND_STORE_DEMAND_ORDER_TOPIC, message)
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.DEMAND_STORE_DEMAND_ORDER_TOPIC,
                                        message=message)
                except Exception as e:
                    import traceback
                    logger.error("检查订单出错, store_id:{}, demand_ids:{}, msg:{}".format(store.get('id'), demand_ids,
                                                                                     traceback.format_exc()))
                    DBSession.rollback()

    @staticmethod
    @db_commit
    def _generate_order_by_demand(demand_id, partner_id):

        demand = DBSession.query(Supply_demand).with_for_update().get(demand_id)

        # --拆单前判断是否需要拆单，部分客户要求拆单步骤不在boh进行 —— HJT专，saas可不看
        bus_config_db = BusinessConfigModel.get_business_config(partner_id=partner_id, business_schema="generate_order")
        if bus_config_db and bus_config_db.is_open == "false":  # false 说明不在boh拆单
            if bus_config_db.extra_config:
                extra_config = json.loads(bus_config_db.extra_config)
                include_stores = extra_config.get("include_stores", [])
                all_stores = extra_config.get("all_stores")
                if all_stores:  # 所有门店均不拆单
                    return {}
                if len(include_stores) and str(demand.receive_by) in include_stores:  # 部门门店不拆单
                    return {}
        # 拆单判断end--

        # 订货单拆成要货单
        product_list, p_count = Supply_demand_product.get_by_demand_id(demand.id)
        temp_order_map = {}
        key_set_list = []
        key_set_order_map = {}
        key_set_order_count = 0
        username = metadata_service.get_username_by_pid_uid(demand.partner_id, demand.updated_by)
        product_id_list = []
        product_code_list = []
        configs = metadata_service.get_supply_config(partner_id=partner_id, domain='boh.store.order', user_id=1)
        order_config, switch = configs.get('combine_storage_type', {}), configs.get('order_by_storage_type')
        pur_storage = {i: v[0] for v in order_config.get('direct_pur', []) for i in v}
        nmd_storage = {i: v[0] for v in order_config.get('direct_nmd', []) for i in v}
        pur_storage = pur_storage if switch and pur_storage.keys() == DEFAULT_STORAGE_TYPE.keys() else DEFAULT_STORAGE_TYPE
        nmd_storage = nmd_storage if switch and nmd_storage.keys() == DEFAULT_STORAGE_TYPE.keys() else DEFAULT_STORAGE_TYPE
        # print(pur_storage, nmd_storage)

        for product in product_list:
            product_id_list.append(str(product.product_id))
            product_code_list.append(str(product.product_code))
            if product.quantity == 0:
                continue
            # seesaw允許成品半成品訂貨拆單
            # if product.product_type == "FINISHED" and not (str(product.sale_type).startswith("NEW-RETAIL")
            #                                                or product.sale_type == 'SOUVENIR'):
            #     # 如果是成品，但不是新零售和周边类型则跳过
            #     continue
            # if demand.type == Demand_type.SD.code or demand.type == Demand_type.AD.code:
            #     # 配送订货对部分储藏方式合并
            #     if product.distribution_type == 'NMD':
            #         # 冷藏+冷冻 不拆单，合并为冻货
            #         if product.storage_type == 'LC' or product.storage_type == 'COLD':
            #             key_set = (product.distribute_by, product.distribution_type, product.arrival_days, 'COLD')
            #         # 常温+恒温 不拆单，合并成常温
            #         elif product.storage_type == 'NORMALTP' or product.storage_type == 'HW':
            #             key_set = (product.distribute_by, product.distribution_type, product.arrival_days, 'NORMALTP')
            #         else:
            #             key_set = (
            #                 product.distribute_by, product.distribution_type, product.arrival_days,
            #                 product.storage_type)
            #     else:
            #         key_set = (
            #             product.distribute_by, product.distribution_type, product.arrival_days, product.storage_type)
            #
            # if demand.type in (Demand_type.HD.code, Demand_type.MD.code):
            #     # 配送订货对部分储藏方式合并
            #     # 总仓配送/ 加工配送
            #     if product.distribution_type == 'NMD' or product.distribution_type == 'PAD':
            #         # 冷藏+冷冻 不拆单，合并为冻货
            #         if product.storage_type == 'LC' or product.storage_type == 'COLD':
            #             key_set = (product.distribute_by, product.distribution_type, 'COLD')
            #         # 常温+恒温 不拆单，合并成常温
            #         elif product.storage_type == 'NORMALTP' or product.storage_type == 'HW':
            #             key_set = (product.distribute_by, product.distribution_type, 'NORMALTP')
            #         else:
            #             key_set = (product.distribute_by, product.distribution_type, product.storage_type)
            #     else:
            #         key_set = (product.distribute_by, product.distribution_type, product.storage_type)

            if demand.type == Demand_type.SD.code or demand.type == Demand_type.AD.code:
                if product.distribution_type == 'NMD':
                    key_set = (product.distribute_by, product.distribution_type, product.arrival_days,
                               nmd_storage.get(product.storage_type, ''))
                else:
                    key_set = (product.distribute_by, product.distribution_type, product.arrival_days,
                               pur_storage.get(product.storage_type, ''))
                if key_set in temp_order_map:
                    temp_order_map.get(key_set).append(product)
                else:
                    temp_order_map[key_set] = [product]
                    key_set_list.append(key_set)
            elif demand.type in (Demand_type.HD.code, Demand_type.MD.code):
                if product.distribution_type == 'NMD':
                    key_set = (product.distribute_by, product.distribution_type,
                               nmd_storage.get(product.storage_type, ''))
                else:
                    key_set = (product.distribute_by, product.distribution_type,
                               pur_storage.get(product.storage_type, ''))
                if key_set in temp_order_map:
                    temp_order_map.get(key_set).append(product)
                else:
                    temp_order_map[key_set] = [product]
                    key_set_list.append(key_set)
            else:
                logging.info(f'{demand_id}: 未拆单')
                return {}

        key_set_list = list(set(key_set_list))
        key_set_list.sort()
        for ks in key_set_list:
            key_set_order_count += 1
            key_set_order_map[ks] = str(key_set_order_count)
        if not temp_order_map:
            # # 空单作废
            # demand.status = Demand_enum.CANCELLED.code
            # logging.info('空单作废{}'.format(demand.id))
            return {}
        result = {}
        for key, value in temp_order_map.items():
            key_set_key = key
            distribution = key[0]
            distribution_type = key[1]
            storage_type = None
            arrival_date = None
            if demand.type == Demand_type.SD.code or demand.type == Demand_type.AD.code:
                expect_time = str_convert_to_timedelta(demand.expect_time) if demand.expect_time else timedelta()
                arrival_date = demand.demand_date + timedelta(days=int(key[2])) + expect_time
                storage_type = key[3]
            if demand.type in (Demand_type.HD.code, Demand_type.MD.code):
                arrival_date = demand.arrival_date
                storage_type = key[2]

            order_id = gen_snowflake_id()
            order_code = Supply_doc_code.get_code_by_type(Demand_bill_code.STORE_DM.code, demand.partner_id, None)

            # 转换单位
            product_unit_dict = {}
            products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                              include_units=True,
                                                              return_fields='id,code,name',
                                                              partner_id=demand.partner_id,
                                                              user_id=demand.updated_by).get('rows', [])
            for product_detail in products_info:
                product_unit_dict[product_detail['id']] = {}
                if product_detail.get('units'):
                    for unit in product_detail.get('units'):
                        if unit.get('purchase') and unit['purchase'] == True:
                            product_unit_dict[product_detail['id']]['purchase'] = unit['rate'] if unit.get(
                                'rate') else 0
                        if unit.get('order') and unit['order'] == True:
                            product_unit_dict[product_detail['id']]['order'] = unit['rate'] if unit.get('rate') else 0
                        if unit.get('default') and unit['default'] == True:
                            product_unit_dict[product_detail['id']]['default'] = unit['rate'] if unit.get('rate') else 0
                else:
                    product_unit_dict[product_detail['id']] = {
                        'default': 0
                    }
            # 直送单需要拿到价格，获取商品成本
            purchase2order_rate_dict = {}
            product_tax_dict = {}
            if distribution_type == 'PUR':
                # 获取采购成本
                valid_time = demand.demand_date + timedelta(hours=8)  # 合同按北京时间处理，所以要强制utc转北京时间。在主档修改前，这8小时不能删。
                product_tax_dict = metadata_service.get_tax_list(
                    vendor_id=distribution, product_id_list=product_id_list,
                    store_id=demand.receive_by, valid_time=valid_time,
                    partner_id=demand.partner_id, user_id=demand.updated_by)
                # 订货单位*订货rate=采购单位*采购rate
                # 采购单位/订货单位=订货rate/采购rate

                # 订货单价*订货单位=采购单价*采购单位
                # 订货单价 = 采购单价*采购单位/订货单位
                # 订货单价 = 采购单价*订货rate/采购rate
                for key, key_value in product_unit_dict.items():
                    purchase_rate = key_value.get('purchase') if key_value.get('purchase') else 1
                    order_rate = key_value.get('order') if key_value.get('order') else 1
                    purchase2order_rate_dict[int(key)] = order_rate / purchase_rate

            # ---ForReceiptStart---#
            # 生成receipt订货单 —— 替换成receipt内部单据
            line_count = 0
            products = []
            for product in value:
                unit_rate = product.unit_rate
                unit_id = product.unit_id
                unit_name = product.unit_name
                # lelecha订货库存调整存入的是订货单位！！！！！ TODO 待优化
                if product_unit_dict.get(str(product.product_id)) and demand.is_adjust == True:
                    unit_rate = product_unit_dict.get(str(product.product_id)).get('order')
                line_count += 1
                product_detail = {}
                product_detail['order_id'] = order_id
                product_detail['order_by'] = demand.receive_by
                product_detail['product_id'] = product.product_id
                product_detail['product_code'] = product.product_code
                product_detail['product_name'] = product.product_name
                product_detail['storage_type'] = product.storage_type
                product_detail['category_id'] = product.product_category_id
                product_detail['category_name'] = product.product_category_name
                # product_detail['price'] =
                product_detail['is_confirmed'] = False
                product_detail['partner_id'] = demand.partner_id
                product_detail['created_by'] = demand.updated_by
                product_detail['created_name'] = username
                product_detail['updated_by'] = demand.updated_by
                product_detail['updated_name'] = username
                product_detail['order_quantity'] = product.quantity

                product_detail['unit_id'] = unit_id
                product_detail['unit_name'] = unit_name
                product_detail['unit_spec'] = product.spec
                product_detail['unit_rate'] = unit_rate
                product_detail['sale_type'] = product.sale_type
                if distribution_type == 'PUR':
                    # if not product_tax_dict.get(product.get('product_id')):
                    #     raise DataValidationException('{} 没有维护合同'.format(product.get('product_id')))
                    # 采购价格转成订货价格
                    product_detail['cost_price'] = product_tax_dict.get(product.product_id, {}).get('no_tax',
                                                                                                    0) * purchase2order_rate_dict.get(
                        product.product_id, 1)
                    product_detail['tax_price'] = product_tax_dict.get(product.product_id, {}).get('tax',
                                                                                                   0) * purchase2order_rate_dict.get(
                        product.product_id, 1)
                    product_detail['tax_rate'] = product_tax_dict.get(product.product_id, {}).get('rate', 0)
                # order_product.product_type = product.product_type
                products.append(product_detail)
            batch_type = None
            if demand.type == 'AD':
                batch_type = 'DEMAND_ADJUST'
            else:
                batch_type = 'DEMAND'
            logging.info('generate_order_by_demand:{}, product_qty:{}, key:{}'.format(demand.id, key, len(products)))
            receipt_service.create_orders(
                batch_id=demand.id, batch_code=demand.code, batch_type=batch_type,
                order_id=order_id, order_code=order_code, main_branch_type='S',
                receive_by=demand.receive_by, delivery_by=distribution,
                storage_type=storage_type, distr_type=distribution_type, demand_type=demand.type,
                demand_date=demand.demand_date, delivery_date=demand.demand_date, expect_date=arrival_date,
                products=products, partner_id=demand.partner_id, user_id=demand.updated_by,
                separate_no=key_set_order_map.get(key_set_key), remark=demand.remark, reason=demand.reason_type)
            # ---ForReceiptEnd---#

        return result

    @staticmethod
    @time_cost
    def get_valid_demand_product(store_id, partner_id, user_id, distr_type=None, type=Demand_type.SD.code,
                                 product_ids=None,
                                 order_date=None, vendor_id=None, category_ids=None, limit=None, offset=None,
                                 order_by=None):
        """
        获得可订货商品（门市订货单都是提前生成，所以前端不会查门市可订货商品）
        type:门店订货(SD)、紧急订货(HD)、主配订货(MD)
        distr_type:物流模式: 配送:NMD, 直送:PUR, 不传查询所有
        vendor_id:配送的配送中心/直送的供应商，默认可不传
        """
        order_by_inventory = order_by == 'real_inventory'
        logging.info("distr_type------{}".format(distr_type))

        real_limit = limit
        real_offset = offset
        if order_by_inventory:
            real_limit = -1
            real_offset = 0
        if vendor_id:
            filters = {"distribution_center_id": str(vendor_id)}
        else:
            filters = {}

        if category_ids:
            product_relation_filters = {"product_category": [str(id) for id in category_ids]}
        else:
            product_relation_filters = {}

        ret = {}
        if type == Demand_type.SD.code:
            # 门市订货
            # 2021-06-02 商品属性&配方属性业务影响：只对配方属性=现做bom的无法订货和主配做限制
            product_filters = {"status__eq": "ENABLED", "allow_order__eq": True, "bom_type__neq": "MANUFACTURE"}
            # 2021-06-02 商品销售类型、商品属性过滤去除
            #    "__or": [{"bom_type__neq": "MANUFACTURE", # MANUFACTURE=现做BOM
            #              "sale_type__in": ["NEW-RETAIL-SHORT-TERM", "NEW-RETAIL-NORMAL", "SOUVENIR"]
            #              },
            #             {"product_type__neq": "FINISHED"}
            #             ]}
            ret = metadata_service.get_list_valid_product_for_distr_by_id(store_id,
                                                                          product_ids=product_ids,
                                                                          include_product_fields='name,code,model_name,model_code,storage_type,category,product_type,sale_type,barcode',
                                                                          distr_type=distr_type, order_date=order_date,
                                                                          filters=filters, limit=real_limit,
                                                                          offset=real_offset,
                                                                          product_relation_filters=product_relation_filters,
                                                                          product_filters=product_filters,
                                                                          partner_id=partner_id, user_id=user_id)

        elif type == 'RETURN' or type == 'PURCHASR_RETURN':
            # 门店退货商品
            product_filters = {"status__eq": "ENABLED",
                               "bom_type__neq": "MANUFACTURE",
                               "allow_order__eq": True}

            ret = metadata_service.get_list_valid_product_for_distr_by_id(store_id, product_ids=product_ids,
                                                                          filter_type=2,
                                                                          include_product_fields='name,code,model_name,model_code,storage_type,category,product_type,sale_type,barcode',
                                                                          include_product_units=True,
                                                                          distr_type=distr_type, filters=filters,
                                                                          limit=real_limit, offset=real_offset,
                                                                          product_relation_filters=product_relation_filters,
                                                                          product_filters=product_filters,
                                                                          partner_id=partner_id, user_id=user_id)
        else:
            # 非门市订货
            filters = filters.update({"allow_main_order": True})
            if type == Demand_type.HD.code:
                # 2021-06-02 商品属性&配方属性业务影响：只对配方属性=现做bom的无法订货和主配做限制
                product_filters = {"status__eq": "ENABLED", "bom_type__neq": "MANUFACTURE", "allow_order__eq": True}
                # 2021-06-02 商品销售类型、商品属性过滤去除
                #    "__or": [{"bom_type__neq": "MANUFACTURE", "allow_order__eq": True,
                #              "sale_type__in": ["NEW-RETAIL-SHORT-TERM", "NEW-RETAIL-NORMAL",
                #                                "SOUVENIR"]},
                #             {"product_type__neq": "FINISHED"}]}
            elif type == Demand_type.MD.code:
                # 2021-06-02 商品属性&配方属性业务影响：只对配方属性=现做bom的无法订货和主配做限制
                product_filters = {"status": "ENABLED", "bom_type__in": ["INVENTORY"]}
                # 2021-06-02 商品销售类型、商品属性过滤去除
                #    "__or": [{"bom_type__neq": "MANUFACTURE",
                #              "sale_type__in": ["NEW-RETAIL-SHORT-TERM", "NEW-RETAIL-NORMAL",
                #                                "SOUVENIR"]},
                #             {"product_type__neq": "FINISHED"}]}
            else:
                # 2021-06-02 商品属性&配方属性业务影响：只对配方属性=现做bom的无法订货和主配做限制
                product_filters = {"status__eq": "ENABLED", "allow_order__eq": True}
            ret = metadata_service.get_list_valid_product_for_distr_by_id(store_id, product_ids=product_ids,
                                                                          include_product_fields='name,code,model_name,model_name,model_code,storage_type,category,product_type,sale_type,barcode',
                                                                          distr_type=distr_type, filters=filters,
                                                                          limit=real_limit, offset=real_offset,
                                                                          filter_type=2,
                                                                          product_relation_filters=product_relation_filters,
                                                                          product_filters=product_filters,
                                                                          partner_id=partner_id, user_id=user_id)

        rows = ret.get('rows')
        total = int(ret.get("total", 0))
        result = []
        final_product_ids = []
        product_dict = {}
        if rows:

            config_product_ids = []
            for i in rows:
                config_product_ids.append(int(i.get("product_id")))
            product_dict = dict()
            product_list = metadata_service.get_product_list(ids=config_product_ids,
                                                             return_fields="id,code,name,status,allow_order,image_url",
                                                             filters={"status": "ENABLED", "allow_order": True},
                                                             partner_id=partner_id,
                                                             user_id=user_id).get('rows', [])
            item_codes = []
            for c in product_list:
                product_dict[int(c.get('id'))] = c
                item_codes.append(c.get('code'))
            price_dict = dict()
            price_list = metadata_service.get_product_price_by_store_id(store_id=store_id,
                                                                        item_codes=item_codes,
                                                                        partner_id=partner_id,
                                                                        user_id=user_id).get('rows', [])
            for c in price_list:
                price_dict[c.get('item_code')] = [c['price'], c.get('currency','')]

            logging.info("price_dict---{}".format(price_dict))
            for i in rows:
                product_id = int(i.get("product_id"))

                product_info = product_dict.get(product_id)
                if not product_info:
                    continue
                product_code = product_info.get('code')
                product_name = product_info.get('name')
                image = product_info.get('extends', {}).get('image_url')
                logging.info("product_info---{}".format(product_info))

                price = price_dict.get(product_code, ['', ''])[0]
                currency = price_dict.get(product_code, ['', ''])[1]
                logging.info("price_price---{}".format(price))
                temp_product = {
                    'increment_quantity': float(i.get("incr_qty", 0)) if i.get("incr_qty") else 0.0,
                    'max_quantity': float(i.get("max_qty", 0)) if i.get("max_qty") else 0.0,
                    'min_quantity': float(i.get("min_qty", 0)) if i.get("min_qty") else 0.0,
                    'product_category_id': int(i.get("product_category_id")) if i.get("product_category_id") else 0,
                    'product_code': product_code,
                    'product_id': int(i.get("product_id")) if i.get("product_id") else 0,
                    'product_name': product_name,
                    'spec': i.get("spec", "无"),
                    'storage_type': i.get("storage_type"),
                    'unit_id': int(i.get("order_unit_id")) if i.get("order_unit_id") else 0,
                    # "unit_rate": float(unit.get("rate", 0)),
                    'distribution_circle': i.get("circle_type"),
                    'arrival_days': int(i.get("main_order_arrival_day", 0)),
                    'sale_type': i.get("sale_type"),
                    'product_type': i.get("product_type"),
                    'distribution_center_id': int(i.get("distribution_center_id")) if i.get(
                        "distribution_center_id") else 0,
                    'vendor_id': int(i.get("vendor_id")) if i.get("vendor_id") else 0,
                    'unit_name': i.get('order_unit_name', "未设置"),
                    'unit_spec': i.get("order_unit_code", "无"),
                    'unit_rate': float(i.get("order_unit_rate", 1)),
                    'cycle_coef': i.get("cycle_coef"),
                    'barcode': i['extends'].get('barcode') if i.get('extends') else [],
                    'price': price,
                    "image": image,
                    'currency': currency
                }

                if i.get("logistic_mode") == Demand_type.NMD.code:
                    # 配送区域的商品
                    temp_product['distribute_by'] = int(i.get("distribution_center_id")) if i.get(
                        "distribution_center_id") else 0
                    temp_product['distribution_type'] = Demand_type.NMD.code
                    # 面包订货
                    if i.get("extends"):
                        # 面包订货的商品
                        extends = i.get("extends")
                        if extends.get('distr_type') == 'BREAD':
                            temp_product['distribute_by'] = int(i.get("distribution_center_id")) if i.get(
                                "distribution_center_id") else 0
                            temp_product['distribution_type'] = 'BREAD'

                if i.get("logistic_mode") == 'PAD':
                    # 配送区域的商品
                    temp_product['distribute_by'] = int(i.get("distribution_center_id")) if i.get(
                        "distribution_center_id") else 0
                    temp_product['distribution_type'] = 'PAD'

                if i.get("logistic_mode") == Demand_type.PUR.code:
                    # 采购区域的商品
                    temp_product['distribute_by'] = int(i.get("distribution_center_id")) if i.get(
                        "distribution_center_id") else 0
                    temp_product['distribution_type'] = Demand_type.PUR.code
                    temp_product['purchase_price'] = float(i.get("purchase_price", 0))
                    temp_product['purchase_tax'] = float(i.get('purchase_tax', 0))

                logging.info("final_result-distr_type-logistic_mode---{}--{}".format(distr_type, temp_product.get(
                    'distribution_type', "")))
                if distr_type != "":
                    if distr_type != temp_product.get('distribution_type', ""):
                        continue

                result.append(temp_product)
                final_product_ids.append(temp_product['product_id'])
                product_dict[temp_product['product_id']] = temp_product

        # 新增按照实时库存排序
        final_result = result
        total = len(final_product_ids)
        if order_by_inventory and final_product_ids:

            final_result = []
            order_result = inventory_service.query_branch_inventory_sort(partner_id, user_id, store_id,
                                                                         product_ids=final_product_ids, limit=limit,
                                                                         offset=offset).get('rows')
            # logging.info(f'库存排序：{order_result}')
            if order_result:
                for i in order_result:
                    tmp_record = product_dict.get(int(i.get('product_id')))
                    if tmp_record:
                        tmp_record['real_inventory_qty'] = i.get('qty')
                        final_result.append(tmp_record)
        logging.info("final_result---{}".format(final_result))
        return final_result, total

    @staticmethod
    @db_commit
    def update_demand_info(demand_id, remark, arrival_date,tag_id, partner_id, user_id, expect_time: datetime = None):
        demand = Supply_demand.get(demand_id)
        if not demand:
            raise DemandNotExistError("订货单不存在")
        if demand.status in (Demand_enum.APPROVED.code, Demand_enum.SUBMITTED.code, Demand_enum.CANCELLED.code):
            raise DemandStatusError("订货单审核通过、作废、提交状态，不能修改订货单信息")
        if remark:
            demand.remark = remark
        if arrival_date and demand.type != Demand_type.SD.code and arrival_date.year != 1970:
            demand.arrival_date = arrival_date
        if expect_time:
            expect_time = datetime_slice_to_time(expect_time)
            demand.expect_time = expect_time
        if tag_id:
            demand.tag_id = tag_id
        demand.updated_by = user_id
        demand.updated_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        DBSession.add(demand)

        receipt_service.update_order(batch_id=demand_id, remark=remark, partner_id=partner_id, user_id=user_id)

    @staticmethod
    @db_commit
    def _demand_parse_bom(demand):
        if isinstance(demand, int):
            demand = Supply_demand.get(demand)
        if not demand:
            raise DemandNotExistError('订货单不存在')
        demand_finished_product_list = Supply_demand_product.get_finished_product_by_id(demand.id)

        if demand_finished_product_list:
            tea_and_bread_demand_products = []
            other_demand_products = []
            for p in demand_finished_product_list:
                if p.sale_type in ['TEA', 'BREAD']:
                    tea_and_bread_demand_products.append(p)
                else:
                    other_demand_products.append(p)
            tea_and_bread_material_suggest_data = {}
            try:
                tea_and_bread_material_suggest_data = demand_module.get_tea_and_bread_suggest(
                    demand, tea_and_bread_demand_products
                )
            except Exception as e:
                import traceback
                print(traceback.format_exc())
                logger.error("get_tea_and_bread_suggest error " + str(e))

            demand_date = datetime_to_strdate2(demand.demand_date)
            for item in demand_finished_product_list:
                try:
                    if not item.quantity:
                        continue
                    bom_result = Bom_service.get_bom(gen_snowflake_id(), item.product_id, item.product_code,
                                                     demand.receive_by,
                                                     demand.store_secondary_id, demand_date, "DEMAND", demand.code,
                                                     float(item.quantity),
                                                     partner_id=demand.partner_id, user_id=demand.updated_by)
                    boms = bom_result.get('bom')
                    if boms:
                        for bom in boms:
                            raw_product = Supply_demand_product.get_product_by_demand_id_and_product_id(demand.id, int(
                                bom['product_id']))
                            if not raw_product:
                                continue
                            product = metadata_service.get_product(raw_product.product_id, include_units=True,
                                                                   partner_id=demand.partner_id,
                                                                   user_id=demand.updated_by)
                            quantity = float(bom.get("qty", 0)) * float(raw_product.unit_rate)
                            logger.info(
                                "quantity={},raw_product.max_quantity={}, raw_product.min_quantity={}, raw_product.increment_quantity={}".format(
                                    quantity,
                                    raw_product.max_quantity, raw_product.min_quantity, raw_product.increment_quantity))

                            raw_product.quantity = get_quantity(quantity, raw_product.max_quantity,
                                                                raw_product.min_quantity,
                                                                raw_product.increment_quantity)
                            cycle_extends = raw_product.cycle_extends
                            if cycle_extends != '':
                                cycle_extends = eval(cycle_extends)
                                cycle_extends['quantity'] = float(raw_product.quantity)
                                raw_product.cycle_extends = str(cycle_extends)
                            if raw_product.product_id in tea_and_bread_material_suggest_data:
                                raw_product.suggest_quantity = tea_and_bread_material_suggest_data[
                                    raw_product.product_id]
                            DBSession.add(raw_product)
                except Exception as e:
                    import traceback
                    logger.warning("物料拆解失败, 暂时不做处理, msg:{}".format(traceback.format_exc()))
                    continue

    @staticmethod
    def get_tea_and_bread_suggest(demand, tea_and_bread_products):
        """

        :return: {
            <raw_product_id>: suggest_quantity,
            ...
        }
        """
        if not tea_and_bread_products:
            return {}
        product_ids = [p.product_id for p in tea_and_bread_products]
        suggest_data = TeaBreadDemandMaterialSuggest.get_suggest(
            demand.partner_id, demand.user_id, demand.demand_date, demand.receive_by, product_ids
        )
        return suggest_data

    @staticmethod
    @time_cost
    def get_valid_store_product(product_ids, type, partner_id, user_id, order_date=None, branch_ids=None,
                                category_ids=None):
        # 此接口暂时不支持分页
        if type == Demand_type.SD.code:
            raise DemandTypeError("此接口不能使用门市订货类型,type=SD")
        if not product_ids:
            return None
        if branch_ids and isinstance(branch_ids, list):
            store_relation_filters = {'geo_region': [str(i) for i in branch_ids]}
        else:
            store_relation_filters = {}

        if category_ids:
            product_relation_filters = {"product_category": [str(id) for id in category_ids]}
        else:
            product_relation_filters = {}

        # product_filters = {"allow_order__eq": True}
        product_filters = {}

        include_product_fields = 'name,code,model_name,model_name,model_code,storage_type,category,product_type,sale_type,distr_type'
        filters = {"allow_main_order": True}
        store_filters = {"status": "ENABLED"}
        result = []
        valid_store_products = metadata_service.get_list_valid_store_for_distr_by_product_ids(
            product_ids, include_product_fields=include_product_fields, filters=filters, store_filters=store_filters,
            product_filters=product_filters,
            product_relation_filters=product_relation_filters, store_relation_filters=store_relation_filters,
            partner_id=partner_id, user_id=user_id
        )
        logging.info("valid_store_products======:{}".format(valid_store_products))
        total = int(valid_store_products.get('total', 0))

        store_list = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id).get('rows', [])
        store_dict = {}
        if store_list:
            for s in store_list:
                store_dict[int(s['id'])] = s
        product_dict = dict()
        product_list = metadata_service.get_product_list(ids=product_ids,
                                                         filters={"status": "ENABLED", "allow_order": True},
                                                         return_fields="id,code,name",
                                                         partner_id=partner_id,
                                                         user_id=user_id).get('rows', [])
        for c in product_list:
            product_dict[int(c.get('id'))] = c
        # print("valid_store_products===" + str(valid_store_products))
        if valid_store_products and valid_store_products.get("rows"):
            for item in valid_store_products.get("rows"):
                # print("valid_store_products===2222222" + str(item))
                store_id = int(item.get("store_id", 0))
                store_produts = None
                if not store_id:
                    continue
                for store_item in result:
                    if store_item.get("store_id") == store_id:
                        store_produts = store_item
                        break
                # units = item.get('units')

                # unit = {}
                # if units:
                #     for u in units:
                #         if u.get("order"):
                #             unit = u
                #             break
                # if not unit:
                #     logger.warning("没有设置订货单位, product_id:{}".format(item.get("product_id")))
                #     continue
                product_info = product_dict.get(int(item.get("product_id")))
                if not product_info:
                    continue
                temp_product = {
                    "arrival_days": int(item.get('main_order_arrival_day', 0)),
                    "increment_quantity": float(item.get("incr_qty", 0)),
                    "max_quantity": float(item.get("max_qty", 0)),
                    "min_quantity": float(item.get("min_qty", 0)),
                    'unit_name': item.get('order_unit_name', "未设置"),
                    'unit_spec': item.get("order_unit_code", "无"),
                    # 'unit_rate': item.get("order_unit_rate", 1),
                    'unit_id': int(item.get("order_unit_id")) if item.get("order_unit_id") else 0,
                    "purchase_price": float(item.get("purchase_price", 0)),
                    "purchase_tax": float(item.get("purchase_tax", 0)),
                    "distribute_by": int(item.get("distribution_center_id", 0)) if item.get(
                        "logistic_mode") == Demand_type.PUR.code else int(item.get("distribution_center_id", 0)),
                    "distribution_type": item.get("logistic_mode"),
                    "product_id": int(item.get("product_id", 0)),
                    "product_code": product_info.get("code"),
                    "product_name": product_info.get("name")
                }
                if store_produts:
                    store_produts['store_products'].append(temp_product)
                else:
                    store = store_dict.get(store_id)
                    if not store:
                        raise StoreNotExist("门店不存在，store_id:%s" % store_id)
                    store_produts = {
                        "store_id": store_id,
                        "store_code": store.get("code"),
                        "store_name": store.get("name"),
                        "store_products": [temp_product]
                    }
                    result.append(store_produts)

        return result, total

    @staticmethod
    @db_commit
    def get_invalid_order(bizdt, partner_id, user_id, store_ids=None):
        try:
            # 获取 订货日是今天及之前，非审核/已作废状态的订货订单状态变更为“已作废”
            q = DBSession.query(Supply_demand).filter(
                Supply_demand.demand_date < bizdt).filter(
                Supply_demand.demand_date >= bizdt - timedelta(days=1)).filter(
                Supply_demand.status.notin_(['APPROVED', 'CANCELLED'])).filter(
                Supply_demand.type == 'SD').filter(
                Supply_demand.partner_id == int(partner_id))
            if store_ids:
                q.query(Supply_demand.receive_by.in_(store_ids))
            unapproved_demand_list = q.all()
            if not unapproved_demand_list:
                return True
            for unapproved_demand in unapproved_demand_list:
                message = {}
                message['partner_id'] = partner_id
                message['user_id'] = user_id
                message['demand_id'] = unapproved_demand.id
                if unapproved_demand.status != Demand_enum.SUBMITTED.code:
                    logger.info("开始作废需要处理的订单id:{}".format(unapproved_demand.id))
                    # public(MessageTopic.CANCEL_INVALID_ORDER_TOPIC, message)
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.CANCEL_INVALID_ORDER_TOPIC,
                                        message=message)
                else:
                    # public(MessageTopic.AUTO_APPROVED_ORDER_TOPIC, message)
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.AUTO_APPROVED_ORDER_TOPIC,
                                        message=message)
        except Exception as e:
            import traceback
            logger.error("获取订单出错,  msg:{}".format(traceback.format_exc()))
            DBSession.rollback()

    @staticmethod
    @db_commit
    def invalidate_order(demand_id, partner_id, user_id):
        try:
            # 作废订单
            demand_order_db = Supply_demand.get(demand_id)
            if not demand_order_db:
                raise DataValidationException('No result found!')

            args = {
                'status': 'CANCELLED',
                'process_status': 'SUCCESS',
                'updated_at': datetime.utcnow(),
                'updated_by': user_id
            }
            demand_order_db.update(**args)

            return True

        except Exception as e:
            import traceback
            logger.error("作废订单出错, demand_id:{}, msg:{}".format(demand_id, traceback.format_exc()))
            DBSession.rollback()

    ######总部帮订导入功能开始 坚决不用反人类的@staticmethod！！！
    #######################

    # 1.1）总部分配读取文件获取data 坚决不用反人类的@staticmethod！！！
    def read_excel_master_help_upload_row_to_data_of_ST(self, row):
        get_header_map = {
            u'门店编号': 'store_code',
            u'门店名称': 'store_name',
            u'商品编号': 'product_code',
            u'商品名称': 'product_name',
            u'数量': 'quantity',
            u'订货日期': 'start_date',
            u'到货日期': 'end_date',
            u'备注': 'remark'
        }
        data = {}
        for header, key in get_header_map.items():
            data[key] = row.get(header)
        return data

    # 1.2）仓库分配读取文件获取data
    def read_excel_master_help_upload_row_to_data_of_WH(self, row):
        get_header_map = {
            u'门店编号': 'store_code',
            u'门店名称': 'store_name',
            u'商品编号': 'product_code',
            u'商品名称': 'product_name',
            u'数量': 'quantity',
            u'订货日期': 'start_date',
            u'到货日期': 'end_date',
            u'配送中心编号': 'centre_code',
            u'备注': 'remark'
        }
        data = {}
        for header, key in get_header_map.items():
            data[key] = row.get(header)
        return data

    # 2）读取文件row
    @time_cost
    def read_file(self, file_obj, filename, file_type):
        extension = filename.split('.')[-1]
        content = file_obj
        if extension.upper() == 'XLS':
            sheet = pyexcel.get_sheet(file_type='xls', file_content=content)
        elif extension.upper() == 'XLSX':
            sheet = pyexcel.get_sheet(file_type='xlsx', file_content=content)
        else:
            raise MasterUploadException('文件格式需xls或xlsx！')
        sheet.name_columns_by_row(0)
        headers = list(sheet.colnames)
        if file_type == 'WH':
            expect_headers = [u'门店编号',
                              u'门店名称',
                              u'商品编号',
                              u'商品名称',
                              u'数量',
                              u'订货日期',
                              u'到货日期',
                              u'配送中心编号',
                              u'备注']
        elif file_type == 'ST':
            expect_headers = [u'门店编号',
                              u'门店名称',
                              u'商品编号',
                              u'商品名称',
                              u'数量',
                              u'订货日期',
                              u'到货日期',
                              u'备注']
        else:
            expect_headers = []
        if set(headers) != set(expect_headers):
            raise MasterUploadException('表格表头错误！')
        rows = sheet.to_records()
        if sheet.number_of_rows() > 5000:
            raise MasterUploadException('文件过大，请导入5000行以内的数据！')
        data_list = []
        for row in rows:
            if file_type == 'WH':
                data = self.read_excel_master_help_upload_row_to_data_of_WH(row)
            elif file_type == 'ST':
                data = self.read_excel_master_help_upload_row_to_data_of_ST(row)
            else:
                data = {'store_code': '', 'product_code': ''}
            if data['store_code'] == '' and data['product_code'] == '':
                pass
            else:
                data_list.append(data)
        if len(data_list) == 0:
            raise MasterUploadException('文件为空！')
        return data_list

    # 3）获取合法商品id
    def get_valid_product_map(self, data_list, partner_id, user_id):
        code_list = list(set([str(d['product_code']).strip() for d in data_list]))
        product_info_list = metadata_service.get_product_list(filters={"status__eq": "ENABLED",
                                                                       'code__in': code_list},
                                                              return_fields='id,code,name',
                                                              partner_id=partner_id,
                                                              user_id=user_id).get('rows', [])
        product_id_map = {}
        if len(product_info_list) > 0:
            for row in product_info_list:
                product_id_map[row['code']] = [int(row['id']), row.get('name')]
        return product_id_map

    # 4）获取合法门店id
    def get_valid_store_map(self, data_list, partner_id, user_id):
        code_list = list(set([str(d['store_code']).strip() for d in data_list]))
        store_info_list = metadata_service.get_store_list(filters={"status__eq": "ENABLED",
                                                                   'code__in': code_list},
                                                          return_fields='id,code,name,type',
                                                          partner_id=partner_id,
                                                          user_id=user_id).get('rows', [])
        store_id_map = {}
        if len(store_info_list) > 0:
            for row in store_info_list:
                store_id_map[row['code']] = [int(row['id']), row.get('name'), row.get('type')]
        return store_id_map

    # 5) 获取仓库主配配送中心
    def get_valid_centre_map(self, data_list, partner_id, user_id):
        code_list = list(set([str(d['centre_code']).strip() for d in data_list]))
        centre_info_list = metadata_service.get_distribution_center_list(filters={"status__eq": "ENABLED",
                                                                                  'code__in': code_list},
                                                                         return_fields='id,code,name',
                                                                         partner_id=partner_id,
                                                                         user_id=user_id).get('rows', [])
        centre_id_map = {}
        if len(centre_info_list) > 0:
            for row in centre_info_list:
                centre_id_map[row['code']] = [int(row['id']), row.get('name')]
        return centre_id_map

    # 6）获取门店订货区域商品map
    @time_cost
    def get_valid_product_of_store_map(self, store_id_map, product_id_map, data_list,
                                       partner_id, user_id):
        product_of_store_map = {}
        store_ids = []
        for data in data_list:
            store_id = store_id_map.get(str(data['store_code']).strip(), [0])[0]
            product_id = product_id_map.get(str(data['product_code']).strip(), [0])[0]
            if not store_id:
                continue
            if not product_id:
                continue
            if store_id not in product_of_store_map:
                product_of_store_map[store_id] = []
            if product_id not in product_of_store_map[store_id]:
                product_of_store_map[store_id].append(product_id)
            if store_id not in store_ids:
                store_ids.append(store_id)
        for store_id in store_ids:
            product_ids = product_of_store_map[store_id]
            product_distribution_ret = metadata_service.get_list_valid_product_for_distr_by_id(
                store_id,
                product_ids=product_ids,
                filter_type=1,
                partner_id=partner_id, user_id=user_id).get("rows", [])
            # product_purchase_ret = metadata_service.get_purchase_product_by_store_id(
            #     store_id,
            #     product_ids=product_ids,
            #     return_fields='id,allow_main_order',
            #     partner_id=partner_id, user_id=user_id).get("rows", [])
            product_of_store_map[store_id] = {}
            for p_d in product_distribution_ret:
                product_of_store_map[store_id][int(p_d['product_id'])] = p_d
            # for p_p in product_distribution_ret:
            #     if not product_of_store_map[store_id].get(int(p_p['product_id'])):
            #         product_of_store_map[store_id][int(p_p['product_id'])] = p_p
        return product_of_store_map

    # 7）检查门店分部
    def get_valid_check_store_division_map(self, store_id_map, product_id_map, data_list,
                                           partner_id, user_id):
        store_division_map = {}
        store_ids = []
        product_id_ids = []
        for data in data_list:
            store_id = store_id_map.get(str(data['store_code']).strip(), [0])[0]
            product_id = product_id_map.get(str(data['product_code']).strip(), [0])[0]
            if not store_id:
                continue
            if not product_id:
                continue
            if store_id not in store_division_map:
                store_division_map[store_id] = []
            if product_id not in product_id_ids:
                product_id_ids.append(product_id)
            if store_id not in store_ids:
                store_ids.append(store_id)
        for store_id in store_ids:
            filters = {"store_id__in": [str(store_id)]}
            list_store_division_ret = metadata_service.list_store_division(
                filters=filters, return_fields='product_list',
                partner_id=partner_id, user_id=user_id)
            product_id_list = store_division_map.get(store_id)
            for p_id in list_store_division_ret:
                if int(p_id) in product_id_ids:
                    product_id_list.append(int(p_id))
        return store_division_map

    # 8）用合法数据重构data_list
    def get_rest_data_list(self, data_list, store_id_map, product_id_map, product_of_store_map,
                           file_type, centre_id_map=None, partner_id=None, user_id=None):
        has_invalid_row = False
        # 重复数据也要给提示
        map_key_dict = {}
        for index, data in enumerate(data_list):
            row_num = index + 1
            data['row_num'] = row_num
            store_code = str(data.get('store_code')).strip()
            product_code = str(data.get('product_code')).strip()
            centre_code = ''
            if file_type == 'WH':
                centre_code = str(data.get('centre_code')).strip()
                data['centre_code'] = centre_code
            data['store_code'] = store_code
            data['product_code'] = product_code
            data['quantity'] = float(data.get('quantity')) if data.get('quantity') else 0
            if store_code not in store_id_map:
                data['error'] = u"找不到对应门店或该门店已关闭:{}".format(store_code)
                has_invalid_row = True
                continue
            elif product_code not in product_id_map:
                data['error'] = u"找不到对应商品或已停用:{}".format(product_code)
                has_invalid_row = True
                continue
            elif file_type == 'WH' and centre_code not in centre_id_map:
                data['error'] = u"找不到对应配送中心或已停用:{}".format(centre_code)
                has_invalid_row = True
                continue
            elif data['quantity'] <= 0:
                data['error'] = u"订货商品数量不能小于等于0"
                has_invalid_row = True
                continue
            else:
                today = datetime(datetime.now().year, datetime.now().month, datetime.now().day)
                ### 获取门店时区
                store_id = store_id_map.get(store_code)
                tz = metadata_service.list_entity(schema_name='store', ids=[store_id[0]], return_fields='tz',
                                                  partner_id=partner_id, user_id=user_id)['rows'][0]['fields'].get('tz',
                                                                                                                   "+00:00")[
                     :3]
                today_ = today + timedelta(hours=int(tz))
                try:
                    start_date_ = str(data.get('start_date')).strip()
                    start_date = datetime.strptime(start_date_, '%Y%m%d')
                    bijiao_start_date = start_date
                    start_date = start_date + timedelta(
                        hours=-int(tz))

                    end_date_ = str(data.get('end_date')).strip()

                    end_date = datetime.strptime(end_date_, '%Y%m%d')

                    end_date = end_date + timedelta(
                        hours=-int(tz))
                except Exception:
                    data['error'] = u"订货、到货日期没有或格式不合法，请遵照格式如：20190621"
                    has_invalid_row = True
                    continue
                if start_date > end_date:
                    data['error'] = u"到货日期不能小于订货日期"
                    has_invalid_row = True
                    continue
                bijiao_today = datetime(today_.year, today_.month, today_.day)
                if bijiao_start_date < bijiao_today:
                    data['error'] = u"订货日期{}不能小于今天日期{}".format(bijiao_start_date, bijiao_today)
                    has_invalid_row = True
                    continue
                store_id = store_id_map.get(store_code)[0]
                store_name = store_id_map.get(store_code)[1]
                product_id = product_id_map.get(product_code)[0]
                product_name = product_id_map.get(product_code)[1]
                product_of_store_info = product_of_store_map.get(store_id)
                product_of_store = product_of_store_info.get(product_id)
                if not product_of_store:
                    data['error'] = u"此商品:{}不在门店:{}订货规则".format(product_name, store_name)
                    data['product_name'] = product_name
                    data['store_name'] = store_name
                    if file_type == 'WH':
                        data['centre_name'] = centre_id_map.get(centre_code)[1]
                    has_invalid_row = True
                    continue
                else:
                    # if not product_of_store.get('allow_main_order'):
                    #     data['error'] = u"此商品:{},不允许主配".format(product_name)
                    #     data['product_name'] = product_name
                    #     data['store_name'] = store_name
                    #     if file_type == 'WH':
                    #         data['centre_name'] = centre_id_map.get(centre_code)[1]
                    #     has_invalid_row = True
                    #     continue
                    # units = product_of_store.get('units', [{}])
                    has_order = False
                    if product_of_store.get("order_unit_id"):
                        has_order = True
                    if not has_order:
                        data['error'] = u"此商品:{},未设置订货单位".format(product_name)
                        has_invalid_row = True
                        data['product_name'] = product_name
                        data['store_name'] = store_name
                        if file_type == 'WH':
                            data['centre_name'] = centre_id_map.get(centre_code)[1]
                        continue
                if file_type == 'WH':
                    # 仓库主配
                    map_key = str(store_id) + str(start_date) + str(end_date) + str(product_id) + str(
                        centre_id_map.get(centre_code)[0])
                elif file_type == 'ST':
                    # 总部分配
                    map_key = str(store_id) + str(start_date) + str(end_date) + str(product_id)
                else:
                    map_key = ''
                    raise MasterUploadException('不支持的导入类型！')
                if map_key in map_key_dict:
                    has_invalid_row = True
                    data['product_name'] = product_name
                    data['store_name'] = store_name
                    if file_type == 'WH':
                        data['centre_name'] = centre_id_map.get(centre_code)[1]
                        data['error'] = u"仓库主配门店、商品、订货日、到货日、配送中心不能完全相同" \
                                        u"。\n导入第{}行与第{}行重复，请删除一行！".format(row_num + 1, map_key_dict[map_key])
                    elif file_type == 'ST':
                        data['error'] = u"总部分配门店、商品、订货日、到货日不能完全相同" \
                                        u"。\n导入第{}行与第{}行重复，请删除一行！".format(row_num + 1, map_key_dict[map_key])
                    continue
                else:
                    map_key_dict[map_key] = row_num
                data['store_id'] = store_id
                data['product_id'] = product_id
                data['store_name'] = store_name
                data['store_type'] = store_id_map.get(store_code)[2]
                data['product_name'] = product_name
                data['start_date'] = start_date
                data['end_date'] = end_date
                if file_type == 'WH':
                    data['centre_id'] = centre_id_map.get(centre_code)[0]
                    data['centre_name'] = centre_id_map.get(centre_code)[1]
        return data_list, has_invalid_row

    ##主流程导入函数
    @time_cost
    def upload_demand_master(self, request, partner_id, user_id):
        ret = {}
        file = base64.b64decode(request.file)
        filename = request.file_name
        temp = tempfile.TemporaryFile()
        temp.write(file)
        temp.seek(0)
        s1 = temp.read()
        file_type = request.file_type
        data_list = self.read_file(s1, filename, file_type)
        store_id_map = self.get_valid_store_map(data_list, partner_id, user_id)
        product_id_map = self.get_valid_product_map(data_list, partner_id, user_id)
        centre_id_map = {}
        if file_type == 'WH':
            centre_id_map = self.get_valid_centre_map(data_list, partner_id, user_id)
        product_of_store_map = self.get_valid_product_of_store_map(store_id_map, product_id_map, data_list,
                                                                   partner_id, user_id)
        check_data_list, has_invalid_row = self.get_rest_data_list(data_list, store_id_map, product_id_map,
                                                                   product_of_store_map, file_type,
                                                                   centre_id_map=centre_id_map, partner_id=partner_id,
                                                                   user_id=user_id)
        temp.close()
        # 有错误直接返回
        if len(data_list) <= 0:
            return ret
        if has_invalid_row:
            ret['result'] = has_invalid_row
            ret['rows'] = check_data_list
        # 无错误则存储所有导入数据
        else:
            # for r in check_data_list:
            #     r['start_date'] = str(r['start_date'])
            #     r['end_date'] = str(r['end_date'])
            # ret['result'] = has_invalid_row
            # ret['rows'] = check_data_list
            # return ret
            batch = {
                "file_name": str(filename),
                "status": 'INIT',
                "file_type": file_type,
                'id': get_guid()
            }
            for c in check_data_list:
                c['id'] = get_guid()
                c['batch_id'] = batch['id']
                c['status'] = 'INIT'
                c['partner_id'] = partner_id
            demand_master_repository.save_valid_data_for_demand_master(batch=batch, detail_list=check_data_list,
                                                                       partner_id=partner_id, user_id=user_id)

        for r in check_data_list:
            r['start_date'] = str(r['start_date'])
            r['end_date'] = str(r['end_date'])
        ret['result'] = has_invalid_row
        ret['rows'] = check_data_list
        ret['file_name'] = filename
        return ret

    ##获取导入详情
    def get_demand_master_upload_by_batch_id(self, request, partner_id):
        batch_id = request.batch_id
        offset = request.offset
        limit = request.limit
        include_total = request.include_total
        ret = {}
        batch = demand_master_repository.get_demand_master_upload_batch_by_id(batch_id)
        details = demand_master_repository.get_demand_master_upload_by_batch_id(batch_id, offset=offset,
                                                                                limit=limit,
                                                                                include_total=include_total,
                                                                                partner_id=partner_id)
        if isinstance(details, tuple):
            ret['total'], ret['rows'] = details
        else:
            ret['rows'] = details
        ret['file_name'] = batch.file_name
        ret['status'] = batch.status
        ret['updated_name'] = batch.updated_name
        return ret

    ##获取导入记录
    def get_demand_master_upload(self, request, partner_id, user_id):
        start_date = request.start_date
        end_date = request.end_date
        offset = request.offset
        limit = request.limit
        file_name = request.file_name
        file_type = request.file_type
        include_total = request.include_total
        ret = {}
        details = demand_master_repository.get_demand_master_upload(start_date, end_date,
                                                                    file_name=file_name,
                                                                    file_type=file_type,
                                                                    offset=offset,
                                                                    limit=limit,
                                                                    include_total=include_total,
                                                                    partner_id=partner_id,
                                                                    user_id=user_id
                                                                    )
        if isinstance(details, tuple):
            ret['total'], ret['rows'] = details
        else:
            ret['rows'] = details
        return ret

    # 校验完毕数据，保证用合法数据开始干活
    @time_cost
    def approve_demand_master_upload_by_batch_id(self, request, partner_id, user_id):
        ret = {}
        batch_id = request.batch_id
        batch_obj = demand_master_repository.get_demand_master_upload_batch_by_id(batch_id)
        unit_dict = metadata_service.get_unit_map(partner_id=partner_id, user_id=user_id)
        if batch_obj.status == 'INIT' or batch_obj.status == 'FAIL':
            # 1、进行拆单(必须保证excel校验无误基础上)
            # 1）获取详细 INIT
            # 非异步实现，直接一把干完
            demand_master_repository.update_demand_master_upload_batch_status(batch_id, status='PROCESS')
            # try:
            detail_objs = demand_master_repository.get_demand_master_upload_detail(
                batch_id=batch_id, status='INIT', partner_id=partner_id
            )
            valid_store_demand_data_product_dict = {}
            demand_more_info_dict = {}
            for obj in detail_objs:
                demand_date = str(obj.start_date) + '/' + str(obj.end_date)
                if batch_obj.file_type == 'WH':
                    # 仓库主配
                    map_key = str(obj.store_id) + demand_date + str(obj.centre_id)
                elif batch_obj.file_type == 'ST':
                    map_key = str(obj.store_id) + demand_date
                else:
                    map_key = ''
                    raise MasterUploadException('不支持的导入类型！')
                # 需要关联导入数据，记录更详细信息
                if map_key not in demand_more_info_dict:
                    info = dict(
                        store_code=obj.store_code,
                        store_name=obj.store_name,
                        store_type=obj.store_type,
                        row_nums=[obj.id]
                    )
                    demand_more_info_dict[map_key] = info
                else:
                    demand_more_info_dict[map_key]['row_nums'].append(obj.id)
                # 拆单
                doc_info = dict(
                    store_id=obj.store_id,
                    demand_date=demand_date,
                    product_id=obj.product_id,
                    quantity=obj.quantity,
                    centre_id=obj.centre_id,
                    remark=obj.remark
                )
                if map_key not in valid_store_demand_data_product_dict:
                    valid_store_demand_data_product_dict[map_key] = [doc_info]
                else:
                    valid_store_demand_data_product_dict[map_key].append(doc_info)
            # 总部分配导入拆单结束
            print('拆单结束得到demand_data_product_dict', valid_store_demand_data_product_dict)
            # 获取商品配送属性"WAREHOUSE"仓库配送
            username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            # username = None
            supply_demand_list = []
            supply_demand_product_list = []
            for map_key, doc_info_list in valid_store_demand_data_product_dict.items():
                demand_date = doc_info_list[0]['demand_date']
                store_id = doc_info_list[0]['store_id']

                ### 获取门店时区
                tz = metadata_service.list_entity(schema_name='store', ids=[store_id], return_fields='tz',
                                                  partner_id=partner_id, user_id=user_id)['rows'][0]['fields'].get('tz',
                                                                                                                   "+00:00")[
                     :3]

                start_date = datetime.strptime(demand_date.split('/')[0], '%Y-%m-%d %H:%M:%S')
                end_date = datetime.strptime(demand_date.split('/')[1], '%Y-%m-%d %H:%M:%S')
                more_info = demand_more_info_dict[map_key]
                supply_demand = dict(
                    id=gen_snowflake_id(),
                    code=Supply_doc_code.get_code_by_type(Demand_bill_code.STORE_DO.code, partner_id,
                                                          user_id),
                    store_secondary_id=more_info['store_code'], store_type=more_info['store_type'],
                    type=Demand_type.MD.code,
                    receive_by=store_id, receive_name=more_info['store_name'],
                    demand_date=start_date,
                    partner_id=partner_id, created_by=user_id, updated_by=user_id,
                    # 帮订备注
                    sub_type=Demand_sub_type.MASTER.code,
                    status=Demand_enum.INITED.code, process_status=Demand_enum.INITED.code,
                    remark='总部导入', arrival_date=end_date,
                    has_product=1, created_name=username, updated_name=username,
                )
                # 记录导入数据和订单关联
                more_info['master_id'] = supply_demand['id']
                more_info['master_code'] = supply_demand['code']
                supply_demand_list.append(supply_demand)
                # 1,一把写入订单，和商品，全部用批量插入事务操作
                # 2,更新订单和导入数据关系字段
                # 循环商品
                product_ids = [doc['product_id'] for doc in doc_info_list]

                product_dict = dict()
                product_list = metadata_service.get_product_list(ids=product_ids,
                                                                 filters={"status": "ENABLED", "allow_order": True},
                                                                 return_fields="id,code,name",
                                                                 partner_id=partner_id,
                                                                 user_id=user_id).get('rows', [])
                for c in product_list:
                    product_dict[int(c.get('id'))] = c

                product_distribution_ret = metadata_service.get_list_valid_product_for_distr_by_id(
                    store_id,
                    product_ids=product_ids,
                    filter_type=1,
                    partner_id=partner_id, user_id=user_id).get("rows", [])
                # product_purchase_ret = metadata_service.get_purchase_product_by_store_id(
                #     store_id,
                #     product_ids=product_ids,
                #     include_product_fields=["name", "code", "sale_type", "model_name", "model_code",
                #                             "storage_type",
                #                             "product_type", "category"],
                #     partner_id=partner_id, user_id=user_id).get("rows", [])

                product_info_dict = {}
                if len(product_distribution_ret) > 0:
                    for p_p in product_distribution_ret:
                        p_p["distr_type"] = p_p.get("logistic_mode", "")
                        product_info_dict[int(p_p['product_id'])] = p_p
                # if len(product_distribution_ret) > 0:
                #     for p_d in product_distribution_ret:
                #         if not product_info_dict.get(int(p_d['product_id'])):
                #             product_info_dict[int(p_d['product_id'])] = p_d

                for doc_info in doc_info_list:
                    #### 取到备注就写入订货单
                    if doc_info.get('remark') and doc_info.get('remark') != 'None':
                        supply_demand['remark'] = doc_info.get('remark')
                    #### 取到备注就写入订货单
                    product = product_info_dict[doc_info['product_id']]
                    # 2022-02-17 总部主配导入不受最大最小订量规则限制
                    quanity = doc_info.get('quantity', 0)
                    # quanity = get_quantity(doc_info['quantity'], float(product.get("max_number", 0)),
                    #                        float(product.get("min_number", 0)),
                    #                        float(product.get("increment_number", 0)))
                    order_unit = unit_dict.get(str(product.get("order_unit_id", 0)), {})
                    arrival_days = 0
                    if product.get("main_order_arrival_day"):
                        arrival_days = product.get("main_order_arrival_day")
                    # units = product.get('units')
                    unit_rate = 1
                    unit_rate = float(product.get("order_unit_rate", 1))
                    product_info = product_dict.get(int(doc_info['product_id']))
                    if not product_info:
                        continue

                    temp_distribution_type = product.get("logistic_mode")
                    demand_product = dict(
                        demand_id=supply_demand['id'],
                        product_id=doc_info['product_id'],
                        product_code=product_info.get("code"),
                        product_name=product_info.get("name"),
                        product_category_id=int(product.get('product_category_id', 0)),
                        increment_quantity=float(product.get("incr_qty", 0)),
                        max_quantity=float(product.get("max_qty", 0)),
                        min_quantity=float(product.get("min_qty", 0)),
                        arrival_days=arrival_days,
                        distribution_circle=product.get('circle_type'),
                        sale_type=product.get('sale_type'),
                        product_type=product.get('product_type'),
                        unit_id=int(product['order_unit_id']),
                        unit_spec=product.get("order_unit_code", "无"),
                        unit_name=product.get('order_unit_name', "无"),
                        unit_rate=unit_rate,
                        spec=product.get('spec', "无"),
                        quantity=float(quanity),
                        storage_type=product.get("storage_type"),
                    )
                    if temp_distribution_type == Demand_type.NMD.code:
                        demand_product['distribute_by'] = int(product.get("distribution_center_id", 0))
                        demand_product['distribution_type'] = Demand_type.NMD.code
                    elif temp_distribution_type == 'PAD':
                        demand_product['distribute_by'] = int(product.get("distribution_center_id", 0))
                        demand_product['distribution_type'] = "PAD"
                    elif temp_distribution_type == Demand_type.PUR.code:
                        demand_product['distribute_by'] = int(product.get("distribution_center_id", 0))
                        demand_product['distribution_type'] = Demand_type.PUR.code
                        demand_product['purchase_price'] = float(product.get("purchase_price", 0))
                        demand_product['purchase_tax'] = float(product.get('purchase_tax', 0))
                    elif temp_distribution_type == 'BREAD':
                        demand_product['distribute_by'] = int(product.get("distribution_center_id", 0))
                        demand_product['distribution_type'] = 'BREAD'
                    # 仓库导入增加配送中心
                    if batch_obj.file_type == 'WH':
                        demand_product['distribute_by'] = doc_info['distribution_center_id']
                    supply_demand_product_list.append(demand_product)
            # print('拆单结束：结果', supply_demand_list)
            # print('拆单结束：结果', supply_demand_product_list)
            # 最后一把写入数据库
            demand_master_repository.insert_demand_master_detail_and_product(supply_demand_list,
                                                                             supply_demand_product_list)
            # 更新导入数据和单据关联
            # print('拆单结束：结果', demand_more_info_dict)
            demand_master_upload_list = []
            demand_master_upload_batch = [{
                'id': batch_id,
                'status': 'SUCCESS',
                'updated_name': username,
            }]
            for _, more_info in demand_more_info_dict.items():
                for id in more_info['row_nums']:
                    demand_master_upload = {}
                    demand_master_upload['id'] = id
                    demand_master_upload['status'] = 'SUCCESS'
                    demand_master_upload['master_id'] = more_info['master_id']
                    demand_master_upload['master_code'] = more_info['master_code']
                    demand_master_upload_list.append(demand_master_upload)
            demand_master_repository.update_demand_master_upload_detail_more_info(demand_master_upload_batch,
                                                                                  demand_master_upload_list)
            ret['result'] = True
            # except Exception as e:
            #     logger.error('总部分配导入拆单batch_id:{}。失败:{}'.format(batch_id, str(e)))
            #     demand_master_repository.update_demand_master_upload_batch_status(batch_id, status='FAIL',
            #                                                                       partner_id=partner_id,
            #                                                                       user_id=user_id)
        else:
            ret['result'] = False
        return ret

    ##校验完毕数据,取消该次导入
    def cancel_demand_master_upload_by_batch_id(self, request, partner_id, user_id):
        ret = {}
        batch_id = request.batch_id
        demand_master_repository.update_demand_master_upload_batch_status(batch_id, status='CANCEL',
                                                                          partner_id=partner_id, user_id=user_id)
        ret['result'] = True
        return ret

    #######################
    ######总部帮订导入功能结束

    #######################
    ######ForReceiptStart
    # 查询receipt订货单
    def list_orders(self, start_arrival_date=None, end_arrival_date=None, start_date=None, end_date=None,
                    store_ids=None, demand_code=None, order_code=None, demand_type=None, storage_type=None,
                    store_type=None, trans_mode=None, offset=None, limit=None, is_received=None,
                    jde_order_id=None, distribution_type=None, distribute_bys=None, is_adjust=None,
                    havi_code=None, order=None, sort=None, partner_id=None, user_id=None, status=None):
        entity = receipt_service.list_orders(start_arrival_date=start_arrival_date, end_arrival_date=end_arrival_date,
                                             start_date=start_date, end_date=end_date, store_ids=store_ids,
                                             demand_code=demand_code, order_code=order_code,
                                             demand_type=demand_type, storage_type=storage_type, store_type=store_type,
                                             trans_mode=trans_mode, offset=offset, limit=limit,
                                             is_received=is_received, jde_order_id=jde_order_id,
                                             distribution_type=distribution_type, distribute_bys=distribute_bys,
                                             is_adjust=is_adjust, havi_code=havi_code,
                                             order=order, sort=sort, partner_id=partner_id, user_id=user_id,
                                             status=status)
        total = entity.total
        rows = entity.rows
        trans_rows = []
        for row in rows:
            trans_row = {
                'id': row.id,
                'code': row.code,
                'type': row.demand_type,
                'receive_by': row.receive_by,
                'distribute_by': row.delivery_by,
                "distribution_type": row.distr_type,
                "order_date": row.demand_date,
                "arrival_date": row.arrival_date,
                "partner_id": row.partner_id,
                "demand_id": row.batch_id,
                "demand_code": row.batch_code,
                "storage_type": row.storage_type,
                "status": row.status,
                "process_status": row.status,
                "updated_by": row.updated_by,
                "updated_at": row.updated_at,
                "created_at": row.created_at,
                "created_by": row.created_by,
                "created_name": row.created_name,
                "updated_name": row.updated_name,
                "is_adjust": True if row.batch_type == 'DEMAND_ADJUST' or row.batch_type == 'CHECKING_ADJUST' else False
            }
            trans_rows.append(trans_row)
        return total, trans_rows

    # 查询receipt订货单详情
    def get_order_by_id(self, order_id, partner_id=None, user_id=None):
        row = receipt_service.get_orders_by_id(order_id, partner_id, user_id)

        trans_entity = {
            'id': row.id,
            'code': row.code,
            'type': row.demand_type,
            'receive_by': row.receive_by,
            'distribute_by': row.delivery_by,
            "distribution_type": row.distr_type,
            "order_date": row.demand_date,
            "arrival_date": row.arrival_date,
            "partner_id": row.partner_id,
            "demand_id": row.batch_id,
            "demand_code": row.batch_code,
            "storage_type": row.storage_type,
            "status": row.status,
            "process_status": row.status,
            "updated_by": row.updated_by,
            "updated_at": row.updated_at,
            "created_at": row.created_at,
            "created_by": row.created_by,
            "created_name": row.created_name,
            "updated_name": row.updated_name,
            "is_adjust": True if row.batch_type == 'DEMAND_ADJUST' or row.batch_type == 'CHECKING_ADJUST' else False
        }
        return trans_entity

    # 查询receipt订货单商品
    def get_order_products_by_order_id(self, order_id, partner_id=None, user_id=None):
        entity = receipt_service.get_order_products_by_order_id(order_id, partner_id=partner_id, user_id=user_id)
        total = entity.total
        rows = entity.rows
        trans_rows = []
        for row in rows:
            trans_row = {
                'id': row.id,
                'demand_order_id': row.order_id,
                'product_id': row.product_id,
                'product_code': row.product_code,
                'product_name': row.product_name,
                # 'line':
                'sale_type': row.sale_type,
                # 'product_type': ,
                'unit_id': row.unit_id,
                'unit_name': row.unit_name,
                'unit_code': row.unit_spec,
                'quantity': row.order_quantity,
                'distr_quantity': row.delivery_quantity
                # 'unit_rate': 1,
                # 'order_date': row.d,
            }
            trans_rows.append(trans_row)
        return total, trans_rows

    #######################
    ######ForReceiptEnd

    #######################
    ######demand的一些方法

    # 获得可订货的商品
    def get_demand_adjust_product(self, store_id, partner_id, user_id, distr_type=None, type=Demand_type.SD.code,
                                  product_ids=None, order_date=None, vendor_id=None, category_ids=None,
                                  limit=None, offset=None):
        """
        获得可订货商品（门市订货单都是提前生成，所以前端不会查门市可订货商品）
        type:门店订货(SD)、紧急订货(HD)、主配订货(MD)
        distr_type:物流模式: 配送:NMD, 直送:PUR, 不传查询所有
        vendor_id:配送的配送中心/直送的供应商，默认可不传
        """
        if vendor_id:
            filters = {"distribution_center_id": str(vendor_id)}
        else:
            filters = {}

        if category_ids:
            product_relation_filters = {"product_category": [str(id) for id in category_ids]}
        else:
            product_relation_filters = {}

        # 2021-06-02 商品属性&配方属性业务影响：只对配方属性=现做bom的无法订货和主配做限制
        product_filters = {"status__eq": "ENABLED", "bom_type__neq": "MANUFACTURE"}
        # product_filters = {"status__eq": "ENABLED",
        #                    "__or": [{"bom_type__neq": "MANUFACTURE",
        #                              "sale_type__in": ["NEW-RETAIL-SHORT-TERM", "NEW-RETAIL-NORMAL",
        #                                                "SOUVENIR"]},
        #                             {"product_type__neq": "FINISHED"}]}

        product_dict = dict()
        product_list = metadata_service.get_product_list(ids=product_ids,
                                                         return_fields="id,code,name,image_url",
                                                         filters={"status": "ENABLED", "allow_order": True},
                                                         partner_id=partner_id,
                                                         user_id=user_id).get('rows', [])
        item_codes = []
        for c in product_list:
            product_dict[int(c.get('id'))] = c
            item_codes.append(c.get('code'))
        price_dict = dict()
        price_list = metadata_service.get_product_price_by_store_id(store_id=store_id,
                                                                    item_codes=item_codes,
                                                                    partner_id=partner_id,
                                                                    user_id=user_id).get('rows', [])
        for c in price_list:
            price_dict[c.get('item_code')] = [c['price'], c.get('currency','')]

        ret = metadata_service.get_list_valid_product_for_distr_by_id(store_id, product_ids=product_ids, filter_type=2,
                                                                      include_product_fields='name,code,model_name,model_code,storage_type,category,product_type,sale_type',
                                                                      distr_type=distr_type, filters=filters,
                                                                      limit=limit, offset=offset,
                                                                      product_relation_filters=product_relation_filters,
                                                                      product_filters=product_filters,
                                                                      partner_id=partner_id, user_id=user_id)

        # print('ret', ret)
        rows = ret.get('rows', [])
        total = int(ret.get("total", 0))
        result = []
        for i in rows:
            if i.get('product_type') == "FINISHED" and not i.get("sale_type", "").startswith("NEW-RETAIL"):
                logger.warning("成品非新零售绝对不能订，请维护好主档:" + str(i.get("product_id")))
                continue
            product_info = product_dict.get(int(i.get("product_id")))
            if not product_info:
                continue
            product_code = product_info.get("code")
            price = price_dict.get(product_code, ['', ''])[0]
            currency = price_dict.get(product_code, ['', ''])[1]
            image = product_info.get('extends', {}).get('image_url')
            temp_product = {
                'increment_quantity': float(i.get("incr_qty", 0)) if i.get("incr_qty") else 0.0,
                'max_quantity': float(i.get("max_qty", 0)) if i.get("max_qty") else 0.0,
                'min_quantity': float(i.get("min_qty", 0)) if i.get("min_qty") else 0.0,
                'product_category_id': int(i.get("product_category_id")) if i.get("product_category_id") else 0,
                'product_code': product_info.get("code"),
                'product_id': int(i.get("product_id")) if i.get("product_id") else 0,
                'product_name': product_info.get("name"),
                'spec': i.get("spec", "无"),
                'storage_type': i.get("storage_type"),
                'distribution_circle': i.get("circle_type"),
                'arrival_days': int(i.get("arrival_day", 0)),
                'sale_type': i.get("sale_type"),
                'product_type': i.get("product_type"),
                'distribution_center_id': int(i.get("distribution_center_id")) if i.get(
                    "distribution_center_id") else 0,
                'vendor_id': int(i.get("vendor_id")) if i.get("vendor_id") else 0,
                'units': [],
                'price': price,
                'image': image,
                'currency': currency
            }
            # 多单位
            # units = i.get('units', [])
            # for i in units:
            #     if i.get("order") == True:
            unit = {}
            unit['unit_id'] = int(i['order_unit_id'])
            unit['unit_code'] = i.get('order_unit_code')
            unit['unit_name'] = i.get('order_unit_name')
            unit['unit_rate'] = Decimal(i.get('order_unit_rate', 1))
            temp_product['units'].append(unit)
            # if len(temp_product['units']) < 1:
            #     logger.warning("没有设置订货单位, product_id:{}".format(i.get("product_id")))
            #     continue
            if i.get("logistic_mode") == Demand_type.NMD.code:
                # 配送区域的商品
                temp_product['distribute_by'] = int(i.get("distribution_center_id")) if i.get(
                    "distribution_center_id") else 0
                temp_product['distribution_type'] = Demand_type.NMD.code
                # 面包订货
                if i.get("extends"):
                    # 面包订货的商品
                    extends = i.get("extends")
                    if extends.get('logistic_mode') == 'BREAD':
                        temp_product['distribute_by'] = int(i.get("distribution_center_id")) if i.get(
                            "distribution_center_id") else 0
                        temp_product['distribution_type'] = 'BREAD'

            if i.get("logistic_mode") == Demand_type.PUR.code:
                # 采购区域的商品
                temp_product['distribute_by'] = int(i.get("vendor_id")) if i.get("vendor_id") else 0
                temp_product['distribution_type'] = Demand_type.PUR.code
                temp_product['purchase_price'] = float(i.get("purchase_price", 0))
                temp_product['purchase_tax'] = float(i.get('purchase_tax', 0))
            logging.info("adjust-distr_type-logistic_mode---{}{}".format(distr_type,
                                                                         temp_product.get('distribution_type',
                                                                                          "")))
            if distr_type != "":
                if distr_type != temp_product.get('distribution_type', ""):
                    continue
            result.append(temp_product)
        total = len(result)
        return result, total

    # 创建订货调整单在用的辅助转日期格式方法
    def get_datetime(self, value):
        timestamp = Timestamp()
        timestamp.seconds = value.seconds
        date = timestamp.ToDatetime()
        if date == datetime(1970, 1, 1):
            return None
        return date

    # 创建订货调整单
    def create_demand_adjust(self, request, partner_id, user_id):

        demand_date = self.get_datetime(request.demand_date)
        arrival_date = self.get_datetime(request.arrival_date)
        remark = request.remark
        # type = request.type
        # sub_type = request.sub_type
        store_id = request.store_id
        products = request.products
        supply_demand_list = []
        supply_demand_product_list = []
        # username = None
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        store_info = metadata_service.get_store(store_id=store_id,
                                                return_fields='id,code,name,type',
                                                partner_id=partner_id,
                                                user_id=user_id)
        if not store_info:
            raise StoreNotExist("门店未找到！")
        # 非预开店/已开店的门店不允许订货
        status_check = metadata_service.check_store_status(store_id, partner_id, user_id)
        if not status_check:
            raise DataValidationException('请检查门店的开店状态！')
        supply_demand_id = gen_snowflake_id()
        supply_demand_code = Supply_doc_code.get_code_by_type(Demand_bill_code.STORE_DO.code, partner_id, user_id)
        supply_demand = dict(
            id=supply_demand_id,
            code=supply_demand_code,
            store_secondary_id=store_info.get('code'), store_type=store_info.get('type'),
            type=Demand_type.AD.code,
            receive_by=store_id, receive_name=store_info.get('name'),
            demand_date=demand_date,
            partner_id=partner_id, created_by=user_id, updated_by=user_id,
            sub_type=Demand_sub_type.STORE.code,
            status=Demand_enum.INITED.code, process_status=Demand_enum.INITED.code,
            remark=remark, arrival_date=arrival_date,
            has_product=1, created_name=username, updated_name=username,
            # 调整单
            is_adjust=True
        )
        supply_demand_list.append(supply_demand)
        # 循环商品
        product_ids = [doc.product_id for doc in products]
        product_dict = dict()
        product_list = metadata_service.get_product_list(ids=product_ids,
                                                         return_fields="id,code,name,status,allow_order",
                                                         include_units=True,
                                                         filters={"status": "ENABLED", "allow_order": True},
                                                         partner_id=partner_id,
                                                         user_id=user_id).get('rows', [])
        for c in product_list:
            product_dict[int(c.get('id'))] = c
        # 采用优先采购原则 负值product.get("distr_type")
        product_distribution_ret = metadata_service.get_list_valid_product_for_distr_by_id(
            store_id,
            product_ids=product_ids,
            filter_type = 2,
            partner_id=partner_id, user_id=user_id).get("rows", [])
        product_info_dict = {}
        print('pp', product_distribution_ret)
        if len(product_distribution_ret) > 0:
            for p_d in product_distribution_ret:
                if not product_info_dict.get(int(p_d['product_id'])):
                    product_info_dict[int(p_d['product_id'])] = p_d
        print('product_info_dict', product_info_dict)

        # 商品类别
        category_ret = metadata_service.get_product_category_list(return_fields='id,code,name', partner_id=partner_id,
                                                                  user_id=user_id)
        category_map = {}
        if category_ret and category_ret.get('rows'):
            for c in category_ret.get('rows'):
                category_map[int(c.get('id', 0))] = {
                    'code': c.get('code'),
                    'name': c.get('name')
                }

        for doc_info in products:
            product = product_info_dict.get(doc_info.product_id)
            if not product:
                continue
            product_info = product_dict.get(int(doc_info.product_id), {})
            if not product_info:
                continue
            quanity = doc_info.quantity
            order_unit = metadata_service.get_unit(doc_info.unit_id, partner_id=partner_id,
                                                   user_id=user_id)
            arrival_days = 0
            if product.get("main_order_arrival_day"):
                arrival_days = product.get("main_order_arrival_day")
            units = product_info.get('units')
            unit_rate = 1
            accounting_unit_id = 0
            accounting_unit = {}
            if units:
                for u in units:
                    # 区域关联的单位是否存在且是否为可订货状态
                    if u.get("order") and str(order_unit['id']) == str(u.get('id')):
                        unit_rate = float(u.get("rate", 1))
                    if u.get("default"):
                        accounting_unit_id = int(u.get('id'))
                        print('accounting_unit_id', accounting_unit_id)
                        accounting_unit = metadata_service.get_unit(accounting_unit_id, partner_id=partner_id,
                                                                    user_id=user_id)
            distribution_type = product.get(
                "logistic_mode")
            temp_distribution_type = None
            if distribution_type == Demand_type.NMD.code:
                # 配送区域
                temp_distribution_type = Demand_type.NMD.code
            if distribution_type == 'PAD':
                # 配送区域
                temp_distribution_type = 'PAD'
            if distribution_type == 'BREAD':
                temp_distribution_type = 'BREAD'
            if distribution_type == Demand_type.PUR.code or not temp_distribution_type:
                # 采购区域
                temp_distribution_type = Demand_type.PUR.code
            # 调整单quanity存订货数量  accounting_quanity 存核算数量
            accounting_quantity = quanity * unit_rate
            demand_product = dict(
                demand_id=supply_demand['id'],
                product_id=doc_info.product_id,
                product_code=product_info.get("code"),
                product_name=product_info.get("name"),
                product_category_id=int(product.get('product_category_id', 0)),
                product_category_name=category_map.get(int(product.get('product_category_id', 0)), {}).get('name',
                                                                                                           '未分类'),
                increment_quantity=float(product.get("incr_qty", 0)),
                max_quantity=float(product.get("max_qty", 0)),
                min_quantity=float(product.get("min_qty", 0)),
                arrival_days=arrival_days,
                distribution_circle=product.get('circle_type'),
                sale_type=product.get('sale_type'),
                product_type=product.get('product_type'),
                spec=product.get('spec', "无"),
                quantity=float(quanity),
                storage_type=product.get("storage_type"),
                # 调整单(非核算单位)
                unit_id=int(order_unit['id']),
                unit_name=order_unit.get("name", "无"),
                unit_spec=order_unit.get('code', "无"),
                #####调整单核算单位
                accounting_unit_id=accounting_unit_id,
                accounting_unit_spec=accounting_unit.get('code', "无"),
                accounting_unit_name=accounting_unit.get("name", "无"),
                ### 核算数量
                accounting_quantity=float(accounting_quantity),
                unit_rate=unit_rate,
            )
            if temp_distribution_type == Demand_type.NMD.code:
                demand_product['distribute_by'] = int(product.get("distribution_center_id", 0))
                demand_product['distribution_type'] = Demand_type.NMD.code
            elif temp_distribution_type == 'BREAD':
                demand_product['distribute_by'] = int(product.get("distribution_center_id", 0))
                demand_product['distribution_type'] = 'BREAD'
                # 面包订货
                if product.get("extends"):
                    # 面包订货的商品
                    extends = product.get("extends")
                    if extends.get('distr_type') == 'BREAD':
                        demand_product['distribute_by'] = int(product.get("distribution_center_id")) if product.get(
                            "distribution_center_id") else 0
                        demand_product['distribution_type'] = 'BREAD'
            elif temp_distribution_type == 'PAD':
                demand_product['distribute_by'] = int(product.get("distribution_center_id", 0))
                demand_product['distribution_type'] = 'PAD'
            elif temp_distribution_type == Demand_type.PUR.code:
                demand_product['distribute_by'] = int(product.get("distribution_center_id", 0))
                demand_product['distribution_type'] = Demand_type.PUR.code
                demand_product['purchase_price'] = float(product.get("purchase_price", 0))
                demand_product['purchase_tax'] = float(product.get('purchase_tax', 0))
            supply_demand_product_list.append(demand_product)
        print('调整单：结果', supply_demand_list)
        print('调整单：结果', supply_demand_product_list)
        # 最后一把写入数据库 复用总部帮配映射
        demand_master_repository.insert_demand_master_detail_and_product(supply_demand_list,
                                                                         supply_demand_product_list)

        return True

    # 预拆单根据商品返回对应仓库id
    def pre_generate_order_by_demand(self, demand_id, partner_id, user_id):
        demand = DBSession.query(Supply_demand).with_for_update().get(demand_id)
        # 订货单拆成要货单
        product_list, count = Supply_demand_product.get_by_demand_id(demand.id)
        temp_order_map = {}
        username = metadata_service.get_username_by_pid_uid(demand.partner_id, demand.updated_by)

        configs = metadata_service.get_supply_config(partner_id=partner_id, domain='boh.store.order', user_id=user_id)
        order_config, switch = configs.get('combine_storage_type', {}), configs.get('order_by_storage_type')
        pur_storage = {i: v[0] for v in order_config.get('direct_pur', []) for i in v}
        nmd_storage = {i: v[0] for v in order_config.get('direct_nmd', []) for i in v}
        pur_storage = pur_storage if switch and pur_storage.keys() == DEFAULT_STORAGE_TYPE.keys() else DEFAULT_STORAGE_TYPE
        nmd_storage = nmd_storage if switch and nmd_storage.keys() == DEFAULT_STORAGE_TYPE.keys() else DEFAULT_STORAGE_TYPE
        # user_name = 'test'
        for product in product_list:
            # if demand.type == Demand_type.SD.code or demand.type == Demand_type.AD.code:
            #     # 配送订货对部分储藏方式合并
            #     if product.distribution_type == 'NMD':
            #         # 冷藏+冷冻 不拆单，合并为冻货
            #         if product.storage_type == 'LC' or product.storage_type == 'COLD':
            #             key_set = (product.distribute_by, product.distribution_type, product.arrival_days, 'COLD')
            #         # 常温+恒温 不拆单，合并成常温
            #         elif product.storage_type == 'NORMALTP' or product.storage_type == 'HW':
            #             key_set = (product.distribute_by, product.distribution_type, product.arrival_days, 'NORMALTP')
            #         else:
            #             key_set = (
            #                 product.distribute_by, product.distribution_type, product.arrival_days,
            #                 product.storage_type)
            #     else:
            #         key_set = (
            #             product.distribute_by, product.distribution_type, product.arrival_days, product.storage_type)
            #
            # if demand.type in (Demand_type.HD.code, Demand_type.MD.code):
            #     # 配送订货对部分储藏方式合并
            #     if product.distribution_type == 'NMD':
            #         # 冷藏+冷冻 不拆单，合并为冻货
            #         if product.storage_type == 'LC' or product.storage_type == 'COLD':
            #             key_set = (product.distribute_by, product.distribution_type, 'COLD')
            #         # 常温+恒温 不拆单，合并成常温
            #         elif product.storage_type == 'NORMALTP' or product.storage_type == 'HW':
            #             key_set = (product.distribute_by, product.distribution_type, 'NORMALTP')
            #         else:
            #             key_set = (product.distribute_by, product.distribution_type, product.storage_type)
            #     else:
            #         key_set = (product.distribute_by, product.distribution_type, product.storage_type)
            if demand.type == Demand_type.SD.code or demand.type == Demand_type.AD.code:
                if product.distribution_type == 'NMD':
                    key_set = (product.distribute_by, product.distribution_type, product.arrival_days,
                               nmd_storage.get(product.storage_type, ''))
                else:
                    key_set = (product.distribute_by, product.distribution_type, product.arrival_days,
                               pur_storage.get(product.storage_type, ''))
                if key_set in temp_order_map:
                    temp_order_map.get(key_set).append(product)
                else:
                    temp_order_map[key_set] = [product]
            elif demand.type in (Demand_type.HD.code, Demand_type.MD.code):
                if product.distribution_type == 'NMD':
                    key_set = (product.distribute_by, product.distribution_type,
                               nmd_storage.get(product.storage_type, ''))
                else:
                    key_set = (product.distribute_by, product.distribution_type,
                               pur_storage.get(product.storage_type, ''))
                if key_set in temp_order_map:
                    temp_order_map.get(key_set).append(product)
                else:
                    temp_order_map[key_set] = [product]
            else:
                logging.info(f'{demand_id}: 未拆单')
                return {}

        if not temp_order_map:
            # 空单作废
            demand.status = Demand_enum.CANCELLED.code
            return {}
        result = {}
        # print('************', temp_order_map)
        products = []
        for key, value in temp_order_map.items():
            distribution = key[0]
            distribution_type = key[1]
            if demand.type == Demand_type.SD.code or demand.type == Demand_type.AD.code:
                arrival_date = demand.demand_date + timedelta(days=int(key[2]))
                storage_type = key[3]
            if demand.type in (Demand_type.HD.code, Demand_type.MD.code):
                arrival_date = demand.arrival_date
                storage_type = key[2]
            line_count = 0

            for product in value:
                line_count += 1
                product_detail = {}
                product_detail['product_id'] = product.product_id
                product_detail['distribute_by'] = distribution
                product_detail['distribution_type'] = product.distribution_type
                products.append(product_detail)
        return products

    #######################
    ######demand的一些方法结束

    def get_product_sale_forecast(self, request, partner_id, user_id):
        store_id = request.store_id
        demand_day = request.demand_day
        timestamp = Timestamp()
        timestamp.seconds = demand_day.seconds
        demand_day = timestamp.ToDatetime()
        demand_day = demand_day
        product_ids = list(request.product_ids)
        print('s', product_ids, demand_day)
        is_tea_and_bread = request.is_tea_and_bread
        if is_tea_and_bread:
            res = TeaBreadDemandMaterialSuggest.get_suggest(
                partner_id, user_id, demand_day, store_id, product_ids
            )
        else:
            res = DemandSuggest.get_demand_suggest(partner_id, user_id, store_id, demand_day, product_ids)
        print('res', res)
        return res

    def set_close_order_data(self, product_ids, close_order_start_time, close_order_end_time, close_order_stores,
                             close_order_companies, close_order_factories, close_order_branch_regions,
                             partner_id, user_id, status=False):

        if not product_ids:
            raise DataValidationException('required product_ids')

        if not close_order_start_time or not close_order_end_time:
            raise DataValidationException('required close_order_start_time and close_order_end_time')

        if close_order_start_time >= close_order_end_time:
            raise DataValidationException('close_order_start_time must be less than close_order_end_time')

        if not any([close_order_stores, close_order_companies, close_order_factories, close_order_branch_regions]):
            raise DataValidationException('required effective scopes')

        check_metadata_exists_by_ids(partner_id, user_id, 'PRODUCT', product_ids)

        if close_order_companies:
            if close_order_companies[0] != "None":
                check_metadata_exists_by_ids(partner_id, user_id, 'COMPANY_INFO', close_order_companies)

        # 区域
        if close_order_branch_regions:
            if close_order_branch_regions[0] != "None":
                check_metadata_exists_by_ids(partner_id, user_id, 'BRANCH_REGION', close_order_branch_regions)

        # 门店
        if close_order_stores:
            if close_order_stores[0] != "None":
                check_metadata_exists_by_ids(partner_id, user_id, 'STORE', close_order_stores)

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        str_now = convert_to_RFC3339(datetime.now())

        field = {
            "close_order_updated_by": user_id,
            "close_order_updated_name": username,
            "close_order_updated_time": str_now,
            "close_order_start_time": convert_to_RFC3339(close_order_start_time),
            "close_order_end_time": convert_to_RFC3339(close_order_end_time),
            "close_order_status": status,
            "close_order_factories": [str(i) for i in close_order_factories],
            "close_order_stores": [str(i) for i in close_order_stores],
            "close_order_companies": [str(i) for i in close_order_companies],
            "close_order_branch_regions": [str(i) for i in close_order_branch_regions]
        }
        field_struct = dict_to_struct(field)

        update_data = [{
            "id": i,
            "is_merged": True,
            "fields": field_struct,
            "auto_apply": True,
            "lan": "zh-CN",
            "schema_name": "PRODUCT"
        } for i in product_ids]

        res = metadata_service.batch_update_entity(entities=update_data, partner_id=partner_id, user_id=user_id)
        logging.info(f'batch_update_entity:= {res}')

    def switch_close_orders(self, product_ids, status, partner_id, user_id):
        if not product_ids:
            raise DataValidationException('required product_ids')

        check_metadata_exists_by_ids(partner_id, user_id, 'PRODUCT', product_ids)

        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        str_now = str(datetime.now())

        fields = {
            "close_order_updated_by": user_id,
            "close_order_updated_name": username,
            "close_order_updated_time": str_now,
            "close_order_status": status,
        }
        field_struct = dict_to_struct(fields)
        update_data = [{
            "id": i,
            "is_merged": True,
            "fields": field_struct,
            "auto_apply": True,
            "lan": "zh-CN",
            "schema_name": "PRODUCT"
        } for i in product_ids]

        res = metadata_service.batch_update_entity(entities=update_data, partner_id=partner_id, user_id=user_id)
        logging.info(f'batch_update_entity:= {res}')

    def turn_off_close_orders(self, partner_id, user_id):
        if not partner_id and user_id:
            logging.error("turn_off_close_orders required partner_id and user_id!")
            return
        str_now = str(datetime.now())

        product_meta = metadata_service.list_entity(
            schema_name='product', return_fields='id', partner_id=partner_id, user_id=user_id,
            filters={
                "close_order_end_time__lt": str_now,
                "close_order_status": True,
            }
        )

        update_data = [{
            "id": str(i['id']),
            "is_merged": True,
            "fields": {
                # "close_order_updated_by": user_id,
                # "close_order_updated_name": username,
                # "close_order_updated_time": str_now,
                "close_order_status": False,
            },
            "auto_apply": True,
            "lan": "zh-CN"
        } for i in product_meta]

        res = metadata_service.batch_update_entity(entities=update_data, partner_id=partner_id, user_id=user_id)
        logging.info(f'batch_update_entity:= {res}')


def check_metadata_exists_by_ids(partner_id, user_id, schema_name, ids):
    if not ids:
        return
    ids = [int(i) for i in ids]
    meta_info = product_meta = metadata_service.list_entity(
        schema_name=schema_name, ids=ids, return_fields='id', partner_id=partner_id, user_id=user_id)
    meta_ids = [int(p['id']) for p in meta_info['rows']]
    # 获取差集
    diff_id_list = list(filter(lambda x: x not in meta_ids, ids))
    if diff_id_list:
        raise DataValidationException('%s ids not exist:%s' % (schema_name, diff_id_list))
