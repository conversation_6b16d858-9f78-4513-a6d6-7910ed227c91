from datetime import datetime, timedelta
import math, traceback

from supply.client.report_service import report_service
from supply.utils.helper import get_today_datetime, translate_utc_time, get_quantity
from supply import time_cost
from supply.model.supply_factor import Supply_factor
from supply import logger
from supply.driver.Redis import Redis_cli
from supply.client.metadata_service import metadata_service

class demand_two_module(object):

    @staticmethod
    @time_cost
    def get_suggest_quantity(store_id, products:list, bizdt:datetime, partner_id, user_id):
        '''
        params:
            products=[
                {
                    'product_id':1432512512651,
                    'arrival_day':3,
                    'min_quantity':33,
                    'max_quantity':55,
                    'increment_quantity':2,
                    'sale_type':'NEW-RETAIL',
                    'unit_rate':1.3
                },
                ]
            bizdt: datatime
        return:
                {
                    1432512512651:{suggest_quantity":13.4},
                    6355135324135:{suggest_quantity":21.5},
                    2623561114234:{suggest_quantity":45.1},
                }
        '''
        # D: 到货日
        # # 获得商品建议订货量（）
        # if （Qo-Qs） > 0(有报损)
        #     Qd	= Qf =roundup[Q(D-7)*80% + Q(WMA)*20%]*Wd*Prom/Qm * Qm
        # QD-7(到货日-7) ，11-2-1,11-7-1-
        # if （Qo-Qs） ≤ 0（卖完）
        # Qd	= Qf roundup[Q(D-7)*80% + Q(WMA)*20%]*Wd*Prom/Qm * Qm+N*Qm
        # D-7日销售量	：Q(D-7)- 如D-7日为节日，调休日，促销日，则改用D-14或D-21直至为正常日
        # D-LT-1至D-LT-7日移动平均销量:	Q(WMA)
        # D-LT-1日订货量	Qo
        # D-LT-1日销售量	Qs
        # D日天气因子：	Wd  
        # 促销因子（折扣率）：Prom1
        # 节日因子：Prom2
        if not products or not store_id or not bizdt:
            return {}
        logger.info("store_id=" + str(store_id) + "suggest_quanity_products:" + str(products))
        result = {}
        Wd = 1
        Prom1 = 1
        Prom2 = 1
        factor = Supply_factor.get_by_store_id_and_date(store_id, bizdt, partner_id)
        if factor:
            Wd = float(factor.weather) if factor.weather else 1
            Prom1 = float(factor.Prom1) if factor.Prom1 else 1
            Prom2 = float(factor.Prom2) if factor.Prom2 else 1
        logger.info("store_id={}_wd={}_prom1={}_prom2={}".format(store_id, Wd, Prom1, Prom2))

        for product in products:
            try:
                sale_type = product.get('sale_type', "")
                product_id = int(product.get("product_id", 0))
                unit_rate = float(product.get("unit_rate", 0))
                Qm = float(product.get("increment_quantity", 0))
                max_quantity = float(product.get("max_quantity", 0))
                increment_quantity = float(product.get("increment_quantity", 0))
                if not Qm:
                    logger.info("请设置最小订货量,store_id_{}_min_quanity_product_id:{}".format(store_id, product_id))
                    continue
                arrival_day = int(product.get("arrival_day", 0))

                Qd = 0
                # 新零售建议订货
                if sale_type.startswith("NEW-RETAIL"):
                    Qd = demand_two_module._get_suggest_new_retail(store_id, bizdt, product_id, unit_rate, Qm, max_quantity, 
                        increment_quantity, arrival_day, Wd, Prom1, Prom2, partner_id, user_id)
                result[product_id] = {"suggest_quantity":Qd, "product_id":product_id}
            except Exception as e:
                logger.info("suggest_error_store_id={}_product_id={}_error={}".format(store_id, product, traceback.format_exc()))
                continue

        return result

    @staticmethod
    @time_cost
    def _get_suggest_new_retail(store_id, bizdt, product_id, unit_rate, Qm, max_quantity, increment_quantity, arrival_day, Wd, Prom1, Prom2, partner_id, user_id):
        # product_ids = [int(i.get("product_id")) for i in products if i.get("product_id")]
        D_date = bizdt + timedelta(days=arrival_day) # D为到货日
        LT = arrival_day
        q_date = D_date - timedelta(days=LT) - timedelta(days=1)
        logger.info("store_id:{}_get_suggest_quantity_q_date:{}".format(store_id, str(q_date)))
        # D-LT-1日销售量
        D_LT_1_sale_amount_of_products = report_service.get_amount_of_product_sales(q_date, q_date + timedelta(days=1), [product_id], store_id, partner_id, user_id)
        logger.info("store_id_{}_D_LT_1_sale_amount_of_products_before:{}".format(store_id, str(D_LT_1_sale_amount_of_products)))
        if D_LT_1_sale_amount_of_products:
            D_LT_1_sale_amount_of_products = dict((int(i.get("product_id")), i) for i in D_LT_1_sale_amount_of_products if i.get("product_id"))
        else:
            D_LT_1_sale_amount_of_products = {}
        logger.info("D_LT_1_sale_amount_of_products_after:" + str(D_LT_1_sale_amount_of_products))
        # D-LT-1日订货量
        # order_amount_of_products = Supply_demand_order_product.get_amount_of_order_product(store_id, [product_id], q_date, partner_id)
        # order_amount_of_products = dict((i[0], i[1]) for i in order_amount_of_products)
        # D-LT-1至D-LT-7日移动平均销量:	Q(WMA)
        D_LT_1_DAY = D_date - timedelta(days=LT) - timedelta(days=1)
        D_LT_7_DAY = D_date - timedelta(days=LT) - timedelta(days=7)
        D_LT_1_7_sale_amount_of_product = report_service.get_amount_of_product_sales(D_LT_7_DAY, D_LT_1_DAY+timedelta(days=1), [product_id], store_id, partner_id, user_id)
        logger.info("D_LT_1_7_sale_amount_of_product_before:" + str(D_LT_1_7_sale_amount_of_product))
        if D_LT_1_7_sale_amount_of_product:
            D_LT_1_7_sale_amount_of_product = dict((int(i.get("product_id")), i) for i in D_LT_1_7_sale_amount_of_product if i.get("product_id"))        
        else:
            D_LT_1_7_sale_amount_of_product = {}
        logger.info("D_LT_1_7_sale_amount_of_product_after:" + str(D_LT_1_7_sale_amount_of_product))

        Qo = 0
        sale_amount = D_LT_1_sale_amount_of_products.get(product_id, {})
        Qs = 0
        D_7 = D_date - timedelta(days=7)
        is_holiday = Redis_cli.get_is_holiday(D_7) #如果是假期和调休日要往前推7天(暂时最多只推一个月)
        logger.info("store_id_{}_is_holiday:{}".format(store_id, is_holiday))
        if is_holiday:
            D_7 = D_7 - timedelta(days=7)
            is_holiday = Redis_cli.get_is_holiday(D_7)
            if is_holiday:
                D_7 = D_7 - timedelta(days=7)
                is_holiday = Redis_cli.get_is_holiday(D_7)
                if is_holiday:
                    D_7 = D_7 - timedelta(days=7)

        Qd_7 = report_service.get_amount_of_product_sales(D_7, D_7+timedelta(days=1), [product_id], store_id, partner_id, user_id)
        logger.info("Qd_7_amount::" + str(Qd_7))
        if Qd_7:
            for i in Qd_7:
                if int(i.get("product_id", 0)) == product_id:
                    Qd_7 = float(i.get("quantity", 0)) * unit_rate
                    break
            else:
                Qd_7 = 0
        else:
            Qd_7 = 0
        if sale_amount:
            Qs = float(sale_amount.get("quantity"))
        Qwma = D_LT_1_7_sale_amount_of_product.get(product_id, {})
        logger.info("store_id_{}_Qooooooo:{}".format(store_id, str(Qo)))
        logger.info("store_id_{}_Qsssssss:{}".format(store_id, str(Qs)))
        if (Qo - Qs) > 0:
            #(有报损)Qd	= Qf =roundup[Q(D-7)*80% + Q(WMA)*20%]*Wd*Prom/Qm * Qm
            logger.info("store_id_{}_Qd_7:{}, Qwma:{}".format(store_id, Qd_7, Qwma.get("average_quantity", 0)))
            Qd= math.ceil((Qd_7*0.8 + float(Qwma.get("average_quantity", 0) * unit_rate) * 0.2) * Wd * Prom1 * Prom2/ Qm) * Qm
        else:
            #(卖完)  Qd= Qf roundup[Q(D-7)*80% + Q(WMA)*20%]*Wd*Prom/Qm * Qm+N*Qm
            sales_end_date = None
            if sale_amount:
                sales_end_date = translate_utc_time(sale_amount.get('sales_end_date'))
            # D-LT-1日最后一笔销售对应时段	Hs
            # 18<Hs≤21	　	N=1
            # 14<Hs≤18	　	N=2
            # Hs≤14	　	    N=3
            N = 0
            if sales_end_date:
                hour = sales_end_date.hour
                # 这里是取销售时段
                if hour>=18 and hour<=21:
                    N = 1
                elif hour>=14 and hour<18:
                    N = 2
                elif hour<14:
                    N = 3
            logger.info("store_id_{}_Qd_7:{}, Qwma:{}, N:{}".format(store_id, Qd_7, Qwma.get("average_quantity", 0), N))
            Qd = math.ceil((Qd_7*0.8 + float(Qwma.get("average_quantity", 0) * unit_rate) * 0.2) * Wd * Prom1 * Prom2/ Qm) * Qm + N * Qm
        Qd = get_quantity(Qd, max_quantity, Qm, increment_quantity)
        return Qd
