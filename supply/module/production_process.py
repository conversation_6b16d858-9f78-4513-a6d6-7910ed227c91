import logging
from datetime import datetime
from google.protobuf.timestamp_pb2 import Timestamp
from supply.utils.helper import convert_to_int, MessageTopic, get_product_unit_rate_map, convert_to_decimal, \
    get_cost_center_map, get_product_unit_map, get_product_map
from supply.error.exception import DataValidationException, DataDuplicationException, OrderNotExistException, \
    StatusUnavailable
from supply.model.production_process import production_process_receipts_db
from supply.model.supply_doc_code import Supply_doc_code
from supply.client.metadata_service import metadata_service
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.auth import branch_list_scope_check, branch_scope_check
from supply.utils.inventory_enum import ACTION
from supply.model.inventory import inventory_repository
from supply.driver.mq import mq_producer


class ProductProcessReceiptsService(object):
    """生产单业务处理"""

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    # noinspection PyMethodMayBeStatic
    def utctimestamp2datetime(self, timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.utcfromtimestamp(timestamp.seconds)
        return date

    def update_res_datetime2timestamp(self, keys, res: dict):
        """更新查询集中的datetime to Timestamp
        :param keys: 字段值列表 -> (str)
        :param res: 返回查询集 -> (dict)
        """
        if keys:
            for key in keys:
                if key in res.keys():
                    res.update({key: self.get_timestamp(res[key])})
        return res

    def create_product_process_receipts(self, request, user_id=None, partner_id=None, store_type="store"):
        request_id = convert_to_int(request.request_id)
        if not request_id:
            raise DataValidationException("没有请求id")

        record = production_process_receipts_db.get_production_receipt_by_id(request_id=request_id,
                                                                             partner_id=partner_id)
        if record:
            raise DataDuplicationException("该生产单已创建{}".format(record.id))

        process_date = self.utctimestamp2datetime(request.process_date)
        if not process_date:
            raise DataValidationException("请传入生产单日期")
        store_id = convert_to_int(request.process_store_id)
        process_store_name = request.process_store_name
        process_store_code = request.process_store_code
        remark = request.remark

        code = Supply_doc_code.get_code_by_type(code_type='PROPRE_REC', partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        main_id = gen_snowflake_id()
        receipt = dict(
            id=main_id,
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            code=code,
            status="INITED",
            process_date=process_date,
            process_store_id=store_id,
            type=store_type,
            process_store_name=process_store_name,
            process_store_code=process_store_code,
            remark=remark,
            request_id=request_id
        )

        details = request.details
        if not details:
            raise DataValidationException("生产单必须包含商品")
        items = request.items
        if not items:
            raise DataValidationException("生产单必须包含商品产出率")
        # product_ids = [convert_to_int(d.product_id) for r in rows for d in r.details]
        # product_map = get_product_map(product_ids=product_ids, return_fields="id,code,name",
        #                               partner_id=partner_id,
        #                               user_id=user_id)
        receipt_detail_list = []
        receipt_detail_item = []

        for detail in details:
            receipt_detail = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username,
                main_id=main_id,
                # status='INITED',
                # code=code,
                # process_date=process_date,
                # process_store_id=store_id,
                # process_store_name=process_store_name,
                # process_store_code=process_store_code,
                # remark=remark,
                # request_id=request_id,
                production_rule=detail.production_rule,
                production_rule_name=detail.production_rule_name,
                production_rule_code=detail.production_rule_code,
                type=detail.type,
                product_id=detail.product_id,
                product_code=detail.product_code,
                product_name=detail.product_name,
                unit_id=detail.unit_id,
                unit_code=detail.unit_code,
                unit_name=detail.unit_name,
                specification_id=detail.specification_id,
                specification_code=detail.specification_code,
                specification_name=detail.specification_name,
                production_quantity=detail.quantity
            )
            receipt_detail_list.append(receipt_detail)
        for item in items:
            receipt_item = dict(
                id=gen_snowflake_id(),
                partner_id=partner_id,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username,
                main_id=main_id,
                production_rule=item.production_rule,
                production_rule_name=item.production_rule_name,
                production_rule_code=item.production_rule_code,
                from_material_id=item.from_material_id,
                from_material_code=item.from_material_code,
                from_material_name=item.from_material_name,
                to_material_id=item.to_material_id,
                to_material_code=item.to_material_code,
                to_material_name=item.to_material_name,
                theoretical_rate=item.theoretical_rate,
                actual_rate=item.actual_rate
            )
            receipt_detail_item.append(receipt_item)
        res = production_process_receipts_db.create_production_receipts(receipt_data=receipt,
                                                                        receipt_details=receipt_detail_list,
                                                                        items=receipt_detail_item)
        response = {}
        if res is True:
            response["receipt_id"] = main_id
            response["result"] = "success"
        else:
            response["result"] = "failed"
        return response

    def list_production_process_receipts(self, request, partner_id=None, user_id=None, branch_ids=None,
                                         branch_type='store'):
        response = {}
        start_date = self.utctimestamp2datetime(request.start_date)
        end_date = self.utctimestamp2datetime(request.end_date)
        code = request.code
        status = list(request.status) if request.status else []
        limit = request.limit or -1
        offset = request.offset
        include_total = request.include_total

        query_set = production_process_receipts_db.list_process_receipts(partner_id=partner_id, user_id=user_id,
                                                                         start_date=start_date, end_date=end_date,
                                                                         code=code, status=status, limit=limit,
                                                                         offset=offset, branch_ids=branch_ids,
                                                                         include_total=include_total,
                                                                         branch_type=branch_type)
        if isinstance(query_set, tuple):
            total, query_set = query_set
            response["total"] = total
        else:
            query_set = query_set
        result_list = []
        if query_set:
            for row in query_set:
                row = row.as_dict()
                # 需要更新datetime的字段
                keys = ["created_at", "updated_at", "process_date"]
                row = self.update_res_datetime2timestamp(keys=keys, res=row)
                result_list.append(row)
        response["rows"] = result_list
        return response

    def get_production_process_detail(self, request, partner_id=None, user_id=None):
        response = {}
        receipt_id = convert_to_int(request.receipt_id)
        receipt = production_process_receipts_db.get_production_receipt_by_id(receipt_id=receipt_id,
                                                                              partner_id=partner_id)
        # 权限校验
        # branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
        #                    domain='boh.store', branch_id=receipt.process_store_id)
        response.update(receipt.as_dict())
        keys = ["created_at", "updated_at", "process_date"]
        response = self.update_res_datetime2timestamp(keys=keys, res=response)
        details, items = production_process_receipts_db.get_production_receipt_by_id(receipt_id=receipt_id,
                                                                                     partner_id=partner_id,
                                                                                     is_detail=True)
        rule_details_items = {}
        reserve_field = ['id', 'product_id', 'product_code', 'product_name', 'unit_id', 'unit_code', 'unit_name',
                         'type', 'quantity', 'production_rule', 'production_rule_code', 'production_rule_name',
                         'from_material_id', 'from_material_code', 'from_material_name', 'to_material_id',
                         'to_material_code', 'to_material_name', 'theoretical_rate', 'actual_rate', 'specification_id',
                         'specification_code', 'specification_name', 'production_quantity']
        for row in details:
            row = row.as_dict()
            if 'partner_id' in row:
                del row['partner_id']
            keys = ["created_at", "updated_at"]
            row = self.update_res_datetime2timestamp(keys=keys, res=row)
            rule_id = row['production_rule']
            if rule_id not in rule_details_items:
                rule_details_items[rule_id] = {'details': [], 'items': [],
                                               'production_rule_code': row['production_rule_code'],
                                               'production_rule_name': row['production_rule_name']}
            new_keys = {}
            for k, v in row.items():
                if k in reserve_field:
                    if k == 'production_quantity':
                        new_keys['quantity'] = v
                    else:
                        new_keys[k] = v
            rule_details_items[rule_id]['details'].append(new_keys)
        for row in items:
            row = row.as_dict()
            keys = ["created_at", "updated_at"]
            row = self.update_res_datetime2timestamp(keys=keys, res=row)
            rule_id = row['production_rule']
            if rule_id not in rule_details_items:
                rule_details_items[rule_id] = {'details': [], 'items': [],
                                               'production_rule_code': row['production_rule_code'],
                                               'production_rule_name': row['production_rule_name']}
            new_keys = {}
            for k, v in row.items():
                if k in reserve_field:
                    new_keys[k] = v
            rule_details_items[rule_id]['items'].append(new_keys)

        rows = []
        for rule_id, data in rule_details_items.items():
            rows.append({
                'production_rule': rule_id,
                'production_rule_code': data.get('production_rule_code'),
                'production_rule_name': data.get('production_rule_name'),
                'details': data['details'],
                'items': data['items'],
            })
        response['rows'] = rows
        return response

    def update_production_process_receipts(self, request, partner_id=None, user_id=None):
        """更新生产单"""
        ret = {}
        receipt_id = convert_to_int(request.receipt_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        process_date = self.utctimestamp2datetime(timestamp=request.process_date)
        query_order = production_process_receipts_db.get_production_receipt_by_id(receipt_id=receipt_id,
                                                                                  partner_id=partner_id)
        if not query_order:
            raise DataDuplicationException("该生产单不存在{}".format(query_order.id))
        if query_order.status in ["SUBMITTED", "APPROVED", "CONFIRMED"]:
            raise DataValidationException("{}状态下不允许更新详情".format(query_order.status))

        receipt = dict(
            id=receipt_id,
            updated_by=user_id,
            updated_name=username,
            process_date=process_date,
            remark=request.remark,
            process_store_id=request.process_store_id,
            process_store_code=request.process_store_code,
            process_store_name=request.process_store_name,
        )
        # 待删除的规则id
        receipt_rule_ids = request.receipt_rule_ids
        details = request.details
        # 已存在的实际能更改的只有数量/或者是新增规则进来的
        update_details = []
        insert_details = []
        for detail in details:
            if detail.id:
                update_details.append({
                    "id": detail.id,
                    "production_quantity": detail.quantity,
                    "updated_name": username,
                    "updated_by": user_id
                })
            else:
                insert_details.append(dict(
                    id=gen_snowflake_id(),
                    partner_id=partner_id,
                    created_by=user_id,
                    created_name=username,
                    updated_by=user_id,
                    updated_name=username,
                    main_id=receipt_id,
                    production_rule=detail.production_rule,
                    production_rule_name=detail.production_rule_name,
                    production_rule_code=detail.production_rule_code,
                    type=detail.type,
                    product_id=detail.product_id,
                    product_code=detail.product_code,
                    product_name=detail.product_name,
                    unit_id=detail.unit_id,
                    unit_code=detail.unit_code,
                    unit_name=detail.unit_name,
                    specification_id=detail.specification_id,
                    specification_code=detail.specification_code,
                    specification_name=detail.specification_name,
                    production_quantity=detail.quantity
                ))
        # 已存在的实际能更改的只有实际产出率/或者是新增规则进来的
        update_items = []
        insert_items = []
        items = request.items
        for item in items:
            if item.id:
                update_items.append({
                    "id": item.id,
                    "actual_rate": item.actual_rate,
                    "updated_name": username,
                    "updated_by": user_id
                })
            else:
                insert_items.append(dict(
                    id=gen_snowflake_id(),
                    partner_id=partner_id,
                    created_by=user_id,
                    created_name=username,
                    updated_by=user_id,
                    updated_name=username,
                    main_id=receipt_id,
                    production_rule=item.production_rule,
                    production_rule_name=item.production_rule_name,
                    production_rule_code=item.production_rule_code,
                    from_material_id=item.from_material_id,
                    from_material_code=item.from_material_code,
                    from_material_name=item.from_material_name,
                    to_material_id=item.to_material_id,
                    to_material_code=item.to_material_code,
                    to_material_name=item.to_material_name,
                    theoretical_rate=item.theoretical_rate,
                    actual_rate=item.actual_rate
                ))
        if insert_items or insert_details:
            production_process_receipts_db.create_production_receipts(receipt_details=insert_details,
                                                                      items=insert_items)
        if receipt_rule_ids:
            production_process_receipts_db.delete_production_process_receipts(partner_id=partner_id,
                                                                              receipt_id=receipt_id,
                                                                              rule_ids=receipt_rule_ids)
        res = production_process_receipts_db.update_production_process_receipts(receipt=receipt,
                                                                                receipt_detail_list=update_details,
                                                                                items_list=update_items,
                                                                                partner_id=partner_id)
        if res is True:
            ret["receipt_id"] = receipt_id
            ret["result"] = "success"
        return ret

    def change_product_process_receipt_status(self, request, partner_id=None, user_id=None):
        ret = {}
        receipt_id = convert_to_int(request.receipt_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        status = request.status
        query_order = production_process_receipts_db.get_production_receipt_by_id(receipt_id=receipt_id,
                                                                                  partner_id=partner_id)
        if not query_order:
            raise OrderNotExistException("该生产单不存在-{}".format(receipt_id))
        if status and status not in ["INITED", "SUBMITTED", "APPROVED", "REJECTED", "DELETED", "CONFIRMED", "INVALID"]:
            raise DataValidationException("更新状态不合法{}".format(status))
        if status == query_order.status:
            raise DataValidationException("状态已变更请勿重复操作")
        if status == "SUBMITTED" and query_order.status not in ("INITED", "REJECTED"):
            raise StatusUnavailable("只有草稿/新建/已驳回的生产单可以提交!")
        if status == 'INVALID' and query_order.status not in ("INITED", "REJECTED"):
            raise StatusUnavailable("只有新建/已驳回的生产单可以作废!")
        if status == "REJECTED" and query_order.status != "SUBMITTED":
            raise StatusUnavailable("只有已提交的生产单可以驳回!")
        if status == "APPROVED" and query_order.status != "SUBMITTED":
            raise StatusUnavailable("只有已提交的生产单可以审核!")
        receipt = dict(
            id=receipt_id,
            updated_by=user_id,
            updated_name=username,
            status=status,
        )
        res = production_process_receipts_db.update_production_process_receipts(receipt=receipt, receipt_detail_list=[],
                                                                                items_list=[], partner_id=partner_id)
        if res is True:
            ret["receipt_id"] = receipt_id
            ret["result"] = "success"
            if status == "CONFIRMED":
                """单据审核后需做库存处理"""
                # 查询物料和产出物
                products = production_process_receipts_db.get_production_receipt_by_id(receipt_id=receipt_id,
                                                                                       is_detail=True, need_item=False,
                                                                                       partner_id=partner_id)
                product_ids = []
                for p in products:
                    product_ids.append(p.product_id)
                # 获取商品单位和转换率map
                product_unit_rate_map = get_product_unit_rate_map(product_ids=product_ids,
                                                                  partner_id=partner_id,
                                                                  user_id=user_id)
                accounts = []
                for p in products:
                    accounting = dict(sub_account=None)
                    unit_rate_dict = product_unit_rate_map.get(p.product_id) if product_unit_rate_map.get(
                        p.product_id) else {}
                    quantity = convert_to_decimal(p.production_quantity)
                    unit_rate = convert_to_decimal(unit_rate_dict.get(p.unit_id, 1))
                    accounting_quantity = quantity * unit_rate
                    accounting.update(dict(
                        branch_id=query_order.process_store_id,
                        product_id=p.product_id,
                        amount=str(accounting_quantity),  # 存字符串为了json序列化
                        action=2 if p.type == 'product' else 1
                    ))
                    accounts.append(accounting)
                message = dict(batch_no=str(receipt_id),
                               code='MATERIAL_CONVERT',
                               action=100,
                               description='MATERIAL_CONVERT',
                               trace_id=query_order.code,
                               accounts=accounts,
                               partner_id=partner_id,
                               user_id=user_id,
                               business_time=datetime.utcnow())
                inventory_dict = dict(batch_no=str(receipt_id), code="MATERIAL_CONVERT", batch_action=100,
                                      action_dec=ACTION[100],
                                      batch_id=None,
                                      status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                      )
                inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                 partner_id=partner_id, user_id=user_id)
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                    message=message)

        return ret

    def get_material_to_product_rate(self, request, partner_id=None, user_id=None, branch_type='store'):
        limit = request.limit
        offset = request.offset
        store_ids = list(request.store_ids)
        start_date = request.start_date
        end_date = request.end_date
        include_total = request.include_total
        # code = request.code
        material_ids = list(request.material_ids) if request.material_ids else None
        product_ids = list(request.product_ids) if request.product_ids else None
        conversion_rate = request.conversion_rate
        if start_date and not isinstance(start_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = start_date.seconds
            start_date = timestamp.ToDatetime()
            start_date = start_date
        if end_date and not isinstance(end_date, datetime):
            timestamp = Timestamp()
            timestamp.seconds = end_date.seconds
            end_date = timestamp.ToDatetime()
            end_date = end_date
        return production_process_receipts_db.get_material_to_product_rate(partner_id=partner_id, limit=limit,
                                                                           offset=offset, include_total=include_total,
                                                                           start_date=start_date, end_date=end_date,
                                                                           store_ids=store_ids,
                                                                           material_ids=material_ids,
                                                                           product_ids=product_ids,
                                                                           conversion_rate=conversion_rate,
                                                                           branch_type=branch_type)

    def get_rule_by_branch_id(self, partner_id, user_id, branch_id, branch_type='store'):
        schema_name = 'store'
        if branch_type == 'warehouse':
            schema_name = 'distrcenter'
        if branch_type == 'machining':
            schema_name = 'machining-center'
        branch_info = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id, id=branch_id,
                                                        schema_name=schema_name)
        if not branch_info:
            raise DataValidationException("未找到该主档")
        relation = branch_info.get('fields', {}).get('relation', {})
        branch_region = relation.get('branch_region')
        geo_region = relation.get('geo_region')
        franchisee_region = relation.get('franchisee_region')
        branch_type = 'stores' if branch_type == 'store' else branch_type
        filters = 'relation.{}__in'.format(branch_type)
        rules_info = metadata_service.list_entity(schema_name='production-process-rule', return_fields='id,code,name',
                                                  filters={"status__eq": "ENABLED",
                                                           "{}".format(filters): [str(branch_id)]},
                                                  partner_id=partner_id, user_id=user_id).get('rows', [])
        return_info = [{'id': r['id'], 'name': r['fields']['name'], 'code': r['fields']['code']} for r in rules_info]
        if branch_region:
            branch_entity = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id,
                                                              id=int(branch_region), schema_name='branch-region',
                                                              include_parents=True)
            branch_ids = [branch_region]
            for i in range(10):
                if 'parent' in branch_entity:
                    parent = branch_entity.get('parent') or {}
                    parent_id = int(parent.get('id') or 0)
                    if parent_id:
                        branch_ids.append(parent_id)
                        branch_entity = parent
                    else:
                        break
            branch_rules_info = metadata_service.list_entity(schema_name='production-process-rule',
                                                             return_fields='id,code,name',
                                                             filters={"status__eq": "ENABLED",
                                                                      'relation.branch_region__in': [str(b_id) for b_id
                                                                                                     in branch_ids]},
                                                             partner_id=partner_id, user_id=user_id).get('rows', [])

            return_info.extend(
                [{'id': r['id'], 'name': r['fields']['name'], 'code': r['fields']['code']} for r in branch_rules_info])
        if geo_region:
            geo_entity = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id,
                                                           id=int(geo_region), schema_name='geo-region',
                                                           include_parents=True)
            geo_ids = [geo_region]
            for i in range(10):
                if 'parent' in geo_entity:
                    parent = geo_entity.get('parent') or {}
                    parent_id = int(parent.get('id') or 0)
                    if parent_id:
                        geo_ids.append(parent_id)
                        geo_entity = parent
                    else:
                        break

            geo_rules_info = metadata_service.list_entity(schema_name='production-process-rule',
                                                          return_fields='id,code,name', relation='all',
                                                          filters={"status__eq": "ENABLED",
                                                                   'relation.geo_region__in': [str(b_id) for b_id in
                                                                                               geo_ids]},
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
            return_info.extend(
                [{'id': r['id'], 'name': r['fields']['name'], 'code': r['fields']['code']} for r in geo_rules_info])
        if franchisee_region:
            franchisee_entity = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id,
                                                                  id=int(franchisee_region),
                                                                  schema_name='franchisee-region',
                                                                  include_parents=True)
            franchisee_ids = [franchisee_region]
            for i in range(10):
                if 'parent' in franchisee_entity:
                    parent = franchisee_entity.get('parent') or {}
                    parent_id = int(parent.get('id') or 0)
                    if parent_id:
                        franchisee_ids.append(parent_id)
                        franchisee_entity = parent
                    else:
                        break
            franchisee_rules_info = metadata_service.list_entity(schema_name='production-process-rule',
                                                                 return_fields='id,code,name',
                                                                 filters={"status__eq": "ENABLED",
                                                                          'relation.franchisee_region__in': [str(b_id)
                                                                                                             for b_id in
                                                                                                             franchisee_ids]},
                                                                 partner_id=partner_id, user_id=user_id).get('rows', [])

            return_info.extend([{'id': r['id'], 'name': r['fields']['name'], 'code': r['fields']['code']} for r in
                                franchisee_rules_info])

        unique_set = {tuple((item['id'], item['name'], item['code'])) for item in return_info}
        unique_dicts = [dict(zip(('id', 'name', 'code'), values)) for values in unique_set]
        return unique_dicts


product_process_receipts_service = ProductProcessReceiptsService()
