# -*- coding: utf-8 -*-
import json
import logging
import traceback

from collections import Iterable, namedtuple, deque
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp
from decimal import Decimal, ROUND_HALF_UP
from ast import literal_eval

from supply.client.credit_pay import credit_pay_service
from supply.client.products_manage_service import products_manage_service
from supply.driver.Redis import redis_server
from supply import logger, APP_CONFIG
from supply.driver.mysql import session
from supply.model.supply_partner_action import SupplyPartnerAction
from supply.utils.auth import branch_list_scope_check, branch_scope_check
from supply.utils.kit import Kit, KitEnum
from supply.utils.meta_schema import SchemaName
from supply.utils.enums import Demand_type, RefundType, Platform, PartnerActionModule
from supply.error.exception import StatusUnavailable, DataValidationException, \
    OrderNotExistException
from supply.model.supply_doc_code import Supply_doc_code
from supply.utils.encode import CJsonEncoder
from supply.utils.helper import get_guid, get_branch_map, get_product_map, get_uuids, convert_to_int, \
    convert_to_decimal, get_username_map, get_category_map, get_unit_map, MessageTopic, get_bus_date,\
    check_quantity
from supply.client.metadata_service import metadata_service
from supply.client.receipt_service import receipt_service
from supply.client.interactive import interactive_service
from supply.error.demand import DemandError, ProductError, DemandNotExistError
from supply.module import BaseToolsModule
from supply.proto.mobile.mobile_franchisee_demand_pb2 import Product
from supply.model.franchisee.franchisee_demand import DemandAction as Action
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand as sFD
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandLog as FdLogDB
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProduct as sFDP
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefund as sFR
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProductLog as FdpLogDB
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProductRelation as SFDPR


class MobileFranchiseeDemandService(BaseToolsModule):
    """加盟商订货相关业务操作-移动端"""
    def __init__(self):
        super(MobileFranchiseeDemandService, self).__init__()
        self.show_status = ["PREPARE", "P_SUBMIT", "SUBMITTED", "R_APPROVE", "REJECTED", "CANCELLED", "CONFIRMED",
                            "APPROVING", "APPROVED", "REFUNDING", "REFUNDED"]
        self.valid_status = ["PREPARE", "P_SUBMIT", "SUBMITTED", "R_APPROVE", "REJECTED", "CONFIRMED", "APPROVING",
                             "APPROVED", "REFUNDING"]     # 有效订单状态
        self.batch_type = "FRS_DEMAND"
        self.platform = Platform.HEX_MOBILE.code

    @staticmethod
    def _attach_product_attr(product: Product, product_map: dict, category_map: dict, unit_map: dict) -> None:
        """attach Request Product extra fields"""
        product_id = product.product_id
        pro_fields = product_map.get(str(product_id), {})
        units = pro_fields.get('units')
        unit_id = None
        accounting_unit_id = None
        unit_rate = 1
        if units:
            order_flag = False
            for u in units:
                if u.get("order"):
                    unit_rate = float(u.get("rate", 1))
                    unit_id = int(u.get('id'))
                    order_flag = True
                if u.get('default'):
                    accounting_unit_id = int(u.get('id'))
            if not order_flag:
                raise ProductError("此商品未设置订货单位，请检查主档设置, product_id:{}".format(product_id))
        else:
            raise ProductError("此商品未设置单位，请检查主档设置, product_id:{}".format(product_id))
        unit = unit_map.get(unit_id, {})
        accounting_unit = unit_map.get(accounting_unit_id, {})
        product.product_code = pro_fields.get('code', '')
        product.product_name = pro_fields.get('name', '')
        product.category_id = convert_to_int(pro_fields.get('category'))
        product.category_name = category_map.get(product.category_id, {}).get('name', '')
        product.unit_id = unit_id
        product.unit_spec = unit.get("code", "")
        product.unit_name = unit.get('name', "")
        product.unit_rate = unit_rate
        product.accounting_unit_id = accounting_unit_id
        product.accounting_unit_name = accounting_unit.get('name')
        product.accounting_unit_spec = accounting_unit.get("code")
        product.storage_type = pro_fields.get('storage_type', '')

    def create_franchisee_demand(self, partner_id, user_id, request):
        jjy_partner_ids = APP_CONFIG.get('jjy_partner_ids','').split(',')
        demand_date = self.utcTimestamp2datetime(request.demand_date) \
            if str(partner_id) not in jjy_partner_ids else self.get_bus_date()
        received_by = request.received_by
        products = request.products
        batch_id = request.batch_id
        # print("products=====",products)
        if demand_date.year == 1970:
            raise DataValidationException("请填写订货日期")
        if batch_id:
            if_exist = sFD.get_demand_by_id(partner_id=partner_id, request_id=batch_id)
            if if_exist:
                raise DemandError("请勿重复提交订货单！")
        else:
            raise DemandError("缺少唯一请求号，不允许创建！")
        if not products:
            raise ProductError("订单须包含商品")
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.frs_store', branch_id=received_by)
        # todo: 查询校验门店订货开关、订货类型等一堆垃圾配置
        order_switch = metadata_service.list_entity(schema_name=SchemaName.ORDER_SWITCH.code,
                                                    partner_id=partner_id, user_id=user_id,
                                                    filters={"store_id__in": [received_by],
                                                             "status__eq": "ENABLED"}).get('rows', [])
        if not order_switch:
            # 门店未配置订货开关默认开启
            pass
        else:
            order_switch = order_switch[0]
            fields = order_switch.get('fields') if order_switch.get('fields') else {}
            if fields.get('order_status') != "ENABLED":
                raise DemandError("门店订货开关限制不允许订货")
            if not fields.get('credit_status') or fields.get('') != "ENABLED":
                raise DemandError("门店订货开关信用额度禁用不允许订货")

        # 订货类型校验
        order_type = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id, id=request.order_type_id,
                                                       schema_name=SchemaName.ORDER_TYPE.code)
        if not order_type:
            raise DataValidationException("订货类型主档未找到")
        allow_multiple_order = order_type.get('fields', {}).get('allow_multiple_order')
        if not allow_multiple_order:
            exist_demand = sFD.list_f_demand(partner_id=partner_id, start_date=demand_date, end_date=demand_date,
                                             received_bys=[received_by], order_type_ids=[request.order_type_id],
                                             status=self.valid_status, types=[Demand_type.FSD.code])
            if len(exist_demand) > 0:
                raise DemandError("订货类型配置一天只允许创建一张订货单")
        product_ids = set()
        category_ids = set()
        unit_ids = set()
        all_products = {}
        ids = get_uuids(len(products))
        key = namedtuple('key', ['id', 'type', 'relation_type', 'father_id'])
        for i, p in enumerate(products):
            product_ids.add(convert_to_int(p.product_id))
            category_ids.add(convert_to_int(p.category_id))
            unit_ids.add(convert_to_int(p.unit_id))
            r_ids = get_uuids(len(p.relation_products)) if p.relation_products else None
            for ri, rp in enumerate(p.relation_products):
                product_ids.add(convert_to_int(rp.product_id))
                category_ids.add(convert_to_int(rp.category_id))
                unit_ids.add(convert_to_int(rp.unit_id))
                all_products[key(r_ids[ri], 'bind', '', ids[i])] = rp
                # all_products[(r_ids[ri], ids[i])] = rp
            if p.relation_products:
                all_products[key(ids[i], 'main', 'BIND', None)] = p
                continue

            all_products[key(ids[i], 'main', '', None)] = p
        # 门店
        branch_map = get_branch_map(branch_ids=[received_by], branch_type="STORE", partner_id=partner_id, user_id=user_id,
                                    return_fields="id,code,name,type,chain_type,franchisee")
        store_info = branch_map.get(received_by, {})
        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type",
                                      partner_id=partner_id, user_id=user_id)
        #校验商品订货类型
        if not request.order_type_id:
            raise DemandError("无订货类型不允许创建订货单")
        order_type_products = products_manage_service.query_order_type_product(store_id=received_by,
                                                                  type_id=request.order_type_id,
                                                                  partner_id=partner_id,
                                                                  user_id=user_id).get('rows',[])
        order_type_product_ids = []
        for order_type_product in order_type_products:
            order_type_product_ids.append(int(order_type_product['product_id']))
        # 商品类别
        category_map = get_category_map(category_ids, partner_id, user_id)
        # 单位
        unit_map = get_unit_map(unit_ids=unit_ids, partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        if not store_info.get('franchisee'):
            raise DataValidationException(f"门店：{store_info.get('name', '')} 未配置加盟商，无法订货！")
        franchisee_id = convert_to_int(store_info.get('franchisee'))
        supply_demand = dict(
            id=get_guid(),
            partner_id=partner_id,
            code=Supply_doc_code.get_code_by_type('FD', partner_id, None),
            received_by=received_by,
            received_name=store_info.get('name'),
            received_code=store_info.get('code'),
            store_type=store_info.get('type'),
            chain_type=store_info.get('chain_type'),
            franchisee_id=franchisee_id,
            distribute_by=request.distribute_by,
            demand_date=demand_date,
            arrival_date=None,
            status='PREPARE',
            process_status='INITED',
            type=request.type if request.type else 'FSD',
            bus_type=request.bus_type if request.bus_type else 'MANUAL',
            order_type_id=request.order_type_id,
            payment_way=request.payment_way,
            created_by=user_id,
            updated_by=user_id,
            created_name=username,
            updated_name=username,
            batch_id=batch_id,
            remark=request.remark,
            reason=request.reason,
            sum_price_tax=Decimal(0),
            sum_tax=Decimal(0),
            sales_amount=Decimal(0),
            extends=request.extends,
        )
        demand_products = []
        demand_relation_products = []
        # ids = get_uuids(len(all_products))
        for k, p in all_products.items():
            is_main, ratio = k.type == 'main', None
            product_id = convert_to_int(p.product_id)
            product = product_map.get(str(product_id), {})
            units = product.get('units')
            unit_rate = 1
            unit_id = None
            accounting_unit_id = None
            if not is_main:
                ratio = Decimal(p.ratio) if p.ratio else p.ratio
                if not ratio or ratio < 0:
                    raise ProductError(f"捆绑商品id: {product_id}比率错误")
                    # raise ProductError(f"商品id: {all_products[(k[1],)].product_id}的捆绑商品id: {product_id}比率错误")

            if is_main and not p.quantity:
                raise ProductError("商品数量不能为空！")
            if units:
                order_flag = False
                for u in units:
                    if u.get("order"):
                        unit_rate = float(u.get("rate", 1))
                        unit_id = int(u.get('id'))
                        order_flag = True
                    if u.get('default'):
                        accounting_unit_id = int(u.get('id'))
                if not order_flag:
                    raise ProductError("此商品未设置订货单位，请检查主档设置, product_id:{}".format(product_id))
            else:
                raise ProductError("此商品未设置单位，请检查主档设置, product_id:{}".format(product_id))
            unit = unit_map.get(unit_id)
            if not unit:
                raise ProductError("主档未找到单位信息, unit_id:{}".format(unit_id))
            accounting_unit = unit_map.get(accounting_unit_id, {})
            quantity = convert_to_decimal(p.quantity) if p.quantity else convert_to_decimal(0)
            tax_price = convert_to_decimal(p.tax_price) if p.tax_price else convert_to_decimal(0)
            # 确定税率的存法: 经价格中心确认为百分号前，需要手动除以100计算
            tax_rate = convert_to_decimal(p.tax_rate) if p.tax_rate else convert_to_decimal(0)
            cost_price = tax_price / convert_to_decimal(1 + tax_rate / 100)
            tax_amount = (tax_price - cost_price) * quantity
            amount = (quantity * tax_price).quantize(Decimal('0.00'), ROUND_HALF_UP)
            supply_demand["sum_price_tax"] += amount
            supply_demand["sum_tax"] += tax_amount
            sales_price = Decimal(0)
            if p.extends:
                extends = json.loads(p.extends)
                product_price = extends.get('product_price', [])
                for price in product_price:
                    if price.get('price_type_id') == "2":
                        sales_price = convert_to_decimal(price.get('tax_price', 0))
            sales_amount = (quantity * sales_price).quantize(Decimal('0.00'), ROUND_HALF_UP)
            supply_demand["sales_amount"] += sales_amount
            product_dict = {
                "id": k.id,
                "partner_id": partner_id,
                "demand_id": supply_demand["id"],
                "status": "INITED",
                "product_id": int(product_id),
                "product_code": product.get("code"),
                "product_name": product.get("name"),
                "category_id": int(product.get('category', 0)),
                "category_name": category_map.get(int(product.get('category', 0)), {}).get('name', ''),
                "arrival_days": int(p.arrival_days) if p.arrival_days and p.arrival_days.isdecimal() else p.arrival_days,
                "unit_id": int(p.unit_id),
                "unit_spec": unit.get("code", "无"),
                "unit_name": unit.get('name', "无"),
                "unit_rate": unit_rate,
                'accounting_unit_id': accounting_unit_id,
                'accounting_unit_name': accounting_unit.get('name'),
                'accounting_unit_spec': accounting_unit.get("code"),
                "quantity": quantity,
                "accounting_quantity": convert_to_decimal(unit_rate) * quantity,
                "min_quantity": p.min_quantity,
                "max_quantity": p.max_quantity,
                "increment_quantity": p.increment_quantity,
                "tax_price": tax_price,
                "sales_price": sales_price,
                "cost_price": cost_price,
                "tax_rate": tax_rate,
                "amount": amount,
                "sales_amount": sales_amount,
                "distribute_by": p.distribute_by,
                "distribution_type": p.distribution_type,
                "storage_type": product.get('storage_type'),
                "created_by": user_id,
                "updated_by": user_id,
                "created_name": username,
                "updated_name": username,
                "extends": p.extends,
                "approve_quantity": quantity,
                "confirm_quantity": quantity,
                "approve_amount": amount,
                "confirm_amount": amount,
                "approve_sales_amount": sales_amount,
                "confirm_sales_amount": sales_amount,
                'relation_type': k.relation_type
            }
            if is_main and product_id not in order_type_product_ids:
                raise ProductError("商品{}订货类型与单据不一致！".format(product_dict['product_name']))
            # print("p.arrival_days===",p.arrival_days)
            # if p.arrival_days and p.arrival_days.isdigit():
            #     product_dict["arrival_days"] = int(p.arrival_days)
            # else:
            #     product_dict["arrival_days"] = None
            # print('demand_products======',demand_products)
     
            if not is_main:
                product_dict.update({'demand_product_id': k.father_id,
                                     'ratio': ratio,
                                     'configure': p.configure})
            # print(product_dict)
            demand_products.append(product_dict) if is_main else demand_relation_products.append(product_dict)
        demand_log = dict(
            partner_id=partner_id,
            demand_id=supply_demand["id"],
            action=Action.PREPARE.code,
            action_name=Action.PREPARE.desc,
            end_status=supply_demand['status'],
            platform=self.platform,
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow(),
            trace_id=get_guid()
        )
        if not supply_demand["sum_price_tax"]:
            action = SupplyPartnerAction.get_to_action(partner_id, PartnerActionModule.DEMAND.code, "SUBMITTED")
            supply_demand['pay_amount'] = 0
            supply_demand['status'] = action
            demand_log['end_status'] = action

        sFD.create_f_demand([supply_demand], demand_products, [demand_log], demand_relation_products)
        return {'demand_id': supply_demand.get('id'), 'total_amount': supply_demand['sum_price_tax']}

    def list_franchisee_demand(self, request, partner_id, user_id):
        res = dict()
        start_date = self.utcTimestamp2datetime(request.start_date)
        end_date = self.utcTimestamp2datetime(request.end_date)
        status = list(request.status) if request.status else []
        received_bys = [convert_to_int(_id) for _id in request.received_bys] if request.received_bys else []
        types = [_type for _type in request.types] if request.types else []
        franchisee_ids = [convert_to_int(_id) for _id in request.franchisee_ids] if request.franchisee_ids else []
        payment_ways = [str(way) for way in request.payment_ways] if request.payment_ways else []
        order_type_ids = [convert_to_int(_id) for _id in request.order_type_ids] if request.order_type_ids else []
        bus_types = [bus_type for bus_type in request.bus_types] if request.bus_types else []
        code = request.code
        sort = request.sort if request.sort else 'updated_at'
        order = request.order
        limit = request.limit
        offset = request.offset
        include_total = request.include_total
        ids = list(request.ids)
        if len(ids) == 0:
            ids = None
        if not status:
            status = self.show_status
        received_bys = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                               domain='boh.frs_store', branch_ids=received_bys)
        product_ids = list(request.product_ids) if request.product_ids else []
        query_set = sFD.list_f_demand(partner_id=partner_id, start_date=start_date, received_bys=received_bys,
                                      franchisee_ids=franchisee_ids, payment_ways=payment_ways, types=types,
                                      order_type_ids=order_type_ids, end_date=end_date, bus_types=bus_types,
                                      status=status, code=code, offset=offset, limit=limit, ids=ids,
                                      include_total=include_total, order=order, product_ids=product_ids, sort=sort)
        if isinstance(query_set, tuple):
            total, demands = query_set
            res["total"] = total
        else:
            demands = query_set
        demand_list = []
        demand_ids = []
        for d in demands:
            if d.order_type_id not in order_type_ids:
                order_type_ids.append(d.order_type_id)
            demand_ids.append(d.id)

        if not demand_ids:
            return {}

        order_type_map = self.get_order_type_map(order_type_ids=order_type_ids, partner_id=partner_id,
                                                 user_id=user_id, return_fields="id,code,name,audit_time,time_bucket")
        # 查询商品名称
        pro_names = sFDP.query_product_name_by_demand_ids(demand_ids=demand_ids, partner_id=partner_id)
        pro_names_map = {}
        pro_count_map = {}
        if pro_names:
            for p in pro_names:
                if p[0] in pro_names_map.keys():
                    pro_count_map[p[0]] += 1
                    if len(pro_names_map[p[0]]) < 5:
                        pro_names_map[p[0]].append(p[1])
                else:
                    pro_names_map[p[0]] = [p[1]]
                    pro_count_map[p[0]] = 1
        # 查询退款单
        refund_map = {}
        refunds = sFR.query_refund_by_main_ids(partner_id=partner_id, main_ids=demand_ids)
        for rd in refunds:
            if rd[1] in refund_map.keys():
                refund_map[rd[1]].append(dict(
                    refund_id=rd[0],
                    refund_code=rd[3],
                    status=rd[4]
                ))
            else:
                refund_map[rd[1]] = [dict(refund_id=rd[0], refund_code=rd[3], status=rd[4])]

        # 查询收货单
        receives = receipt_service.list_receives(batch_ids=demand_ids, batch_type=[self.batch_type],
                                                 partner_id=partner_id, user_id=user_id)
        receive_map = dict()
        for rev in receives.rows:
            if rev.batch_id in receive_map.keys():
                receive_map[rev.batch_id].append(
                    dict(receive_id=rev.id,
                         receive_code=rev.code,
                         status=rev.status)
                )
            else:
                receive_map[rev.batch_id] = [dict(receive_id=rev.id, receive_code=rev.code, status=rev.status)]

        for demand in demands:
            extends = dict(
                product_names=pro_names_map.get(demand.id, []),
                product_count=pro_count_map.get(demand.id, 0)
            )
            order_type = order_type_map.get(demand.order_type_id, {})
            demand_return_fields = {
                'id': demand.id,
                'code': demand.code,
                'status': demand.status,
                'demand_date': Timestamp(seconds=int(demand.demand_date.timestamp())),
                'type': demand.type,
                'bus_type': demand.bus_type,
                'order_type_id': demand.order_type_id,
                'order_type_name': order_type.get('name'),
                'order_type_code': order_type.get('code'),
                'order_type_time': dict(order_time=order_type.get('time_bucket', []),
                                        audit_time=order_type.get('audit_time', [])),
                'remark': demand.remark,
                'sum_price_tax': demand.sum_price_tax,
                'sum_tax': demand.sum_tax,
                'sales_amount': demand.sales_amount,
                'confirm_amount': demand.confirm_amount,
                'confirm_sales_amount': demand.confirm_sales_amount,
                'approve_amount': demand.approve_amount,
                'approve_sales_amount': demand.approve_sales_amount,
                'reject_reason': demand.reject_reason,
                'refunds': refund_map.get(demand.id, []),
                'receives': receive_map.get(demand.id, []),
                'attachments': literal_eval(demand.attachments) if demand.attachments else [],
                'payment_way': demand.payment_way,
                'extends': json.dumps(extends, ensure_ascii=False)
            }
            demand_list.append(demand_return_fields)
        res["rows"] = demand_list
        # 不同状态单据排序，自上而下：待付款、待确认、已驳回、已取消、已确认、退款中、已退款
        # 同状态单据排序，自上而下：按单据创建时间从近到远
        # res['rows'] = sorted(res['rows'], key=lambda e: self.STATUS_ENUMS.index(e.__getitem__('status')))
        return res

    def get_demand_by_id(self, partner_id, user_id, request):
        demand_id = request.demand_id
        demand = sFD.get_demand_by_id(demand_id=demand_id, partner_id=partner_id)
        if not demand:
            raise OrderNotExistException("订货单不存在")
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.frs_store', branch_id=demand.received_by)
        branch_map = get_branch_map(branch_ids=[demand.franchisee_id], branch_type="FRANCHISEE", partner_id=partner_id,
                                    user_id=user_id, return_fields="id,code,name")
        franchisee = branch_map.get(demand.franchisee_id, {})
        order_type_map = self.get_order_type_map(order_type_ids=[demand.order_type_id], partner_id=partner_id,
                                                 user_id=user_id,
                                                 return_fields="id,code,name,audit_time,time_bucket,price_type")
        result = demand.serialize(conv=True)
        # 查询退款单
        result['refunds'] = []
        refunds = sFR.query_refund_by_main_ids(partner_id=partner_id, main_ids=[demand_id])
        for rd in refunds:
            result['refunds'].append(dict(
                refund_id=rd[0],
                refund_code=rd[3],
                status=rd[4]
            ))
        # 查询收货单
        result['receives'] = []
        receives = receipt_service.list_receives(batch_ids=[demand_id], batch_type=[self.batch_type],
                                                 partner_id=partner_id, user_id=user_id)
        for rev in receives.rows:
            result['receives'].append(
                dict(receive_id=rev.id,
                     receive_code=rev.code,
                     status=rev.status)
            )
        order_type = order_type_map.get(demand.order_type_id, {})
        result['franchisee_name'] = franchisee.get('name')
        result['franchisee_code'] = franchisee.get('code')
        result['order_type_name'] = order_type.get('name')
        result['order_type_code'] = order_type.get('code')
        result['order_type_time'] = dict(order_time=order_type.get('time_bucket', []),
                                         audit_time=order_type.get('audit_time', []))
        result['attachments'] = literal_eval(demand.attachments) if demand.attachments else []
        result['price_type'] = order_type.get('price_type')
        return result

    def list_product(self, request, partner_id, user_id):
        res = dict()
        demand_id = request.demand_id
        sort = request.sort if request.sort else 'updated_at'
        order = request.order
        limit = request.limit
        offset = request.offset
        # demand = sFD.get_demand_by_id(demand_id=demand_id, partner_id=partner_id)
        demand = session.query(sFD).filter_by(id=demand_id, partner_id=partner_id).first()
        if not demand:
            raise OrderNotExistException("订货单不存在")
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.frs_store', branch_id=demand.received_by)
        query_set = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id, limit=limit,
                                             sort=sort, order=order, offset=offset)
        if isinstance(query_set, tuple):
            total, demand_products = query_set
            res['total'] = total
        else:
            demand_products = query_set

        relation_products = demand.relation_products.filter(
            SFDPR.demand_product_id.in_(i.id for i in query_set if i.relation_type)).all()
        relation_products_dict = {}
        for p in relation_products:
            if p.demand_product_id in relation_products_dict:
                relation_products_dict[p.demand_product_id].append(p)
                continue
            relation_products_dict[p.demand_product_id] = [p]

        product_ids = []
        for p in demand_products + relation_products:
            if p.org_product_id and p.org_product_id not in product_ids:
                product_ids.append(p.org_product_id)
            if p.product_id and p.product_id not in product_ids:
                product_ids.append(p.product_id)
        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,model_name",
                                      partner_id=partner_id, user_id=user_id)
        product_list = []
        for product in demand_products:
            org_pro = product_map.get(str(product.org_product_id), {})
            pro = product_map.get(str(product.product_id), {})
            product_detail = {
                'id': product.id,
                'product_id': product.product_id,
                'product_code': product.product_code,
                'product_name': product.product_name,
                'org_product_id': product.org_product_id,
                'org_product_code': org_pro.get('code'),
                'org_product_name': org_pro.get('name'),
                'category_id': product.category_id,
                'category_name': product.category_name,
                'product_spec': pro.get('model_name'),
                'tax_price': product.tax_price,
                'cost_price': product.cost_price,
                'sales_price': product.sales_price,
                'quantity': product.quantity,
                'amount': product.amount,
                'sales_amount': product.sales_amount,
                'approve_quantity': product.approve_quantity,
                'approve_sales_amount': product.approve_sales_amount,
                'approve_amount': product.approve_amount,
                'is_approve_qty': product.is_approve_qty,
                'confirm_quantity': product.confirm_quantity,
                'confirm_amount': product.confirm_amount,
                'confirm_sales_amount': product.confirm_sales_amount,
                'is_confirm_qty': product.is_confirm_qty,
                'unit_name': product.unit_name,
                'min_quantity': product.min_quantity,
                'max_quantity': product.max_quantity,
                'increment_quantity': product.increment_quantity,
            }
            if product.id in relation_products_dict:
                product_detail['relation_products'] = [{
                    'id': rp.id,
                    'product_id': rp.product_id,
                    'product_code': rp.product_code,
                    'product_name': rp.product_name,
                    'org_product_id': rp.org_product_id,
                    'org_product_code': product_map.get(str(rp.org_product_id), {}).get('code'),
                    'org_product_name': product_map.get(str(rp.org_product_id), {}).get('name'),
                    'category_id': rp.category_id,
                    'category_name': rp.category_name,
                    'product_spec': product_map.get(str(product.product_id), {}).get('model_name'),
                    'tax_price': rp.tax_price,
                    'cost_price': rp.cost_price,
                    'sales_price': rp.sales_price,
                    'quantity': rp.quantity,
                    'amount': rp.amount,
                    'sales_amount': rp.sales_amount,
                    'approve_quantity': rp.approve_quantity,
                    'approve_sales_amount': rp.approve_sales_amount,
                    'approve_amount': rp.approve_amount,
                    'ratio': str(rp.ratio or ''),
                    'confirm_quantity': rp.confirm_quantity,
                    'confirm_amount': rp.confirm_amount,
                    'confirm_sales_amount': rp.confirm_sales_amount,
                    'configure': rp.configure,
                    'unit_name': rp.unit_name,
                } for rp in relation_products_dict[product.id]]

            product_list.append(product_detail)


        res["rows"] = product_list
        return res

    def update_products(self, partner_id, user_id, request):
        """大掌柜更新订单商品，同时单据状态变更仅支持审核订单"""
        result = {}
        demand_id = request.demand_id
        updated_products = request.products
        action = request.status if request.status else Action.UPDATE.code
        demand_db = sFD.get_demand_by_id(demand_id=demand_id, partner_id=partner_id)
        if not demand_db:
            raise OrderNotExistException("订货单不存在")
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.frs_store', branch_id=demand_db.received_by)
        # 订单更新状态校验
        if demand_db.status not in ["REJECTED", "SUBMITTED"]:
            raise DataValidationException("当前状态不允许更新！")

        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        product_ids, main_product_dict = set(), {}
        for i in updated_products:
            product_ids.add(i.product_id)
            main_product_dict[int(i.product_id)] = Decimal(i.unit_rate)
            if i.relation_products:
                product_ids |= set(r.product_id for r in i.relation_products)
        # print(product_ids)
        # product_ids = [p.product_id for p in updated_products]
        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type,model_name",
                                      partner_id=partner_id, user_id=user_id)
        category_ids = []
        for _, p in product_map.items():
            if p.get('category'):
                category_ids.append(int(p.get('category')))
        # 商品类别
        category_ret = metadata_service.get_product_category_list(ids=category_ids, return_fields='id,code,name',
                                                                  partner_id=partner_id, user_id=user_id)
        category_map = {}
        if category_ret and category_ret.get('rows'):
            for c in category_ret.get('rows'):
                category_map[int(c.get('id', 0))] = {
                    'code': c.get('code'),
                    'name': c.get('name')
                }
        # 单位
        unit_list = metadata_service.get_unit_list(partner_id=partner_id, user_id=user_id).get('rows', [])
        unit_map = {}
        if unit_list:
            for u in unit_list:
                unit_map[int(u['id'])] = u
        product_list = []
        relaiton_product_list = []
        approve_amount = Decimal(0)
        approve_sales_amount = Decimal(0)
        product_logs = []
        # demand_product_ids = []
        # 操作ID
        trace_id = get_guid()
        key = namedtuple('key', ['type', 'product'])
        products_deque = deque([key('main', updated_products[0])])
        index, product_len = 0, len(updated_products)
        # 返回校验信息
        check_messages = []

        valid_products = {}
        free_products = {}
        if action == "R_APPROVE":
            free_products = products_manage_service.query_free_order_limit(main_product_dict.keys(), partner_id,
                                                                                user_id).get('rows', [])
            free_products = {int(i['product_id']): Decimal(i['limit_qty']) for i in free_products if 'limit_qty' in i}

            valid_products = products_manage_service.get_order_rule_by_store(store_id=demand_db.received_by,
                                                                             product_ids=main_product_dict.keys(),
                                                                             partner_id=partner_id,
                                                                             user_id=user_id).get('rows', {})

            valid_products = {int(vp['product_id']): vp['order_rule'] for vp in valid_products
                              if 'order_rule' in vp and 'product_id' in vp}

        while products_deque:
        # for product in updated_products:
            p = products_deque.pop()
            product, is_main = p.product, p.type == 'main'
            if product.relation_products:
                products_deque.extend(key('bind', i) for i in product.relation_products[::-1])
            self._attach_product_attr(product, product_map, category_map, unit_map)

            if is_main:
                index += 1
                products_deque.append(key('main', updated_products[index])) if index < product_len else ...
                updated_args = {
                    'id': product.id,
                    "partner_id": partner_id,
                    "storage_type": product.storage_type,
                    'product_id': product.product_id,
                    'product_code': product.product_code,
                    'product_name': product.product_name,
                    'category_id': product.category_id,
                    'category_name': product.category_name,
                    "unit_id": product.unit_id,
                    "unit_spec": product.unit_spec,
                    "unit_name": product.unit_name,
                    "unit_rate": product.unit_rate,
                    'accounting_unit_id': product.accounting_unit_id,
                    'accounting_unit_name': product.accounting_unit_name,
                    'accounting_unit_spec': product.accounting_unit_spec,
                    'updated_by': user_id,
                    'updated_name': operator_name,
                }
            else:
                updated_args = {'id': product.id}
            # 待审核状态更新审核数量
            if demand_db.status == "SUBMITTED":
                # demand_product_ids.append(product.id)
                updated_args['approve_quantity'] = product.approve_quantity
                updated_args['approve_amount'] = (
                    convert_to_decimal(product.tax_price) * convert_to_decimal(
                        product.approve_quantity)).quantize(Decimal('0.00'), ROUND_HALF_UP)
                updated_args['approve_sales_amount'] = (
                    convert_to_decimal(product.sales_price) * convert_to_decimal(
                        product.approve_quantity)).quantize(Decimal('0.00'), ROUND_HALF_UP)

                # 确认数量要随着审核数量联动
                updated_args['confirm_quantity'] = updated_args['approve_quantity']
                updated_args['confirm_amount'] = updated_args['approve_amount']
                updated_args['confirm_sales_amount'] = updated_args['approve_sales_amount']

                approve_amount += updated_args['approve_amount']
                approve_sales_amount += updated_args['approve_sales_amount']
                updated_args['is_approve_qty'] = True
            # TODO: 校验商品是否符合订货规则(起订量, 递增订量,最大订量)
            if is_main and action == "R_APPROVE" and product.approve_quantity:
                pass_check = False
                vp = valid_products.get(product.product_id, {})
                if vp or (not vp and not product.tax_price):
                    if product.product_id in free_products:
                        unit_rate = Decimal(product.unit_rate or 0)
                        if unit_rate:
                            free_products[product.product_id] = free_products[product.product_id] / unit_rate

                    max_qty = -Decimal(vp.get('max_qty') or 0)
                    if not product.tax_price:
                        if product.product_id in free_products:
                            free_max_qty = -Decimal(free_products[product.product_id] or 0)
                            max_qty = max(max_qty, free_max_qty) if \
                                vp.get('max_qty') and product.product_id in free_products else min(max_qty, free_max_qty)
                        elif not vp:
                            pass_check = True

                    max_qty = -max_qty
                    check = check_quantity(product.approve_quantity, vp.get('min_qty'), vp.get('increase_qty'),
                                           max_qty) if vp else (pass_check or product.approve_quantity <= max_qty)

                    if not check:
                        msg = {"product_name": product.product_name,
                               "product_code": product.product_code,
                               "spec": product_map.get(str(product.product_id), {}).get("model_name", ""),
                               "quantity": product.approve_quantity,
                               "min_quantity": float(vp.get('min_qty', 0)),
                               "increment_quantity": float(vp.get('increase_qty', 0)),
                               "max_quantity": float(max_qty), "unit": product.unit_name,
                               "tax_price": product.tax_price, "amount": round(
                                convert_to_decimal(product.tax_price) * convert_to_decimal(
                                    product.approve_quantity), 2), "storage_type": product.storage_type,
                               'id': product.id,
                               'product_id': product.product_id}
                        check_messages.append(msg)

            if is_main:
                product_logs.append(dict(
                    partner_id=partner_id,
                    trace_id=trace_id,
                    demand_id=demand_id,
                    action=action,
                    action_name=Action.get_desc(action),
                    product_id=product.product_id,
                    product_code=product.product_code,
                    product_name=product.product_name,
                    unit_id=product.unit_id,
                    unit_name=product.unit_name,
                    tax_price=str(product.tax_price),
                    sales_price=str(product.sales_price),
                    approve_quantity=str(product.approve_quantity),
                    approve_amount=updated_args['approve_amount'],
                    approve_sales_amount=updated_args['approve_sales_amount'],
                    created_by=user_id,
                    created_name=operator_name,
                ))
                product_list.append(updated_args)
            else:
                relaiton_product_list.append(updated_args)
        if check_messages:
            result["msgs"] = check_messages
            return result
        demand_log = dict(
            partner_id=partner_id,
            demand_id=demand_id,
            action=action,
            action_name=Action.get_desc(action),
            start_status=demand_db.status,
            end_status=action,
            platform=self.platform,
            created_by=user_id,
            created_name=operator_name,
            created_at=datetime.utcnow(),
            trace_id=trace_id
        )
        # 总部审核订货单
        if action == "R_APPROVE":
            demand = dict(
                id=demand_id,
                partner_id=partner_id,
                approve_amount=approve_amount,
                approve_sales_amount=approve_sales_amount,
                updated_by=user_id,
                updated_name=operator_name,
                status=action
            )
            demand_log['start_status'] = demand_db.status
            demand_log['end_status'] = action
            res = sFD.update_f_demand(update_data=demand, demand_logs=[demand_log], allow_status=["SUBMITTED"],
                                      product_list=product_list, product_logs=product_logs,
                                      relation_products=relaiton_product_list)
        else:
            res = sFDP.update_products(product_list, demand_logs=[demand_log], product_logs=product_logs,
                                       relation_products=relaiton_product_list)
        if res is True:
            result['demand_id'] = demand_id
        return result

    def deal_demand_by_ids(self, request, allow_status: list, partner_id=None, user_id=None):
        """变更订货单状态, 支持批量修改"""
        ret = {}
        action = request.action
        demand_ids = [int(_id) for _id in request.demand_ids] if request.demand_ids else []
        if len(demand_ids) <= 0:
            raise DataValidationException("订单ID不可为空！")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        demand_logs = []
        refund_detail = None
        refund_products = None
        refund_log = None
        if len(demand_ids) > 1:
            raise StatusUnavailable("移动端暂不支持批量操作！")
        else:
            demand_id = demand_ids[0]
            demand_db = sFD.get_demand_by_id(partner_id=partner_id, demand_id=demand_id)
            if not demand_db:
                raise OrderNotExistException("订单不存在-{}".format(demand_id))
            update_data = dict(
                id=demand_id,
                partner_id=partner_id,
                updated_by=user_id,
                updated_name=username,
                status=action,
                remark=request.remark
            )
            if action == "P_SUBMIT":
                update_data["attachments"] = str(request.attachments) if request.attachments else ''
                update_data['pay_amount'] = demand_db.sum_price_tax
            if action == "CANCELLED" and demand_db.status in ["SUBMITTED", "R_APPROVE"] and demand_db.pay_amount:
                # 创建单据退款记录
                refund_detail, refund_products, refund_log = self.build_refund_details(demand_db, partner_id, user_id,
                                                                                       username)
            demand_logs.append(dict(
                partner_id=partner_id,
                demand_id=demand_id,
                action=action,
                action_name=Action.get_desc(action),
                start_status=demand_db.status,
                end_status=action,
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
                trace_id=get_guid()
            ))
        try:
            sync_approve = True if action == "R_APPROVE" else False
            res = sFD.update_f_demand(update_data=update_data, demand_logs=demand_logs, allow_status=allow_status,
                                      sync_approve=sync_approve, refund_detail=refund_detail, demand_ids=[demand_id],
                                      refund_products=refund_products, refund_log=refund_log)
            if res is True:
                ret["result"] = "success"
            else:
                ret['result'] = "failed"

            Kit.upload(partner_id, demand_db.code, "加盟订货单",
                       KitEnum.MOBILE_FRS_DEMAND_APPROVE.description,
                       actionStatus=res, storeId=demand_db.received_by,
                       storeName=demand_db.received_name,content="加盟订货单审核成功") if action == 'R_APPROVE' else ...

        except Exception as e:
            Kit.upload(partner_id, demand_db.code, "加盟订货单",
                       KitEnum.MOBILE_FRS_DEMAND_APPROVE.description, traceback.format_exc(),
                       actionStatus=False, storeId=demand_db.received_by,
                       storeName=demand_db.received_name,content="加盟订货单审核失败:"+str(e)) if action == 'R_APPROVE' else ...
            raise e

        if refund_detail:
            res = credit_pay_service.Refund(refund_ids=[refund_detail['id']], partner_id=partner_id, user_id=user_id)
            logging.info(f'{refund_detail["id"]}: res->{res}')

        return ret

    def get_demand_history(self, request, partner_id, user_id):
        """订货历史记录"""
        res = {}
        demand_id = request.demand_id
        total, logs = FdLogDB.get_franchisee_demand_log(demand_id=demand_id, partner_id=partner_id)
        res["rows"] = []
        res["total"] = total
        user_ids = [log.created_by for log in logs]
        user_dict = get_username_map(partner_id=partner_id, user_id=user_id, ids=user_ids)
        for log in logs:
            row = dict(
                id=log.id,
                status=log.end_status,
                created_at=self.get_timestamp(log.created_at),
                created_name=user_dict.get(log.created_by),
                created_by=log.created_by
            )
            res["rows"].append(row)
        return res

    def build_new_product(self, pro, product_map: dict, price_map: dict, unit_map: dict, category_map: dict,
                          partner_id=None, user_id=None, username=None):
        """构建新增商品参数"""
        product = product_map.get(str(pro.product_id), {})
        if not product:
            raise ProductError(f"商品主档未找到-{pro.product_id}")
        # pro_price = price_map.get(pro.product_id)
        # if not pro_price:
        #     raise ProductError(f"商品未配置价格-{pro.product_id}")
        units = product.get('units')
        unit_id = None
        accounting_unit_id = None
        unit_rate = 1
        if units:
            order_flag = False
            for u in units:
                if u.get("order"):
                    unit_rate = float(u.get("rate", 1))
                    unit_id = int(u.get('id'))
                    order_flag = True
                if u.get('default'):
                    accounting_unit_id = int(u.get('id'))
            if not order_flag:
                raise ProductError("此商品未设置订货单位，请检查主档设置, product_id:{}".format(pro.product_id))
        else:
            raise ProductError("此商品未设置单位，请检查主档设置, product_id:{}".format(pro.product_id))
        unit = unit_map.get(unit_id, {})
        accounting_unit = unit_map.get(accounting_unit_id, {})
        tax_price = convert_to_decimal(pro.tax_price) if pro.tax_price else Decimal(0)
        tax_rate = convert_to_decimal(pro.tax_rate) if pro.tax_rate else Decimal(0)
        cost_price = tax_price / convert_to_decimal(1 + tax_rate / 100)
        sales_price = convert_to_decimal(pro.sales_price) if pro.sales_price else Decimal(0)
        # pro_price_list = pro_price.get('product_price', [])
        # sales_price = Decimal(0)
        # for price in pro_price_list:
        #     if price.get('price_type_id') == "2":
        #         sales_price = convert_to_decimal(price.get('tax_price', 0))
        approve_quantity = convert_to_decimal(pro.approve_quantity)
        approve_amount = (tax_price * approve_quantity).quantize(Decimal('0.00'), ROUND_HALF_UP)
        approve_sales_amount = (sales_price * approve_quantity).quantize(Decimal('0.00'), ROUND_HALF_UP)
        new_pro = {
            "partner_id": partner_id,
            "status": "INITED",
            "product_id": int(pro.product_id),
            "product_code": product.get("code"),
            "product_name": product.get("name"),
            "category_id": int(product.get('category', 0)),
            "category_name": category_map.get(int(product.get('category', 0)), {}).get('name', ''),
            # "arrival_days": pro.arrival_days,
            "unit_id": unit_id,
            "unit_spec": unit.get("code", "无"),
            "unit_name": unit.get('name', "无"),
            "unit_rate": unit_rate,
            'accounting_unit_id': accounting_unit_id,
            'accounting_unit_name': accounting_unit.get('name'),
            'accounting_unit_spec': accounting_unit.get("code"),
            "approve_quantity": approve_quantity,
            "is_approve_qty": True,
            "approve_amount": approve_amount,
            "approve_sales_amount": approve_sales_amount,
            "min_quantity": pro.min_quantity,
            "max_quantity": pro.max_quantity,
            "increment_quantity": pro.increment_quantity,
            "tax_price": tax_price,
            "sales_price": sales_price,
            "cost_price": cost_price,
            "tax_rate": tax_rate,
            "storage_type": product.get('storage_type'),
            "created_by": user_id,
            "updated_by": user_id,
            "created_name": username,
            "updated_name": username,
            "extends": pro.extends
        }
        if pro.arrival_days and pro.arrival_days.isdigit():
            new_pro["arrival_days"] = int(pro.arrival_days)
        else:
            new_pro["arrival_days"] = None
        return new_pro

    def add_demand_product(self, partner_id, user_id, request):
        """小程序`待审核`状态下添加商品，支持带数量"""
        demand_db = sFD.get_demand_by_id(demand_id=request.demand_id, partner_id=partner_id)
        if not demand_db:
            raise OrderNotExistException("订货单不存在")
        result = {"demand_id": demand_db.id}
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.frs_store', branch_id=demand_db.received_by)
        # 状态校验: 待审核状态下支持添加商品
        if demand_db.status not in ["SUBMITTED"]:
            raise DataValidationException("当前状态不允许新增商品！")
        if len(request.products) == 0:
            raise DataValidationException("添加商品不允许为空")
        bind_products = dict()  # 将捆绑商品的数量合并  这里默认同一个捆绑商品在不同商品组合里价格一致
        product_ids = []
        for pro in request.products:
            if int(pro.product_id) not in product_ids:
                product_ids.append(int(pro.product_id))
            if not pro.bind_products:
                continue
            for p in pro.bind_products:
                if int(p.product_id) in bind_products.keys():
                    bind_products[int(p.product_id)].approve_quantity += p.approve_quantity
                else:
                    bind_products[int(p.product_id)] = p
                if int(p.product_id) not in product_ids:
                    product_ids.append(int(p.product_id))

        exist_products = sFDP.query_exist_product_by_demand_id(demand_id=request.demand_id, partner_id=partner_id)
        exist_pro_map = dict()
        for p in exist_products:
            exist_pro_map[p[0]] = dict(
                id=p[1],
                quantity=p[2],
                approve_quantity=p[3],
                is_approve_qty=p[4],
                tax_price=p[7],
                sales_price=p[8],
                product_code=p[9],
                product_name=p[10],
                unit_id=p[11],
                unit_name=p[12]
            )
        exist_product_ids = list(exist_pro_map.keys())
        # 剔除订单里存在的商品 再请求主档和价格中心
        product_ids = list(set(product_ids) - set(exist_product_ids))

        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type",
                                      partner_id=partner_id, user_id=user_id)
        category_ids = []
        for _, p in product_map.items():
            if p.get('category'):
                category_ids.append(int(p.get('category')))
        category_map = get_category_map(category_ids, partner_id, user_id)
        unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
        # # 查询商品价格(由于捆绑商品价格和常规商品不一样，后面需要改成让前端传)
        # price_map = self.get_product_price_map(partner_id=partner_id, user_id=user_id, store_id=demand_db.received_by,
        #                                        order_type_id=demand_db.order_type_id, product_ids=product_ids)
        price_map = None
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        ids = get_uuids(len(request.products))
        trace_id = get_guid()
        add_product_map = dict()
        update_products = []
        product_logs = []
        for inx, pro in enumerate(request.products):
            if pro.product_id in exist_product_ids:
                continue
            new_pro = self.build_new_product(pro, product_map, price_map, unit_map, category_map, partner_id=partner_id,
                                             user_id=user_id, username=username)
            new_pro['id'] = ids[inx]
            new_pro['demand_id'] = demand_db.id
            if int(pro.product_id) in add_product_map:
                add_product_map[int(pro.product_id)]['approve_quantity'] += new_pro.get('approve_quantity')
            else:
                add_product_map[int(pro.product_id)] = new_pro
        for product_id, pro in bind_products.items():
            # 存在合并
            if exist_pro_map.get(product_id):
                exist_pro = exist_pro_map.get(product_id)
                if exist_pro.get('is_approve_qty'):
                    approve_quantity = convert_to_decimal(pro.approve_quantity) + exist_pro.get('approve_quantity')
                else:
                    approve_quantity = convert_to_decimal(pro.approve_quantity) + exist_pro.get('quantity')
                tax_price = exist_pro.get('tax_price')
                sales_price = exist_pro.get('sales_price')
                approve_amount = (tax_price * approve_quantity).quantize(Decimal('0.00'), ROUND_HALF_UP)
                approve_sales_amount = (sales_price * approve_quantity).quantize(Decimal('0.00'), ROUND_HALF_UP)
                update_products.append(dict(
                    id=exist_pro.get('id'),
                    approve_quantity=approve_quantity,
                    approve_amount=approve_amount,
                    approve_sales_amount=approve_sales_amount,
                    updated_by=user_id,
                    updated_name=username
                ))
                product_logs.append(dict(
                    partner_id=partner_id,
                    trace_id=trace_id,
                    demand_id=demand_db.id,
                    action=Action.ADD_PRODUCT.code,
                    action_name=Action.ADD_PRODUCT.desc,
                    product_id=product_id,
                    product_code=exist_pro.get('product_code'),
                    product_name=exist_pro.get('product_name'),
                    unit_id=exist_pro.get('unit_id'),
                    unit_name=exist_pro.get('unit_name'),
                    tax_price=str(tax_price),
                    sales_price=str(sales_price),
                    approve_quantity=str(pro.approve_quantity),
                    approve_amount=(tax_price * convert_to_decimal(pro.approve_quantity)).quantize(Decimal('0.00'), ROUND_HALF_UP),
                    approve_sales_amount=(sales_price * convert_to_decimal(pro.approve_quantity)).quantize(Decimal('0.00'), ROUND_HALF_UP),
                    created_by=user_id,
                    created_name=username,
                    created_at=datetime.utcnow(),
                ))
            # 不存在插入
            else:
                new_pro = self.build_new_product(pro, product_map, price_map, unit_map, category_map,
                                                 partner_id=partner_id,
                                                 user_id=user_id, username=username)
                new_pro['id'] = get_guid()
                new_pro['demand_id'] = demand_db.id
                if int(pro.product_id) in add_product_map:
                    add_product_map[int(pro.product_id)]['approve_quantity'] += new_pro.get('approve_quantity')
                else:
                    add_product_map[int(pro.product_id)] = new_pro
        if len(add_product_map) == 0 and len(update_products) == 0:
            return result
        for _, pro in add_product_map.items():
            product_logs.append(dict(
                partner_id=partner_id,
                trace_id=trace_id,
                demand_id=demand_db.id,
                action=Action.ADD_PRODUCT.code,
                action_name=Action.ADD_PRODUCT.desc,
                product_id=pro.get('product_id'),
                product_code=pro.get('product_code'),
                product_name=pro.get('product_name'),
                unit_id=pro.get('unit_id'),
                unit_name=pro.get('unit_name'),
                tax_price=str(pro.get('tax_price', '')),
                sales_price=str(pro.get('sales_price', '')),
                approve_quantity=str(pro.get('approve_quantity', '')),
                approve_amount=str(pro.get('approve_amount', '')),
                approve_sales_amount=str(pro.get('approve_sales_amount', '')),
                created_by=user_id,
                created_name=username,
                created_at=datetime.utcnow(),
            ))
        demand_log = dict(
            partner_id=partner_id,
            demand_id=demand_db.id,
            action=Action.ADD_PRODUCT.code,
            action_name=Action.ADD_PRODUCT.desc,
            platform=self.platform,
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow(),
            trace_id=trace_id
        )
        _ = sFDP.add_demand_product(add_products=list(add_product_map.values()), update_products=update_products,
                                    demand_logs=[demand_log], product_logs=product_logs)

        return result

    def query_demand_report(self, request, partner_id, user_id):
        """查询门店订货清单报表"""
        start_date = self.timestamp2datetime(request.start_date)
        end_date = self.timestamp2datetime(request.end_date)
        received_bys = [convert_to_int(_id) for _id in request.received_bys] if request.received_bys else []
        today = self.get_bus_date()
        valid_status = ["SUBMITTED", "R_APPROVE", "CONFIRMED", "APPROVING", "APPROVED"]
        # 数据权限校验
        received_bys = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                               domain='boh.frs_store', branch_ids=received_bys)
        full_access = not received_bys
        # logger.info("received_bys: {}".format(received_bys))
        query_set = sFD.query_demand_report(partner_id=partner_id, start_date=start_date, end_date=end_date,
                                            status=valid_status, received_bys=received_bys, limit=request.limit,
                                            offset=request.offset, order=request.order, sort=request.sort)
        demand_summary = sFD.query_today_demand_summary(partner_id=partner_id, today=today, status=valid_status,
                                                        received_bys=received_bys)
        res = dict(rows=[], total_amount=0, total_demand=0, demand_price=0, sales_amount=0)
        demand_stores = []
        for row in query_set:
            demand_stores.append(row[0])
            res['rows'].append(dict(
                received_by=row[0],
                received_code=row[1],
                received_name=row[2],
                amount=row[3],
                sales_amount=row[4],
                total_demand=row[5],
            ))
        if demand_summary:
            res['total_amount'] = demand_summary.amount
            res['total_demand'] = demand_summary.total_demand
            res['sales_amount'] = demand_summary.sales_amount
            res['demand_price'] = res['total_amount'] / res['total_demand'] if res['total_demand'] else 0
        # 取出未订货门店
        demand_stores_set = set(demand_stores)
        no_demand_stores = list(set(received_bys) - demand_stores_set)
        if no_demand_stores or full_access:
            branch_map = get_branch_map(branch_ids=no_demand_stores, branch_type="STORE", partner_id=partner_id,
                                        user_id=user_id, allow_all=full_access)
            no_demand_stores = no_demand_stores or {k: v for k, v in branch_map.items() if k not in demand_stores_set}
            for store_id in no_demand_stores:
                res['rows'].append(dict(
                    received_by=store_id,
                    received_code=branch_map.get(store_id, {}).get('code'),
                    received_name=branch_map.get(store_id, {}).get('name'),
                    amount=0,
                    confirm_amount=0,
                    sales_amount=0,
                    confirm_sales_amount=0,
                    total_demand=0,
                ))
        return res

    def build_refund_details(self, demand_db: sFD, partner_id, user_id, username):
        """构建仅退款退款单数据结构
        ps: 总部分配订货单不生成退款单"""
        if demand_db.type == Demand_type.FMD.code:
            return None, None, None
        refund_id = get_guid()
        refund_products = []
        demand_products = sFDP.list_demand_product(demand_id=demand_db.id, partner_id=partner_id)
        refund_detail = dict(
            id=refund_id,
            partner_id=partner_id,
            type=RefundType.ORDER_REFUND.code,
            batch_id=demand_db.id,
            main_id=demand_db.id,
            main_code=demand_db.code,
            main_type=self.batch_type,
            code=Supply_doc_code.get_code_by_type('FDR', partner_id, None),
            received_by=demand_db.received_by,
            franchisee_id=demand_db.franchisee_id,
            trade_company=self.get_trade_company_by_franchisee(demand_db.franchisee_id, partner_id, user_id),
            payment_way=self.get_refund_way(demand_db.payment_way),
            refund_date=datetime.utcnow(),
            status='INITED',
            process_status='INITED',
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            refund_amount=demand_db.pay_amount
        )
        refund_log = dict(
            id=get_guid(),
            partner_id=partner_id,
            refund_id=refund_id,
            action='INITED',
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow()
        )
        ids = get_uuids(len(demand_products))
        for inx, p in enumerate(demand_products):
            refund_products.append(dict(
                id=ids[inx],
                refund_id=refund_id,
                partner_id=partner_id,
                product_id=p.product_id,
                product_code=p.product_code,
                product_name=p.product_name,
                category_id=p.category_id,
                unit_id=p.unit_id,
                unit_name=p.unit_name,
                unit_rate=p.unit_rate,
                unit_spec=p.unit_spec,
                quantity=p.quantity,
                tax_price=p.tax_price,
                cost_price=p.cost_price,
                tax_rate=p.tax_rate,
                amount=p.amount,
                created_by=user_id,
                created_name=username,
                updated_by=user_id,
                updated_name=username
            ))
        return refund_detail, refund_products, refund_log

    def add_shopping_cart_cache(self, request, partner_id, user_id):
        """添加购物车缓存：
        redis缓存key: 服务名:租户ID:门店ID:订货类型ID
        redis缓存value: 商品列表
        """
        value = []
        for p in request.products:
            value.append(dict(
                product_id=p.product_id,
                quantity=p.quantity
            ))
        try:
            _ = redis_server.set_shopping_cart_cache(partner_id=partner_id, store_id=request.store_id,
                                                     order_type_id=request.order_type_id, value=value)
            return dict(result=True, description="购物车缓存设置成功")
        except Exception as e:
            logger.error("add_shopping_cart_cache Error: {}".format(e))
            return dict(result=False, description="设置购物车缓存失败")

    def get_shopping_cart_cache(self, request, partner_id, user_id):
        """查询购物车缓存"""
        res = dict()
        res['products'] = redis_server.get_shopping_cart_cache(partner_id=partner_id, store_id=request.store_id,
                                                               order_type_id=request.order_type_id)
        return res

    def check_demand_todo(self, request, partner_id, user_id):
        """检查订货待办任务：
        先校验业务配置看当前门店是否开启订货校验
        “当收未收”：订货日T日，指是否有预计收货日期为T-1前（包含T-1)的未收货单据
        “当盘未盘”：订货日T日，指是否有盘点单生成日期为T-1前（包含T-1)的未审核盘点单
        该开关仅对门店提交订货生效，不阻塞区经&计划改单、总部强配业务
        """
        if request.demand_id:
            demand_db = sFD.get_demand_by_id(demand_id=request.demand_id, partner_id=partner_id)
            if not demand_db:
                raise OrderNotExistException("订货单不存在")
            store_id = demand_db.received_by
            bus_date = demand_db.demand_date
        else:
            bus_date = self.timestamp2datetime(request.bus_date)
            if bus_date.year == 1970:
                bus_date = get_bus_date()
            store_id = request.store_id
        # end_date = bus_date - timedelta(days=1)     # T-1
        # 校验订货业务配置
        is_check = False
        receiving_check, stocktaking_check = False, False
        order_config = metadata_service.get_supply_config(partner_id=partner_id, domain='boh.store.order',
                                                          user_id=user_id)
        branch_method = order_config.get("branch_method")
        store_ids = order_config.get("store_ids", [])
        branch_ids = order_config.get("branch_ids", [])
        branch_type = order_config.get("branch_type", '')
        order_todo_receive = order_config.get("order_todo_receive", False)
        order_todo_stocktake = order_config.get("order_todo_stocktake", False)
        stocktake_branch_method = order_config.get("stocktake_branch_method")
        stocktake_store_ids = order_config.get("stocktake_store_ids", [])
        stocktake_branch_ids = order_config.get("stocktake_branch_ids", [])
        stocktake_branch_type = order_config.get("stocktake_branch_type", '')
        # 自定义门店范围, 否则就是全门店生效
        if branch_method == 'all_store':
            receiving_check = True
            is_check = True
        else:
            if str(store_id) in store_ids:
                receiving_check = True
                is_check = True
            else:
                if branch_ids:
                    relation_filters = {}
                    if branch_type == "branch_region":
                        relation_filters = {"branch_region": [str(b) for b in branch_ids]}
                    if branch_type == "geo_region":
                        relation_filters = {"geo_region": [str(b) for b in branch_ids]}
                    if branch_type == "franchisee_region":
                        relation_filters = {"franchisee_region": [str(b) for b in branch_ids]}
                    stores = metadata_service.get_store_list(relation_filters=relation_filters,
                                                             partner_id=partner_id,
                                                             return_fields='id',
                                                             user_id=user_id).get("rows", [])
                    for s in stores:
                        store_ids.append(s.get("id"))
                    if str(store_id) in store_ids:
                        is_check = True
                        receiving_check = True

        if stocktake_branch_method == 'all_store':
            stocktaking_check = True
            is_check = True
        else:
            if str(store_id) in stocktake_store_ids:
                stocktaking_check = True
                is_check = True
            else:
                if stocktake_branch_ids:
                    relation_filters = {}
                    if stocktake_branch_type == "branch_region":
                        relation_filters = {"branch_region": [str(b) for b in stocktake_branch_ids]}
                    if stocktake_branch_type == "geo_region":
                        relation_filters = {"geo_region": [str(b) for b in stocktake_branch_ids]}
                    if stocktake_branch_type == "franchisee_region":
                        relation_filters = {"franchisee_region": [str(b) for b in stocktake_branch_ids]}
                    stores = metadata_service.get_store_list(relation_filters=relation_filters,
                                                             partner_id=partner_id,
                                                             return_fields='id',
                                                             user_id=user_id).get("rows", [])
                    str_store_id = str(store_id)
                    for s in stores:
                        if s.get('id') == str_store_id:
                            is_check = True
                            stocktaking_check = True
                            break

        if not is_check:
            return {"handler": True}
        receiving_list = []
        stocktake_list = []

        if order_todo_receive and receiving_check:
            # 查询未完成收货单(待收货INITED)
            receiving = interactive_service.get_receiving_frs_store(store_id=store_id, end_date=bus_date,
                                                                    partner_id=partner_id, user_id=user_id)
            if receiving and isinstance(receiving, Iterable):
                for receiving_obj in receiving:
                    receiving_dict = dict(
                        id=receiving_obj.id,
                        code=receiving_obj.code,
                        status=receiving_obj.status,
                    )
                    receiving_list.append(receiving_dict)

        if order_todo_stocktake and stocktaking_check:
            # 查询门店未完成盘点单(新建，已提交，已驳回)
            list_stocktake = interactive_service.get_demand_todo_stocktake(partner_id=partner_id,
                                                                           store_id=store_id,
                                                                           end_date=bus_date,
                                                                           status=["INITED", "SUBMITTED",
                                                                                   "REJECTED"],
                                                                           branch_type="FRS_STORE")
            if isinstance(list_stocktake, list) and len(list_stocktake) > 0:
                for stocktake_obj in list_stocktake:
                    stocktake_list.append(dict(
                        id=stocktake_obj[0],
                        code=stocktake_obj[1],
                        status=stocktake_obj[3],
                        type=stocktake_obj[2] if stocktake_obj[4] == 'PLAN' else stocktake_obj[4]
                    ))
        result = dict()
        result['receiving'] = receiving_list
        result['stocktake'] = stocktake_list
        if len(receiving_list) == 0 and len(stocktake_list) == 0:
            result['handler'] = True
        else:
            result['handler'] = False
        return result

    def add_relation_products(self, partner_id, user_id, request):
        if not request.demand_id:
            raise DemandNotExistError(f'Not Found')
        demand = session.query(sFD).filter_by(id=request.demand_id).with_for_update().first()
        if not demand:
            raise DemandNotExistError(f'Not Found')

        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.frs_store', branch_id=demand.received_by)

        if demand.status not in ["SUBMITTED"]:
            raise DataValidationException("当前状态不允许新增商品！")
        if not request.products:
            raise DataValidationException("添加商品不允许为空")

        key = namedtuple('key', ['id', 'type', 'relation_type'])
        all_products, product_ids, ids, relation_id_dict = {}, set(), get_uuids(len(request.products)), {}
        d_ids = set()
        for i, v in enumerate(request.products):
            product_ids.add(int(v.product_id))
            d_ids.add(int(v.product_id))
            if v.relation_products:
                r_ids = get_uuids(len(v.relation_products))
                for r_i, r_v in enumerate(v.relation_products):
                    print(len(v.relation_products))
                    all_products[key(r_ids[r_i], 'bind', '')] = r_v
                    relation_id_dict[r_ids[r_i]] = ids[i]
                    product_ids.add(int(r_v.product_id))
                all_products[key(ids[i], 'main', 'BIND')] = v
                continue
            all_products[key(ids[i], 'main', '')] = v

        duplicated_products = demand.products.filter(sFDP.product_id.in_(d_ids)).with_entities(sFDP.id).first()
        if duplicated_products:
            raise DataValidationException("不能重复添加相同商品！")

        product_map = get_product_map(product_ids=product_ids,
                                      return_fields="id,code,name,category,storage_type",
                                      partner_id=partner_id, user_id=user_id)
        category_ids = [int(p.get('category')) for p in product_map.values() if p.get('category')]
        category_map = get_category_map(category_ids, partner_id, user_id)
        unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        add_products, add_relation_products, add_demand_log, add_demand_product_log = [], [], [], []
        trace_id = get_guid()

        for k, p in all_products.items():
            is_main = k.type == 'main'

            new_product = self.build_new_product(p, product_map, {}, unit_map, category_map, partner_id=partner_id,
                                                 user_id=user_id, username=username)
            new_product['id'] = k.id
            new_product['demand_id'] = demand.id

            if not is_main:
                ratio = Decimal(p.ratio) if p.ratio else p.ratio
                if not ratio or ratio < 0:
                    raise ProductError(f"捆绑商品id: {p.product_id}比率错误")

                new_product.update({'demand_product_id': relation_id_dict[k.id], 'ratio': ratio,
                                    'configure': p.configure})

                add_relation_products.append(new_product)
            else:
                new_product.update({'relation_type': k.relation_type})
                add_products.append(new_product)

                add_demand_product_log.append({'partner_id': partner_id,
                                               'trace_id': trace_id,
                                               'demand_id': demand.id,
                                               'action': Action.ADD_PRODUCT.code,
                                               'action_name': Action.ADD_PRODUCT.desc,
                                               'product_id': new_product.get('product_id'),
                                               'product_code': new_product.get('product_code'),
                                               'product_name': new_product.get('product_name'),
                                               'unit_id': new_product.get('unit_id'),
                                               'unit_name': new_product.get('unit_name'),
                                               'tax_price': str(new_product.get('tax_price', '')),
                                               'sales_price': str(new_product.get('sales_price', '')),
                                               'approve_quantity': str(new_product.get('approve_quantity', '')),
                                               'approve_amount': str(new_product.get('approve_amount', '')),
                                               'approve_sales_amount': str(new_product.get('approve_sales_amount', '')),
                                               'created_by': user_id,
                                               'created_name': username,
                                               'created_at': datetime.utcnow(), })

        add_demand_log.append({'partner_id': partner_id,
                               'demand_id': demand.id,
                               'action': Action.ADD_PRODUCT.code,
                               'action_name': Action.ADD_PRODUCT.desc,
                               'platform': self.platform,
                               'created_by': user_id,
                               'created_name': username,
                               'created_at': datetime.utcnow(),
                               'trace_id': trace_id})

        session.bulk_insert_mappings(sFDP, add_products)
        session.bulk_insert_mappings(FdLogDB, add_demand_log)
        session.bulk_insert_mappings(FdpLogDB, add_demand_product_log)
        session.bulk_insert_mappings(SFDPR, add_relation_products) if add_relation_products else ...
        session.commit()

        return {"demand_id": demand.id}


mobile_f_demand_service = MobileFranchiseeDemandService()
