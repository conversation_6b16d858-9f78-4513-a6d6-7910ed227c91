from collections import Iterable
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp
from supply.client.interactive import interactive_service
from supply.driver.Redis import redis_server
from supply.model.receiving_diff import ReceivingDiffModel
from supply import time_cost
import time
import logging


class MobileCommonService(object):

    def __init__(self):
        self.doc_types = ["receiving", "receiving_diff", "transfer", "stocktake", "adjust", "return", "demand",
                          "self_picking"]

    # noinspection PyMethodMayBeStatic
    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    def get_unfinished_doc(self, partner_id=None, user_id=None, branch_type=None,
                           end_date=None, store_id=None):
        """
        首页待办任务查询，由于单据太多需要设置缓存:
        以租户+门店+单据类型做key，value=单据信息(json)
            1、查询之前先去redis读取是否有缓存
            2、没有去实时查询，然后插入缓存，设置30分钟过期时间
            3、当单据有状态变更的时候清掉该单据的缓存
        单据类型：
            receiving、receiving_diff、transfer、adjust、
            return、stocktake、self_picking、demand
        """
        # datetime
        day_end = datetime.fromtimestamp(end_date.seconds)
        doc_map = redis_server.get_unfinished_doc_map(partner_id=partner_id, store_id=store_id,
                                                      doc_types=self.doc_types)
        insert_cache_mapping = dict()
        # 查询未完成收货单(待收货INITED)
        logging.info("get_unfinished_doc branch_type==" + str(branch_type))
        receiving_list = doc_map.get("receiving", [])
        if not receiving_list:
            if branch_type == "FRS_STORE":
                receiving = interactive_service.get_receiving_frs_store(
                    store_id=store_id,
                    end_date=end_date,
                    partner_id=partner_id,
                    user_id=user_id)
                logging.info("get_unfinished_doc receiving==" + str(receiving))
            else:
                start_date = Timestamp(seconds=int(time.time()) - 60 * 60 * 24 * 60)
                end_date = None
                receiving = interactive_service.get_list_receiving_details_store(
                    store_id=store_id,
                    end_date=end_date,
                    partner_id=partner_id,
                    user_id=user_id,
                    start_date=start_date
                )

            receiving_list = []
            if receiving and isinstance(receiving, Iterable):
                for receiving_obj in receiving:
                    receiving_dict = dict(
                        id=receiving_obj.id,
                        code=receiving_obj.code,
                        status=receiving_obj.status,
                    )
                    receiving_list.append(receiving_dict)
                insert_cache_mapping[
                    redis_server.unfinished_doc_key.format(partner_id, store_id, 'receiving')] = dict(
                    receiving=receiving_list)
                # Redis_cli.set_unfinished_doc_cache(partner_id=partner_id, store_id=store_id, doc_type="receiving",
                #                                    doc_data=dict(receiving=receiving_list))

        # 查询未完成收货差异单(新建，已提交，已驳回)
        receiving_diff_list = doc_map.get("receiving_diff", [])
        if not receiving_diff_list:
            in_branch_type = "STORE"
            received_type = None
            if branch_type == "FRS_STORE":
                in_branch_type = "FRS_STORE"
                received_type = "FRS_DEMAND"
            _, list_receiving_diff = ReceivingDiffModel.list_receiving_diffs(
                partner_id=partner_id,
                store_ids=[store_id],
                status=["INITED", "SUBMITTED", "REJECTED"],
                end_date=day_end, branch_type=in_branch_type, received_type=received_type)
            receiving_diff_list = []
            if isinstance(list_receiving_diff, list) and len(list_receiving_diff) > 0:
                for receiving_diff_obj in list_receiving_diff:
                    receiving_diff_list.append(dict(
                        id=receiving_diff_obj.id,
                        code=receiving_diff_obj.code,
                        status=receiving_diff_obj.status,
                    ))
                # Redis_cli.set_unfinished_doc_cache(partner_id=partner_id, store_id=store_id,
                #                                    doc_type="receiving_diff",
                #                                    doc_data=dict(receiving_diff=receiving_diff_list))
                insert_cache_mapping[
                    redis_server.unfinished_doc_key.format(partner_id, store_id, 'receiving_diff')] = dict(
                    receiving_diff=receiving_diff_list)

        # 查询门店未完成调拨单(新建，待收货)
        transfer_list = doc_map.get("transfer", [])
        if not transfer_list:
            in_branch_type = "STORE"
            if branch_type == "FRS_STORE":
                in_branch_type = "FRS_STORE"
            list_transfer = interactive_service.get_list_transfer_detail_neq_status(partner_id=partner_id,
                                                                                    store_id=store_id,
                                                                                    end_date=day_end,
                                                                                    status1="INITED",
                                                                                    status2="SUBMITTED",
                                                                                    branch_type=in_branch_type)
            transfer_list = []
            if isinstance(list_transfer, list) and len(list_transfer) > 0:
                for transfer_obj in list_transfer:
                    transfer_list.append(dict(
                        id=transfer_obj[0],
                        code=transfer_obj[1],
                        status=transfer_obj[2],
                        shipping_store=transfer_obj[3],
                        receiving_store=transfer_obj[4]
                    ))
                # Redis_cli.set_unfinished_doc_cache(partner_id=partner_id, store_id=store_id, doc_type="transfer",
                #                                    doc_data=dict(transfer=transfer_list))
                insert_cache_mapping[
                    redis_server.unfinished_doc_key.format(partner_id, store_id, 'transfer')] = dict(
                    transfer=transfer_list)

        # 查询门店未完成报废单(新建，提交，已驳回)
        adjust_list = doc_map.get('adjust', [])
        if not adjust_list:
            in_branch_type = "STORE"
            if branch_type == "FRS_STORE":
                in_branch_type = "FRS_STORE"
            list_adjust = interactive_service.get_unfinished_adjust(partner_id=partner_id,
                                                                    store_id=store_id,
                                                                    end_date=day_end,
                                                                    branch_type=in_branch_type)
            adjust_list = []
            if isinstance(list_adjust, list) and len(list_adjust) > 0:
                for adjust_obj in list_adjust:
                    adjust_list.append(dict(
                        id=adjust_obj[0],
                        code=adjust_obj[1],
                        status=adjust_obj[2]
                    ))
                # Redis_cli.set_unfinished_doc_cache(partner_id=partner_id, store_id=store_id, doc_type="adjust",
                #                                    doc_data=dict(adjust=adjust_list))
                insert_cache_mapping[
                    redis_server.unfinished_doc_key.format(partner_id, store_id, 'adjust')] = dict(
                    adjust=adjust_list)

        # 查询门店未完成退货单(新建，已提交，已驳回，已审核)
        return_list = doc_map.get('return', [])
        if not return_list:
            in_branch_type = "STORE"
            if branch_type == "FRS_STORE":
                in_branch_type = "FRS_STORE"
            list_return = interactive_service.get_list_store_return_neq_status(
                partner_id=partner_id,
                store_id=store_id,
                end_date=day_end,
                branch_type=in_branch_type,
                status=["INITED", "SUBMITTED", "REJECTED", "APPROVED"]
            )
            return_list = []
            if list_return and isinstance(list_return, list) and len(list_return) > 0:
                for return_obj in list_return:
                    return_list.append(dict(
                        id=return_obj[0],
                        code=return_obj[1],
                        status=return_obj[3]
                    ))
                # Redis_cli.set_unfinished_doc_cache(partner_id=partner_id, store_id=store_id, doc_type="return",
                #                                    doc_data={"return": return_list})
                insert_cache_mapping[
                    redis_server.unfinished_doc_key.format(partner_id, store_id, 'return')] = {"return": return_list}

        # 查询门店未完成盘点单(新建，已提交，已驳回)
        stocktake_list = doc_map.get('stocktake', [])
        if not stocktake_list:
            in_branch_type = "STORE"
            if branch_type == "FRS_STORE":
                in_branch_type = "FRS_STORE"
            list_stocktake = interactive_service.get_unfinished_stocktake_mobile(partner_id=partner_id,
                                                                                 store_id=store_id,
                                                                                 end_date=day_end,
                                                                                 status=["INITED", "SUBMITTED",
                                                                                         "REJECTED"],
                                                                                 branch_type=in_branch_type)
            stocktake_list = []
            if isinstance(list_stocktake, list) and len(list_stocktake) > 0:
                for stocktake_obj in list_stocktake:
                    stocktake_list.append(dict(
                        id=stocktake_obj[0],
                        code=stocktake_obj[1],
                        status=stocktake_obj[3],
                        type=stocktake_obj[2] if stocktake_obj[4] == 'PLAN' else stocktake_obj[4]
                    ))
                # Redis_cli.set_unfinished_doc_cache(partner_id=partner_id, store_id=store_id, doc_type="stocktake",
                #                                    doc_data=dict(stocktake=stocktake_list))
                insert_cache_mapping[
                    redis_server.unfinished_doc_key.format(partner_id, store_id, 'stocktake')] = dict(
                    stocktake=stocktake_list)

        # 查询未完成的订货单(新建，已提交，已驳回)
        demand_list = doc_map.get('demand', [])
        if not demand_list:
            in_branch_type = "STORE"
            if branch_type == "FRS_STORE":
                in_branch_type = "FRS_STORE"
            list_demand = interactive_service.get_unfinished_demand(partner_id=partner_id,
                                                                    store_id=store_id,
                                                                    end_date=day_end, branch_type=in_branch_type)
            demand_list = []
            if isinstance(list_demand, list) and len(list_demand) > 0:
                for demand_obj in list_demand:
                    demand_dict = dict(
                        id=demand_obj[0],
                        code=demand_obj[1],
                        type=demand_obj[2],
                        status=demand_obj[3]
                    )
                    demand_list.append(demand_dict)
                # Redis_cli.set_unfinished_doc_cache(partner_id=partner_id, store_id=store_id, doc_type="demand",
                #                                    doc_data=dict(demand=demand_list))
                insert_cache_mapping[
                    redis_server.unfinished_doc_key.format(partner_id, store_id, 'demand')] = dict(
                    demand=demand_list)

        # 查询未完成的门店自采单(新建，已提交，已驳回)
        self_picking_list = doc_map.get('self_picking', [])
        if not self_picking_list:
            in_branch_type = "STORE"
            if branch_type == "FRS_STORE":
                in_branch_type = "FRS_STORE"
            list_self_picking = interactive_service.get_unfinished_self_picking(end_date=day_end, store_id=store_id,
                                                                                partner_id=partner_id,
                                                                                status=["INITED", "SUBMITTED",
                                                                                        "REJECTED"],
                                                                                branch_type=in_branch_type)
            self_picking_list = []
            if isinstance(list_self_picking, list) and len(list_self_picking) > 0:
                for pick in list_self_picking:
                    self_picking_list.append(dict(
                        id=pick[0],
                        code=pick[1],
                        status=pick[2]
                    ))
                # Redis_cli.set_unfinished_doc_cache(partner_id=partner_id, store_id=store_id, doc_type="self_picking",
                #                                    doc_data=dict(self_picking=self_picking_list))
                insert_cache_mapping[
                    redis_server.unfinished_doc_key.format(partner_id, store_id, 'self_picking')] = dict(
                    self_picking=self_picking_list)

        if insert_cache_mapping:
            redis_server.set_unfinished_doc_cache_by_pipeline(insert_cache_mapping)
        result = dict()
        result['receiving'] = receiving_list
        result['receiving_diff'] = receiving_diff_list
        result['transfer'] = transfer_list
        result['adjust'] = adjust_list
        result['return'] = return_list
        result['stocktake'] = stocktake_list
        result['self_picking'] = self_picking_list
        result['demand'] = demand_list
        return result


mobile_common_service = MobileCommonService()
