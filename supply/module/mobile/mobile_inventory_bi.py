# -*- coding: utf-8 -*-
import json
from datetime import datetime, timedelta
import logging
from decimal import Decimal
from hex_exception import RecordAlreadyExist
from google.protobuf.timestamp_pb2 import Timestamp
from supply.error.exception import NoResultFoundError, StatusUnavailable, DataValidationException
from supply.utils.helper import set_model_from_db, convert_to_int, convert_to_datetime, set_db, translate_utc_time,\
    get_guid, convert_to_decimal
from supply.utils.enums import Demand_type

from supply.client.metadata_service import metadata_service
from supply.client.inventory_service import inventory_service
from supply.client.report_service import report_service


class MobileInventoryBiService():
    '''库存报表相关服务
    service: 
        - get_realtime_inventory()：BOH门店实时库存查询
        - query_inventory_log_list()：库存流水查询
        - get_daily_inventory()：每日库存切片查询
    '''
    def get_store_or_warehouse_map(self, branch_ids, branch_type, partner_id=None, user_id=None):
        """获取仓库或者门店map"""
        branch_dict = {}
        if branch_type == "WAREHOUSE":
            warehouse_detail_list = metadata_service.get_distribution_center_list(ids=branch_ids, partner_id=partner_id,
                                                                                  user_id=user_id)
            if warehouse_detail_list.get("rows"):
                warehouse_detail_list = warehouse_detail_list.get("rows")
                for warehouse_detail in warehouse_detail_list:
                    branch_dict[warehouse_detail["id"]] = warehouse_detail
        elif branch_type == "MACHINING_CENTER":
            machining_detail_list = metadata_service.get_machining_center_list(ids=branch_ids, partner_id=partner_id,
                                                                               user_id=user_id)
            if machining_detail_list and isinstance(machining_detail_list, list):
                for machining_detail in machining_detail_list:
                    branch_dict[machining_detail.get("id")] = machining_detail
        else:
            store_detail_list = metadata_service.get_store_list(ids=branch_ids, partner_id=partner_id, user_id=user_id)

            if store_detail_list.get('rows'):
                store_detail_list = store_detail_list.get('rows')
                for store_detail in store_detail_list:
                    branch_dict[store_detail['id']] = store_detail
        return branch_dict

    def get_realtime_inventory(self, request, partner_id, user_id):
        ''' 查询门店实时库存 '''
        branch_ids = request.branch_ids
        geo_regions = request.geo_regions
        category_ids = request.category_ids
        product_ids = request.product_ids
        limit = request.limit
        offset = request.offset
        sort = request.sort
        order = request.order
        exclude = request.exclude
        branch_type = request.branch_type
        position_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        return_fields = request.return_fields # 返回参数
        product_search_fields = request.product_search_fields # 商品模糊匹配查询

        realtime_inventory_list = []
        count = 0

        if product_search_fields or category_ids:
            relation_filters = dict()
            search = product_search_fields
            search_fields = 'name,code'
            if category_ids:
                category_list = [category_id for category_id in category_ids]
                relation_filters = dict(product_category=category_list)
 
            products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                             search=search,
                                                             search_fields=search_fields,
                                                             return_fields='id',
                                                             partner_id=partner_id, user_id=user_id
                                                             ).get('rows')
            print("?", products_ret)
            if products_ret:
                filter_product_ids = []
                for product_detail in products_ret:
                    filter_product_ids.append(int(product_detail.get("id", 0)))
                if product_ids:
                    product_ids = list(set(product_ids).intersection(set(filter_product_ids)))
                else:
                    product_ids = filter_product_ids
                if len(product_ids) == 0:
                    return [], 0
            else:
                return [], 0

        geo_region_list=[]
        store_ids=[]
        if geo_regions:
            for geo_region in geo_regions:
                geo_region_list.append(str(geo_region))
            relation_filters = {'geo_region': geo_region_list}
            filters = {
                            "open_status__in": ['OPENED'],
                            "status__in": ["ENABLED"]
                        }
            store_id_list = metadata_service.get_store_list(relation_filters=relation_filters,
                                                    filters=filters, return_fields="id",
                                                    partner_id=partner_id, user_id=user_id).get('rows')
            
            if not store_id_list:
                relation_filters = {'branch_region': geo_region_list}
                filters = {
                                "open_status__in": ['OPENED'],
                                "status__in": ["ENABLED"]
                            }
                store_id_list = metadata_service.get_store_list(relation_filters=relation_filters,
                                                    filters=filters, return_fields="id",
                                                    partner_id=partner_id, user_id=user_id).get('rows')


            if store_id_list:
                for store_id in store_id_list:
                    store_ids.append(int(store_id['id']))
            else:
                return [], 0

        if store_ids != []:
            branch_id_list = store_ids
        else:
            branch_id_list = []
        if branch_ids:
            for branch_id in branch_ids:
                branch_id_list.append(branch_id)

        product_inventory_dict, total = inventory_service.query_realtime_inventory(
            branch_id=branch_id_list,
            sub_account_ids=position_ids,
            sort=sort, order=order, exclude=exclude,
            limit=limit, offset=offset,
            partner_id=partner_id,
            user_id=user_id, product_ids=product_ids,
            multi_product=True, aggregate=True, detail=True)

        filter_product_ids = []
        for i in product_inventory_dict:
            filter_product_ids.append(int(i))

        filter_product_details = metadata_service.get_product_list(ids=filter_product_ids, include_units=True,
                                                                    return_fields='name,code,category,status,units,model_name',
                                                                   partner_id=partner_id, user_id=user_id).get('rows')
        product_unit_dict,_ = metadata_service.get_product_units_dict(product_ids=filter_product_ids,
                                                                    partner_id=partner_id, user_id=user_id)
        filter_product_details_dict = {}
        if filter_product_details:
            for i in filter_product_details:
                filter_product_details_dict[str(i['id'])] = i

        # 批量获取unit和category详情，并整理成以id为key的dict
        category_ids = []
        unit_ids = []
        if filter_product_details:
            for filter_product_detail in filter_product_details:
                if filter_product_detail.get('category'):
                    category_id = int(filter_product_detail['category'])
                    category_ids.append(category_id)

                if (filter_product_detail.get('units')):
                    for product_units_detail in filter_product_detail['units']:
                        unit_id = int(product_units_detail['id'])
                        unit_ids.append(unit_id)

        filter_unit_list = metadata_service.get_unit_list(ids=unit_ids, partner_id=partner_id, user_id=user_id)
        filter_unit_list = filter_unit_list.get('rows')
        key_filter_unit_dict = {}
        for filter_unit in filter_unit_list:
            key_filter_unit_dict[filter_unit['id']] = filter_unit

        filter_category_list = metadata_service.get_product_category_list(ids=category_ids, partner_id=partner_id,
                                                                          user_id=user_id)
        filter_category_list = filter_category_list.get('rows')
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        # 新增一个branch_type 来区分门店还是仓库分别拉取主档信息
        branch_dict = self.get_store_or_warehouse_map(branch_ids=branch_id_list, branch_type=branch_type,
                                                      partner_id=partner_id, user_id=user_id)
        # 拉取仓位主档
        position_dict = metadata_service.get_position_dict(partner_id=partner_id, user_id=user_id)

        cost_dict = {}
        if "cost" in return_fields:
            # TODO: 对接价格中心client
            pass

        for key, value in product_inventory_dict.items():
            product_id = int(key)
            if filter_product_details_dict.get(key):
                product_detail = filter_product_details_dict.get(key)
                product = value
                if isinstance(product, list):
                    for p in product:
                        acct_tax_price = cost_dict.get(product_id, {}).get("adjust_tax_price", 0) # 核算含税价格
                        acct_cost_price = cost_dict.get(product_id, {}).get("adjust_price", 0) # 核算不含税价格
                        order_tax_price = cost_dict.get(product_id, {}).get("tax_price", 0) # 订货含税价格

                        inventory_detail = {}
                        inventory_detail['store_id'] = int(p['branch_id'])
                        inventory_detail['store_code'] = branch_dict.get(p['branch_id'], {}).get('code')
                        inventory_detail['store_name'] = branch_dict.get(p['branch_id'], {}).get('name')
                        inventory_detail['product_id'] = int(product_detail['id'])
                        inventory_detail['product_code'] = product_detail['code']
                        inventory_detail['product_name'] = product_detail['name']
                        inventory_detail['product_status'] = product_detail['status']
                        inventory_detail['spec'] = product_detail.get('model_name', '')
                        if product_detail.get('category'):
                            category_id = int(product_detail['category'])
                            inventory_detail['category_id'] = int(category_id)
                            inventory_detail['category_name'] = key_filter_category_dict.get(str(category_id)).get('name')

                        demand_unit_rate = 1
                        purchase_unit_rate = 1
                        product_id = product_detail['id']
                        # 核算单位
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('default'):
                            unit_id = int(product_unit_dict.get(str(product_id)).get('default').get('id'))
                            unit_detail = key_filter_unit_dict.get(str(unit_id))
                            if unit_detail:
                                inventory_detail['accounting_unit_id'] = unit_id
                                inventory_detail['accounting_unit_code'] = unit_detail['code']
                                inventory_detail['accounting_unit_name'] = unit_detail['name']
                        # 订货单位
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('order'):
                            demand_unit_id = int(product_unit_dict.get(str(product_id)).get('order').get('id'))
                            demand_unit_rate = product_unit_dict.get(str(product_id)).get('order').get('rate')
                            demand_unit_rate = round(demand_unit_rate, 6) if demand_unit_rate else 1
                            demand_unit_detail = key_filter_unit_dict.get(str(demand_unit_id))
                            if demand_unit_detail:
                                inventory_detail['demand_unit_id'] = demand_unit_id
                                inventory_detail['demand_unit_code'] = demand_unit_detail.get("code")
                                inventory_detail['demand_unit_name'] = demand_unit_detail.get("name")
                        # 采购单位
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('purchase'):
                            purchase_unit_id = int(product_unit_dict.get(str(product_id)).get('purchase').get('id'))
                            purchase_unit_rate = product_unit_dict.get(str(product_id)).get('purchase').get('rate')
                            purchase_unit_rate = round(purchase_unit_rate, 6) if purchase_unit_rate else 1
                            purchase_unit_detail = key_filter_unit_dict.get(str(purchase_unit_id))
                            if purchase_unit_detail:
                                inventory_detail['purchase_unit_id'] = purchase_unit_id
                                inventory_detail['purchase_unit_code'] = purchase_unit_detail.get("code")
                                inventory_detail['purchase_unit_name'] = purchase_unit_detail.get("name")

                        inventory_detail['qty'] = p['quantity_avail']
                        inventory_detail['sku_amount'] = p['quantity_avail']*acct_tax_price
                        # 核算数量=订货数量*订货单位比率
                        inventory_detail['demand_qty'] = (convert_to_decimal(p['quantity_avail']) / convert_to_decimal(
                            demand_unit_rate) if demand_unit_rate else p[
                            'quantity_avail']).quantize(Decimal('0.********'))
                        # 核算数量=采购数量*采购单位比率
                        inventory_detail['purchase_qty'] = (convert_to_decimal(p['quantity_avail']) / convert_to_decimal(
                            purchase_unit_rate) if purchase_unit_rate else p[
                            'quantity_avail']).quantize(Decimal('0.********'))
                        # 采购单位
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('purchase'):
                            # purchase_unit_id = int(product_unit_dict.get(str(product_id)).get('purchase').get('id'))
                            purchase_unit_rate = product_unit_dict.get(str(product_id)).get('purchase').get('rate')
                        inventory_detail['freeze_qty'] = p['quantity_freeze']
                        inventory_detail['broker_qty'] = convert_to_decimal(p['quantity_broker'])/convert_to_decimal(
                                                purchase_unit_rate) if (purchase_unit_rate and p['quantity_broker']) else p['quantity_broker']

                        inventory_detail['extra_detail'] = list()
                        inventory_detail['children'] = list()
                        if p.get('extra_detail'):
                            for i in p['extra_detail']:
                                # 在途账户处理
                                if i.get('sku_type') and i['sku_type'] == 'broker':
                                    # 发货在途 -》订货单位
                                    if i.get('code') and i['code'] == 'DemandOrder':
                                        i['qty'] = (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                                                demand_unit_rate) if i.get('quantity_avail') else 0
                                    # 采购在途 -》 采购单位
                                    if i.get('code') and i['code'] == 'WarehousePurchase':
                                        i['qty'] = (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                                                purchase_unit_rate) if i.get('quantity_avail') else 0
                                    # 退货在途 -》 订货单位
                                    if i.get('code') and i['code'] == 'StoreReturn':
                                        i['qty'] = (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                                                    demand_unit_rate) if i.get('quantity_avail') else 0
                                    # 调拨在途 -》订货单位
                                    transfer_code = ['Transfer', 'StoreTransfer']
                                    if i.get('code') and i['code'] in transfer_code:
                                        i['qty'] = (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                                                    demand_unit_rate) if i.get('quantity_avail') else 0

                                    extra_detail = {
                                        "code": i['code'],
                                        "qty": i['qty'],
                                        "sku_type": i['sku_type'],
                                        "amount": i['qty']*order_tax_price
                                    }
                                    inventory_detail['extra_detail'].append(extra_detail)

                                # 子账户处理
                                if i.get('sku_type') and i['sku_type'] == 'child':
                                    sub_inventory_detail = {
                                        "position_id": int(i.get('sub_account_id', 0)),
                                        "position_name": position_dict.get(i.get('sub_account_id', 0), {}).get('name'),
                                        "position_code": position_dict.get(i.get('sub_account_id', 0), {}).get('code'),
                                        "product_id": int(product_detail['id']),
                                        "product_code": product_detail['code'],
                                        "product_name": product_detail['name'],
                                        "product_status": product_detail['status'],
                                        "qty": convert_to_decimal(i.get('quantity_avail',0)),
                                        "freeze_qty": convert_to_decimal(i.get('quantity_freeze', 0)),
                                        "broker_qty": convert_to_decimal(i.get('quantity_broker', 0)),
                                        "accounting_unit_id": inventory_detail.get('accounting_unit_id'),
                                        "accounting_unit_code": inventory_detail.get('accounting_unit_code'),
                                        "accounting_unit_name": inventory_detail.get('accounting_unit_name'),
                                        "demand_unit_id": inventory_detail.get('demand_unit_id'),
                                        "demand_unit_code": inventory_detail.get('demand_unit_code'),
                                        "demand_unit_name": inventory_detail.get('demand_unit_name'),
                                        "purchase_unit_id": inventory_detail.get('purchase_unit_id'),
                                        "purchase_unit_code": inventory_detail.get('purchase_unit_code'),
                                        "purchase_unit_name": inventory_detail.get('purchase_unit_name'),
                                        "demand_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                        demand_unit_rate) if demand_unit_rate else i[
                                                                'quantity_avail']).quantize(Decimal('0.********')),
                                        "purchase_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                        purchase_unit_rate) if purchase_unit_rate else i[
                                                                'quantity_avail']).quantize(Decimal('0.********'))

                                    }
                                    if position_ids: # 与前段沟通结果：如果传入指定仓位，则不要把仓位信息包含在child里
                                        inventory_detail['position_id'] = int(i.get('sub_account_id', 0))
                                        inventory_detail['position_name'] = position_dict.get(i.get('sub_account_id', 0), {}).get('name')
                                        inventory_detail['position_code'] = position_dict.get(i.get('sub_account_id', 0), {}).get('code')
                                    else:
                                        inventory_detail['children'].append(sub_inventory_detail)
                                
                                #   sku主账户处理 —— 现处理为“无仓位”
                                if i.get('sku_type') and i['sku_type'] == 'sku':
                                    if position_ids: # 如果传入仓位id，说明主账户不用显示
                                        pass                        
                                    else:
                                        sub_inventory_detail = {
                                            "position_id": int(i.get('sub_account_id', 0)),
                                            "position_name": position_dict.get(i.get('sub_account_id', 0), {}).get('name'),
                                            "position_code": position_dict.get(i.get('sub_account_id', 0), {}).get('code'),
                                            "product_id": int(product_detail['id']),
                                            "product_code": product_detail['code'],
                                            "product_name": product_detail['name'],
                                            "product_status": product_detail['status'],
                                            "qty": convert_to_decimal(i.get('quantity_avail',0)),
                                            "freeze_qty": convert_to_decimal(i.get('quantity_freeze', 0)),
                                            "broker_qty": convert_to_decimal(i.get('quantity_broker', 0)),
                                            "accounting_unit_id": inventory_detail.get('accounting_unit_id'),
                                            "accounting_unit_code": inventory_detail.get('accounting_unit_code'),
                                            "accounting_unit_name": inventory_detail.get('accounting_unit_name'),
                                            "demand_unit_id": inventory_detail.get('demand_unit_id'),
                                            "demand_unit_code": inventory_detail.get('demand_unit_code'),
                                            "demand_unit_name": inventory_detail.get('demand_unit_name'),
                                            "purchase_unit_id": inventory_detail.get('purchase_unit_id'),
                                            "purchase_unit_code": inventory_detail.get('purchase_unit_code'),
                                            "purchase_unit_name": inventory_detail.get('purchase_unit_name'),
                                            "demand_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                            demand_unit_rate) if demand_unit_rate else i[
                                                                    'quantity_avail']).quantize(Decimal('0.********')),
                                            "purchase_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                            purchase_unit_rate) if purchase_unit_rate else i[
                                                                    'quantity_avail']).quantize(Decimal('0.********'))

                                        }
                                        inventory_detail['children'].append(sub_inventory_detail)
                        
                        count += 1
                        realtime_inventory_list.append(inventory_detail)

                else:
                    acct_tax_price = cost_dict.get(product_id, {}).get("adjust_tax_price", 0) # 核算含税价格
                    acct_cost_price = cost_dict.get(product_id, {}).get("adjust_price", 0) # 核算不含税价格
                    order_tax_price = cost_dict.get(product_id, {}).get("tax_price", 0) # 订货含税价格
                    
                    inventory_detail = {}
                    # if product_inventory_dict.get(str(product_detail['id'])):
                    #     product = product_inventory_dict.get(str(product_detail['id']))
                    inventory_detail['store_id'] = int(product['branch_id'])
                    inventory_detail['store_code'] = branch_dict.get(product['branch_id']).get('code')
                    inventory_detail['store_name'] = branch_dict.get(product['branch_id']).get('name')
                    inventory_detail['product_id'] = int(product_detail['id'])
                    inventory_detail['product_code'] = product_detail['code']
                    inventory_detail['product_name'] = product_detail['name']
                    inventory_detail['product_status'] = product_detail['status']
                    inventory_detail['spec'] = product_detail.get('model_name','')

                    if product_detail.get('category'):
                        category_id = int(product_detail['category'])
                        inventory_detail['category_id'] = int(category_id)
                        inventory_detail['category_name'] = key_filter_category_dict.get(str(category_id)).get('name')

                    demand_unit_rate = 1
                    purchase_unit_rate = 1
                    product_id = product_detail['id']
                    # 核算单位
                    if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('default'):
                        unit_id = int(product_unit_dict.get(str(product_id)).get('default').get('id'))
                        unit_detail = key_filter_unit_dict.get(str(unit_id))
                        if unit_detail:
                            inventory_detail['accounting_unit_id'] = unit_id
                            inventory_detail['accounting_unit_code'] = unit_detail['code']
                            inventory_detail['accounting_unit_name'] = unit_detail['name']
                    # 订货单位
                    if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('order'):
                        demand_unit_id = int(product_unit_dict.get(str(product_id)).get('order').get('id'))
                        demand_unit_rate = product_unit_dict.get(str(product_id)).get('order').get('rate')
                        demand_unit_rate = round(demand_unit_rate, 6) if demand_unit_rate else 1
                        demand_unit_detail = key_filter_unit_dict.get(str(demand_unit_id))
                        if demand_unit_detail:
                            inventory_detail['demand_unit_id'] = demand_unit_id
                            inventory_detail['demand_unit_code'] = demand_unit_detail['code']
                            inventory_detail['demand_unit_name'] = demand_unit_detail['name']

                    # 采购单位
                    if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('purchase'):
                        purchase_unit_id = int(product_unit_dict.get(str(product_id)).get('purchase').get('id'))
                        purchase_unit_rate = product_unit_dict.get(str(product_id)).get('purchase').get('rate')
                        purchase_unit_rate = round(purchase_unit_rate, 6) if purchase_unit_rate else 1
                        purchase_unit_detail = key_filter_unit_dict.get(str(purchase_unit_id))
                        if purchase_unit_detail:
                            inventory_detail['purchase_unit_id'] = purchase_unit_id
                            inventory_detail['purchase_unit_code'] = purchase_unit_detail['code']
                            inventory_detail['purchase_unit_name'] = purchase_unit_detail['name']

                    inventory_detail['qty'] = p['quantity_avail']
                    inventory_detail['sku_amount'] = p['quantity_avail']*acct_tax_price
                    # 核算数量=订货数量*订货单位比率
                    inventory_detail['demand_qty'] = (convert_to_decimal(p['quantity_avail']) / convert_to_decimal(
                        demand_unit_rate) if demand_unit_rate else p[
                            'quantity_avail']).quantize(Decimal('0.********'))
                    # 核算数量=采购数量*采购单位比率
                    inventory_detail['purchase_qty'] = (convert_to_decimal(p['quantity_avail']) / convert_to_decimal(
                            purchase_unit_rate) if purchase_unit_rate else p[
                            'quantity_avail']).quantize(Decimal('0.********'))
                    inventory_detail['freeze_qty'] = p['quantity_freeze']
                    inventory_detail['broker_qty'] = p['quantity_broker']
                    inventory_detail['extra_detail'] = list()
                    inventory_detail['children'] = list()
                    if p.get('extra_detail'):
                        for i in p['extra_detail']:
                            # 在途账户处理
                            if i.get('sku_type') and i['sku_type'] == 'broker':
                                # 发货在途 -》订货单位
                                if i.get('code') and i['code'] == 'DemandOrder':
                                    i['qty'] = (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                                                demand_unit_rate) if i.get('quantity_avail') else 0
                                # 采购在途 -》 采购单位
                                if i.get('code') and i['code'] == 'WarehousePurchase':
                                    i['qty'] = (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                                                purchase_unit_rate) if i.get('quantity_avail') else 0
                                # 退货在途 -》 订货单位
                                if i.get('code') and i['code'] == 'StoreReturn':
                                    i['qty'] = (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                                                demand_unit_rate) if i.get('quantity_avail') else 0
                                # 调拨在途 -》订货单位
                                if i.get('code') and i['code'] == 'Transfer':
                                    i['qty'] = (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                                                demand_unit_rate) if i.get('quantity_avail') else 0
                                extra_detail = {
                                        "code": i['code'],
                                        "qty": i['qty'],
                                        "sku_type": i['sku_type'],
                                        "amount": i['qty']*order_tax_price
                                    }
                                inventory_detail['extra_detail'].append(extra_detail)
                            # 子账户处理
                            elif i.get('sku_type') and i['sku_type'] == 'child':
                                sub_inventory_detail = {
                                        "position_id": int(i.get('sub_account_id', 0)),
                                        "position_name": position_dict.get(i.get('sub_account_id', 0), {}).get('name'),
                                        "position_code": position_dict.get(i.get('sub_account_id', 0), {}).get('code'),
                                        "product_id": int(product_detail['id']),
                                        "product_code": product_detail['code'],
                                        "product_name": product_detail['name'],
                                        "product_status": product_detail['status'],
                                        "qty": convert_to_decimal(i.get('quantity_avail',0)),
                                        "freeze_qty": convert_to_decimal(i.get('quantity_freeze', 0)),
                                        "broker_qty": convert_to_decimal(i.get('quantity_broker', 0))
                                    }
                                # 如果查询的是仓位，就直接把仓位的信息扔到最外层
                                if position_ids:
                                    inventory_detail['position_id'] = int(i.get('sub_account_id', 0))
                                    inventory_detail['position_name'] = position_dict.get(i.get('sub_account_id', 0), {}).get('name')
                                    inventory_detail['position_code'] = position_dict.get(i.get('sub_account_id', 0), {}).get('code')
                                else:
                                    inventory_detail['children'].append(sub_inventory_detail)
                            
                            # sku主账户处理 —— 现处理为“无仓位”
                            elif i.get('sku_type') and i['sku_type'] == 'sku':
                                if position_ids: # 如果传入仓位id，说明主账户不用显示
                                    pass                        
                                else:
                                    sub_inventory_detail = {
                                            "position_id": int(i.get('sub_account_id', 0)),
                                            "position_name": position_dict.get(i.get('sub_account_id', 0), {}).get('name'),
                                            "position_code": position_dict.get(i.get('sub_account_id', 0), {}).get('code'),
                                            "product_id": int(product_detail['id']),
                                            "product_code": product_detail['code'],
                                            "product_name": product_detail['name'],
                                            "product_status": product_detail['status'],
                                            "qty": convert_to_decimal(i.get('quantity_avail',0)),
                                            "freeze_qty": convert_to_decimal(i.get('quantity_freeze', 0)),
                                            "broker_qty": convert_to_decimal(i.get('quantity_broker', 0)),
                                            "accounting_unit_id": inventory_detail.get('accounting_unit_id'),
                                            "accounting_unit_code": inventory_detail.get('accounting_unit_code'),
                                            "accounting_unit_name": inventory_detail.get('accounting_unit_name'),
                                            "demand_unit_id": inventory_detail.get('demand_unit_id'),
                                            "demand_unit_code": inventory_detail.get('demand_unit_code'),
                                            "demand_unit_name": inventory_detail.get('demand_unit_name'),
                                            "purchase_unit_id": inventory_detail.get('purchase_unit_id'),
                                            "purchase_unit_code": inventory_detail.get('purchase_unit_code'),
                                            "purchase_unit_name": inventory_detail.get('purchase_unit_name'),
                                            "demand_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                            demand_unit_rate) if demand_unit_rate else i[
                                                                    'quantity_avail']).quantize(Decimal('0.********')),
                                            "purchase_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                            purchase_unit_rate) if purchase_unit_rate else i[
                                                                    'quantity_avail']).quantize(Decimal('0.********'))

                                        }
                                    inventory_detail['children'].append(sub_inventory_detail)
                    count += 1
                    realtime_inventory_list.append(inventory_detail)
            
            else:
                product = value
                if isinstance(product, list):
                    for p in product:
                        inventory_detail = {}
                        inventory_detail['store_id'] = int(p.get('branch_id')) 
                        inventory_detail['store_code'] = branch_dict.get(p['branch_id'], {}).get('code') if branch_dict.get(p['branch_id']) else None
                        inventory_detail['store_name'] = branch_dict.get(p['branch_id'], {}).get('name') if branch_dict.get(p['branch_id']) else '主档无此门店记录'
                        inventory_detail['product_id'] = int(p.get('product_id'))
                        inventory_detail['product_name'] = '主档无此商品记录'
                        
                        inventory_detail['qty'] = p['quantity_avail']
                        inventory_detail['freeze_qty'] = p['quantity_freeze']
                        inventory_detail['broker_qty'] = p['quantity_broker']

                        count += 1
                        realtime_inventory_list.append(inventory_detail)


                else:
                    inventory_detail = {}
                    inventory_detail['store_id'] = int(product['branch_id'])
                    inventory_detail['store_code'] = branch_dict.get(product['branch_id']).get('code') if branch_dict.get(product['branch_id']) else None
                    inventory_detail['store_name'] = branch_dict.get(product['branch_id']).get('name') if branch_dict.get(product['branch_id']) else '主档无此门店记录'
                    inventory_detail['product_id'] = int(product['product_id'])
                    inventory_detail['product_name'] = '主档无此商品记录'

                    inventory_detail['qty'] = product['quantity_avail']
                    inventory_detail['freeze_qty'] = product['quantity_freeze']
                    inventory_detail['broker_qty'] = product['quantity_broker']

                    count += 1
                    realtime_inventory_list.append(inventory_detail)
            
        return realtime_inventory_list, total

    def query_inventory_log_list(self, request, partner_id, user_id):
        '''
        查询库存流水
        可根据门店id以及商品id
        '''
        branch_id = request.branch_id
        product_ids = request.product_ids
        code = request.code
        action = request.action
        limit = request.limit
        offset = request.offset
        category_ids = request.category_ids
        start_date = request.start_date
        end_date = request.end_date
        order_type = request.order_type
        account_type = request.account_type
        sub_account_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        return_fields = request.return_fields # 返回参数
        product_search_fields = request.product_search_fields # 商品模糊匹配查询

        if code != '' and order_type == 'SALES':
            trace_ids = report_service.get_eticket_trace_id(partner_id=partner_id, user_id=user_id,
                                                            pos_ticket_number=code, branch_id=branch_id,
                                                            product_ids=product_ids,
                                                            start_date=start_date, end_date=end_date)
            if not trace_ids:
                return [], 0, 0
            code = trace_ids['trace_ids']
            result = inventory_service.list_inventory_log(branch_id=branch_id, start_date=start_date, end_date=end_date,
                                                          product_ids=product_ids, order_type=order_type,
                                                          action=action, limit=limit, offset=offset,
                                                          partner_id=partner_id, user_id=user_id, code=code,
                                                          sub_account_ids=sub_account_ids)

        else:
            if product_search_fields or category_ids:
                relation_filters = dict()
                search = product_search_fields
                search_fields = 'name,code'
                if category_ids:
                    category_list = [category_id for category_id in category_ids]
                    relation_filters = dict(product_category=category_list)
    
                products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                search=search,
                                                                search_fields=search_fields,
                                                                return_fields='id',
                                                                partner_id=partner_id, user_id=user_id
                                                                ).get('rows')
                
                if products_ret:
                    filter_product_ids = []
                    for product_detail in products_ret:
                        filter_product_ids.append(int(product_detail.get("id", 0)))
                    if product_ids:
                        product_ids = list(set(product_ids).intersection(set(filter_product_ids)))
                    else:
                        product_ids = filter_product_ids
                    if len(product_ids) == 0:
                        return [], 0
                else:
                    return [], 0

            result = inventory_service.list_inventory_log(branch_id, start_date, end_date, product_ids, order_type,
                                                          action,limit, offset, partner_id, user_id, code, 
                                                          account_type, sub_account_ids)
        
        total = result.get('total')
        if total:
            total = int(total)
        amount_sum = result.get('amount_sum')
        if not amount_sum:
            amount_sum = 0
        result = result.get('logs')
        count = 0
        if not result:
            return [], 0, 0

        filters = {"status__in": ["ENABLED"]}

        filter_unit_list = metadata_service.get_unit_list(filters=filters, partner_id=partner_id, user_id=user_id)
        filter_unit_list = filter_unit_list.get('rows',[])
        key_filter_unit_dict = {}
        for filter_unit in filter_unit_list:
            key_filter_unit_dict[filter_unit['id']] = filter_unit

        filter_category_list = metadata_service.get_product_category_list(filters=filters, partner_id=partner_id,
                                                                          user_id=user_id)
        filter_category_list = filter_category_list.get('rows')
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        # 拉取仓位主档
        position_dict = metadata_service.get_position_dict(partner_id=partner_id, user_id=user_id)

        log_detail_list = []
        all_product_ids = []
        all_codes = []
        for detail in result:
            if detail.get('product_id'):
                all_product_ids.append(int(detail.get('product_id', 0)))
                log_detail_list.append(detail)
                if detail['code'] != 'SALES':
                    all_codes.append(detail.get('trace_id'))
            else:
                pass

        store_detail = metadata_service.get_store(int(branch_id), partner_id=partner_id, user_id=user_id)
        # print(product_ids)
        if product_ids:
            product_ids = list(set(product_ids))
            product_detail_list = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                                    partner_id=partner_id, user_id=user_id)
            product_detail_list = product_detail_list.get('rows')
            # print('product_detail_list:', product_detail_list)
        else:
            all_product_ids = list(set(all_product_ids))
            product_detail_list = metadata_service.get_product_list(ids=all_product_ids, include_units=True,
                                                                    partner_id=partner_id, user_id=user_id)
            # print(all_product_ids)
            product_detail_list = product_detail_list.get('rows')

        product_mapping = {}
        for product_detail_single in product_detail_list:
            product_mapping[str(product_detail_single['id'])] = product_detail_single
        
        product_unit_dict,_ = metadata_service.get_product_units_dict(product_ids=all_product_ids,
                                                                    partner_id=partner_id, user_id=user_id)

        inventory_log_list = []
        flag = 0

        for detail in log_detail_list:
            # print(detail)
            flag += 1
            product_id = detail.get('product_id')
            inventory_log = {}
            inventory_log['store_id'] = branch_id
            inventory_log['store_code'] = store_detail['code']
            inventory_log['store_name'] = store_detail['name']
            inventory_log['sub_account_id'] = int(detail.get('sub_account_id', 0))
            inventory_log['sub_account_name'] = position_dict.get(detail.get('sub_account_id', 0),{}).get('name')
            inventory_log['sub_account_code'] = position_dict.get(detail.get('sub_account_id', 0),{}).get('code')
            # 商品明细
            product_detail = product_mapping.get(str(product_id))
            if not product_detail:
                continue
            inventory_log['product_id'] = int(product_id)
            inventory_log['product_code'] = product_detail['code']
            inventory_log['product_name'] = product_detail['name']
            inventory_log['spec'] = product_detail.get('model_name') if product_detail.get('model_name') else None

            if product_detail.get('category'):
                category_id = int(product_detail['category'])
                inventory_log['category_id'] = int(category_id)
                inventory_log['category_name'] = key_filter_category_dict.get(str(category_id),{}).get('name')
                inventory_log['category_code'] = key_filter_category_dict.get(str(category_id),{}).get('code')
            if detail['code'] == 'SALES':
                inventory_log['order_code'] = detail.get('batch_no')
            else:
                inventory_log['order_code'] = detail.get('trace_id')
            inventory_log['order_type'] = detail['code']
            inventory_log['account_type'] = detail.get('account_type')
            inventory_log['action'] = detail['action']
            inventory_log['stock_id'] = detail['stock_id']
            inventory_log['status'] = detail['status']
            # 时间转换
            order_date = detail['business_time']
            order_date = translate_utc_time(order_date)
            order_date = Timestamp(seconds=int(order_date.timestamp()))
            inventory_log['order_time'] = order_date


            qty = detail.get('qty') if detail.get('qty') else 0
            if detail['action'] == 'WITHDRAW' or detail['action'] == 'TRANSFER_WITHDRAW':
                inventory_log['qty'] = -qty
            else:
                inventory_log['qty'] = qty
            
            # 核算单位
            if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('default'):
                unit_id = int(product_unit_dict.get(str(product_id)).get('default').get('id'))
                unit_detail = key_filter_unit_dict.get(str(unit_id))
                if unit_detail:
                    inventory_log['accounting_unit_id'] = unit_id
                    inventory_log['accounting_unit_code'] = unit_detail.get('code')
                    inventory_log['accounting_unit_name'] = unit_detail.get('name')
            # 订货单位  
            if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('order'):
                demand_unit_id = int(product_unit_dict.get(str(product_id)).get('order').get('id'))
                tax_rate = product_unit_dict.get(str(product_id)).get('order').get('rate')
                tax_rate = round(tax_rate, 6) if tax_rate else 1
                            
                demand_unit_detail = key_filter_unit_dict.get(str(demand_unit_id))
                if demand_unit_detail:
                    inventory_log['demand_unit_id'] = demand_unit_id
                    inventory_log['demand_unit_code'] = demand_unit_detail.get('code')
                    inventory_log['demand_unit_name'] = demand_unit_detail.get('name')
                    if detail['action'] == 'WITHDRAW' or detail['action'] == 'TRANSFER_WITHDRAW':
                        inventory_log['demand_qty'] = -qty / tax_rate
                    else:
                        inventory_log['demand_qty'] = qty / tax_rate
                    count += 1
            
            inventory_log_list.append(inventory_log)

        # print('*******库存流水报表**********', inventory_log_list)
        return inventory_log_list, total, amount_sum

    def get_daily_inventory(self, request, partner_id, user_id):
        branch_id = request.branch_id
        product_ids = request.product_ids
        category_ids = request.category_ids
        limit = request.limit
        offset = request.offset
        start_date = request.start_date
        end_date = request.end_date
        code = request.code
        action = request.action
        # extra = request.extra
        if_pre = request.if_pre
        if_end = request.if_end
        exclude_empty = request.exclude_empty
        branch_type = request.branch_type
        position_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        return_fields = request.return_fields # 返回参数
        product_search_fields = request.product_search_fields # 商品模糊匹配查询
        
        extra = {}
        if if_pre:
            extra['pre_qty'] = if_pre
        if if_end:
            extra['end_qty'] = if_end
        if exclude_empty:
            extra['exclude_empty'] = exclude_empty

        if product_search_fields or category_ids:
            relation_filters = dict()
            search = product_search_fields
            search_fields = 'name,code'
            if category_ids:
                category_list = [category_id for category_id in category_ids]
                relation_filters = dict(product_category=category_list)
 
            products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                             search=search,
                                                             search_fields=search_fields,
                                                             return_fields='id',
                                                             partner_id=partner_id, user_id=user_id
                                                             ).get('rows')
            
            if products_ret:
                filter_product_ids = []
                for product_detail in products_ret:
                    filter_product_ids.append(int(product_detail.get("id", 0)))
                if product_ids:
                    product_ids = list(set(product_ids).intersection(set(filter_product_ids)))
                else:
                    product_ids = filter_product_ids
                if len(product_ids) == 0:
                    return [], 0
            else:
                return [], 0

        inventory_snapshot_list = inventory_service.query_snap_list(branch_id=branch_id, start_date=start_date,
                                                                    end_date=end_date,
                                                                    product_ids=product_ids, limit=limit, offset=offset,
                                                                    partner_id=partner_id, user_id=user_id,
                                                                    code=code, action=action, extra=extra, sub_account_ids=position_ids)
        total = inventory_snapshot_list.get('total')
        inventory_snapshot_list = inventory_snapshot_list.get('rows')

        if not inventory_snapshot_list:
            return [], 0

        log_detail_list = []
        all_product_ids = []
        for detail in inventory_snapshot_list:
            if detail.get('account'):
                all_product_ids.append(int(detail.get('account').get('product_id')))
                log_detail_list.append(detail)
            else:
                pass
        # 判断是门店还是仓库查询
        if branch_type in ("WAREHOUSE", "MACHINING_CENTER"):
            store_detail = metadata_service.get_distribution_center(int(branch_id), partner_id=partner_id,
                                                                    user_id=user_id)
        else:
            store_detail = metadata_service.get_store(int(branch_id), partner_id=partner_id, user_id=user_id)

        product_detail_list = metadata_service.get_product_list(ids=all_product_ids, include_units=True,
                                                                partner_id=partner_id, user_id=user_id).get('rows', [])


        # 批量获取unit和category详情，并整理成以id为key的dict
        category_ids = []
        unit_ids = []
        for filter_product_detail in product_detail_list:
            if filter_product_detail.get('category'):
                category_id = int(filter_product_detail['category'])
                category_ids.append(category_id)

            if (filter_product_detail.get('units')):
                for product_units_detail in filter_product_detail['units']:
                    unit_id = int(product_units_detail['id'])
                    unit_ids.append(unit_id)
        filter_unit_list = metadata_service.get_unit_list(ids=unit_ids, partner_id=partner_id, user_id=user_id)
        filter_unit_list = filter_unit_list.get('rows', [])
        key_filter_unit_dict = {}
        for filter_unit in filter_unit_list:
            key_filter_unit_dict[filter_unit['id']] = filter_unit

        filter_category_list = metadata_service.get_product_category_list(ids=category_ids, partner_id=partner_id,
                                                                          user_id=user_id)
        filter_category_list = filter_category_list.get('rows')
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        # 拉取仓位主档
        position_dict = metadata_service.get_position_dict(partner_id=partner_id, user_id=user_id)


        # 拼接数据
        daily_inventory_list = []
        for detail in inventory_snapshot_list:

            # 没有期初也没期末库存
            # if (not detail['amount'].get('qty')) and (not detail['previous'].get('amount')):
            #     continue
            product_id = detail.get('account', {}).get('product_id')
            daily_inventory = {}
            daily_inventory['start_time'] = detail['start']
            daily_inventory['end_time'] = detail['end']
            # 门店明细
            daily_inventory['store_id'] = branch_id
            daily_inventory['store_code'] = store_detail['code']
            daily_inventory['store_name'] = store_detail['name']
            # 商品明细
            for product_detail in product_detail_list:
                if product_detail['id'] == product_id:
                    daily_inventory['product_id'] = int(product_id)
                    daily_inventory['product_code'] = product_detail['code']
                    daily_inventory['product_name'] = product_detail['name']
                    daily_inventory['spec'] = product_detail.get('model_name') if product_detail.get('model_name') else None
                    if product_detail.get('units'):
                        for unit in product_detail["units"]:
                            if unit.get("default") and unit["default"] == True:
                                accounting_unit_id = unit["id"]
                                unit_detail = key_filter_unit_dict.get(str(accounting_unit_id), {})
                                daily_inventory['accounting_unit_name'] = unit_detail.get('name')
                                daily_inventory['accounting_unit_id'] = int(unit_detail.get('id', 0))
                                daily_inventory['accounting_unit_code'] = unit_detail.get('code')
            # 可用期初库存
            daily_inventory['pre_qty'] = 0
            if detail.get('previous'):
                if detail['previous'].get('amount'):
                    daily_inventory['pre_qty'] = detail['previous'].get('amount').get('qty', 0)
            # 可用期末库存
            amount = detail['amount'] if 'amount' in detail else None
            if amount:
                daily_inventory['qty'] = amount.get('qty', 0)
            else:
                daily_inventory['qty'] = 0
            
            # sku库存切片业务详情
            if detail.get('stats'):
                # print(detail.get('stats'))
                daily_inventory['rec_deposit'] = 0
                daily_inventory['stoc_deposit'] = 0
                daily_inventory['stoc_withdraw'] = 0
                daily_inventory['adj_withdraw'] = 0
                daily_inventory['ret_withdraw'] = 0
                daily_inventory['rec_withdraw'] = 0
                daily_inventory['ret_deposit'] = 0
                daily_inventory['trans_withdraw'] = 0
                daily_inventory['ret_transfer'] = 0
                daily_inventory['trans_transfer'] = 0
                daily_inventory['trans_transfer_release'] = 0
                daily_inventory['trans_delivery'] = 0
                daily_inventory['trans_delivery_release'] = 0
                daily_inventory['trans_purchase'] = 0
                daily_inventory['trans_purchase_release'] = 0
                daily_inventory['trans_return_release'] = 0


                for stats_detail in detail['stats']:
                    # print(stats_detail['code'], stats_detail.get('amount').get('qty'), stats_detail['action'] )
                    if stats_detail.get('amount').get('qty') and stats_detail.get('account_type') == "SKU":
                        # 发货出库
                        if stats_detail['code'] == 'ORDER_DELIVERY' and 'WITHDRAW' in stats_detail['action']:
                            daily_inventory['rec_withdraw'] += stats_detail.get('amount').get('qty')

                        # 配送收货入库
                        if (stats_detail['code'] == 'ORDER_RECEIVING' or stats_detail['code'] == 'ADJUST_ORDER_RECEIVING')  and\
                             (stats_detail['action'] == 'DEPOSIT' or stats_detail['action'] == 'TRANSFER_DEPOSIT'):
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')

                        # 配送收货出库
                        if (stats_detail['code'] == 'ORDER_RECEIVING' or stats_detail['code'] == 'ADJUST_ORDER_RECEIVING') and\
                             (stats_detail['action'] == 'WITHDRAW' or stats_detail['action'] == 'TRANSFER_WITHDRAW'):
                            if branch_type in ("WAREHOUSE", "MACHINING_CENTER"): # 门店超收产生的退货单算入收货差异出库中
                                daily_inventory['rec_diff_withdraw'] = stats_detail.get('amount').get('qty')
                            else:
                                daily_inventory['rec_withdraw'] += stats_detail.get('amount').get('qty')
                        
                        # 直送收货入库
                        if stats_detail['code'] == 'DIR_RECEIVING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')
                        if stats_detail['code'] == 'ORDER_DIR_RECEIVING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')
                        
                        # 对账调整入库 —— 计入收货入库
                        if stats_detail['code'] == 'CHECKING_ADJUST_REC' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')

                        # 对账调整出库 —— 计入退货出库
                        if stats_detail['code'] == 'CHECKING_ADJUST_RET' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 采购收货入库 —— 仓库采购收货
                        if stats_detail['code'] == 'WAREHOUSE_PURCHASE' and \
                            (stats_detail['action'] == 'TRANSFER_DEPOSIT' or stats_detail['action'] == 'DEPOSIT'):
                            daily_inventory['purchase_deposit'] = stats_detail.get('amount').get('qty')

                        # 收货差异出库
                        if stats_detail['code'] == 'RECEIVING_DIFF' and 'WITHDRAW' in stats_detail['action']:
                            daily_inventory['rec_diff_withdraw'] = stats_detail.get('amount').get('qty')

                        # 收货差异入库
                        if stats_detail['code'] == 'RECEIVING_DIFF' and 'DEPOSIT' in stats_detail['action']:
                            # if branch_type in ("WAREHOUSE", "MACHINING_CENTER"): # 门店收货差异产生的退货单算入退货入库中
                            #     daily_inventory['ret_deposit'] += stats_detail.get('amount').get('qty')
                            # else:
                            daily_inventory['rec_diff_deposit'] = stats_detail.get('amount').get('qty')

                        # 退货入库
                        status_returned_in = ["DEPOSIT", "TRANSFER_DEPOSIT"]
                        if stats_detail['code'] in ('RETURN_IN', 'ADJUST_ORDER_RETURN') and stats_detail['action'] in status_returned_in:
                            daily_inventory['ret_deposit'] += stats_detail.get('amount').get('qty')

                        # 退货出库
                        status_returned_out = ["WITHDRAW", "TRANSFER_WITHDRAW"]
                        if stats_detail['code'] in ('ORDER_RETURN','ADJUST_ORDER_RETURN') and stats_detail['action'] in status_returned_out:
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 采购退货
                        if stats_detail['code'] == 'PURCHASE_RETURN':
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 调拨入库
                        if stats_detail['code'] in ('TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                            and stats_detail['action'] == 'TRANSFER_DEPOSIT':
                            daily_inventory['trans_deposit'] = stats_detail.get('amount').get('qty')

                        # 调拨出库
                        if stats_detail['code'] in ('TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                            and stats_detail['action'] == 'TRANSFER_WITHDRAW':
                            daily_inventory['trans_withdraw'] = stats_detail.get('amount').get('qty')

                        # 盘点入库
                        if stats_detail['code'][0:9] == 'STOCKTAKE' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['stoc_deposit'] += stats_detail.get('amount').get('qty')

                        # 盘点出库
                        if stats_detail['code'][0:9] == 'STOCKTAKE' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['stoc_withdraw'] += stats_detail.get('amount').get('qty')

                        # 报废入库
                        if stats_detail['code'] == 'ADJUST' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['adj_deposit'] = stats_detail.get('amount').get('qty')

                        # 报废出库
                        if stats_detail['code'] == 'ADJUST' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['adj_withdraw'] += stats_detail.get('amount').get('qty')
                        if stats_detail['code'] == 'DIFF_ADJUST' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['adj_withdraw'] += stats_detail.get('amount').get('qty')

                        # 销售入库
                        if stats_detail['code'] == 'SALES' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['sales_deposit'] = stats_detail.get('amount').get('qty')

                        # 销售出库
                        if stats_detail['code'] == 'SALES' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['sales_withdraw'] = stats_detail.get('amount').get('qty')

                        # 自采入库
                        if stats_detail['code'] == 'SELF_PICKING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['spick_deposit'] = stats_detail.get('amount').get('qty')
                        
                        # 冻结
                        if stats_detail['code'] == 'SUBMIT_ORDER' and stats_detail['action'] == 'TRANSFER_WITHDRAW':
                            daily_inventory['freeze_qty'] = stats_detail.get('amount').get('qty')

                        # 物料转换入库
                        if stats_detail['code'] == 'MATERIAL_CONVERT' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['material_trans_deposit'] = stats_detail.get('amount').get('qty')

                        # 物料转换出库
                        if stats_detail['code'] == 'MATERIAL_CONVERT' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['material_trans_withdraw'] = stats_detail.get('amount').get('qty')

                        # 加工入库
                        if stats_detail['code'] == 'PROCESSING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['processing_deposit'] = stats_detail.get('amount').get('qty')

                        # 加工出库
                        if stats_detail['code'] == 'PROCESSING' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['processing_withdraw'] = stats_detail.get('amount').get('qty')

                        # 包装入库
                        if stats_detail['code'] == 'PACKING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['packing_deposit'] = stats_detail.get('amount').get('qty')

                        # 包装出库
                        if stats_detail['code'] == 'PACKING' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['packing_withdraw'] = stats_detail.get('amount').get('qty')

                    elif stats_detail.get('amount').get('qty') and stats_detail.get('account_type') == "BROKER":
                        status_deposit = ["DEPOSIT", "TRANSFER_DEPOSIT"]
                        status_withdraw = ["WITHDRAW", "TRANSFER_WITHDRAW"]                        
                        # 退货在途
                        if stats_detail['code'] in ('ORDER_RETURN','ADJUST_ORDER_RETURN') and stats_detail['action'] in status_deposit:
                            daily_inventory['ret_transfer'] += stats_detail.get('amount').get('qty')

                        # 退货在途释放
                        if stats_detail['code'] in ('RETURN_IN','ADJUST_ORDER_RETURN') and stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_return_release'] += stats_detail.get('amount').get('qty')

                        # 调拨在途
                        if stats_detail['code'] in ('EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                            and stats_detail['action'] == 'TRANSFER_DEPOSIT':
                            daily_inventory['trans_transfer'] += stats_detail.get('amount').get('qty')
                        
                        # 调拨在途释放
                        if stats_detail['code'] in ('EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                            and stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_transfer_release'] += stats_detail.get('amount').get('qty')
                        
                        # 发货在途
                        if stats_detail['code'] in ('ORDER_DELIVERY') and stats_detail['action'] in status_deposit:
                            daily_inventory['trans_delivery'] += stats_detail.get('amount').get('qty')

                        # 发货在途释放
                        if stats_detail['code'] in ('ORDER_RECEIVING', 'RECEIVING_DIFF', 'ADJUST_ORDER_RECEIVING') and stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_delivery_release'] += stats_detail.get('amount').get('qty')

                        # 采购在途
                        if stats_detail['code'] in ('PURCHASE_DELIVERY') and stats_detail['action'] in status_deposit:
                            daily_inventory['trans_purchase'] += stats_detail.get('amount').get('qty')

                        # 采购在途释放
                        if stats_detail['code'] in ('WAREHOUSE_PURCHASE') and stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_purchase_release'] += stats_detail.get('amount').get('qty')

            # 在途聚合取值
            status_account = json.loads(detail.get('status_account')) if detail.get('status_account') else None
            if status_account:
                daily_inventory['trans_begin'] = 0  # 在途期初库存
                daily_inventory['trans_end'] = 0  # 在途期末库存
                for transfer_data in status_account:
                    # 在途期初库存
                    if transfer_data.get('account_type') == 'BROKER':
                        trans_begin = transfer_data.get('pre_qty', 0)
                        daily_inventory['trans_begin'] += trans_begin
                    # 在途期末库存
                    if transfer_data.get('account_type') == 'BROKER':
                        trans_end = transfer_data.get('qty', 0)
                        daily_inventory['trans_end'] += trans_end

            # 子账户库存切片业务详情
            if detail.get('account', {}).get('sub_account'):
                position_id = detail['account']['sub_account'].get('id', 0)
                daily_inventory['position_id'] = int(position_id)
                daily_inventory['position_name'] = position_dict.get(str(position_id),{}).get('name')
                daily_inventory['position_code'] = position_dict.get(str(position_id),{}).get('code')

            daily_inventory_list.append(daily_inventory)
        # print('*******每日库存报表**********', daily_inventory_list)
        return daily_inventory_list, total


mobile_inventory_bi_service = MobileInventoryBiService()
