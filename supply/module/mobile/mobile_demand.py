# -*- coding: utf-8 -*-
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp
from supply import logger
from supply.error.exception import NoResultFoundError, StatusUnavailable, DataValidationException
from supply.model.supply_doc_code import Supply_doc_code
from supply.utils.helper import get_guid, MessageTopic, get_branch_map, get_product_map, get_uuids, convert_to_int
from supply.utils.enums import Demand_type, Demand_enum
from supply.driver.mq import mq_consumer, mq_producer
from supply.client.metadata_service import metadata_service
from supply.client.receipt_service import receipt_service
from supply.client.ianvs_service import ianvs_service
from supply.error.demand import DemandError, ProductError

from supply.model.doc_plan.doc_plan import doc_plan_repository
from supply.model.demand.supply_demand import Supply_demand
from supply.model.demand.supply_demand_product import Supply_demand_product
from supply.model.demand.supply_demand_log import Supply_demand_log


class MobileDemandService():
    """订货移动端相关服务
    related_client_service:
        - receipt
    related_model:
        - Supply_demand
        - doc_plan_repository"""
    # noinspection PyMethodMayBeStatic
    def utctimestamp2datetime(self, timestamp):
        """前端传的Timestamp转为datetime"""
        date = None
        if timestamp and isinstance(timestamp, Timestamp):
            date = datetime.utcfromtimestamp(timestamp.seconds)
        return date

    def get_branch_map(self, partner_id, user_id):
        branch_map = {}
        warehouse_list = metadata_service.list_entity(schema_name='distrcenter', 
                                                    return_fields="id,code,name",
                                                    partner_id=partner_id, user_id=user_id).get('rows', [])
        vendor_list = metadata_service.list_entity(schema_name='vendor', 
                                                    return_fields="id,code,name",
                                                    partner_id=partner_id, user_id=user_id).get('rows', [])
        manufactory_list = metadata_service.list_entity(schema_name='machining-center', 
                                                    return_fields="id,code,name",
                                                    partner_id=partner_id, user_id=user_id).get('rows', [])
        for warehouse in warehouse_list:
            branch_map[int(warehouse.get('id', 0))] = warehouse.get('fields',{}).get('name')
        for vendor in vendor_list:
            branch_map[int(vendor.get('id', 0))] = vendor.get('fields',{}).get('name')
        for manufactory in manufactory_list:
            branch_map[int(manufactory.get('id', 0))] = manufactory.get('fields',{}).get('name')
        return branch_map

    def combine_data(self, demands, plans, orders, plan_ids=None, partner_id=None, user_id=None):
        
        plan_status_dict = {}
        if plan_ids and len(plan_ids) > 0:
            paln_status_dbs = doc_plan_repository.get_by_plan_ids(plan_ids, partner_id)
            for plan_status in paln_status_dbs:
                if plan_status_dict.get(plan_status.plan_id):
                    plan_status_dict[plan_status.plan_id].append(
                        {
                            'time': plan_status.time,
                            'start_status': plan_status.start_status,
                            'end_status': plan_status.end_status,
                            'time_around': plan_status.time_around,
                            'doc_filter': plan_status.doc_filter
                        }
                    )
                else:
                    plan_status_dict[plan_status.plan_id]=[
                        {
                            'time': plan_status.time,
                            'start_status': plan_status.start_status,
                            'end_status': plan_status.end_status,
                            'time_around': plan_status.time_around,
                            'doc_filter': plan_status.doc_filter
                        }
                    ]
        
        plan_dict = {}
        if plans:
            for plan in plans:
                plan_dict[int(plan.id)] = {
                    'name': plan.name, 
                    'method': plan.method,
                    'month_method': plan.month_method,
                    'week_method': plan.week_method,
                    'day_method': plan.day_method,
                    'interval': plan.interval,
                    'start_date': Timestamp(seconds=int(plan.start.timestamp())), 
                    'end_date': Timestamp(seconds=int(plan.end.timestamp())), 
                    'status_plan': plan_status_dict.get(plan.id)
                }

        branch_map = self.get_branch_map(partner_id=partner_id, user_id=user_id)
        order_dict = {}
        if orders:
            for order in orders:
                if order_dict.get(int(order.batch_id)):
                    order_dict[int(order.batch_id)].append(
                        {
                            'id': order.id,
                            'code': order.code,
                            'status': order.status,
                            'delivery_name': branch_map.get(int(order.delivery_by))
                        }
                    )
                else:
                    order_dict[int(order.batch_id)] = [{
                        'id': order.id,
                        'code': order.code,
                        'status': order.status,
                        'delivery_name': branch_map.get(int(order.delivery_by))

                    }]

        demand_return_list = []
        for demand in demands:
            demand_return_fields = {
                'id': demand.id,
                'code': demand.code,
                'status': demand.status,
                'demand_date': Timestamp(seconds=int(demand.demand_date.timestamp())), 
                'is_adjust': demand.is_adjust,
                'type':demand.type,
                'remark': demand.remark,
                'schedule': plan_dict.get(int(demand.batch_id), {}) if demand.batch_id else {},
                'orders': order_dict.get(int(demand.id), {}),
                'has_product': str(demand.has_product)
            }
            demand_return_list.append(demand_return_fields)
        return demand_return_list

    def list_demand(self, partner_id, user_id, request):
        start_date = request.start_date
        end_date = request.end_date
        if start_date and not isinstance(start_date, datetime):
            start_date = datetime.fromtimestamp(start_date.seconds)
        if end_date and not isinstance(end_date, datetime):
            end_date = datetime.fromtimestamp(end_date.seconds)
        status = request.status
        store_ids = request.store_ids

        has_product = request.has_product
        types = request.types
        schedule_name = request.schedule_name
        codes = request.codes
        sort = request.sort if request.sort else 'updated_at'
        order = request.order
        limit = request.limit
        offset = request.offset

        ids = request.ids
        if len(ids) == 0:
            ids=None

        demands = []
        plans = [] 
        orders = []
        # 根据计划名称取doc_plan表中查出doc_id列表
        plan_ids = []
        if schedule_name:
            plans = doc_plan_repository.list_doc_plan(plan_name=schedule_name, partner_id=partner_id, user_id=user_id)
            for plan in plans:
                plan_ids.append(int(plan.id))
            if len(plan_ids)==0:
                return [], 0

        demands, total = Supply_demand.get_store_demand_list_by_args(
            partner_id=partner_id, user_id=user_id, store_ids=store_ids, has_product=has_product, start_date=start_date, end_date=end_date, status=status,
            codes=codes, offset=offset, limit=limit, order=order, sort=sort, plan_ids=plan_ids, ids=ids, types=types)
        
        for demand in demands:
            if demand.batch_id:
                plan_ids.append(demand.batch_id)
        if plan_ids and len(plan_ids)>0:
            plans = doc_plan_repository.list_doc_plan_by_ids(plan_ids=plan_ids, partner_id=partner_id, user_id=user_id)

        
        demand_ids = [int(demand.id) for demand in demands]
        if demand_ids and len(demand_ids)>0:
            orders = receipt_service.list_orders_by_batch_ids(batch_ids=demand_ids, partner_id=partner_id, user_id=user_id)
            orders = orders.rows

        return self.combine_data(demands, plans, orders, plan_ids, partner_id, user_id), total

    def get_demand(self, partner_id, user_id, request):
        demand_id = request.id
        demand = Supply_demand.get_by_id(demand_id=demand_id, partner_id=partner_id)

        if demand.batch_id:
            plan_ids = [int(demand.batch_id)] 
            plans = doc_plan_repository.list_doc_plan_by_ids(plan_ids=plan_ids, partner_id=partner_id, user_id=user_id)
        else:
            plan_ids = None
            plans = None

        orders = receipt_service.list_orders_by_batch_ids(batch_ids=[demand_id], partner_id=partner_id, user_id=user_id)
        orders = orders.rows

        return self.combine_data([demand], plans, orders, plan_ids, partner_id, user_id)[0]

    def get_demand_history(self, partner_id, user_id, request):
        demand_id = request.id
        demand = Supply_demand.get_by_id(demand_id=demand_id, partner_id=partner_id)
        demand_logs, total = Supply_demand_log.get_by_demand_id(partner_id=partner_id, demand_id=demand_id)
        orders = receipt_service.list_orders_by_batch_ids(batch_ids=[demand_id], partner_id=partner_id, user_id=user_id)
        orders = orders.rows

        branch_map = self.get_branch_map(partner_id=partner_id, user_id=user_id)
        
        user_dict = ianvs_service.get_user_dict(partner_id, user_id)

        order_dict = {}
        for order in orders:
            if order_dict.get(int(order.batch_id)):
                order_dict[int(order.batch_id)].append(
                    {
                        'id': order.id,
                        'code': order.code,
                        'status': order.status,
                        'delivery_name': branch_map.get(int(order.delivery_by))
                    }
                )
            else:
                order_dict[int(order.batch_id)] = [{
                    'id': order.id,
                    'code': order.code,
                    'status': order.status,
                    'delivery_name': branch_map.get(int(order.delivery_by))
                }]

        log_list = []
        for d_log in demand_logs:
            log_detail = {
                'id': d_log.id,
                'status': d_log.action_code,
                'updated_by': d_log.updated_by,
                'updated_at':  Timestamp(seconds=int(d_log.updated_at.timestamp())), 
                'orders': order_dict.get(int(demand_id)),
                'updated_by_name': d_log.updated_name
            }
            log_list.append(log_detail)
        create_log_detail = {
            'id': demand.id,
            'status': 'CREATE',
            'updated_by': demand.created_by,
            'updated_at':  Timestamp(seconds=int(demand.created_at.timestamp())), 
            'orders': order_dict.get(int(demand_id)),
            'updated_by_name': demand.created_name
        }
        log_list.append(create_log_detail)
        # 排序
        log_list = sorted(log_list, key = lambda e:e["updated_at"].seconds, reverse=True)
        return log_list, total

    def list_product_by_ids(self, partner_id, user_id, request):
        demand_id = request.id
        demand_products, total = Supply_demand_product.get_by_demand_id(demand_id=demand_id)

        demand = Supply_demand.get(demand_id)

        product_list = []
        unfinish_products_map = receipt_service.get_unifish_receive_products(demand.receive_by, partner_id=partner_id, user_id=user_id)
        for product in demand_products:
            product_detail = {
                'id': product.id,
                'product_id': product.product_id,
                'product_code': product.product_code,                
                'product_name': product.product_name,
                'category_id': product.product_category_id,
                'category_name': product.product_category_name,
                'min_quantity': product.min_quantity,
                'increment_quantity': product.increment_quantity,
                'max_quantity': product.max_quantity,
                'quantity': product.quantity,
                'unit_name': product.unit_name,
                'spec': product.spec,
                'store_unfinish_qty': unfinish_products_map.get(product.product_id, 0),
                'circle_type': product.distribution_circle,
                'unit_id': product.unit_id
            }
            product_list.append(product_detail)
        return product_list, total

    def update_products(self, partner_id, user_id, request):
        demand_id = request.id
        remark = request.remark
        updated_products = request.products
        
        demand_db = Supply_demand.get_by_id(demand_id=demand_id, partner_id=partner_id)
        if demand_db == None:
            raise DataValidationException("订货单不存在")

        # 作废/准备拆单时会锁定处理状态为正在处理中，此时不再允许更新订货单信息
        if demand_db.process_status == 'PROCESSING':
            raise DataValidationException("订货单正在处理中，不允许更新")

        # 只有刚初始化和被驳回的订货单才能订货
        if demand_db.status not in ('INITED', 'CAL_DONE', 'REJECTED'):
            raise DataValidationException("订货单状态不正确:{}".format(demand_db.status))
        
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        updated_list = []
        zero_flag = True
        for product in updated_products:
            if product.quantity != 0:
                zero_flag = False
            updated_args = {
                'id': product.id,
                'quantity': product.quantity,
                'product_id': product.product_id,
                # 'updated_by':user_id

            }
            updated_list.append(updated_args)
        Supply_demand_product.update_products(updated_list)
        demand_update_args = {
            'id': demand_id, 
            'remark': remark, 
            'updated_by': user_id,
            'updated_name': operator_name
            }
        if zero_flag:
            demand_update_args['has_product'] = 0
        else:
            demand_update_args['has_product'] = 1
        Supply_demand.update_demands([demand_update_args])
        return {'demand_id':demand_id, 'products': updated_list}

    def get_product_detail(self, partner_id, user_id, request):
        product_id = request.product_id

        product_info = metadata_service.get_product(product_id, partner_id=partner_id, user_id=user_id)

        attribute_relation_info = metadata_service.get_attribute_info_by_product_id(product_id, 'attribute-region', partner_id, user_id)
        if attribute_relation_info:
            attribute_relation_info = attribute_relation_info[0].get('fields', {})
        else:
            attribute_relation_info = {}

        distribution_region_name = None
        distribution_name = None
        distribution_type = None
        distribution_relation_info = metadata_service.get_attribute_info_by_product_id(product_id, 'distribution-region', partner_id, user_id)
        if distribution_relation_info:
            distribution_relation_info = distribution_relation_info[0]
            distribution_region_id = distribution_relation_info.get('target_entity_id')
            distribution_region = metadata_service.get_region_by_id(int(distribution_region_id), 'distribution', partner_id=partner_id, user_id=user_id)
            distribution_region_name = distribution_region.get('name')

            distribution_relation_info = distribution_relation_info.get('fields', {})
            distribution_id = distribution_relation_info.get('distribution_center_id')
            distribution_center = metadata_service.get_distribution_center(int(distribution_id), partner_id=partner_id, user_id=user_id)
            distribution_name = distribution_center.get('name')
            distribution_type = 'NMD'
        else:
            purchase_relation_info = metadata_service.get_attribute_info_by_product_id(product_id, 'purchase-region', partner_id, user_id)
            if purchase_relation_info:
                distribution_relation_info = purchase_relation_info[0]
                distribution_region_id = distribution_relation_info.get('target_entity_id')
                distribution_region = metadata_service.get_region_by_id(int(distribution_region_id), 'purchase', partner_id=partner_id, user_id=user_id)
                distribution_region_name = distribution_region.get('name')

                distribution_relation_info = distribution_relation_info.get('fields', {})
                distribution_id = distribution_relation_info.get('vendor_id')
                distribution_center = metadata_service.get_vendor_center(int(distribution_id), partner_id=partner_id, user_id=user_id)
                distribution_name = distribution_center.get('name')
                distribution_type = 'PUR'
            else:
                distribution_relation_info = {}


        product_detail = {
                'product_id': product_id,
                'product_code': product_info.get('code'),                
                'product_name': product_info.get('name'),  

                'allow_adjust': attribute_relation_info.get('allow_adjust', False),
                'allow_stocktake': attribute_relation_info.get('allow_stocktake', False),
                'allow_transfer': attribute_relation_info.get('allow_transfer', False),
                'allow_self_purchase': attribute_relation_info.get('allow_self_picking', False),
                'circle_per_thousand': attribute_relation_info.get('circle_per_thousand'),
                'replenish_method': attribute_relation_info.get('replenish_method'),
                'safe_stock_method': attribute_relation_info.get('safe_stock_method'),
                'unfreeze': attribute_relation_info.get('unfreeze'),

                'distribution_type': distribution_type,
                'distribution_region_name': distribution_region_name,
                'distribution_name': distribution_name,
                'planned_arrival_days': distribution_relation_info.get('planned_arrival_days'),
                'circle_type': distribution_relation_info.get('circle_type'),
                'cycles': distribution_relation_info.get('cycles'),
                'start_date': Timestamp(seconds=int(datetime.strptime(distribution_relation_info.get('start_date', '2000-01-01T00:00:00Z'), "%Y-%m-%dT%H:%M:%SZ").timestamp())), 
                'interval_days': distribution_relation_info.get('interval_days'),
                'allow_main_order': distribution_relation_info.get('allow_main_order', False)
        }

        return product_detail

    def deal_demand_by_id(self, demand_id, action, partner_id=None, user_id=None):
        demand = Supply_demand.get_by_id(demand_id=demand_id, partner_id=partner_id)
        if not demand:
            raise NoResultFoundError("demand not exist")

        message = {
            "partner_id": partner_id,
            "user_id": user_id,
            "demand_ids": [demand.id]
        }
        
        if action == Demand_enum.SUBMITTED.code:
            if demand.type == Demand_type.SD.code:
                # 如果是门市订货单，则必须要先成品订货完成且原料拆减完成
                if demand.status not in (Demand_enum.REJECTED.code, Demand_enum.INITED.code):
                    raise StatusUnavailable("门市订货单的确认要在初始化或驳回状态才能执行")
            else:
                if demand.status not in (Demand_enum.INITED.code, Demand_enum.REJECTED.code):
                    raise StatusUnavailable("非门市订货单确认要在初始化或者驳回状态才能执行")

        if action == "REJECTED":
            if demand.status not in (Demand_enum.SUBMITTED.code, Demand_enum.CONFIRMED.code):
                raise StatusUnavailable("订货单驳回要在确认状态")

        if action == "reset":
            if demand.status not in (Demand_enum.INITED.code, Demand_enum.REJECTED.code):
                raise DataValidationException("订货单类型错误")
            demand.has_product = 0
            demand.status = "INITED"
            DBSession.add(demand)
            return Supply_demand_product.reset_quantity(demand_id)
        
        if action == "delete":
            if demand.status != Demand_enum.INITED.code:
                raise StatusUnavailable("订货单状态错误")
            if demand.type == Demand_type.SD.code:
                # 门市订货单不能删除
                raise DataValidationException("订货单类型错误")
            Supply_demand.delete_demand_and_product_by_id(demand_id)
            return True

        if action == "APPROVED":
            if demand.process_status in (Demand_enum.INITED.code, Demand_enum.PROCESSING.code, Demand_enum.FAILED.code):
                # 单子审核后需要发送消息去拆减成要货单
                # public(MessageTopic.DEMAND_STORE_DEMAND_ORDER_TOPIC, message)
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.DEMAND_STORE_DEMAND_ORDER_TOPIC,
                                        message=message)
                logger.info("发送主配拆单消息, demand.id:{}".format(demand.id))
        
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        demand_update_args = {
            "id": demand.id, 
            "status": action,
            "updated_by": user_id,
            "updated_name": username
        }

        demand_log = {
            "partner_id": partner_id,
            "store_id": demand.receive_by,
            "demand_id": demand.id,
            "demand_code": demand.code,
            "action_code": action,
            "action_name": Demand_enum.get_name_by_code(action),
            "updated_name": username,
            "created_by": user_id,
            "created_at": datetime.utcnow(),
            "updated_by": user_id,
            "updated_at": datetime.utcnow(),
        }
        Supply_demand.update_demands([demand_update_args], [demand_log])

        if action in (Demand_enum.SUBMITTED.code, Demand_enum.APPROVED.code, Demand_enum.REJECTED.code):
            # 清理订货单待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=demand.receive_by,
                                             doc_type="demand"))

        return True


mobile_demand_service = MobileDemandService()
