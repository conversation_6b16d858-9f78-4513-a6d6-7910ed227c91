# -*- coding: utf-8 -*-
from sqlalchemy import func
from ast import literal_eval
import logging
from datetime import datetime, timedelta, date,timezone
from decimal import Decimal
import json
from google.protobuf.timestamp_pb2 import Timestamp
from google.protobuf.message import Message as PbMessage
from hex_exception import RecordAlreadyExist
from supply import logger, time_cost
from supply.api import handle_attachments, handle_request_attachments
from supply.driver.mq import mq_producer
from supply.error.exception import NoResultFoundError, StatusUnavailable, DataValidationException, \
    DealInventoryException, ReturnInventoryException

from supply.utils.helper import set_model_from_db, convert_to_int, convert_to_datetime, set_db, get_product_map, \
    get_branch_list_map, MessageTopic
from supply.utils.encode import encodeUTF8
from supply.utils.resulting import ErrorCode
from supply.utils.snowflake import gen_snowflake_id
from supply.utils import pb2dict

from supply.client.metadata_service import metadata_service
from supply.client.inventory_service import inventory_service
from supply.client.receipt_service import receipt_service
from supply.task.message_service_pub import MessageServicePub

from supply.model.returns import ReturnModel, ReturnProductModel, ReturnLogModel
from supply.model.third_party import ThirdParty
from supply.model.supply_doc_code import Supply_doc_code
from supply.module.returns import get_stub, get_tax, get_tax_price


class MobileReturnService():
    '''退货单相关服务
    service: 
        - create_return_entrace()：新建退货单
        - create_diff_return()：按照配送收货差异单的仓库承担生成退货单
        - get_return_by_id(): 通过receiving_id查询退货单信息
        - list_returns(): 枚举所有退货单
        - list_return_products_by_return_id(): return_id 枚举该退货单上所有商品明细
        - submit_return()：提交退货单
        - reject_return()：驳回退货单
        - confirm_return()：确认退货单
        - update_return_product_quantity()：更新退货单商品数量
        - delivery_return()：确认派送退货单商品
        - approve_return()：审核退货单
        - delete_return()：删除退货单
    '''

    # 判断退货数量是否超库存
    def check_return_available(self, store_id, product_detail_list, partner_id, user_id, sub_type=None, logistics_type=None,action=None):
        # 检查业务配置-是否需要进行负库存校验
        need_check_map = metadata_service.get_neg_inv_config(partner_id=partner_id, user_id=user_id,
                                                             domain="boh.store.return",
                                                             store_id=store_id)
        if not need_check_map.get(action, False):
            return True

        product_ids = []
        for product_detail in product_detail_list:
            product_ids.append(product_detail['product_id'])

        # 获取对应仓位
        if sub_type == 'store':
            relation_branch_type = 'stores'
            if logistics_type == 'PUR': 
                position_relation_type = 'directReturn'  # 直送退货
            else:
                position_relation_type = 'deliveryReturn'  # 配送退货
        if sub_type == 'warehouse':
            relation_branch_type = 'warehouse'
            position_relation_type = 'purchaseReturn' 
        if sub_type == 'machining':
            relation_branch_type = 'machining'
            position_relation_type = 'purchaseReturn' 
        if sub_type:
            relation_filters = {relation_branch_type: [str(store_id)]}
            sub_accounts = metadata_service.get_position_relation_list(relation_filters=relation_filters, partner_id=partner_id, user_id=user_id).get('rows', [])
            if len(sub_accounts)>0:
                sub_accounts = sub_accounts[0]
            else:
                sub_accounts = {}
            position_id = sub_accounts.get('fields', {}).get(position_relation_type) if (sub_accounts and position_relation_type) else None
        sub_account_ids = []
        if position_id:
            sub_account_ids = [int(position_id)]
        
        product_inventory_detail = inventory_service.get_products_inventory_by_branch_id(
                                        branch_id=store_id, product_ids=product_ids, sub_account_ids=sub_account_ids, partner_id=partner_id, user_id=user_id)
        over_product_id_list = []
        over_product_name_list = []
        for product_detail in product_detail_list:
            if not product_detail.get('unit_rate'):
                product_detail['unit_rate'] = 1  # 防止历史数据报错
            if sub_account_ids:
                key = str(sub_account_ids[0]) + str(product_detail['product_id'])
            else:
                key = str(product_detail['product_id'])
            if product_inventory_detail.get(key):
                inventory_qty = product_inventory_detail.get(key).get('quantity_avail', 0)
                if Decimal(inventory_qty).quantize(Decimal('0.********'))-Decimal(product_detail['quantity'])*Decimal(product_detail['unit_rate']) < 0:
                    product_info = metadata_service.get_product(product_id=int(product_detail['product_id']), 
                                        partner_id=partner_id, user_id=user_id)
                    logging.info('{} return_qty_is_larger_than_inventory {},{}'.format(product_detail['product_id'], inventory_qty, Decimal(product_detail['quantity'])*Decimal(product_detail['unit_rate'])))
                #     raise DataValidationException('{}库存不足，不允许退货!'.format(product_info['name']))
                # else:
                #     continue
                    over_product_id_list.append(str(product_detail['product_id']))
                    over_product_name_list.append(product_info['name'])
            else:
                product_info = metadata_service.get_product(product_id=int(product_detail['product_id']), 
                                        partner_id=partner_id, user_id=user_id)
                logging.info('{} return_qty_is_larger_than_inventory'.format(product_detail['product_id']))
                # raise DataValidationException('{}库存不足，不允许退货!'.format(product_info['name']))
                over_product_id_list.append(str(product_detail['product_id']))
                over_product_name_list.append(product_info['name'])
        if over_product_name_list:
            raise ReturnInventoryException('以下商品库存不足，不允许退货：{}'.format(over_product_name_list),
                                           detail=over_product_id_list)
        return True

    # 判断原单退货是否超退
    def if_over_return_by_rec(self, source_code, product_detail, partner_id, user_id, return_id=None):
        """ 
        检查原单退货的是否超退, 
        WARNING: 历史遗留问题，前端传入的receiving_id其实是receiving_code
        更新单据时，必须传入已有的return_id
        """

        # 新建时的校验
        if not return_id:
            # 1、如果收货单存在新建/提交/驳回状态的退货单，则不允许建新退单
            # count, return_dbs = ReturnModel.list_returns(partner_id=partner_id, source_code=source_code, status=['INITED', 'SUBMITTED', 'REJECTED'])
            count = ReturnModel.get_return_by_source_code(source_code,partner_id, ['INITED', 'SUBMITTED', 'REJECTED'])
            if count:
                raise DataValidationException('存在新建/提交/驳回状态的退货单，不允许重复创建') 
        # 对于驳回和新建的单子进行校验
        else:
            return_db = ReturnModel.get_return_by_id(int(return_id), partner_id)
            count, return_dbs = ReturnModel.list_returns(partner_id=partner_id, source_code=source_code, status=['INITED', 'SUBMITTED'])
            for renturn_db in return_dbs:
                if return_db.status in ('INITED', 'REJECTED', 'SUBMITTED') and renturn_db.id!=return_id:
                    raise DataValidationException('存在新建/提交/驳回状态的退货单，请先确认！') 

        # 2、校验数量
        # a。获取该收货单收货数量, key:int(product_id), value:实际收货数量
        actual_rec_p_dict = {}
        rec_product_list = receipt_service.get_receive_products_by_receive_code(receive_code=str(source_code), 
                                                partner_id=partner_id, user_id=user_id)
        rec_product_list = rec_product_list.rows
        for rec_p in rec_product_list:
            actual_rec_p_dict[int(rec_p.product_id)] = rec_p.receive_quantity

        # b。捞取该收货单已创建的退货单, key:int(product_id), value:已退总数
        exist_ret_p_dict = ReturnProductModel.get_sum_return_quantity_by_source_code(source_code, partner_id=partner_id)
        # c。校验：已退数量+此次退货数量 > 实际收货数 False
        for product in product_detail:
            if isinstance(product, PbMessage):
                product = pb2dict(product)
            product_id = int(product.get('product_id',0))
            
            # 此次退货数量
            return_qty = product.get('quantity', 0) 
            return_qty = Decimal(return_qty).quantize(Decimal('0.********'))
            # 实际收货数
            receive_qty = actual_rec_p_dict.get(product_id) if actual_rec_p_dict.get(product_id) else 0
            receive_qty = Decimal(receive_qty).quantize(Decimal('0.********'))
            # 已退数量
            exist_return_qty = exist_ret_p_dict.get(product_id) if exist_ret_p_dict.get(product_id) else 0
            exist_return_qty = Decimal(exist_return_qty).quantize(Decimal('0.********'))

            if exist_return_qty+return_qty > receive_qty:
                # print(exist_return_qty, return_qty, receive_qty, exist_return_qty+return_qty)
                raise DataValidationException('{}超出退货限制{}'.format(product.get('product_name'), exist_return_qty+return_qty-receive_qty)) 

        return True
    
    # 获取退货单详情
    def get_return_by_id(self, return_id, partner_id, user_id):
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        if return_db:
            delivery_by_map = {}
            delivery_by_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center", branch_ids=[return_db.return_to],
                                             partner_id=partner_id, user_id=user_id) 
            return_obj = return_db.serialize(conv=True)
            return_obj['return_to_name'] = delivery_by_map.get(int(return_db.return_to), {}).get('name')
            return_obj['phone'] = delivery_by_map.get(int(return_db.return_to), {}).get('tel')
            return_obj['attachments'] = handle_attachments(return_db.attachments)
            return return_obj
        return {}

    # 枚举退货单商品
    def list_return_products_by_return_id(self, return_id,limit=None, offset=None, sort=None, order=None, partner_id=None, user_id=None):
        count = None
        count,return_products_db_list = ReturnProductModel.list_return_products_by_return_id(return_id, limit, offset, sort, order, partner_id)

        filter_product_ids=[]
        for return_product_db in return_products_db_list:
            filter_product_ids.append(return_product_db.product_id)

        filter_category_list = metadata_service.get_product_category_list(partner_id=partner_id,
                                                                          user_id=user_id)
        filter_category_list = filter_category_list.get('rows')
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        filter_product_details = metadata_service.get_product_list(ids=filter_product_ids, include_units=True,
                                                                    return_fields='name,code,category,status,units,model_name',
                                                                   partner_id=partner_id, user_id=user_id).get('rows')
        filter_product_details_dict = {}
        if filter_product_details:
            for i in filter_product_details:
                if i.get('category'):
                    category_id = int(i['category'])
                    filter_product_details_dict[str(i['id'])] = {
                            'id':key_filter_category_dict.get(str(category_id)).get('id'),
                            'code': key_filter_category_dict.get(str(category_id)).get('code'),
                            'name':key_filter_category_dict.get(str(category_id)).get('name'),
                        }

        return_products_list = []
        if return_products_db_list is not None and len(return_products_db_list) > 0:
            for return_product_db in return_products_db_list:
                return_product_obj = return_product_db.serialize(conv=True)
                return_product_obj['category_code'] = filter_product_details_dict.get(str(return_product_obj['product_id']), {}).get('code')
                return_product_obj['category_id'] = int(filter_product_details_dict.get(str(return_product_obj['product_id']), {}).get('id', 0))
                return_product_obj['category_name'] = filter_product_details_dict.get(str(return_product_obj['product_id']), {}).get('name')
                return_product_obj["sum_price"] = return_product_obj.get("price_tax", 0) * return_product_obj.get(
                    "quantity", 0)
                return_product_obj['attachments'] = handle_attachments(return_product_db.attachments)
                return_products_list.append(return_product_obj)
            return count, return_products_list
        return 0, None
  
    # 枚举退货单
    def list_returns(self, partner_id, user_id, request):
        return_bys = request.store_ids
        return_tos = request.return_tos
        status=request.status
        code = request.code
        logistics_type=request.logistics_type
        type=request.type
        sub_type=request.sub_type
        source_id = request.source_id
        source_code = request.source_code

        return_date_from=request.return_date_from
        return_date_to=request.return_date_to
        delivery_date_from=request.delivery_date_from
        delivery_date_to=request.delivery_date_to

        sort = request.sort
        order = request.order
        limit = request.limit
        offset = request.offset

        ids = request.ids
        if len(ids) == 0:
            ids=None
        
        storeIdList = []
        for storeId in return_bys:
            storeIdList.append(int(storeId))
        return_bys = storeIdList

        returnToList = []
        for return_to in return_tos:
            returnToList.append(int(return_to))
        return_tos = returnToList

        count, return_db_list = ReturnModel.list_returns(partner_id=partner_id, 
                                                            code=code,  return_bys=return_bys,
                                                            status=status, logistics_type=logistics_type, 
                                                            type=type, sub_type=sub_type, source_code=source_code, source_id=source_id, 
                                                            start_date=return_date_from, end_date=return_date_to,
                                                            delivery_start_date=delivery_date_from, delivery_end_date=delivery_date_to,
                                                            sort=sort, order=order, limit=limit, offset=offset, ids=ids, return_tos=return_tos)
        if return_db_list:
            branch_map = metadata_service.get_delivery_name_dict(partner_id=partner_id, user_id=user_id)
            return_list = []
            for return_db in return_db_list:
                return_obj = return_db.serialize(conv=True)
                return_obj['return_to_name'] = branch_map.get(int(return_obj['return_to']))
                return_obj['attachments'] = handle_attachments(return_db.attachments)
                return_list.append(return_obj)
            return count, return_list
        return 0, None
    
    # 退货单任务分配入口
    def create_return_entrance(self, return_by, return_delivery_date, 
                                    type, sub_type, logistics_type,
                                    return_reason, products, request_id,
                                    partner_id, user_id,
                                    remark=None, attachments=None, source_id=None, source_code=None):
        """
        退货单任务分配入口
        根据sub_type判断退货单创建流程
        """
        res = {"payload": False}
        return_ids = []
        # 判断单据是否重复创建
        exist_return_db = ReturnModel.get_by_request_id(request_id, partner_id)
        if exist_return_db:
            raise DataValidationException('请勿重复提交创建请求！')
        
        returns_dict = {}
        for product_detail in products:
            if returns_dict.get(product_detail.return_to):
                returns_dict[product_detail.return_to].append(product_detail)
            else:
                returns_dict[product_detail.return_to]=[product_detail]
    
        # 仓库/加工中心退货
        if sub_type == 'warehouse' or sub_type == 'machining':
            logistics_type = 'PUR'
            for key, value in returns_dict.items():
                return_to = key
                if value[0].logistics_type:
                    logistics_type = value[0].logistics_type
                return_id = self.create_return_warehouse(return_by=return_by, return_to=return_to, 
                                    return_delivery_date=return_delivery_date, 
                                    type=type, sub_type=sub_type, return_reason=return_reason,
                                    logistics_type=logistics_type, request_id=request_id,
                                    product_detail=value, partner_id=partner_id, user_id=user_id, 
                                    remark=remark, attachments=attachments, 
                                    source_id=source_id, source_code=source_code)
                return_ids.append(return_id)

        # 门店退货
        else:
            # 判断门店状态是否允许退货
            status_check = metadata_service.check_store_status(return_by, partner_id, user_id)
            if not status_check:
                raise DataValidationException('请检查门店的开店状态！')
            
            # 如果是原单退货且收货单存在新建/提交/驳回状态的退货单，则不允许建新退单
            if type == 'BO':
                # count, return_dbs = ReturnModel.list_returns(partner_id=partner_id, source_code=source_code, status=['INITED', 'SUBMITTED', 'REJECTED'])
                count = ReturnModel.get_return_by_source_code(source_code, partner_id, ['INITED', 'SUBMITTED', 'REJECTED'])
                if count:
                    raise DataValidationException('存在新建/提交/驳回状态的退货单，不允许重复创建') 
            for key, value in returns_dict.items():
                return_to = key
                if value[0].logistics_type:
                    logistics_type = value[0].logistics_type
                return_id = self.create_return_store(return_by=return_by, return_to=return_to, 
                                    return_delivery_date=return_delivery_date, 
                                    type=type, sub_type=sub_type, return_reason=return_reason,
                                    logistics_type=logistics_type, request_id=request_id,
                                    product_detail=value, partner_id=partner_id, user_id=user_id, 
                                    remark=remark, attachments=attachments, 
                                    source_id=source_id, source_code=source_code)
                return_ids.append(return_id)
            # 清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=return_by,
                                             doc_type="return"))
        res["payload"] = True
        res["return_id"] = return_ids
        return res
    
    # 门店退货单
    def create_return_store(self, return_by, return_to, return_delivery_date, 
                                    type, sub_type, return_reason,
                                    logistics_type, request_id,
                                    product_detail, partner_id, user_id, 
                                    remark=None, attachments=None, 
                                    source_id=None, source_code=None):
        """
        创建门店退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        
        return_id = gen_snowflake_id()
        return_code = Supply_doc_code.get_code_by_type('RETURN_OD',partner_id, None)
        return_date = datetime.now()
        return_delivery_date = datetime.fromtimestamp(return_delivery_date.seconds)

        # 创建退货单商品
        product_nums = 0
        delivery_p_list = []
        rec_p_list = []
        
        product_id_list = []
        product_code_list = []
        for product_d in product_detail:
            product_code_list.append(product_d.product_code)
            product_id_list.append(int(product_d.product_id))
        
        # 转换单位
        product_unit_dict = {}
        products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                          include_units=True,
                                                          return_fields='id,code,name',
                                                          partner_id=partner_id, user_id=user_id).get('rows', [])
        for product_detail_info in products_info:
            if product_detail_info.get('units'):
                product_unit_dict[str(product_detail_info['id'])] = {}
                for unit in product_detail_info.get('units'):
                    if unit.get('purchase') and unit['purchase']==True:
                        product_unit_dict[str(product_detail_info['id'])]['purchase'] = unit['rate'] if unit.get('rate') else 0
                    if unit.get('order') and unit['order']==True:
                        product_unit_dict[str(product_detail_info['id'])]['order'] = unit['rate'] if unit.get('rate') else 0
                    if unit.get('default') and unit['default']==True:
                        product_unit_dict[str(product_detail_info['id'])]['default'] = unit['rate'] if unit.get('rate') else 0
            else:
                product_unit_dict[str(product_detail_info['id'])]['default'] = 0  
        
        # 直送单需要拿到价格，获取商品成本
        if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
            # 原单从收货单取
            if type == "BO":
                product_tax_dict = self.get_receive_prod_price(source_code, partner_id, user_id)
                stub = 'metadata'
            else:
                stub = get_stub(partner_id=partner_id, is_original_receipt=True)
                product_tax_dict = get_tax(
                        stub=stub,vendor_id=return_to, product_id_list=product_id_list,
                        partner_id=partner_id, user_id=user_id, store_id=return_by
                    )

            # 订货单位*订货rate=采购单位*采购rate
            # 采购单位/订货单位=订货rate/采购rate

            # 订货单价*订货单位=采购单价*采购单位
            # 订货单价 = 采购单价*采购单位/订货单位
            # 订货单价 = 采购单价*订货rate/采购rate
            purchase2order_rate_dict = get_tax_price(stub, product_unit_dict)

        product_insert_list = []
        for product in product_detail:
            product_nums += 1
            product_id = product.product_id
            attachment = handle_request_attachments(product.attachments)
            product = pb2dict(product)
            product['attachments'] = attachment
            product['id'] = gen_snowflake_id()
            product['return_id'] = return_id
            product['return_by'] = return_by
            product['return_date'] = return_date
            product['status'] = 'INITED'
            product['partner_id'] = partner_id
            product['created_by'] = user_id
            product['updated_by'] = user_id
            product['updated_name'] = operator_name
            product['created_name'] = operator_name
            product['created_at'] = datetime.now()
            product['updated_at'] = datetime.now()
            if not product.get('unit_rate'):
                product['unit_rate'] = product_unit_dict.get(str(product_id), {}).get('order', 1)
            
            if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                product_id = product.get('product_id')
                prod_dict = product_tax_dict.get(product_id)
                if not prod_dict:
                    raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))
                
                tax_rate = prod_dict.get('rate',0)
                if type == 'BO': # 原单价格从收货单里取，不必做转换
                    price = prod_dict.get('no_tax')
                    price_tax = prod_dict.get('tax')
                else:
                    price = prod_dict.get('no_tax', 0)*purchase2order_rate_dict.get(product_id, 1)
                    price_tax = prod_dict.get('tax', 0)*purchase2order_rate_dict.get(product_id, 1)

                product['tax_rate'] = tax_rate
                product['price'] = price
                product['price_tax'] = price_tax
            
            product_insert_list.append(product)
            
            #---ForReceiptStart---#
            # ForReceiptDelivery
            delivery_p = {
                'delivery_by': return_by, 
                'product_id': product.get('product_id'),
                'product_code': product.get('product_code'),
                'product_name': product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.get('quantity'),
                'unit_id':product.get('unit_id'),
                'unit_name':product.get('unit_name'),
                'unit_rate':product.get('unit_rate', 1),
                # 'unit_spec':product.unit_spec,
                # 'category_id':product.category_id}
            }
            if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                delivery_p['cost_price'] = product_tax_dict.get(product.get('product_id'),{}).get('no_tax', 0)*purchase2order_rate_dict.get(product.get('product_id'), 1)
                delivery_p['tax_price'] = product_tax_dict.get(product.get('product_id'),{}).get('tax', 0)*purchase2order_rate_dict.get(product.get('product_id'), 1)
                delivery_p['tax_rate'] = product_tax_dict.get(product.get('product_id'),{}).get('rate', 0)
            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by': int(return_to), 
                'product_id': product.get('product_id'),
                'product_code': product.get('product_code'),
                'product_name': product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.get('quantity'),
                'unit_id':product.get('unit_id'),
                'unit_name':product.get('unit_name'),
                'unit_rate':product.get('unit_rate', 1),
                # 'unit_spec':product.unit_spec,
                # 'category_id':product.category_id
            }
            if logistics_type == 'PUR' and type not in ('IAD', 'CAD'):
                rec_p['cost_price'] = product_tax_dict.get(product.get('product_id'),{}).get('no_tax', 0)*purchase2order_rate_dict.get(product.get('product_id'), 1)
                rec_p['tax_price'] = product_tax_dict.get(product.get('product_id'),{}).get('tax', 0)*purchase2order_rate_dict.get(product.get('product_id'), 1)
                rec_p['tax_rate'] = product_tax_dict.get(product.get('product_id'),{}).get('rate', 0)
            rec_p_list.append(rec_p)

        ReturnProductModel.create_returns_products(product_insert_list)

        args = {
            'id':return_id,
            'code':return_code,
            'return_by':return_by,
            'return_delivery_date':return_delivery_date,
            'type':type,
            'sub_type':sub_type,
            'logistics_type':logistics_type,
            'return_reason':return_reason,
            'status':'INITED',
            'partner_id':partner_id,
            'created_at':datetime.now(),
            'created_by':user_id,
            'updated_at':datetime.now(),
            'updated_name':operator_name,
            'created_name':operator_name,
            'updated_by':user_id,
            'return_to':return_to,
            'source_id':source_id,
            'source_code':source_code,
            'return_date':return_date,
            'product_nums':product_nums,
            'request_id':request_id
        }
        if remark:
            args.update(dict(remark=remark))
        if attachments:
            args.update(dict(attachments=handle_request_attachments(attachments)))
        if type in ('IAD', 'CAD'):
            args.update(dict(status='APPROVED'))
        
        #---ForReceiptStart---#
        batch_type = 'RETURN'
        if type == 'IAD':
            batch_type = 'RETURN_ADJUST'
        elif type == 'CAD':
            batch_type = 'CHECKING_ADJUST'
        
        # 退货单创建到发货单
        today = datetime.today()
        delivery_res = receipt_service.create_deliverys(
            demand_type=type,
            batch_id=return_id, batch_code=return_code, batch_type=batch_type, 
            # id=None, code=None, 
            order_id=return_id, order_code=return_code, 
            demand_id=source_id if type == 'BO' else return_id, 
            demand_code=source_code if type == 'BO' else return_code, # TODO 原单退货的门店出库单，原单收货单单号暂时塞到demand_id里，处理不优美，后期需要改
            # receive_id=None, receive_code=None, 
            receive_by=int(return_to), delivery_by=return_by, 
            distr_type=logistics_type, 
            delivery_date=return_delivery_date, demand_date=return_date,
            # arrival_date=None,  expect_date=None, 
            # storage_type=None,
            products=delivery_p_list, main_branch_type='S',
            partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)

        main_branch_type=None
        if logistics_type == 'PUR':
            main_branch_type = 'V'
        elif logistics_type == 'PAD':
            main_branch_type = 'M'
        else:
            main_branch_type = 'W'
        
        # 退货单创建到仓库收货单
        # 原单退货 —— batch_code是收货单原单code
        if source_code:
            receipt_service.create_receives(
                demand_type=type,
                batch_id=return_id, batch_code=str(source_code), batch_type=batch_type,  
                order_id=return_id, order_code=return_code,  
                delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by, 
                distr_type=logistics_type, main_branch_type=main_branch_type,
                delivery_date=return_delivery_date, demand_date=return_date,
                products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)
        # 非原单退货
        else: 
            receipt_service.create_receives(
                demand_type=type,
                batch_id=return_id, batch_code=return_code, batch_type=batch_type,  
                order_id=return_id, order_code=return_code,  
                delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by, 
                distr_type=logistics_type, main_branch_type=main_branch_type,
                delivery_date=return_delivery_date, demand_date=return_date,
                products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)
            
        #---ForReceiptEnd---#

        # 创建退货单
        ReturnModel.create_returns(**args)
        if type in ('IAD', 'CAD'):
            # 调整单的退货直接更新成终态——已提货，仓库那边也需要判断并直接入库
            self.deal_return(return_id=return_id, action='delivery', partner_id=partner_id, user_id=user_id)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.now()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)
        
        return return_id

    # 仓库退货单
    def create_return_warehouse(self, return_by, return_to, return_delivery_date, 
                                    type, sub_type, return_reason,
                                    logistics_type, request_id,
                                    product_detail, partner_id, user_id, 
                                    remark=None, attachments=None, 
                                    source_id=None, source_code=None):
        """
        创建仓库/加工中心退货单
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        
        return_id = gen_snowflake_id()
        return_code = Supply_doc_code.get_code_by_type('RETURN_OD',partner_id, None)
        return_date = datetime.now()
        return_delivery_date = datetime.fromtimestamp(return_delivery_date.seconds)

        # 创建退货单商品
        product_nums = 0
        delivery_p_list = []
        rec_p_list = []
        
        # 直送单需要拿到价格，获取商品成本
        product_id_list = []
        product_id_list_int = []
        for product in product_detail:
            product_id_list.append(str(product.product_id))
            product_id_list_int.append(int(product.product_id))
        if sub_type == 'warehouse':
            product_tax_dict = metadata_service.get_tax_list(
                            vendor_id=return_to, product_id_list=product_id_list, 
                                warehouse_id=return_by, valid_time=datetime.now(),
                                    partner_id=partner_id, user_id=user_id)
        elif sub_type == 'machining':
            product_tax_dict = metadata_service.get_tax_list(
                            vendor_id=return_to, product_id_list=product_id_list, 
                                machining_id=return_by, valid_time=datetime.now(),
                                    partner_id=partner_id, user_id=user_id)
        product_unit_dict = metadata_service.get_product_unit(ids=product_id_list_int, partner_id=partner_id, user_id=user_id)

        product_insert_list = []
        for product in product_detail:
            product_nums += 1
            product = pb2dict(product)
            product['id'] = gen_snowflake_id()
            product['attachments'] = str(product.get('attachments')) if product.get('attachments') else ['']
            product['return_id'] = return_id
            product['return_by'] = return_by
            product['return_date'] = return_date
            product['status'] = 'INITED'
            product['partner_id'] = partner_id
            product['created_by'] = user_id
            product['updated_by'] = user_id
            product['updated_name'] = operator_name
            product['created_name'] = operator_name
            product['created_at'] = datetime.now()
            product['updated_at'] = datetime.now()
            product_id = product.get('product_id')
            product['unit_rate'] = product_unit_dict.get(product_id).get(
                int(product.get('unit_id',))) if product.get('unit_id') else 1
            product_tax_detail = product_tax_dict.get(product_id)
            if not product_tax_detail:
                raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))
            product['tax_rate'] = product_tax_detail.get('rate', 0)
            product['price'] = product_tax_detail.get('no_tax', 0)
            product['price_tax'] = product_tax_detail.get('tax', 0)
            product_insert_list.append(product)
            
            #---ForReceiptStart---#
            # ForReceiptDelivery
            delivery_p = {
                'delivery_by': return_by, 
                'product_id': product.get('product_id'),
                'product_code': product.get('product_code'),
                'product_name': product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.get('quantity'),
                'unit_id':product.get('unit_id'),
                'unit_name':product.get('unit_name'),
                'unit_rate':product.get('unit_rate', 1),
                'cost_price':product_tax_dict.get(product.get('product_id')).get('no_tax') if product_tax_dict.get(product.get('product_id')) else 0,
                'tax_price':product_tax_dict.get(product.get('product_id')).get('tax') if product_tax_dict.get(product.get('product_id')) else 0,
                'tax_rate':product_tax_dict.get(product.get('product_id')).get('rate') if product_tax_dict.get(product.get('product_id')) else 0
                # 'unit_spec':product.unit_spec,
                # 'category_id':product.category_id
            }
            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by': int(return_to), 
                'product_id': product.get('product_id'),
                'product_code': product.get('product_code'),
                'product_name': product.get('product_name'),
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.get('quantity'),
                'unit_id':product.get('unit_id'),
                'unit_name':product.get('unit_name'),
                'unit_rate':product.get('unit_rate', 1),
                'cost_price':product_tax_dict.get(product.get('product_id')).get('no_tax') if product_tax_dict.get(product.get('product_id')) else 0,
                'tax_price':product_tax_dict.get(product.get('product_id')).get('tax') if product_tax_dict.get(product.get('product_id')) else 0,
                'tax_rate':product_tax_dict.get(product.get('product_id')).get('rate') if product_tax_dict.get(product.get('product_id')) else 0
                # 'unit_spec':product.unit_spec,
                # 'category_id':product.category_id
            }
            rec_p_list.append(rec_p)
        
        ReturnProductModel.create_returns_products(product_insert_list)

        args = {
            'id':return_id,
            'code':return_code,
            'return_by':return_by,
            'return_delivery_date':return_delivery_date,
            'type':type,
            'sub_type':sub_type,
            'return_reason':return_reason,
            'status':'INITED',
            'partner_id':partner_id,
            'created_at':datetime.now(),
            'created_by':user_id,
            'updated_at':datetime.now(),
            'updated_name':operator_name,
            'created_name':operator_name,
            'updated_by':user_id,
            'return_to':return_to,
            'source_id':source_id,
            'source_code':source_code,
            'logistics_type':logistics_type,
            'product_nums':product_nums,
            'request_id':request_id
        }
        if remark:
            args.update(dict(remark=remark))
        if attachments:
            args.update(dict(attachments=attachments))
        
        batch_type = 'PUR_RETURN'
        if type == 'IAD':
            batch_type = 'PUR_RETURN_ADJUST'
        elif type == 'CAD':
            batch_type = 'CHECKING_ADJUST'
            # 调整单的退货直接更新成终态
            args.update(dict(status='SUBMITTED'))

        #---ForReceiptStart---#
        main_branch_type = 'W'
        if sub_type == 'machining':
            main_branch_type = 'M'
        # 退货单创建到发货单
        today = datetime.today()
        delivery_res = receipt_service.create_deliverys(
            demand_type=type,
            batch_id=return_id, batch_code=return_code, batch_type=batch_type, 
            # id=None, code=None, 
            order_id=return_id, order_code=return_code, 
            demand_id=return_id, demand_code=return_code, 
            # receive_id=None, receive_code=None, 
            receive_by=int(return_to), delivery_by=return_by, 
            distr_type=logistics_type, 
            delivery_date=return_delivery_date, demand_date=return_date,
            # arrival_date=None,  expect_date=None, 
            # storage_type=None,
            products=delivery_p_list, main_branch_type=main_branch_type,
            remark=remark, reason=return_reason,
            partner_id=partner_id, user_id=user_id)

        # 退货单创建到供应商收货单
        receive_res = receipt_service.create_receives(
            demand_type=type,
            batch_id=return_id, batch_code=return_code, batch_type=batch_type,  
            order_id=return_id, order_code=return_code,  
            delivery_id=int(delivery_res['id']), receive_by=int(return_to), delivery_by=return_by, 
            distr_type=logistics_type, main_branch_type='V',
            delivery_date=return_delivery_date, demand_date=return_date,
            products=rec_p_list, partner_id=partner_id, user_id=user_id, remark=remark, reason=return_reason)
        #---ForReceiptEnd---#

        # 创建退货单
        ReturnModel.create_returns(**args)
        # 调整单的退货直接更新成终态
        if type in ('IAD', 'CAD'):
            self.deal_return(return_id=return_id, action='approve', partner_id=partner_id, user_id=user_id)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.now()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)

        return return_id

    # 处理退货单
    def deal_return(self, partner_id, user_id, return_id, action, reject_reason=None, trans_type=None):
        deal_status = False
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        if not return_db:
            raise NoResultFoundError("未找到退货单！")

        count, return_products_db = ReturnProductModel.list_return_products_by_return_id(return_id, partner_id=partner_id)
        if not count:
            raise NoResultFoundError("退货单不包含上商品！")
        
        if action == 'submit':
            # 时间校验
            now = datetime.now(timezone.utc)
            time_config_map, allow_specified_time = metadata_service.get_time_config(partner_id, user_id, "boh.store.return",
                                                               store_id=return_db.return_by)
            if return_db.type in ('BO', 'NBO') and return_db.sub_type in ('store', 'fs_store') and allow_specified_time and time_config_map.get(return_db.return_by):
                start = time_config_map.get(return_db.return_by)[0]
                end = time_config_map.get(return_db.return_by)[1]
                # if start <= now <= end:
                if now < start or now > end:
                    raise DataValidationException("时间限制, 不允许提交退货单".format())
            if return_db.status == 'INITED' or return_db.status == 'REJECTED':
                # 增加库存校验
                product_detail_list = []
                for return_product_db in return_products_db:
                    product_detail = {}
                    product_detail['product_id'] = return_product_db.product_id
                    product_detail['product_name'] = return_product_db.product_name
                    product_detail['quantity'] = return_product_db.quantity
                    product_detail['unit_rate'] = return_product_db.unit_rate
                    product_detail_list.append(product_detail)
                # 如果是原单退货，校验是否超过收货数量
                if return_db.type=='BO':
                    self.if_over_return_by_rec(source_code=return_db.source_code, 
                                                product_detail=product_detail_list, partner_id=partner_id, 
                                                user_id=user_id, return_id=return_db.id)

                # 实时库存是否允许退货
                self.check_return_available(store_id=return_db.return_by, product_detail_list=product_detail_list, 
                                                        partner_id=partner_id, user_id=user_id, 
                                                        sub_type=return_db.sub_type, logistics_type=return_db.logistics_type,action=action)

                # 状态同步receipt单据
                today = datetime.today()
                entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if rows:
                    delivery_detail = rows[0]
                    receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='SUBMITTED', 
                                                            partner_id=partner_id, user_id=user_id)

                entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if rows:
                    receive_detail = rows[0]
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='SUBMITTED', 
                                                                partner_id=partner_id, user_id=user_id)

                args = {
                    'status':'SUBMITTED',
                    'updated_at':datetime.now(),
                    'updated_by':user_id,
                    'updated_name':operator_name,
                    'review_by':user_id,
                }
                return_db.update_returns(return_id, args, partner_id)

                product_update_list = []
                for return_product_db in return_products_db:
                    p_args = {
                                'id': return_product_db.id,
                                'status':'SUBMITTED',
                                'updated_at':datetime.now(),
                                'updated_by':user_id,
                                'updated_name':operator_name
                            }
                    product_update_list.append(p_args)
                ReturnProductModel.update_products_in_all(updated_products=product_update_list)

                deal_status = True
            else:
                raise StatusUnavailable("只有新建/驳回的退货单可以提交!")

        elif action == 'reject':
            if return_db.status != 'SUBMITTED':
                raise StatusUnavailable("只有提交的单据可以被驳回！")

            # 状态同步receipt单据
            today = datetime.today()
            entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                delivery_detail = rows[0]
                receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='REJECTED', 
                                                            partner_id=partner_id, user_id=user_id)

            entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                receive_detail = rows[0]
                receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='REJECTED', 
                                                                partner_id=partner_id, user_id=user_id)
            args = {
                'status':'REJECTED',
                'updated_at':datetime.now(),
                'updated_by':user_id,
                'updated_name':operator_name,
                'review_by':user_id,
                'reject_reason':reject_reason
            }
            return_db.update_returns(return_id, args, partner_id)
            
            product_update_list = []
            for return_product_db in return_products_db:
                p_args = {
                            'id': return_product_db.id,
                            'status': 'REJECTED',
                            'updated_at': datetime.now(),
                            'updated_by': user_id,
                            'updated_name': operator_name,
                            'review_by': user_id
                        }
                product_update_list.append(p_args)
            ReturnProductModel.update_products_in_all(updated_products=product_update_list)

            deal_status = True
        
        elif action == 'approve':
            if return_db.status != 'SUBMITTED' and return_db.logistics_type == 'NMD':
                raise StatusUnavailable("只有提交的退货单可以审核!")
            product_detail_list = []
            for return_product_db in return_products_db:
                product_detail = {}
                product_detail['product_id'] = return_product_db.product_id
                product_detail['quantity'] = return_product_db.quantity
                product_detail['unit_rate'] = return_product_db.unit_rate
                product_detail_list.append(product_detail)
            self.check_return_available(store_id=return_db.return_by, product_detail_list=product_detail_list, 
                                        partner_id=partner_id, user_id=user_id, 
                                        sub_type=return_db.sub_type, logistics_type=return_db.logistics_type,action=action)

            args = {
                'status':'APPROVED', 
                'updated_at':datetime.now(),
                'updated_name':operator_name,
                'updated_by':user_id,
                'review_by':user_id,
                'inventory_status':'INITED',
            }
            return_db.update_returns(return_id, args, partner_id)

            product_update_list = []
            for return_product_db in return_products_db:
                p_args = {
                            'id': return_product_db.id,
                            'status':'APPROVED', 
                            # 'status':'CONFIRMED',
                            'updated_at':datetime.now(),
                            'updated_name':operator_name,
                            'updated_by':user_id,
                            'review_by':user_id,
                            'inventory_status':'INITED',
                        }
                product_update_list.append(p_args)
            ReturnProductModel.update_products_in_all(updated_products=product_update_list)
            
            # 门店退货
            if return_db.sub_type == 'store':
                # source_type = 'RETURN'
                today = datetime.today()
                # 状态同步receipt单据
                entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if rows:
                    delivery_detail = rows[0]
                    receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='APPROVED', 
                                                                partner_id=partner_id, user_id=user_id)

                entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
                rows = entity.rows
                if not rows:
                    raise NoResultFoundError('NoResultFound!')
                receive_detail = rows[0]
                # 审核之后，仓库退货收货单被激活，状态同步给仓库
                receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='ACTIVATE', 
                                                        partner_id=partner_id, user_id=user_id)
                if trans_type and trans_type == "RefundOnly":
                    self.deal_return(return_id=return_id, action='delivery', partner_id=partner_id, user_id=user_id)
            
            # 仓库/加工中心采购退货
            else:
                # 采购退货，审核之后自动提货，自动入库
                self.deal_return(return_id=return_id, action='delivery', partner_id=partner_id, user_id=user_id)

            deal_status = True

        elif action == 'delivery':
            if return_db.status != 'APPROVED':
                raise StatusUnavailable("only APPROVED data can be delivered!")

            ## receipt.delivery发货，扣库存
            entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if not rows:
                raise NoResultFoundError('NoResultFound!')
            delivery_detail = rows[0]
            receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='AUTO_DELIVERY', 
                                                        partner_id=partner_id, user_id=user_id)

            ## receipt.receive提货/入库
            today = datetime.today()
            entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if not rows:
                raise NoResultFoundError('NoResultFound!')
            receive_detail = rows[0]

            args = {
                'id': return_id,
                'status':'DELIVERED',
                'updated_at':datetime.now(),
                'updated_by':user_id,
                'review_by':user_id,
                'inventory_status':'INITED',
                'updated_name':operator_name
            }

            # 调整单，直接入库
            if return_db.type in ('IAD', 'CAD'):
                receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='AUTO_RECEIVE', 
                                                        partner_id=partner_id, user_id=user_id)
                args['status']='CONFIRMED'
            # 非调整单
            else:
                # 仓库/加工中心采购退货单 & 门店直送退货单，直接推进入库，更新为终态
                if return_db.logistics_type == "PUR":
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='CONFIRM', 
                                                        partner_id=partner_id, user_id=user_id)
                    args['status']='CONFIRMED'

                # 退货收货单做提货操作，更新已提货状态
                else:
                    receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='DELIVERY_OUT', 
                                                partner_id=partner_id, user_id=user_id)

            
            product_update_list = []
            for return_product_db in return_products_db:
                p_args = {
                            'id': return_product_db.id,
                            'status':'DELIVERED',
                            'updated_at':datetime.now(),
                            'updated_by':user_id,
                            'review_by':user_id,
                            'inventory_status':'INITED',
                            'updated_name':operator_name
                        }
                product_update_list.append(p_args)
            ReturnProductModel.update_products_in_all(updated_products=product_update_list, update_return_details=args)
            deal_status = True
        
        elif action == 'confirm':
            if return_db.logistics_type in ('NMD', 'PAD'):
                if return_db.type in ('IAD', 'CAD') and return_db.status != 'DELIVERED':
                    raise StatusUnavailable("单据未提货，不允许确认!")
            else:
                if return_db.status not in ('APPROVED', 'DELIVERYED'):
                    raise StatusUnavailable("单据未审核，不允许确认!")
            args = {
                'status':'CONFIRMED',
                'updated_at':datetime.now(),
                'updated_by':user_id,
                'updated_name':operator_name,
                'review_by':user_id,
                'inventory_status':'INITED',
            }
            return_db.update_returns(return_id, args, partner_id)

            product_update_list = []
            for return_product_db in return_products_db:
                p_args = {
                            'id': return_product_db.id,
                            'status':'CONFIRMED',
                            'updated_at':datetime.now(),
                            'updated_by':user_id,
                            'updated_name':operator_name
                        }
                product_update_list.append(p_args)
            ReturnProductModel.update_products_in_all(updated_products=product_update_list)
            deal_status = True
        
        elif action == 'delete':
            if not (return_db.status == 'INITED' or return_db.status == 'REJECTED'):
                raise StatusUnavailable("只有新建单据可以被删除!")

            # 状态同步receipt单据
            today = datetime.today()
            entity = receipt_service.list_deliverys(batch_id=return_id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                delivery_detail = rows[0]
                receipt_service.deal_delivery_by_id(delivery_id=delivery_detail.id, action='DELETED', 
                                                            partner_id=partner_id, user_id=user_id)

            entity = receipt_service.list_receives(batch_id=return_db.id, partner_id=partner_id, user_id=user_id)
            rows = entity.rows
            if rows:
                receive_detail = rows[0]
                receipt_service.deal_receive_by_id(receive_id=receive_detail.id, action='DELETED', 
                                                                partner_id=partner_id, user_id=user_id)
            
            args = {
                'status':'DELETED',
                'updated_at':datetime.now(),
                'updated_by':user_id,
                'updated_name':operator_name,
                'review_by':user_id
            }
            return_db.update_returns(return_id, args, partner_id)
            
            product_update_list = []
            for return_product_db in return_products_db:
                p_args = {
                            'id': return_product_db.id,
                            'status':'DELETED',
                            'updated_at':datetime.now(),
                            'updated_by':user_id,
                            'updated_name':operator_name,
                            'review_by':user_id
                        }
                product_update_list.append(p_args)
            ReturnProductModel.update_products_in_all(updated_products=product_update_list)
            deal_status = True

        # 清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=return_db.return_by,
                                         doc_type="return"))
        # 记录操作日志
        log_detail = {
            'doc_id': return_id,
            'operation': str(action).upper(),
            'success': deal_status,
            'partner_id': partner_id,
            'created_by': user_id,
            'created_at': datetime.now(),
            'created_name': operator_name
        }
        ReturnLogModel.create_returns_log(**log_detail)
        return {'id': return_id, 'status': deal_status}
        
    # 更新
    def update_return_product_quantity(self, return_id, partner_id, user_id, 
                                products=None, return_reason=None, remark=None, 
                                return_delivery_date=None, return_to=None,
                                attachments=None, logistics_type=None):
        """
        更新退货单及退货商品详情
        """
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        product_id_list = []
        str_product_id_list = []
        returns_db = ReturnModel.get_return_by_id(return_id, partner_id)
        if not return_to:
            return_to = returns_db.return_to
        if products and len(products)>0:
            for product in products:
                product_id_list.append(int(product.product_id))
                str_product_id_list.append(str(product.product_id))

            product_dict = {}
            product_unit_dict = {}
            product_meta_details = metadata_service.get_product_list(ids=product_id_list, 
                                        include_units=True, partner_id=partner_id, user_id=user_id).get('rows', [])
            for product_detail_info in product_meta_details:
                product_dict[int(product_detail_info['id'])] = product_detail_info
                if product_detail_info.get('units'):
                    product_unit_dict[product_detail_info['id']] = {}
                    for unit in product_detail_info.get('units'):
                        if unit.get('purchase') and unit['purchase']==True:
                            product_unit_dict[product_detail_info['id']]['purchase'] = unit['rate'] if unit.get('rate') else 0
                        if unit.get('order') and unit['order']==True:
                            product_unit_dict[product_detail_info['id']]['order'] = unit['rate'] if unit.get('rate') else 0
                        if unit.get('default') and unit['default']==True:
                            product_unit_dict[product_detail_info['id']]['default'] = unit['rate'] if unit.get('rate') else 0
                else:
                    product_unit_dict[product_detail_info['id']]['default'] = 0 
            
            unit_dict = {}
            unit_details = metadata_service.get_unit_list(partner_id=partner_id, user_id=user_id).get('rows', [])
            for unit_detail in unit_details:
                unit_dict[int(unit_detail['id'])] = unit_detail


            # 直送单需要拿到价格，获取商品成本
            product_tax_dict = {}
            if returns_db.logistics_type == 'PUR' and returns_db.type not in ('IAD', 'CAD'):
                if returns_db.sub_type == 'warehouse':
                    product_tax_dict = metadata_service.get_tax_list(
                                    vendor_id=return_to, product_id_list=str_product_id_list, 
                                        warehouse_id=returns_db.return_by, valid_time=datetime.utcnow(),
                                            partner_id=partner_id, user_id=user_id)
                elif returns_db.sub_type == 'machining':
                    product_tax_dict = metadata_service.get_tax_list(
                                    vendor_id=return_to, product_id_list=str_product_id_list, 
                                        machining_id=returns_db.return_by, valid_time=datetime.utcnow(),
                                            partner_id=partner_id, user_id=user_id)
                else:
                    product_tax_dict = metadata_service.get_tax_list(
                            vendor_id=return_to, product_id_list=str_product_id_list, 
                                store_id=returns_db.return_by, valid_time=datetime.utcnow(),
                                    partner_id=partner_id, user_id=user_id)
                
                if returns_db.type == 'BO': # 原单退货价格从收货单里取
                    product_tax_dict = self.get_receive_prod_price(returns_db.source_code, partner_id, user_id)
                # 订货单位*订货rate=采购单位*采购rate
                # 采购单位/订货单位=订货rate/采购rate

                # 订货单价*订货单位=采购单价*采购单位
                # 订货单价 = 采购单价*采购单位/订货单位
                # 订货单价 = 采购单价*订货rate/采购rate
                purchase2order_rate_dict = {}
                if returns_db.sub_type == "store": # 只有门店直送需要转换单位价格，仓库/加工中心采购退货本身业务单位就是采购单位
                    for key, key_value in product_unit_dict.items():
                        purchase_rate = key_value.get('purchase') if key_value.get('purchase') else 1
                        order_rate = key_value.get('order') if key_value.get('order') else 1
                        purchase2order_rate_dict[int(key)] = order_rate/purchase_rate

            new_product_list = []
            update_product_list = []
            delete_product_list = []

            count = 0
            for product in products:
                count += 1
                attachment = handle_request_attachments(product.attachments)
                product = pb2dict(product)
                product['attachments'] = attachment
                if not product.get('id'):
                    product_id = product.get('product_id', 0)
                    product['id'] = gen_snowflake_id()
                    product['return_id'] = return_id
                    product['return_by'] = returns_db.return_by
                    product['return_to'] = return_to
                    product['return_date'] = returns_db.return_date
                    product['status'] = 'INITED'
                    product['partner_id'] = partner_id
                    product['created_by'] = user_id
                    product['updated_name'] = operator_name
                    product['created_name'] = operator_name
                    product['created_at'] = datetime.now()
                    product['updated_at'] = datetime.now()
                    product['returned_quantity'] = product['quantity']
                    product_meta_detail = product_dict.get(int(product_id))
                    if not product_meta_detail:
                        raise NoResultFoundError("未找到商品主档-product_id-{}".format(convert_to_int(product_id)))
                    product['product_name'] = product_meta_detail['name']
                    product['product_code'] = product_meta_detail['code']
                    unit_detail = unit_dict.get(int(product['unit_id']))
                    if not unit_detail:
                        raise NoResultFoundError("未找到商品单位-unit_id-{}".format(convert_to_int(product['unit_id'])))
                    product['unit_name'] = unit_detail['name']
                    product['unit_spec'] = unit_detail['code']
                    if returns_db.logistics_type == 'PUR' and returns_db.type not in ('IAD', 'CAD'):
                        product_tax_detail = product_tax_dict.get(product_id)
                        if not product_tax_detail:
                            raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))
                        
                        tax_rate = product_tax_detail.get('rate',0)
                        if returns_db.type == 'BO': # 原单价格从收货单里取，不必做转换
                            price = product_tax_detail.get('no_tax')
                            price_tax = product_tax_detail.get('tax')
                        else:
                            price = product_tax_detail.get('no_tax', 0)*purchase2order_rate_dict.get(product_id, 1)
                            price_tax = product_tax_detail.get('tax', 0)*purchase2order_rate_dict.get(product_id, 1)

                        product['tax_rate'] = tax_rate
                        product['price'] = price
                        product['price_tax'] = price_tax
                    new_product_list.append(product)
                else:
                    if product['quantity'] == 0:
                        delete_product_list.append(product['id'])
                    else:
                        product_db = ReturnProductModel.get(product['id'])
                        product['unit_rate'] = product_db.unit_rate
                        product['updated_by'] = user_id
                        product['updated_at'] = datetime.now()
                        if returns_db.logistics_type == 'PUR' and returns_db.type not in ('IAD', 'CAD'):
                            product_id = int(product_db.product_id)
                            product_tax_detail = product_tax_dict.get(product_id)
                            if not product_tax_detail:
                                raise DataValidationException('{} 没有维护合同'.format(product.get('product_name')))
                            
                            tax_rate = product_tax_detail.get('rate',0)
                            if returns_db.type == 'BO': # 原单价格从收货单里取，不必做转换
                                price = product_tax_detail.get('no_tax')
                                price_tax = product_tax_detail.get('tax')
                            else:
                                price = product_tax_detail.get('no_tax', 0)*purchase2order_rate_dict.get(product_id, 1)
                                price_tax = product_tax_detail.get('tax', 0)*purchase2order_rate_dict.get(product_id, 1)

                            product['tax_rate'] = tax_rate
                            product['price'] = price
                            product['price_tax'] = price_tax
                            product['returned_quantity'] = product['quantity']
                        product.pop('product_id')
                        update_product_list.append(product)
 
            ReturnProductModel.create_returns_products(new_product_list, update_product_list, delete_product_list)
        
        return_db = ReturnModel.get_return_by_id(return_id, partner_id)
        p_total, p_db = ReturnProductModel.list_return_products_by_return_id(return_id, partner_id=partner_id)
        args = {
            'product_nums': p_total,
            'updated_at':datetime.now(),
            'updated_by':user_id,
            'updated_name':operator_name
        }
        if return_reason:
            args['return_reason'] = return_reason
        if remark:
            args['remark'] = remark
        if return_delivery_date:
            timestamp = Timestamp()
            timestamp.seconds = return_delivery_date.seconds
            return_delivery_date = timestamp.ToDatetime()
            args['return_delivery_date'] = return_delivery_date
        if return_to:
            args['return_to'] = return_to
        if attachments:
            args['attachments'] = handle_request_attachments(attachments)
        if logistics_type:
            args['logistics_type'] = logistics_type
        if args:
            return_db.update_returns(return_id, args, partner_id)
        
        delivery_p_list = []
        rec_p_list = []
        for product in p_db:
            #---ForReceiptStart---#
            # ForReceiptDelivery
            delivery_p = {
                'delivery_by': return_db.return_by, 
                'product_id': product.product_id,
                'product_code': product.product_code,
                'product_name': product.product_name,
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.quantity,
                'unit_id':product.unit_id,
                'unit_name':product.unit_name,
                'unit_rate':product.unit_rate,
                'cost_price':product.price,
                'tax_price':product.price_tax,
                'tax_rate':product.tax_rate
                # 'unit_spec':product.unit_spec,
                # 'category_id':product.category_id
            }
            delivery_p_list.append(delivery_p)

            rec_p = {
                'receive_by': int(return_db.return_to), 
                'product_id': product.product_id,
                'product_code': product.product_code,
                'product_name': product.product_name,
                # 'storage_type': product.storage_type,
                'delivery_quantity': product.quantity,
                'unit_id':product.unit_id,
                'unit_name':product.unit_name,
                'unit_rate':product.unit_rate,
                'cost_price':product.price,
                'tax_price':product.price_tax,
                'tax_rate':product.tax_rate
                # 'unit_spec':product.unit_spec,
                # 'category_id':product.category_id
            }
            rec_p_list.append(rec_p)
        logging.info('Start Update Receipt.Receive')
        receipt_service.update_receives_products(
                batch_id=return_db.id, order_id=return_db.id,
                deal_products=rec_p_list, partner_id=partner_id, user_id=user_id,
                reason=return_reason, remark=remark, distr_type=logistics_type, 
                receive_by=return_to)
        receipt_service.update_deliverys_products(
                batch_id=return_db.id, order_id=return_db.id,
                deal_products=delivery_p_list, partner_id=partner_id, user_id=user_id,
                reason=return_reason, remark=remark, distr_type=logistics_type,
                receive_by=return_to)

        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = return_id
        log_detail['operation'] = 'UPDATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.now()
        log_detail['created_name'] = operator_name
        ReturnLogModel.create_returns_log(**log_detail)
        
        return {'id': return_id, 'status': True}
    
    # 获取可退货商品
    def get_valid_demand_product(self, store_id, partner_id, user_id, 
                                 distr_type=None, distr_by=None,
                                 product_ids=None, category_ids=None, 
                                 limit=None, offset=None, order_by=None,
                                 search=None, search_fields=None):
        """
        获得可退货商品， 此接口为非原单退货查询商品接口即 type != 'BO'
        distr_type: 物流模式: 配送:NMD, 直送:PUR, 不传查询所有
        vendor_id: 配送的配送中心/直送的供应商，默认可不传
        """
        order_by_inventory = order_by == 'real_inventory'
        if distr_by:
            filters = {"distribution_center_id": str(distr_by)}
        else:
            filters = {}

        if category_ids:
            product_relation_filters = {"product_category": [str(id) for id in category_ids]}
            filter_category_list = metadata_service.get_product_category_list(ids=category_ids, partner_id=partner_id,
                                                                          user_id=user_id)

        else:
            product_relation_filters = {}
            
            category_filters = {"status__in": ["ENABLED"]}
            filter_category_list = metadata_service.get_product_category_list(filters=category_filters, partner_id=partner_id,
                                                                            user_id=user_id)
        filter_category_list = filter_category_list.get('rows')
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        ret = {}
        # 门店退货商品
        product_filters = {"status__eq": "ENABLED",
                               "bom_type__neq": "MANUFACTURE",
                               "allow_order__eq": True}

        if search:
            search_ids = metadata_service.get_product_list(ids=product_ids, search=search,
                                                           search_fields=search_fields,
                                                           return_fields='id', partner_id=partner_id,
                                                           user_id=user_id).get('rows', [])
            product_ids = [int(i['id']) for i in search_ids]

            if not product_ids:
                return [], [], None

            # 按库存排序时，分页在库存服务完成
        real_limit = limit
        real_offset = offset
        if order_by_inventory:
            real_limit = -1
            real_offset = 0
        ret = metadata_service.get_list_valid_product_for_distr_by_id(store_id, product_ids=product_ids,
                                                                          include_product_fields='name,code,model_name,model_code,storage_type,category,product_type,sale_type,barcode',
                                                                          include_product_units=True,
                                                                          distr_type=distr_type, filters=filters,
                                                                          limit=real_limit, offset= real_offset,
                                                                          product_relation_filters=product_relation_filters,
                                                                          product_filters=product_filters,
                                                                          partner_id=partner_id, user_id=user_id)
        rows = ret.get('rows', [])
        total = ret.get("total", 0)
        result = []
        return_product_dict = {}

        # 增加查询商品价格

        product_ids = [int(product.get('product_id', 0)) for product in rows]
        product_code_list = [product.get('code') for product in rows]
        # logging.info('rows%s  ' % rows)
        # logging.info('product_ids%s  ' % product_ids)
        # logging.info('product_code_list%s  ' % product_code_list)
        type = 'NBO'
        is_third_party = ThirdParty.get_third_party(partner_id=partner_id)
        if distr_type == 'PUR':
            if is_third_party:
                is_original_receipt = True if type == 'BO' else False
                stub = get_stub(partner_id, is_original_receipt)
                product_tax_dict = get_tax(
                    stub=stub,vendor_id=distr_by, product_id_list=product_ids,
                    partner_id=partner_id, user_id=user_id, store_id=store_id
                )
                product_unit_dict = {}
                products_info = metadata_service.get_product_list(filters={'code__in': product_code_list},
                                                                include_units=True,
                                                                return_fields='id,code,name,model_name',
                                                                partner_id=partner_id, user_id=user_id).get('rows', [])
                for product_detail_info in products_info:
                    if product_detail_info.get('units'):
                        product_unit_dict[product_detail_info['id']] = {}
                        for unit in product_detail_info.get('units'):
                            if unit.get('purchase') and unit['purchase']==True:
                                product_unit_dict[product_detail_info['id']]['purchase'] = unit['rate'] if unit.get('rate') else 0
                            if unit.get('order') and unit['order']==True:
                                product_unit_dict[product_detail_info['id']]['order'] = unit['rate'] if unit.get('rate') else 0
                            if unit.get('default') and unit['default']==True:
                                product_unit_dict[product_detail_info['id']]['default'] = unit['rate'] if unit.get('rate') else 0
                    else:
                        product_unit_dict[product_detail_info['id']]['default'] = 0  
                
                purchase2order_rate_dict = get_tax_price(stub, product_unit_dict)

        final_product_ids = []
        if rows:
            for i in rows:
                units = i.get('units')
                unit = {}
                if units:
                    for u in units:
                        if u.get('order') and u.get('order') == True:
                            unit = u
                        else:
                            logger.warning("没有设置订货单位, product_id:{}".format(i.get("product_id")))
                            if total>0:
                                total -= 1
                            continue
                if not unit:
                    logger.warning("没有设置订货单位, product_id:{}".format(i.get("product_id")))
                    if total > 0:
                        total -= 1
                    continue

                distr_by = 0
                if i.get("distribution_center_id"):
                    distr_by = i["distribution_center_id"]
                else:
                    distr_by = i.get("vendor_id", 0)
                temp_product = {
                    'product_id': int(i.get("product_id")) if i.get("product_id") else 0,
                    'product_code': i.get("code"),
                    'product_name': i.get("name"),
                    'spec': i.get("model_name", "无"),
                    
                    'unit_id': int(unit.get("id")) if unit.get("id") else 0,
                    'unit_name': unit.get('name', "未设置"),
                    'unit_spec': unit.get("code", "无"),
                    'unit_rate': unit.get("rate", 1),

                    'category_id': int(i.get("category")) if i.get("category") else 0,
                    'category_name': key_filter_category_dict.get(str(i.get("category", 0)), {}).get('name'),
                    'category_code': key_filter_category_dict.get(str(i.get("category", 0)), {}).get('code'),
                    
                    'distr_by': int(distr_by),
                    'distr_type': i.get("distr_type"),

                    'product_type': i.get("product_type"),
                    'storage_type':  i.get("storage_type"),

                    'purchase_price': float(i.get("purchase_price", 0)),
                    'purchase_tax':  float(i.get('purchase_tax', 0)) 
                }

                # 增加查询商品价格
                product_id = int(i.get("product_id"))
                if distr_type == 'PUR' and type not in ('IAD', 'CAD'):
                    if is_third_party:
                        if type != 'BO':
                            # if not product_tax_dict.get(product_id) and stub == 'metadata':
                            #     raise DataValidationException('{} 没有维护合同'.format(i.get("name")))
                            # elif not product_tax_dict.get(product_id) and stub == 'third_party':
                            #     raise NoResultFoundError("系统未找到该商品: {}!".format(i.get("name")))
                            origin_product = product_tax_dict.get(product_id, {})
                            temp_product['tax_rate'] = origin_product.get('rate',0)
                            temp_product['price'] = origin_product.get('no_tax', 0)*purchase2order_rate_dict.get(str(product_id), 1)
                            temp_product['price_tax'] = origin_product.get('tax', 0)*purchase2order_rate_dict.get(str(product_id), 1)
                            # logging.info('purchase2order_rate_dict%s' % purchase2order_rate_dict)

                result.append(temp_product)
                final_product_ids.append(product_id)
                return_product_dict[product_id] = temp_product
                # logging.info('最终结果result %s' % result)

        # 新增按照实时库存排序
        inv_unchanged_products, inv_changed_products = [], []
        if order_by_inventory and final_product_ids:
            total = len(final_product_ids)
            result= []
            order_result = inventory_service.query_branch_inventory_sort(partner_id, user_id, store_id,
                                                                         product_ids=final_product_ids,limit=limit,offset=offset).get('rows')
            # logging.info(f'库存排序：{order_result}')
            if order_result:
                for i in order_result:
                    tmp_record =  return_product_dict.get(int(i.get('product_id')))
                    if tmp_record:
                        tmp_record['real_inventory_qty'] = i.get('qty')
                        result.append(tmp_record)

        return result, inv_unchanged_products, total

    # 查询历史操作
    def get_history(self, partner_id, user_id, request):
        return_id = request.id
        return_logs, total = ReturnLogModel.get_by_doc_id(partner_id=partner_id, doc_id=return_id)
        log_list = []
        for log in return_logs:
            log_detail = {
                'id': log.id,
                'status': log.operation,
                'updated_by': log.created_by,
                'updated_by_name': log.created_name,
                'updated_at':  Timestamp(seconds=int(log.updated_at.timestamp())), 
            }
            log_list.append(log_detail)
        return log_list, total

    # 获取收货单原单价格
    def get_receive_prod_price(self, receive_code, partner_id, user_id):
        if not receive_code:
            return {}
        res = receipt_service.get_receive_products_by_receive_code(receive_code, partner_id, user_id)
        res = pb2dict(res)
        # logger.info(res)
        products = res.get('rows')
        collections = {}
        for product in products:
            prod = {}
            product_id = product.get('product_id')
            prod['rate'] = product.get('tax_rate')
            prod['no_tax'] = product.get('cost_price')
            prod['tax'] = product.get('tax_price')
            collections[product_id] = prod
        # logger.info(collections)
        return collections


mobile_returns_service = MobileReturnService()