# -*- coding: utf-8 -*-
import json

from datetime import datetime
from supply.error.exception import StatusUnavailable, DataValidationException, \
    OrderNotExistException
from supply.module import BaseToolsModule
from supply.utils.auth import branch_list_scope_check, branch_scope_check
from supply.utils.helper import get_guid, get_branch_map, convert_to_int, get_company_map, get_category_map, \
    get_product_map
from supply.client.metadata_service import metadata_service

from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefund as sFR
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefundProduct as sFRP
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefundLog as sFRL


class MobileFranchiseeRefundsService(BaseToolsModule):
    """加盟商退款相关业务操作-移动端"""

    def __init__(self):
        super(MobileFranchiseeRefundsService, self).__init__()

    def list_mobile_refund(self, request, partner_id, user_id):
        res = dict()
        refund_start_date = self.utcTimestamp2datetime(request.refund_start_date)
        refund_end_date = self.utcTimestamp2datetime(request.refund_end_date)
        approved_start_date = self.utcTimestamp2datetime(request.approved_start_date)
        approved_end_date = self.utcTimestamp2datetime(request.approved_end_date)
        status = list(request.status) if request.status else []
        franchisee_ids = [convert_to_int(_id) for _id in request.franchisee_ids] if request.franchisee_ids else []
        received_bys = [convert_to_int(_id) for _id in request.received_bys] if request.received_bys else []
        types = [str(tp) for tp in request.types] if request.types else []
        payment_ways = [str(way) for way in request.payment_ways] if request.payment_ways else []
        ids = list(request.ids) if request.ids else []
        sort = request.sort if request.sort else 'updated_at'
        received_bys = branch_list_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                                               domain='boh.frs_store', branch_ids=received_bys)
        query_set = sFR.list_franchisee_refund(partner_id=partner_id, refund_start_date=refund_start_date, ids=ids,
                                               refund_end_date=refund_end_date, approved_start_date=approved_start_date,
                                               approved_end_date=approved_end_date, franchisee_ids=franchisee_ids,
                                               main_type=request.main_type, payment_ways=payment_ways, types=types,
                                               main_code=request.main_code, received_bys=received_bys, status=status,
                                               code=request.code, offset=request.offset, limit=request.limit,
                                               include_total=request.include_total, order=request.order, sort=sort)
        if isinstance(query_set, tuple):
            total, refunds = query_set
            res['total'] = total
        else:
            refunds = query_set
        franchisee_ids = []
        store_ids = []
        company_ids = []
        refund_ids = []
        for r in refunds:
            refund_ids.append(r.id)
            if r.franchisee_id:
                franchisee_ids.append(r.franchisee_id)
            if r.received_by:
                store_ids.append(r.received_by)
            if r.trade_company:
                company_ids.append(r.trade_company)
        franchisee_map = get_branch_map(branch_ids=franchisee_ids, branch_type="FRANCHISEE", partner_id=partner_id,
                                        user_id=user_id, return_fields="id,code,name")
        store_map = get_branch_map(branch_ids=store_ids, branch_type="STORE", partner_id=partner_id,
                                   user_id=user_id, return_fields="id,code,name")
        # 查询贸易公司信息
        company_map = get_company_map(company_ids=company_ids, partner_id=partner_id, user_id=user_id)
        pro_names = sFRP.query_product_name_by_refund_ids(refund_ids=refund_ids, partner_id=partner_id)
        pro_names_map = {}
        if pro_names:
            for p in pro_names:
                if p[0] in pro_names_map.keys():
                    if len(pro_names_map[p[0]]) < 5:
                        pro_names_map[p[0]].append(p[1])
                else:
                    pro_names_map[p[0]] = [p[1]]

        demand_list = []
        for r in refunds:
            row = r.serialize(conv=True)
            franchisee = franchisee_map.get(r.franchisee_id, {})
            store = store_map.get(r.received_by, {})
            company = company_map.get(r.trade_company, {})
            row['company_name'] = company.get('name')
            row['company_code'] = company.get('code')
            row['franchisee_name'] = franchisee.get('name')
            row['franchisee_code'] = franchisee.get('code')
            row['received_code'] = store.get('code')
            row['received_name'] = store.get('name')
            row['extends'] = json.dumps(dict(product_names=pro_names_map.get(r.id, [])), ensure_ascii=False)
            demand_list.append(row)
        res["rows"] = demand_list
        return res

    def get_mobile_refund_by_id(self, request, partner_id, user_id):
        """查询退款单详情"""
        refund_id = request.refund_id
        refund = sFR.get_refund_by_id(partner_id=partner_id, refund_id=refund_id)
        if not refund:
            raise OrderNotExistException("退款单不存在")
        branch_scope_check(partner_id=partner_id, user_id=user_id, schema='store',
                           domain='boh.frs_store', branch_id=refund.received_by)
        result = refund.serialize(conv=True)
        refund_products = sFRP.list_refund_product(partner_id=partner_id, refund_id=refund_id)
        if refund_products:
            category_ids = []
            product_ids = []
            for p in refund_products:
                if p.category_id and p.category_id not in category_ids:
                    category_ids.append(p.category_id)
                if p.product_id and p.product_id not in product_ids:
                    product_ids.append(p.product_id)
            product_map = get_product_map(product_ids=product_ids,
                                          return_fields="id,code,name,model_name",
                                          partner_id=partner_id, user_id=user_id)
            category_map = get_category_map(category_ids, partner_id, user_id)
            result['products'] = []
            for p in refund_products:
                category = category_map.get(p.category_id, {})
                product = product_map.get(str(p.product_id), {})
                row = p.serialize(conv=True)
                row['category_name'] = category.get('name')
                row['product_code'] = product.get('code')
                row['product_name'] = product.get('name')
                row['product_spec'] = product.get('model_name')
                result['products'].append(row)
        franchisee_map = {}
        store_map = {}
        company_map = {}

        if refund.received_by:
            store_map = get_branch_map(branch_ids=[refund.received_by], branch_type="STORE", partner_id=partner_id,
                                       user_id=user_id, return_fields="id,code,name")
        if refund.franchisee_id:
            franchisee_map = get_branch_map(branch_ids=[refund.franchisee_id], branch_type="FRANCHISEE",
                                            partner_id=partner_id, user_id=user_id, return_fields="id,code,name")
        if refund.trade_company:
            company_map = get_company_map(company_ids=[refund.trade_company], partner_id=partner_id, user_id=user_id)

        franchisee = franchisee_map.get(refund.franchisee_id, {})
        store = store_map.get(refund.received_by, {})
        company = company_map.get(refund.trade_company, {})
        result['franchisee_name'] = franchisee.get('name')
        result['franchisee_code'] = franchisee.get('code')
        result['received_code'] = store.get('code')
        result['received_name'] = store.get('name')
        result['company_name'] = company.get('name')
        result['company_code'] = company.get('code')
        return result

    def deal_mobile_refund_by_ids(self, request, allow_status: list, partner_id=None, user_id=None):
        """变更退款单状态, 门店端仅支持取消操作"""
        ret = {}
        action = request.action
        refund_id = request.refund_id
        if not refund_id:
            raise DataValidationException("订单ID不可为空！")
        username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        # 退款单取消后同步回退订货单状态为待确认
        demand_status = 'SUBMITTED'
        update_data = []
        refund_logs = []
        demand_detail = []
        refund_db = sFR.get_refund_by_id(partner_id=partner_id, refund_id=refund_id)
        if not refund_db:
            raise OrderNotExistException("退款单不存在-{}".format(refund_id))
        row = dict(
            id=refund_id,
            partner_id=partner_id,
            updated_by=user_id,
            updated_name=username,
            status=action
        )
        if request.remark:
            row['remark'] = request.remark
        if request.reject_reason:
            row['reject_reason'] = request.reject_reason
        update_data.append(row)
        refund_logs.append(dict(
            id=get_guid(),
            partner_id=partner_id,
            refund_id=refund_id,
            action=action,
            created_by=user_id,
            created_name=username,
            created_at=datetime.utcnow())
        )
        demand_detail.append(dict(
            id=refund_db.main_id,
            partner_id=partner_id,
            updated_by=user_id,
            updated_name=username,
            status=demand_status)
        )
        res = sFR.update_franchisee_refund(update_data=update_data, refund_logs=refund_logs, allow_status=allow_status,
                                           demand_detail=demand_detail)
        if res is True:
            ret["result"] = "success"
        else:
            ret['result'] = "failed"
        return ret

    def get_mobile_refund_log(self, request, partner_id, user_id):
        """查询退款单log"""
        result = dict(rows=[], total=0)
        result["total"], refund_logs = sFRL.get_franchisee_refund_log(refund_id=request.refund_id,
                                                                      partner_id=partner_id)
        if refund_logs:
            for log in refund_logs:
                result['rows'].append(log.serialize(conv=True))
        return result


mobile_franchisee_refund_service = MobileFranchiseeRefundsService()
