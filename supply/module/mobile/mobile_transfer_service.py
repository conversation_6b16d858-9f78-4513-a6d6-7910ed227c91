from copy import deepcopy
from hex_exception import exception_from_str
import json
from decimal import Decimal

from ...error.exception import *
from datetime import datetime
from ...model.inventory import inventory_repository
from ...model.operation_log import ReceiptFlowLogModel
from ...utils.inventory_enum import ACTION
from supply.utils.snowflake import gen_snowflake_id
from ...utils.transfer_enum import STATUS, P_STAUS
from supply.utils.helper import convert_to_int, convert_to_decimal, set_model_from_props, MessageTopic, get_product_map, \
    get_username_map
from google.protobuf.timestamp_pb2 import Timestamp
from ...client.metadata_service import metadata_service
from supply.model.supply_doc_code import Supply_doc_code
from ...client.inventory_service import inventory_service
from ...client.receipt_service import receipt_service
from supply.module.franchisee.helper import *
from google.protobuf.json_format import MessageToDict
from ...task.message_service_pub import MessageServicePub
from ..transfer_service import TransferProduct, TransferDetail, ssm
from supply import logger
from decimal import Decimal
from supply.driver.mq import mq_producer
from supply.model.operation_log import TpTransLogModel
from supply.module.utils import get_transfer_products


class Dict(dict):
    __setattr__ = dict.__setitem__
    __getattr__ = dict.__getitem__


class MobileTransferModule(object):
    def __init__(self):
        self.transfer_repo = ssm.return_repo

    def get_timestamp(self, date):
        if isinstance(date, datetime):
            date = Timestamp(seconds=int(date.timestamp()))
        else:
            date = None
        return date

    def deal_params_transfer(self, td, pro_names_map=None):
        """整理移动端返回调拨参数
        :param td 查询集 - dict/list
        :param pro_names_map 单据id/商品map
        """
        if isinstance(td, list):
            result = []
            for t in td:
                result.append(self.deal_params_transfer(t, pro_names_map))
        elif isinstance(td, dict):
            result = dict(
                id=td.get('transfer_id') if td.get('transfer_id') else td.get('id'),
                partner_id=td.get('partner_id'),
                shipping_store=td.get('shipping_store'),
                shipping_store_name=td.get('shipping_store_name'),
                receiving_store=td.get('receiving_store'),
                receiving_store_name=td.get('receiving_store_name'),
                code=td.get('code'),
                remark=td.get('remark'),
                status=td.get('status'),
                process_status=td.get('process_status'),
                transfer_date=td.get('transfer_date'),
                created_at=td.get('created_at'),
                created_by=td.get('created_by'),
                sub_type=td.get('sub_type'),
                branch_type=td.get('branch_type'),
                total_amount=td.get('total_amount'),
                total_sales_amount=td.get('total_sales_amount')
            )
            if pro_names_map:
                result["extends"] = json.dumps(dict(product_names=pro_names_map.get(result['id'], [])),
                                               ensure_ascii=False)
        else:
            result = None
        return result

    def deal_params_transfer_product(self, tdp, product_map=None):
        """整理移动端返回调拨商品参数
        :param tdp 查询集 - dict/list
        :param product_map 主档商品map
        """
        if isinstance(tdp, list):
            result = []
            for t in tdp:
                result.append(self.deal_params_transfer_product(t, product_map))
        elif isinstance(tdp, dict):
            product_id = tdp.get('product_id')
            category_id = None
            category_name = None
            model_name = None
            if product_map:
                product = product_map.get(str(product_id)) if product_map.get(str(product_id)) else {}
                category_id = convert_to_int(product.get("category"))
                category_name = product.get("category_name")
                model_name = product.get('model_name')
            result = dict(
                id=tdp.get('id'),
                product_id=product_id,
                product_code=tdp.get('product_code'),
                product_name=tdp.get('product_name'),
                unit_id=tdp.get('unit_id'),
                unit_name=tdp.get('unit_name'),
                quantity=tdp.get('quantity'),
                tax_rate=tdp.get('tax_rate'),
                tax_price=tdp.get('tax_price'),
                cost_price=tdp.get('cost_price'),
                amount=tdp.get('amount'),
                sales_price=tdp.get('sales_price'),
                sales_amount=tdp.get('sales_amount'),
                unit_rate=tdp.get('unit_rate'),
                created_at=tdp.get('created_at'),
                created_by=tdp.get('created_by'),
                category_id=category_id,
                category_name=category_name,
                model_name=model_name
            )
        else:
            result = None
        return result

    def check_transfer_branch_company(self, shipping_store, receiving_store, branch_type, partner_id, user_id):
        """校验调拨组织是否跨公司"""
        cross_company = False
        if branch_type == "WAREHOUSE":
            shipping = metadata_service.get_entity_by_id(schema_name='distrcenter', id=shipping_store,
                                                         partner_id=partner_id, user_id=user_id)
            receiving = metadata_service.get_entity_by_id(schema_name='distrcenter', id=receiving_store,
                                                          partner_id=partner_id, user_id=user_id)
            shipping_company = shipping.get('fields', {}).get('relation', {}).get('company_info')
            receiving_company = receiving.get('fields', {}).get('relation', {}).get('company_info')
        elif branch_type == "MACHINING_CENTER":
            shipping = metadata_service.get_machining_center_by_id(_id=shipping_store, return_fields="id",
                                                                   relation='company_info', partner_id=partner_id,
                                                                   user_id=user_id)
            receiving = metadata_service.get_machining_center_by_id(_id=receiving_store, return_fields="id",
                                                                    relation='company_info', partner_id=partner_id,
                                                                    user_id=user_id)
            shipping_company = shipping.get('relation', {}).get('company_info')
            receiving_company = receiving.get('relation', {}).get('company_info')

        else:
            shipping = metadata_service.get_store(store_id=shipping_store, return_fields='company_info',
                                                  partner_id=partner_id, user_id=user_id)
            receiving = metadata_service.get_store(store_id=receiving_store, return_fields='company_info',
                                                   partner_id=partner_id, user_id=user_id)
            shipping_company = shipping.get('company_info', [])[0]
            receiving_company = receiving.get('company_info', [])[0]
        if shipping_company != receiving_company:
            cross_company = True
        return cross_company

    def create_transfer(self, request, partner_id, user_id, username=None):
        data = {}
        request_id = request.request_id
        branch_type = request.branch_type
        if not request_id:
            raise NoRequestIDError('没有请求ID')
        # 验证该请求是否已创建
        transfer_d = self.transfer_repo.get_transfer_by_request_id(request_id)
        if transfer_d:
            return transfer_d
        transfer_detail = TransferDetail()
        for name in dir(transfer_detail):
            if not name.startswith('__') and not name.startswith('_'):
                if hasattr(request, name):
                    r_value = getattr(request, name)
                    if not callable(r_value):
                        if isinstance(r_value, Timestamp):
                            data[name] = datetime.fromtimestamp(r_value.seconds)

                        else:
                            data[name] = r_value
        set_model_from_props(data, transfer_detail)
        transfer_products = []
        if request.products:
            transfer_products = self.get_request_products_dict(request.products)
        if transfer_detail.sub_type == "EXTERNAL":
            if not transfer_detail.receiving_store:
                raise DataValidationException('receiving_store is required')
            if not transfer_detail.shipping_store:
                raise DataValidationException('shipping_store is required')
        else:
            if not transfer_detail.shipping_store:
                raise DataValidationException('shipping_store is required')
        # if transfer_detail.sub_type == "EXTERNAL":
        #     is_costcenter_id = check_stores_costcenter_belonging(
        #         [transfer_detail.shipping_store, transfer_detail.receiving_store], partner_id, user_id)
        #     if not is_costcenter_id:
        #         if branch_type == "WAREHOUSE":
        #             raise DataValidationException("仓库不属于同一个成本中心")
        #         raise DataValidationException("门店不属于同一个成本中心")

        # 兼容仓库/加工中心处理
        if branch_type == "WAREHOUSE":
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
            receiving_store = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
                                                          branch_type=branch_type,
                                                          partner_id=partner_id, user_id=user_id)
        elif branch_type == "MACHINING_CENTER":
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
            receiving_store = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
                                                          branch_type=branch_type,
                                                          partner_id=partner_id, user_id=user_id)
        else:
            if transfer_detail.sub_type == "EXTERNAL":
                is_region, set_close_store = self.valid_same_region_and_direct(transfer_detail.shipping_store,
                                                                               transfer_detail.receiving_store,
                                                                               partner_id, user_id)
                if not is_region:
                    raise DataValidationException("门店调拨区域不一样")
                if set_close_store and isinstance(set_close_store, list) and len(set_close_store) > 0:
                    raise DataValidationException("门店不是开店状态")
            shipping_store = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                         branch_type=branch_type,
                                                         partner_id=partner_id, user_id=user_id)
            receiving_store = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
                                                          branch_type=branch_type,
                                                          partner_id=partner_id, user_id=user_id)
        if not isinstance(shipping_store, dict):
            raise DataValidationException('invalid shipping_store')
        if not isinstance(receiving_store, dict):
            raise DataValidationException('invalid receiving_store')

        shipping_store_name = shipping_store.get('name')
        receiving_store_name = receiving_store.get('name')
        transfer_detail.shipping_store_name = shipping_store_name
        transfer_detail.receiving_store_name = receiving_store_name
        transfer_detail.partner_id = partner_id
        transfer_detail.cross_company = self.check_transfer_branch_company(
            shipping_store=transfer_detail.shipping_store,
            receiving_store=transfer_detail.receiving_store,
            branch_type=branch_type, partner_id=partner_id,
            user_id=user_id)
        if not transfer_detail.transfer_date:
            transfer_detail.transfer_date = datetime.utcnow()
        if not transfer_detail.shipper:
            transfer_detail.shipper = user_id
        if transfer_detail.shipping_date:
            transfer_detail.shipping_date = datetime.utcnow()
        if not transfer_detail.transfer_id:
            transfer_detail.created_by = user_id
            transfer_detail.created_at = datetime.utcnow()
            transfer_detail.status = 'INITED'
            transfer_detail.process_status = 'INITED'
        else:
            transfer_detail.updated_by = user_id
            transfer_detail.updated_at = datetime.utcnow()
        if not transfer_detail.code:
            transfer_detail.code = Supply_doc_code.get_code_by_type(
                'STORE_TRS', partner_id, None)
        if transfer_detail.code:
            transfer_detail.transfer_order_number = convert_to_int(transfer_detail.code)
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            # 库存单位转换
            transfer_products = self.update_product_unit(transfer_products, partner_id, user_id)

            # 校验库存数量，多仓位主档配置后需要通过仓位来校验
            if transfer_products:
                # 租户是否校验库存
                allow_neg_inv = metadata_service.get_business_extra_config(
                    partner_id=partner_id, user_id=user_id, domain="boh.store.transfer").get("allow_neg_inv")
                if allow_neg_inv and allow_neg_inv is True:
                    # 允许负库存
                    pass
                else:
                    # 配置多仓位需要通过仓位来校验库存
                    if transfer_detail.shipping_position:
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                   store_id=transfer_detail.shipping_store,
                                                                   position_id=transfer_detail.shipping_position,
                                                                   detail=True,
                                                                   partner_id=partner_id, user_id=user_id)
                    else:
                        # 未配置多仓位还校验门店库存
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                   store_id=transfer_detail.shipping_store,
                                                                   partner_id=partner_id, user_id=user_id)
                    if len(out_product) > 0:
                        raise TransferInventoryException("商品库存不存在或超出库存:" + '\n' + ','.join(out_product))

            if (transfer_detail.branch_type=="FRS_STORE" and transfer_detail.shipping_store is not None and transfer_products):
                product_id_list = [int(transfer_product.product_id) for transfer_product in transfer_products]
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=transfer_detail.shipping_store, user_id=user_id, partner_id=partner_id, product_ids=product_id_list, include_sales_price=True)
                for product_item in transfer_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity, 'amount', 'sales_amount', product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
            transfer_d = self.transfer_repo.create_update_transfer(transfer_detail, transfer_products, partner_id,
                                                                   user_id, username)
            res = {}
            if transfer_d:
                props = transfer_d.props()
                res = self.deal_params_transfer(props)
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                    message=dict(partner_id=partner_id, store_id=transfer_detail.shipping_store,
                                                 doc_type="transfer"))
            return res

    def update_transfer_product(self, request, partner_id, user_id, username=None):
        transfer_id = request.transfer_id
        remark = request.remark
        transfer_date = request.transfer_date
        if transfer_date:
            timestamp = Timestamp()
            timestamp.seconds = transfer_date.seconds
            transfer_date = timestamp.ToDatetime()
            if transfer_date == datetime(1970, 1, 1):
                transfer_date = None
            else:
                transfer_date = transfer_date
        transfer_products = []
        if request.products:
            transfer_products = self.get_request_products_dict(request.products)
        transfer_detail = self.transfer_repo.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if (transfer_detail and transfer_detail.status == 'INITED' and transfer_products and isinstance(
                transfer_products, list) and len(transfer_products) > 0):
            transfer_products = self.update_product_unit(transfer_products, partner_id, user_id)
            # 校验库存数量, 多仓位主档配置后需要通过仓位来校验库存
            if transfer_products:
                # 租户是否校验库存
                allow_neg_inv = metadata_service.get_business_extra_config(
                    partner_id=partner_id, user_id=user_id, domain="boh.store.transfer").get("allow_neg_inv")
                if allow_neg_inv and allow_neg_inv is True:
                    # 允许负库存
                    pass
                else:
                    # 配置多仓位需要通过仓位来校验库存
                    if transfer_detail.shipping_position:
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                   store_id=transfer_detail.shipping_store,
                                                                   position_id=transfer_detail.shipping_position,
                                                                   detail=True,
                                                                   partner_id=partner_id, user_id=user_id)
                    else:
                        # 未配置多仓位还校验门店库存
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                   store_id=transfer_detail.shipping_store,
                                                                   partner_id=partner_id, user_id=user_id)
                    if len(out_product) > 0:
                        raise TransferInventoryException("商品库存不存在或超出库存:" + '\n' + ','.join(out_product))
            if (transfer_detail.branch_type=="FRS_STORE" and transfer_detail.shipping_store is not None and transfer_products):
                product_id_list = [int(transfer_product.product_id) for transfer_product in transfer_products]
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=transfer_detail.shipping_store, user_id=user_id, partner_id=partner_id, product_ids=product_id_list, include_sales_price=True)
                for product_item in transfer_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity, 'amount', 'sales_amount', product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
            transfer_d = self.transfer_repo.update_transfer_product(transfer_detail, transfer_products,
                                                                    remark, transfer_date, user_id, username,
                                                                    partner_id=partner_id)
            if transfer_d:
                return True
            else:
                raise ActionException('更新失败')
        elif (remark and transfer_detail and transfer_detail.status == 'INITED') \
                or (transfer_date and transfer_detail and transfer_detail.status == 'INITED'):
            if (transfer_detail.branch_type=="FRS_STORE" and transfer_detail.shipping_store is not None and transfer_products):
                product_id_list = [int(transfer_product.product_id) for transfer_product in transfer_products]
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=transfer_detail.shipping_store, user_id=user_id, partner_id=partner_id, product_ids=product_id_list, include_sales_price=True)
                for product_item in transfer_products:
                    fill_adjust_tax_price_tax_rate(product_item, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map,
                                                   product_item.product_id, 1, product_item.accounting_quantity, 'amount', 'sales_amount', product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
            transfer_d = self.transfer_repo.update_transfer_product(transfer_detail, transfer_products,
                                                                    remark, transfer_date, user_id, username,
                                                                    partner_id=partner_id)
            if transfer_d:
                return True
            else:
                raise ActionException('更新失败')
        else:
            raise ActionException('更新数据不合法')

    def list_transfer_detail(self, request, partner_id, user_id):
        start_date = request.start_date
        end_date = request.end_date
        start_date = datetime.fromtimestamp(start_date.seconds)
        end_date = datetime.fromtimestamp(end_date.seconds)
        code = request.code
        ids = [convert_to_int(_id) for _id in request.ids] if request.ids else None
        # logger.info("start_date: {}  end_date: {}  partner_id: {} ids: {}".format(start_date, end_date, partner_id, ids))
        if not code and not ids:
            if start_date.year == 1970:
                raise DataValidationException("请传入查询开始日期")
            if end_date.year == 1970:
                raise DataValidationException("请传入查询结束日期")
        shipping_stores = list(request.shipping_stores)
        receiving_stores = list(request.receiving_stores)
        logger.info("shipping_stores: {}  receiving_stores: {}".format(shipping_stores, receiving_stores))
        types = list(request.types) if request.types else []
        sub_type = request.sub_type
        receiving_positions = list(request.receiving_positions) if request.receiving_positions else []
        shipping_positions = list(request.shipping_positions) if request.shipping_positions else []
        include_total = request.include_total
        limit = request.limit
        offset = request.offset
        order = request.order
        sort = request.sort
        branch_type = request.branch_type
        # if not sub_type:
        #     raise DataValidationException("请传入sub_type来区分内/外调拨")
        if not sort:
            sort = 'updated_at'
        status_list = []
        if request.status:
            for st in request.status:
                status = STATUS[st]
                status_list.append(status)
        else:
            status_list = None
        # 对于加盟商调拨单, 只查询调入门店时, 不显示CANCELLED状态的调拨单
        if branch_type=="FRS_STORE" and receiving_stores and not shipping_stores:
            if not status_list:
                status_list = ['NONE', 'INITED', 'SUBMITTED', 'CONFIRMED', 'FINALIZED']
            elif "CANCELLED" in status_list:
                status_list[status_list.index("CANCELLED")]="INVALID"
        store_ids = None
        transfer_details_list = self.transfer_repo.list_transfer_detail(partner_id=partner_id,
                                                                        shipping_stores=shipping_stores,
                                                                        receiving_stores=receiving_stores,
                                                                        status=status_list, branch_type=branch_type,
                                                                        limit=limit, offset=offset,
                                                                        include_total=include_total,
                                                                        start_date=start_date, code=code,
                                                                        end_date=end_date, store_ids=store_ids,
                                                                        order=order, sort=sort, types=types,
                                                                        sub_type=sub_type, ids=ids,
                                                                        shipping_positions=shipping_positions,
                                                                        receiving_positions=receiving_positions)
        total = None
        if isinstance(transfer_details_list, tuple):
            total, transfer_details_list = transfer_details_list
        # 给移动端列表拼接商品名称
        transfer_ids = [t.get('id') for t in transfer_details_list]
        pro_names = self.transfer_repo.query_product_name_by_transfer_ids(transfer_ids, partner_id)
        pro_names_map = {}
        if pro_names:
            for p in pro_names:
                if p[0] in pro_names_map.keys():
                    if len(pro_names_map[p[0]]) < 5:
                        pro_names_map[p[0]].append(p[1])
                else:
                    pro_names_map[p[0]] = [p[1]]
        rows = self.deal_params_transfer(transfer_details_list, pro_names_map)
        # 按单据状态排序
        # 我调出的：新建、已提交、已完成、已取消
        # 调给我的：待收货、已收货
        rows = sorted(rows, key=lambda e: e.__getitem__('status'))
        if total is not None:
            res = dict(total=total, rows=rows)
        else:
            res = dict(rows=rows)
        return res

    def get_transfer_by_id(self, transfer_id, is_details=False, partner_id=None, user_id=None):
        res = {}
        detail_obj = self.transfer_repo.get_transfer_by_id(transfer_id, is_details=is_details, partner_id=partner_id)
        if detail_obj:
            props = detail_obj.props()
            props['status'] = STATUS.index(props['status'])
            props['process_status'] = P_STAUS.index(props['process_status'])
            res = self.deal_params_transfer(props)
        return res

    def get_store_detail_by_id(self, store_id, branch_type, partner_id, user_id):
        """根据id拉取主档
        :param store_id 门店或仓库id
        :param branch_type 区分门店/仓库/加工中心
        :param partner_id
        :param user_id
        """
        store_detail = {}
        if not store_id:
            return store_detail
        if branch_type == "WAREHOUSE":
            store_detail = metadata_service.get_distribution_center(
                center_id=int(store_id), return_fields="code,name", partner_id=partner_id, user_id=user_id)
        elif branch_type == "MACHINING_CENTER":
            store_detail = metadata_service.get_machining_center_by_id(_id=store_id, return_fields='code,name',
                                                                       partner_id=partner_id, user_id=user_id)
        elif branch_type == "POSITION":
            position = metadata_service.get_entity_by_id(schema_name="POSITION", id=store_id,
                                                         partner_id=partner_id, user_id=user_id)
            store_detail = position.get('fields', {})
            store_detail["id"] = convert_to_int(position.get('id'))
        else:
            store_detail = metadata_service.get_store(store_id=store_id, return_fields='code,name',
                                                      partner_id=partner_id, user_id=user_id)
        return store_detail

    def iterate_inventory_product(self, inventory_products, products_unit, list_transfer_products,
                                  partner_id=None, user_id=None):
        store_product_inventory = []
        unit_dict = {}
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code',
                                                   partner_id=partner_id, user_id=user_id
                                                   )
        units = []
        if units_ret:
            units = units_ret['rows']
        if isinstance(units, list):
            for u in units:
                if isinstance(u, dict) and 'id' in u:
                    unit_dict[str(u['id'])] = dict()
                    if 'name' in u:
                        unit_dict[str(u['id'])]['name'] = u['name']
            quantity_avail = 0
            # 可用数量小于0,不能调拨
            # if inventory.quantity_avail < 0:
            #     quantity_avail = 0
            # else:
        for p in products_unit:
            product = {}
            product_id = convert_to_int(p['id']) if 'id' in p else None
            inventory = inventory_products.get(str(product_id))
            # if not inventory:
            #     continue
            if inventory:
                quantity_avail = inventory['quantity_avail']
            else:
                quantity_avail = 0
            for transfer_p in list_transfer_products:
                if transfer_p.product_id == product_id:
                    product['quantity'] = transfer_p.quantity
                    product['unit_id'] = transfer_p.unit_id
                    break
            product['id'] = product_id
            if 'name' in p:
                product['product_name'] = p['name']
            product['product_code'] = p['code'] if 'code' in p else ''
            product['product_category_id'] = int(p['category']) if 'category' in p else None
            units = p['units'] if isinstance(p, dict) and 'units' in p else []
            product_units = []
            for m_unit in units:
                if isinstance(m_unit, dict):
                    if m_unit.get('order'):
                        unit = {}
                        unit['id'] = int(m_unit['id'])
                        if str(m_unit['id']) in unit_dict:
                            if 'name' in unit_dict[str(m_unit['id'])]:
                                unit['name'] = \
                                    unit_dict[str(m_unit['id'])]['name']
                        rate = convert_to_decimal(m_unit['rate']) if 'rate' in m_unit and m_unit['rate'] and m_unit[
                            'rate'] != 0 else 1.0
                        unit['quantity'] = round(convert_to_decimal(quantity_avail) / convert_to_decimal(rate), 6)
                        product_units.append(unit)
            product['unit'] = product_units
            if len(product_units) == 0:
                continue
            store_product_inventory.append(product)
        return store_product_inventory

    def list_transfer_store_product(self, store_id, limit=-1, offset=0,
                                    include_total=False, search=None, search_fields=None, partner_id=None,
                                    user_id=None, category_ids=None, branch_type=None, order_by=None):
        order_by_inventory = order_by == 'real_inventory'
        if category_ids:
            product_relation_filters = {"product_category__in": [str(id) for id in category_ids]}
        else:
            product_relation_filters = None
        #按库存排序时，分页在库存服务完成
        real_limit = limit
        real_offset = offset
        if order_by_inventory:
            real_limit = -1
            real_offset = 0

        list_store_product_ret = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_transfer=True),
            product_filters={
                "status": "ENABLED",
                "bom_type__neq": 'MANUFACTURE'
            },
            product_search=search,
            product_search_fields=search_fields,
            user_id=user_id,
            offset=real_offset,
            limit=real_limit,
            include_total=include_total,
            include_product_units=True,
            return_fields="allow_transfer",
            include_product_fields='code,name,model_name,category,category_name,barcode,spec',
            can_order=True,
            product_relation_filters=product_relation_filters,
            region="ATTRIBUTE_REGION"
        )
        total = 0
        store_product_transfer = []
        store_product_transfer_dict = {}
        final_product_ids = []
        if list_store_product_ret:
            list_store_product = list_store_product_ret.get('rows', [])
            total = list_store_product_ret.get('total') if list_store_product_ret.get('total') else 0
            #商品的含税单价和税率
            if branch_type == "FRS_STORE":
                product_id_list = []
                for product in list_store_product:
                    if product.get('allow_transfer'):
                        product_id_list.append(int(product.get('product_id')))
                product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=store_id, user_id=user_id, partner_id=partner_id, product_ids=product_id_list, include_sales_price=True)
            for product in list_store_product:
                product_dict = {}
                unit = {}
                product_dict['product_id'] = int(product.get('product_id'))
                product_dict['product_code'] = product.get('code')
                product_dict['product_name'] = product.get('name')
                product_dict['model_name'] = product.get('model_name')
                product_dict['category_id'] = int(product.get('category')) if product.get('category') else 0
                product_dict["category_name"] = product.get('category_name')
                product_dict['barcode'] = product['extends'].get('barcode') if product.get('extends') else []
                product_dict['unit'] = []
                if product.get('units'):
                    for m_uint in product.get('units'):
                        if m_uint.get('transfer') is True:
                            unit['id'] = int(m_uint.get('id'))
                            unit['name'] = m_uint.get('name')                                
                            if branch_type == "FRS_STORE":
                                unit['unit_rate'] = m_uint.get('rate')
                                fill_adjust_tax_price_tax_rate(unit, product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map,product.get('product_id'), m_uint['rate'], product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)
                            product_dict['unit'].append(unit)
                # 上层过滤无用，需要根据relation关联，因此在这里加一层过滤
                if product.get('allow_transfer'):
                    store_product_transfer.append(product_dict)
                    final_product_ids.append(product_dict['product_id'])
                    store_product_transfer_dict[product_dict['product_id']] = product_dict
                else:
                    if branch_type != "FRS_STORE":
                        if total>0:
                            total -= 1
                        continue
                    else:
                        product_dict['unit'] = []
                        store_product_transfer.append(product_dict)
                        final_product_ids.append(product_dict['product_id'])
                        store_product_transfer_dict[product_dict['product_id']] = product_dict

        # 新增按照实时库存排序

        inv_unchanged_products, inv_changed_products = [], []
        final_result =store_product_transfer
        if order_by_inventory and final_product_ids:
            total = len(final_product_ids)
            final_result = []
            order_result = inventory_service.query_branch_inventory_sort(partner_id, user_id, store_id,
                                                                         product_ids=final_product_ids,limit=limit,offset=offset).get('rows')
            # logging.info(f'调拨库存排序：{order_result}')
            if order_result:
              for i in order_result:
                 tmp_record = store_product_transfer_dict.get(int(i.get('product_id')))
                 if tmp_record:
                    tmp_record['real_inventory_qty'] = i.get('qty')
                    final_result.append(tmp_record)


        # 为了让除最后一页外的前几页, 每页产品数量显示的都一样, 在分页时即使没有单位的产品也显示, 前端提醒用户
        if branch_type != "FRS_STORE":
            final_result = get_transfer_products(final_result)
        if include_total:
            return total, final_result, inv_unchanged_products
        return final_result, inv_unchanged_products

    def list_transfer_store_product_category(self, store_id, partner_id=None, user_id=None):
        list_store_product_ret = metadata_service.list_region_product_by_store(
            store_id=store_id,
            partner_id=partner_id,
            filters=dict(allow_transfer=True),
            product_filters={
                "status": "ENABLED",
                "bom_type__neq": 'MANUFACTURE'
            },
            user_id=user_id,
            include_product_units=False,
            return_fields="allow_transfer",
            include_product_fields='category,category_name',
            can_order=True,
            region="ATTRIBUTE_REGION"
        )
        store_product_category_ids = []
        store_product_category_names={}
        store_product_category_product_count={}
        if list_store_product_ret:
            list_store_product = list_store_product_ret.get('rows', [])
            for product in list_store_product:
                if product.get('category') and product.get('allow_transfer'):
                    category_id = int(product.get('category'))
                    if category_id not in store_product_category_product_count:
                        store_product_category_ids.append(category_id)
                        store_product_category_names[category_id]= product.get('category_name')
                        store_product_category_product_count[category_id] = 1
                    else:
                        store_product_category_product_count[category_id] = store_product_category_product_count[category_id] + 1
        category_list = metadata_service.get_product_category_list(ids=store_product_category_ids if len(store_product_category_ids) == 1 else None,return_fields="id,parent_id", partner_id=partner_id,user_id=user_id).get('rows', []) if store_product_category_ids else []
        category_children_map = {}
        for category in category_list:
            category_id = int(category["id"])
            category_parent_id = int(category.get("parent_id", 0))
            if category_parent_id:
                if category_parent_id in category_children_map:
                    if category_id not in category_children_map[category_parent_id]:
                        category_children_map[category_parent_id].append(category_id)
                else:
                    category_children_map[category_parent_id]=[category_id]
        store_product_category_product_count_sum = {}
        category_children_ret_map = {}
        for category_id in store_product_category_ids:
            store_product_category_product_count_sum[category_id] = store_product_category_product_count[category_id]
            for category_child_id in get_category_children(category_id, category_children_map, category_children_ret_map):
                store_product_category_product_count_sum[category_id] += store_product_category_product_count.get(category_child_id,0)
        return [{'category_id':category_id,'category_name':store_product_category_names[category_id],'product_count':store_product_category_product_count_sum[category_id]} for category_id in store_product_category_ids]

    def list_same_attribute_region_store(self, store_id, partner_id, user_id):
        list_store = metadata_service.get_store(store_id=store_id, return_fields="id,name,transfer_region",
                                                partner_id=partner_id, user_id=user_id
                                                )
        if list_store and isinstance(list_store, dict) and len(list_store) > 0:
            store = list_store
            if 'transfer_region' in store and store['transfer_region']:
                filters = {"status__eq": "ENABLED", 'open_status': 'OPENED',
                           'relation.transfer_region__in': [str(store['transfer_region'][0])]}
                list_same_store_ret = metadata_service.get_store_list(
                    filters=filters, partner_id=partner_id, user_id=user_id)
                list_same_store = []
                if list_same_store_ret:
                    list_same_store = list_same_store_ret['rows']
                return_content = []
                if list_same_store and isinstance(list_same_store, list) and len(list_same_store) > 0:
                    for s in list_same_store:
                        content = {}
                        if convert_to_int(s['id']) == store_id:
                            continue
                        content['id'] = convert_to_int(s['id'])
                        content['name'] = s['name']
                        content['code'] = s['code']
                        content['address'] = s['address'] if 'address' in s else ''
                        return_content.append(content)
                    return return_content
        return None

    def list_transfer_product(self, partner_id=None, user_id=None, limit=None, offset=None, include_total=False,
                              transfer_id=None, branch_type=None, branch_id=None, status=None, **kwargs):
        res = {}
        list_transfer_product = self.transfer_repo.list_transfer_product(partner_id=partner_id, limit=limit,
                                                                         offset=offset, include_total=include_total,
                                                                         transfer_id=transfer_id)
        total = None
        if isinstance(list_transfer_product, tuple):
            total, list_transfer_product = list_transfer_product
        product_ids = [p.get('product_id') for p in list_transfer_product]

        product_map = get_product_map(product_ids=product_ids, return_fields="id,category,model_name",
                                      partner_id=partner_id, user_id=user_id)
        if list_transfer_product and branch_id and branch_type=="FRS_STORE":
            for p in list_transfer_product:
                if str(p["product_id"]) in product_map and product_map[str(p["product_id"])].get("units"):
                    p["unit_rate"] = [m_unit["rate"] for m_unit in product_map[str(p["product_id"])].get("units") if m_unit.get("transfer") is True][0]

        if list_transfer_product and branch_id and branch_type=="FRS_STORE":
            product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map, product_unit_adjust_sales_price_map = get_adjust_tax_price_tax_rate(store_id=branch_id, user_id=user_id, partner_id=partner_id, product_ids=product_ids, include_sales_price=True)
            for p in list_transfer_product:
                if (branch_type=="FRS_STORE" and branch_id is not None and status is not None and status not in ["INITED","REJECTED"]):
                    # 前端需要使用调拨单上保存的旧价格计算并显示金额
                    update_adjust_tax_price_tax_rate(product_unit_adjust_tax_price_map=product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map=product_unit_adjust_tax_ratio_map, product_id=p.get('product_id'),
                                                     tax_price=p.get('tax_price'), tax_rate=p.get('tax_rate'), product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map, sales_price=p.get('sales_price'))
                fill_adjust_tax_price_tax_rate(p, product_unit_adjust_tax_price_map=product_unit_adjust_tax_price_map, product_unit_adjust_tax_ratio_map=product_unit_adjust_tax_ratio_map, product_id=p.get("product_id"), unit_rate=p.get("unit_rate"), quantity=p.get("quantity"),
                                               quantityAmountName='amount', quantitySalesAmountName='sales_amount', product_unit_adjust_sales_price_map=product_unit_adjust_sales_price_map)

        list_transfer_product = self.deal_params_transfer_product(list_transfer_product, product_map=product_map)
        if total is not None:
            res['total'] = total
            res['rows'] = list_transfer_product
        res['rows'] = list_transfer_product
        return res

    def valid_transfer_store_product(self, store_id, transfer_date, transfer_products, partner_id, user_id):
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            list_store_product = []
            list_store_product_ret = metadata_service.get_attribute_products_by_store_id(store_id=store_id,
                                                                                         partner_id=partner_id,
                                                                                         user_id=user_id)
            if list_store_product_ret:
                list_store_product = list_store_product_ret['rows']
            if list_store_product and isinstance(list_store_product, list) \
                    and len(list_store_product) > 0:
                set_error_product = []
                not_allow_transfer = []
                product_ids = []
                for store_product in list_store_product:
                    product_ids.append(convert_to_int(store_product['product_id']))

                for store_product in list_store_product:
                    for transfer_product in transfer_products:
                        # 判断产品是否为区域产品
                        if transfer_product.product_id not in product_ids:
                            set_error_product.append(str(transfer_product.product_id))
                        # 门店产品不允许调拨
                        if convert_to_int(store_product['product_id']) == transfer_product.product_id \
                                and not store_product['allow_transfer']:
                            not_allow_transfer.append(str(transfer_product.product_id))
                # 错误的产品和不允许调拨的产品
                set_error_product = list(set(set_error_product))
                not_allow_transfer = list(set(not_allow_transfer))
                if len(set_error_product) > 0 and len(not_allow_transfer) > 0:
                    set_error_product.extend(not_allow_transfer)
                    set_error_product = list(set(set_error_product))
                    all_ids = ','.join(set_error_product)
                    raise DataValidationException(all_ids)
                # 错误的产品
                if len(set_error_product) > 0:
                    err_ids = ','.join(set_error_product)
                    raise DataValidationException(err_ids)
                # 不允许调拨的产品
                if len(not_allow_transfer) > 0:
                    all_ids = ','.join(not_allow_transfer)
                    raise DataValidationException(not_allow_transfer)
        return True

    def update_confirmed_product_unit(self, transfer_products, partner_id, user_id):
        if transfer_products is None or not isinstance(transfer_products, list) or len(transfer_products) == 0:
            return None
        units = []
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code', partner_id=partner_id, user_id=user_id)
        if units_ret:
            units = units_ret['rows']
        if not units or not isinstance(units, list) or len(units) == 0:
            return None
        dict_unit = {}
        for unit in units:
            dict_unit[str(unit['id'])] = unit
        product_ids = []
        for p in transfer_products:
            product_ids.append(p.product_id)
        main_products_ret = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                              return_fields='name,code,bom_type', partner_id=partner_id,
                                                              user_id=user_id)
        main_products = []
        if main_products_ret:
            main_products = main_products_ret['rows']
        if main_products is None or not isinstance(main_products, list) or len(main_products) == 0:
            return None
        list_bom_type = []
        for main_p in main_products:
            bom_type = main_p['bom_type'].upper() if 'bom_type' in main_p and main_p['bom_type'] else None
            for product in transfer_products:
                product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
                product.partner_id = partner_id
                if product_id == product.product_id:
                    if bom_type == 'MADE':
                        list_bom_type.append(str(product_id))
                    if 'name' in main_p:
                        product.product_name = main_p['name']
                    if 'code' in main_p:
                        product.product_code = str(main_p['code'])
                        product.material_number = str(main_p['code'])
                    units = main_p['units'] if 'units' in main_p else []
                    unit_current = None
                    # unit_order = None
                    unit_default = None
                    for m_unit in units:
                        if isinstance(m_unit, dict):
                            if convert_to_int(product.unit_id) == convert_to_int(m_unit['id']):
                                unit_current = m_unit
                            # if m_unit['order']:
                            #     unit_order = m_unit
                            if m_unit.get('default'):
                                unit_default = m_unit
                    if unit_current and unit_default:
                        d_unit_current = dict_unit.get(str(unit_current['id']))
                        d_unit_default = dict_unit.get(str(unit_default['id']))
                        if 'name' in d_unit_current:
                            product.unit_name = d_unit_current['name'] if 'name' in d_unit_current else None
                            product.unit_spec = d_unit_current['code'] if 'code' in d_unit_current else None

                        if unit_current['id'] == unit_default['id']:
                            product.accounting_unit_id = convert_to_int(unit_current['id'])
                            if 'name' in d_unit_current and d_unit_current['name']:
                                product.accounting_unit_name = d_unit_current['name']
                            rate = convert_to_decimal(round(unit_current['rate'], 8)) if 'rate' in unit_current else 1.0
                            if product.confirmed_received_quantity != None:
                                product.accounting_received_quantity = (
                                        convert_to_decimal(product.confirmed_received_quantity) * rate).quantize(
                                    Decimal('0.********'))
                            product.accounting_unit_spec = str(
                                d_unit_current['code']) if 'code' in d_unit_current else None
                        else:
                            product.accounting_unit_id = convert_to_int(unit_default['id'])
                            if 'name' in d_unit_default and d_unit_default['name']:
                                product.accounting_unit_name = d_unit_default['name']
                            if product.confirmed_received_quantity != None:
                                product.accounting_received_quantity = (convert_to_decimal(
                                    product.confirmed_received_quantity) * convert_to_decimal(
                                    round(unit_current['rate'], 8))).quantize(Decimal('0.********'))
                            product.accounting_unit_spec = str(
                                d_unit_default['code']) if 'code' in d_unit_default else None
                    else:
                        product.accounting_received_quantity = (
                            convert_to_decimal(product.confirmed_received_quantity)).quantize(Decimal('0.********'))
                        product.accounting_unit_id = product.unit_id
        dict_product = {}
        for p in transfer_products:
            if str(p.product_id) not in dict_product:
                dict_product[str(p.product_id)] = p
            else:
                dict_p = dict_product[str(p.product_id)]
                dict_p.confirmed_received_quantity = p.confirmed_received_quantity + dict_p.confirmed_received_quantity
                dict_p.accounting_received_quantity = p.accounting_received_quantity + dict_p.accounting_received_quantity
                transfer_products.remove(p)
        if list_bom_type and isinstance(list_bom_type, list) and len(list_bom_type) > 0:
            raise DataValidationException(','.join(list_bom_type))
        return transfer_products

    def update_product_unit(self, transfer_products, partner_id, user_id):
        if transfer_products is None or not isinstance(transfer_products, list) or len(transfer_products) == 0:
            return None
        units = []
        units_ret = metadata_service.get_unit_list(return_fields='id,name,code', partner_id=partner_id, user_id=user_id)
        if units_ret:
            units = units_ret['rows']
        if not units or not isinstance(units, list) or len(units) == 0:
            return None
        dict_unit = {}
        for unit in units:
            dict_unit[str(unit['id'])] = unit
        product_ids = []
        for p in transfer_products:
            product_ids.append(p.product_id)
        main_products_ret = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                              return_fields='name,code,bom_type', partner_id=partner_id,
                                                              user_id=user_id)
        main_products = []
        if main_products_ret:
            main_products = main_products_ret['rows']
        if main_products is None or not isinstance(main_products, list) or len(main_products) == 0:
            return None
        list_bom_type = []
        for main_p in main_products:
            bom_type = main_p['bom_type'].upper() if 'bom_type' in main_p and main_p['bom_type'] else None
            for product in transfer_products:
                product_id = convert_to_int(main_p['id']) if 'id' in main_p else None
                product.partner_id = partner_id
                if product_id == product.product_id:
                    if bom_type == 'MANUFACTURE':
                        list_bom_type.append(str(product_id))
                    if 'name' in main_p:
                        product.product_name = main_p['name']
                    if 'code' in main_p:
                        product.product_code = str(main_p['code'])
                        product.material_number = str(main_p['code'])
                    units = main_p['units'] if 'units' in main_p else []
                    unit_current = None
                    # unit_order = None
                    unit_default = None
                    for m_unit in units:
                        if isinstance(m_unit, dict):
                            if convert_to_int(product.unit_id) == convert_to_int(m_unit['id']):
                                unit_current = m_unit
                            # if m_unit['order']:
                            #     unit_order = m_unit
                            if m_unit.get('default'):
                                unit_default = m_unit
                    if unit_current and unit_default:
                        d_unit_current = dict_unit.get(str(unit_current['id']))
                        d_unit_default = dict_unit.get(str(unit_default['id']))
                        if 'name' in d_unit_current:
                            product.unit_name = d_unit_current['name'] if 'name' in d_unit_current else None
                            product.unit_spec = d_unit_current['code'] if 'code' in d_unit_current else None

                        if unit_current['id'] == unit_default['id']:
                            product.accounting_unit_id = convert_to_int(unit_current['id'])
                            if 'name' in d_unit_current and d_unit_current['name']:
                                product.accounting_unit_name = d_unit_current['name']
                            rate = convert_to_decimal(round(unit_current['rate'], 8)) if 'rate' in unit_current else 1.0
                            if product.quantity != None:
                                product.accounting_quantity = (convert_to_decimal(product.quantity) * rate).quantize(
                                    Decimal('0.********'))
                            product.accounting_unit_spec = str(
                                d_unit_current['code']) if 'code' in d_unit_current else None
                        else:
                            product.accounting_unit_id = convert_to_int(unit_default['id'])
                            if 'name' in d_unit_default and d_unit_default['name']:
                                product.accounting_unit_name = d_unit_default['name']
                            if product.quantity != None:
                                product.accounting_quantity = (convert_to_decimal(
                                    product.quantity) * convert_to_decimal(
                                    round(unit_current['rate'], 8))).quantize(
                                    Decimal('0.********'))
                            product.accounting_unit_spec = str(
                                d_unit_default['code']) if 'code' in d_unit_default else None
                    else:
                        product.accounting_quantity = (convert_to_decimal(product.quantity)).quantize(
                            Decimal('0.********'))
                        product.accounting_unit_id = product.unit_id
        dict_product = {}
        for p in transfer_products:
            if str(p.product_id) not in dict_product:
                dict_product[str(p.product_id)] = p
            else:
                dict_p = dict_product[str(p.product_id)]
                dict_p.quantity = p.quantity + dict_p.quantity
                dict_p.accounting_quantity = p.accounting_quantity + dict_p.accounting_quantity
                transfer_products.remove(p)
        if list_bom_type and isinstance(list_bom_type, list) and len(list_bom_type) > 0:
            raise DataValidationException("现做商品不允许调拨:" + ','.join(list_bom_type))
        return transfer_products

    def check_product_inventory(self, transfer_products=None, store_id=None, position_id=None, detail=False,
                                partner_id=None, user_id=None):
        out_product = []
        if position_id:
            sub_account_ids = [position_id]
        else:
            sub_account_ids = []
        product_ids = [p.product_id for p in transfer_products]
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:

            store_product_inventory_map = inventory_service.get_products_inventory_by_branch_id(branch_id=store_id,
                                                                                                sub_account_ids=sub_account_ids,
                                                                                                detail=detail,
                                                                                                partner_id=partner_id,
                                                                                                user_id=user_id,
                                                                                                product_ids=product_ids)
            # 校验库存
            for transfer_product in transfer_products:
                if position_id:
                    product_inventory = store_product_inventory_map.get(
                        str(position_id) + str(transfer_product.product_id))
                else:
                    product_inventory = store_product_inventory_map.get(str(transfer_product.product_id))
                if product_inventory:
                    if transfer_product.accounting_quantity != None and product_inventory.get('quantity_avail') != None:
                        logger.info("库存校验：调拨数量-{} - 库存数量-{}".format(float(transfer_product.accounting_quantity),
                                                                    float(product_inventory.get('quantity_avail'))))
                        if float(transfer_product.accounting_quantity) > float(product_inventory.get('quantity_avail')):
                            out_product.append(str(transfer_product.product_name))
                else:
                    out_product.append(str(transfer_product.product_name))
        return out_product

    def get_request_products_dict(self, request_product):
        transfer_products = []
        try:
            for p in request_product:
                product_obj = TransferProduct()
                p_data = {}
                for name in dir(product_obj):
                    if not name.startswith('__') and not name.startswith('_'):
                        if hasattr(p, name):
                            r_value = getattr(p, name)
                            if not callable(r_value):
                                p_data[name] = r_value
                set_model_from_props(p_data, product_obj)
                transfer_products.append(product_obj)
            return transfer_products
        except Exception:
            return []

    def valid_same_region_and_direct(self, shipping_store, receiving_store, partner_id, user_id):
        store_ids = [receiving_store, shipping_store]
        list_store_ret = metadata_service.get_store_list(ids=store_ids, partner_id=partner_id, user_id=user_id)
        list_store = []
        if list_store_ret:
            list_store = list_store_ret['rows']
        if list_store and isinstance(list_store, list) and len(list_store) == 2:
            attribute_region = []
            set_close_store = []
            for s in list_store:
                if 'transfer_region' in s and s['transfer_region'][0] not in attribute_region:
                    attribute_region.append(convert_to_int(s['transfer_region'][0]))
                if 'open_status' in s and str(s['open_status']).upper() != 'OPENED':
                    set_close_store.append(convert_to_int(s['id']))
            attribute_region = list(set(attribute_region))
            is_region = False
            if len(attribute_region) == 1:
                is_region = True
            return is_region, list(set(set_close_store))
        else:
            if list_store and isinstance(list_store, list) and len(list_store) > 0:
                ids = []
                for s in list_store:
                    ids.append(s.get('id'))
                if shipping_store not in ids:
                    raise DataValidationException(str(shipping_store))
                if receiving_store not in ids:
                    raise DataValidationException(str(receiving_store))

    def submit_transfer(self, request, partner_id, user_id, username=None):
        transfer_id = request.transfer_id
        branch_type = request.branch_type
        transfer_obj = self.get_transfer_by_id(transfer_id, partner_id=partner_id)
        transfer_obj = self.dict_to_object(transfer_obj)
        if not transfer_id:
            return None
        
        transfer_products = self.transfer_repo.list_transfer_product(transfer_id=transfer_id)
        transfer_products = [self.dict_to_object(i) for i in transfer_products]

        transfer_detail = self.transfer_repo.get_transfer_by_id(transfer_id, partner_id=partner_id, is_details=True)
        # if transfer_detail.status == 'INITED' or transfer_detail.status == 'CREATED':
        #     # 校验权限
        #     self.transfer_scope_check_branch(branch_type=branch_type, shipping_store=transfer_detail.shipping_store,
        #                                      partner_id=partner_id, user_id=user_id)
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            # 库存单位转换
            transfer_products = self.update_product_unit(transfer_products, partner_id, user_id)
            if transfer_products:
                # 租户是否校验库存
                allow_neg_inv = metadata_service.get_business_extra_config(
                    partner_id=partner_id, user_id=user_id, domain="boh.store.transfer").get("allow_neg_inv")
                if allow_neg_inv and allow_neg_inv is True:
                    # 允许负库存
                    pass
                else:
                    # 配置多仓位需要通过仓位来校验库存
                    if transfer_detail.shipping_position:
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                   store_id=transfer_detail.shipping_store,
                                                                   position_id=transfer_detail.shipping_position,
                                                                   detail=True,
                                                                   partner_id=partner_id, user_id=user_id)
                    else:
                        # 未配置多仓位还校验门店库存
                        out_product = self.check_product_inventory(transfer_products=transfer_products,
                                                                   store_id=transfer_detail.shipping_store,
                                                                   partner_id=partner_id, user_id=user_id)
                    if len(out_product) > 0:
                        raise TransferInventoryException("商品库存不存在或超出库存:" + '\n' + ','.join(out_product))
        if transfer_detail is not None and transfer_detail.status == 'INITED':
            # # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作
            transfer_detail = self.transfer_repo.submit_transfer(transfer_id=transfer_id,
                                                                 transfer_products=transfer_products,
                                                                 user_id=user_id, username=username,
                                                                 partner_id=partner_id)

            transfer_shipping_store_ret = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                                      branch_type=branch_type,
                                                                      partner_id=partner_id, user_id=user_id)
            transfer_receiving_store_code_ret = deepcopy(transfer_shipping_store_ret)
            transfer_shipping_store_name = transfer_shipping_store_ret.get('name')
            transfer_receiving_store_name = transfer_receiving_store_code_ret.get('name')
            
            # 调补提货
            TRANSFER_ACCOUNTS = []
            delivery_p_list = []
            rec_p_list = []
            for p in transfer_products:
                delivery_p = {
                    'delivery_by': int(transfer_detail.shipping_store), 
                    'product_id': int(p.product_id),
                    'product_code': p.product_code,
                    'product_name': p.product_name,
                    # 'storage_type': p.storage_type,
                    'unit_id': int(p.unit_id),
                    'unit_name': p.unit_name,
                    'unit_spec': p.unit_spec,
                    'delivery_quantity':p.accounting_quantity
                    # 'category_id':product.category_id
                }
                delivery_p_list.append(delivery_p)

                to_branch_id = transfer_detail.shipping_store
                TRANSFER_ACCOUNTS.append(dict(from_branch_id=transfer_detail.shipping_store,
                                                to_branch_id=to_branch_id,
                                                from_sub_account=transfer_detail.shipping_position,
                                                to_sub_account=transfer_detail.shipping_position,
                                                product_id=p.product_id,
                                                amount=str(abs(
                                                    p.accounting_quantity)) if p.accounting_quantity else 0
                                                )
                                            )

            # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
            # 承接sub_type不为INTERNAL,防止数据空跑
            out_batch_no = transfer_id
            if TRANSFER_ACCOUNTS:
                description = 'EXTERNAL_TRANSFER' if transfer_detail.sub_type == 'EXTERNAL' else 'INTERNAL_TRANSFER'
                code = description
                transfer_mode_deliver = 0  # 调拨发货模式
                message = dict(batch_no=str(out_batch_no),
                            code=code,
                            action=3,
                            description=description,
                            trace_id=transfer_detail.code,
                            accounts=TRANSFER_ACCOUNTS,
                            partner_id=partner_id,
                            user_id=user_id,
                            business_time=datetime.utcnow(),
                            transfer_mode=transfer_mode_deliver)
                inventory_dict = dict(batch_no=str(out_batch_no), code=code, batch_action=3,
                                    action_dec=ACTION[3],
                                    batch_id=None,
                                    status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                    )
                inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                                partner_id=partner_id, user_id=user_id, trace_id=str(transfer_id))

                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                    message=message)
            
            # 状态变更清理待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=transfer_detail.shipping_store,
                                             doc_type="transfer"))

            # PDA消息推送
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=transfer_detail.shipping_store,
                                                  source_root_id=transfer_id,
                                                  source_id=transfer_id,
                                                  source_type="TRANSFER",
                                                  action="SUBMITTED",
                                                  ref_source_id=transfer_id,
                                                  ref_source_type="TRANSFER",
                                                  ref_action="INITED",
                                                  content={"transfer_date": str(transfer_detail.transfer_date),
                                                           "updated_at": str(datetime.now()),
                                                           "updated_by": str(user_id),
                                                           "remark": transfer_detail.remark,
                                                           "updated_name": username,
                                                           "shipping_store_name": transfer_shipping_store_name,
                                                           "receiving_store_name": transfer_shipping_store_name
                                                           }
                                                  )
            
            # ---ForReceiptStart---#
            main_branch_type = None
            if transfer_detail.branch_type == 'STORE':
                main_branch_type = 'S'
            elif transfer_detail.branch_type == 'FRS_STORE':
                main_branch_type = 'FS'
            elif transfer_detail.branch_type == 'WAREHOUSE':
                main_branch_type = 'W'
            elif transfer_detail.branch_type == 'MACHINING_CENTER':
                main_branch_type = 'M'
            
            # vendor单据同步给三方
            message = {
                                    'doc_resource': main_branch_type.lower() +'_transfer',
                                    'doc_id': transfer_id,
                                    'partner_id': partner_id,
                                    'user_id': user_id,
                                    'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                }
            mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)
                
            # 同步记录落表，用于后续补偿
            tp_trans_log = {
                                'id': out_batch_no,
                                'doc_code': transfer_detail.code,
                                'doc_type': 's_transfer' if transfer_detail.branch_type !='FRS_STORE' else 'fs_transfer',
                                'status': 'inited',
                                'msg': str(message),
                                'partner_id': partner_id,
                                'created_by': user_id,
                                'created_at': datetime.utcnow(),
                                'updated_at': datetime.utcnow()
                            }
            TpTransLogModel.create_logs_list([tp_trans_log])
            
            # 同步到receipt里的发货
            receipt_service.create_deliverys(
                batch_id=transfer_id, batch_code=transfer_detail.code, batch_type='TRANSFER', 
                order_id=transfer_id, order_code=transfer_detail.code,
                demand_id=transfer_id, demand_code=transfer_detail.code,  
                receive_by=int(transfer_detail.receiving_store), delivery_by=int(transfer_detail.shipping_store), 
                distr_type='NMD', main_branch_type=main_branch_type,
                delivery_date=transfer_detail.transfer_date, demand_date=transfer_detail.transfer_date, 
                products=delivery_p_list, 
                partner_id=partner_id, user_id=user_id)
                    
        return transfer_detail

    def cancel_transfer(self, request, partner_id, user_id, username=None):
        """取消调拨单"""
        transfer_id = request.transfer_id
        if not transfer_id:
            return None
        transfer_detail = self.transfer_repo.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if not transfer_detail:
            raise DataValidationException("未找到单据-{}".format(transfer_id))
        if transfer_detail.status != 'INITED':
            raise Exception("只有新建状态单据才能取消！")
        transfer_detail = self.transfer_repo.cancel_transfer(transfer_id=transfer_id, user_id=user_id,
                                                             partner_id=partner_id, username=username)
        # 取消清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=transfer_detail.shipping_store,
                                         doc_type="transfer"))
        return transfer_detail

    def check_confirmed_product_inventory(self, transfer_products, store_id, partner_id, user_id, position_id=None,
                                          detail=False):
        out_product = []
        if position_id:
            sub_account_ids = [position_id]
        else:
            sub_account_ids = []
        product_ids = [p.product_id for p in transfer_products]
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:

            store_product_inventory_map = inventory_service.get_products_inventory_by_branch_id(branch_id=store_id,
                                                                                                partner_id=partner_id,
                                                                                                user_id=user_id,
                                                                                                detail=detail,
                                                                                                sub_account_ids=sub_account_ids,
                                                                                                product_ids=product_ids)
            # 校验库存
            for transfer_product in transfer_products:
                if position_id:
                    product_inventory = store_product_inventory_map.get(
                        str(position_id) + str(transfer_product.product_id))
                else:
                    product_inventory = store_product_inventory_map.get(str(transfer_product.product_id))
                if product_inventory:
                    # + float(product_inventory.quantity_lock))
                    if transfer_product.accounting_received_quantity != None and product_inventory.get(
                            'quantity_avail') != None:
                        if float(transfer_product.accounting_received_quantity) > float(
                                product_inventory.get('quantity_avail')):
                            out_product.append(str(transfer_product.product_id))
                else:
                    out_product.append(str(transfer_product.product_id))
        return out_product

    def confirmed_transfer(self, request, partner_id, user_id, username=None):
        receiver = request.receiver
        receiving_store = request.receiving_store
        transfer_id = request.transfer_id
        branch_type = request.branch_type
        transfer_obj = self.transfer_repo.get_transfer_by_id(transfer_id, partner_id=partner_id)
        transfer_products = []
        # 调拨确认数量默认调出数量
        if request.products:
            if request.products:
                transfer_products = self.get_request_products_dict(request.products)
        if receiver is None:
            receiver = user_id
        transfer_detail = self.transfer_repo.get_transfer_by_id(transfer_id, is_details=True, partner_id=partner_id)
        if transfer_products and isinstance(transfer_products, list) and len(transfer_products) > 0:
            # 库存单位转换
            transfer_products = self.update_confirmed_product_unit(transfer_products, partner_id, user_id)
            if transfer_products:
                # 配置多仓位需要通过仓位来校验库存
                if transfer_detail.shipping_position:
                    out_product = self.check_confirmed_product_inventory(transfer_products=transfer_products,
                                                                         store_id=transfer_detail.shipping_store,
                                                                         position_id=transfer_detail.shipping_position,
                                                                         detail=True,
                                                                         partner_id=partner_id, user_id=user_id)
                else:
                    # 未配置多仓位还校验门店库存
                    out_product = self.check_confirmed_product_inventory(transfer_products=transfer_products,
                                                                         store_id=transfer_detail.shipping_store,
                                                                         partner_id=partner_id, user_id=user_id)
                if len(out_product) > 0:
                    pass
                    # raise TransferInventoryException("调出门店的商品库存不存在或超出库存:" +'\n'+ ','.join(out_product))
        if transfer_detail is not None and transfer_detail.status == 'SUBMITTED':
            # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作
            # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作
            # transfer_product_list = self.list_transfer_product(transfer_id=transfer_detail.transfer_id)
            # # 冻结处理 1、调拨点做冻结出库操作  2、接收方做冻结入库操作

            transfer_shipping_store_ret = self.get_store_detail_by_id(store_id=transfer_detail.shipping_store,
                                                                      branch_type=branch_type,
                                                                      partner_id=partner_id, user_id=user_id)
            transfer_shipping_store_code = transfer_shipping_store_ret.get('code')
            transfer_shipping_store_name = transfer_shipping_store_ret.get('name')
            transfer_receiving_store_code_ret = self.get_store_detail_by_id(store_id=transfer_detail.receiving_store,
                                                                            branch_type=branch_type,
                                                                            partner_id=partner_id, user_id=user_id)
            transfer_receiving_store_code = transfer_receiving_store_code_ret.get('code')
            transfer_receiving_store_name = transfer_receiving_store_code_ret.get('name')
            message = {
                "transfer_code": transfer_detail.code,
                "transfer_shipping_store_code": transfer_shipping_store_code,
                "transfer_receiving_store_code": transfer_receiving_store_code,
                "transfer_receiving_date": datetime.now().strftime("%Y-%m-%d"),
                'transfer_products': []
            }
            TRANSFER_ACCOUNTS = []
            rec_p_list = []
            for p in transfer_products:           
                rec_p = {
                    'receive_by': int(transfer_obj.receiving_store),
                    'product_id': int(p.product_id),
                    'product_code': p.product_code,
                    'product_name': p.product_name,
                    # 'storage_type': product.storage_type,
                    'unit_id': int(p.unit_id),
                    'unit_name': p.unit_name,
                    'unit_spec': p.unit_spec,
                    'delivery_quantity': p.accounting_received_quantity,
                    'receive_quantity': p.accounting_received_quantity
                    # 'category_id':product.category_id
                }
                rec_p_list.append(rec_p)

                to_branch_id = transfer_detail.receiving_store
                TRANSFER_ACCOUNTS.append(dict(from_branch_id=transfer_detail.shipping_store,
                                              to_branch_id=to_branch_id,
                                              from_sub_account=transfer_detail.shipping_position,
                                              to_sub_account=transfer_detail.receiving_position,
                                              product_id=p.product_id,
                                              amount=str(abs(
                                                  p.accounting_received_quantity)) if p.accounting_received_quantity else 0
                                              )
                                         )

            # 整理库存请求参数先存在supply_inventory_action_doc表中防止nsq未发出去
            description = 'EXTERNAL_TRANSFER' if transfer_detail.sub_type == 'EXTERNAL' else 'INTERNAL_TRANSFER'
            code = description
            transfer_mode_deliver = 1  # 调拨收货模式
            in_batch_no = transfer_detail.id
            message = dict(batch_no=str(in_batch_no),
                           code=code,
                           action=3,
                           description=description,
                           trace_id=transfer_obj.code,
                           accounts=TRANSFER_ACCOUNTS,
                           partner_id=partner_id,
                           user_id=user_id,
                           business_time=datetime.utcnow(),
                           transfer_mode=transfer_mode_deliver)
            inventory_dict = dict(batch_no=str(in_batch_no), code=code, batch_action=3,
                                  action_dec=ACTION[3],
                                  batch_id=None,
                                  status="ERROR",  # 默认先给ERROR一旦消息未发出补偿就会自动进行重发
                                  )
            inventory_repository.save_calculate_send_message(inventory_dict, message,
                                                             partner_id=partner_id, user_id=user_id, trace_id=str(transfer_id))

            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.INVENTORY_CALCULATE_TOPIC,
                                message=message)

            transfer_detail = self.transfer_repo.confirmed_transfer(transfer_id=transfer_id, receiver=receiver,
                                                                    receiving_store=receiving_store,
                                                                    transfer_products=transfer_products,
                                                                    user_id=user_id, username=username,
                                                                    partner_id=partner_id)
            # 状态变更清理接受门店待办缓存
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                                message=dict(partner_id=partner_id, store_id=transfer_detail.receiving_store,
                                             doc_type="transfer"))
            # 记录工作流日志
            flow_log_detail = dict(
                doc_id=transfer_detail.transfer_id,
                doc_type="insideTransfer" if transfer_detail.sub_type == "INTERNAL" else "transfer",
                operation="INNER_TRANSFER" if transfer_detail.sub_type == "EXTERNAL" else "MATERIAL_CONVERT",
                branch_id=transfer_detail.receiving_store if transfer_detail.sub_type == "EXTERNAL" else transfer_detail.shipping_store,
                sub_branch_id=transfer_detail.receiving_position,
                sub_doc_type='position' if transfer_detail.receiving_position else 'store',
                posterior_operation='MATERIAL_CONVERT' if transfer_detail.sub_type == "EXTERNAL" else "",
                process_status='INITED',
                partner_id=partner_id,
                created_by=user_id,
                created_at=datetime.utcnow(),
                created_name=username
            )
            flow_log_db = ReceiptFlowLogModel.create_flow_logs(**flow_log_detail)

            message = {
                'flow_id': flow_log_db.id,
                'partner_id': partner_id,
                'user_id': user_id
            }
            logger.info("调拨工作流触发:{}".format(flow_log_db.id))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.HANDLE_FLOW_TASK,
                                message=message)

            # vendor单据同步给三方
            message = {
                                'doc_resource': 's_transfer' if transfer_detail.branch_type !='FRS_STORE' else 'fs_transfer',
                                'doc_id': transfer_id,
                                'partner_id': partner_id,
                                'user_id': user_id,
                                'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }
            mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)
            
            # 同步记录落表，用于后续补偿
            tp_trans_log = {
                            'id': in_batch_no,
                            'doc_code': transfer_detail.code,
                            'doc_type': 's_transfer' if transfer_detail.branch_type !='FRS_STORE' else 'fs_transfer',
                            'status': 'inited',
                            'msg': str(message),
                            'partner_id': partner_id,
                            'created_by': user_id,
                            'created_at': datetime.utcnow(),
                            'updated_at': datetime.utcnow()
                        }
            TpTransLogModel.create_logs_list([tp_trans_log])

            # ---ForReceiptStart---#
            main_branch_type = None
            if transfer_obj.branch_type == 'STORE':
                main_branch_type = 'S'
            elif transfer_obj.branch_type == 'FRS_STORE':
                main_branch_type = 'FS'
            elif transfer_obj.branch_type == 'WAREHOUSE':
                main_branch_type = 'W'
            delivery_res = receipt_service.list_deliverys(batch_id=transfer_id,partner_id=partner_id, user_id=user_id)
            delivery_res = MessageToDict(delivery_res).get('rows')
            if delivery_res:
                delivery_res = delivery_res[0]
                # 同步到receipt里的收货
                receipt_service.create_receives(
                    batch_id=transfer_id, batch_code=transfer_obj.code, batch_type='TRANSFER',
                    order_id=transfer_id, order_code=transfer_obj.code,
                    delivery_id=int(delivery_res['id']),
                    receive_by=int(transfer_obj.receiving_store), delivery_by=int(transfer_obj.shipping_store),
                    distr_type='NMD', main_branch_type=main_branch_type,
                    delivery_date=transfer_obj.transfer_date, demand_date=transfer_obj.transfer_date,
                    products=rec_p_list, partner_id=partner_id, user_id=user_id)
            else:
                raise ActionException('确认失败')
            # ---ForReceiptEnd---#

            # PDA消息推送
            MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=transfer_detail.shipping_store,
                                                  source_root_id=transfer_id,
                                                  source_id=transfer_id,
                                                  source_type="TRANSFER",
                                                  action="CONFIRMED",
                                                  ref_source_id=transfer_id,
                                                  ref_source_type="TRANSFER",
                                                  ref_action="SUBMITTED",
                                                  content={"transfer_date": str(transfer_detail.transfer_date),
                                                           "updated_at": str(datetime.now()),
                                                           "updated_by": str(user_id),
                                                           "remark": transfer_detail.remark,
                                                           "updated_name": username,
                                                           "shipping_store_name": transfer_shipping_store_name,
                                                           "receiving_store_name": transfer_receiving_store_name
                                                           }
                                                  )

        else:
            raise ActionException('确认失败')
        return transfer_detail

    def delete_transfer_product(self, transfer_id, ids, partner_id, user_id):
        transfer_obj = self.transfer_repo.get_transfer_by_id(transfer_id, partner_id=partner_id)
        if transfer_obj:
            if transfer_obj.status == 'INITED':
                ret = self.transfer_repo.delete_transfer_by_product(ids, partner_id=partner_id)
                self.transfer_repo.recalculate_transfer_total_amount(transfer_id=transfer_id)
                return ret
            else:
                raise DataValidationException("status must be inited")
        raise DataValidationException('data not find')

    def delete_transfer(self, transfer_id, partner_id, user_id):
        transfer_obj = self.transfer_repo.get_transfer_by_id(transfer_id, partner_id=partner_id)
        if transfer_id and transfer_obj:
            if transfer_obj.status == 'INITED':
                # store_scope_check(transfer_obj.shipping_store, partner_id, user_id)
                return self.transfer_repo.delete_transfer(transfer_id, partner_id=partner_id)
            else:
                raise DataValidationException("status must be inited")
        raise DataValidationException('data not find')

    def update_transfer_status(self, transfer_id, user_id=None, partner_id=None):
        return self.transfer_repo.update_transfer_status(transfer_id=transfer_id, user_id=user_id, partner_id=partner_id)

    def bi_get_transfer_collect(self, partner_id, user_id, st_ids=None, category_ids=None, product_name=None,
                                start_date=None, end_date=None, limit=None, offset=None, include_total=False,
                                is_in=False, store_ids=None, order=None, sort=None, branch_type=None, sub_type=None,
                                transfer_type=None):
        product_ids = []
        if (category_ids and isinstance(category_ids, list) and len(category_ids) > 0) or product_name:
            relation_filters = dict()
            if category_ids and isinstance(category_ids, list) and len(category_ids) > 0:
                relation_filters['product_category'] = [str(category_id) for category_id in category_ids]
            search, search_fields = None, None
            if product_name:
                search = str(product_name)
                search_fields = 'name'
            list_products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                  return_fields='id,code,name',
                                                                  search=search, search_fields=search_fields,
                                                                  include_units=True,
                                                                  partner_id=partner_id, user_id=user_id
                                                                  )
            list_products = []
            if list_products_ret:
                list_products = list_products_ret['rows']
            # list_products = hex_api.get_service('product').list(partner_id, querystring=querystring)
            if not isinstance(list_products, list) or len(list_products) == 0:
                return None, 0, 0, 0
            for product in list_products:
                product_ids.append(convert_to_int(product['id']))
        return self.transfer_repo.bi_get_transfer_collect(partner_id, user_id=user_id, st_ids=st_ids,
                                                          product_ids=product_ids, start_date=start_date,
                                                          end_date=end_date, is_in=is_in, limit=limit, offset=offset,
                                                          include_total=include_total, store_ids=store_ids,
                                                          order=order, sort=sort, branch_type=branch_type,
                                                          sub_type=sub_type, transfer_type=transfer_type)

    def bi_get_transfer_detailed(self, partner_id, user_id, st_ids=None, category_ids=None, product_name=None,
                                 start_date=None, end_date=None, limit=None, offset=None, include_total=False,
                                 is_in=False, store_ids=None, order=None, sort=None, branch_type=None, sub_type=None,
                                 transfer_type=None):
        product_ids = []
        if (category_ids and isinstance(category_ids, list) and len(category_ids) > 0) or product_name:
            relation_filters = dict()
            if category_ids and isinstance(category_ids, list) and len(category_ids) > 0:
                relation_filters['product_category'] = [str(category_id) for category_id in category_ids]
            search, search_fields = None, None
            if product_name:
                search = str(product_name)
                search_fields = 'name'
            list_products_ret = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                  return_fields='id,code,name',
                                                                  search=search, search_fields=search_fields,
                                                                  include_units=True,
                                                                  partner_id=partner_id, user_id=user_id
                                                                  )
            list_products = []
            if list_products_ret:
                list_products = list_products_ret['rows']
            if not isinstance(list_products, list) or len(list_products) == 0:
                return None, 0, 0, 0
            for product in list_products:
                product_ids.append(convert_to_int(product['id']))
        return self.transfer_repo.bi_get_transfer_detailed(partner_id, user_id=user_id, st_ids=st_ids,
                                                           product_ids=product_ids, start_date=start_date,
                                                           end_date=end_date, is_in=is_in, limit=limit, offset=offset,
                                                           include_total=include_total, store_ids=store_ids,
                                                           order=order, sort=sort, branch_type=branch_type,
                                                           sub_type=sub_type, transfer_type=transfer_type)

    def get_transfer_log(self, transfer_id, partner_id=None, user_id=None):
        res = {}
        total, transfer_logs = self.transfer_repo.get_transfer_log(transfer_id, partner_id=partner_id)
        res["rows"] = []
        res["total"] = total
        user_ids = [log.created_by for log in transfer_logs]
        user_dict = get_username_map(partner_id=partner_id, user_id=user_id, ids=user_ids)
        for log in transfer_logs:
            row = dict(
                id=log.id,
                status=log.transfer_status,
                created_at=self.get_timestamp(log.created_at),
                created_by=log.created_by,
                created_name=user_dict.get(log.created_by),
                reason=log.reason
            )
            res["rows"].append(row)
        return res

    def dict_to_object(self, dictObj):
        if not isinstance(dictObj, dict):
            return dictObj
        inst=Dict()
        for k,v in dictObj.items():
            inst[k] = self.dict_to_object(v)
        return inst


mobile_transfer_service = MobileTransferModule()
