# -*- coding: utf-8 -*-
import json

from supply.utils.helper import get_guid
from sqlalchemy import func
import logging
from datetime import datetime, timedelta,timezone
from google.protobuf.timestamp_pb2 import Timestamp
from decimal import Decimal
from supply import logger
from supply.api import handle_attachments, handle_request_attachments
from supply.error.exception import NoResultFoundError, StatusUnavailable, DataValidationException, DealInventoryException

from supply.driver.mysql import session
from supply.model.attachments import AttachmentsModel
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefund
from supply.module.returns import returns_service
from supply.utils.encode import encodeUTF8
from supply.utils.resulting import ErrorCode
from supply.utils.snowflake import gen_snowflake_id
from supply.utils.helper import MessageTopic, convert_to_int, get_product_unit_map, get_branch_list_map
from supply.utils import pb2dict
from supply.client.metadata_service import metadata_service 
from supply.client.inventory_service import inventory_service
from supply.client.receipt_service import receipt_service
from supply.task.message_service_pub import MessageServicePub
# from supply.task import public
from supply.driver.mq import mq_producer

from supply.model.supply_doc_code import Supply_doc_code
from supply.model.receiving_diff import ReceivingDiffModel, ReceivingDiffProductModel, ReceivingDiffLogModel
from supply.client.products_manage_service import products_manage_service



class MobileReceivingDiffService():
    '''收货差异单相关服务
    service: 
        - create_receiving_diff()：新建收货差异单
        - get_receiving_diff_by_id(): 通过diff_id查询收货差异单信息
        - list_receiving_diff_products_by_diff_id():根据diff_id查询收货差异单商品明细
        - list_receiving_diffs(): 枚举所有收货差异单
        - update_receiving_diff(): 更新收货差异单（差异数量两边承担数量、差异原因、备注）
        - submit_receiving_diff(): 提交收货差异单
        - confirm_receiving_diff(): 确认收货差异单
        - reject_receiving_diff(): 驳回收货差异单
        - delete_receiving_diff(): 删除收货差异单
    '''

    def deal_with_inventory(self, batch_no, s_diff_product_list, d_diff_product_list, code, action, partner_id, user_id, trace_id=None):
        detail_list = []
        sequence = 0
        for s_product_detail in s_diff_product_list:
            # 仓库在途转门店可用
            if s_product_detail["amount"] != 0:
                detail = {
                        "sequence_id": sequence,
                        "transfer": {
                                        "from": {
                                            "branch_id": s_product_detail["from"],
                                            "product_id": s_product_detail["product_id"],
                                            "extra":
                                            {
                                                "code":'DemandOrder', # 门店配送收货
                                                "type":3
                                            } # from`里要有`extra`字段，`"type":3`是`broker`类型，code`是业务自定义字段，发货和收货和收货差异的时候必须相同
                                        },
                                        "to": {
                                            "branch_id": s_product_detail["to"],
                                            "product_id": s_product_detail["product_id"]
                                        },
                                        "amount": s_product_detail["amount"]
                                    },
                        "action": "TRANSFER"
                    }
                sequence += 1
                detail_list.append(detail)
        # 仓库在途转仓库可用
        for d_product_detail in d_diff_product_list:
            if d_product_detail["amount"] != 0:
                detail = {
                        "sequence_id": sequence,
                        "transfer": {
                                        "from": {
                                            "branch_id": d_product_detail["from"],
                                            "product_id": d_product_detail["product_id"],
                                            "extra":
                                            {
                                                "code":'DemandOrder', # 门店配送收货
                                                "type":3
                                            } # from`里要有`extra`字段，`"type":3`是`broker`类型，code`是业务自定义字段，发货和收货和收货差异的时候必须相同
                                        },
                                        "to": {
                                            "branch_id": d_product_detail["to"],
                                            "product_id": d_product_detail["product_id"]
                                        },
                                        "amount": d_product_detail["amount"]
                                    },
                        "action": "TRANSFER"
                    }
                sequence += 1
                detail_list.append(detail)
  
        if len(detail_list) == 0:
            return None
            
        result = inventory_service.deal_with_inventory(batch_no=str(batch_no), code=code, action=action,
                        description="ReceiveDiffApprove", detail=detail_list, partner_id=partner_id, 
                        user_id=user_id, trace_id=trace_id)

        return result


    def get_receiving_diff_by_id(self, receiving_diff_id, partner_id, user_id):
        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)
        if receiving_diff_db:
            branch_ids = [receiving_diff_db.delivery_by]
            supplier_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center", branch_ids=branch_ids,
                                             partner_id=partner_id, user_id=user_id)
            store_map = get_branch_list_map(branch_type="store", branch_ids=[receiving_diff_db.received_by],
                                             partner_id=partner_id, user_id=user_id)
            refunds = SupplyFranchiseeRefund.query_refund_by_main_ids(partner_id=partner_id, main_ids=[receiving_diff_db.id])
            receiving_diff_obj = receiving_diff_db.serialize(conv=True)
            receiving_diff_obj['delivery_by_name'] = supplier_map.get(receiving_diff_obj['delivery_by'], {}).get('name')
            receiving_diff_obj['received_by_name'] = store_map.get(receiving_diff_obj['received_by'], {}).get('name')
            receiving_diff_obj['received_by_code'] = store_map.get(receiving_diff_obj['received_by'], {}).get('code')
            receiving_diff_obj['attachments'] = handle_attachments(receiving_diff_db.attachments)
            receiving_diff_obj['refund_id'] = refunds[0].id if refunds else 0
            receiving_diff_obj['refund_code'] = refunds[0].code if refunds else ''
            
            return receiving_diff_obj
        return None

    def list_receiving_diff_products_by_diff_id(self, diff_id,limit=None,offset=None,include_total=False, 
                                                    sort=None, order=None, partner_id=None, user_id=None, is_frs=False):
        ''' 根据diff_id 枚举该收货单上所有商品明细 '''
        count = None
        count, receiving_diff_products_db_list = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(diff_id,limit,offset,include_total, sort, order, partner_id)
        # 获取订货价和零售价
        product_ids = [rf.product_id for rf in receiving_diff_products_db_list]
        product_dict = {}
        products = metadata_service.get_product_list(return_fields='id,name,code,category,model_name',
                                                     partner_id=partner_id, user_id=user_id,
                                                     ids=product_ids).get("rows", [])
        # products = products['rows']
        for product in products:
            product_dict[int(product.get('id', 0))] = product
        price_map = {}
        if is_frs and count > 0:
            products = products_manage_service.GetAgentProducts(receiving_diff_products_db_list[0].received_by,
                                                                product_ids=product_ids, partner_id=partner_id,
                                                                user_id=user_id).get("rows", {}).get("list", [])
            for p in products:
                price_map[int(p.get("id"))] = {}
                for pr in p.get("product_price", []):
                    # 含税订货价
                    if pr.get("price_type_id", '0') == '1':
                        price_map[int(p.get("id"))]['tax_price'] = float(pr.get("tax_price", 0))
                    # 含税零售价
                    if pr.get("price_type_id", '0') == '2':
                        price_map[int(p.get("id"))]['retail_price'] = float(pr.get("tax_price", 0))
        logging.info("从价格中心获取零售价:{}-{}".format(price_map, products))
        receiving_diff_products_list = []
        total_d_amount = 0
        total_s_amount = 0
        if receiving_diff_products_db_list:
            for receiving_diff_product_db in receiving_diff_products_db_list:
                receiving_diff_product_obj = receiving_diff_product_db.serialize(conv=True)
                receiving_diff_product_obj['unit_spec'] = product_dict.get(receiving_diff_product_db.product_id, {}).get("model_name", '')
                receiving_diff_product_obj['retail_price'] = price_map.get(receiving_diff_product_db.product_id,
                                                                           {}).get("retail_price", 0)
                receiving_diff_product_obj['d_amount'] = float(receiving_diff_product_db.d_diff_quantity) * receiving_diff_product_obj.get('tax_price', 0)
                receiving_diff_product_obj['s_amount'] = float(receiving_diff_product_db.s_diff_quantity) * receiving_diff_product_obj.get('tax_price', 0)
                total_d_amount += receiving_diff_product_obj['d_amount'] if receiving_diff_product_obj.get("d_amount") else 0
                total_s_amount += receiving_diff_product_obj['s_amount'] if receiving_diff_product_obj.get("s_amount") else 0
                receiving_diff_products_list.append(receiving_diff_product_obj)
            return count, receiving_diff_products_list, total_d_amount, total_s_amount
        return 0, [], 0, 0

    def list_receiving_diffs(self, partner_id, user_id, request, received_type=None):
        '''枚举所有收货单'''

        limit=request.limit
        offset=request.offset
        include_total=False
        store_ids=request.store_ids
        status=request.status
        start_date=request.start_date
        end_date=request.end_date
        receiving_ids=request.receiving_ids
        receiving_code=request.receiving_code
        code=request.code
        logistics_type=request.logistics_type
        sort = request.sort
        order = request.order
        # 多租户版本新加筛选字段
        receive_date_from=request.receive_date_from
        receive_date_to=request.receive_date_to
        demand_date_from=request.demand_date_from
        demand_date_to=request.demand_date_to
        ids=request.ids
        delivery_bys = request.delivery_bys
        diff_type = request.diff_type
        product_ids = request.product_ids
        if len(ids) == 0:
            ids=None


        storeIdList = []
        for storeId in store_ids:
            storeIdList.append(int(storeId))
        store_ids = storeIdList

        count, receiving_diff_db = ReceivingDiffModel.list_receiving_diffs(partner_id=partner_id,
                                                                           user_id=user_id,
                                                                           limit=limit, offset=offset,
                                                                           include_total=include_total,
                                                                           store_ids=store_ids,
                                                                           status=status, start_date=start_date,
                                                                           end_date=end_date, receiving_ids=receiving_ids,
                                                                           receiving_code=receiving_code,
                                                                           code=code, logistics_type=logistics_type,
                                                                           sort=sort, order=order,
                                                                           receive_date_from=receive_date_from,
                                                                           receive_date_to=receive_date_to,
                                                                           demand_date_from=demand_date_from,
                                                                           demand_date_to=demand_date_to,ids=ids,
                                                                           received_type=received_type,
                                                                           delivery_bys=delivery_bys,
                                                                           product_ids=product_ids,
                                                                           diff_type=diff_type)
        if receiving_diff_db:
            supplier_map = {}
            branch_ids = [receiving_diff.delivery_by for receiving_diff in receiving_diff_db]
            store_ids = [receiving_diff.received_by for receiving_diff in receiving_diff_db]
            supplier_map = get_branch_list_map(branch_type="warehouse, vendor, machining_center", branch_ids=branch_ids,
                                             partner_id=partner_id, user_id=user_id)
            store_map = get_branch_list_map(branch_type="store", branch_ids=store_ids,
                                             partner_id=partner_id, user_id=user_id)

            diff_ids = []
            diff_pname_dict = {}
            for receiving_diff in receiving_diff_db:
                diff_ids.append(receiving_diff.id)
            diff_products_db_list = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_ids(
                                                    receiving_diff_ids=diff_ids,limit=5, partner_id=partner_id)
            # 获取退款单
            refund_dbs = SupplyFranchiseeRefund.query_refund_by_main_ids(partner_id=partner_id, main_ids=diff_ids)
            refund_map = {refund.main_id: refund for refund in refund_dbs}
            if diff_products_db_list:
                for diff_products_db in diff_products_db_list:
                    if diff_pname_dict.get(diff_products_db.diff_id):
                        diff_pname_dict[diff_products_db.diff_id].append(diff_products_db.product_name)
                    else:  
                        diff_pname_dict[diff_products_db.diff_id] = [diff_products_db.product_name]

            receiving_diff_list = []
            for receiving_diff in receiving_diff_db:
                refund = refund_map.get(receiving_diff.id)
                receiving_diff_obj = receiving_diff.serialize(conv=True)
                # receiving_diff_obj['attachments'] = eval(receiving_diff_obj['attachments']) if receiving_diff_obj.get('attachments') else []
                receiving_diff_obj['delivery_by_name'] = supplier_map.get(receiving_diff_obj['delivery_by'], {}).get('name')
                receiving_diff_obj['received_by_code'] = store_map.get(receiving_diff_obj['received_by'], {}).get('code')
                receiving_diff_obj['received_by_name'] = store_map.get(receiving_diff_obj['received_by'], {}).get('name')
                receiving_diff_obj['product_names_brief'] = diff_pname_dict.get(receiving_diff.id)
                receiving_diff_obj['refund_id'] = refund.id if refund else 0
                receiving_diff_obj['refund_code'] = refund.code if refund else ''
                receiving_diff_obj['refund_status'] = refund.status if refund else ''
                receiving_diff_obj['attachments'] = handle_attachments(receiving_diff.attachments)
                receiving_diff_list.append(receiving_diff_obj)
            return count, receiving_diff_list
        return 0, None

    def update_receiving_diff(self, receiving_diff_id, partner_id, user_id, products=None, attachments=None, remark=None):
        # 配送收货差异单商品明细更新
        # 修改差异单商品明细（差异原因、门店承担数量、配送方承担数量、备注）
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        diff_args = {
            'id': receiving_diff_id,
            'updated_at': datetime.now(),
            'updated_by': user_id,
            'updated_name': operator_name
        }
        if attachments:
            diff_args.update(dict(attachments=handle_request_attachments(attachments)))
        if remark:
            diff_args.update(dict(remark=remark))

        updated_products = []
        rec_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id, receiving_diff_id)
        for product in products:
            p_args = {
                'id': product.id,
                'updated_at':datetime.now(),
                'updated_by':user_id,
                'updated_name':operator_name,
            }
            product = pb2dict(product)

            if product.get('remark'):
                p_args['remark'] = product['remark']
            if product.get('reason_type'):
                p_args['reason_type'] = product['reason_type']
            if product.get('s_diff_quantity'):
                p_args['s_diff_quantity'] = product['s_diff_quantity']
                p_args['s_diff_accounting_quantity'] = float(product['s_diff_quantity'])*float(product['unit_rate']) if product.get('unit_rate') else float(product['s_diff_quantity'])
            else:
                p_args['s_diff_quantity'] = 0
            if product.get('d_diff_quantity'):
                p_args['d_diff_quantity'] = product['d_diff_quantity']
                p_args['d_diff_accounting_quantity'] = float(product['d_diff_quantity'])*float(product['unit_rate']) if product.get('unit_rate') else float(product['d_diff_quantity'])
            else:
                p_args['d_diff_quantity'] = 0
            if product.get("diff_quantity"):
                p_args['diff_quantity'] = float(product.get("diff_quantity", 0))
            if rec_diff_db.diff_type == 'HC':
                p_args['diff_quantity'] = product['d_diff_quantity']
                p_args['d_diff_accounting_quantity'] = product['d_diff_quantity'] * float(product['unit_rate']) if product.get('unit_rate') else float(product['d_diff_quantity'])
                p_args['s_diff_quantity'] = 0
                p_args['s_diff_accounting_quantity'] = 0

            updated_products.append(p_args)
            
        # 一把更新数据库
        ReceivingDiffProductModel.update_receive_diff_in_all(diff_detail=[diff_args], diff_product_list=updated_products)

        # 记录操作日志
        log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'UPDATE',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
            }
        ReceivingDiffLogModel.create_logs_in_all([log_detail])
        # 附件入库
        attachments_list = []
        AttachmentsModel.delete_attachments_by_doc_id(receiving_diff_id, 'receive_diff')
        if attachments:
            for attachment in attachments:
                args = {
                    'doc_id': receiving_diff_id,
                    'doc_type': 'receive_diff',
                    'attachment': handle_request_attachments(attachment),
                    'signature': '',
                    'nosign_reason': '',
                    'partner_id': partner_id,
                    'created_by': user_id,
                    'updated_by': user_id,
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                }
                attachments_list.append(args)
        AttachmentsModel.create_attachment_in_all(attachments=attachments_list)
        return True
     
    def submit_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)

        if receiving_diff_db.status not in ('INITED','REJECTED'):
            # 状态不允许提交
            raise StatusUnavailable("只有新建状态和驳回状态的单据可以被提交")
        now = datetime.now(timezone.utc)
        time_config_map, allow_specified_time = metadata_service.get_time_config(partner_id, user_id, "boh.store.diff", store_id=receiving_diff_db.received_by)
        if allow_specified_time and time_config_map.get(receiving_diff_db.received_by):
            start = time_config_map.get(receiving_diff_db.received_by)[0]
            end = time_config_map.get(receiving_diff_db.received_by)[1]
            # if start <= now <= end:
            if now < start or now > end:
                raise DataValidationException("时间限制, 不允许提交收货差异单")

        args = {
                'id':receiving_diff_id,
                'updated_at':datetime.now(),
                'status':'SUBMITTED',
                'updated_by':user_id,
                'review_by':user_id,
                'updated_name':operator_name,
                'auto_confirm_date': datetime.now()+timedelta(hours=2)
                }
        # 一把更新数据库

        # 记录操作日志
        log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'SUBMIT',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
            }
        ReceivingDiffProductModel.update_receive_diff_in_all([args], [], log_list=[log_detail])
        # ReceivingDiffLogModel.create_logs_in_all([log_detail])
        # 状态变更清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=receiving_diff_db.received_by,
                                         doc_type="receiving_diff"))
        # PDA消息推送
        MessageServicePub.pub_message_service(partner_id=partner_id,
                                                    user_id=user_id,
                                                    scope_id=1,
                                                    store_id=receiving_diff_db.received_by,
                                                    source_root_id=receiving_diff_db.receiving_id,
                                                    source_id=receiving_diff_db.id,
                                                    source_type="REC_DIFF",
                                                    action="SUBMITTED",
                                                    ref_source_id=receiving_diff_db.receiving_id,
                                                    ref_source_type="RECEIVING",
                                                    ref_action="INITED",
                                                    content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name}
                                                    )  
        return True

    def confirm_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        count, receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(
            receiving_diff_id=receiving_diff_id, partner_id=partner_id)
        if not receiving_diff_product_db:
            # 无差异商品
            raise NoResultFoundError("no diff products found!")

        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)

        # 提交状态
        if receiving_diff_db.status != 'SUBMITTED':
            # 状态不允许提交
            raise StatusUnavailable("only SUBMIITED data can be confirmed!")   
        
        # 2021-08-10 收货差异单不再生成退货单
        # 根据门店承担部分增加门店库存，仓库承担部分释放锁定的在途库存
        diff_product_list = []
        s_diff_product_list = []
        d_diff_product_list = []
        detail_list = []
        sequence = 0
        diff_args = {}
        for receiving_diff_product in receiving_diff_product_db:
            if receiving_diff_product.s_diff_quantity:
                if receiving_diff_product.unit_rate:
                    amount = receiving_diff_product.s_diff_quantity * receiving_diff_product.unit_rate 
                else:
                    amount = receiving_diff_product.s_diff_quantity
                parg = {
                            "product_id":receiving_diff_product.product_id,
                            "amount": amount,
                            "from": receiving_diff_db.delivery_by,
                            "to": receiving_diff_db.received_by
                        }
                s_diff_product_list.append(parg)

                recall_diff_arg = {
                    "product_id":receiving_diff_product.product_id,
                    "s_diff_quantity": receiving_diff_product.s_diff_quantity
                }
                diff_product_list.append(recall_diff_arg)
            
            if receiving_diff_product.d_diff_quantity:
                if receiving_diff_product.unit_rate:
                    amount = receiving_diff_product.d_diff_quantity * receiving_diff_product.unit_rate 
                else:
                    amount = receiving_diff_product.d_diff_quantity
                d_parg = {
                            "product_id":receiving_diff_product.product_id,
                            "amount": amount,
                            "from": receiving_diff_db.delivery_by,
                            "to": receiving_diff_db.delivery_by
                        }
                d_diff_product_list.append(d_parg)
            if receiving_diff_db.diff_type == 'HC':
                detail = {}
                detail['sequence_id'] = sequence
                accounting = {}
                account = {}
                account['branch_id'] = receiving_diff_db.delivery_by
                account['product_id'] = receiving_diff_product.product_id
                accounting['account'] = account
                accounting['amount'] = receiving_diff_product.d_diff_quantity * Decimal(
                    receiving_diff_product.unit_rate) if receiving_diff_product.unit_rate else receiving_diff_product.d_diff_quantity
                detail['accounting'] = accounting
                sequence += 1

                detail_list.append(detail)

        if receiving_diff_db.diff_type == 'HC':
            # 按照仓库承担数量生成差异退货单
            d_diff_pargs = []
            d_flag = False
            for receiving_diff_product in receiving_diff_product_db:

                if receiving_diff_product.d_diff_quantity:
                    d_flag = True
                    parg = {
                                'product_id':receiving_diff_product.product_id,
                                'product_name':receiving_diff_product.product_name,
                                'product_code':receiving_diff_product.product_code,
                                'unit_id':receiving_diff_product.unit_id,
                                'unit_spec':receiving_diff_product.unit_spec,
                                'unit_name':receiving_diff_product.unit_name,
                                'quantity':receiving_diff_product.d_diff_quantity,
                                'reason_type':'收货差异生成',
                                'tax_rate': receiving_diff_product.tax_rate,
                                'price': receiving_diff_product.cost_price,
                                'price_tax': receiving_diff_product.tax_price
                            }
                    d_diff_pargs.append(parg)

            if d_flag:
                order_json = {
                        'demand_order_code': receiving_diff_db.master_code,
                        'rec_code': receiving_diff_db.receiving_code,
                        'diff_code': receiving_diff_db.code
                        }

                _, diff_args = returns_service.create_diff_return(return_by=receiving_diff_db.received_by,
                                        return_to=receiving_diff_db.delivery_by,
                                        return_delivery_date=datetime.now(), return_reason='收货差异生成',
                                        product_detail=d_diff_pargs, partner_id=partner_id, user_id=user_id,
                                        source_id=receiving_diff_db.id, source_code=receiving_diff_db.code,
                                        logistics_type=receiving_diff_db.logistics_type, remark=order_json,
                                        diff_type=receiving_diff_db.diff_type, receiving_diff_id=receiving_diff_db.id)


        # 反写更新收货单数据库
        receipt_service.deal_receive_by_id(receive_id=receiving_diff_db.request_id, action='DIFF_CONFIRM', 
                                                    partner_id=partner_id, user_id=user_id, deal_products=diff_product_list)
        if receiving_diff_db.diff_type != 'HC':
            self.deal_with_inventory(batch_no=receiving_diff_db.id, s_diff_product_list=s_diff_product_list, \
                            d_diff_product_list=d_diff_product_list, code="RECEIVING_DIFF", action="MIXED", \
                            partner_id=partner_id, user_id=user_id, trace_id=receiving_diff_db.code)
        else:
            code = 'RECEIVING_DIFF'
            description='mcu receiving diff'
            result = inventory_service.deal_with_inventory(batch_no=str(receiving_diff_db.id), code=code, action='DEPOSIT',
                                                           description=description, detail=detail_list,
                                                           partner_id=partner_id,
                                                           user_id=user_id, trace_id=receiving_diff_db.code)
            diff_args = {'inventory_req_id': result['id'], 'inventory_status': result['status']}
        
        args = {            
                    'id': receiving_diff_id,
                    'updated_at': datetime.now(),
                    'receive_date': datetime.now(),
                    'status': 'CONFIRMED',
                    'has_checked': True,
                    'updated_by': user_id,
                    'review_by': user_id,
                    'updated_name': operator_name
                }
        args.update(**diff_args)
        # 记录操作日志
        log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'CONFIRM',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
                }

        # vendor单据同步给三方
        message = {
                            'doc_resource': 's_demand_rec_diff' if receiving_diff_db.branch_type !='FRS_STORE' else 'fs_demand_rec_diff',
                            'doc_id': receiving_diff_id,
                            'partner_id': partner_id,
                            'user_id': user_id,
                            'operate_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        }
        mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)

        # 同步记录落表，用于后续补偿
        tp_trans_log = {
                        'id': get_guid(),
                        'doc_code': receiving_diff_db.code,
                        'doc_type': 's_demand_rec_diff' if receiving_diff_db.branch_type !='FRS_STORE' else 'fs_demand_rec_diff',
                        'status': 'inited',
                        'msg': str(message),
                        'partner_id': partner_id,
                        'created_by': user_id,
                        'created_at': datetime.utcnow(),
                        'updated_at': datetime.utcnow()
                    }

        # 一把更新数据库
        ReceivingDiffProductModel.update_receive_diff_in_all([args], [], [log_detail], [tp_trans_log])

        # 状态变更清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=receiving_diff_db.received_by,
                                         doc_type="receiving_diff"))
        # PDA消息推送
        MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=receiving_diff_db.received_by,
                                                  source_root_id=receiving_diff_db.receiving_id,
                                                  source_id=receiving_diff_db.id,
                                                  source_type="REC_DIFF",
                                                  action="CONFIRMED",
                                                  ref_source_id=receiving_diff_db.id,
                                                  ref_source_type="REC_COLD",
                                                  ref_action="SUBMITTED",
                                                  content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name,}
                                                  )

        return True
        
    def reject_receiving_diff(self, receiving_diff_id, partner_id, user_id, reject_reason=None, attachments=None):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)
        receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id, partner_id)
        if not receiving_diff_product_db:
            # 无差异商品
            raise NoResultFoundError("no diff products found!")

        if receiving_diff_db.status != 'SUBMITTED':
            # 状态不允许提交
            raise StatusUnavailable("only SUBMIITED data can be rejected!")  
            
        args = {
                'id':receiving_diff_id,
                'updated_at':datetime.now(),
                'status':'REJECTED',
                'updated_by':user_id,
                'updated_name':operator_name,
                'review_by':user_id,
                'reject_reason':reject_reason,
                # 'attachments': handle_request_attachments(attachments) if attachments else receiving_diff_db.attachments,
                'auto_confirm_date': datetime.now()+timedelta(hours=2)
            }
        # 一把更新数据库
        ReceivingDiffProductModel.update_receive_diff_in_all([args], [])

        # 记录操作日志
        log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'REJECT',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'created_name': operator_name
            }
        ReceivingDiffLogModel.create_logs_in_all([log_detail])
        # 状态变更清理待办缓存
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                            topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC,
                            message=dict(partner_id=partner_id, store_id=receiving_diff_db.received_by,
                                         doc_type="receiving_diff"))
        # PDA消息推送
        MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=receiving_diff_db.received_by,
                                                  source_root_id=receiving_diff_db.receiving_id,
                                                  source_id=receiving_diff_db.id,
                                                  source_type="REC_DIFF",
                                                  action="REJECTED",
                                                  ref_source_id=receiving_diff_db.id,
                                                  ref_source_type="REC_COLD",
                                                  ref_action="SUBMITTED",
                                                  content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name,}
                                                  )
             
        return True
        
    def delete_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=receiving_diff_id)

        if receiving_diff_db.status == 'INITED' or receiving_diff_db.status == 'REJECTED':
            args = {
                'id': receiving_diff_id,
                'updated_at':datetime.now(),
                'status':'DELETED',
                'updated_by':user_id,
                'review_by':user_id,
                'updated_name':operator_name
            }
            # 一把更新数据库
            ReceivingDiffProductModel.update_receive_diff_in_all([args], [])

            # 记录操作日志
            log_detail = {
                'doc_id': receiving_diff_id,
                'operation': 'DELETE',
                'success': True,
                'partner_id': partner_id,
                'created_by': user_id,
                'created_at': datetime.now(),
                'created_name': operator_name
            }
            ReceivingDiffLogModel.create_logs_in_all([log_detail])
            return True
        
        else:
            # 状态不允许提交
            raise StatusUnavailable("only INITED data can be deleted!")  
    
    def get_diff_history(self, partner_id, user_id, receiving_diff_id):
        diff_logs, total = ReceivingDiffLogModel.get_by_doc_id(doc_id=receiving_diff_id, partner_id=partner_id,
                                                               need_update_create=False)
        log_list = []
        for log in diff_logs:
            log_detail = {
                'id': log.id,
                'status': log.operation,
                'updated_by': log.created_by,
                'updated_by_name': log.created_name,
                'updated_at':  Timestamp(seconds=int(log.updated_at.timestamp())), 
            }
            log_list.append(log_detail)
        return log_list, total


mobile_receiving_diff_service = MobileReceivingDiffService()