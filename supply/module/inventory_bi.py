# -*- coding: utf-8 -*-
import json
from datetime import datetime, timedelta
import logging
from decimal import Decimal
from ast import literal_eval
from hex_exception import RecordAlreadyExist
from google.protobuf.timestamp_pb2 import Timestamp

from ..client.products_manage_service import products_manage_service
from ..driver.xmq_python.adapters.producer_rocketmq import logger
from ..error.exception import NoResultFoundError, StatusUnavailable, DataValidationException
from ..utils.helper import set_model_from_db, convert_to_int, set_db, translate_utc_time, convert_to_decimal, \
    set_response_primary_key
from ..utils.decorators import method_decorator
# from ..utils.enums import Demand_type
from ..client.metadata_service import metadata_service
from ..client.inventory_service import inventory_service
from ..client.report_service import report_service
from ..driver.mysql import session
from ..client.infra_metadata_service import metadata_center_service

from ..model.inventory import inventory_repository


class InventoryBiService():
    '''库存报表相关服务
    service: 
        - get_realtime_inventory()：BOH门店实时库存查询
        - query_inventory_log_list()：库存流水查询
        - query_mcu_realtime_inventory()：JDE仓库实时库存查询
        - get_daily_inventory()：每日库存切片查询
    '''

    def get_store_or_warehouse_map(self, branch_ids, branch_type, partner_id=None, user_id=None, return_fields=None):
        """获取仓库或者门店map"""
        branch_dict = {}
        if branch_type == "WAREHOUSE":
            warehouse_detail_list = metadata_service.get_distribution_center_list(ids=branch_ids, partner_id=partner_id,
                                                                                  user_id=user_id, return_fields=return_fields)
            if warehouse_detail_list.get("rows"):
                warehouse_detail_list = warehouse_detail_list.get("rows")
                for warehouse_detail in warehouse_detail_list:
                    branch_dict[warehouse_detail["id"]] = warehouse_detail
        elif branch_type == "MACHINING_CENTER":
            machining_detail_list = metadata_service.get_machining_center_list(ids=branch_ids, partner_id=partner_id,
                                                                               user_id=user_id, return_fields=return_fields)
            if machining_detail_list and isinstance(machining_detail_list, list):
                for machining_detail in machining_detail_list:
                    branch_dict[machining_detail.get("id")] = machining_detail
        else:
            store_detail_list = metadata_service.get_store_list(ids=branch_ids, partner_id=partner_id, user_id=user_id,
                                                                return_fields=return_fields)

            if store_detail_list.get('rows'):
                store_detail_list = store_detail_list.get('rows')
                for store_detail in store_detail_list:
                    branch_dict[store_detail['id']] = store_detail
        return branch_dict

    @method_decorator(set_response_primary_key)
    def get_realtime_inventory(self, request, partner_id, user_id, is_frs=False):
        ''' 查询门店实时库存 '''
        branch_ids = request.branch_ids
        geo_regions = request.geo_regions
        category_ids = request.category_ids
        product_ids = request.product_ids
        limit = request.limit
        offset = request.offset
        sort = request.sort
        order = request.order
        exclude = request.exclude
        branch_type = request.branch_type
        position_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        return_fields = request.return_fields # 返回参数

        realtime_inventory_list = []
        count = 0
        if category_ids and product_ids:
            new_product_ids = []
            category_list = [c_id for c_id in category_ids]
            relation_filters = dict(product_category=category_list)
            products_by_catogory = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                     return_fields="id", partner_id=partner_id,
                                                                     user_id=user_id)
            products_by_catogory = products_by_catogory.get('rows', [])
            for product_by_catogory in products_by_catogory:
                new_product_ids.append(int(product_by_catogory['id']))
            product_ids = list(set(product_ids).intersection(set(new_product_ids)))
            if not product_ids:
                return [], 0


        elif category_ids and (not product_ids):
            new_product_ids = []
            category_list = [c_id for c_id in category_ids]
            relation_filters = dict(product_category=category_list)
            product_ids_list = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                 return_fields="id", partner_id=partner_id,
                                                                 user_id=user_id)
            product_ids_list = product_ids_list.get('rows', [])
            if not product_ids_list:
                return [], 0
            for product_id in product_ids_list:
                new_product_ids.append(int(product_id['id']))
            product_ids = new_product_ids

        geo_region_list = []
        store_ids = []
        if geo_regions:
            for geo_region in geo_regions:
                geo_region_list.append(str(geo_region))
            relation_filters = {'geo_region': geo_region_list}
            filters = {
                "open_status__in": ['OPENED'],
                "status__in": ["ENABLED"]
            }
            store_id_list = metadata_service.get_store_list(relation_filters=relation_filters,
                                                            filters=filters, return_fields="id",
                                                            partner_id=partner_id, user_id=user_id).get('rows', [])

            if not store_id_list:
                relation_filters = {'branch_region': geo_region_list}
                filters = {
                    "open_status__in": ['OPENED'],
                    "status__in": ["ENABLED"]
                }
                store_id_list = metadata_service.get_store_list(relation_filters=relation_filters,
                                                                filters=filters, return_fields="id",
                                                                partner_id=partner_id, user_id=user_id).get('rows', [])

            if store_id_list:
                for store_id in store_id_list:
                    store_ids.append(int(store_id['id']))
            else:
                return [], 0

        if not store_ids:
            branch_id_list = store_ids
        else:
            branch_id_list = []
        if branch_ids:
            for branch_id in branch_ids:
                branch_id_list.append(branch_id)

        # 新增一个branch_type 来区分门店还是仓库分别拉取主档信息
        branch_dict = self.get_store_or_warehouse_map(branch_ids=branch_id_list, branch_type=branch_type,
                                                      partner_id=partner_id, user_id=user_id,return_fields='id,code,name')

        # 传了商品id，但没有门店/仓库/加工中心id的 —— 业务场景：单商品全门店查询
        if len(product_ids) and len(branch_id_list) == 0:
            for key, value in branch_dict.items():
                branch_id_list.append(int(key))  # 筛选出相应的branch id，做一层branch过滤，以免查门店查出仓库实时库存

        product_inventory_dict, total = inventory_service.query_realtime_inventory(
            branch_id=branch_id_list,
            # sub_account_ids=position_ids,
            sort=sort, order=order, exclude=exclude,
            limit=limit, offset=offset,
            partner_id=partner_id,
            user_id=user_id, product_ids=product_ids,
            multi_product=True, aggregate=True, detail=True)
        # print('******', json.dumps(product_inventory_dict))
        if not product_inventory_dict:
            return realtime_inventory_list, total
        filter_product_ids = []
        for i in product_inventory_dict:
            filter_product_ids.append(int(i))

        # filter_product_details = metadata_service.get_product_list(ids=filter_product_ids, include_units=True,
        #                                                            return_fields='name,code,category,status,units,model_name',
        #                                                            partner_id=partner_id, user_id=user_id).get('rows')
        product_unit_dict, filter_product_details = metadata_service.get_product_units_dict(product_ids=filter_product_ids,
                                                                                            return_fields='name,code,category,status,units,model_name',
                                                                                            partner_id=partner_id, user_id=user_id)
        filter_product_details_dict = {}
        if filter_product_details:
            for i in filter_product_details:
                filter_product_details_dict[str(i['id'])] = i

        # 批量获取unit和category详情，并整理成以id为key的dict
        category_ids = []
        unit_ids = []
        if filter_product_details:
            for filter_product_detail in filter_product_details:
                if filter_product_detail.get('category'):
                    category_id = int(filter_product_detail['category'])
                    category_ids.append(category_id)

                if (filter_product_detail.get('units')):
                    for product_units_detail in filter_product_detail['units']:
                        unit_id = int(product_units_detail['id'])
                        unit_ids.append(unit_id)

        filter_unit_list = metadata_service.get_unit_list(ids=unit_ids, partner_id=partner_id, user_id=user_id,
                                                          return_fields='id,code,name')
        filter_unit_list = filter_unit_list.get('rows', [])
        key_filter_unit_dict = {}
        for filter_unit in filter_unit_list:
            key_filter_unit_dict[filter_unit['id']] = filter_unit

        filter_category_list = metadata_service.get_product_category_list(ids=category_ids, partner_id=partner_id,
                                                                          user_id=user_id, return_fields='id,code,name')
        filter_category_list = filter_category_list.get('rows')
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        # 拉取仓位主档
        # position_dict = metadata_service.get_position_dict(partner_id=partner_id, user_id=user_id)
        # print('position_listposition_listposition_listposition_listposition_listposition_list', position_dict)
        # 接入最外层返回的价格
        price_map = {}
        if "cost" in return_fields:
            if (not request.branch_ids) and len(request.product_ids) == 1:
                    price_map = self.get_price_by_product_id(product_id=product_ids[0], partner_id=partner_id)
                    # logging.info("从数据库获取的订货价格{}".format(price_map))
            else:
                for branch_id in branch_id_list:
                    price_map = self.get_price_by_store_id(store_id=branch_id, partner_id=partner_id)
        entities_currency = self.get_entities_currency(branch_type, branch_id_list, partner_id, user_id)

        for key, value in product_inventory_dict.items():
            product_id = int(key)
            demand_unit_rate = product_unit_dict.get(str(product_id),{}).get('order', {}).get('rate')
            if filter_product_details_dict.get(key):
                product_detail = filter_product_details_dict.get(key)
                product = value
                if isinstance(product, list):
                    for p in product:
                        tax_key = str(product_detail['id']) + str(p['branch_id'])
                        order_tax_price = price_map.get(tax_key, {}).get("tax_price", 0)
                            # 订货含税价格
                        retail_price = price_map.get(tax_key, {}).get("retail_price", 0)

                        inventory_detail = {}
                        # START拼接主档数据
                        inventory_detail['store_id'] = int(p['branch_id'])
                        inventory_detail['store_code'] = branch_dict.get(p['branch_id'], {}).get('code')
                        inventory_detail['store_name'] = branch_dict.get(p['branch_id'], {}).get('name')
                        inventory_detail['currency'] = entities_currency.get(int(p['branch_id']), '')
                        inventory_detail['product_id'] = int(product_detail['id'])
                        inventory_detail['product_code'] = product_detail['code']
                        inventory_detail['product_name'] = product_detail['name']
                        inventory_detail['product_status'] = product_detail['status']
                        inventory_detail['spec'] = product_detail.get('model_name', '')
                        inventory_detail['tax_price'] = order_tax_price
                        if is_frs:
                            inventory_detail['retail_price'] = retail_price
                        if product_detail.get('category'):
                            category_id = int(product_detail['category'])
                            inventory_detail['category_id'] = int(category_id)
                            if key_filter_category_dict.get(str(category_id)) is not None:
                                inventory_detail['category_name'] = key_filter_category_dict.get(str(category_id)).get(
                                    'name')

                        demand_unit_rate = 1
                        purchase_unit_rate = 1
                        product_id = product_detail['id']
                        # 核算单位
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get(
                                'default'):
                            unit_id = int(product_unit_dict.get(str(product_id)).get('default').get('id'))
                            unit_detail = key_filter_unit_dict.get(str(unit_id))
                            if unit_detail:
                                inventory_detail['accounting_unit_id'] = unit_id
                                inventory_detail['accounting_unit_code'] = unit_detail['code']
                                inventory_detail['accounting_unit_name'] = unit_detail['name']
                        # 订货单位
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get(
                                'order'):
                            demand_unit_id = int(product_unit_dict.get(str(product_id)).get('order').get('id'))
                            demand_unit_rate = product_unit_dict.get(str(product_id)).get('order').get('rate')
                            demand_unit_rate = round(demand_unit_rate, 6) if demand_unit_rate else 1
                            demand_unit_detail = key_filter_unit_dict.get(str(demand_unit_id))
                            if demand_unit_detail:
                                inventory_detail['demand_unit_id'] = demand_unit_id
                                inventory_detail['demand_unit_code'] = demand_unit_detail.get("code")
                                inventory_detail['demand_unit_name'] = demand_unit_detail.get("name")
                        # 采购单位
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get(
                                'purchase'):
                            purchase_unit_id = int(product_unit_dict.get(str(product_id)).get('purchase').get('id'))
                            purchase_unit_rate = product_unit_dict.get(str(product_id)).get('purchase').get('rate')
                            purchase_unit_rate = round(purchase_unit_rate, 6) if purchase_unit_rate else 1
                            purchase_unit_detail = key_filter_unit_dict.get(str(purchase_unit_id))
                            if purchase_unit_detail:
                                inventory_detail['purchase_unit_id'] = purchase_unit_id
                                inventory_detail['purchase_unit_code'] = purchase_unit_detail.get("code")
                                inventory_detail['purchase_unit_name'] = purchase_unit_detail.get("name")

                        inventory_detail['qty'] = convert_to_decimal(p['quantity_avail'])
                        inventory_detail['amount'] = p['quantity_avail'] * p['price']
                        # 核算数量=订货数量*订货单位比率
                        inventory_detail['demand_price'] = p['price'] * demand_unit_rate
                        inventory_detail['demand_qty'] = (convert_to_decimal(p['quantity_avail']) / convert_to_decimal(
                            demand_unit_rate) if demand_unit_rate else p[
                            'quantity_avail']).quantize(Decimal('0.********'))
                        inventory_detail['sku_amount'] = inventory_detail["demand_qty"]* order_tax_price
                        # 核算数量=采购数量*采购单位比率
                        inventory_detail['purchase_price'] = p['price'] * purchase_unit_rate
                        inventory_detail['purchase_qty'] = (
                            convert_to_decimal(p['quantity_avail']) / convert_to_decimal(
                                purchase_unit_rate) if purchase_unit_rate else p[
                                'quantity_avail']).quantize(Decimal('0.********'))
                        # 采购单位
                        if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get(
                                'purchase'):
                            # purchase_unit_id = int(product_unit_dict.get(str(product_id)).get('purchase').get('id'))
                            purchase_unit_rate = product_unit_dict.get(str(product_id)).get('purchase').get('rate')
                        inventory_detail['freeze_qty'] = p['quantity_freeze']
                        inventory_detail['broker_qty'] = convert_to_decimal(p['quantity_broker']) # /convert_to_decimal(
                                                # purchase_unit_rate) if (purchase_unit_rate and p['quantity_broker']) else p['quantity_broker']
                        inventory_detail['demand_broker_qty'] = convert_to_decimal(p['quantity_broker'])/convert_to_decimal(
                                                demand_unit_rate) if (demand_unit_rate and p['quantity_broker']) else p['quantity_broker']

                        inventory_detail['extra_detail'] = list()
                        inventory_detail['children'] = list()
                        broker_code_map = {}
                        demand_order_map = {}
                        warehouse_purchase_map = {}
                        store_return_map = {}
                        transfer_map = {}
                        if p.get('extra_detail'):
                            for i in p['extra_detail']:
                                # 在途账户处理
                                if i.get('sku_type') and i['sku_type'] == 'broker':
                                    # 发货在途 -》订货单位
                                    if i.get('code') and i['code'] == 'DemandOrder':
                                        temp_qty = broker_code_map.get(i.get('code')) if broker_code_map.get(
                                            i.get('code')) else Decimal('0.********')
                                        temp_qty += (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                            demand_unit_rate) if i.get('quantity_avail') else 0
                                        broker_code_map[i['code']] = temp_qty
                                        demand_order_map["code"] = i['code']
                                        demand_order_map['qty'] = broker_code_map.get(i.get('code')) * convert_to_decimal(
                                                                    demand_unit_rate) if broker_code_map.get(i.get('code')) else Decimal('0.********')
                                        demand_order_map['sku_type'] = i['sku_type']
                                        demand_order_map['demand_qty'] = temp_qty
                                        demand_order_map['amount'] = temp_qty * order_tax_price
                                    # 采购在途 -》 采购单位
                                    if i.get('code') and i['code'] == 'WarehousePurchase':
                                        temp_qty = broker_code_map.get(i.get('code')) if broker_code_map.get(
                                            i.get('code')) else Decimal('0.********')
                                        temp_qty += (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                            demand_unit_rate) if i.get('quantity_avail') else 0
                                        broker_code_map[i['code']] = temp_qty
                                        warehouse_purchase_map["code"] = i['code']
                                        warehouse_purchase_map['qty'] = broker_code_map.get(i.get('code')) * convert_to_decimal(
                                                                    demand_unit_rate) if broker_code_map.get(i.get('code')) else Decimal('0.********')
                                        warehouse_purchase_map['sku_type'] = i['sku_type']
                                        warehouse_purchase_map['amount'] = temp_qty * order_tax_price
                                        warehouse_purchase_map['demand_qty'] = temp_qty
                                    # 退货在途 -》 订货单位
                                    if i.get('code') and i['code'] == 'StoreReturn':
                                        temp_qty = broker_code_map.get(i.get('code')) if broker_code_map.get(
                                            i.get('code')) else Decimal('0.********')
                                        temp_qty += (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                            demand_unit_rate) if i.get('quantity_avail') else 0
                                        broker_code_map[i['code']] = temp_qty
                                        store_return_map["code"] = i['code']
                                        store_return_map['qty'] = broker_code_map.get(i.get('code')) * convert_to_decimal(
                                                                    demand_unit_rate) if broker_code_map.get(i.get('code')) else Decimal('0.********')
                                        store_return_map['sku_type'] = i['sku_type']
                                        store_return_map['amount'] = temp_qty*order_tax_price
                                        store_return_map['demand_qty'] = temp_qty
                                    # 调拨在途 -》订货单位
                                    transfer_code = ['Transfer', 'StoreTransfer']
                                    if i.get('code') and i['code'] in transfer_code:
                                        temp_qty = broker_code_map.get(i.get('code')) if broker_code_map.get(
                                            i.get('code')) else Decimal('0.********')
                                        temp_qty += (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                            demand_unit_rate) if i.get('quantity_avail') else 0
                                        broker_code_map[i['code']] = temp_qty
                                        transfer_map["code"] = i['code']
                                        transfer_map['qty'] = broker_code_map.get(i.get('code')) * convert_to_decimal(
                                                                    demand_unit_rate) if broker_code_map.get(i.get('code')) else Decimal('0.********')
                                        transfer_map['sku_type'] = i['sku_type']
                                        transfer_map['amount'] = temp_qty * order_tax_price
                                        transfer_map['demand_qty'] = temp_qty

                                # 子账户处理
                                if i.get('sku_type') and i['sku_type'] == 'child':
                                    sub_inventory_detail = {
                                        "position_id": int(i.get('sub_account_id', 0)),
                                        # "position_name": position_dict.get(i.get('sub_account_id', 0), {}).get('name'),
                                        # "position_code": position_dict.get(i.get('sub_account_id', 0), {}).get('code'),
                                        "product_id": int(product_detail['id']),
                                        "product_code": product_detail['code'],
                                        "product_name": product_detail['name'],
                                        "product_status": product_detail['status'],
                                        "qty": convert_to_decimal(i.get('quantity_avail', 0)),
                                        "freeze_qty": convert_to_decimal(i.get('quantity_freeze', 0)),
                                        "broker_qty": convert_to_decimal(i.get('quantity_broker', 0)),
                                        "accounting_unit_id": inventory_detail.get('accounting_unit_id'),
                                        "accounting_unit_code": inventory_detail.get('accounting_unit_code'),
                                        "accounting_unit_name": inventory_detail.get('accounting_unit_name'),
                                        "demand_unit_id": inventory_detail.get('demand_unit_id'),
                                        "demand_unit_code": inventory_detail.get('demand_unit_code'),
                                        "demand_unit_name": inventory_detail.get('demand_unit_name'),
                                        "purchase_unit_id": inventory_detail.get('purchase_unit_id'),
                                        "purchase_unit_code": inventory_detail.get('purchase_unit_code'),
                                        "purchase_unit_name": inventory_detail.get('purchase_unit_name'),
                                        "demand_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                            demand_unit_rate) if demand_unit_rate else i[
                                            'quantity_avail']).quantize(Decimal('0.********')),
                                        "purchase_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                        purchase_unit_rate) if purchase_unit_rate else i[
                                                                'quantity_avail']).quantize(Decimal('0.********')),
                                        "demand_broker_qty": (convert_to_decimal(i.get('quantity_broker', 0)) / convert_to_decimal(
                                                        demand_unit_rate) if demand_unit_rate else i['quantity_broker']).quantize(Decimal('0.********'))

                                    }
                                    sub_inventory_detail['sku_amount'] =  sub_inventory_detail["demand_qty"] * order_tax_price

                                    if position_ids:  # 与前段沟通结果：如果传入指定仓位，则不要把仓位信息包含在child里
                                        inventory_detail['position_id'] = int(i.get('sub_account_id', 0))
                                        # inventory_detail['position_name'] = position_dict.get(
                                        #     i.get('sub_account_id', 0), {}).get('name')
                                        # inventory_detail['position_code'] = position_dict.get(
                                        #     i.get('sub_account_id', 0), {}).get('code')
                                    else:
                                        inventory_detail['children'].append(sub_inventory_detail)

                                #   sku主账户处理 —— 现处理为“无仓位”
                                if i.get('sku_type') and i['sku_type'] == 'sku':
                                    if position_ids:  # 如果传入仓位id，说明主账户不用显示
                                        pass
                                    else:
                                        sub_inventory_detail = {
                                            "position_id": int(i.get('sub_account_id', 0)),
                                            # "position_name": position_dict.get(i.get('sub_account_id', 0), {}).get(
                                            #     'name'),
                                            # "position_code": position_dict.get(i.get('sub_account_id', 0), {}).get(
                                            #     'code'),
                                            "product_id": int(product_detail['id']),
                                            "product_code": product_detail['code'],
                                            "product_name": product_detail['name'],
                                            "product_status": product_detail['status'],
                                            "qty": convert_to_decimal(i.get('quantity_avail', 0)),
                                            "freeze_qty": convert_to_decimal(i.get('quantity_freeze', 0)),
                                            "broker_qty": convert_to_decimal(i.get('quantity_broker', 0)),
                                            "accounting_unit_id": inventory_detail.get('accounting_unit_id'),
                                            "accounting_unit_code": inventory_detail.get('accounting_unit_code'),
                                            "accounting_unit_name": inventory_detail.get('accounting_unit_name'),
                                            "demand_unit_id": inventory_detail.get('demand_unit_id'),
                                            "demand_unit_code": inventory_detail.get('demand_unit_code'),
                                            "demand_unit_name": inventory_detail.get('demand_unit_name'),
                                            "purchase_unit_id": inventory_detail.get('purchase_unit_id'),
                                            "purchase_unit_code": inventory_detail.get('purchase_unit_code'),
                                            "purchase_unit_name": inventory_detail.get('purchase_unit_name'),
                                            "demand_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                            demand_unit_rate) if demand_unit_rate else i[
                                                                    'quantity_avail']).quantize(Decimal('0.********')),
                                            "purchase_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                            purchase_unit_rate) if purchase_unit_rate else i[
                                                                    'quantity_avail']).quantize(Decimal('0.********')),
                                            "demand_broker_qty": (convert_to_decimal(i.get('quantity_broker', 0)) / convert_to_decimal(
                                                        demand_unit_rate) if demand_unit_rate else i['quantity_broker']).quantize(Decimal('0.********'))

                                        }
                                        sub_inventory_detail['sku_amount'] = sub_inventory_detail[
                                                                         "demand_qty"] * order_tax_price

                        if warehouse_purchase_map:
                            inventory_detail['extra_detail'].append(warehouse_purchase_map)
                        if store_return_map:
                            inventory_detail['extra_detail'].append(store_return_map)
                        if transfer_map:
                            inventory_detail['extra_detail'].append(transfer_map)
                        if demand_order_map:
                            inventory_detail['extra_detail'].append(demand_order_map)
                        count += 1
                        realtime_inventory_list.append(inventory_detail)

                else:
                    tax_key = str(product_detail['id']) + str(product['branch_id'])
                    order_tax_price = price_map.get(tax_key, {}).get("tax_price", 0)
                    # 订货含税价格
                    retail_price = price_map.get(tax_key, {}).get("retail_price", 0)

                    inventory_detail = {}
                    inventory_detail['store_id'] = int(product['branch_id'])
                    inventory_detail['store_code'] = branch_dict.get(product['branch_id']).get('code')
                    inventory_detail['store_name'] = branch_dict.get(product['branch_id']).get('name')
                    inventory_detail['currency'] = entities_currency.get(int(product['branch_id']), '')
                    inventory_detail['product_id'] = int(product_detail['id'])
                    inventory_detail['product_code'] = product_detail['code']
                    inventory_detail['product_name'] = product_detail['name']
                    inventory_detail['product_status'] = product_detail['status']
                    inventory_detail['spec'] = product_detail.get('model_name', '')
                    inventory_detail['tax_price'] = order_tax_price
                    if is_frs:
                        inventory_detail['retail_price'] = retail_price

                    if product_detail.get('category'):
                        category_id = int(product_detail['category'])
                        inventory_detail['category_id'] = int(category_id)
                        inventory_detail['category_name'] = key_filter_category_dict.get(str(category_id), {}).get('name')

                    demand_unit_rate = 1
                    purchase_unit_rate = 1
                    product_id = product_detail['id']
                    
                    # 核算单位
                    if product_unit_dict.get(str(product_id),{}).get('default'):
                        unit_id = int(product_unit_dict.get(str(product_id)).get('default').get('id'))
                        unit_detail = key_filter_unit_dict.get(str(unit_id))
                        if unit_detail:
                            inventory_detail['accounting_unit_id'] = unit_id
                            inventory_detail['accounting_unit_code'] = unit_detail['code']
                            inventory_detail['accounting_unit_name'] = unit_detail['name']
                    
                    # 订货单位
                    if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('order'):
                        demand_unit_id = int(product_unit_dict.get(str(product_id)).get('order').get('id'))
                        demand_unit_rate = product_unit_dict.get(str(product_id)).get('order').get('rate')
                        demand_unit_rate = round(demand_unit_rate, 6) if demand_unit_rate else 1
                        demand_unit_detail = key_filter_unit_dict.get(str(demand_unit_id))
                        if demand_unit_detail:
                            inventory_detail['demand_unit_id'] = demand_unit_id
                            inventory_detail['demand_unit_code'] = demand_unit_detail.get("code")
                            inventory_detail['demand_unit_name'] = demand_unit_detail.get("name")

                    # 采购单位
                    if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get(
                            'purchase'):
                        purchase_unit_id = int(product_unit_dict.get(str(product_id)).get('purchase').get('id'))
                        purchase_unit_rate = product_unit_dict.get(str(product_id)).get('purchase').get('rate')
                        purchase_unit_rate = round(purchase_unit_rate, 6) if purchase_unit_rate else 1
                        purchase_unit_detail = key_filter_unit_dict.get(str(purchase_unit_id))
                        if purchase_unit_detail:
                            inventory_detail['purchase_unit_id'] = purchase_unit_id
                            inventory_detail['purchase_unit_code'] = purchase_unit_detail.get("code")
                            inventory_detail['purchase_unit_name'] = purchase_unit_detail.get("name")

                    inventory_detail['qty'] = convert_to_decimal(p['quantity_avail'])
                    inventory_detail['amount'] = p['quantity_avail'] * p['price']
                    # 核算数量=订货数量*订货单位比率
                    inventory_detail['demand_price'] = p['price'] * demand_unit_rate
                    inventory_detail['demand_qty'] = (convert_to_decimal(p['quantity_avail']) / convert_to_decimal(
                        demand_unit_rate) if demand_unit_rate else p[
                        'quantity_avail']).quantize(Decimal('0.********'))
                    inventory_detail['sku_amount'] = convert_to_decimal(inventory_detail['demand_qty'])* order_tax_price
                    # 核算数量=采购数量*采购单位比率
                    inventory_detail['purchase_price'] = p['price'] * purchase_unit_rate
                    inventory_detail['purchase_qty'] = (convert_to_decimal(p['quantity_avail']) / convert_to_decimal(
                        purchase_unit_rate) if purchase_unit_rate else p[
                        'quantity_avail']).quantize(Decimal('0.********'))
                    inventory_detail['freeze_qty'] = p['quantity_freeze']
                    inventory_detail['broker_qty'] = p['quantity_broker']
                    inventory_detail["demand_broker_qty"] = (convert_to_decimal(p.get('quantity_broker', 0.0)) / convert_to_decimal(
                        demand_unit_rate) if demand_unit_rate else p['quantity_broker']).quantize(Decimal('0.********'))


                    inventory_detail['extra_detail'] = list()
                    inventory_detail['children'] = list()
                    broker_code_map = {}
                    demand_order_map = {}
                    warehouse_purchase_map = {}
                    store_return_map = {}
                    transfer_map = {}
                    if p.get('extra_detail'):
                        transfer_qty = convert_to_decimal(0)
                        for i in p['extra_detail']:
                            # 在途账户处理
                            if i.get('sku_type') and i['sku_type'] == 'broker':
                                # 发货在途 -》订货单位
                                if i.get('code') and i['code'] == 'DemandOrder':
                                    temp_qty = broker_code_map.get(i.get('code')) if broker_code_map.get(
                                        i.get('code')) else Decimal('0.********')
                                    temp_qty += (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                        demand_unit_rate) if demand_unit_rate else convert_to_decimal(i.get("quantity_avail", 0))
                                    broker_code_map[i['code']] = temp_qty
                                    demand_order_map["code"] = i['code']
                                    demand_order_map['qty'] = broker_code_map.get(i.get('code')) * convert_to_decimal(
                                                                    demand_unit_rate) if broker_code_map.get(i.get('code')) else Decimal('0.********')
                                    demand_order_map['sku_type'] = i['sku_type']
                                    demand_order_map['amount'] = temp_qty * order_tax_price
                                    demand_order_map['demand_qty'] = temp_qty
                                # 采购在途 -》 采购单位
                                if i.get('code') and i['code'] == 'WarehousePurchase':
                                    temp_qty = broker_code_map.get(i.get('code')) if broker_code_map.get(
                                        i.get('code')) else Decimal('0.********')
                                    temp_qty += (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                        demand_unit_rate) if i.get('quantity_avail') else 0
                                    broker_code_map[i['code']] = temp_qty
                                    warehouse_purchase_map["code"] = i['code']
                                    warehouse_purchase_map['qty'] = broker_code_map.get(i.get('code')) * convert_to_decimal(
                                                                    demand_unit_rate) if broker_code_map.get(i.get('code')) else Decimal('0.********')
                                    warehouse_purchase_map['sku_type'] = i['sku_type']
                                    warehouse_purchase_map['amount'] = temp_qty * order_tax_price
                                    warehouse_purchase_map['demand_qty'] = temp_qty

                                # 退货在途 -》 订货单位
                                if i.get('code') and i['code'] == 'StoreReturn':
                                    temp_qty = broker_code_map.get(i.get('code')) if broker_code_map.get(
                                        i.get('code')) else Decimal('0.********')
                                    temp_qty += (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                        demand_unit_rate) if i.get('quantity_avail') else 0
                                    broker_code_map[i['code']] = temp_qty
                                    store_return_map["code"] = i['code']
                                    store_return_map['qty'] = broker_code_map.get(i.get('code')) * convert_to_decimal(
                                                                    demand_unit_rate) if broker_code_map.get(i.get('code')) else Decimal('0.********')
                                    store_return_map['sku_type'] = i['sku_type']
                                    store_return_map['amount'] = temp_qty * order_tax_price
                                    store_return_map['demand_qty'] = temp_qty

                                # 调拨在途 -》订货单位
                                transfer_code = ['Transfer', 'StoreTransfer']
                                if i.get('code') and i['code'] in transfer_code:
                                    temp_qty = broker_code_map.get(i.get('code')) if broker_code_map.get(
                                        i.get('code')) else Decimal('0.********')
                                    temp_qty += (convert_to_decimal(i['quantity_avail'])) / convert_to_decimal(
                                        demand_unit_rate) if i.get('quantity_avail') else 0
                                    broker_code_map[i['code']] = temp_qty
                                    transfer_map["code"] = i['code']
                                    transfer_map['qty'] = broker_code_map.get(i.get('code')) * convert_to_decimal(
                                                                    demand_unit_rate) if broker_code_map.get(i.get('code')) else Decimal('0.********')
                                    transfer_map['sku_type'] = i['sku_type']
                                    transfer_map['amount'] = temp_qty * order_tax_price
                                    transfer_map['demand_qty'] = temp_qty

                            # 子账户处理
                            elif i.get('sku_type') and i['sku_type'] == 'child':
                                sub_inventory_detail = {
                                        "position_id": int(i.get('sub_account_id', 0)),
                                        # "position_name": position_dict.get(i.get('sub_account_id', 0), {}).get('name'),
                                        # "position_code": position_dict.get(i.get('sub_account_id', 0), {}).get('code'),
                                        "product_id": int(product_detail['id']),
                                        "product_code": product_detail['code'],
                                        "product_name": product_detail['name'],
                                        "product_status": product_detail['status'],
                                        "qty": convert_to_decimal(i.get('quantity_avail',0)),
                                        "freeze_qty": convert_to_decimal(i.get('quantity_freeze', 0)),
                                        "broker_qty": convert_to_decimal(i.get('quantity_broker', 0)),
                                        "sku_amount": convert_to_decimal(i.get('quantity_avail', 0)) * order_tax_price,
                                        "demand_broker_qty": (
                                    convert_to_decimal(i.get('quantity_broker', 0.0)) / convert_to_decimal(
                                        demand_unit_rate) if demand_unit_rate else i['quantity_broker']).quantize(
                                    Decimal('0.********'))
                                    }
                                sub_inventory_detail["sku_amount"] = (sub_inventory_detail["qty"] / convert_to_decimal(demand_unit_rate) if demand_unit_rate else sub_inventory_detail["qty"]).quantize(Decimal('0.********'))
                                # 如果查询的是仓位，就直接把仓位的信息扔到最外层
                                if position_ids:
                                    inventory_detail['position_id'] = int(i.get('sub_account_id', 0))
                                    # inventory_detail['position_name'] = position_dict.get(i.get('sub_account_id', 0),
                                    #                                                       {}).get('name')
                                    # inventory_detail['position_code'] = position_dict.get(i.get('sub_account_id', 0),
                                    #                                                       {}).get('code')
                                else:
                                    inventory_detail['children'].append(sub_inventory_detail)

                            # sku主账户处理 —— 现处理为“无仓位”
                            elif i.get('sku_type') and i['sku_type'] == 'sku':
                                if position_ids:  # 如果传入仓位id，说明主账户不用显示
                                    pass
                                else:
                                    sub_inventory_detail = {
                                            "position_id": int(i.get('sub_account_id', 0)),
                                            # "position_name": position_dict.get(i.get('sub_account_id', 0), {}).get('name'),
                                            # "position_code": position_dict.get(i.get('sub_account_id', 0), {}).get('code'),
                                            "product_id": int(product_detail['id']),
                                            "product_code": product_detail['code'],
                                            "product_name": product_detail['name'],
                                            "product_status": product_detail['status'],
                                            "qty": convert_to_decimal(i.get('quantity_avail',0)),
                                            "freeze_qty": convert_to_decimal(i.get('quantity_freeze', 0)),
                                            "broker_qty": convert_to_decimal(i.get('quantity_broker', 0)),
                                            "accounting_unit_id": inventory_detail.get('accounting_unit_id'),
                                            "accounting_unit_code": inventory_detail.get('accounting_unit_code'),
                                            "accounting_unit_name": inventory_detail.get('accounting_unit_name'),
                                            "demand_unit_id": inventory_detail.get('demand_unit_id'),
                                            "demand_unit_code": inventory_detail.get('demand_unit_code'),
                                            "demand_unit_name": inventory_detail.get('demand_unit_name'),
                                            "purchase_unit_id": inventory_detail.get('purchase_unit_id'),
                                            "purchase_unit_code": inventory_detail.get('purchase_unit_code'),
                                            "purchase_unit_name": inventory_detail.get('purchase_unit_name'),
                                            "demand_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                            demand_unit_rate) if demand_unit_rate else i[
                                                                    'quantity_avail']).quantize(Decimal('0.********')),
                                            "purchase_qty": (convert_to_decimal(i['quantity_avail']) / convert_to_decimal(
                                                            purchase_unit_rate) if purchase_unit_rate else i[
                                                                    'quantity_avail']).quantize(Decimal('0.********')),
                                            "demand_broker_qty": (
                                            convert_to_decimal(i.get('quantity_broker', 0)) / convert_to_decimal(
                                                demand_unit_rate) if demand_unit_rate else i['quantity_broker']).quantize(
                                                Decimal('0.********'))
                                    }
                                    sub_inventory_detail["sku_amount"] = sub_inventory_detail["demand_qty"] * order_tax_price
                                    inventory_detail['children'].append(sub_inventory_detail)
                    if warehouse_purchase_map:
                        inventory_detail['extra_detail'].append(warehouse_purchase_map)
                    if store_return_map:
                        inventory_detail['extra_detail'].append(store_return_map)
                    if transfer_map:
                        inventory_detail['extra_detail'].append(transfer_map)
                    if demand_order_map:
                        inventory_detail['extra_detail'].append(demand_order_map)
                    count += 1
                    realtime_inventory_list.append(inventory_detail)

            else:
                product = value
                # acct_tax_price = cost_dict.get(product_id, {}).get("adjust_tax_price", 0)  # 核算含税价格
                # acct_cost_price = cost_dict.get(product_id, {}).get("adjust_price", 0)  # 核算不含税价格

                if isinstance(product, list):
                    for p in product:
                        tax_key = str(p.get("product_id", 0)) + str(p.get("branch_id", 0))
                        order_tax_price = price_map.get(tax_key, {}).get("tax_price", 0)
                        # 订货含税价格
                        retail_price = price_map.get(tax_key, {}).get("retail_price", 0)
                        inventory_detail = {}
                        inventory_detail['store_id'] = int(p.get('branch_id'))
                        inventory_detail['store_code'] = branch_dict.get(p['branch_id'], {}).get(
                            'code') if branch_dict.get(p['branch_id']) else None
                        inventory_detail['store_name'] = branch_dict.get(p['branch_id']).get('name') if branch_dict.get(
                            p['branch_id']) else '主档无此门店记录'
                        inventory_detail['currency'] = entities_currency.get(int(p['branch_id']), '')
                        inventory_detail['product_id'] = int(p.get('product_id', 0))
                        inventory_detail['product_name'] = '主档无此商品记录'
                        inventory_detail['tax_price'] = order_tax_price
                        if is_frs:
                            inventory_detail['retail_price'] = retail_price

                        inventory_detail['qty'] = convert_to_decimal(p['quantity_avail'])
                        inventory_detail['freeze_qty'] = p['quantity_freeze']
                        inventory_detail['broker_qty'] = p['quantity_broker']
                        # inventory_detail['sku_amount'] = convert_to_decimal(p['quantity_avail']) * order_tax_price
                        inventory_detail["demand_broker_qty"] = (
                            convert_to_decimal(p.get('quantity_broker', 0.0)) / convert_to_decimal(
                                demand_unit_rate) if demand_unit_rate else convert_to_decimal(p.get('quantity_broker', 0.0))).quantize(
                            Decimal('0.********'))
                        inventory_detail["sku_amount"] = convert_to_decimal(p['quantity_avail']) * order_tax_price / convert_to_decimal(
                                demand_unit_rate) if demand_unit_rate else convert_to_decimal(p['quantity_avail'], "0.0").quantize(
                            Decimal('0.********')) * order_tax_price
                        count += 1
                        realtime_inventory_list.append(inventory_detail)


                else:
                    inventory_detail = {}
                    tax_key = str(product['product_id']) + str(product['branch_id'])
                    order_tax_price = price_map.get(tax_key, {}).get("tax_price", 0)
                    # 订货含税价格
                    retail_price = price_map.get(tax_key, {}).get("retail_price", 0)
                    inventory_detail['store_id'] = int(product['branch_id'])
                    inventory_detail['store_code'] = branch_dict.get(product['branch_id']).get(
                        'code') if branch_dict.get(product['branch_id']) else None
                    inventory_detail['store_name'] = branch_dict.get(product['branch_id']).get(
                        'name') if branch_dict.get(product['branch_id']) else '主档无此门店记录'
                    inventory_detail['currency'] = entities_currency.get(int(product['branch_id']), '')
                    inventory_detail['product_id'] = int(product['product_id'])
                    inventory_detail['product_name'] = '主档无此商品记录'
                    if is_frs:
                        inventory_detail['retail_price'] = retail_price
                        inventory_detail['tax_price'] = order_tax_price

                    inventory_detail['qty'] = convert_to_decimal(product['quantity_avail'])
                    inventory_detail['freeze_qty'] = product['quantity_freeze']
                    inventory_detail['broker_qty'] = product['quantity_broker']
                    # inventory_detail['sku_amount'] = convert_to_decimal(p['quantity_avail']) * order_tax_price
                    inventory_detail["demand_broker_qty"] = (
                        convert_to_decimal(p.get('quantity_broker', 0.0)) / convert_to_decimal(
                            demand_unit_rate) if demand_unit_rate else p['quantity_broker']).quantize(
                        Decimal('0.********'))

                    inventory_detail["sku_amount"] = convert_to_decimal(p['quantity_avail']) * order_tax_price / convert_to_decimal(
                        demand_unit_rate) if demand_unit_rate else convert_to_decimal(p['quantity_avail'],"0.0").quantize(
                        Decimal('0.********')) * order_tax_price
                    count += 1
                    realtime_inventory_list.append(inventory_detail)

        # print('????', realtime_inventory_list)
        return realtime_inventory_list, total

    def query_inventory_log_list(self, request, partner_id, user_id, is_frs=False,schema_name='STORE'):
        '''
        查询库存流水
        可根据门店id以及商品id
        '''
        branch_id = request.branch_id
        product_ids = request.product_ids
        code = request.code
        action = request.action
        limit = request.limit
        offset = request.offset
        category_ids = request.category_ids
        start_date = request.start_date
        end_date = request.end_date
        order_type = request.order_type
        account_type = request.account_type
        # sub_account_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        return_fields = request.return_fields  if is_frs else ''# 返回参数

        if code != '' and order_type == 'SALES':
            trace_ids = report_service.get_eticket_trace_id(partner_id=partner_id, user_id=user_id,
                                                            pos_ticket_number=code, branch_id=branch_id,
                                                            start_date=start_date, end_date=end_date)
            if not trace_ids:
                return [], 0, 0
            code = trace_ids['trace_ids']
            result = inventory_service.list_inventory_log(branch_id=branch_id, start_date=start_date, end_date=end_date,
                                                          product_ids=product_ids, order_type=order_type,
                                                          action=action, limit=limit, offset=offset,
                                                          partner_id=partner_id, user_id=user_id, code=code,
                                                          account_type=account_type)
        else:
            if category_ids and product_ids:
                new_product_ids = []
                category_list = [c_id for c_id in category_ids]
                relation_filters = dict(product_category=category_list)
                products_by_catogory = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                         return_fields="id", partner_id=partner_id,
                                                                         user_id=user_id)
                products_by_catogory = products_by_catogory.get('rows')
                for product_by_catogory in products_by_catogory:
                    for product_id in product_ids:
                        if str(product_id) == str(product_by_catogory['id']):
                            new_product_ids.append(int(product_id))
                product_ids = new_product_ids
            elif category_ids and (not product_ids):
                new_product_ids = []
                category_list = [c_id for c_id in category_ids]
                relation_filters = dict(product_category=category_list)
                product_ids_list = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                     return_fields="id", partner_id=partner_id,
                                                                     user_id=user_id).get('rows')
                if not product_ids_list:
                    return [], 0, 0
                for product_id in product_ids_list:
                    new_product_ids.append(int(product_id['id']))
                product_ids = new_product_ids

            result = inventory_service.list_inventory_log(branch_id, start_date, end_date, product_ids, order_type,
                                                          action, limit, offset, partner_id, user_id, code,
                                                          account_type)

        total = result.get('total')
        if total:
            total = int(total)
        amount_sum = result.get('amount_sum')
        if not amount_sum:
            amount_sum = 0
        result = result.get('logs')
        count = 0
        if not result:
            return [], 0, 0

        filters = {"status__in": ["ENABLED"]}

        filter_unit_list = metadata_service.get_unit_list(filters=filters, partner_id=partner_id, user_id=user_id,
                                                          return_fields='id,code,name')
        filter_unit_list = filter_unit_list.get('rows', [])
        key_filter_unit_dict = {}
        for filter_unit in filter_unit_list:
            key_filter_unit_dict[filter_unit['id']] = filter_unit

        filter_category_list = metadata_service.get_product_category_list(filters=filters, partner_id=partner_id,
                                                                          user_id=user_id, return_fields='id,code,name')
        filter_category_list = filter_category_list.get('rows', [])
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        # 拉取仓位主档
        # position_dict = metadata_service.get_position_dict(partner_id=partner_id, user_id=user_id)

        log_detail_list = []
        all_product_ids = []
        all_codes = []
        for detail in result:
            if detail.get('product_id'):
                all_product_ids.append(int(detail.get('product_id', 0)))
                log_detail_list.append(detail)
                if detail['code'] != 'SALES':
                    all_codes.append(detail.get('trace_id'))
            else:
                pass

        store_detail = metadata_service.get_entity_by_id(partner_id=partner_id, user_id=user_id,id=int(branch_id),schema_name=schema_name,
                                                  return_fields='id,code,name')
        store_detail=store_detail.get('fields', {})
        if product_ids:
            product_ids = list(set(product_ids))
            product_detail_list = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                                    partner_id=partner_id, user_id=user_id,
                                                                    return_fields='id,name,code,category,status,units,model_name')
            product_detail_list = product_detail_list.get('rows', [])
        else:
            all_product_ids = list(set(all_product_ids))
            product_detail_list = metadata_service.get_product_list(ids=all_product_ids, include_units=True,
                                                                    partner_id=partner_id, user_id=user_id,
                                                                    return_fields='id,name,code,category,status,units,model_name')
            product_detail_list = product_detail_list.get('rows', [])

        product_mapping = {}
        for product_detail_single in product_detail_list:
            product_mapping[str(product_detail_single['id'])] = product_detail_single

        product_unit_dict,_ = metadata_service.get_product_units_dict(product_ids=all_product_ids,
                                                                    partner_id=partner_id, user_id=user_id)

        cost_dict = {}
        # if "cost" in return_fields:
        #     cost_dict = self.get_price_by_store_id(store_id=int(branch_id), partner_id=partner_id)
        entities_currency = self.get_entities_currency(schema_name, [branch_id], partner_id, user_id)

        inventory_log_list = []
        flag = 0

        for detail in log_detail_list:
            flag += 1
            product_id = int(detail.get('product_id'))
            inventory_log = {}
            inventory_log['store_id'] = branch_id
            inventory_log['store_code'] = store_detail['code']
            inventory_log['store_name'] = store_detail['name']
            inventory_log['currency'] = entities_currency.get(branch_id, '')
            inventory_log['sub_account_id'] = int(detail.get('sub_account_id', 0))
            # inventory_log['sub_account_name'] = position_dict.get(detail.get('sub_account_id', 0), {}).get('name')
            # inventory_log['sub_account_code'] = position_dict.get(detail.get('sub_account_id', 0), {}).get('code')
            # 商品明细
            product_detail = product_mapping.get(str(product_id))
            if not product_detail:
                continue
            inventory_log['product_id'] = int(product_id)
            inventory_log['product_code'] = product_detail['code']
            inventory_log['product_name'] = product_detail['name']
            inventory_log['spec'] = product_detail.get('model_name') if product_detail.get('model_name') else None

            if product_detail.get('category'):
                category_id = int(product_detail['category'])
                inventory_log['category_id'] = int(category_id)
                inventory_log['category_name'] = key_filter_category_dict.get(str(category_id),{}).get('name')
                inventory_log['category_code'] = key_filter_category_dict.get(str(category_id),{}).get('code')
            if detail['code'] == 'SALES':
                inventory_log['order_code'] = detail.get('batch_no')
            else:
                inventory_log['order_code'] = detail.get('trace_id')
            inventory_log['order_type'] = detail['code']
            inventory_log['account_type'] = detail.get('account_type')
            inventory_log['action'] = detail['action']
            inventory_log['stock_id'] = detail['stock_id']
            inventory_log['status'] = detail['status']
            # 时间转换
            order_date = detail['business_time']
            order_date = translate_utc_time(order_date)
            order_date = Timestamp(seconds=int(order_date.timestamp()))
            inventory_log['order_time'] = order_date

            qty = detail.get('qty') if detail.get('qty') else 0
            if detail['action'] == 'WITHDRAW' or detail['action'] == 'TRANSFER_WITHDRAW':
                inventory_log['qty'] = -qty
            else:
                inventory_log['qty'] = qty
            if is_frs:
                key = str(product_id) + str(branch_id)
                inventory_log['retail_price'] = cost_dict.get(key, {}).get("retail_price", 0)
                inventory_log['tax_price'] = cost_dict.get(key, {}).get("tax_price", 0)
            # 核算单位
            if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('default'):
                unit_id = int(product_unit_dict.get(str(product_id)).get('default').get('id'))
                unit_detail = key_filter_unit_dict.get(str(unit_id))
                if unit_detail:
                    inventory_log['accounting_unit_id'] = unit_id
                    inventory_log['accounting_unit_code'] = unit_detail.get('code')
                    inventory_log['accounting_unit_name'] = unit_detail.get('name')
            # 订货单位  
            if product_unit_dict.get(str(product_id)) and product_unit_dict.get(str(product_id)).get('order'):
                demand_unit_id = int(product_unit_dict.get(str(product_id)).get('order').get('id'))
                tax_rate = product_unit_dict.get(str(product_id)).get('order').get('rate')
                tax_rate = round(tax_rate, 6) if tax_rate else 1

                demand_unit_detail = key_filter_unit_dict.get(str(demand_unit_id))
                if demand_unit_detail:
                    inventory_log['demand_unit_id'] = demand_unit_id
                    inventory_log['demand_unit_code'] = demand_unit_detail.get('code')
                    inventory_log['demand_unit_name'] = demand_unit_detail.get('name')
                    if detail['action'] == 'WITHDRAW' or detail['action'] == 'TRANSFER_WITHDRAW':
                        inventory_log['demand_qty'] = -qty / tax_rate
                    else:
                        inventory_log['demand_qty'] = qty / tax_rate
                    count += 1
            inventory_log['source_id'] = detail['batch_no']
            inventory_log_list.append(inventory_log)
        self.handle_inventory_log_amount(inventory_log_list)
        # print('*******库存流水报表**********', inventory_log_list)
        return inventory_log_list, total, amount_sum

    def query_mcu_realtime_inventory(self, request, partner_id, user_id):
        '''库存实时流水'''
        mcu_code = request.mcu
        product_ids_filter = request.product_ids

        # results = jde_service.get_mcu_realtime_inventory(mcu=mcu_code)
        results = []
        # results = [{'uom1':1111, 'litm':'1', 'pqoh':2}, {'uom1':222, 'litm':'2', 'pqoh':22}]
        products_code_list = []
        unit_code_list = []
        for result in results:
            products_code_list.append(result.get('litm'))
            unit_code_list.append(result.get('uom1'))
        # print(products_code_list)
        product_detail = metadata_service.get_product_list(filters={'code__in': products_code_list, },
                                                           # return_fields='name, code, id, status',
                                                           partner_id=partner_id,
                                                           user_id=user_id)
        products = product_detail.get('rows')
        # print(products)

        unit_detail = metadata_service.get_unit_list(filters={'code__in': unit_code_list, },
                                                     # return_fields='name, code, id',
                                                     partner_id=partner_id,
                                                     user_id=user_id)
        units = unit_detail.get('rows')
        # logging.info('units')
        # logging.info(units)
        # print(units)

        mcu_detail = metadata_service.get_distribution_center_list(search=mcu_code, search_fields='code',
                                                                   return_fields="name", partner_id=partner_id,
                                                                   user_id=user_id)
        mcus = mcu_detail.get('rows')

        mcu_detail_list = []
        count = 0
        for result in results:
            mcu_inventory_detail = {}
            # 仓库
            for mcu in mcus:
                mcu_id = mcu.get('id')
                mcu_name = mcu.get('name')

                mcu_inventory_detail['mcu_id'] = int(mcu_id)
                mcu_inventory_detail['mcu_code'] = mcu_code
                mcu_inventory_detail['mcu_name'] = mcu_name

            for product in products:
                if product.get('code') == result.get('litm'):
                    product_id = product.get('id')
                    product_name = product.get('name')
                    product_code = product.get('code')
                    mcu_inventory_detail['product_id'] = int(product_id)
                    mcu_inventory_detail['product_code'] = product_code
                    mcu_inventory_detail['product_name'] = product_name

            for unit in units:
                if unit.get('code') == result.get('uom1'):
                    unit_id = unit.get('id')
                    unit_name = unit.get('name')
                    unit_code = unit.get('code')
                    mcu_inventory_detail['unit_id'] = int(unit_id)
                    mcu_inventory_detail['unit_code'] = unit_code
                    mcu_inventory_detail['unit_name'] = unit_name

            # 现有数量  
            quantity = result.get('pqoh')
            mcu_inventory_detail['quantity'] = float(quantity)
            if product_ids_filter:
                for product_id_filter in product_ids_filter:
                    if int(product_id_filter) == int(mcu_inventory_detail['product_id']):
                        mcu_detail_list.append(mcu_detail)
                        count += 1
            else:
                mcu_detail_list.append(mcu_inventory_detail)
                count += 1

        # print(mcu_detail_list)
        return mcu_detail_list, count

    def get_daily_inventory(self, request, partner_id, user_id, is_frs=False):
        branch_id = request.branch_id
        product_ids = request.product_ids
        category_ids = request.category_ids
        limit = request.limit
        offset = request.offset
        start_date = request.start_date
        end_date = request.end_date
        code = request.code
        action = request.action
        # extra = request.extra
        if_pre = request.if_pre
        if_end = request.if_end
        exclude_empty = request.exclude_empty
        branch_type = request.branch_type
        position_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        return_fields = request.return_fields if is_frs else ''  # 返回参数
        extra = {}
        if if_pre:
            extra['pre_qty'] = if_pre
        if if_end:
            extra['end_qty'] = if_end
        if exclude_empty:
            extra['exclude_empty'] = exclude_empty

        if category_ids and product_ids:
            new_product_ids = []
            relation_filters = {}
            category_list = []
            for category_id in category_ids:
                category_list.append(str(category_id))
            relation_filters = dict(product_category=category_list)
            products_by_catogory = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                     return_fields="id", partner_id=partner_id,
                                                                     user_id=user_id)
            products_by_catogory = products_by_catogory.get('rows')
            for product_by_catogory in products_by_catogory:
                for product_id in product_ids:
                    if str(product_id) == str(product_by_catogory['id']):
                        new_product_ids.append(int(product_id))
            product_ids = new_product_ids

        elif category_ids and (not product_ids):
            new_product_ids = []
            relation_filters = {}
            category_list = []
            for category_id in category_ids:
                category_list.append(str(category_id))
            relation_filters = dict(product_category=category_list)
            product_ids_list = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                 return_fields="id", partner_id=partner_id,
                                                                 user_id=user_id).get('rows')
            if not product_ids_list:
                return [], 0
            for product_id in product_ids_list:
                # print(product_id, type(product_id))
                new_product_ids.append(int(product_id['id']))
            product_ids = new_product_ids
        inventory_snapshot_list = inventory_service.query_snap_list(branch_id=branch_id, start_date=start_date,
                                                                    end_date=end_date,
                                                                    product_ids=product_ids, limit=limit, offset=offset,
                                                                    partner_id=partner_id, user_id=user_id,
                                                                    code=code, action=action, extra=extra)
        total = inventory_snapshot_list.get('total')
        inventory_snapshot_list = inventory_snapshot_list.get('rows')

        if not inventory_snapshot_list:
            return [], 0

        log_detail_list = []
        all_product_ids = []
        for detail in inventory_snapshot_list:
            if detail.get('account'):
                all_product_ids.append(int(detail.get('account').get('product_id')))
                log_detail_list.append(detail)
            else:
                pass
        # 判断是门店还是仓库查询
        if branch_type in ("WAREHOUSE", "MACHINING_CENTER"):
            store_detail = metadata_service.get_distribution_center(int(branch_id), partner_id=partner_id,
                                                                    user_id=user_id)
        else:
            store_detail = metadata_service.get_store(int(branch_id), partner_id=partner_id, user_id=user_id)

        product_detail_list = metadata_service.get_product_list(ids=all_product_ids, include_units=True,
                                                                return_fields='id,name,code,category,status,units,model_name',
                                                                partner_id=partner_id, user_id=user_id).get('rows', [])

        # 批量获取unit和category详情，并整理成以id为key的dict
        category_ids = []
        unit_ids = []
        for filter_product_detail in product_detail_list:
            if filter_product_detail.get('category'):
                category_id = int(filter_product_detail['category'])
                category_ids.append(category_id)

            if (filter_product_detail.get('units')):
                for product_units_detail in filter_product_detail['units']:
                    unit_id = int(product_units_detail['id'])
                    unit_ids.append(unit_id)
        filter_unit_list = metadata_service.get_unit_list(ids=unit_ids, partner_id=partner_id,
                                                          user_id=user_id, return_fields='id,code,name')
        filter_unit_list = filter_unit_list.get('rows', [])
        key_filter_unit_dict = {}
        for filter_unit in filter_unit_list:
            key_filter_unit_dict[filter_unit['id']] = filter_unit

        filter_category_list = metadata_service.get_product_category_list(ids=category_ids, partner_id=partner_id,
                                                                          user_id=user_id,
                                                                          return_fields='id,code,name')
        filter_category_list = filter_category_list.get('rows')
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        # 拉取仓位主档
        # position_dict = metadata_service.get_position_dict(partner_id=partner_id, user_id=user_id)
        cost_dict = {}
        # if "cost" in return_fields:
        #     if "cost" in return_fields:
        #         cost_dict = self.get_price_by_store_id(store_id=int(branch_id), partner_id=partner_id)
        entities_currency = self.get_entities_currency(branch_type, [branch_id], partner_id, user_id)
        # 拼接数据
        daily_inventory_list = []
        for detail in inventory_snapshot_list:

            # 没有期初也没期末库存
            # if (not detail['amount'].get('qty')) and (not detail['previous'].get('amount')):
            #     continue
            product_id = detail.get('account', {}).get('product_id')
            daily_inventory = {}
            daily_inventory['start_time'] = detail['start']
            daily_inventory['end_time'] = detail['end']
            # 门店明细
            daily_inventory['store_id'] = branch_id
            daily_inventory['store_code'] = store_detail['code']
            daily_inventory['store_name'] = store_detail['name']
            daily_inventory['currency'] = entities_currency.get(branch_id, '')
            # 商品明细
            for product_detail in product_detail_list:
                if product_detail['id'] == product_id:
                    daily_inventory['product_id'] = int(product_id)
                    daily_inventory['product_code'] = product_detail['code']
                    daily_inventory['product_name'] = product_detail['name']
                    daily_inventory['spec'] = product_detail.get('model_name') if product_detail.get(
                        'model_name') else None
                    if product_detail.get('units'):
                        for unit in product_detail["units"]:
                            if unit.get("default") and unit["default"] == True:
                                accounting_unit_id = unit["id"]
                                unit_detail = key_filter_unit_dict.get(str(accounting_unit_id), {})
                                daily_inventory['accounting_unit_name'] = unit_detail.get('name')
                                daily_inventory['accounting_unit_id'] = int(unit_detail.get('id', 0))
                                daily_inventory['accounting_unit_code'] = unit_detail.get('code')
            # 可用期初库存
            daily_inventory['pre_qty'] = 0
            if detail.get('previous'):
                if detail['previous'].get('amount'):
                    daily_inventory['pre_qty'] = detail['previous'].get('amount').get('qty', 0)
            # 可用期末库存
            amount = detail['amount'] if 'amount' in detail else None
            if amount:
                daily_inventory['qty'] = amount.get('qty', 0)
                daily_inventory['price'] = amount.get('price', 0)
                daily_inventory['amount'] = amount.get('amount', 0)
            else:
                daily_inventory['qty'] = 0
                daily_inventory['price'] = 0
                daily_inventory['amount'] = 0

            # sku库存切片业务详情
            if detail.get('stats'):
                # print(detail.get('stats'))
                daily_inventory['rec_deposit'] = 0
                daily_inventory['stoc_deposit'] = 0
                daily_inventory['stoc_withdraw'] = 0
                daily_inventory['adj_withdraw'] = 0
                daily_inventory['ret_withdraw'] = 0
                daily_inventory['rec_withdraw'] = 0
                daily_inventory['ret_deposit'] = 0
                daily_inventory['trans_withdraw'] = 0
                daily_inventory['ret_transfer'] = 0
                daily_inventory['trans_transfer'] = 0
                daily_inventory['trans_transfer_release'] = 0
                daily_inventory['trans_delivery'] = 0
                daily_inventory['trans_delivery_release'] = 0
                daily_inventory['trans_purchase'] = 0
                daily_inventory['trans_purchase_release'] = 0
                daily_inventory['trans_return_release'] = 0

                for stats_detail in detail['stats']:
                    # print(stats_detail['code'], stats_detail.get('amount').get('qty'), stats_detail['action'] )
                    if stats_detail.get('amount').get('qty') and stats_detail.get('account_type') == "SKU":
                        # 发货出库
                        if stats_detail['code'] == 'ORDER_DELIVERY' and 'WITHDRAW' in stats_detail['action']:
                            daily_inventory['rec_withdraw'] += stats_detail.get('amount').get('qty')

                        # 配送收货入库
                        if (stats_detail['code'] == 'ORDER_RECEIVING' or stats_detail[
                            'code'] == 'ADJUST_ORDER_RECEIVING') and \
                                (stats_detail['action'] == 'DEPOSIT' or stats_detail['action'] == 'TRANSFER_DEPOSIT'):
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')

                        # 配送收货出库
                        if (stats_detail['code'] == 'ORDER_RECEIVING' or stats_detail[
                            'code'] == 'ADJUST_ORDER_RECEIVING') and \
                                (stats_detail['action'] == 'WITHDRAW' or stats_detail['action'] == 'TRANSFER_WITHDRAW'):
                            if branch_type in ("WAREHOUSE", "MACHINING_CENTER"):  # 门店超收产生的退货单算入收货差异出库中
                                daily_inventory['rec_diff_withdraw'] = stats_detail.get('amount').get('qty')
                            else:
                                daily_inventory['rec_withdraw'] += stats_detail.get('amount').get('qty')

                        # 直送收货入库
                        if stats_detail['code'] == 'DIR_RECEIVING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')
                        if stats_detail['code'] == 'ORDER_DIR_RECEIVING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')

                        # 对账调整入库 —— 计入收货入库
                        if stats_detail['code'] == 'CHECKING_ADJUST_REC' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')

                        # 对账调整出库 —— 计入退货出库
                        if stats_detail['code'] == 'CHECKING_ADJUST_RET' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 采购收货入库 —— 仓库采购收货
                        if stats_detail['code'] == 'WAREHOUSE_PURCHASE' and \
                                (stats_detail['action'] == 'TRANSFER_DEPOSIT' or stats_detail['action'] == 'DEPOSIT'):
                            daily_inventory['purchase_deposit'] = stats_detail.get('amount').get('qty')

                        # 收货差异出库
                        if stats_detail['code'] == 'RECEIVING_DIFF' and 'WITHDRAW' in stats_detail['action']:
                            daily_inventory['rec_diff_withdraw'] = stats_detail.get('amount').get('qty')

                        # 收货差异入库
                        if stats_detail['code'] == 'RECEIVING_DIFF' and 'DEPOSIT' in stats_detail['action']:
                            # if branch_type in ("WAREHOUSE", "MACHINING_CENTER"): # 门店收货差异产生的退货单算入退货入库中
                            #     daily_inventory['ret_deposit'] += stats_detail.get('amount').get('qty')
                            # else:
                            daily_inventory['rec_diff_deposit'] = stats_detail.get('amount').get('qty')

                        # 退货入库
                        status_returned_in = ["DEPOSIT", "TRANSFER_DEPOSIT"]
                        if stats_detail['code'] in ('RETURN_IN', 'ADJUST_ORDER_RETURN') and stats_detail[
                            'action'] in status_returned_in:
                            daily_inventory['ret_deposit'] += stats_detail.get('amount').get('qty')

                        # 退货出库
                        status_returned_out = ["WITHDRAW", "TRANSFER_WITHDRAW"]
                        if stats_detail['code'] in ('ORDER_RETURN', 'ADJUST_ORDER_RETURN') and stats_detail[
                            'action'] in status_returned_out:
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 采购退货
                        if stats_detail['code'] == 'PURCHASE_RETURN':
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 调拨入库
                        if stats_detail['code'] in ('TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                                and stats_detail['action'] == 'TRANSFER_DEPOSIT':
                            daily_inventory['trans_deposit'] = stats_detail.get('amount').get('qty')

                        # 调拨出库
                        if stats_detail['code'] in ('TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                                and stats_detail['action'] == 'TRANSFER_WITHDRAW':
                            daily_inventory['trans_withdraw'] = stats_detail.get('amount').get('qty')

                        # 盘点入库
                        if stats_detail['code'][0:9] == 'STOCKTAKE' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['stoc_deposit'] += stats_detail.get('amount').get('qty')

                        # 盘点出库
                        if stats_detail['code'][0:9] == 'STOCKTAKE' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['stoc_withdraw'] += stats_detail.get('amount').get('qty')

                        # 报废入库
                        if stats_detail['code'] == 'ADJUST' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['adj_deposit'] = stats_detail.get('amount').get('qty')

                        # 报废出库
                        if stats_detail['code'] == 'ADJUST' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['adj_withdraw'] += stats_detail.get('amount').get('qty')
                        if stats_detail['code'] == 'DIFF_ADJUST' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['adj_withdraw'] += stats_detail.get('amount').get('qty')

                        # 销售入库
                        if stats_detail['code'] == 'SALES' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['sales_deposit'] = stats_detail.get('amount').get('qty')

                        # 销售出库
                        if stats_detail['code'] == 'SALES' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['sales_withdraw'] = stats_detail.get('amount').get('qty')

                        # 自采入库
                        if stats_detail['code'] == 'SELF_PICKING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['spick_deposit'] = stats_detail.get('amount').get('qty')

                        # 冻结
                        if stats_detail['code'] == 'SUBMIT_ORDER' and stats_detail['action'] == 'TRANSFER_WITHDRAW':
                            daily_inventory['freeze_qty'] = stats_detail.get('amount').get('qty')

                        # 物料转换入库
                        if stats_detail['code'] == 'MATERIAL_CONVERT' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['material_trans_deposit'] = stats_detail.get('amount').get('qty')

                        # 物料转换出库
                        if stats_detail['code'] == 'MATERIAL_CONVERT' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['material_trans_withdraw'] = stats_detail.get('amount').get('qty')

                        # 加工入库
                        if stats_detail['code'] == 'PROCESSING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['processing_deposit'] = stats_detail.get('amount').get('qty')

                        # 加工出库
                        if stats_detail['code'] == 'PROCESSING' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['processing_withdraw'] = stats_detail.get('amount').get('qty')

                        # 包装入库
                        if stats_detail['code'] == 'PACKING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['packing_deposit'] = stats_detail.get('amount').get('qty')

                        # 包装出库
                        if stats_detail['code'] == 'PACKING' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['packing_withdraw'] = stats_detail.get('amount').get('qty')

                    elif stats_detail.get('amount').get('qty') and stats_detail.get('account_type') == "BROKER":
                        status_deposit = ["DEPOSIT", "TRANSFER_DEPOSIT"]
                        status_withdraw = ["WITHDRAW", "TRANSFER_WITHDRAW"]
                        # 退货在途
                        if stats_detail['code'] in ('ORDER_RETURN', 'ADJUST_ORDER_RETURN') and stats_detail[
                            'action'] in status_deposit:
                            daily_inventory['ret_transfer'] += stats_detail.get('amount').get('qty')

                        # 退货在途释放
                        if stats_detail['code'] in ('RETURN_IN', 'ADJUST_ORDER_RETURN') and stats_detail[
                            'action'] in status_withdraw:
                            daily_inventory['trans_return_release'] += stats_detail.get('amount').get('qty')

                        # 调拨在途
                        if stats_detail['code'] in ('EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                                and stats_detail['action'] == 'TRANSFER_DEPOSIT':
                            daily_inventory['trans_transfer'] += stats_detail.get('amount').get('qty')

                        # 调拨在途释放
                        if stats_detail['code'] in ('EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                                and stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_transfer_release'] += stats_detail.get('amount').get('qty')

                        # 发货在途
                        if stats_detail['code'] in ('ORDER_DELIVERY') and stats_detail['action'] in status_deposit:
                            daily_inventory['trans_delivery'] += stats_detail.get('amount').get('qty')

                        # 发货在途释放
                        if stats_detail['code'] in ('ORDER_RECEIVING', 'RECEIVING_DIFF', 'ADJUST_ORDER_RECEIVING') and \
                                stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_delivery_release'] += stats_detail.get('amount').get('qty')

                        # 采购在途
                        if stats_detail['code'] in ('PURCHASE_DELIVERY') and stats_detail['action'] in status_deposit:
                            daily_inventory['trans_purchase'] += stats_detail.get('amount').get('qty')

                        # 采购在途释放
                        if stats_detail['code'] in ('WAREHOUSE_PURCHASE') and stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_purchase_release'] += stats_detail.get('amount').get('qty')

            # 在途聚合取值
            status_account = json.loads(detail.get('status_account')) if detail.get('status_account') else None
            if status_account:
                daily_inventory['trans_begin'] = 0  # 在途期初库存
                daily_inventory['trans_end'] = 0  # 在途期末库存
                for transfer_data in status_account:
                    # 在途期初库存
                    if transfer_data.get('account_type') == 'BROKER':
                        trans_begin = transfer_data.get('pre_qty', 0)
                        daily_inventory['trans_begin'] += trans_begin
                    # 在途期末库存
                    if transfer_data.get('account_type') == 'BROKER':
                        trans_end = transfer_data.get('qty', 0)
                        daily_inventory['trans_end'] += trans_end

            # 子账户库存切片业务详情
            if detail.get('account', {}).get('sub_account'):
                position_id = detail['account']['sub_account'].get('id', 0)
                daily_inventory['position_id'] = int(position_id)
                # daily_inventory['position_name'] = position_dict.get(str(position_id), {}).get('name')
                # daily_inventory['position_code'] = position_dict.get(str(position_id), {}).get('code')
            if is_frs:
                key = str(product_id) + str(branch_id)
                daily_inventory['retail_price'] = cost_dict.get(key, {}).get("retail_price", 0)
                daily_inventory['tax_price'] = cost_dict.get(key, {}).get("tax_price", 0)
            daily_inventory_list.append(daily_inventory)
        # print('*******每日库存报表**********', daily_inventory_list)
        return daily_inventory_list, total

    def get_inventory_log_in_total(self, request, partner_id, user_id):
        branch_id = request.branch_id
        product_ids = request.product_ids
        limit = request.limit
        offset = request.offset
        start_time = request.start_time
        end_time = request.end_time

        result = inventory_service.list_group_event_log(branch_id, start_time, end_time, product_ids,
                                                        limit, offset, partner_id, user_id)

        total = result.get('total')
        if total:
            total = int(total)
        result = result.get('group_logs')
        count = 0
        if not result:
            return [], 0

        log_detail_list = []
        all_product_ids = []
        for detail in result:
            if detail.get('product_id'):
                all_product_ids.append(int(detail.get('product_id')))
                log_detail_list.append(detail)
            else:
                pass

        # store_detail = metadata_service.get_store(int(branch_id), partner_id=partner_id, user_id=user_id)
        # print(product_ids)
        if product_ids:
            product_ids = list(set(product_ids))
            product_detail_list = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                                    partner_id=partner_id, user_id=user_id)
            product_detail_list = product_detail_list.get('rows')
            # print('product_detail_list:', product_detail_list)
        else:
            all_product_ids = list(set(all_product_ids))
            product_detail_list = metadata_service.get_product_list(ids=all_product_ids, include_units=True,
                                                                    partner_id=partner_id, user_id=user_id)
            # print(all_product_ids)
            product_detail_list = product_detail_list.get('rows')

        product_mapping = {}
        for product_detail_single in product_detail_list:
            product_mapping[str(product_detail_single['id'])] = product_detail_single

        inventory_log_list = []
        for detail in log_detail_list:
            product_id = detail.get('product_id')
            inventory_log = {}

            # 商品明细
            product_detail = product_mapping.get(str(product_id))
            if not product_detail:
                continue
            inventory_log['product_id'] = int(product_id)
            inventory_log['product_code'] = product_detail['code']
            inventory_log['product_name'] = product_detail['name']

            inventory_log['code'] = detail.get('code')
            inventory_log['action'] = detail.get('action')
            inventory_log['qty'] = detail.get('qty')
            inventory_log['count'] = int(detail.get('count'))
            inventory_log_list.append(inventory_log)

        # print(inventory_log_list)
        return inventory_log_list, int(total)

    def get_snapshot_for_sales(self, code, biz_date, store_id, partner_id, user_id):
        # 假设今天是7.26
        # biz_date='2019-07-26 00:00:00'
        demand_date = datetime.strptime(biz_date, '%Y-%m-%d %H:%M:%S')
        biz_date = demand_date + timedelta(hours=4)
        # 如果是明日的订货单，订货日期为7.27 
        # 无【昨日销量】，记为【前日销量】
        # biz_date = '2019-07-25 00:00:00'
        if demand_date > datetime.today():
            biz_date = demand_date + timedelta(days=-1) + timedelta(hours=4)

        end_date = biz_date
        start_date = end_date + timedelta(days=-7)
        yesterday = end_date + timedelta(days=-1)

        biz_date_g = Timestamp(seconds=int(biz_date.timestamp()))
        start_date_g = Timestamp(seconds=int(start_date.timestamp()))
        yesterday_g = Timestamp(seconds=int(yesterday.timestamp()))

        yesterday_sales = inventory_service.query_snapshot_for_sales(branch_id=store_id, \
                                                                     start_time=yesterday_g, end_time=biz_date_g,
                                                                     code=code, \
                                                                     partner_id=partner_id, user_id=user_id).get(
            'snapshot_stat')

        week_sales = inventory_service.query_snapshot_for_sales(branch_id=store_id, \
                                                                start_time=start_date_g, end_time=biz_date_g, code=code, \
                                                                partner_id=partner_id, user_id=user_id).get(
            'snapshot_stat')

        # print(yesterday_sales)
        # 前日销量dict
        snapshot_sales_list = []
        day_product_dict = {}
        if yesterday_sales:
            for detail in yesterday_sales:
                qty = 0
                if detail['action'] == 'WITHDRAW':
                    qty = detail['qty'] if detail.get('qty') else 0
                elif detail['action'] == 'DEPOSIT':
                    qty = -detail['qty'] if detail.get('qty') else 0
                day_product_dict[int(detail['product_id'])] = qty

        # 周销量dict
        product_dict = {}
        if week_sales:
            # print('**************', week_sales)
            for detail in week_sales:
                qty = 0
                if detail['action'] == 'WITHDRAW':
                    qty = detail['qty'] if detail.get('qty') else 0
                elif detail['action'] == 'DEPOSIT':
                    qty = -detail['qty'] if detail.get('qty') else 0

                product_detail = {
                    'store_id': int(detail['branch_id']),
                    'product_id': int(detail['product_id']),
                    'code': detail['code'],
                    'week_qty': qty
                }
                if product_dict.get(int(detail['product_id'])):
                    product_dict.get(int(detail['product_id'])).append(product_detail)
                else:
                    product_dict[int(detail['product_id'])] = [product_detail]

        # print('*************new**********', day_product_dict, product_dict)

        # select_sql = """
        #         SELECT c.branch_id, c.product_id, a.`code`, a.action, a.qty
        #         FROM inventory_snapshot_stat a 
        #         JOIN inventory_snapshot b, inventory_sku c 
        #         WHERE c.branch_id={} AND b.`end`=\'{}\' AND b.stock_id=c.id AND a.shotsnap_id=b.id AND a.`code`=\'{}\'
        #         """.format(store_id, biz_date, code)

        # week_select_sql = """
        #         SELECT c.branch_id, c.product_id, a.`code`, a.action, a.qty
        #         FROM inventory_snapshot_stat a 
        #         JOIN inventory_snapshot b, inventory_sku c 
        #         WHERE c.branch_id={} 
        #         AND b.`start`>=\'{}\' AND b.`end`<=\'{}\' 
        #         AND b.stock_id=c.id AND a.shotsnap_id=b.id AND a.`code`=\'{}\'
        #         """.format(store_id, start_date, end_date, code)
        # print(select_sql, week_select_sql)
        # with DummyTransaction(auto_commit=False) as trans:
        #     total_row = trans.scope_session.execute(select_sql).fetchall()
        #     week_total_row = trans.scope_session.execute(week_select_sql).fetchall()

        # # 前日销量dict
        # snapshot_sales_list = []
        # day_product_dict = {}
        # for detail in total_row:
        #     qty = 0
        #     if detail[3] == 'WITHDRAW':
        #         qty = detail[4]
        #     elif detail[3] == 'DEPOSIT':
        #         qty = -detail[4]
        #     day_product_dict[detail[1]] = qty

        # # 周销量dict
        # product_dict = {}
        # for detail in week_total_row:
        #     qty = 0
        #     if detail[3] == 'WITHDRAW':
        #         qty = detail[4]
        #     elif detail[3] == 'DEPOSIT':
        #         qty = -detail[4]

        #     product_detail = {
        #         'store_id': detail[0],
        #         'product_id': detail[1],
        #         'code': detail[2],
        #         'week_qty': qty
        #     }
        #     if product_dict.get(detail[1]):
        #         product_dict.get(detail[1]).append(product_detail)
        #     else:
        #         product_dict[detail[1]] = [product_detail]

        # print('*************old**********', day_product_dict, product_dict)

        snapshot_sales_list = []
        for key, value in product_dict.items():
            # 计算周平均销量
            total_week_qty = 0
            for i in value:
                total_week_qty += i['week_qty']
            week_average_sales = total_week_qty / 7

            # 拼接
            product_detail = {
                'week_average_qty': week_average_sales,
                'store_id': value[0]['store_id'],
                'product_id': value[0]['product_id'],
                'code': value[0]['code'],
                'yesterday_qty': day_product_dict.get(key)
            }
            snapshot_sales_list.append(product_detail)

        return snapshot_sales_list

    # -------------------

    def query_accounting(self, request, partner_id, user_id):
        batch_id = request.batch_id
        batch_no = request.batch_no
        code = request.code
        action = request.action
        detail = request.detail

        result = inventory_service.query_accounting(batch_id, batch_no, code, action, detail, partner_id, user_id)
        print(result)

    def bacth_query_by_id(self, request, partner_id, user_id):
        id = request.id
        test = inventory_service.get_batch_inventory_by_id_batch_no_code(id, partner_id, user_id)
        return test

    # 弃用！！！！
    def query_inventory_log(self, request, partner_id, user_id):
        '''门店库存流水, 已弃用'''
        branch_id = request.branch_id
        product_ids = request.product_ids
        code = request.code
        action = request.action
        limit = request.limit
        offset = request.offset
        category_ids = request.category_ids
        start_date = request.start_date
        end_date = request.end_date

        if category_ids and product_ids:
            new_product_ids = []
            relation_filters = {}
            category_list = []
            for category_id in category_ids:
                category_list.append(str(category_id))

            relation_filters = dict(product_category=category_list)
            # relation_filters={'product_category':category_list}
            products_by_catogory = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                     return_fields="id")
            products_by_catogory = products_by_catogory.get('rows')
            # print(products_by_catogory)
            for product_by_catogory in products_by_catogory:
                for product_id in product_ids:
                    # print(product_id, product_by_catogory['id'])
                    if str(product_id) == str(product_by_catogory['id']):
                        new_product_ids.append(int(product_id))
            product_ids = new_product_ids

        elif category_ids and (not product_ids):
            new_product_ids = []
            relation_filters = {}
            category_list = []
            for category_id in category_ids:
                category_list.append(str(category_id))
            relation_filters = dict(product_category=category_list)
            product_ids_list = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                 return_fields="id", partner_id=partner_id,
                                                                 user_id=user_id).get('rows')
            if not product_ids_list:
                return [], 0
            for product_id in product_ids_list:
                # print(product_id, type(product_id))
                new_product_ids.append(int(product_id['id']))
            product_ids = new_product_ids

        result = inventory_service.query_log_list(branch_id, start_date, end_date, product_ids, code, action,
                                                  limit, offset, partner_id, user_id)
        # print('**********', json.dumps(result))
        total = result.get('total')
        if total:
            total = int(total)
        result = result.get('data')
        count = 0
        if not result:
            return [], 0

        category_ids = []
        unit_ids = []
        for filter_product_detail in result:
            if filter_product_detail.get('category'):
                category_id = int(filter_product_detail['category'])
                category_ids.append(category_id)

            if (filter_product_detail.get('units')):
                for product_units_detail in filter_product_detail['units']:
                    if not product_units_detail.get('order'):
                        pass
                    else:
                        if product_units_detail['order'] == True:
                            unit_id = int(product_units_detail['id'])
                            unit_ids.append(unit_id)

        filter_unit_list = metadata_service.get_unit_list(ids=unit_ids, partner_id=partner_id, user_id=user_id)
        filter_unit_list = filter_unit_list.get('rows', [])
        key_filter_unit_dict = {}
        for filter_unit in filter_unit_list:
            key_filter_unit_dict[filter_unit['id']] = filter_unit

        filter_category_list = metadata_service.get_product_category_list(ids=category_ids, partner_id=partner_id,
                                                                          user_id=user_id)
        filter_category_list = filter_category_list.get('rows')
        key_filter_category_dict = {}
        for filter_category in filter_category_list:
            key_filter_category_dict[filter_category['id']] = filter_category

        log_detail_list = []
        all_product_ids = []
        for detail in result:
            if detail.get('account'):
                all_product_ids.append(int(detail.get('account').get('product_id')))
                log_detail_list.append(detail)
            else:
                pass

        store_detail = metadata_service.get_store(int(branch_id), partner_id=partner_id, user_id=user_id)
        # print(product_ids)
        if product_ids:
            product_ids = list(set(product_ids))
            product_detail_list = metadata_service.get_product_list(ids=product_ids, include_units=True,
                                                                    partner_id=partner_id, user_id=user_id)
            product_detail_list = product_detail_list.get('rows')
            # print('product_detail_list:', product_detail_list)
        else:
            all_product_ids = list(set(all_product_ids))
            product_detail_list = metadata_service.get_product_list(ids=all_product_ids, include_units=True,
                                                                    partner_id=partner_id, user_id=user_id)
            # print(all_product_ids)
            product_detail_list = product_detail_list.get('rows')
        inventory_log_list = []
        flag = 0
        for detail in log_detail_list:
            flag += 1
            product_id = detail.get('account').get('product_id')
            inventory_log = {}
            inventory_log['store_id'] = branch_id
            inventory_log['store_code'] = store_detail['code']
            inventory_log['store_name'] = store_detail['name']
            for product_detail in product_detail_list:
                if product_detail['id'] == product_id:
                    inventory_log['product_id'] = int(product_id)
                    inventory_log['product_code'] = product_detail['code']
                    inventory_log['product_name'] = product_detail['name']
                    inventory_log['spec'] = product_detail.get('model_name') if product_detail.get(
                        'model_name') else None

                    if product_detail.get('category'):
                        category_id = int(product_detail['category'])
                        inventory_log['category_id'] = int(category_id)
                        inventory_log['category_name'] = key_filter_category_dict.get(str(category_id),{}).get('name')
                        inventory_log['category_code'] = key_filter_category_dict.get(str(category_id),{}).get('code')

                    inventory_log['order_code'] = detail['data']['trace_id']
                    inventory_log['order_type'] = detail['data']['code']
                    order_date = detail['data']['business_time']
                    order_date = translate_utc_time(order_date)
                    order_date = Timestamp(seconds=int(order_date.timestamp()))

                    inventory_log['order_time'] = order_date
                    inventory_log['action'] = detail['data']['action']
                    inventory_log['stock_id'] = detail['data']['stock_id']
                    inventory_log['status'] = detail['data']['status']

                    if (product_detail.get('units')):
                        len_flag = 0
                        for product_units_detail in product_detail['units']:
                            len_flag = len_flag + 1
                            qty = detail['data'].get('qty') if detail['data'].get('qty') else 0
                            inventory_log['qty'] = qty
                            if product_units_detail.get('default') == True:
                                unit_id = int(product_units_detail['id'])
                                unit_detail = key_filter_unit_dict.get(str(unit_id))

                                inventory_log['accounting_unit_id'] = unit_id
                                inventory_log['accounting_unit_code'] = unit_detail['code']
                                inventory_log['accounting_unit_name'] = unit_detail['name']

                                if product_units_detail.get('order') == True:
                                    demand_unit_id = int(product_units_detail['id'])
                                    tax_rate = product_units_detail['rate']
                                    demand_unit_detail = key_filter_unit_dict.get(str(demand_unit_id))
                                    inventory_log['demand_unit_id'] = demand_unit_id
                                    inventory_log['demand_unit_code'] = demand_unit_detail['code']
                                    inventory_log['demand_unit_name'] = demand_unit_detail['name']
                                    inventory_log['demand_qty'] = qty * tax_rate
                                    count += 1
                                    inventory_log_list.append(inventory_log)
                                    break
                                else:
                                    if len_flag == len(product_detail['units']):
                                        count += 1
                                        inventory_log_list.append(inventory_log)
                                        break

                            else:
                                if product_units_detail.get('order') == True:
                                    demand_unit_id = int(product_units_detail['id'])
                                    tax_rate = product_units_detail['rate']
                                    demand_unit_detail = key_filter_unit_dict.get(str(demand_unit_id))
                                    inventory_log['demand_unit_id'] = demand_unit_id
                                    inventory_log['demand_unit_code'] = demand_unit_detail['code']
                                    inventory_log['demand_unit_name'] = demand_unit_detail['name']
                                    inventory_log['demand_qty'] = qty * tax_rate
                                    count += 1
                                    inventory_log_list.append(inventory_log)
                                    break
                                else:
                                    if len_flag == len(product_detail['units']):
                                        count += 1
                                        inventory_log_list.append(inventory_log)
                                        break

        # print('*******库存流水报表**********', inventory_log_list)
        # print(count)
        return inventory_log_list, total

    def get_realtime_inventory_by_accounts(self, request, partner_id, user_id):
        """按账户和指定商品查询实时库存（目前只兼容仓库/加工中心查询发货仓位库存），包装给前端用
            查询库存前校验当前账户是否开启多仓位，
            若开启多仓位，查询该组织对应业务操作(发货/收货等)的子账户，
            返回该组织对应子账户的库存
            check_type: 拉取组织仓位配置校验的业务类型: 例如:
                    "发货" - deliverGoods、
                    "采购收货" - purchaseReceive等
        """
        result = {}
        accounts = request.accounts
        check_type = request.check_type
        if not accounts:
            return result
        query_accounts = []  # 批量查询库存请求参数
        opened_position_warehouse_ids = []  # 总部配置了多仓位的仓库列表
        opened_position_machining_ids = []  # 总部配置了多仓位的加工中心列表
        warehouse_ids = []
        machining_ids = []
        # 拉取总部业务配置
        business_config = metadata_service.get_business_config(partner_id=partner_id, user_id=user_id)
        if not business_config:
            # logging.info("未拉取到总部业务配置，不需要校验多仓位")
            warehouse = []
            machining = []
            warehouse_all = False
            machining_all = False
        else:
            warehouse = business_config.get('warehouse', [])
            machining = business_config.get('machining', [])
            warehouse_all = business_config.get('warehouse_all')
            machining_all = business_config.get('machining_all')

        for account in accounts:
            branch_id = int(account.branch_id)
            distribution_type = account.distribution_type
            # 总部配送
            if distribution_type == 'NMD':
                warehouse_ids.append(branch_id)
                # 校验仓库是否开启了多仓位配置，开启多仓位配置的组织存起来再拉取仓位配置
                if (str(branch_id) in warehouse or warehouse_all is True) and branch_id not in \
                        opened_position_warehouse_ids:
                    opened_position_warehouse_ids.append(str(branch_id))
            # 加工配送
            if distribution_type == 'PAD':
                machining_ids.append(branch_id)
                # 校验加工中心是否开启了多仓位配置，开启多仓位配置的组织存起来再拉取仓位配置
                if (str(branch_id) in machining or machining_all is True) and branch_id not in \
                        opened_position_machining_ids:
                    opened_position_machining_ids.append(str(branch_id))
            row = dict(
                branch_id=branch_id,
                product_id=int(account.product_id)
            )
            query_accounts.append(row)
        branch_map = dict()
        if warehouse_ids:
            warehouse_map = self.get_store_or_warehouse_map(branch_ids=warehouse_ids, branch_type="WAREHOUSE",
                                                            partner_id=partner_id, user_id=user_id)
            branch_map.update(warehouse_map)
        if machining_ids:
            machining_map = self.get_store_or_warehouse_map(branch_ids=machining_ids, branch_type="MACHINING_CENTER",
                                                            partner_id=partner_id, user_id=user_id)
            branch_map.update(machining_map)

        # logging.info("[debug]opened_position_warehouse_ids: {}".format(opened_position_warehouse_ids))
        # logging.info("[debug]opened_position_machining_ids: {}".format(opened_position_machining_ids))
        # 批量查询开启了多仓位的仓库仓位配置
        position_relation_config_list = []
        if len(opened_position_warehouse_ids) > 0:
            config_list = metadata_service.get_position_relation_config(
                branch_type="WAREHOUSE",
                relation='all',
                branch_ids=list(opened_position_warehouse_ids),
                partner_id=partner_id,
                user_id=user_id)
            position_relation_config_list += config_list
        if len(opened_position_machining_ids) > 0:
            config_list = metadata_service.get_position_relation_config(
                branch_type="MACHINING_CENTER",
                relation='all',
                branch_ids=list(opened_position_machining_ids),
                partner_id=partner_id,
                user_id=user_id)
            position_relation_config_list += config_list
        # 如果没有仓位配置直接返回对应仓库的库存
        position_ids = []
        if position_relation_config_list:
            for config in position_relation_config_list:
                for branch in query_accounts:
                    position_id = 0
                    if check_type == "deliverGoods":
                        position_id = convert_to_int(config.get('deliverGoods'))
                    if position_id:
                        relation = config.get('relation') if config.get('relation') else {}
                        branches = relation.get('warehouse') if relation.get('warehouse') else []
                        branches += relation.get('machining') if relation.get('machining') else []
                        if len(branches) > 0:
                            branch_id = convert_to_int(branches[0])
                            if branch_id == branch.get('branch_id'):
                                branch["sub_account"] = {"id": position_id}
                        position_ids.append(position_id)
        # 拉取仓位主档
        position_map = metadata_service.get_position_dict(ids=position_ids, return_fields="id,name,code",
                                                          partner_id=partner_id, user_id=user_id)
        # logging.info("[debug]query_accounts: {}".format(query_accounts))
        # 批量查询实时库存接口
        total, inventory_accounts = inventory_service.query_account_inventory_list(accounts=query_accounts,
                                                                                   partner_id=partner_id,
                                                                                   user_id=user_id)
        if not inventory_accounts:
            return result
        accounts = []
        for row in inventory_accounts:
            account = {}  # 保存库存账户信息
            sub_accounts = []
            detail = row.get('detail')
            if row.get('account') and isinstance(row.get('account'), dict):
                account['branch_id'] = convert_to_int(row.get('account').get('branch_id'))
                account['product_id'] = convert_to_int(row.get('account').get('product_id'))
                account['branch_name'] = branch_map.get(str(account['branch_id'])).get('name') if branch_map.get(
                    str(account['branch_id'])) else ""
            if row.get('amount') and isinstance(row.get('amount'), dict):
                account['qty'] = row['amount'].get('qty')
            if detail and isinstance(detail, dict):
                childes = detail.get('child', [])
                for child in childes:
                    sub_account = {}
                    if child.get('account') and isinstance(child.get('account'), dict):
                        sub_account['branch_id'] = convert_to_int(child.get('account').get('sub_account', {}).get('id'))
                        sub_account['product_id'] = convert_to_int(child.get('account').get('product_id'))
                        sub_account['branch_name'] = position_map.get(str(sub_account['branch_id']), {}).get('name')
                    if child.get('amount') and isinstance(child.get('amount'), dict):
                        sub_account['qty'] = child['amount'].get('qty')
                    sub_accounts.append(sub_account)
            if len(sub_accounts) > 0:
                account["sub_accounts"] = sub_accounts
            accounts.append(account)
        result["rows"] = accounts
        result["total"] = total
        return result

    def get_summary_inventory_by_summary(self, request, partner_id, user_id):
        branch_id = request.branch_id
        product_ids = request.product_ids
        category_ids = request.category_ids
        limit = request.limit
        offset = request.offset
        start_date = request.start_date
        end_date = request.end_date
        if_pre = request.if_pre
        if_end = request.if_end
        branch_type = request.branch_type
        position_ids = [convert_to_int(_id) for _id in request.position_ids] if request.position_ids else []
        extra = {}
        if if_pre:
            extra['pre_qty'] = if_pre
        if if_end:
            extra['end_qty'] = if_end

        # logging.info("get_summary_inventory_by_summary1 request: {}".format(request))

        if category_ids and product_ids:
            new_product_ids = []
            relation_filters = {}
            category_list = []
            for category_id in category_ids:
                category_list.append(str(category_id))

            relation_filters = dict(product_category=category_list)
            products_by_catogory = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                     return_fields="id", partner_id=partner_id,
                                                                     user_id=user_id)
            products_by_catogory = products_by_catogory.get('rows')
            for product_by_catogory in products_by_catogory:
                for product_id in product_ids:
                    # print(product_id, product_by_catogory['id'])
                    if str(product_id) == str(product_by_catogory['id']):
                        new_product_ids.append(int(product_id))
            product_ids = new_product_ids

        elif category_ids and (not product_ids):
            new_product_ids = []
            relation_filters = {}
            category_list = []
            for category_id in category_ids:
                category_list.append(str(category_id))
            relation_filters = dict(product_category=category_list)
            product_ids_list = metadata_service.get_product_list(relation_filters=relation_filters,
                                                                 return_fields="id", partner_id=partner_id,
                                                                 user_id=user_id).get('rows')
            if not product_ids_list:
                return [], 0
            for product_id in product_ids_list:
                # print(product_id, type(product_id))
                new_product_ids.append(int(product_id['id']))
            product_ids = new_product_ids
        # branch_id, start_date, end_date, product_id=None, limit=None, offset=None,
        #                                 if_pre=None, if_end=None, partner_id=None, user_id=None
        # logging.info("get_summary_inventory_by_summary2 request: {}".format(request))
        snapshot_sum_list = inventory_service.query_snapshot_sum_list(branch_id=branch_id, start_date=start_date,
                                                                      end_date=end_date, product_id=product_ids,
                                                                      limit=limit, offset=offset, if_pre=if_pre,
                                                                      if_end=if_end, partner_id=partner_id,
                                                                      user_id=user_id)
        # logger.info("get_summary_inventory_by_summary3 snapshot_sum_list: {}".format(snapshot_sum_list.get('total')))
        # logging.info("get inventory response: {}".format(snapshot_sum_list))
        # print('***', json.dumps(inventory_snapshot_list))
        total = snapshot_sum_list.get('total')
        inventory_snapshot_list = snapshot_sum_list.get('rows')
        # logging.info("inventory_snapshot_list: {}".format(len(inventory_snapshot_list or [])))

        if not inventory_snapshot_list:
            return [], 0

        log_detail_list = []
        all_product_ids = []
        for detail in inventory_snapshot_list:
            if detail.get('account'):
                if detail.get('account').get('product_id'):
                    all_product_ids.append(int(detail.get('account').get('product_id')))
                    log_detail_list.append(detail)
            else:
                pass
        # 判断是门店还是仓库查询
        if branch_type in ("WAREHOUSE", "MACHINING_CENTER"):
            store_detail = metadata_service.get_distribution_center(int(branch_id), partner_id=partner_id,
                                                                    user_id=user_id)
        else:
            store_detail = metadata_service.get_store(int(branch_id), partner_id=partner_id, user_id=user_id)

        return_fields = ["id", "code", "name", "sale_type", "product_type", "bom_type", "storage_type", "status",
                         "model_name", "default_receiving_deviation_min", "category"]
        product_detail_list = metadata_service.get_product_list(ids=all_product_ids, include_units=True,
                                                                partner_id=partner_id, user_id=user_id,
                                                                return_fields=','.join(return_fields)).get('rows', [])

        # 批量获取unit和category详情，并整理成以id为key的dict
        unit_ids = []
        for filter_product_detail in product_detail_list:
            if filter_product_detail.get('units'):
                for product_units_detail in filter_product_detail['units']:
                    unit_id = int(product_units_detail['id'])
                    unit_ids.append(unit_id)

        filter_unit_list = metadata_service.get_unit_list(ids=unit_ids, partner_id=partner_id, user_id=user_id)
        filter_unit_list = filter_unit_list.get('rows', [])
        key_filter_unit_dict = {}
        for filter_unit in filter_unit_list:
            key_filter_unit_dict[filter_unit['id']] = filter_unit

        category_parent_name_map = metadata_service.get_category_parent(partner_id=partner_id, user_id=user_id)

        # 拉取仓位主档
        # position_dict = metadata_service.get_position_dict(partner_id=partner_id, user_id=user_id)

        # 拼接数据
        daily_inventory_list = []
        for detail in inventory_snapshot_list:

            # 没有期初也没期末库存
            # if (not detail['amount'].get('qty')) and (not detail['previous'].get('amount')):
            #     continue
            product_id = detail.get('account', {}).get('product_id')
            daily_inventory = {}
            daily_inventory.setdefault("trans_transfer_release", 0)
            daily_inventory['start_time'] = start_date
            daily_inventory['end_time'] = end_date
            # 门店明细
            daily_inventory['store_id'] = branch_id
            daily_inventory['store_code'] = store_detail['code']
            daily_inventory['store_name'] = store_detail['name']
            daily_inventory['trans_begin'] = 0  # 在途期初库存
            daily_inventory['trans_end'] = 0  # 在途期末库存
            # 商品明细
            for product_detail in product_detail_list:
                if product_detail['id'] == product_id:
                    # logging.info(f"product_detail: {product_detail}")

                    daily_inventory['product_id'] = int(product_id)
                    daily_inventory['product_code'] = product_detail['code']
                    daily_inventory['product_name'] = product_detail['name']
                    daily_inventory['product_status'] = product_detail['status']
                    daily_inventory['spec'] = product_detail.get('model_name') if product_detail.get(
                        'model_name') else None
                    if product_detail.get('units'):
                        for unit in product_detail["units"]:
                            if unit.get("default") and unit["default"] == True:
                                accounting_unit_id = unit["id"]
                                unit_detail = key_filter_unit_dict.get(str(accounting_unit_id), {})
                                daily_inventory['accounting_unit_name'] = unit_detail.get('name')
                                daily_inventory['accounting_unit_id'] = int(unit_detail.get('id', 0))
                                daily_inventory['accounting_unit_code'] = unit_detail.get('code')

                    # 商品类别相关
                    if product_detail.get('category'):
                        category_id = int(product_detail.get('category'))
                        idx = 1
                        for name in category_parent_name_map[category_id]:
                            if idx <= 10:
                                daily_inventory['category' + str(idx)] = name
                            idx = idx + 1

            # 可用期初库存
            daily_inventory['pre_qty'] = 0
            if detail.get('start_amount'):
                daily_inventory['pre_qty'] = detail['start_amount'].get('qty', 0)
                # 在途期初库存
                status_account = detail['start_amount'].get('status_account')
                if status_account:
                    # logging.info("status_account: {}".format(status_account))
                    datas = json.loads(status_account)
                    if datas:
                        for transfer_data in datas:
                            trans_begin = transfer_data.get('pre_qty', 0)
                            daily_inventory['trans_begin'] += trans_begin

            # 可用期末库存
            daily_inventory['qty'] = 0
            if detail.get('end_amount'):
                daily_inventory['qty'] = detail['end_amount'].get('qty', 0)
                # 在途期末库存
                status_account = detail['end_amount'].get('status_account')
                if status_account:
                    # logging.info("status_account: {}".format(status_account))
                    datas = json.loads(status_account)
                    if datas:
                        for transfer_data in datas:
                            if transfer_data.get('account_type') == 'BROKER':
                                trans_end = transfer_data.get('qty', 0)
                                daily_inventory['trans_end'] += trans_end

            # sku库存切片业务详情
            if detail.get('stats'):
                # print(detail.get('stats'))
                daily_inventory['rec_deposit'] = 0
                daily_inventory['stoc_deposit'] = 0
                daily_inventory['stoc_withdraw'] = 0
                daily_inventory['adj_withdraw'] = 0
                daily_inventory['ret_withdraw'] = 0
                daily_inventory['rec_withdraw'] = 0
                daily_inventory['ret_deposit'] = 0
                daily_inventory['trans_withdraw'] = 0
                daily_inventory['ret_transfer'] = 0
                daily_inventory['trans_transfer'] = 0
                daily_inventory['trans_transfer_release'] = 0
                daily_inventory['trans_delivery'] = 0
                daily_inventory['trans_delivery_release'] = 0
                daily_inventory['trans_purchase'] = 0
                daily_inventory['trans_purchase_release'] = 0
                daily_inventory['trans_return_release'] = 0
                # logging.info("daily_inventory: {}".format(daily_inventory))

                for stats_detail in detail['stats']:
                    # print(stats_detail['code'], stats_detail.get('amount').get('qty'), stats_detail['action'] )
                    if stats_detail.get('amount').get('qty') and stats_detail.get('account_type') == "SKU":
                        # 发货出库
                        if stats_detail['code'] == 'ORDER_DELIVERY' and 'WITHDRAW' in stats_detail['action']:
                            daily_inventory['rec_withdraw'] += stats_detail.get('amount').get('qty')

                        # 配送收货入库
                        if (stats_detail['code'] == 'ORDER_RECEIVING' or stats_detail[
                            'code'] == 'ADJUST_ORDER_RECEIVING') and \
                                (stats_detail['action'] == 'DEPOSIT' or stats_detail['action'] == 'TRANSFER_DEPOSIT'):
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')

                        # 配送收货出库
                        if (stats_detail['code'] == 'ORDER_RECEIVING' or stats_detail[
                            'code'] == 'ADJUST_ORDER_RECEIVING') and \
                                (stats_detail['action'] == 'WITHDRAW' or stats_detail['action'] == 'TRANSFER_WITHDRAW'):
                            if branch_type in ("WAREHOUSE", "MACHINING_CENTER"):  # 门店超收产生的退货单算入收货差异出库中
                                daily_inventory['rec_diff_withdraw'] = stats_detail.get('amount').get('qty')
                            else:
                                daily_inventory['rec_withdraw'] += stats_detail.get('amount').get('qty')

                        # 直送收货入库
                        if stats_detail['code'] == 'DIR_RECEIVING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')
                        if stats_detail['code'] == 'ORDER_DIR_RECEIVING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')

                        # 对账调整入库 —— 计入收货入库
                        if stats_detail['code'] == 'CHECKING_ADJUST_REC' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['rec_deposit'] += stats_detail.get('amount').get('qty')

                        # 对账调整出库 —— 计入退货出库
                        if stats_detail['code'] == 'CHECKING_ADJUST_RET' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 采购收货入库 —— 仓库采购收货
                        if stats_detail['code'] == 'WAREHOUSE_PURCHASE' and \
                                (stats_detail['action'] == 'TRANSFER_DEPOSIT' or stats_detail['action'] == 'DEPOSIT'):
                            daily_inventory['purchase_deposit'] = stats_detail.get('amount').get('qty')

                        # 收货差异出库
                        if stats_detail['code'] == 'RECEIVING_DIFF' and 'WITHDRAW' in stats_detail['action']:
                            daily_inventory['rec_diff_withdraw'] = stats_detail.get('amount').get('qty')

                        # 收货差异入库
                        if stats_detail['code'] == 'RECEIVING_DIFF' and 'DEPOSIT' in stats_detail['action']:
                            # if branch_type in ("WAREHOUSE", "MACHINING_CENTER"): # 门店收货差异产生的退货单算入退货入库中
                            #     daily_inventory['ret_deposit'] += stats_detail.get('amount').get('qty')
                            # else:
                            daily_inventory['rec_diff_deposit'] = stats_detail.get('amount').get('qty')

                        # 退货入库
                        status_returned_in = ["DEPOSIT", "TRANSFER_DEPOSIT"]
                        if stats_detail['code'] in ('RETURN_IN', 'ADJUST_ORDER_RETURN') and stats_detail[
                            'action'] in status_returned_in:
                            daily_inventory['ret_deposit'] += stats_detail.get('amount').get('qty')

                        # 退货出库
                        status_returned_out = ["WITHDRAW", "TRANSFER_WITHDRAW"]
                        if stats_detail['code'] in ('ORDER_RETURN', 'ADJUST_ORDER_RETURN') and stats_detail[
                            'action'] in status_returned_out:
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 采购退货
                        if stats_detail['code'] == 'PURCHASE_RETURN':
                            daily_inventory['ret_withdraw'] += stats_detail.get('amount').get('qty')

                        # 调拨入库
                        if stats_detail['code'] in ('TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                                and stats_detail['action'] == 'TRANSFER_DEPOSIT':
                            daily_inventory['trans_deposit'] = stats_detail.get('amount').get('qty')

                        # 调拨出库
                        if stats_detail['code'] in ('TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                                and stats_detail['action'] == 'TRANSFER_WITHDRAW':
                            daily_inventory['trans_withdraw'] = stats_detail.get('amount').get('qty')

                        # 盘点入库
                        if stats_detail['code'][0:9] == 'STOCKTAKE' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['stoc_deposit'] += stats_detail.get('amount').get('qty')

                        # 盘点出库
                        if stats_detail['code'][0:9] == 'STOCKTAKE' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['stoc_withdraw'] += stats_detail.get('amount').get('qty')

                        # 报废入库
                        if stats_detail['code'] == 'ADJUST' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['adj_deposit'] = stats_detail.get('amount').get('qty')

                        # 报废出库
                        if stats_detail['code'] == 'ADJUST' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['adj_withdraw'] += stats_detail.get('amount').get('qty')
                        if stats_detail['code'] == 'DIFF_ADJUST' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['adj_withdraw'] += stats_detail.get('amount').get('qty')

                        # 销售入库
                        if stats_detail['code'] == 'SALES' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['sales_deposit'] = stats_detail.get('amount').get('qty')

                        # 销售出库
                        if stats_detail['code'] == 'SALES' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['sales_withdraw'] = stats_detail.get('amount').get('qty')

                        # 自采入库
                        if stats_detail['code'] == 'SELF_PICKING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['spick_deposit'] = stats_detail.get('amount').get('qty')

                        # 冻结
                        if stats_detail['code'] == 'SUBMIT_ORDER' and stats_detail['action'] == 'TRANSFER_WITHDRAW':
                            daily_inventory['freeze_qty'] = stats_detail.get('amount').get('qty')

                        # 物料转换入库
                        if stats_detail['code'] == 'MATERIAL_CONVERT' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['material_trans_deposit'] = stats_detail.get('amount').get('qty')

                        # 物料转换出库
                        if stats_detail['code'] == 'MATERIAL_CONVERT' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['material_trans_withdraw'] = stats_detail.get('amount').get('qty')

                        # 加工入库
                        if stats_detail['code'] == 'PROCESSING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['processing_deposit'] = stats_detail.get('amount').get('qty')

                        # 加工出库
                        if stats_detail['code'] == 'PROCESSING' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['processing_withdraw'] = stats_detail.get('amount').get('qty')

                        # 包装入库
                        if stats_detail['code'] == 'PACKING' and stats_detail['action'] == 'DEPOSIT':
                            daily_inventory['packing_deposit'] = stats_detail.get('amount').get('qty')

                        # 包装出库
                        if stats_detail['code'] == 'PACKING' and stats_detail['action'] == 'WITHDRAW':
                            daily_inventory['packing_withdraw'] = stats_detail.get('amount').get('qty')

                    elif stats_detail.get('amount').get('qty') and stats_detail.get('account_type') == "BROKER":
                        status_deposit = ["DEPOSIT", "TRANSFER_DEPOSIT"]
                        status_withdraw = ["WITHDRAW", "TRANSFER_WITHDRAW"]
                        # 退货在途
                        if stats_detail['code'] in ('ORDER_RETURN', 'ADJUST_ORDER_RETURN') and stats_detail[
                            'action'] in status_deposit:
                            daily_inventory['ret_transfer'] += stats_detail.get('amount').get('qty')

                        # 退货在途释放
                        if stats_detail['code'] in ('RETURN_IN', 'ADJUST_ORDER_RETURN') and stats_detail[
                            'action'] in status_withdraw:

                            daily_inventory['trans_return_release'] += stats_detail.get('amount').get('qty')

                        # 调拨在途
                        if stats_detail['code'] in ('EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                                and stats_detail['action'] == 'TRANSFER_DEPOSIT':
                            daily_inventory['trans_transfer'] += stats_detail.get('amount').get('qty')

                        # 调拨在途释放
                        if stats_detail['code'] in ('EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER') \
                                and stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_transfer_release'] = daily_inventory['trans_transfer_release'] +\
                                                                        stats_detail.get('amount').get('qty')

                        # 发货在途
                        if stats_detail['code'] in ('ORDER_DELIVERY') and stats_detail['action'] in status_deposit:
                            daily_inventory['trans_delivery'] = stats_detail.get('amount').get('qty')

                        # 发货在途释放
                        if stats_detail['code'] in ('ORDER_RECEIVING', 'RECEIVING_DIFF', 'ADJUST_ORDER_RECEIVING') and \
                                stats_detail['action'] in status_withdraw:
                            daily_inventory['trans_delivery_release'] += stats_detail.get('amount').get('qty')

                        # 采购在途
                        if stats_detail['code'] in ('PURCHASE_DELIVERY') and stats_detail[
                            'action'] in status_deposit:
                            daily_inventory['trans_purchase'] = stats_detail.get('amount').get('qty')

                        # 采购在途释放
                        if stats_detail['code'] in ('WAREHOUSE_PURCHASE') and stats_detail[
                            'action'] in status_withdraw:
                            daily_inventory['trans_purchase_release'] = stats_detail.get('amount').get('qty')

            # 子账户库存切片业务详情
            if detail.get('account', {}).get('sub_account'):
                position_id = detail['account']['sub_account'].get('id', 0)
                daily_inventory['position_id'] = int(position_id)
                # daily_inventory['position_name'] = position_dict.get(str(position_id), {}).get('name')
                # daily_inventory['position_code'] = position_dict.get(str(position_id), {}).get('code')

            daily_inventory_list.append(daily_inventory)
        return daily_inventory_list, total

    def get_price_by_product_id(self, product_id, partner_id):
        from supply.driver.pm_mysql import session_maker
        sql = """
        select product_id, store_id, price_type_id, tax_price from product_sku sku
        join price p on sku.id = p.sku_id
        where product_id={} and p.partner_id = {};
        """.format(product_id, partner_id)
        db_session = session_maker()
        try:
            rows = session.execute(sql).fetchall()
            price_map = {}
            return_map = {}
            for row in rows:
                key = str(row[0]) + str(row[1])
                if price_map.get(key):
                    price_map[key].append(row)
                else:
                    price_map[key] = [row]
            for key, items in price_map.items():
                return_map[key] = {}
                for item in items:
                    if str(item[2]) == "1":
                        return_map[key].update({"tax_price": convert_to_decimal(item[3])})
                    if str(item[2]) == "2":
                        return_map[key].update({"retail_price": convert_to_decimal(item[3])})
            return return_map
        except Exception as e:
            logging.error("商品查询价格中心错误:{}-{}, {}".format(product_id, partner_id, str(e)))
        finally:
            db_session.close()

    def get_price_by_store_id(self, store_id, partner_id):
        from supply.driver.pm_mysql import session_maker
        sql = """
        select product_id, store_id, price_type_id, tax_price from product_sku sku
        join price p on sku.id = p.sku_id
        where store_id={} and p.partner_id = {};
        """.format(store_id, partner_id)
        db_session = session_maker()
        try:
            rows = db_session.execute(sql).fetchall()
            price_map = {}
            return_map = {}
            for row in rows:
                key = str(row[0]) + str(row[1])
                if price_map.get(key):
                    price_map[key].append(row)
                else:
                    price_map[key] = [row]
            for key, items in price_map.items():
                return_map[key] = {}
                for item in items:
                    if str(item[2]) == "1":
                        return_map[key].update({"tax_price": convert_to_decimal(item[3])})
                    if str(item[2]) == "2":
                        return_map[key].update({"retail_price": convert_to_decimal(item[3])})
            return return_map
        except Exception as e:
            logging.error("查询价格中心报错{}-{}:{}".format(store_id, partner_id, str(e)))
        finally:
            db_session.close()

    def get_entities_currency(self, schema_name, entity_ids, partner_id, user_id):
        """获取实体配置的币种，entity可能是门店或仓库"""
        if not entity_ids:
            return {}
        if not schema_name:
            schema_name = 'STORE'

        entity_ids = [int(entity_id) for entity_id in entity_ids]
        if schema_name == 'STORE':
            store_entities = metadata_center_service.list_entity(schema_name=schema_name, ids=entity_ids,
                                                              return_fields='currency',
                                                              partner_id=partner_id,
                                                              user_id=user_id).get('rows', [])
            return dict([(int(entity.get('id')), entity.get('fields', {}).get('currency')) for entity in store_entities])
        elif schema_name in ('WAREHOUSE', 'DISTRCENTER'):
            # 先查询仓库关联的公司，再得到公司配置的币种
            warehouse_entities = metadata_service.list_entity(schema_name='DISTRCENTER',
                                                            ids=entity_ids,
                                                           relation='all',
                                                           partner_id=partner_id,
                                                           user_id=user_id, ).get('rows', [])
            warehouse_company_map = dict([(int(entity.get('id')),  entity.get('fields', {}).get('relation', {}).get('company_info', 0)) for entity in warehouse_entities])
            company_ids = set(int(company) for warehouse, company in warehouse_company_map.items() if company)
            if not any(company_ids):
                return {}
            company_entities = metadata_service.get_company_list(ids=company_ids, return_fields='currency',
                                                              partner_id=partner_id,
                                                              user_id=user_id).get('rows', [])
            company_currency_map = dict([(entity.get('id'), entity.get('currency')) for entity in company_entities])
            for warehouse, company in warehouse_company_map.items():
                warehouse_company_map[warehouse] = company_currency_map.get(company)
            return warehouse_company_map

        return {}

    def handle_inventory_log_amount(self, inventory_logs):
        """处理库存流水的金额"""
        if not inventory_logs:
            return

        doc_ids = {inventory_log['source_id'] for inventory_log in inventory_logs}
        product_ids = {int(inventory_log['product_id']) for inventory_log in inventory_logs}
        sql = """SELECT source_id, product_id, price, unit_rate
                           FROM price_chain_document_products
                           WHERE source_id IN :doc_ids
                             AND product_id IN :product_ids;"""
        doc_data_list = session.execute(sql, {'doc_ids': tuple(doc_ids),
                                              'product_ids': tuple(product_ids)}
                                        ).fetchall()
        doc_data_map = {
            (doc_data.source_id, doc_data.product_id): {'price': doc_data.price, 'unit_rate': doc_data.unit_rate}
            for doc_data in doc_data_list}
        for inventory_log in inventory_logs:
            doc_data = doc_data_map.get((inventory_log['source_id'], int(inventory_log['product_id'])), {})
            del inventory_log['source_id']
            if not doc_data:
                continue
            inventory_log['price'] = doc_data.get('price', 0)
            if inventory_log['order_type'] in ('RECEIVING_DIFF', 'RETURN_IN'):
                inventory_log['amount'] = float(doc_data.get('price', 0)) * inventory_log.get('demand_qty', 0)
            else:
                inventory_log['amount'] = float(doc_data.get('price', 0)) * inventory_log['qty']


inventory_bi_service = InventoryBiService()
