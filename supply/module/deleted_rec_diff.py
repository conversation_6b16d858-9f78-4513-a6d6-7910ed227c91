# -*- coding: utf-8 -*-
from datetime import datetime
from sqlalchemy import func
import logging
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp
from decimal import Decimal
from hex_exception import RecordAlreadyExist
from supply import logger
from ..error.exception import NoResultFoundError, StatusUnavailable, DataValidationException, DealInventoryException

from ..driver.mysql import db_commit, session
from ..utils.encode import encodeUTF8
from ..utils.resulting import ErrorCode
from ..utils.snowflake import gen_snowflake_id
from ..utils.helper import MessageTopic, convert_to_int, get_product_unit_map
from ..utils import pb2dict
from ..client.metadata_service import metadata_service 
from ..client.inventory_service import inventory_service
from ..client.receipt_service import receipt_service
from ..task.message_service_pub import MessageServicePub
# from ..task import public
from ..driver.mq import mq_producer

from ..model.supply_doc_code import Supply_doc_code
from ..model.receiving_diff import ReceivingDiffModel, ReceivingDiffProductModel, ReceivingDiffLogModel
from ..model.returns import ReturnModel, ReturnProductModel
from ..module.adjust_service import ads
from ..module.returns import returns_service




def round_2(number,digits=None):
    if digits is None:
        digits = 2
    return round(Decimal(number) + Decimal(0.0001), digits)



class ReceivingDiffService():
    '''收货差异单相关服务
    service: 
        - create_receiving_diff()：新建收货差异单
        - get_receiving_diff_by_id(): 通过diff_id查询收货差异单信息
        - list_receiving_diff_products_by_diff_id():根据diff_id查询收货差异单商品明细
        - list_receiving_diffs(): 枚举所有收货差异单
        - update_receiving_diff(): 更新收货差异单（差异数量两边承担数量、差异原因、备注）
        - submit_receiving_diff(): 提交收货差异单
        - confirm_receiving_diff(): 确认收货差异单
        - reject_receiving_diff(): 驳回收货差异单
        - delete_receiving_diff(): 删除收货差异单
        - update_receiving_diff_cold(): 更新直送收货差异单（全覆盖式更新）
        - update_receiving_diff(): 更新收货差异单（差异原因、备注）
    '''

    # def is_less_to_return(self, receiving_id, diff_ids, product_id, store_id, diff_quantity, partner_id):
    #     ''' 
    #     直送单是否可以差异退货
    #     计算规则：退货数量不允许超过实际收货数量
    #     '''
    #     product_db = ReceivingProductModel.get_p_detail_by_product_id(product_id, receiving_id)
    #     # 已经申请退货的收货差异总和（针对允许多次提交差异单的情况）
    #     # 只计算已确认的单子
    #     existed_diff_quantity = ReceivingDiffProductModel.calculate_product_diff_quantity(diff_ids, product_id, partner_id)
    #     # 此次退货的数量（直送收货差异单的diff_quantity其实是需要退货的数量）
    #     return_quantity = Decimal(diff_quantity)
    #     # 累计要退货的差异值
    #     diff_quantity_sum = existed_diff_quantity+return_quantity 
    #     # print(existed_diff_quantity, return_quantity, diff_quantity_sum)
    #     # 判断商品是否可退货
    #     # 门店实际收货数量
    #     actual_received_quantity = Decimal(product_db.confirmed_quantity)
    #     # 剩余数量
    #     left_quantity = actual_received_quantity-diff_quantity_sum
        
    #     if left_quantity < 0:
    #         raise DataValidationException("\"{}\"收货差异累计超出偏差范围({},{})".format(
    #                                         product_db.product_name,round(existed_diff_quantity,2),round(left_quantity,2)))
    
    #     return True

    def deposit_inventory(self, receiving_diff_id, description, partner_id, user_id, branch_id=None, time=3):
        diff_db = ReceivingDiffModel.get(receiving_diff_id)
        count, diff_product_db_list = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id, partner_id=partner_id)

        detail_list = []
        sequence = 0
        for product_db in diff_product_db_list:
            detail = {}
            
            detail['sequence_id'] = sequence
            accounting = {}
            account = {}
            account['branch_id'] = branch_id if branch_id else product_db.received_by
            account['product_id'] = product_db.product_id
            accounting['account'] = account
            # 门店承担数量增加仓库库存
            accounting['amount'] = product_db.d_diff_accounting_quantity
            detail['accounting'] = accounting
            sequence += 1
            if product_db.d_diff_accounting_quantity > 0:
                detail_list.append(detail)

        # print('************库存调用detail*************')
        # print(detail_list)
        
        # 增加库存
        if detail_list != []:
            result = inventory_service.deal_with_inventory(batch_no=str(receiving_diff_id), code='RECEIVING_DIFF', action='DEPOSIT',
                    description=description, detail=detail_list, partner_id=partner_id, user_id=user_id, trace_id=diff_db.code)
            # print('***********库存返回***********')
            # print(result)
            if result.get('success') and result['success'] == True:
                return result
            else:
                # 超过三次没成功：
                if time == 0:
                    raise DealInventoryException("failed to desposit inventory:{}".format(receiving_diff_id))
                else:
                    time -= 1
                    self.deposit_inventory(receiving_diff_id, description, partner_id, user_id, time)

    def withdraw_inventory(self, receiving_diff_id, description, partner_id, user_id):
        diff_db = ReceivingDiffModel.get(receiving_diff_id)
        count, diff_product_db_list = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id, partner_id=partner_id)

        detail_list = []
        sequence = 0
        for product_db in diff_product_db_list:
            detail = {}
            detail['sequence_id'] = sequence
            accounting = {}
            account = {}
            if product_db.sub_receive_by:
                account.update(dict(sub_account=dict(id=product_db.sub_receive_by)))
            account['branch_id'] = product_db.received_by
            account['product_id'] = product_db.product_id
            accounting['account'] = account
            if diff_db.is_direct:
                accounting['amount'] = product_db.diff_quantity
            else:
                # 配送扣减库存 仓库承担数量
                accounting['amount'] = product_db.d_diff_quantity
            detail['accounting'] = accounting
            sequence += 1

            detail_list.append(detail)

        # print('************库存调用detail*************')
        # print(detail_list)
        
        # 减少库存
        result = inventory_service.deal_with_inventory(batch_no=str(receiving_diff_id), code='PURCHSE_DIFF_RETURN',
                                                       action='WITHDRAW', description=description,
                                                       detail=detail_list, partner_id=partner_id, user_id=user_id,
                                                       trace_id=diff_db.code)
        # print('***********库存返回***********')
        # print(result)
        time = 0
        if result.get('status') and result['status'] == 'SUCCESS':
            return result
        elif result.get('success'):
            return result
        else:
            time += 1
            # 超过三次没成功：
            if time == 3:
                raise DealInventoryException("failed to desposit inventory:{}".format(receiving_diff_id))
            else:
                self.withdraw_inventory(receiving_diff_id, description, partner_id, user_id)

    def prepare_inventory(self, receiving_diff_id, description, code, partner_id, user_id, action=None):
        diff_db = ReceivingDiffModel.get(receiving_diff_id)
        count, diff_product_db_list = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id, partner_id)
        receive_db = receipt_service.get_receive_by_id(diff_db.receiving_id, partner_id, user_id)

        # 收货差异取消在途——库存本质：仓库在途去除，加库存
        detail_list = []
        sequence = 0
            
        # 门店配送收货——库存本质：仓库在途转入门店主账户
        for product_db in diff_product_db_list:
            # 单位转换
            if product_db.unit_rate:
                amount = product_db.diff_quantity * product_db.unit_rate
            else:
                amount = product_db.diff_quantity

            transfer_detail = {
                        "from": {
                            "branch_id": receive_db.delivery_by,
                            "product_id": product_db.product_id,
                            "extra":
                            {
                                "code":'DemandOrder', # 门店配送收货
                                "type":3
                            } # from`里要有`extra`字段，`"type":3`是`broker`类型，code`是业务自定义字段，发货和收货的时候必须相同
                        },
                        "to": {
                            "branch_id": receive_db.delivery_by,
                            "product_id": product_db.product_id
                        },
                        "amount": amount
                    }
            detail = {
                        "sequence_id": sequence,
                        "transfer": transfer_detail
                    }
            sequence += 1
            detail_list.append(detail)

        # 在途COMMIT
        result = inventory_service.deal_with_inventory(
                        batch_no=str(receiving_diff_id), code=code, action='TRANSFER_COMMIT',
                        description=description, detail=detail_list, partner_id=partner_id,
                        user_id=user_id, trace_id=diff_db.code, pre_trace_id=receive_db.code)
        return result

    def check_return_available(self, store_id, product_detail_list, partner_id, user_id):
        product_ids = []
        for product_detail in product_detail_list:
            product_ids.append(product_detail['product_id'])
        
        product_inventory_detail = inventory_service.get_products_inventory_by_branch_id(
                                        branch_id=store_id, product_ids=product_ids)
        
        for product_detail in product_detail_list:
            if product_inventory_detail.get(str(product_detail['product_id'])):
                inventory_qty = product_inventory_detail.get(str(product_detail['product_id']))['quantity_avail']
                if Decimal(inventory_qty)-Decimal(product_detail['quantity']) < 0:
                    product_info = metadata_service.get_product(product_id=int(product_detail['product_id']), 
                                        partner_id=partner_id, user_id=user_id)
                    logging.info('{} return_qty_is_larger_than_inventory'.format(product_detail['product_id']))
                    raise DataValidationException('{}库存不足，不允许退货!'.format(product_info['name']))
                else:
                    return True
            else:
                product_info = metadata_service.get_product(product_id=int(product_detail['product_id']), 
                                        partner_id=partner_id, user_id=user_id)
                logging.info('{} return_qty_is_larger_than_inventory'.format(product_detail['product_id']))
                raise DataValidationException('{}库存不足，不允许退货!'.format(product_info['name']))

    def get_receiving_diff_by_id(self, receiving_diff_id):
        receiving_diff_db = ReceivingDiffModel.get(receiving_diff_id)
        if receiving_diff_db:
            receiving_diff_obj = receiving_diff_db.serialize(conv=True)
            return receiving_diff_obj
        return None

    def list_receiving_diff_products_by_diff_id(self, diff_id,limit=None,offset=None,include_total=False, 
                                                    sort=None, order=None, partner_id=None):
        ''' 根据diff_id 枚举该收货单上所有商品明细 '''
        count = None
        count, receiving_diff_products_db_list = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(diff_id,limit,offset,include_total, sort, order, partner_id)
        receiving_diff_products_list = []
        if receiving_diff_products_db_list:
            for receiving_diff_product_db in receiving_diff_products_db_list:
                receiving_diff_product_obj = receiving_diff_product_db.serialize(conv=True)
                receiving_diff_products_list.append(receiving_diff_product_obj)
            return count, receiving_diff_products_list
        return 0, None

    def list_receiving_diffs(self,partner_id, user_id, request):
        '''枚举所有收货单'''

        limit=request.limit
        offset=request.offset
        include_total=False
        store_ids=request.store_ids
        status=request.status
        start_date=request.start_date
        end_date=request.end_date
        receiving_ids=request.receiving_ids
        receiving_code=request.receiving_code
        code=request.code
        logistics_type=request.logistics_type
        sort = request.sort
        order = request.order

        storeIdList = []
        for storeId in store_ids:
            storeIdList.append(int(storeId))
        store_ids = storeIdList

        count, receiving_diff_db = ReceivingDiffModel.list_receiving_diffs(partner_id, user_id, 
                                        limit, offset, include_total, store_ids,
                                        status, start_date, end_date, receiving_ids, receiving_code, 
                                        code, logistics_type, sort, order)
        if receiving_diff_db:
            receiving_diff_list = []
            for receiving_diff in receiving_diff_db:
                receiving_diff_obj = receiving_diff.serialize(conv=True)
                receiving_diff_list.append(receiving_diff_obj)
            return count, receiving_diff_list
        return 0, None

    @db_commit
    def update_receiving_diff(self, receiving_diff_id, products, partner_id, user_id, attachments=None, remark=None):
        # 配送收货差异单商品明细更新
        # 修改差异单商品明细（差异原因、门店承担数量、配送方承担数量、备注）
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

        receiving_diff_db = ReceivingDiffModel.get(receiving_diff_id)
        diff_args = {
            'updated_at':datetime.now(),
            'updated_by':user_id,
            'updated_name':operator_name,
        }
        if attachments:
            diff_args.update(dict(attachments=str(attachments)))
        if remark:
            diff_args.update(dict(remark=remark))
        receiving_diff_db.update(**diff_args)

        for product in products:
            p_id = product.id
            product_db = ReceivingDiffProductModel.get(p_id)
            if not product_db:
                raise NoResultFoundError("no product found!")

            product = pb2dict(product)
            args = {
                    'updated_at':datetime.now(),
                    'updated_by':user_id,
                    'updated_name':operator_name
                    }
            if product.get('remark'):
                args['remark'] = product['remark']
            if product.get('reason_type'):
                args['reason_type'] = product['reason_type']
            if product.get('s_diff_quantity'):
                args['s_diff_quantity'] = product['s_diff_quantity']
                args['s_diff_accounting_quantity'] = float(product['s_diff_quantity'])*float(product_db.unit_rate) if product_db.unit_rate else float(product['s_diff_quantity'])
                
            else:
                args['s_diff_quantity'] = 0
            if product.get('d_diff_quantity'):
                args['d_diff_quantity'] = product['d_diff_quantity']
                args['d_diff_accounting_quantity'] = float(product['d_diff_quantity'])*float(product_db.unit_rate) if product_db.unit_rate else float(product['d_diff_quantity'])
            else:
                args['d_diff_quantity'] = 0

            # 检验门店承担数量是否等于仓库承担数量
            if (args['d_diff_quantity']+args['s_diff_quantity'])!= float(product_db.diff_quantity):
                raise DataValidationException("差异数量=配送方承担数量+门店承担数量")
            product_db.update(**args)

            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = product_db.diff_id
            log_detail['operation'] = 'UPDATE'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.now()
            log_detail['created_name'] = operator_name
            log_db = ReceivingDiffLogModel.new(**log_detail)
            # return update_product.serialize(conv=True)
        return True

    @db_commit         
    def submit_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get(receiving_diff_id)
        count, receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id, partner_id)
        if not receiving_diff_product_db:
            # 无差异商品
            raise NoResultFoundError("no diff products found!")
        
        if receiving_diff_db.status not in ('INITED','REJECTED'):
            # 状态不允许提交
            raise StatusUnavailable("only INITED/ REJECTED data can be submitted!")

        if receiving_diff_db.is_direct == True:
            # 增加库存校验：是否允许退货
            product_detail_list = []
            for return_product_db in receiving_diff_product_db:
                product_detail = {}
                product_detail['product_id'] = return_product_db.product_id
                product_detail['quantity'] = return_product_db.diff_quantity
                product_detail_list.append(product_detail)
            self.check_return_available(store_id=receiving_diff_db.received_by, product_detail_list=product_detail_list, partner_id=partner_id, user_id=user_id)
        
        else:
            # 只要每个商品门店填写的承担数量等于差异数量，直接通过审核，无需仓库审批
            flag = False
            for index, product_db in enumerate(receiving_diff_product_db):
                if product_db.s_diff_quantity != product_db.diff_quantity:
                    flag = True

            if flag:
                pass
            else:
                receiving_diff_db.update(status="SUBMITTED")
                res = self.confirm_receiving_diff(receiving_diff_id, partner_id, user_id)
                return res

        # 配送差异门店不需要校验，并非真正退货
        # else:
        #     # 增加库存校验：门店承担数量是否允许退货
        #     product_detail_list = []
        #     for return_product_db in receiving_diff_product_db:
        #         product_detail = {}
        #         product_detail['product_id'] = return_product_db.product_id
        #         product_detail['quantity'] = return_product_db.s_diff_quantity
        #         product_detail_list.append(product_detail)
        #     self.check_return_available(store_id=receiving_diff_db.received_by, product_detail_list=product_detail, partner_id=partner_id, user_id=user_id)
            
        # 驳回状态重新提交需要重新验证它的偏差值范围
        if receiving_diff_db.status == 'REJECTED' and receiving_diff_db.is_direct == True:
            receiving_diff_list = ReceivingDiffModel.list_receiving_diffs_by_receiving_id(receiving_id=receiving_diff_db.receiving_id, partner_id=partner_id)
            # print(receiving_diff_list)
            diff_ids = []
            for receiving_diff in receiving_diff_list:
                if receiving_diff.status == 'CONFIRMED':
                    diff_ids.append(receiving_diff.id)

            product_transfer_list = []
            count = 0
            for product_db in receiving_diff_product_db:
                count = count+1
                # # 判断是否可以申请差异单退货
                # self.is_less_to_return(receiving_diff_db.receiving_id, diff_ids, product_db.product_id, receiving_diff_db.received_by, product_db.diff_quantity, partner_id)
                
                p_args = {
                    'updated_at':datetime.now(),
                    'status':'SUBMITTED',
                    'updated_by':user_id,
                    'updated_name':operator_name
                }
                product_db.update(**p_args)

                p_trans_detail = {}
                if receiving_diff_db.is_direct == False:
                    p_trans_detail = {
                        'sku_no':product_db.product_code,
                        'uom':product_db.unit_spec,
                        # 'order_qty':str(float(product_db.diff_quantity)),
                        'difference_qty':str(float(product_db.diff_quantity)),
                        'store_part_qty':str(float(product_db.diff_quantity)),
                        'wh_part_qty':str(float(product_db.diff_quantity)),
                        # 'customer_return_line_no':str(product_db.hws_line),
                        'difference_line_no':str(product_db.hws_line)
                        }
                    product_transfer_list.append(p_trans_detail)
                else:
                    p_trans_detail = {
                        'sku_no':product_db.product_code,
                        'uom':product_db.unit_spec,
                        'order_qty':str(float(product_db.diff_quantity)),
                        # 'difference_qty':str(float(product_db.diff_quantity)),
                        # 'store_part_qty':str(float(product_db.diff_quantity)),
                        # 'wh_part_qty':str(float(product_db.diff_quantity)),
                        'customer_return_line_no':str(count),
                        # 'difference_line_no':str(product_db.hws_line)
                        }
                    product_transfer_list.append(p_trans_detail)


            args = {
                'updated_at':datetime.now(),
                'status':'SUBMITTED',
                'updated_by':user_id,
                'review_by':user_id,
                'updated_name':operator_name
                }
            receiving_diff_db.update(**args)

            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = product_db.diff_id
            log_detail['operation'] = 'SUBMIT'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.now()
            log_detail['created_name'] = operator_name
            log_db = ReceivingDiffLogModel.new(**log_detail)
            session.flush()
            return True
            
        else:
            product_transfer_list = []
            for product_db in receiving_diff_product_db:
                if not product_db.reason_type:
                    # 差异原因没填
                    raise DataValidationException("reason type required!")
                p_args = {
                    'updated_at':datetime.now(),
                    'status':'SUBMITTED',
                    'updated_by':user_id,
                    'updated_name':operator_name
                }
                product_db.update(**p_args)

                p_trans_detail = {}
                if receiving_diff_db.is_direct == False:
                    p_trans_detail = {
                        'sku_no':product_db.product_code,
                        'uom':product_db.unit_spec,
                        # 'order_qty':str(float(product_db.diff_quantity)),
                        'difference_qty':str(float(product_db.diff_quantity)),
                        'store_part_qty':str(float(product_db.diff_quantity)),
                        'wh_part_qty':str(float(product_db.diff_quantity)),
                        # 'customer_return_line_no':str(product_db.hws_line),
                        'difference_line_no':str(product_db.hws_line)
                        }
                    product_transfer_list.append(p_trans_detail)
                else:
                    p_trans_detail = {
                        'sku_no':product_db.product_code,
                        'uom':product_db.unit_spec,
                        'order_qty':str(float(product_db.diff_quantity)),
                        # 'difference_qty':str(float(product_db.diff_quantity)),
                        # 'store_part_qty':str(float(product_db.diff_quantity)),
                        # 'wh_part_qty':str(float(product_db.diff_quantity)),
                        'customer_return_line_no':str(product_db.hws_line),
                        # 'difference_line_no':str(product_db.hws_line)
                        }
                    product_transfer_list.append(p_trans_detail)


            args = {
                'updated_at':datetime.now(),
                'status':'SUBMITTED',
                'updated_by':user_id,
                'review_by':user_id,
                'updated_name':operator_name
                }
            receiving_diff_db.update(**args)

            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = receiving_diff_id
            log_detail['operation'] = 'SUBMIT'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.now()
            log_detail['created_name'] = operator_name
            log_db = ReceivingDiffLogModel.new(**log_detail)
            session.flush()

            # PDA消息推送
            if receiving_diff_db.is_direct:
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                    user_id=user_id,
                                                    scope_id=1,
                                                    store_id=receiving_diff_db.received_by,
                                                    source_root_id=receiving_diff_db.receiving_id,
                                                    source_id=receiving_diff_db.id,
                                                    source_type="REC_DIFF_C",
                                                    action="SUBMITTED",
                                                    ref_source_id=receiving_diff_db.receiving_id,
                                                    ref_source_type="RECEIVING",
                                                    ref_action="INITED",
                                                    content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name}
                                                    )
            else:
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                    user_id=user_id,
                                                    scope_id=1,
                                                    store_id=receiving_diff_db.received_by,
                                                    source_root_id=receiving_diff_db.receiving_id,
                                                    source_id=receiving_diff_db.id,
                                                    source_type="REC_DIFF",
                                                    action="SUBMITTED",
                                                    ref_source_id=receiving_diff_db.receiving_id,
                                                    ref_source_type="RECEIVING",
                                                    ref_action="INITED",
                                                    content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name}
                                                    )
                
            return True

    @db_commit  
    def confirm_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        count, receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id, partner_id)
        if not receiving_diff_product_db:
            # 无差异商品
            raise NoResultFoundError("no diff products found!")

        receiving_diff_db = ReceivingDiffModel.get(receiving_diff_id)

        # 提交状态/ havi单据的审核状态
        if (receiving_diff_db.status == 'SUBMITTED'):
            # 直送退货
            if receiving_diff_db.is_direct:
                # 更新单据状态
                args = {
                        'updated_at':datetime.now(),
                        'status':'CONFIRMED',
                        'has_checked':True,
                        'updated_by':user_id,
                        'review_by':user_id,
                        'updated_name':operator_name
                    }
                receiving_diff_db.update(**args)

                # 调用库存引擎，按照退货数量减少库存
                description='direct diff return'
                result = receiving_diff_service.withdraw_inventory(receiving_diff_db.id, description,partner_id, user_id)
                if result and result.get('id'):
                    args = {'inventory_req_id':result['id'], 'inventory_status':result['status']}
                    receiving_diff_db.update(**args)

                # PDA消息推送
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                    user_id=user_id,
                                                    scope_id=1,
                                                    store_id=receiving_diff_db.received_by,
                                                    source_root_id=receiving_diff_db.receiving_id,
                                                    source_id=receiving_diff_db.id,
                                                    source_type="REC_DIFF_C",
                                                    action="CONFIRMED",
                                                    ref_source_id=receiving_diff_db.id,
                                                    ref_source_type="REC_COLD",
                                                    ref_action="SUBMITTED",
                                                    content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name,}
                                                    )
               
            # 配送收货差异
            else: 
                args = {
                            'updated_at':datetime.now(),
                            'status':'CONFIRMED',
                            'has_checked':True,
                            'updated_by':user_id,
                            'review_by':user_id,
                            'updated_name':operator_name
                        }


                # 按照门店承担数量生成报废单
                # p_args = []
                # s_flag = 0
                # for receiving_diff_product in receiving_diff_product_db:
                #     p_arg = {}
                #     if receiving_diff_product.s_diff_quantity:
                #         s_flag = 1
                #         p_arg = {
                #             'product_id':receiving_diff_product.product_id,
                #             'unit_id':receiving_diff_product.unit_id,
                #             'quantity':receiving_diff_product.s_diff_quantity,
                #             'reason_type':'01',
                #             'position_id': receiving_diff_product.sub_receive_by
                #         }
                #         p_args.append(p_arg)
                # if s_flag != 0:
                #     receiving_db = receipt_service.get_receive_by_id(receiving_diff_db.receiving_id, partner_id, user_id)
                #     args = {
                #         'request_id':receiving_diff_db.id,
                #         'receive_id':receiving_diff_db.receiving_id,
                #         'receive_code':receiving_db.order_code,
                #         # 'receive_jde_code':receiving_db.jde_order_id,
                #         'adjust_date':datetime.now(),
                #         'adjust_store':receiving_diff_db.received_by,
                #         'reason_type':'01',
                #         'products':p_args,
                #         'is_receive':True,
                #         'remark':'收货差异生成'+' '+str(receiving_diff_db.code),
                #         'branch_type':'STORE' if receiving_diff_db.received_type=='DEMAND' else 'WAREHOUSE'
                        
                #     }
                #     # 收货差异按仓位报废
                #     adj_obj = ads.create_adjust_from_receive(adjust_detail=args, partner_id=partner_id, user_id=user_id)
                #     if adj_obj:
                #         adj_id = adj_obj.adjust_id
                #         public(MessageTopic.ADJUST_AUTO_COMFIRM_TOPIC, dict(adjust_id=adj_id, partner_id=partner_id,
                #                                         user_id=user_id, branch_type=args['branch_type']))

                # 按照仓库承担数量生成差异退货单
                p_args = []
                d_flag = 0
                count = 0
                for receiving_diff_product in receiving_diff_product_db:
                    count = count+1
                    p_arg = {}
                    # 配送差异产生的退货单，商品行号从1开始
                    if receiving_diff_product.d_diff_quantity:
                        d_flag = 1
                        p_arg = {
                            'product_id':receiving_diff_product.product_id,
                            'product_name':receiving_diff_product.product_name,
                            'product_code':receiving_diff_product.product_code,
                            'unit_id':receiving_diff_product.unit_id,
                            'quantity':receiving_diff_product.d_diff_quantity,
                            'hws_line':count,
                            'reason_type':'收货差异生成'
                        }
                        p_args.append(p_arg)
                if d_flag != 0 :
                    receiving_db = receipt_service.get_receive_by_id(receiving_diff_db.receiving_id, partner_id, user_id)

                    order_json = {}
                    order_json['demand_order_code'] = receiving_db.order_code
                    # order_json['rec_jde_code'] = receiving_db.jde_order_id
                    order_json['rec_code'] = receiving_db.code
                    order_json['diff_code'] = receiving_diff_db.code


                    diff_ret_id, diff_args= returns_service.create_diff_return(return_to=receiving_diff_db.delivery_by,
                                    return_delivery_date=datetime.now(), return_reason='收货差异生成',
                                    product_detail=p_args, partner_id=partner_id, user_id=user_id, 
                                    source_id=receiving_diff_db.id, source_code=receiving_diff_db.code,
                                    logistics_type=receiving_diff_db.logistics_type, remark=order_json)
                    args.update(**diff_args)
                receiving_diff_db.update(**args)
                    # 调用库存引擎，按照仓库承担数量增加仓库库存
                    # description='mcu receiving diff'
                    # result = receiving_diff_service.deposit_inventory(
                    #         receiving_diff_db.id, description, partner_id, user_id, receiving_diff_db.delivery_by)
                    # if result and result.get('id'):
                    #     args = {'inventory_req_id':result['id'], 'inventory_status':result['status']}
                    #     receiving_diff_db.update(**args)
                
                
                # PDA消息推送
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=receiving_diff_db.received_by,
                                                  source_root_id=receiving_diff_db.receiving_id,
                                                  source_id=receiving_diff_db.id,
                                                  source_type="REC_DIFF",
                                                  action="CONFIRMED",
                                                  ref_source_id=receiving_diff_db.id,
                                                  ref_source_type="REC_COLD",
                                                  ref_action="SUBMITTED",
                                                  content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name,}
                                                  )
                
            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = receiving_diff_id
            log_detail['operation'] = 'CONFIRM'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.now()
            log_detail['created_name'] = operator_name
            log_db = ReceivingDiffLogModel.new(**log_detail)

            return True
        
        else:
            # 状态不允许提交
            raise StatusUnavailable("only SUBMIITED data can be confirmed!")       

    @db_commit
    def reject_receiving_diff(self, receiving_diff_id, partner_id, user_id, reject_reason=None, attachments=None):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get(receiving_diff_id)
        receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id, partner_id)
        if not receiving_diff_product_db:
            # 无差异商品
            raise NoResultFoundError("no diff products found!")

        if receiving_diff_db.status == 'SUBMITTED':
            # for product_db in receiving_diff_product_db:
            #     p_args = {
            #         'updated_at':datetime.now(),
            #         'status':'REJECTED',
            #         # 'updated_by':review_by
            #     }
            #     product_db.update(**p_args)

            args = {
                'updated_at':datetime.now(),
                'status':'REJECTED',
                'updated_by':user_id,
                'updated_name':operator_name,
                'review_by':user_id,
                'reject_reason':reject_reason,
                'attachments':attachments
            }
            receiving_diff_db.update(**args)

            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = receiving_diff_id
            log_detail['operation'] = 'REJECT'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.now()
            log_detail['created_name'] = operator_name
            log_db = ReceivingDiffLogModel.new(**log_detail)
            
            # PDA消息推送
            if receiving_diff_db.is_direct:
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                  user_id=user_id,
                                                  scope_id=1,
                                                  store_id=receiving_diff_db.received_by,
                                                  source_root_id=receiving_diff_db.receiving_id,
                                                  source_id=receiving_diff_db.id,
                                                  source_type="REC_DIFF_C",
                                                  action="REJECTED",
                                                  ref_source_id=receiving_diff_db.id,
                                                  ref_source_type="REC_COLD",
                                                  ref_action="SUBMITTED",
                                                  content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name,}
                                                  )
            
            else:
                MessageServicePub.pub_message_service(partner_id=partner_id,
                                                    user_id=user_id,
                                                    scope_id=1,
                                                    store_id=receiving_diff_db.received_by,
                                                    source_root_id=receiving_diff_db.receiving_id,
                                                    source_id=receiving_diff_db.id,
                                                    source_type="REC_DIFF",
                                                    action="REJECTED",
                                                    ref_source_id=receiving_diff_db.id,
                                                    ref_source_type="REC_COLD",
                                                    ref_action="SUBMITTED",
                                                    content={
                                                            "store_name":metadata_service.get_store(receiving_diff_db.received_by, partner_id=partner_id, user_id=user_id).get('name'),
                                                            "receiving_code":str(receiving_diff_db.receiving_code),
                                                            "delivery_date":str(receiving_diff_db.delivery_date),
                                                            "updated_at":str(datetime.now()),
                                                            "updated_by":str(user_id),
                                                            "updated_name": operator_name,}
                                                    )
            
            return True
        
        else:
            # 状态不允许提交
            raise StatusUnavailable("only SUBMIITED data can be rejected!")  
    
    @db_commit
    def delete_receiving_diff(self, receiving_diff_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_diff_db = ReceivingDiffModel.get(receiving_diff_id)
        count, receiving_diff_product_db = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(receiving_diff_id, partner_id)
        if receiving_diff_product_db:
            for product_db in receiving_diff_product_db:
                p_args = {
                    'updated_at':datetime.now(),
                    'status':'DELETED',
                    'updated_by':user_id,
                    'updated_name':operator_name
                }
                product_db.update(**p_args)

        if receiving_diff_db.status == 'INITED' or receiving_diff_db.status == 'REJECTED':
            args = {
                'updated_at':datetime.now(),
                'status':'DELETED',
                'updated_by':user_id,
                'review_by':user_id,
                'updated_name':operator_name
            }
            receiving_diff_db.update(**args)
            # receiving_diff_product_db.update(**args)
            # 记录操作日志
            log_detail = {}
            log_detail['doc_id'] = product_db.diff_id
            log_detail['operation'] = 'DELETE'
            log_detail['success'] = True
            log_detail['partner_id'] = partner_id
            log_detail['created_by'] = user_id
            log_detail['created_at'] = datetime.now()
            log_detail['created_name'] = metadata_service.get_username_by_pid_uid(partner_id, user_id)
            log_db = ReceivingDiffLogModel.new(**log_detail)
            return True
        
        else:
            # 状态不允许提交
            raise StatusUnavailable("only INITED data can be deleted!")  
    
    def get_unconfirmed_diffs(self, end_hours, end_mins, status, partner_id, user_id):
        # 月底月盘日，在当日12:00PM前自动作业配送差异单
        today = datetime.today()
        start_time = datetime(today.year, today.month, 1, 0, 0)
        end_time = datetime(today.year, today.month, today.day, end_hours, end_mins)

        # 新建\提交状态的配送差异单，自动审核完成
        is_direct=False
        status=['INITED', 'REJECTED']
        diff_dbs = ReceivingDiffModel.get_diffs_by_status(start_time, end_time, status, is_direct, partner_id, user_id)

        diff_id_list=[]
        for diff_db in diff_dbs:
            message = {}
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            message['diff_id'] = diff_db.id
            message['status'] = diff_db.status
            # public(MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC, message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC,
                                    message=message)
            

    def get_unconfirmed_diffs_over1day(self, overhours, status, partner_id, user_id, max_overdays=None):
        # 收货后24小时，门店没有填写的差异单
        if not max_overdays:
            max_overdays = 30
        start_time = datetime.now()-timedelta(days=max_overdays)
        end_time = datetime.now()-timedelta(hours=overhours)

        # 新建\提交状态的配送差异单，自动审核完成
        is_direct=False
        diff_dbs = ReceivingDiffModel.get_diffs_by_status(start_time, end_time, status, is_direct, partner_id, user_id)

        for diff_db in diff_dbs:
            message = {}
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            message['diff_id'] = diff_db.id
            message['status'] = diff_db.status
            message['owner'] = 'warehouse'
            logging.info('StartAutoConfirmDiffs: {}'.format(message))
            # public(MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC, message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC,
                                    message=message)
        
    @db_commit
    def auto_confirm(self, diff_id, status=None, owner=None, partner_id=None, user_id=None):
        if not status:
            raise DataValidationException('状态异常！')
        
        receiving_diff_db = ReceivingDiffModel.get(diff_id)
        # 新建状态的配送差异单，默认门店承担全部数量
        # 如果owner='warehouse',仓库承担数量
        if status == 'INITED' or status == 'REJECTED':
            count, diff_products = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(diff_id, partner_id)
            for diff_product_db in diff_products:
                args = {
                    's_diff_quantity':0 if owner=='warehouse' else diff_product_db.diff_quantity,
                    'd_diff_quantity':diff_product_db.diff_quantity if owner=='warehouse' else 0,
                    'updated_at':datetime.now(),
                    'updated_by':user_id
                }
                diff_product_db.update(**args)

            receiving_diff_db.update(status="SUBMITTED")
            res = self.confirm_receiving_diff(diff_id, partner_id, user_id)
            return res

        # 提交状态的配送差异单，自动审核完成
        # if status == 'SUBMITTED':
        #     count, diff_products = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(diff_id)
        #     for diff_product_db in diff_products:
        #         args = {
        #             's_diff_quantity':diff_product_db.diff_quantity,
        #             'd_diff_quantity':0,
        #             'updated_at':datetime.now(),
        #             'updated_by':user_id
        #         }
        #         diff_product_db.update(**args)

        #     res = self.confirm_receiving_diff(diff_id, partner_id, user_id)
        #     return res 


    #---ForReceiptStart---#
    # 创建收货差异单
    @db_commit
    def create_receive_diff(self, receiving_id, partner_id, user_id):
        operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
        receiving_db = receipt_service.get_receive_by_id(receive_id=receiving_id, partner_id=partner_id, user_id=user_id)
        entity = receipt_service.get_receive_products_by_receive_id(receive_id=receiving_id, partner_id=partner_id, user_id=user_id)
        confirmed_products = entity.rows

        # id、code生成
        args = {
                'id':gen_snowflake_id(),
                'code':Supply_doc_code.get_code_by_type('REC_DIFF',partner_id, None),
                'receiving_id':receiving_id,
                'received_by':receiving_db.receive_by,
                'receiving_code':receiving_db.code,
                'master_id':receiving_db.batch_id,
                'master_code':receiving_db.batch_code,
                'status':'INITED',
                'demand_date':receiving_db.demand_date.ToDatetime() if receiving_db.demand_date else None,
                'delivery_by':receiving_db.delivery_by,
                'is_direct':1 if receiving_db.distr_type == 'PUR' else 0,
                'delivery_date':receiving_db.delivery_date.ToDatetime() if receiving_db.delivery_date else None,
                'review_by':user_id, # ?
                'inventory_status':'INITED',
                'updated_by':user_id,
                'created_by':user_id,
                'updated_name':operator_name,
                'created_name':operator_name,
                'updated_at':datetime.now(),
                'created_at':datetime.now(),
                'partner_id':partner_id,
                'received_type':receiving_db.batch_type,
                # 'store_secondary_id':receiving_db.store_secondary_id,
                'request_id':receiving_id,
                'logistics_type':receiving_db.distr_type,
                'branch_type': "WAREHOUSE" if receiving_db.main_branch_type == 'W' else "STORE",
                'sub_receive_by': receiving_db.sub_receive_by,
                'auto_confirm_date': datetime.now()+timedelta(hours=2)
            }
        
        new_diff = ReceivingDiffModel.new(**args)

        product_code_list = []
        product_ids = []
        for product in confirmed_products:
            product_code_list.append(product.product_code)
            product_ids.append(convert_to_int(product.product_id))
        # 转换单位，调用封装好的接口获取商品单位map
        product_unit_dict = get_product_unit_map(product_ids=product_ids, partner_id=partner_id, user_id=user_id)
                
        # 新建收货差异单商品明细
        product_nums = 0
        has_diffs = False
        for product in confirmed_products:
            product_detail = pb2dict(product) 
            diff_quantity = round((Decimal(
                            product_detail['delivery_quantity']) if product_detail.get('delivery_quantity') else Decimal(
                            0.0)) - (Decimal(
                            product_detail['receive_quantity']) if product_detail.get('receive_quantity') else Decimal(
                            0.0)), 2)
          
            # 商品无差异   
            if diff_quantity == 0:
                pass
                
            else:
                unit_detail = product_unit_dict.get(convert_to_int(product_detail.get('product_id')))
                unit_rate = 1
                unit_name = None
                unit_spec = None
                # 这里取"order"订货单位
                if unit_detail.get('order', {}):
                    unit_rate = unit_detail.get('order').get('rate')
                    unit_name = unit_detail.get('order').get('name')
                    unit_spec = unit_detail.get('order').get('code')
                else:
                    logger.warning("没有设置订货单位, product_id:{}".format(product_detail.get('product_id')))
                has_diffs = True
                product_nums += 1
                p_args = {
                    'id': gen_snowflake_id(),
                    'receiving_id': new_diff.receiving_id,
                    'received_by': new_diff.received_by,
                    'diff_id': new_diff.id,
                    'product_id': product_detail.get('product_id'),
                    'material_number': product_detail.get('material_number'),
                    'accounting_unit_id': product_detail.get('accounting_unit_id'),
                    'unit_id': product_detail.get('unit_id'),
                    'product_code': product_detail.get('product_code'),
                    'product_name': product_detail.get('product_name'),
                    'accounting_unit_name': product_detail.get('accounting_unit_name'),
                    'accounting_unit_spec': product_detail.get('accounting_unit_spec'),
                    'unit_name': unit_name,
                    'unit_spec': unit_spec,
                    'unit_rate': unit_rate,
                    'received_accounting_quantity': float(product_detail.get('delivery_quantity', 0)) * unit_rate,
                    'received_quantity': float(product_detail.get('delivery_quantity', 0)),
                    'confirmed_accounting_quantity': float(product_detail.get('receive_quantity', 0)) * unit_rate,
                    'confirmed_quantity': float(product_detail.get('receive_quantity', 0)),
                    'demand_date': receiving_db.demand_date.ToDatetime() if receiving_db.demand_date else None,
                    'diff_accounting_quantity': float(diff_quantity) * unit_rate,
                    'diff_quantity': diff_quantity,
                    's_diff_quantitu': diff_quantity, # 210421-saas版本新需求，默认全由门店承担
                    'updated_by': user_id,
                    'created_by': user_id,
                    'updated_name': operator_name,
                    'created_name': operator_name,
                    'updated_at': datetime.now(),
                    'created_at': datetime.now(),
                    'partner_id': partner_id,
                    'sub_receive_by': receiving_db.sub_receive_by,
                    'cost_price': float(product_detail.get('cost_price', 0)),
                    'tax_price': float(product_detail.get('tax_price', 0)),
                    'tax_rate': float(product_detail.get('tax_rate', 0))
                }

                ReceivingDiffProductModel.new(**p_args)
                    
        # 整单均无差异：
        if not has_diffs:
            ReceivingDiffModel.delete(new_diff.id)
            return None

        # 更新收货差异单的product_num
        args = {'product_nums':product_nums}
        new_diff.update(**args)



        # 记录操作日志
        log_detail = {}
        log_detail['doc_id'] = new_diff.id
        log_detail['operation'] = 'CREATE'
        log_detail['success'] = True
        log_detail['partner_id'] = partner_id
        log_detail['created_by'] = user_id
        log_detail['created_at'] = datetime.now()
        log_detail['created_name'] = operator_name
        ReceivingDiffLogModel.new(**log_detail)
        return new_diff.id

    #---ForReceiptEnd---#

receiving_diff_service = ReceivingDiffService()