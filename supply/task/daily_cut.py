from supply.utils.helper import MessageTopic, convert_to_int
import logging
from supply.module.inventory_cut import inventory_cut_service
from supply.client.metadata_service import metadata_service
from datetime import datetime, timedelta
from supply.driver.mq import register_mq_task, mq_producer
from . import partner_task_distribution


# 全门店每日库存切片任务激活
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.INVENTORY_DAILY_CUT_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.INVENTORY_DAILY_CUT_TOPIC)
def daily_cut_inventory(message):
    """
    全门店每日库存切片任务激活
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    end_date = message.get('end_date')
    if not partner_id or not user_id:
        logging.error('激活全门店库存切片任务缺少pid/uid - %s' % message)
        return True
    logging.info("激活全门店库存切片任务:{}".format(message))
    # 生成批次
    inventory_cut_service.auto_daily_cut_inventory(partner_id=partner_id, user_id=user_id, end_date=end_date)
    return True


# 按门店一家一家生成批次
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.INVENTORY_GET_PRODUCTS_TOPIC)
def inventory_get_products(message):
    """
    每日库存切片
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    store_id = convert_to_int(message['store_id']) if 'store_id' in message else None
    end_date = message.get('end_date')
    batch_no = message.get('batch_no')
    if not partner_id or not user_id:
        logging.error(
            'doing daily cut schedule - unexpected message format: invalid partner_id - %s' % message)
        return True

    logging.info("按门店处理库存切片任务:{}, {}".format(store_id, end_date))
    if end_date:
        end_date = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")

    # 按门店一家一家生成批次
    inventory_cut_service.cut_inventory_by_store(store_id=store_id, partner_id=partner_id, user_id=user_id,
                                                 end_date=end_date, batch_no=batch_no)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.INVENTORY_DAILY_CUT_BY_STORE_TOPIC)
def daily_cut_inventory_by_stores(message):
    """
    部分门店库存切片任务激活
    从数据库里拿到需要切片的门店
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    if not partner_id or not user_id:
        logging.error(
            'initiating daily cut schedule - unexpected message format: invalid partner_id - %s' % message)
        return True
    logging.debug('** initiating daily cut schedule - partner_id=%s **' % partner_id)
    # 生成批次
    inventory_cut_service.daily_snapshot_by_store(partner_id=partner_id, user_id=user_id)
    logging.debug('** initiated daily cut schedule - partner_id=%s **' % partner_id)
    return True


# 全仓库每日库存切片任务激活
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.WAREHOUSE_INVENTORY_DAILY_CUT_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.WAREHOUSE_INVENTORY_DAILY_CUT_TOPIC)
def daily_cut_inventory_warehouse(message):
    """
    全门店每日库存切片任务激活
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    end_date = message.get('end_date')
    include_supply = message.get('include_supply')
    if not partner_id or not user_id:
        logging.error('激活全仓库库存切片任务缺少pid/uid - %s' % message)
        return True
    logging.info("激活全仓库库存切片任务:{}".format(message))
    # 生成批次
    inventory_cut_service.auto_daily_cut_warehouse_inventory(
        partner_id=partner_id, user_id=user_id, end_date=end_date, include_supply=include_supply)
    return True


# 按仓库一家一家生成批次
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.WAREHOUSE_INVENTORY_GET_PRODUCTS_TOPIC)
def inventory_get_products_warehouse(message):
    """
    每日库存切片
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    warehouse_id = convert_to_int(message['warehouse_id']) if 'warehouse_id' in message else None
    end_date = message.get('end_date')
    batch_no = message.get('batch_no')
    if not partner_id or not user_id:
        logging.error(
            'doing daily cut schedule - unexpected message format: invalid partner_id - %s' % message)
        return True

    logging.info("按仓库处理库存切片任务:{}, {}".format(warehouse_id, end_date))
    if end_date:
        end_date = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")

    # 按仓库一家一家生成批次
    inventory_cut_service.cut_inventory_by_warehouse(
        warehouse_id=warehouse_id, partner_id=partner_id, user_id=user_id, end_date=end_date, batch_no=batch_no)
    return True


# 全成本中心每日库存切片任务激活
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.COST_CENTER_INVENTORY_DAILY_CUT_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.COST_CENTER_INVENTORY_DAILY_CUT_TOPIC)
def daily_cut_inventory_costcenter(message):
    """
    全成本中心每日库存切片任务激活
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    end_date = message.get('end_date')
    if not partner_id or not user_id:
        logging.error('激活全成本中心库存切片任务缺少pid/uid - %s' % message)
        return True
    logging.info("激活全成本中心库存切片任务:{}".format(message))
    # 生成批次
    inventory_cut_service.auto_daily_cut_costcenter_inventory(
        partner_id=partner_id, user_id=user_id, end_date=end_date)
    return True


# 按成本中心一家一家生成批次
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.COST_CENTER_INVENTORY_GET_PRODUCTS_TOPIC)
def inventory_get_products_costcenter(message):
    """
    每日成本中心切片
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    cost_center_id = convert_to_int(message['cost_center_id']) if 'cost_center_id' in message else None
    cost_center_code = message.get('cost_center_code')
    end_date = message.get('end_date')
    batch_no = message.get('batch_no')
    if not partner_id or not user_id:
        logging.error(
            'doing daily cut schedule - unexpected message format: invalid partner_id - %s' % message)
        return True

    logging.info("按成本中心处理库存切片任务:{}, {}".format(cost_center_id, end_date))
    if end_date:
        end_date = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")

    # 成本中心一家一家生成批次
    inventory_cut_service.cut_inventory_by_cost_center(
        cost_center_id=cost_center_id, cost_center_code=cost_center_code,
        partner_id=partner_id, user_id=user_id, end_date=end_date, batch_no=batch_no)
    return True


# 库存同步成本中心与门店配置
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.COST_CENTER_STORE_TOPIC)
def inventory_get_costcenter_to_store(message):
    """
    同步门店和成本中心的信息给库存引擎
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    if not partner_id or not user_id:
        logging.error(
            'doing daily cut schedule - unexpected message format: invalid partner_id - %s' % message)
        return True

    logging.info("同步门店-成本中心信息给库存引擎")
    # 成本中心一家一家生成批次
    inventory_cut_service.update_cost_center_for_inventory(partner_id=partner_id, user_id=user_id)
    return True


# saas平台最新成本计算分发任务——拉取所有成本中心
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.TRIGGER_COST_ALL_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.TRIGGER_COST_ALL_TOPIC)
def create_cost_task(message):
    """
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    schema_name = message.get('schema_name') # e.g. cost-center 
    # start_date = message.get('start_date')
    # end_date = message.get('end_date')
    # hour = message.get('hour') if message.get('hour') else 20
    # minute = message.get('minute') if message.get('minute') else 0
    # period_id = message.get('period_id')
    # pre_period_id = message.get('pre_period_id')
    # task_type = message.get('task_type')
    # report_type = message.get('report_type')
    # branch_type = message.get('branch_type') 
    # 以上注释参数也需要配置！！！ 这个接口只做多个成本中心时候的转发处理！！！

    branch_ids = message.get('branch_ids') 
    logging.info("接收到TRIGGER_COST_TASK 信息 %s" % message)
    if not partner_id or not user_id:
        logging.info("TRIGGER_COST_TASK 缺少租户信息 %s" % message)
        return True
    
    # 指定参数
    if branch_ids:
        for branch_id in branch_ids:
            message['branch_id'] = branch_id
            logging.info("分发成本引擎计算任务 TRIGGER_COST_TASK %s" % message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.TRIGGER_COST_TASK_TOPIC,
                                    message=message)
    # 全量拉取
    else:
        cost_center_list = metadata_service.list_entity(partner_id=partner_id, user_id=user_id,
                                    schema_name=schema_name).get('rows')
        logging.info("cost_center_list:{}, partner_id{}, user{}".format(cost_center_list, partner_id, user_id))
        for cost_center_detail in cost_center_list:
            message['branch_id'] = cost_center_detail['id']
            logging.info("分发成本引擎计算任务 TRIGGER_COST_TASK %s" % message)
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.TRIGGER_COST_TASK_TOPIC,
                                        message=message)


    return True

# saas平台最新成本计算触发
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.TRIGGER_COST_TASK_TOPIC)
def trigger_cost_task(message):
    """
    触发成本中心计算物料任务
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    start_date = message.get('start_date')
    end_date = message.get('end_date')
    branch_id = message.get('branch_id')
    branch_type = message.get('branch_type')
    hour = message.get('hour') if message.get('hour') else 20
    minute = message.get('minute') if message.get('minute') else 0
    period_id = message.get('period_id')
    pre_period_id = message.get('pre_period_id')
    task_type = message.get('task_type')
    report_type = message.get('report_type')
    advance_days = message.get('advance_days', 1)
    extra_config = message.get('extra_config')

    if not partner_id or not user_id:
        logging.info("TRIGGER_COST_TASK 缺少租户信息 %s" % message)
        return True

    logging.info("触发成本引擎计算物料 TRIGGER_COST_TASK %s" % message)
    inventory_cut_service.trigger_cost_task(branch_id=branch_id, branch_type=branch_type, 
                                        start_date=start_date, end_date=end_date, 
                                        hour=hour, minute=minute,
                                        partner_id=partner_id, user_id=user_id,
                                        pre_period_id=pre_period_id, period_id=period_id, 
                                        task_type=task_type,
                                        report_type=report_type,
                                        advance_days=advance_days, extra_config=extra_config)
    return True


# 触发成本执行计算
# 可用于每日计算任务dkron配置
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.TRIGGER_COST_COUNT_MATERIAL_TOPIC)
def trigger_cost_count_material(message):
    """
    触发成本中心计算物料任务
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    start_date = message.get('start_date')
    end_date = message.get('end_date')
    branch_id = message.get('branch_id')
    branch_type = message.get('branch_type')
    product_ids = message.get('product_ids')
    # hour是utc时区下hour, 默认是20
    hour = message.get('hour') if message.get('hour') else 20
    minute = message.get('minute') if message.get('minute') else 0
    period_id = message.get('period_id')
    task_type = message.get('task_type')
    report_type = message.get('report_type')
    category = message.get('category')

    if not partner_id or not user_id:
        return False

    logging.info("触发成本引擎计算物料 TRIGGER_COST_COUNT_MATERIAL %s" % message)
    inventory_cut_service.trigger_cost_count_material(branch_id=branch_id, branch_type=branch_type,
                                                      start_date=start_date, end_date=end_date, 
                                                      partner_id=partner_id, user_id=user_id,
                                                      product_ids=product_ids, 
                                                      hour=hour, minute=minute,
                                                      period_id=period_id, task_type=task_type, 
                                                      report_type=report_type, category=category)

    return True


# 触发成本执行BOM计算
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.TRIGGER_COST_COUNT_BOM_TOPIC)
def trigger_cost_count_bom(message):
    """
    触发成本中心计算bom任务
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    start_date = message.get('start_date')
    end_date = message.get('end_date')
    branch_id = message.get('branch_id')
    branch_type = message.get('branch_type')
    region_type = convert_to_int(message['region_type']) if 'region_type' in message else 0
    period_group_by = convert_to_int(message['period_group_by']) if 'period_group_by' in message else 0

    if not partner_id or not user_id:
        return False

    logging.info("触发成本引擎计算bom TRIGGER_COST_COUNT_BOM %s" % message)
    inventory_cut_service.trigger_cost_count_bom(region_type=region_type, period_group_by=period_group_by,
                                                 branch_id=branch_id, branch_type=branch_type,
                                                 start_date=start_date, end_date=end_date, partner_id=partner_id,
                                                 user_id=user_id)
    logging.info("触发成本引擎计算bom TRIGGER_COST_COUNT_BOM 成功")
    return True


# 触发成本执行计算
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.TRIGGER_COST_RECOUNT_MATERIAL_TOPIC)
def trigger_cost_recount_material(message):
    """
    触发成本中心重新计算物料任务
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    start_date = message.get('start_date')
    end_date = message.get('end_date')
    branch_id = message.get('branch_id')
    branch_type = message.get('branch_type')
    product_ids = message.get('product_ids')
    days = message.get('days')

    if not partner_id or not user_id:
        return False

    logging.info("触发成本引擎计算物料 TRIGGER_COST_RECOUNT_MATERIAL %s" % message)
    inventory_cut_service.trigger_cost_recount_material(branch_id=branch_id, branch_type=branch_type,
                                                        start_date=start_date, end_date=end_date, days=days,
                                                        partner_id=partner_id, user_id=user_id,
                                                        product_ids=product_ids)
    return True


# 触发成本执行BOM计算
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.TRIGGER_COST_RECOUNT_BOM_TOPIC)
def trigger_cost_recount_bom(message):
    """
    触发成本中心重新计算bom任务
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    start_date = message.get('start_date')
    end_date = message.get('end_date')
    branch_id = message.get('branch_id')
    branch_type = message.get('branch_type')
    region_type = convert_to_int(message['region_type']) if 'region_type' in message else 0
    period_group_by = convert_to_int(message['period_group_by']) if 'period_group_by' in message else 0
    days = message.get('days')

    if not partner_id or not user_id:
        return False

    logging.info("触发成本引擎重新计算bom TRIGGER_COST_RECOUNT_BOM %s" % message)
    inventory_cut_service.trigger_cost_recount_bom(region_type=region_type, period_group_by=period_group_by,
                                                   branch_id=branch_id, branch_type=branch_type,
                                                   start_date=start_date, end_date=end_date, days=days,
                                                   partner_id=partner_id, user_id=user_id)
    logging.info("触发成本引擎重新计算bom TRIGGER_COST_RECOUNT_BOM 成功")
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.TRIGGER_COST_CLOSE_TOPIC)
def trigger_cost_count_close_task(message):
    """
    触发成本中心关闭账期任务
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    batch_no = message.get('batch_no')

    if not partner_id or not user_id:
        return False

    logging.info("触发成本引擎关闭账期任务 TRIGGER_COST_CLOSE_TOPIC %s" % message)
    inventory_cut_service.trigger_cost_close_count_task(batch_no=batch_no, partner_id=partner_id, user_id=user_id)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.PRE_TRIGGER_COST_COUNT_MATERIAL_TOPIC)
def pre_trigger_cost_count_material(message):
    """
    触发成本中心计算试算物料任务
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    start_date = message.get('start_date')
    end_date = message.get('end_date')
    branch_id = message.get('branch_id')
    branch_type = message.get('branch_type')
    product_ids = message.get('product_ids')
    # hour是utc时区下hour, 默认是20
    hour = message.get('hour') if message.get('hour') else 20
    minute = message.get('minute') if message.get('minute') else 0
    period_id = message.get('period_id')
    task_type = message.get('task_type')
    report_type = message.get('report_type')
    extra_type = message.get('extra_type')  # 正常无，特殊要求时

    if not partner_id or not user_id:
        return False

    logging.info("触发成本引擎每日试算计算物料 PRE_TRIGGER_COST_COUNT_MATERIAL_TOPIC %s" % message)
    inventory_cut_service.trigger_cost_count_material(branch_id=branch_id, branch_type=branch_type,
                                                      start_date=start_date, end_date=end_date, partner_id=partner_id,
                                                      user_id=user_id,
                                                      product_ids=product_ids, hour=hour, minute=minute,
                                                      period_id=period_id, task_type=task_type, report_type=report_type,
                                                      extra_type=extra_type)
    return True


# 库存处理情况订阅
@register_mq_task("boh_inventory", MessageTopic.INVENTORY_BATCH_PROCESS_COMPLETE)
def inventory_batch_process_result(message):
    """
    接收库存batch处理结果
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    # 　检查message参数
    partner_id = message.get('PartnerId')
    batch_no = message.get('BatchNo')
    code = message.get('Code')
    inventory_id = message.get('ID')
    # action = message.get('Action')
    status = message.get('Status')
    total = message.get('Total')
    success = message.get('Success')
    fail = message.get('Fail')

    if not partner_id:
        logging.error(
            'INVENTORY_BATCH_PROCESS_COMPLETE - no partner_id - %s' % message)
        return True
    # logging.info('收到库存处理情况 ReceiveInventoryBatchRes - %s' % message)
    if code == 'DAILY_SNAPSHOT':
        inventory_cut_service.update_inventory_batch_process_result(partner_id=partner_id,
                                                                    batch_no=batch_no, inventory_id=inventory_id,
                                                                    status=status, total=total, success=success,
                                                                    fail=fail)
    return True


# 轮询补偿切片任务
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.RECOVER_INVENTORY_DAILY_CUT)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.RECOVER_INVENTORY_DAILY_CUT)
def recover_inventory_batch(message):
    """
    切片轮询补偿
    :param message:　｛"partner_id":xxx｝
    :return:
    """
    logging.info('接收到库存切片轮训补偿消息 ReceiveInventoryCutRecoverMsg - %s' % message)
    # 　检查message参数
    minutes = message.get('minutes') if message.get('minutes') else 60
    status = message.get('status') if message.get('status') else ['FINISH', 'SUCCESS']
    max_retry = message.get('max_retry')
    partner_id = message.get('partner_id')

    inventory_cut_service.recover_inventory_batch(minutes=minutes, status=status, max_retry=max_retry,
                                                  partner_id=partner_id)
    return True
