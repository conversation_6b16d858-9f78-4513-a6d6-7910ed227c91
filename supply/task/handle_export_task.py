import logging
import json
import time
import pytz
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp
from urllib.parse import unquote

from supply import logger
from supply.driver.mq import register_mq_task, mq_producer
from supply.utils.helper import MessageTopic, convert_to_int
from supply.utils.auth import branch_list_scope_check

from supply.model.warehouse.purchase_order import warehouse_purchase_order_db, warehouse_purchase_order_product_db
from supply.client.export_service import export_service
from supply.module.inventory_bi import inventory_bi_service
from supply.module.demand import demand_module
from supply.proto.manufactory.inventory_bi_pb2 import RealtimeInventoryRequest as mRealtimeInventoryRequest
from supply.proto.warehouse.inventory_bi_pb2 import RealtimeInventoryRequest as wRealtimeInventoryRequest
from supply.proto.store.inventory_bi_pb2 import RealtimeInventoryRequest as sRealtimeInventoryRequest
from supply.proto.store.inventory_bi_pb2 import SummaryInventoryByTimeRequest as sSummaryInventoryRequest
from supply.proto.manufactory.inventory_bi_pb2 import SummaryInventoryRequest as mSummaryInventoryRequest
from supply.proto.warehouse.inventory_bi_pb2 import SummaryInventoryRequest as wSummaryInventoryRequest


def get_timestamp(date):
    if isinstance(date, datetime):
        date = Timestamp(seconds=int(date.timestamp()))
    else:
        date = None
    return date


def getAsiaShanghaiNow(return_str=True):
    tz = pytz.timezone("Asia/Shanghai")
    if return_str is True:
        return datetime.now(tz=tz).strftime("%Y-%m-%d %H:%M:%S")
    else:
        return datetime.now(tz=tz)


# 导出任务
# --- 采购单明细导出 --- #
@register_mq_task("boh_export", MessageTopic.EXPORT_PURCHASE_DETAILS)
def export_delivery_details(message):
    """
        导出采购单明细
    """
    logging.info("接收到【导出采购单明细】消息:{}".format(message))
    query = message.get("query")
    if query and isinstance(query, str):
        query = eval(query)
        doc_id = query.get("id")
    else:
        logging.info("缺少采购单id {}".format(message))
        return True
    task_id = message.get("id")
    partner_id = message.get("pid")
    user_id = message.get("uid")

    query_order = warehouse_purchase_order_db.get_order_by_id(order_id=doc_id, partner_id=partner_id)
    if not query_order:
        logging.info("找不到该采购单 {}".format(doc_id))
        return True

    products = warehouse_purchase_order_product_db.get_order_product(user_id=user_id, partner_id=partner_id,
                                                                     order_id=doc_id)

    order_type_map = {
        "MH": "总仓采购",
        "MP": "加工中心采购"
    }

    export_list = []
    if len(products) > 0:
        for product in products:
            export_detail = {
                "code": query_order.order_code,
                "order_date": (query_order.order_date + timedelta(hours=8)).strftime("%Y-%m-%d"),
                "order_type": order_type_map.get(query_order.order_type),
                "received_name": query_order.received_name,
                "supplier_name": query_order.supplier_name,
                "remark": query_order.remark,

                "product_code": product.product_code,
                "product_name": product.product_name,
                "unit_name": product.purchase_unit_name,
                "spec": product.spec,
                "quantity": '%.3f' % float(product.quantity) if product.quantity else "0.000",
                "price": '%.3f' % float(product.price_tax) if product.price_tax else "0.000",
                "tax_rate": '%.2f' % float(product.tax_rate) + "%" if product.tax_rate else "0.00%",
                "amount": '%.3f' % (float(product.quantity) * float(
                    product.price_tax)) if product.quantity and product.price_tax else "0.000"
            }
            export_list.append(export_detail)
        title = {
            "curr": getAsiaShanghaiNow(return_str=True)
        }
        ret = export_service.download_files(task_id=task_id, title=title, data=export_list, partner_id=partner_id,
                                            user_id=user_id)
    return ret


# --- 门店运营-实时库存导出 ---
@register_mq_task("boh_export", MessageTopic.EXPORT_STORE_REALTIME)
def export_store_delivery_details(message):
    """门店运营-实时库存导出"""
    logger.info("接收到【门店实时库存列表】消息:{}".format(message))
    query = message.get("query")
    partner_id = message.get("pid")
    user_id = message.get("uid")
    task_id = message.get("id")
    if query and isinstance(query, str):
        query = json.loads(query)
        # 门店id
        branch_ids = query["branch_ids"] if 'branch_ids' in query else []
        branch_ids = [int(branch_id) for branch_id in branch_ids]
        product_ids = [int(p) for p in query["product_ids"]] if query.get("product_ids") else []
        category_ids = query["category_ids"] if 'category_ids' in query else []
        position_ids = query["position_ids"] if 'position_ids' in query else []
        position_ids = [int(position_id) for position_id in position_ids]
        geo_regions = query["geo_regions"] if 'geo_regions' in query else []
        geo_regions = [int(geo_region) for geo_region in geo_regions]
        sort = query["sort"] if 'sort' in query else ''
        order = query["order"] if 'order' in query else ''
        exclude = query["exclude"] if 'exclude' in query else ''

        branch_ids = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store', branch_ids)

        if branch_ids and len(branch_ids) == 0:
            logger.info("缺少门店id {}".format(message))
            return True
    else:
        logger.info("缺少门店id {}".format(message))
        return True

    ret = False

    logger.info("category_ids: {}, product_ids: {}, branch_ids: {}".format(category_ids, product_ids, branch_ids))
    req = sRealtimeInventoryRequest(branch_ids=branch_ids, category_ids=category_ids,
                                    product_ids=product_ids, sort=sort, order=order, exclude=exclude)
    realtime_inventory_list, total = inventory_bi_service.get_realtime_inventory(request=req, partner_id=partner_id,
                                                                                 user_id=user_id)

    logger.info("实时库存数据: {}".format(realtime_inventory_list))

    order_type_map = {
        "ENABLED": "启用",
        "DISABLED": "停用"
    }
    export_list = []
    if realtime_inventory_list and len(realtime_inventory_list) == 0:
        logger.info("查询不到实时库存: {}".format(message))
        return True
    if len(realtime_inventory_list) > 0:
        for realtime_inventory in realtime_inventory_list:
            export_detail = {
                "store_code": "-",
                "store_name": "-",
                "product_code": "-",
                "product_name": "-",
                "product_status": "-",
                "spec": "-",
                "qty": "0.000000",
                "accounting_unit_name": "-",
                "demand_qty": "0.000000",
                "demand_unit_name": "-",
                "store_return_broker": '0.000000',
                "store_transfer_broker": '0.000000'
            }
            if "category_name" in realtime_inventory:
                export_detail["category_name"] = str(realtime_inventory["category_name"])
            if "store_code" in realtime_inventory:
                export_detail["store_code"] = str(realtime_inventory["store_code"])
            if "store_name" in realtime_inventory:
                export_detail["store_name"] = str(realtime_inventory["store_name"])
            if "product_code" in realtime_inventory:
                export_detail["product_code"] = str(realtime_inventory["product_code"])
            if "product_name" in realtime_inventory:
                export_detail["product_name"] = str(realtime_inventory["product_name"])
            # if "product_status" in realtime_inventory:
            #     export_detail["product_status"] = str(order_type_map.get(realtime_inventory["product_status"]))
            if "spec" in realtime_inventory:
                export_detail["spec"] = str(realtime_inventory["spec"]) if realtime_inventory["spec"] else "-"
            if "qty" in realtime_inventory:
                export_detail["qty"] = '{0:f}'.format(realtime_inventory["qty"])
            if "accounting_unit_name" in realtime_inventory:
                export_detail["accounting_unit_name"] = str(realtime_inventory["accounting_unit_name"])
            if "demand_qty" in realtime_inventory:
                export_detail["demand_qty"] = '{0:f}'.format(realtime_inventory["demand_qty"])
            if "demand_unit_name" in realtime_inventory:
                export_detail["demand_unit_name"] = str(realtime_inventory["demand_unit_name"])
            if "extra_detail" in realtime_inventory:
                extra_detail = realtime_inventory["extra_detail"]
                if len(extra_detail) > 0:
                    for detail in extra_detail:
                        if detail.get('code') == 'StoreReturn' and detail.get('sku_type') == 'broker':
                            if '{0:f}'.format(detail.get('qty')) != '0':
                                export_detail['store_return_broker'] = '{0:f}'.format(detail.get('qty'))
                        elif detail.get('code') == 'Transfer' and detail.get('sku_type') == 'broker':
                            if '{0:f}'.format(detail.get('qty')) != '0':
                                export_detail['store_transfer_broker'] = '{0:f}'.format(detail.get('qty'))
            export_list.append(export_detail)

        title = {
            "curr": getAsiaShanghaiNow(return_str=True)
        }
        # logger.info("data: {}, title: {}".format(export_list, title))
        ret = export_service.download_files(task_id=task_id, title=title, data=export_list, partner_id=partner_id,
                                            user_id=user_id)
    # logger.info("ret: {}".format(ret))
    return ret


# --- 加工业务-实时库存导出 ---
@register_mq_task("boh_export", MessageTopic.EXPORT_MACHINING_REALTIME)
def export_manufactory_delivery_details(message):
    """ 加工业务-实时库存导出 """
    logger.info("接收到【导出加工业务-实时库存】消息:{}".format(message))
    query = message.get("query")
    partner_id = message.get("pid")
    user_id = message.get("uid")
    task_id = message.get("id")
    if query and isinstance(query, str):
        query = json.loads(query)
        # 门店id
        branch_ids = query["branch_ids"] if 'branch_ids' in query else []
        branch_ids = [int(branch_id) for branch_id in branch_ids]
        branch_type = query["branch_type"] if 'branch_type' in query else ''
        product_ids = query["product_ids"] if 'product_ids' in query else []
        product_ids = [int(product_id) for product_id in product_ids]
        category_ids = query["category_ids"] if 'category_ids' in query else []
        position_ids = query["position_ids"] if 'position_ids' in query else []
        position_ids = [int(position_id) for position_id in position_ids]
        geo_regions = query["geo_regions"] if 'geo_regions' in query else []
        geo_regions = [int(geo_region) for geo_region in geo_regions]
        sort = query["sort"] if 'sort' in query else ''
        order = query["order"] if 'order' in query else ''
        exclude = query["exclude"] if 'exclude' in query else ''

        branch_ids = branch_list_scope_check(partner_id, user_id, 'manufactory', 'boh.manufactory', branch_ids)
        if branch_ids and len(branch_ids) == 0:
            logger.info("缺少加工中心id {}".format(message))
            return True
    else:
        logger.info("缺少加工中心id {}".format(message))
        return True

    ret = False

    req = mRealtimeInventoryRequest(branch_ids=branch_ids, category_ids=category_ids,
                                    branch_type=branch_type, product_ids=product_ids,
                                    sort=sort, order=order, exclude=exclude)
    realtime_inventory_list, total = inventory_bi_service.get_realtime_inventory(request=req, partner_id=partner_id,
                                                                                 user_id=user_id)

    order_type_map = {
        "ENABLED": "启用",
        "DISABLED": "停用"
    }
    export_list = []
    if realtime_inventory_list and len(realtime_inventory_list) == 0:
        logger.info("查询不到实时库存: {}".format(message))
        return True
    if len(realtime_inventory_list) > 0:
        for realtime_inventory in realtime_inventory_list:
            export_detail = {
                "store_code": "-",
                "store_name": "-",
                "product_code": "-",
                "product_name": "-",
                "product_status": "-",
                "spec": "-",
                "qty": "0.000000",
                "accounting_unit_name": "-",
                "demand_qty": "0.000000",
                "demand_unit_name": "-",
                "demand_order_broker": '0.000000',
                "warehouse_purchase_broker": '0.000000',
                "store_transfer_broker": '0.000000'
            }
            if "category_name" in realtime_inventory:
                export_detail["category_name"] = str(realtime_inventory["category_name"])
            if "store_code" in realtime_inventory:
                export_detail["store_code"] = str(realtime_inventory["store_code"])
            if "store_name" in realtime_inventory:
                export_detail["store_name"] = str(realtime_inventory["store_name"])
            if "product_code" in realtime_inventory:
                export_detail["product_code"] = str(realtime_inventory["product_code"])
            if "product_name" in realtime_inventory:
                export_detail["product_name"] = str(realtime_inventory["product_name"])
            # if "product_status" in realtime_inventory:
            #     export_detail["product_status"] = str(order_type_map.get(realtime_inventory["product_status"]))
            if "spec" in realtime_inventory:
                export_detail["spec"] = str(realtime_inventory["spec"]) if realtime_inventory["spec"] else "-"
            if "qty" in realtime_inventory:
                export_detail["qty"] = '{0:f}'.format(realtime_inventory["qty"])
            if "accounting_unit_name" in realtime_inventory:
                export_detail["accounting_unit_name"] = str(realtime_inventory["accounting_unit_name"])
            if "demand_qty" in realtime_inventory:
                export_detail["demand_qty"] = '{0:f}'.format(realtime_inventory["demand_qty"])
            if "demand_unit_name" in realtime_inventory:
                export_detail["demand_unit_name"] = str(realtime_inventory["demand_unit_name"])
            if "extra_detail" in realtime_inventory:
                extra_detail = realtime_inventory["extra_detail"]
                if len(extra_detail) > 0:
                    for detail in extra_detail:
                        if detail.get('code') == 'DemandOrder' and detail.get('sku_type') == 'broker':
                            if '{0:f}'.format(detail.get('qty')) != '0':
                                export_detail['demand_order_broker'] = '{0:f}'.format(detail.get('qty'))
                        elif detail.get('code') == 'WarehousePurchase' and detail.get('sku_type') == 'broker':
                            if '{0:f}'.format(detail.get('qty')) != '0':
                                export_detail['warehouse_purchase_broker'] = '{0:f}'.format(detail.get('qty'))
                        elif detail.get('code') == 'Transfer' and detail.get('sku_type') == 'broker':
                            if '{0:f}'.format(detail.get('qty')) != '0':
                                export_detail['store_transfer_broker'] = '{0:f}'.format(detail.get('qty'))
            export_list.append(export_detail)

        title = {
            "curr": getAsiaShanghaiNow(return_str=True)
        }
        # logger.info("data: {}, title: {}".format(export_list, title))
        ret = export_service.download_files(task_id=task_id, title=title, data=export_list, partner_id=partner_id,
                                            user_id=user_id)
    # logger.info("ret: {}".format(ret))
    return ret


# --- 仓库运营-实时库存导出 ---
@register_mq_task("boh_export", MessageTopic.EXPORT_WAREHOUSE_REALTIME)
def export_warehouse_delivery_details(message):
    """ 加工业务-实时库存导出 """
    logger.info("接收到【导出仓库-实时库存】消息:{}".format(message))
    query = message.get("query")
    partner_id = message.get("pid")
    user_id = message.get("uid")
    task_id = message.get("id")
    if query and isinstance(query, str):
        query = json.loads(query)
        # 门店id
        branch_ids = query["branch_ids"] if 'branch_ids' in query else []
        branch_ids = [int(branch_id) for branch_id in branch_ids]
        branch_type = query["branch_type"] if 'branch_type' in query else ''
        product_ids = query["product_ids"] if 'product_ids' in query else []
        product_ids = [int(product_id) for product_id in product_ids]
        category_ids = query["category_ids"] if 'category_ids' in query else []
        position_ids = query["position_ids"] if 'position_ids' in query else []
        position_ids = [int(position_id) for position_id in position_ids]
        geo_regions = query["geo_regions"] if 'geo_regions' in query else []
        geo_regions = [int(geo_region) for geo_region in geo_regions]
        sort = query["sort"] if 'sort' in query else ''
        order = query["order"] if 'order' in query else ''
        exclude = query["exclude"] if 'exclude' in query else ''

        branch_ids = branch_list_scope_check(partner_id, user_id, 'warehouse', 'boh.warehouse', branch_ids)

        if branch_ids and len(branch_ids) == 0:
            logger.info("缺少加工中心id {}".format(message))
            return True
    else:
        logger.info("缺少加工中心id {}".format(message))
        return True

    ret = False

    req = wRealtimeInventoryRequest(branch_ids=branch_ids, category_ids=category_ids,
                                    branch_type=branch_type, product_ids=product_ids,
                                    sort=sort, order=order, exclude=exclude)
    realtime_inventory_list, total = inventory_bi_service.get_realtime_inventory(request=req, partner_id=partner_id,
                                                                                 user_id=user_id)

    order_type_map = {
        "ENABLED": "启用",
        "DISABLED": "停用"
    }
    export_list = []
    if realtime_inventory_list and len(realtime_inventory_list) == 0:
        logger.info("查询不到实时库存: {}".format(message))
        return True
    if len(realtime_inventory_list) > 0:
        for realtime_inventory in realtime_inventory_list:
            export_detail = {
                "store_code": "-",
                "store_name": "-",
                "product_code": "-",
                "product_name": "-",
                "product_status": "-",
                "spec": "-",
                "qty": "0.000000",
                "accounting_unit_name": "-",
                "demand_qty": "0.000000",
                "demand_unit_name": "-",
                "demand_order_broker": '0.000000',
                "warehouse_purchase_broker": '0.000000',
                "store_transfer_broker": '0.000000'
            }
            if "category_name" in realtime_inventory:
                export_detail["category_name"] = str(realtime_inventory["category_name"])
            if "store_code" in realtime_inventory:
                export_detail["store_code"] = str(realtime_inventory["store_code"])
            if "store_name" in realtime_inventory:
                export_detail["store_name"] = str(realtime_inventory["store_name"])
            if "product_code" in realtime_inventory:
                export_detail["product_code"] = str(realtime_inventory["product_code"])
            if "product_name" in realtime_inventory:
                export_detail["product_name"] = str(realtime_inventory["product_name"])
            # if "product_status" in realtime_inventory:
            #     export_detail["product_status"] = str(order_type_map.get(realtime_inventory["product_status"]))
            if "spec" in realtime_inventory:
                if realtime_inventory["spec"]:
                    export_detail["spec"] = str(realtime_inventory["spec"])
            if "qty" in realtime_inventory:
                export_detail["qty"] = '{0:f}'.format(realtime_inventory["qty"])
            if "accounting_unit_name" in realtime_inventory:
                export_detail["accounting_unit_name"] = str(realtime_inventory["accounting_unit_name"])
            if "demand_qty" in realtime_inventory:
                export_detail["demand_qty"] = '{0:f}'.format(realtime_inventory["demand_qty"])
            if "demand_unit_name" in realtime_inventory:
                export_detail["demand_unit_name"] = str(realtime_inventory["demand_unit_name"])
            if "extra_detail" in realtime_inventory:
                extra_detail = realtime_inventory["extra_detail"]
                if len(extra_detail) > 0:
                    for detail in extra_detail:
                        if detail.get('code') == 'DemandOrder' and detail.get('sku_type') == 'broker':
                            if '{0:f}'.format(detail.get('qty')) != '0':
                                export_detail['demand_order_broker'] = '{0:f}'.format(detail.get('qty'))
                        elif detail.get('code') == 'WarehousePurchase' and detail.get('sku_type') == 'broker':
                            if '{0:f}'.format(detail.get('qty')) != '0':
                                export_detail['warehouse_purchase_broker'] = '{0:f}'.format(detail.get('qty'))
                        elif detail.get('code') == 'Transfer' and detail.get('sku_type') == 'broker':
                            if '{0:f}'.format(detail.get('qty')) != '0':
                                export_detail['store_transfer_broker'] = '{0:f}'.format(detail.get('qty'))
            export_list.append(export_detail)

        title = {
            "curr": getAsiaShanghaiNow(return_str=True)
        }
        # logger.info("data: {}, title: {}".format(export_list, title))
        ret = export_service.download_files(task_id=task_id, title=title, data=export_list, partner_id=partner_id,
                                            user_id=user_id)
    # logger.info("ret: {}".format(ret))
    return ret


# --- 门店运营-时段汇总库存导出 ---
@register_mq_task("boh_export", MessageTopic.EXPORT_STORE_INVENTORY_SUMMARY)
def export_store_inventory_summary(message):
    logging.info("接收到【导出门店运营-时段汇总库存】消息:{}".format(message))
    query = message.get("query")
    if query and isinstance(query, str):
        query = json.loads(query)
        branch_id = int(query['branch_id']) if 'branch_id' in query else 0
        start_date = query['start_date'] if 'start_date' in query else ''
        end_date = query['end_date'] if 'end_date' in query else ''
        category_ids = list(query['category_ids']) if 'category_ids' in query else 0
        product_ids = list(query['product_ids']) if 'product_ids' in query else []
        if_pre = query['if_pre'] if 'if_pre' in query else ''
        if_end = query['if_end'] if 'if_end' in query else ''
        branch_type = query['branch_type'] if 'branch_type' in query else None

        if branch_id == 0 or start_date == '' or end_date == '':
            logging.info("缺少必选项 {}".format(message))
            return True
        start_date = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%S.%fZ")
        end_date = datetime.strptime(end_date, "%Y-%m-%dT%H:%M:%S.%fZ")
        start_date = get_timestamp(start_date)
        end_date = get_timestamp(end_date)
        category_ids = [int(id) for id in category_ids]
        product_ids = [int(id) for id in product_ids]
    else:
        logging.info("缺少query {}".format(message))
        return True
    task_id = message.get("id")
    partner_id = message.get("pid")
    user_id = message.get("uid")

    req = sSummaryInventoryRequest(branch_id=branch_id, start_date=start_date, end_date=end_date,
                                   category_ids=category_ids, product_ids=product_ids, if_pre=if_pre, if_end=if_end,
                                   branch_type=branch_type, limit=-1)
    summary_inventory_list, total = inventory_bi_service.get_summary_inventory_by_summary(req, partner_id, user_id)
    order_type_map = {
        "ENABLED": "启用",
        "DISABLED": "停用"
    }

    export_list = []
    if summary_inventory_list and len(summary_inventory_list) == 0:
        logging.info("查询不到库存: {}".format(message))
        return True
    if len(summary_inventory_list) > 0:
        for summary in summary_inventory_list:
            export_detail = {
                "store_code": "-",
                "store_name": "-",
                "product_code": "-",
                "product_name": "-",
                "product_status": "-",
                "spec": "-",
                "category1": "-",
                "category2": "-",
                "category3": "-",
                "category4": "-",
                "category5": "-",
                "category6": "-",
                "category7": "-",
                "category8": "-",
                "category9": "-",
                "category10": "-",
                "pre_qty": 0.0,
                "end_qty": 0.0,
                "sales_deposit": 0.0,
                "sales_withdraw": 0.0,
                "rec_deposit": 0.0,
                "rec_diff_deposit": 0.0,
                "spick_deposit": 0.0,
                "ret_withdraw": 0.0,
                "material_trans_deposit": 0.0,
                "material_trans_withdraw": 0.0,
                "trans_deposit": 0.0,
                "trans_withdraw": 0.0,
                "stoc_deposit": 0.0,
                "stoc_withdraw": 0.0,
                "adj_withdraw": 0.0,
                "trans_delivery": 0.0,
                "trans_transfer": 0.0,
                "trans_purchase": 0.0,
                "ret_transfer": 0.0,
                "ret_transfer_release": 0.0,
                "trans_return_release": 0.0,
                "trans_transfer_release": 0.0,
                "trans_delivery_release": 0.0,
                "trans_purchase_release": 0.0,
                "trans_begin": 0.0,
                "trans_end": 0.0,
                "start_time": "-",
                "end_time": "-"
            }
            if "accounting_unit_name" in summary:
                export_detail["accounting_unit_name"] = str(summary["accounting_unit_name"]) if summary[
                    'accounting_unit_name'] else '-'
            if "store_code" in summary:
                export_detail["store_code"] = str(summary["store_code"]) if summary['store_code'] else '-'
            if "store_name" in summary:
                export_detail["store_name"] = str(summary["store_name"]) if summary['store_name'] else '-'
            if "product_code" in summary:
                export_detail["product_code"] = str(summary["product_code"]) if summary["product_code"] else '-'
            if 'product_name' in summary:
                export_detail['product_name'] = str(summary['product_name']) if summary['product_name'] else '-'
            if 'product_status' in summary:
                export_detail['product_status'] = str(order_type_map.get(summary['product_status'])) if summary[
                    'product_status'] else '-'
            if 'spec' in summary:
                export_detail['spec'] = str(summary['spec']) if summary['spec'] else '-'
            if 'category1' in summary:
                export_detail['category1'] = str(summary['category1']) if summary['category1'] else '-'
            if 'category2' in summary:
                export_detail['category2'] = str(summary['category2']) if summary['category2'] else '-'
            if 'category3' in summary:
                export_detail['category3'] = str(summary['category3']) if summary['category3'] else '-'
            if 'category4' in summary:
                export_detail['category4'] = str(summary['category4']) if summary['category4'] else '-'
            if 'category5' in summary:
                export_detail['category5'] = str(summary['category5']) if summary['category5'] else '-'
            if 'category6' in summary:
                export_detail['category6'] = str(summary['category6']) if summary['category6'] else '-'
            if 'category7' in summary:
                export_detail['category7'] = str(summary['category7']) if summary['category7'] else '-'
            if 'category8' in summary:
                export_detail['category8'] = str(summary['category8']) if summary['category8'] else '-'
            if 'category9' in summary:
                export_detail['category9'] = str(summary['category9']) if summary['category9'] else '-'
            if 'category10' in summary:
                export_detail['category10'] = str(summary['category10']) if summary['category10'] else '-'
            if 'pre_qty' in summary:
                export_detail['pre_qty'] = str(summary['pre_qty']) if summary['pre_qty'] else 0.0
            if 'qty' in summary:
                export_detail['end_qty'] = str(summary['qty']) if summary['qty'] else 0.0
            if 'sales_deposit' in summary:
                export_detail['sales_deposit'] = float(summary['sales_deposit']) if summary['sales_deposit'] else 0.0
            if 'sales_withdraw' in summary:
                export_detail['sales_withdraw'] = float(summary['sales_withdraw']) if summary['sales_withdraw'] else 0.0
            if 'rec_deposit' in summary:
                export_detail['rec_deposit'] = float(summary['rec_deposit']) if summary['rec_deposit'] else 0.0
            if 'rec_diff_deposit' in summary:
                export_detail['rec_diff_deposit'] = float(summary['rec_diff_deposit']) if summary[
                    'rec_diff_deposit'] else 0.0
            if 'spick_deposit' in summary:
                export_detail['spick_deposit'] = float(summary['spick_deposit']) if summary['spick_deposit'] else '0.0'
            if 'spick_deposit' in summary:
                export_detail['spick_deposit'] = float(summary['spick_deposit']) if summary['spick_deposit'] else '0.0'
            if 'material_trans_deposit' in summary:
                export_detail['material_trans_deposit'] = float(summary['material_trans_deposit']) if summary[
                    'material_trans_deposit'] else 0.0
            if 'material_trans_withdraw' in summary:
                export_detail['material_trans_withdraw'] = float(summary['material_trans_withdraw']) if summary[
                    'material_trans_withdraw'] else 0.0
            if 'trans_deposit' in summary:
                export_detail['trans_deposit'] = float(summary['trans_deposit']) if summary['trans_deposit'] else 0.0
            if 'trans_withdraw' in summary:
                export_detail['trans_withdraw'] = float(summary['trans_withdraw']) if summary['trans_withdraw'] else 0.0
            if 'stoc_deposit' in summary:
                export_detail['stoc_deposit'] = float(summary['stoc_deposit']) if summary['stoc_deposit'] else 0.0
            if 'stoc_withdraw' in summary:
                export_detail['stoc_withdraw'] = float(summary['stoc_withdraw']) if summary['stoc_withdraw'] else 0.0
            if 'ret_withdraw' in summary:
                export_detail['ret_withdraw'] = float(summary['ret_withdraw']) if summary['ret_withdraw'] else 0.0
            if 'adj_withdraw' in summary:
                export_detail['adj_withdraw'] = float(summary['adj_withdraw']) if summary['adj_withdraw'] else 0.0
            if 'trans_transfer' in summary:
                export_detail['trans_transfer'] = float(summary['trans_transfer']) if summary['trans_transfer'] else 0.0
            if 'trans_delivery' in summary:
                export_detail['trans_delivery'] = float(summary['trans_delivery']) if summary['trans_delivery'] else 0.0
            if 'trans_purchase' in summary:
                export_detail['trans_purchase'] = float(summary['trans_purchase']) if summary['trans_purchase'] else 0.0
            if 'trans_return_release' in summary:
                export_detail['trans_return_release'] = float(summary['trans_return_release']) if \
                    summary['trans_return_release'] else 0.0
            if 'trans_transfer_release' in summary:
                export_detail['trans_transfer_release'] = float(summary['trans_transfer_release']) if \
                    summary['trans_transfer_release'] else 0.0
            if 'trans_delivery_release' in summary:
                export_detail['trans_delivery_release'] = float(summary['trans_delivery_release']) if \
                    summary['trans_delivery_release'] else 0.0
            if 'trans_purchase_release' in summary:
                export_detail['trans_purchase_release'] = float(summary['trans_purchase_release']) if \
                    summary['trans_purchase_release'] else 0.0
            if 'trans_begin' in summary:
                export_detail['trans_begin'] = float(summary['trans_begin']) if summary['trans_begin'] else 0.0
            if 'trans_end' in summary:
                export_detail['trans_end'] = float(summary['trans_end']) if summary['trans_end'] else 0.0
            if 'ret_transfer' in summary:
                export_detail['ret_transfer'] = float(summary['ret_transfer']) if summary['ret_transfer'] else 0.0
            if 'ret_transfer_release' in summary:
                export_detail['ret_transfer'] = float(summary['ret_transfer']) if summary['ret_transfer'] else 0.0
            if 'start_time' in summary:
                export_detail['start_time'] = time.strftime("%Y-%m-%d %H:%M:%S",
                                                            time.localtime(
                                                                summary['start_time'].seconds + 8 * 60 * 60)) if \
                    summary['start_time'] else "-"
            if 'end_time' in summary:
                export_detail['end_time'] = time.strftime("%Y-%m-%d %H:%M:%S",
                                                          time.localtime(summary['end_time'].seconds + 8 * 60 * 60)) if \
                    summary['end_time'] else "-"
            export_list.append(export_detail)

        title = {
            "curr": getAsiaShanghaiNow(return_str=True)
        }
        # logging.info("data: {}, title: {}".format(export_list, title))
        ret = export_service.download_files(task_id=task_id, title=title, data=export_list, partner_id=partner_id,
                                            user_id=user_id)
        # logging.info("ret: {}".format(ret))
    return True


# --- 加工业务-时段汇总库存导出 ---
@register_mq_task("boh_export", MessageTopic.EXPORT_MACHINING_INVENTORY_SUMMARY)
def export_manufactory_inventory_summary(message):
    logging.info("接收到【导出加工业务-时段汇总库存】消息:{}".format(message))
    query = message.get("query")
    if query and isinstance(query, str):
        query = json.loads(query)
        branch_id = int(query['branch_id']) if 'branch_id' in query else 0
        start_date = query['start_date'] if 'start_date' in query else ''
        end_date = query['end_date'] if 'end_date' in query else ''
        category_ids = list(query['category_ids']) if 'category_ids' in query else 0
        product_ids = list(query['product_ids']) if 'product_ids' in query else []
        if_pre = query['if_pre'] if 'if_pre' in query else ''
        if_end = query['if_end'] if 'if_end' in query else ''
        branch_type = query['branch_type'] if 'branch_type' in query else None

        if branch_id == 0 or start_date == '' or end_date == '':
            logging.info("缺少必选项 {}".format(message))
            return True
        start_date = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%S.%fZ")
        end_date = datetime.strptime(end_date, "%Y-%m-%dT%H:%M:%S.%fZ")
        start_date = get_timestamp(start_date)
        end_date = get_timestamp(end_date)
        category_ids = [int(id) for id in category_ids]
        product_ids = [int(id) for id in product_ids]
    else:
        logging.info("缺少query {}".format(message))
        return True
    task_id = message.get("id")
    partner_id = message.get("pid")
    user_id = message.get("uid")
    req = mSummaryInventoryRequest(branch_id=branch_id, product_ids=product_ids, category_ids=category_ids,
                                   start_date=start_date, end_date=end_date, if_pre=if_pre, if_end=if_end,
                                   branch_type=branch_type, limit=-1)
    summary_inventory_list, total = inventory_bi_service.get_summary_inventory_by_summary(req, partner_id, user_id)
    order_type_map = {
        "ENABLED": "启用",
        "DISABLED": "停用"
    }

    export_list = []
    if summary_inventory_list and len(summary_inventory_list) == 0:
        logging.info("查询不到库存: {}".format(message))
        return True
    if len(summary_inventory_list) > 0:
        for summary in summary_inventory_list:
            export_detail = {
                "machining_center_code": "-",
                "machining_center_name": "-",
                "product_code": "-",
                "product_name": "-",
                "product_status": "-",
                "spec": "-",
                "category1": "-",
                "category2": "-",
                "category3": "-",
                "category4": "-",
                "category5": "-",
                "category6": "-",
                "category7": "-",
                "category8": "-",
                "category9": "-",
                "category10": "-",
                "pre_qty": 0.0,
                "end_qty": 0.0,
                "rec_withdraw": 0.0,
                "rec_diff_withdraw": 0.0,
                "rec_diff_deposit": 0.0,
                "ret_deposit": 0.0,
                "purchase_deposit": 0.0,
                "ret_withdraw": 0.0,
                "packing_deposit": 0.0,
                "packing_withdraw": 0.0,
                "material_trans_deposit": 0.0,
                "material_trans_withdraw": 0.0,
                "processing_deposit": 0.0,
                "processing_withdraw": 0.0,
                "trans_deposit": 0.0,
                "trans_withdraw": 0.0,
                "stoc_deposit": 0.0,
                "stoc_withdraw": 0.0,
                "adj_withdraw": 0.0,
                "trans_transfer": 0.0,
                "trans_delivery": 0.0,
                "trans_purchase": 0.0,
                "trans_return_release": 0.0,
                "trans_transfer_release": 0.0,
                "trans_delivery_release": 0.0,
                "trans_purchase_release": 0.0,
                "trans_begin": 0.0,
                "trans_end": 0.0,
                "start_time": "-",
                "end_time": "-"
            }
            if "accounting_unit_name" in summary:
                export_detail["accounting_unit_name"] = str(summary["accounting_unit_name"]) if summary[
                    'accounting_unit_name'] else '-'
            if "store_code" in summary:
                export_detail["machining_center_code"] = str(summary["store_code"]) if summary['store_code'] else '-'
            if "store_name" in summary:
                export_detail["machining_center_name"] = str(summary["store_name"]) if summary['store_name'] else '-'
            if "product_code" in summary:
                export_detail["product_code"] = str(summary["product_code"]) if summary["product_code"] else '-'
            if 'product_name' in summary:
                export_detail['product_name'] = str(summary['product_name']) if summary['product_name'] else '-'
            if 'product_status' in summary:
                export_detail['product_status'] = str(order_type_map.get(summary['product_status'])) if summary[
                    'product_status'] else '-'
            if 'spec' in summary:
                export_detail['spec'] = str(summary['spec']) if summary['spec'] else '-'
            if 'category1' in summary:
                export_detail['category1'] = str(summary['category1']) if summary['category1'] else '-'
            if 'category2' in summary:
                export_detail['category2'] = str(summary['category2']) if summary['category2'] else '-'
            if 'category3' in summary:
                export_detail['category3'] = str(summary['category3']) if summary['category3'] else '-'
            if 'category4' in summary:
                export_detail['category4'] = str(summary['category4']) if summary['category4'] else '-'
            if 'category5' in summary:
                export_detail['category5'] = str(summary['category5']) if summary['category5'] else '-'
            if 'category6' in summary:
                export_detail['category6'] = str(summary['category6']) if summary['category6'] else '-'
            if 'category7' in summary:
                export_detail['category7'] = str(summary['category7']) if summary['category7'] else '-'
            if 'category8' in summary:
                export_detail['category8'] = str(summary['category8']) if summary['category8'] else '-'
            if 'category9' in summary:
                export_detail['category9'] = str(summary['category9']) if summary['category9'] else '-'
            if 'category10' in summary:
                export_detail['category10'] = str(summary['category10']) if summary['category10'] else '-'
            if 'pre_qty' in summary:
                export_detail['pre_qty'] = str(summary['pre_qty']) if summary['pre_qty'] else 0.0
            if 'qty' in summary:
                export_detail['end_qty'] = str(summary['qty']) if summary['qty'] else 0.0
            if 'rec_withdraw' in summary:
                export_detail['rec_withdraw'] = float(summary['rec_withdraw']) if summary['rec_withdraw'] else 0.0
            if 'rec_diff_withdraw' in summary:
                export_detail['rec_diff_withdraw'] = float(summary['rec_diff_withdraw']) if summary[
                    'rec_diff_withdraw'] else 0.0
            if 'rec_diff_deposit' in summary:
                export_detail['rec_diff_deposit'] = float(summary['rec_diff_deposit']) if summary[
                    'rec_diff_deposit'] else 0.0
            if 'purchase_deposit' in summary:
                export_detail['purchase_deposit'] = float(summary['purchase_deposit']) if summary[
                    'purchase_deposit'] else '0.0'
            if 'ret_withdraw' in summary:
                export_detail['ret_withdraw'] = float(summary['ret_withdraw']) if summary['ret_withdraw'] else 0.0
            if 'packing_deposit' in summary:
                export_detail['packing_deposit'] = float(summary['packing_deposit']) if summary[
                    'packing_deposit'] else 0.0
            if 'packing_withdraw' in summary:
                export_detail['packing_withdraw'] = float(summary['packing_withdraw']) if summary[
                    'packing_withdraw'] else 0.0
            if 'material_trans_deposit' in summary:
                export_detail['material_trans_deposit'] = float(summary['material_trans_deposit']) if summary[
                    'material_trans_deposit'] else 0.0
            if 'material_trans_withdraw' in summary:
                export_detail['material_trans_withdraw'] = float(summary['material_trans_withdraw']) if summary[
                    'material_trans_withdraw'] else 0.0
            if 'processing_deposit' in summary:
                export_detail['processing_deposit'] = float(summary['processing_deposit']) if summary[
                    'processing_deposit'] else 0.0
            if 'processing_withdraw' in summary:
                export_detail['processing_withdraw'] = float(summary['processing_withdraw']) if summary[
                    'processing_withdraw'] else 0.0
            if 'trans_deposit' in summary:
                export_detail['trans_deposit'] = float(summary['trans_deposit']) if summary['trans_deposit'] else 0.0
            if 'trans_withdraw' in summary:
                export_detail['trans_withdraw'] = float(summary['trans_withdraw']) if summary['trans_withdraw'] else 0.0
            if 'stoc_deposit' in summary:
                export_detail['stoc_deposit'] = float(summary['stoc_deposit']) if summary['stoc_deposit'] else 0.0
            if 'stoc_deposit' in summary:
                export_detail['stoc_deposit'] = float(summary['stoc_deposit']) if summary['stoc_deposit'] else 0.0
            if 'stoc_withdraw' in summary:
                export_detail['stoc_withdraw'] = float(summary['stoc_withdraw']) if summary['stoc_withdraw'] else 0.0
            if 'adj_withdraw' in summary:
                export_detail['adj_withdraw'] = float(summary['adj_withdraw']) if summary['adj_withdraw'] else 0.0
            if 'ret_deposit' in summary:
                export_detail['ret_deposit'] = float(summary['ret_deposit']) if summary['ret_deposit'] else 0.0
            if 'trans_transfer' in summary:
                export_detail['trans_transfer'] = float(summary['trans_transfer']) if summary['trans_transfer'] else 0.0
            if 'trans_delivery' in summary:
                export_detail['trans_delivery'] = float(summary['trans_delivery']) if summary['trans_delivery'] else 0.0
            if 'trans_purchase' in summary:
                export_detail['trans_purchase'] = float(summary['trans_purchase']) if summary['trans_purchase'] else 0.0
            if 'trans_return_release' in summary:
                export_detail['trans_return_release'] = float(summary['trans_return_release']) if \
                    summary['trans_return_release'] else 0.0
            if 'trans_transfer_release' in summary:
                export_detail['trans_transfer_release'] = float(summary['trans_transfer_release']) if \
                    summary['trans_transfer_release'] else 0.0
            if 'trans_delivery_release' in summary:
                export_detail['trans_delivery_release'] = float(summary['trans_delivery_release']) if \
                    summary['trans_delivery_release'] else 0.0
            if 'trans_purchase_release' in summary:
                export_detail['trans_purchase_release'] = float(summary['trans_purchase_release']) if \
                    summary['trans_purchase_release'] else 0.0
            if 'trans_begin' in summary:
                export_detail['trans_begin'] = float(summary['trans_begin']) if summary['trans_begin'] else 0.0
            if 'trans_end' in summary:
                export_detail['trans_end'] = float(summary['trans_end']) if summary['trans_end'] else 0.0
            if 'start_time' in summary:
                export_detail['start_time'] = time.strftime("%Y-%m-%d %H:%M:%S",
                                                            time.localtime(
                                                                summary['start_time'].seconds + 8 * 60 * 60)) if \
                    summary['start_time'] else "-"
            if 'end_time' in summary:
                export_detail['end_time'] = time.strftime("%Y-%m-%d %H:%M:%S",
                                                          time.localtime(summary['end_time'].seconds + 8 * 60 * 60)) if \
                    summary['end_time'] else "-"
            export_list.append(export_detail)

        title = {
            "curr": getAsiaShanghaiNow(return_str=True)
        }
        # logging.info("data: {}, title: {}".format(export_list, title))
        ret = export_service.download_files(task_id=task_id, title=title, data=export_list, partner_id=partner_id,
                                            user_id=user_id)
        # logging.info("ret: {}".format(ret))
    return True


# --- 仓库运营-时段汇总库存导出 ---
@register_mq_task("boh_export", MessageTopic.EXPORT_WAREHOUSE_INVENTORY_SUMMARY)
def export_warehouse_inventory_summary(message):
    logging.info("接收到【导出仓库运营-时段汇总库存】消息:{}".format(message))
    query = message.get("query")
    if query and isinstance(query, str):
        query = json.loads(query)
        branch_id = int(query['branch_id']) if 'branch_id' in query else 0
        start_date = query['start_date'] if 'start_date' in query else ''
        end_date = query['end_date'] if 'end_date' in query else ''
        category_ids = list(query['category_ids']) if 'category_ids' in query else 0
        product_ids = list(query['product_ids']) if 'product_ids' in query else []
        if_pre = query['if_pre'] if 'if_pre' in query else ''
        if_end = query['if_end'] if 'if_end' in query else ''
        branch_type = query['branch_type'] if 'branch_type' in query else None

        if branch_id == 0 or start_date == '' or end_date == '':
            logging.info("缺少必选项 {}".format(message))
            return True
        start_date = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%S.%fZ")
        end_date = datetime.strptime(end_date, "%Y-%m-%dT%H:%M:%S.%fZ")
        start_date = get_timestamp(start_date)
        end_date = get_timestamp(end_date)
        category_ids = [int(id) for id in category_ids]
        product_ids = [int(id) for id in product_ids]
    else:
        logging.info("缺少query {}".format(message))
        return True
    task_id = message.get("id")
    partner_id = message.get("pid")
    user_id = message.get("uid")

    req = wSummaryInventoryRequest(branch_id=branch_id, category_ids=category_ids, product_ids=product_ids,
                                   start_date=start_date, end_date=end_date, if_pre=if_pre, if_end=if_end,
                                   branch_type=branch_type, limit=-1)
    summary_inventory_list, total = inventory_bi_service.get_summary_inventory_by_summary(req, partner_id, user_id)
    order_type_map = {
        "ENABLED": "启用",
        "DISABLED": "停用"
    }

    export_list = []
    if summary_inventory_list and len(summary_inventory_list) == 0:
        logging.info("查询不到库存: {}".format(message))
        return True
    if len(summary_inventory_list) > 0:
        for summary in summary_inventory_list:
            export_detail = {
                "warehouse_code": "-",
                "warehouse_name": "-",
                "product_code": "-",
                "product_name": "-",
                "product_status": "-",
                "spec": "-",
                "category1": "-",
                "category2": "-",
                "category3": "-",
                "category4": "-",
                "category5": "-",
                "category6": "-",
                "category7": "-",
                "category8": "-",
                "category9": "-",
                "category10": "-",
                "pre_qty": 0.0,
                "end_qty": 0.0,
                "rec_withdraw": 0.0,
                "rec_diff_withdraw": 0.0,
                "rec_diff_deposit": 0.0,
                "ret_deposit": 0.0,
                "purchase_deposit": 0.0,
                "ret_withdraw": 0.0,
                "material_trans_deposit": 0.0,
                "material_trans_withdraw": 0.0,
                "trans_deposit": 0.0,
                "trans_withdraw": 0.0,
                "stoc_deposit": 0.0,
                "stoc_withdraw": 0.0,
                "adj_withdraw": 0.0,
                "trans_transfer": 0.0,
                "trans_delivery": 0.0,
                "trans_purchase": 0.0,
                "trans_return_release": 0.0,
                "trans_transfer_release": 0.0,
                "trans_delivery_release": 0.0,
                "trans_purchase_release": 0.0,
                "trans_begin": 0.0,
                "trans_end": 0.0,
                "start_time": "-",
                "end_time": "-"
            }
            if "accounting_unit_name" in summary:
                export_detail["accounting_unit_name"] = str(summary["accounting_unit_name"]) if summary[
                    'accounting_unit_name'] else '-'
            if "store_code" in summary:
                export_detail["warehouse_code"] = str(summary["store_code"]) if summary['store_code'] else '-'
            if "store_name" in summary:
                export_detail["warehouse_name"] = str(summary["store_name"]) if summary['store_name'] else '-'
            if "product_code" in summary:
                export_detail["product_code"] = str(summary["product_code"]) if summary["product_code"] else '-'
            if 'product_name' in summary:
                export_detail['product_name'] = str(summary['product_name']) if summary['product_name'] else '-'
            if 'product_status' in summary:
                export_detail['product_status'] = str(order_type_map.get(summary['product_status'])) if summary[
                    'product_status'] else '-'
            if 'spec' in summary:
                export_detail['spec'] = str(summary['spec']) if summary['spec'] else '-'
            if 'category1' in summary:
                export_detail['category1'] = str(summary['category1']) if summary['category1'] else '-'
            if 'category2' in summary:
                export_detail['category2'] = str(summary['category2']) if summary['category2'] else '-'
            if 'category3' in summary:
                export_detail['category3'] = str(summary['category3']) if summary['category3'] else '-'
            if 'category4' in summary:
                export_detail['category4'] = str(summary['category4']) if summary['category4'] else '-'
            if 'category5' in summary:
                export_detail['category5'] = str(summary['category5']) if summary['category5'] else '-'
            if 'category6' in summary:
                export_detail['category6'] = str(summary['category6']) if summary['category6'] else '-'
            if 'category7' in summary:
                export_detail['category7'] = str(summary['category7']) if summary['category7'] else '-'
            if 'category8' in summary:
                export_detail['category8'] = str(summary['category8']) if summary['category8'] else '-'
            if 'category9' in summary:
                export_detail['category9'] = str(summary['category9']) if summary['category9'] else '-'
            if 'category10' in summary:
                export_detail['category10'] = str(summary['category10']) if summary['category10'] else '-'
            if 'pre_qty' in summary:
                export_detail['pre_qty'] = str(summary['pre_qty']) if summary['pre_qty'] else 0.0
            if 'qty' in summary:
                export_detail['end_qty'] = str(summary['qty']) if summary['qty'] else 0.0
            if 'rec_withdraw' in summary:
                export_detail['rec_withdraw'] = float(summary['rec_withdraw']) if summary['rec_withdraw'] else 0.0
            if 'rec_diff_withdraw' in summary:
                export_detail['rec_diff_withdraw'] = float(summary['rec_diff_withdraw']) if summary[
                    'rec_diff_withdraw'] else 0.0
            if 'rec_diff_deposit' in summary:
                export_detail['rec_diff_deposit'] = float(summary['rec_diff_deposit']) if summary[
                    'rec_diff_deposit'] else 0.0
            if 'ret_deposit' in summary:
                export_detail['ret_deposit'] = float(summary['ret_deposit']) if summary['ret_deposit'] else '0.0'
            if 'purchase_deposit' in summary:
                export_detail['purchase_deposit'] = float(summary['purchase_deposit']) if summary[
                    'purchase_deposit'] else '0.0'
            if 'ret_withdraw' in summary:
                export_detail['ret_withdraw'] = float(summary['ret_withdraw']) if summary['ret_withdraw'] else 0.0
            if 'material_trans_deposit' in summary:
                export_detail['material_trans_deposit'] = float(summary['material_trans_deposit']) if summary[
                    'material_trans_deposit'] else 0.0
            if 'material_trans_withdraw' in summary:
                export_detail['material_trans_withdraw'] = float(summary['material_trans_withdraw']) if summary[
                    'material_trans_withdraw'] else 0.0
            if 'trans_deposit' in summary:
                export_detail['trans_deposit'] = float(summary['trans_deposit']) if summary['trans_deposit'] else 0.0
            if 'trans_withdraw' in summary:
                export_detail['trans_withdraw'] = float(summary['trans_withdraw']) if summary['trans_withdraw'] else 0.0
            if 'stoc_deposit' in summary:
                export_detail['stoc_deposit'] = float(summary['stoc_deposit']) if summary['stoc_deposit'] else 0.0
            if 'stoc_withdraw' in summary:
                export_detail['stoc_withdraw'] = float(summary['stoc_withdraw']) if summary['stoc_withdraw'] else 0.0
            if 'adj_withdraw' in summary:
                export_detail['adj_withdraw'] = float(summary['adj_withdraw']) if summary['adj_withdraw'] else 0.0
            if 'trans_transfer' in summary:
                export_detail['trans_transfer'] = float(summary['trans_transfer']) if summary['trans_transfer'] else 0.0
            if 'trans_delivery' in summary:
                export_detail['trans_delivery'] = float(summary['trans_delivery']) if summary['trans_delivery'] else 0.0
            if 'trans_purchase' in summary:
                export_detail['trans_purchase'] = float(summary['trans_purchase']) if summary['trans_purchase'] else 0.0
            if 'trans_return_release' in summary:
                export_detail['trans_return_release'] = float(summary['trans_return_release']) if \
                    summary['trans_return_release'] else 0.0
            if 'trans_transfer_release' in summary:
                export_detail['trans_transfer_release'] = float(summary['trans_transfer_release']) if \
                    summary['trans_transfer_release'] else 0.0
            if 'trans_delivery_release' in summary:
                export_detail['trans_delivery_release'] = float(summary['trans_delivery_release']) if \
                    summary['trans_delivery_release'] else 0.0
            if 'trans_purchase_release' in summary:
                export_detail['trans_purchase_release'] = float(summary['trans_purchase_release']) if \
                    summary['trans_purchase_release'] else 0.0
            if 'trans_begin' in summary:
                export_detail['trans_begin'] = float(summary['trans_begin']) if summary['trans_begin'] else 0.0
            if 'trans_end' in summary:
                export_detail['trans_end'] = float(summary['trans_end']) if summary['trans_end'] else 0.0
            if 'start_time' in summary:
                export_detail['start_time'] = time.strftime("%Y-%m-%d %H:%M:%S",
                                                            time.localtime(
                                                                summary['start_time'].seconds + 8 * 60 * 60)) if \
                    summary['start_time'] else "-"
            if 'end_time' in summary:
                export_detail['end_time'] = time.strftime("%Y-%m-%d %H:%M:%S",
                                                          time.localtime(summary['end_time'].seconds + 8 * 60 * 60)) if \
                    summary['end_time'] else "-"
            export_list.append(export_detail)

        title = {
            "curr": getAsiaShanghaiNow(return_str=True)
        }
        # logging.info("data: {}, title: {}".format(export_list, title))
        ret = export_service.download_files(task_id=task_id, title=title, data=export_list, partner_id=partner_id,
                                            user_id=user_id)
        # logging.info("ret: {}".format(ret))
    return True

### --- 订货单明细导出 --- ###
@register_mq_task("boh_export", MessageTopic.EXPORT_DEMAND_DETAILS)
def export_demand_details(message):
    """
        导出订货单列表带明细
    """

    logging.info("接收到【导出订货单明细】消息:{}".format(message))

    query = message.get("query")
    if query and isinstance(query, str):
        query = eval(query)

        start_date = query.get("start_date")
        if start_date and isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%dT%H:%M:%S.000Z')

        end_date = query.get("end_date")
        if end_date and isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%dT%H:%M:%S.000Z')

        store_ids = query.get("store_ids")
        status = query.get("status")
        has_product = query.get("has_product")
        product_ids = query.get("product_ids")
        types = query.get("types")
        plan_name = query.get("plan_name")
        if plan_name:
            plan_name = unquote(plan_name)

    else:
        logging.info("导出订货单列表缺少参数 {}".format(message))
        return True

    task_id = message.get("id")
    partner_id = message.get("pid")
    user_id = message.get("uid")

    store_ids = branch_list_scope_check(partner_id, user_id, 'store', 'boh.store', store_ids)

    export_list = demand_module().list_demand_with_products(partner_id=partner_id, user_id=user_id, \
                                                            start_date=start_date, end_date=end_date, \
                                                            status=status, store_ids=store_ids, has_product=has_product,
                                                            types=types, \
                                                            plan_name=plan_name, product_ids=product_ids)

    if export_list and len(export_list) > 0:
        title = {
            "start_date": (start_date + timedelta(hours=8)).strftime("%Y-%m-%d"),
            "end_date": (end_date + timedelta(hours=8)).strftime("%Y-%m-%d")
        }
        ret = export_service.download_files(task_id=task_id, title=title, data=export_list, partner_id=partner_id,
                                            user_id=user_id)
        return ret
    else:
        logging.info("导出订货单列表数据为空 {}".format(message))
        return True
