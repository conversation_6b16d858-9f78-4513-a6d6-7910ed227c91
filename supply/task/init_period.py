import time
import pytz
import traceback
import json
from supply import logger
from dateutil import rrule, parser
from . import partner_task_distribution
from datetime import datetime, timedelta
from supply.driver.mq import register_mq_task
from supply.utils.helper import MessageTopic, get_uuids
from supply.client.infra_metadata_service import metadata_center_service
from supply.model.task_batch.period_init_batch import PeriodInitBatch


def get_safe_value(data: dict, key, default_value):
    if data.get(key) and isinstance(data.get(key), type(default_value)):
        return data.get(key)
    else:
        return default_value


def get_utc_datetime_str(date_str: str):
    utc_datetime_str = ''
    try:
        utc_datetime = parser.parse(date_str) - timedelta(hours=8)
        utc_datetime = utc_datetime.astimezone(pytz.timezone('UTC'))
        utc_datetime_str = datetime.strftime(utc_datetime, '%Y-%m-%dT%H:%M:%SZ')
    except Exception as e:
        logger.error("convert utc datetime str Error: {}".format(e))
    return utc_datetime_str


def getTheMonth(now_date: datetime, n):
    """获取当前日期的前几个月
    :param now_date: 当前日期
    :param n: 向前推的月份
    :return: "YYYY-mm"
    """
    month = now_date.month
    year = now_date.year
    for i in range(n):
        if month == 1:
            year -= 1
            month = 12
        else:
            month -= 1
    return datetime(year, month, 1).strftime("%Y-%m")


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.INIT_PERIOD_TOPIC)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.INIT_PERIOD_TOPIC)
def init_period_task_main(message):
    """
    saas初始化账期(注意要调用租户平台的主档)
    1、获取任务配置的所有租户ID，处理每一个租户(在装饰器中处理)
    2、查询账期设置判断是否为`自然月`
    3、如果为自然月再判断当前账期是否已经初始化
    4、如未初始化一次性初始化一年的账期数据
    5、如当前日期距离账期最后一天小于一周，开始初始化下一年的账期
    """
    logger.info("Received INIT_PERIOD_TASK_MAIN: {}".format(message))
    partner_id = message['partner_id'] if 'partner_id' in message else None
    user_id = message['user_id'] if 'user_id' in message else None
    if not partner_id or not user_id:
        logger.error('INIT_PERIOD_TASK_MAIN: not partner_id or not user_id')
        return True
    period_set = metadata_center_service.list_entity(schema_name="PERIOD_SET", partner_id=partner_id,
                                                     user_id=user_id).get('rows', [])
    if not period_set:
        logger.warning('INIT_PERIOD_BATCH_TASK: Not found period set')
        return True
    try:
        fields = get_safe_value(period_set[0], 'fields', {})
        period_type = fields.get('period_type')
        if period_type == "MONTH":
            period_list = metadata_center_service.list_entity(schema_name="PERIOD", partner_id=partner_id, order="asc",
                                                              sort="end_day", user_id=user_id).get('rows', [])
            if not period_list:
                # 初始化当前一年的账期, 并向前推6个月
                start_month = getTheMonth(datetime.now(), 6)
                init_period(year=int(datetime.now().year), start_month=start_month, partner_id=partner_id,
                            user_id=user_id)
            else:
                last_period = period_list[-1]
                fields = get_safe_value(last_period, 'fields', {})
                end_datetime = fields.get('end_datetime')
                if end_datetime:
                    end_datetime = datetime.strptime(end_datetime, "%Y-%m-%dT%H:%M:%SZ")
                    if (end_datetime - datetime.utcnow()).days <= 7:
                        # 初始化下一年的账期
                        next_year = (end_datetime + timedelta(days=1)).year  # 将账期结束日期+1跳到下一年
                        init_period(year=int(next_year), partner_id=partner_id, user_id=user_id)
    except Exception as e:
        logger.error(e)
        traceback.print_exc()
    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_PERIOD_BATCH_TOPIC)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_PERIOD_BATCH_TOPIC)
def recover_init_period_batch(message):
    logger.info("Received RECOVER_INIT_PERIOD_BATCH: {}".format(message))
    partner_id = message['partner_id'] if 'partner_id' in message else None
    user_id = message['user_id'] if 'user_id' in message else None
    if not partner_id or not user_id:
        logger.error('RECOVER_INIT_PERIOD_BATCH: not partner_id or not user_id')
        return True
    periods_batch = PeriodInitBatch.list_period_batch(partner_id=partner_id, status=["INITED", "FAILED"])
    if periods_batch:
        for pb in periods_batch:
            period = json.loads(pb.content) if pb.content else {}
            filters = {"start_datetime": period.get('start_datetime'),
                       "end_datetime": period.get('end_datetime')}
            exist_period = metadata_center_service.list_entity(schema_name="PERIOD", partner_id=pb.partner_id,
                                                               filters=filters, user_id=user_id).get('rows', [])
            if len(exist_period) > 0:
                PeriodInitBatch.update_period(partner_id=partner_id, update_data=dict(id=pb.id,
                                                                                      process_status="SUCCESS"))
                continue
            try:
                res = metadata_center_service.add_entity(schema_name="PERIOD", auto_enable=True, fields=period,
                                                         partner_id=partner_id, user_id=user_id)
                logger.info("{} 账期初始化结果: {}".format(period.get('name'), res.get('id')))
            except Exception as e:
                logger.warning("request metadata add period error: {}".format(e))
                PeriodInitBatch.update_period(partner_id=partner_id, update_data=dict(id=pb.id,
                                                                                      process_status="FAILED"))
                continue
            if res.get('id'):
                PeriodInitBatch.update_period(partner_id=partner_id, update_data=dict(id=pb.id,
                                                                                      process_status="SUCCESS"))
            logger.info("租户{} 初始化账期补偿结果: {}".format(partner_id, res.get('id')))
    return True


def init_period(year: int, partner_id, user_id, start_month=None):
    if not start_month:
        start_month = str(year) + "-01"
    end_month = str(year + 1) + "-01"
    logger.info("正在初始化租户{}的{} - {} 账期...".format(partner_id, start_month, end_month))
    month_list = getMonthRangList(start_month, end_month)
    period_data = build_period_data(month_list)
    period_ids = get_uuids(len(period_data))
    for inx, period in enumerate(period_data):
        # 校验当前账期是否已经创建
        # 先往批次控制表里插入本条记录，如果成功就请求主档，失败直接跳过
        not_exist = PeriodInitBatch.add_one(period=dict(
            id=period_ids[inx],
            partner_id=partner_id, 
            process_status="INITED", 
            period_name=period.get('name'),
            content=json.dumps(period)))
        if not_exist is True: 
            filters = {"start_datetime": period.get('start_datetime'),
                       "end_datetime": period.get('end_datetime')}
            exist_period = metadata_center_service.list_entity(schema_name="PERIOD", partner_id=partner_id,
                                                               filters=filters, user_id=user_id).get('rows', [])
            if len(exist_period) > 0:
                logger.info("{} 账期已经初始化".format(period.get('name')))
                continue
            try:
                res = metadata_center_service.add_entity(schema_name="PERIOD", auto_enable=True, fields=period,
                                                         partner_id=partner_id, user_id=user_id)
                logger.info("{} 账期初始化结果: {}".format(period.get('name'), res.get('id')))
            except Exception as e:
                logger.warning("request metadata add period error: {}".format(e))
                PeriodInitBatch.update_period(partner_id=partner_id, update_data=dict(id=period_ids[inx],
                                                                                      process_status="FAILED"))
                continue
            if res.get('id'):
                PeriodInitBatch.update_period(partner_id=partner_id, update_data=dict(id=period_ids[inx],
                                                                                      process_status="SUCCESS"))
    logger.info("租户: {}的{} - {} 账期初始化完成.".format(partner_id, start_month, end_month))


def getMonthRangList(start_month, end_month):
    """
    从开始日期到结束日期查询存在的月份列表
    :param start_month:
    :param end_month:
    :return:
    """
    start_time = datetime.strptime(start_month, "%Y-%m")
    end_time = datetime.strptime(end_month, "%Y-%m")
    month_count = rrule.rrule(rrule.MONTHLY, dtstart=start_time, until=end_time).count()
    now_month = datetime.strptime(str(datetime.now())[:7], "%Y-%m")
    if start_time == now_month == end_time:
        return []
    else:
        month_list = []
        for x in range(month_count):
            year, month = [int(y) for y in str(start_time)[:7].split("-")]
            month = month + x
            if month > 12:
                year += 1
                month -= 12
            elif month < 1:
                year -= 1
                month += 12
            year, month = str(year), str(month)
            if len(month) == 1:
                month = "0" + month
            month_list.append(year + "-" + month)
        return month_list


def build_period_data(month_list):
    period_data = []
    for i in range(len(month_list) - 1):
        period_data.append(
            {
                "end_time": "4:00:00",
                "end_day": month_list[i + 1] + "-01",
                "start_day": month_list[i] + "-01",
                "start_time": "4:00:00",
                "start_datetime": get_utc_datetime_str(month_list[i] + "-01 4:00:00"),
                "end_datetime": get_utc_datetime_str(month_list[i + 1] + "-01 4:00:00"),
                "name": month_list[i]
            }
        )
    return period_data



