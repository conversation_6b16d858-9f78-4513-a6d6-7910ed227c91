import logging

from supply.utils.helper import MessageTopic
from supply.driver.mq import register_mq_task
from ..model.metadata import MetadataRepository
from . import partner_task_distribution


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.UPDATE_METADATA_TOPIC)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.UPDATE_METADATA_TOPIC)
def update_metadata_task(message):
    """
    每天定时更新主档表-store，product
    """
    logging.info("Received UPDATE_METADATA_TOPIC: {}".format(message))
    partner_id = message['partner_id'] if 'partner_id' in message else None
    user_id = message['user_id'] if 'user_id' in message else None
    if not partner_id or not user_id:
        logging.error(
            'UPDATE_METADATA_TOPIC: not partner_id or not user_id')
        return True
    MetadataRepository.update_store(partner_id=partner_id, user_id=user_id)
    logging.debug('** update metadata store=- partner_id=%s - user_id=%s **' % (
        str(partner_id), str(user_id)))
    MetadataRepository.update_product(partner_id=partner_id, user_id=user_id)
    logging.debug('** update metadata product=- partner_id=%s - user_id=%s **' % (
        str(partner_id), str(user_id)))
    return True
