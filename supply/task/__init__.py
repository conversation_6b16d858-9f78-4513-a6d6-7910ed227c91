import logging
import functools
import copy
from supply.driver.mq import mq_producer
from supply.client.ianvs_service import ianvs_service

"""
定时任务支持多租户公用装饰器:
**只在外部触发时需要绑定**
新增四个参数：
    all_partner: True/False (bool)  是否对全部租户生效
    include_partners: [] 任务生效租户ID列表         当all_partner为False时看此参数
    exclude_partners: [] 任务需要排除的租户ID列表    当all_partner为True时需要校验此参数
    params: dict         任务处理业务参数
"""


def partner_task_distribution(topic_group, topic):
    def f(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not args:
                return func(*args, **kwargs)
            is_sub_task = args[0].get('is_sub_task') if isinstance(args[0], dict) else None
            if is_sub_task is True:
                del args[0]['is_sub_task']
                return func(*args, **kwargs)
            all_partner = args[0].get('all_partner') if isinstance(args[0], dict) else None
            include_partners = args[0].get('include_partners') if isinstance(args[0], dict) else []
            exclude_partners = args[0].get('exclude_partners') if isinstance(args[0], dict) else []
            partner_id = args[0].get('partner_id') if isinstance(args[0], dict) else None
            user_id = args[0].get('user_id') if isinstance(args[0], dict) else None
            params = args[0].get('params') if isinstance(args[0], dict) else dict()
            tz= args[0].get('tz') if isinstance(args[0], dict) else None
            partner_ids = []
            if not all([partner_id, user_id]):
                logging.warning('[partner_task_distribution] partner_id and user_id is required!')
                return func(*args, **kwargs)
            if not (all_partner or include_partners or exclude_partners):
                return func(*args, **kwargs)
            if all_partner is True:
                # 查询所有租户
                partners = ianvs_service.ListAllPartner(partner_id=partner_id, user_id=user_id).get('rows', [])
                for p in partners:
                    partner_ids.append(p.get('id'))
                if exclude_partners and isinstance(exclude_partners, list):
                    partner_ids = list(set(partner_ids) - set(exclude_partners))
            else:
                partner_ids = include_partners
            logging.info("partners total: {}".format(len(partner_ids)))
            for partner_id in partner_ids:
                msg = copy.deepcopy(params)
                msg['user_id'] = user_id
                msg['partner_id'] = partner_id
                msg['is_sub_task'] = True
                msg['tz'] = tz
                mq_producer.publish(topic_group, topic, message=msg)
            return None
        return wrapper
    return f


# from concurrent.futures import ThreadPoolExecutor
#
# from supply import APP_CONFIG, NSQ_ADDRESS, NSQ_TCP_ADDRESS, logger
#
# requests.adapters.DEFAULT_RETRIES = 5
#
# loop = tornado.ioloop.IOLoop.current()
# executor = ThreadPoolExecutor(3) # 最大同时处理包含所有topic的3个消息
#
# class NSQService(object):
#     def __init__(self, address):
#         self.nsq_host = address
#         print("start ping")
#         self.connected = self.ping()
#         print("self.connected_init:", self.connected)
#
#     def stats(self):
#         r = requests.get(self.nsq_host + '/stats')
#
#     def join_url(self, pre_url, pro_url):
#         return "%s/%s" % (pre_url.rstrip('/'), pro_url.lstrip('/'))
#
#     def ping(self):
#         try:
#             r = requests.get(self.join_url('http://'+self.nsq_host, 'ping'))
#             if r.status_code == 200:
#                 return True
#             # return True
#         except Exception as e:
#             logging.warning('nsq cannot be connected %s' % e)
#             return False
#         return False
#
#     def __serialize(self, message):
#         return json.dumps(
#             message,
#             default=lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if isinstance(x, datetime.datetime) else None
#         )
#
#     def __pub(self, topic, message):
#         try:
#             with requests.session() as r:
#                 r.keep_alive = False
#                 res = r.post(self.join_url('http://'+self.nsq_host, 'pub'),
#                              params=dict(topic=topic),
#                              data=self.__serialize(message))
#                 res.raise_for_status()
#                 return res.content
#         except Exception as e:
#             logger.error('nsqTopicCannotBeSend %s , args: %s, error: %s' % (topic, message, e))
#         return None
#
#     def publish(self, topic: str, data: Dict):
#         return self.__pub(topic, data)
#
#
# # 装饰器风格的绑定。
# def nsq_bind(topic: str, channel: str):
#     def decorator(func):
#         def message_handler(message_body):
#             data = None
#             try:
#                 data = json.loads(message_body)
#                 _ = func(data)
#             except Exception as e:
#                 logger.error("nsq_data:{}, exception: {}".format(data, traceback.format_exc()))
#             finally:
#                 if 'mysql' in APP_CONFIG:
#                     from supply.driver.mysql import session as MySQLSession
#                     MySQLSession.close()
#                 if 'postgre' in APP_CONFIG:
#                     from supply.driver.postgre import session as PostgreSession
#                     PostgreSession.close()
#                 if 'sqlite' in APP_CONFIG:
#                     from supply.driver.sqlite import session as SqliteSession
#                     SqliteSession.close()
#             return True
#
#         def handler(message):
#             message.enable_async() # 消息开启异步处理
#             def finish(result):
#                 message.finish() # 事件监听线程设置回调(不能在处理的线程调用)
#             future = executor.submit(message_handler, message.body)
#             loop.add_future(future, finish)
#             return True
#
#         _reader = nsq.Reader(message_handler=handler, nsqd_tcp_addresses=NSQ_TCP_ADDRESS,
#                              topic=topic, channel=channel, max_in_flight=2, # 客户端每个topic最多一次取2个消息
#                              **{"msg_timeout":900}) # 设置客户端timeout时间为900秒
#         logging.info("listen on topic: {topic} channel: {channel} use function {name}".format(
#             topic=topic, channel=channel, name=func.__name__,
#         ))
#         return func
#
#     return decorator
#
# __nsq_service = NSQService(NSQ_ADDRESS)
#
#
# def public(topic: str, data: Dict):
    # __nsq_service.publish(topic, data)


def public(topic: str, data: dict):
    pass
