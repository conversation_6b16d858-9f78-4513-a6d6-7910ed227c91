import traceback

from supply import logger
from supply.utils.helper import MessageTopic, convert_to_int
from supply.module.stocktake_service import ssm as stocktake_schedule_service
from datetime import datetime, timedelta
from . import partner_task_distribution
from supply.driver.mq import register_mq_task, mq_producer


# 盘点结算批量拆解bom，折算,结算，同步调用，实现
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.STOCKTAKE_BOM_TOPIC)
def stocktake_bom_handler(message):
    doc_id = message.get('doc_id')
    bom_products_map = message.get('bom_products_map')
    partner_id = message.get('partner_id')
    user_id = message.get('user_id')
    logger.info('------start_finalize_bom_stocktake-{}-----'.format(doc_id))
    try:
        doc_obj = stocktake_schedule_service.finalize_store_stocktake(doc_id, bom_products_map, partner_id=partner_id,
                                                                      user_id=user_id)
        if not doc_obj:
            logger.info('------error-finalize_bom_stocktake--------')
        else:
            logger.info('------success-finalize_bom_stocktake--------')
    except Exception as e:
        logger.info('------error-finalize_bom_stocktake----- \n{}-\n{}'.format(e, traceback.print_exc()))
    return True


# 月盘点次日8点推送jde
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.STOCKTAKE_MONTH_JDE_TOPIC)
def stocktake_month_jde_handler(message):
    date_time = message.get('target_date')
    partner_id = message['partner_id']
    if date_time:
        date_now = datetime.strptime(date_time, '%Y-%m-%d')
    else:
        date_now = datetime.utcnow() - timedelta(days=1)
    retain = date_now
    pub_date = datetime(int(retain.year), int(retain.month), int(retain.day))
    pub_messages = stocktake_schedule_service.get_month_stocktake_messages(pub_date, partner_id)
    for p in pub_messages:
        if p['status'] == 'INITED':
            month_stocktake_message = p['month_message']
            # differ_stocktake_message = p['diff_message']
            # mq_producer.publish(MessageTopic.MONTH_STOCKTAKE_JDE_TOPIC, json.loads(month_stocktake_message))
            # 月盘不传差异
            # mq_producer.publish(MessageTopic.DIFFER_STOCKTAKE_JDE_TOPIC, differ_stocktake_message)
            stocktake_schedule_service.update_month_stocktake_messages(p['id'])
    return True


# 核算盘点失败的重新发送
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.RE_HANDLE_STOCKTAKE_BOM_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.RE_HANDLE_STOCKTAKE_BOM_TOPIC)
def rehandle_stocktake_bom_handler(message):
    logger.info("Received rehandle_stocktake_bom_handler message: {}".format(message))
    bus_date = message.get('bus_date')
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    if bus_date:
        bus_date = datetime.strptime(bus_date, '%Y-%m-%d')
    else:
        bus_date = datetime(datetime.utcnow().year, datetime.utcnow().month, datetime.utcnow().day)
    stocktake_db = stocktake_schedule_service.get_need_rehandle_stocktake(bus_date=bus_date, partner_id=partner_id)
    if stocktake_db:
        for s in stocktake_db:
            stocktake_schedule_service.rehandle_stocktake(s.doc_id, s.partner_id, s.user_id)
    return True


# 轮询获取盘点差异数据 st_diff_flag = 0
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.UPDATE_STOCKTAKE_DIFF_TOPIC)
def update_stocktake_diff_handler(message):
    bus_date = message.get('bus_date')
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    if bus_date:
        bus_date = datetime.strptime(bus_date, '%Y-%m-%d')
    else:
        bus_date = datetime(datetime.utcnow().year, datetime.utcnow().month, datetime.utcnow().day)
    stocktake_db = stocktake_schedule_service.get_need_update_stocktake_diff(bus_date)
    print('stocktake_db', stocktake_db)
    if stocktake_db:
        for s in stocktake_db:
            mq_producer.publish(MessageTopic.SUPPLY_GROUP,
                                MessageTopic.UPDATE_STORE_STOCKTAKE_DIFF_TOPIC,
                                {"doc_id": s.doc_id, "partner_id": partner_id, "user_id": user_id})
    return True


### 轮询获取盘点差异数据 st_diff_flag = 1
@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.UPDATE_STORE_STOCKTAKE_DIFF_TOPIC)
def update_store_stocktake_diff_handler(message):
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    doc_id = convert_to_int(message['doc_id']) if 'doc_id' in message else None
    stocktake_schedule_service.update_store_stocktake_diff(doc_id, partner_id, user_id)
    return True


### 接受库存重新盘点
@register_mq_task("boh_inventory", "INVENTORY_STOCKTAKE_PROCESS_COMPLETE")
def update_store_stocktake_diff_by_inventory_handler(message):
    doc_id = convert_to_int(message['BatchNo']) if 'BatchNo' in message else None
    stocktake_schedule_service.update_store_stocktake_diff_by_inventory(doc_id)
    return True

### 捞取特定状态的盘点单
### JJY定制化需求，有新建状态盘点单的门店不允许订货，因此需要把有新建状态盘点单的门店推给三方
@register_mq_task('boh_order', MessageTopic.GET_STOCKTAKE_LIST)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_STOCKTAKE_LIST)
def get_stock_list(message):
    '''
    
    '''
    logger.info("接收到GET_STOCKTAKE_LIST任务:{}".format(message))
    partner_id = message.get("partner_id") 
    status = message.get("status")
    minutes = message.get("minutes", 30)

    if not (partner_id and status):
        logger.info("GET_STOCKTAKE_LIST 缺少必须字段:{}".format(message))
        return True

    start_date = datetime.utcnow() - timedelta(minutes=minutes)

    stocktake_list = stocktake_schedule_service.list_stocktake_doc_details(partner_id=partner_id, status=status, start_date=start_date)
    if stocktake_list and len(stocktake_list)>0:
        for stocktake in stocktake_list:
            # vendor单据同步给三方
            message = {
                                'doc_resource': 's_stocktake' if stocktake.get('branch_type') !='FRS_STORE' else 'fs_stocktake',
                                'doc_id': stocktake.get("id"),
                                'partner_id': partner_id,
                                'user_id': stocktake.get("user_id")
                                # 'operate_time': stocktake.get("target_date")
                            }
            mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, message)

    return True
