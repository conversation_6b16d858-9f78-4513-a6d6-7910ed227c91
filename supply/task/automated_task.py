import logging
from datetime import datetime, timedelta

from supply import logger
from supply.utils.helper import MessageTopic, convert_to_int, get_today_datetime, translate_to_datetime
from supply.driver.mq import register_mq_task
from . import partner_task_distribution
from supply.driver.mq import mq_producer

from supply.model.adjust import AdjustRepository
from supply.model.self_picking.self_picking import self_picking_db

from supply.proto.store import self_picking_pb2
from supply.proto.store import transfer_pb2

from supply.client.metadata_service import metadata_service

from supply.module.receive_diff import receiving_diff_service
from supply.module.returns import returns_service
from supply.module.transfer_service import ssm
from supply.module.adjust_service import ads as adjust_schedule_service
from supply.module.self_picking import self_picking_service
from supply import APP_CONFIG


"""
自动任务推进
"""

@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_UNCONFIRMED_DIFF_TOPIC)
def get_unconfirmed_diffs(message):
    '''
    获取未审核的收货差异单
    '''
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    end_hours = message['end_hours'] if 'end_hours' in message else 0
    end_mins = message['end_mins'] if 'end_mins' in message else 0
    status = message['status'] if 'status' in message else None
    receiving_diff_service.get_unconfirmed_diffs(end_hours, end_mins, status, partner_id, user_id)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_UNCONFIRMED_DIFF_OVER_DAY_TOPIC)
def get_unconfirmed_diffs_over1day(message):
    '''
    获取超过24h未填写的收货差异单
    '''
    logging.info("Receive GET_UNCONFIRMED_DIFF_OVER_DAY_TOPIC")
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    overhours = message['overhours'] if 'overhours' in message else 24
    max_overdays = message['max_overdays'] if 'max_overdays' in message else 31
    status = message['status'] if 'status' in message else None
    receiving_diff_service.get_unconfirmed_diffs_over1day(overhours=overhours, status=status, 
                                                partner_id=partner_id, user_id=user_id, max_overdays=max_overdays)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_AUTO_CONFIRM_DIFF_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_AUTO_CONFIRM_DIFF_TOPIC)
def get_unfinished_diffs_over1day(message):
    '''
    获取符合自动确认时间的收货差异单
    '''
    logging.info("Receive GET_UNCONFIRMED_DIFF_OVER_DAY_TOPIC")
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    status = message['status'] if 'status' in message else None

    # 获取tz
    tz_str=message["tz"]
    store_ids=[]
    if tz_str:
        tz = fill_tz(tz_str)
        #查询门店
        filters = {}
        filters["tz"]=tz
        store_list = metadata_service.get_store_list(partner_id=partner_id, user_id=user_id,filters=filters, return_fields="id,code,name,tz")
        for store in store_list.get("rows", []):
            store_ids.append(int(store.get("id")))

    diff_dbs = receiving_diff_service.get_auto_confirm_data(partner_id=partner_id, user_id=user_id,status=status)
    if diff_dbs and len(diff_dbs)>0:
        for diff_db in diff_dbs:
            if diff_db.received_by not in store_ids:
                continue
            message = {}
            message['partner_id'] = partner_id
            message['user_id'] = user_id
            message['diff_id'] = diff_db.id
            message['status'] = diff_db.status
            message['owner'] = 'store'
            logging.info('StartAutoConfirmDiffs: {}'.format(message))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC,
                                    message=message)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CONFIRM_DIFFS_TOPIC)
def auto_confirm(message):
    '''
    自动审核收货差异单
    '''
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    diff_id = message['diff_id'] if 'diff_id' in message else None
    status = message['status'] if 'status' in message else None
    owner = message['owner'] if 'owner' in message else 'store' # 差异数量由谁承担
    logging.info("Receive AUTO_CONFIRM_DIFFS_TOPIC diff_id: {}".format(diff_id))

    receiving_diff_service.auto_confirm(diff_id, status, owner, partner_id, user_id)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_UNFINISHED_RETURNS_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_UNFINISHED_RETURNS_TOPIC)
def get_unfinished_returns_overdays(message):
    '''
    获取未到终态的退货单
    '''
    logging.info("Receive GET_UNFINISHED_RETURNS_TOPIC")
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    overdays = message.get("overdays", 31)
    sub_type = message.get("branch_type")

    start_date = datetime.utcnow()-timedelta(days=overdays)
    end_date = datetime.utcnow()

    cancel_return_db_list, confirmed_return_db_list = returns_service.list_unfinished_returns(return_date_from=start_date, \
        return_date_to=end_date, delivery_date_from=start_date, delivery_date_to=end_date, \
        partner_id=partner_id, user_id=user_id, sub_type=sub_type, trans_type="RefundOnly")
    
    if cancel_return_db_list and len(cancel_return_db_list)>0:
        for return_db in cancel_return_db_list:
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.AUTO_CANCEL_RETURNS_TOPIC,
                                message=dict(user_id=user_id, partner_id=partner_id, return_id=return_db.id))

    if confirmed_return_db_list and len(confirmed_return_db_list)>0:
        for return_db in confirmed_return_db_list:
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.AUTO_CONFIRM_RETURNS_TOPIC,
                                message=dict(user_id=user_id, partner_id=partner_id, return_id=return_db.id))
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CONFIRM_RETURNS_TOPIC)
def auto_confirm_returns(message):
    '''
    自动确认退货单
    '''
    partner_id = message.get("partner_id")
    user_id = message.get("user_id")
    return_id = message.get("return_id")
    logging.info("Receive AUTO_CONFIRM_RETURNS_TOPIC return_id: {}".format(return_id))

    returns_service.delivery_return(return_id=return_id, partner_id= partner_id, user_id=user_id, is_auto=True)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CANCEL_RETURNS_TOPIC)
def auto_cancel_returns(message):
    '''
    自动作废退货单
    '''
    partner_id = message.get("partner_id")
    user_id = message.get("user_id")
    return_id = message.get("return_id")
    logging.info("Receive AUTO_CANCEL_RETURNS_TOPIC return_id: {}, partner_id:{}".format(return_id, partner_id))

    returns_service.delete_return(return_id=return_id, partner_id= partner_id, user_id=user_id, is_auto=True)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CONFIRM_TRANSFER)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CONFIRM_TRANSFER)
def auto_confirm_transfer(message):
    '''
    自动收货——调拨
    '''
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    transfer_date = message.get('end_date')
    status = message.get("status")
    overdays = message.get('overdays', 31)
    branch_type = message.get('branch_type')
    if transfer_date:
        start_date = datetime.strptime(transfer_date, "%Y-%m-%d %H:%M:%S")
        end_date = datetime(datetime.utcnow().year, datetime.utcnow().month, datetime.utcnow().day) + timedelta(days=1)
    else:
        end_date = datetime.utcnow()
        s = datetime.utcnow() - timedelta(days=overdays)
        start_date = datetime(s.year, s.month, s.day)
    transfer_objs = ssm.get_un_confirm_transfer_by_transfer_date(start_date=start_date, \
        end_date=end_date, partner_id=partner_id, status=status, branch_type=branch_type)
    if len(transfer_objs) > 0:
        for obj in transfer_objs:
            if obj[1] == "INITED":
                mq_producer.publish(MessageTopic.SUPPLY_GROUP,
                                MessageTopic.AUTO_CANCEL_TRANSFER_BY_ID,
                                dict(id=obj[0], partner_id=partner_id, user_id=user_id, branch_type=branch_type))
            elif obj[1] == "SUBMITTED":
                mq_producer.publish(MessageTopic.SUPPLY_GROUP,
                                MessageTopic.AUTO_CONFIRM_TRANSFER_BY_ID,
                                dict(id=obj[0], partner_id=partner_id, user_id=user_id, branch_type=branch_type))
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CONFIRM_TRANSFER_BY_ID)
def auto_confirm_transfer_by_id(message):
    '''
    自动收货——调拨
    '''
    
    id = message.get("id")
    partner_id = message.get("partner_id")
    user_id = message.get("user_id")
    username =message.get("username")
    branch_type = message.get('branch_type')
    logging.info("Receive AUTO_CONFIRM_TRANSFER_BY_ID :{}".format(message))
    
    ssm.auto_confirm_transfer(transfer_id=id, partner_id=partner_id, user_id=user_id)
    return True

@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CANCEL_TRANSFER_BY_ID)
def auto_cancel_transfer_by_id(message):
    '''
    自动作废——调拨
    '''
    
    id = message.get("id")
    partner_id = message.get("partner_id")
    user_id = message.get("user_id")
    logging.info("Receive AUTO_CANCEL_TRANSFER_BY_ID :{}".format(message))

    ssm.auto_cancel_transfer(transfer_id=id, partner_id=partner_id, user_id=user_id)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_UNFINISHED_ADJUST_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_UNFINISHED_ADJUST_TOPIC)
def get_unfinished_adjust_overdays(message):
    '''
    获取未到终态的报废单
    '''
    logging.info("Receive GET_UNFINISHED_ADJUST_TOPIC")
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    overdays = message.get("overdays", 31)
    branch_type = message.get("branch_type")
    status = message.get("status")

    start_date = datetime.utcnow()-timedelta(days=overdays)
    end_date = datetime.utcnow()
    adjust_detail_list = AdjustRepository().list_store_adjust(partner_id=partner_id, status=status, \
        start_date=start_date, end_date=end_date, branch_type=branch_type)
    for adjust_detail in adjust_detail_list:
        if adjust_detail.get("status") and adjust_detail["status"] in ("INITED", "REJECTED"):
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.AUTO_CANCEL_ADJUST_TOPIC,
                                message=dict(user_id=user_id, partner_id=partner_id, adjust_id=adjust_detail.get("id")))
        elif adjust_detail.get("status") and adjust_detail["status"] in ("SUBMITTED"):
            if str(partner_id) in (APP_CONFIG.get('jjy_partner_ids','')).split(','):
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CANCEL_ADJUST_TOPIC,
                                    message=dict(user_id=user_id, partner_id=partner_id,
                                                 adjust_id=adjust_detail.get("id")))
            else:
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.ADJUST_AUTO_CONFIRM_TOPIC,
                                    message=dict(user_id=user_id, partner_id=partner_id, adjust_id=adjust_detail.get("id")))
    
    return True

@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.ADJUST_AUTO_CONFIRM_TOPIC)
def auto_comfirm_adjust(message):
    """
    收获差异损耗表自动确认
    """
    adjust_id = message['adjust_id'] if 'adjust_id' in message else 0
    partner_id = message['partner_id'] if 'partner_id' in message else 0
    user_id = message['user_id'] if 'user_id' in message else None
    if not partner_id or not user_id:
        logging.error(
            'AutoComfirmAdjust: not partner_id or not user_id')
        return True
    adjust_schedule_service.approve_adjust(adjust_id=adjust_id, partner_id=int(partner_id),
                                             user_id=int(user_id))
    return True

@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CANCEL_ADJUST_TOPIC)
def auto_cancel_adjust(message):
    """
    收获差异损耗表自动作废
    """
    adjust_id = message['adjust_id'] if 'adjust_id' in message else 0
    partner_id = message['partner_id'] if 'partner_id' in message else 0
    user_id = message['user_id'] if 'user_id' in message else None
    if not partner_id or not user_id:
        logging.error(
            'AutoComfirmAdjust: not partner_id or not user_id')
        return True
    allow_status = ["INITED", "REJECTED", "SUBMITTED"]
    adjust_schedule_service.adjusts_cancel(adjust_ids=[adjust_id], partner_id=partner_id, user_id=user_id,allow_status=allow_status)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_UNFINISHED_SELF_PICKING_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.GET_UNFINISHED_SELF_PICKING_TOPIC)
def get_unfinished_self_picking_overdays(message):
    '''
    获取未收货的自采单
    '''
    logging.info("Receive GET_UNFINISHED_SELF_PICKING_TOPIC")
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    overdays = message.get("overdays", 31)
    status = message.get("status")
    branch_type = message.get("branch_type")

    start_date = datetime.utcnow()-timedelta(days=overdays)
    end_date = datetime.utcnow()

    self_picking_list = self_picking_db.list_self_picking(partner_id=partner_id, start_date=start_date, end_date=end_date,
                                                      status=status, branch_type=branch_type)
    if self_picking_list:
        for self_picking in self_picking_list:
            if self_picking.status in ("INITED", "REJECTED"):
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CHANGE_SELF_PICKING_TOPIC,
                                    message=dict(user_id=user_id, partner_id=partner_id, doc_id=self_picking.id, status="CANCELLED"))
            elif self_picking.status in ("SUBMITTED"):
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.AUTO_CHANGE_SELF_PICKING_TOPIC,
                                    message=dict(user_id=user_id, partner_id=partner_id, doc_id=self_picking.id, status="APPROVED"))

    return True

@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CHANGE_SELF_PICKING_TOPIC)
def auto_change_self_picking(message):
    """
    自采单自动确认
    """
    doc_id = message.get("doc_id")
    partner_id = message['partner_id'] if 'partner_id' in message else 0
    user_id = message['user_id'] if 'user_id' in message else None
    status = message.get("status")
    if not partner_id or not user_id:
        logging.error(
            'AutoChangeAdjust: no partner_id or user_id')
        return True
    req = self_picking_pb2.ChangeSelfPickingStatusRequest(
        receipt_id = doc_id,
        status = status,
        remark = "自动任务推进"
    )
    self_picking_service.change_self_picking_status(request=req, user_id=user_id, partner_id=partner_id)
    return True

#  处理时区
def fill_tz(tz):
    # 将简单的时区表示转换为标准格式
    tz = str(tz).strip()  # 确保输入是字符串并去除空格

    # 如果已经是标准格式，直接返回
    if len(tz) >= 5 and (tz[0] in ['+', '-']) and ':' in tz:
        return tz

    # 处理特殊情况："0" -> "+00:00"
    if tz == "0":
        return "+00:00"

    # 提取符号和数值
    sign = "+"
    if tz.startswith("-"):
        sign = "-"
        tz = tz[1:]
    elif tz.startswith("+"):
        tz = tz[1:]

    # 转换为数字
    try:
        hours = int(tz)
        # 格式化为标准格式
        return f"{sign}{hours:02d}:00"
    except ValueError:
        # 如果无法转换为数字，返回原始值
        return tz


