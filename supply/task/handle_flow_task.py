import logging
import time
from datetime import datetime, timedelta

from supply.driver.mq import register_mq_task, mq_producer
from supply.utils.helper import convert_to_int, convert_to_decimal, MessageTopic
from supply.model.operation_log import ReceiptFlowLogModel as flowDB
from supply.model.operation_log import ReceiptFlowProductLog as flowProDB
from supply.model.transfer import TransferRepository
from supply.proto.transfer_pb2 import CreateTransferRequest
from supply.proto.manufactory.material_convert_pb2 import CreateMaterialConvertRequest
from supply.model.manufactory.packing_receipts import packing_receipts_db
from supply.model.manufactory.processing_receipts import processing_receipts_db
from supply.model.receiving_diff import ReceivingDiffProductModel, ReceivingDiffModel
from supply.module.material_convert import material_convert_service
from supply.module.transfer_service import ssm as transfer_service
from google.protobuf.timestamp_pb2 import Timestamp
from supply.utils.snowflake import gen_snowflake_id
from supply.client.metadata_service import metadata_service
from . import partner_task_distribution

"""
处理supply入库单据的业务流转任务逻辑：
入库单据：
    1、包装单
    2、加工单
    3、内/外部调拨单
目前主要实现：
    1、自动内部调拨
    2、自动物料转换
"""


def get_timestamp(date):
    if isinstance(date, datetime):
        date = Timestamp(seconds=int(date.timestamp()))
    else:
        date = None
    return date


def get_product_unit_rate_map(product_ids=None, partner_id=None, user_id=None):
    """拉一把主档返回商品和单位id对应转换率的map
    :param product_ids 商品id列表
    :param partner_id
    :param user_id
    :returns products_map{product_id（int）: {unit_id(key): rate(value), ...}}
    """
    product_list = metadata_service.get_product_list(ids=product_ids,
                                                     return_fields="id",
                                                     include_units=True,
                                                     partner_id=partner_id, user_id=user_id).get("rows", [])

    products_unit_rate_map = {}
    if product_list and isinstance(product_list, list):
        for product in product_list:
            units = product.get('units') if product.get('units') else []
            if units:
                unit_rate = dict()
                for unit in units:
                    unit_rate[convert_to_int(unit.get('id'))] = round(unit.get('rate', 1), 5)
                products_unit_rate_map[convert_to_int(product.get('id'))] = unit_rate
    return products_unit_rate_map


# noinspection PyMethodMayBeStatic
def get_product_map(product_ids, partner_id=None, user_id=None):
    """拉一把主档返回商品和单位的map
    :param product_ids 商品id列表
    :param partner_id
    :param user_id
    :returns products_map{product_id: product_dict}
    """
    product_list = metadata_service.get_product_list(ids=product_ids,
                                                     return_fields="id,code,name,product_type",
                                                     include_units=True,
                                                     partner_id=partner_id, user_id=user_id).get("rows", [])

    products_map = {}
    if product_list:
        for product in product_list:
            products_map[int(product["id"])] = product
    return products_map


def get_branch_map(branch_id=None, branch_type=None, partner_id=None, user_id=None):
    """获取门店/仓库/加工中心主档map
    :return branch_map{"id": id, "name": name, "code": code}
    """
    if branch_type == "STORE":
        branch = metadata_service.get_store(store_id=branch_id, return_fields="id,code,name",
                                            partner_id=partner_id, user_id=user_id)
    elif branch_type == "WAREHOUSE":
        branches = metadata_service.get_distribution_center_list(ids=[branch_id],
                                                                 return_fields="id,code,name",
                                                                 partner_id=partner_id, user_id=user_id).get('rows', [])
        if branches:
            branch = branches[0]
        else:
            branch = {}
    elif branch_type == "MACHINING_CENTER":
        machining = metadata_service.get_entity_by_id(schema_name="MACHINING-CENTER", id=branch_id,
                                                      partner_id=partner_id, user_id=user_id)
        branch = machining.get('fields', {})
        branch["id"] = convert_to_int(machining.get('id'))
    elif branch_type == "POSITION":
        position = metadata_service.get_entity_by_id(schema_name="POSITION", id=branch_id,
                                                     partner_id=partner_id, user_id=user_id)
        branch = position.get('fields', {})
        branch["id"] = convert_to_int(position.get('id'))
    else:
        branch = {}
    return branch


def get_unit_map(partner_id=None, user_id=None):
    unit_dict = {}
    units = metadata_service.get_unit_list(return_fields='id,code,name',
                                           partner_id=partner_id, user_id=user_id).get('rows', [])
    if units and isinstance(units, list):
        for unit in units:
            if isinstance(unit, dict) and 'id' in unit:
                unit_dict[int(unit['id'])] = unit
    return unit_dict


def get_origin_receive_products_map(receipt_id, doc_type, partner_id):
    """拉取原单据的商品列表做成map"""
    origin_products_map = {}  # 保存原单据的商品
    if doc_type in ["transfer", "insideTransfer"]:
        # 查询内/外部调拨单据商品
        receive_products = TransferRepository().list_transfer_product(transfer_id=receipt_id, partner_id=partner_id)
        for p in receive_products:
            origin_products_map[convert_to_int(p.get('product_id'))] = dict(
                product_id=p.get('product_id'),
                product_name=p.get('product_name'),
                product_code=p.get('product_code'),
                unit_id=p.get('unit_id'),
                quantity=p.get('quantity')
            )
    elif doc_type == "packingOrder":
        # 查询包装单据商品
        packing_detail = packing_receipts_db.get_packing_receipts_by_id(receipt_id=receipt_id, is_detail=True,
                                                                        partner_id=partner_id)
        origin_products_map[packing_detail.target_material] = dict(
            product_id=packing_detail.target_material,
            product_name=packing_detail.target_material_name,
            product_code=packing_detail.target_material_code,
            unit_id=packing_detail.target_material_unit_id,
            quantity=packing_detail.packed_quantity
        )
    elif doc_type == "processOrder":
        # 查询加工单据商品
        processing_detail = processing_receipts_db.get_processing_receipts_by_id(receipt_id=receipt_id,
                                                                                 partner_id=partner_id,
                                                                                 is_detail=True)
        origin_products_map[processing_detail.target_material] = dict(
            product_id=processing_detail.target_material,
            product_name=processing_detail.target_material_name,
            product_code=processing_detail.target_material_code,
            unit_id=processing_detail.target_material_unit_id,
            quantity=processing_detail.actual_output_quantity
        )
    elif doc_type == "receiveDiff":
        # 查询收货差异单商品
        _, products = ReceivingDiffProductModel.list_receiving_diff_products_by_diff_id(
            receiving_diff_id=receipt_id, partner_id=partner_id)
        for product in products:
            origin_products_map[product.product_id] = dict(
                product_id=product.product_id,
                product_name=product.product_name,
                product_code=product.product_code,
                unit_id=product.unit_id,
                quantity=product.s_diff_quantity
            )
    return origin_products_map


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FLOW_TASK)
def handle_flow_task_main(message):
    """处理supply入库单据业务流转任务分发主函数
    sub_doc_type -> 标记当前单据是仓位收货还是门店收货 position/store
    doc_type -> 单据类型和业务配置中值一致
    1、拉取总部配置，看当前组织是否开启多仓位配置
    2、如果开启多仓位先假设有自动调拨业务处理，拉取组织下的业务配置看当前仓位是否配置了自动调拨仓位
    3、拉取内部调拨规则，创建自动调拨单，保存处理的商品
    4、再拉取物料转换业务配置主档，判断该组织的单据是否可以进行物料转换
    """
    flow_id = convert_to_int(message.get('flow_id'))  # 工作流记录id
    partner_id = convert_to_int(message.get('partner_id'))
    user_id = convert_to_int(message.get('user_id'))
    logging.info("[HANDLE_FLOW_TASK]接收到入库单据业务流转任务 - flow_id: {}".format(flow_id))
    if not all([flow_id, partner_id, user_id]):
        logging.warning("[HANDLE_FLOW_TASK]缺少任务处理必要参数！")
        return True
    # 通过工作流单据id捞取工作流详情
    flow_log = flowDB.get_flow_log(_id=flow_id, partner_id=partner_id)
    if not flow_log:
        logging.warning("[HANDLE_FLOW_TASK]未找到工作流信息!")
        return True
    branch_id = flow_log.branch_id
    sub_branch_id = flow_log.sub_branch_id
    sub_doc_type = flow_log.sub_doc_type
    operation = flow_log.operation
    doc_type = flow_log.doc_type
    doc_id = flow_log.doc_id
    branch_type = None
    if doc_type in ["transfer", "insideTransfer"]:
        # 查询内/外部调拨单据
        transfer = TransferRepository().get_transfer_by_id(transfer_id=doc_id,  partner_id=partner_id)
        branch_type = transfer.branch_type
    elif doc_type in ["packingOrder", "processOrder"]:
        branch_type = "MACHINING_CENTER"
    elif doc_type == "receiveDiff":
        receive_diff = ReceivingDiffModel.get_diff_by_id(partner_id=partner_id, diff_id=doc_id)
        if receive_diff:
            branch_type = receive_diff.branch_type
    if not branch_type:
        logging.warning("[HANDLE_FLOW_TASK]branch_type不合理, 直接返回 - {}".format(branch_type))
        return True
    # 标记是否开启多仓位
    opened_position = True if sub_branch_id else False
    operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
    if operation == "INNER_TRANSFER":
        if opened_position is True:
            # 组织开启多仓位才进行自动内部调拨业务
            flow_id = deal_auto_inner_transfer(branch_type=branch_type, branch_id=branch_id,
                                               sub_branch_id=sub_branch_id, doc_type=doc_type,
                                               doc_id=doc_id, flow_id=flow_id, operator_name=operator_name,
                                               partner_id=partner_id, user_id=user_id)
        else:
            # 不是仓位收货，不进行内部调拨，更新当前工作流状态，并创建下一个业务流转(物料转换)
            flow_id = flowDB.update_flow_logs(flow_id=flow_id,
                                              update_data=dict(process_status="COMPLETED", remark="该单据不是仓位收货"),
                                              user_id=user_id, partner_id=partner_id, username=operator_name)

    res = deal_auto_material_convert(branch_type=branch_type, branch_id=branch_id, sub_branch_id=sub_branch_id,
                                     doc_id=doc_id, flow_id=flow_id, sub_doc_type=sub_doc_type,
                                     doc_type=doc_type, opened_position=opened_position,
                                     operator_name=operator_name, partner_id=partner_id, user_id=user_id)

    return res


def deal_auto_inner_transfer(branch_type=None, branch_id=None, sub_branch_id=None, doc_type=None,
                             doc_id=None, flow_id=None, operator_name=None, partner_id=None, user_id=None):
    """处理自动内部调拨
    组织开启多仓位才进行自动内部调拨业务
    """
    # 只拉取自动调拨规则进行生成调拨，用调出仓位(也就是收货仓位)和收货的物料进行筛选调拨规则
    origin_products_map = get_origin_receive_products_map(receipt_id=doc_id, doc_type=doc_type, partner_id=partner_id)
    # 根据调出仓位和组织id拉取自动内部调拨规则列表
    inner_transfer_config = metadata_service.get_position_transfer_config(branch_ids=[str(branch_id)],
                                                                          branch_type=branch_type,
                                                                          out_position=sub_branch_id,
                                                                          partner_id=partner_id, user_id=user_id)
    if not inner_transfer_config:
        # 没拉到调拨规则直接更新工作流表为COMPLETED,并创建下一个业务流转(物料转换)
        flow_id = flowDB.update_flow_logs(flow_id=flow_id,
                                          update_data=dict(process_status="COMPLETED", remark="该组织未配置内部调拨规则"),
                                          user_id=user_id, partner_id=partner_id, username=operator_name)

        return flow_id
    # 用来保存调入仓位(唯一)对应的商品列表
    #   {"inWh1": [prd_id,....], "inWh2": [...]}
    in_position_product_map = {}
    # 最终参与自动调拨的商品id列表
    inner_transfer_products = []
    # 拉取到调拨规则根据调入仓位的不同进行拆成不同内部调拨单
    for trans in inner_transfer_config:
        "{code,in_whs,material_id,name,out_whs}"
        in_position = convert_to_int(trans.get('in_whs', 0))
        material_id = convert_to_int(trans.get('material_id'))
        if material_id not in origin_products_map.keys():
            # 把不在收货单里的商品筛出去
            continue
        if material_id not in inner_transfer_products:
            inner_transfer_products.append(material_id)
        if in_position not in in_position_product_map.keys():
            in_position_product_map[in_position] = [material_id]
        else:
            if material_id not in in_position_product_map[in_position]:
                in_position_product_map[in_position].append(material_id)
    logging.info("[create_inner_transfer]共有{}个内部调拨单需要生成".format(len(in_position_product_map.keys())))
    if len(in_position_product_map.keys()) == 0:
        # 原单据商品无对应调拨规则，直接更新工作流表为COMPLETED，并创建下一个业务流转(物料转换)
        flow_id = flowDB.update_flow_logs(flow_id=flow_id,
                                          update_data=dict(process_status="COMPLETED", remark="原单据商品无对应调拨规则"),
                                          user_id=user_id, partner_id=partner_id, username=operator_name)
        return flow_id
    # 整理好的创建内部自动调拨请求参数列表
    create_transfer_request_list = []
    # 保存经过物料转换的商品列表到ReceiptFlowProductLog中, 用于补偿
    flow_pro_list = []
    for in_position, product_ids in in_position_product_map.items():
        request_id = gen_snowflake_id()
        business_date = datetime.utcnow()
        create_transfer_request = dict(
            request_id=request_id,
            shipping_store=branch_id,
            remark="自动内部调拨-{}".format(doc_id),
            shipping_date=get_timestamp(business_date),
            transfer_date=get_timestamp(business_date),
            branch_type=branch_type,
            type="AUTO",
            sub_type="INTERNAL",
            receiving_position=in_position,
            shipping_position=sub_branch_id,
            sub_account_type="position"
        )
        products = []
        for p_id in product_ids:
            # 内部调拨, 收货什么单位就调什么单位
            product_map = origin_products_map.get(p_id) if origin_products_map.get(p_id) else {}
            unit_id = product_map.get('unit_id')
            quantity = product_map.get('quantity', 0)
            row = dict(
                product_id=p_id,
                unit_id=unit_id,
                quantity=quantity
            )
            products.append(row)
            flow_pro_list.append(dict(
                id=gen_snowflake_id(),
                status="INITED",
                flow_id=flow_id,
                batch_id=request_id,
                business_date=business_date,
                type='AUTO_TRANSFER',
                reusable=False,
                doc_id=doc_id,
                receiving_position=in_position,
                product_id=p_id,
                unit_id=unit_id,
                quantity=quantity,
                partner_id=partner_id,
                created_by=user_id,
                create_name=operator_name,
                updated_by=user_id,
                updated_name=operator_name
            ))
        create_transfer_request["products"] = products
        create_transfer_request_list.append(create_transfer_request)

    # 一次性把所有参与内部调拨的商品以及批次保存起来
    flowProDB.create_flow_product_logs(data_list=flow_pro_list)
    # 更新工作流表记录状态为: PROCESSING
    _ = flowDB.update_flow_logs(flow_id=flow_id, update_data=dict(process_status="PROCESSING"),
                                user_id=user_id, partner_id=partner_id, username=operator_name)
    # 再调用supply生成自动内部调拨单
    created_num = 0
    for request in create_transfer_request_list:
        create_transfer_request = CreateTransferRequest(**request)
        transfer_id = transfer_service.create_inner_transfer(request=create_transfer_request, partner_id=partner_id,
                                                             user_id=user_id, username=operator_name)
        request_id = request.get('request_id')
        created_num += 1
        logging.info("[create_inner_transfer]正在创建第{}个内部调拨单, res:{}".format(created_num, transfer_id))
        if transfer_id:
            logging.info("[create_inner_transfer]成功创建第{}个内部调拨单, transfer_id:{}".format(created_num, transfer_id))
            # 通过request_id更新该批次商品状态为SUCCESS
            flowProDB.update_flow_product_logs(batch_id=request_id, status="SUCCESS", partner_id=partner_id,
                                               operator_name=operator_name, user_id=user_id)

    # 批次全部创建成功，更新工作流表, 状态标记为已完成COMPLETED，并创建下一个操作的记录
    if created_num == len(in_position_product_map.keys()):
        flow_id = flowDB.update_flow_logs(flow_id=flow_id, update_data=dict(process_status="COMPLETED"),
                                          user_id=user_id, partner_id=partner_id, username=operator_name)
    return flow_id


def deal_auto_material_convert(branch_type=None, branch_id=None, sub_branch_id=None, doc_id=None,
                               flow_id=None, sub_doc_type=None, doc_type=None,
                               opened_position=None, operator_name=None, partner_id=None, user_id=None):
    """处理完自动调拨，处理自动物料转换
    先拉取总部配置的物料转换规则
    通过当前单据的组织id和规则类型(自动)筛选
    """
    auto_convert_rules = metadata_service.get_material_convert_rules(filters={"type": "automatic",
                                                                              "status": "ENABLED"},
                                                                     return_fields="id",
                                                                     partner_id=partner_id,
                                                                     user_id=user_id)
    auto_convert_rule_ids = []
    if auto_convert_rules:
        for row in auto_convert_rules:
            auto_convert_rule_ids.append(row.get('id'))
    if not auto_convert_rule_ids:
        # 没有规则不进行物料转换
        _ = flowDB.update_flow_logs(flow_id=flow_id,
                                    update_data=dict(process_status="COMPLETED", remark="总部没有自动物料规则"),
                                    user_id=user_id, partner_id=partner_id, username=operator_name)
        return True
    # 再通过总部配置的自动物料转换规则id筛选出门店下生效的规则
    material_convert_config = metadata_service.get_position_material_config(branch_ids=[str(branch_id)],
                                                                            branch_type=branch_type,
                                                                            material_rules=auto_convert_rule_ids,
                                                                            partner_id=partner_id,
                                                                            user_id=user_id)
    # ruleRange = "store" --> "deliveryReceive": false
    # ruleRange = "whs" --> "deliveryReceive": {4404605329568956417: true}
    allowed_rules = []
    if material_convert_config:
        doc_type_checked = False  # 单据类型校验是否允许物料转换标记
        for m in material_convert_config:
            fields = m.get('fields') if m.get('fields') else {}
            rule_switch = fields.get('ruleSwitch')
            # 规则未开启略过
            if not rule_switch:
                continue
            rule_range = fields.get('ruleRange')
            checked = fields.get('checked') if fields.get('checked') else {}
            if sub_doc_type == "position":
                if rule_range == "whs":
                    # 筛选出对仓位生效的规则
                    # 存的doc_type和业务配置中值一致才可以这么取
                    doc_type_checked_p = checked.get(doc_type) if checked.get(doc_type) else {}
                    doc_type_checked = doc_type_checked_p.get(str(sub_branch_id))
                if rule_range == "store":
                    doc_type_checked = checked.get(doc_type)  # 存的doc_type和业务配置中值一致才可以这么取
            if sub_doc_type == "store" and rule_range == "store":
                # 筛选出对门店生效的规则
                doc_type_checked = checked.get(doc_type)  # 存的doc_type和业务配置中值一致才可以这么取
            if doc_type_checked is True:
                allowed_rules.append(convert_to_int(fields.get('material_id')) if fields.get('material_id') else 0)
    logging.info("[auto_material_convert]对该组织生效的物料规则有{}个：{}".format(len(allowed_rules), allowed_rules))
    if len(allowed_rules) == 0:
        logging.warning("[auto_material_convert]对该组织生效的物料转换规则为空，直接返回")
        _ = flowDB.update_flow_logs(flow_id=flow_id,
                                    update_data=dict(process_status="COMPLETED", remark="该业务单据未配置物料转换规则"),
                                    user_id=user_id, partner_id=partner_id, username=operator_name)
        return True
    valid_convert_rules = metadata_service.get_material_convert_rules(ids=allowed_rules,
                                                                      filters={"status": "ENABLED"},
                                                                      partner_id=partner_id,
                                                                      user_id=user_id)

    final_convert_rules = []  # 最终对该门店生效的转换规则
    convert_product_ids = []  # 最终生成物料转换单的原物料id列表, 全部生成成功需要保存在商品log表中
    target_material_ids = []  # 最终生效目标物料id列表

    # 捞出当前单据商品中已经进行其他业务操作的商品列表, 并和原单据商品做差
    origin_products_map = get_origin_receive_products_map(receipt_id=doc_id, doc_type=doc_type, partner_id=partner_id)
    flow_product_ids = flowProDB.get_flow_product_ids(doc_id=doc_id, reusable=False, partner_id=partner_id)
    flow_product_ids = list(set(list(origin_products_map.keys())) - set(flow_product_ids))
    logging.info("[auto_material_convert]原单据商品：{}".format(list(origin_products_map.keys())))
    logging.info("[auto_material_convert]原单据允许物料转换的商品：{}".format(flow_product_ids))

    for r in valid_convert_rules:
        fields = r.get('fields') if r.get('fields') else {}
        material = fields.get('material') if fields.get('material') else {}
        material_id = convert_to_int(material.get('material_id')) if material.get('material_id') else None
        # 如果生效的规则中商品在原单据中且未做其他业务操作，把生效的规则存起来
        if material_id in flow_product_ids:
            convert_product_ids.append(material_id)
            target_material = fields.get('targetMaterial') if fields.get('targetMaterial') else {}
            target_material_id = convert_to_int(target_material.get('material_id')) if target_material.get(
                'material_id') else None
            target_material_ids.append(target_material_id)
            final_convert_rules.append(dict(convert_rule=convert_to_int(r.get('id')),
                                            material=material, target_material=target_material))

    logging.info("[auto_material_convert]共有{}个物料转换单需要创建".format(len(final_convert_rules)))
    if not final_convert_rules:
        # 没有物料转换单直接更新工作流状态为已完成并返回
        _ = flowDB.update_flow_logs(flow_id=flow_id,
                                    update_data=dict(process_status="COMPLETED", remark="原单据无允许物料转换商品"),
                                    user_id=user_id, partner_id=partner_id, username=operator_name)
        return True
    # 拉取目标物料商品主档做成map
    target_material_map = get_product_map(product_ids=target_material_ids, partner_id=partner_id, user_id=user_id)
    # 拉取商品单位和转换率
    product_unit_rate_map = get_product_unit_rate_map(product_ids=convert_product_ids,
                                                      partner_id=partner_id, user_id=user_id)
    # 拉取门店主档/仓位主档
    branch_map = get_branch_map(branch_id=branch_id, branch_type=branch_type, partner_id=partner_id, user_id=user_id)
    # 拉取单位主档map
    unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
    if sub_branch_id:
        position_map = get_branch_map(branch_id=sub_branch_id, branch_type="POSITION", partner_id=partner_id,
                                      user_id=user_id)
    else:
        position_map = {}
    # 保存经过物料转换的商品列表到ReceiptFlowProductLog中
    flow_pro_list = []
    create_convert_requests = []
    for rule in final_convert_rules:
        material = rule.get('material', {})
        target_material = rule.get('target_material', {})
        material_id = convert_to_int(material.get('material_id'))
        target_material_id = convert_to_int(target_material.get('material_id'))
        target_unit_id = convert_to_int(target_material.get('unit_id'))
        target_product = target_material_map.get(target_material_id) if target_material_map.get(
            target_material_id) else {}
        convert_rate = convert_to_decimal(target_material.get('material_number', 1)) / convert_to_decimal(
            material.get('material_number', 1))
        product_map = origin_products_map.get(material_id, {})
        origin_unit_id = product_map.get('unit_id')  # 原单据单位
        convert_unit_id = convert_to_int(material.get('unit_id'))  # 物料转换配置的单位
        origin_quantity = convert_to_decimal(product_map.get('quantity', 0))

        if origin_unit_id == convert_unit_id:
            # 如果收货单里的商品单位和物料转换单位一致不需要进行单位数量转换
            quantity = origin_quantity
        else:
            # 否则需要进行单位数量转换
            product_unit = product_unit_rate_map.get(material_id) if product_unit_rate_map.get(material_id) else {}
            unit_rate_org = convert_to_decimal(product_unit.get(origin_unit_id, 1))
            unit_rate_cov = convert_to_decimal(product_unit.get(convert_unit_id, 1))
            origin_quantity_accounting = unit_rate_org * origin_quantity
            quantity = origin_quantity_accounting / unit_rate_cov
        request_id = gen_snowflake_id()
        business_date = datetime.utcnow()
        convert_rule = rule.get('convert_rule')
        materials = [dict(
            product_id=material_id,
            product_code=product_map.get('product_code'),
            product_name=product_map.get('product_name'),
            product_type="",
            unit_id=convert_unit_id,
            unit_name=unit_map.get(convert_unit_id).get('name') if unit_map.get(convert_unit_id) else "",
            unit_spec="",
            quantity=quantity,
            type="origin",
        ),
            dict(
                product_id=target_material_id,
                product_code=target_product.get('code'),
                product_name=target_product.get('name'),
                product_type=target_product.get('product_type'),
                unit_id=target_unit_id,
                unit_name=unit_map.get(target_unit_id).get('name') if unit_map.get(target_unit_id) else "",
                unit_spec="",
                quantity=quantity * convert_rate,
                type="target")
        ]
        create_convert_request = dict(
            branch_type=branch_type,
            convert_type="AUTO",
            convert_date=get_timestamp(business_date),
            branch_id=branch_id,
            branch_code=branch_map.get('code'),
            branch_name=branch_map.get('name'),
            position_id=sub_branch_id,
            position_name=position_map.get('name'),
            position_code=position_map.get('code'),
            convert_rule=convert_rule,
            opened_position=opened_position,
            remark=str(doc_id),
            auto_confirm=True,  # 默认自动确认
            request_id=request_id,
            materials=materials,
        )
        create_convert_requests.append(create_convert_request)
        flow_pro_list.append(dict(
            id=gen_snowflake_id(),
            status="INITED",
            flow_id=flow_id,
            batch_id=request_id,
            business_date=business_date,
            type='AUTO_CONVERT',
            reusable=False,
            doc_id=doc_id,
            rule_id=convert_rule,
            receiving_position=sub_branch_id,
            product_id=material_id,
            target_product_id=target_material_id,
            unit_id=material.get('unit_id'),
            target_unit_id=target_unit_id,
            quantity=quantity,
            target_quantity=quantity * convert_rate,
            partner_id=partner_id,
            created_by=user_id,
            create_name=operator_name,
            updated_by=user_id,
            updated_name=operator_name
        ))
    # 一次性把所有参与物料转换的商品以及批次保存起来
    flowProDB.create_flow_product_logs(data_list=flow_pro_list)
    # 更新工作流表记录状态为: PROCESSING
    _ = flowDB.update_flow_logs(flow_id=flow_id, update_data=dict(process_status="PROCESSING"),
                                user_id=user_id, partner_id=partner_id, username=operator_name)
    # 再批量调用supply服务生成自动物料转换单
    success_num = 0
    for create_convert_request in create_convert_requests:
        try:
            request = CreateMaterialConvertRequest(**create_convert_request)
            res = material_convert_service.create_material_convert(request=request, user_id=user_id, partner_id=partner_id)
            request_id = create_convert_request.get('request_id')
            if res and res.get('convert_id'):
                success_num += 1
                logging.info("[auto_material_convert]创建第{}个自动物料转换结果：{}".format(success_num, res.get('convert_id')))
                # 通过request_id更新该批次商品状态为SUCCESS
                flowProDB.update_flow_product_logs(batch_id=request_id, status="SUCCESS", partner_id=partner_id,
                                                   operator_name=operator_name, user_id=user_id)
        except Exception as e:
            logging.warning("[auto_material_convert] 创建第自动物料转换失败：{}".format(e))
            continue

    auto_convert_result = False
    if success_num == len(final_convert_rules):
        auto_convert_result = True
    if auto_convert_result is True:
        # 更新工作流表, 状态标记为已完成COMPLETED
        _ = flowDB.update_flow_logs(flow_id=flow_id, update_data=dict(process_status="COMPLETED"),
                                    user_id=user_id, partner_id=partner_id, username=operator_name)
        logging.info("[auto_material_convert]自动物料转换单全部创建成功-{}".format(success_num))

    return auto_convert_result


def recover_auto_inner_transfer(branch_type=None, branch_id=None, sub_branch_id=None, doc_id=None, flow_id=None,
                                partner_id=None, user_id=None):
    """补偿自动内部调拨处理失败的单据
    """
    # 把未处理成功的调拨商品(状态为INITED)的商品批次id捞出来, 一个批次一个批次补偿
    count, batch_ids = flowProDB.list_flow_product_batch_ids(flow_id=flow_id, status="INITED")
    logging.info("[recover_auto_inner_transfer]共有{}个批次的内部调拨单需要补偿".format(count))
    operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)

    success_num = 0
    for index, batch_id in enumerate(batch_ids):
        p_count, flow_products = flowProDB.list_flow_product_logs(batch_ids=[batch_id], status="INITED",
                                                                  partner_id=partner_id)
        logging.info("[recover_auto_inner_transfer]正在补偿第{}个批次调拨单据: batch_id={} 共{}个商品".format(index + 1,
                                                                                              batch_id, p_count))
        if not flow_products:
            continue
        business_date = flow_products[0].business_date
        receiving_position = flow_products[0].receiving_position
        create_transfer_request = dict(
            request_id=batch_id,
            shipping_store=branch_id,
            remark="自动内部调拨-{}".format(doc_id),
            shipping_date=get_timestamp(business_date if not business_date else datetime.utcnow()),
            transfer_date=get_timestamp(business_date if not business_date else datetime.utcnow()),
            branch_type=branch_type,
            type="AUTO",
            sub_type="INTERNAL",
            receiving_position=receiving_position,
            shipping_position=sub_branch_id,
            sub_account_type="position"
        )
        products = []
        for p in flow_products:
            # 内部调拨收货什么单位就调什么单位
            row = dict(
                product_id=p.product_id,
                unit_id=p.unit_id,
                quantity=p.quantity
            )
            products.append(row)
        create_transfer_request["products"] = products
        request = CreateTransferRequest(**create_transfer_request)
        transfer_id = transfer_service.create_inner_transfer(request=request, partner_id=partner_id,
                                                             user_id=user_id, username=operator_name)
        time.sleep(1)   # 控制调用频率
        if transfer_id:
            success_num += 1
            logging.info("[recover_auto_inner_transfer]成功补偿创建第{}个内部调拨单, transfer_id:{}".format(success_num,
                                                                                               transfer_id))
            # 通过request_id更新该批次商品状态为SUCCESS
            flowProDB.update_flow_product_logs(batch_id=batch_id, status="SUCCESS", partner_id=partner_id,
                                               operator_name=operator_name, user_id=user_id)

    # 批次全部创建成功，更新工作流表, 状态标记为已完成COMPLETED，并创建下一个操作的记录
    if success_num == count:
        flow_id = flowDB.update_flow_logs(flow_id=flow_id, update_data=dict(process_status="COMPLETED"),
                                          user_id=user_id, partner_id=partner_id, username=operator_name)
    return flow_id


def recover_auto_material_convert(branch_type=None, branch_id=None, sub_branch_id=None, doc_id=None, flow_id=None,
                                  partner_id=None, user_id=None):
    """补偿自动物料转换处理失败的商品批次
    """
    # 把未处理成功的物料转换商品(状态为INITED)的商品批次id捞出来, 一个批次一个批次补偿
    count, batch_ids = flowProDB.list_flow_product_batch_ids(flow_id=flow_id, status="INITED")
    logging.info("[recover_auto_material_convert]共有{}个批次的物料转换单需要补偿".format(count))
    _, flow_products = flowProDB.list_flow_product_logs(batch_ids=batch_ids, partner_id=partner_id)
    flow_products_ids = []
    for p in flow_products:
        flow_products_ids.append(p.product_id)
        flow_products_ids.append(p.target_product_id)
    product_map = get_product_map(product_ids=flow_products_ids, partner_id=partner_id, user_id=user_id)
    operator_name = metadata_service.get_username_by_pid_uid(partner_id, user_id)
    # 拉取门店主档/仓位主档
    branch_map = get_branch_map(branch_id=branch_id, branch_type=branch_type, partner_id=partner_id, user_id=user_id)
    # 拉取单位主档map
    unit_map = get_unit_map(partner_id=partner_id, user_id=user_id)
    if sub_branch_id:
        opened_position = True
        position_map = get_branch_map(branch_id=sub_branch_id, branch_type="POSITION", partner_id=partner_id,
                                      user_id=user_id)
    else:
        opened_position = False
        position_map = {}
    success_num = 0
    for index, batch_id in enumerate(batch_ids):
        p_count, flow_products = flowProDB.list_flow_product_logs(batch_ids=[batch_id], status="INITED",
                                                                  partner_id=partner_id)
        logging.info("[recover_auto_material_convert]正在补偿第{}个批次物料转换单据: batch_id={} 共{}个商品".format(index + 1,
                                                                                                  batch_id, p_count))
        if not flow_products:
            continue
        business_date = flow_products[0].business_date
        for p in flow_products:
            product = product_map.get(p.product_id) if product_map.get(p.product_id) else {}
            materials = [dict(
                product_id=p.product_id,
                product_code=product.get('code'),
                product_name=product.get('name'),
                product_type=product.get('product_type'),
                unit_id=p.unit_id,
                unit_name=unit_map.get(p.unit_id).get('name') if unit_map.get(p.unit_id) else "",
                unit_spec="",
                quantity=p.quantity,
                type="origin",
            ),
                dict(
                    product_id=p.target_product_id,
                    product_code=product.get('code'),
                    product_name=product.get('name'),
                    product_type=product.get('product_type'),
                    unit_id=p.target_unit_id,
                    unit_name=unit_map.get(p.target_unit_id).get('name') if unit_map.get(p.target_unit_id) else "",
                    unit_spec="",
                    quantity=p.target_quantity,
                    type="target")
            ]
            create_convert_request = dict(
                branch_type=branch_type,
                convert_type="AUTO",
                convert_date=get_timestamp(business_date),
                branch_id=branch_id,
                branch_code=branch_map.get('code'),
                branch_name=branch_map.get('name'),
                position_id=sub_branch_id,
                position_name=position_map.get('name'),
                position_code=position_map.get('code'),
                convert_rule=p.rule_id,
                opened_position=opened_position,
                remark=str(doc_id),
                request_id=batch_id,
                materials=materials,
            )

            # 再生成自动物料转换单
            request = CreateMaterialConvertRequest(**create_convert_request)
            res = material_convert_service.create_material_convert(request=request, user_id=user_id,
                                                                   partner_id=partner_id)
            time.sleep(1)  # 控制跨服务调用频率
            if res and res.get('convert_id'):
                success_num += 1
                convert_id = res.get('convert_id')
                logging.info("[recover_auto_material_convert]成功补偿创建{}个内部调拨单, convert_id:{}".format(success_num,
                                                                                                   convert_id))
                # 通过request_id更新该批次商品状态为SUCCESS
                flowProDB.update_flow_product_logs(batch_id=batch_id, status="SUCCESS", partner_id=partner_id,
                                                   operator_name=operator_name, user_id=user_id)

    # 批次全部创建成功，更新工作流表, 状态标记为已完成COMPLETED
    if success_num == count:
        _ = flowDB.update_flow_logs(flow_id=flow_id, update_data=dict(process_status="COMPLETED"),
                                    user_id=user_id, partner_id=partner_id, username=operator_name)
    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RETRY_HANDLE_FLOW_TASK)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RETRY_HANDLE_FLOW_TASK)
def handle_retry_flow_task(message):
    """补偿工作流表状态为INITED的任务"""
    partner_id = convert_to_int(message.get('partner_id'))
    user_id = convert_to_int(message.get('user_id'))
    if not all([partner_id, user_id]):
        logging.warning("[HANDLE_RETRY_FLOW_TASK]缺少任务处理必要参数！")
        return True
    # logging.info("[HANDLE_RETRY_FLOW_TASK]接收到补偿工作流任务消息")
    count, flow_logs = flowDB.list_flow_logs(process_status="INITED", partner_id=partner_id)
    if count:
        logging.info("[HANDLE_RETRY_FLOW_TASK]共有{}个未处理的flow task".format(count))
    if flow_logs:
        for index, flow in enumerate(flow_logs):
            message = {
                'flow_id': flow.id,
                'partner_id': partner_id,
                'user_id': user_id
            }
            logging.info("[HANDLE_RETRY_FLOW_TASK] 正在补偿第{}个工作流:{}".format(index + 1, flow.id))
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.HANDLE_FLOW_TASK, message=message)
            time.sleep(0.5)
    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RETRY_HANDLE_FLOW_TASK_BATCH)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RETRY_HANDLE_FLOW_TASK_BATCH)
def handle_retry_flow_task_batch(message):
    """补偿工作流表状态为PROCESSING的任务
    并捞取商品处理批次表明细"""
    partner_id = convert_to_int(message.get('partner_id'))
    user_id = convert_to_int(message.get('user_id'))
    if not all([partner_id, user_id]):
        logging.warning("[HANDLE_RETRY_FLOW_TASK_BATCH]缺少任务处理必要参数！")
        return True
    # logging.info("[HANDLE_RETRY_FLOW_TASK_BATCH]接收到补偿工作流批次任务消息")
    count, flow_logs = flowDB.list_flow_logs(process_status="PROCESSING", partner_id=partner_id)
    if count:
        logging.info("[HANDLE_RETRY_FLOW_TASK_BATCH]共有{}个未处理成功的flow task".format(count))
    if flow_logs:
        for index, flow in enumerate(flow_logs):
            flow_id = flow.id
            doc_id = flow.doc_id
            branch_id = flow.branch_id
            sub_branch_id = flow.sub_branch_id
            operator_name = flow.created_name
            sub_doc_type = flow.sub_doc_type
            doc_type = flow.doc_type
            branch_type = None
            if doc_type in ["transfer", "insideTransfer"]:
                # 查询内/外部调拨单据
                transfer = TransferRepository().get_transfer_by_id(transfer_id=doc_id, partner_id=partner_id)
                branch_type = transfer.branch_type
            if doc_type in ["packingOrder", "processOrder"]:
                branch_type = "MACHINING_CENTER"
            if not branch_type:
                logging.warning("[HANDLE_RETRY_FLOW_TASK_BATCH]branch_type不合理, 直接跳过 - {}".format(branch_type))
                continue
            if flow.operation == "INNER_TRANSFER" and sub_branch_id:
                # 补偿内部调拨
                flow_id = recover_auto_inner_transfer(branch_type=branch_type, doc_id=doc_id, flow_id=flow_id,
                                                      branch_id=branch_id, sub_branch_id=sub_branch_id,
                                                      partner_id=partner_id, user_id=user_id)
                # 处理完内部调拨接着要处理物料转换
                res = deal_auto_material_convert(branch_type=branch_type, branch_id=branch_id,
                                                 sub_branch_id=sub_branch_id,
                                                 doc_id=doc_id, flow_id=flow_id, sub_doc_type=sub_doc_type,
                                                 doc_type=doc_type, opened_position=True,
                                                 operator_name=operator_name, partner_id=partner_id, user_id=user_id)
                logging.info("[HANDLE_RETRY_FLOW_TASK_BATCH]补偿内部调拨：".format(res))
            if flow.operation == "MATERIAL_CONVERT":
                # 补偿物料转换
                res = recover_auto_material_convert(branch_type=branch_type, branch_id=branch_id,
                                                    sub_branch_id=sub_branch_id, doc_id=doc_id, flow_id=flow_id,
                                                    partner_id=partner_id, user_id=user_id)
                logging.info("[HANDLE_RETRY_FLOW_TASK_BATCH]补偿物料转换：".format(res))

    return True
