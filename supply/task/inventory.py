import json
import logging
from supply.utils.helper import MessageTopic, convert_to_int, convert_to_decimal
from supply.driver.mq import register_mq_task, mq_producer
from ..model.inventory import inventory_repository
from ..client.metadata_service import metadata_service
from supply.module.inventory_handle import inventory_handler
from datetime import datetime, timedelta
from . import partner_task_distribution


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.INVENTORY_CALCULATE_TOPIC)
def inventory_calculated_handler(message):
    """
    目前可以处理加、减、调拨库存操作
    """
    logging.info('** start calculating inventory - %s **' % message)
    id  = message.get('id')
    if id:
        error_list = inventory_repository.get_need_re_calculate_send_message_by_id(id)
        if len(error_list)>0:
            message = json.loads(error_list[0]['message'])
    return inventory_handler(message)


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.INVENTORY_DIFF_TOPIC)
def inventory_diff_handler(message):
    partner_id = message['partner_id'] if 'partner_id' in message else None
    user_id = message['user_id'] if 'user_id' in message else None
    stores = metadata_service.get_store_list(
        filters={"open_status__in": ["OPENED"],
                 "status__in": ["ENABLED"]},
        return_fields='id,code,name',
        partner_id=partner_id,
        user_id=user_id
    ).get('rows', [])
    for store in stores:
        data = dict(
            partner_id=partner_id,
            user_id=user_id,
            store_id=int(store['id']),
            store_code=store.get('code'),
            store_name=store.get('name')
        )
        mq_producer.publish(MessageTopic.SUPPLY_GROUP,
                            MessageTopic.INVENTORY_DIFF_STORE_TOPIC, data)
    return True


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.RE_INVENTORY_CALCULATE_TOPIC)
@partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.RE_INVENTORY_CALCULATE_TOPIC)
def re_inventory_diff_handler(message):
    partner_id = message['partner_id'] if 'partner_id' in message else None
    days = message.get('days') if message.get('days') else 1
    now = datetime.utcnow() - timedelta(days=days)
    date_time = datetime(now.year, now.month, now.day)
    logging.info("[re_inventory_diff_handler] query date_time{}".format(date_time))
    error_list = inventory_repository.get_need_re_calculate_send_message(date_time, partner_id=partner_id)
    if len(error_list):
        logging.info("[re_inventory_diff_handler] partner-{} 未处理的库存请求有{}个".format(partner_id, len(error_list)))
    if len(error_list) > 0:
        for error in error_list:
            message_to = {}
            message_to['id'] = error['id']
            mq_producer.publish(MessageTopic.SUPPLY_GROUP,
                                MessageTopic.INVENTORY_CALCULATE_TOPIC, message_to)
    return True
