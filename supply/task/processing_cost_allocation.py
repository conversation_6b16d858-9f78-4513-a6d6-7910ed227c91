import logging

from dateutil.relativedelta import relativedelta
from supply.client.metadata_service import metadata_service
from decimal import Decimal
from datetime import date, timedelta, datetime
from supply.utils.helper import MessageTopic, convert_to_int, get_product_unit_rate_map, get_uuids, \
    get_product_unit_map, get_month_first_and_last_date, get_safe_value
from supply.model.manufactory.processing_cost import processing_cost_db
from supply.model.manufactory.processing_receipts import processing_receipts_db
from supply.driver.mq import register_mq_task, mq_producer
from . import partner_task_distribution
from ..client.infra_metadata_service import metadata_center_service

"""
    处理加工费用分摊任务以及补偿
"""


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.PROCESSING_COST_ALLOCATION)
def processing_cost_allocation_main(message):
    """处理加工费用分摊主处理任务，加工费用单审核时触发该任务：
        1、根据加工费用单属于哪一个加工中心和仓位下捞取对应的加工单
        2、统计该仓位下的各个商品数量，并根据加工费用配置的单位算出总价格，然后固化在一张中间表
        3、传输成本中心(后期改成了成本服务自己同步数据，不主动传输)
    2021.9.4优化更新:
        1、支持一个账期内重复计算分摊加工费用
        加工费用提前录入，每天重新算一次，不再进行补偿
        当前日期大于本账期结束日期24h时将加工费用单更新为终态不再进行计算
    """
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    receipt_id = convert_to_int(message['receipt_id']) if 'receipt_id' in message else None
    product_ids = message.get('product_ids') if message.get('product_ids') else []
    logging.info("Received processing_cost_allocation nsq message: {}-{}-{}".format(partner_id, user_id, receipt_id))
    if not all([partner_id, user_id, receipt_id]):
        logging.warning("[processing_cost_allocation_main]Missing required field - partner_id/user_id/receipt_id")
        return True

    processing_cost = processing_cost_db.get_processing_cost_by_id(receipt_id=receipt_id, partner_id=partner_id)
    if not processing_cost:
        logging.warning("[processing_cost_allocation_main]Processing Cost Receipt Not Found")
        return True
    if processing_cost.status != "APPROVED":
        return True
    if processing_cost.process_status == "SUCCESS":
        logging.warning("[processing_cost_allocation_main]Processing Cost Receipt Allocation is SUCCESS!")
        return True
    username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
    # 考虑要不要记录操作状态变更log
    # month = processing_cost.month
    period_id = processing_cost.period_id
    machining_center_id = processing_cost.machining_center_id
    position_id = processing_cost.position_id
    processing_cost_unit = processing_cost.unit_id
    # year = int(str(month).split('-')[0])
    # month = int(str(month).split('-')[1])
    # first_day, last_day = get_month_first_and_last_date(year=year,
    #                                                     month=month,
    #                                                     return_utc=True, utc_offset=8)
    # 2021-7-14 根据账期ID查询账期范围
    period_list = metadata_center_service.list_entity(schema_name="PERIOD", ids=[period_id],
                                                      partner_id=partner_id,
                                                      user_id=user_id).get('rows', [])
    if len(period_list) <= 0:
        logging.warning("Period data not found: {}".format(period_id))
        return True
    try:
        fields = get_safe_value(period_list[0], 'fields', {})
        start_datetime = fields.get('start_datetime')
        end_datetime = fields.get('end_datetime')
        start_datetime = datetime.strptime(start_datetime, "%Y-%m-%dT%H:%M:%SZ")
        end_datetime = datetime.strptime(end_datetime, "%Y-%m-%dT%H:%M:%SZ")
    except Exception as e:
        logging.error("Period datetime parsing failed: {}".format(e))
        return True
    # 当前日期大于本账期结束日期24h时将加工费用单更新为终态不再进行计算
    if int((datetime.utcnow() - end_datetime).total_seconds()) > 24 * 3600:
        processing_cost_db.update_processing_cost(receipt_detail=dict(id=receipt_id, process_status="SUCCESS"),
                                                  partner_id=partner_id)
    # 捞取加工单，计算数量/总费用
    # 成本计算要按帐期范围来筛选，目前帐期时间范围是当月1号凌晨4:00 到次月1号凌晨4:00
    # first_day = first_day + timedelta(hours=4)      # 2020-12-31 20:00:00
    # last_day = last_day + timedelta(hours=4)        # 2021-01-31 20:00:00
    pro_receipt_data = processing_receipts_db.query_processing_detail_by_sql(start_date=start_datetime,
                                                                             end_date=end_datetime,
                                                                             machining_center_id=machining_center_id,
                                                                             position_id=position_id,
                                                                             product_ids=product_ids)
    product_ids = [int(p[2]) for p in pro_receipt_data]
    product_unit_rate_map = get_product_unit_rate_map(product_ids=product_ids, partner_id=partner_id,
                                                      user_id=user_id)
    product_unit_map = get_product_unit_map(product_ids=product_ids, partner_id=partner_id, user_id=user_id)
    add_details = []
    update_details = []
    has_error = False
    ids = get_uuids(len(pro_receipt_data))
    exist_record_map = dict()
    cost_list = processing_cost_db.list_processing_cost_allocation(partner_id=partner_id, product_ids=product_ids,
                                                                   cost_rcpt_ids=[receipt_id])
    if cost_list:
        for c in cost_list:
            exist_record_map[c.product_id] = dict(id=c.id)
    for inx, r in enumerate(pro_receipt_data):
        product_id = int(r[2])
        product_rate = product_unit_rate_map.get(product_id, {})
        product_unit = product_unit_map.get(product_id, {})
        # 加工费用单中： 当月加工单价 = 每quantity(数量): unit(计价单位) unit_cost元
        # 若加工费用单中单位转换率为 unit_rate
        # 则转换为核算数量的加工单价 = 每(quantity * unit_rate)(核算数量): unit(核算单位) unit_cost元
        #   例：本月商品 p1 加工了 q1(核算数量)的物料，核算单位下的加工单价为c1元 每 q2(核算数量)
        #   则当月该商品总加工费用为：(q1 / q2) * c1
        cost_unit_rate = product_rate.get(processing_cost_unit, 0)
        processing_unit_rate = product_rate.get(r[3], 0)
        logging.info("[processing_cost_allocation_main] cost_unit_rate: {} - processing_unit_rate: {}".format(
            cost_unit_rate, processing_unit_rate))
        row = dict(
            partner_id=partner_id,
            created_by=user_id,
            created_name=username,
            updated_by=user_id,
            updated_name=username,
            machining_center_id=machining_center_id,
            position_id=position_id,
            product_id=product_id,
            processing_unit_id=r[3],
            processing_unit_name=r[4],
            processing_quantity=r[5],
            cost_rcpt_id=receipt_id,
            cost_center_id=r[6],
            remark="",
            status="INITED"
        )
        try:
            cost_accounting_quantity = Decimal(str(processing_cost.quantity)) * Decimal(str(cost_unit_rate))
            accounting_quantity = Decimal(str(r[5])) * Decimal(str(processing_unit_rate))
            unit_cost = Decimal(str(processing_cost.unit_cost))
            amounts = (accounting_quantity / cost_accounting_quantity) * unit_cost if cost_accounting_quantity else 0
        except Exception as e:
            logging.error(e)
            row['status'] = "FAILED"
            row['remark'] = str(e)
            accounting_quantity = 0
            amounts = 0

        row['accounting_unit_id'] = product_unit.get('default', {}).get('id')
        row['accounting_unit_name'] = product_unit.get('default', {}).get('name')
        row['accounting_quantity'] = accounting_quantity
        if not cost_unit_rate:
            row['remark'] += "/该商品没有配置加工费用中的单位-{}".format(processing_cost_unit)
            row['status'] = "FAILED"
        if not processing_unit_rate:
            row['remark'] += "/商品未取到加工单位税率"
            row['status'] = "FAILED"
        if not r[6]:
            row['remark'] += "/该组织没有成本中心-{}".format(machining_center_id)
            row['status'] = "FAILED"
        row['amounts'] = amounts
        if exist_record_map.get(product_id):
            row['id'] = exist_record_map.get(product_id).get('id')
            update_details.append(row)
        else:
            row['id'] = ids[inx]
            add_details.append(row)
        # if row.get('status') == "FAILED":
        #     has_error = True

    processing_cost_db.upsert_processing_cost_allocation(add_details=add_details, update_details=update_details)
    # if res is True:
    #     if has_error is False:
    #         # 传输成本
    #         message = dict(
    #             batch_id=receipt_id,
    #             partner_id=partner_id,
    #             user_id=user_id,
    #             batch_type="PROCESSING_COST"
    #         )
    #         mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
    #                             topic=MessageTopic.SEND_TO_COST_CENTER, data=message)
    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.PROCESSING_COST_ALLOCATION_RECOVER)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.PROCESSING_COST_ALLOCATION_RECOVER)
def processing_cost_allocation_recover(message):
    """定时轮训每小时补偿加工费用未分摊成功的记录
    is_open: 补偿开关，不在计算期间可以手动关掉：bool
    month: 补偿的加工费用月份，不传默认上个月：  "yyyy-mm"
    status: 查询的加工费用单状态(预留)
    """
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    is_open = message.get('is_open')
    month = message.get('month')
    period_id = message.get('period_id')
    logging.info("Received 补偿加工费用未分摊成功的 nsq message: {}".format(message))
    if is_open is False:
        return True
    if not month:
        # 没有传月份默认取当前月份的上个月
        month = str(date.today() - relativedelta(months=1))[:7]

    init_processing_costs = processing_cost_db.list_processing_cost(partner_id=partner_id,
                                                                    period_ids=[period_id] if period_id else None,
                                                                    # start_month=month, end_month=month,
                                                                    status=["APPROVED"],
                                                                    process_status=["INITED"])
    logging.info("[processing_cost_allocation_recover] {}账期-共有{}个未处理[INITED]的加工费用单".format(period_id,
                                                                                           len(init_processing_costs)))
    if len(init_processing_costs) > 0:
        # 补偿未处理分摊的加工费用单
        for cost in init_processing_costs:
            logging.info("[processing_cost_allocation_recover] 正在补偿加工费用单分摊-{}".format(cost.id))
            msg = dict(
                partner_id=partner_id,
                user_id=user_id,
                receipt_id=cost.id
            )
            mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                topic=MessageTopic.PROCESSING_COST_ALLOCATION, message=msg)

    processing_costs = processing_cost_db.list_processing_cost(partner_id=partner_id,
                                                               period_ids=[period_id],
                                                               # start_month=month, end_month=month,
                                                               status=["APPROVED"],
                                                               process_status=["PROCESSING"])
    logging.info("[processing_cost_allocation_recover] {}账期-共有{}个未处理成功的[PROCESSING]的加工费用单".format(period_id, len(
        processing_costs)))
    if len(processing_costs) > 0:
        """
        补偿处理分摊失败的加工费用单: 分两步->
            1、根据费用单id捞出分摊记录把状态为FAILED的记录标记为删除deleted=1
            2、重新计算这些记录然后再次插入
        """
        for receipt in processing_costs:
            cost_list = processing_cost_db.list_processing_cost_allocation(partner_id=partner_id, status=["FAILED"],
                                                                           cost_rcpt_ids=[receipt.id], deleted=0)
            update_data = []
            product_ids = []
            for c in cost_list:
                update_data.append(dict(
                    id=c.id,
                    deleted=1
                ))
                product_ids.append(c.product_id)
            res = processing_cost_db.update_processing_cost_allocation(update_data=update_data, update_detail=True)
            logging.info("Deleted FAILED Processing Cost res - {}".format(res))
            if res is True:
                # 补偿未处理分摊的加工费用条目
                msg = dict(
                    partner_id=partner_id,
                    user_id=user_id,
                    receipt_id=receipt.id,
                    product_ids=product_ids
                )
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.PROCESSING_COST_ALLOCATION, message=msg)

    return True

