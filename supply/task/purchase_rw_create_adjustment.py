import logging
import json
import sys
import traceback

from supply.client.metadata_service import metadata_service
from supply.module.price_adjustment import price_adjustment_service
from supply.utils.helper import MessageTopic, convert_to_int
from supply.model.purchase_review import purchase_review_order_db
from supply.driver.mq import register_mq_task


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.PURCHASE_REVIEW_CREATE_ADJUSTMENT_RECOVER)
def purchase_review_create_adjustment_recover(message):
    """
        采购复核创建调价单补偿
        暂定每30min一次
        支持手动触发
    """
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    logging.info(
        "【Recover price adjustment】Receive purchase_review_create_adjustment_recover nsq message: {}-{}".format(
            partner_id, user_id))
    if not all([partner_id, user_id]):
        logging.info("【Recover price adjustment】Missing required field - partner_id/user_id/order_id")
        return True
    try:
        # 查询采购复核单将已复核但未生成
        review_list = purchase_review_order_db.list_purchase_review_order(partner_id=partner_id,
                                                                          user_id=user_id,
                                                                          status='COMPLETED',    # 已复核
                                                                          generate_adjust=0,
                                                                          is_modify=1)
        logging.info(
            "【Recover price adjustment】轮训补偿复核单未生成调价单有: {}-个".format(len(review_list)))
        if review_list and isinstance(review_list, list):
            for review in review_list:
                result = price_adjustment_service.create_price_adjustment(review_id=review.id,
                                                                          partner_id=partner_id,
                                                                          user_id=user_id)
                logging.info("【Recover price adjustment】采购复核单-{}-生成调价单-result={}-&adjust_code={}-&msg={}".format(
                    review.id, result.get('result'), result.get('adjust_code'), result.get('msg')))
                # 生成调价单成功复核单要更新保存调价单单号,并标记为已生成调价单
                username = metadata_service.get_username_by_pid_uid(partner_id, user_id)
                if result.get("result") is True:
                    update_data = dict(
                        id=review.id,
                        updated_by=user_id,
                        updated_name=username,
                        adjust_code=result.get('adjust_code'),
                        generate_adjust=1
                    )
                    purchase_review_order_db.update_review_order_no_product(update_data=[update_data])

    except Exception as e:
        logging.error("【Recover price adjustment】Except Error:{}-{}-{}".format(e, sys.exc_info(),
                                                                               traceback.format_exc().replace('\n',
                                                                                                              ',')))
        return False
