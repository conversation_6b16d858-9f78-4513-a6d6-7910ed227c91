from supply.driver.mq import mq_producer
from supply.utils.helper import MessageTopic
from supply import logger


class MessageServicePub(object):
    @classmethod
    def pub_message_service(cls, partner_id:int,
                                 user_id:int,
                                 scope_id:int,
                                 store_id:int,
                                 source_root_id:int,
                                 source_id:int,
                                 source_type:str,
                                 action:str,
                                 ref_source_id:int,
                                 ref_source_type:str,
                                 ref_action:str,
                                 content:dict,
                                 push_rule=None):
        """
        partner_id:uint64            商户 id,
        user_id:uint64               用户 ID,
        scope_id:uint64              商户区块 id,
        store_id:uint64              门店 id,
        source_root_id:uint64        业务数源 id, 如要货单/收获单/差异单的数据源 id 都是要货单 id,
        source_id:uint64             业务数据 id,
        source_type:string(0,10]     业务数据类型，如ORDER(要货单)、DEMAND(订货单),
        action:string(0,10]          业务操作类型，如INITED(新建)、CONFIRMED(确认)、UPLOADED(上传文件),
        ref_source_id:uint64         上一个业务数据 id，如收货单 id 的上一个业务数据 id 是订货单 id,
        ref_source_type:string(0,10] 上一个业务数据类型,
        ref_action:string(0,10]      上次业务操作类型,
        content:string<json>         消息内容，消息系统将不再对内容进行解析,
        push_rule:{
            "type":"string",         推送渠道。将在消息系统中枚举，例如 web_socket、phone、wechat等。
            "user":"List<string>",   推送人列表。
            "priority":"int",        优先级（1 最大）。默认为 1
            "successful_flag":"string",推送的成功的条件，默认值：所有都确认成功。将提供枚举：发出请求、任一人、半数人（人数为奇数与大半数人相同）、大半数人、全部人。
            "delayed":"int",         延迟时间，单位 ms，默认值为 5000。分为几种情况：1. 没有更大优先级，且没设置此参数，将直接推送。2. 没有更大优先级，但设置此参数，将延迟指定时间推送。3. 有更大优先级，且上一优先级在延迟时间内未确认推送成功，将取消上一级推送，并开启本级推送。
            "ignore_pre_priority_success":"bool" 忽略上一优先级是否推送成功，默认值为 false。当值为 true 时，将忽略上一次推送是否成功，在延迟指定时间后开始本级推送。**注意：**依赖上级推送是否被调用，如果上级推送没有被调用，将无法执行到本级推送。
        }
        """

        message_flow = {
            "message": {
            },
            "version": "master",
            "push_rule": [
                {
                    "type": "web_socket"
                }
            ]
        }
        if push_rule is not None and isinstance(push_rule, list):
            if len(push_rule) == 0:
                message_flow["push_rule"] = []
            else:
                for rule in push_rule:
                    rule_={}
                    rule_['type']=rule
                    message_flow["push_rule"].append(rule_)
        message_flow["message"] = dict(
            partner_id=partner_id,
            user_id=user_id,
            scope_id=scope_id,
            store_id=store_id,
            source_root_id=source_root_id,
            source_id=source_id,
            source_type=source_type,
            action=action,
            ref_source_id=ref_source_id,
            ref_source_type=ref_source_type,
            ref_action=ref_action,
            content=content
        )

        # logger.info("supply_to_pda_message:{}".format(message_flow))
        mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.MESSAGE_SERVER,
                            message=message_flow)