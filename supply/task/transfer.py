from datetime import datetime, timedelta
# from supply.task import nsq_bind
from supply.driver.mq import register_mq_task, mq_producer
from supply.utils.helper import MessageTopic, convert_to_int
from . import partner_task_distribution

# 迁移至automated_task.py
# from supply.module.transfer_service import ssm


# @partner_task_distribution(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CONFRIM_TRANSFER)
# def auto_confirm_transfer(message):
#     '''
#     自动收货——调拨
#     '''
#     partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
#     user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
#     transfer_date = message.get('end_date')
#     status = message.get("status")
#     overdays = message.get('overdays', 30)
#     if transfer_date:
#         start_date = datetime.strptime(transfer_date, "%Y-%m-%d %H:%M:%S")
#         end_date = datetime(datetime.utcnow().year, datetime.utcnow().month, datetime.utcnow().day) + timedelta(days=1)
#     else:
#         end_date = datetime.utcnow()
#         s = datetime.utcnow() - timedelta(days=overdays)
#         start_date = datetime(s.year, s.month, s.day)
#     transfer_objs = ssm.get_un_confrim_transfer_by_transfer_date(start_date, end_date, partner_id, status)
#     if len(transfer_objs) > 0:
#         for obj in transfer_objs:
#             mq_producer.publish(MessageTopic.SUPPLY_GROUP,
#                                 MessageTopic.AUTO_CONFIRM_TRANSFER_BY_ID,
#                                 dict(id=obj[0], partner_id=partner_id, user_id=user_id))
#     return True


# @register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.AUTO_CONFIRM_TRANSFER_BY_ID)
# def auto_confirm_transfer_by_id(message):
#     '''
#     自动收货——调拨
#     '''
#     id = convert_to_int(message['id']) if 'id' in message else None
#     partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
#     user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
#     ret = ssm.auto_confrim_transfer(id, partner_id, user_id)
#     return True
