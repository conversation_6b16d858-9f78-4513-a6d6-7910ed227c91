import logging
import traceback
from supply.driver.mq import register_mq_task
from supply.driver.Redis import Redis_cli
from supply.utils.helper import MessageTopic


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.CLEAN_TODO_CACHE_TOPIC)
def clean_unfinished_doc_cache(message):
    """
    当待办单据状态变化时触发该任务
    直接清理掉该租户门店单据的缓存
    param:
        partner_id: 租户ID
        store_id: 门店ID
        doc_type: 单据类型
    """
    logging.info("Received clean_unfinished_doc_cache task: {}".format(message))
    partner_id = message.get('partner_id')
    store_id = message.get('store_id')
    doc_type = message.get('doc_type')
    if not all([partner_id, store_id, doc_type]):
        logging.error('CLEAN_UNFINISHED_DOC_CACHE: not partner_id or not user_id')
        return True
    try:
        Redis_cli.clean_unfinished_doc_cache(partner_id=partner_id, store_id=store_id, doc_type=doc_type)
    except Exception as e:
        logging.error(e)
        traceback.print_exc()
    return True
