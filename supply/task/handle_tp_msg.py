import logging
from datetime import datetime,timedelta

# from supply.task import nsq_bind
from supply.driver.mq import register_mq_task, mq_producer
from supply.utils.helper import MessageTopic
from supply.utils.helper import convert_to_int

from supply.module.common_tp import common_tp_service
from supply.model.operation_log import TpTransLogModel
from supply import APP_CONFIG
from . import partner_task_distribution

tp_to_big = APP_CONFIG.get('extra_business_config', {}).get('tp_to_big_partners')


### 处理与其他服务交互的任务
# @nsq_bind(MessageTopic.SEND_TO_REPORTS, MessageTopic.RECEIPT_CHANNEL)
@register_mq_task('boh_order', MessageTopic.SEND_TO_TP)
def send_receipt_to_tp(message):
    '''
    传输单据给成本中心
    '''
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    user_id = convert_to_int(message['user_id']) if 'user_id' in message else None
    doc_id = convert_to_int(message['doc_id']) if 'doc_id' in message else None
    doc_resource = message['doc_resource'] if 'doc_resource' in message else ''
    operate_time = message.get('operate_time')

    # logging.info("接收到要传给三方的任务:{}".format(message))
    if not (partner_id and user_id and doc_id):
        logging.info("传给三方的消息缺少必须字段:{}".format(doc_id))
        return False
    if operate_time and isinstance(operate_time, str):
        operate_time = datetime.strptime(operate_time, '%Y-%m-%d %H:%M:%S')

    doc_detail = common_tp_service.deal_receipt_for_tp(
            doc_id=doc_id, doc_resource=doc_resource, partner_id=partner_id, user_id=user_id)

    if doc_detail != {}:
        if doc_resource.startswith("fs") or (partner_id in tp_to_big):
            # 发送给Big集成
            # logging.info("发送给Big的消息: {}".format(doc_detail))
            ret = mq_producer.publish('boh_order_big', MessageTopic.SYNC_RECEIPT_TO_TP, doc_detail)
            logging.info("发送给Big的消息: {}, ret: {}".format(doc_id, ret))
        else:
            # 发送给三方
            ret = mq_producer.publish('boh_order_tp', MessageTopic.SYNC_RECEIPT_TO_TP, doc_detail)
            logging.info("发送给三方的消息: {}, ret: {}".format(doc_id, ret))
        resend_dbs, _= TpTransLogModel.get_by_doc_id(doc_id=doc_id, partner_id=partner_id)
        update_logs = []
        for log in resend_dbs:
            tp_log = {"id": log.id, "status": "sent", "updated_at": datetime.now()}
            update_logs.append(tp_log)
        TpTransLogModel.update_doc_status(update_logs)
    return True


### 补偿传输给第三方传送的单据
@register_mq_task('boh_order', MessageTopic.RESEND_TO_TP)
@partner_task_distribution(topic_group='boh_order', topic=MessageTopic.RESEND_TO_TP)
def resend_receipt_to_tp(message):
    '''
    接收单据回传
    '''
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    status = message.get('status')
    overhours = message.get('overhours', 1)
    
    start_time = datetime.utcnow()-timedelta(hours=overhours)
    end_time = datetime.utcnow()

    resend_dbs = TpTransLogModel.get_docs(status=status, partner_id=partner_id, start_time=start_time, end_time=end_time)
    if resend_dbs:
        for resend_db in resend_dbs:
            # logging.info("重发给三方的消息: {}".format(resend_db.msg))
            mq_producer.publish('boh_order', MessageTopic.SEND_TO_TP, eval(resend_db.msg))
        # update_logs = []
        # for log in resend_dbs:
        #     tp_log = {"id": log.id, "status":"sent", "updated_at": datetime.now()}
        #     update_logs.append(tp_log)
        # TpTransLogModel.update_doc_status(update_logs)
    return True

### 接收第三方传送的单据
@register_mq_task('boh_order_tp', MessageTopic.ACK_FROM_TP)
def receive_ack_from_tp(message):

    '''
    接收单据回传
    '''
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    doc_id = message.get("doc_id")
    doc_code = message.get("doc_code")

    # logging.info("接收到第三方回传的消息:{}".format(message))
    if not (partner_id and (doc_id or doc_code)):
        logging.info("接收到第三方回传缺少必须字段:{}".format(message))
        return True
    
    if doc_id:
        tp_log_dbs, count = TpTransLogModel.get_by_doc_id(partner_id=partner_id, doc_id=doc_id)
        if count == 0:
            return True
        # 更新表中记录
        update_obj = {
                'id': doc_id,
                'status': 'success',
                'updated_at': datetime.utcnow()
            }
        TpTransLogModel.update_doc_status([update_obj])
    
    else:
        tp_log_dbs, count = TpTransLogModel.get_by_doc_code(partner_id=partner_id, doc_code=doc_code)
        if count == 0:
            return True
        update_list = []
        for tp_log in tp_log_dbs:
            update_obj = {
                'id': tp_log.id,
                'status': 'success',
                'updated_at': datetime.utcnow()
            }
            update_list.append(update_obj)
        TpTransLogModel.update_doc_status(update_list)

    return True


### 接收big集成第三方传送的单据
@register_mq_task('boh_order_big', MessageTopic.ACK_FROM_TP)
def receive_ack_from_big(message):

    '''
    接收单据回传
    '''
    partner_id = convert_to_int(message['partner_id']) if 'partner_id' in message else None
    doc_id = message.get("doc_id")
    doc_code = message.get("doc_code")

    # logging.info("接收到big回传的消息:{}".format(message))
    if not (partner_id and (doc_id or doc_code)):
        logging.info("接收到big回传缺少必须字段:{}".format(message))
        return True
    
    if doc_id:
        tp_log_dbs, count = TpTransLogModel.get_by_doc_id(partner_id=partner_id, doc_id=doc_id)
        if count == 0:
            return True
        # 更新表中记录
        update_obj = {
                'id': doc_id,
                'status': 'success',
                'updated_at': datetime.utcnow()
            }
        TpTransLogModel.update_doc_status([update_obj])
    
    else:
        tp_log_dbs, count = TpTransLogModel.get_by_doc_code(partner_id=partner_id, doc_code=doc_code)
        if count == 0:
            return True
        update_list = []
        for tp_log in tp_log_dbs:
            update_obj = {
                'id': tp_log.id,
                'status': 'success',
                'updated_at': datetime.utcnow()
            }
            update_list.append(update_obj)
        TpTransLogModel.update_doc_status(update_list)

    return True