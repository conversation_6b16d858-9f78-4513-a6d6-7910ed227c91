import logging
import traceback

from . import partner_task_distribution
from .. import logger
from datetime import timedelta, datetime
from supply.utils.helper import MessageTopic, convert_to_int
from supply.driver.mq import register_mq_task, mq_producer
from supply.module.franchisee.franchisee_demand import franchisee_demand_service
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand as sFD
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemandProduct as sFDP

"""
    加盟商订货拆单
"""


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND)
def handle_frs_demand(message):
    """
    处理加盟商订货单分配后拆单生成要货单
        依赖服务：receipt
    """
    logger.info("Received handle_frs_demand task: {}".format(message))
    demand_id = message.get('demand_id')
    partner_id = message.get('partner_id')
    user_id = message.get('user_id')
    username = message.get('username')
    if not all([demand_id, partner_id]):
        return True
    try:
        _ = franchisee_demand_service.handle_frs_demand(demand_id=demand_id, partner_id=partner_id, user_id=user_id,
                                                        username=username)
        un_pro_count, _ = sFDP.list_demand_product(demand_id=demand_id, partner_id=partner_id,
                                                   status=["INITED", "PROCESS"],
                                                   include_total=True)
        # 商品全部拆单完毕，更新主单处理状态
        if un_pro_count == 0:
            update_data = dict(
                id=demand_id,
                partner_id=partner_id,
                process_status="SUCCESS"
            )
            sFD.update_f_demand(update_data=update_data, allow_status=["APPROVING", "APPROVED"])
    except Exception as e:
        logger.error("[handle_frs_demand] Error:{}-{}".format(e, traceback.format_exc().replace('\n', ',')))

    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_HANDLE_FRANCHISEE_DEMAND)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_HANDLE_FRANCHISEE_DEMAND)
def recover_frs_demand(message):
    """补偿订货单拆单失败任务:
        overtime: 补偿超过更新时间多久的单据(min)
        days: 补偿超过更新时间多少天的单据(day)
        status: 订单状态(list) "APPROVING", 部分分配 "APPROVED" 全部分配
        process_status: 订单处理状态(list) PROCESSING处理中
     """
    logger.info("Received recover_frs_demand task: {}".format(message))
    partner_id = convert_to_int(message.get('partner_id'))
    user_id = convert_to_int(message.get('user_id'))
    overtime = convert_to_int(message.get('overtime')) if 'overtime' in message else 5
    days = convert_to_int(message.get('days')) if 'days' in message else 1
    # 去掉部分分配
    status = message.get('status', ["APPROVED"])
    process_status = message.get('process_status', ["PROCESSING"])
    if not all([partner_id, user_id]):
        return True
    try:
        # 获取一天内超过overtime(min)未拆的订货单
        end_time = datetime.now() - timedelta(minutes=overtime)
        start_time = end_time - timedelta(days=days)
        undone_demand, count = sFD.get_undone_f_demand(partner_id=partner_id, start_time=start_time, end_time=end_time,
                                                       status=status, process_status=process_status)
        logger.info("[recover_frs_demand]共有{}个未处理的订货单".format(count))
        if undone_demand:
            for demand in undone_demand:
                message = {
                    'demand_id': demand[0],
                    'partner_id': partner_id,
                    'user_id': user_id
                }
                logging.info(f'补偿仓库下发: {message}')
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND, message=message)
    except Exception as e:
        logger.error("[recover_frs_demand] Error:{}-{}".format(e, traceback.format_exc().replace('\n', ',')))
    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND_DISTRIBUTE)
def handle_frs_demand_distribute(message):
    """
    处理加盟商订货单确认后自动分配仓库
    """
    logger.info("Received handle_frs_demand_distribute task: {}".format(message))
    demand_ids = message.get('demand_ids', [])
    partner_id = message.get('partner_id')
    user_id = message.get('user_id')
    if not all([demand_ids, partner_id, user_id]):
        return True
    try:
        for demand_id in demand_ids:
            try:
                # _ = franchisee_demand_service.handle_frs_demand_distribute(demand_id=demand_id, partner_id=partner_id,
                #                                                            user_id=user_id)
                franchisee_demand_service.allocate_distribute_by(demand_id=demand_id, partner_id=partner_id,
                                                                 user_id=user_id, operator_name='系统机器人',
                                                                 raise_exception=False)
            except Exception as e:
                logger.error(
                    "handle_frs_demand_distribute Error: {} \n {}".format(e, traceback.format_exc().replace('\n', ',')))
                continue
    except Exception as e:
        logger.error("[handle_frs_demand_distribute] Error:{}-{}".format(e, traceback.format_exc().replace('\n', ',')))

    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_FRANCHISEE_DEMAND_DISTRIBUTE)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_FRANCHISEE_DEMAND_DISTRIBUTE)
def recover_frs_demand_distribute(message):
    """
    补偿"已确认"订单未自动分配仓库
    overtime: 补偿超过更新时间多久的单据(min) 默认5min
    days: 补偿超过更新时间多少天的单据(day)   默认1day
    status: 订单状态(list) ["CONFIRMED"] 已确认
    """
    logger.info("Received recover_frs_demand_distribute task: {}".format(message))
    partner_id = convert_to_int(message.get('partner_id'))
    user_id = convert_to_int(message.get('user_id'))
    overtime = convert_to_int(message.get('overtime')) if 'overtime' in message else 5
    days = convert_to_int(message.get('days')) if 'days' in message else 1
    status = message.get('status')
    if not all([partner_id, user_id]):
        return True
    if not status:
        status = ["CONFIRMED"]
    try:
        end_time = datetime.now() - timedelta(minutes=overtime)
        start_time = end_time - timedelta(days=days)
        undone_demand, count = sFD.get_undone_f_demand(partner_id=partner_id, start_time=start_time, end_time=end_time,
                                                       status=status)
        logger.info("[recover_frs_demand_distribute]共有{}个未自动分配的订货单".format(count))
        if undone_demand:
            for demand in undone_demand:
                mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                    topic=MessageTopic.HANDLE_FRANCHISEE_DEMAND_DISTRIBUTE,
                                    message={
                                        'demand_ids': [demand[0]],
                                        'partner_id': partner_id,
                                        'user_id': user_id
                                    })
    except Exception as e:
        logger.error("[recover_frs_demand_distribute] Error:{}-{}".format(e, traceback.format_exc().replace('\n', ',')))

    return True
