from datetime import datetime

from supply import logger
from supply.driver.mysql import db_commit
from supply.module.sale_forecast import SaleForecastTask
from supply.utils.helper import (MessageTopic)
from supply.driver.mq import register_mq_task


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.DEMAND_SUGGEST_FORECAST_CHECK)
@db_commit
def demand_suggest_forecast_trigger(message):
    """夜间0:00以后，自动跑任务，给每个门店建立7天后的预测值"""
    logger.info("demand_suggest_forecast_trigger get message %s " % message)
    partner_id = message.get('partner_id', 0)
    user_id = message.get('user_id', 0)
    if not (partner_id and user_id):
        logger.error("demand_suggest_forecast_trigger error no user_id and partner_id ")
        return True
    SaleForecastTask.trigger_store_sale_forecast(partner_id, user_id)
    logger.info("demand_suggest_forecast_trigger execute over %s " % message)


@register_mq_task(MessageTopic.SUPPLY_GROUP, MessageTopic.DEMAND_SUGGEST_FORECAST_STORE_CREATE)
@db_commit
def demand_suggest_create(message):
    logger.info("demand_suggest_create get message %s " % message)
    store_id = message.get('store_id')
    partner_id = message.get('partner_id')
    user_id = message.get('user_id')
    biz_date_str = message.get('biz_date')
    if biz_date_str:
        biz_date = datetime.strptime(biz_date_str, '%Y-%m-%d %H:%M:%S')
    else:
        biz_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    if not (store_id and partner_id and user_id):
        logger.error("demand_suggest_create error no user_id or partner_id or store_id( %s )" % message)
        return
    SaleForecastTask.create_store_sale_forecast(partner_id, user_id, store_id, biz_date)
    logger.info('demand_suggest_create execute over!')


@register_mq_task(MessageTopic.SUPPLY_GROUP, "demand.suggest.demo")
@db_commit
def demo(message):
    logger.info("demand_suggest_create get message %s " % message)
    id = message.get('id') or 10
    # SaleForecastTask.call_session(id)
    logger.info('demand_suggest_create execute over!')
