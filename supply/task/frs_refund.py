
import traceback

from . import partner_task_distribution
from .. import logger
from datetime import timedelta, datetime
from supply.utils.helper import MessageTopic, convert_to_int
from supply.driver.mq import register_mq_task, mq_producer
from supply.client.credit_pay import credit_pay_service
from supply.model.franchisee.franchisee_refund import SupplyFranchiseeRefund as sFR
from supply.module.franchisee.franchisee_refund import franchisee_refund_service
from supply.model.franchisee.franchisee_demand import SupplyFranchiseeDemand as sFD

"""加盟商退款单自动任务：
    1、加盟商退款单自动推进
"""


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_FRANCHISEE_REFUND)
def handle_frs_refund(message):
    """
    处理加盟商各业务申请退款自动触发退款审核
        支持批量退款, 最多支持10个同时退
        依赖服务：credit-pay
    """
    logger.info("Received handle_frs_refund task: {}".format(message))
    refund_id = message.get('refund_id')
    partner_id = message.get('partner_id')
    user_id = message.get('user_id')
    if not all([refund_id, partner_id, user_id]):
        return True
    try:
        if isinstance(refund_id, list):
            res = credit_pay_service.Refund(refund_ids=refund_id, partner_id=partner_id, user_id=user_id)
        else:
            res = credit_pay_service.Refund(refund_ids=[int(refund_id)], partner_id=partner_id, user_id=user_id)
        logger.info(f"[handle_frs_refund] Refund res: {res}")
    except Exception as e:
        logger.error("[handle_frs_refund] Error:{}-{}".format(e, traceback.format_exc().replace('\n', ',')))
    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_HANDLE_FRANCHISEE_REFUND)
@partner_task_distribution(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_HANDLE_FRANCHISEE_REFUND)
def recover_frs_refund(message):
    """补偿未在规定时间内审核的退款单:
        overtime: 补偿超过更新时间多久的单据(min) 默认3min
        days: 补偿超过更新时间多少天的单据(day)   默认1day
        status: 订单状态(list) "INITED", 待审核
        max_process: 最大退款单批处理数量  默认10个
     """
    logger.info("Received recover_frs_demand task: {}".format(message))
    partner_id = convert_to_int(message.get('partner_id'))
    user_id = convert_to_int(message.get('user_id'))
    overtime = convert_to_int(message.get('overtime')) if 'overtime' in message else 3
    days = convert_to_int(message.get('days')) if 'days' in message else 1
    status = message.get('status', ["INITED"])
    max_process = convert_to_int(message.get('max_process', 10))
    if not all([partner_id, user_id]):
        return True
    try:
        end_time = datetime.now() - timedelta(minutes=overtime)
        start_time = end_time - timedelta(days=days)
        undone_refund, count = sFR.get_undone_frs_refund(partner_id=partner_id, start_time=start_time,
                                                         end_time=end_time, status=status)
        logger.info("[recover_frs_refund]共有{}个未处理的退款单".format(count))
        if undone_refund:
            refund_ids = []
            for inx, refund in enumerate(undone_refund):
                if len(refund_ids) < max_process:
                    refund_ids.append(refund.id)
                else:
                    message = {
                        'refund_id': refund_ids.copy(),
                        'partner_id': partner_id,
                        'user_id': user_id
                    }
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.HANDLE_FRANCHISEE_REFUND, message=message)
                    refund_ids.clear()
                if inx + 1 == count:
                    message = {
                        'refund_id': refund_ids,
                        'partner_id': partner_id,
                        'user_id': user_id
                    }
                    mq_producer.publish(topic_group=MessageTopic.SUPPLY_GROUP,
                                        topic=MessageTopic.HANDLE_FRANCHISEE_REFUND, message=message)
    except Exception as e:
        logger.error("[recover_frs_refund] Error:{}-{}".format(e, traceback.format_exc().replace('\n', ',')))
    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.HANDLE_BATCH_OSS_FRANCHISEE_REFUND)
def batch_handle_oss_frs_refund(message):
    """
    处理批量"确认订单"自动生成缺货退款单
    """
    logger.info("Received batch_handle_oss_frs_refund task: {}".format(message))
    demand_ids = message.get('demand_ids')
    partner_id = message.get('partner_id')
    user_id = message.get('user_id')
    if not all([demand_ids, partner_id, user_id]):
        return True
    try:
        _ = franchisee_refund_service.generate_oss_refunds(demand_ids=demand_ids, partner_id=partner_id,
                                                           user_id=user_id)
    except Exception as e:
        logger.error("[batch_handle_oss_frs_refund] Error:{}-{}".format(e, traceback.format_exc().replace('\n', ',')))
    return True


@register_mq_task(topic_group=MessageTopic.SUPPLY_GROUP, topic=MessageTopic.RECOVER_GENERATE_OSS_FRANCHISEE_REFUND)
def recover_generate_oss_frs_refund(message):
    """补偿"已确认"订货单未生成缺货退款单:
    缺货退款条件：非总部分配单 + 付款方式为代金券 + 门店确认订单(且商品确认订货数量<商品订货数量)
        overtime: 补偿超过更新时间多久的单据(min) 默认3min
        days: 补偿超过更新时间多少天的单据(day)   默认1day
        status: 订单状态(list) ["CONFIRMED", "APPROVING", "APPROVED"] 已确认
    """
    logger.info("Received recover_generate_oss_frs_refund task: {}".format(message))
    partner_id = convert_to_int(message.get('partner_id'))
    user_id = convert_to_int(message.get('user_id'))
    overtime = convert_to_int(message.get('overtime')) if 'overtime' in message else 3
    days = convert_to_int(message.get('days')) if 'days' in message else 1
    status = message.get('status')
    if not all([partner_id, user_id]):
        return True
    if not status:
        status = ["CONFIRMED", "APPROVING", "APPROVED"]
    try:
        end_time = datetime.now() - timedelta(minutes=overtime)
        start_time = end_time - timedelta(days=days)
        pass
    except Exception as e:
        logger.error(
            "[recover_generate_oss_frs_refund] Error:{}-{}".format(e, traceback.format_exc().replace('\n', ',')))
    return True
