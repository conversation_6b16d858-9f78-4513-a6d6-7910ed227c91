# from supply.task import nsq_bind

from typing import Dict
from supply.task import public
from supply.driver.mq import register_mq_task

#
# @register_mq_task(topic_group="boh.order", topic="test_topic")
# def test(message):
#     print("received new mq message: {}".format(message))
#     return True


# @nsq_bind("test", "my_channel")
# def task_1(message: Dict):
#     # print("i get message: ", message)
#     pass


# import nsq
# import tornado.ioloop
# import time
#
# def pub_message():
#     writer.pub('20190115.supply.stocktake.store.init', '', finish_pub)
#
# def finish_pub(conn, data):
#     print(data)
#
# writer = nsq.Writer(['127.0.0.1:4150'])
# tornado.ioloop.PeriodicCallback(pub_message, 1000).start()
# nsq.run()