import hex_exception
from hex_exception import HexException


class DemandCode(hex_exception.ExceptionCode):
    DEMAND_NOT_EXIST_ERROR = "DEMAND_NOT_EXIST_ERROR"
    DEMAND_INVALID_ERROR = "DEMAND_INVALID_ERROR"
    DEMAND_BELONG_ERROR = "DEMAND_BELONG_ERROR"
    DEMAND_STATUS_ERROR = "DEMAND_STATUS_ERROR"

    DEMAND_TYPE_ERROR = "DEMAND_TYPE_ERROR"
    DEMAND_ERROR = "DEMAND_STATUS_ERROR"

class DemandError(HexException):
    def __init__(self, description, detail=None, source=None):
        super(DemandError, self).__init__(DemandCode.DEMAND_ERROR, description, detail, source)

class DemandNotExistError(HexException):
    def __init__(self, description, detail=None, source=None):
        super(DemandNotExistError, self).__init__(DemandCode.DEMAND_NOT_EXIST_ERROR, description, detail, source)


class DemandInvalidError(HexException):
    def __init__(self, description, detail=None, source=None):
        super(DemandInvalidError, self).__init__(DemandCode.DEMAND_INVALID_ERROR, description, detail, source)


class DemandBelongError(HexException):
    def __init__(self, description, detail=None, source=None):
        super(DemandBelongError, self).__init__(DemandCode.DEMAND_BELONG_ERROR, description, detail, source)


class DemandStatusError(HexException):
    def __init__(self, description="DemandStatusError", detail=None, source=None):
        super(DemandStatusError, self).__init__(DemandCode.DEMAND_STATUS_ERROR, description, detail, source)

class DemandTypeError(HexException):
    def __init__(self, description="DemandTypeError", detail=None, source=None):
        super(DemandTypeError, self).__init__(DemandCode.DEMAND_TYPE_ERROR, description, detail, source)

class StoreCode(hex_exception.ExceptionCode):
    STORE_NOT_EXIST_ERROR = "STORE_NOT_EXIST_ERROR"
    UNIT_ERROR = "UNIT_ERROR"
    PRODUCT_ERROR = "PRODUCT_ERROR"

class StoreNotExist(HexException):
    def __init__(self, description="STORE_NOT_EXIST_ERROR", detail=None, source=None):
        super(StoreNotExist, self).__init__(StoreCode.STORE_NOT_EXIST_ERROR, description, detail, source)

class UnitError(HexException):
    def __init__(self, description="UNIT_ERROR", detail=None, source=None):
        super(UnitError, self).__init__(StoreCode.UNIT_ERROR, description, detail, source)

class ProductError(HexException):
    def __init__(self, description="PRODUCT_ERROR", detail=None, source=None):
        super(ProductError, self).__init__(StoreCode.PRODUCT_ERROR, description, detail, source)

class VacancyCode(hex_exception.ExceptionCode):
    VACANCY_ERROR = "VACANCY_ERROR"

class VacancyError(HexException):
    def __init__(self, description="VACANCY_ERROR", detail=None, source=None):
        super(VacancyError, self).__init__(VacancyCode.VACANCY_ERROR, description, detail, source)