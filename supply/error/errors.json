{"UNKNOWN_ERROR": {"code": 10000, "description": "未知错误", "status_code": 500}, "CONFIG_ERROR": {"code": 10001, "description": "配置错误", "status_code": 500}, "GRPC_API_UNDEFINED": {"code": 10002, "description": "GRPC接口不存在", "status_code": 500}, "PARAMETER_ERROR": {"code": 10003, "description": "参数错误", "status_code": 400}, "DEMAND_NOT_EXIST_ERROR": {"code": 10004, "description": "订单不存在", "status_code": 400}, "DEMAND_INVALID_ERROR": {"code": 10005, "description": "订货单状态错误", "status_code": 400}, "DEMAND_BELONG_ERROR": {"code": 10006, "description": "订货单不属于该租户", "status_code": 400}, "DEMAND_STATUS_ERROR": {"code": 10007, "description": "订单状态不允许继续操作", "status_code": 400}, "DEMAND_TYPE_ERROR": {"code": 10008, "description": "订单类型错误", "status_code": 400}, "DEMAND_SUB_TYPE_ERROR": {"code": 10009, "description": "主配单类型错误", "status_code": 400}, "DEMAND_STORE_ERROR": {"code": 10010, "description": "门店不存在", "status_code": 400}, "UNIT_ERROR": {"code": 10011, "description": "商品单位错误", "status_code": 400}}