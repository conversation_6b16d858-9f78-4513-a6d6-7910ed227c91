import hex_exception
from hex_exception import HexException


class NoRequestIDError(HexException):
    def __init__(self, description, detail=None, source=None):
        super(NoRequestIDError, self).__init__('NO_REQUESTID_ERROR', description, detail, source)


class DataValidationException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(DataValidationException, self).__init__('DATA_VALIDATION_EXCEPTION', description, detail, source)


class DataDuplicationException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(DataDuplicationException, self).__init__('Data_Duplication_Exception', description, detail, source)


class NoResultFoundError(HexException):
    def __init__(self, description, detail=None, source=None):
        super(NoResultFoundError, self).__init__('NO_RESULT_FOUND_ERROR', description, detail, source)


class StatusUnavailable(HexException):
    def __init__(self, description, detail=None, source=None):
        super(StatusUnavailable, self).__init__('STATUS_UNAVAILABLE', description, detail, source)


class GetMetadataException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(GetMetadataException, self).__init__('GET_METADATA_EXCEPTION', description, detail, source)


class GetInventoryException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(GetInventoryException, self).__init__('GET_INVENTORY_EXCEPTION', description, detail, source)


class DealInventoryException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(DealInventoryException, self).__init__('DEAL_INVENTORY_EXCEPTION', description, detail, source)


class StocktakeUnitException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(StocktakeUnitException, self).__init__('STOCKTAKE_UNIT_EXCEPTION', description, detail, source)


class InventoryCalculateException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(InventoryCalculateException, self).__init__('INVENTORY_CALCULATE_EXCEPTION', description, detail, source)


class AdjustInventoryException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(AdjustInventoryException, self).__init__('ADJUST_INVENTORY_EXCEPTION', description, detail, source)


class TransferInventoryException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(TransferInventoryException, self).__init__('TRANSFER_INVENTORY_EXCEPTION', description, detail, source)


class ActionException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(ActionException, self).__init__('ACTION_EXCEPTION', description, detail, source)


class StoreScopeException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(StoreScopeException, self).__init__('StoreScopeException', description, detail, source)


class JDEActionException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(JDEActionException, self).__init__('JDE_ACTION_EXCEPTION', description, detail, source)


class AdjustJDEActionException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(AdjustJDEActionException, self).__init__('ADJUST_JDE_ACTION_EXCEPTION', description, detail, source)


class StockTakePermissionException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(StockTakePermissionException, self).__init__('STOCKTAKE_PERMISSION_EXCEPTION', description, detail,
                                                           source)


class MasterUploadException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(MasterUploadException, self).__init__('MASTER_UPLOAD_EXCEPTION', description, detail, source)


class OrderNotExistException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(OrderNotExistException, self).__init__('ORDER_NOT_EXIST_EXCEPTION', description, detail, source)


class FileFormatIncorrectError(HexException):
    def __init__(self, descriotion, detail=None, source=None):
        super(FileFormatIncorrectError, self).__init__('FILE_FORMAT_INCORRECT_ERROR', descriotion, detail, source)


class AuthScopeException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(AuthScopeException, self).__init__('AuthScopeException', description, detail, source)


class Unauthorized(HexException):
    def __init__(self, description, detail=None, source=None):
        super(Unauthorized, self).__init__('Unauthorized', description, detail, source)


class NoUserInfo(HexException):
    def __init__(self, description, detail=None, source=None):
        super(NoUserInfo, self).__init__('NoUserInfo', description, detail, source)


class ReturnInventoryException(HexException):
    def __init__(self, description, detail=None, source=None):
        super(ReturnInventoryException, self).__init__('RETURN_INVENTORY_EXCEPTION', description, detail, source)
