#FROM registry.hexcloud.cn/infra/python:3.6.8
#ADD ./deployment/rocketmq-client-cpp-2.0.0.amd64.deb rocketmq-client-cpp-2.0.0.amd64.deb
#RUN dpkg -i rocketmq-client-cpp-2.0.0.amd64.deb
#RUN mkdir /app
#WORKDIR /app
#ADD requirements.txt requirements.txt
#RUN cat requirements.txt
#RUN pip install pandas==1.1.5 -i https://pypi.douban.com/simple/  \
#    && pip install -i https://pypi.hexcloud.cn/hex/pypi -r ./requirements.txt
#RUN pip install pandas==1.1.5 -i https://pypi.douban.com/simple/  \
#    && pip install -i https://pypi.hexcloud.cn/hex/pypi -r ./requirements.txt
#COPY .. /app/supply
#WORKDIR /app/supply
#ENTRYPOINT ["python"]
#CMD ["start.py"]
FROM hex-container-registry.cn-shanghai.cr.aliyuncs.com/saas-boh/supply:master-8a1d5212-2023-05-14-00-45-53
COPY .. /app/supply
WORKDIR /app/supply
ENTRYPOINT ["python"]
CMD ["start.py"]



